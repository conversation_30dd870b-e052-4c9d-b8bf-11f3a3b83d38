import { z } from 'zod';
export declare const BacktestStatusSchema: z.Zod<PERSON><["pending", "running", "completed", "error"]>;
export type BacktestStatus = z.infer<typeof BacktestStatusSchema>;
export declare const BacktestConfigSchema: z.Zod<PERSON>ffects<z.ZodObject<{
    name: z.ZodString;
    description: z.ZodOptional<z.ZodString>;
    symbols: z.Z<PERSON><z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>, "many">;
    start_date: z.ZodDate;
    end_date: z.ZodDate;
    initial_balance: z.ZodDefault<z.ZodNumber>;
    strategy: z.ZodObject<{
        name: z.ZodString;
        parameters: z.ZodRecord<z.ZodString, z.ZodAny>;
    }, "strip", z.<PERSON>odType<PERSON>ny, {
        name: string;
        parameters: Record<string, any>;
    }, {
        name: string;
        parameters: Record<string, any>;
    }>;
    risk_management: z.ZodObject<{
        max_risk_per_trade: z.ZodDefault<z.ZodNumber>;
        max_concurrent_trades: z.ZodDefault<z.ZodNumber>;
        stop_loss_pips: z.ZodOptional<z.ZodNumber>;
        take_profit_pips: z.ZodOptional<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        max_risk_per_trade: number;
        max_concurrent_trades: number;
        stop_loss_pips?: number | undefined;
        take_profit_pips?: number | undefined;
    }, {
        max_risk_per_trade?: number | undefined;
        max_concurrent_trades?: number | undefined;
        stop_loss_pips?: number | undefined;
        take_profit_pips?: number | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    strategy: {
        name: string;
        parameters: Record<string, any>;
    };
    name: string;
    initial_balance: number;
    start_date: Date;
    end_date: Date;
    symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
    risk_management: {
        max_risk_per_trade: number;
        max_concurrent_trades: number;
        stop_loss_pips?: number | undefined;
        take_profit_pips?: number | undefined;
    };
    description?: string | undefined;
}, {
    strategy: {
        name: string;
        parameters: Record<string, any>;
    };
    name: string;
    start_date: Date;
    end_date: Date;
    symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
    risk_management: {
        max_risk_per_trade?: number | undefined;
        max_concurrent_trades?: number | undefined;
        stop_loss_pips?: number | undefined;
        take_profit_pips?: number | undefined;
    };
    description?: string | undefined;
    initial_balance?: number | undefined;
}>, {
    strategy: {
        name: string;
        parameters: Record<string, any>;
    };
    name: string;
    initial_balance: number;
    start_date: Date;
    end_date: Date;
    symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
    risk_management: {
        max_risk_per_trade: number;
        max_concurrent_trades: number;
        stop_loss_pips?: number | undefined;
        take_profit_pips?: number | undefined;
    };
    description?: string | undefined;
}, {
    strategy: {
        name: string;
        parameters: Record<string, any>;
    };
    name: string;
    start_date: Date;
    end_date: Date;
    symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
    risk_management: {
        max_risk_per_trade?: number | undefined;
        max_concurrent_trades?: number | undefined;
        stop_loss_pips?: number | undefined;
        take_profit_pips?: number | undefined;
    };
    description?: string | undefined;
    initial_balance?: number | undefined;
}>;
export type BacktestConfig = z.infer<typeof BacktestConfigSchema>;
export declare const CreateBacktestRequestSchema: z.ZodObject<{
    config: z.ZodEffects<z.ZodObject<{
        name: z.ZodString;
        description: z.ZodOptional<z.ZodString>;
        symbols: z.ZodArray<z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>, "many">;
        start_date: z.ZodDate;
        end_date: z.ZodDate;
        initial_balance: z.ZodDefault<z.ZodNumber>;
        strategy: z.ZodObject<{
            name: z.ZodString;
            parameters: z.ZodRecord<z.ZodString, z.ZodAny>;
        }, "strip", z.ZodTypeAny, {
            name: string;
            parameters: Record<string, any>;
        }, {
            name: string;
            parameters: Record<string, any>;
        }>;
        risk_management: z.ZodObject<{
            max_risk_per_trade: z.ZodDefault<z.ZodNumber>;
            max_concurrent_trades: z.ZodDefault<z.ZodNumber>;
            stop_loss_pips: z.ZodOptional<z.ZodNumber>;
            take_profit_pips: z.ZodOptional<z.ZodNumber>;
        }, "strip", z.ZodTypeAny, {
            max_risk_per_trade: number;
            max_concurrent_trades: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        }, {
            max_risk_per_trade?: number | undefined;
            max_concurrent_trades?: number | undefined;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        }>;
    }, "strip", z.ZodTypeAny, {
        strategy: {
            name: string;
            parameters: Record<string, any>;
        };
        name: string;
        initial_balance: number;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        risk_management: {
            max_risk_per_trade: number;
            max_concurrent_trades: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        description?: string | undefined;
    }, {
        strategy: {
            name: string;
            parameters: Record<string, any>;
        };
        name: string;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        risk_management: {
            max_risk_per_trade?: number | undefined;
            max_concurrent_trades?: number | undefined;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        description?: string | undefined;
        initial_balance?: number | undefined;
    }>, {
        strategy: {
            name: string;
            parameters: Record<string, any>;
        };
        name: string;
        initial_balance: number;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        risk_management: {
            max_risk_per_trade: number;
            max_concurrent_trades: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        description?: string | undefined;
    }, {
        strategy: {
            name: string;
            parameters: Record<string, any>;
        };
        name: string;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        risk_management: {
            max_risk_per_trade?: number | undefined;
            max_concurrent_trades?: number | undefined;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        description?: string | undefined;
        initial_balance?: number | undefined;
    }>;
    data_source: z.ZodEnum<["historical", "uploaded"]>;
    data_file_id: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    config: {
        strategy: {
            name: string;
            parameters: Record<string, any>;
        };
        name: string;
        initial_balance: number;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        risk_management: {
            max_risk_per_trade: number;
            max_concurrent_trades: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        description?: string | undefined;
    };
    data_source: "historical" | "uploaded";
    data_file_id?: string | undefined;
}, {
    config: {
        strategy: {
            name: string;
            parameters: Record<string, any>;
        };
        name: string;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        risk_management: {
            max_risk_per_trade?: number | undefined;
            max_concurrent_trades?: number | undefined;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        description?: string | undefined;
        initial_balance?: number | undefined;
    };
    data_source: "historical" | "uploaded";
    data_file_id?: string | undefined;
}>;
export type CreateBacktestRequest = z.infer<typeof CreateBacktestRequestSchema>;
export declare const BacktestSchema: z.ZodObject<{
    id: z.ZodString;
    user_id: z.ZodString;
    config: z.ZodEffects<z.ZodObject<{
        name: z.ZodString;
        description: z.ZodOptional<z.ZodString>;
        symbols: z.ZodArray<z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>, "many">;
        start_date: z.ZodDate;
        end_date: z.ZodDate;
        initial_balance: z.ZodDefault<z.ZodNumber>;
        strategy: z.ZodObject<{
            name: z.ZodString;
            parameters: z.ZodRecord<z.ZodString, z.ZodAny>;
        }, "strip", z.ZodTypeAny, {
            name: string;
            parameters: Record<string, any>;
        }, {
            name: string;
            parameters: Record<string, any>;
        }>;
        risk_management: z.ZodObject<{
            max_risk_per_trade: z.ZodDefault<z.ZodNumber>;
            max_concurrent_trades: z.ZodDefault<z.ZodNumber>;
            stop_loss_pips: z.ZodOptional<z.ZodNumber>;
            take_profit_pips: z.ZodOptional<z.ZodNumber>;
        }, "strip", z.ZodTypeAny, {
            max_risk_per_trade: number;
            max_concurrent_trades: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        }, {
            max_risk_per_trade?: number | undefined;
            max_concurrent_trades?: number | undefined;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        }>;
    }, "strip", z.ZodTypeAny, {
        strategy: {
            name: string;
            parameters: Record<string, any>;
        };
        name: string;
        initial_balance: number;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        risk_management: {
            max_risk_per_trade: number;
            max_concurrent_trades: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        description?: string | undefined;
    }, {
        strategy: {
            name: string;
            parameters: Record<string, any>;
        };
        name: string;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        risk_management: {
            max_risk_per_trade?: number | undefined;
            max_concurrent_trades?: number | undefined;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        description?: string | undefined;
        initial_balance?: number | undefined;
    }>, {
        strategy: {
            name: string;
            parameters: Record<string, any>;
        };
        name: string;
        initial_balance: number;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        risk_management: {
            max_risk_per_trade: number;
            max_concurrent_trades: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        description?: string | undefined;
    }, {
        strategy: {
            name: string;
            parameters: Record<string, any>;
        };
        name: string;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        risk_management: {
            max_risk_per_trade?: number | undefined;
            max_concurrent_trades?: number | undefined;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        description?: string | undefined;
        initial_balance?: number | undefined;
    }>;
    status: z.ZodEnum<["pending", "running", "completed", "error"]>;
    progress: z.ZodDefault<z.ZodNumber>;
    started_at: z.ZodOptional<z.ZodDate>;
    completed_at: z.ZodOptional<z.ZodDate>;
    error_message: z.ZodOptional<z.ZodString>;
    created_at: z.ZodDate;
    updated_at: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    status: "error" | "running" | "pending" | "completed";
    id: string;
    user_id: string;
    config: {
        strategy: {
            name: string;
            parameters: Record<string, any>;
        };
        name: string;
        initial_balance: number;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        risk_management: {
            max_risk_per_trade: number;
            max_concurrent_trades: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        description?: string | undefined;
    };
    created_at: Date;
    progress: number;
    updated_at: Date;
    started_at?: Date | undefined;
    completed_at?: Date | undefined;
    error_message?: string | undefined;
}, {
    status: "error" | "running" | "pending" | "completed";
    id: string;
    user_id: string;
    config: {
        strategy: {
            name: string;
            parameters: Record<string, any>;
        };
        name: string;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        risk_management: {
            max_risk_per_trade?: number | undefined;
            max_concurrent_trades?: number | undefined;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        description?: string | undefined;
        initial_balance?: number | undefined;
    };
    created_at: Date;
    updated_at: Date;
    progress?: number | undefined;
    started_at?: Date | undefined;
    completed_at?: Date | undefined;
    error_message?: string | undefined;
}>;
export type Backtest = z.infer<typeof BacktestSchema>;
export declare const BacktestTradeSchema: z.ZodObject<{
    entry_time: z.ZodDate;
    exit_time: z.ZodOptional<z.ZodDate>;
    symbol: z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>;
    order_type: z.ZodEnum<["buy", "sell"]>;
    entry_price: z.ZodNumber;
    exit_price: z.ZodOptional<z.ZodNumber>;
    volume: z.ZodNumber;
    pnl: z.ZodNumber;
    pnl_pips: z.ZodNumber;
    duration_minutes: z.ZodOptional<z.ZodNumber>;
    reason: z.ZodOptional<z.ZodEnum<["stop_loss", "take_profit", "strategy_exit", "timeout"]>>;
}, "strip", z.ZodTypeAny, {
    symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
    volume: number;
    order_type: "buy" | "sell";
    pnl: number;
    entry_time: Date;
    entry_price: number;
    pnl_pips: number;
    exit_time?: Date | undefined;
    exit_price?: number | undefined;
    duration_minutes?: number | undefined;
    reason?: "stop_loss" | "take_profit" | "strategy_exit" | "timeout" | undefined;
}, {
    symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
    volume: number;
    order_type: "buy" | "sell";
    pnl: number;
    entry_time: Date;
    entry_price: number;
    pnl_pips: number;
    exit_time?: Date | undefined;
    exit_price?: number | undefined;
    duration_minutes?: number | undefined;
    reason?: "stop_loss" | "take_profit" | "strategy_exit" | "timeout" | undefined;
}>;
export type BacktestTrade = z.infer<typeof BacktestTradeSchema>;
export declare const BacktestMetricsSchema: z.ZodObject<{
    total_trades: z.ZodNumber;
    winning_trades: z.ZodNumber;
    losing_trades: z.ZodNumber;
    win_rate: z.ZodNumber;
    total_pnl: z.ZodNumber;
    gross_profit: z.ZodNumber;
    gross_loss: z.ZodNumber;
    profit_factor: z.ZodNumber;
    max_drawdown: z.ZodNumber;
    max_drawdown_percent: z.ZodNumber;
    sharpe_ratio: z.ZodOptional<z.ZodNumber>;
    sortino_ratio: z.ZodOptional<z.ZodNumber>;
    average_win: z.ZodNumber;
    average_loss: z.ZodNumber;
    largest_win: z.ZodNumber;
    largest_loss: z.ZodNumber;
    average_trade_duration_minutes: z.ZodOptional<z.ZodNumber>;
    total_time_in_market_minutes: z.ZodOptional<z.ZodNumber>;
    expectancy: z.ZodNumber;
    kelly_criterion: z.ZodOptional<z.ZodNumber>;
    calmar_ratio: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    profit_factor: number;
    win_rate: number;
    max_drawdown: number;
    total_trades: number;
    winning_trades: number;
    losing_trades: number;
    total_pnl: number;
    average_win: number;
    average_loss: number;
    gross_profit: number;
    gross_loss: number;
    max_drawdown_percent: number;
    largest_win: number;
    largest_loss: number;
    expectancy: number;
    sharpe_ratio?: number | undefined;
    sortino_ratio?: number | undefined;
    average_trade_duration_minutes?: number | undefined;
    total_time_in_market_minutes?: number | undefined;
    kelly_criterion?: number | undefined;
    calmar_ratio?: number | undefined;
}, {
    profit_factor: number;
    win_rate: number;
    max_drawdown: number;
    total_trades: number;
    winning_trades: number;
    losing_trades: number;
    total_pnl: number;
    average_win: number;
    average_loss: number;
    gross_profit: number;
    gross_loss: number;
    max_drawdown_percent: number;
    largest_win: number;
    largest_loss: number;
    expectancy: number;
    sharpe_ratio?: number | undefined;
    sortino_ratio?: number | undefined;
    average_trade_duration_minutes?: number | undefined;
    total_time_in_market_minutes?: number | undefined;
    kelly_criterion?: number | undefined;
    calmar_ratio?: number | undefined;
}>;
export type BacktestMetrics = z.infer<typeof BacktestMetricsSchema>;
export declare const BacktestResultsSchema: z.ZodObject<{
    backtest_id: z.ZodString;
    config: z.ZodEffects<z.ZodObject<{
        name: z.ZodString;
        description: z.ZodOptional<z.ZodString>;
        symbols: z.ZodArray<z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>, "many">;
        start_date: z.ZodDate;
        end_date: z.ZodDate;
        initial_balance: z.ZodDefault<z.ZodNumber>;
        strategy: z.ZodObject<{
            name: z.ZodString;
            parameters: z.ZodRecord<z.ZodString, z.ZodAny>;
        }, "strip", z.ZodTypeAny, {
            name: string;
            parameters: Record<string, any>;
        }, {
            name: string;
            parameters: Record<string, any>;
        }>;
        risk_management: z.ZodObject<{
            max_risk_per_trade: z.ZodDefault<z.ZodNumber>;
            max_concurrent_trades: z.ZodDefault<z.ZodNumber>;
            stop_loss_pips: z.ZodOptional<z.ZodNumber>;
            take_profit_pips: z.ZodOptional<z.ZodNumber>;
        }, "strip", z.ZodTypeAny, {
            max_risk_per_trade: number;
            max_concurrent_trades: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        }, {
            max_risk_per_trade?: number | undefined;
            max_concurrent_trades?: number | undefined;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        }>;
    }, "strip", z.ZodTypeAny, {
        strategy: {
            name: string;
            parameters: Record<string, any>;
        };
        name: string;
        initial_balance: number;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        risk_management: {
            max_risk_per_trade: number;
            max_concurrent_trades: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        description?: string | undefined;
    }, {
        strategy: {
            name: string;
            parameters: Record<string, any>;
        };
        name: string;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        risk_management: {
            max_risk_per_trade?: number | undefined;
            max_concurrent_trades?: number | undefined;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        description?: string | undefined;
        initial_balance?: number | undefined;
    }>, {
        strategy: {
            name: string;
            parameters: Record<string, any>;
        };
        name: string;
        initial_balance: number;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        risk_management: {
            max_risk_per_trade: number;
            max_concurrent_trades: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        description?: string | undefined;
    }, {
        strategy: {
            name: string;
            parameters: Record<string, any>;
        };
        name: string;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        risk_management: {
            max_risk_per_trade?: number | undefined;
            max_concurrent_trades?: number | undefined;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        description?: string | undefined;
        initial_balance?: number | undefined;
    }>;
    metrics: z.ZodObject<{
        total_trades: z.ZodNumber;
        winning_trades: z.ZodNumber;
        losing_trades: z.ZodNumber;
        win_rate: z.ZodNumber;
        total_pnl: z.ZodNumber;
        gross_profit: z.ZodNumber;
        gross_loss: z.ZodNumber;
        profit_factor: z.ZodNumber;
        max_drawdown: z.ZodNumber;
        max_drawdown_percent: z.ZodNumber;
        sharpe_ratio: z.ZodOptional<z.ZodNumber>;
        sortino_ratio: z.ZodOptional<z.ZodNumber>;
        average_win: z.ZodNumber;
        average_loss: z.ZodNumber;
        largest_win: z.ZodNumber;
        largest_loss: z.ZodNumber;
        average_trade_duration_minutes: z.ZodOptional<z.ZodNumber>;
        total_time_in_market_minutes: z.ZodOptional<z.ZodNumber>;
        expectancy: z.ZodNumber;
        kelly_criterion: z.ZodOptional<z.ZodNumber>;
        calmar_ratio: z.ZodOptional<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        profit_factor: number;
        win_rate: number;
        max_drawdown: number;
        total_trades: number;
        winning_trades: number;
        losing_trades: number;
        total_pnl: number;
        average_win: number;
        average_loss: number;
        gross_profit: number;
        gross_loss: number;
        max_drawdown_percent: number;
        largest_win: number;
        largest_loss: number;
        expectancy: number;
        sharpe_ratio?: number | undefined;
        sortino_ratio?: number | undefined;
        average_trade_duration_minutes?: number | undefined;
        total_time_in_market_minutes?: number | undefined;
        kelly_criterion?: number | undefined;
        calmar_ratio?: number | undefined;
    }, {
        profit_factor: number;
        win_rate: number;
        max_drawdown: number;
        total_trades: number;
        winning_trades: number;
        losing_trades: number;
        total_pnl: number;
        average_win: number;
        average_loss: number;
        gross_profit: number;
        gross_loss: number;
        max_drawdown_percent: number;
        largest_win: number;
        largest_loss: number;
        expectancy: number;
        sharpe_ratio?: number | undefined;
        sortino_ratio?: number | undefined;
        average_trade_duration_minutes?: number | undefined;
        total_time_in_market_minutes?: number | undefined;
        kelly_criterion?: number | undefined;
        calmar_ratio?: number | undefined;
    }>;
    trades: z.ZodArray<z.ZodObject<{
        entry_time: z.ZodDate;
        exit_time: z.ZodOptional<z.ZodDate>;
        symbol: z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>;
        order_type: z.ZodEnum<["buy", "sell"]>;
        entry_price: z.ZodNumber;
        exit_price: z.ZodOptional<z.ZodNumber>;
        volume: z.ZodNumber;
        pnl: z.ZodNumber;
        pnl_pips: z.ZodNumber;
        duration_minutes: z.ZodOptional<z.ZodNumber>;
        reason: z.ZodOptional<z.ZodEnum<["stop_loss", "take_profit", "strategy_exit", "timeout"]>>;
    }, "strip", z.ZodTypeAny, {
        symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
        volume: number;
        order_type: "buy" | "sell";
        pnl: number;
        entry_time: Date;
        entry_price: number;
        pnl_pips: number;
        exit_time?: Date | undefined;
        exit_price?: number | undefined;
        duration_minutes?: number | undefined;
        reason?: "stop_loss" | "take_profit" | "strategy_exit" | "timeout" | undefined;
    }, {
        symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
        volume: number;
        order_type: "buy" | "sell";
        pnl: number;
        entry_time: Date;
        entry_price: number;
        pnl_pips: number;
        exit_time?: Date | undefined;
        exit_price?: number | undefined;
        duration_minutes?: number | undefined;
        reason?: "stop_loss" | "take_profit" | "strategy_exit" | "timeout" | undefined;
    }>, "many">;
    balance_curve: z.ZodArray<z.ZodObject<{
        timestamp: z.ZodDate;
        balance: z.ZodNumber;
        equity: z.ZodNumber;
        drawdown: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        timestamp: Date;
        balance: number;
        equity: number;
        drawdown: number;
    }, {
        timestamp: Date;
        balance: number;
        equity: number;
        drawdown: number;
    }>, "many">;
    monthly_returns: z.ZodArray<z.ZodObject<{
        year: z.ZodNumber;
        month: z.ZodNumber;
        return_percent: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        year: number;
        month: number;
        return_percent: number;
    }, {
        year: number;
        month: number;
        return_percent: number;
    }>, "many">;
    created_at: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    config: {
        strategy: {
            name: string;
            parameters: Record<string, any>;
        };
        name: string;
        initial_balance: number;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        risk_management: {
            max_risk_per_trade: number;
            max_concurrent_trades: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        description?: string | undefined;
    };
    trades: {
        symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
        volume: number;
        order_type: "buy" | "sell";
        pnl: number;
        entry_time: Date;
        entry_price: number;
        pnl_pips: number;
        exit_time?: Date | undefined;
        exit_price?: number | undefined;
        duration_minutes?: number | undefined;
        reason?: "stop_loss" | "take_profit" | "strategy_exit" | "timeout" | undefined;
    }[];
    created_at: Date;
    backtest_id: string;
    metrics: {
        profit_factor: number;
        win_rate: number;
        max_drawdown: number;
        total_trades: number;
        winning_trades: number;
        losing_trades: number;
        total_pnl: number;
        average_win: number;
        average_loss: number;
        gross_profit: number;
        gross_loss: number;
        max_drawdown_percent: number;
        largest_win: number;
        largest_loss: number;
        expectancy: number;
        sharpe_ratio?: number | undefined;
        sortino_ratio?: number | undefined;
        average_trade_duration_minutes?: number | undefined;
        total_time_in_market_minutes?: number | undefined;
        kelly_criterion?: number | undefined;
        calmar_ratio?: number | undefined;
    };
    balance_curve: {
        timestamp: Date;
        balance: number;
        equity: number;
        drawdown: number;
    }[];
    monthly_returns: {
        year: number;
        month: number;
        return_percent: number;
    }[];
}, {
    config: {
        strategy: {
            name: string;
            parameters: Record<string, any>;
        };
        name: string;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        risk_management: {
            max_risk_per_trade?: number | undefined;
            max_concurrent_trades?: number | undefined;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        description?: string | undefined;
        initial_balance?: number | undefined;
    };
    trades: {
        symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
        volume: number;
        order_type: "buy" | "sell";
        pnl: number;
        entry_time: Date;
        entry_price: number;
        pnl_pips: number;
        exit_time?: Date | undefined;
        exit_price?: number | undefined;
        duration_minutes?: number | undefined;
        reason?: "stop_loss" | "take_profit" | "strategy_exit" | "timeout" | undefined;
    }[];
    created_at: Date;
    backtest_id: string;
    metrics: {
        profit_factor: number;
        win_rate: number;
        max_drawdown: number;
        total_trades: number;
        winning_trades: number;
        losing_trades: number;
        total_pnl: number;
        average_win: number;
        average_loss: number;
        gross_profit: number;
        gross_loss: number;
        max_drawdown_percent: number;
        largest_win: number;
        largest_loss: number;
        expectancy: number;
        sharpe_ratio?: number | undefined;
        sortino_ratio?: number | undefined;
        average_trade_duration_minutes?: number | undefined;
        total_time_in_market_minutes?: number | undefined;
        kelly_criterion?: number | undefined;
        calmar_ratio?: number | undefined;
    };
    balance_curve: {
        timestamp: Date;
        balance: number;
        equity: number;
        drawdown: number;
    }[];
    monthly_returns: {
        year: number;
        month: number;
        return_percent: number;
    }[];
}>;
export type BacktestResults = z.infer<typeof BacktestResultsSchema>;
export declare const PythonBacktestRequestSchema: z.ZodObject<{
    request_id: z.ZodString;
    config: z.ZodEffects<z.ZodObject<{
        name: z.ZodString;
        description: z.ZodOptional<z.ZodString>;
        symbols: z.ZodArray<z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>, "many">;
        start_date: z.ZodDate;
        end_date: z.ZodDate;
        initial_balance: z.ZodDefault<z.ZodNumber>;
        strategy: z.ZodObject<{
            name: z.ZodString;
            parameters: z.ZodRecord<z.ZodString, z.ZodAny>;
        }, "strip", z.ZodTypeAny, {
            name: string;
            parameters: Record<string, any>;
        }, {
            name: string;
            parameters: Record<string, any>;
        }>;
        risk_management: z.ZodObject<{
            max_risk_per_trade: z.ZodDefault<z.ZodNumber>;
            max_concurrent_trades: z.ZodDefault<z.ZodNumber>;
            stop_loss_pips: z.ZodOptional<z.ZodNumber>;
            take_profit_pips: z.ZodOptional<z.ZodNumber>;
        }, "strip", z.ZodTypeAny, {
            max_risk_per_trade: number;
            max_concurrent_trades: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        }, {
            max_risk_per_trade?: number | undefined;
            max_concurrent_trades?: number | undefined;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        }>;
    }, "strip", z.ZodTypeAny, {
        strategy: {
            name: string;
            parameters: Record<string, any>;
        };
        name: string;
        initial_balance: number;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        risk_management: {
            max_risk_per_trade: number;
            max_concurrent_trades: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        description?: string | undefined;
    }, {
        strategy: {
            name: string;
            parameters: Record<string, any>;
        };
        name: string;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        risk_management: {
            max_risk_per_trade?: number | undefined;
            max_concurrent_trades?: number | undefined;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        description?: string | undefined;
        initial_balance?: number | undefined;
    }>, {
        strategy: {
            name: string;
            parameters: Record<string, any>;
        };
        name: string;
        initial_balance: number;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        risk_management: {
            max_risk_per_trade: number;
            max_concurrent_trades: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        description?: string | undefined;
    }, {
        strategy: {
            name: string;
            parameters: Record<string, any>;
        };
        name: string;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        risk_management: {
            max_risk_per_trade?: number | undefined;
            max_concurrent_trades?: number | undefined;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        description?: string | undefined;
        initial_balance?: number | undefined;
    }>;
    data: z.ZodObject<{
        market_data: z.ZodArray<z.ZodObject<{
            symbol: z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>;
            timestamp: z.ZodDate;
            open: z.ZodNumber;
            high: z.ZodNumber;
            low: z.ZodNumber;
            close: z.ZodNumber;
            volume: z.ZodOptional<z.ZodNumber>;
        }, "strip", z.ZodTypeAny, {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            low: number;
            high: number;
            close: number;
            open: number;
            timestamp: Date;
            volume?: number | undefined;
        }, {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            low: number;
            high: number;
            close: number;
            open: number;
            timestamp: Date;
            volume?: number | undefined;
        }>, "many">;
    }, "strip", z.ZodTypeAny, {
        market_data: {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            low: number;
            high: number;
            close: number;
            open: number;
            timestamp: Date;
            volume?: number | undefined;
        }[];
    }, {
        market_data: {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            low: number;
            high: number;
            close: number;
            open: number;
            timestamp: Date;
            volume?: number | undefined;
        }[];
    }>;
}, "strip", z.ZodTypeAny, {
    data: {
        market_data: {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            low: number;
            high: number;
            close: number;
            open: number;
            timestamp: Date;
            volume?: number | undefined;
        }[];
    };
    request_id: string;
    config: {
        strategy: {
            name: string;
            parameters: Record<string, any>;
        };
        name: string;
        initial_balance: number;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        risk_management: {
            max_risk_per_trade: number;
            max_concurrent_trades: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        description?: string | undefined;
    };
}, {
    data: {
        market_data: {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            low: number;
            high: number;
            close: number;
            open: number;
            timestamp: Date;
            volume?: number | undefined;
        }[];
    };
    request_id: string;
    config: {
        strategy: {
            name: string;
            parameters: Record<string, any>;
        };
        name: string;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        risk_management: {
            max_risk_per_trade?: number | undefined;
            max_concurrent_trades?: number | undefined;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        description?: string | undefined;
        initial_balance?: number | undefined;
    };
}>;
export type PythonBacktestRequest = z.infer<typeof PythonBacktestRequestSchema>;
export declare const PythonBacktestResponseSchema: z.ZodObject<{
    request_id: z.ZodString;
    success: z.ZodBoolean;
    results: z.ZodOptional<z.ZodObject<{
        backtest_id: z.ZodString;
        config: z.ZodEffects<z.ZodObject<{
            name: z.ZodString;
            description: z.ZodOptional<z.ZodString>;
            symbols: z.ZodArray<z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>, "many">;
            start_date: z.ZodDate;
            end_date: z.ZodDate;
            initial_balance: z.ZodDefault<z.ZodNumber>;
            strategy: z.ZodObject<{
                name: z.ZodString;
                parameters: z.ZodRecord<z.ZodString, z.ZodAny>;
            }, "strip", z.ZodTypeAny, {
                name: string;
                parameters: Record<string, any>;
            }, {
                name: string;
                parameters: Record<string, any>;
            }>;
            risk_management: z.ZodObject<{
                max_risk_per_trade: z.ZodDefault<z.ZodNumber>;
                max_concurrent_trades: z.ZodDefault<z.ZodNumber>;
                stop_loss_pips: z.ZodOptional<z.ZodNumber>;
                take_profit_pips: z.ZodOptional<z.ZodNumber>;
            }, "strip", z.ZodTypeAny, {
                max_risk_per_trade: number;
                max_concurrent_trades: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            }, {
                max_risk_per_trade?: number | undefined;
                max_concurrent_trades?: number | undefined;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            }>;
        }, "strip", z.ZodTypeAny, {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            initial_balance: number;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade: number;
                max_concurrent_trades: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
        }, {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade?: number | undefined;
                max_concurrent_trades?: number | undefined;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
            initial_balance?: number | undefined;
        }>, {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            initial_balance: number;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade: number;
                max_concurrent_trades: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
        }, {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade?: number | undefined;
                max_concurrent_trades?: number | undefined;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
            initial_balance?: number | undefined;
        }>;
        metrics: z.ZodObject<{
            total_trades: z.ZodNumber;
            winning_trades: z.ZodNumber;
            losing_trades: z.ZodNumber;
            win_rate: z.ZodNumber;
            total_pnl: z.ZodNumber;
            gross_profit: z.ZodNumber;
            gross_loss: z.ZodNumber;
            profit_factor: z.ZodNumber;
            max_drawdown: z.ZodNumber;
            max_drawdown_percent: z.ZodNumber;
            sharpe_ratio: z.ZodOptional<z.ZodNumber>;
            sortino_ratio: z.ZodOptional<z.ZodNumber>;
            average_win: z.ZodNumber;
            average_loss: z.ZodNumber;
            largest_win: z.ZodNumber;
            largest_loss: z.ZodNumber;
            average_trade_duration_minutes: z.ZodOptional<z.ZodNumber>;
            total_time_in_market_minutes: z.ZodOptional<z.ZodNumber>;
            expectancy: z.ZodNumber;
            kelly_criterion: z.ZodOptional<z.ZodNumber>;
            calmar_ratio: z.ZodOptional<z.ZodNumber>;
        }, "strip", z.ZodTypeAny, {
            profit_factor: number;
            win_rate: number;
            max_drawdown: number;
            total_trades: number;
            winning_trades: number;
            losing_trades: number;
            total_pnl: number;
            average_win: number;
            average_loss: number;
            gross_profit: number;
            gross_loss: number;
            max_drawdown_percent: number;
            largest_win: number;
            largest_loss: number;
            expectancy: number;
            sharpe_ratio?: number | undefined;
            sortino_ratio?: number | undefined;
            average_trade_duration_minutes?: number | undefined;
            total_time_in_market_minutes?: number | undefined;
            kelly_criterion?: number | undefined;
            calmar_ratio?: number | undefined;
        }, {
            profit_factor: number;
            win_rate: number;
            max_drawdown: number;
            total_trades: number;
            winning_trades: number;
            losing_trades: number;
            total_pnl: number;
            average_win: number;
            average_loss: number;
            gross_profit: number;
            gross_loss: number;
            max_drawdown_percent: number;
            largest_win: number;
            largest_loss: number;
            expectancy: number;
            sharpe_ratio?: number | undefined;
            sortino_ratio?: number | undefined;
            average_trade_duration_minutes?: number | undefined;
            total_time_in_market_minutes?: number | undefined;
            kelly_criterion?: number | undefined;
            calmar_ratio?: number | undefined;
        }>;
        trades: z.ZodArray<z.ZodObject<{
            entry_time: z.ZodDate;
            exit_time: z.ZodOptional<z.ZodDate>;
            symbol: z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>;
            order_type: z.ZodEnum<["buy", "sell"]>;
            entry_price: z.ZodNumber;
            exit_price: z.ZodOptional<z.ZodNumber>;
            volume: z.ZodNumber;
            pnl: z.ZodNumber;
            pnl_pips: z.ZodNumber;
            duration_minutes: z.ZodOptional<z.ZodNumber>;
            reason: z.ZodOptional<z.ZodEnum<["stop_loss", "take_profit", "strategy_exit", "timeout"]>>;
        }, "strip", z.ZodTypeAny, {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            volume: number;
            order_type: "buy" | "sell";
            pnl: number;
            entry_time: Date;
            entry_price: number;
            pnl_pips: number;
            exit_time?: Date | undefined;
            exit_price?: number | undefined;
            duration_minutes?: number | undefined;
            reason?: "stop_loss" | "take_profit" | "strategy_exit" | "timeout" | undefined;
        }, {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            volume: number;
            order_type: "buy" | "sell";
            pnl: number;
            entry_time: Date;
            entry_price: number;
            pnl_pips: number;
            exit_time?: Date | undefined;
            exit_price?: number | undefined;
            duration_minutes?: number | undefined;
            reason?: "stop_loss" | "take_profit" | "strategy_exit" | "timeout" | undefined;
        }>, "many">;
        balance_curve: z.ZodArray<z.ZodObject<{
            timestamp: z.ZodDate;
            balance: z.ZodNumber;
            equity: z.ZodNumber;
            drawdown: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            timestamp: Date;
            balance: number;
            equity: number;
            drawdown: number;
        }, {
            timestamp: Date;
            balance: number;
            equity: number;
            drawdown: number;
        }>, "many">;
        monthly_returns: z.ZodArray<z.ZodObject<{
            year: z.ZodNumber;
            month: z.ZodNumber;
            return_percent: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            year: number;
            month: number;
            return_percent: number;
        }, {
            year: number;
            month: number;
            return_percent: number;
        }>, "many">;
        created_at: z.ZodDate;
    }, "strip", z.ZodTypeAny, {
        config: {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            initial_balance: number;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade: number;
                max_concurrent_trades: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
        };
        trades: {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            volume: number;
            order_type: "buy" | "sell";
            pnl: number;
            entry_time: Date;
            entry_price: number;
            pnl_pips: number;
            exit_time?: Date | undefined;
            exit_price?: number | undefined;
            duration_minutes?: number | undefined;
            reason?: "stop_loss" | "take_profit" | "strategy_exit" | "timeout" | undefined;
        }[];
        created_at: Date;
        backtest_id: string;
        metrics: {
            profit_factor: number;
            win_rate: number;
            max_drawdown: number;
            total_trades: number;
            winning_trades: number;
            losing_trades: number;
            total_pnl: number;
            average_win: number;
            average_loss: number;
            gross_profit: number;
            gross_loss: number;
            max_drawdown_percent: number;
            largest_win: number;
            largest_loss: number;
            expectancy: number;
            sharpe_ratio?: number | undefined;
            sortino_ratio?: number | undefined;
            average_trade_duration_minutes?: number | undefined;
            total_time_in_market_minutes?: number | undefined;
            kelly_criterion?: number | undefined;
            calmar_ratio?: number | undefined;
        };
        balance_curve: {
            timestamp: Date;
            balance: number;
            equity: number;
            drawdown: number;
        }[];
        monthly_returns: {
            year: number;
            month: number;
            return_percent: number;
        }[];
    }, {
        config: {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade?: number | undefined;
                max_concurrent_trades?: number | undefined;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
            initial_balance?: number | undefined;
        };
        trades: {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            volume: number;
            order_type: "buy" | "sell";
            pnl: number;
            entry_time: Date;
            entry_price: number;
            pnl_pips: number;
            exit_time?: Date | undefined;
            exit_price?: number | undefined;
            duration_minutes?: number | undefined;
            reason?: "stop_loss" | "take_profit" | "strategy_exit" | "timeout" | undefined;
        }[];
        created_at: Date;
        backtest_id: string;
        metrics: {
            profit_factor: number;
            win_rate: number;
            max_drawdown: number;
            total_trades: number;
            winning_trades: number;
            losing_trades: number;
            total_pnl: number;
            average_win: number;
            average_loss: number;
            gross_profit: number;
            gross_loss: number;
            max_drawdown_percent: number;
            largest_win: number;
            largest_loss: number;
            expectancy: number;
            sharpe_ratio?: number | undefined;
            sortino_ratio?: number | undefined;
            average_trade_duration_minutes?: number | undefined;
            total_time_in_market_minutes?: number | undefined;
            kelly_criterion?: number | undefined;
            calmar_ratio?: number | undefined;
        };
        balance_curve: {
            timestamp: Date;
            balance: number;
            equity: number;
            drawdown: number;
        }[];
        monthly_returns: {
            year: number;
            month: number;
            return_percent: number;
        }[];
    }>>;
    error: z.ZodOptional<z.ZodString>;
    execution_time_seconds: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    request_id: string;
    execution_time_seconds: number;
    error?: string | undefined;
    results?: {
        config: {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            initial_balance: number;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade: number;
                max_concurrent_trades: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
        };
        trades: {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            volume: number;
            order_type: "buy" | "sell";
            pnl: number;
            entry_time: Date;
            entry_price: number;
            pnl_pips: number;
            exit_time?: Date | undefined;
            exit_price?: number | undefined;
            duration_minutes?: number | undefined;
            reason?: "stop_loss" | "take_profit" | "strategy_exit" | "timeout" | undefined;
        }[];
        created_at: Date;
        backtest_id: string;
        metrics: {
            profit_factor: number;
            win_rate: number;
            max_drawdown: number;
            total_trades: number;
            winning_trades: number;
            losing_trades: number;
            total_pnl: number;
            average_win: number;
            average_loss: number;
            gross_profit: number;
            gross_loss: number;
            max_drawdown_percent: number;
            largest_win: number;
            largest_loss: number;
            expectancy: number;
            sharpe_ratio?: number | undefined;
            sortino_ratio?: number | undefined;
            average_trade_duration_minutes?: number | undefined;
            total_time_in_market_minutes?: number | undefined;
            kelly_criterion?: number | undefined;
            calmar_ratio?: number | undefined;
        };
        balance_curve: {
            timestamp: Date;
            balance: number;
            equity: number;
            drawdown: number;
        }[];
        monthly_returns: {
            year: number;
            month: number;
            return_percent: number;
        }[];
    } | undefined;
}, {
    success: boolean;
    request_id: string;
    execution_time_seconds: number;
    error?: string | undefined;
    results?: {
        config: {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade?: number | undefined;
                max_concurrent_trades?: number | undefined;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
            initial_balance?: number | undefined;
        };
        trades: {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            volume: number;
            order_type: "buy" | "sell";
            pnl: number;
            entry_time: Date;
            entry_price: number;
            pnl_pips: number;
            exit_time?: Date | undefined;
            exit_price?: number | undefined;
            duration_minutes?: number | undefined;
            reason?: "stop_loss" | "take_profit" | "strategy_exit" | "timeout" | undefined;
        }[];
        created_at: Date;
        backtest_id: string;
        metrics: {
            profit_factor: number;
            win_rate: number;
            max_drawdown: number;
            total_trades: number;
            winning_trades: number;
            losing_trades: number;
            total_pnl: number;
            average_win: number;
            average_loss: number;
            gross_profit: number;
            gross_loss: number;
            max_drawdown_percent: number;
            largest_win: number;
            largest_loss: number;
            expectancy: number;
            sharpe_ratio?: number | undefined;
            sortino_ratio?: number | undefined;
            average_trade_duration_minutes?: number | undefined;
            total_time_in_market_minutes?: number | undefined;
            kelly_criterion?: number | undefined;
            calmar_ratio?: number | undefined;
        };
        balance_curve: {
            timestamp: Date;
            balance: number;
            equity: number;
            drawdown: number;
        }[];
        monthly_returns: {
            year: number;
            month: number;
            return_percent: number;
        }[];
    } | undefined;
}>;
export type PythonBacktestResponse = z.infer<typeof PythonBacktestResponseSchema>;
export declare const BacktestProgressSchema: z.ZodObject<{
    backtest_id: z.ZodString;
    progress: z.ZodNumber;
    current_date: z.ZodOptional<z.ZodDate>;
    trades_executed: z.ZodNumber;
    current_balance: z.ZodOptional<z.ZodNumber>;
    status: z.ZodEnum<["pending", "running", "completed", "error"]>;
    message: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    status: "error" | "running" | "pending" | "completed";
    progress: number;
    backtest_id: string;
    trades_executed: number;
    message?: string | undefined;
    current_date?: Date | undefined;
    current_balance?: number | undefined;
}, {
    status: "error" | "running" | "pending" | "completed";
    progress: number;
    backtest_id: string;
    trades_executed: number;
    message?: string | undefined;
    current_date?: Date | undefined;
    current_balance?: number | undefined;
}>;
export type BacktestProgress = z.infer<typeof BacktestProgressSchema>;
//# sourceMappingURL=backtest.schemas.d.ts.map