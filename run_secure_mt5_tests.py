#!/usr/bin/env python3
"""
Secure MT5 Bridge Test Runner
Comprehensive test execution with detailed reporting for TDD implementation.
"""

import sys
import os
import subprocess
import time
from datetime import datetime

def run_command(command, description):
    """Run a command and return success status"""
    print(f"\n🔄 {description}")
    print(f"Command: {command}")
    print("-" * 60)
    
    start_time = time.time()
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            cwd=os.path.dirname(os.path.abspath(__file__))
        )
        
        execution_time = time.time() - start_time
        
        if result.returncode == 0:
            print(f"✅ SUCCESS ({execution_time:.2f}s)")
            if result.stdout:
                print("Output:")
                print(result.stdout)
            return True
        else:
            print(f"❌ FAILED ({execution_time:.2f}s)")
            if result.stderr:
                print("Error:")
                print(result.stderr)
            if result.stdout:
                print("Output:")
                print(result.stdout)
            return False
            
    except Exception as e:
        execution_time = time.time() - start_time
        print(f"❌ EXCEPTION ({execution_time:.2f}s): {e}")
        return False


def check_dependencies():
    """Check if required dependencies are installed"""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        'pytest',
        'pytest-asyncio'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ Missing packages: {', '.join(missing_packages)}")
        print("Install with: pip install " + " ".join(missing_packages))
        return False
    
    return True


def run_unit_tests():
    """Run unit tests for secure MT5 bridge"""
    return run_command(
        "python -m pytest tests/test_secure_mt5_bridge.py -v --tb=short",
        "Running unit tests for secure MT5 bridge"
    )


def run_specific_test_class(class_name):
    """Run specific test class"""
    return run_command(
        f"python -m pytest tests/test_secure_mt5_bridge.py::{class_name} -v --tb=short",
        f"Running {class_name} tests"
    )


def run_security_tests():
    """Run security-focused tests"""
    security_classes = [
        "TestDataIntegrityValidator",
        "TestZeroHallucinationValidator",
        "TestSecurityLevels"
    ]
    
    results = []
    for class_name in security_classes:
        success = run_specific_test_class(class_name)
        results.append((class_name, success))
    
    return all(result[1] for result in results)


def run_integration_tests():
    """Run integration tests"""
    return run_command(
        "python -m pytest tests/test_secure_mt5_bridge.py::TestIntegrationScenarios -v --tb=short",
        "Running integration tests"
    )


def run_performance_tests():
    """Run performance tests"""
    return run_command(
        "python -m pytest tests/test_secure_mt5_bridge.py::TestSecureMT5Bridge::test_thread_safety -v --tb=short",
        "Running performance and thread safety tests"
    )


def run_demo():
    """Run the secure MT5 bridge demo"""
    return run_command(
        "python demo_secure_mt5_bridge.py",
        "Running secure MT5 bridge demo"
    )


def run_code_quality_checks():
    """Run code quality checks if tools are available"""
    print("\n🔍 Code Quality Checks")
    print("=" * 50)
    
    # Check if flake8 is available
    try:
        import flake8
        run_command(
            "python -m flake8 src/trading/secure_mt5_bridge.py --max-line-length=100 --ignore=E501,W503",
            "Running flake8 code style check"
        )
    except ImportError:
        print("⚠️ flake8 not available - skipping style check")
    
    # Check if mypy is available
    try:
        import mypy
        run_command(
            "python -m mypy src/trading/secure_mt5_bridge.py --ignore-missing-imports",
            "Running mypy type checking"
        )
    except ImportError:
        print("⚠️ mypy not available - skipping type check")


def generate_test_report():
    """Generate comprehensive test report"""
    return run_command(
        "python -m pytest tests/test_secure_mt5_bridge.py --tb=short --junit-xml=secure_mt5_test_report.xml",
        "Generating comprehensive test report"
    )


def run_coverage_analysis():
    """Run test coverage analysis if available"""
    try:
        import coverage
        return run_command(
            "python -m pytest tests/test_secure_mt5_bridge.py --cov=src.trading.secure_mt5_bridge --cov-report=html --cov-report=term",
            "Running test coverage analysis"
        )
    except ImportError:
        print("⚠️ coverage not available - skipping coverage analysis")
        return True


def validate_security_implementation():
    """Validate security implementation"""
    print("\n🔒 Security Implementation Validation")
    print("=" * 50)
    
    # Quick security validation
    try:
        sys.path.append(os.path.join('src', 'trading'))
        from secure_mt5_bridge import SecureMT5Bridge, SecurityLevel, TradeOrder, OrderType
        from decimal import Decimal
        
        # Test security levels
        for level in SecurityLevel:
            bridge = SecureMT5Bridge(level)
            print(f"  ✅ {level.value} security level initialized")
        
        # Test basic security features
        bridge = SecureMT5Bridge(SecurityLevel.PRODUCTION)
        
        # Test data integrity
        data_hash = bridge.data_integrity.store_price_data("TEST", {"bid": "1.0", "ask": "1.1"})
        print(f"  ✅ Data integrity hash generated: {data_hash[:16]}...")
        
        # Test order validation
        order = TradeOrder(symbol="EURUSD", order_type=OrderType.BUY, volume=Decimal('0.1'))
        is_valid, errors = bridge.order_validator.validate_order(order)
        print(f"  ✅ Order validation: {'PASSED' if is_valid else 'FAILED'}")
        
        # Test market data
        market_data = bridge.market_data.get_market_data("EURUSD")
        print(f"  ✅ Market data integrity: {'VERIFIED' if market_data.verify_integrity() else 'FAILED'}")
        
        print("  ✅ Security implementation validation completed")
        return True
        
    except Exception as e:
        print(f"  ❌ Security validation failed: {e}")
        return False


def main():
    """Main test runner"""
    print("🔒 Secure MT5 Bridge - Comprehensive Test Suite")
    print("=" * 70)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Track test results
    test_results = []
    
    # Check dependencies first
    if not check_dependencies():
        print("\n❌ Dependency check failed. Please install missing packages.")
        return 1
    
    # Validate security implementation
    security_valid = validate_security_implementation()
    test_results.append(("Security Validation", security_valid))
    
    # Run test suites
    test_suites = [
        ("Unit Tests", run_unit_tests),
        ("Security Tests", run_security_tests),
        ("Integration Tests", run_integration_tests),
        ("Performance Tests", run_performance_tests),
        ("Demo Execution", run_demo),
        ("Test Report Generation", generate_test_report),
        ("Coverage Analysis", run_coverage_analysis)
    ]
    
    for suite_name, test_function in test_suites:
        success = test_function()
        test_results.append((suite_name, success))
    
    # Run code quality checks (optional)
    run_code_quality_checks()
    
    # Summary report
    print("\n\n📊 Test Execution Summary")
    print("=" * 70)
    
    passed = 0
    failed = 0
    
    for suite_name, success in test_results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{suite_name:<25} {status}")
        if success:
            passed += 1
        else:
            failed += 1
    
    print(f"\nTotal: {len(test_results)} suites")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    
    # Security summary
    print(f"\n🔒 Security Features Tested:")
    print("   • Zero-hallucination order validation")
    print("   • Cryptographic data integrity verification")
    print("   • Multi-level security configurations")
    print("   • Comprehensive audit trails")
    print("   • Production-ready error handling")
    print("   • Thread-safe concurrent operations")
    
    if failed == 0:
        print("\n🎉 All tests passed! Secure MT5 Bridge is ready for production.")
        print("\n🚀 Key Achievements:")
        print("   ✅ Enterprise-grade security implementation")
        print("   ✅ Zero-hallucination validation system")
        print("   ✅ Cryptographic data integrity")
        print("   ✅ Comprehensive TDD test coverage")
        print("   ✅ Production-ready error handling")
        print("   ✅ Multi-level security configurations")
        return 0
    else:
        print(f"\n⚠️ {failed} test suite(s) failed. Please review the errors above.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)