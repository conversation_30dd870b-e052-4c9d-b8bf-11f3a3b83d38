import { z } from 'zod';
import { PerformanceMetricsSchema, RiskMetricsSchema, TradeExecutionSchema } from './trading.schemas';

// Strategy Configuration Schema
export const StrategyConfigSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(100),
  description: z.string().max(1000).optional(),
  type: z.enum(['technical', 'fundamental', 'ml', 'hybrid']),
  parameters: z.record(z.unknown()),
  universe: z.array(z.string().min(1)), // Trading symbols
  frequency: z.enum(['1m', '5m', '15m', '30m', '1h', '4h', '1d']),
  isActive: z.boolean(),
  createdBy: z.string().uuid(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Backtest Configuration Schema
export const BacktestConfigSchema = z.object({
  id: z.string().uuid(),
  strategyId: z.string().uuid(),
  name: z.string().min(1).max(100),
  startDate: z.date(),
  endDate: z.date(),
  initialCapital: z.number().positive(),
  commission: z.number().min(0).max(1), // Commission rate
  slippage: z.number().min(0).max(1), // Slippage rate
  benchmark: z.string().optional(), // Benchmark symbol
  riskFreeRate: z.number().optional(), // For Sharpe ratio calculation
  maxPositions: z.number().int().positive().optional(),
  positionSizing: z.enum(['fixed', 'percent', 'volatility', 'kelly']),
  positionSizeValue: z.number().positive(),
});

// Backtest Result Schema
export const BacktestResultSchema = z.object({
  id: z.string().uuid(),
  configId: z.string().uuid(),
  strategyId: z.string().uuid(),
  status: z.enum(['pending', 'running', 'completed', 'failed']),
  startedAt: z.date(),
  completedAt: z.date().optional(),
  executionTime: z.number().nonnegative().optional(), // in seconds
  performance: PerformanceMetricsSchema,
  risk: RiskMetricsSchema,
  trades: z.array(TradeExecutionSchema),
  equity: z.array(z.object({
    timestamp: z.date(),
    value: z.number(),
    drawdown: z.number().min(0).max(1),
  })),
  holdings: z.array(z.object({
    timestamp: z.date(),
    positions: z.array(z.object({
      symbol: z.string(),
      quantity: z.number(),
      price: z.number().positive(),
      value: z.number(),
    })),
  })),
  benchmarkReturns: z.array(z.object({
    timestamp: z.date(),
    return: z.number(),
  })).optional(),
  errorMessage: z.string().optional(),
});

// Backtest Request Schema
export const BacktestRequestSchema = z.object({
  strategyId: z.string().uuid(),
  config: BacktestConfigSchema.omit({ id: true, strategyId: true }),
  runAsync: z.boolean().default(true),
});

// Strategy Optimization Schema
export const StrategyOptimizationSchema = z.object({
  id: z.string().uuid(),
  strategyId: z.string().uuid(),
  optimizationTarget: z.enum(['return', 'sharpe', 'calmar', 'sortino', 'maxDrawdown']),
  parameters: z.array(z.object({
    name: z.string(),
    min: z.number(),
    max: z.number(),
    step: z.number().positive(),
    type: z.enum(['int', 'float']),
  })),
  constraints: z.array(z.object({
    parameter: z.string(),
    operator: z.enum(['>', '<', '>=', '<=', '==', '!=']),
    value: z.number(),
  })).optional(),
  maxIterations: z.number().int().positive().default(100),
  status: z.enum(['pending', 'running', 'completed', 'failed']),
  bestResult: BacktestResultSchema.optional(),
  allResults: z.array(BacktestResultSchema).optional(),
});

// Walk Forward Analysis Schema
export const WalkForwardAnalysisSchema = z.object({
  id: z.string().uuid(),
  strategyId: z.string().uuid(),
  trainingPeriod: z.number().int().positive(), // in days
  testingPeriod: z.number().int().positive(), // in days
  stepSize: z.number().int().positive(), // in days
  reoptimizationFrequency: z.number().int().positive(), // in periods
  results: z.array(z.object({
    period: z.object({
      start: z.date(),
      end: z.date(),
    }),
    inSample: BacktestResultSchema,
    outOfSample: BacktestResultSchema,
  })),
  aggregateResults: z.object({
    inSample: PerformanceMetricsSchema,
    outOfSample: PerformanceMetricsSchema,
    efficiency: z.number().min(0).max(1), // out-of-sample / in-sample performance
  }),
});

// Monte Carlo Analysis Schema
export const MonteCarloAnalysisSchema = z.object({
  id: z.string().uuid(),
  backtestId: z.string().uuid(),
  simulations: z.number().int().positive().default(1000),
  confidenceLevel: z.number().min(0.5).max(0.99).default(0.95),
  results: z.object({
    returns: z.object({
      mean: z.number(),
      std: z.number(),
      percentiles: z.record(z.number()), // e.g., "5": -0.05, "95": 0.12
    }),
    maxDrawdown: z.object({
      mean: z.number(),
      std: z.number(),
      percentiles: z.record(z.number()),
    }),
    probabilityOfLoss: z.number().min(0).max(1),
    expectedShortfall: z.number(),
    tailRisk: z.number(),
  }),
});

// Types
export type StrategyConfig = z.infer<typeof StrategyConfigSchema>;
export type BacktestConfig = z.infer<typeof BacktestConfigSchema>;
export type BacktestResult = z.infer<typeof BacktestResultSchema>;
export type BacktestRequest = z.infer<typeof BacktestRequestSchema>;
export type StrategyOptimization = z.infer<typeof StrategyOptimizationSchema>;
export type WalkForwardAnalysis = z.infer<typeof WalkForwardAnalysisSchema>;
export type MonteCarloAnalysis = z.infer<typeof MonteCarloAnalysisSchema>;