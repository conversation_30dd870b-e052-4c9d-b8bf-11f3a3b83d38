"""
Simple script to set up the database without circular imports
"""

import os
import sys
import logging
from dotenv import load_dotenv
import subprocess

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("setup_db")

# Load environment variables
load_dotenv()

def check_database_url():
    """Check if DATABASE_URL is set and valid"""
    db_url = os.getenv("DATABASE_URL")
    if not db_url:
        logger.error("DATABASE_URL environment variable is not set")
        return False
    
    if db_url.startswith("sqlite:"):
        logger.info("Using SQLite database")
        return True
    
    if db_url.startswith("postgresql:"):
        logger.info("Using PostgreSQL database")
        return True
    
    logger.error(f"Unsupported database URL: {db_url}")
    return False

def create_initial_migration():
    """Create initial migration if none exists"""
    try:
        logger.info("Checking for existing migrations...")
        current_dir = os.path.dirname(os.path.abspath(__file__))
        versions_dir = os.path.join(current_dir, "alembic", "versions")
        
        # Create versions directory if it doesn't exist
        if not os.path.exists(versions_dir):
            os.makedirs(versions_dir)
            logger.info("Created versions directory")
        
        # Check if any migration files exist
        migration_files = [f for f in os.listdir(versions_dir) if f.endswith('.py')]
        
        if not migration_files:
            logger.info("No existing migrations found. Creating initial migration...")
            # Create initial migration
            subprocess.run(
                ["alembic", "-c", os.path.join(current_dir, "alembic.ini"), "revision", "--autogenerate", "-m", "Initial migration"],
                check=True,
                cwd=current_dir
            )
            logger.info("Initial migration created")
        else:
            logger.info(f"Found {len(migration_files)} existing migrations")
        
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Error creating initial migration: {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error creating initial migration: {e}")
        return False

def run_alembic_migrations():
    """Run Alembic migrations"""
    try:
        logger.info("Running Alembic migrations...")
        # Get the current directory (where setup_db.py is located)
        current_dir = os.path.dirname(os.path.abspath(__file__))
        
        # Create initial migration if needed
        if not create_initial_migration():
            logger.error("Failed to create initial migration")
            return False
        
        # Run alembic with the correct config file
        subprocess.run(
            ["alembic", "-c", os.path.join(current_dir, "alembic.ini"), "upgrade", "head"],
            check=True,
            cwd=current_dir  # Set the working directory to backend
        )
        logger.info("Migrations completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Error running migrations: {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return False

def create_test_users():
    """Create test users directly using SQL"""
    from sqlalchemy import create_engine, text
    import sqlalchemy.exc
    
    db_url = os.getenv("DATABASE_URL")
    if not db_url:
        logger.error("DATABASE_URL not set")
        return False
    
    try:
        # Create engine
        engine = create_engine(db_url)
        
        # Create admin user
        with engine.connect() as conn:
            try:
                # Check if users table exists
                if db_url.startswith("postgresql"):
                    result = conn.execute(text("SELECT to_regclass('users')"))
                    table_exists = result.scalar()
                elif db_url.startswith("sqlite"):
                    result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='users'"))
                    table_exists = result.scalar() is not None
                else:
                    logger.error("Unsupported database type")
                    return False
                
                if not table_exists:
                    logger.warning("Users table does not exist yet - will be created by migrations")
                    return True
                
                # Check if admin user already exists
                result = conn.execute(
                    text("SELECT COUNT(*) FROM users WHERE email = :email"),
                    {"email": "<EMAIL>"}
                )
                count = result.scalar()
                
                if count == 0:
                    # Create admin user
                    # Note: In a real application, you would use a proper password hashing function
                    conn.execute(
                        text("""
                            INSERT INTO users (email, hashed_password, full_name, role, is_active)
                            VALUES (:email, :password, :name, :role, :active)
                        """),
                        {
                            "email": "<EMAIL>",
                            "password": "$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # "admin123"
                            "name": "Admin User",
                            "role": "admin",
                            "active": True
                        }
                    )
                    logger.info("Admin user created")
                else:
                    logger.info("Admin user already exists")
                
                # Check if regular user already exists
                result = conn.execute(
                    text("SELECT COUNT(*) FROM users WHERE email = :email"),
                    {"email": "<EMAIL>"}
                )
                count = result.scalar()
                
                if count == 0:
                    # Create regular user
                    conn.execute(
                        text("""
                            INSERT INTO users (email, hashed_password, full_name, role, is_active)
                            VALUES (:email, :password, :name, :role, :active)
                        """),
                        {
                            "email": "<EMAIL>",
                            "password": "$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # "user123"
                            "name": "Regular User",
                            "role": "user",
                            "active": True
                        }
                    )
                    logger.info("Regular user created")
                else:
                    logger.info("Regular user already exists")
                
                conn.commit()
            except sqlalchemy.exc.ProgrammingError as e:
                logger.warning(f"Database schema not ready yet: {e}")
                return True
        
        return True
    except Exception as e:
        logger.error(f"Error creating test users: {e}")
        return False

def main():
    """Main function"""
    # Check database URL
    if not check_database_url():
        sys.exit(1)
    
    # Run migrations
    if not run_alembic_migrations():
        sys.exit(1)
    
    # Create test users
    if not create_test_users():
        logger.warning("Failed to create test users, but continuing...")
    
    logger.info("Database setup completed successfully")

if __name__ == "__main__":
    main()