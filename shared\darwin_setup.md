# 🧬 Darwin Strategy Verification Platform - Hybrid Setup

**Professional trading strategy verification with Python backend + Web frontend**

## 🚀 Quick Start

### 1. **Create Project Directory**
```bash
mkdir darwin-platform
cd darwin-platform
```

### 2. **Save the Files**
- Save the **Python backend** as `darwin_backend.py`
- Save the **Web frontend** as `darwin_platform.html`
- Create `requirements.txt` (see below)

### 3. **Install Python Dependencies**
```bash
# Create virtual environment (recommended)
python -m venv darwin_env
source darwin_env/bin/activate  # On Windows: darwin_env\Scripts\activate

# Install required packages
pip install -r requirements.txt
```

### 4. **Start the Platform**
```bash
# Terminal 1: Start Python backend
python darwin_backend.py

# Terminal 2: Open frontend (or just double-click darwin_platform.html)
# The frontend will automatically connect to the backend
```

## 📦 Requirements.txt
```txt
Flask==2.3.3
Flask-CORS==4.0.0
pandas==2.1.0
numpy==1.24.3
yfinance==0.2.21
python-dateutil==2.8.2
requests==2.31.0
```

## 🌐 Platform Architecture

### **Python Backend (Port 5000)**
- 📊 **Real market data** from Yahoo Finance
- 🔬 **Professional backtesting** engine
- 🎲 **Monte Carlo simulations**
- 📈 **Technical indicators** (RSI, MACD, Bollinger Bands)
- ⚡ **Safe strategy execution** environment

### **Web Frontend**
- 🎨 **Modern responsive UI**
- 📱 **Cross-platform compatibility**
- 🔄 **Real-time communication** with backend
- 📊 **Interactive results dashboard**
- 📄 **Export capabilities**

## 🎯 Key Features

### **Real Market Data Integration**
- ✅ Live data from Yahoo Finance
- ✅ Multiple timeframes (1m to 1d)
- ✅ Forex, stocks, crypto support
- ✅ Technical indicators calculated server-side

### **Professional Backtesting**
- ✅ Realistic execution with spreads
- ✅ Commission costs
- ✅ Position sizing
- ✅ Stop-loss/take-profit
- ✅ Comprehensive performance metrics

### **Advanced Verification**
- ✅ Monte Carlo simulations (up to 2000)
- ✅ Statistical significance testing
- ✅ Robustness analysis
- ✅ Walk-forward validation (coming soon)

## 🔧 API Endpoints

### **Backend API** (`http://localhost:5000/api/`)

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/health` | GET | Backend health check |
| `/market-data` | POST | Download real market data |
| `/backtest` | POST | Run strategy backtest |
| `/monte-carlo` | POST | Monte Carlo analysis |
| `/technical-indicators` | POST | Calculate indicators |

### **Example API Usage**
```python
import requests

# Download market data
response = requests.post('http://localhost:5000/api/market-data', json={
    'symbol': 'AAPL',
    'timeframe': '1h',
    'period': '1y'
})

data = response.json()
print(f"Downloaded {data['dataPoints']} data points")
```

## 🛠️ Development Setup

### **VS Code Setup**
```bash
# Install Python extension
# Install REST Client extension for API testing

# Create .vscode/settings.json
{
    "python.defaultInterpreterPath": "./darwin_env/bin/python",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": true
}
```

### **Testing the API**
```bash
# Test backend health
curl http://localhost:5000/api/health

# Test market data download
curl -X POST http://localhost:5000/api/market-data \
  -H "Content-Type: application/json" \
  -d '{"symbol": "EURUSD=X", "timeframe": "1h", "period": "1y"}'
```

## 📊 Strategy Development

### **Strategy Template**
```python
def tradingStrategy(data, params):
    """
    Your trading strategy with real technical indicators
    
    Args:
        data: List of OHLCV data with calculated indicators
        params: Strategy parameters from frontend
        
    Returns:
        List of trading signals
    """
    signals = []
    
    for i in range(50, len(data)):
        current = data[i]
        
        # Access real technical indicators
        rsi = current.get('rsi', 50)
        sma_20 = current.get('sma_20')
        bb_upper = current.get('bb_upper')
        macd = current.get('macd')
        
        # Your strategy logic here
        if rsi < 30 and current['close'] < bb_lower:
            signals.append({
                'timestamp': current['timestamp'],
                'action': 'buy',
                'size': 0.1,
                'price': current['close']
            })
    
    return signals
```

## 🚀 Production Deployment

### **Backend Deployment**
```bash
# Use Gunicorn for production
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 darwin_backend:app
```

### **Frontend Deployment**
- Host `darwin_platform.html` on any web server
- Update `API_BASE` in frontend to production backend URL
- Enable HTTPS for production use

## 🔒 Security Notes

- ✅ **Safe code execution** environment for strategies
- ✅ **Input validation** on all API endpoints
- ✅ **Rate limiting** recommended for production
- ✅ **CORS** configured for cross-origin requests

## 🎯 Next Steps

1. **Start with sample data** - Test the platform with built-in examples
2. **Develop your strategy** - Use the enhanced code editor
3. **Run backtests** - Verify performance with real market data
4. **Monte Carlo analysis** - Test robustness across scenarios
5. **Export results** - Generate professional reports

## 🆘 Troubleshooting

### **Backend Connection Failed**
```bash
# Check if backend is running
curl http://localhost:5000/api/health

# Check Python dependencies
pip list | grep Flask
```

### **Market Data Download Issues**
- Verify internet connection
- Check symbol format (EURUSD=X for forex, AAPL for stocks)
- Try different time periods

### **Strategy Execution Errors**
- Ensure strategy defines `tradingStrategy` function
- Check for Python syntax errors
- Verify all required parameters are provided

## 📈 Performance Tips

- Use **vectorized operations** in strategy code
- **Limit Monte Carlo simulations** for faster testing
- **Cache market data** to avoid re-downloading
- **Use smaller timeframes** for faster backtests during development

---

**Ready to build professional trading strategies!** 🚀