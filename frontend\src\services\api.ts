/**
 * API Service - Central HTTP client with comprehensive error handling
 * Integrates with backend API and provides type-safe endpoints
 */

import axios, { AxiosInstance, AxiosError } from 'axios';
import { toast } from 'react-hot-toast';

// Import shared types from our backend
import type {
  User,
  FileUploadSession,
  Backtest,
  BacktestConfig,
  ChatSession,
  ChatMessage,
  DGMExperiment,
  WorkerStats,
  WorkerHealthCheck,
  BacktestJob,
} from '@shared/schemas';

// API Response types
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: string;
  };
  meta?: {
    total?: number;
    page?: number;
    limit?: number;
  };
}

interface LoginResponse {
  token: string;
  user: User;
  expiresAt: string;
}

interface BacktestResults {
  backtest: Backtest;
  equityCurve: Array<{
    timestamp: string;
    equity: number;
    drawdownPct?: number;
  }>;
  trades: Array<{
    id: string;
    symbol: string;
    side: 'BUY' | 'SELL';
    entryTime: string;
    exitTime?: string;
    entryPrice: number;
    exitPrice?: number;
    quantity: number;
    pnl?: number;
    pnlPct?: number;
    reasonEntry?: string;
    reasonExit?: string;
  }>;
  metrics: {
    totalReturn: number;
    annualizedReturn: number;
    maxDrawdown: number;
    sharpeRatio: number;
    winRate: number;
    avgWin: number;
    avgLoss: number;
    profitFactor: number;
  };
}

class ApiService {
  private api: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = import.meta.env.VITE_API_URL || 'http://localhost:8000';
    
    this.api = axios.create({
      baseURL: `${this.baseURL}/api/v1`,
      timeout: 60000, // 60 seconds for long-running operations
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor for auth
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error: AxiosError) => {
        this.handleApiError(error);
        return Promise.reject(error);
      }
    );
  }

  private handleApiError(error: AxiosError): void {
    if (error.response?.status === 401) {
      // Unauthorized - clear token and redirect to login
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
      return;
    }

    // Extract error message
    const errorData = error.response?.data as any;
    const message = errorData?.error?.message || 
                   errorData?.detail || 
                   error.message || 
                   'An unexpected error occurred';

    // Show toast notification
    toast.error(message);
  }

  // Auth endpoints
  async login(email: string, password: string): Promise<any> {
    try {
      // Use FormData for compatibility with FastAPI's OAuth2PasswordRequestForm
      const formData = new FormData();
      formData.append('username', email);
      formData.append('password', password);
      
      const response = await this.api.post('/auth/login', formData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });
      
      // Store the token
      if (response.data.access_token) {
        localStorage.setItem('auth_token', response.data.access_token);
      }
      
      return response.data;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  async register(email: string, password: string, fullName: string): Promise<any> {
    try {
      const response = await this.api.post('/auth/register', {
        email,
        password,
        full_name: fullName,
      });
      
      return response.data;
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  }

  async getCurrentUser(): Promise<any> {
    try {
      const response = await this.api.get('/auth/me');
      return response.data;
    } catch (error) {
      console.error('Get current user error:', error);
      throw error;
    }
  }

  async logout(): Promise<void> {
    try {
      await this.api.post('/auth/logout');
    } finally {
      localStorage.removeItem('auth_token');
    }
  }

  // Upload endpoints
  async uploadFile(file: File, userId: string): Promise<FileUploadSession> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('user_id', userId);

    const response = await this.api.post<ApiResponse<FileUploadSession>>('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 300000, // 5 minutes for file upload
    });

    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error?.message || 'File upload failed');
    }

    return response.data.data;
  }

  async confirmMapping(
    uploadSessionId: string,
    finalMapping: Record<string, string>,
    timezone: string,
    temporaryFilePath: string
  ): Promise<{ message: string }> {
    const response = await this.api.post<ApiResponse<{ message: string }>>('/upload/confirm-mapping', {
      uploadSessionId,
      finalMapping,
      timezone,
      temporaryFilePath,
    });

    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error?.message || 'Mapping confirmation failed');
    }

    return response.data.data;
  }

  async getUploadSessions(): Promise<FileUploadSession[]> {
    const response = await this.api.get<ApiResponse<FileUploadSession[]>>('/upload/sessions');
    
    if (!response.data.success) {
      throw new Error(response.data.error?.message || 'Failed to get upload sessions');
    }

    return response.data.data || [];
  }

  async getUploadSession(sessionId: string): Promise<FileUploadSession> {
    const response = await this.api.get<ApiResponse<FileUploadSession>>(`/upload/sessions/${sessionId}`);
    
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error?.message || 'Failed to get upload session');
    }

    return response.data.data;
  }

  // Chat endpoints
  async createChatSession(title?: string): Promise<ChatSession> {
    const response = await this.api.post<ApiResponse<ChatSession>>('/chat/sessions', { title });
    
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error?.message || 'Failed to create chat session');
    }

    return response.data.data;
  }

  async getChatSessions(): Promise<ChatSession[]> {
    const response = await this.api.get<ApiResponse<ChatSession[]>>('/chat/sessions');
    
    if (!response.data.success) {
      throw new Error(response.data.error?.message || 'Failed to get chat sessions');
    }

    return response.data.data || [];
  }

  async sendMessage(
    sessionId: string,
    message: string,
    models?: string[],
    requireConsensus?: boolean
  ): Promise<{
    message: string;
    consensus: any;
    tradingContext?: any;
    messageId: string;
  }> {
    const response = await this.api.post<ApiResponse<{
      message: string;
      consensus: any;
      tradingContext?: any;
      messageId: string;
    }>>(`/chat/sessions/${sessionId}/messages`, {
      message,
      models,
      requireConsensus,
    });

    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error?.message || 'Failed to send message');
    }

    return response.data.data;
  }

  async getSessionMessages(sessionId: string, limit = 50): Promise<ChatMessage[]> {
    const response = await this.api.get<ApiResponse<ChatMessage[]>>(
      `/chat/sessions/${sessionId}/messages?limit=${limit}`
    );
    
    if (!response.data.success) {
      throw new Error(response.data.error?.message || 'Failed to get messages');
    }

    return response.data.data || [];
  }

  // Backtest endpoints
  async createBacktest(config: BacktestConfig): Promise<{ backtestId: string }> {
    const response = await this.api.post<ApiResponse<{ backtestId: string }>>('/backtest', config);
    
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error?.message || 'Failed to create backtest');
    }

    return response.data.data;
  }

  async getBacktests(): Promise<Backtest[]> {
    const response = await this.api.get<ApiResponse<Backtest[]>>('/backtest');
    
    if (!response.data.success) {
      throw new Error(response.data.error?.message || 'Failed to get backtests');
    }

    return response.data.data || [];
  }

  async getBacktest(backtestId: string): Promise<Backtest> {
    const response = await this.api.get<ApiResponse<Backtest>>(`/backtest/${backtestId}`);
    
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error?.message || 'Failed to get backtest');
    }

    return response.data.data;
  }

  async getBacktestResults(backtestId: string): Promise<BacktestResults> {
    const response = await this.api.get<ApiResponse<BacktestResults>>(`/backtest/${backtestId}/results`);
    
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error?.message || 'Failed to get backtest results');
    }

    return response.data.data;
  }

  async cancelBacktest(backtestId: string): Promise<void> {
    const response = await this.api.delete<ApiResponse<void>>(`/backtest/${backtestId}`);
    
    if (!response.data.success) {
      throw new Error(response.data.error?.message || 'Failed to cancel backtest');
    }
  }

  // DGM endpoints
  async createDGMExperiment(
    name: string,
    baseStrategy: Record<string, any>
  ): Promise<{ experimentId: string }> {
    const response = await this.api.post<ApiResponse<{ experimentId: string }>>('/dgm/experiments', {
      experimentName: name,
      baseStrategy,
    });
    
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error?.message || 'Failed to create DGM experiment');
    }

    return response.data.data;
  }

  async getDGMExperiments(): Promise<DGMExperiment[]> {
    const response = await this.api.get<ApiResponse<DGMExperiment[]>>('/dgm/experiments');
    
    if (!response.data.success) {
      throw new Error(response.data.error?.message || 'Failed to get DGM experiments');
    }

    return response.data.data || [];
  }

  async getDGMExperiment(experimentId: string): Promise<DGMExperiment> {
    const response = await this.api.get<ApiResponse<DGMExperiment>>(`/dgm/experiments/${experimentId}`);
    
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error?.message || 'Failed to get DGM experiment');
    }

    return response.data.data;
  }

  async getBestStrategies(): Promise<any[]> {
    const response = await this.api.get<ApiResponse<any[]>>('/dgm/best-strategies');
    
    if (!response.data.success) {
      throw new Error(response.data.error?.message || 'Failed to get best strategies');
    }

    return response.data.data || [];
  }
  
  // Strategy Management
  async getStrategies(): Promise<any[]> {
    try {
      const response = await this.api.get('/strategies');
      return response.data;
    } catch (error) {
      console.error('Error getting strategies:', error);
      throw error;
    }
  }

  async getStrategy(strategyId: string): Promise<any> {
    try {
      const response = await this.api.get(`/strategies/${strategyId}`);
      return response.data;
    } catch (error) {
      console.error('Error getting strategy:', error);
      throw error;
    }
  }

  async createStrategy(strategyData: {
    name: string;
    description: string;
    config: any;
  }): Promise<any> {
    try {
      const response = await this.api.post('/strategies', strategyData);
      return response.data;
    } catch (error) {
      console.error('Error creating strategy:', error);
      throw error;
    }
  }

  async updateStrategy(strategyId: string, strategyData: any): Promise<any> {
    try {
      const response = await this.api.put(`/strategies/${strategyId}`, strategyData);
      return response.data;
    } catch (error) {
      console.error('Error updating strategy:', error);
      throw error;
    }
  }

  async deleteStrategy(strategyId: string): Promise<any> {
    try {
      const response = await this.api.delete(`/strategies/${strategyId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting strategy:', error);
      throw error;
    }
  }

  // Portfolio endpoints
  async getPortfolioOverview(): Promise<any> {
    const response = await this.api.get<ApiResponse<any>>('/portfolio/overview');
    
    if (!response.data.success) {
      throw new Error(response.data.error?.message || 'Failed to get portfolio overview');
    }

    return response.data.data;
  }

  async getPerformanceMetrics(period = '1M'): Promise<any> {
    const response = await this.api.get<ApiResponse<any>>(`/portfolio/metrics?period=${period}`);
    
    if (!response.data.success) {
      throw new Error(response.data.error?.message || 'Failed to get performance metrics');
    }

    return response.data.data;
  }

  // Worker management endpoints
  async getWorkerStatus(): Promise<WorkerStats> {
    const response = await this.api.get<ApiResponse<WorkerStats>>('/workers/status');
    
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error?.message || 'Failed to get worker status');
    }

    return response.data.data;
  }

  async getWorkerHealth(): Promise<WorkerHealthCheck> {
    const response = await this.api.get<ApiResponse<WorkerHealthCheck>>('/workers/health');
    
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error?.message || 'Failed to get worker health');
    }

    return response.data.data;
  }

  async restartWorker(workerName: string): Promise<void> {
    const response = await this.api.post<ApiResponse<void>>(`/workers/${workerName}/restart`);
    
    if (!response.data.success) {
      throw new Error(response.data.error?.message || 'Failed to restart worker');
    }
  }

  // MT5 Account Management
  async getMT5Accounts(): Promise<any[]> {
    try {
      const response = await this.api.get('/mt5/accounts');
      return response.data;
    } catch (error) {
      console.error('Error getting MT5 accounts:', error);
      throw error;
    }
  }

  async getMT5Account(accountId: string): Promise<any> {
    try {
      const response = await this.api.get(`/mt5/accounts/${accountId}`);
      return response.data;
    } catch (error) {
      console.error('Error getting MT5 account:', error);
      throw error;
    }
  }

  async createMT5Account(accountData: {
    name: string;
    server: string;
    login: string;
    password: string;
    is_demo: boolean;
  }): Promise<any> {
    try {
      const response = await this.api.post('/mt5/accounts', accountData);
      return response.data;
    } catch (error) {
      console.error('Error creating MT5 account:', error);
      throw error;
    }
  }

  async deleteMT5Account(accountId: string): Promise<any> {
    try {
      const response = await this.api.delete(`/mt5/accounts/${accountId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting MT5 account:', error);
      throw error;
    }
  }

  // MT5 Bridge endpoints
  async getMT5Status(): Promise<any> {
    try {
      const response = await this.api.get('/mt5/status');
      return response.data;
    } catch (error) {
      console.error('Error getting MT5 status:', error);
      throw error;
    }
  }

  async connectMT5(): Promise<{ success: boolean }> {
    const response = await this.api.post<ApiResponse<{ success: boolean }>>('/mt5/connect');
    
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error?.message || 'Failed to connect to MT5');
    }

    return response.data.data;
  }

  async disconnectMT5(): Promise<{ success: boolean }> {
    const response = await this.api.post<ApiResponse<{ success: boolean }>>('/mt5/disconnect');
    
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error?.message || 'Failed to disconnect from MT5');
    }

    return response.data.data;
  }

  async placeMT5Order(orderParams: {
    symbol: string;
    orderType: string;
    volume: number;
    price?: number;
    stopLoss?: number;
    takeProfit?: number;
  }): Promise<{ orderId: number }> {
    const response = await this.api.post<ApiResponse<{ orderId: number }>>('/mt5/order', orderParams);
    
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error?.message || 'Failed to place MT5 order');
    }

    return response.data.data;
  }

  async getMT5Positions(): Promise<any[]> {
    const response = await this.api.get<ApiResponse<any[]>>('/mt5/positions');
    
    if (!response.data.success) {
      throw new Error(response.data.error?.message || 'Failed to get MT5 positions');
    }

    return response.data.data || [];
  }

  async closeMT5Position(orderId: number): Promise<{ success: boolean }> {
    const response = await this.api.post<ApiResponse<{ success: boolean }>>(`/mt5/positions/${orderId}/close`);
    
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error?.message || 'Failed to close MT5 position');
    }

    return response.data.data;
  }

  async getRunningJobs(): Promise<{
    uploads: FileUploadSession[];
    backtests: BacktestJob[];
    dgmExperiments: DGMExperiment[];
  }> {
    const response = await this.api.get<ApiResponse<{
      uploads: FileUploadSession[];
      backtests: BacktestJob[];
      dgmExperiments: DGMExperiment[];
    }>>('/workers/jobs');
    
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error?.message || 'Failed to get running jobs');
    }

    return response.data.data;
  }

  // Utility methods
  getBaseURL(): string {
    return this.baseURL;
  }

  isAuthenticated(): boolean {
    return !!localStorage.getItem('auth_token');
  }

  getAuthToken(): string | null {
    return localStorage.getItem('auth_token');
  }

  setAuthToken(token: string): void {
    localStorage.setItem('auth_token', token);
  }

  clearAuthToken(): void {
    localStorage.removeItem('auth_token');
  }
}

// Export singleton instance
export const apiService = new ApiService();
export type { ApiResponse, LoginResponse, BacktestResults };