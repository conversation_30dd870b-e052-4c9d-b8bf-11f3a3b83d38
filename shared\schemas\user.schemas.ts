/**
 * User schemas - re-exports from auth.schemas.ts for compatibility
 * This maintains backward compatibility while centralizing user-related schemas
 */

export {
  // Core schemas
  UserSchema,
  UserIdSchema,
  SubscriptionTierSchema,
  UserWithPasswordHashSchema,
  UserProfileSchema,
  
  // Request/Response schemas
  CreateUserRequestSchema,
  UpdateUserProfileRequestSchema,
  
  // Types
  type User,
  type UserId,
  type SubscriptionTier,
  type UserWithPasswordHash,
  type UserProfile,
  type CreateUserRequest,
  type UpdateUserProfileRequest,
} from './auth.schemas';