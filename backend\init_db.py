"""
Database initialization script
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables
load_dotenv()

# Import database modules
from app.db.config import engine, Base, SessionLocal
from app.db.models import User, MT5Account, Position, Order, Strategy, Backtest, ApiKey
from app.auth.auth_handler import get_password_hash

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("init_db")

def create_tables():
    """Create database tables"""
    try:
        # Drop all tables if they exist
        Base.metadata.drop_all(engine)
        
        # Create all tables
        Base.metadata.create_all(engine)
        
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Error creating database tables: {str(e)}")
        raise

def create_initial_data():
    """Create initial data"""
    try:
        # Create session
        session = SessionLocal()
        
        # Create admin user
        admin_user = User(
            email="<EMAIL>",
            hashed_password=get_password_hash("admin123"),
            full_name="Admin User",
            role="admin"
        )
        session.add(admin_user)
        
        # Create regular user
        regular_user = User(
            email="<EMAIL>",
            hashed_password=get_password_hash("user123"),
            full_name="Regular User",
            role="user"
        )
        session.add(regular_user)
        
        # Commit the session
        session.commit()
        session.close()
        
        logger.info("Initial data created successfully")
    except Exception as e:
        logger.error(f"Error creating initial data: {str(e)}")
        raise

def main():
    """Main function"""
    try:
        # Create tables
        create_tables()
        
        # Create initial data
        create_initial_data()
        
        logger.info("Database initialization completed successfully")
    except Exception as e:
        logger.error(f"Database initialization failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()