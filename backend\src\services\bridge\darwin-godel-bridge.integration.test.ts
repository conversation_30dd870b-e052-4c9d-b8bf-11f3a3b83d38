// darwin-godel-bridge.integration.test.ts
// Integration tests for Darwin Gödel Bridge Service

import { DarwinGodelBridgeService, DarwinGodelRequest } from './darwin-godel-bridge.service';
import { PythonEngineService } from './python-engine.service';

describe('DarwinGodelBridgeService Integration Tests', () => {
  let bridgeService: DarwinGodelBridgeService;
  let pythonEngine: PythonEngineService;

  beforeAll(async () => {
    // Mock Python engine for testing
    pythonEngine = {
      executePythonScript: jest.fn(),
      healthCheck: jest.fn().mockResolvedValue({ status: 'healthy' })
    } as any;

    bridgeService = new DarwinGodelBridgeService(pythonEngine);
    await bridgeService.initialize();
  });

  afterAll(async () => {
    await bridgeService.cleanup();
  });

  describe('Chat Request Processing', () => {
    it('should process natural language trading queries', async () => {
      const request: DarwinGodelRequest = {
        type: 'chat',
        data: {
          message: 'Analyze EUR/USD 4H chart',
          userId: 'test-user'
        }
      };

      const response = await bridgeService.processRequest(request);

      expect(response.success).toBe(true);
      expect(response.data).toHaveProperty('query');
      expect(response.data.query.type).toBe('analysis');
      expect(response.data.query.pair).toBe('EURUSD');
      expect(response.data.query.timeframe).toBe('4H');
    });

    it('should handle market scanning requests', async () => {
      const request: DarwinGodelRequest = {
        type: 'chat',
        data: {
          message: 'Scan all major pairs for bullish divergence',
          userId: 'test-user'
        }
      };

      const response = await bridgeService.processRequest(request);

      expect(response.success).toBe(true);
      expect(response.data.query.type).toBe('scan');
      expect(response.data).toHaveProperty('scanResults');
    });

    it('should handle indicator explanation requests', async () => {
      const request: DarwinGodelRequest = {
        type: 'chat',
        data: {
          message: 'Explain RSI indicator for EUR/USD',
          userId: 'test-user'
        }
      };

      const response = await bridgeService.processRequest(request);

      expect(response.success).toBe(true);
      expect(response.data.query.type).toBe('indicator');
      expect(response.data).toHaveProperty('explanation');
    });
  });

  describe('Evolution Request Processing', () => {
    it('should start evolution jobs', async () => {
      // Mock Python script execution
      (pythonEngine.executePythonScript as jest.Mock).mockResolvedValue(
        JSON.stringify({
          success: true,
          job_id: 'test-job-123',
          pair: 'EURUSD',
          timeframe: '4H',
          status: 'started'
        })
      );

      const request: DarwinGodelRequest = {
        type: 'evolve',
        data: {
          pair: 'EURUSD',
          timeframe: '4H',
          generations: 20,
          populationSize: 50,
          fitnessGoal: 'sharpe',
          userId: 'test-user'
        }
      };

      const response = await bridgeService.processRequest(request);

      expect(response.success).toBe(true);
      expect(response.data.success).toBe(true);
      expect(response.data.jobId).toBeDefined();
      expect(pythonEngine.executePythonScript).toHaveBeenCalled();
    });

    it('should handle evolution job failures gracefully', async () => {
      // Mock Python script failure
      (pythonEngine.executePythonScript as jest.Mock).mockRejectedValue(
        new Error('Python execution failed')
      );

      const request: DarwinGodelRequest = {
        type: 'evolve',
        data: {
          pair: 'GBPUSD',
          timeframe: '1H',
          userId: 'test-user'
        }
      };

      const response = await bridgeService.processRequest(request);

      expect(response.success).toBe(true); // Bridge handles the error gracefully
      expect(response.data.success).toBe(false);
      expect(response.data.error).toBeDefined();
    });
  });

  describe('Strategy Verification', () => {
    it('should verify trading strategies', async () => {
      const strategy = {
        id: 'test-strategy-001',
        name: 'RSI Mean Reversion',
        description: 'Buy when RSI < 30, sell when RSI > 70',
        conditions: [
          {
            indicator: 'RSI',
            operator: '<' as const,
            value: 30,
            period: 14
          }
        ],
        action: 'buy' as const,
        riskManagement: {
          stopLossPct: 2.0,
          takeProfitPct: 4.0,
          positionSizePct: 1.0
        }
      };

      const request: DarwinGodelRequest = {
        type: 'verify',
        data: {
          strategy,
          pair: 'EURUSD',
          userId: 'test-user'
        }
      };

      const response = await bridgeService.processRequest(request);

      expect(response.success).toBe(true);
      expect(response.data).toHaveProperty('verification');
      expect(response.data.strategy).toEqual(strategy);
      expect(response.data.pair).toBe('EURUSD');
    });

    it('should handle verification errors', async () => {
      const invalidStrategy = {
        id: 'invalid-strategy',
        name: 'Invalid Strategy',
        description: 'This strategy has invalid conditions',
        conditions: [], // Empty conditions should cause validation error
        action: 'buy' as const,
        riskManagement: {
          stopLossPct: 2.0,
          takeProfitPct: 4.0,
          positionSizePct: 1.0
        }
      };

      const request: DarwinGodelRequest = {
        type: 'verify',
        data: {
          strategy: invalidStrategy,
          pair: 'EURUSD',
          userId: 'test-user'
        }
      };

      const response = await bridgeService.processRequest(request);

      // Should handle gracefully even with invalid strategy
      expect(response.success).toBeDefined();
    });
  });

  describe('Analysis Request Processing', () => {
    it('should process technical analysis requests', async () => {
      const request: DarwinGodelRequest = {
        type: 'analyze',
        data: {
          pair: 'EURUSD',
          timeframe: '4H',
          analysisType: 'technical',
          userId: 'test-user'
        }
      };

      const response = await bridgeService.processRequest(request);

      expect(response.success).toBe(true);
      expect(response.data).toHaveProperty('pair');
      expect(response.data).toHaveProperty('timeframe');
    });

    it('should get forex genome data', async () => {
      const request: DarwinGodelRequest = {
        type: 'analyze',
        data: {
          pair: 'EURUSD',
          timeframe: '4H',
          analysisType: 'genome',
          userId: 'test-user'
        }
      };

      const response = await bridgeService.processRequest(request);

      expect(response.success).toBe(true);
      expect(response.data).toHaveProperty('sessionPatterns');
      expect(response.data).toHaveProperty('volatilityProfile');
      expect(response.data).toHaveProperty('confidenceScore');
    });

    it('should get proven strategies', async () => {
      const request: DarwinGodelRequest = {
        type: 'analyze',
        data: {
          pair: 'EURUSD',
          analysisType: 'strategies',
          userId: 'test-user'
        }
      };

      const response = await bridgeService.processRequest(request);

      expect(response.success).toBe(true);
      expect(Array.isArray(response.data)).toBe(true);
      if (response.data.length > 0) {
        expect(response.data[0]).toHaveProperty('id');
        expect(response.data[0]).toHaveProperty('name');
        expect(response.data[0]).toHaveProperty('fitnessScore');
      }
    });
  });

  describe('Job Management', () => {
    it('should track active jobs', async () => {
      // Mock successful evolution job
      (pythonEngine.executePythonScript as jest.Mock).mockResolvedValue(
        JSON.stringify({
          success: true,
          job_id: 'test-job-456',
          pair: 'GBPUSD',
          timeframe: '1H'
        })
      );

      const request: DarwinGodelRequest = {
        type: 'evolve',
        data: {
          pair: 'GBPUSD',
          timeframe: '1H',
          userId: 'test-user'
        }
      };

      const response = await bridgeService.processRequest(request);
      const jobId = response.data.jobId;

      // Check job status
      const jobStatus = await bridgeService.getJobStatus(jobId);
      expect(jobStatus).toBeDefined();
      expect(jobStatus.userId).toBe('test-user');

      // Get all active jobs for user
      const userJobs = await bridgeService.getAllActiveJobs('test-user');
      expect(Array.isArray(userJobs)).toBe(true);
      expect(userJobs.some(job => job.jobId === jobId)).toBe(true);
    });

    it('should cancel jobs', async () => {
      // Mock job creation
      (pythonEngine.executePythonScript as jest.Mock).mockResolvedValue(
        JSON.stringify({
          success: true,
          job_id: 'test-job-789',
          pair: 'USDJPY',
          timeframe: '4H'
        })
      );

      const request: DarwinGodelRequest = {
        type: 'evolve',
        data: {
          pair: 'USDJPY',
          timeframe: '4H',
          userId: 'test-user'
        }
      };

      const response = await bridgeService.processRequest(request);
      const jobId = response.data.jobId;

      // Cancel the job
      const cancelled = await bridgeService.cancelJob(jobId);
      expect(cancelled).toBe(true);

      // Check job status
      const jobStatus = await bridgeService.getJobStatus(jobId);
      expect(jobStatus.status).toBe('cancelled');
    });
  });

  describe('Health Check', () => {
    it('should perform health checks', async () => {
      const health = await bridgeService.healthCheck();

      expect(health).toHaveProperty('status');
      expect(health).toHaveProperty('details');
      expect(health.details).toHaveProperty('pythonEngine');
      expect(health.details).toHaveProperty('s3Core');
      expect(health.details).toHaveProperty('verificationEngine');
    });
  });

  describe('Error Handling', () => {
    it('should handle unknown request types', async () => {
      const request: DarwinGodelRequest = {
        type: 'unknown' as any,
        data: {},
        userId: 'test-user'
      };

      const response = await bridgeService.processRequest(request);

      expect(response.success).toBe(false);
      expect(response.error).toContain('Unknown request type');
    });

    it('should handle malformed requests gracefully', async () => {
      const request: DarwinGodelRequest = {
        type: 'chat',
        data: null as any,
        userId: 'test-user'
      };

      const response = await bridgeService.processRequest(request);

      expect(response.success).toBe(false);
      expect(response.error).toBeDefined();
    });

    it('should measure processing time', async () => {
      const request: DarwinGodelRequest = {
        type: 'chat',
        data: {
          message: 'Quick test message',
          userId: 'test-user'
        }
      };

      const response = await bridgeService.processRequest(request);

      expect(response.processingTime).toBeGreaterThan(0);
      expect(typeof response.processingTime).toBe('number');
    });
  });

  describe('Integration with S3 Core', () => {
    it('should translate complex trading queries', async () => {
      const complexQuery = 'Find me all EUR pairs with RSI below 30 and MACD showing bullish divergence on 4H timeframe';
      
      const request: DarwinGodelRequest = {
        type: 'chat',
        data: {
          message: complexQuery,
          userId: 'test-user'
        }
      };

      const response = await bridgeService.processRequest(request);

      expect(response.success).toBe(true);
      expect(response.data.query).toBeDefined();
      expect(response.data.query.confidence).toBeGreaterThan(0);
    });

    it('should handle evolution trigger from chat', async () => {
      // Mock Python execution for evolution
      (pythonEngine.executePythonScript as jest.Mock).mockResolvedValue(
        JSON.stringify({
          success: true,
          job_id: 'evolution-from-chat',
          pair: 'EURUSD',
          timeframe: '4H'
        })
      );

      const request: DarwinGodelRequest = {
        type: 'chat',
        data: {
          message: 'Evolve strategies for EUR/USD 4H with 30 generations',
          userId: 'test-user'
        }
      };

      const response = await bridgeService.processRequest(request);

      expect(response.success).toBe(true);
      expect(response.data.query.type).toBe('strategy');
      expect(response.data).toHaveProperty('evolutionJob');
    });
  });
});

// Mock implementations for testing
jest.mock('../s3-core-nlp-engine', () => {
  return {
    __esModule: true,
    default: jest.fn().mockImplementation(() => ({
      translateQuery: jest.fn().mockImplementation((message) => {
        // Simple mock translation logic
        if (message.includes('EUR/USD') || message.includes('EURUSD')) {
          return Promise.resolve({
            type: message.includes('scan') ? 'scan' : 
                  message.includes('RSI') ? 'indicator' :
                  message.includes('evolve') ? 'strategy' : 'analysis',
            pair: 'EURUSD',
            timeframe: message.includes('4H') ? '4H' : '1H',
            indicators: message.includes('RSI') ? ['RSI'] : [],
            confidence: 0.85,
            reasoning: 'Mock translation'
          });
        }
        return Promise.resolve({
          type: 'general',
          confidence: 0.6,
          reasoning: 'Mock general query'
        });
      }),
      generateAnalysis: jest.fn().mockResolvedValue({
        pair: 'EURUSD',
        timeframe: '4H',
        analysis: {
          trend: 'bullish',
          strength: 0.75,
          key_levels: {
            support: [1.0850, 1.0820],
            resistance: [1.0920, 1.0950]
          },
          indicators: {
            RSI: { value: 65, signal: 'neutral' }
          },
          signals: []
        },
        recommendation: {
          action: 'buy',
          confidence: 0.7,
          reasoning: 'Mock analysis',
          risk_level: 'medium'
        }
      }),
      scanMarkets: jest.fn().mockResolvedValue([
        { pair: 'EURUSD', signal: 'Bullish divergence', strength: 0.8 }
      ]),
      explainIndicator: jest.fn().mockResolvedValue('Mock indicator explanation'),
      validateStrategy: jest.fn().mockResolvedValue({
        isValid: true,
        confidence: 0.9,
        suggestions: [],
        riskAssessment: 'Low risk'
      })
    }))
  };
});

jest.mock('../strategy-verification-engine', () => {
  return {
    __esModule: true,
    default: jest.fn().mockImplementation(() => ({
      initialize: jest.fn().mockResolvedValue(undefined),
      verifyStrategy: jest.fn().mockResolvedValue({
        verified: true,
        theorem: 'Mock theorem',
        proofSteps: ['Step 1', 'Step 2'],
        errors: [],
        verificationTime: 1500,
        confidence: 0.95
      }),
      getVerificationStats: jest.fn().mockResolvedValue({
        totalVerifications: 10,
        successRate: 0.9,
        averageVerificationTime: 2000
      }),
      cleanup: jest.fn().mockResolvedValue(undefined)
    }))
  };
});