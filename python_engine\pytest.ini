[tool:pytest]
# Pytest configuration for Python AI Trading Engine tests
# Mirrors the Jest configuration for consistent testing

# Test discovery
testpaths = tests services/darwin_godel/__tests__ tests/performance tests/property_based
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 6.0

# Add options for all test runs
addopts = 
    # Verbose output
    -v
    # Show local variables in tracebacks
    --tb=short
    # Show capture output
    -s
    # Fail on first failure for integration tests
    --maxfail=1
    # Show slowest tests
    --durations=10
    # Generate coverage report
    --cov=python_engine
    --cov-report=html:test-results/coverage/html
    --cov-report=xml:test-results/coverage/coverage.xml
    --cov-report=term-missing
    # Generate JUnit XML for CI integration
    --junit-xml=test-results/junit.xml
    # Generate HTML report
    --html=test-results/report.html
    --self-contained-html
    # Parallel execution (use with caution for integration tests)
    -n auto
    # Mark slow tests
    --strict-markers

# Markers for organizing tests (matching Jest describe blocks)
markers =
    unit: Unit tests (fast, isolated)
    integration: Integration tests (slower, with external dependencies)
    e2e: End-to-end tests (slowest, full system)
    trading: Trading-related tests
    backtest: Backtest-related tests
    chat: Chat/AI-related tests
    performance: Performance benchmarks
    property: Property-based tests using Hypothesis
    slow: Slow tests (over 1 second)
    network: Tests requiring network access
    mt5: Tests requiring MT5 connection
    benchmark: Benchmark tests for performance measurement
    load: Load testing with high concurrency
    
# Test timeouts
timeout = 300
timeout_method = thread

# Filtering options
filterwarnings =
    # Ignore specific warnings
    ignore::UserWarning
    ignore::DeprecationWarning
    # Treat specific warnings as errors
    error::RuntimeWarning
    
# Log configuration
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Asyncio configuration
asyncio_mode = auto

# Coverage configuration
[coverage:run]
source = python_engine
omit = 
    */tests/*
    */test_*
    */__pycache__/*
    */venv/*
    */env/*
    .venv/*
    setup.py
    
[coverage:report]
exclude_lines =
    # Have to re-enable the standard pragma
    pragma: no cover
    
    # Don't complain about missing debug-only code:
    def __repr__
    if self\.debug
    
    # Don't complain if tests don't hit defensive assertion code:
    raise AssertionError
    raise NotImplementedError
    
    # Don't complain if non-runnable code isn't run:
    if 0:
    if __name__ == .__main__.:
    
    # Don't complain about abstract methods
    @abstractmethod
    
precision = 2
show_missing = true

[coverage:html]
directory = test-results/coverage/html