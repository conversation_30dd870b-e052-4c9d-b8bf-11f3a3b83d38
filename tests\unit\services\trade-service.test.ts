// tests/unit/services/trade-service.test.ts
import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { TestFactory } from '../../helpers/test-factory';

// Mock implementations for services that don't exist yet
class TradeService {
  constructor(
    private tradeRepository: any,
    private eventBus: any,
    private marketDataService: any
  ) {}

  async executeTrade(trade: any) {
    // Validate market is open
    const isMarketOpen = await this.marketDataService.validateTradeability(trade.symbol);
    if (!isMarketOpen) {
      const failedTrade = await this.tradeRepository.update({ ...trade, status: 'failed' });
      await this.eventBus.publish('trade.failed', {
        tradeId: trade.id,
        reason: 'Market closed'
      });
      return failedTrade;
    }

    // Get current price
    const currentPrice = await this.marketDataService.getCurrentPrice(trade.symbol);
    
    // Execute trade
    const executedTrade = await this.tradeRepository.update({ ...trade, status: 'executed' });
    
    // Publish event
    await this.eventBus.publish('trade.executed', {
      tradeId: trade.id,
      userId: trade.userId,
      symbol: trade.symbol,
      executedPrice: currentPrice,
    });

    return executedTrade;
  }
}

describe('TradeService', () => {
  let tradeService: TradeService;
  let tradeRepository: jest.Mocked<any>;
  let eventBus: jest.Mocked<any>;
  let marketDataService: jest.Mocked<any>;

  beforeEach(() => {
    // Create mocks
    tradeRepository = {
      create: jest.fn(),
      findById: jest.fn(),
      update: jest.fn(),
      findByUserId: jest.fn(),
    };

    eventBus = {
      publish: jest.fn(),
      subscribe: jest.fn(),
    };

    marketDataService = {
      getCurrentPrice: jest.fn(),
      validateTradeability: jest.fn(),
    };

    tradeService = new TradeService(tradeRepository, eventBus, marketDataService);
  });

  describe('executeTrade', () => {
    it('should execute a valid trade successfully', async () => {
      // Arrange
      const trade = TestFactory.createTrade({ status: 'pending' });
      const currentPrice = 150.50;
      
      marketDataService.validateTradeability.mockResolvedValue(true);
      marketDataService.getCurrentPrice.mockResolvedValue(currentPrice);
      tradeRepository.create.mockResolvedValue(trade);
      tradeRepository.update.mockResolvedValue({ ...trade, status: 'executed' });

      // Act
      const result = await tradeService.executeTrade(trade);

      // Assert
      expect(result.status).toBe('executed');
      expect(marketDataService.validateTradeability).toHaveBeenCalledWith(trade.symbol);
      expect(eventBus.publish).toHaveBeenCalledWith('trade.executed', {
        tradeId: trade.id,
        userId: trade.userId,
        symbol: trade.symbol,
        executedPrice: currentPrice,
      });
    });

    it('should fail trade when market is closed', async () => {
      // Arrange
      const trade = TestFactory.createTrade({ status: 'pending' });
      
      marketDataService.validateTradeability.mockResolvedValue(false);
      tradeRepository.create.mockResolvedValue(trade);
      tradeRepository.update.mockResolvedValue({ ...trade, status: 'failed' });

      // Act
      const result = await tradeService.executeTrade(trade);

      // Assert
      expect(result.status).toBe('failed');
      expect(marketDataService.getCurrentPrice).not.toHaveBeenCalled();
      expect(eventBus.publish).toHaveBeenCalledWith('trade.failed', {
        tradeId: trade.id,
        reason: 'Market closed',
      });
    });
  });
});