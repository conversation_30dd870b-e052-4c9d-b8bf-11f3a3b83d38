"""
Strategy Assistant - Integrated Helper System
Combines AI prompts and quantitative strategies for comprehensive trading support
"""

from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import json
import logging
from datetime import datetime

from .ai_trading_prompts import AITradingPromptsLibrary, PromptCategory
from .quant_strategies import QuantStrategiesLibrary, StrategyType, TimeFrame

logger = logging.getLogger(__name__)

@dataclass
class StrategyRecommendation:
    """Comprehensive strategy recommendation"""
    name: str
    type: str  # "ai_prompt" or "quant_strategy"
    description: str
    complexity: str
    expected_performance: Dict[str, float]
    requirements: List[str]
    implementation_guide: str
    ai_prompts: List[str]
    code_example: Optional[str] = None

class StrategyAssistant:
    """
    Comprehensive strategy assistant that combines AI prompts with quantitative strategies
    Provides intelligent recommendations and implementation guidance
    """
    
    def __init__(self):
        self.ai_prompts = AITradingPromptsLibrary()
        self.quant_strategies = QuantStrategiesLibrary()
        self.user_preferences = {}
        self.strategy_history = []
    
    def get_comprehensive_recommendations(self, 
                                        user_profile: Dict[str, Any]) -> List[StrategyRecommendation]:
        """
        Get comprehensive strategy recommendations based on user profile
        
        Args:
            user_profile: {
                'experience_level': 'beginner'|'intermediate'|'advanced',
                'asset_classes': ['forex', 'stocks', 'crypto'],
                'timeframes': ['1h', '4h', '1d'],
                'risk_tolerance': 'low'|'medium'|'high',
                'capital': float,
                'goals': ['income', 'growth', 'learning']
            }
        """
        recommendations = []
        
        # Get quantitative strategy recommendations
        for asset_class in user_profile.get('asset_classes', ['forex']):
            for timeframe_str in user_profile.get('timeframes', ['1h']):
                try:
                    timeframe = TimeFrame(timeframe_str)
                    quant_recs = self.quant_strategies.get_strategy_recommendations(
                        asset_class=asset_class,
                        timeframe=timeframe,
                        complexity=user_profile.get('experience_level', 'beginner')
                    )
                    
                    for rec in quant_recs[:2]:  # Top 2 per asset/timeframe
                        strategy_rec = self._create_quant_recommendation(rec, user_profile)
                        recommendations.append(strategy_rec)
                        
                except ValueError:
                    continue
        
        # Add AI-assisted strategy development recommendations
        ai_recs = self._get_ai_strategy_recommendations(user_profile)
        recommendations.extend(ai_recs)
        
        # Sort by suitability score
        recommendations.sort(key=lambda x: self._calculate_suitability_score(x, user_profile), reverse=True)
        
        return recommendations[:10]  # Top 10 recommendations
    
    def _create_quant_recommendation(self, quant_rec: Dict[str, Any], 
                                   user_profile: Dict[str, Any]) -> StrategyRecommendation:
        """Create recommendation from quantitative strategy"""
        metadata = quant_rec['metadata']
        
        # Get relevant AI prompts for this strategy
        relevant_prompts = self._get_relevant_prompts(metadata.strategy_type)
        
        # Create implementation guide
        implementation_guide = self._create_implementation_guide(metadata, user_profile)
        
        # Get code example
        code_example = self._get_strategy_code_example(quant_rec['name'])
        
        return StrategyRecommendation(
            name=metadata.name,
            type="quant_strategy",
            description=metadata.description,
            complexity=metadata.complexity,
            expected_performance={
                'sharpe_ratio': metadata.expected_sharpe,
                'max_drawdown': metadata.max_drawdown,
                'win_rate': metadata.win_rate
            },
            requirements=metadata.requirements,
            implementation_guide=implementation_guide,
            ai_prompts=relevant_prompts,
            code_example=code_example
        )
    
    def _get_ai_strategy_recommendations(self, user_profile: Dict[str, Any]) -> List[StrategyRecommendation]:
        """Get AI-assisted strategy development recommendations"""
        recommendations = []
        
        # Custom strategy development using AI
        if user_profile.get('experience_level') in ['intermediate', 'advanced']:
            custom_strategy_rec = StrategyRecommendation(
                name="AI-Assisted Custom Strategy Development",
                type="ai_prompt",
                description="Develop a custom trading strategy using AI guidance and analysis",
                complexity=user_profile.get('experience_level', 'intermediate'),
                expected_performance={
                    'sharpe_ratio': 1.0,  # Variable based on development
                    'max_drawdown': 0.15,
                    'win_rate': 0.50
                },
                requirements=["Market data", "AI assistant", "Backtesting platform"],
                implementation_guide=self._create_ai_strategy_guide(user_profile),
                ai_prompts=[
                    "market_scanner",
                    "technical_analyzer", 
                    "strategy_educator",
                    "backtest_analyzer"
                ]
            )
            recommendations.append(custom_strategy_rec)
        
        # Market analysis and research
        research_rec = StrategyRecommendation(
            name="AI-Enhanced Market Research & Analysis",
            type="ai_prompt",
            description="Use AI to conduct comprehensive market research and identify opportunities",
            complexity="beginner",
            expected_performance={
                'accuracy': 0.75,
                'time_saved': 0.80,
                'insight_quality': 0.85
            },
            requirements=["Market data access", "AI assistant"],
            implementation_guide=self._create_research_guide(),
            ai_prompts=[
                "market_research",
                "market_scanner",
                "sector_analysis"
            ]
        )
        recommendations.append(research_rec)
        
        return recommendations
    
    def _get_relevant_prompts(self, strategy_type: StrategyType) -> List[str]:
        """Get relevant AI prompts for a strategy type"""
        prompt_mapping = {
            StrategyType.MEAN_REVERSION: ["technical_analyzer", "rsi_analyzer", "performance_analyzer"],
            StrategyType.MOMENTUM: ["technical_analyzer", "macd_analyzer", "trend_analyzer"],
            StrategyType.BREAKOUT: ["pattern_recognition", "technical_analyzer", "volatility_analyzer"],
            StrategyType.PAIRS_TRADING: ["correlation_analyzer", "statistical_analyzer", "pairs_scanner"],
            StrategyType.VOLATILITY: ["volatility_analyzer", "options_analyzer", "risk_calculator"],
            StrategyType.ARBITRAGE: ["arbitrage_scanner", "correlation_analyzer", "execution_optimizer"]
        }
        
        # Return available prompts (some may not be implemented yet)
        available_prompts = [
            "market_scanner", "technical_analyzer", "trade_executor", 
            "performance_analyzer", "backtest_analyzer", "risk_calculator"
        ]
        
        return available_prompts[:3]  # Return top 3 relevant prompts
    
    def _create_implementation_guide(self, metadata, user_profile: Dict[str, Any]) -> str:
        """Create implementation guide for a strategy"""
        guide = f"""
# {metadata.name} Implementation Guide

## Overview
{metadata.description}

## Complexity Level: {metadata.complexity.title()}

## Expected Performance
- Sharpe Ratio: {metadata.expected_sharpe:.2f}
- Maximum Drawdown: {metadata.max_drawdown:.1%}
- Win Rate: {metadata.win_rate:.1%}

## Requirements
{chr(10).join([f"- {req}" for req in metadata.requirements])}

## Implementation Steps

### 1. Data Preparation
- Ensure you have clean OHLCV data for your chosen assets
- Verify data quality and handle missing values
- Set up proper timeframe alignment

### 2. Parameter Configuration
Strategy Parameters:
{chr(10).join([f"- {k}: {v}" for k, v in metadata.parameters.items()])}

### 3. Risk Management
- Position sizing: Risk {2 if user_profile.get('risk_tolerance') == 'high' else 1}% per trade
- Stop loss: Set based on technical levels
- Take profit: Use risk/reward ratio of at least 1:2

### 4. Backtesting
- Test on at least 2 years of historical data
- Use out-of-sample testing
- Validate across different market conditions

### 5. Live Implementation
- Start with paper trading
- Begin with small position sizes
- Monitor performance closely
- Keep detailed trade journal

## Suitable Market Conditions
- Assets: {', '.join(metadata.assets)}
- Timeframes: {', '.join([tf.value for tf in metadata.timeframes])}
- Best in: {'trending' if metadata.strategy_type in [StrategyType.MOMENTUM, StrategyType.BREAKOUT] else 'ranging'} markets

## Warning Signs
- Excessive drawdown beyond {metadata.max_drawdown:.1%}
- Win rate dropping below {(metadata.win_rate - 0.1):.1%}
- Strategy not working in current market regime
"""
        return guide
    
    def _create_ai_strategy_guide(self, user_profile: Dict[str, Any]) -> str:
        """Create guide for AI-assisted strategy development"""
        return """
# AI-Assisted Custom Strategy Development Guide

## Phase 1: Market Research & Opportunity Identification
1. Use the Market Scanner prompt to identify potential opportunities
2. Conduct sector analysis to understand market dynamics
3. Research fundamental factors affecting your chosen assets

## Phase 2: Strategy Conceptualization
1. Define your trading hypothesis
2. Identify key indicators and signals
3. Determine entry and exit criteria
4. Set risk management rules

## Phase 3: Technical Implementation
1. Use Technical Analysis prompts to refine signals
2. Backtest your strategy concept
3. Optimize parameters using AI guidance
4. Validate with out-of-sample data

## Phase 4: Risk Assessment
1. Calculate position sizing using Risk Calculator
2. Assess correlation risks
3. Stress test under different market conditions
4. Set maximum drawdown limits

## Phase 5: Live Testing
1. Start with paper trading
2. Use Trade Journal prompts to track performance
3. Regular performance reviews using AI analysis
4. Continuous improvement based on results

## AI Prompts to Use Throughout:
- Market Scanner: Identify opportunities
- Technical Analyzer: Refine entry/exit signals
- Risk Calculator: Optimize position sizing
- Performance Analyzer: Review and improve
- Strategy Educator: Learn new concepts
"""
    
    def _create_research_guide(self) -> str:
        """Create guide for AI-enhanced research"""
        return """
# AI-Enhanced Market Research Guide

## Daily Research Routine
1. **Market Scanner**: Identify trending assets and opportunities
2. **Sector Analysis**: Understand rotation and themes
3. **Technical Analysis**: Analyze key levels and patterns
4. **News Analysis**: Process market-moving events

## Weekly Deep Dive
1. **Performance Review**: Analyze previous week's trades
2. **Strategy Assessment**: Evaluate strategy performance
3. **Market Research**: Deep dive into specific opportunities
4. **Risk Assessment**: Review portfolio risk metrics

## Monthly Strategy Review
1. **Comprehensive Performance Analysis**: Full month review
2. **Strategy Optimization**: Refine based on results
3. **Market Outlook**: Develop next month's thesis
4. **Education**: Learn new strategies and concepts

## Key AI Prompts for Research:
- Market Research: Comprehensive analysis
- Market Scanner: Opportunity identification
- Technical Analyzer: Chart analysis
- Performance Analyzer: Results review
"""
    
    def _get_strategy_code_example(self, strategy_name: str) -> Optional[str]:
        """Get code example for a strategy"""
        strategy = self.quant_strategies.get_strategy(strategy_name)
        if not strategy:
            return None
        
        return f"""# {strategy.name} Implementation Example

import pandas as pd
import numpy as np

# Initialize strategy
strategy = {strategy.__class__.__name__}(**{strategy.parameters})

# Load your data
# data = pd.read_csv('your_data.csv')
# data.set_index('timestamp', inplace=True)

# Generate signals
signals = strategy.generate_signals(data)

# Basic backtesting
backtest_results = strategy.backtest(data, initial_capital=10000)

print(f"Total Return: {{backtest_results['total_return']:.2%}}")
print(f"Win Rate: {{backtest_results['win_rate']:.2%}}")
print(f"Sharpe Ratio: {{backtest_results['sharpe_ratio']:.2f}}")
print(f"Max Drawdown: {{backtest_results['max_drawdown']:.2%}}")"""
    
    def _calculate_suitability_score(self, recommendation: StrategyRecommendation, 
                                   user_profile: Dict[str, Any]) -> float:
        """Calculate how suitable a recommendation is for the user"""
        score = 0.0
        
        # Experience level match
        complexity_scores = {'beginner': 1, 'intermediate': 2, 'advanced': 3}
        user_level = complexity_scores.get(user_profile.get('experience_level', 'beginner'), 1)
        rec_level = complexity_scores.get(recommendation.complexity, 1)
        
        if user_level >= rec_level:
            score += 30  # Can handle the complexity
        else:
            score -= 20  # Too complex
        
        # Performance expectations
        if recommendation.type == "quant_strategy":
            perf = recommendation.expected_performance
            score += perf.get('sharpe_ratio', 0) * 10
            score += perf.get('win_rate', 0) * 20
            score -= perf.get('max_drawdown', 0) * 50
        
        # Risk tolerance
        risk_tolerance = user_profile.get('risk_tolerance', 'medium')
        if risk_tolerance == 'low' and recommendation.expected_performance.get('max_drawdown', 0) > 0.15:
            score -= 15
        elif risk_tolerance == 'high' and recommendation.expected_performance.get('sharpe_ratio', 0) > 1.5:
            score += 15
        
        return max(0, score)
    
    def generate_strategy_prompt(self, strategy_name: str, context: Dict[str, Any]) -> Optional[str]:
        """Generate a formatted AI prompt for strategy analysis"""
        if strategy_name in self.quant_strategies.list_strategies():
            # For quantitative strategies, use technical analysis prompt
            return self.ai_prompts.format_prompt(
                "technical_analyzer",
                trading_asset=context.get('asset', 'EURUSD')
            )
        else:
            # For custom strategies, use market scanner
            return self.ai_prompts.format_prompt(
                "market_scanner",
                criteria=context.get('criteria', 'trending assets with momentum')
            )
    
    def create_trading_plan(self, strategy_name: str, user_profile: Dict[str, Any]) -> Dict[str, Any]:
        """Create a comprehensive trading plan"""
        strategy = self.quant_strategies.get_strategy(strategy_name)
        metadata = self.quant_strategies.get_metadata(strategy_name)
        
        if not strategy or not metadata:
            return {"error": f"Strategy {strategy_name} not found"}
        
        # Calculate position sizing
        account_size = user_profile.get('capital', 10000)
        risk_per_trade = 0.01 if user_profile.get('risk_tolerance') == 'low' else 0.02
        
        trading_plan = {
            'strategy_name': metadata.name,
            'description': metadata.description,
            'parameters': strategy.parameters,
            'risk_management': {
                'account_size': account_size,
                'risk_per_trade': risk_per_trade,
                'max_positions': 3 if user_profile.get('risk_tolerance') == 'low' else 5,
                'max_correlation': 0.7
            },
            'execution_rules': {
                'entry_criteria': "Follow strategy signals",
                'exit_criteria': "Stop loss or take profit hit",
                'position_sizing': "Based on risk per trade",
                'timing': "Trade during active market hours"
            },
            'monitoring': {
                'daily_review': "Check open positions and new signals",
                'weekly_review': "Analyze performance and adjust if needed",
                'monthly_review': "Full strategy evaluation"
            },
            'ai_prompts': self._get_relevant_prompts(metadata.strategy_type),
            'implementation_guide': self._create_implementation_guide(metadata, user_profile)
        }
        
        return trading_plan
    
    def get_learning_path(self, user_profile: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Create a personalized learning path"""
        experience = user_profile.get('experience_level', 'beginner')
        
        learning_modules = []
        
        if experience == 'beginner':
            learning_modules = [
                {
                    'title': 'Trading Fundamentals',
                    'description': 'Learn basic trading concepts and terminology',
                    'ai_prompts': ['strategy_educator'],
                    'strategies': ['rsi_mean_reversion', 'macd_momentum'],
                    'duration': '2 weeks'
                },
                {
                    'title': 'Technical Analysis Basics',
                    'description': 'Understand charts, indicators, and patterns',
                    'ai_prompts': ['technical_analyzer', 'pattern_recognition'],
                    'strategies': ['bollinger_bands_mean_reversion'],
                    'duration': '3 weeks'
                },
                {
                    'title': 'Risk Management',
                    'description': 'Learn to manage risk and size positions',
                    'ai_prompts': ['risk_calculator', 'trading_psychology'],
                    'strategies': ['dual_thrust'],
                    'duration': '2 weeks'
                }
            ]
        elif experience == 'intermediate':
            learning_modules = [
                {
                    'title': 'Advanced Technical Analysis',
                    'description': 'Complex patterns and multi-timeframe analysis',
                    'ai_prompts': ['technical_analyzer', 'pattern_recognition'],
                    'strategies': ['london_breakout', 'dual_thrust'],
                    'duration': '3 weeks'
                },
                {
                    'title': 'Quantitative Strategies',
                    'description': 'Statistical and algorithmic approaches',
                    'ai_prompts': ['backtest_analyzer', 'performance_analyzer'],
                    'strategies': ['pairs_trading'],
                    'duration': '4 weeks'
                }
            ]
        else:  # advanced
            learning_modules = [
                {
                    'title': 'Strategy Development',
                    'description': 'Create and optimize custom strategies',
                    'ai_prompts': ['strategy_educator', 'backtest_analyzer'],
                    'strategies': ['pairs_trading', 'custom_strategies'],
                    'duration': '6 weeks'
                },
                {
                    'title': 'Portfolio Management',
                    'description': 'Multi-strategy portfolio optimization',
                    'ai_prompts': ['performance_analyzer', 'risk_calculator'],
                    'strategies': ['portfolio_optimization'],
                    'duration': '4 weeks'
                }
            ]
        
        return learning_modules
    
    def save_user_preferences(self, user_id: str, preferences: Dict[str, Any]):
        """Save user preferences for future recommendations"""
        self.user_preferences[user_id] = {
            **preferences,
            'last_updated': datetime.now().isoformat()
        }
    
    def get_user_preferences(self, user_id: str) -> Dict[str, Any]:
        """Get saved user preferences"""
        return self.user_preferences.get(user_id, {})

# Example usage and testing
if __name__ == "__main__":
    # Initialize the assistant
    assistant = StrategyAssistant()
    
    # Example user profile
    user_profile = {
        'experience_level': 'intermediate',
        'asset_classes': ['forex', 'stocks'],
        'timeframes': ['1h', '4h'],
        'risk_tolerance': 'medium',
        'capital': 25000,
        'goals': ['income', 'growth']
    }
    
    # Get recommendations
    recommendations = assistant.get_comprehensive_recommendations(user_profile)
    
    print(f"Found {len(recommendations)} recommendations:")
    for i, rec in enumerate(recommendations[:3], 1):
        print(f"\n{i}. {rec.name} ({rec.type})")
        print(f"   Complexity: {rec.complexity}")
        print(f"   Description: {rec.description}")
        if rec.type == "quant_strategy":
            perf = rec.expected_performance
            print(f"   Expected Sharpe: {perf.get('sharpe_ratio', 'N/A')}")
            print(f"   Max Drawdown: {perf.get('max_drawdown', 'N/A'):.1%}")
    
    # Create trading plan
    if recommendations:
        plan = assistant.create_trading_plan(recommendations[0].name.lower().replace(' ', '_'), user_profile)
        print(f"\nTrading plan created for: {plan.get('strategy_name', 'Unknown')}")
    
    # Get learning path
    learning_path = assistant.get_learning_path(user_profile)
    print(f"\nLearning path has {len(learning_path)} modules:")
    for module in learning_path:
        print(f"- {module['title']} ({module['duration']})")