#!/usr/bin/env python
# test_mt5_bridge_standalone.py
"""
Standalone test script for MT5 Bridge TDD implementation
"""

import sys
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("mt5_standalone_test")

# Standalone MT5 Bridge Mock
class MT5BridgeMock:
    def __init__(self):
        self.connected = True
        self.orders = []
        self.simulate_error = False
    
    def connect(self):
        self.connected = True
        return True
    
    def is_connected(self):
        return self.connected
    
    def place_order(self, symbol, order_type, lot, price=None, stop_loss=None, take_profit=None):
        if self.simulate_error:
            raise Exception("API error")
        
        if not self.connected:
            self.connect()
        
        if symbol == "INVALID":
            raise ValueError("Invalid symbol")
        
        # Normalize order type to uppercase for consistency
        order_type = order_type.upper() if isinstance(order_type, str) else order_type
        
        order_id = len(self.orders) + 1
        self.orders.append({
            "id": order_id,
            "symbol": symbol,
            "type": order_type,
            "lot": lot,
            "price": price,
            "status": "filled"
        })
        return order_id
    
    def get_order_status(self, order_id):
        for order in self.orders:
            if order["id"] == order_id:
                return order["status"]
        return "not_found"
    
    def close_order(self, order_id):
        for order in self.orders:
            if order["id"] == order_id:
                order["status"] = "closed"
                return True
        return False
    
    def simulate_connection_loss(self):
        self.connected = False
    
    def simulate_api_error(self):
        self.simulate_error = True

def run_tests():
    """Run standalone tests for MT5 Bridge"""
    logger.info("Running standalone tests for MT5 Bridge...")
    
    # Test 1: Place order success
    logger.info("Test 1: Place order success")
    try:
        bridge = MT5BridgeMock()
        order_id = bridge.place_order(
            symbol="EURUSD",
            order_type="buy",
            lot=0.1,
            price=1.1000
        )
        assert order_id == 1, f"Expected order_id 1, got {order_id}"
        logger.info("Test 1: PASSED")
    except Exception as e:
        logger.error(f"Test 1 failed: {str(e)}")
        return 1
    
    # Test 2: Place order with invalid symbol
    logger.info("Test 2: Place order with invalid symbol")
    try:
        bridge = MT5BridgeMock()
        try:
            bridge.place_order(
                symbol="INVALID",
                order_type="buy",
                lot=0.1,
                price=1.1000
            )
            logger.error("Test 2 failed: Expected ValueError not raised")
            return 1
        except ValueError as e:
            assert "Invalid symbol" in str(e), f"Expected 'Invalid symbol' in error message, got {str(e)}"
            logger.info("Test 2: PASSED")
    except Exception as e:
        logger.error(f"Test 2 failed: {str(e)}")
        return 1
    
    # Test 3: Auto reconnect on connection loss
    logger.info("Test 3: Auto reconnect on connection loss")
    try:
        bridge = MT5BridgeMock()
        bridge.simulate_connection_loss()
        assert not bridge.is_connected(), "Expected bridge to be disconnected"
        
        order_id = bridge.place_order("EURUSD", "buy", 0.1, 1.1000)
        assert order_id == 1, f"Expected order_id 1, got {order_id}"
        assert bridge.is_connected(), "Expected bridge to be reconnected"
        logger.info("Test 3: PASSED")
    except Exception as e:
        logger.error(f"Test 3 failed: {str(e)}")
        return 1
    
    # Test 4: Handle API error
    logger.info("Test 4: Handle API error")
    try:
        bridge = MT5BridgeMock()
        bridge.simulate_api_error()
        
        try:
            bridge.place_order("EURUSD", "buy", 0.1, 1.1000)
            logger.error("Test 4 failed: Expected Exception not raised")
            return 1
        except Exception as e:
            assert "API error" in str(e), f"Expected 'API error' in error message, got {str(e)}"
            logger.info("Test 4: PASSED")
    except Exception as e:
        logger.error(f"Test 4 failed: {str(e)}")
        return 1
    
    logger.info("All tests PASSED!")
    return 0

if __name__ == "__main__":
    sys.exit(run_tests())