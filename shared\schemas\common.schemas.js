"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StatusSchema = exports.SearchRequestSchema = exports.FileUploadSchema = exports.DateRangeSchema = exports.ApiResponseSchema = exports.ApiErrorSchema = exports.PaginationResponseSchema = exports.PaginationRequestSchema = exports.IdSchema = void 0;
const zod_1 = require("zod");
// Common ID schemas
exports.IdSchema = zod_1.z.string().uuid();
// Pagination schemas
exports.PaginationRequestSchema = zod_1.z.object({
    page: zod_1.z.number().int().min(1).default(1),
    limit: zod_1.z.number().int().min(1).max(100).default(20),
    sortBy: zod_1.z.string().optional(),
    sortOrder: zod_1.z.enum(['asc', 'desc']).default('desc'),
});
exports.PaginationResponseSchema = zod_1.z.object({
    page: zod_1.z.number().int().min(1),
    limit: zod_1.z.number().int().min(1),
    total: zod_1.z.number().int().min(0),
    totalPages: zod_1.z.number().int().min(0),
    hasNext: zod_1.z.boolean(),
    hasPrev: zod_1.z.boolean(),
});
// API Response schemas
exports.ApiErrorSchema = zod_1.z.object({
    code: zod_1.z.string(),
    message: zod_1.z.string(),
    details: zod_1.z.string().optional(),
    timestamp: zod_1.z.date().default(() => new Date()),
});
const ApiResponseSchema = (dataSchema) => zod_1.z.object({
    success: zod_1.z.boolean(),
    data: dataSchema.optional(),
    error: exports.ApiErrorSchema.optional(),
    timestamp: zod_1.z.date().default(() => new Date()),
});
exports.ApiResponseSchema = ApiResponseSchema;
// Date range schema
exports.DateRangeSchema = zod_1.z.object({
    startDate: zod_1.z.date(),
    endDate: zod_1.z.date(),
}).refine((data) => data.startDate <= data.endDate, {
    message: "Start date must be before or equal to end date",
    path: ["endDate"],
});
// File upload schemas
exports.FileUploadSchema = zod_1.z.object({
    filename: zod_1.z.string().min(1),
    mimetype: zod_1.z.string(),
    size: zod_1.z.number().int().min(0),
    buffer: zod_1.z.instanceof(Buffer).optional(),
});
// Search schemas
exports.SearchRequestSchema = zod_1.z.object({
    query: zod_1.z.string().min(1),
    filters: zod_1.z.record(zod_1.z.string(), zod_1.z.any()).optional(),
    ...exports.PaginationRequestSchema.shape,
});
// Status schemas
exports.StatusSchema = zod_1.z.enum(['active', 'inactive', 'pending', 'completed', 'error']);
//# sourceMappingURL=common.schemas.js.map