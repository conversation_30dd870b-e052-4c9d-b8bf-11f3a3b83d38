Metadata-Version: 2.4
Name: ai_enhanced_trading_platform
Version: 0.1.0
Summary: AI Enhanced Trading Platform Python Package
Author: DeepFriedCyber
Requires-Python: >=3.8
Description-Content-Type: text/markdown

# 🚀 AI-Enhanced Trading Platform

[![CI/CD Pipeline](https://github.com/your-username/AI-Enhanced-Trading-Platform/actions/workflows/ci-cd.yml/badge.svg)](https://github.com/your-username/AI-Enhanced-Trading-Platform/actions/workflows/ci-cd.yml)
[![Coverage](https://codecov.io/gh/your-username/AI-Enhanced-Trading-Platform/branch/main/graph/badge.svg)](https://codecov.io/gh/your-username/AI-Enhanced-Trading-Platform)
[![Python 3.11+](https://img.shields.io/badge/python-3.11+-blue.svg)](https://www.python.org/downloads/)
[![Node.js 18+](https://img.shields.io/badge/node.js-18+-green.svg)](https://nodejs.org/)

A sophisticated trading platform featuring AI-powered strategy verification, backtesting, and risk management using formal verification methods.

## 🌟 Key Features

### 🧠 Darwin Godel Strategy Verifier
- **Security Validation**: Uses RestrictedPython for safe code execution
- **Strategy Type Detection**: Automatically identifies mean reversion, momentum, and breakout strategies
- **Risk Assessment**: Calculates risk and robustness scores
- **Monte Carlo Simulation**: Tests strategy performance under various market conditions
- **Formal Verification**: Mathematical proof of strategy soundness

### 📊 Advanced Analytics
- Real-time backtesting with historical data
- Comprehensive performance metrics (Sharpe ratio, max drawdown, win rate)
- Risk management validation
- Statistical robustness analysis

### 🔒 Enterprise Security
- Sandboxed strategy execution environment
- Input validation with Zod schemas
- Rate limiting and authentication
- Comprehensive audit logging

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │  Python ML      │
│   (React/Vue)   │◄──►│   (Node.js)     │◄──►│   Engine        │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                        │
                              ▼                        ▼
                       ┌─────────────┐         ┌─────────────┐
                       │ PostgreSQL  │         │ Darwin Godel│
                       │ Database    │         │ Verifier    │
                       └─────────────┘         └─────────────┘
                              │
                              ▼
                       ┌─────────────┐
                       │ Redis Cache │
                       └─────────────┘
```

## 🚀 MVP Quick Start

### Prerequisites
- **Node.js** 16+ and npm
- **Python** 3.10+
- **MetaTrader 5** (optional, for live trading)

### MVP Setup

#### 1. Backend Setup
```bash
# Create a virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Create logs directory
mkdir logs
```

#### 2. Start the Minimal Server
```bash
# Start the minimal server for MVP testing
python backend/minimal_server.py
```

#### 3. Frontend Setup
```bash
# Install dependencies
cd frontend
npm install

# Start the frontend development server
npm run dev:mvp
```

The application will be available at:
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

### API Authentication

The MVP version uses HTTP Basic Authentication:

- **Username**: admin
- **Password**: trading123

Example API request with authentication:
```bash
curl -X GET http://localhost:8000/api/strategies -u admin:trading123
```

### Documentation

- [MVP User Guide](MVP_USER_GUIDE.md): Guide for using the MVP version
- [API Reference](docs/API_REFERENCE.md): Detailed API documentation
- [Deployment Guide](DEPLOYMENT_GUIDE.md): Instructions for deploying the platform

## 🧪 Testing

### Run Tests
```bash
# Run all tests
python run_tests.py

# Run quick tests (faster execution)
python run_tests.py quick

# Run tests with coverage analysis
python run_tests.py coverage

# Check testing dependencies
python run_tests.py deps

# Run pilot tests with enhanced reporting
python scripts/run_pilot_tests.py
```

### Test Categories
The platform includes comprehensive test categories:
- **Unit Tests**: Test individual components
- **Integration Tests**: Test component interactions
- **Security Tests**: Test security features
- **ML Model Tests**: Test machine learning models
- **Data Pipeline Tests**: Test data processing
- **Trading Service Tests**: Test trading functionality
- **Property-Based Tests**: Test with generated inputs

### Test Coverage
```bash
# Run tests with coverage
python run_tests.py coverage

# View coverage report
# Open htmlcov/index.html in your browser
```

### Test Dashboard
```bash
# Generate test dashboard
python tests/dashboard.py

# Track test history
python tests/test_history_tracker.py
```

### Test Darwin Godel Verifier
```bash
# Test strategy verification
curl -X POST http://localhost:5001/api/verify-strategy \
  -H "Content-Type: application/json" \
  -d '{
    "strategy_code": "def trading_strategy(data, params):\n    sma = calculate_sma(data[\"close\"], params[\"period\"])\n    current_price = data[\"close\"][-1]\n    if current_price < sma[-1] * 0.98:\n        return {\"signal\": \"buy\", \"confidence\": 0.8}\n    else:\n        return {\"signal\": \"hold\", \"confidence\": 0.5}"
  }'
```

## 📚 API Documentation

### Darwin Godel Endpoints

#### Verify Strategy
```http
POST /api/verify-strategy
Content-Type: application/json

{
  "strategy_code": "def trading_strategy(data, params): ...",
  "run_monte_carlo": false
}
```

#### Backtest with Historical Data
```http
POST /api/verify-with-backtest
Content-Type: application/json

{
  "strategy_code": "def trading_strategy(data, params): ...",
  "historical_data": {
    "close": [100, 101, 102, ...],
    "high": [101, 102, 103, ...],
    "low": [99, 100, 101, ...],
    "volume": [1000, 1100, 1200, ...]
  },
  "initial_capital": 10000
}
```

#### Monte Carlo Validation
```http
POST /api/monte-carlo
Content-Type: application/json

{
  "strategy_code": "def trading_strategy(data, params): ...",
  "simulations": 100,
  "data_variations": 0.02
}
```

### Backend API Endpoints

#### Strategy Management
- `POST /api/strategies` - Create new strategy
- `GET /api/strategies` - List strategies
- `GET /api/strategies/:id` - Get strategy details
- `PUT /api/strategies/:id` - Update strategy
- `DELETE /api/strategies/:id` - Delete strategy

#### Backtesting
- `POST /api/backtest` - Run backtest
- `GET /api/backtest/:id` - Get backtest results
- `GET /api/backtest/:id/report` - Generate report

#### Darwin Godel Integration
- `POST /api/darwin-godel/verify` - Verify strategy
- `POST /api/darwin-godel/verify-with-backtest` - Verify with backtest
- `POST /api/darwin-godel/monte-carlo` - Monte Carlo validation
- `GET /api/darwin-godel/health` - Health check

## 🔧 Development

### TDD Workflow
This project follows Test-Driven Development (TDD):

1. **Red**: Write a failing test
2. **Green**: Write minimal code to pass
3. **Refactor**: Improve code while keeping tests green

```bash
# Watch mode for continuous testing
cd backend
npm run test:watch

cd python_engine
pytest --watch
```

#### TDD Resources
- [TDD Workflow Guide](TDD_WORKFLOW_GUIDE.md): Detailed guide on TDD workflow
- [TDD Quick Reference](TDD_QUICK_REFERENCE.md): Quick reference for TDD patterns
- [Test-First Development](TEST_FIRST_DEVELOPMENT.md): Guide to test-first approach
- [TDD Implementation Roadmap](TDD_IMPLEMENTATION_ROADMAP.md): Roadmap for TDD implementation

#### TDD Templates
```bash
# Copy the TDD template for a new feature
cp tests/templates/tdd_template.py tests/test_new_feature.py
```

### Code Quality
```bash
# Linting
cd backend
npm run lint

cd python_engine
flake8 services
black services

# Type checking
cd backend
npm run type-check
```

### Pre-commit Hooks
```bash
# Install pre-commit hooks
npm install
npx husky install
```

## 🚀 Deployment

### Production Build
```bash
# Build all services
docker-compose -f docker-compose.prod.yml build

# Deploy to production
docker-compose -f docker-compose.prod.yml up -d
```

### Environment Variables
```bash
# Backend (.env)
NODE_ENV=production
DATABASE_URL=********************************/db
REDIS_URL=redis://host:6379
JWT_SECRET=your-secret-key
ML_ENGINE_URL=http://ml-engine:5001

# Python ML Engine (.env)
FLASK_ENV=production
FLASK_DEBUG=0
```

## 📊 Monitoring

### Health Checks
- **Backend**: `GET /health`
- **ML Engine**: `GET /health`
- **Darwin Godel**: `GET /api/darwin-godel/health`

### Metrics
- Test coverage: 90%+ required
- Response time: <200ms for API calls
- Uptime: 99.9% target

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Write tests first (TDD)
4. Implement the feature
5. Ensure all tests pass: `npm test`
6. Commit changes: `git commit -m 'Add amazing feature'`
7. Push to branch: `git push origin feature/amazing-feature`
8. Open a Pull Request

### Code Standards
- **Test Coverage**: Minimum 90%
- **TypeScript**: Strict mode enabled
- **Python**: Black formatting, flake8 linting
- **Commits**: Conventional commit format

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [Wiki](https://github.com/your-username/AI-Enhanced-Trading-Platform/wiki)
- **Issues**: [GitHub Issues](https://github.com/your-username/AI-Enhanced-Trading-Platform/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/AI-Enhanced-Trading-Platform/discussions)

## 🙏 Acknowledgments

- **RestrictedPython**: For secure code execution
- **Zod**: For runtime type validation
- **Jest**: For comprehensive testing
- **Flask**: For the ML engine API
- **PostgreSQL**: For reliable data storage

---

**Built with ❤️ using Test-Driven Development**
