# Application Configuration
NODE_ENV=development
PORT=3001
APP_NAME=AI Trading Platform API
LOG_LEVEL=debug

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ai_trading_dev
DB_USERNAME=postgres
DB_PASSWORD=password
DB_SSL=false
DB_POOL_SIZE=10

# Neon PostgreSQL Database URL
DATABASE_URL=postgresql://username:password@hostname:port/database_name

# Redis Configuration (optional)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Security Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-at-least-32-characters
JWT_EXPIRES_IN=24h
REFRESH_TOKEN_EXPIRES_IN=7d
BCRYPT_ROUNDS=12

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:5173

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Configuration
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=text/csv,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

# External API Keys
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
GOOGLE_API_KEY=your-google-api-key
COHERE_API_KEY=your-cohere-api-key

# Trading Data Sources
ALPHA_VANTAGE_API_KEY=your-alpha-vantage-key
POLYGON_API_KEY=your-polygon-key
IEX_CLOUD_API_KEY=your-iex-cloud-key

# Monitoring and Logging
SENTRY_DSN=your-sentry-dsn
LOG_FILE_PATH=logs/app.log
ENABLE_REQUEST_LOGGING=true

# Python Scripts
PYTHON_EXECUTABLE=python
XLLM_SCRIPT_PATH=../scripts/xllm-integration.py
DGM_SCRIPT_PATH=../scripts/dgm-runner.py

# Darwin Gödel Machine Configuration
COQ_PATH=coqc
COQ_WORKSPACE_DIR=./coq_workspace
DARWIN_POPULATION_SIZE=50
DARWIN_MAX_GENERATIONS=50
DARWIN_MAX_CONCURRENT_JOBS=5

# S3 Core NLP Engine Configuration
S3_CORE_MODEL=gpt-4
S3_CORE_TEMPERATURE=0.3
S3_CORE_MAX_TOKENS=1000

# Strategy Verification Configuration
VERIFICATION_TIMEOUT=30000
VERIFICATION_BATCH_SIZE=3
ENABLE_FORMAL_VERIFICATION=true

# WebSocket Configuration
WS_PORT=3001
WS_HEARTBEAT_INTERVAL=30000

# Frontend Integration
FRONTEND_URL=http://localhost:3000
ENABLE_WEBSOCKET=true

# Evolution Engine Configuration
EVOLUTION_MUTATION_RATE=0.15
EVOLUTION_CROSSOVER_RATE=0.7
EVOLUTION_ELITE_SIZE=20
EVOLUTION_CONVERGENCE_THRESHOLD=0.001

# Performance Monitoring
ENABLE_METRICS=true
METRICS_INTERVAL=60000
ENABLE_PERFORMANCE_LOGGING=true