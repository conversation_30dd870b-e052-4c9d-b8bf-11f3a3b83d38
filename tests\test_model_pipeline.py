# tests/test_model_pipeline.py
import pytest
import asyncio
from unittest.mock import patch, MagicMock, AsyncMock
from datetime import datetime
from src.ml.model_pipeline import (
    ModelPipeline, PredictionInput, PredictionFeatures, 
    PredictionResult, ModelVersion, PerformanceMetrics
)

class TestModelPipelineInitialization:
    """Test ML Model Pipeline initialization"""
    
    def test_model_pipeline_initialization_default(self):
        """Test default initialization"""
        # Act
        pipeline = ModelPipeline()
        
        # Assert
        assert pipeline.min_confidence_threshold == 0.7
        assert pipeline.current_model is not None
        assert len(pipeline.prediction_cache) == 0
        assert pipeline.max_cache_size == 1000
    
    def test_model_pipeline_initialization_custom_threshold(self):
        """Test initialization with custom confidence threshold"""
        # Act
        pipeline = ModelPipeline(min_confidence_threshold=0.8)
        
        # Assert
        assert pipeline.min_confidence_threshold == 0.8
    
    def test_set_confidence_threshold_valid(self):
        """Test setting valid confidence threshold"""
        # Arrange
        pipeline = ModelPipeline()
        
        # Act
        pipeline.set_min_confidence_threshold(0.85)
        
        # Assert
        assert pipeline.min_confidence_threshold == 0.85
    
    def test_set_confidence_threshold_invalid(self):
        """Test setting invalid confidence threshold"""
        # Arrange
        pipeline = ModelPipeline()
        
        # Act & Assert
        with pytest.raises(ValueError, match="Confidence threshold must be between 0 and 1"):
            pipeline.set_min_confidence_threshold(1.5)
        
        with pytest.raises(ValueError, match="Confidence threshold must be between 0 and 1"):
            pipeline.set_min_confidence_threshold(-0.1)

class TestPredictionInput:
    """Test prediction input validation"""
    
    def test_prediction_features_valid(self):
        """Test valid prediction features"""
        # Act
        features = PredictionFeatures(
            rsi=65.5,
            macd=0.0012,
            volume=150000.0,
            sma_20=1.0850,
            ema_12=1.0855
        )
        
        # Assert
        assert features.rsi == 65.5
        assert features.macd == 0.0012
        assert features.volume == 150000.0
        assert features.sma_20 == 1.0850
        assert features.ema_12 == 1.0855
    
    def test_prediction_features_rsi_bounds(self):
        """Test RSI bounds validation"""
        # Act & Assert - RSI too high
        with pytest.raises(ValueError):
            PredictionFeatures(rsi=105.0, macd=0.001, volume=100000.0)
        
        # RSI too low
        with pytest.raises(ValueError):
            PredictionFeatures(rsi=-5.0, macd=0.001, volume=100000.0)
    
    def test_prediction_features_negative_volume(self):
        """Test negative volume validation"""
        # Act & Assert
        with pytest.raises(ValueError):
            PredictionFeatures(rsi=50.0, macd=0.001, volume=-1000.0)
    
    def test_prediction_input_valid_symbol(self):
        """Test valid symbol format"""
        # Arrange
        features = PredictionFeatures(rsi=50.0, macd=0.001, volume=100000.0)
        
        # Act
        prediction_input = PredictionInput(symbol="EURUSD", features=features)
        
        # Assert
        assert prediction_input.symbol == "EURUSD"
        assert prediction_input.features == features
        assert prediction_input.timestamp is not None
    
    def test_prediction_input_invalid_symbol(self):
        """Test invalid symbol format"""
        # Arrange
        features = PredictionFeatures(rsi=50.0, macd=0.001, volume=100000.0)
        
        # Act & Assert
        with pytest.raises(ValueError):
            PredictionInput(symbol="EUR", features=features)  # Too short
        
        with pytest.raises(ValueError):
            PredictionInput(symbol="EURUSD123", features=features)  # Too long
        
        with pytest.raises(ValueError):
            PredictionInput(symbol="eurusd", features=features)  # Lowercase

class TestPredictionExecution:
    """Test prediction execution"""
    
    @pytest.mark.asyncio
    async def test_predict_success(self):
        """Test successful prediction"""
        # Arrange
        pipeline = ModelPipeline()
        features = PredictionFeatures(rsi=65.5, macd=0.0012, volume=150000.0)
        prediction_input = PredictionInput(symbol="EURUSD", features=features)
        
        # Act
        result = await pipeline.predict(prediction_input)
        
        # Assert
        assert isinstance(result, PredictionResult)
        assert 0.0 <= result.value <= 1.0
        assert 0.0 <= result.confidence <= 1.0
        assert result.model_version is not None
        assert result.input_hash is not None
        assert result.prediction_hash is not None
        assert isinstance(result.timestamp, datetime)
        assert isinstance(result.rejected, bool)
    
    @pytest.mark.asyncio
    async def test_predict_with_dict_input(self):
        """Test prediction with dictionary input"""
        # Arrange
        pipeline = ModelPipeline()
        input_dict = {
            "symbol": "EURUSD",
            "features": {
                "rsi": 65.5,
                "macd": 0.0012,
                "volume": 150000.0
            }
        }
        
        # Act
        result = await pipeline.predict(input_dict)
        
        # Assert
        assert isinstance(result, PredictionResult)
        assert result.value is not None
    
    @pytest.mark.asyncio
    async def test_predict_caching(self):
        """Test prediction caching"""
        # Arrange
        pipeline = ModelPipeline()
        features = PredictionFeatures(rsi=65.5, macd=0.0012, volume=150000.0)
        prediction_input = PredictionInput(symbol="EURUSD", features=features)
        
        # Act - First prediction
        result1 = await pipeline.predict(prediction_input)
        
        # Act - Second prediction with same input
        result2 = await pipeline.predict(prediction_input)
        
        # Assert - Should return cached result
        assert result1.prediction_hash == result2.prediction_hash
        assert result1.timestamp == result2.timestamp
        assert len(pipeline.prediction_cache) == 1
    
    @pytest.mark.asyncio
    async def test_predict_no_model_loaded(self):
        """Test prediction when no model is loaded"""
        # Arrange
        pipeline = ModelPipeline()
        pipeline.current_model = None  # Simulate no model
        features = PredictionFeatures(rsi=65.5, macd=0.0012, volume=150000.0)
        prediction_input = PredictionInput(symbol="EURUSD", features=features)
        
        # Act & Assert
        with pytest.raises(RuntimeError, match="No model loaded"):
            await pipeline.predict(prediction_input)
    
    @pytest.mark.asyncio
    async def test_predict_low_confidence_rejection(self):
        """Test prediction rejection due to low confidence"""
        # Arrange
        pipeline = ModelPipeline(min_confidence_threshold=0.9)  # High threshold
        features = PredictionFeatures(rsi=50.0, macd=0.0001, volume=50000.0)  # Low confidence features
        prediction_input = PredictionInput(symbol="EURUSD", features=features)
        
        # Act
        result = await pipeline.predict(prediction_input)
        
        # Assert
        assert result.rejected is True
        assert result.rejection_reason == "Confidence below threshold"
        assert result.confidence < 0.9

class TestFeaturePreprocessing:
    """Test feature preprocessing"""
    
    def test_preprocess_features_basic(self):
        """Test basic feature preprocessing"""
        # Arrange
        pipeline = ModelPipeline()
        features = PredictionFeatures(rsi=65.5, macd=0.0012, volume=150000.0)
        
        # Act
        processed = pipeline._preprocess_features(features)
        
        # Assert
        assert "rsi_normalized" in processed
        assert processed["rsi_normalized"] == 0.655  # 65.5 / 100
        assert "macd" in processed
        assert processed["macd"] == 0.0012
        assert "volume_log" in processed
        assert processed["volume_log"] > 0  # log(150000 + 1)
    
    def test_preprocess_features_with_moving_averages(self):
        """Test preprocessing with moving averages"""
        # Arrange
        pipeline = ModelPipeline()
        features = PredictionFeatures(
            rsi=65.5, 
            macd=0.0012, 
            volume=150000.0,
            sma_20=1.0850,
            ema_12=1.0855
        )
        
        # Act
        processed = pipeline._preprocess_features(features)
        
        # Assert
        assert "ma_ratio" in processed
        expected_ratio = 1.0855 / 1.0850
        assert abs(processed["ma_ratio"] - expected_ratio) < 0.0001
    
    def test_preprocess_features_with_bollinger_bands(self):
        """Test preprocessing with Bollinger Bands"""
        # Arrange
        pipeline = ModelPipeline()
        features = PredictionFeatures(
            rsi=65.5,
            macd=0.0012,
            volume=150000.0,
            sma_20=1.0850,
            bollinger_upper=1.0900,
            bollinger_lower=1.0800
        )
        
        # Act
        processed = pipeline._preprocess_features(features)
        
        # Assert
        assert "bb_position" in processed
        # Position should be calculated based on current price (sma_20) relative to bands
        assert 0.0 <= processed["bb_position"] <= 1.0
    
    def test_preprocess_features_bollinger_zero_range(self):
        """Test preprocessing with zero Bollinger Band range"""
        # Arrange
        pipeline = ModelPipeline()
        features = PredictionFeatures(
            rsi=65.5,
            macd=0.0012,
            volume=150000.0,
            bollinger_upper=1.0850,
            bollinger_lower=1.0850  # Same as upper
        )
        
        # Act
        processed = pipeline._preprocess_features(features)
        
        # Assert
        assert processed["bb_position"] == 0.5  # Default when range is zero

class TestModelInference:
    """Test model inference"""
    
    @pytest.mark.asyncio
    async def test_run_model_inference(self):
        """Test model inference execution"""
        # Arrange
        pipeline = ModelPipeline()
        features = {"rsi_normalized": 0.65, "macd": 0.0012, "volume_log": 11.92}
        
        # Act
        result = await pipeline._run_model_inference(features)
        
        # Assert
        assert "value" in result
        assert "model_confidence" in result
        assert 0.0 <= result["value"] <= 1.0
        assert 0.0 <= result["model_confidence"] <= 1.0
    
    @pytest.mark.asyncio
    async def test_run_model_inference_empty_features(self):
        """Test model inference with empty features"""
        # Arrange
        pipeline = ModelPipeline()
        features = {}
        
        # Act
        result = await pipeline._run_model_inference(features)
        
        # Assert
        assert "value" in result
        assert "model_confidence" in result
        # Should handle empty features gracefully

class TestConfidenceCalculation:
    """Test confidence calculation"""
    
    def test_calculate_confidence_high_confidence(self):
        """Test confidence calculation with high confidence prediction"""
        # Arrange
        pipeline = ModelPipeline()
        prediction = {"value": 0.6, "model_confidence": 0.9}
        features = {"rsi_normalized": 0.65, "macd": 0.0012, "volume_log": 11.92}
        
        # Act
        confidence = pipeline._calculate_confidence(prediction, features)
        
        # Assert
        assert 0.0 <= confidence <= 1.0
        assert confidence > 0.5  # Should be reasonably high
    
    def test_calculate_confidence_extreme_prediction(self):
        """Test confidence calculation with extreme prediction"""
        # Arrange
        pipeline = ModelPipeline()
        prediction = {"value": 0.95, "model_confidence": 0.8}  # Very extreme prediction
        features = {"rsi_normalized": 0.65, "macd": 0.0012, "volume_log": 11.92}
        
        # Act
        confidence = pipeline._calculate_confidence(prediction, features)
        
        # Assert
        assert confidence < 0.8  # Should be reduced due to extremeness
    
    def test_calculate_confidence_missing_features(self):
        """Test confidence calculation with missing features"""
        # Arrange
        pipeline = ModelPipeline()
        prediction = {"value": 0.6, "model_confidence": 0.8}
        features = {"rsi_normalized": 0.65}  # Missing macd and volume_log
        
        # Act
        confidence = pipeline._calculate_confidence(prediction, features)
        
        # Assert
        assert confidence < 0.8  # Should be reduced due to missing features

class TestModelManagement:
    """Test model management"""
    
    @pytest.mark.asyncio
    async def test_update_model_success(self):
        """Test successful model update"""
        # Arrange
        pipeline = ModelPipeline()
        old_model_version = pipeline.current_model.version
        
        new_model = ModelVersion(
            id="model_v2.0.0_test",
            version="2.0.0",
            algorithm="XGBoost",
            training_data_hash="sha256:new_training_hash",
            hyperparameters={"n_estimators": 200, "max_depth": 15},
            performance_metrics=PerformanceMetrics(
                accuracy=0.85,
                precision=0.82,
                recall=0.88,
                f1_score=0.85,
                backtest_sharpe=1.65
            ),
            created_at=datetime.now(),
            model_hash="sha256:new_model_hash"
        )
        
        # Act
        await pipeline.update_model(new_model)
        
        # Assert
        assert pipeline.current_model.version == "2.0.0"
        assert pipeline.current_model.algorithm == "XGBoost"
        assert len(pipeline.prediction_cache) == 0  # Cache should be cleared
    
    @pytest.mark.asyncio
    async def test_update_model_validation_failure(self):
        """Test model update with validation failure"""
        # Arrange
        pipeline = ModelPipeline()
        
        # Create model with poor performance
        poor_model = ModelVersion(
            id="model_poor",
            version="0.1.0",
            algorithm="LinearRegression",
            training_data_hash="sha256:poor_hash",
            hyperparameters={},
            performance_metrics=PerformanceMetrics(
                accuracy=0.45,  # Below minimum threshold
                precision=0.40,
                recall=0.50,
                f1_score=0.44,
                backtest_sharpe=0.3  # Below minimum threshold
            ),
            created_at=datetime.now(),
            model_hash="sha256:poor_model_hash"
        )
        
        # Act & Assert
        with pytest.raises(ValueError, match="Model validation failed"):
            await pipeline.update_model(poor_model)
    
    def test_validate_model_success(self):
        """Test successful model validation"""
        # Arrange
        pipeline = ModelPipeline()
        
        good_model = ModelVersion(
            id="model_good",
            version="1.5.0",
            algorithm="RandomForest",
            training_data_hash="sha256:good_hash",
            hyperparameters={"n_estimators": 150},
            performance_metrics=PerformanceMetrics(
                accuracy=0.78,
                precision=0.75,
                recall=0.82,
                f1_score=0.78,
                backtest_sharpe=1.2
            ),
            created_at=datetime.now(),
            model_hash="sha256:good_model_hash"
        )
        
        # Act
        is_valid = pipeline._validate_model(good_model)
        
        # Assert
        assert is_valid is True
    
    def test_validate_model_missing_fields(self):
        """Test model validation with missing fields"""
        # Arrange
        pipeline = ModelPipeline()
        
        incomplete_model = ModelVersion(
            id="",  # Missing ID
            version="1.0.0",
            algorithm="",  # Missing algorithm
            training_data_hash="sha256:hash",
            hyperparameters={},
            performance_metrics=PerformanceMetrics(
                accuracy=0.78,
                precision=0.75,
                recall=0.82,
                f1_score=0.78,
                backtest_sharpe=1.2
            ),
            created_at=datetime.now(),
            model_hash="sha256:hash"
        )
        
        # Act
        is_valid = pipeline._validate_model(incomplete_model)
        
        # Assert
        assert is_valid is False
    
    def test_get_current_model(self):
        """Test getting current model information"""
        # Arrange
        pipeline = ModelPipeline()
        
        # Act
        current_model = pipeline.get_current_model()
        
        # Assert
        assert current_model is not None
        assert isinstance(current_model, ModelVersion)
        assert current_model.version is not None

class TestCacheManagement:
    """Test cache management"""
    
    @pytest.mark.asyncio
    async def test_cache_size_limit(self):
        """Test cache size limit enforcement"""
        # Arrange
        pipeline = ModelPipeline()
        pipeline.max_cache_size = 3  # Small cache for testing
        
        # Act - Add more predictions than cache size
        for i in range(5):
            features = PredictionFeatures(rsi=50.0 + i, macd=0.001, volume=100000.0)
            prediction_input = PredictionInput(symbol="EURUSD", features=features)
            await pipeline.predict(prediction_input)
        
        # Assert
        assert len(pipeline.prediction_cache) <= 3
    
    def test_clear_cache(self):
        """Test cache clearing"""
        # Arrange
        pipeline = ModelPipeline()
        pipeline.prediction_cache["test_key"] = "test_value"
        
        # Act
        pipeline.clear_cache()
        
        # Assert
        assert len(pipeline.prediction_cache) == 0
    
    def test_get_cache_stats(self):
        """Test cache statistics"""
        # Arrange
        pipeline = ModelPipeline()
        pipeline.prediction_cache["key1"] = "value1"
        pipeline.prediction_cache["key2"] = "value2"
        
        # Act
        stats = pipeline.get_cache_stats()
        
        # Assert
        assert "cache_size" in stats
        assert stats["cache_size"] == 2

class TestHashGeneration:
    """Test hash generation"""
    
    def test_generate_input_hash_consistency(self):
        """Test input hash consistency"""
        # Arrange
        pipeline = ModelPipeline()
        features = PredictionFeatures(rsi=65.5, macd=0.0012, volume=150000.0)
        input1 = PredictionInput(symbol="EURUSD", features=features)
        input2 = PredictionInput(symbol="EURUSD", features=features)
        
        # Act
        hash1 = pipeline._generate_input_hash(input1)
        hash2 = pipeline._generate_input_hash(input2)
        
        # Assert
        assert hash1 == hash2  # Same input should produce same hash
        assert len(hash1) == 64  # SHA256 hex length
    
    def test_generate_input_hash_different_inputs(self):
        """Test input hash for different inputs"""
        # Arrange
        pipeline = ModelPipeline()
        features1 = PredictionFeatures(rsi=65.5, macd=0.0012, volume=150000.0)
        features2 = PredictionFeatures(rsi=70.0, macd=0.0012, volume=150000.0)
        input1 = PredictionInput(symbol="EURUSD", features=features1)
        input2 = PredictionInput(symbol="EURUSD", features=features2)
        
        # Act
        hash1 = pipeline._generate_input_hash(input1)
        hash2 = pipeline._generate_input_hash(input2)
        
        # Assert
        assert hash1 != hash2  # Different inputs should produce different hashes
    
    def test_generate_prediction_hash(self):
        """Test prediction hash generation"""
        # Arrange
        pipeline = ModelPipeline()
        prediction_data = {
            "value": 0.75,
            "model_version": "test_model",
            "input_hash": "test_hash",
            "timestamp": "2024-01-01T00:00:00"
        }
        
        # Act
        hash_result = pipeline._generate_prediction_hash(prediction_data)
        
        # Assert
        assert len(hash_result) == 64  # SHA256 hex length
        assert isinstance(hash_result, str)

class TestModelPipelineIntegration:
    """Test integration scenarios"""
    
    @pytest.mark.asyncio
    async def test_complete_prediction_workflow(self):
        """Test complete prediction workflow"""
        # Arrange
        pipeline = ModelPipeline(min_confidence_threshold=0.6)
        
        # Act - Complete workflow
        # 1. Create prediction input
        features = PredictionFeatures(
            rsi=65.5,
            macd=0.0012,
            volume=150000.0,
            sma_20=1.0850,
            ema_12=1.0855,
            bollinger_upper=1.0900,
            bollinger_lower=1.0800
        )
        prediction_input = PredictionInput(symbol="EURUSD", features=features)
        
        # 2. Make prediction
        result = await pipeline.predict(prediction_input)
        
        # 3. Verify result
        assert isinstance(result, PredictionResult)
        assert result.value is not None
        assert result.confidence is not None
        assert result.model_version is not None
        
        # 4. Check caching
        cached_result = await pipeline.predict(prediction_input)
        assert cached_result.prediction_hash == result.prediction_hash
        
        # 5. Get model info
        model_info = pipeline.get_current_model()
        assert model_info is not None
        
        # 6. Get cache stats
        cache_stats = pipeline.get_cache_stats()
        assert cache_stats["cache_size"] == 1
    
    @pytest.mark.asyncio
    async def test_multiple_symbol_predictions(self):
        """Test predictions for multiple symbols"""
        # Arrange
        pipeline = ModelPipeline()
        symbols = ["EURUSD", "GBPUSD", "USDJPY", "USDCHF", "AUDUSD"]
        
        # Act
        results = []
        for symbol in symbols:
            features = PredictionFeatures(rsi=60.0, macd=0.001, volume=100000.0)
            prediction_input = PredictionInput(symbol=symbol, features=features)
            result = await pipeline.predict(prediction_input)
            results.append(result)
        
        # Assert
        assert len(results) == 5
        assert all(isinstance(r, PredictionResult) for r in results)
        assert len(set(r.input_hash for r in results)) == 5  # All different hashes
        assert len(pipeline.prediction_cache) == 5
    
    @pytest.mark.asyncio
    async def test_performance_under_load(self):
        """Test performance under load"""
        # Arrange
        pipeline = ModelPipeline()
        
        # Act - Make many predictions
        import time
        start_time = time.time()
        
        tasks = []
        for i in range(100):
            features = PredictionFeatures(rsi=50.0 + (i % 50), macd=0.001, volume=100000.0)
            prediction_input = PredictionInput(symbol="EURUSD", features=features)
            task = pipeline.predict(prediction_input)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        end_time = time.time()
        
        # Assert
        assert len(results) == 100
        assert all(isinstance(r, PredictionResult) for r in results)
        assert (end_time - start_time) < 5.0  # Should complete within 5 seconds
        
        # Check that caching worked (some predictions should be identical)
        unique_hashes = set(r.prediction_hash for r in results)
        assert len(unique_hashes) <= 50  # Due to RSI cycling every 50 values