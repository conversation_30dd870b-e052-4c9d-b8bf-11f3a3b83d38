"""
Background worker for running backtests
Handles backtest queue processing with concurrent execution
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Set
from sqlalchemy.orm import Session

from app.database import SessionLocal
from app.models.database import Backtests
from app.services.backtest_service import BacktestService
from app.config import settings

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BacktestRunner:
    """Background worker for running backtests"""
    
    def __init__(self):
        self.poll_interval = getattr(settings, 'WORKER_POLL_INTERVAL', 10)
        self.backtest_service = BacktestService()
        self.max_concurrent = getattr(settings, 'MAX_CONCURRENT_BACKTESTS', 3)
        self.running_backtests: Set[str] = set()
    
    async def run(self):
        """Main worker loop"""
        logger.info("Backtest Runner started")
        
        while True:
            try:
                await self._process_pending_backtests()
                await asyncio.sleep(self.poll_interval)
            except Exception as e:
                logger.error(f"Backtest runner error: {e}", exc_info=True)
                await asyncio.sleep(self.poll_interval)
    
    async def _process_pending_backtests(self):
        """Process pending backtests"""
        db = SessionLocal()
        
        try:
            # Get pending backtests
            pending_backtests = db.query(Backtests).filter(
                Backtests.status == 'pending',
                ~Backtests.id.in_(self.running_backtests)
            ).order_by(Backtests.created_at).limit(
                self.max_concurrent - len(self.running_backtests)
            ).all()
            
            # Start backtest tasks
            tasks = []
            for backtest in pending_backtests:
                if len(self.running_backtests) >= self.max_concurrent:
                    break
                    
                self.running_backtests.add(backtest.id)
                task = asyncio.create_task(
                    self._run_single_backtest(backtest.id)
                )
                tasks.append(task)
            
            # Wait for any completed tasks
            if tasks:
                done, pending = await asyncio.wait(
                    tasks, 
                    timeout=1.0, 
                    return_when=asyncio.FIRST_COMPLETED
                )
                
                # Clean up completed tasks
                for task in done:
                    try:
                        await task
                    except Exception as e:
                        logger.error(f"Backtest task error: {e}")
        
        finally:
            db.close()
    
    async def _run_single_backtest(self, backtest_id: str):
        """Run a single backtest"""
        db = SessionLocal()
        
        try:
            logger.info(f"Starting backtest {backtest_id}")
            
            # Get backtest from database
            backtest = db.query(Backtests).filter(Backtests.id == backtest_id).first()
            if not backtest:
                logger.error(f"Backtest {backtest_id} not found")
                return
            
            # Update status to running
            backtest.status = 'running'
            backtest.started_at = datetime.utcnow()
            db.commit()
            
            # Execute backtest
            result = await self.backtest_service.execute_backtest(backtest_id, db)
            
            if result.status.value == 'completed':
                logger.info(f"Backtest {backtest_id} completed successfully")
                
                # Update backtest with results
                backtest.status = 'completed'
                backtest.completed_at = datetime.utcnow()
                backtest.results = result.to_dict()
                db.commit()
                
            else:
                logger.error(f"Backtest {backtest_id} failed: {result.error_message}")
                
                # Update backtest with error
                backtest.status = 'error'
                backtest.error_message = result.error_message
                backtest.completed_at = datetime.utcnow()
                db.commit()
                
        except Exception as e:
            logger.error(f"Error running backtest {backtest_id}: {e}")
            
            # Mark backtest as error
            backtest = db.query(Backtests).filter(Backtests.id == backtest_id).first()
            if backtest:
                backtest.status = 'error'
                backtest.error_message = str(e)
                backtest.completed_at = datetime.utcnow()
                db.commit()
        
        finally:
            self.running_backtests.discard(backtest_id)
            db.close()
    
    def get_running_backtests(self) -> List[str]:
        """Get list of currently running backtest IDs"""
        return list(self.running_backtests)
    
    def get_worker_stats(self) -> Dict[str, any]:
        """Get worker statistics"""
        return {
            'running_backtests': len(self.running_backtests),
            'max_concurrent': self.max_concurrent,
            'poll_interval': self.poll_interval,
            'backtest_ids': list(self.running_backtests)
        }