class MomentumMACDStrategy(StrategyBase):
    """Momentum strategy using MACD for trend-following signals"""
    
    def __init__(self, symbol="EURUSD", timeframe="H4", mt5_bridge=None, risk_per_trade=0.02):
        super().__init__(
            name=f"Momentum MACD {symbol} {timeframe}",
            symbols=[symbol],
            mt5_bridge=mt5_bridge,
            risk_per_trade=risk_per_trade
        )
        self.macd_fast = 12
        self.macd_slow = 26
        self.macd_signal = 9
        self.timeframe = timeframe
    
    def calculate_macd(self, data):
        """Calculate MACD indicator"""
        close_prices = data['close']
        
        # Calculate EMAs
        ema_fast = close_prices.ewm(span=self.macd_fast).mean()
        ema_slow = close_prices.ewm(span=self.macd_slow).mean()
        
        # MACD line
        macd_line = ema_fast - ema_slow
        
        # Signal line
        signal_line = macd_line.ewm(span=self.macd_signal).mean()
        
        # Histogram
        histogram = macd_line - signal_line
        
        return {
            'macd': macd_line,
            'signal': signal_line,
            'histogram': histogram
        }
    
    def generate_signal(self, symbol, data):
        """Generate momentum signal based on MACD"""
        if len(data['close']) < self.macd_slow + self.macd_signal + 2:
            return {
                "signal": "hold",
                "confidence": 0,
                "reason": "Insufficient data for MACD calculation"
            }
        
        # Calculate MACD
        macd_data = self.calculate_macd(data)
        
        current_macd = macd_data['macd'].iloc[-1]
        current_signal = macd_data['signal'].iloc[-1]
        prev_macd = macd_data['macd'].iloc[-2]
        prev_signal = macd_data['signal'].iloc[-2]
        
        current_price = data['close'].iloc[-1]
        
        # MACD crossover signals
        if prev_macd <= prev_signal and current_macd > current_signal:
            return {
                "signal": "buy",
                "confidence": 0.75,
                "reason": f"MACD bullish crossover: {current_macd:.6f} > {current_signal:.6f}",
                "stop_loss": current_price * 0.985,
                "take_profit": current_price * 1.025
            }
        elif prev_macd >= prev_signal and current_macd < current_signal:
            return {
                "signal": "sell",
                "confidence": 0.75,
                "reason": f"MACD bearish crossover: {current_macd:.6f} < {current_signal:.6f}",
                "stop_loss": current_price * 1.015,
                "take_profit": current_price * 0.975
            }
        else:
            return {
                "signal": "hold",
                "confidence": 0.4,
                "reason": f"No MACD crossover detected"
            }
