// TradingOracle.tsx
// Advanced Trading Oracle Component with Darwin Gödel Machine Integration
// Provides natural language trading interface with formal verification

import { useState, useEffect, useRef, useCallback } from 'react';
import { Send, Activity, Brain, Shield, Zap } from 'lucide-react';

interface TradingQuery {
  type: 'analysis' | 'scan' | 'strategy' | 'indicator' | 'general';
  pair?: string;
  timeframe?: string;
  indicators?: string[];
  action?: 'buy' | 'sell' | 'hold' | 'scan';
  confidence: number;
  reasoning: string;
}

interface TradingAnalysis {
  pair: string;
  timeframe: string;
  analysis: {
    trend: 'bullish' | 'bearish' | 'sideways';
    strength: number;
    key_levels: {
      support: number[];
      resistance: number[];
    };
    indicators: Record<string, any>;
    signals: Array<{
      type: string;
      strength: number;
      description: string;
    }>;
  };
  recommendation: {
    action: 'buy' | 'sell' | 'hold';
    confidence: number;
    reasoning: string;
    risk_level: 'low' | 'medium' | 'high';
  };
}

interface ChatMessage {
  id: string;
  type: 'user' | 'oracle';
  content: string;
  timestamp: Date;
  query?: TradingQuery;
  analysis?: TradingAnalysis;
  scanResults?: Array<{ pair: string; signal: string; strength: number }>;
  verification?: any;
  isLoading?: boolean;
}

interface EvolutionJob {
  jobId: string;
  pair: string;
  timeframe: string;
  status: string;
  generation?: number;
  bestFitness?: number;
  averageFitness?: number;
}

const TradingOracle: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [evolutionJobs, setEvolutionJobs] = useState<EvolutionJob[]>([]);
  const [, setWebsocket] = useState<WebSocket | null>(null); // websocket not directly used
  const [isConnected, setIsConnected] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // WebSocket connection
  useEffect(() => {
    const ws = new WebSocket(process.env.REACT_APP_WS_URL || 'ws://localhost:3001');
    
    ws.onopen = () => {
      console.log('Connected to Darwin Gödel Machine');
      setIsConnected(true);
      setWebsocket(ws);
    };

    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      handleWebSocketMessage(data);
    };

    ws.onclose = () => {
      console.log('Disconnected from Darwin Gödel Machine');
      setIsConnected(false);
      setWebsocket(null);
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      setIsConnected(false);
    };

    return () => {
      ws.close();
    };
  }, []);

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Initial welcome message
  useEffect(() => {
    const welcomeMessage: ChatMessage = {
      id: 'welcome',
      type: 'oracle',
      content: `🚀 **Darwin Gödel Machine Trading Oracle Active**

I'm your advanced AI trading assistant with formal mathematical verification capabilities. I can help you with:

• **Technical Analysis** - "Analyze EUR/USD 4H" or "What's the RSI on GBP/JPY?"
• **Market Scanning** - "Scan all majors for bullish divergence"
• **Strategy Verification** - "Verify my RSI mean reversion strategy"
• **Evolution Engine** - "Evolve strategies for EUR/USD 4H"
• **Indicator Explanations** - "Explain MACD crossover signals"

Try asking: *"Is there bullish divergence on EUR/USD 4H?"* or *"Scan all majors for strong trends"*`,
      timestamp: new Date()
    };
    
    setMessages([welcomeMessage]);
  }, []);

  const handleWebSocketMessage = useCallback((data: any) => {
    switch (data.type) {
      case 'evolution_update':
        setEvolutionJobs(prev => 
          prev.map(job => 
            job.jobId === data.job_id 
              ? { ...job, ...data }
              : job
          )
        );
        
        // Add evolution update to chat
        if (data.status === 'completed') {
          const updateMessage: ChatMessage = {
            id: `evolution_${data.job_id}_${Date.now()}`,
            type: 'oracle',
            content: `🧬 **Evolution Complete!**\n\nJob: ${data.job_id}\nFinal Generation: ${data.final_generation}\nBest Fitness: ${data.best_fitness?.toFixed(4)}\nProven Strategies: ${data.proven_strategies}`,
            timestamp: new Date()
          };
          setMessages(prev => [...prev, updateMessage]);
        }
        break;

      case 'evolution_started':
        const newJob: EvolutionJob = {
          jobId: data.jobId,
          pair: data.data.pair,
          timeframe: data.data.timeframe,
          status: 'running'
        };
        setEvolutionJobs(prev => [...prev, newJob]);
        break;
    }
  }, []);

  const sendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: `user_${Date.now()}`,
      type: 'user',
      content: inputMessage,
      timestamp: new Date()
    };

    const loadingMessage: ChatMessage = {
      id: `loading_${Date.now()}`,
      type: 'oracle',
      content: '🧠 Analyzing your query...',
      timestamp: new Date(),
      isLoading: true
    };

    setMessages(prev => [...prev, userMessage, loadingMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: inputMessage,
          context: {}
        }),
      });

      const data = await response.json();

      // Remove loading message
      setMessages(prev => prev.filter(msg => !msg.isLoading));

      // Create response message
      const responseMessage: ChatMessage = {
        id: `oracle_${Date.now()}`,
        type: 'oracle',
        content: formatOracleResponse(data),
        timestamp: new Date(),
        query: data.query,
        analysis: data.analysis,
        scanResults: data.scanResults,
        verification: data.verification
      };

      setMessages(prev => [...prev, responseMessage]);

      // Handle evolution requests
      if (data.query?.type === 'strategy' && inputMessage.toLowerCase().includes('evolve')) {
        await handleEvolutionRequest(data.query);
      }

    } catch (error) {
      console.error('Error sending message:', error);
      
      // Remove loading message and add error message
      setMessages(prev => prev.filter(msg => !msg.isLoading));
      
      const errorMessage: ChatMessage = {
        id: `error_${Date.now()}`,
        type: 'oracle',
        content: '❌ **Error**: Failed to process your request. Please try again.',
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEvolutionRequest = async (query: TradingQuery) => {
    if (!query.pair || !query.timeframe) return;

    try {
      const response = await fetch('/api/evolve-strategies', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pair: query.pair,
          timeframe: query.timeframe,
          generations: 20,
          populationSize: 50,
          fitnessGoal: 'sharpe'
        }),
      });

      const data = await response.json();

      if (data.success) {
        const evolutionMessage: ChatMessage = {
          id: `evolution_${Date.now()}`,
          type: 'oracle',
          content: `🧬 **Evolution Started!**\n\nJob ID: ${data.job.jobId}\nPair: ${data.job.pair}\nTimeframe: ${data.job.timeframe}\nGenerations: ${data.job.generations}\n\nI'll notify you when evolution completes with proven strategies!`,
          timestamp: new Date()
        };

        setMessages(prev => [...prev, evolutionMessage]);
      }
    } catch (error) {
      console.error('Evolution request failed:', error);
    }
  };

  const formatOracleResponse = (data: any): string => {
    let response = '';

    // Query understanding
    if (data.query) {
      const confidence = Math.round(data.query.confidence * 100);
      response += `🎯 **Query Analysis** (${confidence}% confidence)\n`;
      response += `Type: ${data.query.type.toUpperCase()}`;
      if (data.query.pair) response += ` | Pair: ${data.query.pair}`;
      if (data.query.timeframe) response += ` | Timeframe: ${data.query.timeframe}`;
      response += '\n\n';
    }

    // Technical Analysis
    if (data.analysis) {
      const analysis = data.analysis;
      response += `📊 **Technical Analysis - ${analysis.pair} ${analysis.timeframe}**\n\n`;
      
      // Trend
      const trendIcon = analysis.analysis.trend === 'bullish' ? '📈' : 
                       analysis.analysis.trend === 'bearish' ? '📉' : '➡️';
      response += `${trendIcon} **Trend**: ${analysis.analysis.trend.toUpperCase()} (Strength: ${Math.round(analysis.analysis.strength * 100)}%)\n\n`;
      
      // Key Levels
      if (analysis.analysis.key_levels) {
        response += `🎯 **Key Levels**\n`;
        response += `Support: ${analysis.analysis.key_levels.support.join(', ')}\n`;
        response += `Resistance: ${analysis.analysis.key_levels.resistance.join(', ')}\n\n`;
      }
      
      // Indicators
      if (analysis.analysis.indicators) {
        response += `📈 **Indicators**\n`;
        Object.entries(analysis.analysis.indicators).forEach(([key, value]: [string, any]) => {
          response += `${key}: ${value.value} (${value.signal})\n`;
        });
        response += '\n';
      }
      
      // Recommendation
      const actionIcon = analysis.recommendation.action === 'buy' ? '🟢' : 
                        analysis.recommendation.action === 'sell' ? '🔴' : '🟡';
      response += `${actionIcon} **Recommendation**: ${analysis.recommendation.action.toUpperCase()}\n`;
      response += `Confidence: ${Math.round(analysis.recommendation.confidence * 100)}%\n`;
      response += `Risk Level: ${analysis.recommendation.risk_level.toUpperCase()}\n`;
      response += `Reasoning: ${analysis.recommendation.reasoning}\n`;
    }

    // Scan Results
    if (data.scanResults && data.scanResults.length > 0) {
      response += `🔍 **Market Scan Results**\n\n`;
      data.scanResults.forEach((result: any, index: number) => {
        const strength = Math.round(result.strength * 100);
        response += `${index + 1}. **${result.pair}** - ${result.signal} (${strength}%)\n`;
      });
      response += '\n';
    }

    // Verification Results
    if (data.verification) {
      const verifyIcon = data.verification.verified ? '✅' : '❌';
      response += `${verifyIcon} **Strategy Verification**\n`;
      response += `Status: ${data.verification.verified ? 'PROVEN' : 'UNPROVEN'}\n`;
      response += `Confidence: ${Math.round(data.verification.confidence * 100)}%\n`;
      if (data.verification.errors?.length > 0) {
        response += `Errors: ${data.verification.errors.join(', ')}\n`;
      }
      response += '\n';
    }

    // Explanation
    if (data.explanation) {
      response += `💡 **Explanation**\n\n${data.explanation}\n`;
    }

    // Validation
    if (data.validation) {
      const validIcon = data.validation.isValid ? '✅' : '⚠️';
      response += `${validIcon} **Strategy Validation**\n`;
      response += `Valid: ${data.validation.isValid ? 'YES' : 'NO'}\n`;
      response += `Confidence: ${Math.round(data.validation.confidence * 100)}%\n`;
      response += `Risk Assessment: ${data.validation.riskAssessment}\n`;
      if (data.validation.suggestions?.length > 0) {
        response += `Suggestions:\n${data.validation.suggestions.map((s: string) => `• ${s}`).join('\n')}\n`;
      }
    }

    // Default message
    if (data.message) {
      response += data.message;
    }

    return response || 'I understand your query. How can I help you with trading analysis?';
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const quickActions = [
    { label: 'Analyze EUR/USD 4H', query: 'Analyze EUR/USD 4H chart' },
    { label: 'Scan for Trends', query: 'Scan all major pairs for strong trends' },
    { label: 'RSI Levels', query: 'What are the RSI levels on major pairs?' },
    { label: 'Evolve Strategies', query: 'Evolve strategies for EUR/USD 4H' }
  ];

  return (
    <div className="flex flex-col h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <div className="bg-black/20 backdrop-blur-sm border-b border-purple-500/20 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <Brain className="w-8 h-8 text-purple-400" />
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
            </div>
            <div>
              <h1 className="text-xl font-bold text-white">Darwin Gödel Machine</h1>
              <p className="text-sm text-purple-300">Trading Oracle with Formal Verification</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className={`flex items-center space-x-2 px-3 py-1 rounded-full ${
              isConnected ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
            }`}>
              <div className={`w-2 h-2 rounded-full ${
                isConnected ? 'bg-green-400 animate-pulse' : 'bg-red-400'
              }`}></div>
              <span className="text-xs font-medium">
                {isConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
            
            {evolutionJobs.length > 0 && (
              <div className="flex items-center space-x-2 px-3 py-1 bg-blue-500/20 text-blue-400 rounded-full">
                <Activity className="w-4 h-4 animate-spin" />
                <span className="text-xs font-medium">{evolutionJobs.length} Jobs</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-4xl p-4 rounded-lg ${
                message.type === 'user'
                  ? 'bg-purple-600 text-white ml-12'
                  : 'bg-black/40 backdrop-blur-sm text-gray-100 mr-12 border border-purple-500/20'
              }`}
            >
              {message.type === 'oracle' && (
                <div className="flex items-center space-x-2 mb-2">
                  <Shield className="w-4 h-4 text-purple-400" />
                  <span className="text-xs text-purple-300 font-medium">ORACLE</span>
                  <span className="text-xs text-gray-400">
                    {message.timestamp.toLocaleTimeString()}
                  </span>
                </div>
              )}
              
              <div className="prose prose-invert max-w-none">
                {message.content.split('\n').map((line, index) => {
                  if (line.startsWith('**') && line.endsWith('**')) {
                    return (
                      <div key={index} className="font-bold text-purple-300 mt-2 mb-1">
                        {line.replace(/\*\*/g, '')}
                      </div>
                    );
                  } else if (line.startsWith('• ')) {
                    return (
                      <div key={index} className="ml-4 text-gray-300">
                        {line}
                      </div>
                    );
                  } else {
                    return (
                      <div key={index} className="text-gray-100">
                        {line || <br />}
                      </div>
                    );
                  }
                })}
              </div>
              
              {message.isLoading && (
                <div className="flex items-center space-x-2 mt-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-400"></div>
                  <span className="text-sm text-purple-300">Processing...</span>
                </div>
              )}
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Quick Actions */}
      <div className="px-4 py-2">
        <div className="flex flex-wrap gap-2">
          {quickActions.map((action, index) => (
            <button
              key={index}
              onClick={() => setInputMessage(action.query)}
              className="px-3 py-1 bg-purple-600/20 hover:bg-purple-600/30 text-purple-300 text-sm rounded-full border border-purple-500/30 transition-colors"
            >
              {action.label}
            </button>
          ))}
        </div>
      </div>

      {/* Input */}
      <div className="p-4 bg-black/20 backdrop-blur-sm border-t border-purple-500/20">
        <div className="flex space-x-4">
          <div className="flex-1 relative">
            <input
              ref={inputRef}
              type="text"
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask me anything about trading... (e.g., 'Analyze EUR/USD 4H' or 'Scan for bullish divergence')"
              className="w-full px-4 py-3 bg-black/40 backdrop-blur-sm border border-purple-500/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20"
              disabled={isLoading}
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-2">
              <Zap className="w-4 h-4 text-purple-400" />
            </div>
          </div>
          <button
            onClick={sendMessage}
            disabled={isLoading || !inputMessage.trim()}
            className="px-6 py-3 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors flex items-center space-x-2"
          >
            <Send className="w-4 h-4" />
            <span>Send</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default TradingOracle;