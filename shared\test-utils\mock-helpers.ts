/**
 * Mock helper utilities for testing
 */

import { Request, Response, NextFunction } from 'express';
import { User } from '../schemas';

/**
 * Create mock Express request
 */
export const createMockRequest = (overrides: Partial<Request> = {}): Request => {
  const req: Partial<Request> = {
    body: {},
    params: {},
    query: {},
    headers: {},
    user: undefined,
    method: 'GET',
    url: '/',
    path: '/',
    ...overrides,
  };
  return req as Request;
};

/**
 * Create mock Express response
 */
export const createMockResponse = (): Response => {
  const res: Partial<Response> = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis(),
    send: jest.fn().mockReturnThis(),
    end: jest.fn().mockReturnThis(),
    cookie: jest.fn().mockReturnThis(),
    clearCookie: jest.fn().mockReturnThis(),
    header: jest.fn().mockReturnThis(),
    setHeader: jest.fn().mockReturnThis(),
    locals: {},
  };
  return res as Response;
};

/**
 * Create mock next function
 */
export const createMockNext = (): NextFunction => {
  return jest.fn() as NextFunction;
};

/**
 * Create mock authenticated request
 */
export const createMockAuthenticatedRequest = (
  user: User,
  overrides: Partial<Request> = {}
): Request & { user: User } => {
  const req = createMockRequest(overrides);
  return { ...req, user } as Request & { user: User };
};

/**
 * Create mock logger
 */
export const createMockLogger = () => ({
  error: jest.fn(),
  warn: jest.fn(),
  info: jest.fn(),
  debug: jest.fn(),
  trace: jest.fn(),
});

/**
 * Create mock database connection
 */
export const createMockDb = () => ({
  select: jest.fn().mockReturnThis(),
  from: jest.fn().mockReturnThis(),
  where: jest.fn().mockReturnThis(),
  first: jest.fn(),
  insert: jest.fn().mockReturnThis(),
  returning: jest.fn(),
  update: jest.fn().mockReturnThis(),
  delete: jest.fn().mockReturnThis(),
  orderBy: jest.fn().mockReturnThis(),
  limit: jest.fn().mockReturnThis(),
  offset: jest.fn().mockReturnThis(),
  transaction: jest.fn(),
  raw: jest.fn(),
  migrate: {
    latest: jest.fn(),
    rollback: jest.fn(),
  },
  seed: {
    run: jest.fn(),
  },
  destroy: jest.fn(),
  on: jest.fn(),
});

/**
 * Create mock service response
 */
export const createMockServiceResponse = <T>(
  data?: T,
  error?: { code: string; message: string; details?: string }
) => {
  if (error) {
    return {
      success: false,
      error,
    };
  }
  
  return {
    success: true,
    data,
  };
};

/**
 * Create promise that resolves after specified time
 */
export const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * Mock function that can be called multiple times with different return values
 */
export const createSequentialMock = <T>(...returnValues: T[]) => {
  let callCount = 0;
  return jest.fn(() => {
    const value = returnValues[callCount] ?? returnValues[returnValues.length - 1];
    callCount++;
    return value;
  });
};

/**
 * Mock async function with controllable resolution/rejection
 */
export const createAsyncMock = <T>(
  shouldResolve = true,
  value?: T,
  error?: Error
) => {
  return jest.fn(() => {
    if (shouldResolve) {
      return Promise.resolve(value);
    } else {
      return Promise.reject(error || new Error('Mock async error'));
    }
  });
};

/**
 * Mock timer functions for testing time-dependent code
 */
export const mockTimers = {
  setup: () => {
    jest.useFakeTimers();
    jest.setSystemTime(new Date('2025-01-01T00:00:00.000Z'));
  },
  cleanup: () => {
    jest.useRealTimers();
  },
  advanceBy: (ms: number) => {
    jest.advanceTimersByTime(ms);
  },
  runAll: () => {
    jest.runAllTimers();
  },
};

/**
 * Create mock file upload
 */
export const createMockFileUpload = (overrides: {
  filename?: string;
  mimetype?: string;
  size?: number;
  buffer?: Buffer;
} = {}) => ({
  filename: 'test-file.csv',
  mimetype: 'text/csv',
  size: 1024,
  buffer: Buffer.from('test,data\n1,2\n'),
  ...overrides,
});