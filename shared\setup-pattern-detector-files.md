# Step-by-Step: Create Pattern Detector Files

## 📁 First, Create the Directory Structure

```bash
# Navigate to your project root (where your ml-engine folder is)
cd /path/to/your/AI-Enhanced-Trading-Platform

# Create the directories
mkdir -p ml-engine/services/darwin_godel/__tests__
mkdir -p ml-engine/services/darwin_godel

# Navigate to the ml-engine directory
cd ml-engine
```

## 📝 Step 1: Create the Test File

```bash
# Create the test file
touch services/darwin_godel/__tests__/test_pattern_detector.py

# Open it in your editor (use whatever editor you prefer)
# For VS Code:
code services/darwin_godel/__tests__/test_pattern_detector.py

# For nano:
nano services/darwin_godel/__tests__/test_pattern_detector.py
```

Now copy and paste the test code from the previous artifact into this file.

## 📝 Step 2: Create Empty Implementation Files

```bash
# Create the implementation files (empty for now - TDD!)
touch services/darwin_godel/__init__.py
touch services/darwin_godel/pattern_detector.py
touch services/darwin_godel/pattern_report.py

# Create __init__ files for Python to recognize as packages
touch services/__init__.py
touch services/darwin_godel/__tests__/__init__.py
```

## 📝 Step 3: Create a Simple Implementation Stub

For TDD, we start with minimal implementation that will fail:

```bash
# Edit pattern_detector.py
code services/darwin_godel/pattern_detector.py
```

Add this minimal stub:

```python
# services/darwin_godel/pattern_detector.py
class StrategyPatternDetector:
    def analyze_strategy(self, strategy_code: str):
        # Stub - will make tests fail
        return {}
```

## 📝 Step 4: Install pytest if you haven't already

```bash
# Make sure you're in the ml-engine directory
pip install pytest pytest-cov

# Or add to requirements-dev.txt
echo "pytest==7.4.0" >> requirements-dev.txt
echo "pytest-cov==4.1.0" >> requirements-dev.txt
pip install -r requirements-dev.txt
```

## 🏃 Step 5: Run the Tests (See Them Fail)

```bash
# From the ml-engine directory
pytest services/darwin_godel/__tests__/test_pattern_detector.py -v

# Or with more detail
pytest services/darwin_godel/__tests__/test_pattern_detector.py -v --tb=short
```

You should see output like:
```
test_pattern_detector.py::TestStrategyPatternDetector::test_detects_mean_reversion_strategy FAILED
test_pattern_detector.py::TestStrategyPatternDetector::test_detects_momentum_strategy FAILED
... (all tests failing)
```

**This is good! Red phase of Red-Green-Refactor**

## 📝 Step 6: Implement Pattern Detector (Make Tests Pass)

Now copy the full implementation from the artifact into:
```bash
code services/darwin_godel/pattern_detector.py
```

And create the report generator:
```bash
code services/darwin_godel/pattern_report.py
```

## 🏃 Step 7: Run Tests Again (See Them Pass)

```bash
pytest services/darwin_godel/__tests__/test_pattern_detector.py -v
```

Now you should see:
```
test_pattern_detector.py::TestStrategyPatternDetector::test_detects_mean_reversion_strategy PASSED ✅
test_pattern_detector.py::TestStrategyPatternDetector::test_detects_momentum_strategy PASSED ✅
... (all green!)
```

## 📊 Step 8: Check Coverage

```bash
# Run with coverage
pytest services/darwin_godel/__tests__/test_pattern_detector.py --cov=services.darwin_godel --cov-report=term-missing
```

## 🎯 Quick Test Script

Create a quick test script to try it out:

```bash
# Create test script
touch scripts/test_pattern.py
code scripts/test_pattern.py
```

Add this content:
```python
#!/usr/bin/env python
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.darwin_godel.pattern_detector import StrategyPatternDetector
from services.darwin_godel.pattern_report import PatternReport

# Test strategy
strategy = """
def trading_strategy(data, params):
    sma = calculate_sma(data['close'], 20)
    if data['close'][-1] < sma[-1] * 0.98:
        return {'signal': 'buy'}
    elif data['close'][-1] > sma[-1] * 1.02:
        return {'signal': 'sell'}
    return {'signal': 'hold'}
"""

detector = StrategyPatternDetector()
result = detector.analyze_strategy(strategy)
print(PatternReport.generate_report(result))
```

Run it:
```bash
python scripts/test_pattern.py
```

## 🔧 Troubleshooting

If you get import errors:
```bash
# Make sure you're in the ml-engine directory
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Or run tests with python -m
python -m pytest services/darwin_godel/__tests__/test_pattern_detector.py -v
```

## 📁 Your Final Structure Should Look Like:

```
ml-engine/
├── services/
│   ├── __init__.py
│   └── darwin_godel/
│       ├── __init__.py
│       ├── pattern_detector.py      # Implementation
│       ├── pattern_report.py        # Report generator
│       └── __tests__/
│           ├── __init__.py
│           └── test_pattern_detector.py  # Tests
├── scripts/
│   └── test_pattern.py             # Quick test script
└── requirements-dev.txt            # Test dependencies
```

## ✅ Success Checklist

- [ ] Created directory structure
- [ ] Created test file with tests
- [ ] Created stub implementation (fails)
- [ ] Ran tests and saw them fail (Red)
- [ ] Added real implementation
- [ ] Ran tests and saw them pass (Green)
- [ ] Checked coverage
- [ ] Tried it with a real strategy

Now you're doing TDD! 🎉