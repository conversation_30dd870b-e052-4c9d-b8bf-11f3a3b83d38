<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI Forex Trading Platform - Live Demo</title>
  <script src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
  <script src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    @keyframes pulse {
      0% { opacity: 1; }
      50% { opacity: 0.5; }
      100% { opacity: 1; }
    }
    .price-update { animation: pulse 0.3s ease-in-out; }
    .scrollbar-hide::-webkit-scrollbar { display: none; }
    .scrollbar-hide { -ms-overflow-style: none; scrollbar-width: none; }
    @keyframes scroll {
      0% { transform: translateX(100%); }
      100% { transform: translateX(-100%); }
    }
    .animate-scroll { animation: scroll 30s linear infinite; }
  </style>
</head>
<body>
  <div id="root"></div>
  
  <script type="text/babel">
    const { useState, useEffect, useRef, useCallback } = React;
    
    function EnhancedForexTradingApp() {
      const [portfolio, setPortfolio] = useState(null);
      const [forexData, setForexData] = useState({});
      const [selectedPair, setSelectedPair] = useState('EUR/USD');
      const [marketEvents, setMarketEvents] = useState([]);
      const [wsConnected, setWsConnected] = useState(false);
      const [priceHistory, setPriceHistory] = useState({});
      const [tradeResult, setTradeResult] = useState(null);
      const [lotSize, setLotSize] = useState(0.1);
      const [leverage, setLeverage] = useState(100);
      const wsRef = useRef(null);
      const chartRef = useRef(null);
      const chartInstanceRef = useRef(null);
      
      // Connect to WebSocket for real-time updates
      useEffect(() => {
        const connectWebSocket = () => {
          const ws = new WebSocket('ws://localhost:8080');
          wsRef.current = ws;
          
          ws.onopen = () => {
            console.log('WebSocket connected');
            setWsConnected(true);
            ws.send(JSON.stringify({ action: 'subscribe' }));
          };
          
          ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            
            switch (data.type) {
              case 'market_data':
                data.updates.forEach(update => {
                  setForexData(prev => ({
                    ...prev,
                    [update.pair]: { 
                      ...prev[update.pair], 
                      ...update, 
                      lastUpdate: Date.now(),
                      marketOpen: true
                    }
                  }));
                  
                  // Store price history for charts
                  setPriceHistory(prev => ({
                    ...prev,
                    [update.pair]: [...(prev[update.pair] || []).slice(-50), {
                      time: new Date(),
                      price: (update.bid + update.ask) / 2
                    }]
                  }));
                });
                break;
                
              case 'market_event':
                setMarketEvents(prev => [{
                  ...data,
                  id: Date.now()
                }, ...prev].slice(0, 10));
                break;
            }
          };
          
          ws.onclose = () => {
            console.log('WebSocket disconnected');
            setWsConnected(false);
            // Attempt to reconnect after 3 seconds
            setTimeout(connectWebSocket, 3000);
          };
          
          ws.onerror = (error) => {
            console.error('WebSocket error:', error);
          };
        };
        
        connectWebSocket();
        
        // Cleanup
        return () => {
          if (wsRef.current) {
            wsRef.current.close();
          }
        };
      }, []);
      
      // Update mini chart
      useEffect(() => {
        if (!chartRef.current || !priceHistory[selectedPair]?.length) return;
        
        const ctx = chartRef.current.getContext('2d');
        const history = priceHistory[selectedPair] || [];
        
        if (chartInstanceRef.current) {
          chartInstanceRef.current.destroy();
        }
        
        chartInstanceRef.current = new Chart(ctx, {
          type: 'line',
          data: {
            labels: history.map(h => h.time.toLocaleTimeString()),
            datasets: [{
              label: selectedPair,
              data: history.map(h => h.price),
              borderColor: 'rgb(59, 130, 246)',
              backgroundColor: 'rgba(59, 130, 246, 0.1)',
              borderWidth: 2,
              pointRadius: 0,
              tension: 0.4
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: { display: false },
              tooltip: { enabled: false }
            },
            scales: {
              x: { display: false },
              y: { display: false }
            }
          }
        });
      }, [selectedPair, priceHistory]);
      
      // Fetch portfolio data
      useEffect(() => {
        const fetchPortfolio = () => {
          fetch('http://localhost:3001/api/forex/portfolio')
            .then(res => res.json())
            .then(data => setPortfolio(data))
            .catch(err => console.error('Portfolio fetch error:', err));
        };
        
        fetchPortfolio();
        const interval = setInterval(fetchPortfolio, 5000);
        return () => clearInterval(interval);
      }, []);
      
      const executeTrade = async (side) => {
        try {
          const res = await fetch('http://localhost:3001/api/forex/trade', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              pair: selectedPair,
              side: side,
              lotSize: parseFloat(lotSize),
              leverage: leverage,
              orderType: 'market'
            })
          });
          const result = await res.json();
          setTradeResult(result);
          setTimeout(() => setTradeResult(null), 5000);
        } catch (err) {
          console.error('Trade execution error:', err);
          setTradeResult({ success: false, error: 'Network error' });
        }
      };
      
      return (
        <div className="min-h-screen bg-gray-900 text-white">
          {/* Header */}
          <div className="bg-gray-800 border-b border-gray-700 px-4 py-3">
            <div className="max-w-7xl mx-auto flex justify-between items-center">
              <div className="flex items-center gap-4">
                <h1 className="text-2xl font-bold">AI Forex Trading Platform</h1>
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${wsConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span className="text-sm text-gray-400">
                    {wsConnected ? 'Live' : 'Disconnected'}
                  </span>
                </div>
              </div>
              <div className="text-sm text-gray-400">
                {new Date().toLocaleString()}
              </div>
            </div>
          </div>
          
          <div className="max-w-7xl mx-auto p-4">
            {/* Trade Result Alert */}
            {tradeResult && (
              <div className={`mb-4 p-4 rounded ${tradeResult.success ? 'bg-green-900 border border-green-500' : 'bg-red-900 border border-red-500'}`}>
                <div className="font-semibold">
                  {tradeResult.success ? '✅ Trade Executed' : '❌ Trade Failed'}
                </div>
                <div className="text-sm mt-1">
                  {tradeResult.success ? 
                    `Executed at ${tradeResult.executionPrice} | Pip Value: $${tradeResult.pipValue} | Margin: $${tradeResult.marginRequired}` :
                    tradeResult.error
                  }
                </div>
              </div>
            )}
            
            {/* Market Events Ticker */}
            {marketEvents.length > 0 && (
              <div className="bg-gray-800 rounded p-3 mb-4 overflow-hidden">
                <div className="flex items-center gap-4">
                  <span className="text-yellow-400 font-semibold">LIVE</span>
                  <div className="flex-1 overflow-hidden">
                    <div className="flex gap-8 animate-scroll scrollbar-hide">
                      {marketEvents.map(event => (
                        <div key={event.id} className="flex items-center gap-2 whitespace-nowrap">
                          <span className={`w-2 h-2 rounded-full bg-${
                            event.severity === 'high' ? 'red' : event.severity === 'medium' ? 'yellow' : 'green'
                          }-500`}></span>
                          <span className="text-sm">{event.message}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            {/* Main Trading Interface */}
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
              {/* Left Panel - Currency Pairs */}
              <div className="lg:col-span-1 space-y-4">
                <div className="bg-gray-800 rounded p-4">
                  <h3 className="font-semibold mb-3">Major Pairs</h3>
                  <div className="space-y-2">
                    {['EUR/USD', 'GBP/USD', 'USD/JPY', 'USD/CHF'].map(pair => {
                      const data = forexData[pair];
                      const isSelected = selectedPair === pair;
                      const isUpdating = data?.lastUpdate && Date.now() - data.lastUpdate < 300;
                      
                      return (
                        <div
                          key={pair}
                          onClick={() => setSelectedPair(pair)}
                          className={`p-3 rounded cursor-pointer transition-all ${
                            isSelected ? 'bg-blue-900 border border-blue-500' : 'bg-gray-700 hover:bg-gray-600'
                          } ${isUpdating ? 'price-update' : ''}`}
                        >
                          <div className="flex justify-between items-center">
                            <span className="font-medium">{pair}</span>
                            <span className="text-yellow-400 text-xs">{data?.spread}</span>
                          </div>
                          <div className="text-sm mt-1">
                            <span className="text-gray-400">B:</span> {data?.bid?.toFixed(5) || '...'} 
                            <span className="text-gray-400 ml-2">A:</span> {data?.ask?.toFixed(5) || '...'}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
              
              {/* Center Panel - Chart and Trading */}
              <div className="lg:col-span-2 space-y-4">
                <div className="bg-gray-800 rounded p-4">
                  <h3 className="font-semibold mb-3">{selectedPair} Chart</h3>
                  <div className="h-64">
                    <canvas ref={chartRef}></canvas>
                  </div>
                </div>
                
                {/* Trading Controls */}
                <div className="bg-gray-800 rounded p-4">
                  <h3 className="font-semibold mb-3">Execute Trade</h3>
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm text-gray-400 mb-1">Lot Size</label>
                      <select 
                        value={lotSize} 
                        onChange={(e) => setLotSize(e.target.value)}
                        className="w-full bg-gray-700 p-2 rounded"
                      >
                        <option value="0.01">0.01 (Micro)</option>
                        <option value="0.1">0.1 (Mini)</option>
                        <option value="1">1.0 (Standard)</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm text-gray-400 mb-1">Leverage</label>
                      <select 
                        value={leverage} 
                        onChange={(e) => setLeverage(e.target.value)}
                        className="w-full bg-gray-700 p-2 rounded"
                      >
                        <option value="50">1:50</option>
                        <option value="100">1:100</option>
                        <option value="200">1:200</option>
                      </select>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <button 
                      onClick={() => executeTrade('sell')}
                      className="bg-red-600 hover:bg-red-700 p-3 rounded font-semibold transition-colors"
                    >
                      SELL {forexData[selectedPair]?.bid?.toFixed(5) || '...'}
                    </button>
                    <button 
                      onClick={() => executeTrade('buy')}
                      className="bg-green-600 hover:bg-green-700 p-3 rounded font-semibold transition-colors"
                    >
                      BUY {forexData[selectedPair]?.ask?.toFixed(5) || '...'}
                    </button>
                  </div>
                </div>
              </div>
              
              {/* Right Panel - Account Info & AI Signals */}
              <div className="lg:col-span-1 space-y-4">
                <div className="bg-gray-800 rounded p-4">
                  <h3 className="font-semibold mb-3">AI Signals</h3>
                  <div className="space-y-2">
                    <div className="p-3 bg-green-900 rounded">
                      <div className="flex justify-between">
                        <span className="text-sm">EUR/USD</span>
                        <span className="text-green-400 font-bold">BUY</span>
                      </div>
                      <div className="text-xs text-gray-400">Confidence: 85%</div>
                    </div>
                    <div className="p-3 bg-red-900 rounded">
                      <div className="flex justify-between">
                        <span className="text-sm">GBP/USD</span>
                        <span className="text-red-400 font-bold">SELL</span>
                      </div>
                      <div className="text-xs text-gray-400">Confidence: 72%</div>
                    </div>
                  </div>
                </div>
                
                <div className="bg-gray-800 rounded p-4">
                  <h3 className="font-semibold mb-3">Account</h3>
                  {portfolio && (
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Balance:</span>
                        <span>${portfolio.balance?.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Equity:</span>
                        <span>${portfolio.equity?.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Margin Level:</span>
                        <span>{portfolio.marginLevel}%</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }
    
    ReactDOM.render(<EnhancedForexTradingApp />, document.getElementById('root'));
  </script>
</body>
</html>