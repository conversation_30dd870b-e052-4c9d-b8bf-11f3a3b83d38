{"version": 3, "file": "backtest.schemas.js", "sourceRoot": "", "sources": ["backtest.schemas.ts"], "names": [], "mappings": ";;;AAAA,6BAAwB;AACxB,uDAAyE;AACzE,qDAA4C;AAE5C,kBAAkB;AACL,QAAA,oBAAoB,GAAG,OAAC,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC;AAGzF,yBAAyB;AACZ,QAAA,oBAAoB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC3C,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;IAChC,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAClC,OAAO,EAAE,OAAC,CAAC,KAAK,CAAC,qCAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5C,UAAU,EAAE,OAAC,CAAC,IAAI,EAAE;IACpB,QAAQ,EAAE,OAAC,CAAC,IAAI,EAAE;IAClB,eAAe,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IACrD,QAAQ,EAAE,OAAC,CAAC,MAAM,CAAC;QACjB,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE;QAChB,UAAU,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,GAAG,EAAE,CAAC;KAC9B,CAAC;IACF,eAAe,EAAE,OAAC,CAAC,MAAM,CAAC;QACxB,kBAAkB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK;QACjE,qBAAqB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;QAC7D,cAAc,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;QAChD,gBAAgB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;KACnD,CAAC;CACH,CAAC,CAAC,MAAM,CACP,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,EACzC;IACE,OAAO,EAAE,oCAAoC;IAC7C,IAAI,EAAE,CAAC,UAAU,CAAC;CACnB,CACF,CAAC;AAGF,8CAA8C;AACjC,QAAA,2BAA2B,GAAG,OAAC,CAAC,MAAM,CAAC;IAClD,MAAM,EAAE,4BAAoB;IAC5B,WAAW,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;IAC/C,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,EAAE,yBAAyB;CAC/D,CAAC,CAAC;AAGH,kBAAkB;AACL,QAAA,cAAc,GAAG,OAAC,CAAC,MAAM,CAAC;IACrC,EAAE,EAAE,yBAAQ;IACZ,OAAO,EAAE,yBAAQ;IACjB,MAAM,EAAE,4BAAoB;IAC5B,MAAM,EAAE,4BAAoB;IAC5B,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;IAC/C,UAAU,EAAE,OAAC,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;IAC/B,YAAY,EAAE,OAAC,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;IACjC,aAAa,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACpC,UAAU,EAAE,OAAC,CAAC,IAAI,EAAE;IACpB,UAAU,EAAE,OAAC,CAAC,IAAI,EAAE;CACrB,CAAC,CAAC;AAGH,wCAAwC;AAC3B,QAAA,mBAAmB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC1C,UAAU,EAAE,OAAC,CAAC,IAAI,EAAE;IACpB,SAAS,EAAE,OAAC,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;IAC9B,MAAM,EAAE,qCAAmB;IAC3B,UAAU,EAAE,iCAAe;IAC3B,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAClC,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;IAC5C,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC7B,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE;IACf,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE;IACpB,gBAAgB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE,CAAC,QAAQ,EAAE;IAC3D,MAAM,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,aAAa,EAAE,eAAe,EAAE,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE;CACpF,CAAC,CAAC;AAGU,QAAA,qBAAqB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC5C,sBAAsB;IACtB,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE;IAC5C,cAAc,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE;IAC9C,aAAa,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE;IAC7C,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAElC,cAAc;IACd,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE;IACrB,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,WAAW,EAAE;IACtC,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,WAAW,EAAE;IACpC,aAAa,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,WAAW,EAAE;IAEvC,eAAe;IACf,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,WAAW,EAAE;IACtC,oBAAoB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAC/C,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACnC,aAAa,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAEpC,gBAAgB;IAChB,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,WAAW,EAAE;IACrC,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,WAAW,EAAE;IACtC,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,WAAW,EAAE;IACrC,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,WAAW,EAAE;IAEtC,eAAe;IACf,8BAA8B,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,WAAW,EAAE,CAAC,QAAQ,EAAE;IACnE,4BAA4B,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,WAAW,EAAE,CAAC,QAAQ,EAAE;IAEjE,qBAAqB;IACrB,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE;IACtB,eAAe,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACtC,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CACpC,CAAC,CAAC;AAGU,QAAA,qBAAqB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC5C,WAAW,EAAE,yBAAQ;IACrB,MAAM,EAAE,4BAAoB;IAC5B,OAAO,EAAE,6BAAqB;IAC9B,MAAM,EAAE,OAAC,CAAC,KAAK,CAAC,2BAAmB,CAAC;IACpC,aAAa,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,CAAC;QAC9B,SAAS,EAAE,OAAC,CAAC,IAAI,EAAE;QACnB,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE;QACnB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;QAClB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE;KACrB,CAAC,CAAC;IACH,eAAe,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,CAAC;QAChC,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE;QACtB,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;QACtC,cAAc,EAAE,OAAC,CAAC,MAAM,EAAE;KAC3B,CAAC,CAAC;IACH,UAAU,EAAE,OAAC,CAAC,IAAI,EAAE;CACrB,CAAC,CAAC;AAGH,oCAAoC;AACvB,QAAA,2BAA2B,GAAG,OAAC,CAAC,MAAM,CAAC;IAClD,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE;IAC7B,MAAM,EAAE,4BAAoB;IAC5B,IAAI,EAAE,OAAC,CAAC,MAAM,CAAC;QACb,WAAW,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,CAAC;YAC5B,MAAM,EAAE,qCAAmB;YAC3B,SAAS,EAAE,OAAC,CAAC,IAAI,EAAE;YACnB,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE;YAChB,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE;YAChB,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE;YACf,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE;YACjB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;SAC9B,CAAC,CAAC;KACJ,CAAC;CACH,CAAC,CAAC;AAGU,QAAA,4BAA4B,GAAG,OAAC,CAAC,MAAM,CAAC;IACnD,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE;IAC7B,OAAO,EAAE,OAAC,CAAC,OAAO,EAAE;IACpB,OAAO,EAAE,6BAAqB,CAAC,QAAQ,EAAE;IACzC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC5B,sBAAsB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,WAAW,EAAE;CACjD,CAAC,CAAC;AAGH,sCAAsC;AACzB,QAAA,sBAAsB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC7C,WAAW,EAAE,yBAAQ;IACrB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;IACpC,YAAY,EAAE,OAAC,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;IACjC,eAAe,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE;IAC/C,eAAe,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACtC,MAAM,EAAE,4BAAoB;IAC5B,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CAC/B,CAAC,CAAC"}