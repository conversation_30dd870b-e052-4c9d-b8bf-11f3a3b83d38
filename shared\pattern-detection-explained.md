# Strategy Pattern Detection - Explained with Examples

## What is Pattern Detection?

Pattern detection in the Darwin Godel verifier means **automatically identifying what type of trading strategy** someone has written, and **recognizing common trading patterns** or potential issues in their code.

Think of it like a code reviewer that understands trading strategies.

## 📊 Types of Trading Strategy Patterns

### 1. **Mean Reversion Pattern**
Strategy assumes price will return to average

```python
# Example Mean Reversion Strategy
def trading_strategy(data, params):
    sma = calculate_sma(data['close'], params['period'])
    current_price = data['close'][-1]
    
    # Pattern: Buy when price is below average, sell when above
    if current_price < sma[-1] * 0.98:  # 2% below SMA
        return {'signal': 'buy', 'confidence': 0.8}
    elif current_price > sma[-1] * 1.02:  # 2% above SMA
        return {'signal': 'sell', 'confidence': 0.8}
    else:
        return {'signal': 'hold', 'confidence': 0.5}

# Pattern Detection would identify:
# - Uses SMA (moving average)
# - Compares price to average
# - Buys low, sells high
# → Classification: "MEAN REVERSION"
```

### 2. **Momentum/Trend Following Pattern**
Strategy follows the current trend

```python
# Example Momentum Strategy
def trading_strategy(data, params):
    # Pattern: Buy when price is rising, sell when falling
    short_ma = calculate_sma(data['close'], params['short_period'])
    long_ma = calculate_sma(data['close'], params['long_period'])
    
    if short_ma[-1] > long_ma[-1] and short_ma[-2] <= long_ma[-2]:
        return {'signal': 'buy', 'confidence': 0.9}  # Crossover up
    elif short_ma[-1] < long_ma[-1] and short_ma[-2] >= long_ma[-2]:
        return {'signal': 'sell', 'confidence': 0.9}  # Crossover down
        
# Pattern Detection would identify:
# - Moving average crossover
# - Follows trend direction
# - Momentum-based signals
# → Classification: "MOMENTUM/TREND"
```

### 3. **Breakout Pattern**
Strategy trades on price breaking key levels

```python
# Example Breakout Strategy
def trading_strategy(data, params):
    # Pattern: Trade when price breaks recent highs/lows
    recent_high = max(data['high'][-params['lookback']:])
    recent_low = min(data['low'][-params['lookback']:])
    current_price = data['close'][-1]
    
    if current_price > recent_high:
        return {'signal': 'buy', 'confidence': 0.85}  # Breakout up
    elif current_price < recent_low:
        return {'signal': 'sell', 'confidence': 0.85}  # Breakout down

# Pattern Detection would identify:
# - Uses recent highs/lows
# - Trades on breakouts
# - Range-based logic
# → Classification: "BREAKOUT"
```

## 🚨 Anti-Patterns Detection

Your Darwin Godel verifier should also detect **problematic patterns**:

### 1. **Overfitting Pattern**
Too specific, unlikely to work on new data

```python
# BAD: Overfitted Strategy
def trading_strategy(data, params):
    # Anti-pattern: Too many specific conditions
    if (data['close'][-1] == 150.23 and     # Exact price match?!
        data['volume'][-1] == 12345 and      # Exact volume?!
        len(data) == 252 and                 # Exact data length?!
        params['magic_number'] == 42):       # Magic numbers?!
        return {'signal': 'buy', 'confidence': 1.0}
    
    return {'signal': 'hold', 'confidence': 0.1}

# Pattern Detection would warn:
# ⚠️ "Overfitting detected: too many hard-coded values"
# ⚠️ "Low robustness score: 0.2/1.0"
# ⚠️ "This strategy unlikely to work on live data"
```

### 2. **Look-Ahead Bias Pattern**
Using future data (cheating)

```python
# BAD: Look-ahead bias
def trading_strategy(data, params):
    # Anti-pattern: Looking at tomorrow's price!
    if data['close'][1] > data['close'][0]:  # WRONG: future data
        return {'signal': 'buy', 'confidence': 1.0}

# Pattern Detection would error:
# ❌ "Look-ahead bias detected: accessing future data"
# ❌ "Strategy invalid for live trading"
```

### 3. **Over-Complex Pattern**
Too complicated, likely overfitted

```python
# BAD: Over-complex
def trading_strategy(data, params):
    # Anti-pattern: Too many nested conditions
    if data['rsi'] > 70:
        if data['macd'] > 0:
            if data['volume'] > data['avg_volume']:
                if data['close'] > data['open']:
                    if data['high'] - data['low'] > 5:
                        if params['moon_phase'] == 'full':  # Really?!
                            return {'signal': 'buy'}

# Pattern Detection would warn:
# ⚠️ "Complexity warning: 6 nested conditions"
# ⚠️ "High risk of overfitting"
```

## 🔍 How Pattern Detection Works

```python
class StrategyPatternDetector:
    def detect_patterns(self, strategy_code: str) -> Dict:
        patterns = {
            'strategy_type': self._identify_strategy_type(strategy_code),
            'indicators_used': self._extract_indicators(strategy_code),
            'complexity_score': self._measure_complexity(strategy_code),
            'anti_patterns': self._check_anti_patterns(strategy_code),
            'risk_flags': self._identify_risks(strategy_code)
        }
        return patterns
    
    def _identify_strategy_type(self, code: str) -> str:
        """Identify the primary strategy pattern"""
        
        # Check for mean reversion indicators
        if any(ind in code.lower() for ind in ['sma', 'ema', 'bollinger', 'mean']):
            if 'above' in code or 'below' in code:
                return 'mean_reversion'
        
        # Check for momentum indicators  
        if any(ind in code.lower() for ind in ['crossover', 'momentum', 'rsi', 'macd']):
            return 'momentum'
            
        # Check for breakout patterns
        if any(ind in code.lower() for ind in ['high', 'low', 'breakout', 'range']):
            return 'breakout'
            
        return 'custom'
    
    def _check_anti_patterns(self, code: str) -> List[str]:
        """Check for problematic patterns"""
        warnings = []
        
        # Check for hard-coded values
        import re
        numbers = re.findall(r'\d+\.\d+|\d+', code)
        if len(numbers) > 10:
            warnings.append('Too many hard-coded numbers - possible overfitting')
        
        # Check for excessive conditions
        if code.count('if') > 5:
            warnings.append('High complexity - too many conditions')
            
        # Check for look-ahead bias
        if re.search(r'data\[.*\]\[.*\+.*\]', code):  # data[x][i+1]
            warnings.append('Possible look-ahead bias detected')
            
        return warnings
```

## 🎯 Why Pattern Detection Matters

### 1. **Instant Feedback**
```python
# User submits strategy
result = verifier.verify_strategy(user_code)

print(result)
# Output:
# {
#   'strategy_type': 'mean_reversion',
#   'risk_level': 'medium',
#   'warnings': ['Consider using dynamic thresholds instead of 0.98/1.02'],
#   'suggestions': ['This pattern works best in ranging markets']
# }
```

### 2. **Educational Value**
Helps users understand what they've built:
- "Your strategy is a momentum strategy"
- "It may struggle in sideways markets"
- "Consider adding a trend filter"

### 3. **Risk Assessment**
Different patterns have different risks:
- Mean reversion → Risk in trending markets
- Momentum → Risk in choppy markets  
- Breakout → Risk of false breakouts

### 4. **Optimization Hints**
Based on pattern, suggest improvements:
- "Mean reversion strategies benefit from volatility filters"
- "Momentum strategies work better with trend confirmation"
- "Breakout strategies need volume confirmation"

## 💡 Practical Example for Darwin Godel

```python
def verify_strategy_with_patterns(self, strategy_code: str) -> Dict:
    """Complete verification with pattern detection"""
    
    # 1. Detect patterns
    patterns = self.detect_patterns(strategy_code)
    
    # 2. Run appropriate tests based on pattern
    if patterns['strategy_type'] == 'mean_reversion':
        # Test in ranging market conditions
        test_data = self.generate_ranging_market_data()
    elif patterns['strategy_type'] == 'momentum':
        # Test in trending market conditions
        test_data = self.generate_trending_market_data()
    
    # 3. Provide pattern-specific feedback
    feedback = {
        'classification': patterns['strategy_type'],
        'strengths': self.get_pattern_strengths(patterns['strategy_type']),
        'weaknesses': self.get_pattern_weaknesses(patterns['strategy_type']),
        'market_conditions': self.get_optimal_conditions(patterns['strategy_type']),
        'improvement_suggestions': self.get_suggestions(patterns)
    }
    
    return feedback
```

## 🎨 Visual Pattern Detection

You could even show users a visual representation:

```
Strategy Analysis: Your Mean Reversion Strategy
═══════════════════════════════════════════════

Pattern Type: MEAN REVERSION 📊
├── Indicators Used: SMA(20)
├── Signal Logic: Buy < 98% SMA, Sell > 102% SMA
└── Complexity: Low (Good!)

Risk Assessment: MEDIUM ⚠️
├── Overfitting Risk: Low ✅
├── Look-ahead Bias: None ✅
└── Market Risk: High in trends ⚠️

Suggestions:
• Works best in sideways markets
• Consider adding trend filter
• Test with different SMA periods
• Add stop-loss for trending markets
```

## Summary

Pattern detection in your Darwin Godel verifier:
1. **Identifies** what type of strategy the user wrote
2. **Classifies** it into known categories
3. **Detects** potential problems or anti-patterns
4. **Provides** specific feedback based on the pattern
5. **Suggests** improvements tailored to that strategy type

This makes your verifier not just a validator, but an intelligent assistant that helps users write better strategies!