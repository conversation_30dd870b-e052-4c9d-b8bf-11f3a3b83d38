"""
Trading Chatbot with Zero-Hallucination Architecture
Provides transparent, auditable trading updates with provenance tracking.
"""
import asyncio
import json
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class MessageType(Enum):
    """Message type enumeration"""
    TRADE_UPDATE = "trade_update"
    ALERT = "alert"
    SYSTEM_STATUS = "system_status"
    ERROR = "error"
    UNKNOWN_DATA = "unknown_data"


@dataclass
class ChatbotResponse:
    """Structured chatbot response with audit trail"""
    message: str
    message_type: MessageType
    confidence: float
    source_data: Dict[str, Any]
    timestamp: datetime
    provenance: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses"""
        return {
            "message": self.message,
            "type": self.message_type.value,
            "confidence": self.confidence,
            "timestamp": self.timestamp.isoformat(),
            "provenance": self.provenance,
            "source_data_hash": hash(str(self.source_data))
        }


class TradingChatbot:
    """
    Zero-hallucination trading chatbot with enterprise features:
    - Transparent data sourcing
    - Audit trail for all responses
    - Explicit handling of missing data
    - Provenance tracking
    - Rate limiting and error handling
    """
    
    def __init__(self, max_message_length: int = 500):
        self.max_message_length = max_message_length
        self.message_history: List[ChatbotResponse] = []
        self.unknown_data_responses = [
            "I don't have sufficient data to provide that information.",
            "The requested data is not available in my current dataset.",
            "I cannot provide that information without verified trading data.",
            "Insufficient data available - please check your data sources.",
            "I don't know that information based on the current data."
        ]
        
    async def send_update(self, update_data: Dict[str, Any]) -> ChatbotResponse:
        """
        Send trading update with full transparency
        """
        try:
            # Validate input data
            if not update_data:
                return await self._handle_missing_data("Empty update data received")
                
            # Generate update message
            response = await self.generate_update_message(update_data)
            
            # Store in history
            self.message_history.append(response)
            
            # In production, this would send to actual chat interface
            logger.info(f"Chatbot update sent: {response.message[:100]}...")
            
            return response
            
        except Exception as e:
            logger.error(f"Error sending update: {e}")
            return await self._handle_error(str(e), update_data)
            
    async def generate_update_message(self, update_data: Dict[str, Any]) -> ChatbotResponse:
        """
        Generate trading update message with provenance
        """
        # Check for missing critical data
        if update_data is None:
            return await self._handle_missing_data("Update data is None")
            
        required_fields = ["user_id", "strategy", "source"]
        missing_fields = [field for field in required_fields if field not in update_data]
        
        if missing_fields:
            return await self._handle_missing_data(f"Missing required fields: {missing_fields}")
            
        # Extract data with defaults
        user_id = update_data.get("user_id", "unknown")
        strategy = update_data.get("strategy", "unknown")
        profit = update_data.get("profit", 0)
        drawdown = update_data.get("drawdown", 0)
        trades_today = update_data.get("trades_today", 0)
        source = update_data.get("source", "unknown")
        timestamp_str = update_data.get("timestamp", datetime.now(timezone.utc).isoformat())
        
        # Build transparent message
        message_parts = []
        
        # Strategy performance
        if trades_today > 0:
            message_parts.append(f"{strategy} strategy closed {trades_today} trades today")
            if profit > 0:
                message_parts.append(f"with ${profit:.2f} profit")
            elif profit < 0:
                message_parts.append(f"with ${abs(profit):.2f} loss")
        else:
            message_parts.append(f"{strategy} strategy closed 0 trades today")
            
        # Risk information
        if drawdown > 0:
            message_parts.append(f"Current drawdown: {drawdown:.1f}%")
            
        # Provenance and audit trail
        provenance = [
            f"Data source: {source}",
            f"Timestamp: {timestamp_str}",
            f"User ID: {user_id}"
        ]
        
        message = ". ".join(message_parts) + f". [Source: {source}]"
        
        # Ensure message length compliance
        if len(message) > self.max_message_length:
            message = message[:self.max_message_length-3] + "..."
            
        return ChatbotResponse(
            message=message,
            message_type=MessageType.TRADE_UPDATE,
            confidence=1.0,  # High confidence since we have verified data
            source_data=update_data,
            timestamp=datetime.now(timezone.utc),
            provenance=provenance
        )
        
    async def _handle_missing_data(self, context: str) -> ChatbotResponse:
        """Handle missing data with transparency"""
        import random
        
        base_message = random.choice(self.unknown_data_responses)
        full_message = f"{base_message} Context: {context}"
        
        return ChatbotResponse(
            message=full_message,
            message_type=MessageType.UNKNOWN_DATA,
            confidence=0.0,  # Zero confidence for missing data
            source_data={"error": context},
            timestamp=datetime.now(timezone.utc),
            provenance=[f"Missing data handler: {context}"]
        )
        
    async def _handle_error(self, error_msg: str, original_data: Dict[str, Any]) -> ChatbotResponse:
        """Handle errors transparently"""
        message = f"Error processing trading update: {error_msg}. Please check system status."
        
        return ChatbotResponse(
            message=message,
            message_type=MessageType.ERROR,
            confidence=0.0,
            source_data=original_data,
            timestamp=datetime.now(timezone.utc),
            provenance=[f"Error handler: {error_msg}"]
        )
        
    async def query_trading_data(self, query: str, context_data: Optional[Dict[str, Any]] = None) -> ChatbotResponse:
        """
        Handle user queries about trading data with zero hallucination
        """
        if not context_data:
            return await self._handle_missing_data("No trading data available for query")
            
        # Simple query processing (in production, use NLP/ML)
        query_lower = query.lower()
        
        if "profit" in query_lower or "pnl" in query_lower:
            return await self._handle_profit_query(context_data)
        elif "risk" in query_lower or "drawdown" in query_lower:
            return await self._handle_risk_query(context_data)
        elif "trades" in query_lower:
            return await self._handle_trades_query(context_data)
        else:
            return await self._handle_missing_data(f"Cannot answer query: {query}")
            
    async def _handle_profit_query(self, data: Dict[str, Any]) -> ChatbotResponse:
        """Handle profit-related queries"""
        profit = data.get("profit")
        if profit is None:
            return await self._handle_missing_data("Profit data not available")
            
        message = f"Current profit/loss: ${profit:.2f}"
        if "source" in data:
            message += f" [Source: {data['source']}]"
            
        return ChatbotResponse(
            message=message,
            message_type=MessageType.TRADE_UPDATE,
            confidence=1.0,
            source_data=data,
            timestamp=datetime.now(timezone.utc),
            provenance=[f"Profit query response from: {data.get('source', 'unknown')}"]
        )
        
    async def _handle_risk_query(self, data: Dict[str, Any]) -> ChatbotResponse:
        """Handle risk-related queries"""
        drawdown = data.get("drawdown")
        if drawdown is None:
            return await self._handle_missing_data("Risk data not available")
            
        message = f"Current drawdown: {drawdown:.1f}%"
        if "source" in data:
            message += f" [Source: {data['source']}]"
            
        return ChatbotResponse(
            message=message,
            message_type=MessageType.TRADE_UPDATE,
            confidence=1.0,
            source_data=data,
            timestamp=datetime.now(timezone.utc),
            provenance=[f"Risk query response from: {data.get('source', 'unknown')}"]
        )
        
    async def _handle_trades_query(self, data: Dict[str, Any]) -> ChatbotResponse:
        """Handle trades-related queries"""
        trades_today = data.get("trades_today")
        if trades_today is None:
            return await self._handle_missing_data("Trade count data not available")
            
        message = f"Trades executed today: {trades_today}"
        if "source" in data:
            message += f" [Source: {data['source']}]"
            
        return ChatbotResponse(
            message=message,
            message_type=MessageType.TRADE_UPDATE,
            confidence=1.0,
            source_data=data,
            timestamp=datetime.now(timezone.utc),
            provenance=[f"Trades query response from: {data.get('source', 'unknown')}"]
        )
        
    async def get_message_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent message history"""
        recent_messages = self.message_history[-limit:] if self.message_history else []
        return [msg.to_dict() for msg in recent_messages]
        
    async def clear_history(self) -> None:
        """Clear message history"""
        self.message_history.clear()
        logger.info("Chatbot message history cleared")
        
    async def get_system_status(self) -> ChatbotResponse:
        """Get chatbot system status"""
        status_data = {
            "messages_sent": len(self.message_history),
            "system_status": "operational",
            "last_update": datetime.now(timezone.utc).isoformat()
        }
        
        message = f"Chatbot operational. Messages sent: {len(self.message_history)}"
        
        return ChatbotResponse(
            message=message,
            message_type=MessageType.SYSTEM_STATUS,
            confidence=1.0,
            source_data=status_data,
            timestamp=datetime.now(timezone.utc),
            provenance=["System status check"]
        )