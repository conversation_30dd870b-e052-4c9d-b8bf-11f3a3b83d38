{"name": "ai-trading-platform", "version": "1.0.0", "description": "AI Enhanced Trading Platform with TDD-compliant architecture", "private": true, "workspaces": ["backend", "frontend", "shared"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "npm run dev --workspace=backend", "dev:frontend": "npm run dev --workspace=frontend", "test": "npm run test --workspaces", "test:watch": "npm run test:watch --workspaces", "test:coverage": "npm run test:coverage --workspaces", "test:unit": "jest --config jest.config.js", "test:integration": "jest --config jest.integration.config.js", "test:all": "node scripts/test-all.js", "test:jest-only": "node scripts/test-all.js --jest-only", "test:python-only": "node scripts/test-all.js --pytest-only", "build": "npm run build --workspaces", "lint": "npm run lint --workspaces", "lint:fix": "npm run lint:fix --workspaces", "type-check": "npm run type-check --workspaces", "clean": "npm run clean --workspaces", "setup": "npm install && npm run build --workspace=shared", "demo": "concurrently \"npm run demo:api\" \"npm run demo:ui\"", "demo:api": "ts-node-dev --respawn src/demo/server.ts", "demo:ui": "npx serve public -p 3000", "demo:docker": "docker-compose up --build forex-demo forex-ui", "platform": "concurrently \"npm run platform:api\" \"npm run platform:ui\"", "platform:api": "ts-node-dev --respawn src/platform/server.ts", "platform:ui": "npx serve public -p 3000", "platform:complete": "ts-node-dev --respawn src/platform/server.ts"}, "devDependencies": {"@faker-js/faker": "^8.3.1", "@types/jest": "^29.5.8", "@types/node": "^20.9.0", "@types/pg": "^8.10.7", "@types/ws": "^8.18.1", "concurrently": "^8.2.2", "husky": "^8.0.3", "jest": "^29.7.0", "jest-websocket-mock": "^2.4.0", "lint-staged": "^15.2.0", "pg": "^8.11.3", "playwright": "^1.40.0", "ts-jest": "^29.1.1", "typescript": "^5.2.2"}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run test"}}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "dependencies": {"@fastify/cors": "^11.0.1", "fastify": "^5.4.0", "serve": "^14.2.4", "ts-node-dev": "^2.0.0", "ws": "^8.18.3", "zod": "^3.25.73"}}