import { z } from 'zod';

// Core Trading Types
export const OrderTypeSchema = z.enum(['buy', 'sell']);
export type OrderType = z.infer<typeof OrderTypeSchema>;

export const TradingSymbolSchema = z.enum([
  'EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 
  'USDCAD', 'USDCHF', 'NZDUSD', 'EURGBP'
]);
export type TradingSymbol = z.infer<typeof TradingSymbolSchema>;

// Order Schemas (matching Python Pydantic models)
export const OrderRequestSchema = z.object({
  symbol: TradingSymbolSchema,
  volume: z.number().positive(),
  order_type: OrderTypeSchema,
  price: z.number().positive(),
  stop_loss: z.number().positive().optional(),
  take_profit: z.number().positive().optional(),
});
export type OrderRequest = z.infer<typeof OrderRequestSchema>;

export const OrderResultSchema = z.object({
  success: z.boolean(),
  order_id: z.number().int().optional(),
  error: z.string().optional(),
});
export type OrderResult = z.infer<typeof OrderResultSchema>;

// Account Schemas
export const AccountInfoSchema = z.object({
  balance: z.number(),
  equity: z.number(),
  margin: z.number(),
  currency: z.string().default('USD'),
});
export type AccountInfo = z.infer<typeof AccountInfoSchema>;

// Trading Engine Communication Schemas
export const TradingEngineRequestSchema = z.object({
  action: z.enum(['get_account', 'submit_order', 'close_order', 'get_positions']),
  payload: z.record(z.any()).optional(),
  timestamp: z.date().default(() => new Date()),
  request_id: z.string().uuid(),
});
export type TradingEngineRequest = z.infer<typeof TradingEngineRequestSchema>;

export const TradingEngineResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.string().optional(),
  timestamp: z.date().default(() => new Date()),
  request_id: z.string().uuid(),
});
export type TradingEngineResponse = z.infer<typeof TradingEngineResponseSchema>;

// Position Schemas
export const PositionSchema = z.object({
  position_id: z.number().int(),
  symbol: TradingSymbolSchema,
  volume: z.number(),
  open_price: z.number(),
  current_price: z.number(),
  pnl: z.number(),
  order_type: OrderTypeSchema,
  open_time: z.date(),
});
export type Position = z.infer<typeof PositionSchema>;

// Market Data Schemas
export const TickDataSchema = z.object({
  symbol: TradingSymbolSchema,
  bid: z.number().positive(),
  ask: z.number().positive(),
  timestamp: z.date(),
});
export type TickData = z.infer<typeof TickDataSchema>;

export const OHLCDataSchema = z.object({
  symbol: TradingSymbolSchema,
  timestamp: z.date(),
  open: z.number().positive(),
  high: z.number().positive(),
  low: z.number().positive(),
  close: z.number().positive(),
  volume: z.number().nonnegative().optional(),
});
export type OHLCData = z.infer<typeof OHLCDataSchema>;

// Analysis Schemas (bridging with Python analysis module)
export const TradeAnalysisSchema = z.object({
  total_trades: z.number().int().nonnegative(),
  winning_trades: z.number().int().nonnegative(),
  losing_trades: z.number().int().nonnegative(),
  win_rate: z.number().min(0).max(1),
  total_pnl: z.number(),
  average_win: z.number(),
  average_loss: z.number(),
  profit_factor: z.number().optional(),
  max_drawdown: z.number().optional(),
});
export type TradeAnalysis = z.infer<typeof TradeAnalysisSchema>;

// ML Prediction Schemas
export const MLPredictionSchema = z.object({
  symbol: TradingSymbolSchema,
  prediction_type: z.enum(['price', 'direction', 'volatility']),
  value: z.number(),
  confidence: z.number().min(0).max(1),
  timestamp: z.date(),
  model_version: z.string(),
});
export type MLPrediction = z.infer<typeof MLPredictionSchema>;

// Chatbot Integration Schemas
export const ChatbotQuerySchema = z.object({
  query: z.string().min(1),
  context: z.object({
    user_id: z.string(),
    session_id: z.string(),
    trading_data: z.any().optional(),
  }),
  timestamp: z.date().default(() => new Date()),
});
export type ChatbotQuery = z.infer<typeof ChatbotQuerySchema>;

export const ChatbotResponseSchema = z.object({
  response: z.string(),
  confidence: z.number().min(0).max(1),
  sources: z.array(z.string()).optional(),
  suggested_actions: z.array(z.string()).optional(),
  timestamp: z.date().default(() => new Date()),
});
export type ChatbotResponse = z.infer<typeof ChatbotResponseSchema>;

// Simulation Schemas
export const SimulationConfigSchema = z.object({
  initial_balance: z.number().positive().default(10000),
  start_date: z.date(),
  end_date: z.date(),
  symbols: z.array(TradingSymbolSchema),
  strategy_config: z.record(z.any()),
});
export type SimulationConfig = z.infer<typeof SimulationConfigSchema>;

export const SimulationResultSchema = z.object({
  config: SimulationConfigSchema,
  analysis: TradeAnalysisSchema,
  trades: z.array(z.object({
    symbol: TradingSymbolSchema,
    entry_time: z.date(),
    exit_time: z.date().optional(),
    entry_price: z.number(),
    exit_price: z.number().optional(),
    volume: z.number(),
    pnl: z.number().optional(),
    order_type: OrderTypeSchema,
  })),
  balance_history: z.array(z.object({
    timestamp: z.date(),
    balance: z.number(),
  })),
  created_at: z.date().default(() => new Date()),
});
export type SimulationResult = z.infer<typeof SimulationResultSchema>;