import { z } from 'zod';
import { IdSchema } from './common.schemas';

// Subscription tiers
export const SubscriptionTierSchema = z.enum(['free', 'solo', 'pro', 'enterprise']);
export type SubscriptionTier = z.infer<typeof SubscriptionTierSchema>;

// User ID with proper branding
export const UserIdSchema = IdSchema.brand<'UserId'>();
export type UserId = z.infer<typeof UserIdSchema>;

// Core User Schema
export const UserSchema = z.object({
  id: UserIdSchema,
  email: z.string().email(),
  fullName: z.string().optional(),
  subscriptionTier: SubscriptionTierSchema,
  apiQuotaUsed: z.number().int().nonnegative(),
  apiQuotaLimit: z.number().int().positive(),
  createdAt: z.date(),
  updatedAt: z.date(),
});
export type User = z.infer<typeof UserSchema>;

// User with password hash (for backend use)
export const UserWithPasswordHashSchema = UserSchema.extend({
  passwordHash: z.string().min(1),
});
export type UserWithPasswordHash = z.infer<typeof UserWithPasswordHashSchema>;

// Authentication request schemas
export const LoginRequestSchema = z.object({
  email: z.string().email(),
  password: z.string().min(1),
});
export type LoginRequest = z.infer<typeof LoginRequestSchema>;

export const CreateUserRequestSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8).refine(
    (password) => {
      const hasUpperCase = /[A-Z]/.test(password);
      const hasLowerCase = /[a-z]/.test(password);
      const hasNumbers = /\d/.test(password);
      const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
      return hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar;
    },
    {
      message: "Password must contain uppercase, lowercase, number, and special character",
    }
  ),
  fullName: z.string().min(1).optional(),
});
export type CreateUserRequest = z.infer<typeof CreateUserRequestSchema>;

export const RefreshTokenRequestSchema = z.object({
  refreshToken: z.string().min(1),
});
export type RefreshTokenRequest = z.infer<typeof RefreshTokenRequestSchema>;

export const ResetPasswordRequestSchema = z.object({
  email: z.string().email(),
});
export type ResetPasswordRequest = z.infer<typeof ResetPasswordRequestSchema>;

export const ConfirmResetPasswordRequestSchema = z.object({
  token: z.string().min(1),
  newPassword: z.string().min(8),
});
export type ConfirmResetPasswordRequest = z.infer<typeof ConfirmResetPasswordRequestSchema>;

export const ChangePasswordRequestSchema = z.object({
  currentPassword: z.string().min(1),
  newPassword: z.string().min(8),
});
export type ChangePasswordRequest = z.infer<typeof ChangePasswordRequestSchema>;

// Authentication response schemas
export const AuthTokensSchema = z.object({
  accessToken: z.string(),
  refreshToken: z.string(),
  expiresIn: z.number().int().positive(),
});
export type AuthTokens = z.infer<typeof AuthTokensSchema>;

export const AuthResultSchema = z.object({
  user: UserSchema,
  tokens: AuthTokensSchema,
});
export type AuthResult = z.infer<typeof AuthResultSchema>;

// JWT Token Payload schemas
export const TokenPayloadSchema = z.object({
  userId: UserIdSchema,
  email: z.string().email().optional(),
  subscriptionTier: SubscriptionTierSchema.optional(),
  type: z.enum(['access', 'refresh']),
  iat: z.number().optional(),
  exp: z.number().optional(),
});
export type TokenPayload = z.infer<typeof TokenPayloadSchema>;

export const RefreshTokenPayloadSchema = z.object({
  userId: UserIdSchema,
  type: z.literal('refresh'),
  iat: z.number().optional(),
  exp: z.number().optional(),
});
export type RefreshTokenPayload = z.infer<typeof RefreshTokenPayloadSchema>;

// User profile schemas
export const UpdateUserProfileRequestSchema = z.object({
  fullName: z.string().min(1).optional(),
  email: z.string().email().optional(),
});
export type UpdateUserProfileRequest = z.infer<typeof UpdateUserProfileRequestSchema>;

export const UserProfileSchema = UserSchema.omit({ 
  apiQuotaUsed: true, 
  apiQuotaLimit: true 
}).extend({
  lastLoginAt: z.date().optional(),
  emailVerified: z.boolean().default(false),
  phoneNumber: z.string().optional(),
  timezone: z.string().default('UTC'),
  preferences: z.object({
    theme: z.enum(['light', 'dark']).default('light'),
    language: z.string().default('en'),
    notifications: z.object({
      email: z.boolean().default(true),
      push: z.boolean().default(true),
      trading: z.boolean().default(true),
    }).default({}),
  }).default({}),
});
export type UserProfile = z.infer<typeof UserProfileSchema>;

// Session schemas
export const SessionSchema = z.object({
  id: IdSchema,
  userId: UserIdSchema,
  deviceInfo: z.object({
    userAgent: z.string(),
    ip: z.string(),
    location: z.string().optional(),
  }),
  isActive: z.boolean(),
  lastActivity: z.date(),
  createdAt: z.date(),
  expiresAt: z.date(),
});
export type Session = z.infer<typeof SessionSchema>;

// API Key schemas (for programmatic access)
export const ApiKeySchema = z.object({
  id: IdSchema,
  userId: UserIdSchema,
  name: z.string().min(1),
  keyPrefix: z.string(), // First 8 chars for identification
  hashedKey: z.string(),
  permissions: z.array(z.enum([
    'read:account', 'read:trades', 'write:trades', 
    'read:backtests', 'write:backtests',
    'read:uploads', 'write:uploads',
    'read:chat', 'write:chat'
  ])),
  lastUsed: z.date().optional(),
  usageCount: z.number().int().nonnegative().default(0),
  isActive: z.boolean().default(true),
  createdAt: z.date(),
  expiresAt: z.date().optional(),
});
export type ApiKey = z.infer<typeof ApiKeySchema>;

export const CreateApiKeyRequestSchema = z.object({
  name: z.string().min(1).max(255),
  permissions: z.array(z.enum([
    'read:account', 'read:trades', 'write:trades', 
    'read:backtests', 'write:backtests',
    'read:uploads', 'write:uploads',
    'read:chat', 'write:chat'
  ])).min(1),
  expiresAt: z.date().optional(),
});
export type CreateApiKeyRequest = z.infer<typeof CreateApiKeyRequestSchema>;

export const ApiKeyResponseSchema = z.object({
  id: IdSchema,
  name: z.string(),
  keyPrefix: z.string(),
  fullKey: z.string(), // Only returned on creation
  permissions: z.array(z.string()),
  createdAt: z.date(),
  expiresAt: z.date().optional(),
});
export type ApiKeyResponse = z.infer<typeof ApiKeyResponseSchema>;

// OAuth schemas (for third-party integration)
export const OAuthProviderSchema = z.enum(['google', 'github', 'microsoft']);
export type OAuthProvider = z.infer<typeof OAuthProviderSchema>;

export const OAuthCallbackRequestSchema = z.object({
  code: z.string(),
  state: z.string(),
  provider: OAuthProviderSchema,
});
export type OAuthCallbackRequest = z.infer<typeof OAuthCallbackRequestSchema>;

// Two-factor authentication schemas
export const TwoFactorAuthSchema = z.object({
  enabled: z.boolean(),
  backupCodes: z.array(z.string()).optional(),
  lastUsed: z.date().optional(),
});

export const EnableTwoFactorRequestSchema = z.object({
  password: z.string(),
});
export type EnableTwoFactorRequest = z.infer<typeof EnableTwoFactorRequestSchema>;

export const VerifyTwoFactorRequestSchema = z.object({
  code: z.string().length(6),
});
export type VerifyTwoFactorRequest = z.infer<typeof VerifyTwoFactorRequestSchema>;

// Password strength validation
export const PasswordStrengthSchema = z.object({
  score: z.number().int().min(0).max(4), // 0-4 strength score
  feedback: z.object({
    warning: z.string().optional(),
    suggestions: z.array(z.string()),
  }),
  crackTime: z.string(), // Human readable time estimate
});
export type PasswordStrength = z.infer<typeof PasswordStrengthSchema>;