#!/usr/bin/env python3
"""
Frontend and Backend Runner
Runs both the frontend and backend servers
"""

import subprocess
import sys
import os
import threading
import time
from pathlib import Path

def print_banner(text, char="="):
    """Print a formatted banner"""
    print(f"\n{char * 60}")
    print(f" {text}")
    print(f"{char * 60}\n")

def run_backend(use_auth=False):
    """Run the backend server"""
    print_banner("STARTING BACKEND SERVER", "=")
    
    # Get the project root directory
    project_root = Path.cwd()
    
    # Path to the server script
    if use_auth:
        server_script = os.path.join(project_root, "backend", "simple_server.py")
        print("Using authenticated server")
    else:
        server_script = os.path.join(project_root, "backend", "simple_server_no_auth.py")
        print("Using non-authenticated server for testing")
    
    # Run the server
    try:
        subprocess.run([sys.executable, server_script], check=True)
    except KeyboardInterrupt:
        print("Backend server stopped by user")
    except Exception as e:
        print(f"Error running backend server: {e}")

def run_frontend():
    """Run the frontend server"""
    print_banner("STARTING FRONTEND SERVER", "=")
    
    # Get the project root directory
    project_root = Path.cwd()
    
    # Path to the frontend directory
    frontend_dir = os.path.join(project_root, "frontend")
    
    # Run the frontend server
    try:
        # Change to the frontend directory
        os.chdir(frontend_dir)
        # Run npm start
        subprocess.run(["npm", "start"], check=True)
    except KeyboardInterrupt:
        print("Frontend server stopped by user")
    except Exception as e:
        print(f"Error running frontend server: {e}")

def main():
    """Main execution function"""
    print("🚀 AI Enhanced Trading Platform - Development Environment")
    
    # Parse command-line arguments
    import argparse
    parser = argparse.ArgumentParser(description="Run the frontend and backend servers")
    parser.add_argument("--auth", action="store_true", help="Use authenticated server")
    args = parser.parse_args()
    
    # Start the backend server in a separate thread
    backend_thread = threading.Thread(target=run_backend, args=(args.auth,))
    backend_thread.daemon = True
    backend_thread.start()
    
    # Wait for the backend server to start
    print("Waiting for backend server to start...")
    time.sleep(2)
    
    # Run the frontend server in the main thread
    run_frontend()

if __name__ == "__main__":
    main()