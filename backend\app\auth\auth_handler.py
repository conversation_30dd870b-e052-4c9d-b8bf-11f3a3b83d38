"""
Authentication handler
"""

import os
import logging
from datetime import datetime, timedelta
from typing import Dict, Optional
from backend.app.utils.security import get_password_hash, verify_password
from .jwt_handler import create_access_token
from sqlalchemy.ext.asyncio import AsyncSession
from ..db.crud.user import get_user_by_email, authenticate_user as db_authenticate_user, create_user as db_create_user

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("auth_handler")


async def get_user(db: AsyncSession, email: str):
    """Get user from database"""
    return await get_user_by_email(db, email)

async def authenticate_user(db: AsyncSession, email: str, password: str):
    """Authenticate user"""
    return await db_authenticate_user(db, email, password)

async def create_user(db: AsyncSession, email: str, password: str, full_name: str, role: str = "user"):
    """Create a new user"""
    return await db_create_user(db, email, password, full_name, role)

def create_token_for_user(user):
    """Create token for user"""
    access_token_expires = timedelta(minutes=30)
    
    # Convert user model to dict for token
    user_data = {
        "email": user.email,
        "role": user.role,
        "full_name": user.full_name
    }
    
    access_token = create_access_token(
        data={"sub": user.email, "role": user.role, "user_id": str(user.id)},
        expires_delta=access_token_expires
    )
    
    return access_token, user_data