import { z } from 'zod';
export declare const IdSchema: z.ZodString;
export type Id = z.infer<typeof IdSchema>;
export declare const PaginationRequestSchema: z.ZodObject<{
    page: z.Zod<PERSON>efault<z.ZodNumber>;
    limit: z.<PERSON><PERSON><z.ZodNumber>;
    sortBy: z.Zod<PERSON>ptional<z.ZodString>;
    sortOrder: z.Zod<PERSON>efault<z.ZodEnum<["asc", "desc"]>>;
}, "strip", z.ZodTypeAny, {
    limit: number;
    page: number;
    sortOrder: "asc" | "desc";
    sortBy?: string | undefined;
}, {
    limit?: number | undefined;
    page?: number | undefined;
    sortBy?: string | undefined;
    sortOrder?: "asc" | "desc" | undefined;
}>;
export type PaginationRequest = z.infer<typeof PaginationRequestSchema>;
export declare const PaginationResponseSchema: z.ZodObject<{
    page: z.ZodNumber;
    limit: z.ZodNumber;
    total: z.ZodNumber;
    totalPages: z.ZodN<PERSON>;
    hasNext: z.ZodBoolean;
    hasPrev: z.ZodBoolean;
}, "strip", z.ZodTypeAny, {
    limit: number;
    page: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
}, {
    limit: number;
    page: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
}>;
export type PaginationResponse = z.infer<typeof PaginationResponseSchema>;
export declare const ApiErrorSchema: z.ZodObject<{
    code: z.ZodString;
    message: z.ZodString;
    details: z.ZodOptional<z.ZodString>;
    timestamp: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    code: string;
    message: string;
    timestamp: Date;
    details?: string | undefined;
}, {
    code: string;
    message: string;
    details?: string | undefined;
    timestamp?: Date | undefined;
}>;
export type ApiError = z.infer<typeof ApiErrorSchema>;
export declare const ApiResponseSchema: <T extends z.ZodTypeAny>(dataSchema: T) => z.ZodObject<{
    success: z.ZodBoolean;
    data: z.ZodOptional<T>;
    error: z.ZodOptional<z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    }, {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    }>>;
    timestamp: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, z.objectUtil.addQuestionMarks<z.baseObjectOutputType<{
    success: z.ZodBoolean;
    data: z.ZodOptional<T>;
    error: z.ZodOptional<z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    }, {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    }>>;
    timestamp: z.ZodDefault<z.ZodDate>;
}>, any> extends infer T_1 ? { [k in keyof T_1]: z.objectUtil.addQuestionMarks<z.baseObjectOutputType<{
    success: z.ZodBoolean;
    data: z.ZodOptional<T>;
    error: z.ZodOptional<z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    }, {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    }>>;
    timestamp: z.ZodDefault<z.ZodDate>;
}>, any>[k]; } : never, z.baseObjectInputType<{
    success: z.ZodBoolean;
    data: z.ZodOptional<T>;
    error: z.ZodOptional<z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    }, {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    }>>;
    timestamp: z.ZodDefault<z.ZodDate>;
}> extends infer T_2 ? { [k_1 in keyof T_2]: z.baseObjectInputType<{
    success: z.ZodBoolean;
    data: z.ZodOptional<T>;
    error: z.ZodOptional<z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    }, {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    }>>;
    timestamp: z.ZodDefault<z.ZodDate>;
}>[k_1]; } : never>;
export type ApiResponse<T = unknown> = {
    success: boolean;
    data?: T;
    error?: ApiError;
    timestamp: Date;
};
export declare const DateRangeSchema: z.ZodEffects<z.ZodObject<{
    startDate: z.ZodDate;
    endDate: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    startDate: Date;
    endDate: Date;
}, {
    startDate: Date;
    endDate: Date;
}>, {
    startDate: Date;
    endDate: Date;
}, {
    startDate: Date;
    endDate: Date;
}>;
export type DateRange = z.infer<typeof DateRangeSchema>;
export declare const FileUploadSchema: z.ZodObject<{
    filename: z.ZodString;
    mimetype: z.ZodString;
    size: z.ZodNumber;
    buffer: z.ZodOptional<z.ZodType<Buffer<ArrayBufferLike>, z.ZodTypeDef, Buffer<ArrayBufferLike>>>;
}, "strip", z.ZodTypeAny, {
    filename: string;
    mimetype: string;
    size: number;
    buffer?: Buffer<ArrayBufferLike> | undefined;
}, {
    filename: string;
    mimetype: string;
    size: number;
    buffer?: Buffer<ArrayBufferLike> | undefined;
}>;
export type FileUpload = z.infer<typeof FileUploadSchema>;
export declare const SearchRequestSchema: z.ZodObject<{
    page: z.ZodDefault<z.ZodNumber>;
    limit: z.ZodDefault<z.ZodNumber>;
    sortBy: z.ZodOptional<z.ZodString>;
    sortOrder: z.ZodDefault<z.ZodEnum<["asc", "desc"]>>;
    query: z.ZodString;
    filters: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
}, "strip", z.ZodTypeAny, {
    limit: number;
    page: number;
    sortOrder: "asc" | "desc";
    query: string;
    sortBy?: string | undefined;
    filters?: Record<string, any> | undefined;
}, {
    query: string;
    limit?: number | undefined;
    page?: number | undefined;
    sortBy?: string | undefined;
    sortOrder?: "asc" | "desc" | undefined;
    filters?: Record<string, any> | undefined;
}>;
export type SearchRequest = z.infer<typeof SearchRequestSchema>;
export declare const StatusSchema: z.ZodEnum<["active", "inactive", "pending", "completed", "error"]>;
export type Status = z.infer<typeof StatusSchema>;
//# sourceMappingURL=common.schemas.d.ts.map