/* MainHomepage.css */
/* Modern styling for the AI Trading Platform homepage */

/* Chat Demo Section */
.chat-demo-section {
  background: #f8fafc;
  padding: 80px 20px;
  min-height: 70vh;
  position: relative;
  z-index: 1;
  margin-bottom: 0;
}

.chat-demo-section h2 {
  text-align: center;
  font-size: 36px;
  margin-bottom: 20px;
  color: #1e293b;
}

.chat-demo-section > .section-container > p {
  text-align: center;
  font-size: 18px;
  color: #64748b;
  margin-bottom: 60px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.chat-layout {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
  max-width: 1400px;
  margin: 0 auto;
  height: 600px;
  position: relative;
  z-index: 1;
}

.chat-layout.expanded {
  grid-template-columns: 1fr;
}

.prompts-sidebar.hidden {
  display: none;
}

/* Category filters */
.category-filters {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.category-filters .filter {
  padding: 5px 15px;
  border-radius: 20px;
  background-color: #f1f5f9;
  color: #475569;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.category-filters .filter:hover {
  background-color: #e2e8f0;
}

.category-filters .filter.active {
  background-color: #3b82f6;
  color: white;
}

.template-label {
  font-size: 12px;
  color: #64748b;
  margin-right: 5px;
}

/* MQL5 Tools Section */
.mql5-tools {
  background: #f0f7ff;
  padding: 80px 20px;
  min-height: 50vh;
}

.mql5-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.mql5-description h3 {
  font-size: 24px;
  margin-bottom: 20px;
  color: #0f172a;
}

.mql5-description p {
  font-size: 16px;
  line-height: 1.6;
  color: #334155;
  margin-bottom: 20px;
}

.mql5-description ul {
  margin-left: 20px;
  margin-bottom: 20px;
}

.mql5-description ul li {
  margin-bottom: 10px;
  color: #334155;
}

.premium-note {
  background: #f8fafc;
  border-left: 4px solid #3b82f6;
  padding: 15px;
  margin: 20px 0;
  border-radius: 4px;
}

.code-preview {
  background: #1e293b;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  max-height: 400px;
  overflow-y: auto;
}

.code-preview pre {
  margin: 0;
}

.code-preview code {
  color: #e2e8f0;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 14px;
  line-height: 1.5;
}

/* Prompts Sidebar */
.prompts-sidebar {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
}

.prompts-sidebar h3 {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  padding-bottom: 16px;
  border-bottom: 2px solid #f1f5f9;
}

.prompt-list {
  flex: 1;
  overflow-y: auto;
  margin: -8px;
  padding: 8px;
}

.prompt-list::-webkit-scrollbar {
  width: 6px;
}

.prompt-list::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.prompt-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.prompt-list::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.prompt-card-compact {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  margin-bottom: 12px;
  background: #f8fafc;
  border-radius: 12px;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.prompt-card-compact:hover {
  border-color: #2563eb;
  background: #eff6ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.15);
}

.prompt-card-compact.active {
  border-color: #2563eb;
  background: linear-gradient(135deg, #eff6ff, #dbeafe);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(37, 99, 235, 0.25);
  position: relative;
}

.prompt-card-compact.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  border-radius: 2px 0 0 2px;
  animation: pulse-border 2s infinite;
}

@keyframes pulse-border {
  0%, 100% { 
    opacity: 1; 
    box-shadow: 0 0 0 0 rgba(37, 99, 235, 0.4);
  }
  50% { 
    opacity: 0.8;
    box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
  }
}

.prompt-card-compact.active .prompt-icon {
  color: #1d4ed8;
}

.prompt-card-compact.active .prompt-content h4 {
  color: #1e293b;
  font-weight: 700;
}

.prompt-card-compact.active .prompt-arrow {
  color: #2563eb;
  transform: translateX(2px);
}

.prompt-icon {
  color: #2563eb;
  flex-shrink: 0;
  margin-top: 2px;
}

.prompt-content {
  flex: 1;
  min-width: 0;
}

.prompt-content h4 {
  margin: 0 0 6px 0;
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  line-height: 1.3;
}

.prompt-content p {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #64748b;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.prompt-category {
  font-size: 10px;
  color: #8b5cf6;
  text-transform: uppercase;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.prompt-arrow {
  color: #94a3b8;
  flex-shrink: 0;
  width: 16px;
  height: 16px;
  margin-top: 2px;
  transition: all 0.3s ease;
}

.prompt-card-compact:hover .prompt-arrow {
  color: #2563eb;
  transform: translateX(2px);
}

/* Chatbot Main */
.chatbot-main {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
}

.chatbot-main > *:last-child {
  flex: 1;
  overflow: hidden;
}

.chatbot-content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
}

.chatbot-content-wrapper > * {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Hide the StrategyChatbot's internal header since we have our own */
.chatbot-content-wrapper .flex.items-center.justify-between.p-4.border-b {
  display: none;
}

/* Ensure the input area is always visible and accessible */
.chatbot-content-wrapper .p-4.border-t {
  flex-shrink: 0;
  background: white;
}

/* Make sure the messages area is scrollable */
.chatbot-content-wrapper .flex-1.overflow-y-auto {
  min-height: 200px;
}

.chatbot-header-integrated {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.chatbot-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.chatbot-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.chatbot-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.active-prompt-indicator {
  background: #2563eb;
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  margin-left: 12px;
  animation: slide-in 0.3s ease;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@keyframes slide-in {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.chatbot-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #10b981;
  font-weight: 500;
}

.status-dot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.expand-btn {
  background: #e2e8f0;
  border: none;
  border-radius: 6px;
  padding: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  color: #64748b;
}

.expand-btn:hover {
  background: #cbd5e1;
  color: #1e293b;
  transform: scale(1.05);
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 40px 20px;
  color: #64748b;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #2563eb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced Pricing */
.popular-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  color: white;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.pricing-card {
  position: relative;
}

.pricing-card.featured {
  transform: scale(1.05);
  border: 2px solid #2563eb;
  box-shadow: 0 20px 40px rgba(37, 99, 235, 0.2);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .chat-layout {
    grid-template-columns: 2fr 1fr;
    gap: 20px;
  }
}

@media (max-width: 968px) {
  .chat-layout {
    grid-template-columns: 1fr;
    height: auto;
  }
  
  .prompts-sidebar {
    order: 2;
    height: 400px;
  }
  
  .chatbot-main {
    order: 1;
    height: 500px;
  }
}

@media (max-width: 640px) {
  .chat-demo-section {
    padding: 60px 15px;
  }
  
  .chat-layout {
    gap: 15px;
  }
  
  .prompts-sidebar,
  .chatbot-main {
    margin: 0 -5px;
  }
}

/* Integration with existing styles */
.enhanced-homepage .chat-demo-section .section-container {
  max-width: none;
  padding: 0;
}

/* Features Section Adjustments */
.enhanced-homepage .features {
  background: white;
  margin-top: 40px;
  padding-top: 80px;
  clear: both;
}
