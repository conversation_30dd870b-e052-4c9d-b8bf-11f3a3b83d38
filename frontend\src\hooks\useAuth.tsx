/**
 * Authentication Hook & Context
 * Manages user authentication state and operations
 */

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { toast } from 'react-hot-toast';
import { apiService } from '@/services/api';
import { webSocketService } from '@/services/websocket';
import type { User } from '@shared/schemas';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, fullName?: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  updateUser: (updates: Partial<User>) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user && !!apiService.getAuthToken();

  // Initialize auth state on mount
  useEffect(() => {
    initializeAuth();
  }, []);

  // Set up WebSocket when user changes
  useEffect(() => {
    if (user && !webSocketService.isWebSocketConnected()) {
      webSocketService.connect().catch(error => {
        console.error('Failed to connect WebSocket:', error);
      });
    } else if (!user && webSocketService.isWebSocketConnected()) {
      webSocketService.disconnect();
    }
  }, [user]);

  const initializeAuth = async () => {
    const token = apiService.getAuthToken();
    
    if (!token) {
      setIsLoading(false);
      return;
    }

    try {
      const userData = await apiService.getCurrentUser();
      setUser(userData);
      
      // Connect WebSocket for real-time updates
      await webSocketService.connect();
      
      toast.success(`Welcome back, ${userData.fullName || userData.email}!`);
    } catch (error) {
      console.error('Failed to initialize auth:', error);
      // Clear invalid token
      apiService.clearAuthToken();
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      
      const response = await apiService.login(email, password);
      
      // Store token
      apiService.setAuthToken(response.token);
      
      // Set user
      setUser(response.user);
      
      // Connect WebSocket
      await webSocketService.connect();
      
      toast.success(`Welcome, ${response.user.fullName || response.user.email}!`);
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (email: string, password: string, fullName?: string) => {
    try {
      setIsLoading(true);
      
      const response = await apiService.register(email, password, fullName);
      
      // Store token
      apiService.setAuthToken(response.token);
      
      // Set user
      setUser(response.user);
      
      // Connect WebSocket
      await webSocketService.connect();
      
      toast.success(`Welcome to AI Trading Platform, ${response.user.fullName || response.user.email}!`);
    } catch (error) {
      console.error('Registration failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      await apiService.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear state regardless of API call success
      setUser(null);
      apiService.clearAuthToken();
      webSocketService.disconnect();
      
      toast.success('Logged out successfully');
    }
  };

  const refreshUser = async () => {
    if (!isAuthenticated) return;

    try {
      const userData = await apiService.getCurrentUser();
      setUser(userData);
    } catch (error) {
      console.error('Failed to refresh user:', error);
      // Don't throw here to avoid disrupting the app
    }
  };

  const updateUser = (updates: Partial<User>) => {
    if (user) {
      setUser({ ...user, ...updates });
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout,
    refreshUser,
    updateUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
}

// Hook for protecting routes
export function useRequireAuth() {
  const { isAuthenticated, isLoading } = useAuth();
  
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      window.location.href = '/login';
    }
  }, [isAuthenticated, isLoading]);
  
  return { isAuthenticated, isLoading };
}

// Hook for subscription tier checks
export function useSubscriptionTier() {
  const { user } = useAuth();
  
  const tier = user?.subscriptionTier || 'free';
  
  const hasFeature = (feature: string): boolean => {
    const features: Record<string, string[]> = {
      free: ['basic_chat', 'file_upload', 'basic_backtests'],
      solo: ['basic_chat', 'file_upload', 'basic_backtests', 'advanced_chat', 'multiple_models'],
      pro: ['basic_chat', 'file_upload', 'basic_backtests', 'advanced_chat', 'multiple_models', 'dgm_experiments', 'auto_deployment'],
      enterprise: ['basic_chat', 'file_upload', 'basic_backtests', 'advanced_chat', 'multiple_models', 'dgm_experiments', 'auto_deployment', 'priority_support', 'custom_models'],
    };
    
    return features[tier]?.includes(feature) || false;
  };
  
  const canUseFeature = (feature: string): { allowed: boolean; reason?: string } => {
    if (hasFeature(feature)) {
      return { allowed: true };
    }
    
    const upgradeMessage = tier === 'free' 
      ? 'Upgrade to Solo or higher to access this feature'
      : tier === 'solo'
      ? 'Upgrade to Pro or higher to access this feature'
      : tier === 'pro'
      ? 'Upgrade to Enterprise to access this feature'
      : 'This feature is not available';
    
    return { 
      allowed: false, 
      reason: upgradeMessage 
    };
  };
  
  return {
    tier,
    hasFeature,
    canUseFeature,
    isFreeTier: tier === 'free',
    isSoloTier: tier === 'solo',
    isProTier: tier === 'pro',
    isEnterpriseTier: tier === 'enterprise',
  };
}

// Hook for API quota monitoring
export function useApiQuota() {
  const { user, updateUser } = useAuth();
  
  useEffect(() => {
    // Subscribe to quota updates via WebSocket
    const handleQuotaUpdate = (data: { used: number; limit: number }) => {
      if (user) {
        updateUser({
          apiQuotaUsed: data.used,
          apiQuotaLimit: data.limit,
        });
      }
    };
    
    webSocketService.on('user:quota_updated', handleQuotaUpdate);
    
    return () => {
      webSocketService.off('user:quota_updated', handleQuotaUpdate);
    };
  }, [user, updateUser]);
  
  const quotaUsed = user?.apiQuotaUsed || 0;
  const quotaLimit = user?.apiQuotaLimit || 1000;
  const quotaPercentage = Math.round((quotaUsed / quotaLimit) * 100);
  
  const isNearLimit = quotaPercentage >= 80;
  const isAtLimit = quotaPercentage >= 100;
  
  return {
    quotaUsed,
    quotaLimit,
    quotaPercentage,
    quotaRemaining: Math.max(0, quotaLimit - quotaUsed),
    isNearLimit,
    isAtLimit,
  };
}