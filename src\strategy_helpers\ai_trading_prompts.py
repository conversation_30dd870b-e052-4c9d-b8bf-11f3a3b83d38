"""
AI Trading Prompts - Enhanced Strategy Helper System
Integrates proven ChatGPT prompts for trading analysis and strategy development
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import json
import logging

logger = logging.getLogger(__name__)

class PromptCategory(Enum):
    """Categories of trading prompts"""
    MARKET_ANALYSIS = "market_analysis"
    TECHNICAL_ANALYSIS = "technical_analysis"
    TRADE_EXECUTION = "trade_execution"
    TRADE_JOURNALING = "trade_journaling"
    PERFORMANCE_REVIEW = "performance_review"
    RESEARCH = "research"
    PSYCHOLOGY = "psychology"
    LEARNING = "learning"
    BACKTESTING = "backtesting"

@dataclass
class TradingPrompt:
    """Structure for trading prompts"""
    id: str
    category: PromptCategory
    title: str
    description: str
    prompt_template: str
    variables: List[str]
    example_usage: Optional[str] = None
    expected_output: Optional[str] = None

class AITradingPromptsLibrary:
    """
    Library of AI trading prompts for strategy development and analysis
    Based on proven ChatGPT prompts for effective trading
    """
    
    def __init__(self):
        self.prompts = self._initialize_prompts()
    
    def _initialize_prompts(self) -> Dict[str, TradingPrompt]:
        """Initialize the library with proven trading prompts"""
        
        prompts = {
            # Market Analysis Prompts
            "market_scanner": TradingPrompt(
                id="market_scanner",
                category=PromptCategory.MARKET_ANALYSIS,
                title="Market Analysis & Asset Scanner",
                description="Identify trading assets that meet specific criteria",
                prompt_template="""Act as a day trading assistant. Your task is to identify trading assets that meet the specified {criteria}. 

Utilize your expertise and available market analysis tools to scan, filter, and evaluate potential assets for trading. Once identified, create a comprehensive list with supporting data for each asset, indicating why it meets the criteria. 

Ensure that all information is up-to-date and relevant to the current market conditions.

Focus on:
1. Recent performance trends
2. Market news and catalysts
3. Technical indicators (RSI, MACD, Moving Averages)
4. Volume analysis
5. Support and resistance levels

Provide a ranked list with risk assessment for each asset.""",
                variables=["criteria"],
                example_usage="criteria: 'tech stocks with strong momentum and AI exposure'",
                expected_output="Ranked list of assets with technical analysis and risk assessment"
            ),
            
            "sector_analysis": TradingPrompt(
                id="sector_analysis",
                category=PromptCategory.MARKET_ANALYSIS,
                title="Sector Rotation Analysis",
                description="Analyze sector performance and rotation opportunities",
                prompt_template="""Act as a market analyst specializing in sector rotation. Analyze the current market environment and identify sectors showing:

1. **Outperformance**: Sectors beating the market
2. **Rotation Signals**: Money flow between sectors
3. **Economic Drivers**: Fundamental factors driving sector performance
4. **Technical Strength**: Chart patterns and momentum indicators

For the {time_frame} timeframe, provide:
- Top 3 strongest sectors with supporting evidence
- Top 3 weakest sectors to avoid
- Sector rotation recommendations
- Key stocks within recommended sectors
- Risk factors and market conditions to monitor

Include both fundamental and technical analysis in your assessment.""",
                variables=["time_frame"],
                example_usage="time_frame: 'next 3 months'",
                expected_output="Comprehensive sector analysis with rotation recommendations"
            ),
            
            # Technical Analysis Prompts
            "technical_analyzer": TradingPrompt(
                id="technical_analyzer",
                category=PromptCategory.TECHNICAL_ANALYSIS,
                title="Comprehensive Technical Analysis",
                description="Detailed technical analysis with entry/exit points",
                prompt_template="""Act as an experienced day trader. Your objective is to analyze the price and volume patterns of {trading_asset} to identify potential buying or selling opportunities.

Utilize advanced charting tools and technical indicators to scrutinize both short-term and long-term patterns, taking into account historical data and recent market movements. 

Assess the correlation between price and volume to gauge the strength or weakness of a particular price trend. 

Provide a comprehensive analysis report that details:
1. **Support and Resistance Levels**: Key price levels
2. **Breakout/Breakdown Points**: Critical levels to watch
3. **Technical Indicators**: RSI, MACD, Bollinger Bands, Moving Averages
4. **Volume Analysis**: Volume patterns and confirmations
5. **Chart Patterns**: Triangles, flags, head and shoulders, etc.
6. **Risk/Reward Scenarios**: Entry, stop-loss, and target levels
7. **Anomalies and Divergences**: Warning signs or confirmations

Your analysis should be backed by logical reasoning and include potential risk and reward scenarios. Always adhere to best practices in technical analysis and maintain the highest standards of accuracy and objectivity.""",
                variables=["trading_asset"],
                example_usage="trading_asset: 'NVDA (NVIDIA Corporation)'",
                expected_output="Detailed technical analysis report with specific trading levels"
            ),
            
            "pattern_recognition": TradingPrompt(
                id="pattern_recognition",
                category=PromptCategory.TECHNICAL_ANALYSIS,
                title="Chart Pattern Recognition",
                description="Identify and analyze chart patterns for trading opportunities",
                prompt_template="""Act as a technical analysis expert specializing in chart pattern recognition. Analyze the {asset} chart for the {timeframe} and identify:

**Classic Patterns:**
- Head and Shoulders / Inverse Head and Shoulders
- Double Top / Double Bottom
- Triangles (Ascending, Descending, Symmetrical)
- Flags and Pennants
- Cup and Handle
- Wedges (Rising, Falling)

**For each identified pattern, provide:**
1. Pattern type and completion status
2. Key levels (necklines, breakout points)
3. Volume confirmation requirements
4. Price targets and measurement rules
5. Failure scenarios and invalidation levels
6. Probability of success based on market context
7. Recommended position sizing

**Additional Analysis:**
- Multiple timeframe confirmation
- Market context and trend alignment
- Risk management recommendations

Focus on actionable patterns with clear entry and exit criteria.""",
                variables=["asset", "timeframe"],
                example_usage="asset: 'EUR/USD', timeframe: 'daily chart'",
                expected_output="Pattern analysis with specific trading recommendations"
            ),
            
            # Trade Execution Prompts
            "trade_executor": TradingPrompt(
                id="trade_executor",
                category=PromptCategory.TRADE_EXECUTION,
                title="Trade Execution Strategy",
                description="Determine optimal entry, stop-loss, and target points",
                prompt_template="""Act as an experienced day trader. Based on your comprehensive analysis of current market conditions, historical data, and emerging trends, decide on optimal entry, stop-loss, and target points for {trading_asset}.

Begin by thoroughly reviewing:
1. **Recent Price Action**: Last 5-10 trading sessions
2. **Key Technical Indicators**: RSI, MACD, Bollinger Bands, Volume
3. **Market Context**: Overall trend, volatility, market sentiment
4. **News and Events**: Relevant fundamental factors
5. **Support/Resistance**: Key levels from multiple timeframes

**Provide specific recommendations for:**
- **Entry Strategy**: Exact price levels or conditions for entry
- **Position Sizing**: Recommended position size based on risk
- **Stop Loss**: Protective stop level with reasoning
- **Take Profit**: Target levels with partial profit-taking plan
- **Risk/Reward Ratio**: Expected return vs. risk assessment
- **Market Conditions**: Ideal conditions for this trade
- **Plan B**: Alternative scenarios and adjustments

**Include timing considerations:**
- Best time of day/week for entry
- Market session preferences
- Economic calendar awareness

Format as a complete trading plan ready for execution.""",
                variables=["trading_asset"],
                example_usage="trading_asset: 'AAPL with current price at $175'",
                expected_output="Complete trading plan with specific entry/exit criteria"
            ),
            
            "risk_calculator": TradingPrompt(
                id="risk_calculator",
                category=PromptCategory.TRADE_EXECUTION,
                title="Position Sizing & Risk Calculator",
                description="Calculate optimal position size and risk parameters",
                prompt_template="""Act as a risk management specialist. For the proposed trade on {asset}, calculate optimal position sizing and risk parameters.

**Given Information:**
- Account Size: {account_size}
- Risk Per Trade: {risk_percentage}%
- Entry Price: {entry_price}
- Stop Loss: {stop_loss}
- Take Profit: {take_profit}

**Calculate and Provide:**
1. **Position Size**: Exact number of shares/lots/units
2. **Dollar Risk**: Total amount at risk
3. **Risk/Reward Ratio**: Potential profit vs. loss
4. **Break-even Analysis**: Required win rate for profitability
5. **Maximum Drawdown Impact**: Effect on account if stopped out
6. **Correlation Risk**: If holding related positions
7. **Volatility Adjustment**: Position size adjustment for volatility

**Risk Management Rules:**
- Never risk more than specified percentage
- Consider correlation with existing positions
- Adjust for market volatility
- Account for slippage and commissions

**Additional Recommendations:**
- Position scaling strategies (partial entries/exits)
- Trailing stop suggestions
- Portfolio heat analysis
- Stress testing scenarios

Provide both conservative and aggressive position sizing options.""",
                variables=["asset", "account_size", "risk_percentage", "entry_price", "stop_loss", "take_profit"],
                example_usage="asset: 'TSLA', account_size: '$50000', risk_percentage: '2', entry_price: '$200', stop_loss: '$190', take_profit: '$220'",
                expected_output="Detailed position sizing calculation with risk analysis"
            ),
            
            # Trade Journaling Prompts
            "trade_journal": TradingPrompt(
                id="trade_journal",
                category=PromptCategory.TRADE_JOURNALING,
                title="Comprehensive Trade Journal",
                description="Structure and analyze trading performance",
                prompt_template="""Act as an experienced day trader and create a comprehensive trade journal entry for the following trade:

**Trade Details:**
- Asset: {asset}
- Direction: {direction}
- Entry: {entry_price} at {entry_time}
- Exit: {exit_price} at {exit_time}
- Position Size: {position_size}
- P&L: {profit_loss}

**Journal Structure:**
1. **Pre-Trade Analysis**
   - Market conditions at entry
   - Technical setup and reasoning
   - Risk/reward calculation
   - Expected outcome

2. **Trade Execution**
   - Entry timing and execution quality
   - Stop loss and target placement
   - Position management decisions
   - Emotional state during trade

3. **Post-Trade Review**
   - What went right/wrong
   - Adherence to trading plan
   - Market behavior vs. expectations
   - Lessons learned

4. **Performance Metrics**
   - Win/loss classification
   - Risk/reward achieved
   - Time in trade
   - Efficiency rating (1-10)

5. **Improvement Areas**
   - Specific areas for improvement
   - Pattern recognition
   - Emotional management notes
   - Strategy refinements

**Additional Analysis:**
- Compare to similar setups
- Market context impact
- Timing optimization
- Position sizing effectiveness

Provide actionable insights for future trades.""",
                variables=["asset", "direction", "entry_price", "entry_time", "exit_price", "exit_time", "position_size", "profit_loss"],
                example_usage="asset: 'EURUSD', direction: 'Long', entry_price: '1.0850', entry_time: '09:30 EST', exit_price: '1.0875', exit_time: '11:45 EST', position_size: '1 lot', profit_loss: '+$250'",
                expected_output="Detailed trade journal entry with performance analysis"
            ),
            
            # Performance Review Prompts
            "performance_analyzer": TradingPrompt(
                id="performance_analyzer",
                category=PromptCategory.PERFORMANCE_REVIEW,
                title="Trading Performance Analysis",
                description="Analyze trading performance and identify improvements",
                prompt_template="""Act as a trading performance analyst. Review the trading performance data for the {time_period} and provide a comprehensive analysis.

**Performance Data:**
- Total Trades: {total_trades}
- Winning Trades: {winning_trades}
- Losing Trades: {losing_trades}
- Win Rate: {win_rate}%
- Average Win: {avg_win}
- Average Loss: {avg_loss}
- Profit Factor: {profit_factor}
- Maximum Drawdown: {max_drawdown}
- Sharpe Ratio: {sharpe_ratio}

**Analysis Framework:**
1. **Performance Metrics Review**
   - Win rate analysis and benchmarking
   - Risk/reward ratio assessment
   - Profit factor evaluation
   - Drawdown analysis and recovery

2. **Trade Distribution Analysis**
   - Best performing setups
   - Worst performing patterns
   - Time-based performance (day/hour)
   - Asset class performance

3. **Risk Management Assessment**
   - Position sizing effectiveness
   - Stop loss performance
   - Maximum risk exposure
   - Correlation analysis

4. **Behavioral Analysis**
   - Emotional trading patterns
   - Discipline adherence
   - Overtrading tendencies
   - Revenge trading incidents

5. **Improvement Recommendations**
   - Specific areas for improvement
   - Strategy modifications
   - Risk management adjustments
   - Psychological improvements

**Benchmarking:**
- Compare to market performance
- Industry standard metrics
- Personal historical performance
- Goal achievement assessment

Provide specific, actionable recommendations for the next {time_period}.""",
                variables=["time_period", "total_trades", "winning_trades", "losing_trades", "win_rate", "avg_win", "avg_loss", "profit_factor", "max_drawdown", "sharpe_ratio"],
                example_usage="time_period: 'last quarter', total_trades: '150', winning_trades: '85', losing_trades: '65', win_rate: '56.7', avg_win: '$125', avg_loss: '$95', profit_factor: '1.38', max_drawdown: '8.5%', sharpe_ratio: '1.2'",
                expected_output="Comprehensive performance analysis with improvement recommendations"
            ),
            
            # Research Prompts
            "market_research": TradingPrompt(
                id="market_research",
                category=PromptCategory.RESEARCH,
                title="Market Research & Analysis",
                description="Comprehensive market research for trading decisions",
                prompt_template="""Act as a financial research analyst. Conduct comprehensive research on {research_topic} to support trading decisions.

**Research Framework:**
1. **Fundamental Analysis**
   - Economic indicators and trends
   - Industry analysis and outlook
   - Company-specific factors (if applicable)
   - Regulatory environment
   - Competitive landscape

2. **Technical Analysis**
   - Price trends and patterns
   - Volume analysis
   - Momentum indicators
   - Support/resistance levels
   - Multiple timeframe analysis

3. **Market Sentiment**
   - Institutional positioning
   - Retail sentiment indicators
   - Options flow analysis
   - News sentiment analysis
   - Social media trends

4. **Risk Factors**
   - Systematic risks
   - Specific risks
   - Black swan events
   - Correlation risks
   - Liquidity considerations

5. **Trading Implications**
   - Directional bias
   - Time horizon considerations
   - Position sizing recommendations
   - Entry/exit strategies
   - Risk management requirements

**Sources to Consider:**
- Economic data releases
- Earnings reports and guidance
- Central bank communications
- Geopolitical developments
- Technical chart analysis

**Deliverables:**
- Executive summary
- Key findings and insights
- Trading recommendations
- Risk assessment
- Monitoring checklist

Focus on actionable insights that directly impact trading decisions.""",
                variables=["research_topic"],
                example_usage="research_topic: 'Impact of Federal Reserve policy changes on technology stocks'",
                expected_output="Comprehensive research report with trading implications"
            ),
            
            # Psychology Prompts
            "trading_psychology": TradingPrompt(
                id="trading_psychology",
                category=PromptCategory.PSYCHOLOGY,
                title="Trading Psychology Coach",
                description="Address psychological challenges in trading",
                prompt_template="""Act as a trading psychology coach. Help address the psychological challenge: {psychological_issue}.

**Assessment Framework:**
1. **Issue Identification**
   - Root cause analysis
   - Behavioral patterns
   - Emotional triggers
   - Impact on performance

2. **Psychological Factors**
   - Fear and greed dynamics
   - Cognitive biases at play
   - Emotional regulation challenges
   - Confidence issues

3. **Practical Solutions**
   - Specific techniques and exercises
   - Mindset adjustments
   - Behavioral modifications
   - Routine improvements

4. **Implementation Plan**
   - Step-by-step approach
   - Daily practices
   - Progress tracking methods
   - Accountability measures

**Common Issues Addressed:**
- Fear of missing out (FOMO)
- Revenge trading
- Overconfidence after wins
- Analysis paralysis
- Emotional decision making
- Discipline breakdown
- Stress management

**Tools and Techniques:**
- Meditation and mindfulness
- Visualization exercises
- Journaling practices
- Risk management rules
- Trading plan adherence
- Performance review processes

**Long-term Development:**
- Building mental resilience
- Developing emotional intelligence
- Creating sustainable habits
- Continuous improvement mindset

Provide practical, actionable advice that can be implemented immediately.""",
                variables=["psychological_issue"],
                example_usage="psychological_issue: 'tendency to hold losing trades too long due to hope they will recover'",
                expected_output="Psychological coaching plan with specific techniques and exercises"
            ),
            
            # Learning Prompts
            "strategy_educator": TradingPrompt(
                id="strategy_educator",
                category=PromptCategory.LEARNING,
                title="Trading Strategy Education",
                description="Learn and understand trading strategies",
                prompt_template="""Act as a trading education specialist. Provide comprehensive education on {trading_strategy}.

**Educational Framework:**
1. **Strategy Overview**
   - Definition and core concepts
   - Historical background
   - Market conditions where it works best
   - Success rate and expectations

2. **Detailed Mechanics**
   - Entry criteria and signals
   - Exit rules and conditions
   - Position sizing guidelines
   - Risk management requirements

3. **Technical Implementation**
   - Required indicators and tools
   - Chart setup and timeframes
   - Signal identification process
   - Execution best practices

4. **Real-World Examples**
   - Historical case studies
   - Step-by-step trade examples
   - Common mistakes to avoid
   - Optimization techniques

5. **Advanced Concepts**
   - Strategy variations
   - Market adaptation methods
   - Combination with other strategies
   - Automation possibilities

**Learning Objectives:**
- Understand the strategy completely
- Identify optimal market conditions
- Execute trades with confidence
- Manage risk effectively
- Adapt to changing markets

**Practice Recommendations:**
- Paper trading exercises
- Backtesting procedures
- Forward testing approach
- Performance evaluation methods

**Resources for Further Learning:**
- Recommended books and articles
- Online courses and tutorials
- Practice platforms
- Community forums

Make the content accessible for {skill_level} traders.""",
                variables=["trading_strategy", "skill_level"],
                example_usage="trading_strategy: 'Bollinger Band squeeze breakout strategy', skill_level: 'intermediate'",
                expected_output="Comprehensive educational content on the specified strategy"
            ),
            
            # Backtesting Prompts
            "backtest_analyzer": TradingPrompt(
                id="backtest_analyzer",
                category=PromptCategory.BACKTESTING,
                title="Backtesting Analysis & Optimization",
                description="Analyze and optimize trading strategy backtests",
                prompt_template="""Act as a quantitative analyst specializing in backtesting. Analyze the backtest results for {strategy_name} and provide optimization recommendations.

**Backtest Data:**
- Strategy: {strategy_name}
- Time Period: {time_period}
- Total Trades: {total_trades}
- Win Rate: {win_rate}%
- Profit Factor: {profit_factor}
- Maximum Drawdown: {max_drawdown}%
- Sharpe Ratio: {sharpe_ratio}
- Average Trade: {avg_trade}

**Analysis Framework:**
1. **Performance Evaluation**
   - Risk-adjusted returns analysis
   - Drawdown analysis and recovery
   - Consistency of returns
   - Benchmark comparison

2. **Statistical Significance**
   - Sample size adequacy
   - Statistical confidence
   - Robustness testing
   - Out-of-sample validation

3. **Risk Assessment**
   - Maximum drawdown analysis
   - Volatility patterns
   - Tail risk evaluation
   - Correlation analysis

4. **Optimization Opportunities**
   - Parameter sensitivity analysis
   - Entry/exit rule improvements
   - Position sizing optimization
   - Risk management enhancements

5. **Market Regime Analysis**
   - Performance in different market conditions
   - Trending vs. ranging markets
   - Volatility impact
   - Seasonal patterns

**Optimization Recommendations:**
- Parameter adjustments
- Rule modifications
- Risk management improvements
- Market filter additions
- Position sizing changes

**Forward Testing Plan:**
- Out-of-sample testing approach
- Paper trading recommendations
- Live testing guidelines
- Performance monitoring

**Red Flags to Address:**
- Overfitting indicators
- Curve fitting issues
- Look-ahead bias
- Survivorship bias

Provide specific, actionable recommendations for strategy improvement.""",
                variables=["strategy_name", "time_period", "total_trades", "win_rate", "profit_factor", "max_drawdown", "sharpe_ratio", "avg_trade"],
                example_usage="strategy_name: 'RSI Mean Reversion', time_period: '2020-2023', total_trades: '245', win_rate: '62', profit_factor: '1.45', max_drawdown: '12.3', sharpe_ratio: '1.8', avg_trade: '$85'",
                expected_output="Detailed backtesting analysis with optimization recommendations"
            )
        }
        
        return prompts
    
    def get_prompt(self, prompt_id: str) -> Optional[TradingPrompt]:
        """Get a specific prompt by ID"""
        return self.prompts.get(prompt_id)
    
    def get_prompts_by_category(self, category: PromptCategory) -> List[TradingPrompt]:
        """Get all prompts in a specific category"""
        return [prompt for prompt in self.prompts.values() if prompt.category == category]
    
    def list_all_prompts(self) -> List[TradingPrompt]:
        """Get all available prompts"""
        return list(self.prompts.values())
    
    def format_prompt(self, prompt_id: str, **kwargs) -> Optional[str]:
        """Format a prompt with provided variables"""
        prompt = self.get_prompt(prompt_id)
        if not prompt:
            return None
        
        try:
            return prompt.prompt_template.format(**kwargs)
        except KeyError as e:
            logger.error(f"Missing variable {e} for prompt {prompt_id}")
            return None
    
    def get_prompt_variables(self, prompt_id: str) -> List[str]:
        """Get required variables for a prompt"""
        prompt = self.get_prompt(prompt_id)
        return prompt.variables if prompt else []
    
    def search_prompts(self, query: str) -> List[TradingPrompt]:
        """Search prompts by title or description"""
        query_lower = query.lower()
        results = []
        
        for prompt in self.prompts.values():
            if (query_lower in prompt.title.lower() or 
                query_lower in prompt.description.lower()):
                results.append(prompt)
        
        return results
    
    def export_prompts(self, category: Optional[PromptCategory] = None) -> Dict[str, Any]:
        """Export prompts to dictionary format"""
        if category:
            prompts_to_export = self.get_prompts_by_category(category)
        else:
            prompts_to_export = self.list_all_prompts()
        
        return {
            "prompts": [
                {
                    "id": p.id,
                    "category": p.category.value,
                    "title": p.title,
                    "description": p.description,
                    "prompt_template": p.prompt_template,
                    "variables": p.variables,
                    "example_usage": p.example_usage,
                    "expected_output": p.expected_output
                }
                for p in prompts_to_export
            ]
        }

# Example usage and testing
if __name__ == "__main__":
    # Initialize the library
    prompt_library = AITradingPromptsLibrary()
    
    # Example: Get market analysis prompt
    market_prompt = prompt_library.get_prompt("market_scanner")
    if market_prompt:
        print(f"Title: {market_prompt.title}")
        print(f"Description: {market_prompt.description}")
        print(f"Variables needed: {market_prompt.variables}")
        
        # Format the prompt with variables
        formatted = prompt_library.format_prompt(
            "market_scanner", 
            criteria="high-growth tech stocks with strong AI exposure"
        )
        print(f"\nFormatted prompt:\n{formatted}")
    
    # Example: Search for technical analysis prompts
    tech_prompts = prompt_library.search_prompts("technical")
    print(f"\nFound {len(tech_prompts)} technical analysis prompts:")
    for prompt in tech_prompts:
        print(f"- {prompt.title}")