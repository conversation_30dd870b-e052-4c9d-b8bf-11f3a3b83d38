#!/usr/bin/env python3
"""
Demo script to test complete MT5 integration with Ollama chatbot
"""

import asyncio
import aiohttp
import json
import sys
import time
from datetime import datetime

class MT5IntegrationDemo:
    def __init__(self):
        self.base_url = "http://localhost:8003"  # Try port 8003 since 8002 is in use
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_ollama_status(self):
        """Test Ollama server status"""
        print("🔍 Testing Ollama Server Status...")
        try:
            async with self.session.get(f"{self.base_url}/api/ollama/status") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Ollama Status: {data['message']}")
                    print(f"   Available models: {', '.join(data['models'])}")
                    return True
                else:
                    print(f"❌ Ollama not available (HTTP {response.status})")
                    return False
        except Exception as e:
            print(f"❌ Ollama connection failed: {e}")
            return False
    
    async def test_strategy_generation(self):
        """Test strategy generation with Ollama"""
        print("\n🤖 Testing Strategy Generation...")
        
        request_data = {
            "message": "Create a mean reversion strategy using RSI for EUR/USD with 2% risk per trade",
            "model": "gemma:2b",
            "conversation_id": f"test_{int(time.time())}"
        }
        
        try:
            async with self.session.post(f"{self.base_url}/api/ollama/chat", json=request_data) as response:
                if response.status == 200:
                    data = await response.json()
                    print("✅ Strategy Generated Successfully!")
                    print(f"   Response length: {len(data['response'])} characters")
                    
                    if data.get('template_data', {}).get('code'):
                        print("✅ Python code generated")
                        return data['template_data']['code']
                    else:
                        print("ℹ️  No Python code in response")
                        return None
                else:
                    print(f"❌ Strategy generation failed (HTTP {response.status})")
                    return None
        except Exception as e:
            print(f"❌ Strategy generation error: {e}")
            return None
    
    async def test_mt5_deployment(self, strategy_code):
        """Test MT5 strategy deployment"""
        print("\n🚀 Testing MT5 Strategy Deployment...")
        
        if not strategy_code:
            print("❌ No strategy code to deploy")
            return False
            
        deployment_data = {
            "strategy_name": "TestMeanReversionStrategy",
            "strategy_code": strategy_code,
            "parameters": {
                "symbol": "EURUSD",
                "risk_per_trade": 0.02,
                "max_positions": 3
            }
        }
        
        try:
            async with self.session.post(f"{self.base_url}/api/mt5/deploy", json=deployment_data) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('success'):
                        print("✅ Strategy Deployed Successfully!")
                        print(f"   Deployment ID: {data.get('deployment_id', 'N/A')}")
                        print(f"   Strategy Name: {data.get('strategy_name', 'N/A')}")
                        return data.get('deployment_id')
                    else:
                        print(f"❌ Deployment failed: {data.get('error', 'Unknown error')}")
                        return False
                else:
                    response_text = await response.text()
                    print(f"❌ MT5 deployment failed (HTTP {response.status}): {response_text}")
                    return False
        except Exception as e:
            print(f"❌ MT5 deployment error: {e}")
            return False
    
    async def test_strategy_status(self, deployment_id):
        """Test strategy status monitoring"""
        print("\n📊 Testing Strategy Status Monitoring...")
        
        if not deployment_id:
            print("❌ No deployment ID to check")
            return False
            
        try:
            async with self.session.get(f"{self.base_url}/api/mt5/status/{deployment_id}") as response:
                if response.status == 200:
                    data = await response.json()
                    print("✅ Strategy Status Retrieved!")
                    print(f"   Status: {data.get('status', 'Unknown')}")
                    print(f"   Uptime: {data.get('uptime_seconds', 0)} seconds")
                    return True
                else:
                    print(f"❌ Status check failed (HTTP {response.status})")
                    return False
        except Exception as e:
            print(f"❌ Status check error: {e}")
            return False
    
    async def run_full_test(self):
        """Run complete MT5 integration test"""
        print("🎯 Starting Complete MT5 Integration Test")
        print("=" * 50)
        
        # Test Ollama status
        ollama_ok = await self.test_ollama_status()
        
        # Test strategy generation
        strategy_code = None
        if ollama_ok:
            strategy_code = await self.test_strategy_generation()
        
        # Test MT5 deployment
        deployment_id = None
        if strategy_code:
            deployment_id = await self.test_mt5_deployment(strategy_code)
        
        # Test strategy monitoring
        if deployment_id:
            await self.test_strategy_status(deployment_id)
        
        print("\n" + "=" * 50)
        print("🏁 Test Summary:")
        print(f"   Ollama Status: {'✅' if ollama_ok else '❌'}")
        print(f"   Strategy Generation: {'✅' if strategy_code else '❌'}")
        print(f"   MT5 Deployment: {'✅' if deployment_id else '❌'}")
        print(f"   Status Monitoring: {'✅' if deployment_id else '❌'}")
        
        if all([ollama_ok, strategy_code, deployment_id]):
            print("\n🎉 ALL TESTS PASSED! MT5 Integration is working perfectly!")
        else:
            print("\n⚠️  Some tests failed. Check the logs above for details.")

async def main():
    """Main demo function"""
    print("🚀 AI Enhanced Trading Platform - MT5 Integration Demo")
    print(f"⏰ Started at: {datetime.now()}")
    
    # Test different ports in case 8002 is busy
    ports = [8003, 8002, 8001, 8000]
    
    for port in ports:
        print(f"\n🔌 Trying server on port {port}...")
        demo = MT5IntegrationDemo()
        demo.base_url = f"http://localhost:{port}"
        
        try:
            async with demo:
                # Quick connectivity test
                async with demo.session.get(f"{demo.base_url}/api/ollama/status", timeout=3) as response:
                    if response.status == 200:
                        print(f"✅ Connected to server on port {port}")
                        await demo.run_full_test()
                        return
        except:
            print(f"❌ Port {port} not available")
            continue
    
    print("\n❌ Could not connect to any server port. Make sure the server is running!")
    print("   Start the server with: python ollama_server.py")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n💥 Demo failed with error: {e}")
        sys.exit(1)
