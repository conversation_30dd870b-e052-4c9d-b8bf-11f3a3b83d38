"""
Async Strategy Monitor for Real-time Trading Updates
Provides enterprise-grade monitoring with audit trails and provenance tracking.
"""
import asyncio
import hashlib
import json
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)


class MonitoringStatus(Enum):
    """Monitoring status enumeration"""
    ACTIVE = "active"
    PAUSED = "paused"
    ERROR = "error"
    STOPPED = "stopped"


@dataclass
class AccountStats:
    """Account statistics with audit trail"""
    user_id: int
    strategy: str
    trades_today: int
    profit: float
    drawdown: float
    timestamp: datetime
    source_hash: str
    raw_data: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data


@dataclass
class MonitoringAlert:
    """Alert structure with provenance"""
    user_id: int
    alert_type: str
    message: str
    severity: str
    timestamp: datetime
    source_hash: str
    metadata: Dict[str, Any]


class AsyncStrategyMonitor:
    """
    Async monitor for trading strategies with enterprise-grade features:
    - Real-time account monitoring
    - Audit trail generation
    - Provenance tracking
    - Error handling and recovery
    - Rate limiting and throttling
    """
    
    def __init__(self, accounts: List[Dict[str, Any]], poll_interval: float = 30.0):
        self.accounts = accounts
        self.poll_interval = poll_interval
        self.status = MonitoringStatus.STOPPED
        self.last_stats: Dict[int, AccountStats] = {}
        self.error_count = 0
        self.max_errors = 5
        self._running = False
        
    async def start_monitoring(self) -> None:
        """Start the monitoring loop"""
        if self._running:
            logger.warning("Monitor already running")
            return
            
        self._running = True
        self.status = MonitoringStatus.ACTIVE
        logger.info(f"Starting monitor for {len(self.accounts)} accounts")
        
        try:
            while self._running:
                await self.poll_accounts_and_notify()
                await asyncio.sleep(self.poll_interval)
        except Exception as e:
            logger.error(f"Monitor loop error: {e}")
            self.status = MonitoringStatus.ERROR
            raise
        finally:
            self.status = MonitoringStatus.STOPPED
            
    async def stop_monitoring(self) -> None:
        """Stop the monitoring loop"""
        self._running = False
        self.status = MonitoringStatus.STOPPED
        logger.info("Monitor stopped")
        
    async def poll_accounts_and_notify(self) -> List[AccountStats]:
        """
        Poll all accounts and send notifications
        Returns list of account statistics
        """
        stats_list = []
        
        for account in self.accounts:
            try:
                stats = await self.fetch_account_stats(account)
                stats_list.append(stats)
                
                # Store for comparison
                self.last_stats[account["user_id"]] = stats
                
                # Send notification if significant change
                await self._check_and_notify(stats)
                
            except Exception as e:
                logger.error(f"Error polling account {account.get('user_id')}: {e}")
                self.error_count += 1
                
                if self.error_count >= self.max_errors:
                    self.status = MonitoringStatus.ERROR
                    raise Exception(f"Too many errors ({self.error_count})")
                    
        return stats_list
        
    async def fetch_account_stats(self, account: Dict[str, Any]) -> AccountStats:
        """
        Fetch account statistics from MT5/VPS
        This method should be mocked in tests
        """
        # Simulate async data fetching
        await asyncio.sleep(0.1)
        
        # In real implementation, this would connect to MT5/VPS
        # For now, return mock data structure
        raw_data = {
            "account_id": account["user_id"],
            "strategy": account["strategy"],
            "balance": 10000.0,
            "equity": 10127.0,
            "trades": [],
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        # Generate audit hash
        source_hash = self._generate_audit_hash(raw_data)
        
        return AccountStats(
            user_id=account["user_id"],
            strategy=account["strategy"],
            trades_today=2,
            profit=127.0,
            drawdown=3.2,
            timestamp=datetime.now(timezone.utc),
            source_hash=source_hash,
            raw_data=raw_data
        )
        
    async def _check_and_notify(self, stats: AccountStats) -> None:
        """Check for significant changes and notify"""
        from chatbot import TradingChatbot
        
        # Import here to avoid circular imports
        chatbot = TradingChatbot()
        
        # Always send update for now (in production, add logic for significant changes)
        update_data = {
            "user_id": stats.user_id,
            "strategy": stats.strategy,
            "profit": stats.profit,
            "drawdown": stats.drawdown,
            "trades_today": stats.trades_today,
            "source": stats.source_hash,
            "timestamp": stats.timestamp.isoformat()
        }
        
        await chatbot.send_update(update_data)
        
    def _generate_audit_hash(self, data: Dict[str, Any]) -> str:
        """Generate audit hash for provenance tracking"""
        # Sort keys for consistent hashing
        sorted_data = json.dumps(data, sort_keys=True)
        return f"MT5_VPS_AUDITED_{hashlib.sha256(sorted_data.encode()).hexdigest()[:16]}"
        
    async def get_monitoring_status(self) -> Dict[str, Any]:
        """Get current monitoring status"""
        return {
            "status": self.status.value,
            "accounts_count": len(self.accounts),
            "error_count": self.error_count,
            "last_poll": datetime.now(timezone.utc).isoformat(),
            "running": self._running
        }
        
    async def generate_health_report(self) -> Dict[str, Any]:
        """Generate comprehensive health report"""
        return {
            "monitor_status": await self.get_monitoring_status(),
            "account_stats": [stats.to_dict() for stats in self.last_stats.values()],
            "system_health": {
                "memory_usage": "normal",  # Would implement actual monitoring
                "cpu_usage": "normal",
                "network_latency": "normal"
            }
        }