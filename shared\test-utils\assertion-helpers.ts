/**
 * Custom assertion helpers for testing
 */

export const assertThat = {
  /**
   * Assert that a value is defined and not null
   */
  isDefined: <T>(value: T | undefined | null): asserts value is T => {
    expect(value).toBeDefined();
    expect(value).not.toBeNull();
  },

  /**
   * Assert that a value is a valid number
   */
  isValidNumber: (value: any): asserts value is number => {
    expect(typeof value).toBe('number');
    expect(isNaN(value)).toBe(false);
    expect(isFinite(value)).toBe(true);
  },

  /**
   * Assert that a value is a positive number
   */
  isPositiveNumber: (value: any): asserts value is number => {
    assertThat.isValidNumber(value);
    expect(value).toBeGreaterThan(0);
  },

  /**
   * Assert that a value is a valid date
   */
  isValidDate: (value: any): asserts value is Date => {
    expect(value).toBeInstanceOf(Date);
    expect(isNaN(value.getTime())).toBe(false);
  },

  /**
   * Assert that a string is not empty
   */
  isNonEmptyString: (value: any): asserts value is string => {
    expect(typeof value).toBe('string');
    expect(value.trim().length).toBeGreaterThan(0);
  },

  /**
   * Assert that an array is not empty
   */
  isNonEmptyArray: <T>(value: any): asserts value is T[] => {
    expect(Array.isArray(value)).toBe(true);
    expect(value.length).toBeGreaterThan(0);
  },

  /**
   * Assert that an object has specific properties
   */
  hasProperties: <T extends Record<string, any>>(
    obj: any,
    properties: (keyof T)[]
  ): asserts obj is T => {
    expect(typeof obj).toBe('object');
    expect(obj).not.toBeNull();
    
    for (const prop of properties) {
      expect(obj).toHaveProperty(prop as string);
    }
  },

  /**
   * Assert that a value matches a specific type structure
   */
  matchesStructure: <T>(value: any, structure: Partial<T>): asserts value is T => {
    expect(typeof value).toBe('object');
    expect(value).not.toBeNull();

    for (const [key, expectedType] of Object.entries(structure)) {
      expect(value).toHaveProperty(key);
      
      if (typeof expectedType === 'string') {
        expect(typeof value[key]).toBe(expectedType);
      } else if (expectedType instanceof Date) {
        assertThat.isValidDate(value[key]);
      } else if (Array.isArray(expectedType)) {
        expect(Array.isArray(value[key])).toBe(true);
      } else if (typeof expectedType === 'object') {
        expect(typeof value[key]).toBe('object');
      }
    }
  },

  /**
   * Assert that a promise resolves within a timeout
   */
  resolvesWithin: async <T>(
    promise: Promise<T>,
    timeoutMs: number
  ): Promise<T> => {
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error(`Promise did not resolve within ${timeoutMs}ms`)), timeoutMs);
    });

    return Promise.race([promise, timeoutPromise]);
  },

  /**
   * Assert that a promise rejects with a specific error
   */
  rejectsWith: async (
    promise: Promise<any>,
    expectedError?: string | RegExp | Error
  ): Promise<void> => {
    await expect(promise).rejects.toBeDefined();
    
    if (expectedError) {
      if (typeof expectedError === 'string') {
        await expect(promise).rejects.toThrow(expectedError);
      } else if (expectedError instanceof RegExp) {
        await expect(promise).rejects.toThrow(expectedError);
      } else if (expectedError instanceof Error) {
        await expect(promise).rejects.toThrow(expectedError.message);
      }
    }
  },
};

export const expectEventually = {
  /**
   * Assert that a condition becomes true within a timeout
   */
  toBeTruthy: async (
    condition: () => boolean | Promise<boolean>,
    timeoutMs: number = 5000,
    intervalMs: number = 100
  ): Promise<void> => {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeoutMs) {
      const result = await condition();
      if (result) {
        return;
      }
      await new Promise(resolve => setTimeout(resolve, intervalMs));
    }
    
    throw new Error(`Condition did not become truthy within ${timeoutMs}ms`);
  },

  /**
   * Assert that a value eventually equals an expected value
   */
  toEqual: async <T>(
    getValue: () => T | Promise<T>,
    expectedValue: T,
    timeoutMs: number = 5000,
    intervalMs: number = 100
  ): Promise<void> => {
    await expectEventually.toBeTruthy(
      async () => {
        const value = await getValue();
        return JSON.stringify(value) === JSON.stringify(expectedValue);
      },
      timeoutMs,
      intervalMs
    );
  },
};

export const expectArray = {
  /**
   * Assert that an array contains items matching a predicate
   */
  toContainItemsMatching: <T>(
    array: T[],
    predicate: (item: T) => boolean,
    expectedCount?: number
  ): void => {
    expect(Array.isArray(array)).toBe(true);
    
    const matchingItems = array.filter(predicate);
    
    if (expectedCount !== undefined) {
      expect(matchingItems).toHaveLength(expectedCount);
    } else {
      expect(matchingItems.length).toBeGreaterThan(0);
    }
  },

  /**
   * Assert that an array is sorted by a specific property
   */
  toBeSortedBy: <T>(
    array: T[],
    keySelector: (item: T) => any,
    order: 'asc' | 'desc' = 'asc'
  ): void => {
    expect(Array.isArray(array)).toBe(true);
    
    if (array.length <= 1) return;
    
    for (let i = 1; i < array.length; i++) {
      const prev = keySelector(array[i - 1]);
      const curr = keySelector(array[i]);
      
      if (order === 'asc') {
        expect(prev <= curr).toBe(true);
      } else {
        expect(prev >= curr).toBe(true);
      }
    }
  },

  /**
   * Assert that all items in an array are unique
   */
  toHaveUniqueItems: <T>(
    array: T[],
    keySelector?: (item: T) => any
  ): void => {
    expect(Array.isArray(array)).toBe(true);
    
    const keys = keySelector ? array.map(keySelector) : array;
    const uniqueKeys = new Set(keys);
    
    expect(uniqueKeys.size).toBe(array.length);
  },
};