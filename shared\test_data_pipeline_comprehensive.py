
"""
Comprehensive test suite for data pipeline
Tests data ingestion, validation, processing, and quality checks
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
import asyncio
from hypothesis import given, strategies as st
import json

# Assuming these are your data pipeline classes
# from ai_trading_platform.src.data.ingestion import DataIngester
# from ai_trading_platform.src.data.validator import DataValidator
# from ai_trading_platform.src.data.processor import DataProcessor

class TestDataPipeline:
    """Comprehensive test suite for data pipeline components"""

    def setup_method(self):
        """Setup test components before each test"""
        self.data_ingester = Mock()
        self.data_validator = Mock()
        self.data_processor = Mock()
        self.sample_market_data = self.create_sample_market_data()

    def create_sample_market_data(self):
        """Create realistic market data for testing"""
        return pd.DataFrame({
            'timestamp': pd.date_range('2023-01-01', periods=100, freq='1min'),
            'symbol': ['AAPL'] * 100,
            'open': np.random.uniform(150, 160, 100),
            'high': np.random.uniform(160, 170, 100),
            'low': np.random.uniform(140, 150, 100),
            'close': np.random.uniform(150, 160, 100),
            'volume': np.random.randint(1000, 10000, 100),
            'bid': np.random.uniform(149, 159, 100),
            'ask': np.random.uniform(151, 161, 100)
        })

    # Test 1: Data Ingestion Reliability
    def test_data_ingestion_success(self):
        """Test successful data ingestion from multiple sources"""
        # Arrange
        expected_symbols = ['AAPL', 'GOOGL', 'MSFT']
        self.data_ingester.fetch_market_data.return_value = self.sample_market_data

        # Act
        for symbol in expected_symbols:
            result = self.data_ingester.fetch_market_data(symbol)

            # Assert
            assert result is not None, f"Failed to fetch data for {symbol}"
            assert len(result) > 0, f"Empty data returned for {symbol}"
            assert 'close' in result.columns, f"Missing close price for {symbol}"

    def test_data_ingestion_handles_api_failure(self):
        """Test graceful handling of API failures"""
        # Arrange
        self.data_ingester.fetch_market_data.side_effect = ConnectionError("API unavailable")

        # Act & Assert
        with pytest.raises(ConnectionError):
            self.data_ingester.fetch_market_data('AAPL')

    def test_data_ingestion_retry_mechanism(self):
        """Test retry mechanism for failed API calls"""
        # Arrange
        self.data_ingester.fetch_market_data.side_effect = [
            ConnectionError("Temporary failure"),
            ConnectionError("Still failing"),
            self.sample_market_data  # Success on third try
        ]

        # Mock retry logic
        def mock_fetch_with_retry(symbol, max_retries=3):
            for attempt in range(max_retries):
                try:
                    return self.data_ingester.fetch_market_data(symbol)
                except ConnectionError:
                    if attempt == max_retries - 1:
                        raise
                    continue

        # Act
        result = mock_fetch_with_retry('AAPL')

        # Assert
        assert result is not None, "Retry mechanism should eventually succeed"
        assert len(result) > 0, "Should return valid data after retries"

    # Test 2: Data Validation
    def test_data_validation_detects_missing_values(self):
        """Test detection of missing critical values"""
        # Arrange
        invalid_data = self.sample_market_data.copy()
        invalid_data.loc[10:15, 'close'] = np.nan  # Introduce missing values

        validation_result = {
            'is_valid': False,
            'errors': ['Missing close prices at indices 10-15'],
            'warnings': []
        }
        self.data_validator.validate.return_value = validation_result

        # Act
        result = self.data_validator.validate(invalid_data)

        # Assert
        assert not result['is_valid'], "Should detect missing close prices"
        assert 'Missing close prices' in str(result['errors']), "Should report specific missing data"

    def test_data_validation_detects_price_anomalies(self):
        """Test detection of unrealistic price movements"""
        # Arrange
        anomalous_data = self.sample_market_data.copy()
        anomalous_data.loc[50, 'close'] = 1000000  # Unrealistic price spike

        validation_result = {
            'is_valid': False,
            'errors': ['Price anomaly detected: 1000000 at index 50'],
            'warnings': ['Unusual price movement detected']
        }
        self.data_validator.validate.return_value = validation_result

        # Act
        result = self.data_validator.validate(anomalous_data)

        # Assert
        assert not result['is_valid'], "Should detect price anomalies"
        assert len(result['errors']) > 0, "Should report price anomaly errors"

    def test_data_validation_checks_ohlc_consistency(self):
        """Test OHLC data consistency validation"""
        # Arrange
        inconsistent_data = self.sample_market_data.copy()
        # Make high < low (impossible scenario)
        inconsistent_data.loc[25, 'high'] = 100
        inconsistent_data.loc[25, 'low'] = 150

        validation_result = {
            'is_valid': False,
            'errors': ['OHLC inconsistency: high < low at index 25'],
            'warnings': []
        }
        self.data_validator.validate.return_value = validation_result

        # Act
        result = self.data_validator.validate(inconsistent_data)

        # Assert
        assert not result['is_valid'], "Should detect OHLC inconsistencies"
        assert 'OHLC inconsistency' in str(result['errors']), "Should report specific OHLC errors"

    # Test 3: Real-time Data Processing
    @pytest.mark.asyncio
    async def test_real_time_data_processing_speed(self):
        """Test real-time data processing meets latency requirements"""
        import time

        # Arrange
        real_time_data = self.sample_market_data.iloc[-1:]  # Single tick

        async def mock_process_tick(data):
            # Simulate processing time
            await asyncio.sleep(0.001)  # 1ms processing
            return {'processed': True, 'timestamp': datetime.now()}

        # Act
        start_time = time.time()
        result = await mock_process_tick(real_time_data)
        processing_time = time.time() - start_time

        # Assert
        assert processing_time < 0.01, f"Processing took {processing_time:.4f}s, should be < 0.01s"
        assert result['processed'], "Data should be successfully processed"

    def test_real_time_data_buffer_management(self):
        """Test proper management of real-time data buffers"""
        # Arrange
        buffer_size = 1000
        mock_buffer = []

        # Simulate adding data to buffer
        for i in range(1200):  # Exceed buffer size
            mock_buffer.append(f"tick_{i}")
            if len(mock_buffer) > buffer_size:
                mock_buffer.pop(0)  # Remove oldest

        # Assert
        assert len(mock_buffer) == buffer_size, "Buffer should maintain fixed size"
        assert mock_buffer[0] == "tick_200", "Should contain most recent data"
        assert mock_buffer[-1] == "tick_1199", "Should have latest tick"

    # Test 4: Data Quality Checks
    def test_data_quality_completeness_check(self):
        """Test data completeness validation"""
        # Arrange
        incomplete_data = self.sample_market_data.iloc[::2]  # Skip every other row
        expected_completeness = 0.5  # 50% complete

        def calculate_completeness(data, expected_length):
            return len(data) / expected_length

        # Act
        completeness = calculate_completeness(incomplete_data, len(self.sample_market_data))

        # Assert
        assert abs(completeness - expected_completeness) < 0.01, "Completeness calculation incorrect"
        assert completeness < 0.9, "Should flag incomplete data"

    def test_data_quality_freshness_check(self):
        """Test data freshness validation"""
        # Arrange
        current_time = datetime.now()
        stale_data = self.sample_market_data.copy()
        stale_data['timestamp'] = current_time - timedelta(hours=2)  # 2 hours old

        def check_data_freshness(data, max_age_minutes=30):
            latest_timestamp = data['timestamp'].max()
            age_minutes = (current_time - latest_timestamp).total_seconds() / 60
            return age_minutes <= max_age_minutes

        # Act
        is_fresh = check_data_freshness(stale_data)

        # Assert
        assert not is_fresh, "Should detect stale data"

    # Test 5: Data Transformation and Cleaning
    def test_data_cleaning_removes_duplicates(self):
        """Test removal of duplicate records"""
        # Arrange
        data_with_duplicates = pd.concat([self.sample_market_data, self.sample_market_data.iloc[:10]])
        original_length = len(self.sample_market_data)

        cleaned_data = data_with_duplicates.drop_duplicates()

        # Act & Assert
        assert len(cleaned_data) == original_length, "Should remove duplicate records"

    def test_data_transformation_handles_timezone(self):
        """Test proper timezone handling in data transformation"""
        # Arrange
        utc_data = self.sample_market_data.copy()
        utc_data['timestamp'] = pd.to_datetime(utc_data['timestamp'], utc=True)

        # Mock timezone conversion
        def convert_to_market_timezone(data, target_tz='US/Eastern'):
            data_copy = data.copy()
            data_copy['timestamp'] = data_copy['timestamp'].dt.tz_convert(target_tz)
            return data_copy

        # Act
        market_time_data = convert_to_market_timezone(utc_data)

        # Assert
        assert market_time_data['timestamp'].dt.tz is not None, "Should have timezone info"
        assert str(market_time_data['timestamp'].dt.tz) == 'US/Eastern', "Should convert to market timezone"

    # Test 6: Property-based testing for data validation
    @given(st.floats(min_value=0.01, max_value=1000.0))
    def test_price_validation_properties(self, price):
        """Property-based test for price validation"""
        # Test that valid prices pass validation
        assert price > 0, "Price should be positive"
        assert price < 10000, "Price should be reasonable"

    @given(st.integers(min_value=1, max_value=1000000))
    def test_volume_validation_properties(self, volume):
        """Property-based test for volume validation"""
        # Test that valid volumes pass validation
        assert volume > 0, "Volume should be positive"
        assert isinstance(volume, int), "Volume should be integer"

    # Test 7: Data Pipeline Integration
    def test_end_to_end_data_pipeline(self):
        """Test complete data pipeline from ingestion to processing"""
        # Arrange
        symbol = 'AAPL'

        # Mock the complete pipeline
        self.data_ingester.fetch_market_data.return_value = self.sample_market_data
        self.data_validator.validate.return_value = {'is_valid': True, 'errors': [], 'warnings': []}
        self.data_processor.process.return_value = self.sample_market_data

        # Act
        raw_data = self.data_ingester.fetch_market_data(symbol)
        validation_result = self.data_validator.validate(raw_data)

        if validation_result['is_valid']:
            processed_data = self.data_processor.process(raw_data)
        else:
            processed_data = None

        # Assert
        assert raw_data is not None, "Should successfully ingest data"
        assert validation_result['is_valid'], "Data should pass validation"
        assert processed_data is not None, "Should successfully process data"
        assert len(processed_data) == len(raw_data), "Processed data should maintain record count"

    # Test 8: Error Handling and Recovery
    def test_data_pipeline_handles_partial_failures(self):
        """Test handling of partial data pipeline failures"""
        # Arrange
        symbols = ['AAPL', 'GOOGL', 'MSFT', 'INVALID_SYMBOL']
        successful_fetches = []
        failed_fetches = []

        def mock_fetch_with_failures(symbol):
            if symbol == 'INVALID_SYMBOL':
                raise ValueError(f"Invalid symbol: {symbol}")
            return self.sample_market_data

        # Act
        for symbol in symbols:
            try:
                data = mock_fetch_with_failures(symbol)
                successful_fetches.append(symbol)
            except ValueError:
                failed_fetches.append(symbol)

        # Assert
        assert len(successful_fetches) == 3, "Should successfully fetch valid symbols"
        assert len(failed_fetches) == 1, "Should handle invalid symbol gracefully"
        assert 'INVALID_SYMBOL' in failed_fetches, "Should identify failed symbol"

    # Test 9: Performance Testing
    def test_data_processing_performance_benchmark(self):
        """Test data processing performance under load"""
        import time

        # Arrange
        large_dataset = pd.concat([self.sample_market_data] * 100)  # 10,000 records

        # Act
        start_time = time.time()
        # Mock processing large dataset
        processed_count = len(large_dataset)
        processing_time = time.time() - start_time

        # Assert
        records_per_second = processed_count / max(processing_time, 0.001)
        assert records_per_second > 1000, f"Processing rate {records_per_second:.0f} records/sec too slow"

    # Test 10: Data Storage and Retrieval
    def test_data_storage_and_retrieval(self):
        """Test data storage and retrieval functionality"""
        # Arrange
        storage_mock = Mock()
        test_data = self.sample_market_data.iloc[:10]

        # Mock storage operations
        storage_mock.store.return_value = True
        storage_mock.retrieve.return_value = test_data

        # Act
        store_result = storage_mock.store('AAPL', test_data)
        retrieved_data = storage_mock.retrieve('AAPL')

        # Assert
        assert store_result, "Should successfully store data"
        assert retrieved_data is not None, "Should successfully retrieve data"
        assert len(retrieved_data) == len(test_data), "Retrieved data should match stored data"
