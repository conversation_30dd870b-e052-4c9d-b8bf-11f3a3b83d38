# darwin_godel_orchestrator.py
# Main Darwin Gödel Machine - The Orchestrator

import asyncio
import json
import logging
import threading
import time
from concurrent.futures import Thread<PERSON>oolExecutor, ProcessPoolExecutor, as_completed
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Callable, AsyncGenerator
import uuid
from dataclasses import asdict

from enhanced_darwin_godel_core import (
    TradingStrategy, TradingCondition, RiskManagement,
    EvolutionParameters, EvolutionState, EvolutionStatus,
    CoqVerificationEngine, ForexDataProvider, AdvancedBacktester,
    FitnessObjective
)

logger = logging.getLogger(__name__)

class DarwinGodelMachine:
    """
    The main Darwin Gödel Machine orchestrator.
    Combines evolutionary strategy discovery with formal mathematical verification.
    """
    
    def __init__(self, 
                 evolution_params: Optional[EvolutionParameters] = None,
                 coq_path: str = "coqc",
                 max_workers: int = 4):
        """
        Initialize the Darwin Gödel Machine
        
        Args:
            evolution_params: Parameters controlling evolution
            coq_path: Path to Coq compiler
            max_workers: Maximum parallel workers for processing
        """
        self.evolution_params = evolution_params or EvolutionParameters()
        self.max_workers = max_workers
        
        # Core engines
        self.coq_engine = CoqVerificationEngine(coq_path=coq_path)
        self.data_provider = ForexDataProvider()
        self.backtester = AdvancedBacktester(self.data_provider)
        
        # Evolution state
        self.evolution_state = EvolutionState()
        self.is_running = False
        self.should_stop = False
        
        # Event callbacks
        self.generation_callbacks: List[Callable] = []
        self.verification_callbacks: List[Callable] = []
        self.completion_callbacks: List[Callable] = []
        
        # Thread management
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.evolution_thread: Optional[threading.Thread] = None
        
        # Forex genome storage
        self.forex_genomes: Dict[str, Dict] = {}
        
        logger.info("Darwin Gödel Machine initialized")
    
    def add_generation_callback(self, callback: Callable[[int, EvolutionState], None]):
        """Add callback for generation completion events"""
        self.generation_callbacks.append(callback)
    
    def add_verification_callback(self, callback: Callable[[TradingStrategy, Dict], None]):
        """Add callback for strategy verification events"""
        self.verification_callbacks.append(callback)
    
    def add_completion_callback(self, callback: Callable[[EvolutionState], None]):
        """Add callback for evolution completion events"""
        self.completion_callbacks.append(callback)
    
    async def evolve_strategies(self, 
                              pair: str, 
                              timeframe: str = "1H",
                              start_date: Optional[datetime] = None,
                              end_date: Optional[datetime] = None) -> AsyncGenerator[EvolutionState, None]:
        """
        Main evolution loop - yields state updates as evolution progresses
        
        Args:
            pair: Currency pair (e.g., "EURUSD")
            timeframe: Trading timeframe (e.g., "1H", "4H", "1D")
            start_date: Backtest start date
            end_date: Backtest end date
            
        Yields:
            EvolutionState: Current evolution state after each generation
        """
        logger.info(f"Starting evolution for {pair} {timeframe}")
        
        # Initialize evolution
        self.evolution_state = EvolutionState(
            status=EvolutionStatus.INITIALIZING,
            start_time=datetime.now()
        )
        
        # Set default date range if not provided
        if not start_date:
            start_date = datetime.now() - timedelta(days=365)
        if not end_date:
            end_date = datetime.now() - timedelta(days=30)
        
        try:
            self.is_running = True
            self.should_stop = False
            
            # Initialize population
            yield await self._initialize_population(pair, timeframe)
            
            # Evolution loop
            for generation in range(self.evolution_params.max_generations):
                if self.should_stop:
                    logger.info("Evolution stopped by user request")
                    break
                
                self.evolution_state.generation = generation + 1
                self.evolution_state.status = EvolutionStatus.RUNNING
                
                logger.info(f"Generation {generation + 1}/{self.evolution_params.max_generations}")
                
                # Evaluate population fitness
                await self._evaluate_population_fitness(pair, timeframe, start_date, end_date)
                
                # Sort by fitness
                self.evolution_state.population.sort(
                    key=lambda s: s.fitness_score, 
                    reverse=True
                )
                
                # Update best strategy
                if self.evolution_state.population:
                    best = self.evolution_state.population[0]
                    if best.fitness_score > self.evolution_state.best_fitness:
                        self.evolution_state.best_strategy = best
                        self.evolution_state.best_fitness = best.fitness_score
                
                # Calculate average fitness
                if self.evolution_state.population:
                    self.evolution_state.average_fitness = sum(
                        s.fitness_score for s in self.evolution_state.population
                    ) / len(self.evolution_state.population)
                
                # Verify top strategies periodically
                if generation % self.evolution_params.verification_frequency == 0:
                    await self._verify_top_strategies(pair)
                
                # Record generation history
                self.evolution_state.evolution_history.append({
                    'generation': generation + 1,
                    'best_fitness': self.evolution_state.best_fitness,
                    'average_fitness': self.evolution_state.average_fitness,
                    'verified_count': len(self.evolution_state.verified_strategies),
                    'timestamp': datetime.now().isoformat()
                })
                
                # Fire generation callbacks
                for callback in self.generation_callbacks:
                    try:
                        callback(generation + 1, self.evolution_state)
                    except Exception as e:
                        logger.error(f"Generation callback error: {e}")
                
                # Yield current state
                yield self.evolution_state
                
                # Check convergence
                if self._check_convergence():
                    logger.info("Evolution converged")
                    break
                
                # Create next generation
                if generation < self.evolution_params.max_generations - 1:
                    await self._create_next_generation()
            
            # Final verification of best strategies
            await self._final_verification(pair)
            
            # Generate forex genome
            await self._generate_forex_genome(pair, timeframe)
            
            # Mark as completed
            self.evolution_state.status = EvolutionStatus.COMPLETED
            self.evolution_state.end_time = datetime.now()
            
            # Fire completion callbacks
            for callback in self.completion_callbacks:
                try:
                    callback(self.evolution_state)
                except Exception as e:
                    logger.error(f"Completion callback error: {e}")
            
            logger.info(f"Evolution completed. Best fitness: {self.evolution_state.best_fitness:.4f}")
            yield self.evolution_state
            
        except Exception as e:
            logger.error(f"Evolution failed: {e}")
            self.evolution_state.status = EvolutionStatus.FAILED
            yield self.evolution_state
            raise
        finally:
            self.is_running = False
    
    async def _initialize_population(self, pair: str, timeframe: str) -> EvolutionState:
        """Initialize the population with diverse trading strategies"""
        logger.info("Initializing population...")
        
        strategies = []
        
        # Create diverse initial strategies
        strategy_templates = self._get_strategy_templates()
        
        for i in range(self.evolution_params.population_size):
            template = strategy_templates[i % len(strategy_templates)]
            strategy = self._create_strategy_from_template(template, i)
            strategies.append(strategy)
        
        self.evolution_state.population = strategies
        self.evolution_state.status = EvolutionStatus.RUNNING
        
        logger.info(f"Initialized population with {len(strategies)} strategies")
        return self.evolution_state
    
    def _get_strategy_templates(self) -> List[Dict[str, Any]]:
        """Get diverse strategy templates for initialization"""
        return [
            # RSI Mean Reversion
            {
                'name': 'RSI_MeanReversion',
                'conditions': [
                    {'indicator': 'RSI', 'operator': '<', 'value': 30, 'period': 14}
                ],
                'action': 'buy'
            },
            # RSI Overbought
            {
                'name': 'RSI_Overbought',
                'conditions': [
                    {'indicator': 'RSI', 'operator': '>', 'value': 70, 'period': 14}
                ],
                'action': 'sell'
            },
            # MACD Crossover
            {
                'name': 'MACD_Crossover',
                'conditions': [
                    {'indicator': 'MACD', 'operator': 'crossover', 'value': 0}
                ],
                'action': 'buy'
            },
            # EMA Trend Following
            {
                'name': 'EMA_Trend',
                'conditions': [
                    {'indicator': 'EMA_50', 'operator': '>', 'value': 'EMA_200'},
                    {'indicator': 'close', 'operator': '>', 'value': 'EMA_50'}
                ],
                'action': 'buy'
            },
            # Bollinger Band Breakout
            {
                'name': 'BB_Breakout',
                'conditions': [
                    {'indicator': 'close', 'operator': '>', 'value': 'BB_upper'}
                ],
                'action': 'buy'
            },
            # Stochastic Oversold
            {
                'name': 'Stoch_Oversold',
                'conditions': [
                    {'indicator': 'STOCH_K', 'operator': '<', 'value': 20},
                    {'indicator': 'STOCH_D', 'operator': '<', 'value': 20}
                ],
                'action': 'buy'
            },
            # Multi-timeframe momentum
            {
                'name': 'Multi_Momentum',
                'conditions': [
                    {'indicator': 'RSI', 'operator': '>', 'value': 50},
                    {'indicator': 'MACD', 'operator': '>', 'value': 0},
                    {'indicator': 'EMA_12', 'operator': '>', 'value': 'EMA_26'}
                ],
                'action': 'buy'
            },
            # Contrarian strategy
            {
                'name': 'Contrarian',
                'conditions': [
                    {'indicator': 'RSI', 'operator': '<', 'value': 25},
                    {'indicator': 'close', 'operator': '<', 'value': 'BB_lower'}
                ],
                'action': 'buy'
            }
        ]
    
    def _create_strategy_from_template(self, template: Dict[str, Any], index: int) -> TradingStrategy:
        """Create a trading strategy from a template with random variations"""
        conditions = []
        
        for cond_template in template['conditions']:
            # Add random variation to values
            value = cond_template['value']
            if isinstance(value, (int, float)):
                if cond_template['indicator'] == 'RSI':
                    value += random.uniform(-10, 10)
                    value = max(0, min(100, value))
                else:
                    value *= random.uniform(0.8, 1.2)
            
            condition = TradingCondition(
                indicator=cond_template['indicator'],
                operator=cond_template['operator'],
                value=value,
                period=cond_template.get('period')
            )
            conditions.append(condition)
        
        # Random risk management
        risk_mgmt = RiskManagement(
            stop_loss_pct=random.uniform(1.0, 5.0),
            take_profit_pct=random.uniform(2.0, 8.0),
            position_size_pct=random.uniform(0.5, 2.0),
            risk_per_trade=random.uniform(0.01, 0.05)
        )
        
        return TradingStrategy(
            id=str(uuid.uuid4()),
            name=f"{template['name']}_{index}",
            description=f"Generated strategy based on {template['name']} template",
            conditions=conditions,
            action=template['action'],
            risk_management=risk_mgmt,
            generation=0
        )
    
    async def _evaluate_population_fitness(self, pair: str, timeframe: str, 
                                         start_date: datetime, end_date: datetime):
        """Evaluate fitness for all strategies in parallel"""
        logger.info("Evaluating population fitness...")
        
        # Create tasks for parallel evaluation
        tasks = []
        for strategy in self.evolution_state.population:
            task = self._evaluate_strategy_fitness(strategy, pair, timeframe, start_date, end_date)
            tasks.append(task)
        
        # Execute in batches to avoid overwhelming the system
        batch_size = min(self.max_workers * 2, len(tasks))
        
        for i in range(0, len(tasks), batch_size):
            batch = tasks[i:i + batch_size]
            results = await asyncio.gather(*batch, return_exceptions=True)
            
            # Update strategy fitness scores
            for j, result in enumerate(results):
                strategy_idx = i + j
                if strategy_idx < len(self.evolution_state.population):
                    strategy = self.evolution_state.population[strategy_idx]
                    
                    if isinstance(result, Exception):
                        logger.error(f"Strategy {strategy.id} evaluation failed: {result}")
                        strategy.fitness_score = 0.0
                    else:
                        strategy.fitness_score = result.get('fitness', 0.0)
                        strategy.backtest_results = result
        
        logger.info("Population fitness evaluation completed")
    
    async def _evaluate_strategy_fitness(self, strategy: TradingStrategy, 
                                       pair: str, timeframe: str,
                                       start_date: datetime, end_date: datetime) -> Dict[str, float]:
        """Evaluate fitness for a single strategy"""
        try:
            results = await self.backtester.backtest_strategy(
                strategy, pair, timeframe, start_date, end_date
            )
            return results
        except Exception as e:
            logger.error(f"Fitness evaluation failed for strategy {strategy.id}: {e}")
            return {'fitness': 0.0, 'error': str(e)}
    
    async def _verify_top_strategies(self, pair: str):
        """Verify top performing strategies using Coq"""
        logger.info("Verifying top strategies...")
        
        # Get top strategies for verification
        top_strategies = self.evolution_state.population[:5]
        
        verification_tasks = []
        for strategy in top_strategies:
            if not strategy.is_verified:
                task = self._verify_strategy_with_coq(strategy, pair)
                verification_tasks.append(task)
        
        if verification_tasks:
            results = await asyncio.gather(*verification_tasks, return_exceptions=True)
            
            verified_count = 0
            for i, result in enumerate(results):
                strategy = top_strategies[i]
                
                if isinstance(result, Exception):
                    logger.error(f"Verification failed for strategy {strategy.id}: {result}")
                else:
                    strategy.proof_result = result
                    strategy.is_verified = result.get('verified', False)
                    strategy.verification_time = result.get('verification_time', 0.0)
                    
                    if strategy.is_verified:
                        if strategy not in self.evolution_state.verified_strategies:
                            self.evolution_state.verified_strategies.append(strategy)
                        verified_count += 1
                        
                        # Fire verification callback
                        for callback in self.verification_callbacks:
                            try:
                                callback(strategy, result)
                            except Exception as e:
                                logger.error(f"Verification callback error: {e}")
            
            logger.info(f"Verified {verified_count} strategies")
    
    async def _verify_strategy_with_coq(self, strategy: TradingStrategy, pair: str) -> Dict[str, Any]:
        """Verify a single strategy with Coq formal verification"""
        try:
            # Run verification in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                self.executor,
                self.coq_engine.verify_strategy,
                strategy,
                pair
            )
            return result
        except Exception as e:
            logger.error(f"Coq verification failed for strategy {strategy.id}: {e}")
            return {
                'verified': False,
                'theorem': '',
                'errors': [str(e)],
                'verification_time': 0.0
            }
    
    def _check_convergence(self) -> bool:
        """Check if evolution has converged"""
        if len(self.evolution_state.evolution_history) < 5:
            return False
        
        # Check if fitness improvement has stagnated
        recent_fitness = [
            entry['best_fitness'] 
            for entry in self.evolution_state.evolution_history[-5:]
        ]
        
        fitness_improvement = max(recent_fitness) - min(recent_fitness)
        return fitness_improvement < self.evolution_params.convergence_threshold
    
    async def _create_next_generation(self):
        """Create the next generation using selection, crossover, and mutation"""
        logger.info("Creating next generation...")
        
        current_population = self.evolution_state.population
        new_population = []
        
        # Elite selection - keep best performers
        elite_count = self.evolution_params.elite_size
        elites = current_population[:elite_count]
        new_population.extend(elites)
        
        # Fill rest of population with crossover and mutation
        while len(new_population) < self.evolution_params.population_size:
            # Tournament selection for parents
            parent1 = self._tournament_selection(current_population)
            parent2 = self._tournament_selection(current_population)
            
            # Crossover
            if random.random() < self.evolution_params.crossover_rate:
                child = TradingStrategy.crossover(parent1, parent2)
            else:
                child = random.choice([parent1, parent2])
                child.id = str(uuid.uuid4())  # New ID for the copy
            
            # Mutation
            if random.random() < self.evolution_params.mutation_rate:
                child = child.mutate(self.evolution_params.mutation_rate)
            
            new_population.append(child)
        
        # Update population
        self.evolution_state.population = new_population[:self.evolution_params.population_size]
        
        logger.info(f"Created generation with {len(self.evolution_state.population)} strategies")
    
    def _tournament_selection(self, population: List[TradingStrategy], 
                            tournament_size: int = 3) -> TradingStrategy:
        """Tournament selection for choosing parents"""
        tournament = random.sample(population, min(tournament_size, len(population)))
        return max(tournament, key=lambda s: s.fitness_score)
    
    async def _final_verification(self, pair: str):
        """Final verification of all top strategies"""
        logger.info("Performing final verification...")
        
        # Verify all unverified strategies in top 20
        top_strategies = [s for s in self.evolution_state.population[:20] if not s.is_verified]
        
        if top_strategies:
            verification_tasks = [
                self._verify_strategy_with_coq(strategy, pair) 
                for strategy in top_strategies
            ]
            
            results = await asyncio.gather(*verification_tasks, return_exceptions=True)
            
            for strategy, result in zip(top_strategies, results):
                if not isinstance(result, Exception):
                    strategy.proof_result = result
                    strategy.is_verified = result.get('verified', False)
                    
                    if strategy.is_verified and strategy not in self.evolution_state.verified_strategies:
                        self.evolution_state.verified_strategies.append(strategy)
    
    async def _generate_forex_genome(self, pair: str, timeframe: str):
        """Generate comprehensive forex genome for the currency pair"""
        logger.info(f"Generating forex genome for {pair} {timeframe}...")
        
        # Analyze top strategies to extract patterns
        top_strategies = self.evolution_state.population[:20]
        verified_strategies = [s for s in top_strategies if s.is_verified]
        
        # Extract common patterns
        indicator_usage = {}
        condition_patterns = {}
        risk_patterns = {}
        
        for strategy in top_strategies:
            # Analyze indicator usage
            for condition in strategy.conditions:
                indicator = condition.indicator
                indicator_usage[indicator] = indicator_usage.get(indicator, 0) + 1
            
            # Analyze risk management
            risk_patterns['avg_stop_loss'] = risk_patterns.get('avg_stop_loss', [])
            risk_patterns['avg_stop_loss'].append(strategy.risk_management.stop_loss_pct)
            
            risk_patterns['avg_take_profit'] = risk_patterns.get('avg_take_profit', [])
            risk_patterns['avg_take_profit'].append(strategy.risk_management.take_profit_pct)
        
        # Calculate averages
        avg_risk = {}
        for key, values in risk_patterns.items():
            if values:
                avg_risk[key.replace('avg_', '')] = sum(values) / len(values)
        
        # Build genome
        genome = {
            'pair': pair,
            'timeframe': timeframe,
            'generation_date': datetime.now().isoformat(),
            'total_strategies_tested': len(self.evolution_state.population) * self.evolution_state.generation,
            'verified_strategies_count': len(verified_strategies),
            'best_fitness_achieved': self.evolution_state.best_fitness,
            'evolution_generations': self.evolution_state.generation,
            
            # Strategy patterns
            'most_effective_indicators': dict(sorted(indicator_usage.items(), key=lambda x: x[1], reverse=True)[:5]),
            'optimal_risk_management': avg_risk,
            'best_strategies': [
                {
                    'id': s.id,
                    'name': s.name,
                    'fitness': s.fitness_score,
                    'verified': s.is_verified,
                    'conditions_count': len(s.conditions),
                    'win_rate': s.backtest_results.get('win_rate', 0),
                    'sharpe_ratio': s.backtest_results.get('sharpe_ratio', 0)
                }
                for s in top_strategies[:10]
            ],
            
            # Market characteristics discovered
            'market_insights': {
                'convergence_generation': self.evolution_state.generation,
                'verification_success_rate': len(verified_strategies) / len(top_strategies) if top_strategies else 0,
                'fitness_evolution': [entry['best_fitness'] for entry in self.evolution_state.evolution_history]
            }
        }
        
        # Store genome
        genome_key = f"{pair}_{timeframe}"
        self.forex_genomes[genome_key] = genome
        
        logger.info(f"Forex genome generated for {pair} {timeframe}")
        return genome
    
    def stop_evolution(self):
        """Stop the evolution process"""
        logger.info("Stopping evolution...")
        self.should_stop = True
    
    def get_best_strategies(self, top_n: int = 10, verified_only: bool = False) -> List[TradingStrategy]:
        """Get the best performing strategies"""
        strategies = self.evolution_state.verified_strategies if verified_only else self.evolution_state.population
        return sorted(strategies, key=lambda s: s.fitness_score, reverse=True)[:top_n]
    
    def get_forex_genome(self, pair: str, timeframe: str = None) -> Optional[Dict[str, Any]]:
        """Get the forex genome for a currency pair"""
        if timeframe:
            return self.forex_genomes.get(f"{pair}_{timeframe}")
        else:
            # Return any genome for the pair
            for key, genome in self.forex_genomes.items():
                if genome['pair'] == pair:
                    return genome
        return None
    
    def export_results(self, filename: str = None) -> Dict[str, Any]:
        """Export evolution results to file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"darwin_godel_results_{timestamp}.json"
        
        results = {
            'evolution_state': {
                'generation': self.evolution_state.generation,
                'status': self.evolution_state.status.value,
                'best_fitness': self.evolution_state.best_fitness,
                'average_fitness': self.evolution_state.average_fitness,
                'verified_strategies_count': len(self.evolution_state.verified_strategies),
                'evolution_history': self.evolution_state.evolution_history,
                'start_time': self.evolution_state.start_time.isoformat() if self.evolution_state.start_time else None,
                'end_time': self.evolution_state.end_time.isoformat() if self.evolution_state.end_time else None
            },
            'best_strategies': [
                {
                    'id': s.id,
                    'name': s.name,
                    'description': s.description,
                    'fitness_score': s.fitness_score,
                    'is_verified': s.is_verified,
                    'conditions': [asdict(c) for c in s.conditions],
                    'action': s.action,
                    'risk_management': asdict(s.risk_management),
                    'backtest_results': s.backtest_results,
                    'generation': s.generation
                }
                for s in self.get_best_strategies(20)
            ],
            'forex_genomes': self.forex_genomes,
            'evolution_parameters': asdict(self.evolution_params)
        }
        
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"Results exported to {filename}")
        return results
    
    def __del__(self):
        """Cleanup resources"""
        if self.executor:
            self.executor.shutdown(wait=False)

# Make necessary imports available
import random