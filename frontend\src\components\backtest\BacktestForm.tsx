import { useState } from 'react';
import { <PERSON><PERSON><PERSON>U<PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react';
import { useApi } from '../../hooks/useApi';
import { apiService } from '../../services/api';
import { Button } from '../common/Button';
import { Input } from '../common/Input';

interface BacktestConfig {
  name: string;
  symbol: string;
  startDate: string;
  endDate: string;
  initialBalance: number;
  modelName: string;
  strategyConfig: {
    indicators: {
      rsi: { period: number; oversold: number; overbought: number };
      macd: { fast: number; slow: number; signal: number };
      sma: { period: number };
    };
    riskManagement: {
      stopLoss: number;
      takeProfit: number;
      positionSize: number;
    };
    rules: string[];
  };
}

interface BacktestFormProps {
  onBacktestCreated: (backtestId: string) => void;
}

export function BacktestForm({ onBacktestCreated }: BacktestFormProps) {
  const [config, setConfig] = useState<Partial<BacktestConfig>>({
    name: '',
    symbol: 'EURUSD',
    startDate: '',
    endDate: '',
    initialBalance: 100000,
    modelName: 'xllm-mini',
    strategyConfig: {
      indicators: {
        rsi: { period: 14, oversold: 30, overbought: 70 },
        macd: { fast: 12, slow: 26, signal: 9 },
        sma: { period: 20 }
      },
      riskManagement: {
        stopLoss: 0.02,
        takeProfit: 0.04,
        positionSize: 0.1
      },
      rules: [
        'BUY when RSI < oversold AND MACD > 0',
        'SELL when RSI > overbought OR stop_loss OR take_profit'
      ]
    }
  });

  const { loading, error, execute } = useApi<{ backtestId: string }>();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!config.name || !config.startDate || !config.endDate) {
      return;
    }

    try {
      const result = await execute(() => 
        apiService.createBacktest(config as any)
      );
      
      if (result) {
        onBacktestCreated(result.backtestId);
        // Reset form
        setConfig(prev => ({ ...prev, name: '' }));
      }
    } catch (error) {
      console.error('Failed to create backtest:', error);
    }
  };

  const updateStrategyConfig = (key: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      strategyConfig: {
        ...prev.strategyConfig,
        [key]: value
      }
    } as Partial<BacktestConfig>));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 bg-white p-6 rounded-lg shadow">
      {/* Basic Configuration */}
      <div>
        <div className="flex items-center space-x-2 mb-4">
          <Settings className="w-5 h-5 text-blue-600" />
          <h3 className="text-lg font-medium text-gray-900">Basic Configuration</h3>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <Input
            label="Backtest Name"
            value={config.name || ''}
            onChange={(e) => setConfig(prev => ({ ...prev, name: e.target.value }))}
            placeholder="My Trading Strategy"
            required
          />
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Symbol
            </label>
            <select
              value={config.symbol || 'EURUSD'}
              onChange={(e) => setConfig(prev => ({ ...prev, symbol: e.target.value }))}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            >
              <option value="EURUSD">EUR/USD</option>
              <option value="GBPUSD">GBP/USD</option>
              <option value="USDJPY">USD/JPY</option>
              <option value="AUDUSD">AUD/USD</option>
              <option value="USDCAD">USD/CAD</option>
            </select>
          </div>

          <Input
            label="Start Date"
            type="date"
            value={config.startDate || ''}
            onChange={(e) => setConfig(prev => ({ ...prev, startDate: e.target.value }))}
            required
          />

          <Input
            label="End Date"
            type="date"
            value={config.endDate || ''}
            onChange={(e) => setConfig(prev => ({ ...prev, endDate: e.target.value }))}
            required
          />

          <Input
            label="Initial Balance ($)"
            type="number"
            value={config.initialBalance || 100000}
            onChange={(e) => setConfig(prev => ({ ...prev, initialBalance: Number(e.target.value) }))}
            min="1000"
            step="1000"
          />

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              AI Model
            </label>
            <select
              value={config.modelName || 'xllm-mini'}
              onChange={(e) => setConfig(prev => ({ ...prev, modelName: e.target.value }))}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            >
              <option value="xllm-mini">XLLM Mini (Fast)</option>
              <option value="xllm-pro">XLLM Pro (Balanced)</option>
              <option value="xllm-max">XLLM Max (Advanced)</option>
            </select>
          </div>
        </div>
      </div>

      {/* Technical Indicators */}
      <div>
        <div className="flex items-center space-x-2 mb-4">
          <TrendingUp className="w-5 h-5 text-green-600" />
          <h4 className="text-md font-medium text-gray-800">Technical Indicators</h4>
        </div>
        
        <div className="grid grid-cols-3 gap-6">
          {/* RSI */}
          <div className="p-4 border border-gray-200 rounded-lg">
            <h5 className="font-medium text-sm text-gray-700 mb-3">RSI</h5>
            <div className="space-y-3">
              <Input
                label="Period"
                type="number"
                value={config.strategyConfig?.indicators?.rsi?.period || 14}
                onChange={(e) => updateStrategyConfig('indicators', {
                  ...config.strategyConfig?.indicators,
                  rsi: {
                    ...config.strategyConfig?.indicators?.rsi,
                    period: Number(e.target.value)
                  }
                })}
                min="1"
                max="50"
              />
              <Input
                label="Oversold"
                type="number"
                value={config.strategyConfig?.indicators?.rsi?.oversold || 30}
                onChange={(e) => updateStrategyConfig('indicators', {
                  ...config.strategyConfig?.indicators,
                  rsi: {
                    ...config.strategyConfig?.indicators?.rsi,
                    oversold: Number(e.target.value)
                  }
                })}
                min="10"
                max="40"
              />
              <Input
                label="Overbought"
                type="number"
                value={config.strategyConfig?.indicators?.rsi?.overbought || 70}
                onChange={(e) => updateStrategyConfig('indicators', {
                  ...config.strategyConfig?.indicators,
                  rsi: {
                    ...config.strategyConfig?.indicators?.rsi,
                    overbought: Number(e.target.value)
                  }
                })}
                min="60"
                max="90"
              />
            </div>
          </div>
          
          {/* MACD */}
          <div className="p-4 border border-gray-200 rounded-lg">
            <h5 className="font-medium text-sm text-gray-700 mb-3">MACD</h5>
            <div className="space-y-3">
              <Input
                label="Fast"
                type="number"
                value={config.strategyConfig?.indicators?.macd?.fast || 12}
                onChange={(e) => updateStrategyConfig('indicators', {
                  ...config.strategyConfig?.indicators,
                  macd: {
                    ...config.strategyConfig?.indicators?.macd,
                    fast: Number(e.target.value)
                  }
                })}
                min="1"
                max="50"
              />
              <Input
                label="Slow"
                type="number"
                value={config.strategyConfig?.indicators?.macd?.slow || 26}
                onChange={(e) => updateStrategyConfig('indicators', {
                  ...config.strategyConfig?.indicators,
                  macd: {
                    ...config.strategyConfig?.indicators?.macd,
                    slow: Number(e.target.value)
                  }
                })}
                min="1"
                max="100"
              />
              <Input
                label="Signal"
                type="number"
                value={config.strategyConfig?.indicators?.macd?.signal || 9}
                onChange={(e) => updateStrategyConfig('indicators', {
                  ...config.strategyConfig?.indicators,
                  macd: {
                    ...config.strategyConfig?.indicators?.macd,
                    signal: Number(e.target.value)
                  }
                })}
                min="1"
                max="50"
              />
            </div>
          </div>
          
          {/* SMA */}
          <div className="p-4 border border-gray-200 rounded-lg">
            <h5 className="font-medium text-sm text-gray-700 mb-3">SMA</h5>
            <Input
              label="Period"
              type="number"
              value={config.strategyConfig?.indicators?.sma?.period || 20}
              onChange={(e) => updateStrategyConfig('indicators', {
                ...config.strategyConfig?.indicators,
                sma: {
                  period: Number(e.target.value)
                }
              })}
              min="1"
              max="200"
            />
          </div>
        </div>
      </div>

      {/* Risk Management */}
      <div>
        <div className="flex items-center space-x-2 mb-4">
          <Brain className="w-5 h-5 text-purple-600" />
          <h4 className="text-md font-medium text-gray-800">Risk Management</h4>
        </div>
        
        <div className="grid grid-cols-3 gap-4">
          <Input
            label="Stop Loss (%)"
            type="number"
            value={config.strategyConfig?.riskManagement?.stopLoss || 0.02}
            onChange={(e) => updateStrategyConfig('riskManagement', {
              ...config.strategyConfig?.riskManagement,
              stopLoss: Number(e.target.value)
            })}
            step="0.001"
            min="0"
            max="1"
          />
          
          <Input
            label="Take Profit (%)"
            type="number"
            value={config.strategyConfig?.riskManagement?.takeProfit || 0.04}
            onChange={(e) => updateStrategyConfig('riskManagement', {
              ...config.strategyConfig?.riskManagement,
              takeProfit: Number(e.target.value)
            })}
            step="0.001"
            min="0"
            max="1"
          />
          
          <Input
            label="Position Size (%)"
            type="number"
            value={config.strategyConfig?.riskManagement?.positionSize || 0.1}
            onChange={(e) => updateStrategyConfig('riskManagement', {
              ...config.strategyConfig?.riskManagement,
              positionSize: Number(e.target.value)
            })}
            step="0.01"
            min="0.01"
            max="1"
          />
        </div>
      </div>

      {/* Submit Button */}
      <div className="pt-4 border-t border-gray-200">
        <div className="flex items-center justify-between">
          {error && (
            <p className="text-sm text-red-600">{error}</p>
          )}
          
          <div className="ml-auto">
            <Button
              type="submit"
              loading={loading}
              disabled={!config.name || !config.startDate || !config.endDate}
              className="px-6 py-2"
            >
              Create Backtest
            </Button>
          </div>
        </div>
      </div>
    </form>
  );
}