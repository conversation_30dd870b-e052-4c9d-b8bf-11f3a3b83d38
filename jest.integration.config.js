/**
 * Jest configuration for integration tests
 * Optimized for testing bridge services with Python engine mock
 */

const baseConfig = require('./jest.config');

module.exports = {
  ...baseConfig,
  
  // Integration test specific settings
  displayName: 'Integration Tests',
  testMatch: [
    '<rootDir>/backend/src/**/*.integration.test.ts',
    '<rootDir>/backend/src/**/*.e2e.test.ts',
    '<rootDir>/shared/**/*.integration.test.ts',
  ],
  
  // Longer timeout for integration tests
  testTimeout: 30000,
  
  // Setup files for integration tests
  setupFilesAfterEnv: [
    '<rootDir>/jest.setup.integration.ts',
  ],
  
  // Environment variables for integration tests
  testEnvironment: 'node',
  
  // Coverage settings for integration tests
  collectCoverageFrom: [
    'backend/src/services/bridge/**/*.ts',
    'shared/schemas/**/*.ts',
    '!**/*.test.ts',
    '!**/*.spec.ts',
    '!**/*.d.ts',
  ],
  
  // Module name mapping for integration tests
  moduleNameMapping: {
    ...baseConfig.moduleNameMapping,
    '^@/test-utils/(.*)$': '<rootDir>/shared/test-utils/$1',
  },
  
  // Global setup for integration tests
  globalSetup: '<rootDir>/jest.globalSetup.integration.ts',
  globalTeardown: '<rootDir>/jest.globalTeardown.integration.ts',
  
  // Reporters for integration tests
  reporters: [
    'default',
    [
      'jest-junit',
      {
        outputDirectory: 'test-results/integration',
        outputName: 'integration-results.xml',
        suiteName: 'Bridge Integration Tests',
      },
    ],
    [
      'jest-html-reporters',
      {
        publicPath: 'test-results/integration',
        filename: 'integration-report.html',
        pageTitle: 'Bridge Integration Test Report',
      },
    ],
  ],
  
  // Test result processor for integration with CI/CD
  testResultsProcessor: 'jest-sonar-reporter',
  
  // Verbose output for integration tests
  verbose: true,
  
  // Run tests in sequence for integration tests (avoid conflicts)
  maxWorkers: 1,
  
  // Clear mocks between tests
  clearMocks: true,
  
  // Force exit after tests complete
  forceExit: true,
  
  // Detect open handles for better debugging
  detectOpenHandles: true,
};