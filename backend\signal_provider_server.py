"""
Signal Provider Server - Provides trading signals, NOT trade execution
This is legally compliant as we're providing information/signals only.
Users execute trades on their own MT5 accounts.
"""

import sys
import os
import logging
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, <PERSON>
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import random

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("signal_provider")

# Create FastAPI app
app = FastAPI(
    title="AI Trading Signal Provider API",
    description="Provides trading signals for manual execution on user's own MT5 accounts",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For development - restrict in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Signal generation class
class SignalGenerator:
    """
    Generates trading signals based on various strategies.
    This is educational/informational content only.
    """
    
    def __init__(self):
        self.logger = logging.getLogger("SignalGenerator")
        self.strategies = [
            "Moving Average Crossover",
            "RSI Reversal", 
            "Breakout Scanner",
            "Support/Resistance",
            "Momentum Indicator"
        ]
        
        self.symbols = ["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD"]
        
        # Mock price data for signal generation
        self.current_prices = {
            "EURUSD": 1.1234,
            "GBPUSD": 1.3145,
            "USDJPY": 107.235,
            "AUDUSD": 0.7512,
            "USDCAD": 1.2567
        }
    
    def generate_signal(self) -> Dict[str, Any]:
        """
        Generate a trading signal.
        This is for educational purposes only.
        """
        symbol = random.choice(self.symbols)
        strategy = random.choice(self.strategies)
        signal_type = random.choice(["BUY", "SELL"])
        
        current_price = self.current_prices[symbol]
        
        # Add some random variation to price
        price_variation = random.uniform(-0.001, 0.001)
        entry_price = round(current_price + price_variation, 5)
        
        # Calculate stop loss and take profit
        if signal_type == "BUY":
            stop_loss = round(entry_price - random.uniform(0.002, 0.005), 5)
            take_profit = round(entry_price + random.uniform(0.003, 0.008), 5)
        else:
            stop_loss = round(entry_price + random.uniform(0.002, 0.005), 5)
            take_profit = round(entry_price - random.uniform(0.003, 0.008), 5)
        
        # Generate reasoning based on strategy
        reasoning_map = {
            "Moving Average Crossover": f"Fast MA crossed {'above' if signal_type == 'BUY' else 'below'} slow MA with strong momentum",
            "RSI Reversal": f"RSI showing {'oversold' if signal_type == 'BUY' else 'overbought'} conditions with {'bullish' if signal_type == 'BUY' else 'bearish'} divergence",
            "Breakout Scanner": f"Price broke {'above' if signal_type == 'BUY' else 'below'} key resistance level with volume confirmation",
            "Support/Resistance": f"Price bounced from strong {'support' if signal_type == 'BUY' else 'resistance'} level",
            "Momentum Indicator": f"Strong {'bullish' if signal_type == 'BUY' else 'bearish'} momentum detected with trend confirmation"
        }
        
        signal = {
            "id": random.randint(1000, 9999),
            "symbol": symbol,
            "type": signal_type,
            "entry_price": entry_price,
            "stop_loss": stop_loss,
            "take_profit": take_profit,
            "suggested_lot_size": round(random.uniform(0.01, 0.1), 2),
            "confidence": random.randint(65, 95),
            "strategy": strategy,
            "reasoning": reasoning_map[strategy],
            "timestamp": datetime.now().isoformat(),
            "risk_reward_ratio": round(abs(take_profit - entry_price) / abs(stop_loss - entry_price), 2),
            "disclaimer": "This is a trading signal for educational purposes. Execute at your own risk on your own account."
        }
        
        self.logger.info(f"Generated signal: {signal_type} {symbol} at {entry_price}")
        return signal

# Initialize signal generator
signal_generator = SignalGenerator()

# Mock data for strategies and performance
MOCK_STRATEGIES = [
    {
        "id": 1,
        "name": "Moving Average Crossover",
        "description": "Fast MA crosses slow MA strategy with trend confirmation",
        "parameters": {
            "fast_period": 10,
            "slow_period": 30,
            "trend_filter": True
        },
        "performance": {
            "win_rate": 68.5,
            "profit_factor": 1.45,
            "monthly_return": 12.3,
            "max_drawdown": 8.2,
            "total_signals": 156,
            "winning_signals": 107
        },
        "active": True,
        "created_at": datetime.now().isoformat()
    },
    {
        "id": 2,
        "name": "RSI Reversal",
        "description": "Oversold/Overbought reversal strategy with divergence confirmation",
        "parameters": {
            "rsi_period": 14,
            "oversold": 30,
            "overbought": 70,
            "divergence_check": True
        },
        "performance": {
            "win_rate": 72.1,
            "profit_factor": 1.62,
            "monthly_return": 15.7,
            "max_drawdown": 6.8,
            "total_signals": 89,
            "winning_signals": 64
        },
        "active": True,
        "created_at": datetime.now().isoformat()
    },
    {
        "id": 3,
        "name": "Breakout Scanner",
        "description": "Support/Resistance breakout detection with volume confirmation",
        "parameters": {
            "lookback_period": 20,
            "volume_threshold": 1.5,
            "breakout_strength": 0.8
        },
        "performance": {
            "win_rate": 61.3,
            "profit_factor": 1.38,
            "monthly_return": 9.8,
            "max_drawdown": 12.1,
            "total_signals": 203,
            "winning_signals": 124
        },
        "active": True,
        "created_at": datetime.now().isoformat()
    }
]

# Store generated signals (in production, use database)
GENERATED_SIGNALS = []

# Request/Response models
class SignalRequest(BaseModel):
    """Signal generation request"""
    strategy_id: Optional[int] = Field(None, description="Specific strategy ID (optional)")
    symbol: Optional[str] = Field(None, description="Specific symbol (optional)")
    
class PerformanceQuery(BaseModel):
    """Performance query parameters"""
    strategy_id: Optional[int] = Field(None, description="Strategy ID")
    days: int = Field(30, ge=1, le=365, description="Number of days to analyze")

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "AI Trading Signal Provider API",
        "version": "1.0.0",
        "disclaimer": "This API provides trading signals for educational purposes only. Users execute trades on their own accounts at their own risk."
    }

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "ok",
        "timestamp": datetime.now().isoformat(),
        "service": "signal_provider",
        "active_strategies": len([s for s in MOCK_STRATEGIES if s["active"]])
    }

# Signal endpoints
@app.get("/api/signals/live", response_model=List[Dict[str, Any]])
async def get_live_signals():
    """
    Get current live trading signals.
    These are for educational purposes only.
    """
    # Return recent signals (last 2 hours)
    cutoff_time = datetime.now() - timedelta(hours=2)
    recent_signals = [
        signal for signal in GENERATED_SIGNALS 
        if datetime.fromisoformat(signal["timestamp"]) > cutoff_time
    ]
    
    # If no recent signals, generate some
    if len(recent_signals) < 2:
        for _ in range(2):
            signal = signal_generator.generate_signal()
            GENERATED_SIGNALS.append(signal)
            recent_signals.append(signal)
    
    return recent_signals

@app.post("/api/signals/generate", response_model=Dict[str, Any])
async def generate_new_signal(request: SignalRequest = SignalRequest()):
    """
    Generate a new trading signal.
    This is for educational/analysis purposes only.
    """
    try:
        signal = signal_generator.generate_signal()
        
        # Store the signal
        GENERATED_SIGNALS.append(signal)
        
        # Keep only last 100 signals in memory
        if len(GENERATED_SIGNALS) > 100:
            GENERATED_SIGNALS.pop(0)
        
        logger.info(f"New signal generated: {signal['type']} {signal['symbol']}")
        return signal
        
    except Exception as e:
        logger.error(f"Error generating signal: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Failed to generate signal"
        )

@app.get("/api/signals/history", response_model=List[Dict[str, Any]])
async def get_signal_history(days: int = 7):
    """
    Get historical signals.
    Limited to last 30 days for performance.
    """
    if days > 30:
        days = 30
    
    cutoff_time = datetime.now() - timedelta(days=days)
    historical_signals = [
        signal for signal in GENERATED_SIGNALS 
        if datetime.fromisoformat(signal["timestamp"]) > cutoff_time
    ]
    
    return historical_signals

# Strategy endpoints
@app.get("/api/strategies", response_model=List[Dict[str, Any]])
async def get_strategies():
    """Get all available trading strategies"""
    return MOCK_STRATEGIES

@app.get("/api/strategies/{strategy_id}", response_model=Dict[str, Any])
async def get_strategy(strategy_id: int):
    """Get a specific strategy by ID"""
    for strategy in MOCK_STRATEGIES:
        if strategy["id"] == strategy_id:
            return strategy
    raise HTTPException(status_code=404, detail="Strategy not found")

@app.get("/api/strategies/{strategy_id}/performance", response_model=Dict[str, Any])
async def get_strategy_performance(strategy_id: int, days: int = 30):
    """Get performance metrics for a specific strategy"""
    strategy = None
    for s in MOCK_STRATEGIES:
        if s["id"] == strategy_id:
            strategy = s
            break
    
    if not strategy:
        raise HTTPException(status_code=404, detail="Strategy not found")
    
    # In a real implementation, this would calculate actual performance
    # For now, return the mock performance data
    performance = strategy["performance"].copy()
    performance["period_days"] = days
    performance["last_updated"] = datetime.now().isoformat()
    
    return performance

# Performance and analytics endpoints
@app.get("/api/performance/overall", response_model=Dict[str, Any])
async def get_overall_performance():
    """Get overall signal provider performance"""
    
    # Calculate aggregate performance from all strategies
    total_signals = sum(s["performance"]["total_signals"] for s in MOCK_STRATEGIES)
    total_winning = sum(s["performance"]["winning_signals"] for s in MOCK_STRATEGIES)
    
    overall_win_rate = (total_winning / total_signals * 100) if total_signals > 0 else 0
    avg_monthly_return = sum(s["performance"]["monthly_return"] for s in MOCK_STRATEGIES) / len(MOCK_STRATEGIES)
    
    return {
        "total_signals_generated": total_signals,
        "total_winning_signals": total_winning,
        "overall_win_rate": round(overall_win_rate, 1),
        "average_monthly_return": round(avg_monthly_return, 1),
        "active_strategies": len([s for s in MOCK_STRATEGIES if s["active"]]),
        "period": "Last 30 days",
        "last_updated": datetime.now().isoformat(),
        "disclaimer": "Past performance does not guarantee future results. These are educational signals only."
    }

# Educational content endpoints
@app.get("/api/education/risk-management")
async def get_risk_management_guide():
    """Get risk management educational content"""
    return {
        "title": "Risk Management Guidelines",
        "content": {
            "position_sizing": "Never risk more than 1-2% of your account per trade",
            "stop_losses": "Always use stop losses to limit potential losses",
            "diversification": "Don't put all trades in one currency pair",
            "emotional_control": "Stick to your trading plan, avoid emotional decisions",
            "money_management": "Only trade with money you can afford to lose"
        },
        "disclaimer": "This is educational content only. Consult with a financial advisor for personalized advice."
    }

@app.get("/api/education/how-to-execute")
async def get_execution_guide():
    """Get guide on how to execute signals in MT5"""
    return {
        "title": "How to Execute Signals in MT5",
        "steps": [
            "Open your MetaTrader 5 platform",
            "Navigate to the recommended currency pair",
            "Right-click on the chart and select 'Trading' -> 'New Order'",
            "Set the order type (Market Buy/Sell) as recommended",
            "Enter the lot size (adjust based on your risk tolerance)",
            "Set Stop Loss and Take Profit levels as provided",
            "Click 'Buy' or 'Sell' to execute the trade",
            "Monitor your trade in the 'Trade' tab"
        ],
        "important_notes": [
            "Always verify the signal details before executing",
            "Adjust lot sizes based on your account size and risk tolerance",
            "Market conditions can change rapidly - signals may become invalid",
            "Never execute trades you don't understand"
        ]
    }

# Disclaimer endpoint
@app.get("/api/disclaimer")
async def get_disclaimer():
    """Get legal disclaimer"""
    return {
        "disclaimer": {
            "service_type": "Educational Signal Provider",
            "not_financial_advice": "The signals provided are for educational and informational purposes only and should not be considered as financial advice.",
            "no_trade_execution": "We do not execute trades on behalf of users. All trading decisions and executions are made by users on their own accounts.",
            "risk_warning": "Trading involves substantial risk of loss. Past performance does not guarantee future results.",
            "user_responsibility": "Users are solely responsible for their trading decisions and any resulting profits or losses.",
            "no_guarantees": "We make no guarantees about the accuracy or profitability of our signals.",
            "regulatory_compliance": "This service operates as an educational platform and does not require financial services licensing."
        },
        "contact": {
            "support": "<EMAIL>",
            "compliance": "<EMAIL>"
        }
    }

# Run the server
if __name__ == "__main__":
    import uvicorn
    logger.info("Starting Signal Provider Server...")
    logger.info("This server provides trading signals only - no trade execution")
    uvicorn.run(app, host="0.0.0.0", port=8002)