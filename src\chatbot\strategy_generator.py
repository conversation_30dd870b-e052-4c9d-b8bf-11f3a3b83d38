"""
Strategy Code Generator
Generates Python trading strategy code from structured requirements
"""

import textwrap
from typing import Dict, List, Optional
from datetime import datetime

from .models import StrategyRequest, GeneratedStrategy, TestCase


class StrategyGenerator:
    """Generates Python trading strategy code from requirements"""
    
    def __init__(self):
        self.base_imports = [
            "import numpy as np",
            "import pandas as pd",
            "from typing import Dict, List, Optional, Any",
            "from datetime import datetime, timedelta",
            "from src.strategies.strategy_base import StrategyBase"
        ]
        
        self.indicator_imports = {
            "rsi": "from ta.momentum import RSIIndicator",
            "macd": "from ta.trend import MACD",
            "sma": "from ta.trend import SMAIndicator", 
            "ema": "from ta.trend import EMAIndicator",
            "bollinger_bands": "from ta.volatility import BollingerBands",
            "stochastic": "from ta.momentum import StochasticOscillator",
            "atr": "from ta.volatility import AverageTrueRange"
        }
        
        self.ml_imports = {
            "random_forest": "from sklearn.ensemble import RandomForestClassifier",
            "svm": "from sklearn.svm import SVC",
            "neural_network": "from sklearn.neural_network import MLPClassifier",
            "xgboost": "from xgboost import XGBClassifier",
            "lstm": "from tensorflow.keras.models import Sequential\nfrom tensorflow.keras.layers import LSTM, Dense"
        }
    
    def generate(self, request: StrategyRequest) -> GeneratedStrategy:
        """Generate complete strategy code from requirements"""
        
        # Generate class name and strategy name
        class_name = self._generate_class_name(request)
        strategy_name = self._generate_strategy_name(request)
        
        # Generate imports
        imports = self._generate_imports(request)
        
        # Generate class definition
        class_def = self._generate_class_definition(request, class_name)
        
        # Generate methods
        methods = self._generate_methods(request)
        
        # Combine all parts
        code_parts = [
            '"""',
            f'Generated {request.strategy_type.replace("_", " ").title()} Strategy',
            f'Auto-generated on {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}',
            '"""',
            '',
            *imports,
            '',
            '',
            class_def,
            *methods
        ]
        
        code = '\n'.join(code_parts)
        
        # Generate test cases
        test_cases = self._generate_test_cases(request, class_name)
        
        # Generate documentation
        documentation = self._generate_documentation(request)
        
        # Determine dependencies
        dependencies = self._get_dependencies(request)
        
        return GeneratedStrategy(
            code=code,
            strategy_name=strategy_name,
            class_name=class_name,
            test_cases=test_cases,
            documentation=documentation,
            dependencies=dependencies
        )
    
    def _generate_class_name(self, request: StrategyRequest) -> str:
        """Generate appropriate class name"""
        base_name = request.strategy_type.replace('_', ' ').title().replace(' ', '')
        
        if request.indicators:
            main_indicator = request.indicators[0].upper()
            return f"{base_name}{main_indicator}Strategy"
        
        return f"{base_name}Strategy"
    
    def _generate_strategy_name(self, request: StrategyRequest) -> str:
        """Generate human-readable strategy name"""
        base_name = request.strategy_type.replace('_', ' ').title()
        
        if request.indicators:
            indicators_str = ', '.join(ind.upper() for ind in request.indicators[:2])
            return f"{base_name} ({indicators_str})"
        
        return base_name
    
    def _generate_imports(self, request: StrategyRequest) -> List[str]:
        """Generate necessary imports"""
        imports = self.base_imports.copy()
        
        # Add indicator imports
        for indicator in request.indicators:
            if indicator in self.indicator_imports:
                imports.append(self.indicator_imports[indicator])
        
        # Add ML imports
        if request.ml_model and request.ml_model in self.ml_imports:
            imports.extend(self.ml_imports[request.ml_model].split('\n'))
        
        # Add additional imports based on strategy type
        if request.strategy_type == "machine_learning":
            imports.extend([
                "from sklearn.preprocessing import StandardScaler",
                "from sklearn.model_selection import train_test_split",
                "import joblib"
            ])
        
        return imports
    
    def _generate_class_definition(self, request: StrategyRequest, class_name: str) -> str:
        """Generate class definition and __init__ method"""
        
        # Build constructor parameters
        params = [
            "symbols: List[str]",
            f"timeframe: str = '{request.timeframe or '1H'}'",
            "mt5_bridge: Optional[MT5Bridge] = None",
            f"risk_per_trade: float = {request.risk_per_trade}",
            f"max_positions: int = {request.max_positions}"
        ]
        
        # Add strategy-specific parameters
        if "rsi" in request.indicators:
            params.append("rsi_period: int = 14")
            params.append("oversold_level: float = 30")
            params.append("overbought_level: float = 70")
        
        if "macd" in request.indicators:
            params.append("macd_fast: int = 12")
            params.append("macd_slow: int = 26")
            params.append("macd_signal: int = 9")
        
        if "sma" in request.indicators:
            params.append("sma_period: int = 20")
        
        # ML-specific parameters
        if request.strategy_type == "machine_learning":
            params.extend([
                f"training_bars: int = {request.training_bars or 1000}",
                f"retrain_frequency: int = {request.retrain_frequency or 100}",
                "model_path: Optional[str] = None"
            ])
        
        params_str = ',\n                 '.join(params)
        
        init_body = self._generate_init_body(request)
        
        return f"""class {class_name}(StrategyBase):
    \"\"\"
    {request.strategy_type.replace('_', ' ').title()} Strategy
    
    Symbols: {', '.join(request.symbols)}
    Indicators: {', '.join(request.indicators) if request.indicators else 'None'}
    Timeframe: {request.timeframe or '1H'}
    \"\"\"
    
    def __init__(self,
                 {params_str}):
        super().__init__(
            name="{self._generate_strategy_name(request)}",
            symbols=symbols,
            mt5_bridge=mt5_bridge,
            risk_per_trade=risk_per_trade,
            max_open_positions=max_positions
        )
        
        self.timeframe = timeframe
{init_body}"""
    
    def _generate_init_body(self, request: StrategyRequest) -> str:
        """Generate the body of the __init__ method"""
        lines = []
        
        # Store indicator parameters
        if "rsi" in request.indicators:
            lines.extend([
                "        self.rsi_period = rsi_period",
                "        self.oversold_level = oversold_level", 
                "        self.overbought_level = overbought_level"
            ])
        
        if "macd" in request.indicators:
            lines.extend([
                "        self.macd_fast = macd_fast",
                "        self.macd_slow = macd_slow",
                "        self.macd_signal = macd_signal"
            ])
        
        if "sma" in request.indicators:
            lines.append("        self.sma_period = sma_period")
        
        # ML-specific initialization
        if request.strategy_type == "machine_learning":
            lines.extend([
                "        self.training_bars = training_bars",
                "        self.retrain_frequency = retrain_frequency",
                "        self.model = None",
                "        self.scaler = StandardScaler()",
                "        self.model_path = model_path",
                "        self.bars_since_retrain = 0"
            ])
        
        # Initialize data storage
        lines.extend([
            "        self.data_buffer = {}",
            "        self.last_signals = {}"
        ])
        
        return '\n' + '\n'.join(lines) if lines else ""
    
    def _generate_methods(self, request: StrategyRequest) -> List[str]:
        """Generate all strategy methods"""
        methods = []
        
        # Generate indicator calculation methods
        for indicator in request.indicators:
            method = self._generate_indicator_method(indicator)
            if method:
                methods.extend(['', method])
        
        # Generate ML-specific methods
        if request.strategy_type == "machine_learning":
            methods.extend([
                '',
                self._generate_ml_feature_method(request),
                '',
                self._generate_ml_train_method(request),
                '',
                self._generate_ml_predict_method(request)
            ])
        
        # Generate main signal generation method
        methods.extend(['', self._generate_signal_method(request)])
        
        # Generate risk management method
        methods.extend(['', self._generate_risk_management_method(request)])
        
        return methods
    
    def _generate_indicator_method(self, indicator: str) -> Optional[str]:
        """Generate method for calculating specific indicator"""
        
        if indicator == "rsi":
            return '''    def calculate_rsi(self, prices: pd.Series, period: Optional[int] = None) -> pd.Series:
        """Calculate RSI indicator"""
        period = period or self.rsi_period
        rsi_indicator = RSIIndicator(close=prices, window=period)
        return rsi_indicator.rsi()'''
        
        elif indicator == "macd":
            return '''    def calculate_macd(self, prices: pd.Series) -> Dict[str, pd.Series]:
        """Calculate MACD indicator"""
        macd = MACD(close=prices, 
                   window_fast=self.macd_fast,
                   window_slow=self.macd_slow, 
                   window_sign=self.macd_signal)
        return {
            'macd': macd.macd(),
            'signal': macd.macd_signal(),
            'histogram': macd.macd_diff()
        }'''
        
        elif indicator == "sma":
            return '''    def calculate_sma(self, prices: pd.Series, period: Optional[int] = None) -> pd.Series:
        """Calculate Simple Moving Average"""
        period = period or self.sma_period
        sma_indicator = SMAIndicator(close=prices, window=period)
        return sma_indicator.sma_indicator()'''
        
        elif indicator == "bollinger_bands":
            return '''    def calculate_bollinger_bands(self, prices: pd.Series, period: int = 20, std: float = 2) -> Dict[str, pd.Series]:
        """Calculate Bollinger Bands"""
        bb = BollingerBands(close=prices, window=period, window_dev=std)
        return {
            'upper': bb.bollinger_hband(),
            'middle': bb.bollinger_mavg(),
            'lower': bb.bollinger_lband()
        }'''
        
        return None
    
    def _generate_ml_feature_method(self, request: StrategyRequest) -> str:
        """Generate feature preparation method for ML strategies"""
        feature_calculations = []
        
        for feature in request.features:
            if feature == "rsi":
                feature_calculations.append("            features['rsi'] = self.calculate_rsi(data['close']).iloc[-1]")
            elif feature == "macd":
                feature_calculations.append("""            macd_data = self.calculate_macd(data['close'])
            features['macd'] = macd_data['macd'].iloc[-1]
            features['macd_signal'] = macd_data['signal'].iloc[-1]""")
            elif feature == "price_momentum":
                feature_calculations.append("            features['price_momentum'] = (data['close'].iloc[-1] / data['close'].iloc[-10] - 1) * 100")
        
        features_code = '\n'.join(feature_calculations)
        
        return f'''    def prepare_features(self, data: Dict[str, pd.Series]) -> Dict[str, float]:
        """Prepare features for ML model"""
        features = {{}}
        
        try:
{features_code}
        except Exception as e:
            self.logger.warning(f"Error calculating features: {{e}}")
            return {{}}
        
        return features'''
    
    def _generate_ml_train_method(self, request: StrategyRequest) -> str:
        """Generate model training method"""
        model_init = {
            "random_forest": "RandomForestClassifier(n_estimators=100, random_state=42)",
            "svm": "SVC(kernel='rbf', probability=True, random_state=42)",
            "neural_network": "MLPClassifier(hidden_layer_sizes=(100, 50), random_state=42)",
            "xgboost": "XGBClassifier(random_state=42)"
        }.get(request.ml_model, "RandomForestClassifier(n_estimators=100, random_state=42)")
        
        return f'''    def train_model(self, historical_data: Dict[str, pd.DataFrame]) -> bool:
        """Train the ML model on historical data"""
        try:
            # Prepare training data
            X, y = [], []
            
            for symbol, data in historical_data.items():
                for i in range(self.training_bars, len(data)):
                    features = self.prepare_features({{
                        'close': data['close'].iloc[i-50:i],
                        'high': data['high'].iloc[i-50:i],
                        'low': data['low'].iloc[i-50:i],
                        'volume': data['volume'].iloc[i-50:i]
                    }})
                    
                    if features:
                        X.append(list(features.values()))
                        # Simple labeling: 1 if price goes up, 0 if down
                        future_return = (data['close'].iloc[i+1] / data['close'].iloc[i] - 1)
                        y.append(1 if future_return > 0.001 else 0)
            
            if len(X) < 100:
                self.logger.warning("Insufficient training data")
                return False
            
            X = np.array(X)
            y = np.array(y)
            
            # Scale features
            X_scaled = self.scaler.fit_transform(X)
            
            # Train model
            self.model = {model_init}
            self.model.fit(X_scaled, y)
            
            # Save model if path provided
            if self.model_path:
                joblib.dump({{
                    'model': self.model,
                    'scaler': self.scaler
                }}, self.model_path)
            
            self.bars_since_retrain = 0
            self.logger.info("Model training completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Model training failed: {{e}}")
            return False'''
    
    def _generate_ml_predict_method(self, request: StrategyRequest) -> str:
        """Generate prediction method for ML strategies"""
        return '''    def predict_signal(self, data: Dict[str, pd.Series]) -> Dict[str, Any]:
        """Generate trading signal using ML model"""
        if self.model is None:
            return {"signal": "hold", "confidence": 0.0}
        
        try:
            features = self.prepare_features(data)
            if not features:
                return {"signal": "hold", "confidence": 0.0}
            
            X = np.array([list(features.values())])
            X_scaled = self.scaler.transform(X)
            
            # Get prediction and probability
            prediction = self.model.predict(X_scaled)[0]
            probabilities = self.model.predict_proba(X_scaled)[0]
            
            confidence = max(probabilities)
            
            if prediction == 1 and confidence > 0.6:
                return {"signal": "buy", "confidence": confidence}
            elif prediction == 0 and confidence > 0.6:
                return {"signal": "sell", "confidence": confidence}
            else:
                return {"signal": "hold", "confidence": confidence}
                
        except Exception as e:
            self.logger.error(f"Prediction error: {e}")
            return {"signal": "hold", "confidence": 0.0}'''
    
    def _generate_signal_method(self, request: StrategyRequest) -> str:
        """Generate main signal generation method"""
        
        if request.strategy_type == "machine_learning":
            return self._generate_ml_signal_method(request)
        elif request.strategy_type == "mean_reversion":
            return self._generate_mean_reversion_signal_method(request)
        elif request.strategy_type == "momentum":
            return self._generate_momentum_signal_method(request)
        else:
            return self._generate_default_signal_method(request)
    
    def _generate_mean_reversion_signal_method(self, request: StrategyRequest) -> str:
        """Generate mean reversion signal logic"""
        signal_logic = []
        
        if "rsi" in request.indicators:
            signal_logic.append("""            # RSI-based mean reversion signals
            rsi = self.calculate_rsi(data['close'])
            current_rsi = rsi.iloc[-1]
            
            if current_rsi < self.oversold_level:
                signals.append({"signal": "buy", "confidence": 0.8, "reason": f"RSI oversold: {current_rsi:.2f}"})
            elif current_rsi > self.overbought_level:
                signals.append({"signal": "sell", "confidence": 0.8, "reason": f"RSI overbought: {current_rsi:.2f}"})""")
        
        if "bollinger_bands" in request.indicators:
            signal_logic.append("""            # Bollinger Bands mean reversion
            bb = self.calculate_bollinger_bands(data['close'])
            current_price = data['close'].iloc[-1]
            
            if current_price < bb['lower'].iloc[-1]:
                signals.append({"signal": "buy", "confidence": 0.7, "reason": "Price below lower Bollinger Band"})
            elif current_price > bb['upper'].iloc[-1]:
                signals.append({"signal": "sell", "confidence": 0.7, "reason": "Price above upper Bollinger Band"})""")
        
        signals_code = '\n'.join(signal_logic) if signal_logic else '            # No specific indicators configured'
        
        return f'''    def generate_signal(self, symbol: str, data: Dict[str, pd.Series]) -> Dict[str, Any]:
        """Generate trading signal for mean reversion strategy"""
        try:
            signals = []
            
{signals_code}
            
            # Combine signals
            if not signals:
                return {{"signal": "hold", "confidence": 0.5, "reason": "No clear signal"}}
            
            # Take the strongest signal
            best_signal = max(signals, key=lambda x: x['confidence'])
            return best_signal
            
        except Exception as e:
            self.logger.error(f"Signal generation error for {{symbol}}: {{e}}")
            return {{"signal": "hold", "confidence": 0.0, "reason": f"Error: {{e}}"}}'''
    
    def _generate_momentum_signal_method(self, request: StrategyRequest) -> str:
        """Generate momentum signal logic"""
        signal_logic = []
        
        if "macd" in request.indicators:
            signal_logic.append("""            # MACD momentum signals
            macd_data = self.calculate_macd(data['close'])
            macd_line = macd_data['macd'].iloc[-1]
            signal_line = macd_data['signal'].iloc[-1]
            
            if macd_line > signal_line and macd_data['macd'].iloc[-2] <= macd_data['signal'].iloc[-2]:
                signals.append({"signal": "buy", "confidence": 0.8, "reason": "MACD bullish crossover"})
            elif macd_line < signal_line and macd_data['macd'].iloc[-2] >= macd_data['signal'].iloc[-2]:
                signals.append({"signal": "sell", "confidence": 0.8, "reason": "MACD bearish crossover"})""")
        
        if "sma" in request.indicators:
            signal_logic.append("""            # Moving average momentum
            sma = self.calculate_sma(data['close'])
            current_price = data['close'].iloc[-1]
            
            if current_price > sma.iloc[-1] * 1.001:  # 0.1% above SMA
                signals.append({"signal": "buy", "confidence": 0.6, "reason": "Price above SMA"})
            elif current_price < sma.iloc[-1] * 0.999:  # 0.1% below SMA
                signals.append({"signal": "sell", "confidence": 0.6, "reason": "Price below SMA"})""")
        
        signals_code = '\n'.join(signal_logic) if signal_logic else '            # No specific indicators configured'
        
        return f'''    def generate_signal(self, symbol: str, data: Dict[str, pd.Series]) -> Dict[str, Any]:
        """Generate trading signal for momentum strategy"""
        try:
            signals = []
            
{signals_code}
            
            # Combine signals
            if not signals:
                return {{"signal": "hold", "confidence": 0.5, "reason": "No clear signal"}}
            
            # Take the strongest signal
            best_signal = max(signals, key=lambda x: x['confidence'])
            return best_signal
            
        except Exception as e:
            self.logger.error(f"Signal generation error for {{symbol}}: {{e}}")
            return {{"signal": "hold", "confidence": 0.0, "reason": f"Error: {{e}}"}}'''
    
    def _generate_ml_signal_method(self, request: StrategyRequest) -> str:
        """Generate ML-based signal method"""
        return '''    def generate_signal(self, symbol: str, data: Dict[str, pd.Series]) -> Dict[str, Any]:
        """Generate trading signal using ML model"""
        try:
            # Check if model needs retraining
            self.bars_since_retrain += 1
            if self.bars_since_retrain >= self.retrain_frequency:
                self.logger.info("Retraining model...")
                # Note: In practice, you'd pass historical data here
                # self.train_model(historical_data)
            
            # Generate prediction
            return self.predict_signal(data)
            
        except Exception as e:
            self.logger.error(f"ML signal generation error for {symbol}: {e}")
            return {"signal": "hold", "confidence": 0.0, "reason": f"Error: {e}"}'''
    
    def _generate_default_signal_method(self, request: StrategyRequest) -> str:
        """Generate default signal method"""
        return '''    def generate_signal(self, symbol: str, data: Dict[str, pd.Series]) -> Dict[str, Any]:
        """Generate trading signal"""
        try:
            # Default implementation - always hold
            return {"signal": "hold", "confidence": 0.5, "reason": "Default strategy"}
            
        except Exception as e:
            self.logger.error(f"Signal generation error for {symbol}: {e}")
            return {"signal": "hold", "confidence": 0.0, "reason": f"Error: {e}"}'''
    
    def _generate_risk_management_method(self, request: StrategyRequest) -> str:
        """Generate risk management method"""
        return f'''    def calculate_position_size(self, symbol: str, signal: Dict[str, Any], account_balance: float) -> float:
        """Calculate position size based on risk management rules"""
        try:
            if signal['signal'] == 'hold':
                return 0.0
            
            # Basic position sizing based on risk per trade
            risk_amount = account_balance * self.risk_per_trade
            
            # Adjust based on confidence
            confidence_multiplier = signal.get('confidence', 0.5)
            adjusted_risk = risk_amount * confidence_multiplier
            
            # Calculate position size (simplified)
            # In practice, you'd use actual pip values and stop loss distances
            position_size = adjusted_risk / 100  # Simplified calculation
            
            return min(position_size, account_balance * 0.1)  # Max 10% of account
            
        except Exception as e:
            self.logger.error(f"Position sizing error for {{symbol}}: {{e}}")
            return 0.0'''
    
    def _generate_test_cases(self, request: StrategyRequest, class_name: str) -> List[TestCase]:
        """Generate test cases for the strategy"""
        test_cases = []
        
        # Basic signal generation test
        test_cases.append(TestCase(
            name="test_signal_generation",
            description="Test basic signal generation",
            test_data={"close": [1.1000, 1.1010, 1.1020, 1.1015, 1.1005]},
            expected_signal="hold",
            expected_confidence=0.5,
            test_code=self._generate_basic_test_code(class_name)
        ))
        
        # Strategy-specific tests
        if request.strategy_type == "mean_reversion":
            test_cases.extend(self._generate_mean_reversion_tests(class_name))
        elif request.strategy_type == "momentum":
            test_cases.extend(self._generate_momentum_tests(class_name))
        elif request.strategy_type == "machine_learning":
            test_cases.extend(self._generate_ml_tests(class_name))
        
        return test_cases
    
    def _generate_basic_test_code(self, class_name: str) -> str:
        """Generate basic test code"""
        return f'''def test_basic_signal_generation():
    strategy = {class_name}(symbols=["EURUSD"])
    
    # Mock data
    data = {{
        'close': pd.Series([1.1000, 1.1010, 1.1020, 1.1015, 1.1005]),
        'high': pd.Series([1.1005, 1.1015, 1.1025, 1.1020, 1.1010]),
        'low': pd.Series([1.0995, 1.1005, 1.1015, 1.1010, 1.1000]),
        'volume': pd.Series([1000, 1100, 1200, 1150, 1050])
    }}
    
    signal = strategy.generate_signal("EURUSD", data)
    
    assert signal is not None
    assert 'signal' in signal
    assert 'confidence' in signal
    assert signal['signal'] in ['buy', 'sell', 'hold']
    assert 0 <= signal['confidence'] <= 1'''
    
    def _generate_mean_reversion_tests(self, class_name: str) -> List[TestCase]:
        """Generate mean reversion specific tests"""
        return [
            TestCase(
                name="test_oversold_buy_signal",
                description="Test buy signal when RSI is oversold",
                test_data={"rsi_values": [25, 28, 30]},
                expected_signal="buy",
                expected_confidence=0.8,
                test_code=f'''def test_oversold_buy_signal():
    strategy = {class_name}(symbols=["EURUSD"], oversold_level=30)
    
    # Create data that would result in low RSI
    prices = pd.Series([1.1100, 1.1080, 1.1060, 1.1040, 1.1020, 1.1000, 1.0980, 1.0960])
    data = {{'close': prices, 'high': prices * 1.001, 'low': prices * 0.999, 'volume': pd.Series([1000] * len(prices))}}
    
    signal = strategy.generate_signal("EURUSD", data)
    
    assert signal['signal'] == 'buy'
    assert signal['confidence'] >= 0.7'''
            )
        ]
    
    def _generate_momentum_tests(self, class_name: str) -> List[TestCase]:
        """Generate momentum specific tests"""
        return [
            TestCase(
                name="test_bullish_momentum_signal",
                description="Test buy signal on bullish momentum",
                test_data={"trend": "upward"},
                expected_signal="buy",
                expected_confidence=0.7,
                test_code=f'''def test_bullish_momentum_signal():
    strategy = {class_name}(symbols=["EURUSD"])
    
    # Create upward trending data
    prices = pd.Series([1.1000, 1.1010, 1.1020, 1.1030, 1.1040, 1.1050])
    data = {{'close': prices, 'high': prices * 1.001, 'low': prices * 0.999, 'volume': pd.Series([1000] * len(prices))}}
    
    signal = strategy.generate_signal("EURUSD", data)
    
    # Should generate buy signal for upward momentum
    assert signal['signal'] in ['buy', 'hold']  # Depending on indicator sensitivity'''
            )
        ]
    
    def _generate_ml_tests(self, class_name: str) -> List[TestCase]:
        """Generate ML specific tests"""
        return [
            TestCase(
                name="test_feature_preparation",
                description="Test ML feature preparation",
                test_data={"features": ["rsi", "macd"]},
                expected_signal="hold",
                expected_confidence=0.0,
                test_code=f'''def test_feature_preparation():
    strategy = {class_name}(symbols=["EURUSD"])
    
    prices = pd.Series([1.1000, 1.1010, 1.1020, 1.1015, 1.1005] * 10)  # Longer series for indicators
    data = {{'close': prices, 'high': prices * 1.001, 'low': prices * 0.999, 'volume': pd.Series([1000] * len(prices))}}
    
    features = strategy.prepare_features(data)
    
    assert isinstance(features, dict)
    assert len(features) > 0'''
            )
        ]
    
    def _generate_documentation(self, request: StrategyRequest) -> str:
        """Generate strategy documentation"""
        doc_parts = [
            f"# {self._generate_strategy_name(request)}",
            "",
            "## Overview",
            f"This is an auto-generated {request.strategy_type.replace('_', ' ')} trading strategy.",
            "",
            "## Configuration",
            f"- **Symbols**: {', '.join(request.symbols)}",
            f"- **Timeframe**: {request.timeframe or '1H'}",
            f"- **Risk per Trade**: {request.risk_per_trade * 100:.1f}%",
            f"- **Max Positions**: {request.max_positions}",
        ]
        
        if request.indicators:
            doc_parts.extend([
                "",
                "## Technical Indicators",
                *[f"- {indicator.upper()}" for indicator in request.indicators]
            ])
        
        if request.strategy_type == "machine_learning":
            doc_parts.extend([
                "",
                "## Machine Learning Configuration",
                f"- **Model**: {request.ml_model}",
                f"- **Training Bars**: {request.training_bars}",
                f"- **Retrain Frequency**: {request.retrain_frequency} bars",
                f"- **Features**: {', '.join(request.features)}"
            ])
        
        doc_parts.extend([
            "",
            "## Usage",
            "```python",
            f"strategy = {self._generate_class_name(request)}(symbols={request.symbols})",
            "signal = strategy.generate_signal('EURUSD', market_data)",
            "```",
            "",
            "## Testing",
            "Run the generated test cases to validate the strategy:",
            "```bash",
            "pytest test_generated_strategy.py -v",
            "```"
        ])
        
        return '\n'.join(doc_parts)
    
    def _get_dependencies(self, request: StrategyRequest) -> List[str]:
        """Get list of required dependencies"""
        deps = ["pandas", "numpy", "ta"]
        
        if request.strategy_type == "machine_learning":
            deps.extend(["scikit-learn", "joblib"])
            
            if request.ml_model == "xgboost":
                deps.append("xgboost")
            elif request.ml_model == "lstm":
                deps.extend(["tensorflow", "keras"])
        
        return deps