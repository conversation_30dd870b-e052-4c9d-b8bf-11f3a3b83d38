class MeanReversionRSIStrategy(StrategyBase):
    """Mean Reversion strategy using RSI for overbought/oversold detection"""
    
    def __init__(self, symbol="EURUSD", timeframe="H1", mt5_bridge=None, risk_per_trade=0.02):
        super().__init__(
            name=f"Mean Reversion RSI {symbol} {timeframe}",
            symbols=[symbol],
            mt5_bridge=mt5_bridge,
            risk_per_trade=risk_per_trade
        )
        self.rsi_period = 14
        self.oversold_level = 30
        self.overbought_level = 70
        self.timeframe = timeframe
    
    def calculate_rsi(self, data, period=14):
        """Calculate RSI indicator"""
        close_prices = data['close']
        delta = close_prices.diff()
        
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        
        avg_gain = gain.rolling(window=period).mean()
        avg_loss = loss.rolling(window=period).mean()
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    def generate_signal(self, symbol, data):
        """Generate mean reversion signal based on RSI"""
        if len(data['close']) < self.rsi_period + 2:
            return {
                "signal": "hold",
                "confidence": 0,
                "reason": "Insufficient data for RSI calculation"
            }
        
        # Calculate RSI
        rsi = self.calculate_rsi(data, self.rsi_period)
        current_rsi = rsi.iloc[-1]
        prev_rsi = rsi.iloc[-2]
        
        current_price = data['close'].iloc[-1]
        
        # Mean reversion signals
        if current_rsi <= self.oversold_level and prev_rsi > current_rsi:
            return {
                "signal": "buy",
                "confidence": 0.8,
                "reason": f"RSI oversold at {current_rsi:.2f}, expecting bounce",
                "stop_loss": current_price * 0.99,
                "take_profit": current_price * 1.02
            }
        elif current_rsi >= self.overbought_level and prev_rsi < current_rsi:
            return {
                "signal": "sell",
                "confidence": 0.8,
                "reason": f"RSI overbought at {current_rsi:.2f}, expecting pullback",
                "stop_loss": current_price * 1.01,
                "take_profit": current_price * 0.98
            }
        else:
            return {
                "signal": "hold",
                "confidence": 0.3,
                "reason": f"RSI at neutral level: {current_rsi:.2f}"
            }
