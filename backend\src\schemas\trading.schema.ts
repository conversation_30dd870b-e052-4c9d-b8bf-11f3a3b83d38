import { z } from 'zod';

// Market data validation
export const MarketDataSchema = z.object({
  symbol: z.string()
    .min(1, 'Symbol is required')
    .max(20, 'Symbol must be 20 characters or less')
    .regex(/^[A-Z0-9\-=]+$/, 'Invalid symbol format'),
  
  timeframe: z.enum(['1m', '5m', '15m', '30m', '1h', '4h', '1d'], {
    errorMap: () => ({ message: 'Timeframe must be one of: 1m, 5m, 15m, 30m, 1h, 4h, 1d' })
  }),
  
  startDate: z.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format')
    .refine(date => {
      const parsed = new Date(date);
      return !isNaN(parsed.getTime()) && parsed >= new Date('2000-01-01');
    }, 'Invalid start date or date too early'),
  
  endDate: z.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format')
    .refine(date => {
      const parsed = new Date(date);
      return !isNaN(parsed.getTime()) && parsed <= new Date();
    }, 'Invalid end date or date in the future'),
  
  dataType: z.enum(['ohlcv', 'tick', 'orderbook']).optional().default('ohlcv')
}).refine(data => {
  const start = new Date(data.startDate);
  const end = new Date(data.endDate);
  return start < end;
}, {
  message: 'Start date must be before end date',
  path: ['startDate']
});

// Strategy parameters validation
export const StrategyParametersSchema = z.record(
  z.string(),
  z.union([z.number(), z.string(), z.boolean()])
).refine(params => {
  // Validate common financial parameters
  const validations = [];
  
  // Stop loss validation (0-100%)
  if ('stopLoss' in params && typeof params.stopLoss === 'number') {
    if (params.stopLoss < 0 || params.stopLoss > 1) {
      validations.push('stopLoss must be between 0 and 1 (0-100%)');
    }
  }
  
  // Take profit validation (0-500%)
  if ('takeProfit' in params && typeof params.takeProfit === 'number') {
    if (params.takeProfit < 0 || params.takeProfit > 5) {
      validations.push('takeProfit must be between 0 and 5 (0-500%)');
    }
  }
  
  // Risk per trade validation (0-50%)
  if ('riskPerTrade' in params && typeof params.riskPerTrade === 'number') {
    if (params.riskPerTrade < 0 || params.riskPerTrade > 0.5) {
      validations.push('riskPerTrade must be between 0 and 0.5 (0-50%)');
    }
  }
  
  // Period validations
  const periodFields = ['shortPeriod', 'longPeriod', 'period', 'rsiPeriod', 'smaPeriod'];
  periodFields.forEach(field => {
    if (field in params && typeof params[field] === 'number') {
      if (!Number.isInteger(params[field]) || params[field] < 1 || params[field] > 1000) {
        validations.push(`${field} must be an integer between 1 and 1000`);
      }
    }
  });
  
  // Cross-validation: shortPeriod < longPeriod
  if ('shortPeriod' in params && 'longPeriod' in params && 
      typeof params.shortPeriod === 'number' && typeof params.longPeriod === 'number') {
    if (params.shortPeriod >= params.longPeriod) {
      validations.push('shortPeriod must be less than longPeriod');
    }
  }
  
  return validations.length === 0;
}, {
  message: 'Invalid parameter values'
});

// Strategy configuration
export const StrategyConfigSchema = z.object({
  name: z.string()
    .min(1, 'Strategy name is required')
    .max(100, 'Strategy name must be 100 characters or less')
    .regex(/^[a-zA-Z0-9\s\-_\.]+$/, 'Strategy name contains invalid characters'),
  
  code: z.string()
    .min(10, 'Strategy code must be at least 10 characters')
    .max(50000, 'Strategy code must be 50,000 characters or less')
    .refine(code => {
      // Check for required trading_strategy function
      return code.includes('trading_strategy');
    }, {
      message: 'Strategy must define trading_strategy function'
    })
    .refine(code => {
      // Basic security check - no dangerous imports
      const dangerousPatterns = [
        /import\s+os/,
        /import\s+sys/,
        /import\s+subprocess/,
        /from\s+os/,
        /from\s+sys/,
        /from\s+subprocess/,
        /exec\s*\(/,
        /eval\s*\(/,
        /__import__/,
        /open\s*\(/
      ];
      
      return !dangerousPatterns.some(pattern => pattern.test(code));
    }, {
      message: 'Strategy code contains potentially dangerous operations'
    }),
  
  parameters: StrategyParametersSchema,
  
  description: z.string()
    .max(1000, 'Description must be 1000 characters or less')
    .optional(),
  
  version: z.number()
    .int()
    .min(1)
    .optional()
    .default(1),
  
  tags: z.array(z.string().max(50))
    .max(10, 'Maximum 10 tags allowed')
    .optional()
    .default([])
});

// Backtest configuration
export const BacktestConfigSchema = z.object({
  initialCapital: z.number()
    .min(100, 'Initial capital must be at least $100')
    .max(1000000000, 'Initial capital cannot exceed $1 billion')
    .finite('Initial capital must be a finite number'),
  
  positionSize: z.number()
    .min(0.001, 'Position size must be at least 0.1%')
    .max(1, 'Position size cannot exceed 100%')
    .finite('Position size must be a finite number'),
  
  commission: z.number()
    .min(0, 'Commission cannot be negative')
    .max(0.01, 'Commission cannot exceed 1%')
    .finite('Commission must be a finite number'),
  
  slippage: z.number()
    .min(0, 'Slippage cannot be negative')
    .max(0.01, 'Slippage cannot exceed 1%')
    .finite('Slippage must be a finite number'),
  
  marginRequirement: z.number()
    .min(0, 'Margin requirement cannot be negative')
    .max(1, 'Margin requirement cannot exceed 100%')
    .finite('Margin requirement must be a finite number')
    .optional(),
  
  maxPositions: z.number()
    .int('Max positions must be an integer')
    .min(1, 'Must allow at least 1 position')
    .max(100, 'Cannot exceed 100 simultaneous positions')
    .optional(),
  
  benchmark: z.string()
    .regex(/^[A-Z0-9\-=]+$/, 'Invalid benchmark symbol format')
    .optional(),
  
  startCash: z.number()
    .min(0)
    .finite()
    .optional(),
  
  riskFreeRate: z.number()
    .min(0)
    .max(1)
    .finite()
    .optional()
    .default(0.02) // 2% default risk-free rate
});

// Complete backtest request
export const BacktestRequestSchema = z.object({
  strategy: StrategyConfigSchema,
  marketData: MarketDataSchema,
  config: BacktestConfigSchema,
  
  // Optional metadata
  metadata: z.object({
    userId: z.string().optional(),
    requestId: z.string().optional(),
    priority: z.enum(['low', 'normal', 'high']).optional().default('normal'),
    tags: z.array(z.string()).optional(),
    notes: z.string().max(500).optional()
  }).optional()
}).refine(data => {
  // Cross-validation: ensure date range is reasonable for timeframe
  const start = new Date(data.marketData.startDate);
  const end = new Date(data.marketData.endDate);
  const daysDiff = (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24);
  
  // Minimum data requirements based on timeframe
  const minDays = {
    '1m': 1,
    '5m': 1,
    '15m': 1,
    '30m': 2,
    '1h': 3,
    '4h': 7,
    '1d': 30
  };
  
  const required = minDays[data.marketData.timeframe] || 30;
  return daysDiff >= required;
}, {
  message: 'Date range too short for selected timeframe',
  path: ['marketData', 'endDate']
});

// Export types
export type MarketData = z.infer<typeof MarketDataSchema>;
export type StrategyConfig = z.infer<typeof StrategyConfigSchema>;
export type BacktestConfig = z.infer<typeof BacktestConfigSchema>;
export type BacktestRequest = z.infer<typeof BacktestRequestSchema>;
export type StrategyParameters = z.infer<typeof StrategyParametersSchema>;

// Validation error class
export class ValidationError extends Error {
  constructor(
    message: string, 
    public errors: Array<{path: string, message: string}>
  ) {
    super(message);
    this.name = 'ValidationError';
  }
}

// Validation middleware function
export const validateBacktestRequest = (data: unknown): BacktestRequest => {
  const result = BacktestRequestSchema.safeParse(data);
  
  if (!result.success) {
    const errors = result.error.issues.map(issue => ({
      path: issue.path.join('.'),
      message: issue.message
    }));
    
    throw new ValidationError('Invalid backtest request', errors);
  }
  
  return result.data;
};

// Additional validation functions
export const validateMarketData = (data: unknown): MarketData => {
  const result = MarketDataSchema.safeParse(data);
  
  if (!result.success) {
    const errors = result.error.issues.map(issue => ({
      path: issue.path.join('.'),
      message: issue.message
    }));
    
    throw new ValidationError('Invalid market data', errors);
  }
  
  return result.data;
};

export const validateStrategyConfig = (data: unknown): StrategyConfig => {
  const result = StrategyConfigSchema.safeParse(data);
  
  if (!result.success) {
    const errors = result.error.issues.map(issue => ({
      path: issue.path.join('.'),
      message: issue.message
    }));
    
    throw new ValidationError('Invalid strategy configuration', errors);
  }
  
  return result.data;
};

export const validateBacktestConfig = (data: unknown): BacktestConfig => {
  const result = BacktestConfigSchema.safeParse(data);
  
  if (!result.success) {
    const errors = result.error.issues.map(issue => ({
      path: issue.path.join('.'),
      message: issue.message
    }));
    
    throw new ValidationError('Invalid backtest configuration', errors);
  }
  
  return result.data;
};

// Sanitization helpers
export const sanitizeStrategyName = (name: string): string => {
  return name
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/[^\w\s\-_.]/g, '') // Keep only safe characters
    .trim()
    .substring(0, 100); // Enforce length limit
};

export const sanitizeStrategyCode = (code: string): string => {
  // Basic sanitization - remove potential dangerous patterns
  return code
    .replace(/<!--[\s\S]*?-->/g, '') // Remove HTML comments
    .replace(/<script[\s\S]*?<\/script>/gi, '') // Remove script tags
    .trim()
    .substring(0, 50000); // Enforce length limit
};

// Schema validation middleware for Express
export const createValidationMiddleware = (schema: z.ZodSchema) => {
  return (req: any, res: any, next: any) => {
    try {
      const result = schema.safeParse(req.body);
      
      if (!result.success) {
        const errors = result.error.issues.map(issue => ({
          path: issue.path.join('.'),
          message: issue.message
        }));
        
        return res.status(400).json({
          error: 'Validation failed',
          details: errors
        });
      }
      
      req.validatedData = result.data;
      next();
    } catch (error) {
      return res.status(500).json({
        error: 'Validation error',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };
};

// Pre-configured middleware
export const validateBacktestRequestMiddleware = createValidationMiddleware(BacktestRequestSchema);
export const validateMarketDataMiddleware = createValidationMiddleware(MarketDataSchema);
export const validateStrategyConfigMiddleware = createValidationMiddleware(StrategyConfigSchema);