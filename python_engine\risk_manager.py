# python_engine/risk_manager.py
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum

logger = logging.getLogger(__name__)

class RiskLevel(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class RiskManagerException(Exception):
    """Custom exception for risk management errors"""
    pass

@dataclass
class Position:
    symbol: str
    volume: float
    entry_price: float
    current_price: float
    profit_loss: float
    timestamp: datetime
    order_type: str  # BUY or SELL

@dataclass
class RiskMetrics:
    total_exposure: float
    max_drawdown: float
    var_95: float  # Value at Risk 95%
    sharpe_ratio: float
    risk_level: RiskLevel
    margin_used: float
    free_margin: float

@dataclass
class RiskLimits:
    max_position_size: float = 10.0  # Maximum lot size per position
    max_total_exposure: float = 100.0  # Maximum total exposure
    max_drawdown_percent: float = 20.0  # Maximum drawdown percentage
    max_daily_loss: float = 1000.0  # Maximum daily loss in account currency
    max_correlation_exposure: float = 50.0  # Maximum exposure to correlated pairs

class RiskManager:
    """Risk management system with portfolio simulation capabilities"""
    
    def __init__(self, account_balance: float = 10000.0, offline_mode: bool = True):
        self.account_balance = account_balance
        self.offline_mode = offline_mode
        self.positions: List[Position] = []
        self.daily_pnl: List[float] = []
        self.risk_limits = RiskLimits()
        self.logger = logger
        
        # Offline simulation data
        self.mock_prices = {
            "EURUSD": 1.0850,
            "GBPUSD": 1.2650,
            "USDJPY": 149.50,
            "USDCHF": 0.8950,
            "AUDUSD": 0.6750
        }
        
        self.correlation_matrix = {
            ("EURUSD", "GBPUSD"): 0.75,
            ("EURUSD", "USDCHF"): -0.65,
            ("GBPUSD", "AUDUSD"): 0.60,
            ("USDJPY", "USDCHF"): 0.45
        }
        
        self.logger.info(f"RiskManager initialized - Balance: {account_balance}, Offline: {offline_mode}")
    
    def validate_position_size(self, symbol: str, volume: float) -> Tuple[bool, str]:
        """Validate if position size is within risk limits"""
        try:
            # Check maximum position size
            if volume > self.risk_limits.max_position_size:
                return False, f"Position size {volume} exceeds maximum allowed {self.risk_limits.max_position_size}"
            
            # Check total exposure after adding this position
            current_exposure = self.calculate_total_exposure()
            position_value = volume * 100000  # Standard lot size
            
            if current_exposure + position_value > self.risk_limits.max_total_exposure * 100000:
                return False, f"Total exposure would exceed limit of {self.risk_limits.max_total_exposure} lots"
            
            # Check correlation limits
            correlation_exposure = self.calculate_correlation_exposure(symbol, volume)
            if correlation_exposure > self.risk_limits.max_correlation_exposure:
                return False, f"Correlation exposure {correlation_exposure:.2f} exceeds limit {self.risk_limits.max_correlation_exposure}"
            
            return True, "Position size validated"
            
        except Exception as e:
            self.logger.error(f"Error validating position size: {str(e)}")
            raise RiskManagerException(f"Position validation failed: {str(e)}")
    
    def calculate_total_exposure(self) -> float:
        """Calculate total portfolio exposure"""
        total_exposure = 0.0
        for position in self.positions:
            position_value = position.volume * 100000  # Standard lot size
            total_exposure += position_value
        return total_exposure
    
    def calculate_correlation_exposure(self, symbol: str, volume: float) -> float:
        """Calculate exposure considering currency correlations"""
        correlation_exposure = volume
        
        for position in self.positions:
            correlation_key = tuple(sorted([symbol, position.symbol]))
            correlation = self.correlation_matrix.get(correlation_key, 0.0)
            
            if abs(correlation) > 0.5:  # Significant correlation
                correlation_exposure += position.volume * abs(correlation)
        
        return correlation_exposure
    
    def calculate_var(self, confidence_level: float = 0.95) -> float:
        """Calculate Value at Risk"""
        if len(self.daily_pnl) < 30:  # Need at least 30 days of data
            return 0.0
        
        sorted_pnl = sorted(self.daily_pnl)
        var_index = int((1 - confidence_level) * len(sorted_pnl))
        return abs(sorted_pnl[var_index]) if var_index < len(sorted_pnl) else 0.0
    
    def calculate_max_drawdown(self) -> float:
        """Calculate maximum drawdown"""
        if not self.daily_pnl:
            return 0.0
        
        cumulative_pnl = []
        running_total = 0.0
        
        for pnl in self.daily_pnl:
            running_total += pnl
            cumulative_pnl.append(running_total)
        
        max_drawdown = 0.0
        peak = cumulative_pnl[0]
        
        for value in cumulative_pnl:
            if value > peak:
                peak = value
            drawdown = peak - value
            if drawdown > max_drawdown:
                max_drawdown = drawdown
        
        return max_drawdown
    
    def calculate_sharpe_ratio(self) -> float:
        """Calculate Sharpe ratio"""
        if len(self.daily_pnl) < 2:
            return 0.0
        
        mean_return = sum(self.daily_pnl) / len(self.daily_pnl)
        variance = sum((x - mean_return) ** 2 for x in self.daily_pnl) / len(self.daily_pnl)
        std_dev = variance ** 0.5
        
        return mean_return / std_dev if std_dev > 0 else 0.0
    
    def assess_risk_level(self) -> RiskLevel:
        """Assess current portfolio risk level"""
        risk_score = 0
        
        # Check exposure
        exposure_ratio = self.calculate_total_exposure() / (self.risk_limits.max_total_exposure * 100000)
        if exposure_ratio > 0.8:
            risk_score += 3
        elif exposure_ratio > 0.6:
            risk_score += 2
        elif exposure_ratio > 0.4:
            risk_score += 1
        
        # Check drawdown
        max_drawdown = self.calculate_max_drawdown()
        drawdown_ratio = max_drawdown / self.account_balance
        if drawdown_ratio > 0.15:
            risk_score += 3
        elif drawdown_ratio > 0.10:
            risk_score += 2
        elif drawdown_ratio > 0.05:
            risk_score += 1
        
        # Check VaR
        var_95 = self.calculate_var()
        var_ratio = var_95 / self.account_balance
        if var_ratio > 0.10:
            risk_score += 3
        elif var_ratio > 0.05:
            risk_score += 2
        elif var_ratio > 0.02:
            risk_score += 1
        
        # Determine risk level
        if risk_score >= 7:
            return RiskLevel.CRITICAL
        elif risk_score >= 5:
            return RiskLevel.HIGH
        elif risk_score >= 3:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW
    
    def get_risk_metrics(self) -> RiskMetrics:
        """Get comprehensive risk metrics"""
        total_exposure = self.calculate_total_exposure()
        max_drawdown = self.calculate_max_drawdown()
        var_95 = self.calculate_var()
        sharpe_ratio = self.calculate_sharpe_ratio()
        risk_level = self.assess_risk_level()
        
        # Calculate margin metrics
        margin_used = total_exposure * 0.01  # 1% margin requirement
        free_margin = self.account_balance - margin_used
        
        return RiskMetrics(
            total_exposure=total_exposure,
            max_drawdown=max_drawdown,
            var_95=var_95,
            sharpe_ratio=sharpe_ratio,
            risk_level=risk_level,
            margin_used=margin_used,
            free_margin=free_margin
        )
    
    def add_position(self, symbol: str, volume: float, entry_price: float, order_type: str) -> bool:
        """Add a new position to the portfolio"""
        try:
            # Validate position first
            is_valid, message = self.validate_position_size(symbol, volume)
            if not is_valid:
                self.logger.warning(f"Position rejected: {message}")
                return False
            
            # Get current price (mock in offline mode)
            current_price = self.mock_prices.get(symbol, entry_price)
            
            # Calculate P&L
            if order_type.upper() == "BUY":
                profit_loss = (current_price - entry_price) * volume * 100000
            else:  # SELL
                profit_loss = (entry_price - current_price) * volume * 100000
            
            position = Position(
                symbol=symbol,
                volume=volume,
                entry_price=entry_price,
                current_price=current_price,
                profit_loss=profit_loss,
                timestamp=datetime.now(),
                order_type=order_type
            )
            
            self.positions.append(position)
            self.logger.info(f"Position added: {symbol} {volume} lots at {entry_price}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error adding position: {str(e)}")
            raise RiskManagerException(f"Failed to add position: {str(e)}")
    
    def remove_position(self, symbol: str, volume: float) -> bool:
        """Remove a position from the portfolio"""
        try:
            for i, position in enumerate(self.positions):
                if position.symbol == symbol and position.volume == volume:
                    removed_position = self.positions.pop(i)
                    
                    # Add to daily P&L
                    self.daily_pnl.append(removed_position.profit_loss)
                    
                    self.logger.info(f"Position removed: {symbol} {volume} lots, P&L: {removed_position.profit_loss:.2f}")
                    return True
            
            self.logger.warning(f"Position not found: {symbol} {volume} lots")
            return False
            
        except Exception as e:
            self.logger.error(f"Error removing position: {str(e)}")
            raise RiskManagerException(f"Failed to remove position: {str(e)}")
    
    def update_prices(self, price_updates: Dict[str, float]) -> None:
        """Update current prices and recalculate P&L"""
        try:
            # Update mock prices
            self.mock_prices.update(price_updates)
            
            # Recalculate P&L for all positions
            for position in self.positions:
                if position.symbol in price_updates:
                    position.current_price = price_updates[position.symbol]
                    
                    if position.order_type.upper() == "BUY":
                        position.profit_loss = (position.current_price - position.entry_price) * position.volume * 100000
                    else:  # SELL
                        position.profit_loss = (position.entry_price - position.current_price) * position.volume * 100000
            
            self.logger.debug(f"Prices updated for {len(price_updates)} symbols")
            
        except Exception as e:
            self.logger.error(f"Error updating prices: {str(e)}")
            raise RiskManagerException(f"Failed to update prices: {str(e)}")
    
    def check_stop_loss_conditions(self) -> List[Dict]:
        """Check if any positions should be stopped out"""
        stop_loss_alerts = []
        
        try:
            current_total_loss = sum(pos.profit_loss for pos in self.positions if pos.profit_loss < 0)
            
            # Check daily loss limit
            if abs(current_total_loss) > self.risk_limits.max_daily_loss:
                stop_loss_alerts.append({
                    "type": "daily_loss_limit",
                    "message": f"Daily loss limit exceeded: {current_total_loss:.2f}",
                    "action": "close_all_losing_positions"
                })
            
            # Check individual position drawdown
            for position in self.positions:
                position_drawdown = abs(position.profit_loss) / (position.volume * 100000)
                if position_drawdown > 0.05:  # 5% position drawdown
                    stop_loss_alerts.append({
                        "type": "position_drawdown",
                        "symbol": position.symbol,
                        "message": f"Position drawdown {position_drawdown:.2%} exceeds 5%",
                        "action": "close_position"
                    })
            
            return stop_loss_alerts
            
        except Exception as e:
            self.logger.error(f"Error checking stop loss conditions: {str(e)}")
            return []
    
    def get_position_summary(self) -> Dict:
        """Get summary of all positions"""
        try:
            total_positions = len(self.positions)
            total_volume = sum(pos.volume for pos in self.positions)
            total_pnl = sum(pos.profit_loss for pos in self.positions)
            
            winning_positions = [pos for pos in self.positions if pos.profit_loss > 0]
            losing_positions = [pos for pos in self.positions if pos.profit_loss < 0]
            
            return {
                "total_positions": total_positions,
                "total_volume": total_volume,
                "total_pnl": total_pnl,
                "winning_positions": len(winning_positions),
                "losing_positions": len(losing_positions),
                "win_rate": len(winning_positions) / total_positions if total_positions > 0 else 0.0,
                "average_win": sum(pos.profit_loss for pos in winning_positions) / len(winning_positions) if winning_positions else 0.0,
                "average_loss": sum(pos.profit_loss for pos in losing_positions) / len(losing_positions) if losing_positions else 0.0
            }
            
        except Exception as e:
            self.logger.error(f"Error getting position summary: {str(e)}")
            return {}
    
    def reset_portfolio(self) -> None:
        """Reset portfolio for testing purposes"""
        self.positions.clear()
        self.daily_pnl.clear()
        self.logger.info("Portfolio reset")