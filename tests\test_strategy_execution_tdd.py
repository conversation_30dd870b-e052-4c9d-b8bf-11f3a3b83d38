"""
TDD tests for Strategy Execution component
Focusing on signal generation and parameter handling
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime
from unittest.mock import patch, MagicMock

# Import the backtesting components
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src', 'trading'))

from backtest import (
    TradingStrategy, RSITradingStrategy, StrategyError
)


class TestStrategyExecutionTDD:
    """
    TDD tests for the Strategy Execution component
    """
    
    @pytest.fixture
    def sample_data(self):
        """Sample price data for testing"""
        return pd.DataFrame({
            'close': [100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110,
                     109, 108, 107, 106, 105, 104, 103, 102, 101, 100,
                     101, 102, 103, 104, 105, 106, 107, 108, 109, 110]
        })
    
    @pytest.fixture
    def rsi_data(self):
        """Data designed to trigger RSI signals"""
        # Create data that will generate clear RSI signals
        prices = []
        # First create a downtrend to generate oversold condition
        for i in range(15):
            prices.append(100 - i * 2)
        # Then create an uptrend to generate overbought condition
        for i in range(15):
            prices.append(70 + i * 3)
        
        return pd.DataFrame({'close': prices})
    
    def test_base_strategy_initialization(self):
        """Test that the base strategy initializes correctly"""
        strategy = TradingStrategy()
        assert strategy.params == {}
        assert strategy.name == "TradingStrategy"
        
        strategy = TradingStrategy(params={'param1': 10, 'param2': 'value'})
        assert strategy.params == {'param1': 10, 'param2': 'value'}
    
    def test_base_strategy_requires_implementation(self, sample_data):
        """Test that the base strategy requires implementation of generate_signals"""
        strategy = TradingStrategy()
        
        with pytest.raises(NotImplementedError):
            strategy.generate_signals(sample_data)
    
    def test_rsi_strategy_initialization(self):
        """Test RSI strategy initialization with parameters"""
        # Default parameters
        strategy = RSITradingStrategy()
        assert strategy.params['rsi_period'] == 14
        assert strategy.params['overbought'] == 70
        assert strategy.params['oversold'] == 30
        
        # Custom parameters
        custom_params = {'rsi_period': 21, 'overbought': 80, 'oversold': 20}
        strategy = RSITradingStrategy(params=custom_params)
        assert strategy.params['rsi_period'] == 21
        assert strategy.params['overbought'] == 80
        assert strategy.params['oversold'] == 20
    
    def test_rsi_strategy_signal_generation(self, rsi_data):
        """Test that RSI strategy generates correct signals"""
        strategy = RSITradingStrategy(params={'rsi_period': 10, 'overbought': 70, 'oversold': 30})
        
        signals = strategy.generate_signals(rsi_data)
        
        assert isinstance(signals, pd.Series)
        assert len(signals) == len(rsi_data)
        
        # Verify signals are in the correct range
        assert all(signal in [-1, 0, 1] for signal in signals)
        
        # Check for buy signals in the first half (downtrend should trigger oversold)
        first_half = signals.iloc[:15]
        assert (first_half == 1).any(), "Should have at least one buy signal in downtrend"
        
        # Check for sell signals in the second half (uptrend should trigger overbought)
        second_half = signals.iloc[15:]
        assert (second_half == -1).any(), "Should have at least one sell signal in uptrend"
    
    def test_rsi_calculation(self):
        """Test RSI calculation with known values"""
        # Use known price sequence for RSI calculation
        prices = [44.34, 44.09, 44.15, 43.61, 44.33, 44.83, 45.10, 45.42, 45.84, 46.08,
                 45.89, 46.03, 45.61, 46.28, 46.28, 46.00, 46.03, 46.41, 46.22, 45.64]
        
        data = pd.DataFrame({'close': prices})
        strategy = RSITradingStrategy(params={'rsi_period': 14})
        
        # Access the private method for testing
        rsi_values = strategy._calculate_rsi(pd.Series(prices), 14)
        
        # RSI should be between 0 and 100
        assert all(0 <= rsi <= 100 for rsi in rsi_values.dropna())
        
        # For this specific data, RSI should be around 57-58 at the end
        # (based on known calculation)
        assert 50 <= rsi_values.iloc[-1] <= 65
    
    def test_rsi_strategy_data_validation(self):
        """Test RSI strategy data validation"""
        strategy = RSITradingStrategy()
        
        # Missing close column
        bad_data = pd.DataFrame({'open': [1, 2, 3, 4, 5]})
        with pytest.raises(StrategyError):
            strategy.generate_signals(bad_data)
        
        # Insufficient data
        short_data = pd.DataFrame({'close': [1, 2, 3]})  # Need 15+ for RSI period 14
        with pytest.raises(StrategyError):
            strategy.generate_signals(short_data)
    
    def test_strategy_position_sizing(self, sample_data):
        """Test strategy position sizing"""
        strategy = TradingStrategy()
        
        # Test with different signals and capitals
        position_size_buy = strategy.get_position_size(1, 100, 10000)
        position_size_sell = strategy.get_position_size(-1, 100, 10000)
        position_size_hold = strategy.get_position_size(0, 100, 10000)
        
        # Buy and sell should result in non-zero position size
        assert position_size_buy > 0
        assert position_size_sell > 0
        # Hold should result in zero position size
        assert position_size_hold == 0
        
        # Position size should be proportional to available capital
        assert strategy.get_position_size(1, 100, 20000) == 2 * position_size_buy
    
    def test_custom_strategy_implementation(self, sample_data):
        """Test custom strategy implementation"""
        class MovingAverageStrategy(TradingStrategy):
            def __init__(self, params=None):
                default_params = {'short_window': 5, 'long_window': 10}
                if params:
                    default_params.update(params)
                super().__init__(default_params)
            
            def generate_signals(self, data):
                if 'close' not in data.columns:
                    raise StrategyError("Data must contain 'close' column")
                
                if len(data) < self.params['long_window']:
                    raise StrategyError(f"Insufficient data: need at least {self.params['long_window']} rows")
                
                # Calculate moving averages
                short_ma = data['close'].rolling(window=self.params['short_window']).mean()
                long_ma = data['close'].rolling(window=self.params['long_window']).mean()
                
                # Generate signals
                signals = pd.Series(0, index=data.index)
                signals[short_ma > long_ma] = 1    # Buy signal
                signals[short_ma < long_ma] = -1   # Sell signal
                
                return signals
        
        # Test the custom strategy
        strategy = MovingAverageStrategy()
        signals = strategy.generate_signals(sample_data)
        
        assert isinstance(signals, pd.Series)
        assert len(signals) == len(sample_data)
        assert all(signal in [-1, 0, 1] for signal in signals)
        
        # In an uptrend, we should see buy signals after the initial period
        assert (signals.iloc[strategy.params['long_window']:] == 1).any()
    
    def test_strategy_hash_generation(self):
        """Test strategy hash generation for audit trail"""
        strategy1 = RSITradingStrategy(params={'rsi_period': 14, 'overbought': 70})
        strategy2 = RSITradingStrategy(params={'rsi_period': 14, 'overbought': 70})
        strategy3 = RSITradingStrategy(params={'rsi_period': 21, 'overbought': 70})
        
        # Same parameters should produce same hash
        assert strategy1.get_hash() == strategy2.get_hash()
        
        # Different parameters should produce different hash
        assert strategy1.get_hash() != strategy3.get_hash()
        
        # Hash should be a string of reasonable length
        assert isinstance(strategy1.get_hash(), str)
        assert len(strategy1.get_hash()) > 0


if __name__ == "__main__":
    pytest.main(["-v", __file__])