"""
Strategy Helper API
Provides endpoints for AI trading prompts and quantitative strategies
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Dict, List, Optional, Any
import sys
import os

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'src'))

try:
    from strategy_helpers.ai_trading_prompts import AITrading<PERSON>romptsLibrary, PromptCategory
    from strategy_helpers.quant_strategies import QuantStrategiesLibrary, StrategyType, TimeFrame
    from strategy_helpers.strategy_assistant import StrategyAssistant
except ImportError:
    print("Warning: Strategy helper modules not found. Using mock data.")
    PromptCategory = object()
    
router = APIRouter(tags=["Strategy API"])

# Initialize libraries
try:
    ai_prompts = AITradingPromptsLibrary()
    quant_strategies = QuantStrategiesLibrary()
    strategy_assistant = StrategyAssistant()
    has_libraries = True
except Exception as e:
    print(f"Error initializing strategy libraries: {e}")
    has_libraries = False

# Pydantic models
class UserProfile(BaseModel):
    experience_level: str = "beginner"
    asset_classes: List[str] = ["forex"]
    timeframes: List[str] = ["1h"]
    risk_tolerance: str = "medium"
    capital: float = 10000
    goals: List[str] = ["learning"]

class PromptRequest(BaseModel):
    prompt_id: str
    variables: Dict[str, str]

class StrategyBacktestRequest(BaseModel):
    strategy_name: str
    parameters: Optional[Dict[str, Any]] = None

# Default prompts for when the library is not available
DEFAULT_PROMPTS = [
    {
        "id": "trend_following",
        "title": "Trend Following Strategy",
        "description": "Simple yet effective trend following strategy using moving averages",
        "category": "technical_analysis",
        "prompt": "Create a trend following strategy using 20 and 50 period moving averages for EUR/USD on the H1 timeframe."
    },
    {
        "id": "rsi_strategy",
        "title": "RSI Reversal Strategy",
        "description": "Mean reversion strategy using RSI indicator to identify overbought/oversold conditions",
        "category": "technical_analysis",
        "prompt": "Create a mean reversion strategy using the RSI indicator for EUR/USD. Buy when RSI goes below 30 and sell when it goes above 70."
    },
    {
        "id": "breakout_strategy",
        "title": "Breakout Strategy",
        "description": "Strategy to capture price breakouts from consolidation patterns",
        "category": "trade_execution", 
        "prompt": "Create a breakout strategy that identifies consolidation patterns and enters trades when price breaks out with increased volume."
    },
    {
        "id": "mql5_indicator",
        "title": "Custom MQL5 Indicator",
        "description": "Create a custom indicator in MQL5 without paying marketplace fees",
        "category": "trade_execution",
        "prompt": "Generate a custom MQL5 indicator that identifies divergences between price and RSI."
    }
]

# API Endpoints

@router.get("/ai-prompts")
async def get_ai_prompts():
    """Get all available AI prompts"""
    if has_libraries:
        try:
            prompts = ai_prompts.list_all_prompts()
            return [
                {
                    "id": p.id,
                    "title": p.title,
                    "description": p.description,
                    "category": p.category.value,
                    "variables": p.variables,
                    "example_usage": p.example_usage,
                    "expected_output": p.expected_output,
                    "prompt": p.prompt_template
                }
                for p in prompts
            ]
        except Exception as e:
            print(f"Error getting prompts: {e}")
            return DEFAULT_PROMPTS
    else:
        return DEFAULT_PROMPTS

@router.get("/api/v1/strategies/prompts")
async def get_strategy_prompts():
    """Alias endpoint for AI prompts"""
    return await get_ai_prompts()
