import { User, CreateUserRequest, LoginRequest } from '../shared/types';

export const getMockUser = (overrides?: Partial<User>): User => {
  const baseUser: User = {
    id: 'user_123e4567-e89b-12d3-a456-426614174000' as User['id'],
    email: '<EMAIL>',
    fullName: 'Test User',
    subscriptionTier: 'free',
    apiQuotaUsed: 0,
    apiQuotaLimit: 100,
    createdAt: new Date('2023-01-01T00:00:00.000Z'),
    updatedAt: new Date('2023-01-01T00:00:00.000Z'),
  };

  return { ...baseUser, ...overrides };
};

export const getMockUsers = (count: number = 3): User[] => {
  return Array.from({ length: count }, (_, index) => 
    getMockUser({
      id: `user_${index + 1}` as User['id'],
      email: `user${index + 1}@example.com`,
      fullName: `User ${index + 1}`,
    })
  );
};

// Create User Request Factory
export const getMockCreateUserRequest = (overrides?: Partial<CreateUserRequest>): CreateUserRequest => {
  const baseRequest: CreateUserRequest = {
    email: '<EMAIL>',
    password: 'SecurePassword123!',
    fullName: 'New User',
  };

  return { ...baseRequest, ...overrides };
};

// Login Request Factory
export const getMockLoginRequest = (overrides?: Partial<LoginRequest>): LoginRequest => {
  const baseRequest: LoginRequest = {
    email: '<EMAIL>',
    password: 'SecurePassword123!',
  };

  return { ...baseRequest, ...overrides };
};

// Admin User Factory (using enterprise tier as admin equivalent)
export const getMockAdminUser = (overrides: Partial<User> = {}): User => {
  return getMockUser({
    subscriptionTier: 'enterprise',
    fullName: 'Admin User',
    email: '<EMAIL>',
    ...overrides
  });
};

// Premium User Factory
export const getMockPremiumUser = (overrides: Partial<User> = {}): User => {
  return getMockUser({
    subscriptionTier: 'pro',
    fullName: 'Premium User',
    apiQuotaLimit: 1000,
    ...overrides,
  });
};

// Multiple Users Factory
export const getMockUserList = (): User[] => {
  return [
    getMockUser({
      id: 'user_1' as User['id'],
      email: '<EMAIL>',
      fullName: 'User One',
    }),
    getMockAdminUser({
      id: 'admin_1' as User['id'],
      email: '<EMAIL>',
    }),
    getMockPremiumUser({
      id: 'premium_1' as User['id'],
      email: '<EMAIL>',
    }),
  ];
};