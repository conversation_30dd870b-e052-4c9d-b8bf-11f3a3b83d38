"""
Quantitative Trading Strategies Library
Based on proven strategies from the quantitative trading community
Inspired by je-suis-tm/quant-trading repository
"""

from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
import logging

logger = logging.getLogger(__name__)

class StrategyType(Enum):
    """Types of quantitative strategies"""
    MEAN_REVERSION = "mean_reversion"
    MOMENTUM = "momentum"
    BREAKOUT = "breakout"
    PAIRS_TRADING = "pairs_trading"
    VOLATILITY = "volatility"
    ARBITRAGE = "arbitrage"
    MACHINE_LEARNING = "machine_learning"
    OPTIONS = "options"
    MULTI_ASSET = "multi_asset"

class TimeFrame(Enum):
    """Trading timeframes"""
    MINUTE_1 = "1m"
    MINUTE_5 = "5m"
    MINUTE_15 = "15m"
    MINUTE_30 = "30m"
    HOUR_1 = "1h"
    HOUR_4 = "4h"
    DAILY = "1d"
    WEEKLY = "1w"
    MONTHLY = "1M"

@dataclass
class StrategyMetadata:
    """Metadata for trading strategies"""
    name: str
    description: str
    strategy_type: StrategyType
    timeframes: List[TimeFrame]
    assets: List[str]  # Asset classes: forex, stocks, crypto, commodities
    complexity: str  # beginner, intermediate, advanced
    expected_sharpe: float
    max_drawdown: float
    win_rate: float
    parameters: Dict[str, Any]
    requirements: List[str]  # Required indicators, data, etc.

class BaseStrategy(ABC):
    """Base class for all quantitative strategies"""
    
    def __init__(self, name: str, parameters: Dict[str, Any] = None):
        self.name = name
        self.parameters = parameters or {}
        self.signals = []
        self.positions = []
        
    @abstractmethod
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate trading signals based on the strategy"""
        pass
    
    @abstractmethod
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate required technical indicators"""
        pass
    
    def backtest(self, data: pd.DataFrame, initial_capital: float = 10000) -> Dict[str, Any]:
        """Basic backtesting framework"""
        signals = self.generate_signals(data)
        
        # Simple backtesting logic
        portfolio_value = initial_capital
        positions = 0
        trades = []
        
        for i in range(1, len(signals)):
            if signals.iloc[i]['signal'] == 1 and positions == 0:  # Buy signal
                positions = portfolio_value / signals.iloc[i]['close']
                entry_price = signals.iloc[i]['close']
                entry_date = signals.index[i]
                
            elif signals.iloc[i]['signal'] == -1 and positions > 0:  # Sell signal
                exit_price = signals.iloc[i]['close']
                exit_date = signals.index[i]
                pnl = (exit_price - entry_price) * positions
                portfolio_value += pnl
                
                trades.append({
                    'entry_date': entry_date,
                    'exit_date': exit_date,
                    'entry_price': entry_price,
                    'exit_price': exit_price,
                    'pnl': pnl,
                    'return': pnl / (entry_price * positions)
                })
                positions = 0
        
        return self._calculate_performance_metrics(trades, initial_capital)
    
    def _calculate_performance_metrics(self, trades: List[Dict], initial_capital: float) -> Dict[str, Any]:
        """Calculate performance metrics from trades"""
        if not trades:
            return {"error": "No trades generated"}
        
        returns = [trade['return'] for trade in trades]
        total_return = sum([trade['pnl'] for trade in trades]) / initial_capital
        win_rate = len([r for r in returns if r > 0]) / len(returns)
        
        return {
            'total_trades': len(trades),
            'total_return': total_return,
            'win_rate': win_rate,
            'avg_return': np.mean(returns),
            'sharpe_ratio': np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0,
            'max_drawdown': self._calculate_max_drawdown(trades),
            'trades': trades
        }
    
    def _calculate_max_drawdown(self, trades: List[Dict]) -> float:
        """Calculate maximum drawdown"""
        cumulative_returns = np.cumsum([trade['return'] for trade in trades])
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdown = (cumulative_returns - running_max)
        return abs(min(drawdown)) if len(drawdown) > 0 else 0

class RSIMeanReversionStrategy(BaseStrategy):
    """RSI-based mean reversion strategy"""
    
    def __init__(self, rsi_period: int = 14, oversold: int = 30, overbought: int = 70):
        super().__init__("RSI Mean Reversion")
        self.parameters = {
            'rsi_period': rsi_period,
            'oversold': oversold,
            'overbought': overbought
        }
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate RSI indicator"""
        df = data.copy()
        
        # Calculate RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=self.parameters['rsi_period']).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=self.parameters['rsi_period']).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        return df
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate RSI mean reversion signals"""
        df = self.calculate_indicators(data)
        
        # Generate signals
        df['signal'] = 0
        df.loc[df['rsi'] < self.parameters['oversold'], 'signal'] = 1  # Buy
        df.loc[df['rsi'] > self.parameters['overbought'], 'signal'] = -1  # Sell
        
        return df

class BollingerBandStrategy(BaseStrategy):
    """Bollinger Bands breakout/mean reversion strategy"""
    
    def __init__(self, period: int = 20, std_dev: float = 2.0, mode: str = "breakout"):
        super().__init__("Bollinger Bands")
        self.parameters = {
            'period': period,
            'std_dev': std_dev,
            'mode': mode  # "breakout" or "mean_reversion"
        }
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate Bollinger Bands"""
        df = data.copy()
        
        # Calculate Bollinger Bands
        df['bb_middle'] = df['close'].rolling(window=self.parameters['period']).mean()
        bb_std = df['close'].rolling(window=self.parameters['period']).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * self.parameters['std_dev'])
        df['bb_lower'] = df['bb_middle'] - (bb_std * self.parameters['std_dev'])
        
        # Calculate bandwidth and %B
        df['bb_bandwidth'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
        df['bb_percent'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        return df
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate Bollinger Band signals"""
        df = self.calculate_indicators(data)
        
        df['signal'] = 0
        
        if self.parameters['mode'] == "breakout":
            # Breakout strategy
            df.loc[df['close'] > df['bb_upper'], 'signal'] = 1  # Buy breakout
            df.loc[df['close'] < df['bb_lower'], 'signal'] = -1  # Sell breakout
        else:
            # Mean reversion strategy
            df.loc[df['close'] < df['bb_lower'], 'signal'] = 1  # Buy oversold
            df.loc[df['close'] > df['bb_upper'], 'signal'] = -1  # Sell overbought
        
        return df

class MACDStrategy(BaseStrategy):
    """MACD momentum strategy"""
    
    def __init__(self, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9):
        super().__init__("MACD Momentum")
        self.parameters = {
            'fast_period': fast_period,
            'slow_period': slow_period,
            'signal_period': signal_period
        }
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate MACD indicators"""
        df = data.copy()
        
        # Calculate MACD
        ema_fast = df['close'].ewm(span=self.parameters['fast_period']).mean()
        ema_slow = df['close'].ewm(span=self.parameters['slow_period']).mean()
        df['macd'] = ema_fast - ema_slow
        df['macd_signal'] = df['macd'].ewm(span=self.parameters['signal_period']).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        
        return df
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate MACD signals"""
        df = self.calculate_indicators(data)
        
        df['signal'] = 0
        
        # MACD line crosses above signal line
        df.loc[(df['macd'] > df['macd_signal']) & 
               (df['macd'].shift(1) <= df['macd_signal'].shift(1)), 'signal'] = 1
        
        # MACD line crosses below signal line
        df.loc[(df['macd'] < df['macd_signal']) & 
               (df['macd'].shift(1) >= df['macd_signal'].shift(1)), 'signal'] = -1
        
        return df

class DualThrustStrategy(BaseStrategy):
    """Dual Thrust breakout strategy"""
    
    def __init__(self, k1: float = 0.5, k2: float = 0.5, lookback: int = 4):
        super().__init__("Dual Thrust")
        self.parameters = {
            'k1': k1,  # Upper breakout coefficient
            'k2': k2,  # Lower breakout coefficient
            'lookback': lookback  # Lookback period for range calculation
        }
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate Dual Thrust levels"""
        df = data.copy()
        
        # Calculate HH, LL, HC, LC over lookback period
        df['hh'] = df['high'].rolling(window=self.parameters['lookback']).max()
        df['ll'] = df['low'].rolling(window=self.parameters['lookback']).min()
        df['hc'] = df['close'].rolling(window=self.parameters['lookback']).max()
        df['lc'] = df['close'].rolling(window=self.parameters['lookback']).min()
        
        # Calculate range
        df['range'] = np.maximum(df['hh'] - df['lc'], df['hc'] - df['ll'])
        
        # Calculate breakout levels
        df['upper_level'] = df['open'] + self.parameters['k1'] * df['range']
        df['lower_level'] = df['open'] - self.parameters['k2'] * df['range']
        
        return df
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate Dual Thrust signals"""
        df = self.calculate_indicators(data)
        
        df['signal'] = 0
        
        # Buy when price breaks above upper level
        df.loc[df['close'] > df['upper_level'], 'signal'] = 1
        
        # Sell when price breaks below lower level
        df.loc[df['close'] < df['lower_level'], 'signal'] = -1
        
        return df

class PairsTradingStrategy(BaseStrategy):
    """Statistical arbitrage pairs trading strategy"""
    
    def __init__(self, lookback: int = 60, entry_threshold: float = 2.0, exit_threshold: float = 0.5):
        super().__init__("Pairs Trading")
        self.parameters = {
            'lookback': lookback,
            'entry_threshold': entry_threshold,
            'exit_threshold': exit_threshold
        }
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate pairs trading indicators"""
        df = data.copy()
        
        # Assuming data has 'asset1' and 'asset2' columns
        if 'asset1' not in df.columns or 'asset2' not in df.columns:
            raise ValueError("Pairs trading requires 'asset1' and 'asset2' price columns")
        
        # Calculate spread
        df['spread'] = df['asset1'] - df['asset2']
        
        # Calculate rolling statistics
        df['spread_mean'] = df['spread'].rolling(window=self.parameters['lookback']).mean()
        df['spread_std'] = df['spread'].rolling(window=self.parameters['lookback']).std()
        
        # Calculate z-score
        df['z_score'] = (df['spread'] - df['spread_mean']) / df['spread_std']
        
        return df
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate pairs trading signals"""
        df = self.calculate_indicators(data)
        
        df['signal'] = 0
        
        # Entry signals
        df.loc[df['z_score'] > self.parameters['entry_threshold'], 'signal'] = -1  # Short spread
        df.loc[df['z_score'] < -self.parameters['entry_threshold'], 'signal'] = 1  # Long spread
        
        # Exit signals
        df.loc[abs(df['z_score']) < self.parameters['exit_threshold'], 'signal'] = 0
        
        return df

class LondonBreakoutStrategy(BaseStrategy):
    """London session breakout strategy"""
    
    def __init__(self, breakout_period: int = 30, min_range: float = 0.001):
        super().__init__("London Breakout")
        self.parameters = {
            'breakout_period': breakout_period,  # Minutes for breakout calculation
            'min_range': min_range  # Minimum range for valid breakout
        }
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate London breakout levels"""
        df = data.copy()
        
        # Identify London session (8:00-9:00 GMT)
        # This is simplified - in practice, you'd need proper timezone handling
        df['hour'] = pd.to_datetime(df.index).hour
        df['london_session'] = (df['hour'] >= 8) & (df['hour'] <= 9)
        
        # Calculate breakout levels during London session
        london_high = df[df['london_session']]['high'].max()
        london_low = df[df['london_session']]['low'].min()
        
        df['breakout_high'] = london_high
        df['breakout_low'] = london_low
        df['breakout_range'] = london_high - london_low
        
        return df
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate London breakout signals"""
        df = self.calculate_indicators(data)
        
        df['signal'] = 0
        
        # Only trade if range is significant
        valid_range = df['breakout_range'] > self.parameters['min_range']
        
        # Buy breakout above London high
        df.loc[valid_range & (df['close'] > df['breakout_high']), 'signal'] = 1
        
        # Sell breakout below London low
        df.loc[valid_range & (df['close'] < df['breakout_low']), 'signal'] = -1
        
        return df

class QuantStrategiesLibrary:
    """Library of quantitative trading strategies"""
    
    def __init__(self):
        self.strategies = self._initialize_strategies()
        self.metadata = self._initialize_metadata()
    
    def _initialize_strategies(self) -> Dict[str, BaseStrategy]:
        """Initialize available strategies"""
        return {
            'rsi_mean_reversion': RSIMeanReversionStrategy(),
            'bollinger_bands_breakout': BollingerBandStrategy(mode="breakout"),
            'bollinger_bands_mean_reversion': BollingerBandStrategy(mode="mean_reversion"),
            'macd_momentum': MACDStrategy(),
            'dual_thrust': DualThrustStrategy(),
            'pairs_trading': PairsTradingStrategy(),
            'london_breakout': LondonBreakoutStrategy()
        }
    
    def _initialize_metadata(self) -> Dict[str, StrategyMetadata]:
        """Initialize strategy metadata"""
        return {
            'rsi_mean_reversion': StrategyMetadata(
                name="RSI Mean Reversion",
                description="Buy oversold and sell overbought conditions based on RSI",
                strategy_type=StrategyType.MEAN_REVERSION,
                timeframes=[TimeFrame.HOUR_1, TimeFrame.HOUR_4, TimeFrame.DAILY],
                assets=["forex", "stocks", "crypto"],
                complexity="beginner",
                expected_sharpe=1.2,
                max_drawdown=0.15,
                win_rate=0.55,
                parameters={"rsi_period": 14, "oversold": 30, "overbought": 70},
                requirements=["RSI indicator", "Price data"]
            ),
            'bollinger_bands_breakout': StrategyMetadata(
                name="Bollinger Bands Breakout",
                description="Trade breakouts above/below Bollinger Bands",
                strategy_type=StrategyType.BREAKOUT,
                timeframes=[TimeFrame.MINUTE_15, TimeFrame.HOUR_1, TimeFrame.HOUR_4],
                assets=["forex", "stocks", "crypto"],
                complexity="intermediate",
                expected_sharpe=1.0,
                max_drawdown=0.20,
                win_rate=0.45,
                parameters={"period": 20, "std_dev": 2.0},
                requirements=["Bollinger Bands", "Volume data"]
            ),
            'macd_momentum': StrategyMetadata(
                name="MACD Momentum",
                description="Trade MACD signal line crossovers",
                strategy_type=StrategyType.MOMENTUM,
                timeframes=[TimeFrame.HOUR_1, TimeFrame.HOUR_4, TimeFrame.DAILY],
                assets=["forex", "stocks", "crypto"],
                complexity="beginner",
                expected_sharpe=1.1,
                max_drawdown=0.18,
                win_rate=0.50,
                parameters={"fast_period": 12, "slow_period": 26, "signal_period": 9},
                requirements=["MACD indicator", "Price data"]
            ),
            'dual_thrust': StrategyMetadata(
                name="Dual Thrust",
                description="Intraday breakout strategy based on previous day's range",
                strategy_type=StrategyType.BREAKOUT,
                timeframes=[TimeFrame.MINUTE_5, TimeFrame.MINUTE_15, TimeFrame.MINUTE_30],
                assets=["forex", "futures", "stocks"],
                complexity="advanced",
                expected_sharpe=1.5,
                max_drawdown=0.12,
                win_rate=0.48,
                parameters={"k1": 0.5, "k2": 0.5, "lookback": 4},
                requirements=["OHLC data", "Intraday data"]
            ),
            'pairs_trading': StrategyMetadata(
                name="Pairs Trading",
                description="Statistical arbitrage between correlated assets",
                strategy_type=StrategyType.ARBITRAGE,
                timeframes=[TimeFrame.HOUR_1, TimeFrame.HOUR_4, TimeFrame.DAILY],
                assets=["stocks", "etfs"],
                complexity="advanced",
                expected_sharpe=1.8,
                max_drawdown=0.10,
                win_rate=0.60,
                parameters={"lookback": 60, "entry_threshold": 2.0, "exit_threshold": 0.5},
                requirements=["Correlated assets", "Statistical analysis"]
            ),
            'london_breakout': StrategyMetadata(
                name="London Breakout",
                description="Trade breakouts from London session range",
                strategy_type=StrategyType.BREAKOUT,
                timeframes=[TimeFrame.MINUTE_5, TimeFrame.MINUTE_15],
                assets=["forex"],
                complexity="intermediate",
                expected_sharpe=1.3,
                max_drawdown=0.16,
                win_rate=0.52,
                parameters={"breakout_period": 30, "min_range": 0.001},
                requirements=["Intraday forex data", "Timezone handling"]
            )
        }
    
    def get_strategy(self, strategy_name: str) -> Optional[BaseStrategy]:
        """Get a strategy by name"""
        return self.strategies.get(strategy_name)
    
    def get_metadata(self, strategy_name: str) -> Optional[StrategyMetadata]:
        """Get strategy metadata"""
        return self.metadata.get(strategy_name)
    
    def list_strategies(self) -> List[str]:
        """List all available strategies"""
        return list(self.strategies.keys())
    
    def get_strategies_by_type(self, strategy_type: StrategyType) -> List[str]:
        """Get strategies by type"""
        return [
            name for name, metadata in self.metadata.items()
            if metadata.strategy_type == strategy_type
        ]
    
    def get_strategies_by_complexity(self, complexity: str) -> List[str]:
        """Get strategies by complexity level"""
        return [
            name for name, metadata in self.metadata.items()
            if metadata.complexity == complexity
        ]
    
    def get_strategies_by_asset(self, asset: str) -> List[str]:
        """Get strategies suitable for specific asset class"""
        return [
            name for name, metadata in self.metadata.items()
            if asset in metadata.assets
        ]
    
    def create_custom_strategy(self, name: str, strategy_class: type, **kwargs) -> BaseStrategy:
        """Create a custom strategy instance"""
        if not issubclass(strategy_class, BaseStrategy):
            raise ValueError("Strategy class must inherit from BaseStrategy")
        
        strategy = strategy_class(**kwargs)
        self.strategies[name] = strategy
        return strategy
    
    def backtest_strategy(self, strategy_name: str, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """Backtest a strategy"""
        strategy = self.get_strategy(strategy_name)
        if not strategy:
            raise ValueError(f"Strategy {strategy_name} not found")
        
        return strategy.backtest(data, **kwargs)
    
    def compare_strategies(self, strategy_names: List[str], data: pd.DataFrame) -> pd.DataFrame:
        """Compare multiple strategies"""
        results = []
        
        for name in strategy_names:
            try:
                result = self.backtest_strategy(name, data)
                result['strategy'] = name
                results.append(result)
            except Exception as e:
                logger.error(f"Error backtesting {name}: {e}")
        
        return pd.DataFrame(results)
    
    def get_strategy_recommendations(self, 
                                   asset_class: str, 
                                   timeframe: TimeFrame, 
                                   complexity: str = None) -> List[Dict[str, Any]]:
        """Get strategy recommendations based on criteria"""
        recommendations = []
        
        for name, metadata in self.metadata.items():
            if (asset_class in metadata.assets and 
                timeframe in metadata.timeframes and
                (complexity is None or metadata.complexity == complexity)):
                
                recommendations.append({
                    'name': name,
                    'metadata': metadata,
                    'score': metadata.expected_sharpe * metadata.win_rate  # Simple scoring
                })
        
        # Sort by score
        recommendations.sort(key=lambda x: x['score'], reverse=True)
        return recommendations

# Example usage and testing
if __name__ == "__main__":
    # Initialize the library
    quant_lib = QuantStrategiesLibrary()
    
    # List available strategies
    print("Available strategies:")
    for strategy in quant_lib.list_strategies():
        metadata = quant_lib.get_metadata(strategy)
        print(f"- {metadata.name} ({metadata.complexity}): {metadata.description}")
    
    # Get recommendations for forex trading
    print("\nRecommendations for forex, 1-hour timeframe:")
    recommendations = quant_lib.get_strategy_recommendations("forex", TimeFrame.HOUR_1)
    for rec in recommendations[:3]:  # Top 3
        print(f"- {rec['metadata'].name} (Score: {rec['score']:.2f})")
    
    # Example backtesting (would need real data)
    print("\nExample strategy parameters:")
    rsi_strategy = quant_lib.get_strategy("rsi_mean_reversion")
    print(f"RSI Strategy parameters: {rsi_strategy.parameters}")