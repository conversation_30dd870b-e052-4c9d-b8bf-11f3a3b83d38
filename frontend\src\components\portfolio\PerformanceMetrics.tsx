import { useState } from 'react';
import { Bar<PERSON>hart3, TrendingUp, Calendar, Info } from 'lucide-react';
import { 
  <PERSON><PERSON><PERSON>, 
  Line, 
  Bar<PERSON>hart,
  Bar,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell
} from 'recharts';

interface PerformanceData {
  monthlyReturns: Array<{
    month: string;
    return: number;
    trades: number;
  }>;
  drawdownData: Array<{
    date: string;
    drawdown: number;
  }>;
  riskMetrics: {
    sharpeRatio: number;
    sortino: number;
    maxDrawdown: number;
    volatility: number;
    var95: number;
    calmarRatio: number;
  };
  strategyBreakdown: Array<{
    name: string;
    return: number;
    trades: number;
    winRate: number;
    color: string;
  }>;
}

interface PerformanceMetricsProps {
  data: PerformanceData;
  timeframe?: '1M' | '3M' | '6M' | '1Y' | 'ALL';
  onTimeframeChange?: (timeframe: string) => void;
}

export function PerformanceMetrics({ 
  data, 
  timeframe = '3M', 
  onTimeframeChange 
}: PerformanceMetricsProps) {
  const [activeChart, setActiveChart] = useState<'returns' | 'drawdown' | 'strategies'>('returns');

  const timeframes = [
    { value: '1M', label: '1 Month' },
    { value: '3M', label: '3 Months' },
    { value: '6M', label: '6 Months' },
    { value: '1Y', label: '1 Year' },
    { value: 'ALL', label: 'All Time' },
  ];

  const MetricCard = ({ title, value, subtitle, tooltip }: any) => (
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-xl font-semibold text-gray-900">{value}</p>
          {subtitle && (
            <p className="text-xs text-gray-500 mt-1">{subtitle}</p>
          )}
        </div>
        {tooltip && (
          <div className="group relative">
            <Info className="w-4 h-4 text-gray-400 cursor-help" />
            <div className="absolute bottom-full right-0 mb-2 w-48 p-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity z-10">
              {tooltip}
            </div>
          </div>
        )}
      </div>
    </div>
  );

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {entry.value.toFixed(2)}%
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <BarChart3 className="w-6 h-6 text-blue-600" />
          <h2 className="text-xl font-semibold text-gray-900">Performance Metrics</h2>
        </div>
        
        {onTimeframeChange && (
          <div className="flex items-center space-x-2">
            <Calendar className="w-4 h-4 text-gray-500" />
            <select
              value={timeframe}
              onChange={(e) => onTimeframeChange(e.target.value)}
              className="text-sm border border-gray-300 rounded-lg px-3 py-1 focus:ring-2 focus:ring-blue-500"
            >
              {timeframes.map((tf) => (
                <option key={tf.value} value={tf.value}>
                  {tf.label}
                </option>
              ))}
            </select>
          </div>
        )}
      </div>

      {/* Risk Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <MetricCard
          title="Sharpe Ratio"
          value={data.riskMetrics.sharpeRatio.toFixed(2)}
          subtitle="Risk-adjusted return"
          tooltip="Measures excess return per unit of risk. Higher is better."
        />
        <MetricCard
          title="Sortino Ratio"
          value={data.riskMetrics.sortino.toFixed(2)}
          subtitle="Downside deviation"
          tooltip="Similar to Sharpe but only considers downside volatility."
        />
        <MetricCard
          title="Max Drawdown"
          value={`${data.riskMetrics.maxDrawdown.toFixed(1)}%`}
          subtitle="Peak to trough"
          tooltip="Largest peak-to-trough decline in portfolio value."
        />
        <MetricCard
          title="Volatility"
          value={`${data.riskMetrics.volatility.toFixed(1)}%`}
          subtitle="Annual std dev"
          tooltip="Standard deviation of returns, annualized."
        />
        <MetricCard
          title="VaR (95%)"
          value={`${data.riskMetrics.var95.toFixed(1)}%`}
          subtitle="Value at Risk"
          tooltip="Maximum expected loss at 95% confidence level."
        />
        <MetricCard
          title="Calmar Ratio"
          value={data.riskMetrics.calmarRatio.toFixed(2)}
          subtitle="Return/drawdown"
          tooltip="Annual return divided by maximum drawdown."
        />
      </div>

      {/* Chart Selection */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center space-x-4 mb-6">
          {[
            { id: 'returns', label: 'Monthly Returns', icon: TrendingUp },
            { id: 'drawdown', label: 'Drawdown', icon: TrendingUp },
            { id: 'strategies', label: 'Strategy Breakdown', icon: BarChart3 },
          ].map((chart) => (
            <button
              key={chart.id}
              onClick={() => setActiveChart(chart.id as any)}
              className={`
                flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-colors
                ${activeChart === chart.id
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }
              `}
            >
              <chart.icon className="w-4 h-4" />
              <span>{chart.label}</span>
            </button>
          ))}
        </div>

        <div className="h-80">
          {activeChart === 'returns' && (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={data.monthlyReturns}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip content={<CustomTooltip />} />
                <Bar 
                  dataKey="return" 
                  fill="#10B981"
                  name="Return"
                />
              </BarChart>
            </ResponsiveContainer>
          )}

          {activeChart === 'drawdown' && (
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={data.drawdownData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip content={<CustomTooltip />} />
                <Line 
                  type="monotone" 
                  dataKey="drawdown" 
                  stroke="#EF4444" 
                  strokeWidth={2}
                  name="Drawdown"
                />
              </LineChart>
            </ResponsiveContainer>
          )}

          {activeChart === 'strategies' && (
            <div className="flex items-center justify-center h-full">
              <div className="w-80 h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={data.strategyBreakdown}
                      cx="50%"
                      cy="50%"
                      outerRadius={100}
                      fill="#8884d8"
                      dataKey="return"
                      label={({ name, return: ret }) => `${name}: ${ret.toFixed(1)}%`}
                    >
                      {data.strategyBreakdown.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Strategy Details Table */}
      {activeChart === 'strategies' && (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Strategy Performance</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Strategy
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Return
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Trades
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Win Rate
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {data.strategyBreakdown.map((strategy, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div 
                          className="w-3 h-3 rounded-full mr-3" 
                          style={{ backgroundColor: strategy.color }}
                        />
                        <span className="text-sm font-medium text-gray-900">
                          {strategy.name}
                        </span>
                      </div>
                    </td>
                    <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${
                      strategy.return >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {strategy.return >= 0 ? '+' : ''}{strategy.return.toFixed(2)}%
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {strategy.trades.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {strategy.winRate.toFixed(1)}%
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}