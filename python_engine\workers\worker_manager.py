"""
Worker Manager - Orchestrates all background workers
Provides graceful startup, shutdown, and monitoring capabilities
"""

import asyncio
import logging
import signal
import sys
from typing import List, Dict, Any
from datetime import datetime

from .file_parser import FileParserWorker
from .backtest_runner import BacktestRunner
from .dgm_monitor import DGMMonitor

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class WorkerManager:
    """Manages all background workers"""
    
    def __init__(self):
        self.workers = [
            FileParserWorker(),
            BacktestRunner(),
            DGMMonitor()
        ]
        self.tasks: List[asyncio.Task] = []
        self.shutdown_event = asyncio.Event()
        self.start_time = datetime.utcnow()
        self.is_running = False
    
    async def start(self):
        """Start all workers"""
        logger.info("Starting worker manager...")
        
        # Set up signal handlers for graceful shutdown
        loop = asyncio.get_event_loop()
        if sys.platform != 'win32':  # Signal handling doesn't work on Windows
            for sig in (signal.SIGTERM, signal.SIGINT):
                loop.add_signal_handler(sig, self._signal_handler)
        
        # Start worker tasks
        for worker in self.workers:
            task = asyncio.create_task(worker.run())
            self.tasks.append(task)
            logger.info(f"Started {worker.__class__.__name__}")
        
        self.is_running = True
        logger.info(f"Worker manager started with {len(self.workers)} workers")
        
        # Wait for shutdown signal
        try:
            await self.shutdown_event.wait()
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
        
        # Graceful shutdown
        await self._shutdown()
    
    def _signal_handler(self):
        """Handle shutdown signals"""
        logger.info("Received shutdown signal")
        self.shutdown_event.set()
    
    async def _shutdown(self):
        """Graceful shutdown of all workers"""
        logger.info("Shutting down workers...")
        self.is_running = False
        
        # Cancel all tasks
        for task in self.tasks:
            task.cancel()
        
        # Wait for tasks to complete with timeout
        if self.tasks:
            try:
                await asyncio.wait_for(
                    asyncio.gather(*self.tasks, return_exceptions=True),
                    timeout=30.0  # 30 second timeout for graceful shutdown
                )
            except asyncio.TimeoutError:
                logger.warning("Some workers did not shut down gracefully")
        
        logger.info("All workers shut down")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current status of all workers"""
        uptime = datetime.utcnow() - self.start_time
        
        worker_stats = {}
        for worker in self.workers:
            worker_name = worker.__class__.__name__
            if hasattr(worker, 'get_worker_stats'):
                worker_stats[worker_name] = worker.get_worker_stats()
            else:
                worker_stats[worker_name] = {
                    'status': 'running' if self.is_running else 'stopped'
                }
        
        return {
            'manager_status': 'running' if self.is_running else 'stopped',
            'uptime_seconds': uptime.total_seconds(),
            'start_time': self.start_time.isoformat(),
            'active_workers': len(self.workers),
            'active_tasks': len([t for t in self.tasks if not t.done()]),
            'worker_stats': worker_stats
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on all workers"""
        health_data = {
            'healthy': True,
            'timestamp': datetime.utcnow().isoformat(),
            'workers': {}
        }
        
        for i, worker in enumerate(self.workers):
            worker_name = worker.__class__.__name__
            task = self.tasks[i] if i < len(self.tasks) else None
            
            if task is None:
                health_data['workers'][worker_name] = {
                    'healthy': False,
                    'reason': 'Task not found'
                }
                health_data['healthy'] = False
            elif task.done():
                exception = task.exception()
                health_data['workers'][worker_name] = {
                    'healthy': False,
                    'reason': f'Task completed with exception: {exception}' if exception else 'Task completed unexpectedly'
                }
                health_data['healthy'] = False
            else:
                health_data['workers'][worker_name] = {
                    'healthy': True,
                    'status': 'running'
                }
        
        return health_data
    
    async def restart_worker(self, worker_class_name: str) -> bool:
        """Restart a specific worker"""
        try:
            # Find the worker and its task
            worker_index = None
            for i, worker in enumerate(self.workers):
                if worker.__class__.__name__ == worker_class_name:
                    worker_index = i
                    break
            
            if worker_index is None:
                logger.error(f"Worker {worker_class_name} not found")
                return False
            
            # Cancel the old task
            if worker_index < len(self.tasks):
                old_task = self.tasks[worker_index]
                old_task.cancel()
                try:
                    await old_task
                except asyncio.CancelledError:
                    pass
            
            # Start a new task
            worker = self.workers[worker_index]
            new_task = asyncio.create_task(worker.run())
            
            if worker_index < len(self.tasks):
                self.tasks[worker_index] = new_task
            else:
                self.tasks.append(new_task)
            
            logger.info(f"Restarted worker {worker_class_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to restart worker {worker_class_name}: {e}")
            return False


async def main():
    """Main entry point"""
    manager = WorkerManager()
    try:
        await manager.start()
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
    except Exception as e:
        logger.error(f"Worker manager error: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())