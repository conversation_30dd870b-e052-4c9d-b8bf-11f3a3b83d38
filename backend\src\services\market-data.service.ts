import { OHLCData, TradingSymbol } from '../../../shared/schemas/trading.schemas';

export class MarketDataService {
  constructor() {}

  async fetchHistoricalData(
    symbols: TradingSymbol[],
    startDate: Date,
    endDate: Date
  ): Promise<OHLCData[]> {
    // This is a placeholder implementation
    // In a real application, this would fetch data from a market data provider
    const data: OHLCData[] = [];
    
    for (const symbol of symbols) {
      // Generate sample data for testing
      const sampleData = this.generateSampleData(symbol, startDate, endDate);
      data.push(...sampleData);
    }
    
    return data.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  }

  async validateSymbol(symbol: string): Promise<boolean> {
    // Validate that the symbol is supported
    const supportedSymbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCAD', 'USDCHF', 'NZDUSD', 'EURGBP'];
    return supportedSymbols.includes(symbol);
  }

  private generateSampleData(symbol: TradingSymbol, startDate: Date, endDate: Date): OHLCData[] {
    const data: OHLCData[] = [];
    const current = new Date(startDate);
    let price = this.getBasePrice(symbol);
    
    while (current <= endDate) {
      // Generate realistic price movement
      const change = (Math.random() - 0.5) * 0.002; // ±0.2% change
      price = price * (1 + change);
      
      const high = price * (1 + Math.random() * 0.001);
      const low = price * (1 - Math.random() * 0.001);
      const open = data.length > 0 ? data[data.length - 1].close : price;
      
      data.push({
        symbol,
        timestamp: new Date(current),
        open,
        high,
        low,
        close: price,
        volume: Math.floor(Math.random() * 1000) + 500,
      });
      
      // Move to next hour
      current.setHours(current.getHours() + 1);
    }
    
    return data;
  }

  private getBasePrice(symbol: TradingSymbol): number {
    const basePrices: Record<TradingSymbol, number> = {
      'EURUSD': 1.1000,
      'GBPUSD': 1.3000,
      'USDJPY': 110.00,
      'AUDUSD': 0.7500,
      'USDCAD': 1.2500,
      'USDCHF': 0.9200,
      'NZDUSD': 0.7000,
      'EURGBP': 0.8500,
    };
    
    return basePrices[symbol] || 1.0000;
  }
}