"""
Strategy Helper API
Provides endpoints for AI trading prompts and quantitative strategies
"""

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, List, Optional, Any
import sys
import os

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from strategy_helpers.ai_trading_prompts import AITradingPromptsLibrary, PromptCategory
from strategy_helpers.quant_strategies import QuantStrategiesLibrary, StrategyType, TimeFrame
from strategy_helpers.strategy_assistant import StrategyAssistant

app = FastAPI(title="Strategy Helper API", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize libraries
ai_prompts = AITradingPromptsLibrary()
quant_strategies = QuantStrategiesLibrary()
strategy_assistant = StrategyAssistant()

# Pydantic models
class UserProfile(BaseModel):
    experience_level: str = "beginner"
    asset_classes: List[str] = ["forex"]
    timeframes: List[str] = ["1h"]
    risk_tolerance: str = "medium"
    capital: float = 10000
    goals: List[str] = ["learning"]

class PromptRequest(BaseModel):
    prompt_id: str
    variables: Dict[str, str]

class StrategyBacktestRequest(BaseModel):
    strategy_name: str
    parameters: Optional[Dict[str, Any]] = None

# API Endpoints

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Strategy Helper API",
        "version": "1.0.0",
        "endpoints": {
            "ai_prompts": "/ai-prompts",
            "quant_strategies": "/quant-strategies", 
            "recommendations": "/recommendations",
            "generate_prompt": "/generate-prompt",
            "learning_path": "/learning-path"
        }
    }

@app.get("/ai-prompts")
async def get_ai_prompts():
    """Get all available AI prompts"""
    prompts = ai_prompts.list_all_prompts()
    return {
        "prompts": [
            {
                "id": p.id,
                "title": p.title,
                "description": p.description,
                "category": p.category.value,
                "variables": p.variables,
                "example_usage": p.example_usage,
                "expected_output": p.expected_output
            }
            for p in prompts
        ]
    }

@app.get("/ai-prompts/categories")
async def get_prompt_categories():
    """Get all prompt categories"""
    return {
        "categories": [
            {
                "value": category.value,
                "name": category.value.replace('_', ' ').title(),
                "count": len(ai_prompts.get_prompts_by_category(category))
            }
            for category in PromptCategory
        ]
    }

@app.get("/ai-prompts/{prompt_id}")
async def get_ai_prompt(prompt_id: str):
    """Get a specific AI prompt"""
    prompt = ai_prompts.get_prompt(prompt_id)
    if not prompt:
        raise HTTPException(status_code=404, detail="Prompt not found")
    
    return {
        "id": prompt.id,
        "title": prompt.title,
        "description": prompt.description,
        "category": prompt.category.value,
        "prompt_template": prompt.prompt_template,
        "variables": prompt.variables,
        "example_usage": prompt.example_usage,
        "expected_output": prompt.expected_output
    }

@app.post("/generate-prompt")
async def generate_prompt(request: PromptRequest):
    """Generate a formatted AI prompt"""
    formatted_prompt = ai_prompts.format_prompt(request.prompt_id, **request.variables)
    if not formatted_prompt:
        raise HTTPException(status_code=400, detail="Invalid prompt ID or missing variables")
    
    return {
        "prompt_id": request.prompt_id,
        "variables": request.variables,
        "generated_prompt": formatted_prompt
    }

@app.get("/quant-strategies")
async def get_quant_strategies():
    """Get all available quantitative strategies"""
    strategies = quant_strategies.list_strategies()
    result = []
    
    for strategy_name in strategies:
        metadata = quant_strategies.get_metadata(strategy_name)
        if metadata:
            result.append({
                "name": strategy_name,
                "display_name": metadata.name,
                "description": metadata.description,
                "type": metadata.strategy_type.value,
                "complexity": metadata.complexity,
                "timeframes": [tf.value for tf in metadata.timeframes],
                "assets": metadata.assets,
                "expected_performance": {
                    "sharpe_ratio": metadata.expected_sharpe,
                    "max_drawdown": metadata.max_drawdown,
                    "win_rate": metadata.win_rate
                },
                "parameters": metadata.parameters,
                "requirements": metadata.requirements
            })
    
    return {"strategies": result}

@app.get("/quant-strategies/{strategy_name}")
async def get_quant_strategy(strategy_name: str):
    """Get details of a specific quantitative strategy"""
    metadata = quant_strategies.get_metadata(strategy_name)
    if not metadata:
        raise HTTPException(status_code=404, detail="Strategy not found")
    
    strategy = quant_strategies.get_strategy(strategy_name)
    
    return {
        "name": strategy_name,
        "display_name": metadata.name,
        "description": metadata.description,
        "type": metadata.strategy_type.value,
        "complexity": metadata.complexity,
        "timeframes": [tf.value for tf in metadata.timeframes],
        "assets": metadata.assets,
        "expected_performance": {
            "sharpe_ratio": metadata.expected_sharpe,
            "max_drawdown": metadata.max_drawdown,
            "win_rate": metadata.win_rate
        },
        "parameters": metadata.parameters,
        "requirements": metadata.requirements,
        "current_parameters": strategy.parameters if strategy else {}
    }

@app.get("/strategy-types")
async def get_strategy_types():
    """Get all strategy types"""
    return {
        "types": [
            {
                "value": stype.value,
                "name": stype.value.replace('_', ' ').title(),
                "strategies": quant_strategies.get_strategies_by_type(stype)
            }
            for stype in StrategyType
        ]
    }

@app.post("/recommendations")
async def get_recommendations(profile: UserProfile):
    """Get personalized strategy recommendations"""
    try:
        recommendations = strategy_assistant.get_comprehensive_recommendations(profile.dict())
        
        return {
            "user_profile": profile.dict(),
            "recommendations": [
                {
                    "name": rec.name,
                    "type": rec.type,
                    "description": rec.description,
                    "complexity": rec.complexity,
                    "expected_performance": rec.expected_performance,
                    "requirements": rec.requirements,
                    "implementation_guide": rec.implementation_guide,
                    "ai_prompts": rec.ai_prompts,
                    "code_example": rec.code_example
                }
                for rec in recommendations
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating recommendations: {str(e)}")

@app.post("/learning-path")
async def get_learning_path(profile: UserProfile):
    """Get personalized learning path"""
    try:
        learning_path = strategy_assistant.get_learning_path(profile.dict())
        
        return {
            "user_profile": profile.dict(),
            "learning_modules": learning_path
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating learning path: {str(e)}")

@app.post("/trading-plan/{strategy_name}")
async def create_trading_plan(strategy_name: str, profile: UserProfile):
    """Create a comprehensive trading plan for a strategy"""
    try:
        trading_plan = strategy_assistant.create_trading_plan(strategy_name, profile.dict())
        
        if "error" in trading_plan:
            raise HTTPException(status_code=404, detail=trading_plan["error"])
        
        return trading_plan
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating trading plan: {str(e)}")

@app.get("/search/prompts")
async def search_prompts(q: str):
    """Search AI prompts"""
    results = ai_prompts.search_prompts(q)
    
    return {
        "query": q,
        "results": [
            {
                "id": p.id,
                "title": p.title,
                "description": p.description,
                "category": p.category.value
            }
            for p in results
        ]
    }

@app.get("/search/strategies")
async def search_strategies(
    asset_class: Optional[str] = None,
    complexity: Optional[str] = None,
    strategy_type: Optional[str] = None
):
    """Search quantitative strategies"""
    strategies = quant_strategies.list_strategies()
    results = []
    
    for strategy_name in strategies:
        metadata = quant_strategies.get_metadata(strategy_name)
        if not metadata:
            continue
        
        # Apply filters
        if asset_class and asset_class not in metadata.assets:
            continue
        if complexity and complexity != metadata.complexity:
            continue
        if strategy_type and strategy_type != metadata.strategy_type.value:
            continue
        
        results.append({
            "name": strategy_name,
            "display_name": metadata.name,
            "description": metadata.description,
            "complexity": metadata.complexity,
            "type": metadata.strategy_type.value,
            "assets": metadata.assets
        })
    
    return {
        "filters": {
            "asset_class": asset_class,
            "complexity": complexity,
            "strategy_type": strategy_type
        },
        "results": results
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "ai_prompts_count": len(ai_prompts.list_all_prompts()),
        "quant_strategies_count": len(quant_strategies.list_strategies()),
        "categories": len(list(PromptCategory)),
        "strategy_types": len(list(StrategyType))
    }

# Error handlers
@app.exception_handler(404)
async def not_found_handler(request, exc):
    return {"error": "Resource not found", "detail": str(exc.detail)}

@app.exception_handler(500)
async def internal_error_handler(request, exc):
    return {"error": "Internal server error", "detail": "An unexpected error occurred"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)