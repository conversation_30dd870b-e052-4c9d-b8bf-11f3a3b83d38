---
description: Repository Information Overview
alwaysApply: true
---

# AI Enhanced Trading Platform Information

## Summary
An AI-enhanced trading platform with a FastAPI backend and React frontend. The platform supports strategy creation, backtesting, and live trading through MetaTrader 5 integration.

## Structure
- **backend/**: FastAPI server, database models, and trading logic
- **frontend/**: React application with Vite build system
- **tests/**: Test files for backend and frontend components

## Language & Runtime
**Language**: Python (Backend), TypeScript/JavaScript (Frontend)
**Version**: Python 3.13.2, Node.js
**Build System**: Vite (Frontend)
**Package Manager**: pip (Backend), npm (Frontend)

## Dependencies
**Backend Dependencies**:
- fastapi: Web framework
- sqlalchemy: ORM for database operations
- pydantic: Data validation
- uvicorn: ASGI server
- psycopg2-binary: PostgreSQL adapter

**Frontend Dependencies**:
- react: UI library
- vite: Build tool
- typescript: Type checking
- axios: HTTP client
- react-router-dom: Routing

## Build & Installation
**Backend Setup**:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
python backend/simple_db_setup.py
python backend/minimal_server.py
```

**Frontend Setup**:
```bash
cd frontend
npm install
npm run dev:mvp
```

## Testing
**Framework**: pytest (Backend), Vitest (Frontend)
**Test Location**: tests/ directory
**Run Command**:
```bash
python -m pytest tests/
```

## Main Components

### Backend API
- **Minimal Server**: `backend/minimal_server.py` provides a simplified API for the MVP
- **Database Models**: SQLAlchemy models for strategies, backtests, and users
- **MT5 Integration**: `MT5Bridge` class for executing trades through MetaTrader 5

### Frontend Application
- **MVP Interface**: Simple UI for strategy creation, backtesting, and trading
- **React Components**: Component-based architecture with TypeScript
- **API Services**: Client-side services for communicating with the backend

### Trading Features
- **Strategy Management**: Create and manage trading strategies
- **Backtesting**: Test strategies against historical data
- **Trading**: Execute trades through MetaTrader 5 integration