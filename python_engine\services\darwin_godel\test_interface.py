from flask import Flask, request, jsonify
from flask_cors import CORS
from strategy_verifier import DarwinGodelVerifier
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from strategy_executor.secure_executor import SecurityError

app = Flask(__name__)
CORS(app)
verifier = DarwinGodelVerifier()

@app.route('/api/verify-strategy', methods=['POST'])
def verify_strategy():
    """Simple endpoint to test your verifier"""
    try:
        data = request.json
        strategy_code = data.get('strategy_code')
        
        if not strategy_code:
            return jsonify({'error': 'No strategy code provided'}), 400
        
        # Run verification
        result = verifier.verify_strategy(strategy_code)
        
        # Optional: Run Monte Carlo
        if data.get('run_monte_carlo', False):
            mc_results = verifier.run_monte_carlo_validation(strategy_code)
            result['monte_carlo'] = mc_results
        
        return jsonify(result)
        
    except SecurityError as e:
        return jsonify({'error': str(e), 'type': 'security'}), 400
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/verify-with-backtest', methods=['POST'])
def verify_with_backtest():
    """Verify strategy with historical backtesting"""
    try:
        data = request.json
        strategy_code = data.get('strategy_code')
        historical_data = data.get('historical_data')
        initial_capital = data.get('initial_capital', 10000)
        
        if not strategy_code:
            return jsonify({'error': 'No strategy code provided'}), 400
        
        if not historical_data:
            return jsonify({'error': 'No historical data provided'}), 400
        
        # Run verification with backtest
        result = verifier.verify_with_backtest(strategy_code, historical_data, initial_capital)
        
        return jsonify(result)
        
    except SecurityError as e:
        return jsonify({'error': str(e), 'type': 'security'}), 400
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/monte-carlo', methods=['POST'])
def monte_carlo_validation():
    """Run Monte Carlo validation on a strategy"""
    try:
        data = request.json
        strategy_code = data.get('strategy_code')
        simulations = data.get('simulations', 100)
        data_variations = data.get('data_variations', 0.02)
        
        if not strategy_code:
            return jsonify({'error': 'No strategy code provided'}), 400
        
        # Run Monte Carlo validation
        result = verifier.run_monte_carlo_validation(
            strategy_code, 
            simulations=simulations,
            data_variations=data_variations
        )
        
        return jsonify(result)
        
    except SecurityError as e:
        return jsonify({'error': str(e), 'type': 'security'}), 400
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/health', methods=['GET'])
def health():
    return jsonify({
        'status': 'healthy', 
        'verifier': 'Darwin Godel Model v1.0',
        'capabilities': [
            'Strategy Security Validation',
            'Risk Assessment',
            'Robustness Analysis',
            'Historical Backtesting',
            'Monte Carlo Simulation'
        ]
    })

@app.route('/', methods=['GET'])
def info():
    return jsonify({
        'name': 'Darwin Godel Strategy Verification Engine',
        'version': '1.0.0',
        'description': 'Advanced strategy verification using formal methods and statistical analysis',
        'endpoints': {
            'verify': 'POST /api/verify-strategy - Basic strategy verification',
            'backtest': 'POST /api/verify-with-backtest - Verification with historical data',
            'monte_carlo': 'POST /api/monte-carlo - Monte Carlo robustness testing',
            'health': 'GET /health - Engine health check'
        },
        'example_request': {
            'strategy_code': '''
def trading_strategy(data, params):
    sma = calculate_sma(data['close'], params['period'])
    current_price = data['close'][-1]
    
    if current_price < sma[-1] * 0.98:
        return {'signal': 'buy', 'confidence': 0.8}
    elif current_price > sma[-1] * 1.02:
        return {'signal': 'sell', 'confidence': 0.8}
    else:
        return {'signal': 'hold', 'confidence': 0.5}
''',
            'run_monte_carlo': False
        }
    })

if __name__ == '__main__':
    print("🚀 Starting Darwin Godel Strategy Verification Engine...")
    print("📊 Available at: http://localhost:5001")
    print("🔍 Health check: http://localhost:5001/health")
    print("📖 API docs: http://localhost:5001")
    app.run(debug=True, port=5001, host='0.0.0.0')