import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler

class MachineLearningStrategy(StrategyBase):
    """Machine Learning strategy using Random Forest for signal generation"""
    
    def __init__(self, symbol="EURUSD", timeframe="H1", mt5_bridge=None, risk_per_trade=0.02):
        super().__init__(
            name=f"ML Random Forest {symbol} {timeframe}",
            symbols=[symbol],
            mt5_bridge=mt5_bridge,
            risk_per_trade=risk_per_trade
        )
        self.model = None
        self.scaler = StandardScaler()
        self.lookback_period = 20
        self.training_bars = 1000
        self.timeframe = timeframe
        self.features = ['rsi', 'macd', 'price_momentum', 'volatility']
    
    def prepare_features(self, data):
        """Prepare feature set for ML model"""
        close_prices = data['close']
        
        # RSI calculation
        delta = close_prices.diff()
        gain = delta.where(delta > 0, 0).rolling(window=14).mean()
        loss = -delta.where(delta < 0, 0).rolling(window=14).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        # MACD calculation
        ema_fast = close_prices.ewm(span=12).mean()
        ema_slow = close_prices.ewm(span=26).mean()
        macd = ema_fast - ema_slow
        
        # Price momentum
        price_momentum = close_prices.pct_change(periods=5)
        
        # Volatility
        volatility = close_prices.rolling(window=20).std()
        
        return {
            'rsi': rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else 50,
            'macd': macd.iloc[-1] if not pd.isna(macd.iloc[-1]) else 0,
            'price_momentum': price_momentum.iloc[-1] if not pd.isna(price_momentum.iloc[-1]) else 0,
            'volatility': volatility.iloc[-1] if not pd.isna(volatility.iloc[-1]) else 0
        }
    
    def train_model(self, historical_data):
        """Train the Random Forest model"""
        if len(historical_data) < self.training_bars:
            return False
        
        features_list = []
        labels = []
        
        for i in range(self.lookback_period, len(historical_data) - 1):
            window_data = historical_data.iloc[i-self.lookback_period:i]
            features = self.prepare_features(window_data)
            features_list.append(list(features.values()))
            
            # Create label (1 for buy, 0 for sell) based on future price movement
            current_price = historical_data['close'].iloc[i]
            future_price = historical_data['close'].iloc[i + 1]
            labels.append(1 if future_price > current_price else 0)
        
        if len(features_list) == 0:
            return False
        
        X = np.array(features_list)
        y = np.array(labels)
        
        # Scale features
        X_scaled = self.scaler.fit_transform(X)
        
        # Train model
        self.model = RandomForestClassifier(n_estimators=100, random_state=42)
        self.model.fit(X_scaled, y)
        
        return True
    
    def generate_signal(self, symbol, data):
        """Generate ML-based trading signal"""
        if self.model is None:
            return {
                "signal": "hold",
                "confidence": 0.0,
                "reason": "Model not trained"
            }
        
        if len(data['close']) < self.lookback_period:
            return {
                "signal": "hold",
                "confidence": 0.0,
                "reason": "Insufficient data for feature calculation"
            }
        
        features = self.prepare_features(data)
        X = self.scaler.transform([list(features.values())])
        
        prediction = self.model.predict(X)[0]
        probabilities = self.model.predict_proba(X)[0]
        confidence = max(probabilities)
        
        current_price = data['close'].iloc[-1]
        
        if prediction == 1 and confidence > 0.6:
            return {
                "signal": "buy",
                "confidence": confidence,
                "reason": f"ML model predicts upward movement (confidence: {confidence:.2f})",
                "stop_loss": current_price * 0.99,
                "take_profit": current_price * 1.02
            }
        elif prediction == 0 and confidence > 0.6:
            return {
                "signal": "sell",
                "confidence": confidence,
                "reason": f"ML model predicts downward movement (confidence: {confidence:.2f})",
                "stop_loss": current_price * 1.01,
                "take_profit": current_price * 0.98
            }
        else:
            return {
                "signal": "hold",
                "confidence": confidence,
                "reason": f"ML model uncertain (confidence: {confidence:.2f})"
            }
