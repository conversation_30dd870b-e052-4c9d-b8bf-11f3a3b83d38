import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import axios from 'axios';
import { PythonEngineService, PythonEngineServiceDependencies } from './python-engine.service';
import { createMockLogger } from '@/shared/test-utils';
import {
  TradingEngineRequest,
  PythonBacktestRequest,
  PythonChatRequest,
  PythonDataProcessingRequest,
} from '@/shared/schemas';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('PythonEngineService', () => {
  let service: PythonEngineService;
  let mockDependencies: PythonEngineServiceDependencies;
  let mockAxiosInstance: jest.Mocked<any>;

  beforeEach(() => {
    // Setup mock axios instance
    mockAxiosInstance = {
      post: jest.fn(),
      get: jest.fn(),
      interceptors: {
        request: { use: jest.fn() },
        response: { use: jest.fn() },
      },
    };
    
    mockedAxios.create.mockReturnValue(mockAxiosInstance);

    // Setup mock dependencies
    mockDependencies = {
      logger: createMockLogger(),
      config: {
        baseUrl: 'http://localhost:8000',
        timeout: 30000,
        retryAttempts: 3,
        retryDelay: 1000,
        apiKey: 'test-api-key',
      },
    };

    service = new PythonEngineService(mockDependencies);
  });

  afterEach(async () => {
    await service.stop();
    jest.clearAllMocks();
  });

  describe('Constructor', () => {
    it('should initialize with correct configuration', () => {
      expect(mockedAxios.create).toHaveBeenCalledWith({
        baseURL: 'http://localhost:8000',
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-api-key',
        },
      });
    });

    it('should setup request and response interceptors', () => {
      expect(mockAxiosInstance.interceptors.request.use).toHaveBeenCalled();
      expect(mockAxiosInstance.interceptors.response.use).toHaveBeenCalled();
    });
  });

  describe('sendTradingCommand', () => {
    it('should send trading command successfully', async () => {
      const request: TradingEngineRequest = {
        action: 'submit_order',
        payload: {
          symbol: 'EURUSD',
          volume: 0.01,
          order_type: 'buy',
          price: 1.1000,
        },
        timestamp: new Date(),
        request_id: 'test-request-id',
      };

      const mockResponse = {
        success: true,
        data: { order_id: 12345 },
        timestamp: new Date(),
        request_id: 'test-request-id',
      };

      mockAxiosInstance.post.mockResolvedValue({ data: mockResponse });

      const result = await service.sendTradingCommand(request);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockResponse);
      expect(mockAxiosInstance.post).toHaveBeenCalledWith(
        '/api/trading/command',
        request,
        { headers: { 'X-Request-ID': request.request_id } }
      );
    });

    it('should handle trading command errors', async () => {
      const request: TradingEngineRequest = {
        action: 'submit_order',
        payload: {},
        timestamp: new Date(),
        request_id: 'test-request-id',
      };

      mockAxiosInstance.post.mockRejectedValue(new Error('Network error'));

      const result = await service.sendTradingCommand(request);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('TRADING_ENGINE_ERROR');
      expect(result.error?.message).toBe('Failed to execute trading command');
    });
  });

  describe('submitBacktest', () => {
    it('should submit backtest successfully', async () => {
      const request: PythonBacktestRequest = {
        request_id: 'test-request-id',
        config: {
          name: 'Test Backtest',
          symbols: ['EURUSD'],
          start_date: new Date('2024-01-01'),
          end_date: new Date('2024-12-31'),
          initial_balance: 10000,
          strategy: {
            name: 'test_strategy',
            parameters: { period: 20 },
          },
          risk_management: {
            max_risk_per_trade: 0.02,
            max_concurrent_trades: 5,
          },
        },
        data: {
          market_data: [{
            symbol: 'EURUSD',
            timestamp: new Date(),
            open: 1.1000,
            high: 1.1010,
            low: 1.0990,
            close: 1.1005,
            volume: 1000,
          }],
        },
      };

      const mockResponse = {
        request_id: 'test-request-id',
        success: true,
        results: {
          backtest_id: 'backtest-123',
          metrics: {
            total_trades: 10,
            win_rate: 0.7,
            total_pnl: 150.0,
          },
        },
        execution_time_seconds: 5.2,
      };

      mockAxiosInstance.post.mockResolvedValue({ data: mockResponse });

      const result = await service.submitBacktest(request);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockResponse);
      expect(mockAxiosInstance.post).toHaveBeenCalledWith(
        '/api/backtest/submit',
        request,
        {
          headers: { 'X-Request-ID': request.request_id },
          timeout: 300000,
        }
      );
    });

    it('should handle backtest submission errors', async () => {
      const request: PythonBacktestRequest = {
        request_id: 'test-request-id',
        config: {
          name: 'Test Backtest',
          symbols: ['EURUSD'],
          start_date: new Date('2024-01-01'),
          end_date: new Date('2024-12-31'),
          strategy: { name: 'test', parameters: {} },
          risk_management: {
            max_risk_per_trade: 0.02,
            max_concurrent_trades: 5,
          },
        },
        data: { market_data: [] },
      };

      mockAxiosInstance.post.mockRejectedValue(new Error('Processing error'));

      const result = await service.submitBacktest(request);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('BACKTEST_SUBMISSION_ERROR');
    });
  });

  describe('sendChatQuery', () => {
    it('should send chat query successfully', async () => {
      const request: PythonChatRequest = {
        request_id: 'test-request-id',
        query: 'What is the current market trend?',
        session_id: 'session-123',
        user_context: {
          user_id: 'user-456',
        },
        rag_config: {
          use_knowledge_graph: true,
          use_market_data: true,
          max_context_length: 4000,
        },
      };

      const mockResponse = {
        request_id: 'test-request-id',
        success: true,
        response: {
          message: 'The market is showing bullish trends',
          type: 'analysis',
          confidence: 0.85,
          timestamp: new Date(),
        },
        processing_time_ms: 1500,
      };

      mockAxiosInstance.post.mockResolvedValue({ data: mockResponse });

      const result = await service.sendChatQuery(request);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockResponse);
      expect(mockAxiosInstance.post).toHaveBeenCalledWith(
        '/api/chat/query',
        request,
        {
          headers: { 'X-Request-ID': request.request_id },
          timeout: 60000,
        }
      );
    });

    it('should handle chat query errors', async () => {
      const request: PythonChatRequest = {
        request_id: 'test-request-id',
        query: 'Test query',
        session_id: 'session-123',
        user_context: { user_id: 'user-456' },
      };

      mockAxiosInstance.post.mockRejectedValue(new Error('AI service unavailable'));

      const result = await service.sendChatQuery(request);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('CHAT_ENGINE_ERROR');
    });
  });

  describe('processData', () => {
    it('should process data successfully', async () => {
      const request: PythonDataProcessingRequest = {
        request_id: 'test-request-id',
        upload_id: 'upload-123',
        file_path: '/path/to/file.csv',
        column_mapping: {
          'Time': 'Time',
          'Open': 'Open',
          'High': 'High',
          'Low': 'Low',
          'Close': 'Close',
        },
        symbol: 'EURUSD',
        timeframe: 'H1',
        validation_config: {
          require_ohlc: true,
          min_data_points: 100,
          max_gap_minutes: 60,
          price_validation: {
            min_price: 0.0001,
            max_price: 10,
            max_price_change: 0.2,
          },
        },
      };

      const mockResponse = {
        request_id: 'test-request-id',
        success: true,
        data: {
          symbol: 'EURUSD',
          timeframe: 'H1',
          data: [],
          metadata: {
            total_records: 1000,
            date_range: {
              start: new Date('2024-01-01'),
              end: new Date('2024-12-31'),
            },
            completeness: 0.95,
          },
        },
        processing_stats: {
          rows_processed: 1000,
          rows_valid: 950,
          rows_invalid: 50,
          processing_time_ms: 2500,
        },
      };

      mockAxiosInstance.post.mockResolvedValue({ data: mockResponse });

      const result = await service.processData(request);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockResponse);
      expect(mockAxiosInstance.post).toHaveBeenCalledWith(
        '/api/data/process',
        request,
        {
          headers: { 'X-Request-ID': request.request_id },
          timeout: 180000,
        }
      );
    });
  });

  describe('checkHealth', () => {
    it('should check health successfully', async () => {
      const mockHealthResponse = {
        status: 'healthy',
        version: '1.0.0',
        uptime: 3600,
      };

      mockAxiosInstance.get.mockResolvedValue({
        status: 200,
        data: mockHealthResponse,
      });

      const result = await service.checkHealth();

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockHealthResponse);
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/health', {
        timeout: 5000,
      });

      const healthStatus = service.getHealthStatus();
      expect(healthStatus.healthy).toBe(true);
      expect(healthStatus.lastCheck).toBeInstanceOf(Date);
    });

    it('should handle health check failures', async () => {
      mockAxiosInstance.get.mockRejectedValue(new Error('Service unavailable'));

      const result = await service.checkHealth();

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('HEALTH_CHECK_FAILED');

      const healthStatus = service.getHealthStatus();
      expect(healthStatus.healthy).toBe(false);
    });
  });

  describe('getBacktestStatus', () => {
    it('should get backtest status successfully', async () => {
      const mockStatus = {
        status: 'running',
        progress: 45,
      };

      mockAxiosInstance.get.mockResolvedValue({ data: mockStatus });

      const result = await service.getBacktestStatus('backtest-123');

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockStatus);
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/api/backtest/backtest-123/status');
    });
  });

  describe('Event Emission', () => {
    it('should emit events on successful operations', async () => {
      const tradingCommandListener = jest.fn();
      const backtestListener = jest.fn();
      const chatListener = jest.fn();

      service.on('trading_command_sent', tradingCommandListener);
      service.on('backtest_submitted', backtestListener);
      service.on('chat_query_sent', chatListener);

      // Mock successful responses
      mockAxiosInstance.post.mockResolvedValue({
        data: {
          success: true,
          request_id: 'test-id',
          timestamp: new Date(),
        },
      });

      // Test trading command event
      await service.sendTradingCommand({
        action: 'get_account',
        request_id: 'test-id',
        timestamp: new Date(),
      });

      expect(tradingCommandListener).toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should transform axios errors correctly', async () => {
      const axiosError = {
        response: { status: 500, data: 'Internal server error' },
        message: 'Request failed',
      };

      mockAxiosInstance.post.mockRejectedValue(axiosError);

      const result = await service.sendTradingCommand({
        action: 'submit_order',
        request_id: 'test-id',
        timestamp: new Date(),
      });

      expect(result.success).toBe(false);
      expect(result.error?.details).toContain('500');
    });
  });
});