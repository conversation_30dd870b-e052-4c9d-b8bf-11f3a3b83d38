import { z } from 'zod';

// LLM Provider Schema
export const LLMProviderSchema = z.enum([
  'openai',
  'anthropic',
  'google',
  'cohere',
  'huggingface',
  'custom'
]);

// Message Role Schema
export const MessageRoleSchema = z.enum([
  'system',
  'user',
  'assistant',
  'function'
]);

// Chat Message Schema
export const ChatMessageSchema = z.object({
  id: z.string().uuid(),
  role: MessageRoleSchema,
  content: z.string().min(1),
  timestamp: z.date(),
  metadata: z.object({
    model: z.string().optional(),
    provider: LLMProviderSchema.optional(),
    tokens: z.number().nonnegative().optional(),
    cost: z.number().nonnegative().optional(),
    latency: z.number().nonnegative().optional(), // in milliseconds
    temperature: z.number().min(0).max(2).optional(),
    maxTokens: z.number().positive().optional(),
  }).optional(),
});

// Chat Session Schema
export const ChatSessionSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  title: z.string().min(1).max(200),
  messages: z.array(ChatMessageSchema),
  context: z.object({
    strategy: z.string().uuid().optional(),
    backtest: z.string().uuid().optional(),
    dataset: z.string().uuid().optional(),
    symbols: z.array(z.string()).optional(),
  }).optional(),
  settings: z.object({
    primaryProvider: LLMProviderSchema,
    fallbackProvider: LLMProviderSchema.optional(),
    temperature: z.number().min(0).max(2).default(0.7),
    maxTokens: z.number().positive().default(2000),
    systemPrompt: z.string().optional(),
  }),
  isActive: z.boolean(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Chat Request Schema
export const ChatRequestSchema = z.object({
  sessionId: z.string().uuid().optional(),
  message: z.string().min(1),
  context: z.object({
    strategy: z.string().uuid().optional(),
    backtest: z.string().uuid().optional(),
    dataset: z.string().uuid().optional(),
    symbols: z.array(z.string()).optional(),
  }).optional(),
  settings: z.object({
    provider: LLMProviderSchema.optional(),
    temperature: z.number().min(0).max(2).optional(),
    maxTokens: z.number().positive().optional(),
    stream: z.boolean().default(false),
  }).optional(),
});

// Chat Response Schema
export const ChatResponseSchema = z.object({
  sessionId: z.string().uuid(),
  message: ChatMessageSchema,
  usage: z.object({
    promptTokens: z.number().nonnegative(),
    completionTokens: z.number().nonnegative(),
    totalTokens: z.number().nonnegative(),
    cost: z.number().nonnegative().optional(),
  }),
  metadata: z.object({
    provider: LLMProviderSchema,
    model: z.string(),
    latency: z.number().nonnegative(),
    cached: z.boolean().optional(),
  }),
});

// Multi-LLM Configuration Schema
export const MultiLLMConfigSchema = z.object({
  providers: z.array(z.object({
    name: LLMProviderSchema,
    apiKey: z.string().min(1),
    baseUrl: z.string().url().optional(),
    models: z.array(z.string().min(1)),
    priority: z.number().int().min(1).max(10),
    rateLimits: z.object({
      requestsPerMinute: z.number().positive(),
      tokensPerMinute: z.number().positive(),
    }),
    costPerToken: z.object({
      input: z.number().nonnegative(),
      output: z.number().nonnegative(),
    }).optional(),
  })),
  fallbackStrategy: z.enum(['priority', 'round_robin', 'least_cost', 'fastest']),
  retryAttempts: z.number().int().min(0).max(5).default(3),
  timeout: z.number().positive().default(30000), // milliseconds
});

// LLM Usage Statistics Schema
export const LLMUsageStatsSchema = z.object({
  userId: z.string().uuid(),
  provider: LLMProviderSchema,
  model: z.string(),
  period: z.object({
    start: z.date(),
    end: z.date(),
  }),
  usage: z.object({
    totalRequests: z.number().nonnegative(),
    totalTokens: z.number().nonnegative(),
    promptTokens: z.number().nonnegative(),
    completionTokens: z.number().nonnegative(),
    totalCost: z.number().nonnegative(),
    averageLatency: z.number().nonnegative(),
    successRate: z.number().min(0).max(1),
  }),
});

// Function Call Schema (for function calling LLMs)
export const FunctionCallSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1),
  description: z.string().min(1),
  parameters: z.object({
    type: z.literal('object'),
    properties: z.record(z.object({
      type: z.string(),
      description: z.string().optional(),
      enum: z.array(z.string()).optional(),
    })),
    required: z.array(z.string()).optional(),
  }),
});

// Prompt Template Schema
export const PromptTemplateSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  category: z.enum(['trading', 'analysis', 'education', 'general']),
  template: z.string().min(1),
  variables: z.array(z.object({
    name: z.string().min(1),
    type: z.enum(['string', 'number', 'boolean', 'array', 'object']),
    required: z.boolean(),
    description: z.string().optional(),
    defaultValue: z.unknown().optional(),
  })),
  tags: z.array(z.string()).optional(),
  isPublic: z.boolean(),
  createdBy: z.string().uuid(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Types
export type LLMProvider = z.infer<typeof LLMProviderSchema>;
export type MessageRole = z.infer<typeof MessageRoleSchema>;
export type ChatMessage = z.infer<typeof ChatMessageSchema>;
export type ChatSession = z.infer<typeof ChatSessionSchema>;
export type ChatRequest = z.infer<typeof ChatRequestSchema>;
export type ChatResponse = z.infer<typeof ChatResponseSchema>;
export type MultiLLMConfig = z.infer<typeof MultiLLMConfigSchema>;
export type LLMUsageStats = z.infer<typeof LLMUsageStatsSchema>;
export type FunctionCall = z.infer<typeof FunctionCallSchema>;
export type PromptTemplate = z.infer<typeof PromptTemplateSchema>;