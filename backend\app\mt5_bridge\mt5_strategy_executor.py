"""
MT5 Strategy Executor for Ollama-Generated Trading Strategies
Bridges the gap between AI-generated Python strategies and MT5 execution
"""

import asyncio
import logging
import json
import sys
import os
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import traceback
from RestrictedPython import compile_restricted, safe_globals

# Add paths for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../src'))

try:
    # Try importing from the local project structure
    sys.path.append(os.path.join(os.path.dirname(__file__), '../../../src'))
    from trading.mt5_bridge_tdd import MT5Bridge, OrderType
    from trading.secure_mt5_bridge import SecureMT5Bridge, TradeOrder
except ImportError:
    try:
        # Try importing from the backend app structure
        from .mt5_client import MT5Client as MT5Bridge
        OrderType = None
        SecureMT5Bridge = None
        TradeOrder = None
    except ImportError:
        logger = logging.getLogger(__name__)
        logger.warning("Could not import MT5 bridge components - using mock implementations")
        MT5Bridge = None
        SecureMT5Bridge = None
        OrderType = None
        TradeOrder = None

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class StrategyStatus(Enum):
    """Strategy execution status"""
    STOPPED = "stopped"
    RUNNING = "running"
    PAUSED = "paused"
    ERROR = "error"
    COMPLETED = "completed"

@dataclass
class StrategyExecution:
    """Strategy execution state"""
    strategy_id: str
    strategy_name: str
    strategy_code: str
    status: StrategyStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    stopped_at: Optional[datetime] = None
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    total_profit: float = 0.0
    max_drawdown: float = 0.0
    current_positions: Optional[List[Dict[str, Any]]] = None
    error_message: Optional[str] = None
    
    def __post_init__(self):
        if self.current_positions is None:
            self.current_positions = []

class MT5StrategyExecutor:
    """
    Executes AI-generated Python trading strategies on MT5
    """
    
    def __init__(self, mt5_credentials: Optional[Dict[str, str]] = None, use_secure_bridge: bool = True):
        """
        Initialize the MT5 Strategy Executor
        
        Args:
            mt5_credentials: MT5 login credentials {"login": "", "password": "", "server": ""}
            use_secure_bridge: Whether to use the secure MT5 bridge
        """
        self.mt5_credentials = mt5_credentials
        self.use_secure_bridge = use_secure_bridge
        self.mt5_bridge = None
        self.active_strategies: Dict[str, StrategyExecution] = {}
        self.strategy_tasks: Dict[str, asyncio.Task] = {}
        
        # Initialize MT5 bridge
        self._initialize_mt5_bridge()
        
    def _initialize_mt5_bridge(self):
        """Initialize the MT5 bridge connection"""
        try:
            if self.use_secure_bridge and SecureMT5Bridge:
                self.mt5_bridge = SecureMT5Bridge()
                logger.info("Initialized Secure MT5 Bridge")
            elif MT5Bridge:
                # Initialize with appropriate parameters based on available bridge
                if hasattr(MT5Bridge, '__init__') and 'offline_mode' in MT5Bridge.__init__.__code__.co_varnames:
                    self.mt5_bridge = MT5Bridge(offline_mode=not bool(self.mt5_credentials))
                else:
                    self.mt5_bridge = MT5Bridge()
                logger.info("Initialized Standard MT5 Bridge")
            else:
                logger.error("No MT5 bridge implementation available")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize MT5 bridge: {str(e)}")
            return False
            
    async def deploy_strategy(self, 
                            strategy_name: str,
                            strategy_code: str,
                            strategy_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Deploy and start executing a trading strategy
        
        Args:
            strategy_name: Name of the strategy
            strategy_code: Python code of the strategy
            strategy_config: Configuration parameters for the strategy
            
        Returns:
            Dict with deployment results
        """
        try:
            # Generate unique strategy ID
            strategy_id = f"strategy_{int(datetime.now().timestamp() * 1000)}"
            
            # Create strategy execution object
            strategy_execution = StrategyExecution(
                strategy_id=strategy_id,
                strategy_name=strategy_name,
                strategy_code=strategy_code,
                status=StrategyStatus.STOPPED,
                created_at=datetime.now()
            )
            
            # Store strategy
            self.active_strategies[strategy_id] = strategy_execution
            
            # Start strategy execution
            success = await self._start_strategy_execution(strategy_id, strategy_config)
            
            if success:
                return {
                    "success": True,
                    "strategy_id": strategy_id,
                    "message": f"Strategy '{strategy_name}' deployed and started successfully"
                }
            else:
                return {
                    "success": False,
                    "strategy_id": strategy_id,
                    "error": "Failed to start strategy execution"
                }
                
        except Exception as e:
            logger.error(f"Strategy deployment error: {str(e)}")
            return {
                "success": False,
                "error": f"Deployment failed: {str(e)}"
            }
            
    async def _start_strategy_execution(self, strategy_id: str, config: Optional[Dict[str, Any]] = None) -> bool:
        """
        Start executing a deployed strategy
        
        Args:
            strategy_id: ID of the strategy to start
            config: Strategy configuration parameters
            
        Returns:
            bool: True if started successfully
        """
        try:
            if strategy_id not in self.active_strategies:
                logger.error(f"Strategy {strategy_id} not found")
                return False
                
            strategy = self.active_strategies[strategy_id]
            
            # Update status
            strategy.status = StrategyStatus.RUNNING
            strategy.started_at = datetime.now()
            
            # Create and start strategy execution task
            task = asyncio.create_task(
                self._execute_strategy_loop(strategy_id, config or {})
            )
            self.strategy_tasks[strategy_id] = task
            
            logger.info(f"Started strategy execution: {strategy.strategy_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start strategy {strategy_id}: {str(e)}")
            if strategy_id in self.active_strategies:
                self.active_strategies[strategy_id].status = StrategyStatus.ERROR
                self.active_strategies[strategy_id].error_message = str(e)
            return False
            
    async def _execute_strategy_loop(self, strategy_id: str, config: Dict[str, Any]):
        """
        Main strategy execution loop
        
        Args:
            strategy_id: ID of the strategy to execute
            config: Strategy configuration
        """
        strategy = self.active_strategies[strategy_id]
        
        try:
            # Define safe globals for the execution environment
            strategy_globals = safe_globals.copy()
            strategy_locals = {}

            # Compile the user's code in a restricted environment
            byte_code = compile_restricted(
                strategy.strategy_code,
                filename='<strategy_code>',
                mode='exec'
            )
            
            # Execute the compiled code to define the class
            exec(byte_code, strategy_globals, strategy_locals)
            
            # Find the strategy class
            strategy_class = None
            for name, obj in strategy_locals.items():
                if isinstance(obj, type) and hasattr(obj, 'generate_signals'):
                    strategy_class = obj
                    break
                    
            if not strategy_class:
                raise Exception("No strategy class found in code")
                
            # Initialize strategy instance
            strategy_instance = strategy_class(**config)
            
            logger.info(f"Strategy {strategy.strategy_name} execution loop started")
            
            # Main execution loop
            while strategy.status == StrategyStatus.RUNNING:
                try:
                    # Check MT5 connection
                    if not self.mt5_bridge or not hasattr(self.mt5_bridge, 'connected') or not self.mt5_bridge.connected:
                        await asyncio.sleep(5)
                        continue
                        
                    # Get market data (this would be implemented based on strategy needs)
                    market_data = await self._get_market_data(strategy_instance)
                    
                    if market_data:
                        # Generate trading signals
                        signals = await self._generate_signals(strategy_instance, market_data)
                        
                        # Execute trades based on signals
                        if signals:
                            await self._execute_trades(strategy_id, signals)
                            
                    # Update strategy statistics
                    await self._update_strategy_stats(strategy_id)
                    
                    # Wait before next iteration
                    await asyncio.sleep(1)  # 1 second interval
                    
                except Exception as e:
                    logger.error(f"Error in strategy loop: {str(e)}")
                    await asyncio.sleep(5)
                    
        except Exception as e:
            logger.error(f"Strategy execution failed: {str(e)}")
            strategy.status = StrategyStatus.ERROR
            strategy.error_message = str(e)
            strategy.stopped_at = datetime.now()
            
    async def _get_market_data(self, strategy_instance) -> Optional[Dict[str, Any]]:
        """
        Get market data for strategy analysis
        
        Args:
            strategy_instance: The strategy instance
            
        Returns:
            Market data dictionary or None
        """
        try:
            # This would fetch real market data from MT5
            # For now, return mock data
            return {
                "symbol": "EURUSD",
                "bid": 1.0850,
                "ask": 1.0852,
                "time": datetime.now(),
                "close": [1.0850, 1.0848, 1.0851, 1.0849, 1.0850],  # Last 5 closes
                "high": [1.0855, 1.0853, 1.0856, 1.0854, 1.0855],
                "low": [1.0845, 1.0843, 1.0846, 1.0844, 1.0845],
                "volume": [100, 150, 120, 180, 110]
            }
            
        except Exception as e:
            logger.error(f"Failed to get market data: {str(e)}")
            return None
            
    async def _generate_signals(self, strategy_instance, market_data) -> Optional[Dict[str, Any]]:
        """
        Generate trading signals using the strategy
        
        Args:
            strategy_instance: The strategy instance
            market_data: Current market data
            
        Returns:
            Trading signals or None
        """
        try:
            # Call the strategy's signal generation method
            if hasattr(strategy_instance, 'generate_signals'):
                signals = strategy_instance.generate_signals(market_data)
                return signals
            else:
                logger.warning("Strategy has no generate_signals method")
                return None
                
        except Exception as e:
            logger.error(f"Signal generation error: {str(e)}")
            return None
            
    async def _execute_trades(self, strategy_id: str, signals: Dict[str, Any]):
        """
        Execute trades based on generated signals
        
        Args:
            strategy_id: ID of the strategy
            signals: Trading signals
        """
        try:
            strategy = self.active_strategies[strategy_id]
            
            # Process buy signals
            if signals.get('buy_signal'):
                await self._place_order(
                    strategy_id=strategy_id,
                    symbol=signals.get('symbol', 'EURUSD'),
                    order_type='BUY',
                    volume=signals.get('volume', 0.01),
                    stop_loss=signals.get('stop_loss'),
                    take_profit=signals.get('take_profit')
                )
                
            # Process sell signals
            if signals.get('sell_signal'):
                await self._place_order(
                    strategy_id=strategy_id,
                    symbol=signals.get('symbol', 'EURUSD'),
                    order_type='SELL',
                    volume=signals.get('volume', 0.01),
                    stop_loss=signals.get('stop_loss'),
                    take_profit=signals.get('take_profit')
                )
                
        except Exception as e:
            logger.error(f"Trade execution error: {str(e)}")
            
    async def _place_order(self, 
                          strategy_id: str,
                          symbol: str,
                          order_type: str,
                          volume: float,
                          stop_loss: Optional[float] = None,
                          take_profit: Optional[float] = None) -> bool:
        """
        Place an order through the MT5 bridge
        
        Args:
            strategy_id: ID of the strategy placing the order
            symbol: Trading symbol
            order_type: Order type (BUY/SELL)
            volume: Order volume
            stop_loss: Stop loss price
            take_profit: Take profit price
            
        Returns:
            bool: True if order placed successfully
        """
        try:
            if not self.mt5_bridge:
                logger.error("MT5 bridge not available")
                return False
                
            # Place order through MT5 bridge
            result = self.mt5_bridge.place_order(
                symbol=symbol,
                order_type=order_type,
                volume=volume,
                stop_loss=stop_loss,
                take_profit=take_profit
            )
            
            if result and result.get('success'):
                order_id = result.get('order_id', 'unknown')
                strategy = self.active_strategies[strategy_id]
                strategy.total_trades += 1
                
                logger.info(f"Order placed successfully: {order_id} for strategy {strategy.strategy_name}")
                return True
            else:
                logger.error(f"Failed to place order: {result}")
                return False
                
        except Exception as e:
            logger.error(f"Order placement error: {str(e)}")
            return False
            
    async def _update_strategy_stats(self, strategy_id: str):
        """
        Update strategy performance statistics
        
        Args:
            strategy_id: ID of the strategy to update
        """
        try:
            strategy = self.active_strategies[strategy_id]
            
            # Get current positions from MT5
            if self.mt5_bridge:
                positions = self.mt5_bridge.get_positions()
                strategy.current_positions = positions or []
                
                # Calculate P&L and other statistics
                total_profit = 0.0
                for position in strategy.current_positions:
                    total_profit += position.get('profit', 0.0)
                    
                strategy.total_profit = total_profit
                
        except Exception as e:
            logger.error(f"Failed to update strategy stats: {str(e)}")
            
    async def stop_strategy(self, strategy_id: str) -> bool:
        """
        Stop a running strategy
        
        Args:
            strategy_id: ID of the strategy to stop
            
        Returns:
            bool: True if stopped successfully
        """
        try:
            if strategy_id not in self.active_strategies:
                return False
                
            strategy = self.active_strategies[strategy_id]
            strategy.status = StrategyStatus.STOPPED
            strategy.stopped_at = datetime.now()
            
            # Cancel the execution task
            if strategy_id in self.strategy_tasks:
                self.strategy_tasks[strategy_id].cancel()
                del self.strategy_tasks[strategy_id]
                
            logger.info(f"Strategy {strategy.strategy_name} stopped successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop strategy {strategy_id}: {str(e)}")
            return False
            
    def get_strategy_status(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the current status of a strategy
        
        Args:
            strategy_id: ID of the strategy
            
        Returns:
            Strategy status dictionary or None
        """
        if strategy_id not in self.active_strategies:
            return None
            
        strategy = self.active_strategies[strategy_id]
        return asdict(strategy)
        
    def get_all_strategies(self) -> List[Dict[str, Any]]:
        """
        Get status of all strategies
        
        Returns:
            List of strategy status dictionaries
        """
        return [asdict(strategy) for strategy in self.active_strategies.values()]
        
    async def close_all_positions(self, strategy_id: Optional[str] = None) -> bool:
        """
        Close all positions for a specific strategy or all strategies
        
        Args:
            strategy_id: Optional strategy ID. If None, closes all positions
            
        Returns:
            bool: True if positions closed successfully
        """
        try:
            if not self.mt5_bridge:
                return False
                
            # Get current positions
            positions = self.mt5_bridge.get_positions()
            
            for position in positions:
                # Close position logic here
                self.mt5_bridge.close_position(position['id'])
                
            return True
            
        except Exception as e:
            logger.error(f"Failed to close positions: {str(e)}")
            return False