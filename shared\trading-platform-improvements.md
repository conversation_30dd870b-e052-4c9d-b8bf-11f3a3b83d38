# AI-Enhanced Trading Platform - TDD Improvements & Code Snippets

## 🚨 Critical Security Improvements

### 1. **Secure Strategy Execution Environment**

**Problem**: Based on your Darwin project, secure code execution is critical.

```python
# services/strategy_executor/__tests__/test_secure_executor.py
import pytest
from unittest.mock import Mock, patch
from services.strategy_executor.secure_executor import SecureStrategyExecutor
from RestrictedPython import compile_restricted

class TestSecureStrategyExecutor:
    def setup_method(self):
        self.executor = SecureStrategyExecutor()
        self.safe_strategy = """
def trading_strategy(data, params):
    sma = calculate_sma(data['close'], params['sma_period'])
    return {'signal': 'buy' if data['close'][-1] > sma[-1] else 'sell'}
"""
        self.malicious_strategy = """
import os
os.system('rm -rf /')
"""

    def test_executes_safe_strategy_successfully(self):
        """Test that valid strategy code executes correctly"""
        # Arrange
        data = {'close': [100, 102, 104, 103, 105]}
        params = {'sma_period': 3}
        
        # Act
        result = self.executor.execute(self.safe_strategy, data, params)
        
        # Assert
        assert result['signal'] in ['buy', 'sell']
        assert 'error' not in result

    def test_blocks_malicious_code_execution(self):
        """Test that malicious code is blocked"""
        # Arrange
        data = {'close': [100, 102, 104]}
        params = {}
        
        # Act & Assert
        with pytest.raises(SecurityError) as exc_info:
            self.executor.execute(self.malicious_strategy, data, params)
        
        assert "Restricted code execution" in str(exc_info.value)

    def test_validates_strategy_function_exists(self):
        """Test that strategy must define trading_strategy function"""
        # Arrange
        invalid_strategy = "x = 1"
        
        # Act & Assert
        with pytest.raises(ValueError) as exc_info:
            self.executor.execute(invalid_strategy, {}, {})
        
        assert "must define 'trading_strategy'" in str(exc_info.value)

# services/strategy_executor/secure_executor.py
from RestrictedPython import compile_restricted, safe_globals
from typing import Dict, Any
import ast

class SecurityError(Exception):
    pass

class SecureStrategyExecutor:
    def __init__(self):
        self.safe_builtins = {
            'len': len,
            'range': range,
            'enumerate': enumerate,
            'zip': zip,
            'min': min,
            'max': max,
            'sum': sum,
            'abs': abs,
            'round': round,
            'list': list,
            'dict': dict,
            'tuple': tuple,
            'set': set,
            'bool': bool,
            'int': int,
            'float': float,
            'str': str,
            'sorted': sorted,
            'reversed': reversed,
            'map': map,
            'filter': filter,
            'all': all,
            'any': any,
        }
        
        self.safe_globals = {
            '__builtins__': self.safe_builtins,
            '__name__': '__main__',
            '__metaclass__': type,
            'calculate_sma': self._calculate_sma,
            'calculate_rsi': self._calculate_rsi,
            'calculate_macd': self._calculate_macd,
            'calculate_bollinger': self._calculate_bollinger,
        }

    def execute(self, strategy_code: str, data: Dict[str, Any], 
                params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Safely execute user-provided strategy code
        
        Args:
            strategy_code: Python code defining trading strategy
            data: Market data dictionary
            params: Strategy parameters
            
        Returns:
            Strategy execution results
            
        Raises:
            SecurityError: If code contains restricted operations
            ValueError: If strategy function not found
        """
        try:
            # Compile with restrictions
            byte_code = compile_restricted(
                strategy_code, 
                filename='<strategy>', 
                mode='exec'
            )
            
            if byte_code.errors:
                raise SecurityError(
                    f"Restricted code execution: {byte_code.errors}"
                )
            
            # Create isolated execution environment
            exec_globals = self.safe_globals.copy()
            
            # Execute strategy code
            exec(byte_code.code, exec_globals)
            
            # Extract strategy function
            strategy_func = exec_globals.get('trading_strategy')
            if not strategy_func:
                raise ValueError(
                    "Strategy must define 'trading_strategy' function"
                )
            
            # Execute strategy with data
            return strategy_func(data, params)
            
        except Exception as e:
            if isinstance(e, (SecurityError, ValueError)):
                raise
            raise SecurityError(f"Strategy execution failed: {str(e)}")

    def _calculate_sma(self, values: list, period: int) -> list:
        """Simple Moving Average calculation"""
        if len(values) < period:
            return []
        
        sma = []
        for i in range(period - 1, len(values)):
            avg = sum(values[i - period + 1:i + 1]) / period
            sma.append(avg)
        
        return sma

    def _calculate_rsi(self, values: list, period: int = 14) -> list:
        """Relative Strength Index calculation"""
        # Implementation of RSI calculation
        pass
```

### 2. **Input Validation with Zod Schemas**

```typescript
// schemas/__tests__/trading.schema.test.ts
import { describe, it, expect } from '@jest/globals';
import { 
  MarketDataSchema, 
  StrategyConfigSchema, 
  BacktestRequestSchema,
  validateBacktestRequest 
} from '../trading.schema';

describe('Trading Schemas', () => {
  describe('MarketDataSchema', () => {
    it('should validate valid market data', () => {
      const validData = {
        symbol: 'AAPL',
        timeframe: '1h',
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        dataType: 'ohlcv'
      };

      const result = MarketDataSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should reject invalid timeframe', () => {
      const invalidData = {
        symbol: 'AAPL',
        timeframe: '3h', // Invalid
        startDate: '2024-01-01',
        endDate: '2024-12-31'
      };

      const result = MarketDataSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      expect(result.error.issues[0].path).toContain('timeframe');
    });

    it('should validate date range', () => {
      const invalidDateRange = {
        symbol: 'AAPL',
        timeframe: '1h',
        startDate: '2024-12-31',
        endDate: '2024-01-01' // End before start
      };

      const result = MarketDataSchema.safeParse(invalidDateRange);
      expect(result.success).toBe(false);
    });
  });

  describe('BacktestRequestSchema', () => {
    it('should validate complete backtest request', () => {
      const validRequest = {
        strategy: {
          name: 'SMA Crossover',
          code: 'def trading_strategy(data, params): pass',
          parameters: {
            shortPeriod: 20,
            longPeriod: 50,
            stopLoss: 0.02,
            takeProfit: 0.05
          }
        },
        marketData: {
          symbol: 'BTCUSD',
          timeframe: '4h',
          startDate: '2024-01-01',
          endDate: '2024-12-31'
        },
        config: {
          initialCapital: 10000,
          positionSize: 0.1,
          commission: 0.001,
          slippage: 0.0005
        }
      };

      const result = BacktestRequestSchema.safeParse(validRequest);
      expect(result.success).toBe(true);
    });
  });
});

// schemas/trading.schema.ts
import { z } from 'zod';

// Market data validation
export const MarketDataSchema = z.object({
  symbol: z.string()
    .min(1)
    .max(20)
    .regex(/^[A-Z0-9\-=]+$/, 'Invalid symbol format'),
  
  timeframe: z.enum(['1m', '5m', '15m', '30m', '1h', '4h', '1d']),
  
  startDate: z.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be YYYY-MM-DD'),
  
  endDate: z.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be YYYY-MM-DD'),
  
  dataType: z.enum(['ohlcv', 'tick', 'orderbook']).optional()
}).refine(data => {
  const start = new Date(data.startDate);
  const end = new Date(data.endDate);
  return start < end;
}, {
  message: 'Start date must be before end date'
});

// Strategy configuration
export const StrategyParametersSchema = z.record(
  z.union([z.number(), z.string(), z.boolean()])
).refine(params => {
  // Validate common parameters
  if ('stopLoss' in params && typeof params.stopLoss === 'number') {
    return params.stopLoss >= 0 && params.stopLoss <= 1;
  }
  return true;
}, {
  message: 'Invalid parameter values'
});

export const StrategyConfigSchema = z.object({
  name: z.string().min(1).max(100),
  
  code: z.string()
    .min(10)
    .max(50000)
    .refine(code => code.includes('trading_strategy'), {
      message: 'Strategy must define trading_strategy function'
    }),
  
  parameters: StrategyParametersSchema
});

// Backtest configuration
export const BacktestConfigSchema = z.object({
  initialCapital: z.number().min(100).max(1000000000),
  
  positionSize: z.number().min(0.01).max(1),
  
  commission: z.number().min(0).max(0.01),
  
  slippage: z.number().min(0).max(0.01),
  
  marginRequirement: z.number().min(0).max(1).optional(),
  
  maxPositions: z.number().int().min(1).max(100).optional()
});

// Complete backtest request
export const BacktestRequestSchema = z.object({
  strategy: StrategyConfigSchema,
  marketData: MarketDataSchema,
  config: BacktestConfigSchema
});

export type BacktestRequest = z.infer<typeof BacktestRequestSchema>;

// Validation middleware
export const validateBacktestRequest = (data: unknown) => {
  const result = BacktestRequestSchema.safeParse(data);
  
  if (!result.success) {
    const errors = result.error.issues.map(issue => ({
      path: issue.path.join('.'),
      message: issue.message
    }));
    
    throw new ValidationError('Invalid backtest request', errors);
  }
  
  return result.data;
};

class ValidationError extends Error {
  constructor(message: string, public errors: Array<{path: string, message: string}>) {
    super(message);
    this.name = 'ValidationError';
  }
}
```

## 🧪 Test-Driven Development Framework

### 3. **Comprehensive Test Structure**

```javascript
// __tests__/setup/test-environment.js
const { TextEncoder, TextDecoder } = require('util');

// Setup test environment
global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder;

// Mock Redis for testing
jest.mock('redis', () => ({
  createClient: jest.fn(() => ({
    connect: jest.fn(),
    disconnect: jest.fn(),
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    expire: jest.fn()
  }))
}));

// Mock database connections
jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn(() => ({
    $connect: jest.fn(),
    $disconnect: jest.fn(),
    $transaction: jest.fn()
  }))
}));

// Global test utilities
global.createMockRequest = (overrides = {}) => ({
  headers: { 'content-type': 'application/json' },
  body: {},
  params: {},
  query: {},
  ...overrides
});

global.createMockResponse = () => {
  const res = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  res.send = jest.fn().mockReturnValue(res);
  return res;
};
```

### 4. **Service Layer Testing**

```typescript
// services/backtesting/__tests__/backtesting.service.test.ts
import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { BacktestingService } from '../backtesting.service';
import { MarketDataService } from '../../market-data/market-data.service';
import { StrategyExecutor } from '../../strategy-executor/strategy-executor.service';
import { MetricsCalculator } from '../metrics-calculator';

describe('BacktestingService', () => {
  let backtestingService: BacktestingService;
  let marketDataService: jest.Mocked<MarketDataService>;
  let strategyExecutor: jest.Mocked<StrategyExecutor>;
  let metricsCalculator: jest.Mocked<MetricsCalculator>;

  beforeEach(() => {
    // Create mocks
    marketDataService = {
      fetchData: jest.fn(),
      validateSymbol: jest.fn()
    } as any;

    strategyExecutor = {
      execute: jest.fn()
    } as any;

    metricsCalculator = {
      calculateMetrics: jest.fn()
    } as any;

    // Initialize service with mocks
    backtestingService = new BacktestingService(
      marketDataService,
      strategyExecutor,
      metricsCalculator
    );
  });

  describe('runBacktest', () => {
    it('should execute complete backtest workflow', async () => {
      // Arrange
      const backtestRequest = {
        strategy: {
          name: 'Test Strategy',
          code: 'def trading_strategy(data, params): return {"signal": "buy"}',
          parameters: { period: 20 }
        },
        marketData: {
          symbol: 'AAPL',
          timeframe: '1h',
          startDate: '2024-01-01',
          endDate: '2024-01-31'
        },
        config: {
          initialCapital: 10000,
          positionSize: 0.1,
          commission: 0.001,
          slippage: 0.0005
        }
      };

      const mockMarketData = {
        symbol: 'AAPL',
        data: [
          { timestamp: '2024-01-01', open: 100, high: 102, low: 99, close: 101, volume: 1000 },
          { timestamp: '2024-01-02', open: 101, high: 103, low: 100, close: 102, volume: 1100 }
        ]
      };

      const mockTrades = [
        { timestamp: '2024-01-01', action: 'buy', price: 101, quantity: 10 },
        { timestamp: '2024-01-02', action: 'sell', price: 102, quantity: 10 }
      ];

      const mockMetrics = {
        totalReturn: 0.0099,
        sharpeRatio: 1.2,
        maxDrawdown: 0.02,
        winRate: 0.6,
        profitFactor: 1.5,
        totalTrades: 2
      };

      marketDataService.fetchData.mockResolvedValue(mockMarketData);
      strategyExecutor.execute.mockResolvedValue(mockTrades);
      metricsCalculator.calculateMetrics.mockResolvedValue(mockMetrics);

      // Act
      const result = await backtestingService.runBacktest(backtestRequest);

      // Assert
      expect(marketDataService.fetchData).toHaveBeenCalledWith({
        symbol: 'AAPL',
        timeframe: '1h',
        startDate: '2024-01-01',
        endDate: '2024-01-31'
      });

      expect(strategyExecutor.execute).toHaveBeenCalledWith(
        backtestRequest.strategy,
        mockMarketData,
        backtestRequest.config
      );

      expect(metricsCalculator.calculateMetrics).toHaveBeenCalledWith(
        mockTrades,
        mockMarketData,
        backtestRequest.config
      );

      expect(result).toEqual({
        status: 'success',
        metrics: mockMetrics,
        trades: mockTrades,
        config: backtestRequest.config
      });
    });

    it('should handle errors gracefully', async () => {
      // Arrange
      const backtestRequest = {
        strategy: { name: 'Test', code: 'invalid', parameters: {} },
        marketData: { symbol: 'INVALID', timeframe: '1h', startDate: '2024-01-01', endDate: '2024-01-31' },
        config: { initialCapital: 10000, positionSize: 0.1, commission: 0.001, slippage: 0.0005 }
      };

      marketDataService.fetchData.mockRejectedValue(new Error('Invalid symbol'));

      // Act & Assert
      await expect(backtestingService.runBacktest(backtestRequest))
        .rejects.toThrow('Backtest failed: Invalid symbol');
    });
  });
});

// services/backtesting/backtesting.service.ts
import { MarketDataService } from '../market-data/market-data.service';
import { StrategyExecutor } from '../strategy-executor/strategy-executor.service';
import { MetricsCalculator } from './metrics-calculator';
import { BacktestRequest, BacktestResult } from '../../types/trading.types';
import { Logger } from '../../utils/logger';

export class BacktestingService {
  private logger: Logger;

  constructor(
    private marketDataService: MarketDataService,
    private strategyExecutor: StrategyExecutor,
    private metricsCalculator: MetricsCalculator
  ) {
    this.logger = new Logger('BacktestingService');
  }

  async runBacktest(request: BacktestRequest): Promise<BacktestResult> {
    try {
      this.logger.info('Starting backtest', { strategy: request.strategy.name });

      // Step 1: Fetch market data
      const marketData = await this.marketDataService.fetchData(request.marketData);
      this.logger.debug('Market data fetched', { 
        symbol: request.marketData.symbol, 
        dataPoints: marketData.data.length 
      });

      // Step 2: Execute strategy
      const trades = await this.strategyExecutor.execute(
        request.strategy,
        marketData,
        request.config
      );
      this.logger.debug('Strategy executed', { tradeCount: trades.length });

      // Step 3: Calculate metrics
      const metrics = await this.metricsCalculator.calculateMetrics(
        trades,
        marketData,
        request.config
      );
      this.logger.info('Backtest completed', { metrics });

      return {
        status: 'success',
        metrics,
        trades,
        config: request.config
      };

    } catch (error) {
      this.logger.error('Backtest failed', error);
      throw new Error(`Backtest failed: ${error.message}`);
    }
  }
}
```

## 🔄 Performance & Caching

### 5. **Redis Caching Implementation**

```typescript
// services/cache/__tests__/cache.service.test.ts
import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { CacheService } from '../cache.service';
import { createClient } from 'redis';

jest.mock('redis');

describe('CacheService', () => {
  let cacheService: CacheService;
  let mockRedisClient: any;

  beforeEach(() => {
    mockRedisClient = {
      connect: jest.fn(),
      disconnect: jest.fn(),
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
      expire: jest.fn(),
      exists: jest.fn()
    };

    (createClient as jest.Mock).mockReturnValue(mockRedisClient);
    cacheService = new CacheService();
  });

  describe('market data caching', () => {
    it('should cache market data with TTL', async () => {
      // Arrange
      const key = 'market:AAPL:1h:2024-01-01:2024-01-31';
      const data = { symbol: 'AAPL', data: [] };
      const ttl = 3600; // 1 hour

      mockRedisClient.set.mockResolvedValue('OK');

      // Act
      await cacheService.setMarketData(key, data, ttl);

      // Assert
      expect(mockRedisClient.set).toHaveBeenCalledWith(
        key,
        JSON.stringify(data),
        { EX: ttl }
      );
    });

    it('should retrieve cached market data', async () => {
      // Arrange
      const key = 'market:AAPL:1h:2024-01-01:2024-01-31';
      const cachedData = { symbol: 'AAPL', data: [] };
      
      mockRedisClient.get.mockResolvedValue(JSON.stringify(cachedData));

      // Act
      const result = await cacheService.getMarketData(key);

      // Assert
      expect(result).toEqual(cachedData);
    });

    it('should return null for cache miss', async () => {
      // Arrange
      mockRedisClient.get.mockResolvedValue(null);

      // Act
      const result = await cacheService.getMarketData('nonexistent');

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('cache invalidation', () => {
    it('should invalidate specific cache entries', async () => {
      // Arrange
      const pattern = 'market:AAPL:*';
      mockRedisClient.keys = jest.fn().mockResolvedValue([
        'market:AAPL:1h:2024-01-01:2024-01-31',
        'market:AAPL:1d:2024-01-01:2024-01-31'
      ]);
      mockRedisClient.del.mockResolvedValue(2);

      // Act
      const deletedCount = await cacheService.invalidatePattern(pattern);

      // Assert
      expect(deletedCount).toBe(2);
      expect(mockRedisClient.del).toHaveBeenCalledWith([
        'market:AAPL:1h:2024-01-01:2024-01-31',
        'market:AAPL:1d:2024-01-01:2024-01-31'
      ]);
    });
  });
});

// services/cache/cache.service.ts
import { createClient, RedisClientType } from 'redis';
import { Logger } from '../../utils/logger';

export class CacheService {
  private client: RedisClientType;
  private logger: Logger;
  private connected: boolean = false;

  constructor(private readonly config = {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    ttl: {
      marketData: 3600,      // 1 hour
      indicators: 1800,      // 30 minutes
      backtestResults: 86400 // 24 hours
    }
  }) {
    this.logger = new Logger('CacheService');
    this.client = createClient({ url: this.config.url });
    
    this.client.on('error', (err) => {
      this.logger.error('Redis client error', err);
      this.connected = false;
    });

    this.client.on('connect', () => {
      this.logger.info('Redis connected');
      this.connected = true;
    });
  }

  async connect(): Promise<void> {
    if (!this.connected) {
      await this.client.connect();
    }
  }

  async disconnect(): Promise<void> {
    if (this.connected) {
      await this.client.disconnect();
      this.connected = false;
    }
  }

  // Market data caching
  async setMarketData(key: string, data: any, ttl?: number): Promise<void> {
    await this.connect();
    const serialized = JSON.stringify(data);
    await this.client.set(key, serialized, {
      EX: ttl || this.config.ttl.marketData
    });
    this.logger.debug('Cached market data', { key, ttl });
  }

  async getMarketData(key: string): Promise<any | null> {
    await this.connect();
    const data = await this.client.get(key);
    
    if (!data) {
      this.logger.debug('Cache miss', { key });
      return null;
    }

    this.logger.debug('Cache hit', { key });
    return JSON.parse(data);
  }

  // Cache key generation
  generateMarketDataKey(symbol: string, timeframe: string, 
                       startDate: string, endDate: string): string {
    return `market:${symbol}:${timeframe}:${startDate}:${endDate}`;
  }

  generateIndicatorKey(symbol: string, indicator: string, 
                      params: Record<string, any>): string {
    const paramStr = Object.entries(params)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([k, v]) => `${k}:${v}`)
      .join(':');
    return `indicator:${symbol}:${indicator}:${paramStr}`;
  }

  // Cache invalidation
  async invalidatePattern(pattern: string): Promise<number> {
    await this.connect();
    const keys = await this.client.keys(pattern);
    
    if (keys.length === 0) {
      return 0;
    }

    const deleted = await this.client.del(keys);
    this.logger.info('Invalidated cache entries', { pattern, count: deleted });
    return deleted;
  }

  // Cache warming
  async warmCache(keys: string[], fetcher: (key: string) => Promise<any>): Promise<void> {
    await this.connect();
    
    const promises = keys.map(async (key) => {
      const exists = await this.client.exists(key);
      if (!exists) {
        const data = await fetcher(key);
        await this.setMarketData(key, data);
      }
    });

    await Promise.all(promises);
    this.logger.info('Cache warmed', { keyCount: keys.length });
  }
}
```

## 🔐 Authentication & Authorization

### 6. **JWT-Based Authentication**

```typescript
// middleware/auth/__tests__/auth.middleware.test.ts
import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { AuthMiddleware } from '../auth.middleware';
import { JwtService } from '../../services/auth/jwt.service';
import { Request, Response } from 'express';

describe('AuthMiddleware', () => {
  let authMiddleware: AuthMiddleware;
  let jwtService: jest.Mocked<JwtService>;
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let mockNext: jest.Mock;

  beforeEach(() => {
    jwtService = {
      verify: jest.fn(),
      decode: jest.fn()
    } as any;

    authMiddleware = new AuthMiddleware(jwtService);
    
    mockReq = {
      headers: {},
      user: undefined
    };

    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis()
    };

    mockNext = jest.fn();
  });

  describe('authenticate', () => {
    it('should authenticate valid JWT token', async () => {
      // Arrange
      const token = 'valid.jwt.token';
      const payload = { userId: '123', email: '<EMAIL>' };
      
      mockReq.headers = { authorization: `Bearer ${token}` };
      jwtService.verify.mockResolvedValue(payload);

      // Act
      await authMiddleware.authenticate(
        mockReq as Request,
        mockRes as Response,
        mockNext
      );

      // Assert
      expect(jwtService.verify).toHaveBeenCalledWith(token);
      expect(mockReq.user).toEqual(payload);
      expect(mockNext).toHaveBeenCalled();
    });

    it('should reject missing token', async () => {
      // Act
      await authMiddleware.authenticate(
        mockReq as Request,
        mockRes as Response,
        mockNext
      );

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: 'No token provided'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should reject invalid token', async () => {
      // Arrange
      mockReq.headers = { authorization: 'Bearer invalid.token' };
      jwtService.verify.mockRejectedValue(new Error('Invalid token'));

      // Act
      await authMiddleware.authenticate(
        mockReq as Request,
        mockRes as Response,
        mockNext
      );

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: 'Invalid token'
      });
    });
  });

  describe('authorize', () => {
    it('should authorize user with required role', () => {
      // Arrange
      const requiredRoles = ['admin', 'trader'];
      mockReq.user = { userId: '123', roles: ['trader'] };

      const middleware = authMiddleware.authorize(...requiredRoles);

      // Act
      middleware(mockReq as Request, mockRes as Response, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalled();
    });

    it('should reject user without required role', () => {
      // Arrange
      const requiredRoles = ['admin'];
      mockReq.user = { userId: '123', roles: ['user'] };

      const middleware = authMiddleware.authorize(...requiredRoles);

      // Act
      middleware(mockReq as Request, mockRes as Response, mockNext);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(403);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: 'Insufficient permissions'
      });
    });
  });
});

// middleware/auth/auth.middleware.ts
import { Request, Response, NextFunction } from 'express';
import { JwtService } from '../../services/auth/jwt.service';

interface AuthRequest extends Request {
  user?: {
    userId: string;
    email: string;
    roles: string[];
  };
}

export class AuthMiddleware {
  constructor(private jwtService: JwtService) {}

  async authenticate(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const authHeader = req.headers.authorization;
      
      if (!authHeader) {
        return res.status(401).json({ error: 'No token provided' });
      }

      const token = authHeader.replace('Bearer ', '');
      
      if (!token) {
        return res.status(401).json({ error: 'No token provided' });
      }

      const payload = await this.jwtService.verify(token);
      req.user = payload;
      
      next();
    } catch (error) {
      return res.status(401).json({ error: 'Invalid token' });
    }
  }

  authorize(...roles: string[]) {
    return (req: AuthRequest, res: Response, next: NextFunction) => {
      if (!req.user) {
        return res.status(401).json({ error: 'Not authenticated' });
      }

      const hasRole = roles.some(role => req.user!.roles.includes(role));
      
      if (!hasRole) {
        return res.status(403).json({ error: 'Insufficient permissions' });
      }

      next();
    };
  }

  // Rate limiting per user
  rateLimit(maxRequests: number, windowMs: number) {
    const requests = new Map<string, number[]>();

    return (req: AuthRequest, res: Response, next: NextFunction) => {
      if (!req.user) {
        return res.status(401).json({ error: 'Not authenticated' });
      }

      const userId = req.user.userId;
      const now = Date.now();
      const userRequests = requests.get(userId) || [];
      
      // Remove old requests outside window
      const validRequests = userRequests.filter(
        timestamp => now - timestamp < windowMs
      );

      if (validRequests.length >= maxRequests) {
        return res.status(429).json({ 
          error: 'Rate limit exceeded',
          retryAfter: Math.ceil((validRequests[0] + windowMs - now) / 1000)
        });
      }

      validRequests.push(now);
      requests.set(userId, validRequests);
      
      next();
    };
  }
}
```

## 📊 Advanced Analytics

### 7. **Walk-Forward Analysis Implementation**

```python
# services/analytics/__tests__/test_walk_forward.py
import pytest
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from services.analytics.walk_forward import WalkForwardAnalyzer
from services.analytics.optimization import StrategyOptimizer

class TestWalkForwardAnalyzer:
    def setup_method(self):
        self.analyzer = WalkForwardAnalyzer()
        self.optimizer = StrategyOptimizer()
        
        # Create sample data
        dates = pd.date_range('2022-01-01', '2024-01-01', freq='D')
        self.sample_data = pd.DataFrame({
            'timestamp': dates,
            'open': np.random.uniform(95, 105, len(dates)),
            'high': np.random.uniform(100, 110, len(dates)),
            'low': np.random.uniform(90, 100, len(dates)),
            'close': np.random.uniform(95, 105, len(dates)),
            'volume': np.random.uniform(1000, 5000, len(dates))
        })

    def test_splits_data_correctly(self):
        """Test that walk-forward splits data into correct windows"""
        # Arrange
        train_months = 12
        test_months = 3
        step_months = 3

        # Act
        splits = self.analyzer.create_splits(
            self.sample_data,
            train_months,
            test_months,
            step_months
        )

        # Assert
        assert len(splits) > 0
        for split in splits:
            assert 'train_start' in split
            assert 'train_end' in split
            assert 'test_start' in split
            assert 'test_end' in split
            
            # Verify train period length
            train_period = split['train_end'] - split['train_start']
            assert train_period.days >= 365  # ~12 months

            # Verify test period length
            test_period = split['test_end'] - split['test_start']
            assert test_period.days >= 89  # ~3 months

    def test_runs_complete_walk_forward_analysis(self):
        """Test complete walk-forward analysis workflow"""
        # Arrange
        strategy_code = """
def trading_strategy(data, params):
    sma_short = calculate_sma(data['close'], params['short_period'])
    sma_long = calculate_sma(data['close'], params['long_period'])
    
    if len(sma_short) < 2 or len(sma_long) < 2:
        return {'signal': 'hold'}
    
    if sma_short[-1] > sma_long[-1] and sma_short[-2] <= sma_long[-2]:
        return {'signal': 'buy'}
    elif sma_short[-1] < sma_long[-1] and sma_short[-2] >= sma_long[-2]:
        return {'signal': 'sell'}
    else:
        return {'signal': 'hold'}
"""
        
        param_ranges = {
            'short_period': (5, 20),
            'long_period': (20, 50)
        }

        # Act
        results = self.analyzer.run_walk_forward(
            self.sample_data,
            strategy_code,
            param_ranges,
            train_months=6,
            test_months=2,
            step_months=2
        )

        # Assert
        assert 'windows' in results
        assert 'summary' in results
        assert 'degradation' in results
        
        assert len(results['windows']) > 0
        
        for window in results['windows']:
            assert 'train_metrics' in window
            assert 'test_metrics' in window
            assert 'optimal_params' in window
            assert 'degradation_factor' in window

    def test_detects_overfitting(self):
        """Test that walk-forward analysis detects overfitted strategies"""
        # Arrange
        # Create an overfitted strategy
        overfitted_strategy = """
def trading_strategy(data, params):
    # This strategy is intentionally overfitted to specific dates
    if len(data) > params['magic_number']:
        return {'signal': 'buy'}
    else:
        return {'signal': 'sell'}
"""
        
        param_ranges = {
            'magic_number': (50, 100)
        }

        # Act
        results = self.analyzer.run_walk_forward(
            self.sample_data,
            overfitted_strategy,
            param_ranges,
            train_months=6,
            test_months=2
        )

        # Assert
        # Overfitted strategies should show high degradation
        assert results['summary']['avg_degradation'] > 0.3
        assert results['summary']['consistency_score'] < 0.5

# services/analytics/walk_forward.py
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import concurrent.futures
from services.backtesting.engine import BacktestEngine
from services.analytics.optimization import StrategyOptimizer

@dataclass
class WalkForwardWindow:
    train_start: datetime
    train_end: datetime
    test_start: datetime
    test_end: datetime
    train_data: pd.DataFrame
    test_data: pd.DataFrame

@dataclass
class WindowResult:
    window_id: int
    train_metrics: Dict[str, float]
    test_metrics: Dict[str, float]
    optimal_params: Dict[str, Any]
    degradation_factor: float

class WalkForwardAnalyzer:
    def __init__(self, backtest_engine: BacktestEngine = None):
        self.backtest_engine = backtest_engine or BacktestEngine()
        self.optimizer = StrategyOptimizer()

    def create_splits(self, 
                     data: pd.DataFrame,
                     train_months: int,
                     test_months: int,
                     step_months: int) -> List[WalkForwardWindow]:
        """
        Create walk-forward analysis windows
        
        Args:
            data: Historical market data
            train_months: Training period length in months
            test_months: Testing period length in months
            step_months: Step size between windows in months
            
        Returns:
            List of walk-forward windows
        """
        windows = []
        data['timestamp'] = pd.to_datetime(data['timestamp'])
        
        start_date = data['timestamp'].min()
        end_date = data['timestamp'].max()
        
        current_start = start_date
        
        while True:
            train_end = current_start + timedelta(days=train_months * 30)
            test_start = train_end
            test_end = test_start + timedelta(days=test_months * 30)
            
            if test_end > end_date:
                break
                
            train_data = data[
                (data['timestamp'] >= current_start) & 
                (data['timestamp'] < train_end)
            ]
            
            test_data = data[
                (data['timestamp'] >= test_start) & 
                (data['timestamp'] < test_end)
            ]
            
            if len(train_data) > 0 and len(test_data) > 0:
                windows.append(WalkForwardWindow(
                    train_start=current_start,
                    train_end=train_end,
                    test_start=test_start,
                    test_end=test_end,
                    train_data=train_data,
                    test_data=test_data
                ))
            
            current_start += timedelta(days=step_months * 30)
            
        return windows

    def optimize_window(self,
                       window: WalkForwardWindow,
                       strategy_code: str,
                       param_ranges: Dict[str, Tuple[float, float]]) -> WindowResult:
        """
        Optimize strategy parameters on training data and test on out-of-sample data
        """
        # Optimize on training data
        optimization_result = self.optimizer.optimize(
            strategy_code,
            window.train_data,
            param_ranges,
            objective='sharpe_ratio'
        )
        
        optimal_params = optimization_result['best_params']
        
        # Backtest on training data with optimal params
        train_result = self.backtest_engine.run(
            strategy_code,
            window.train_data,
            optimal_params
        )
        
        # Backtest on test data with same params
        test_result = self.backtest_engine.run(
            strategy_code,
            window.test_data,
            optimal_params
        )
        
        # Calculate degradation
        train_sharpe = train_result['metrics']['sharpe_ratio']
        test_sharpe = test_result['metrics']['sharpe_ratio']
        
        if train_sharpe > 0:
            degradation = max(0, (train_sharpe - test_sharpe) / train_sharpe)
        else:
            degradation = 1.0
            
        return WindowResult(
            window_id=id(window),
            train_metrics=train_result['metrics'],
            test_metrics=test_result['metrics'],
            optimal_params=optimal_params,
            degradation_factor=degradation
        )

    def run_walk_forward(self,
                        data: pd.DataFrame,
                        strategy_code: str,
                        param_ranges: Dict[str, Tuple[float, float]],
                        train_months: int = 12,
                        test_months: int = 3,
                        step_months: int = 3,
                        n_jobs: int = -1) -> Dict[str, Any]:
        """
        Run complete walk-forward analysis
        
        Args:
            data: Historical market data
            strategy_code: Strategy implementation
            param_ranges: Parameter optimization ranges
            train_months: Training period length
            test_months: Testing period length
            step_months: Step between windows
            n_jobs: Number of parallel jobs (-1 for all CPUs)
            
        Returns:
            Analysis results including degradation metrics
        """
        # Create windows
        windows = self.create_splits(data, train_months, test_months, step_months)
        
        if not windows:
            raise ValueError("Insufficient data for walk-forward analysis")
        
        # Process windows in parallel
        if n_jobs == -1:
            n_jobs = os.cpu_count()
            
        results = []
        with concurrent.futures.ProcessPoolExecutor(max_workers=n_jobs) as executor:
            futures = [
                executor.submit(self.optimize_window, window, strategy_code, param_ranges)
                for window in windows
            ]
            
            for future in concurrent.futures.as_completed(futures):
                results.append(future.result())
        
        # Calculate summary statistics
        degradations = [r.degradation_factor for r in results]
        test_sharpes = [r.test_metrics['sharpe_ratio'] for r in results]
        
        summary = {
            'avg_degradation': np.mean(degradations),
            'std_degradation': np.std(degradations),
            'consistency_score': 1 - np.std(test_sharpes) / (np.mean(test_sharpes) + 1e-6),
            'avg_test_sharpe': np.mean(test_sharpes),
            'robustness_score': self._calculate_robustness_score(results)
        }
        
        return {
            'windows': [self._window_to_dict(r) for r in results],
            'summary': summary,
            'degradation': {
                'values': degradations,
                'threshold': 0.3,  # 30% degradation threshold
                'passed': np.mean(degradations) < 0.3
            }
        }

    def _calculate_robustness_score(self, results: List[WindowResult]) -> float:
        """Calculate strategy robustness based on consistency across windows"""
        if not results:
            return 0.0
            
        # Check parameter stability
        param_variations = {}
        for result in results:
            for param, value in result.optimal_params.items():
                if param not in param_variations:
                    param_variations[param] = []
                param_variations[param].append(value)
        
        # Calculate coefficient of variation for each parameter
        cvs = []
        for param, values in param_variations.items():
            if len(values) > 1:
                cv = np.std(values) / (np.mean(values) + 1e-6)
                cvs.append(cv)
        
        # Lower CV means more stable parameters
        param_stability = 1 - np.mean(cvs) if cvs else 1.0
        
        # Check performance consistency
        test_returns = [r.test_metrics.get('total_return', 0) for r in results]
        positive_rate = sum(1 for r in test_returns if r > 0) / len(test_returns)
        
        # Combine factors
        robustness = (param_stability * 0.5 + positive_rate * 0.5)
        
        return robustness

    def _window_to_dict(self, result: WindowResult) -> Dict[str, Any]:
        """Convert window result to dictionary"""
        return {
            'window_id': result.window_id,
            'train_metrics': result.train_metrics,
            'test_metrics': result.test_metrics,
            'optimal_params': result.optimal_params,
            'degradation_factor': result.degradation_factor
        }
```

## 🔄 Event-Driven Architecture

### 8. **Event Bus Implementation**

```typescript
// services/events/__tests__/event-bus.test.ts
import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { EventBus, EventHandler, DomainEvent } from '../event-bus';

// Test events
class OrderPlacedEvent implements DomainEvent {
  readonly eventType = 'OrderPlaced';
  readonly occurredAt = new Date();
  
  constructor(
    public readonly orderId: string,
    public readonly symbol: string,
    public readonly quantity: number,
    public readonly price: number
  ) {}
}

class OrderFilledEvent implements DomainEvent {
  readonly eventType = 'OrderFilled';
  readonly occurredAt = new Date();
  
  constructor(
    public readonly orderId: string,
    public readonly executionPrice: number
  ) {}
}

describe('EventBus', () => {
  let eventBus: EventBus;
  
  beforeEach(() => {
    eventBus = new EventBus();
  });

  describe('event publishing and subscription', () => {
    it('should deliver events to registered handlers', async () => {
      // Arrange
      const handler = jest.fn();
      eventBus.subscribe('OrderPlaced', handler);
      
      const event = new OrderPlacedEvent('123', 'AAPL', 100, 150.00);

      // Act
      await eventBus.publish(event);

      // Assert
      expect(handler).toHaveBeenCalledWith(event);
    });

    it('should support multiple handlers for same event', async () => {
      // Arrange
      const handler1 = jest.fn();
      const handler2 = jest.fn();
      
      eventBus.subscribe('OrderPlaced', handler1);
      eventBus.subscribe('OrderPlaced', handler2);
      
      const event = new OrderPlacedEvent('123', 'AAPL', 100, 150.00);

      // Act
      await eventBus.publish(event);

      // Assert
      expect(handler1).toHaveBeenCalledWith(event);
      expect(handler2).toHaveBeenCalledWith(event);
    });

    it('should handle errors in event handlers gracefully', async () => {
      // Arrange
      const errorHandler = jest.fn(() => {
        throw new Error('Handler error');
      });
      const successHandler = jest.fn();
      
      eventBus.subscribe('OrderPlaced', errorHandler);
      eventBus.subscribe('OrderPlaced', successHandler);
      
      const event = new OrderPlacedEvent('123', 'AAPL', 100, 150.00);

      // Act
      await eventBus.publish(event);

      // Assert - other handlers should still execute
      expect(successHandler).toHaveBeenCalledWith(event);
    });

    it('should support event filtering', async () => {
      // Arrange
      const appleHandler = jest.fn();
      const allHandler = jest.fn();
      
      eventBus.subscribe('OrderPlaced', appleHandler, {
        filter: (event: OrderPlacedEvent) => event.symbol === 'AAPL'
      });
      
      eventBus.subscribe('OrderPlaced', allHandler);
      
      const appleOrder = new OrderPlacedEvent('123', 'AAPL', 100, 150.00);
      const msftOrder = new OrderPlacedEvent('124', 'MSFT', 50, 300.00);

      // Act
      await eventBus.publish(appleOrder);
      await eventBus.publish(msftOrder);

      // Assert
      expect(appleHandler).toHaveBeenCalledTimes(1);
      expect(appleHandler).toHaveBeenCalledWith(appleOrder);
      expect(allHandler).toHaveBeenCalledTimes(2);
    });
  });

  describe('event replay', () => {
    it('should store and replay events', async () => {
      // Arrange
      const handler = jest.fn();
      
      const event1 = new OrderPlacedEvent('123', 'AAPL', 100, 150.00);
      const event2 = new OrderFilledEvent('123', 185.50);
      
      await eventBus.publish(event1);
      await eventBus.publish(event2);

      // Act - subscribe after events published
      eventBus.subscribe('OrderPlaced', handler, { replay: true });

      // Assert
      expect(handler).toHaveBeenCalledWith(event1);
    });
  });
});

// services/events/event-bus.ts
export interface DomainEvent {
  eventType: string;
  occurredAt: Date;
}

export type EventHandler<T extends DomainEvent = DomainEvent> = (event: T) => void | Promise<void>;

interface SubscriptionOptions<T extends DomainEvent = DomainEvent> {
  filter?: (event: T) => boolean;
  replay?: boolean;
}

interface Subscription {
  id: string;
  eventType: string;
  handler: EventHandler;
  options: SubscriptionOptions;
}

export class EventBus {
  private subscriptions: Map<string, Subscription[]> = new Map();
  private eventStore: DomainEvent[] = [];
  private logger: Logger;

  constructor() {
    this.logger = new Logger('EventBus');
  }

  subscribe<T extends DomainEvent>(
    eventType: string,
    handler: EventHandler<T>,
    options: SubscriptionOptions<T> = {}
  ): () => void {
    const subscription: Subscription = {
      id: this.generateId(),
      eventType,
      handler: handler as EventHandler,
      options
    };

    const subs = this.subscriptions.get(eventType) || [];
    subs.push(subscription);
    this.subscriptions.set(eventType, subs);

    // Replay events if requested
    if (options.replay) {
      this.replayEvents(eventType, handler, options);
    }

    // Return unsubscribe function
    return () => this.unsubscribe(subscription.id);
  }

  async publish(event: DomainEvent): Promise<void> {
    this.logger.debug('Publishing event', { 
      type: event.eventType,
      timestamp: event.occurredAt 
    });

    // Store event
    this.eventStore.push(event);

    // Get handlers for this event type
    const handlers = this.subscriptions.get(event.eventType) || [];

    // Execute handlers
    const promises = handlers.map(async (subscription) => {
      try {
        // Apply filter if present
        if (subscription.options.filter) {
          const shouldHandle = subscription.options.filter(event);
          if (!shouldHandle) {
            return;
          }
        }

        await subscription.handler(event);
      } catch (error) {
        this.logger.error('Event handler error', {
          eventType: event.eventType,
          subscriptionId: subscription.id,
          error
        });
      }
    });

    await Promise.all(promises);
  }

  private replayEvents<T extends DomainEvent>(
    eventType: string,
    handler: EventHandler<T>,
    options: SubscriptionOptions<T>
  ): void {
    const eventsToReplay = this.eventStore.filter(
      event => event.eventType === eventType
    );

    eventsToReplay.forEach(event => {
      if (options.filter && !options.filter(event as T)) {
        return;
      }
      
      try {
        handler(event as T);
      } catch (error) {
        this.logger.error('Error replaying event', { error });
      }
    });
  }

  private unsubscribe(subscriptionId: string): void {
    for (const [eventType, subs] of this.subscriptions) {
      const filtered = subs.filter(sub => sub.id !== subscriptionId);
      if (filtered.length !== subs.length) {
        this.subscriptions.set(eventType, filtered);
        break;
      }
    }
  }

  private generateId(): string {
    return `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Event sourcing helpers
  getEventHistory(filters?: {
    eventType?: string;
    startDate?: Date;
    endDate?: Date;
  }): DomainEvent[] {
    let events = [...this.eventStore];

    if (filters?.eventType) {
      events = events.filter(e => e.eventType === filters.eventType);
    }

    if (filters?.startDate) {
      events = events.filter(e => e.occurredAt >= filters.startDate!);
    }

    if (filters?.endDate) {
      events = events.filter(e => e.occurredAt <= filters.endDate!);
    }

    return events;
  }

  clearEventStore(): void {
    this.eventStore = [];
  }
}

// Example domain events
export class StrategyExecutedEvent implements DomainEvent {
  readonly eventType = 'StrategyExecuted';
  readonly occurredAt = new Date();

  constructor(
    public readonly strategyId: string,
    public readonly symbol: string,
    public readonly signal: 'buy' | 'sell' | 'hold',
    public readonly confidence: number,
    public readonly metadata?: Record<string, any>
  ) {}
}

export class BacktestCompletedEvent implements DomainEvent {
  readonly eventType = 'BacktestCompleted';
  readonly occurredAt = new Date();

  constructor(
    public readonly backtestId: string,
    public readonly strategyName: string,
    public readonly metrics: {
      totalReturn: number;
      sharpeRatio: number;
      maxDrawdown: number;
      winRate: number;
    },
    public readonly duration: number
  ) {}
}

export class AlertTriggeredEvent implements DomainEvent {
  readonly eventType = 'AlertTriggered';
  readonly occurredAt = new Date();

  constructor(
    public readonly alertId: string,
    public readonly type: 'price' | 'indicator' | 'risk',
    public readonly message: string,
    public readonly severity: 'low' | 'medium' | 'high' | 'critical',
    public readonly data: Record<string, any>
  ) {}
}
```

## 🏗️ CI/CD Pipeline Configuration

### 9. **GitHub Actions Workflow**

```yaml
# .github/workflows/ci-cd.yml
name: AI Trading Platform CI/CD

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  NODE_VERSION: '18.x'
  PYTHON_VERSION: '3.11'
  DOCKER_REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Test job for Node.js backend
  test-backend:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: testpass
          POSTGRES_DB: trading_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        cd backend
        npm ci
    
    - name: Run linting
      run: |
        cd backend
        npm run lint
    
    - name: Run type checking
      run: |
        cd backend
        npm run type-check
    
    - name: Run unit tests
      run: |
        cd backend
        npm run test:unit -- --coverage
      env:
        DATABASE_URL: postgresql://postgres:testpass@localhost:5432/trading_test
        REDIS_URL: redis://localhost:6379
        JWT_SECRET: test-secret
    
    - name: Run integration tests
      run: |
        cd backend
        npm run test:integration
      env:
        DATABASE_URL: postgresql://postgres:testpass@localhost:5432/trading_test
        REDIS_URL: redis://localhost:6379
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage/lcov.info
        flags: backend
    
    - name: Check test coverage
      run: |
        cd backend
        npm run test:coverage-check

  # Test job for Python ML engine
  test-ml-engine:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: Install dependencies
      run: |
        cd ml-engine
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: Run linting
      run: |
        cd ml-engine
        flake8 . --config=.flake8
        black --check .
        mypy .
    
    - name: Run security scan
      run: |
        cd ml-engine
        bandit -r services/ -ll
    
    - name: Run tests
      run: |
        cd ml-engine
        pytest --cov=services --cov-report=xml --cov-fail-under=90
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./ml-engine/coverage.xml
        flags: ml-engine

  # Test job for React frontend
  test-frontend:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        cd frontend
        npm ci
    
    - name: Run linting
      run: |
        cd frontend
        npm run lint
    
    - name: Run tests
      run: |
        cd frontend
        npm run test -- --coverage --watchAll=false
    
    - name: Build application
      run: |
        cd frontend
        npm run build
    
    - name: Run E2E tests
      run: |
        cd frontend
        npm run test:e2e:ci
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./frontend/coverage/lcov.info
        flags: frontend

  # Security scanning
  security-scan:
    runs-on: ubuntu-latest
    needs: [test-backend, test-ml-engine, test-frontend]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        ignore-unfixed: true
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'
    
    - name: Run OWASP dependency check
      uses: dependency-check/Dependency-Check_Action@main
      with:
        project: 'AI-Trading-Platform'
        path: '.'
        format: 'HTML'

  # Build and push Docker images
  build-and-push:
    runs-on: ubuntu-latest
    needs: [security-scan]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    permissions:
      contents: read
      packages: write
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Log in to Container Registry
      uses: docker/login-action@v2
      with:
        registry: ${{ env.DOCKER_REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    
    - name: Build and push backend image
      uses: docker/build-push-action@v4
      with:
        context: ./backend
        push: true
        tags: |
          ${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}/backend:latest
          ${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}/backend:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
    
    - name: Build and push ML engine image
      uses: docker/build-push-action@v4
      with:
        context: ./ml-engine
        push: true
        tags: |
          ${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}/ml-engine:latest
          ${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}/ml-engine:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
    
    - name: Build and push frontend image
      uses: docker/build-push-action@v4
      with:
        context: ./frontend
        push: true
        tags: |
          ${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}/frontend:latest
          ${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}/frontend:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # Deploy to staging
  deploy-staging:
    runs-on: ubuntu-latest
    needs: [build-and-push]
    if: github.ref == 'refs/heads/main'
    environment: staging
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to Kubernetes
      run: |
        # Setup kubectl
        # Apply manifests with new image tags
        # Run smoke tests
        echo "Deploying to staging..."
```

## 📚 Additional Recommendations

### 10. **Project Structure Enhancement**

```bash
# Recommended project structure
ai-enhanced-trading-platform/
├── .github/
│   ├── workflows/         # CI/CD pipelines
│   ├── ISSUE_TEMPLATE/    # Issue templates
│   └── pull_request_template.md
├── backend/               # Node.js API
│   ├── src/
│   │   ├── api/          # REST endpoints
│   │   ├── services/     # Business logic
│   │   ├── models/       # Data models
│   │   ├── middleware/   # Express middleware
│   │   ├── events/       # Event handling
│   │   ├── utils/        # Utilities
│   │   └── types/        # TypeScript types
│   ├── tests/
│   │   ├── unit/         # Unit tests
│   │   ├── integration/  # Integration tests
│   │   └── e2e/          # End-to-end tests
│   ├── prisma/           # Database schema
│   └── Dockerfile
├── ml-engine/            # Python ML services
│   ├── services/
│   │   ├── strategy_executor/
│   │   ├── backtesting/
│   │   ├── analytics/
│   │   └── optimization/
│   ├── tests/
│   ├── requirements.txt
│   └── Dockerfile
├── frontend/             # React application
│   ├── src/
│   ├── tests/
│   └── Dockerfile
├── infrastructure/       # IaC and deployment
│   ├── kubernetes/       # K8s manifests
│   ├── terraform/        # Cloud infrastructure
│   └── monitoring/       # Prometheus, Grafana
├── docs/                 # Documentation
│   ├── architecture/
│   ├── api/
│   └── deployment/
├── scripts/              # Utility scripts
└── docker-compose.yml    # Local development
```

### Key Improvements Summary:

1. **Security First**: Implemented RestrictedPython for safe strategy execution
2. **Comprehensive Testing**: TDD approach with 90%+ coverage requirement
3. **Input Validation**: Zod schemas for all API inputs
4. **Performance**: Redis caching and async processing
5. **Authentication**: JWT-based auth with role-based access
6. **Advanced Analytics**: Walk-forward analysis for strategy validation
7. **Event-Driven**: Event bus for decoupled architecture
8. **CI/CD**: Automated testing, security scanning, and deployment
9. **Monitoring**: Structured logging and metrics
10. **Documentation**: Comprehensive docs following code structure

These improvements align with your Darwin project's emphasis on security, testing, and professional-grade trading infrastructure. The TDD approach ensures reliability while the modular architecture supports scalability.