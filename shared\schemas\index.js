"use strict";
/**
 * Central export for all Zod schemas
 * This ensures consistent schema usage across the application
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
// Core schemas
__exportStar(require("./user.schemas"), exports);
__exportStar(require("./auth.schemas"), exports);
__exportStar(require("./trading.schemas"), exports);
__exportStar(require("./backtest.schemas"), exports);
__exportStar(require("./upload.schemas"), exports);
__exportStar(require("./chat.schemas"), exports);
__exportStar(require("./api.schemas"), exports);
// export * from './python-bridge'; // File doesn't exist
__exportStar(require("./workers"), exports);
// Darwin Gödel Machine schemas
__exportStar(require("./darwin.schemas"), exports);
// Common schemas
__exportStar(require("./common.schemas"), exports);
//# sourceMappingURL=index.js.map