#!/usr/bin/env python3
"""
Hypothesis generators for financial data testing
Advanced property-based testing for trading platform
"""

from hypothesis import strategies as st
from hypothesis.extra.pandas import data_frames, column
import pandas as pd
import numpy as np
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, Tuple, List


@st.composite
def generate_financial_data(draw, 
                          min_records: int = 100, 
                          max_records: int = 1000,
                          start_date: Optional[datetime] = None,
                          price_range: Tuple[float, float] = (0.1, 1000.0),
                          symbol: str = "TESTPAIR") -> pd.DataFrame:
    """
    Generate realistic financial OHLC data for property-based testing
    
    Args:
        draw: Hypothesis draw function
        min_records: Minimum number of records to generate
        max_records: Maximum number of records to generate
        start_date: Starting date for the data
        price_range: (min_price, max_price) tuple
        symbol: Currency pair symbol
    
    Returns:
        DataFrame with OHLC data and proper index
    """
    
    if start_date is None:
        start_date = datetime(2020, 1, 1, tzinfo=timezone.utc)
    
    num_records = draw(st.integers(min_value=min_records, max_value=max_records))
    
    # Generate base price using geometric brownian motion
    base_price = draw(st.floats(min_value=price_range[0], max_value=price_range[1]))
    
    # Generate realistic price movements with drift and volatility
    drift = draw(st.floats(min_value=-0.001, max_value=0.001))  # Daily drift
    volatility = draw(st.floats(min_value=0.005, max_value=0.05))  # Daily volatility
    
    # Generate returns using normal distribution
    returns = draw(st.lists(
        st.floats(min_value=-0.1, max_value=0.1).filter(lambda x: abs(x) < 0.05),
        min_size=num_records,
        max_size=num_records
    ))
    
    # Calculate prices using geometric brownian motion
    prices = [base_price]
    for i, ret in enumerate(returns[:-1]):
        # Add drift and scale by volatility
        adjusted_return = drift + ret * volatility
        new_price = prices[-1] * np.exp(adjusted_return)
        # Ensure price stays within reasonable bounds
        new_price = max(price_range[0], min(price_range[1], new_price))
        prices.append(new_price)
    
    # Generate OHLC data from base prices
    data = []
    for i, close_price in enumerate(prices):
        # Generate intraday volatility
        intraday_vol = draw(st.floats(min_value=0.001, max_value=0.02))
        
        # Generate open price (could be gap from previous close)
        if i == 0:
            open_price = close_price
        else:
            gap = draw(st.floats(min_value=-0.01, max_value=0.01))
            open_price = prices[i-1] * (1 + gap)
            open_price = max(price_range[0], min(price_range[1], open_price))
        
        # Generate high and low based on open and close
        high_factor = 1 + abs(draw(st.floats(min_value=0, max_value=intraday_vol)))
        low_factor = 1 - abs(draw(st.floats(min_value=0, max_value=intraday_vol)))
        
        high_price = max(open_price, close_price) * high_factor
        low_price = min(open_price, close_price) * low_factor
        
        # Ensure OHLC relationships are maintained
        high_price = max(high_price, open_price, close_price)
        low_price = min(low_price, open_price, close_price)
        
        # Generate volume with log-normal distribution
        volume = draw(st.integers(min_value=100, max_value=1000000))
        
        data.append({
            'open': round(open_price, 5),
            'high': round(high_price, 5),
            'low': round(low_price, 5),
            'close': round(close_price, 5),
            'volume': volume
        })
    
    # Create date range
    dates = pd.date_range(
        start=start_date,
        periods=num_records,
        freq='h'  # Use 'h' instead of deprecated 'H'
    )
    
    df = pd.DataFrame(data, index=dates)
    
    # Add metadata attributes
    df.attrs['symbol'] = symbol
    df.attrs['timeframe'] = '1H'
    df.attrs['generated'] = True
    
    return df


@st.composite
def generate_strategy_parameters(draw, strategy_type: str) -> Dict[str, Any]:
    """
    Generate strategy parameters for different strategy types
    
    Args:
        draw: Hypothesis draw function
        strategy_type: Type of strategy ('RSI', 'MACD', 'MovingAverage', etc.)
    
    Returns:
        Dictionary of strategy parameters
    """
    
    if strategy_type == 'RSI':
        overbought = draw(st.floats(min_value=60, max_value=90))
        oversold = draw(st.floats(min_value=10, max_value=40))
        
        # Ensure overbought > oversold
        if overbought <= oversold:
            overbought = oversold + 20
        
        return {
            'period': draw(st.integers(min_value=5, max_value=50)),
            'overbought': overbought,
            'oversold': oversold,
            'signal_threshold': draw(st.floats(min_value=0.01, max_value=0.1))
        }
    
    elif strategy_type == 'MACD':
        fast_period = draw(st.integers(min_value=5, max_value=20))
        slow_period = draw(st.integers(min_value=fast_period + 1, max_value=50))
        
        return {
            'fast_period': fast_period,
            'slow_period': slow_period,
            'signal_period': draw(st.integers(min_value=5, max_value=20)),
            'signal_threshold': draw(st.floats(min_value=0.001, max_value=0.01))
        }
    
    elif strategy_type == 'MovingAverage':
        short_period = draw(st.integers(min_value=5, max_value=50))
        long_period = draw(st.integers(min_value=short_period + 1, max_value=200))
        
        return {
            'short_period': short_period,
            'long_period': long_period,
            'signal_threshold': draw(st.floats(min_value=0.001, max_value=0.05))
        }
    
    elif strategy_type == 'BollingerBands':
        return {
            'period': draw(st.integers(min_value=10, max_value=50)),
            'std_dev': draw(st.floats(min_value=1.0, max_value=3.0)),
            'signal_threshold': draw(st.floats(min_value=0.01, max_value=0.1))
        }
    
    else:
        return {}


@st.composite
def generate_market_conditions(draw) -> Dict[str, Any]:
    """
    Generate various market conditions for testing strategies
    
    Args:
        draw: Hypothesis draw function
    
    Returns:
        Dictionary describing market conditions
    """
    
    condition_type = draw(st.sampled_from([
        'trending_up', 'trending_down', 'sideways', 
        'volatile', 'low_volume', 'high_volume',
        'gap_up', 'gap_down', 'consolidation'
    ]))
    
    base_config = {
        'condition': condition_type,
        'duration_hours': draw(st.integers(min_value=24, max_value=720)),  # 1 day to 1 month
        'volatility': draw(st.floats(min_value=0.001, max_value=0.1)),
        'volume_multiplier': draw(st.floats(min_value=0.1, max_value=5.0))
    }
    
    # Add condition-specific parameters
    if condition_type in ['trending_up', 'trending_down']:
        base_config['trend_strength'] = draw(st.floats(min_value=0.1, max_value=2.0))
        base_config['trend_consistency'] = draw(st.floats(min_value=0.5, max_value=0.95))
    
    elif condition_type == 'volatile':
        base_config['volatility'] = draw(st.floats(min_value=0.05, max_value=0.3))
        base_config['volatility_clustering'] = draw(st.booleans())
    
    elif condition_type in ['low_volume', 'high_volume']:
        multiplier = 0.1 if condition_type == 'low_volume' else 3.0
        base_config['volume_multiplier'] = draw(st.floats(min_value=multiplier, max_value=multiplier*2))
    
    elif condition_type in ['gap_up', 'gap_down']:
        gap_size = draw(st.floats(min_value=0.01, max_value=0.1))
        base_config['gap_size'] = gap_size if condition_type == 'gap_up' else -gap_size
    
    return base_config


@st.composite
def generate_risk_parameters(draw) -> Dict[str, Any]:
    """
    Generate risk management parameters for testing
    
    Args:
        draw: Hypothesis draw function
    
    Returns:
        Dictionary of risk management parameters
    """
    
    return {
        'max_position_size': draw(st.floats(min_value=0.01, max_value=1.0)),  # 1% to 100% of portfolio
        'stop_loss_pct': draw(st.floats(min_value=0.005, max_value=0.2)),     # 0.5% to 20%
        'take_profit_pct': draw(st.floats(min_value=0.01, max_value=0.5)),    # 1% to 50%
        'max_daily_loss': draw(st.floats(min_value=0.01, max_value=0.1)),     # 1% to 10%
        'max_drawdown': draw(st.floats(min_value=0.05, max_value=0.3)),       # 5% to 30%
        'risk_per_trade': draw(st.floats(min_value=0.001, max_value=0.05)),   # 0.1% to 5%
        'correlation_limit': draw(st.floats(min_value=0.3, max_value=0.9))    # 30% to 90%
    }


@st.composite
def generate_portfolio_data(draw, 
                          num_assets: int = None,
                          portfolio_value: float = None) -> Dict[str, Any]:
    """
    Generate portfolio data for testing
    
    Args:
        draw: Hypothesis draw function
        num_assets: Number of assets in portfolio
        portfolio_value: Total portfolio value
    
    Returns:
        Dictionary containing portfolio information
    """
    
    if num_assets is None:
        num_assets = draw(st.integers(min_value=1, max_value=10))
    
    if portfolio_value is None:
        portfolio_value = draw(st.floats(min_value=1000, max_value=1000000))
    
    # Generate asset symbols
    major_pairs = ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD']
    assets = draw(st.lists(
        st.sampled_from(major_pairs),
        min_size=num_assets,
        max_size=num_assets,
        unique=True
    ))
    
    # Generate position sizes (must sum to <= 1.0)
    raw_weights = draw(st.lists(
        st.floats(min_value=0.01, max_value=0.5),
        min_size=num_assets,
        max_size=num_assets
    ))
    
    # Normalize weights to sum to <= 1.0
    total_weight = sum(raw_weights)
    max_allocation = draw(st.floats(min_value=0.5, max_value=1.0))
    weights = [w / total_weight * max_allocation for w in raw_weights]
    
    # Generate positions
    positions = {}
    for asset, weight in zip(assets, weights):
        positions[asset] = {
            'size': portfolio_value * weight,
            'weight': weight,
            'entry_price': draw(st.floats(min_value=0.5, max_value=2.0)),
            'current_price': draw(st.floats(min_value=0.5, max_value=2.0)),
            'unrealized_pnl': draw(st.floats(min_value=-0.1, max_value=0.1)) * portfolio_value * weight
        }
    
    return {
        'total_value': portfolio_value,
        'cash': portfolio_value * (1 - sum(weights)),
        'positions': positions,
        'num_assets': len(assets),
        'total_allocation': sum(weights)
    }


@st.composite
def generate_trade_sequence(draw, 
                          min_trades: int = 1,
                          max_trades: int = 100) -> List[Dict[str, Any]]:
    """
    Generate a sequence of trades for testing
    
    Args:
        draw: Hypothesis draw function
        min_trades: Minimum number of trades
        max_trades: Maximum number of trades
    
    Returns:
        List of trade dictionaries
    """
    
    num_trades = draw(st.integers(min_value=min_trades, max_value=max_trades))
    
    trades = []
    base_time = datetime(2023, 1, 1, tzinfo=timezone.utc)
    
    for i in range(num_trades):
        trade_time = base_time + timedelta(hours=i * draw(st.integers(min_value=1, max_value=24)))
        
        trade = {
            'timestamp': trade_time,
            'symbol': draw(st.sampled_from(['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF'])),
            'side': draw(st.sampled_from(['BUY', 'SELL'])),
            'quantity': draw(st.floats(min_value=0.01, max_value=10.0)),
            'price': draw(st.floats(min_value=0.5, max_value=2.0)),
            'commission': draw(st.floats(min_value=0.0, max_value=10.0)),
            'slippage': draw(st.floats(min_value=0.0, max_value=0.01)),
            'pnl': draw(st.floats(min_value=-1000, max_value=1000)),
            'trade_type': draw(st.sampled_from(['MARKET', 'LIMIT', 'STOP'])),
            'strategy': draw(st.sampled_from(['RSI', 'MACD', 'MA', 'MANUAL']))
        }
        
        trades.append(trade)
    
    return trades


@st.composite
def generate_corrupted_data(draw, base_data: pd.DataFrame) -> pd.DataFrame:
    """
    Generate corrupted versions of financial data for testing validation
    
    Args:
        draw: Hypothesis draw function
        base_data: Clean financial data to corrupt
    
    Returns:
        Corrupted DataFrame for testing validation logic
    """
    
    corrupted_data = base_data.copy()
    
    corruption_type = draw(st.sampled_from([
        'negative_prices', 'invalid_ohlc', 'missing_values', 
        'duplicate_timestamps', 'extreme_values', 'wrong_order'
    ]))
    
    if corruption_type == 'negative_prices':
        # Introduce negative prices
        corrupt_indices = draw(st.lists(
            st.integers(min_value=0, max_value=len(corrupted_data)-1),
            min_size=1,
            max_size=min(10, len(corrupted_data))
        ))
        for idx in corrupt_indices:
            corrupted_data.iloc[idx, 0] = -abs(corrupted_data.iloc[idx, 0])
    
    elif corruption_type == 'invalid_ohlc':
        # Make high < low
        corrupt_indices = draw(st.lists(
            st.integers(min_value=0, max_value=len(corrupted_data)-1),
            min_size=1,
            max_size=min(5, len(corrupted_data))
        ))
        for idx in corrupt_indices:
            high_val = corrupted_data.iloc[idx]['high']
            low_val = corrupted_data.iloc[idx]['low']
            corrupted_data.iloc[idx, corrupted_data.columns.get_loc('high')] = low_val * 0.9
            corrupted_data.iloc[idx, corrupted_data.columns.get_loc('low')] = high_val * 1.1
    
    elif corruption_type == 'missing_values':
        # Introduce NaN values
        corrupt_indices = draw(st.lists(
            st.integers(min_value=0, max_value=len(corrupted_data)-1),
            min_size=1,
            max_size=min(10, len(corrupted_data))
        ))
        for idx in corrupt_indices:
            col = draw(st.sampled_from(corrupted_data.columns))
            corrupted_data.iloc[idx, corrupted_data.columns.get_loc(col)] = np.nan
    
    elif corruption_type == 'duplicate_timestamps':
        # Duplicate some timestamps
        if len(corrupted_data) > 1:
            duplicate_idx = draw(st.integers(min_value=1, max_value=len(corrupted_data)-1))
            corrupted_data.index = corrupted_data.index.tolist()
            corrupted_data.index[duplicate_idx] = corrupted_data.index[duplicate_idx - 1]
    
    elif corruption_type == 'extreme_values':
        # Introduce extreme values
        corrupt_indices = draw(st.lists(
            st.integers(min_value=0, max_value=len(corrupted_data)-1),
            min_size=1,
            max_size=min(5, len(corrupted_data))
        ))
        for idx in corrupt_indices:
            col = draw(st.sampled_from(['open', 'high', 'low', 'close']))
            extreme_value = draw(st.floats(min_value=1e6, max_value=1e9))
            corrupted_data.iloc[idx, corrupted_data.columns.get_loc(col)] = extreme_value
    
    elif corruption_type == 'wrong_order':
        # Shuffle the data to break temporal order
        corrupted_data = corrupted_data.sample(frac=1)
    
    corrupted_data.attrs['corruption_type'] = corruption_type
    return corrupted_data


# Strategy for generating valid currency pair symbols
currency_pairs = st.sampled_from([
    'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD',
    'EURJPY', 'EURGBP', 'EURCHF', 'EURAUD', 'EURCAD', 'EURNZD',
    'GBPJPY', 'GBPCHF', 'GBPAUD', 'GBPCAD', 'GBPNZD',
    'AUDJPY', 'AUDCHF', 'AUDCAD', 'AUDNZD',
    'CADJPY', 'CADCHF', 'NZDJPY', 'NZDCHF', 'NZDCAD'
])

# Strategy for generating timeframes
timeframes = st.sampled_from(['1M', '5M', '15M', '30M', '1H', '4H', '1D', '1W'])

# Strategy for generating validation levels
validation_levels = st.sampled_from(['BASIC', 'STANDARD', 'STRICT', 'CRYPTOGRAPHIC'])