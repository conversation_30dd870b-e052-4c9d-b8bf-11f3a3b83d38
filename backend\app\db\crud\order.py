"""
Order CRUD operations
"""

import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update, delete

from ..models import Order

async def get_order_by_id(db: AsyncSession, order_id: uuid.UUID) -> Optional[Order]:
    """Get order by ID"""
    result = await db.execute(select(Order).where(Order.id == order_id))
    return result.scalars().first()

async def get_order_by_mt5_id(db: AsyncSession, mt5_account_id: uuid.UUID, mt5_order_id: int) -> Optional[Order]:
    """Get order by MT5 order ID"""
    result = await db.execute(
        select(Order).where(
            Order.mt5_account_id == mt5_account_id,
            Order.mt5_order_id == mt5_order_id
        )
    )
    return result.scalars().first()

async def get_orders_by_account(db: AsyncSession, mt5_account_id: uuid.UUID) -> List[Order]:
    """Get all orders for an MT5 account"""
    result = await db.execute(select(Order).where(Order.mt5_account_id == mt5_account_id))
    return result.scalars().all()

async def get_orders_by_strategy(db: AsyncSession, strategy_id: uuid.UUID) -> List[Order]:
    """Get all orders for a strategy"""
    result = await db.execute(select(Order).where(Order.strategy_id == strategy_id))
    return result.scalars().all()

async def create_order(
    db: AsyncSession,
    mt5_account_id: uuid.UUID,
    mt5_order_id: int,
    symbol: str,
    order_type: str,
    volume: float,
    price: float,
    status: str,
    stop_loss: Optional[float] = None,
    take_profit: Optional[float] = None,
    strategy_id: Optional[uuid.UUID] = None
) -> Order:
    """Create a new order"""
    db_order = Order(
        mt5_account_id=mt5_account_id,
        mt5_order_id=mt5_order_id,
        symbol=symbol,
        order_type=order_type,
        volume=volume,
        price=price,
        status=status,
        stop_loss=stop_loss,
        take_profit=take_profit,
        strategy_id=strategy_id,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    db.add(db_order)
    await db.commit()
    await db.refresh(db_order)
    return db_order

async def update_order(
    db: AsyncSession,
    order_id: uuid.UUID,
    order_data: Dict[str, Any]
) -> Optional[Order]:
    """Update order"""
    # Add updated_at timestamp
    order_data["updated_at"] = datetime.utcnow()
    
    # Update the order
    await db.execute(
        update(Order)
        .where(Order.id == order_id)
        .values(**order_data)
    )
    await db.commit()
    
    # Return the updated order
    return await get_order_by_id(db, order_id)

async def update_order_status(
    db: AsyncSession,
    order_id: uuid.UUID,
    status: str
) -> Optional[Order]:
    """Update order status"""
    order = await get_order_by_id(db, order_id)
    if not order:
        return None
    
    order.status = status
    order.updated_at = datetime.utcnow()
    
    await db.commit()
    await db.refresh(order)
    return order