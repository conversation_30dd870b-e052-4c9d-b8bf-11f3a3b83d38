import { Response, NextFunction } from 'express';
import { AuthService } from '../../features/auth/auth.service';
import { AuthenticatedRequest } from '../types/common.types';

export const authMiddleware = (authService: AuthService) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const authHeader = req.headers.authorization;

      if (!authHeader) {
        res.status(401).json({
          success: false,
          error: {
            code: 'AUTHENTICATION_ERROR',
            message: 'Authentication token required',
          },
        });
        return;
      }

      const token = extractTokenFromHeader(authHeader);
      if (!token) {
        res.status(401).json({
          success: false,
          error: {
            code: 'AUTHENTICATION_ERROR',
            message: 'Invalid token format',
          },
        });
        return;
      }

      const result = await authService.verifyToken(token);

      if (result.success) {
        req.user = result.data;
        next();
      } else {
        res.status(401).json({
          success: false,
          error: result.error,
        });
      }
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Token verification failed',
        },
      });
    }
  };
};

function extractTokenFromHeader(authHeader: string): string | null {
  const trimmedHeader = authHeader.trim();
  const parts = trimmedHeader.split(' ');
  
  if (parts.length !== 2 || parts[0] !== 'Bearer') {
    return null;
  }
  
  const token = parts[1].trim();
  return token.length > 0 ? token : null;
}