"""
Latency Benchmarks

Comprehensive latency testing for all system components with
detailed performance profiling and benchmarking.
"""

import pytest
import time
import statistics
import sys
import os
import cProfile
import pstats
import io
from typing import List, Dict, Any, Callable
from contextlib import contextmanager

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

from services.darwin_godel.strategy_verifier import DarwinGodelVerifier
from services.darwin_godel.secure_executor import SecureStrategyExecutor
from services.darwin_godel.pattern_detector import StrategyPatternDetector

class LatencyProfiler:
    """Utility class for detailed latency profiling"""
    
    def __init__(self):
        self.measurements = []
    
    @contextmanager
    def measure(self, operation_name: str):
        """Context manager for measuring operation latency"""
        start_time = time.perf_counter()
        try:
            yield
        finally:
            end_time = time.perf_counter()
            latency = (end_time - start_time) * 1000  # Convert to milliseconds
            self.measurements.append({
                'operation': operation_name,
                'latency_ms': latency,
                'timestamp': time.time()
            })
    
    def get_stats(self, operation_name: str = None) -> Dict[str, float]:
        """Get latency statistics for an operation"""
        if operation_name:
            latencies = [m['latency_ms'] for m in self.measurements if m['operation'] == operation_name]
        else:
            latencies = [m['latency_ms'] for m in self.measurements]
        
        if not latencies:
            return {}
        
        return {
            'count': len(latencies),
            'mean': statistics.mean(latencies),
            'median': statistics.median(latencies),
            'min': min(latencies),
            'max': max(latencies),
            'std_dev': statistics.stdev(latencies) if len(latencies) > 1 else 0,
            'p95': statistics.quantiles(latencies, n=20)[18] if len(latencies) >= 20 else max(latencies),
            'p99': statistics.quantiles(latencies, n=100)[98] if len(latencies) >= 100 else max(latencies)
        }
    
    def reset(self):
        """Reset all measurements"""
        self.measurements = []

class TestLatencyBenchmarks:
    """Comprehensive latency benchmarking tests"""
    
    def setup_method(self):
        """Setup before each test"""
        self.verifier = DarwinGodelVerifier()
        self.executor = SecureStrategyExecutor()
        self.pattern_detector = StrategyPatternDetector()
        self.profiler = LatencyProfiler()
        
        # Benchmark strategies
        self.benchmark_strategies = {
            'ultra_simple': """
def trading_strategy(data, params):
    return {'signal': 'buy', 'confidence': 0.8}
""",
            'simple_logic': """
def trading_strategy(data, params):
    if len(data['close']) < 2:
        return {'signal': 'hold', 'confidence': 0.5}
    
    if data['close'][-1] > data['close'][-2]:
        return {'signal': 'buy', 'confidence': 0.7}
    else:
        return {'signal': 'sell', 'confidence': 0.7}
""",
            'moving_average': """
def trading_strategy(data, params):
    if len(data['close']) < 20:
        return {'signal': 'hold', 'confidence': 0.5}
    
    sma = sum(data['close'][-20:]) / 20
    current_price = data['close'][-1]
    
    if current_price > sma * 1.02:
        return {'signal': 'buy', 'confidence': 0.8}
    elif current_price < sma * 0.98:
        return {'signal': 'sell', 'confidence': 0.8}
    else:
        return {'signal': 'hold', 'confidence': 0.4}
""",
            'multi_indicator': """
def trading_strategy(data, params):
    if len(data['close']) < 50:
        return {'signal': 'hold', 'confidence': 0.5}
    
    prices = data['close']
    
    # Multiple moving averages
    sma_5 = sum(prices[-5:]) / 5
    sma_20 = sum(prices[-20:]) / 20
    sma_50 = sum(prices[-50:]) / 50
    
    # RSI calculation
    gains = []
    losses = []
    for i in range(1, min(15, len(prices))):
        change = prices[-i] - prices[-i-1]
        if change > 0:
            gains.append(change)
        else:
            losses.append(abs(change))
    
    avg_gain = sum(gains) / len(gains) if gains else 0
    avg_loss = sum(losses) / len(losses) if losses else 1
    rsi = 100 - (100 / (1 + avg_gain / avg_loss))
    
    # Decision logic
    if sma_5 > sma_20 > sma_50 and rsi < 70:
        return {'signal': 'buy', 'confidence': 0.9}
    elif sma_5 < sma_20 < sma_50 and rsi > 30:
        return {'signal': 'sell', 'confidence': 0.9}
    else:
        return {'signal': 'hold', 'confidence': 0.5}
"""
        }
        
        # Test data of different sizes
        self.test_data_sizes = {
            'tiny': self._generate_market_data(10),
            'small': self._generate_market_data(100),
            'medium': self._generate_market_data(1000),
            'large': self._generate_market_data(5000)
        }
    
    def _generate_market_data(self, size: int) -> Dict[str, List[float]]:
        """Generate market data for testing"""
        import random
        
        base_price = 100.0
        prices = [base_price]
        
        for i in range(1, size):
            change = random.uniform(-0.01, 0.01)  # ±1% change
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)
        
        return {
            'close': prices,
            'high': [p * 1.005 for p in prices],
            'low': [p * 0.995 for p in prices],
            'volume': [10000 + random.randint(-1000, 1000) for _ in range(size)]
        }
    
    @pytest.mark.performance
    def test_strategy_execution_latency_benchmark(self):
        """Benchmark strategy execution latency across different complexities"""
        results = {}
        
        for strategy_name, strategy_code in self.benchmark_strategies.items():
            self.profiler.reset()
            
            # Warm up
            for _ in range(5):
                self.executor.execute_strategy(strategy_code, self.test_data_sizes['medium'], {})
            
            # Benchmark
            for i in range(100):
                with self.profiler.measure(f'execute_{strategy_name}'):
                    result = self.executor.execute_strategy(strategy_code, self.test_data_sizes['medium'], {})
                    assert 'signal' in result
            
            results[strategy_name] = self.profiler.get_stats(f'execute_{strategy_name}')
        
        print(f"\n=== Strategy Execution Latency Benchmark ===")
        print(f"{'Strategy':<15} {'Mean':<8} {'P95':<8} {'P99':<8} {'Min':<8} {'Max':<8}")
        print("-" * 65)
        
        for strategy_name, stats in results.items():
            print(f"{strategy_name:<15} {stats['mean']:<8.2f} {stats['p95']:<8.2f} "
                  f"{stats['p99']:<8.2f} {stats['min']:<8.2f} {stats['max']:<8.2f}")
        
        # Performance requirements
        for strategy_name, stats in results.items():
            if strategy_name == 'ultra_simple':
                assert stats['mean'] < 5.0, f"Ultra simple strategy too slow: {stats['mean']:.2f}ms"
                assert stats['p95'] < 10.0, f"Ultra simple P95 too slow: {stats['p95']:.2f}ms"
            elif strategy_name == 'simple_logic':
                assert stats['mean'] < 10.0, f"Simple logic strategy too slow: {stats['mean']:.2f}ms"
                assert stats['p95'] < 20.0, f"Simple logic P95 too slow: {stats['p95']:.2f}ms"
            elif strategy_name == 'moving_average':
                assert stats['mean'] < 20.0, f"Moving average strategy too slow: {stats['mean']:.2f}ms"
                assert stats['p95'] < 40.0, f"Moving average P95 too slow: {stats['p95']:.2f}ms"
            elif strategy_name == 'multi_indicator':
                assert stats['mean'] < 50.0, f"Multi-indicator strategy too slow: {stats['mean']:.2f}ms"
                assert stats['p95'] < 100.0, f"Multi-indicator P95 too slow: {stats['p95']:.2f}ms"
    
    @pytest.mark.performance
    def test_data_size_latency_scaling(self):
        """Test how latency scales with data size"""
        strategy_code = self.benchmark_strategies['moving_average']
        results = {}
        
        for size_name, data in self.test_data_sizes.items():
            self.profiler.reset()
            
            # Warm up
            for _ in range(3):
                self.executor.execute_strategy(strategy_code, data, {})
            
            # Benchmark
            for i in range(50):
                with self.profiler.measure(f'size_{size_name}'):
                    result = self.executor.execute_strategy(strategy_code, data, {})
                    assert 'signal' in result
            
            stats = self.profiler.get_stats(f'size_{size_name}')
            results[size_name] = {
                'data_points': len(data['close']),
                'stats': stats,
                'latency_per_point': stats['mean'] / len(data['close']) * 1000  # microseconds per data point
            }
        
        print(f"\n=== Data Size Latency Scaling ===")
        print(f"{'Size':<8} {'Points':<8} {'Mean (ms)':<12} {'P95 (ms)':<12} {'μs/Point':<12}")
        print("-" * 60)
        
        for size_name, result in results.items():
            stats = result['stats']
            print(f"{size_name:<8} {result['data_points']:<8} {stats['mean']:<12.2f} "
                  f"{stats['p95']:<12.2f} {result['latency_per_point']:<12.4f}")
        
        # Latency should scale sub-linearly with data size
        tiny_latency = results['tiny']['stats']['mean']
        large_latency = results['large']['stats']['mean']
        
        data_scaling = results['large']['data_points'] / results['tiny']['data_points']
        latency_scaling = large_latency / tiny_latency
        
        print(f"\nScaling analysis:")
        print(f"Data scaling: {data_scaling:.1f}x")
        print(f"Latency scaling: {latency_scaling:.1f}x")
        print(f"Efficiency ratio: {latency_scaling / data_scaling:.2f}")
        
        # Latency should not scale linearly with data size
        assert latency_scaling < data_scaling * 0.8, f"Poor scaling: {latency_scaling:.1f}x latency for {data_scaling:.1f}x data"
    
    @pytest.mark.performance
    def test_pattern_detection_latency(self):
        """Benchmark pattern detection latency"""
        results = {}
        
        for strategy_name, strategy_code in self.benchmark_strategies.items():
            self.profiler.reset()
            
            # Warm up
            for _ in range(3):
                self.pattern_detector.analyze_strategy(strategy_code)
            
            # Benchmark
            for i in range(50):
                with self.profiler.measure(f'pattern_{strategy_name}'):
                    result = self.pattern_detector.analyze_strategy(strategy_code)
                    assert 'pattern_type' in result
            
            results[strategy_name] = self.profiler.get_stats(f'pattern_{strategy_name}')
        
        print(f"\n=== Pattern Detection Latency Benchmark ===")
        print(f"{'Strategy':<15} {'Mean':<8} {'P95':<8} {'P99':<8} {'Min':<8} {'Max':<8}")
        print("-" * 65)
        
        for strategy_name, stats in results.items():
            print(f"{strategy_name:<15} {stats['mean']:<8.2f} {stats['p95']:<8.2f} "
                  f"{stats['p99']:<8.2f} {stats['min']:<8.2f} {stats['max']:<8.2f}")
        
        # Pattern detection should be fast
        for strategy_name, stats in results.items():
            assert stats['mean'] < 100.0, f"Pattern detection too slow for {strategy_name}: {stats['mean']:.2f}ms"
            assert stats['p95'] < 200.0, f"Pattern detection P95 too slow for {strategy_name}: {stats['p95']:.2f}ms"
    
    @pytest.mark.performance
    def test_strategy_verification_latency(self):
        """Benchmark complete strategy verification latency"""
        results = {}
        
        for strategy_name, strategy_code in self.benchmark_strategies.items():
            self.profiler.reset()
            
            # Warm up
            for _ in range(2):
                self.verifier.verify_strategy(strategy_code)
            
            # Benchmark
            for i in range(20):  # Fewer iterations as verification is more expensive
                with self.profiler.measure(f'verify_{strategy_name}'):
                    result = self.verifier.verify_strategy(strategy_code)
                    assert 'is_valid' in result
            
            results[strategy_name] = self.profiler.get_stats(f'verify_{strategy_name}')
        
        print(f"\n=== Strategy Verification Latency Benchmark ===")
        print(f"{'Strategy':<15} {'Mean':<8} {'P95':<8} {'P99':<8} {'Min':<8} {'Max':<8}")
        print("-" * 65)
        
        for strategy_name, stats in results.items():
            print(f"{strategy_name:<15} {stats['mean']:<8.2f} {stats['p95']:<8.2f} "
                  f"{stats['p99']:<8.2f} {stats['min']:<8.2f} {stats['max']:<8.2f}")
        
        # Verification should complete within reasonable time
        for strategy_name, stats in results.items():
            assert stats['mean'] < 500.0, f"Verification too slow for {strategy_name}: {stats['mean']:.2f}ms"
            assert stats['p95'] < 1000.0, f"Verification P95 too slow for {strategy_name}: {stats['p95']:.2f}ms"
    
    @pytest.mark.performance
    def test_cold_start_vs_warm_latency(self):
        """Compare cold start vs warm execution latency"""
        strategy_code = self.benchmark_strategies['moving_average']
        data = self.test_data_sizes['medium']
        
        # Cold start measurements
        cold_start_times = []
        for _ in range(10):
            # Create fresh executor for each cold start
            fresh_executor = SecureStrategyExecutor()
            
            start_time = time.perf_counter()
            result = fresh_executor.execute_strategy(strategy_code, data, {})
            end_time = time.perf_counter()
            
            cold_start_times.append((end_time - start_time) * 1000)
            assert 'signal' in result
        
        # Warm execution measurements
        warm_times = []
        for _ in range(10):
            start_time = time.perf_counter()
            result = self.executor.execute_strategy(strategy_code, data, {})
            end_time = time.perf_counter()
            
            warm_times.append((end_time - start_time) * 1000)
            assert 'signal' in result
        
        cold_stats = {
            'mean': statistics.mean(cold_start_times),
            'median': statistics.median(cold_start_times),
            'min': min(cold_start_times),
            'max': max(cold_start_times)
        }
        
        warm_stats = {
            'mean': statistics.mean(warm_times),
            'median': statistics.median(warm_times),
            'min': min(warm_times),
            'max': max(warm_times)
        }
        
        print(f"\n=== Cold Start vs Warm Execution ===")
        print(f"{'Metric':<10} {'Cold Start':<12} {'Warm':<12} {'Ratio':<8}")
        print("-" * 45)
        
        for metric in ['mean', 'median', 'min', 'max']:
            ratio = cold_stats[metric] / warm_stats[metric] if warm_stats[metric] > 0 else 1
            print(f"{metric:<10} {cold_stats[metric]:<12.2f} {warm_stats[metric]:<12.2f} {ratio:<8.2f}")
        
        # Cold start should not be excessively slower than warm
        cold_warm_ratio = cold_stats['mean'] / warm_stats['mean']
        assert cold_warm_ratio < 5.0, f"Cold start too slow: {cold_warm_ratio:.2f}x slower than warm"
    
    @pytest.mark.performance
    def test_profiling_hotspots(self):
        """Profile execution to identify performance hotspots"""
        strategy_code = self.benchmark_strategies['multi_indicator']
        data = self.test_data_sizes['medium']
        
        # Profile execution
        profiler = cProfile.Profile()
        profiler.enable()
        
        # Execute strategy multiple times
        for _ in range(20):
            result = self.executor.execute_strategy(strategy_code, data, {})
            assert 'signal' in result
        
        profiler.disable()
        
        # Analyze profiling results
        s = io.StringIO()
        ps = pstats.Stats(profiler, stream=s)
        ps.sort_stats('cumulative')
        ps.print_stats(20)  # Top 20 functions
        
        profiling_output = s.getvalue()
        
        print(f"\n=== Performance Profiling Hotspots ===")
        print(profiling_output)
        
        # Extract key metrics from profiling
        lines = profiling_output.split('\n')
        function_stats = []
        
        for line in lines:
            if 'function calls' in line:
                continue
            if line.strip() and not line.startswith(' ') and '/' in line:
                parts = line.split()
                if len(parts) >= 6:
                    try:
                        cumtime = float(parts[3])
                        function_name = parts[-1]
                        function_stats.append((cumtime, function_name))
                    except (ValueError, IndexError):
                        continue
        
        # Verify no single function dominates execution time
        if function_stats:
            function_stats.sort(reverse=True)
            top_function_time = function_stats[0][0]
            total_time = sum(stat[0] for stat in function_stats[:10])  # Top 10 functions
            
            if total_time > 0:
                dominance_ratio = top_function_time / total_time
                print(f"\nTop function dominance: {dominance_ratio:.2%}")
                
                # No single function should dominate more than 50% of execution time
                assert dominance_ratio < 0.5, f"Single function dominates execution: {dominance_ratio:.2%}"
    
    @pytest.mark.performance
    def test_latency_consistency(self):
        """Test latency consistency and identify outliers"""
        strategy_code = self.benchmark_strategies['moving_average']
        data = self.test_data_sizes['medium']
        
        # Collect many measurements
        latencies = []
        for _ in range(200):
            start_time = time.perf_counter()
            result = self.executor.execute_strategy(strategy_code, data, {})
            end_time = time.perf_counter()
            
            latency = (end_time - start_time) * 1000
            latencies.append(latency)
            assert 'signal' in result
        
        # Statistical analysis
        mean_latency = statistics.mean(latencies)
        median_latency = statistics.median(latencies)
        std_dev = statistics.stdev(latencies)
        
        # Identify outliers (values > 2 standard deviations from mean)
        outliers = [l for l in latencies if abs(l - mean_latency) > 2 * std_dev]
        outlier_percentage = len(outliers) / len(latencies) * 100
        
        # Calculate percentiles
        percentiles = {
            'p50': statistics.median(latencies),
            'p90': statistics.quantiles(latencies, n=10)[8],
            'p95': statistics.quantiles(latencies, n=20)[18],
            'p99': statistics.quantiles(latencies, n=100)[98],
            'p99.9': statistics.quantiles(latencies, n=1000)[998]
        }
        
        print(f"\n=== Latency Consistency Analysis ===")
        print(f"Measurements: {len(latencies)}")
        print(f"Mean: {mean_latency:.2f}ms")
        print(f"Median: {median_latency:.2f}ms")
        print(f"Std Dev: {std_dev:.2f}ms")
        print(f"Coefficient of Variation: {std_dev / mean_latency:.2%}")
        print(f"Outliers: {len(outliers)} ({outlier_percentage:.1f}%)")
        print(f"Min: {min(latencies):.2f}ms")
        print(f"Max: {max(latencies):.2f}ms")
        
        print(f"\nPercentiles:")
        for percentile, value in percentiles.items():
            print(f"  {percentile}: {value:.2f}ms")
        
        # Consistency requirements
        cv = std_dev / mean_latency  # Coefficient of variation
        assert cv < 0.5, f"High latency variability: CV = {cv:.2%}"
        assert outlier_percentage < 5.0, f"Too many outliers: {outlier_percentage:.1f}%"
        assert percentiles['p99'] < mean_latency * 3, f"P99 too high: {percentiles['p99']:.2f}ms vs mean {mean_latency:.2f}ms"
    
    @pytest.mark.performance
    def test_latency_under_load(self):
        """Test latency degradation under concurrent load"""
        strategy_code = self.benchmark_strategies['moving_average']
        data = self.test_data_sizes['medium']
        
        from concurrent.futures import ThreadPoolExecutor, as_completed
        import threading
        
        def measure_latency_worker(num_executions: int) -> List[float]:
            """Worker function to measure latencies"""
            latencies = []
            for _ in range(num_executions):
                start_time = time.perf_counter()
                result = self.executor.execute_strategy(strategy_code, data, {})
                end_time = time.perf_counter()
                
                latency = (end_time - start_time) * 1000
                latencies.append(latency)
                assert 'signal' in result
            
            return latencies
        
        load_levels = [1, 2, 4, 8]  # Different numbers of concurrent threads
        results_by_load = {}
        
        for num_threads in load_levels:
            executions_per_thread = 25
            
            with ThreadPoolExecutor(max_workers=num_threads) as executor:
                futures = [
                    executor.submit(measure_latency_worker, executions_per_thread)
                    for _ in range(num_threads)
                ]
                
                all_latencies = []
                for future in as_completed(futures):
                    all_latencies.extend(future.result())
            
            # Calculate statistics for this load level
            results_by_load[num_threads] = {
                'mean': statistics.mean(all_latencies),
                'p95': statistics.quantiles(all_latencies, n=20)[18] if len(all_latencies) >= 20 else max(all_latencies),
                'p99': statistics.quantiles(all_latencies, n=100)[98] if len(all_latencies) >= 100 else max(all_latencies),
                'std_dev': statistics.stdev(all_latencies),
                'count': len(all_latencies)
            }
        
        print(f"\n=== Latency Under Load ===")
        print(f"{'Threads':<8} {'Mean':<8} {'P95':<8} {'P99':<8} {'Std Dev':<8} {'Count':<8}")
        print("-" * 55)
        
        for threads, stats in results_by_load.items():
            print(f"{threads:<8} {stats['mean']:<8.2f} {stats['p95']:<8.2f} "
                  f"{stats['p99']:<8.2f} {stats['std_dev']:<8.2f} {stats['count']:<8}")
        
        # Latency should not degrade excessively under load
        baseline_mean = results_by_load[1]['mean']
        high_load_mean = results_by_load[8]['mean']
        
        degradation_factor = high_load_mean / baseline_mean
        print(f"\nLatency degradation under 8x load: {degradation_factor:.2f}x")
        
        assert degradation_factor < 3.0, f"Excessive latency degradation: {degradation_factor:.2f}x under load"