import { env, databaseConfig, corsConfig, rateLimitConfig } from './config';

describe('Configuration', () => {
  describe('Environment Variables', () => {
    test('should have valid NODE_ENV', () => {
      expect(['development', 'production', 'test']).toContain(env.NODE_ENV);
    });

    test('should have valid PORT', () => {
      expect(env.PORT).toBeGreaterThan(0);
      expect(env.PORT).toBeLessThanOrEqual(65535);
    });

    test('should have required database configuration', () => {
      expect(env.DB_HOST).toBeDefined();
      expect(env.DB_PORT).toBeGreaterThan(0);
      expect(env.DB_NAME).toBeDefined();
      expect(env.DB_USERNAME).toBeDefined();
      expect(env.DB_PASSWORD).toBeDefined();
    });

    test('should have valid JWT secret', () => {
      expect(env.JWT_SECRET).toBeDefined();
      expect(env.JWT_SECRET.length).toBeGreaterThanOrEqual(32);
    });

    test('should have valid BCRYPT rounds', () => {
      expect(env.BCRYPT_ROUNDS).toBeGreaterThanOrEqual(4);
      expect(env.BCRYPT_ROUNDS).toBeLessThanOrEqual(15);
    });
  });

  describe('Database Configuration', () => {
    test('should have valid database config', () => {
      expect(databaseConfig.client).toBe('postgresql');
      expect(databaseConfig.connection.host).toBeDefined();
      expect(databaseConfig.connection.port).toBeGreaterThan(0);
      expect(databaseConfig.connection.database).toBeDefined();
      expect(databaseConfig.connection.user).toBeDefined();
      expect(databaseConfig.connection.password).toBeDefined();
    });

    test('should have valid pool configuration', () => {
      expect(databaseConfig.pool.min).toBeGreaterThanOrEqual(1);
      expect(databaseConfig.pool.max).toBeGreaterThan(databaseConfig.pool.min);
    });
  });

  describe('CORS Configuration', () => {
    test('should have valid CORS origins', () => {
      expect(Array.isArray(corsConfig.origin)).toBe(true);
      expect(corsConfig.origin.length).toBeGreaterThan(0);
    });

    test('should include required methods', () => {
      const requiredMethods = ['GET', 'POST', 'PUT', 'DELETE'];
      requiredMethods.forEach(method => {
        expect(corsConfig.methods).toContain(method);
      });
    });
  });

  describe('Rate Limiting Configuration', () => {
    test('should have valid rate limit values', () => {
      expect(rateLimitConfig.windowMs).toBeGreaterThan(0);
      expect(rateLimitConfig.max).toBeGreaterThan(0);
    });
  });

  describe('Configuration in Test Environment', () => {
    test('should use test-specific values when NODE_ENV is test', () => {
      if (env.NODE_ENV === 'test') {
        expect(env.LOG_LEVEL).toBe('error');
        expect(env.BCRYPT_ROUNDS).toBeLessThanOrEqual(4); // Faster hashing for tests
      }
    });
  });
});