[tool:pytest]
minversion = 6.0
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --tb=short
    --strict-markers
    --strict-config
    --cov=src
    --cov-branch
    --cov-report=term-missing:skip-covered
    --cov-report=html:htmlcov
    --cov-report=xml
    --cov-fail-under=80
    --hypothesis-show-statistics
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
    ml: Machine learning tests
    api: API tests
    database: Database tests
    hypothesis: Property-based tests using Hypothesis
    regression: Regression tests for critical functionality
    benchmark: Performance benchmark tests
    security: Security-related tests
    chatbot: Chatbot functionality tests
    trading: Trading platform tests
    tdd: Test-driven development tests
    property: Property-based testing
    fuzz: Fuzzing tests
    stress: Stress testing
    concurrent: Concurrent/parallel testing
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning
    error::hypothesis.errors.HypothesisDeprecationWarning