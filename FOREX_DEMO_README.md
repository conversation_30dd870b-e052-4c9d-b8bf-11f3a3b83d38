# 🚀 AI Forex Trading Platform - Demo Guide

## Quick Start

### 1. Install Dependencies
```bash
# All dependencies are already installed!
npm install
```

### 2. Run the Demo
```bash
# Start the complete demo (API + UI)
npm run demo

# Or start components separately:
npm run demo:api    # API server on port 3001
npm run demo:ui     # UI server on port 3000
```

### 3. Access the Demo
- **Basic UI**: http://localhost:3000
- **Enhanced UI with WebSocket**: http://localhost:3000/enhanced.html
- **API Health**: http://localhost:3001/health
- **Metrics**: http://localhost:3001/metrics

## 🎯 Demo Features

### ✅ Real-time Forex Trading
- Live price updates via WebSocket (500ms intervals)
- Major pairs: EUR/USD, GBP/USD, USD/JPY, USD/CHF, AUD/USD, USD/CAD
- Cross pairs: EUR/GBP, EUR/JPY, GBP/JPY
- Realistic bid/ask spreads and pip movements

### ✅ Trading Features
- **Lot Sizes**: Micro (0.01), Mini (0.1), Standard (1.0)
- **Leverage**: 1:50, 1:100, 1:200, 1:500
- **Order Types**: Market orders (limit orders ready for extension)
- **Pip Value Calculation**: Automatic calculation based on pair and lot size
- **Margin Requirements**: Real-time margin calculation

### ✅ Portfolio Management
- Account balance and equity tracking
- Open positions with real-time P&L
- Used/Free margin monitoring
- Margin level percentage
- Pending orders display

### ✅ AI Integration Points
- Simulated AI trading signals
- Market event notifications
- Risk management indicators
- Performance metrics collection

### ✅ Technical Features
- **WebSocket Server**: Real-time price streaming on port 8080
- **Event-Driven Architecture**: EventBus for trade events
- **Metrics Collection**: Prometheus-compatible metrics
- **Health Monitoring**: System health endpoints
- **TDD Infrastructure**: Complete test suite

## 🔧 API Endpoints

### Forex Data
- `GET /api/forex/:pair` - Get current price for currency pair
- `POST /api/forex/trade` - Execute a trade
- `GET /api/forex/portfolio` - Get account portfolio

### System
- `GET /health` - Health check
- `GET /metrics` - Prometheus metrics

## 🌐 WebSocket Events

### Connection
```javascript
const ws = new WebSocket('ws://localhost:8080');
ws.send(JSON.stringify({ action: 'subscribe' }));
```

### Market Data Updates
```json
{
  "type": "market_data",
  "updates": [
    {
      "pair": "EUR/USD",
      "bid": 1.0856,
      "ask": 1.0858,
      "spread": 2,
      "timestamp": "2024-01-07T..."
    }
  ]
}
```

### Market Events
```json
{
  "type": "market_event",
  "eventType": "news",
  "severity": "high",
  "message": "ECB announces interest rate decision",
  "timestamp": "2024-01-07T..."
}
```

## 🐳 Docker Deployment

### Run with Docker Compose
```bash
# Start forex demo services
npm run demo:docker

# Or manually:
docker-compose up forex-demo forex-ui
```

### Access Points (Docker)
- **Demo UI**: http://localhost:3003
- **Demo API**: http://localhost:3002
- **WebSocket**: ws://localhost:8080

## 📊 Monitoring

### Prometheus Metrics
```bash
# Start monitoring stack
docker-compose --profile monitoring up

# Access Prometheus
open http://localhost:9090
```

### Available Metrics
- `api_requests_total` - API request counter
- `forex_trades_total` - Trade execution counter
- `forex_trade_size` - Trade size histogram
- `trade_latency` - Trade execution latency

## 🎮 Demo Interactions

### 1. **Select Currency Pair**
Click on any currency pair card to select it for trading

### 2. **Monitor Real-time Prices**
Watch live bid/ask prices update every 500ms via WebSocket

### 3. **Execute Trades**
- Choose lot size and leverage
- Click BUY (green) or SELL (red) buttons
- See execution confirmation with details

### 4. **Track Performance**
- Monitor open positions in portfolio panel
- Watch real-time P&L updates
- Check margin usage and account equity

### 5. **AI Signals**
- View simulated AI trading signals
- Monitor market event notifications
- Track system performance metrics

## 🔧 Customization

### Add Currency Pairs
Edit `src/demo/mock-data.ts`:
```typescript
export const mockForexData = {
  'USD/ZAR': { bid: 18.9234, ask: 18.9334, spread: 10 },
  'EUR/TRY': { bid: 32.1234, ask: 32.1434, spread: 20 },
  // Add more pairs...
};
```

### Adjust Update Frequency
In `src/infrastructure/websocket-manager.ts`:
```typescript
// Change update interval (default: 500ms)
this.priceUpdateInterval = setInterval(() => {
  // Price update logic
}, 1000); // 1 second updates
```

### Connect Real Data
Replace mock data with real forex API:
```typescript
// In src/demo/app.ts
const realPrice = await forexAPI.getPrice(pair);
```

## 🚀 Next Steps

### 1. **Real Market Data**
- Integrate with OANDA, FXCM, or other forex providers
- Add real-time news feeds
- Implement economic calendar

### 2. **AI Enhancement**
- Connect Python ML models for signal generation
- Add backtesting capabilities
- Implement risk management algorithms

### 3. **Production Features**
- User authentication and accounts
- Real money trading integration
- Advanced order types (stop loss, take profit)
- Mobile app development

### 4. **Scaling**
- Database integration for trade history
- Redis for session management
- Load balancing for high availability

## 🎉 Demo Summary

This forex trading demo showcases:

✅ **Professional Trading Interface** - Modern, responsive UI with real-time updates  
✅ **WebSocket Integration** - Live price streaming and market events  
✅ **Complete Trading Flow** - From price display to trade execution  
✅ **Portfolio Management** - Real-time account tracking  
✅ **AI Integration Points** - Ready for ML model integration  
✅ **Production-Ready Architecture** - Event-driven, testable, scalable  
✅ **Docker Deployment** - Containerized for easy deployment  
✅ **Monitoring & Metrics** - Prometheus integration for observability  

The demo provides a solid foundation for building a production forex trading platform with AI capabilities!

## 🆘 Troubleshooting

### WebSocket Connection Issues
```bash
# Check if port 8080 is available
netstat -an | findstr :8080

# If needed, change port in src/infrastructure/websocket-manager.ts
```

### CORS Errors
Ensure the API server is running on port 3001 and CORS is properly configured.

### Price Updates Not Showing
1. Check browser console for WebSocket errors
2. Verify WebSocket server is running
3. Try refreshing the page

---

**Happy Trading! 📈💰**