# AI Trading Platform - Test Execution Guide

## 🚀 Quick Start

### Complete Test Suite
```bash
# Run complete test suite
python run_tests.py

# Or use platform-specific scripts
./run_tests.sh      # Unix/Linux/Mac
run_tests.bat       # Windows
```

### Specific Test Categories
```bash
# Run only critical tests
python run_tests.py critical

# Run coverage analysis
python run_tests.py coverage

# Run performance tests
python run_tests.py performance

# Run security tests
python run_tests.py security

# Check dependencies
python run_tests.py deps

# Run emergency TDD validation
python run_tests.py validate
```

## 📋 Test Categories

### 🧪 Critical Tests
- **Strategy Executor**: Core trading strategy execution logic
- **Risk Management**: Position sizing, portfolio risk, stop losses
- **Portfolio Manager**: Portfolio tracking, P&L calculation, rebalancing
- **Market Data Processor**: Real-time data processing and validation
- **Security Tests**: Malicious strategy injection prevention

### ⚡ Performance Tests
- High-frequency data processing (10K+ items/sec)
- Concurrent strategy execution
- Memory management and cleanup
- Cache performance optimization
- Batch processing efficiency

### 🛡️ Security Tests
- Code injection detection
- File system access prevention
- Network access prevention
- Dangerous import detection
- Strategy object validation
- Runtime security validation

## 📊 Expected Results

### ✅ Success Criteria
- **Critical Tests**: 49/49 passing
- **Coverage**: 70%+ (target: 90%+)
- **Performance**: All benchmarks met
- **Security**: Zero vulnerabilities detected
- **Emergency TDD**: Full validation passed

### 📈 Test Metrics
```
Strategy Executor:     6/6 tests passing
Risk Management:      11/11 tests passing
Portfolio Manager:    11/11 tests passing
Market Data Processor: 9/9 tests passing
Security Tests:       12/12 tests passing
```

## 🔧 Troubleshooting

### Common Issues

#### Missing Dependencies
```bash
# Check and install dependencies
python run_tests.py deps

# Manual installation
pip install pytest pytest-cov pytest-asyncio pytest-mock coverage
```

#### Test Failures
```bash
# Run specific failing test with verbose output
cd python_engine
pytest services/darwin_godel/test_strategy_executor_comprehensive.py -v --tb=long

# Run with debugging
pytest services/darwin_godel/test_risk_management.py -v -s --tb=short
```

#### Coverage Issues
```bash
# Generate detailed coverage report
python run_tests.py coverage

# View HTML coverage report
# Open: python_engine/htmlcov/index.html
```

### Performance Issues
```bash
# Run performance tests only
python run_tests.py performance

# Profile specific test
cd python_engine
pytest services/darwin_godel/test_market_data_processor.py::TestMarketDataProcessorPerformance -v --profile
```

## 📁 File Structure

```
AI Enhanced Trading Platform/
├── run_tests.py              # Main test execution script
├── run_tests.bat             # Windows batch script
├── run_tests.sh              # Unix/Linux/Mac shell script
├── TEST_EXECUTION_GUIDE.md   # This guide
├── validate_emergency_tdd.py # Emergency TDD validation
├── python_engine/
│   ├── services/darwin_godel/
│   │   ├── test_strategy_executor_comprehensive.py
│   │   ├── test_risk_management.py
│   │   ├── test_portfolio_manager.py
│   │   └── test_market_data_processor.py
│   └── tests/security/
│       └── test_malicious_strategy_injection.py
└── shared/
    ├── test_data_pipeline_comprehensive.py
    ├── test_trading_services_comprehensive.py
    ├── test_integration_comprehensive.py
    └── test_ml_models_comprehensive.py
```

## 🎯 Advanced Usage

### Continuous Integration
```bash
# CI/CD pipeline command
python run_tests.py critical && python run_tests.py security
```

### Development Workflow
```bash
# Quick development check
python run_tests.py critical

# Before commit
python run_tests.py coverage

# Before deployment
python run_tests.py
```

### Custom Test Execution
```bash
# Run specific test file
cd python_engine
pytest services/darwin_godel/test_strategy_executor_comprehensive.py -v

# Run specific test method
pytest services/darwin_godel/test_risk_management.py::TestRiskManagementCritical::test_portfolio_risk_limits_enforcement -v

# Run tests matching pattern
pytest services/darwin_godel/ -k "performance" -v

# Run tests with markers
pytest services/darwin_godel/ -m "critical" -v
```

## 📊 Report Generation

### Automated Reports
The test execution script automatically generates:
- **Console Output**: Real-time test results
- **JSON Report**: `test_report_YYYYMMDD_HHMMSS.json`
- **HTML Coverage**: `python_engine/htmlcov/index.html`
- **Coverage JSON**: `python_engine/coverage.json`

### Manual Report Generation
```bash
# Generate coverage report only
cd python_engine
pytest services/darwin_godel/ tests/security/ --cov=core --cov=services --cov-report=html

# Generate performance report
pytest services/darwin_godel/ -k "performance" --benchmark-json=performance_report.json
```

## 🚨 Emergency Procedures

### Critical Test Failure
1. **Immediate Action**: Stop deployment
2. **Identify**: Check test output for specific failures
3. **Isolate**: Run failing test individually with verbose output
4. **Fix**: Address the root cause
5. **Verify**: Re-run complete test suite

### Security Test Failure
1. **CRITICAL**: Immediate security review required
2. **Isolate**: Run security tests only: `python run_tests.py security`
3. **Review**: Check for malicious code injection attempts
4. **Fix**: Address security vulnerabilities immediately
5. **Validate**: Re-run security tests until all pass

### Performance Degradation
1. **Benchmark**: Run performance tests: `python run_tests.py performance`
2. **Profile**: Identify bottlenecks
3. **Optimize**: Address performance issues
4. **Validate**: Ensure performance targets are met

## 🎉 Success Indicators

### ✅ Production Ready Checklist
- [ ] All critical tests passing (49/49)
- [ ] Test coverage > 90%
- [ ] All security tests passing
- [ ] Performance benchmarks met
- [ ] Emergency TDD validation passed
- [ ] No critical vulnerabilities detected
- [ ] Documentation up to date

### 🚀 Deployment Approval
When all tests pass and coverage is adequate:
```
🎉 EMERGENCY TDD IMPLEMENTATION: COMPLETE
🛡️ SECURITY HARDENING: COMPLETE
⚡ PERFORMANCE TESTING: COMPLETE
🚀 PRODUCTION READY: YES
```

## 📞 Support

For issues with test execution:
1. Check this guide first
2. Review test output and error messages
3. Check dependencies: `python run_tests.py deps`
4. Run emergency TDD validation: `python run_tests.py validate`

---

**Last Updated**: 2024-12-19
**Version**: 1.0.0
**Status**: Production Ready