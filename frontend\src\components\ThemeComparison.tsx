import React, { useState } from 'react';
import { Eye, Monitor, Smartphone } from 'lucide-react';
import SinglePageDashboard from './SinglePageDashboard';
import PlatformDashboardClean from './PlatformDashboardClean';

const ThemeComparison: React.FC = () => {
  const [currentTheme, setCurrentTheme] = useState('single-page');

  const themes = [
    {
      id: 'single-page',
      name: 'Single Page Dashboard',
      description: 'Clean single-page layout with professional prompts, AI chat, and all features visible at once',
      features: ['Professional AI Prompts', 'Single-page scrolling', 'AI Strategy Helper', 'Live metrics', 'Strategy management'],
      component: SinglePageDashboard
    },
    {
      id: 'clean-dashboard',
      name: 'Clean Dashboard',
      description: 'Advanced dashboard with comprehensive features and professional layout',
      features: ['Advanced prompts library', 'Multi-section layout', 'Professional design', 'Comprehensive tools'],
      component: PlatformDashboardClean
    }
  ];

  const currentThemeData = themes.find(theme => theme.id === currentTheme) || themes[0];
  const CurrentComponent = currentThemeData.component;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Theme Selector Header */}
      <div className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Eye className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">AI Trading Platform</h1>
                <p className="text-sm text-gray-500">Theme Comparison</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Monitor className="w-4 h-4 text-gray-500" />
                <span className="text-sm text-gray-600">Desktop View</span>
              </div>
              
              <div className="flex bg-gray-100 rounded-lg p-1">
                {themes.map((theme) => (
                  <button
                    key={theme.id}
                    onClick={() => setCurrentTheme(theme.id)}
                    className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                      currentTheme === theme.id
                        ? 'bg-white text-blue-600 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    {theme.name}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Theme Info Banner */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h2 className="text-lg font-semibold text-blue-900 mb-1">
                Current Theme: {currentThemeData.name}
              </h2>
              <p className="text-blue-700 text-sm mb-3">
                {currentThemeData.description}
              </p>
              <div className="flex flex-wrap gap-2">
                {currentThemeData.features.map((feature, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-200 text-blue-800"
                  >
                    {feature}
                  </span>
                ))}
              </div>
            </div>
            <div className="ml-4 text-right">
              <div className="text-sm text-blue-600 font-medium">
                Theme {themes.findIndex(t => t.id === currentTheme) + 1} of {themes.length}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Current Theme Component */}
      <div className="theme-container">
        <CurrentComponent />
      </div>
    </div>
  );
};

export default ThemeComparison;