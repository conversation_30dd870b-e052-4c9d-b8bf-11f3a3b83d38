/* CleanHomepage.css */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.clean-homepage {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
    padding: 15px 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 24px;
    font-weight: 600;
    color: #2563eb;
}

.nav-links {
    display: flex;
    gap: 30px;
}

.nav-links a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s;
}

.nav-links a:hover {
    color: #2563eb;
}

/* Sections */
.section {
    padding: 80px 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding-top: 60px;
}

.hero-content {
    max-width: 800px;
}

.hero h1 {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 20px;
    color: #1e293b;
}

.hero p {
    font-size: 20px;
    color: #64748b;
    margin-bottom: 40px;
}

.btn {
    display: inline-block;
    padding: 15px 30px;
    background: #2563eb;
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 600;
    margin: 10px;
    transition: background 0.3s;
    cursor: pointer;
    border: none;
    font-size: 16px;
}

.btn:hover {
    background: #1d4ed8;
}

.btn-secondary {
    background: transparent;
    border: 2px solid #2563eb;
    color: #2563eb;
}

.btn-secondary:hover {
    background: #2563eb;
    color: white;
}

/* Features */
.features {
    background: white;
}

.features h2, .how-it-works h2, .chatbot-section h2, .mql5-tools h2 {
    text-align: center;
    font-size: 36px;
    margin-bottom: 60px;
    color: #1e293b;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

.feature {
    text-align: center;
    padding: 40px 20px;
    border-radius: 12px;
    background: #f8fafc;
    transition: transform 0.3s;
}

.feature:hover {
    transform: translateY(-5px);
}

.feature-icon {
    font-size: 48px;
    margin-bottom: 20px;
}

.feature h3 {
    font-size: 24px;
    margin-bottom: 15px;
    color: #1e293b;
}

.feature p {
    color: #64748b;
}

/* How it works */
.how-it-works {
    background: #f8fafc;
}

.steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
}

.step {
    text-align: center;
}

.step-number {
    width: 60px;
    height: 60px;
    background: #2563eb;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    margin: 0 auto 20px;
}

.step h3 {
    font-size: 20px;
    margin-bottom: 15px;
    color: #1e293b;
}

.step p {
    color: #64748b;
}

/* Chatbot Section */
.chatbot-section {
    background: white;
    padding-top: 100px;
    padding-bottom: 100px;
}

.chatbot-container {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 30px;
    max-height: 600px;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
}

.prompt-sidebar {
    background: #f8fafc;
    padding: 20px;
    border-right: 1px solid #e2e8f0;
    overflow-y: auto;
}

.prompt-sidebar h3 {
    font-size: 18px;
    margin-bottom: 15px;
    color: #1e293b;
    padding-bottom: 10px;
    border-bottom: 1px solid #e2e8f0;
}

.prompts-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.prompt-item {
    padding: 15px;
    border-radius: 8px;
    background: white;
    cursor: pointer;
    transition: all 0.2s;
    border: 1px solid #e2e8f0;
}

.prompt-item:hover {
    background: #f1f5f9;
    transform: translateY(-2px);
}

.prompt-item.active {
    border-left: 4px solid #2563eb;
    background: #f1f5f9;
}

.prompt-item h4 {
    font-size: 16px;
    margin-bottom: 5px;
    color: #1e293b;
}

.prompt-item p {
    font-size: 13px;
    color: #64748b;
}

.chatbot-wrapper {
    background: white;
    height: 600px;
    overflow-y: auto;
    padding: 20px;
}

.chatbot-placeholder {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 20px;
}

.chatbot-placeholder h3 {
    font-size: 24px;
    margin-bottom: 15px;
    color: #1e293b;
}

.chatbot-placeholder p {
    color: #64748b;
    margin-bottom: 30px;
    max-width: 500px;
}

/* MQL5 Tools Section */
.mql5-tools {
    background: #f8fafc;
    padding-top: 100px;
    padding-bottom: 100px;
}

.mql5-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: center;
}

.mql5-description h3 {
    font-size: 24px;
    margin-bottom: 20px;
    color: #1e293b;
}

.mql5-description p {
    margin-bottom: 20px;
    color: #64748b;
}

.mql5-description ul {
    margin-bottom: 30px;
    margin-left: 20px;
}

.mql5-description ul li {
    margin-bottom: 10px;
    color: #64748b;
}

.code-preview {
    background: #1e293b;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    height: 400px;
    overflow-y: auto;
}

.code-preview pre {
    margin: 0;
}

.code-preview code {
    color: #e2e8f0;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 14px;
    line-height: 1.5;
}

/* Footer */
.footer {
    background: #1e293b;
    color: white;
    text-align: center;
    padding: 40px 20px;
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 20px;
}

.footer-links a {
    color: white;
    text-decoration: none;
}

/* Responsive */
@media (max-width: 768px) {
    .hero h1 {
        font-size: 36px;
    }
    
    .hero p {
        font-size: 18px;
    }
    
    .nav-links {
        display: none;
    }
    
    .feature-grid,
    .steps,
    .chatbot-container,
    .mql5-container {
        grid-template-columns: 1fr;
    }
    
    .chatbot-container {
        max-height: none;
    }
    
    .prompt-sidebar {
        border-right: none;
        border-bottom: 1px solid #e2e8f0;
        max-height: 300px;
    }
}
