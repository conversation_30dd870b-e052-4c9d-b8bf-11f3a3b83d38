#!/bin/bash

# 🧬 Darwin Strategy Verification Platform Launcher
# Hybrid Python Backend + Web Frontend

echo "🧬 Darwin Strategy Verification Platform"
echo "========================================"

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not installed."
    echo "Please install Python 3.8+ and try again."
    exit 1
fi

# Create virtual environment if it doesn't exist
if [ ! -d "darwin_env" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv darwin_env
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source darwin_env/bin/activate

# Create requirements.txt if it doesn't exist
if [ ! -f "requirements.txt" ]; then
    echo "📋 Creating requirements.txt..."
    cat > requirements.txt << EOF
Flask==2.3.3
Flask-CORS==4.0.0
pandas==2.1.0
numpy==1.24.3
yfinance==0.2.21
python-dateutil==2.8.2
requests==2.31.0
EOF
fi

# Install dependencies
echo "📥 Installing Python dependencies..."
pip install -r requirements.txt

# Check if backend file exists
if [ ! -f "darwin_backend.py" ]; then
    echo "❌ darwin_backend.py not found!"
    echo "Please save the Python backend code as 'darwin_backend.py'"
    exit 1
fi

# Check if frontend file exists
if [ ! -f "darwin_platform.html" ]; then
    echo "❌ darwin_platform.html not found!"
    echo "Please save the web frontend code as 'darwin_platform.html'"
    exit 1
fi

# Start the backend
echo "🚀 Starting Darwin Backend..."
echo "Backend will be available at: http://localhost:5000"
echo "Frontend will be available at: darwin_platform.html"
echo ""
echo "🔧 Backend Status:"
echo "- Real market data: ✅ Enabled"
echo "- Professional backtesting: ✅ Ready"
echo "- Monte Carlo simulations: ✅ Ready"
echo "- Technical indicators: ✅ Ready"
echo ""
echo "📊 Open darwin_platform.html in your browser to use the platform"
echo "Press Ctrl+C to stop the backend"
echo ""

# Start Python backend
python3 darwin_backend.py
