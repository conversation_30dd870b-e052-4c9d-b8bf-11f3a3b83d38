"""
DGM (Deep Generative Model) Monitor Worker
Manages DGM experiments for strategy evolution and auto-deployment
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set
from sqlalchemy.orm import Session

from app.database import SessionLocal
from app.models.database import DgmExperiments, Users
from app.services.dgm_service import DGMService
from app.config import settings

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DGMMonitor:
    """Monitor and manage DGM experiments"""
    
    def __init__(self):
        self.poll_interval = getattr(settings, 'WORKER_POLL_INTERVAL', 10) * 2  # Slower polling for DGM
        self.dgm_service = DGMService()
        self.max_concurrent = getattr(settings, 'MAX_CONCURRENT_DGM', 2)
        self.running_experiments: Set[str] = set()
        self.dgm_enabled = getattr(settings, 'DGM_ENABLED', False)
        self.fitness_threshold = getattr(settings, 'DGM_FITNESS_THRESHOLD', 0.05)
    
    async def run(self):
        """Main monitoring loop"""
        logger.info("DGM Monitor started")
        
        if not self.dgm_enabled:
            logger.info("DGM is disabled, monitor will idle")
        
        while True:
            try:
                if self.dgm_enabled:
                    await self._process_pending_experiments()
                    await self._monitor_running_experiments()
                    await self._cleanup_old_experiments()
                
                await asyncio.sleep(self.poll_interval)
            except Exception as e:
                logger.error(f"DGM monitor error: {e}", exc_info=True)
                await asyncio.sleep(self.poll_interval)
    
    async def _process_pending_experiments(self):
        """Process pending DGM experiments"""
        db = SessionLocal()
        
        try:
            # Get pending experiments
            pending_experiments = db.query(DgmExperiments).filter(
                DgmExperiments.status == 'pending',
                ~DgmExperiments.id.in_(self.running_experiments)
            ).order_by(DgmExperiments.created_at).limit(
                self.max_concurrent - len(self.running_experiments)
            ).all()
            
            # Start experiment tasks
            for experiment in pending_experiments:
                if len(self.running_experiments) >= self.max_concurrent:
                    break
                
                self.running_experiments.add(experiment.id)
                asyncio.create_task(
                    self._run_single_experiment(experiment.id)
                )
                
        finally:
            db.close()
    
    async def _run_single_experiment(self, experiment_id: str):
        """Run a single DGM experiment"""
        db = SessionLocal()
        
        try:
            logger.info(f"Starting DGM experiment {experiment_id}")
            
            # Get experiment from database
            experiment = db.query(DgmExperiments).filter(
                DgmExperiments.id == experiment_id
            ).first()
            
            if not experiment:
                logger.error(f"DGM experiment {experiment_id} not found")
                return
            
            # Update status to running
            experiment.status = 'running'
            experiment.started_at = datetime.utcnow()
            db.commit()
            
            # Execute experiment
            result = await self.dgm_service.run_experiment(experiment_id, db)
            
            if result.status == 'completed':
                logger.info(
                    f"DGM experiment {experiment_id} completed. "
                    f"Fitness improvement: {result.fitness_improvement:.4f}"
                )
                
                # Update experiment with results
                experiment.status = 'completed'
                experiment.completed_at = datetime.utcnow()
                experiment.fitness_improvement = result.fitness_improvement
                experiment.generated_strategy = result.generated_strategy
                db.commit()
                
                # Check if should auto-deploy
                if result.fitness_improvement > self.fitness_threshold:
                    await self._auto_deploy_strategy(experiment_id, db)
                    
            elif result.status == 'deployed':
                logger.info(f"DGM experiment {experiment_id} auto-deployed")
                experiment.status = 'deployed'
                experiment.completed_at = datetime.utcnow()
                db.commit()
            else:
                logger.error(f"DGM experiment {experiment_id} failed")
                experiment.status = 'error'
                experiment.error_message = getattr(result, 'error_message', 'Unknown error')
                experiment.completed_at = datetime.utcnow()
                db.commit()
                
        except Exception as e:
            logger.error(f"Error running DGM experiment {experiment_id}: {e}")
            
            # Mark experiment as error
            experiment = db.query(DgmExperiments).filter(
                DgmExperiments.id == experiment_id
            ).first()
            if experiment:
                experiment.status = 'error'
                experiment.error_message = str(e)
                experiment.completed_at = datetime.utcnow()
                db.commit()
        
        finally:
            self.running_experiments.discard(experiment_id)
            db.close()
    
    async def _monitor_running_experiments(self):
        """Monitor running experiments for timeouts"""
        db = SessionLocal()
        
        try:
            # Check for experiments running too long (> 1 hour)
            timeout_threshold = datetime.utcnow() - timedelta(hours=1)
            
            stuck_experiments = db.query(DgmExperiments).filter(
                DgmExperiments.status == 'running',
                DgmExperiments.started_at < timeout_threshold
            ).all()
            
            for experiment in stuck_experiments:
                logger.warning(f"DGM experiment {experiment.id} timed out")
                experiment.status = 'error'
                experiment.error_message = 'Experiment timed out'
                experiment.completed_at = datetime.utcnow()
                self.running_experiments.discard(experiment.id)
            
            if stuck_experiments:
                db.commit()
                
        finally:
            db.close()
    
    async def _cleanup_old_experiments(self):
        """Clean up old failed experiments"""
        db = SessionLocal()
        
        try:
            # Delete error experiments older than 7 days
            cleanup_threshold = datetime.utcnow() - timedelta(days=7)
            
            old_experiments = db.query(DgmExperiments).filter(
                DgmExperiments.status == 'error',
                DgmExperiments.created_at < cleanup_threshold
            ).all()
            
            for experiment in old_experiments:
                logger.info(f"Cleaning up old experiment {experiment.id}")
                db.delete(experiment)
            
            if old_experiments:
                db.commit()
                logger.info(f"Cleaned up {len(old_experiments)} old experiments")
                
        finally:
            db.close()
    
    async def _auto_deploy_strategy(self, experiment_id: str, db: Session):
        """Auto-deploy a high-performing strategy"""
        
        experiment = db.query(DgmExperiments).filter(
            DgmExperiments.id == experiment_id
        ).first()
        
        if not experiment:
            return
        
        # Check user's subscription tier
        user = db.query(Users).filter(Users.id == experiment.user_id).first()
        if not user:
            return
        
        # Only auto-deploy for pro/enterprise users
        if user.subscription_tier not in ['pro', 'enterprise']:
            logger.info(
                f"Skipping auto-deploy for user {user.id} "
                f"(tier: {user.subscription_tier})"
            )
            return
        
        try:
            # Create a backtest with the evolved strategy
            from app.services.backtest_service import BacktestService
            from app.models.schemas import BacktestConfig
            
            backtest_service = BacktestService()
            
            config = BacktestConfig(
                name=f"Auto-Deploy: {experiment.experiment_name}",
                symbol=experiment.generated_strategy.get('symbol', 'EURUSD'),
                strategy_config=experiment.generated_strategy,
                start_date=datetime.utcnow().replace(day=1),  # Current month
                end_date=datetime.utcnow(),
                model_name='dgm-auto-deploy'
            )
            
            backtest_id = await backtest_service.create_backtest(
                experiment.user_id, config, db
            )
            
            # Update experiment status
            experiment.status = 'deployed'
            experiment.deployed_backtest_id = backtest_id
            db.commit()
            
            logger.info(
                f"Auto-deployed experiment {experiment_id} as backtest {backtest_id}"
            )
            
        except Exception as e:
            logger.error(f"Failed to auto-deploy experiment {experiment_id}: {e}")
    
    def get_running_experiments(self) -> List[str]:
        """Get list of currently running experiment IDs"""
        return list(self.running_experiments)
    
    def get_worker_stats(self) -> Dict[str, any]:
        """Get worker statistics"""
        return {
            'dgm_enabled': self.dgm_enabled,
            'running_experiments': len(self.running_experiments),
            'max_concurrent': self.max_concurrent,
            'poll_interval': self.poll_interval,
            'fitness_threshold': self.fitness_threshold,
            'experiment_ids': list(self.running_experiments)
        }