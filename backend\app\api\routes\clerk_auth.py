from fastapi import HTTPException, Request
import os

try:
    from clerk import Clerk
except ImportError:
    class Clerk:
        async def get_user(self, token):
            # Mock for testing
            if token == "valid_token":
                return {"id": "user_123", "email": "<EMAIL>"}
            raise Exception("Invalid token")

clerk = Clerk(api_key=os.getenv("CLERK_API_KEY")) if hasattr(Clerk, "__init__") else Clerk()

async def clerk_auth_middleware(request: Request):
    auth_header = request.headers.get("Authorization")
    
    if not auth_header or not auth_header.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Unauthorized")
    
    token = auth_header[7:]  # Remove "Bearer " prefix
    
    try:
        user = await clerk.get_user(token)
        return user
    except Exception:
        raise HTTPException(status_code=401, detail="Invalid token")

async def get_current_user(request: Request):
    return await clerk_auth_middleware(request)
