# 🏗️ Architecture Documentation

This folder contains comprehensive documentation for the AI Trading Platform's architecture, TDD implementation, and full project structure.

## 📁 Directory Contents

- **`full-project-structure.md`** - Complete project architecture and TDD modules
- **`tdd-implementation.md`** - Detailed TDD patterns and test strategies
- **`schema-design.md`** - Schema-first development approach
- **`api-design.md`** - API endpoints and contract documentation
- **`database-design.md`** - Database schema and migration strategies

## 🎯 Purpose

This directory serves as the central hub for:

1. **Project Architecture**: Overall structure and design decisions
2. **TDD Implementation**: Test-driven development patterns and modules
3. **Development Guidelines**: Best practices and coding standards
4. **Integration Patterns**: How different parts of the system work together

## 📋 File Guidelines

### For Comprehensive Architecture Files:
- Place large architectural documents directly in this folder
- Use descriptive filenames that indicate scope
- Include both implementation details and test coverage
- Document integration points between modules

### For Code Examples:
```
docs/architecture/
├── examples/
│   ├── service-patterns/
│   ├── test-patterns/
│   └── integration-examples/
```

## 🔗 Related Documentation

- `/shared/README.md` - Shared components overview
- `/backend/README.md` - Backend-specific documentation  
- `/frontend/README.md` - Frontend-specific documentation
- `/DROP_LARGE_FILES_HERE.md` - For dropping implementation files

## 🚀 Integration

Files placed here should:
- ✅ Document the complete system architecture
- ✅ Include TDD patterns and test coverage strategies
- ✅ Provide implementation guidance
- ✅ Show integration between different modules
- ✅ Include examples and best practices