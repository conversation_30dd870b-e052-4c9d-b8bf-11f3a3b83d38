# TDD Guidelines for AI-Enhanced Trading Platform

## 🎯 TDD Rules for This Project

### 1. **No Code Without Tests**: Write tests first, always
- Every new feature starts with a failing test
- No production code is written without a corresponding test
- Tests define the behavior before implementation

### 2. **Red-Green-Refactor**: Follow the cycle strictly
- **Red**: Write a failing test that describes the desired behavior
- **Green**: Write the minimal code to make the test pass
- **Refactor**: Improve the code while keeping all tests green

### 3. **Coverage Requirements**: 90% minimum, 100% for new code
- Backend (Node.js): 90% line coverage minimum
- Python ML Engine: 90% line coverage minimum
- New features: 100% coverage required
- Security-critical code: 100% coverage mandatory

### 4. **Test Categories**:
- **Unit**: Test single functions/methods in isolation
- **Integration**: Test service interactions and API endpoints
- **E2E**: Test complete user workflows
- **Security**: Test malicious input handling and sandboxing

## 🔄 TDD Workflow Example

### Example: Adding Strategy Risk Assessment

#### Step 1: Red - Write Failing Test
```python
# python_engine/services/darwin_godel/__tests__/test_risk_assessment.py

def test_calculate_risk_score_for_high_risk_strategy():
    """Test that high-risk strategies get appropriate risk scores"""
    # Arrange
    verifier = DarwinGodelVerifier()
    high_risk_strategy = """
def trading_strategy(data, params):
    # High leverage, no stop loss
    if data['close'][-1] > data['close'][-2]:
        return {'signal': 'buy', 'confidence': 1.0, 'leverage': 10}
    else:
        return {'signal': 'sell', 'confidence': 1.0, 'leverage': 10}
"""
    
    # Act
    result = verifier.verify_strategy(high_risk_strategy)
    
    # Assert
    assert result['risk_score'] > 0.8  # High risk
    assert 'High leverage detected' in result['warnings']
```

#### Step 2: Green - Make Test Pass
```python
# python_engine/services/darwin_godel/strategy_verifier.py

def _calculate_risk_score(self, strategy_code: str, ast_tree) -> float:
    """Calculate risk score based on strategy characteristics"""
    risk_factors = []
    
    # Check for high leverage
    if 'leverage' in strategy_code and any(
        isinstance(node, ast.Num) and node.n > 5 
        for node in ast.walk(ast_tree)
    ):
        risk_factors.append(0.4)  # High leverage penalty
    
    # More risk checks...
    return min(sum(risk_factors), 1.0)
```

#### Step 3: Refactor - Improve Code
```python
# Extract risk calculation to separate class
class RiskAssessment:
    def calculate_risk_score(self, strategy_code: str, ast_tree) -> Dict[str, Any]:
        """Comprehensive risk assessment"""
        # Clean, well-structured risk calculation
        pass
```

## 📋 Test Structure Standards

### Unit Test Template
```python
import pytest
from unittest.mock import Mock, patch
from services.darwin_godel.strategy_verifier import DarwinGodelVerifier

class TestDarwinGodelVerifier:
    """Test suite for Darwin Godel Strategy Verifier"""
    
    def setup_method(self):
        """Set up test fixtures before each test"""
        self.verifier = DarwinGodelVerifier()
        self.sample_strategy = """
def trading_strategy(data, params):
    return {'signal': 'hold', 'confidence': 0.5}
"""
    
    def test_verify_valid_strategy_returns_success(self):
        """Test that valid strategies are accepted"""
        # Arrange
        strategy = self.sample_strategy
        
        # Act
        result = self.verifier.verify_strategy(strategy)
        
        # Assert
        assert result['is_valid'] is True
        assert 'strategy_type' in result
        assert 'risk_score' in result
    
    def test_verify_malicious_strategy_raises_security_error(self):
        """Test that malicious code is rejected"""
        # Arrange
        malicious_strategy = """
import os
def trading_strategy(data, params):
    os.system('rm -rf /')
    return {'signal': 'buy'}
"""
        
        # Act & Assert
        with pytest.raises(SecurityError):
            self.verifier.verify_strategy(malicious_strategy)
```

### Integration Test Template
```typescript
// backend/src/routes/__tests__/darwin-godel.integration.test.ts

import request from 'supertest';
import { app } from '../../app';

describe('Darwin Godel Integration', () => {
  beforeEach(async () => {
    // Set up test database
    await setupTestDatabase();
  });

  afterEach(async () => {
    // Clean up
    await cleanupTestDatabase();
  });

  it('should verify strategy end-to-end', async () => {
    // Arrange
    const strategyPayload = {
      strategyCode: `
def trading_strategy(data, params):
    return {'signal': 'buy', 'confidence': 0.8}
`,
      runMonteCarloValidation: true
    };

    // Act
    const response = await request(app)
      .post('/api/darwin-godel/verify')
      .send(strategyPayload)
      .expect(200);

    // Assert
    expect(response.body.success).toBe(true);
    expect(response.body.data.isValid).toBe(true);
    expect(response.body.data.strategyType).toBeDefined();
  });
});
```

## 🛡️ Security Testing Standards

### Security Test Requirements
```python
def test_restricted_python_blocks_dangerous_imports():
    """Ensure RestrictedPython blocks dangerous operations"""
    dangerous_operations = [
        "import os",
        "import subprocess", 
        "import sys",
        "from os import system",
        "exec('malicious code')",
        "eval('dangerous expression')",
        "__import__('os')",
        "open('/etc/passwd')"
    ]
    
    verifier = DarwinGodelVerifier()
    
    for operation in dangerous_operations:
        strategy = f"""
def trading_strategy(data, params):
    {operation}
    return {{'signal': 'buy'}}
"""
        with pytest.raises(SecurityError):
            verifier.verify_strategy(strategy)
```

## 📊 Coverage Standards

### Backend Coverage Configuration
```javascript
// backend/jest.config.js
module.exports = {
  coverageThreshold: {
    global: {
      branches: 85,
      functions: 90,
      lines: 90,
      statements: 90
    },
    // Stricter requirements for critical modules
    './src/services/darwin-godel-bridge.service.ts': {
      branches: 95,
      functions: 100,
      lines: 95,
      statements: 95
    }
  },
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/**/__tests__/**',
    '!src/types/**'
  ]
};
```

### Python Coverage Configuration
```ini
# python_engine/.coveragerc
[run]
source = services
omit = 
    */tests/*
    */test_*
    */__tests__/*

[report]
fail_under = 90
show_missing = True
skip_covered = False

[html]
directory = htmlcov
```

## 🚨 Quality Gates

### Pre-commit Checklist
- [ ] All tests pass (`npm test` and `pytest`)
- [ ] Coverage thresholds met
- [ ] No linting errors
- [ ] Security tests pass
- [ ] Integration tests pass

### CI/CD Requirements
```yaml
# .github/workflows/ci-cd.yml quality gates
- name: Enforce Coverage
  run: |
    cd backend && npm run test:coverage
    cd python_engine && pytest --cov-fail-under=90

- name: Security Scan
  run: |
    cd python_engine && bandit -r services
    cd backend && npm audit --audit-level=high
```

## 🎯 Best Practices

### 1. Test Naming Convention
```python
def test_[method_under_test]_[scenario]_[expected_behavior]():
    """Clear description of what this test verifies"""
    pass

# Examples:
def test_verify_strategy_with_valid_code_returns_success():
def test_verify_strategy_with_malicious_code_raises_security_error():
def test_calculate_risk_score_with_high_leverage_returns_high_risk():
```

### 2. Arrange-Act-Assert Pattern
```python
def test_example():
    # Arrange - Set up test data and conditions
    verifier = DarwinGodelVerifier()
    strategy_code = "def trading_strategy(): pass"
    
    # Act - Execute the behavior being tested
    result = verifier.verify_strategy(strategy_code)
    
    # Assert - Verify the expected outcome
    assert result['is_valid'] is True
```

### 3. Mock External Dependencies
```python
@patch('services.darwin_godel.strategy_verifier.SecureStrategyExecutor')
def test_verify_strategy_handles_execution_error(mock_executor):
    # Arrange
    mock_executor.return_value.execute.side_effect = SecurityError("Blocked")
    verifier = DarwinGodelVerifier()
    
    # Act & Assert
    with pytest.raises(SecurityError):
        verifier.verify_strategy("malicious code")
```

### 4. Test Data Builders
```python
class StrategyBuilder:
    """Builder pattern for creating test strategies"""
    
    def __init__(self):
        self.code = "def trading_strategy(data, params): return {'signal': 'hold'}"
    
    def with_sma_logic(self, period=20):
        self.code = f"""
def trading_strategy(data, params):
    sma = calculate_sma(data['close'], {period})
    return {{'signal': 'buy' if data['close'][-1] > sma[-1] else 'sell'}}
"""
        return self
    
    def with_malicious_import(self):
        self.code = "import os\n" + self.code
        return self
    
    def build(self):
        return self.code

# Usage in tests:
def test_sma_strategy():
    strategy = StrategyBuilder().with_sma_logic(period=10).build()
    result = verifier.verify_strategy(strategy)
    assert result['strategy_type'] == 'mean_reversion'
```

## 🔄 Continuous Improvement

### Weekly TDD Review
Every Friday, review:
1. **Test Quality**: Are tests testing behavior, not implementation?
2. **Coverage Gaps**: What areas need more testing?
3. **Flaky Tests**: Any tests that fail intermittently?
4. **Test Performance**: Are tests running fast enough?

### Metrics to Track
- Test execution time
- Coverage percentage by module
- Number of flaky tests
- Time spent debugging vs. time spent writing new features

## 🚫 Anti-Patterns to Avoid

### ❌ Don't Do This
```python
# Testing implementation details
def test_verify_strategy_calls_ast_parse():
    with patch('ast.parse') as mock_parse:
        verifier.verify_strategy("code")
        mock_parse.assert_called_once()  # Testing implementation!

# Writing tests after code
def some_new_feature():
    # Code written first
    pass

def test_some_new_feature():  # Test written after
    pass
```

### ✅ Do This Instead
```python
# Testing behavior
def test_verify_strategy_with_invalid_syntax_returns_error():
    result = verifier.verify_strategy("invalid python syntax")
    assert result['is_valid'] is False
    assert 'syntax error' in result['warnings']

# TDD approach
def test_new_feature_returns_expected_result():  # Test first
    # Test that defines the behavior
    pass

def new_feature():  # Implementation second
    # Minimal code to make test pass
    pass
```

## 📚 Resources

### Learning TDD
- **Books**: "Test Driven Development: By Example" - Kent Beck
- **Online**: Real Python TDD tutorials
- **Practice**: Code Katas with TDD

### Tools
- **Python**: pytest, pytest-cov, pytest-mock
- **Node.js**: Jest, supertest, @types/jest
- **CI/CD**: GitHub Actions, Codecov
- **IDE**: VS Code with Python Test Explorer

---

**Remember**: TDD is not about testing - it's about design. The tests are a byproduct of good design thinking. When you write tests first, you're forced to think about the API, the behavior, and the edge cases before you write any implementation code.