// src/data-management/forex-data-provider.ts
// STEP 2: Minimal implementation to make tests pass (Green phase)
import * as fs from 'fs/promises';
import * as crypto from 'crypto';

export interface CandleData {
  timestamp: Date;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface DatasetInfo {
  pair: string;
  timeframe: string;
  source: string;
  candles: CandleData[];
  dataHash?: string;
  lastValidated?: Date;
}

export interface AuditOperation {
  operation: string;
  timestamp: Date;
  dataHash: string;
  source?: string;
}

export interface AuditTrail {
  pair: string;
  timeframe: string;
  operations: AuditOperation[];
}

export class ForexDataProvider {
  private dataPath: string;
  private loadedData: Map<string, DatasetInfo> = new Map();
  private auditTrails: Map<string, AuditOperation[]> = new Map();

  constructor(dataPath: string) {
    this.dataPath = dataPath;
  }

  async initialize(): Promise<void> {
    // Create directory (makes first test pass)
    await fs.mkdir(this.dataPath, { recursive: true });
    
    // Load existing files (makes second test pass)
    try {
      const files = await fs.readdir(this.dataPath);
      for (const file of files) {
        if (file.endsWith('.json')) {
          const data = await fs.readFile(`${this.dataPath}/${file}`, 'utf-8');
          const parsed = JSON.parse(data);
          const key = `${parsed.pair}_${parsed.timeframe}`;
          this.loadedData.set(key, parsed);
        }
      }
    } catch (error) {
      // Directory might not exist yet
    }
  }

  async validateCandle(candle: any): Promise<ValidationResult> {
    const errors: string[] = [];
    
    // OHLC validation (makes validation test pass)
    if (candle.high < candle.open || candle.high < candle.close) {
      errors.push('High price must be >= open and close prices');
    }
    if (candle.low > candle.open || candle.low > candle.close) {
      errors.push('Low price must be <= open and close prices');
    }
    
    return { isValid: errors.length === 0, errors };
  }

  async validateSequence(candles: any[]): Promise<ValidationResult> {
    const errors: string[] = [];
    
    // Check sequential timestamps (makes sequence test pass)
    for (let i = 1; i < candles.length; i++) {
      if (new Date(candles[i].timestamp) <= new Date(candles[i-1].timestamp)) {
        errors.push('Timestamps must be in sequential order');
        break;
      }
    }
    
    return { isValid: errors.length === 0, errors };
  }

  async validatePriceRange(candle: any): Promise<ValidationResult> {
    const errors: string[] = [];
    
    // Price range validation (makes price range test pass)
    if (candle.pair?.includes('JPY')) {
      if (candle.open < 50 || candle.open > 200) {
        errors.push('Unrealistic price for JPY pair');
      }
    } else {
      if (candle.open < 0.5 || candle.open > 5) {
        errors.push('Unrealistic price for non-JPY pair');
      }
    }
    
    return { isValid: errors.length === 0, errors };
  }

  async getData(pair: string, timeframe: string, startDate?: Date, endDate?: Date): Promise<any> {
    const key = `${pair}_${timeframe}`;
    const data = this.loadedData.get(key);
    
    if (!data) {
      return {
        success: false,
        error: `No data available for ${pair} ${timeframe}`,
        availableTimeframes: this.getAvailableTimeframes(pair)
      };
    }
    
    let candles = data.candles || [];
    
    // Filter by date range if provided
    if (startDate || endDate) {
      candles = candles.filter((c: any) => {
        const timestamp = new Date(c.timestamp);
        if (startDate && timestamp < startDate) return false;
        if (endDate && timestamp > endDate) return false;
        return true;
      });
    }
    
    return {
      success: true,
      data: {
        ...data,
        candles,
        verification: {
          dataPoints: candles.length,
          validatedAt: data.lastValidated,
          originalHash: data.dataHash
        }
      }
    };
  }

  async loadDataset(data: DatasetInfo): Promise<void> {
    const key = `${data.pair}_${data.timeframe}`;
    const dataHash = this.calculateDataHash(data.candles);
    
    const datasetInfo: DatasetInfo = {
      ...data,
      dataHash,
      lastValidated: new Date()
    };
    
    this.loadedData.set(key, datasetInfo);
    
    // Record audit trail
    this.recordAuditOperation(key, {
      operation: 'load_dataset',
      timestamp: new Date(),
      dataHash,
      source: data.source
    });
  }

  calculateDataHash(candles: any[]): string {
    // SHA-256 hash implementation for data integrity
    return crypto.createHash('sha256').update(JSON.stringify(candles)).digest('hex');
  }

  private getAvailableTimeframes(pair: string): string[] {
    const timeframes: string[] = [];
    for (const [key, _] of this.loadedData) {
      if (key.startsWith(`${pair}_`)) {
        timeframes.push(key.split('_')[1]);
      }
    }
    return timeframes;
  }

  async getAvailableData(): Promise<any> {
    const pairs: any[] = [];
    const pairMap = new Map();
    
    for (const [key, data] of this.loadedData) {
      if (!pairMap.has(data.pair)) {
        pairMap.set(data.pair, { pair: data.pair, timeframes: [] });
      }
      pairMap.get(data.pair).timeframes.push(data.timeframe);
    }
    
    return {
      pairs: Array.from(pairMap.values()),
      totalDataPoints: this.getTotalDataPoints(),
      dateRange: this.getDateRange()
    };
  }

  async getAuditTrail(pair: string, timeframe: string): Promise<AuditTrail> {
    const key = `${pair}_${timeframe}`;
    const operations = this.auditTrails.get(key) || [];
    
    return {
      pair,
      timeframe,
      operations
    };
  }

  private recordAuditOperation(key: string, operation: AuditOperation): void {
    if (!this.auditTrails.has(key)) {
      this.auditTrails.set(key, []);
    }
    this.auditTrails.get(key)!.push(operation);
  }

  private getTotalDataPoints(): number {
    let total = 0;
    for (const [_, data] of this.loadedData) {
      total += data.candles?.length || 0;
    }
    return total;
  }

  private getDateRange(): { start: Date; end: Date } {
    let start = new Date();
    let end = new Date(0);
    
    for (const [_, data] of this.loadedData) {
      if (data.candles && data.candles.length > 0) {
        const firstCandle = new Date(data.candles[0].timestamp);
        const lastCandle = new Date(data.candles[data.candles.length - 1].timestamp);
        
        if (firstCandle < start) start = firstCandle;
        if (lastCandle > end) end = lastCandle;
      }
    }
    
    return { start, end };
  }
}