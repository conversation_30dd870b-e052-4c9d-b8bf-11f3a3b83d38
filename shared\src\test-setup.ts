// Test setup and utilities for shared schemas
import { z } from 'zod';

// Test utilities for schema validation
export const validateSchema = <T>(schema: z.ZodSchema<T>, data: unknown): T => {
  const result = schema.safeParse(data);
  if (!result.success) {
    throw new Error(`Schema validation failed: ${result.error.message}`);
  }
  return result.data;
};

// Mock data generators
export const generateMockId = (): string => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

export const generateMockDate = (daysOffset = 0): Date => {
  const date = new Date();
  date.setDate(date.getDate() + daysOffset);
  return date;
};

export const generateMockEmail = (): string => {
  const domains = ['example.com', 'test.com', 'mock.com'];
  const username = Math.random().toString(36).substring(2, 10);
  const domain = domains[Math.floor(Math.random() * domains.length)];
  return `${username}@${domain}`;
};

// Custom matchers for testing
export const expectValidSchema = <T>(schema: z.ZodSchema<T>, data: unknown) => {
  expect(() => validateSchema(schema, data)).not.toThrow();
};

export const expectInvalidSchema = <T>(schema: z.ZodSchema<T>, data: unknown) => {
  expect(() => validateSchema(schema, data)).toThrow();
};