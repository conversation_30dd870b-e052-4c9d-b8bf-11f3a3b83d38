import { DatabaseConnection } from './connection';
import { Logger } from '../types';

// Mock knex
jest.mock('knex', () => {
  const mockConnection = {
    raw: jest.fn(),
    migrate: {
      latest: jest.fn(),
      rollback: jest.fn(),
    },
    seed: {
      run: jest.fn(),
    },
    transaction: jest.fn(),
    destroy: jest.fn(),
    on: jest.fn(),
  };
  
  return jest.fn(() => mockConnection);
});

describe('DatabaseConnection', () => {
  let mockLogger: Logger;
  let mockKnexInstance: any;

  beforeEach(() => {
    mockLogger = {
      error: jest.fn(),
      warn: jest.fn(),
      info: jest.fn(),
      debug: jest.fn(),
      trace: jest.fn(),
    };

    // Reset the singleton instance for each test
    (DatabaseConnection as any).instance = undefined;

    // Get the mock knex instance
    const knex = require('knex');
    mockKnexInstance = knex();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getInstance', () => {
    it('should return singleton instance', () => {
      const instance1 = DatabaseConnection.getInstance(mockLogger);
      const instance2 = DatabaseConnection.getInstance(mockLogger);

      expect(instance1).toBe(instance2);
      expect(instance1).toBeInstanceOf(DatabaseConnection);
    });

    it('should create instance with logger', () => {
      const instance = DatabaseConnection.getInstance(mockLogger);
      expect(instance).toBeInstanceOf(DatabaseConnection);
    });

    it('should create instance without logger', () => {
      const instance = DatabaseConnection.getInstance();
      expect(instance).toBeInstanceOf(DatabaseConnection);
    });
  });

  describe('connection property', () => {
    it('should return knex connection', () => {
      const instance = DatabaseConnection.getInstance(mockLogger);
      const connection = instance.connection;

      expect(connection).toBeDefined();
      expect(typeof connection).toBe('object');
    });
  });

  describe('testConnection', () => {
    it('should return true when connection is successful', async () => {
      mockKnexInstance.raw.mockResolvedValue(true);

      const instance = DatabaseConnection.getInstance(mockLogger);
      const result = await instance.testConnection();

      expect(result).toBe(true);
      expect(mockKnexInstance.raw).toHaveBeenCalledWith('SELECT 1');
    });

    it('should return false when connection fails', async () => {
      const error = new Error('Connection failed');
      mockKnexInstance.raw.mockRejectedValue(error);

      const instance = DatabaseConnection.getInstance(mockLogger);
      const result = await instance.testConnection();

      expect(result).toBe(false);
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Database connection test failed',
        { error }
      );
    });

    it('should handle connection failure without logger', async () => {
      const error = new Error('Connection failed');
      mockKnexInstance.raw.mockRejectedValue(error);

      const instance = DatabaseConnection.getInstance();
      const result = await instance.testConnection();

      expect(result).toBe(false);
    });
  });

  describe('disconnect', () => {
    it('should destroy connection', async () => {
      mockKnexInstance.destroy.mockResolvedValue(undefined);

      const instance = DatabaseConnection.getInstance(mockLogger);
      await instance.disconnect();

      expect(mockKnexInstance.destroy).toHaveBeenCalled();
    });
  });

  describe('runMigrations', () => {
    it('should run migrations successfully', async () => {
      mockKnexInstance.migrate.latest.mockResolvedValue(true);

      const instance = DatabaseConnection.getInstance(mockLogger);
      await instance.runMigrations();

      expect(mockKnexInstance.migrate.latest).toHaveBeenCalled();
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Database migrations completed successfully'
      );
    });

    it('should handle migration failure', async () => {
      const error = new Error('Migration failed');
      mockKnexInstance.migrate.latest.mockRejectedValue(error);

      const instance = DatabaseConnection.getInstance(mockLogger);

      await expect(instance.runMigrations()).rejects.toThrow('Migration failed');
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Database migration failed',
        { error }
      );
    });
  });

  describe('transaction', () => {
    it('should execute transaction callback', async () => {
      const mockTransaction = { commit: jest.fn(), rollback: jest.fn() };
      const mockCallback = jest.fn().mockResolvedValue('result');
      mockKnexInstance.transaction.mockImplementation((callback) => 
        callback(mockTransaction)
      );

      const instance = DatabaseConnection.getInstance(mockLogger);
      const result = await instance.transaction(mockCallback);

      expect(mockKnexInstance.transaction).toHaveBeenCalled();
      expect(mockCallback).toHaveBeenCalledWith(mockTransaction);
    });
  });
});