import { v4 as uuidv4 } from 'uuid';
import { EventEmitter } from 'events';
import { Logger, ServiceResponse } from '@/shared/types';
import {
  DarwinEvolutionRequest,
  DarwinEvolutionResponse,
  DarwinJobStatus,
  DarwinResults,
  ForexGenome,
  PythonDarwinRequest,
  PythonDarwinResponse,
  EvolutionStatus,
  TradingStrategy,
} from '@ai-trading/shared';
import { PythonEngineService } from './python-engine.service';

export interface DarwinBridgeServiceDependencies {
  pythonEngineService: PythonEngineService;
  logger: Logger;
}

export interface DarwinJobInfo {
  jobId: string;
  status: EvolutionStatus;
  startTime: Date;
  pair: string;
  timeframe: string;
  evolutionParams: any;
}

export class DarwinBridgeService extends EventEmitter {
  private pythonEngineService: PythonEngineService;
  private logger: Logger;
  private activeJobs: Map<string, DarwinJobInfo> = new Map();
  private jobResults: Map<string, DarwinResults> = new Map();

  constructor(dependencies: DarwinBridgeServiceDependencies) {
    super();
    this.pythonEngineService = dependencies.pythonEngineService;
    this.logger = dependencies.logger;
  }

  /**
   * Start a new Darwin evolution job
   */
  async startEvolution(request: DarwinEvolutionRequest): Promise<ServiceResponse<DarwinEvolutionResponse>> {
    try {
      const jobId = uuidv4();
      
      this.logger.info('Starting Darwin evolution', {
        jobId,
        pair: request.pair,
        timeframe: request.timeframe
      });

      // Create Python request
      const pythonRequest: PythonDarwinRequest = {
        action: 'start_evolution',
        payload: {
          job_id: jobId,
          pair: request.pair,
          timeframe: request.timeframe,
          evolution_params: request.evolution_params,
          data_start_date: request.data_start_date,
          data_end_date: request.data_end_date,
        },
        timestamp: new Date(),
        request_id: uuidv4(),
      };

      // Send request to Python Darwin engine
      const pythonResponse = await this.pythonEngineService.sendRequest<PythonDarwinResponse>(
        'darwin_engine',
        pythonRequest
      );

      if (!pythonResponse.success) {
        throw new Error(pythonResponse.error || 'Failed to start Darwin evolution');
      }

      // Store job info
      const jobInfo: DarwinJobInfo = {
        jobId,
        status: 'initializing',
        startTime: new Date(),
        pair: request.pair,
        timeframe: request.timeframe,
        evolutionParams: request.evolution_params || {},
      };

      this.activeJobs.set(jobId, jobInfo);

      // Start monitoring job progress
      this.startJobMonitoring(jobId);

      const response: DarwinEvolutionResponse = {
        job_id: jobId,
        status: 'started',
        message: 'Darwin evolution process initiated successfully',
        estimated_duration_minutes: this.estimateEvolutionDuration(request.evolution_params),
      };

      this.emit('evolutionStarted', { jobId, request });

      return {
        success: true,
        data: response,
      };

    } catch (error) {
      this.logger.error('Failed to start Darwin evolution', { error: error instanceof Error ? error.message : String(error) });
      
      return {
        success: false,
        error: {
          code: 'EVOLUTION_START_FAILED',
          message: 'Failed to start Darwin evolution',
          details: error instanceof Error ? error.message : String(error),
        },
      };
    }
  }

  /**
   * Get the status of a Darwin evolution job
   */
  async getJobStatus(jobId: string): Promise<ServiceResponse<DarwinJobStatus>> {
    try {
      if (!this.activeJobs.has(jobId)) {
        return {
          success: false,
          error: {
            code: 'JOB_NOT_FOUND',
            message: 'Job not found',
            details: `Job with ID ${jobId} does not exist`,
          },
        };
      }

      // Request status from Python engine
      const pythonRequest: PythonDarwinRequest = {
        action: 'get_status',
        job_id: jobId,
        timestamp: new Date(),
        request_id: uuidv4(),
      };

      const pythonResponse = await this.pythonEngineService.sendRequest<PythonDarwinResponse>(
        'darwin_engine',
        pythonRequest
      );

      if (!pythonResponse.success) {
        throw new Error(pythonResponse.error || 'Failed to get job status');
      }

      const status: DarwinJobStatus = pythonResponse.data;
      
      // Update local job info
      const jobInfo = this.activeJobs.get(jobId)!;
      jobInfo.status = status.status;
      this.activeJobs.set(jobId, jobInfo);

      return {
        success: true,
        data: status,
      };

    } catch (error) {
      this.logger.error('Failed to get Darwin job status', { jobId, error: error instanceof Error ? error.message : String(error) });
      
      return {
        success: false,
        error: {
          code: 'JOB_STATUS_ERROR',
          message: 'Failed to get Darwin job status',
          details: error instanceof Error ? error.message : String(error),
        },
      };
    }
  }

  /**
   * Get the results of a completed Darwin evolution job
   */
  async getJobResults(jobId: string): Promise<ServiceResponse<DarwinResults>> {
    try {
      // Check if results are cached
      if (this.jobResults.has(jobId)) {
        return {
          success: true,
          data: this.jobResults.get(jobId)!,
        };
      }

      // Request results from Python engine
      const pythonRequest: PythonDarwinRequest = {
        action: 'get_results',
        job_id: jobId,
        timestamp: new Date(),
        request_id: uuidv4(),
      };

      const pythonResponse = await this.pythonEngineService.sendRequest<PythonDarwinResponse>(
        'darwin_engine',
        pythonRequest
      );

      if (!pythonResponse.success) {
        throw new Error(pythonResponse.error || 'Failed to get job results');
      }

      const results: DarwinResults = pythonResponse.data;
      
      // Cache results
      this.jobResults.set(jobId, results);

      return {
        success: true,
        data: results,
      };

    } catch (error) {
      this.logger.error('Failed to get Darwin job results', { jobId, error: error instanceof Error ? error.message : String(error) });
      
      return {
        success: false,
        error: {
          code: 'JOB_RESULTS_ERROR',
          message: 'Failed to get Darwin job results',
          details: error instanceof Error ? error.message : String(error),
        },
      };
    }
  }

  /**
   * Get the forex genome for a specific pair and timeframe
   */
  async getForexGenome(jobId: string): Promise<ServiceResponse<ForexGenome>> {
    try {
      const pythonRequest: PythonDarwinRequest = {
        action: 'get_genome',
        job_id: jobId,
        timestamp: new Date(),
        request_id: uuidv4(),
      };

      const pythonResponse = await this.pythonEngineService.sendRequest<PythonDarwinResponse>(
        'darwin_engine',
        pythonRequest
      );

      if (!pythonResponse.success) {
        throw new Error(pythonResponse.error || 'Failed to get forex genome');
      }

      const genome: ForexGenome = pythonResponse.data;

      return {
        success: true,
        data: genome,
      };

    } catch (error) {
      this.logger.error('Failed to get forex genome', { jobId, error: error instanceof Error ? error.message : String(error) });
      
      return {
        success: false,
        error: {
          code: 'FOREX_GENOME_ERROR',
          message: 'Failed to get forex genome',
          details: error instanceof Error ? error.message : String(error),
        },
      };
    }
  }

  /**
   * Stop a running Darwin evolution job
   */
  async stopEvolution(jobId: string): Promise<ServiceResponse<{ message: string }>> {
    try {
      if (!this.activeJobs.has(jobId)) {
        return {
          success: false,
          error: {
            code: 'JOB_NOT_FOUND',
            message: 'Job not found',
            details: `Job with ID ${jobId} does not exist`,
          },
        };
      }

      const pythonRequest: PythonDarwinRequest = {
        action: 'stop_evolution',
        job_id: jobId,
        timestamp: new Date(),
        request_id: uuidv4(),
      };

      const pythonResponse = await this.pythonEngineService.sendRequest<PythonDarwinResponse>(
        'darwin_engine',
        pythonRequest
      );

      if (!pythonResponse.success) {
        throw new Error(pythonResponse.error || 'Failed to stop evolution');
      }

      // Update job status
      const jobInfo = this.activeJobs.get(jobId)!;
      jobInfo.status = 'terminated';
      this.activeJobs.set(jobId, jobInfo);

      this.emit('evolutionStopped', { jobId });

      return {
        success: true,
        data: { message: 'Evolution stopped successfully' },
      };

    } catch (error) {
      this.logger.error('Failed to stop Darwin evolution', { jobId, error: error instanceof Error ? error.message : String(error) });
      
      return {
        success: false,
        error: {
          code: 'EVOLUTION_STOP_ERROR',
          message: 'Failed to stop Darwin evolution',
          details: error instanceof Error ? error.message : String(error),
        },
      };
    }
  }

  /**
   * Get all active Darwin jobs
   */
  getActiveJobs(): DarwinJobInfo[] {
    return Array.from(this.activeJobs.values());
  }

  /**
   * Get the best strategies from a completed job
   */
  async getBestStrategies(jobId: string, limit: number = 10, verifiedOnly: boolean = true): Promise<ServiceResponse<TradingStrategy[]>> {
    try {
      const resultsResponse = await this.getJobResults(jobId);
      
      if (!resultsResponse.success) {
        return {
          success: false,
          error: resultsResponse.error,
        };
      }

      if (!resultsResponse.data) {
        return {
          success: false,
          error: {
            code: 'NO_DATA',
            message: 'No results data available',
            details: 'Results response contains no data',
          },
        };
      }

      let strategies = resultsResponse.data.best_strategies;

      // Filter for verified strategies if requested
      if (verifiedOnly) {
        strategies = strategies.filter(strategy => strategy.is_verified);
      }

      // Sort by fitness score and limit
      strategies = strategies
        .sort((a, b) => (b.fitness_score || 0) - (a.fitness_score || 0))
        .slice(0, limit);

      return {
        success: true,
        data: strategies,
      };

    } catch (error) {
      this.logger.error('Failed to get best strategies', { jobId, error: error instanceof Error ? error.message : String(error) });
      
      return {
        success: false,
        error: {
          code: 'BEST_STRATEGIES_ERROR',
          message: 'Failed to get best strategies',
          details: error instanceof Error ? error.message : String(error),
        },
      };
    }
  }

  /**
   * Start monitoring a job's progress
   */
  private startJobMonitoring(jobId: string): void {
    const monitorInterval = setInterval(async () => {
      try {
        const statusResponse = await this.getJobStatus(jobId);
        
        if (statusResponse.success && statusResponse.data) {
          const status = statusResponse.data;
          
          this.emit('jobProgress', { jobId, status });

          // Stop monitoring if job is completed or failed
          if (['completed', 'failed', 'terminated'].includes(status.status)) {
            clearInterval(monitorInterval);
            
            if (status.status === 'completed') {
              this.emit('evolutionCompleted', { jobId });
            } else if (status.status === 'failed') {
              this.emit('evolutionFailed', { jobId, error: status.error });
            }
          }
        }
      } catch (error) {
        this.logger.error('Error monitoring Darwin job', { jobId, error: error instanceof Error ? error.message : String(error) });
      }
    }, 5000); // Check every 5 seconds
  }

  /**
   * Estimate evolution duration based on parameters
   */
  private estimateEvolutionDuration(params: any): number {
    const populationSize = params?.population_size || 50;
    const maxGenerations = params?.max_generations || 30;
    
    // Rough estimate: 2 seconds per strategy per generation
    const estimatedSeconds = (populationSize * maxGenerations * 2);
    return Math.ceil(estimatedSeconds / 60); // Convert to minutes
  }

  /**
   * Clean up completed jobs older than specified time
   */
  cleanupOldJobs(maxAgeHours: number = 24): void {
    const cutoffTime = new Date(Date.now() - (maxAgeHours * 60 * 60 * 1000));
    
    for (const [jobId, jobInfo] of this.activeJobs.entries()) {
      if (jobInfo.startTime < cutoffTime && 
          ['completed', 'failed', 'terminated'].includes(jobInfo.status)) {
        this.activeJobs.delete(jobId);
        this.jobResults.delete(jobId);
        this.logger.info('Cleaned up old Darwin job', { jobId });
      }
    }
  }
}