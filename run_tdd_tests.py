#!/usr/bin/env python
"""
Script to run all TDD tests for the AI Enhanced Trading Platform
"""

import os
import sys
import subprocess
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("tdd_tests")

def run_tests():
    """Run all TDD tests"""
    logger.info("Running TDD tests for AI Enhanced Trading Platform...")
    
    # Define test files
    test_files = [
        "tests/test_mt5_bridge_tdd.py",
        "tests/test_mt5_bridge_comprehensive.py",
        "tests/test_backtest_tdd.py",
        "tests/test_strategy_execution_tdd.py",
        "tests/test_risk_management_tdd.py"
    ]
    
    # Check if pytest is available
    try:
        import pytest
        
        # Run tests with pytest
        logger.info("Running tests with pytest...")
        args = [
            "-v",                  # Verbose output
            "--no-header",         # No header
            "--tb=short",          # Short traceback
        ] + test_files
        
        result = pytest.main(args)
        
        if result == 0:
            logger.info("All tests passed!")
        else:
            logger.error(f"Tests failed with exit code: {result}")
        
        return result
        
    except ImportError:
        logger.warning("pytest not found. Running tests with unittest...")
        
        # Run tests with unittest
        failed = False
        for test_file in test_files:
            logger.info(f"Running {test_file}...")
            try:
                # Run test file as a script
                result = subprocess.run([sys.executable, test_file], check=False)
                if result.returncode != 0:
                    logger.error(f"Test {test_file} failed with exit code: {result.returncode}")
                    failed = True
            except Exception as e:
                logger.error(f"Error running {test_file}: {str(e)}")
                failed = True
        
        return 1 if failed else 0

if __name__ == "__main__":
    sys.exit(run_tests())