import pytest
from unittest.mock import Mock, patch
from .mt5_bridge import MT<PERSON><PERSON><PERSON>, MT5ConnectionError

def test_mt5_connection_success():
    """Test successful MT5 connection"""
    bridge = MT5Bridge()
    
    with patch('mt5.initialize') as mock_init:
        mock_init.return_value = True
        result = bridge.connect()
    
    assert result is True
    assert bridge.is_connected() is True

def test_mt5_connection_failure():
    """Test MT5 connection failure handling"""
    bridge = MT5Bridge()
    
    with patch('mt5.initialize') as mock_init:
        mock_init.return_value = False
        with pytest.raises(MT5ConnectionError):
            bridge.connect()
    
    assert bridge.is_connected() is False

def test_mt5_heartbeat_monitoring():
    """Test heartbeat monitoring detects disconnections"""
    bridge = MT5Bridge()
    bridge._connected = True
    
    with patch('mt5.terminal_info') as mock_info:
        mock_info.return_value = None  # Simulate disconnection
        status = bridge.check_heartbeat()
    assert status is False
