// test/helpers/test-server.ts
import { FastifyInstance } from 'fastify';

/**
 * Test server helper for API testing
 */
export class TestServer {
  private app!: FastifyInstance;

  async start() {
    // Mock Fastify app for testing
    const mockApp = {
      ready: jest.fn().mockResolvedValue(undefined),
      close: jest.fn().mockResolvedValue(undefined),
      inject: jest.fn().mockImplementation((options) => {
        // Mock response based on request
        if (options.url === '/api/trades' && options.method === 'POST') {
          const body = typeof options.body === 'string' ? JSON.parse(options.body) : options.body;
          
          // Validate request
          if (!body.symbol || body.quantity <= 0) {
            return {
              statusCode: 400,
              body: JSON.stringify({
                errors: ['Invalid trade data']
              })
            };
          }
          
          return {
            statusCode: 201,
            body: JSON.stringify({
              id: 'test-trade-id',
              ...body,
              status: 'pending'
            })
          };
        }
        
        return {
          statusCode: 404,
          body: JSON.stringify({ error: 'Not found' })
        };
      })
    } as any;
    
    this.app = mockApp;
    await this.app.ready();
    return this.app;
  }

  async stop() {
    if (this.app) {
      await this.app.close();
    }
  }

  getApp() {
    return this.app;
  }
}