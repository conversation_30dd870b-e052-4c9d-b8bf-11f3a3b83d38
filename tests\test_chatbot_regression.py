#!/usr/bin/env python3
"""
No-Hallucination Regression Tests for Trading Chatbot

This test suite ensures that the chatbot NEVER hallucinates information.
Every response must either:
1. Include verifiable sources and hashes, OR
2. Explicitly state "I don't know"

No middle ground is allowed - this prevents hallucination completely.
"""

import pytest
import tempfile
import os
from datetime import datetime

# Import the chatbot components
from src.chatbot.knowledge_base import KnowledgeBase, TradingChatbot, KnowledgeEntry


class TestChatbotNoHallucinationRegression:
    """Test suite for no-hallucination regression"""
    
    def setup_method(self):
        """Set up test environment"""
        # Create temporary database for testing
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        
        # Initialize knowledge base and chatbot
        self.kb = KnowledgeBase(self.temp_db.name)
        self.chatbot = TradingChatbot(self.kb)
        
        # Add some test knowledge
        self.chatbot.add_trading_knowledge()
    
    def teardown_method(self):
        """Clean up test environment"""
        if os.path.exists(self.temp_db.name):
            os.unlink(self.temp_db.name)
    
    def test_chatbot_includes_source_or_idk(self):
        """Test that chatbot includes source/hash or 'I don't know' for all queries"""
        queries = [
            "Show me the last GBPUSD backtest result.",
            "What is the best RSI period for EURUSD?",
            "Who won the 1987 World Series?",  # Should trigger IDK fallback
            "What is RSI?",  # Should have source from knowledge base
            "How do moving averages work?",  # Should have source
            "What is the capital of Mars?",  # Should trigger IDK
            "Tell me about support and resistance",  # Should have source
            "What is quantum trading?",  # Should trigger IDK
            "Explain MACD crossover signals",  # Should have source
            "What is the best cryptocurrency to buy?"  # Should trigger IDK
        ]
        
        for query in queries:
            response = self.chatbot.answer(query)
            
            # Enforce at least one: source/hash, or honest "I don't know"
            has_source = "source" in response.lower() or "Source:" in response
            has_hash = "hash" in response.lower() or "Verification hash:" in response
            is_idk = "i don't know" in response.lower()
            
            # Must have EITHER (source AND hash) OR "I don't know"
            has_verification = has_source and has_hash
            
            assert has_verification or is_idk, (
                f"Query: '{query}'\n"
                f"Response: '{response}'\n"
                f"Has source: {has_source}, Has hash: {has_hash}, Is IDK: {is_idk}\n"
                f"VIOLATION: Response must include source+hash OR 'I don't know'"
            )
    
    def test_backtest_queries_have_sources(self):
        """Test that backtest queries always include sources and hashes"""
        backtest_queries = [
            "Show me the last GBPUSD backtest result.",
            "What is the GBPUSD backtest performance?",
            "Give me GBPUSD trading results"
        ]
        
        for query in backtest_queries:
            response = self.chatbot.answer(query)
            
            # Must have source attribution
            assert "Source:" in response, f"Missing source in response to: {query}"
            
            # Must have hash verification
            assert "Verification hash:" in response, f"Missing hash in response to: {query}"
            
            # Must have actual data (not "I don't know")
            assert "i don't know" not in response.lower(), f"Unexpected IDK for backtest query: {query}"
    
    def test_rsi_queries_have_sources(self):
        """Test that RSI queries have proper source attribution"""
        rsi_queries = [
            "What is the best RSI period for EURUSD?",
            "Tell me about RSI overbought levels",
            "How does RSI work?",
            "What is RSI oversold?"
        ]
        
        for query in rsi_queries:
            response = self.chatbot.answer(query)
            
            # Must have either source+hash OR "I don't know"
            has_source = "Source:" in response
            has_hash = "Verification hash:" in response or "hash" in response.lower()
            is_idk = "i don't know" in response.lower()
            
            assert (has_source and has_hash) or is_idk, (
                f"RSI query failed verification: {query}\n"
                f"Response: {response}"
            )
    
    def test_unknown_queries_return_idk(self):
        """Test that unknown queries return 'I don't know'"""
        unknown_queries = [
            "Who won the 1987 World Series?",
            "What is the capital of Mars?",
            "How do I bake a cake?",
            "What is quantum trading?",
            "Tell me about alien technology",
            "What is the best cryptocurrency to buy?",
            "How do I fix my car engine?",
            "What is the meaning of life?"
        ]
        
        for query in unknown_queries:
            response = self.chatbot.answer(query)
            
            # Must contain "I don't know"
            assert "i don't know" in response.lower(), (
                f"Unknown query should return 'I don't know': {query}\n"
                f"Response: {response}"
            )
            
            # Should NOT have source/hash for unknown queries
            assert "Source:" not in response, f"Unknown query should not have source: {query}"
    
    def test_technical_analysis_queries_have_sources(self):
        """Test that technical analysis queries have proper sources"""
        ta_queries = [
            "What is RSI?",
            "How do moving averages work?",
            "Tell me about support and resistance",
            "Explain MACD crossover signals",
            "What are Bollinger Bands?",
            "How does stochastic oscillator work?"
        ]
        
        for query in ta_queries:
            response = self.chatbot.answer(query)
            
            # Check if it's a known topic (has source) or unknown (IDK)
            has_source = "Source:" in response
            has_hash = "Verification hash:" in response or "hash" in response.lower()
            is_idk = "i don't know" in response.lower()
            
            # Must be either verified OR IDK
            assert (has_source and has_hash) or is_idk, (
                f"TA query failed verification: {query}\n"
                f"Response: {response}"
            )
    
    def test_response_format_consistency(self):
        """Test that responses with sources follow consistent format"""
        queries_with_expected_sources = [
            "What is RSI?",
            "Tell me about moving averages",
            "Show me the last GBPUSD backtest result."
        ]
        
        for query in queries_with_expected_sources:
            response = self.chatbot.answer(query)
            
            if "Source:" in response:
                # Must have proper format
                lines = response.split('\n')
                
                # Should have source line
                source_lines = [line for line in lines if line.startswith("Source:")]
                assert len(source_lines) >= 1, f"Missing source line in: {query}"
                
                # Should have hash line
                hash_lines = [line for line in lines if "hash:" in line.lower()]
                assert len(hash_lines) >= 1, f"Missing hash line in: {query}"
    
    def test_confidence_threshold_enforcement(self):
        """Test that low confidence entries trigger 'I don't know'"""
        # Add a low confidence entry
        low_confidence_entry = KnowledgeEntry(
            content="This is low confidence information about trading.",
            source="Technical Analysis by John Murphy",
            verification_hash="",
            confidence_score=0.3,  # Below threshold
            last_verified=datetime.now(),
            tags=["test", "low_confidence"]
        )
        
        # Generate hash
        low_confidence_entry.verification_hash = self.kb._generate_content_hash(low_confidence_entry)
        
        # Add to knowledge base
        self.kb.add_verified_entry(low_confidence_entry)
        
        # Query should return "I don't know" due to low confidence
        response = self.chatbot.answer("Tell me about low confidence trading")
        assert "i don't know" in response.lower(), (
            f"Low confidence entry should trigger IDK: {response}"
        )
    
    def test_empty_knowledge_base_returns_idk(self):
        """Test that empty knowledge base returns 'I don't know'"""
        # Create empty knowledge base
        empty_temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        empty_temp_db.close()
        
        try:
            empty_kb = KnowledgeBase(empty_temp_db.name)
            empty_chatbot = TradingChatbot(empty_kb)
            
            queries = [
                "What is RSI?",
                "Tell me about trading",
                "How do I make money?"
            ]
            
            for query in queries:
                response = empty_chatbot.answer(query)
                assert "i don't know" in response.lower(), (
                    f"Empty KB should return IDK for: {query}\n"
                    f"Response: {response}"
                )
        
        finally:
            if os.path.exists(empty_temp_db.name):
                os.unlink(empty_temp_db.name)
    
    def test_strict_mode_enforcement(self):
        """Test that strict mode is properly enforced"""
        # Verify strict mode is enabled
        assert self.chatbot.strict_mode is True
        assert self.chatbot.require_source_or_idk is True
        
        # Test that disabling strict mode changes behavior
        self.chatbot.require_source_or_idk = False
        
        response = self.chatbot.answer("What is quantum trading?")
        # Without strict mode, might not have "I don't know"
        
        # Re-enable strict mode
        self.chatbot.require_source_or_idk = True
        
        response = self.chatbot.answer("What is quantum trading?")
        assert "i don't know" in response.lower(), "Strict mode should enforce IDK"
    
    def test_hash_verification_in_responses(self):
        """Test that hash verification is included in responses"""
        queries_with_sources = [
            "What is RSI?",
            "Show me the last GBPUSD backtest result."
        ]
        
        for query in queries_with_sources:
            response = self.chatbot.answer(query)
            
            if "Source:" in response:
                # Must have hash verification
                assert "hash:" in response.lower(), f"Missing hash verification: {query}"
                
                # Hash should be truncated (not full hash)
                hash_lines = [line for line in response.split('\n') if "hash:" in line.lower()]
                for hash_line in hash_lines:
                    # Should end with "..." indicating truncation
                    assert "..." in hash_line, f"Hash should be truncated: {hash_line}"
    
    def test_source_attribution_accuracy(self):
        """Test that source attribution is accurate"""
        # Query for RSI information
        response = self.chatbot.answer("What is RSI overbought level?")
        
        if "Source:" in response:
            # Should reference a known trading book
            known_sources = [
                "Technical Analysis by John Murphy",
                "Quantitative Trading by Ernest Chan",
                "Market Wizards by Jack Schwager"
            ]
            
            has_known_source = any(source in response for source in known_sources)
            assert has_known_source, f"Response should have known source: {response}"


class TestChatbotRegressionEdgeCases:
    """Test edge cases for no-hallucination regression"""
    
    def setup_method(self):
        """Set up test environment"""
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.kb = KnowledgeBase(self.temp_db.name)
        self.chatbot = TradingChatbot(self.kb)
        self.chatbot.add_trading_knowledge()
    
    def teardown_method(self):
        """Clean up test environment"""
        if os.path.exists(self.temp_db.name):
            os.unlink(self.temp_db.name)
    
    def test_empty_query(self):
        """Test handling of empty queries"""
        response = self.chatbot.answer("")
        assert "i don't know" in response.lower(), "Empty query should return IDK"
    
    def test_very_long_query(self):
        """Test handling of very long queries"""
        long_query = "What is " + "very " * 100 + "long query about trading?"
        response = self.chatbot.answer(long_query)
        
        # Should either have source+hash or IDK
        has_source = "Source:" in response
        has_hash = "hash:" in response.lower()
        is_idk = "i don't know" in response.lower()
        
        assert (has_source and has_hash) or is_idk, "Long query must follow regression rules"
    
    def test_special_characters_query(self):
        """Test handling of queries with special characters"""
        special_queries = [
            "What is RSI @#$%?",
            "Tell me about trading!!! ???",
            "How does MACD work??? $$$ profit $$$"
        ]
        
        for query in special_queries:
            response = self.chatbot.answer(query)
            
            has_source = "Source:" in response
            has_hash = "hash:" in response.lower()
            is_idk = "i don't know" in response.lower()
            
            assert (has_source and has_hash) or is_idk, (
                f"Special character query failed: {query}"
            )
    
    def test_case_insensitive_matching(self):
        """Test that matching is case insensitive"""
        case_variants = [
            "what is RSI?",
            "WHAT IS RSI?",
            "What Is Rsi?",
            "wHaT iS rSi?"
        ]
        
        responses = []
        for query in case_variants:
            response = self.chatbot.answer(query)
            responses.append(response)
        
        # All responses should be similar (either all have sources or all IDK)
        has_sources = ["Source:" in resp for resp in responses]
        is_idks = ["i don't know" in resp.lower() for resp in responses]
        
        # Should be consistent across case variants
        assert len(set(has_sources)) <= 1, "Case variants should have consistent source handling"
        assert len(set(is_idks)) <= 1, "Case variants should have consistent IDK handling"


if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v"])