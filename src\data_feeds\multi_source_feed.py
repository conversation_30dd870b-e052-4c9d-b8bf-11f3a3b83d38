# src/data_feeds/multi_source_feed.py
import asyncio
import aiohttp
import time
from typing import Dict, List, Any, Optional, Tuple, Callable
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
import statistics

class DataSourceType(Enum):
    REST_API = "rest_api"
    WEBSOCKET = "websocket"
    FILE = "file"
    DATABASE = "database"

class DataQuality(Enum):
    EXCELLENT = "excellent"
    GOOD = "good"
    FAIR = "fair"
    POOR = "poor"
    INVALID = "invalid"

@dataclass
class DataSource:
    name: str
    source_type: DataSourceType
    url: str
    api_key: Optional[str]
    priority: int  # 1 = highest priority
    timeout_seconds: int
    retry_attempts: int
    quality_threshold: float  # 0.0 - 1.0
    enabled: bool = True

@dataclass
class PriceData:
    symbol: str
    timestamp: datetime
    bid: float
    ask: float
    last: float
    volume: float
    source: str
    quality_score: float
    latency_ms: float

@dataclass
class DataValidationResult:
    is_valid: bool
    quality_score: float
    validation_errors: List[str]
    cross_validation_passed: bool
    timestamp: datetime

class DataFeedAggregator:
    def __init__(self):
        self.data_sources: Dict[str, DataSource] = {}
        self.price_cache: Dict[str, List[PriceData]] = {}  # symbol -> list of recent prices
        self.validation_rules: List[Callable[[PriceData], Tuple[bool, str]]] = []
        self.quality_metrics: Dict[str, Dict[str, float]] = {}  # source -> metrics
        
        # Callbacks for real-time data
        self.data_callbacks: List[Callable[[PriceData], None]] = []
        self.validation_callbacks: List[Callable[[DataValidationResult], None]] = []
        
        # Performance tracking
        self.request_stats: Dict[str, Dict[str, Any]] = {}
        
        # Setup default validation rules
        self._setup_default_validation_rules()
    
    def _setup_default_validation_rules(self):
        """Setup default data validation rules"""
        def validate_price_range(data: PriceData) -> Tuple[bool, str]:
            """Validate that prices are within reasonable ranges"""
            if data.bid <= 0 or data.ask <= 0 or data.last <= 0:
                return False, "Prices must be positive"
            
            if data.ask < data.bid:
                return False, "Ask price cannot be less than bid price"
            
            spread_percent = ((data.ask - data.bid) / data.bid) * 100
            if spread_percent > 10:  # 10% spread seems excessive
                return False, f"Spread too wide: {spread_percent:.2f}%"
            
            return True, "Price range validation passed"
        
        def validate_timestamp(data: PriceData) -> Tuple[bool, str]:
            """Validate timestamp is recent and not in future"""
            now = datetime.now()
            age_seconds = (now - data.timestamp).total_seconds()
            
            if age_seconds < 0:
                return False, "Timestamp is in the future"
            
            if age_seconds > 300:  # 5 minutes old
                return False, f"Data too old: {age_seconds:.0f} seconds"
            
            return True, "Timestamp validation passed"
        
        def validate_volume(data: PriceData) -> Tuple[bool, str]:
            """Validate volume is reasonable"""
            if data.volume < 0:
                return False, "Volume cannot be negative"
            
            return True, "Volume validation passed"
        
        self.validation_rules.extend([
            validate_price_range,
            validate_timestamp,
            validate_volume
        ])
    
    def add_data_source(self, source: DataSource):
        """Add a new data source"""
        self.data_sources[source.name] = source
        self.quality_metrics[source.name] = {
            "uptime_percent": 100.0,
            "avg_latency_ms": 0.0,
            "error_rate_percent": 0.0,
            "data_quality_score": 1.0
        }
        self.request_stats[source.name] = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "avg_response_time_ms": 0.0,
            "last_request_time": None
        }
    
    def remove_data_source(self, source_name: str):
        """Remove a data source"""
        if source_name in self.data_sources:
            del self.data_sources[source_name]
            del self.quality_metrics[source_name]
            del self.request_stats[source_name]
    
    async def fetch_price_data(self, symbol: str, sources: Optional[List[str]] = None) -> List[PriceData]:
        """Fetch price data from multiple sources"""
        if sources is None:
            sources = [name for name, source in self.data_sources.items() if source.enabled]
        
        # Sort sources by priority
        sorted_sources = sorted(
            [(name, self.data_sources[name]) for name in sources if name in self.data_sources],
            key=lambda x: x[1].priority
        )
        
        tasks = []
        for source_name, source in sorted_sources:
            task = asyncio.create_task(self._fetch_from_source(symbol, source_name, source))
            tasks.append(task)
        
        # Wait for all requests with timeout
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        valid_data = []
        for i, result in enumerate(results):
            source_name = sorted_sources[i][0]
            
            if isinstance(result, Exception):
                self._update_error_stats(source_name, str(result))
                continue
            
            if result is not None:
                # Validate data
                validation_result = self.validate_data(result)
                
                if validation_result.is_valid:
                    valid_data.append(result)
                    self._update_success_stats(source_name, result.latency_ms)
                else:
                    self._update_error_stats(source_name, "Validation failed")
        
        # Cache the data
        if symbol not in self.price_cache:
            self.price_cache[symbol] = []
        
        self.price_cache[symbol].extend(valid_data)
        
        # Keep only recent data (last 100 entries)
        self.price_cache[symbol] = self.price_cache[symbol][-100:]
        
        # Trigger callbacks
        for data in valid_data:
            for callback in self.data_callbacks:
                try:
                    callback(data)
                except Exception as e:
                    print(f"Error in data callback: {e}")
        
        return valid_data
    
    async def _fetch_from_source(self, symbol: str, source_name: str, source: DataSource) -> Optional[PriceData]:
        """Fetch data from a specific source"""
        start_time = time.time()
        
        try:
            if source.source_type == DataSourceType.REST_API:
                return await self._fetch_rest_api(symbol, source_name, source)
            elif source.source_type == DataSourceType.WEBSOCKET:
                return await self._fetch_websocket(symbol, source_name, source)
            elif source.source_type == DataSourceType.FILE:
                return await self._fetch_file(symbol, source_name, source)
            else:
                raise ValueError(f"Unsupported source type: {source.source_type}")
        
        except Exception as e:
            latency_ms = (time.time() - start_time) * 1000
            print(f"Error fetching from {source_name}: {e}")
            return None
    
    async def _fetch_rest_api(self, symbol: str, source_name: str, source: DataSource) -> Optional[PriceData]:
        """Fetch data from REST API"""
        start_time = time.time()
        
        headers = {}
        if source.api_key:
            headers['Authorization'] = f'Bearer {source.api_key}'
        
        # Simulate different API formats
        if "alpha_vantage" in source.name.lower():
            url = f"{source.url}?function=GLOBAL_QUOTE&symbol={symbol}&apikey={source.api_key}"
        elif "fixer" in source.name.lower():
            url = f"{source.url}/latest?access_key={source.api_key}&symbols={symbol}"
        else:
            # Generic format
            url = f"{source.url}/{symbol}"
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers, timeout=source.timeout_seconds) as response:
                if response.status == 200:
                    data = await response.json()
                    latency_ms = (time.time() - start_time) * 1000
                    
                    # Parse response based on source format
                    return self._parse_api_response(data, symbol, source_name, latency_ms)
                else:
                    raise Exception(f"HTTP {response.status}: {await response.text()}")
    
    async def _fetch_websocket(self, symbol: str, source_name: str, source: DataSource) -> Optional[PriceData]:
        """Fetch data from WebSocket (simplified simulation)"""
        # For testing purposes, simulate WebSocket data
        import random
        
        latency_ms = random.uniform(10, 50)
        base_price = 1.1000 if symbol == "EURUSD" else 1.2500
        
        return PriceData(
            symbol=symbol,
            timestamp=datetime.now(),
            bid=base_price - 0.0001,
            ask=base_price + 0.0001,
            last=base_price,
            volume=random.uniform(1000, 10000),
            source=source_name,
            quality_score=0.95,
            latency_ms=latency_ms
        )
    
    async def _fetch_file(self, symbol: str, source_name: str, source: DataSource) -> Optional[PriceData]:
        """Fetch data from file source"""
        # Simulate file-based data source
        import random
        
        latency_ms = random.uniform(5, 20)
        base_price = 1.1000 if symbol == "EURUSD" else 1.2500
        
        return PriceData(
            symbol=symbol,
            timestamp=datetime.now(),
            bid=base_price - 0.0002,
            ask=base_price + 0.0002,
            last=base_price + random.uniform(-0.0001, 0.0001),
            volume=random.uniform(500, 5000),
            source=source_name,
            quality_score=0.90,
            latency_ms=latency_ms
        )
    
    def _parse_api_response(self, data: Dict[str, Any], symbol: str, source_name: str, latency_ms: float) -> PriceData:
        """Parse API response into PriceData"""
        # Simulate different API response formats
        if "Global Quote" in str(data):
            # Alpha Vantage format
            quote = data.get("Global Quote", {})
            price = float(quote.get("05. price", 1.1000))
        elif "rates" in data:
            # Fixer.io format
            price = float(data["rates"].get(symbol, 1.1000))
        else:
            # Generic format
            price = float(data.get("price", 1.1000))
        
        return PriceData(
            symbol=symbol,
            timestamp=datetime.now(),
            bid=price - 0.0001,
            ask=price + 0.0001,
            last=price,
            volume=1000.0,  # Default volume
            source=source_name,
            quality_score=0.85,
            latency_ms=latency_ms
        )
    
    def validate_data(self, data: PriceData) -> DataValidationResult:
        """Validate price data using all validation rules"""
        validation_errors = []
        
        # Run all validation rules
        for rule in self.validation_rules:
            try:
                is_valid, message = rule(data)
                if not is_valid:
                    validation_errors.append(message)
            except Exception as e:
                validation_errors.append(f"Validation rule error: {e}")
        
        # Cross-validation with cached data
        cross_validation_passed = self._cross_validate_data(data)
        
        # Calculate quality score
        quality_score = self._calculate_quality_score(data, validation_errors, cross_validation_passed)
        
        # Overall validation result
        is_valid = len(validation_errors) == 0 and cross_validation_passed
        
        result = DataValidationResult(
            is_valid=is_valid,
            quality_score=quality_score,
            validation_errors=validation_errors,
            cross_validation_passed=cross_validation_passed,
            timestamp=datetime.now()
        )
        
        # Trigger validation callbacks
        for callback in self.validation_callbacks:
            try:
                callback(result)
            except Exception as e:
                print(f"Error in validation callback: {e}")
        
        return result
    
    def _cross_validate_data(self, data: PriceData) -> bool:
        """Cross-validate data against other sources"""
        if data.symbol not in self.price_cache:
            return True  # No data to compare against
        
        recent_data = [
            d for d in self.price_cache[data.symbol]
            if (datetime.now() - d.timestamp).total_seconds() < 60  # Last minute
            and d.source != data.source  # Different source
        ]
        
        if not recent_data:
            return True  # No recent data from other sources
        
        # Compare with median of recent data
        recent_prices = [d.last for d in recent_data]
        median_price = statistics.median(recent_prices)
        
        # Check if price is within reasonable range of median
        price_diff_percent = abs((data.last - median_price) / median_price) * 100
        
        return price_diff_percent < 5.0  # 5% tolerance
    
    def _calculate_quality_score(self, data: PriceData, validation_errors: List[str], cross_validation_passed: bool) -> float:
        """Calculate quality score for the data"""
        base_score = 1.0
        
        # Deduct for validation errors
        base_score -= len(validation_errors) * 0.2
        
        # Deduct for cross-validation failure
        if not cross_validation_passed:
            base_score -= 0.3
        
        # Deduct for high latency
        if data.latency_ms > 1000:
            base_score -= 0.2
        elif data.latency_ms > 500:
            base_score -= 0.1
        
        # Bonus for low latency
        if data.latency_ms < 100:
            base_score += 0.1
        
        return max(0.0, min(1.0, base_score))
    
    def _update_success_stats(self, source_name: str, latency_ms: float):
        """Update success statistics for a source"""
        stats = self.request_stats[source_name]
        stats["total_requests"] += 1
        stats["successful_requests"] += 1
        stats["last_request_time"] = datetime.now()
        
        # Update average response time
        if stats["avg_response_time_ms"] == 0:
            stats["avg_response_time_ms"] = latency_ms
        else:
            # Exponential moving average
            stats["avg_response_time_ms"] = 0.9 * stats["avg_response_time_ms"] + 0.1 * latency_ms
    
    def _update_error_stats(self, source_name: str, error_message: str):
        """Update error statistics for a source"""
        stats = self.request_stats[source_name]
        stats["total_requests"] += 1
        stats["failed_requests"] += 1
        stats["last_request_time"] = datetime.now()
        
        print(f"Error from {source_name}: {error_message}")
    
    def get_best_price(self, symbol: str) -> Optional[PriceData]:
        """Get the best quality price data for a symbol"""
        if symbol not in self.price_cache or not self.price_cache[symbol]:
            return None
        
        # Get recent data (last 30 seconds)
        recent_data = [
            d for d in self.price_cache[symbol]
            if (datetime.now() - d.timestamp).total_seconds() < 30
        ]
        
        if not recent_data:
            return None
        
        # Return data with highest quality score
        return max(recent_data, key=lambda x: x.quality_score)
    
    def get_source_quality_metrics(self) -> Dict[str, Dict[str, float]]:
        """Get quality metrics for all sources"""
        for source_name, stats in self.request_stats.items():
            if stats["total_requests"] > 0:
                error_rate = (stats["failed_requests"] / stats["total_requests"]) * 100
                uptime = ((stats["successful_requests"] / stats["total_requests"]) * 100)
                
                self.quality_metrics[source_name].update({
                    "uptime_percent": uptime,
                    "error_rate_percent": error_rate,
                    "avg_latency_ms": stats["avg_response_time_ms"]
                })
        
        return self.quality_metrics.copy()
    
    def add_data_callback(self, callback: Callable[[PriceData], None]):
        """Add callback for real-time data updates"""
        self.data_callbacks.append(callback)
    
    def add_validation_callback(self, callback: Callable[[DataValidationResult], None]):
        """Add callback for validation results"""
        self.validation_callbacks.append(callback)
    
    def add_validation_rule(self, rule: Callable[[PriceData], Tuple[bool, str]]):
        """Add custom validation rule"""
        self.validation_rules.append(rule)
    
    def get_aggregated_data(self, symbol: str, time_window_seconds: int = 60) -> Optional[Dict[str, Any]]:
        """Get aggregated data for a symbol over a time window"""
        if symbol not in self.price_cache:
            return None
        
        cutoff_time = datetime.now() - timedelta(seconds=time_window_seconds)
        recent_data = [
            d for d in self.price_cache[symbol]
            if d.timestamp >= cutoff_time
        ]
        
        if not recent_data:
            return None
        
        prices = [d.last for d in recent_data]
        volumes = [d.volume for d in recent_data]
        quality_scores = [d.quality_score for d in recent_data]
        
        return {
            "symbol": symbol,
            "time_window_seconds": time_window_seconds,
            "data_points": len(recent_data),
            "price_stats": {
                "min": min(prices),
                "max": max(prices),
                "avg": statistics.mean(prices),
                "median": statistics.median(prices)
            },
            "volume_stats": {
                "total": sum(volumes),
                "avg": statistics.mean(volumes)
            },
            "quality_stats": {
                "avg_quality_score": statistics.mean(quality_scores),
                "min_quality_score": min(quality_scores)
            },
            "sources": list(set(d.source for d in recent_data)),
            "timestamp": datetime.now().isoformat()
        }