<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Trading Platform - Simple Server</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status { padding: 15px; margin: 20px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .endpoint { background: #f8f9fa; padding: 10px; margin: 10px 0; border-left: 4px solid #007bff; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 AI Trading Platform</h1>
        <div class="status success">
            <strong>✅ Simple Server Running!</strong><br>
            This is a basic HTTP server using Python's built-in modules.
        </div>
        
        <h2>Available Endpoints</h2>
        <div class="endpoint">
            <strong>GET /health</strong> - Server health check
        </div>
        <div class="endpoint">
            <strong>GET /api/status</strong> - Platform status
        </div>
        <div class="endpoint">
            <strong>POST /api/echo</strong> - Echo test endpoint
        </div>
        
        <h2>Quick Tests</h2>
        <button onclick="testHealth()">Test Health</button>
        <button onclick="testStatus()">Test Status</button>
        <button onclick="testEcho()">Test Echo</button>
        
        <div id="results"></div>
        
        <h2>Next Steps</h2>
        <div class="info">
            <strong>Server is working!</strong> Now you can:
            <ul>
                <li>Add more API endpoints as needed</li>
                <li>Integrate with your existing components</li>
                <li>Test MT5 integration</li>
                <li>Deploy frontend components</li>
            </ul>
        </div>
    </div>

    <script>
        const results = document.getElementById('results');
        
        async function testHealth() {
            try {
                const response = await fetch('/health');
                const data = await response.json();
                showResult('Health Check', data);
            } catch (error) {
                showResult('Health Check Error', error.message);
            }
        }
        
        async function testStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                showResult('Status Check', data);
            } catch (error) {
                showResult('Status Check Error', error.message);
            }
        }
        
        async function testEcho() {
            try {
                const testData = { message: 'Hello from browser!', timestamp: new Date().toISOString() };
                const response = await fetch('/api/echo', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testData)
                });
                const data = await response.json();
                showResult('Echo Test', data);
            } catch (error) {
                showResult('Echo Test Error', error.message);
            }
        }
        
        function showResult(title, data) {
            const resultDiv = document.createElement('div');
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
            results.appendChild(resultDiv);
        }
    </script>
</body>
</html>