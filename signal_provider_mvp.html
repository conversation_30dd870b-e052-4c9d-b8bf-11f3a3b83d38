<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Trading Signals - Signal Provider Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .disclaimer {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 20px;
            text-align: center;
            font-weight: 600;
        }
        
        .status {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        
        .status-item {
            display: inline-block;
            margin-right: 30px;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
        }
        
        .status-online {
            background: #d4edda;
            color: #155724;
        }
        
        .status-offline {
            background: #f8d7da;
            color: #721c24;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }
        
        .section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            border: 1px solid #e9ecef;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        
        .signal-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #3498db;
        }
        
        .signal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .signal-symbol {
            font-size: 1.2rem;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .signal-type {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }
        
        .signal-buy {
            background: #d4edda;
            color: #155724;
        }
        
        .signal-sell {
            background: #f8d7da;
            color: #721c24;
        }
        
        .signal-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .signal-detail {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 6px;
            text-align: center;
        }
        
        .signal-detail-label {
            font-size: 0.8rem;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .signal-detail-value {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .signal-instructions {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .signal-instructions h4 {
            color: #1976d2;
            margin-bottom: 10px;
        }
        
        .signal-instructions ol {
            margin-left: 20px;
            color: #424242;
        }
        
        .signal-instructions li {
            margin-bottom: 5px;
        }
        
        .btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }
        
        .strategy-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
        }
        
        .strategy-name {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .strategy-performance {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 10px;
            margin-top: 15px;
        }
        
        .performance-metric {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        
        .performance-label {
            font-size: 0.8rem;
            color: #6c757d;
        }
        
        .performance-value {
            font-weight: bold;
            color: #27ae60;
        }
        
        .performance-value.negative {
            color: #e74c3c;
        }
        
        .mt5-instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .mt5-instructions h3 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        .mt5-instructions ol {
            margin-left: 20px;
            color: #856404;
        }
        
        .mt5-instructions li {
            margin-bottom: 8px;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .signal-details {
                grid-template-columns: 1fr;
            }
            
            .strategy-performance {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 AI Trading Signals</h1>
            <p>Professional Trading Signal Provider - Execute on Your Own MT5 Account</p>
        </div>
        
        <div class="disclaimer">
            ⚠️ IMPORTANT: This platform provides trading signals only. You execute trades on your own MT5 account. We do not handle your funds or execute trades for you.
        </div>
        
        <div class="status">
            <div id="signal-status" class="status-item status-online">Signal Engine: Active</div>
            <div id="strategy-status" class="status-item status-online">Strategies: 3 Active</div>
            <div id="signals-count" class="status-item status-online">Today's Signals: 5</div>
        </div>
        
        <div class="main-content">
            <!-- Live Signals Section -->
            <div class="section">
                <h2>🔥 Live Trading Signals</h2>
                
                <div class="mt5-instructions">
                    <h3>How to Use These Signals:</h3>
                    <ol>
                        <li>Open your MT5 platform</li>
                        <li>Navigate to the recommended symbol</li>
                        <li>Place the order manually using the provided details</li>
                        <li>Set stop loss and take profit as suggested</li>
                        <li>Monitor your trade in your MT5 account</li>
                    </ol>
                </div>
                
                <div id="signals-container">
                    <!-- Signals will be populated here -->
                </div>
                
                <button id="refresh-signals" class="btn">🔄 Refresh Signals</button>
            </div>
            
            <!-- Strategies Section -->
            <div class="section">
                <h2>🎯 Active Strategies</h2>
                
                <div id="strategies-container">
                    <!-- Strategies will be populated here -->
                </div>
                
                <button id="view-backtests" class="btn">📈 View Backtest Results</button>
            </div>
        </div>
        
        <!-- Performance Section -->
        <div style="padding: 30px;">
            <div class="section">
                <h2>📊 Signal Performance (Last 30 Days)</h2>
                <div id="performance-container">
                    <!-- Performance metrics will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8002';
        
        // Mock data for demonstration (fallback if API is not available)
        const mockSignals = [
            {
                id: 1,
                symbol: "EURUSD",
                type: "BUY",
                entry_price: 1.1234,
                stop_loss: 1.1200,
                take_profit: 1.1300,
                lot_size: 0.1,
                confidence: 85,
                strategy: "MA Crossover",
                timestamp: new Date(),
                reasoning: "Fast MA crossed above slow MA with strong momentum"
            },
            {
                id: 2,
                symbol: "GBPUSD",
                type: "SELL",
                entry_price: 1.3145,
                stop_loss: 1.3180,
                take_profit: 1.3080,
                lot_size: 0.1,
                confidence: 78,
                strategy: "RSI Reversal",
                timestamp: new Date(Date.now() - 15 * 60 * 1000),
                reasoning: "RSI showing overbought conditions with bearish divergence"
            }
        ];
        
        const mockStrategies = [
            {
                id: 1,
                name: "Moving Average Crossover",
                description: "Fast MA crosses slow MA strategy",
                win_rate: 68.5,
                profit_factor: 1.45,
                monthly_return: 12.3,
                active: true
            },
            {
                id: 2,
                name: "RSI Reversal",
                description: "Oversold/Overbought reversal strategy",
                win_rate: 72.1,
                profit_factor: 1.62,
                monthly_return: 15.7,
                active: true
            },
            {
                id: 3,
                name: "Breakout Scanner",
                description: "Support/Resistance breakout detection",
                win_rate: 61.3,
                profit_factor: 1.38,
                monthly_return: 9.8,
                active: true
            }
        ];
        
        // Initialize the page
        window.addEventListener('load', function() {
            loadSignals();
            loadStrategies();
            loadPerformance();
        });
        
        // Load and display signals
        async function loadSignals() {
            const container = document.getElementById('signals-container');
            
            try {
                // Try to load from API first
                const response = await fetch(`${API_BASE}/api/signals/live`);
                let signals = [];
                
                if (response.ok) {
                    signals = await response.json();
                    document.getElementById('signal-status').textContent = 'Signal Engine: Active';
                    document.getElementById('signal-status').className = 'status-item status-online';
                } else {
                    throw new Error('API not available');
                }
                
                // If no signals from API, use mock data
                if (signals.length === 0) {
                    signals = mockSignals;
                }
                
                if (signals.length === 0) {
                    container.innerHTML = '<div style="text-align: center; padding: 20px; color: #6c757d;">No active signals at the moment. Check back soon!</div>';
                    return;
                }
                
                container.innerHTML = signals.map(signal => {
                    const timestamp = signal.timestamp ? new Date(signal.timestamp) : new Date();
                    const lotSize = signal.suggested_lot_size || signal.lot_size || 0.1;
                    
                    return `
                    <div class="signal-item">
                        <div class="signal-header">
                            <span class="signal-symbol">${signal.symbol}</span>
                            <span class="signal-type signal-${signal.type.toLowerCase()}">${signal.type}</span>
                        </div>
                        
                        <div class="signal-details">
                            <div class="signal-detail">
                                <div class="signal-detail-label">Entry Price</div>
                                <div class="signal-detail-value">${signal.entry_price}</div>
                            </div>
                            <div class="signal-detail">
                                <div class="signal-detail-label">Stop Loss</div>
                                <div class="signal-detail-value">${signal.stop_loss}</div>
                            </div>
                            <div class="signal-detail">
                                <div class="signal-detail-label">Take Profit</div>
                                <div class="signal-detail-value">${signal.take_profit}</div>
                            </div>
                            <div class="signal-detail">
                                <div class="signal-detail-label">Suggested Lot</div>
                                <div class="signal-detail-value">${lotSize}</div>
                            </div>
                        </div>
                        
                        <div style="margin-bottom: 15px;">
                            <strong>Strategy:</strong> ${signal.strategy} | 
                            <strong>Confidence:</strong> ${signal.confidence}% | 
                            <strong>Time:</strong> ${timestamp.toLocaleTimeString()}
                        </div>
                        
                        <div style="margin-bottom: 15px; color: #6c757d; font-style: italic;">
                            <strong>Analysis:</strong> ${signal.reasoning}
                        </div>
                        
                        <div class="signal-instructions">
                            <h4>📋 MT5 Execution Instructions:</h4>
                            <ol>
                                <li>Open ${signal.symbol} chart in MT5</li>
                                <li>Place ${signal.type} order at market price (around ${signal.entry_price})</li>
                                <li>Set Stop Loss: ${signal.stop_loss}</li>
                                <li>Set Take Profit: ${signal.take_profit}</li>
                                <li>Use lot size: ${lotSize} (adjust based on your risk tolerance)</li>
                            </ol>
                        </div>
                    </div>
                `;
                }).join('');
                
            } catch (error) {
                console.error('Failed to load signals from API:', error);
                document.getElementById('signal-status').textContent = 'Signal Engine: Offline';
                document.getElementById('signal-status').className = 'status-item status-offline';
                
                // Fall back to mock data
                if (mockSignals.length === 0) {
                    container.innerHTML = '<div style="text-align: center; padding: 20px; color: #6c757d;">Signal service temporarily unavailable. Please try again later.</div>';
                    return;
                }
                
                container.innerHTML = mockSignals.map(signal => {
                    const timestamp = signal.timestamp ? new Date(signal.timestamp) : new Date();
                    const lotSize = signal.suggested_lot_size || signal.lot_size || 0.1;
                    
                    return `
                    <div class="signal-item">
                        <div class="signal-header">
                            <span class="signal-symbol">${signal.symbol}</span>
                            <span class="signal-type signal-${signal.type.toLowerCase()}">${signal.type}</span>
                        </div>
                        
                        <div class="signal-details">
                            <div class="signal-detail">
                                <div class="signal-detail-label">Entry Price</div>
                                <div class="signal-detail-value">${signal.entry_price}</div>
                            </div>
                            <div class="signal-detail">
                                <div class="signal-detail-label">Stop Loss</div>
                                <div class="signal-detail-value">${signal.stop_loss}</div>
                            </div>
                            <div class="signal-detail">
                                <div class="signal-detail-label">Take Profit</div>
                                <div class="signal-detail-value">${signal.take_profit}</div>
                            </div>
                            <div class="signal-detail">
                                <div class="signal-detail-label">Suggested Lot</div>
                                <div class="signal-detail-value">${lotSize}</div>
                            </div>
                        </div>
                        
                        <div style="margin-bottom: 15px;">
                            <strong>Strategy:</strong> ${signal.strategy} | 
                            <strong>Confidence:</strong> ${signal.confidence}% | 
                            <strong>Time:</strong> ${timestamp.toLocaleTimeString()}
                        </div>
                        
                        <div style="margin-bottom: 15px; color: #6c757d; font-style: italic;">
                            <strong>Analysis:</strong> ${signal.reasoning}
                        </div>
                        
                        <div class="signal-instructions">
                            <h4>📋 MT5 Execution Instructions:</h4>
                            <ol>
                                <li>Open ${signal.symbol} chart in MT5</li>
                                <li>Place ${signal.type} order at market price (around ${signal.entry_price})</li>
                                <li>Set Stop Loss: ${signal.stop_loss}</li>
                                <li>Set Take Profit: ${signal.take_profit}</li>
                                <li>Use lot size: ${lotSize} (adjust based on your risk tolerance)</li>
                            </ol>
                        </div>
                    </div>
                `;
                }).join('');
            }
        }
        
        // Load and display strategies
        function loadStrategies() {
            const container = document.getElementById('strategies-container');
            
            container.innerHTML = mockStrategies.map(strategy => `
                <div class="strategy-card">
                    <div class="strategy-name">${strategy.name}</div>
                    <div style="color: #6c757d; margin-bottom: 15px;">${strategy.description}</div>
                    
                    <div class="strategy-performance">
                        <div class="performance-metric">
                            <div class="performance-label">Win Rate</div>
                            <div class="performance-value">${strategy.win_rate}%</div>
                        </div>
                        <div class="performance-metric">
                            <div class="performance-label">Profit Factor</div>
                            <div class="performance-value">${strategy.profit_factor}</div>
                        </div>
                        <div class="performance-metric">
                            <div class="performance-label">Monthly Return</div>
                            <div class="performance-value ${strategy.monthly_return < 0 ? 'negative' : ''}">${strategy.monthly_return}%</div>
                        </div>
                    </div>
                </div>
            `).join('');
        }
        
        // Load performance metrics
        function loadPerformance() {
            const container = document.getElementById('performance-container');
            
            container.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                    <div class="performance-metric" style="padding: 20px;">
                        <div class="performance-label">Total Signals</div>
                        <div class="performance-value" style="font-size: 1.5rem;">127</div>
                    </div>
                    <div class="performance-metric" style="padding: 20px;">
                        <div class="performance-label">Winning Signals</div>
                        <div class="performance-value" style="font-size: 1.5rem;">89</div>
                    </div>
                    <div class="performance-metric" style="padding: 20px;">
                        <div class="performance-label">Overall Win Rate</div>
                        <div class="performance-value" style="font-size: 1.5rem;">70.1%</div>
                    </div>
                    <div class="performance-metric" style="padding: 20px;">
                        <div class="performance-label">Avg. Monthly Return</div>
                        <div class="performance-value" style="font-size: 1.5rem;">12.6%</div>
                    </div>
                </div>
                
                <div style="margin-top: 30px; padding: 20px; background: #e8f5e8; border-radius: 8px; border: 1px solid #c3e6cb;">
                    <h3 style="color: #155724; margin-bottom: 15px;">📈 Performance Disclaimer</h3>
                    <p style="color: #155724; margin-bottom: 10px;">
                        <strong>Past performance does not guarantee future results.</strong> These statistics are based on historical signal performance and backtesting results.
                    </p>
                    <p style="color: #155724;">
                        <strong>Risk Warning:</strong> Trading involves substantial risk of loss. Only trade with money you can afford to lose. Always use proper risk management.
                    </p>
                </div>
            `;
        }
        
        // Event listeners
        document.getElementById('refresh-signals').addEventListener('click', async function() {
            const button = this;
            button.textContent = '🔄 Generating New Signal...';
            button.disabled = true;
            
            try {
                // Generate a new signal via API
                const response = await fetch(`${API_BASE}/api/signals/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                });
                
                if (response.ok) {
                    const newSignal = await response.json();
                    console.log('New signal generated:', newSignal);
                    
                    // Reload all signals
                    await loadSignals();
                    
                    button.textContent = '✅ New Signal Generated!';
                } else {
                    throw new Error('Failed to generate signal');
                }
            } catch (error) {
                console.error('Error generating signal:', error);
                
                // Fall back to just refreshing existing signals
                await loadSignals();
                button.textContent = '✅ Signals Refreshed';
            }
            
            setTimeout(() => {
                button.textContent = '🔄 Refresh Signals';
                button.disabled = false;
            }, 2000);
        });
        
        document.getElementById('view-backtests').addEventListener('click', function() {
            alert('Backtest results would open in a new window/modal in the full implementation.');
        });
        
        // Auto-refresh signals every 5 minutes
        setInterval(function() {
            loadSignals();
            console.log('Signals auto-refreshed');
        }, 5 * 60 * 1000);
    </script>
</body>
</html>