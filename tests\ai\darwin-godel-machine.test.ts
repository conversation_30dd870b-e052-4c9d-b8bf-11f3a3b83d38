// tests/ai/darwin-godel-machine.test.ts
import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { DarwinGodelMachine } from '../../src/ai/darwin-godel-machine';
import { ForexDataProvider } from '../../src/data-management/forex-data-provider';

// TDD Tests for Reproducible Evolution
describe('DarwinGodelMachine - TDD Implementation', () => {
  let darwin: DarwinGodelMachine;
  let mockDataProvider: jest.Mocked<ForexDataProvider>;

  beforeEach(() => {
    mockDataProvider = {
      getData: jest.fn(),
      calculateDataHash: jest.fn()
    } as any;
    
    darwin = new DarwinGodelMachine(mockDataProvider);
  });

  describe('Reproducible Evolution', () => {
    it('should produce identical results with same seed', async () => {
      // Arrange
      const strategy = {
        id: 'test_strategy',
        parameters: { rsi_period: 14, stop_loss: 20 }
      };
      const data = { candles: generateTestCandles(100) };
      const seed = 'test_seed_123';
      
      mockDataProvider.getData.mockResolvedValue({
        success: true,
        data: { candles: data.candles }
      });
      
      // Act
      const result1 = await darwin.evolve(strategy, data, { seed });
      const result2 = await darwin.evolve(strategy, data, { seed });
      
      // Assert
      expect(result1.optimizedParameters).toEqual(result2.optimizedParameters);
      expect(result1.finalFitness).toBe(result2.finalFitness);
      expect(result1.auditTrail.seed).toBe(seed);
    });

    it('should produce different results with different seeds', async () => {
      // Arrange
      const strategy = {
        id: 'test_strategy',
        parameters: { rsi_period: 14 }
      };
      const data = { candles: generateTestCandles(100) };
      
      mockDataProvider.getData.mockResolvedValue({
        success: true,
        data: { candles: data.candles }
      });
      
      // Act
      const result1 = await darwin.evolve(strategy, data, { seed: 'seed1' });
      const result2 = await darwin.evolve(strategy, data, { seed: 'seed2' });
      
      // Assert
      expect(result1.optimizedParameters).not.toEqual(result2.optimizedParameters);
    });

    it('should generate deterministic random numbers with seed', () => {
      // Act
      const random1 = darwin.seededRandom('test_seed');
      const random2 = darwin.seededRandom('test_seed');
      const random3 = darwin.seededRandom('different_seed');
      
      // Assert
      expect(random1).toBe(random2); // Same seed = same result
      expect(random1).not.toBe(random3); // Different seed = different result
    });
  });

  describe('Transparent Fitness Calculation', () => {
    it('should calculate fitness using verifiable formula', async () => {
      // Arrange
      const backtestResults = {
        sharpeRatio: 1.5,
        profitFactor: 1.8,
        winRate: 60,
        maxDrawdown: 15
      };
      
      // Act
      const fitness = darwin.calculateFitness(backtestResults);
      
      // Assert
      // Expected: (0.3 * 1.5) + (0.2 * 1.8) + (0.2 * 60) + (0.3 * (100 - 15))
      // = 0.45 + 0.36 + 12 + 25.5 = 38.31
      expect(fitness).toBeCloseTo(38.31, 2);
    });

    it('should provide fitness breakdown for transparency', async () => {
      // Arrange
      const backtestResults = {
        sharpeRatio: 2.0,
        profitFactor: 2.5,
        winRate: 70,
        maxDrawdown: 10
      };
      
      // Act
      const breakdown = darwin.getFitnessBreakdown(backtestResults);
      
      // Assert
      expect(breakdown.sharpeComponent).toBeCloseTo(0.6, 2); // 0.3 * 2.0
      expect(breakdown.profitComponent).toBeCloseTo(0.5, 2); // 0.2 * 2.5
      expect(breakdown.winRateComponent).toBeCloseTo(14, 2); // 0.2 * 70
      expect(breakdown.drawdownComponent).toBeCloseTo(27, 2); // 0.3 * (100 - 10)
      expect(breakdown.totalFitness).toBeCloseTo(42.1, 2);
    });

    it('should reject invalid backtest results', async () => {
      // Arrange
      const invalidResults = {
        sharpeRatio: -1, // Invalid
        profitFactor: 0.5,
        winRate: 150, // Invalid: > 100%
        maxDrawdown: -5 // Invalid: negative
      };
      
      // Act & Assert
      expect(() => darwin.calculateFitness(invalidResults)).toThrow('Invalid backtest results');
    });
  });

  describe('Real Backtest Integration', () => {
    it('should only use real historical data for evolution', async () => {
      // Arrange
      const strategy = { id: 'test', parameters: { rsi_period: 14 } };
      const realData = { candles: generateTestCandles(1000) };
      
      mockDataProvider.getData.mockResolvedValue({
        success: true,
        data: {
          candles: realData.candles,
          verification: {
            originalHash: 'sha256:abc123',
            dataPoints: 1000
          }
        }
      });
      
      // Act
      const result = await darwin.evolve(strategy, realData, { generations: 5 });
      
      // Assert
      expect(result.auditTrail.dataHash).toBe('sha256:abc123');
      expect(result.auditTrail.dataPoints).toBe(1000);
      expect(result.auditTrail.backtestsPerformed).toBeGreaterThan(0);
    });

    it('should reject evolution with insufficient data', async () => {
      // Arrange
      const strategy = { id: 'test', parameters: { rsi_period: 14 } };
      const insufficientData = { candles: generateTestCandles(10) }; // Too few candles
      
      mockDataProvider.getData.mockResolvedValue({
        success: true,
        data: { candles: insufficientData.candles }
      });
      
      // Act & Assert
      await expect(darwin.evolve(strategy, insufficientData))
        .rejects.toThrow('Insufficient data for reliable evolution');
    });

    it('should validate data integrity before evolution', async () => {
      // Arrange
      const strategy = { id: 'test', parameters: { rsi_period: 14 } };
      const corruptedData = {
        candles: [
          { open: 1.1, high: 1.05, low: 1.09, close: 1.08 } // Invalid OHLC
        ]
      };
      
      mockDataProvider.getData.mockResolvedValue({
        success: true,
        data: { candles: corruptedData.candles }
      });
      
      // Act & Assert
      await expect(darwin.evolve(strategy, corruptedData))
        .rejects.toThrow('Data validation failed');
    });
  });

  describe('Audit Trail Generation', () => {
    it('should create complete audit trail for evolution', async () => {
      // Arrange
      const strategy = { id: 'test_strategy', parameters: { rsi_period: 14 } };
      const data = { candles: generateTestCandles(500) };
      
      mockDataProvider.getData.mockResolvedValue({
        success: true,
        data: {
          candles: data.candles,
          verification: { originalHash: 'sha256:test123' }
        }
      });
      
      // Act
      const result = await darwin.evolve(strategy, data, { 
        seed: 'audit_test',
        generations: 3
      });
      
      // Assert
      expect(result.auditTrail).toBeDefined();
      expect(result.auditTrail.evolutionId).toMatch(/^evo_\d+_[a-z0-9]+$/);
      expect(result.auditTrail.seed).toBe('audit_test');
      expect(result.auditTrail.generations).toBe(3);
      expect(result.auditTrail.startTime).toBeDefined();
      expect(result.auditTrail.endTime).toBeDefined();
      expect(result.auditTrail.dataHash).toBe('sha256:test123');
    });

    it('should track all parameter mutations', async () => {
      // Arrange
      const strategy = {
        id: 'test',
        parameters: { rsi_period: 14, stop_loss: 20, take_profit: 40 }
      };
      const data = { candles: generateTestCandles(200) };
      
      mockDataProvider.getData.mockResolvedValue({
        success: true,
        data: { candles: data.candles }
      });
      
      // Act
      const result = await darwin.evolve(strategy, data, { generations: 2 });
      
      // Assert
      expect(result.auditTrail.mutations).toBeDefined();
      expect(result.auditTrail.mutations.length).toBeGreaterThan(0);
      
      const mutation = result.auditTrail.mutations[0];
      expect(mutation.parameter).toBeDefined();
      expect(mutation.oldValue).toBeDefined();
      expect(mutation.newValue).toBeDefined();
      expect(mutation.generation).toBeDefined();
    });
  });

  describe('Parameter Evolution', () => {
    it('should evolve parameters within realistic ranges', async () => {
      // Arrange
      const strategy = {
        id: 'rsi_strategy',
        parameters: { rsi_period: 14 },
        parameterRanges: {
          rsi_period: { min: 5, max: 50 }
        }
      };
      const data = { candles: generateTestCandles(300) };
      
      mockDataProvider.getData.mockResolvedValue({
        success: true,
        data: { candles: data.candles }
      });
      
      // Act
      const result = await darwin.evolve(strategy, data, { generations: 10 });
      
      // Assert
      expect(result.optimizedParameters.rsi_period).toBeGreaterThanOrEqual(5);
      expect(result.optimizedParameters.rsi_period).toBeLessThanOrEqual(50);
    });

    it('should improve fitness over generations', async () => {
      // Arrange
      const strategy = { id: 'test', parameters: { rsi_period: 14 } };
      const data = { candles: generateTestCandles(400) };
      
      mockDataProvider.getData.mockResolvedValue({
        success: true,
        data: { candles: data.candles }
      });
      
      // Act
      const result = await darwin.evolve(strategy, data, { generations: 5 });
      
      // Assert
      expect(result.improvementPercentage).toBeGreaterThan(0);
      expect(result.finalFitness).toBeGreaterThan(result.initialFitness);
    });
  });

  describe('Error Handling', () => {
    it('should handle data provider errors gracefully', async () => {
      // Arrange
      const strategy = { id: 'test', parameters: { rsi_period: 14 } };
      const data = { candles: [] };
      
      mockDataProvider.getData.mockRejectedValue(new Error('Data provider error'));
      
      // Act & Assert
      await expect(darwin.evolve(strategy, data))
        .rejects.toThrow('Evolution failed: Data provider error');
    });

    it('should validate strategy parameters before evolution', async () => {
      // Arrange
      const invalidStrategy = {
        id: '', // Invalid: empty ID
        parameters: {}
      };
      const data = { candles: generateTestCandles(100) };
      
      // Act & Assert
      await expect(darwin.evolve(invalidStrategy, data))
        .rejects.toThrow('Invalid strategy configuration');
    });
  });
});

// Helper function for tests
function generateTestCandles(count: number): any[] {
  const candles = [];
  const baseTime = new Date('2024-01-01').getTime();
  
  for (let i = 0; i < count; i++) {
    const open = 1.1 + Math.random() * 0.01;
    const close = open + (Math.random() - 0.5) * 0.005;
    const high = Math.max(open, close) + Math.random() * 0.002;
    const low = Math.min(open, close) - Math.random() * 0.002;
    
    candles.push({
      timestamp: new Date(baseTime + i * 3600000), // Hourly candles
      open: parseFloat(open.toFixed(5)),
      high: parseFloat(high.toFixed(5)),
      low: parseFloat(low.toFixed(5)),
      close: parseFloat(close.toFixed(5)),
      volume: Math.floor(Math.random() * 1000000)
    });
  }
  
  return candles;
}