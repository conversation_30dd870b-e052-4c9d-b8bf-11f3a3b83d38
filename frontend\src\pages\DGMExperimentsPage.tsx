﻿/**
 * DGM Experiments Page
 * Darwin Gödel Machine experiments interface with Trading Oracle integration
 */

import { useState } from 'react';
import { DGMExperiments } from '@/components/dgm/DGMExperiments';
import TradingOracle from '@/components/TradingOracle';
import { Brain, FlaskConical, BarChart3 } from 'lucide-react';

export function DGMExperimentsPage() {
  const [activeTab, setActiveTab] = useState<'oracle' | 'experiments' | 'analytics'>('oracle');

  const tabs = [
    {
      id: 'oracle' as const,
      label: 'Trading Oracle',
      icon: Brain,
      description: 'Natural language trading interface with AI analysis'
    },
    {
      id: 'experiments' as const,
      label: 'DGM Experiments',
      icon: FlaskConical,
      description: 'Darwin Gödel Machine evolution experiments'
    },
    {
      id: 'analytics' as const,
      label: 'Analytics',
      icon: BarChart3,
      description: 'Performance analytics and proven strategies'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <div className="bg-black/20 backdrop-blur-sm border-b border-purple-500/20 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Brain className="w-10 h-10 text-purple-400" />
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full animate-pulse"></div>
              </div>
              <div>
                <h1 className="text-3xl font-bold text-white">Darwin Gödel Machine</h1>
                <p className="text-purple-300">Revolutionary AI Trading Platform with Formal Verification</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="px-4 py-2 bg-green-500/20 text-green-400 rounded-full border border-green-500/30">
                <span className="text-sm font-medium">System Active</span>
              </div>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="flex space-x-1 bg-black/30 rounded-lg p-1">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 rounded-md transition-all duration-200 ${
                    activeTab === tab.id
                      ? 'bg-purple-600 text-white shadow-lg'
                      : 'text-purple-300 hover:text-white hover:bg-purple-600/20'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <span className="font-medium">{tab.label}</span>
                </button>
              );
            })}
          </div>

          {/* Tab Description */}
          <div className="mt-4">
            <p className="text-purple-200 text-sm">
              {tabs.find(tab => tab.id === activeTab)?.description}
            </p>
          </div>
        </div>
      </div>

      {/* Tab Content */}
      <div className="flex-1">
        {activeTab === 'oracle' && (
          <div className="h-[calc(100vh-200px)]">
            <TradingOracle />
          </div>
        )}
        
        {activeTab === 'experiments' && (
          <div className="p-6">
            <div className="max-w-7xl mx-auto">
              <DGMExperiments />
            </div>
          </div>
        )}
        
        {activeTab === 'analytics' && (
          <div className="p-6">
            <div className="max-w-7xl mx-auto">
              <DGMAnalytics />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Analytics component for the third tab
function DGMAnalytics() {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Performance Cards */}
        <div className="bg-black/40 backdrop-blur-sm border border-purple-500/20 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Proven Strategies</h3>
            <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
          </div>
          <div className="text-3xl font-bold text-purple-400 mb-2">127</div>
          <p className="text-sm text-purple-300">Mathematically verified</p>
        </div>

        <div className="bg-black/40 backdrop-blur-sm border border-purple-500/20 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Evolution Jobs</h3>
            <div className="w-3 h-3 bg-blue-400 rounded-full animate-pulse"></div>
          </div>
          <div className="text-3xl font-bold text-blue-400 mb-2">23</div>
          <p className="text-sm text-purple-300">Currently running</p>
        </div>

        <div className="bg-black/40 backdrop-blur-sm border border-purple-500/20 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Avg Fitness</h3>
            <div className="w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
          </div>
          <div className="text-3xl font-bold text-yellow-400 mb-2">0.847</div>
          <p className="text-sm text-purple-300">Sharpe ratio</p>
        </div>

        <div className="bg-black/40 backdrop-blur-sm border border-purple-500/20 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Success Rate</h3>
            <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
          </div>
          <div className="text-3xl font-bold text-green-400 mb-2">94.2%</div>
          <p className="text-sm text-purple-300">Verification rate</p>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-black/40 backdrop-blur-sm border border-purple-500/20 rounded-lg p-6">
        <h3 className="text-xl font-semibold text-white mb-4">Recent Evolution Results</h3>
        <div className="space-y-4">
          {[
            { pair: 'EUR/USD', timeframe: '4H', fitness: 0.923, strategies: 12, status: 'completed' },
            { pair: 'GBP/JPY', timeframe: '1H', fitness: 0.856, strategies: 8, status: 'running' },
            { pair: 'USD/CAD', timeframe: '1D', fitness: 0.791, strategies: 15, status: 'completed' },
            { pair: 'AUD/USD', timeframe: '4H', fitness: 0.834, strategies: 9, status: 'verifying' }
          ].map((job, index) => (
            <div key={index} className="flex items-center justify-between p-4 bg-purple-900/20 rounded-lg border border-purple-500/10">
              <div className="flex items-center space-x-4">
                <div className={`w-3 h-3 rounded-full ${
                  job.status === 'completed' ? 'bg-green-400' :
                  job.status === 'running' ? 'bg-blue-400 animate-pulse' :
                  'bg-yellow-400 animate-pulse'
                }`}></div>
                <div>
                  <div className="text-white font-medium">{job.pair} {job.timeframe}</div>
                  <div className="text-purple-300 text-sm">{job.strategies} proven strategies</div>
                </div>
              </div>
              <div className="text-right">
                <div className="text-purple-400 font-bold">{job.fitness.toFixed(3)}</div>
                <div className="text-purple-300 text-sm capitalize">{job.status}</div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Top Performing Strategies */}
      <div className="bg-black/40 backdrop-blur-sm border border-purple-500/20 rounded-lg p-6">
        <h3 className="text-xl font-semibold text-white mb-4">Top Performing Strategies</h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-purple-500/20">
                <th className="text-left text-purple-300 font-medium py-3">Strategy</th>
                <th className="text-left text-purple-300 font-medium py-3">Pair</th>
                <th className="text-left text-purple-300 font-medium py-3">Fitness</th>
                <th className="text-left text-purple-300 font-medium py-3">Win Rate</th>
                <th className="text-left text-purple-300 font-medium py-3">Status</th>
              </tr>
            </thead>
            <tbody>
              {[
                { name: 'RSI Mean Reversion Pro', pair: 'EUR/USD', fitness: 0.945, winRate: 0.73, verified: true },
                { name: 'MACD Crossover Elite', pair: 'GBP/JPY', fitness: 0.912, winRate: 0.68, verified: true },
                { name: 'Bollinger Breakout', pair: 'USD/CAD', fitness: 0.887, winRate: 0.71, verified: true },
                { name: 'Stoch Divergence', pair: 'AUD/USD', fitness: 0.863, winRate: 0.65, verified: false }
              ].map((strategy, index) => (
                <tr key={index} className="border-b border-purple-500/10">
                  <td className="py-4 text-white font-medium">{strategy.name}</td>
                  <td className="py-4 text-purple-300">{strategy.pair}</td>
                  <td className="py-4 text-purple-400 font-bold">{strategy.fitness.toFixed(3)}</td>
                  <td className="py-4 text-green-400">{(strategy.winRate * 100).toFixed(1)}%</td>
                  <td className="py-4">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      strategy.verified 
                        ? 'bg-green-500/20 text-green-400 border border-green-500/30'
                        : 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30'
                    }`}>
                      {strategy.verified ? 'Verified' : 'Pending'}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}