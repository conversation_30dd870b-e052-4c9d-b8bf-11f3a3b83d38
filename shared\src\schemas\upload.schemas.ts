import { z } from 'zod';

// Column Type Enum
export const ColumnTypeSchema = z.enum([
  'date',
  'datetime',
  'number',
  'string',
  'boolean',
  'price',
  'volume',
  'percentage'
]);

// Column Definition Schema
export const ColumnDefinitionSchema = z.object({
  name: z.string().min(1),
  type: ColumnTypeSchema,
  isRequired: z.boolean(),
  description: z.string().optional(),
  format: z.string().optional(), // For date/datetime formats
});

// File Upload Schema
export const FileUploadSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  filename: z.string().min(1),
  originalName: z.string().min(1),
  mimetype: z.string(),
  size: z.number().positive(),
  path: z.string().min(1),
  status: z.enum(['pending', 'processing', 'completed', 'failed']),
  errorMessage: z.string().optional(),
  rowCount: z.number().nonnegative().optional(),
  columnDefinitions: z.array(ColumnDefinitionSchema).optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Upload Request Schema
export const UploadRequestSchema = z.object({
  file: z.any(), // File object from multipart form
  description: z.string().max(500).optional(),
});

// Column Inference Request Schema
export const ColumnInferenceRequestSchema = z.object({
  fileId: z.string().uuid(),
  sampleRows: z.number().min(1).max(100).default(10),
});

// Column Inference Response Schema
export const ColumnInferenceResponseSchema = z.object({
  fileId: z.string().uuid(),
  suggestedColumns: z.array(ColumnDefinitionSchema),
  sampleData: z.array(z.record(z.string(), z.unknown())),
  confidence: z.number().min(0).max(1),
});

// Column Mapping Schema
export const ColumnMappingSchema = z.object({
  fileId: z.string().uuid(),
  columnMappings: z.array(z.object({
    sourceColumn: z.string(),
    targetColumn: z.string(),
    type: ColumnTypeSchema,
    transformation: z.string().optional(),
  })),
});

// Data Preview Schema
export const DataPreviewSchema = z.object({
  fileId: z.string().uuid(),
  headers: z.array(z.string()),
  rows: z.array(z.array(z.unknown())),
  totalRows: z.number().nonnegative(),
  previewRows: z.number().nonnegative(),
});

// Upload Progress Schema
export const UploadProgressSchema = z.object({
  fileId: z.string().uuid(),
  status: z.enum(['pending', 'processing', 'completed', 'failed']),
  progress: z.number().min(0).max(100),
  processedRows: z.number().nonnegative(),
  totalRows: z.number().nonnegative(),
  errors: z.array(z.object({
    row: z.number().nonnegative(),
    column: z.string(),
    message: z.string(),
  })).optional(),
});

// Types
export type ColumnType = z.infer<typeof ColumnTypeSchema>;
export type ColumnDefinition = z.infer<typeof ColumnDefinitionSchema>;
export type FileUpload = z.infer<typeof FileUploadSchema>;
export type UploadRequest = z.infer<typeof UploadRequestSchema>;
export type ColumnInferenceRequest = z.infer<typeof ColumnInferenceRequestSchema>;
export type ColumnInferenceResponse = z.infer<typeof ColumnInferenceResponseSchema>;
export type ColumnMapping = z.infer<typeof ColumnMappingSchema>;
export type DataPreview = z.infer<typeof DataPreviewSchema>;
export type UploadProgress = z.infer<typeof UploadProgressSchema>;