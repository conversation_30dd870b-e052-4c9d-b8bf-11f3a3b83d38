// import React from 'react'; // Not needed with new JSX transform
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Thum<PERSON>Up, ThumbsDown } from 'lucide-react';

interface ChatMessageProps {
  message: {
    id: string;
    content: string;
    role: 'user' | 'assistant';
    timestamp: string;
    model?: string;
  };
  onCopy?: (content: string) => void;
  onFeedback?: (messageId: string, type: 'up' | 'down') => void;
}

export function ChatMessage({ message, onCopy, onFeedback }: ChatMessageProps) {
  const isAssistant = message.role === 'assistant';
  
  const handleCopy = () => {
    onCopy?.(message.content);
    navigator.clipboard.writeText(message.content);
  };

  return (
    <div className={`flex space-x-3 ${isAssistant ? '' : 'flex-row-reverse space-x-reverse'}`}>
      {/* Avatar */}
      <div className={`
        flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center
        ${isAssistant ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'}
      `}>
        {isAssistant ? <Bot className="w-4 h-4" /> : <User className="w-4 h-4" />}
      </div>
      
      {/* Message Content */}
      <div className={`flex-1 max-w-xs lg:max-w-md ${isAssistant ? '' : 'text-right'}`}>
        <div className={`
          inline-block px-4 py-2 rounded-lg text-sm
          ${isAssistant 
            ? 'bg-white border border-gray-200 text-gray-900' 
            : 'bg-blue-600 text-white'
          }
        `}>
          <div className="whitespace-pre-wrap">{message.content}</div>
          
          {/* Model info for assistant messages */}
          {isAssistant && message.model && (
            <div className="text-xs text-gray-500 mt-1 border-t border-gray-100 pt-1">
              {message.model}
            </div>
          )}
        </div>
        
        {/* Message Actions */}
        {isAssistant && (
          <div className="flex items-center space-x-2 mt-1">
            <button
              onClick={handleCopy}
              className="p-1 hover:bg-gray-100 rounded text-gray-400 hover:text-gray-600"
              title="Copy message"
            >
              <Copy className="w-3 h-3" />
            </button>
            
            {onFeedback && (
              <>
                <button
                  onClick={() => onFeedback(message.id, 'up')}
                  className="p-1 hover:bg-gray-100 rounded text-gray-400 hover:text-green-600"
                  title="Helpful"
                >
                  <ThumbsUp className="w-3 h-3" />
                </button>
                
                <button
                  onClick={() => onFeedback(message.id, 'down')}
                  className="p-1 hover:bg-gray-100 rounded text-gray-400 hover:text-red-600"
                  title="Not helpful"
                >
                  <ThumbsDown className="w-3 h-3" />
                </button>
              </>
            )}
          </div>
        )}
        
        {/* Timestamp */}
        <div className={`text-xs text-gray-500 mt-1 ${isAssistant ? '' : 'text-right'}`}>
          {new Date(message.timestamp).toLocaleTimeString()}
        </div>
      </div>
    </div>
  );
}