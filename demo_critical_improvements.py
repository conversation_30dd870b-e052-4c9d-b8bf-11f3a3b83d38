#!/usr/bin/env python3
"""
AI Trading Platform - Critical Improvements Demo

This script demonstrates the three critical improvements implemented:
1. Enhanced Type-Safe Event System
2. Zero-Hallucination ML Model Pipeline  
3. Advanced Risk Management System

Run with: python demo_critical_improvements.py
"""

import asyncio
import logging
from datetime import datetime
from src.events import EventBus
from src.ml import ModelPipeline, PredictionInput, PredictionFeatures
from src.risk import RiskManager, PortfolioState, Position, RiskLimits

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CriticalImprovementsDemo:
    """Demo class showcasing critical improvements"""
    
    def __init__(self):
        self.event_bus = EventBus()
        self.ml_pipeline = ModelPipeline()
        self.risk_manager = RiskManager(self.event_bus)
        self.demo_events = []
        
        # Subscribe to all events for demo
        self._setup_event_monitoring()
    
    def _setup_event_monitoring(self):
        """Setup event monitoring for demo"""
        def event_logger(event):
            self.demo_events.append({
                'timestamp': datetime.now(),
                'type': event.type,
                'data': event
            })
            logger.info(f"📨 Event received: {event.type}")
        
        # Subscribe to all event types
        for event_type in ['STRATEGY_EXECUTED', 'MARKET_DATA_RECEIVED', 'RISK_LIMIT_BREACHED']:
            self.event_bus.subscribe(event_type, event_logger)
    
    async def demo_event_system(self):
        """Demonstrate the type-safe event system"""
        print("\n" + "="*60)
        print("🚀 DEMO 1: Enhanced Type-Safe Event System")
        print("="*60)
        
        # Valid market data event
        print("\n📊 Publishing valid market data event...")
        market_data_event = {
            'type': 'MARKET_DATA_RECEIVED',
            'timestamp': datetime.now().isoformat(),
            'payload': {
                'symbol': 'EURUSD',
                'data': {
                    'open': 1.2000,
                    'high': 1.2050,
                    'low': 1.1950,
                    'close': 1.2020,
                    'volume': 150000,
                    'timestamp': datetime.now().isoformat()
                },
                'source': 'dukascopy',
                'integrity_hash': 'a1b2c3d4e5f6' + 'a' * 52  # 64-char hash
            }
        }
        
        success = await self.event_bus.publish(market_data_event)
        print(f"✅ Market data event published: {success}")
        
        # Valid strategy execution event
        print("\n🎯 Publishing strategy execution event...")
        strategy_event = {
            'type': 'STRATEGY_EXECUTED',
            'timestamp': datetime.now().isoformat(),
            'strategy_id': 'RSI_MACD_DEMO',
            'payload': {
                'symbol': 'EURUSD',
                'action': 'BUY',
                'confidence': 0.85,
                'parameters': {
                    'rsi_period': 14,
                    'macd_fast': 12,
                    'macd_slow': 26
                },
                'reasoning': 'RSI oversold + MACD bullish crossover'
            },
            'metadata': {
                'dummy_mode': True,
                'user_id': '12345678-1234-1234-1234-123456789012'
            }
        }
        
        success = await self.event_bus.publish(strategy_event)
        print(f"✅ Strategy event published: {success}")
        
        # Try invalid event (should fail validation)
        print("\n❌ Testing invalid event validation...")
        invalid_event = {
            'type': 'STRATEGY_EXECUTED',
            'timestamp': 'invalid-date',
            'strategy_id': '',  # Empty - should fail
            'payload': {
                'symbol': 'INVALID',  # Wrong format
                'action': 'INVALID_ACTION',
                'confidence': 1.5  # Out of range
            }
        }
        
        try:
            await self.event_bus.publish(invalid_event)
            print("❌ Invalid event was accepted (this shouldn't happen)")
        except Exception as e:
            print(f"✅ Invalid event correctly rejected: {str(e)[:100]}...")
        
        # Test idempotency
        print("\n🔄 Testing event idempotency...")
        duplicate_result1 = await self.event_bus.publish(strategy_event)
        duplicate_result2 = await self.event_bus.publish(strategy_event)
        print(f"✅ First duplicate: {duplicate_result1}, Second duplicate: {duplicate_result2}")
        
        print(f"\n📈 Event system stats:")
        print(f"   - Total events processed: {self.event_bus.get_processed_event_count()}")
        print(f"   - Event history size: {len(self.event_bus.get_event_history())}")
        print(f"   - Strategy event subscribers: {self.event_bus.get_subscriber_count('STRATEGY_EXECUTED')}")
    
    async def demo_ml_pipeline(self):
        """Demonstrate the ML model pipeline"""
        print("\n" + "="*60)
        print("🧠 DEMO 2: Zero-Hallucination ML Model Pipeline")
        print("="*60)
        
        # Valid prediction
        print("\n🔮 Making ML prediction with valid input...")
        valid_input = PredictionInput(
            symbol='EURUSD',
            features=PredictionFeatures(
                rsi=65.5,
                macd=0.0012,
                volume=150000,
                sma_20=1.2050,
                ema_12=1.2048,
                bollinger_upper=1.2100,
                bollinger_lower=1.1900
            )
        )
        
        prediction = await self.ml_pipeline.predict(valid_input)
        print(f"✅ Prediction successful:")
        print(f"   - Value: {prediction.value:.4f}")
        print(f"   - Confidence: {prediction.confidence:.3f}")
        print(f"   - Model Version: {prediction.model_version}")
        print(f"   - Input Hash: {prediction.input_hash[:16]}...")
        print(f"   - Prediction Hash: {prediction.prediction_hash[:16]}...")
        print(f"   - Processing Time: {prediction.metadata.processing_time_ms:.2f}ms")
        print(f"   - Features Used: {', '.join(prediction.metadata.features_used)}")
        print(f"   - Rejected: {prediction.rejected}")
        
        # Test deterministic predictions (caching)
        print("\n🔄 Testing prediction determinism (caching)...")
        prediction2 = await self.ml_pipeline.predict(valid_input)
        print(f"✅ Predictions are deterministic:")
        print(f"   - Same value: {prediction.value == prediction2.value}")
        print(f"   - Same hash: {prediction.input_hash == prediction2.input_hash}")
        
        # Test invalid input
        print("\n❌ Testing invalid input validation...")
        try:
            invalid_input = {
                'symbol': 'INVALID',
                'features': {
                    'rsi': 150,  # Invalid RSI
                    'macd': None,
                    'volume': -100  # Negative volume
                }
            }
            await self.ml_pipeline.predict(invalid_input)
            print("❌ Invalid input was accepted (this shouldn't happen)")
        except Exception as e:
            print(f"✅ Invalid input correctly rejected: {str(e)[:100]}...")
        
        # Test confidence threshold
        print("\n🎚️ Testing confidence threshold...")
        self.ml_pipeline.set_min_confidence_threshold(0.9)  # Very high threshold
        
        low_confidence_input = PredictionInput(
            symbol='EURUSD',
            features=PredictionFeatures(
                rsi=50.0,  # Neutral values likely to produce low confidence
                macd=0.0,
                volume=1000
            )
        )
        
        low_conf_prediction = await self.ml_pipeline.predict(low_confidence_input)
        print(f"✅ Low confidence prediction:")
        print(f"   - Confidence: {low_conf_prediction.confidence:.3f}")
        print(f"   - Rejected: {low_conf_prediction.rejected}")
        print(f"   - Reason: {low_conf_prediction.rejection_reason}")
        
        # Reset threshold
        self.ml_pipeline.set_min_confidence_threshold(0.7)
        
        print(f"\n📊 ML Pipeline stats:")
        cache_stats = self.ml_pipeline.get_cache_stats()
        print(f"   - Cache size: {cache_stats['cache_size']}")
        print(f"   - Max cache size: {cache_stats['max_cache_size']}")
        current_model = self.ml_pipeline.get_current_model()
        if current_model:
            print(f"   - Current model: {current_model.version} ({current_model.algorithm})")
            print(f"   - Model accuracy: {current_model.performance_metrics.accuracy:.3f}")
            print(f"   - Backtest Sharpe: {current_model.performance_metrics.backtest_sharpe:.2f}")
    
    async def demo_risk_management(self):
        """Demonstrate the risk management system"""
        print("\n" + "="*60)
        print("⚠️  DEMO 3: Advanced Risk Management System")
        print("="*60)
        
        # Test position size limits
        print("\n📏 Testing position size limits...")
        portfolio = PortfolioState(
            balance=10000,
            equity=10000,
            positions=[
                Position(symbol='EURUSD', size=5.0, unrealized_pnl=-100),
                Position(symbol='GBPUSD', size=3.0, unrealized_pnl=50)
            ],
            total_exposure=8.0
        )
        
        large_position = {
            'symbol': 'USDJPY',
            'size': 3.0,  # Would exceed max exposure
            'estimated_margin': 1000
        }
        
        risk_check = await self.risk_manager.evaluate_new_position(portfolio, large_position)
        print(f"✅ Position risk evaluation:")
        print(f"   - Approved: {risk_check.approved}")
        print(f"   - Risk Score: {risk_check.risk_score:.3f}")
        print(f"   - Violations: {', '.join(risk_check.violations) if risk_check.violations else 'None'}")
        print(f"   - Warnings: {', '.join(risk_check.warnings) if risk_check.warnings else 'None'}")
        print(f"   - Recommendations: {', '.join(risk_check.recommended_actions[:2])}")
        
        # Test drawdown limits
        print("\n📉 Testing drawdown limits...")
        high_drawdown_portfolio = PortfolioState(
            balance=10000,
            equity=7000,  # 30% drawdown
            positions=[],
            total_exposure=0,
            max_equity=10500
        )
        
        portfolio_risk = await self.risk_manager.evaluate_portfolio_risk(high_drawdown_portfolio)
        print(f"✅ Portfolio risk evaluation:")
        print(f"   - Critical: {portfolio_risk.critical}")
        print(f"   - Risk Score: {portfolio_risk.risk_score:.3f}")
        print(f"   - Violations: {', '.join(portfolio_risk.violations)}")
        print(f"   - Emergency Actions: {', '.join(portfolio_risk.recommended_actions[:3])}")
        
        # Test correlation risk
        print("\n🔗 Testing correlation risk...")
        correlated_portfolio = PortfolioState(
            balance=10000,
            equity=10000,
            positions=[
                Position(symbol='EURUSD', size=2.0, unrealized_pnl=0),
                Position(symbol='EURJPY', size=2.0, unrealized_pnl=0),
                Position(symbol='EURGBP', size=2.0, unrealized_pnl=0)
            ],
            total_exposure=6.0
        )
        
        correlation_risk = self.risk_manager.calculate_correlation_risk(correlated_portfolio)
        print(f"✅ Correlation risk analysis:")
        print(f"   - Overall Score: {correlation_risk.overall_score:.3f}")
        print(f"   - Diversification Score: {correlation_risk.diversification_score:.3f}")
        print(f"   - Warnings: {', '.join(correlation_risk.warnings)}")
        
        # Get diversification suggestions
        suggestions = self.risk_manager.correlation_analyzer.get_diversification_suggestions(
            correlated_portfolio.positions
        )
        print(f"   - Diversification Suggestions: {', '.join(suggestions[:3])}")
        
        # Test risk metrics
        print("\n📊 Comprehensive risk metrics...")
        risk_metrics = self.risk_manager.get_risk_metrics(correlated_portfolio)
        print(f"✅ Risk metrics:")
        print(f"   - Current Drawdown: {risk_metrics['current_drawdown']:.2%}")
        print(f"   - Total Exposure: {risk_metrics['total_exposure']:.1f}")
        print(f"   - Correlation Risk: {risk_metrics['correlation_risk']:.3f}")
        print(f"   - Positions Count: {risk_metrics['positions_count']}")
        
        # Test risk limits update
        print("\n⚙️  Testing risk limits update...")
        new_limits = RiskLimits(
            max_drawdown_percent=15.0,
            max_daily_loss_percent=3.0,
            max_position_size_percent=5.0,
            max_total_exposure=5.0
        )
        
        self.risk_manager.update_risk_limits(new_limits)
        updated_limits = self.risk_manager.get_risk_limits()
        print(f"✅ Risk limits updated:")
        print(f"   - Max Drawdown: {updated_limits.max_drawdown_percent}%")
        print(f"   - Max Daily Loss: {updated_limits.max_daily_loss_percent}%")
        print(f"   - Max Position Size: {updated_limits.max_position_size_percent}%")
        print(f"   - Max Total Exposure: {updated_limits.max_total_exposure}x")
    
    async def demo_integration(self):
        """Demonstrate integration between all systems"""
        print("\n" + "="*60)
        print("🔄 DEMO 4: End-to-End Integration")
        print("="*60)
        
        print("\n🎬 Simulating complete trading workflow...")
        
        # Step 1: Market data arrives
        print("\n1️⃣ Market data received...")
        await self.event_bus.publish({
            'type': 'MARKET_DATA_RECEIVED',
            'timestamp': datetime.now().isoformat(),
            'payload': {
                'symbol': 'GBPUSD',
                'data': {
                    'open': 1.3000, 'high': 1.3080, 'low': 1.2950, 'close': 1.3070,
                    'volume': 200000, 'timestamp': datetime.now().isoformat()
                },
                'source': 'dukascopy',
                'integrity_hash': 'b1c2d3e4f5g6' + 'b' * 52
            }
        })
        
        # Step 2: ML prediction
        print("2️⃣ Making ML prediction...")
        prediction_input = PredictionInput(
            symbol='GBPUSD',
            features=PredictionFeatures(
                rsi=72.0,
                macd=0.0025,
                volume=200000,
                sma_20=1.3000,
                ema_12=1.3020
            )
        )
        
        prediction = await self.ml_pipeline.predict(prediction_input)
        print(f"   📊 Prediction: {prediction.value:.4f} (confidence: {prediction.confidence:.3f})")
        
        # Step 3: Risk evaluation
        print("3️⃣ Evaluating risk...")
        portfolio = PortfolioState(
            balance=10000,
            equity=10000,
            positions=[
                Position(symbol='EURUSD', size=1.0, unrealized_pnl=50)
            ],
            total_exposure=1.0
        )
        
        new_position = {
            'symbol': 'GBPUSD',
            'size': 1.5,
            'estimated_margin': 1500
        }
        
        risk_evaluation = await self.risk_manager.evaluate_new_position(portfolio, new_position)
        print(f"   ⚠️  Risk approved: {risk_evaluation.approved} (score: {risk_evaluation.risk_score:.3f})")
        
        # Step 4: Trading decision
        print("4️⃣ Making trading decision...")
        if risk_evaluation.approved and not prediction.rejected:
            action = 'BUY' if prediction.value > 0.5 else 'SELL'
            print(f"   ✅ Decision: {action} GBPUSD")
            
            # Publish strategy execution
            await self.event_bus.publish({
                'type': 'STRATEGY_EXECUTED',
                'timestamp': datetime.now().isoformat(),
                'strategy_id': 'INTEGRATED_ML_STRATEGY',
                'payload': {
                    'symbol': 'GBPUSD',
                    'action': action,
                    'confidence': prediction.confidence,
                    'parameters': {
                        'ml_prediction': prediction.value,
                        'risk_score': risk_evaluation.risk_score,
                        'prediction_hash': prediction.prediction_hash
                    }
                },
                'metadata': {
                    'dummy_mode': True,
                    'user_id': '12345678-1234-1234-1234-123456789012'
                }
            })
        else:
            reasons = []
            if not risk_evaluation.approved:
                reasons.append(f"Risk rejected: {', '.join(risk_evaluation.violations)}")
            if prediction.rejected:
                reasons.append(f"ML rejected: {prediction.rejection_reason}")
            
            print(f"   ❌ Decision: NO TRADE - {'; '.join(reasons)}")
        
        # Step 5: Show audit trail
        print("\n5️⃣ Audit trail summary:")
        await asyncio.sleep(0.1)  # Allow async processing
        
        print(f"   📝 Total events in demo: {len(self.demo_events)}")
        for i, event in enumerate(self.demo_events[-5:], 1):  # Show last 5 events
            print(f"   {i}. {event['type']} at {event['timestamp'].strftime('%H:%M:%S')}")
        
        history = self.event_bus.get_event_history()
        print(f"   📚 Event bus history: {len(history)} events")
        print(f"   🔒 Processed events: {self.event_bus.get_processed_event_count()}")
    
    async def run_demo(self):
        """Run the complete demo"""
        print("🎯 AI Trading Platform - Critical Improvements Demo")
        print("=" * 60)
        print("This demo showcases three critical improvements:")
        print("1. 🚀 Enhanced Type-Safe Event System")
        print("2. 🧠 Zero-Hallucination ML Model Pipeline")
        print("3. ⚠️  Advanced Risk Management System")
        print("4. 🔄 End-to-End Integration")
        
        try:
            await self.demo_event_system()
            await self.demo_ml_pipeline()
            await self.demo_risk_management()
            await self.demo_integration()
            
            print("\n" + "="*60)
            print("🎉 DEMO COMPLETED SUCCESSFULLY!")
            print("="*60)
            print("\nKey achievements demonstrated:")
            print("✅ Type-safe event validation with Pydantic schemas")
            print("✅ Complete ML prediction lineage and auditability")
            print("✅ Multi-layered risk management with real-time monitoring")
            print("✅ Event-driven architecture with idempotency")
            print("✅ Comprehensive correlation risk analysis")
            print("✅ End-to-end integration with full audit trail")
            
            print(f"\n📊 Final Statistics:")
            print(f"   - Events processed: {self.event_bus.get_processed_event_count()}")
            print(f"   - ML predictions made: {len(self.ml_pipeline.prediction_cache)}")
            print(f"   - Risk evaluations: Multiple portfolio and position assessments")
            print(f"   - Demo events captured: {len(self.demo_events)}")
            
        except Exception as e:
            logger.error(f"Demo failed: {str(e)}")
            raise

async def main():
    """Main demo function"""
    demo = CriticalImprovementsDemo()
    await demo.run_demo()

if __name__ == "__main__":
    asyncio.run(main())