document.addEventListener('DOMContentLoaded', () => {
    const chatInput = document.getElementById('chat-input');
    const promptCards = document.querySelectorAll('.prompt-card');
    const mql5CtaBtn = document.getElementById('mql5-cta-btn');
    const proModal = document.getElementById('pro-modal');
    const closeModalBtn = proModal.querySelector('.close-button');

    // Handle prompt card clicks
    promptCards.forEach(card => {
        card.addEventListener('click', () => {
            const promptText = card.getAttribute('data-prompt');
            chatInput.value = promptText;
            chatInput.focus();
            // Optionally, send a message to the chatbot automatically
            // sendChatMessage(`Let's build the '${card.querySelector('h4').innerText}' strategy.`);
        });
    });

    // Handle MQL5 Studio button click
    mql5CtaBtn.addEventListener('click', () => {
        proModal.classList.remove('hidden');
    });

    // Handle modal close
    closeModalBtn.addEventListener('click', () => {
        proModal.classList.add('hidden');
    });

    // Close modal if clicking outside the content
    window.addEventListener('click', (event) => {
        if (event.target === proModal) {
            proModal.classList.add('hidden');
        }
    });

    // Placeholder for sending chat messages
    const sendChatBtn = document.getElementById('send-chat-btn');
    sendChatBtn.addEventListener('click', () => {
        const message = chatInput.value.trim();
        if (message) {
            console.log('Sending message to chatbot:', message);
            // Here you would integrate with your chatbot API
            // For now, we'll just clear the input
            chatInput.value = '';
        }
    });
});
