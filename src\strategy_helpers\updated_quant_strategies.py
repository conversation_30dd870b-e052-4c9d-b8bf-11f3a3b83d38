"""
Updated Quantitative Strategies Library
Based on strategies from je-suis-tm/quant-trading GitHub repository
"""

from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import pandas as pd
import numpy as np
from abc import ABC, abstractmethod

class StrategyType(Enum):
    VOLATILITY = "volatility"
    PATTERN_RECOGNITION = "pattern_recognition"
    COMMODITY_TRADING = "commodity_trading"
    MONTE_CARLO = "monte_carlo"
    OPTIONS = "options"
    CANDLESTICK = "candlestick"
    BREAKOUT = "breakout"
    ALTERNATIVE_DATA = "alternative_data"
    PAIRS_TRADING = "pairs_trading"
    MEAN_REVERSION = "mean_reversion"
    MOMENTUM = "momentum"
    TREND_FOLLOWING = "trend_following"
    OSCILLATOR = "oscillator"

class TimeFrame(Enum):
    MINUTE_1 = "1m"
    MINUTE_5 = "5m"
    MINUTE_15 = "15m"
    MINUTE_30 = "30m"
    HOUR_1 = "1h"
    HOUR_4 = "4h"
    DAY_1 = "1d"
    WEEK_1 = "1w"

@dataclass
class StrategyMetadata:
    name: str
    description: str
    strategy_type: StrategyType
    complexity: str  # beginner, intermediate, advanced
    timeframes: List[TimeFrame]
    assets: List[str]  # forex, stocks, crypto, commodities, etc.
    expected_sharpe: float
    max_drawdown: float
    win_rate: float
    parameters: Dict[str, Any]
    requirements: List[str]

class BaseStrategy(ABC):
    """Base class for all trading strategies"""
    
    def __init__(self, name: str, **parameters):
        self.name = name
        self.parameters = parameters
    
    @abstractmethod
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate technical indicators"""
        pass
    
    @abstractmethod
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate trading signals"""
        pass
    
    def backtest(self, data: pd.DataFrame, initial_capital: float = 10000) -> Dict[str, Any]:
        """Basic backtesting framework"""
        signals_data = self.generate_signals(data.copy())
        
        # Simple backtesting logic
        positions = signals_data['signal'].fillna(0)
        returns = data['close'].pct_change()
        strategy_returns = positions.shift(1) * returns
        
        # Calculate performance metrics
        total_return = (1 + strategy_returns).prod() - 1
        sharpe_ratio = strategy_returns.mean() / strategy_returns.std() * np.sqrt(252) if strategy_returns.std() != 0 else 0
        max_drawdown = (strategy_returns.cumsum() - strategy_returns.cumsum().expanding().max()).min()
        
        # Win rate calculation
        winning_trades = strategy_returns[strategy_returns > 0]
        total_trades = strategy_returns[strategy_returns != 0]
        win_rate = len(winning_trades) / len(total_trades) if len(total_trades) > 0 else 0
        
        return {
            'total_return': total_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': abs(max_drawdown),
            'win_rate': win_rate,
            'total_trades': len(total_trades)
        }

# Strategy Implementations based on je-suis-tm/quant-trading

class VIXCalculatorStrategy(BaseStrategy):
    """VIX-based volatility trading strategy"""
    
    def __init__(self, **parameters):
        default_params = {
            'vix_threshold_low': 15,
            'vix_threshold_high': 25,
            'lookback_period': 20
        }
        default_params.update(parameters)
        super().__init__("VIX Calculator", **default_params)
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        # Simulate VIX calculation using price volatility
        data['returns'] = data['close'].pct_change()
        data['volatility'] = data['returns'].rolling(window=self.parameters['lookback_period']).std() * np.sqrt(252) * 100
        return data
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        data = self.calculate_indicators(data)
        data['signal'] = 0
        
        # Buy when volatility is low (market complacency)
        data.loc[data['volatility'] < self.parameters['vix_threshold_low'], 'signal'] = 1
        # Sell when volatility is high (market fear)
        data.loc[data['volatility'] > self.parameters['vix_threshold_high'], 'signal'] = -1
        
        return data

class PatternRecognitionStrategy(BaseStrategy):
    """Pattern recognition strategy for common chart patterns"""
    
    def __init__(self, **parameters):
        default_params = {
            'pattern_window': 10,
            'breakout_threshold': 0.02
        }
        default_params.update(parameters)
        super().__init__("Pattern Recognition", **default_params)
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        window = self.parameters['pattern_window']
        
        # Simple pattern detection
        data['high_max'] = data['high'].rolling(window=window).max()
        data['low_min'] = data['low'].rolling(window=window).min()
        data['range_pct'] = (data['high_max'] - data['low_min']) / data['close']
        
        return data
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        data = self.calculate_indicators(data)
        data['signal'] = 0
        
        # Breakout pattern
        threshold = self.parameters['breakout_threshold']
        data.loc[data['close'] > data['high_max'].shift(1) * (1 + threshold), 'signal'] = 1
        data.loc[data['close'] < data['low_min'].shift(1) * (1 - threshold), 'signal'] = -1
        
        return data

class CommodityTradingAdvisorStrategy(BaseStrategy):
    """CTA-style momentum strategy"""
    
    def __init__(self, **parameters):
        default_params = {
            'fast_ma': 10,
            'slow_ma': 30,
            'momentum_period': 14
        }
        default_params.update(parameters)
        super().__init__("Commodity Trading Advisor", **default_params)
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        data['fast_ma'] = data['close'].rolling(window=self.parameters['fast_ma']).mean()
        data['slow_ma'] = data['close'].rolling(window=self.parameters['slow_ma']).mean()
        data['momentum'] = data['close'] / data['close'].shift(self.parameters['momentum_period']) - 1
        return data
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        data = self.calculate_indicators(data)
        data['signal'] = 0
        
        # Trend following signals
        data.loc[(data['fast_ma'] > data['slow_ma']) & (data['momentum'] > 0), 'signal'] = 1
        data.loc[(data['fast_ma'] < data['slow_ma']) & (data['momentum'] < 0), 'signal'] = -1
        
        return data

class MonteCarloStrategy(BaseStrategy):
    """Monte Carlo simulation-based strategy"""
    
    def __init__(self, **parameters):
        default_params = {
            'simulation_days': 30,
            'num_simulations': 1000,
            'confidence_level': 0.95
        }
        default_params.update(parameters)
        super().__init__("Monte Carlo", **default_params)
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        # Calculate historical volatility
        data['returns'] = data['close'].pct_change()
        data['volatility'] = data['returns'].rolling(window=30).std()
        data['drift'] = data['returns'].rolling(window=30).mean()
        return data
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        data = self.calculate_indicators(data)
        data['signal'] = 0
        
        # Simplified Monte Carlo signal (in practice, would run full simulation)
        # Buy when expected return is positive with high confidence
        data.loc[data['drift'] > data['volatility'] * 0.5, 'signal'] = 1
        data.loc[data['drift'] < -data['volatility'] * 0.5, 'signal'] = -1
        
        return data

class OptionsStraddleStrategy(BaseStrategy):
    """Options straddle strategy for volatility trading"""
    
    def __init__(self, **parameters):
        default_params = {
            'volatility_threshold': 0.02,
            'days_to_expiry': 30
        }
        default_params.update(parameters)
        super().__init__("Options Straddle", **default_params)
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        data['returns'] = data['close'].pct_change()
        data['realized_vol'] = data['returns'].rolling(window=20).std() * np.sqrt(252)
        data['vol_change'] = data['realized_vol'].pct_change()
        return data
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        data = self.calculate_indicators(data)
        data['signal'] = 0
        
        # Long straddle when expecting volatility increase
        threshold = self.parameters['volatility_threshold']
        data.loc[data['vol_change'] > threshold, 'signal'] = 1
        data.loc[data['vol_change'] < -threshold, 'signal'] = -1
        
        return data

class ShootingStarStrategy(BaseStrategy):
    """Shooting Star candlestick pattern strategy"""
    
    def __init__(self, **parameters):
        default_params = {
            'body_ratio': 0.3,
            'shadow_ratio': 2.0
        }
        default_params.update(parameters)
        super().__init__("Shooting Star", **default_params)
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        data['body_size'] = abs(data['close'] - data['open'])
        data['upper_shadow'] = data['high'] - np.maximum(data['open'], data['close'])
        data['lower_shadow'] = np.minimum(data['open'], data['close']) - data['low']
        data['total_range'] = data['high'] - data['low']
        return data
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        data = self.calculate_indicators(data)
        data['signal'] = 0
        
        # Shooting star pattern detection
        body_ratio = self.parameters['body_ratio']
        shadow_ratio = self.parameters['shadow_ratio']
        
        shooting_star = (
            (data['body_size'] / data['total_range'] < body_ratio) &
            (data['upper_shadow'] / data['body_size'] > shadow_ratio) &
            (data['lower_shadow'] / data['body_size'] < 0.5)
        )
        
        data.loc[shooting_star, 'signal'] = -1  # Bearish signal
        
        return data

class LondonBreakoutStrategy(BaseStrategy):
    """London session breakout strategy"""
    
    def __init__(self, **parameters):
        default_params = {
            'breakout_period': 4,  # hours
            'min_range': 0.001,
            'max_range': 0.005
        }
        default_params.update(parameters)
        super().__init__("London Breakout", **default_params)
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        # Assume data has hourly timeframe
        data['hour'] = pd.to_datetime(data.index).hour if hasattr(data.index, 'hour') else 8
        
        # London session (8-12 GMT)
        london_session = (data['hour'] >= 8) & (data['hour'] <= 12)
        data['london_high'] = data.loc[london_session, 'high'].expanding().max()
        data['london_low'] = data.loc[london_session, 'low'].expanding().min()
        data['london_range'] = data['london_high'] - data['london_low']
        
        return data
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        data = self.calculate_indicators(data)
        data['signal'] = 0
        
        min_range = self.parameters['min_range']
        max_range = self.parameters['max_range']
        
        # Valid range for breakout
        valid_range = (data['london_range'] > min_range) & (data['london_range'] < max_range)
        
        # Breakout signals
        data.loc[valid_range & (data['close'] > data['london_high']), 'signal'] = 1
        data.loc[valid_range & (data['close'] < data['london_low']), 'signal'] = -1
        
        return data

class HeikinAshiStrategy(BaseStrategy):
    """Heikin-Ashi trend following strategy"""
    
    def __init__(self, **parameters):
        default_params = {
            'trend_periods': 3
        }
        default_params.update(parameters)
        super().__init__("Heikin-Ashi", **default_params)
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        # Calculate Heikin-Ashi candles
        ha_close = (data['open'] + data['high'] + data['low'] + data['close']) / 4
        ha_open = pd.Series(index=data.index, dtype=float)
        ha_open.iloc[0] = (data['open'].iloc[0] + data['close'].iloc[0]) / 2
        
        for i in range(1, len(data)):
            ha_open.iloc[i] = (ha_open.iloc[i-1] + ha_close.iloc[i-1]) / 2
        
        ha_high = pd.concat([data['high'], ha_open, ha_close], axis=1).max(axis=1)
        ha_low = pd.concat([data['low'], ha_open, ha_close], axis=1).min(axis=1)
        
        data['ha_open'] = ha_open
        data['ha_close'] = ha_close
        data['ha_high'] = ha_high
        data['ha_low'] = ha_low
        
        # Trend detection
        data['ha_bullish'] = data['ha_close'] > data['ha_open']
        
        return data
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        data = self.calculate_indicators(data)
        data['signal'] = 0
        
        periods = self.parameters['trend_periods']
        
        # Consecutive bullish/bearish Heikin-Ashi candles
        bullish_streak = data['ha_bullish'].rolling(window=periods).sum() == periods
        bearish_streak = (~data['ha_bullish']).rolling(window=periods).sum() == periods
        
        data.loc[bullish_streak, 'signal'] = 1
        data.loc[bearish_streak, 'signal'] = -1
        
        return data

class PairTradingStrategy(BaseStrategy):
    """Statistical arbitrage pairs trading strategy"""
    
    def __init__(self, **parameters):
        default_params = {
            'lookback_period': 60,
            'entry_threshold': 2.0,
            'exit_threshold': 0.5
        }
        default_params.update(parameters)
        super().__init__("Pair Trading", **default_params)
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        # Assumes data has two assets: 'close_A' and 'close_B'
        if 'close_A' not in data.columns:
            data['close_A'] = data['close']  # Fallback for single asset
            data['close_B'] = data['close'] * 1.1  # Simulated second asset
        
        # Calculate spread
        data['spread'] = data['close_A'] - data['close_B']
        data['spread_mean'] = data['spread'].rolling(window=self.parameters['lookback_period']).mean()
        data['spread_std'] = data['spread'].rolling(window=self.parameters['lookback_period']).std()
        data['z_score'] = (data['spread'] - data['spread_mean']) / data['spread_std']
        
        return data
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        data = self.calculate_indicators(data)
        data['signal'] = 0
        
        entry_threshold = self.parameters['entry_threshold']
        exit_threshold = self.parameters['exit_threshold']
        
        # Long spread when z-score is low (spread will revert up)
        data.loc[data['z_score'] < -entry_threshold, 'signal'] = 1
        # Short spread when z-score is high (spread will revert down)
        data.loc[data['z_score'] > entry_threshold, 'signal'] = -1
        # Exit when z-score approaches mean
        data.loc[abs(data['z_score']) < exit_threshold, 'signal'] = 0
        
        return data

class RSIStrategy(BaseStrategy):
    """RSI mean reversion strategy"""
    
    def __init__(self, **parameters):
        default_params = {
            'rsi_period': 14,
            'oversold': 30,
            'overbought': 70
        }
        default_params.update(parameters)
        super().__init__("RSI", **default_params)
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        delta = data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=self.parameters['rsi_period']).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=self.parameters['rsi_period']).mean()
        rs = gain / loss
        data['rsi'] = 100 - (100 / (1 + rs))
        return data
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        data = self.calculate_indicators(data)
        data['signal'] = 0
        
        data.loc[data['rsi'] < self.parameters['oversold'], 'signal'] = 1
        data.loc[data['rsi'] > self.parameters['overbought'], 'signal'] = -1
        
        return data

class BollingerBandsStrategy(BaseStrategy):
    """Bollinger Bands strategy"""
    
    def __init__(self, **parameters):
        default_params = {
            'period': 20,
            'std_dev': 2
        }
        default_params.update(parameters)
        super().__init__("Bollinger Bands", **default_params)
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        data['bb_middle'] = data['close'].rolling(window=self.parameters['period']).mean()
        data['bb_std'] = data['close'].rolling(window=self.parameters['period']).std()
        data['bb_upper'] = data['bb_middle'] + (data['bb_std'] * self.parameters['std_dev'])
        data['bb_lower'] = data['bb_middle'] - (data['bb_std'] * self.parameters['std_dev'])
        return data
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        data = self.calculate_indicators(data)
        data['signal'] = 0
        
        # Mean reversion signals
        data.loc[data['close'] < data['bb_lower'], 'signal'] = 1
        data.loc[data['close'] > data['bb_upper'], 'signal'] = -1
        
        return data

class ParabolicSARStrategy(BaseStrategy):
    """Parabolic SAR trend following strategy"""
    
    def __init__(self, **parameters):
        default_params = {
            'af_start': 0.02,
            'af_increment': 0.02,
            'af_max': 0.2
        }
        default_params.update(parameters)
        super().__init__("Parabolic SAR", **default_params)
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        # Simplified Parabolic SAR calculation
        high = data['high']
        low = data['low']
        close = data['close']
        
        # Initialize
        psar = pd.Series(index=data.index, dtype=float)
        psar.iloc[0] = low.iloc[0]
        
        af = self.parameters['af_start']
        ep = high.iloc[0]
        trend = 1  # 1 for uptrend, -1 for downtrend
        
        for i in range(1, len(data)):
            if trend == 1:  # Uptrend
                psar.iloc[i] = psar.iloc[i-1] + af * (ep - psar.iloc[i-1])
                if high.iloc[i] > ep:
                    ep = high.iloc[i]
                    af = min(af + self.parameters['af_increment'], self.parameters['af_max'])
                if low.iloc[i] < psar.iloc[i]:
                    trend = -1
                    psar.iloc[i] = ep
                    ep = low.iloc[i]
                    af = self.parameters['af_start']
            else:  # Downtrend
                psar.iloc[i] = psar.iloc[i-1] - af * (psar.iloc[i-1] - ep)
                if low.iloc[i] < ep:
                    ep = low.iloc[i]
                    af = min(af + self.parameters['af_increment'], self.parameters['af_max'])
                if high.iloc[i] > psar.iloc[i]:
                    trend = 1
                    psar.iloc[i] = ep
                    ep = high.iloc[i]
                    af = self.parameters['af_start']
        
        data['psar'] = psar
        return data
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        data = self.calculate_indicators(data)
        data['signal'] = 0
        
        # Trend following signals
        data.loc[data['close'] > data['psar'], 'signal'] = 1
        data.loc[data['close'] < data['psar'], 'signal'] = -1
        
        return data

class DualThrustStrategy(BaseStrategy):
    """Dual Thrust breakout strategy"""
    
    def __init__(self, **parameters):
        default_params = {
            'k1': 0.5,
            'k2': 0.5,
            'lookback': 4
        }
        default_params.update(parameters)
        super().__init__("Dual Thrust", **default_params)
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        # Calculate range
        data['hh'] = data['high'].rolling(window=self.parameters['lookback']).max()
        data['ll'] = data['low'].rolling(window=self.parameters['lookback']).min()
        data['hc'] = data['close'].rolling(window=self.parameters['lookback']).max()
        data['lc'] = data['close'].rolling(window=self.parameters['lookback']).min()
        
        data['range'] = np.maximum(data['hh'] - data['lc'], data['hc'] - data['ll'])
        
        # Calculate thresholds
        data['buy_line'] = data['open'] + self.parameters['k1'] * data['range']
        data['sell_line'] = data['open'] - self.parameters['k2'] * data['range']
        
        return data
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        data = self.calculate_indicators(data)
        data['signal'] = 0
        
        # Breakout signals
        data.loc[data['close'] > data['buy_line'], 'signal'] = 1
        data.loc[data['close'] < data['sell_line'], 'signal'] = -1
        
        return data

class AwesomeOscillatorStrategy(BaseStrategy):
    """Awesome Oscillator strategy"""
    
    def __init__(self, **parameters):
        default_params = {
            'fast_period': 5,
            'slow_period': 34
        }
        default_params.update(parameters)
        super().__init__("Awesome", **default_params)
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        median_price = (data['high'] + data['low']) / 2
        data['ao_fast'] = median_price.rolling(window=self.parameters['fast_period']).mean()
        data['ao_slow'] = median_price.rolling(window=self.parameters['slow_period']).mean()
        data['ao'] = data['ao_fast'] - data['ao_slow']
        return data
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        data = self.calculate_indicators(data)
        data['signal'] = 0
        
        # Zero line crossover
        data.loc[(data['ao'] > 0) & (data['ao'].shift(1) <= 0), 'signal'] = 1
        data.loc[(data['ao'] < 0) & (data['ao'].shift(1) >= 0), 'signal'] = -1
        
        return data

class MACDStrategy(BaseStrategy):
    """MACD strategy"""
    
    def __init__(self, **parameters):
        default_params = {
            'fast_period': 12,
            'slow_period': 26,
            'signal_period': 9
        }
        default_params.update(parameters)
        super().__init__("MACD", **default_params)
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        exp1 = data['close'].ewm(span=self.parameters['fast_period']).mean()
        exp2 = data['close'].ewm(span=self.parameters['slow_period']).mean()
        data['macd'] = exp1 - exp2
        data['macd_signal'] = data['macd'].ewm(span=self.parameters['signal_period']).mean()
        data['macd_histogram'] = data['macd'] - data['macd_signal']
        return data
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        data = self.calculate_indicators(data)
        data['signal'] = 0
        
        # MACD line crosses above signal line
        data.loc[(data['macd'] > data['macd_signal']) & 
                 (data['macd'].shift(1) <= data['macd_signal'].shift(1)), 'signal'] = 1
        # MACD line crosses below signal line
        data.loc[(data['macd'] < data['macd_signal']) & 
                 (data['macd'].shift(1) >= data['macd_signal'].shift(1)), 'signal'] = -1
        
        return data

class UpdatedQuantStrategiesLibrary:
    """
    Updated Quantitative Strategies Library with exact strategies from je-suis-tm/quant-trading
    """
    
    def __init__(self):
        self.strategies = {}
        self.metadata = {}
        self._initialize_strategies()
    
    def _initialize_strategies(self):
        """Initialize all strategies with their metadata"""
        
        # 1. VIX Calculator
        self.strategies["vix_calculator"] = VIXCalculatorStrategy
        self.metadata["vix_calculator"] = StrategyMetadata(
            name="VIX Calculator",
            description="Volatility-based trading using VIX-like calculations",
            strategy_type=StrategyType.VOLATILITY,
            complexity="intermediate",
            timeframes=[TimeFrame.DAY_1, TimeFrame.HOUR_4],
            assets=["stocks", "indices", "etfs"],
            expected_sharpe=1.1,
            max_drawdown=0.18,
            win_rate=0.52,
            parameters={"vix_threshold_low": 15, "vix_threshold_high": 25, "lookback_period": 20},
            requirements=["Volatility data", "Market indices", "Options data (optional)"]
        )
        
        # 2. Pattern Recognition
        self.strategies["pattern_recognition"] = PatternRecognitionStrategy
        self.metadata["pattern_recognition"] = StrategyMetadata(
            name="Pattern Recognition",
            description="Automated chart pattern detection and trading",
            strategy_type=StrategyType.PATTERN_RECOGNITION,
            complexity="advanced",
            timeframes=[TimeFrame.HOUR_1, TimeFrame.HOUR_4, TimeFrame.DAY_1],
            assets=["stocks", "forex", "crypto"],
            expected_sharpe=1.3,
            max_drawdown=0.15,
            win_rate=0.58,
            parameters={"pattern_window": 10, "breakout_threshold": 0.02},
            requirements=["High-quality OHLC data", "Pattern recognition algorithms"]
        )
        
        # 3. Commodity Trading Advisor
        self.strategies["commodity_trading_advisor"] = CommodityTradingAdvisorStrategy
        self.metadata["commodity_trading_advisor"] = StrategyMetadata(
            name="Commodity Trading Advisor",
            description="CTA-style systematic momentum strategy",
            strategy_type=StrategyType.COMMODITY_TRADING,
            complexity="intermediate",
            timeframes=[TimeFrame.DAY_1, TimeFrame.WEEK_1],
            assets=["commodities", "futures", "forex"],
            expected_sharpe=1.4,
            max_drawdown=0.20,
            win_rate=0.48,
            parameters={"fast_ma": 10, "slow_ma": 30, "momentum_period": 14},
            requirements=["Futures data", "Momentum indicators", "Risk management"]
        )
        
        # 4. Monte Carlo
        self.strategies["monte_carlo"] = MonteCarloStrategy
        self.metadata["monte_carlo"] = StrategyMetadata(
            name="Monte Carlo",
            description="Monte Carlo simulation-based trading strategy",
            strategy_type=StrategyType.MONTE_CARLO,
            complexity="advanced",
            timeframes=[TimeFrame.DAY_1, TimeFrame.HOUR_4],
            assets=["stocks", "forex", "crypto"],
            expected_sharpe=1.2,
            max_drawdown=0.16,
            win_rate=0.55,
            parameters={"simulation_days": 30, "num_simulations": 1000, "confidence_level": 0.95},
            requirements=["Statistical modeling", "Monte Carlo simulation", "Historical data"]
        )
        
        # 5. Options Straddle
        self.strategies["options_straddle"] = OptionsStraddleStrategy
        self.metadata["options_straddle"] = StrategyMetadata(
            name="Options Straddle",
            description="Volatility trading using options straddle strategies",
            strategy_type=StrategyType.OPTIONS,
            complexity="advanced",
            timeframes=[TimeFrame.DAY_1, TimeFrame.WEEK_1],
            assets=["stocks", "indices", "etfs"],
            expected_sharpe=1.0,
            max_drawdown=0.25,
            win_rate=0.45,
            parameters={"volatility_threshold": 0.02, "days_to_expiry": 30},
            requirements=["Options data", "Volatility modeling", "Greeks calculation"]
        )
        
        # 6. Shooting Star
        self.strategies["shooting_star"] = ShootingStarStrategy
        self.metadata["shooting_star"] = StrategyMetadata(
            name="Shooting Star",
            description="Candlestick pattern strategy based on shooting star formation",
            strategy_type=StrategyType.CANDLESTICK,
            complexity="beginner",
            timeframes=[TimeFrame.HOUR_1, TimeFrame.HOUR_4, TimeFrame.DAY_1],
            assets=["stocks", "forex", "crypto"],
            expected_sharpe=0.9,
            max_drawdown=0.12,
            win_rate=0.62,
            parameters={"body_ratio": 0.3, "shadow_ratio": 2.0},
            requirements=["OHLC data", "Candlestick pattern recognition"]
        )
        
        # 7. London Breakout
        self.strategies["london_breakout"] = LondonBreakoutStrategy
        self.metadata["london_breakout"] = StrategyMetadata(
            name="London Breakout",
            description="Session-based breakout strategy for London trading hours",
            strategy_type=StrategyType.BREAKOUT,
            complexity="intermediate",
            timeframes=[TimeFrame.HOUR_1, TimeFrame.MINUTE_30],
            assets=["forex"],
            expected_sharpe=1.3,
            max_drawdown=0.14,
            win_rate=0.56,
            parameters={"breakout_period": 4, "min_range": 0.001, "max_range": 0.005},
            requirements=["Intraday forex data", "Session time awareness", "Breakout detection"]
        )
        
        # 8. Heikin-Ashi
        self.strategies["heikin_ashi"] = HeikinAshiStrategy
        self.metadata["heikin_ashi"] = StrategyMetadata(
            name="Heikin-Ashi",
            description="Trend following strategy using Heikin-Ashi candlesticks",
            strategy_type=StrategyType.ALTERNATIVE_DATA,
            complexity="beginner",
            timeframes=[TimeFrame.HOUR_1, TimeFrame.HOUR_4, TimeFrame.DAY_1],
            assets=["stocks", "forex", "crypto"],
            expected_sharpe=1.1,
            max_drawdown=0.13,
            win_rate=0.59,
            parameters={"trend_periods": 3},
            requirements=["OHLC data", "Heikin-Ashi calculation"]
        )
        
        # 9. Pair Trading
        self.strategies["pair_trading"] = PairTradingStrategy
        self.metadata["pair_trading"] = StrategyMetadata(
            name="Pair Trading",
            description="Statistical arbitrage strategy trading correlated pairs",
            strategy_type=StrategyType.PAIRS_TRADING,
            complexity="advanced",
            timeframes=[TimeFrame.HOUR_1, TimeFrame.DAY_1],
            assets=["stocks", "etfs"],
            expected_sharpe=1.8,
            max_drawdown=0.10,
            win_rate=0.65,
            parameters={"lookback_period": 60, "entry_threshold": 2.0, "exit_threshold": 0.5},
            requirements=["Correlated assets", "Statistical analysis", "Cointegration testing"]
        )
        
        # 10. RSI
        self.strategies["rsi"] = RSIStrategy
        self.metadata["rsi"] = StrategyMetadata(
            name="RSI",
            description="Mean reversion strategy using Relative Strength Index",
            strategy_type=StrategyType.MEAN_REVERSION,
            complexity="beginner",
            timeframes=[TimeFrame.HOUR_1, TimeFrame.HOUR_4, TimeFrame.DAY_1],
            assets=["stocks", "forex", "crypto"],
            expected_sharpe=1.2,
            max_drawdown=0.15,
            win_rate=0.55,
            parameters={"rsi_period": 14, "oversold": 30, "overbought": 70},
            requirements=["Price data", "RSI calculation"]
        )
        
        # 11. Bollinger Bands
        self.strategies["bollinger_bands"] = BollingerBandsStrategy
        self.metadata["bollinger_bands"] = StrategyMetadata(
            name="Bollinger Bands",
            description="Mean reversion and breakout strategy using Bollinger Bands",
            strategy_type=StrategyType.MEAN_REVERSION,
            complexity="beginner",
            timeframes=[TimeFrame.HOUR_1, TimeFrame.HOUR_4, TimeFrame.DAY_1],
            assets=["stocks", "forex", "crypto"],
            expected_sharpe=1.0,
            max_drawdown=0.16,
            win_rate=0.53,
            parameters={"period": 20, "std_dev": 2},
            requirements=["Price data", "Moving averages", "Standard deviation"]
        )
        
        # 12. Parabolic SAR
        self.strategies["parabolic_sar"] = ParabolicSARStrategy
        self.metadata["parabolic_sar"] = StrategyMetadata(
            name="Parabolic SAR",
            description="Trend following strategy using Parabolic Stop and Reverse",
            strategy_type=StrategyType.TREND_FOLLOWING,
            complexity="intermediate",
            timeframes=[TimeFrame.HOUR_1, TimeFrame.HOUR_4, TimeFrame.DAY_1],
            assets=["stocks", "forex", "crypto"],
            expected_sharpe=1.1,
            max_drawdown=0.17,
            win_rate=0.51,
            parameters={"af_start": 0.02, "af_increment": 0.02, "af_max": 0.2},
            requirements=["OHLC data", "Parabolic SAR calculation"]
        )
        
        # 13. Dual Thrust
        self.strategies["dual_thrust"] = DualThrustStrategy
        self.metadata["dual_thrust"] = StrategyMetadata(
            name="Dual Thrust",
            description="Intraday breakout strategy with dual threshold system",
            strategy_type=StrategyType.BREAKOUT,
            complexity="advanced",
            timeframes=[TimeFrame.MINUTE_15, TimeFrame.MINUTE_30, TimeFrame.HOUR_1],
            assets=["futures", "forex", "stocks"],
            expected_sharpe=1.5,
            max_drawdown=0.12,
            win_rate=0.48,
            parameters={"k1": 0.5, "k2": 0.5, "lookback": 4},
            requirements=["Intraday data", "Range calculation", "Breakout detection"]
        )
        
        # 14. Awesome Oscillator
        self.strategies["awesome"] = AwesomeOscillatorStrategy
        self.metadata["awesome"] = StrategyMetadata(
            name="Awesome",
            description="Momentum strategy using Awesome Oscillator indicator",
            strategy_type=StrategyType.OSCILLATOR,
            complexity="beginner",
            timeframes=[TimeFrame.HOUR_1, TimeFrame.HOUR_4, TimeFrame.DAY_1],
            assets=["stocks", "forex", "crypto"],
            expected_sharpe=0.95,
            max_drawdown=0.18,
            win_rate=0.54,
            parameters={"fast_period": 5, "slow_period": 34},
            requirements=["OHLC data", "Moving averages"]
        )
        
        # 15. MACD
        self.strategies["macd"] = MACDStrategy
        self.metadata["macd"] = StrategyMetadata(
            name="MACD",
            description="Momentum strategy using MACD indicator crossovers",
            strategy_type=StrategyType.MOMENTUM,
            complexity="beginner",
            timeframes=[TimeFrame.HOUR_1, TimeFrame.HOUR_4, TimeFrame.DAY_1],
            assets=["stocks", "forex", "crypto"],
            expected_sharpe=1.1,
            max_drawdown=0.16,
            win_rate=0.52,
            parameters={"fast_period": 12, "slow_period": 26, "signal_period": 9},
            requirements=["Price data", "Exponential moving averages"]
        )
    
    def list_strategies(self) -> List[str]:
        """Get list of all available strategy names"""
        return list(self.strategies.keys())
    
    def get_strategy(self, name: str) -> Optional[BaseStrategy]:
        """Get a strategy instance by name"""
        if name in self.strategies:
            return self.strategies[name]()
        return None
    
    def get_metadata(self, name: str) -> Optional[StrategyMetadata]:
        """Get strategy metadata by name"""
        return self.metadata.get(name)
    
    def get_strategies_by_type(self, strategy_type: StrategyType) -> List[str]:
        """Get strategies by type"""
        return [name for name, metadata in self.metadata.items() 
                if metadata.strategy_type == strategy_type]
    
    def get_strategies_by_complexity(self, complexity: str) -> List[str]:
        """Get strategies by complexity level"""
        return [name for name, metadata in self.metadata.items() 
                if metadata.complexity == complexity]
    
    def get_strategy_recommendations(self, 
                                   asset_class: str, 
                                   timeframe: TimeFrame, 
                                   complexity: str = None) -> List[Dict[str, Any]]:
        """Get strategy recommendations based on criteria"""
        recommendations = []
        
        for name, metadata in self.metadata.items():
            if (asset_class in metadata.assets and 
                timeframe in metadata.timeframes and
                (complexity is None or metadata.complexity == complexity)):
                
                recommendations.append({
                    'name': name,
                    'metadata': metadata,
                    'score': metadata.expected_sharpe * metadata.win_rate  # Simple scoring
                })
        
        # Sort by score
        recommendations.sort(key=lambda x: x['score'], reverse=True)
        return recommendations

# Example usage
if __name__ == "__main__":
    # Initialize the updated library
    quant_lib = UpdatedQuantStrategiesLibrary()
    
    print("📊 Updated Quantitative Strategies Library")
    print("=" * 50)
    print(f"Total strategies: {len(quant_lib.list_strategies())}")
    
    # Show all strategies
    print("\n📋 All Available Strategies:")
    for i, strategy_name in enumerate(quant_lib.list_strategies(), 1):
        metadata = quant_lib.get_metadata(strategy_name)
        print(f"  {i:2d}. {metadata.name} ({metadata.complexity}) - {metadata.strategy_type.value}")
    
    # Show strategies by type
    print("\n🏷️  Strategies by Type:")
    for strategy_type in StrategyType:
        strategies = quant_lib.get_strategies_by_type(strategy_type)
        if strategies:
            print(f"  {strategy_type.value.replace('_', ' ').title()}: {len(strategies)} strategies")
    
    # Example: Get RSI strategy
    print("\n🎯 Example - RSI Strategy:")
    rsi_strategy = quant_lib.get_strategy("rsi")
    if rsi_strategy:
        print(f"  Name: {rsi_strategy.name}")
        print(f"  Parameters: {rsi_strategy.parameters}")
        
        metadata = quant_lib.get_metadata("rsi")
        print(f"  Expected Sharpe: {metadata.expected_sharpe}")
        print(f"  Win Rate: {metadata.win_rate:.1%}")
        print(f"  Max Drawdown: {metadata.max_drawdown:.1%}")