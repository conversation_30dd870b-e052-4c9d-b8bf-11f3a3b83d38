"""
Tests for Backtesting Engine
"""

import pytest
import logging
import pandas as pd
from datetime import datetime, timedelta

from src.strategies.moving_average_crossover import MovingAverageCrossover
from src.backtest.backtest_engine import BacktestEngine, generate_sample_data

# Configure logging
logger = logging.getLogger(__name__)


@pytest.fixture
def sample_data():
    """Fixture to generate sample data for backtesting"""
    symbols = ["EURUSD", "USDJPY"]
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2023, 1, 31)
    
    return generate_sample_data(symbols, start_date, end_date)


@pytest.fixture
def ma_strategy():
    """Fixture to create a Moving Average Crossover strategy"""
    return MovingAverageCrossover(
        symbols=["EURUSD", "USDJPY"],
        fast_period=5,
        slow_period=10,
        offline_mode=True
    )


@pytest.fixture
def backtest_engine(ma_strategy, sample_data):
    """Fixture to create a Backtest Engine"""
    return BacktestEngine(
        strategy=ma_strategy,
        historical_data=sample_data
    )


@pytest.mark.unit
def test_backtest_engine_initialization(backtest_engine):
    """Test backtest engine initialization"""
    assert backtest_engine.strategy.name == "MA Crossover"
    assert backtest_engine.start_date is not None
    assert backtest_engine.end_date is not None
    assert backtest_engine.bridge is not None
    assert backtest_engine.bridge.account_balance == 10000.0


@pytest.mark.unit
def test_backtest_engine_run(backtest_engine):
    """Test running a backtest"""
    # Run the backtest
    results = backtest_engine.run(update_interval=timedelta(hours=6))
    
    # Check results structure
    assert "trades" in results
    assert "equity_curve" in results
    assert "performance_metrics" in results
    
    # Check equity curve
    assert len(results["equity_curve"]) > 0
    
    # Check performance metrics
    metrics = results["performance_metrics"]
    assert "total_trades" in metrics
    assert "winning_trades" in metrics
    assert "losing_trades" in metrics
    assert "win_rate" in metrics
    assert "total_profit" in metrics
    assert "max_drawdown" in metrics
    assert "sharpe_ratio" in metrics
    assert "profit_factor" in metrics


@pytest.mark.unit
def test_backtest_bridge(backtest_engine):
    """Test backtest bridge functionality"""
    bridge = backtest_engine.bridge
    
    # Initialize current prices for testing
    bridge.current_prices = {
        "EURUSD": 1.1,
        "USDJPY": 110.0
    }
    
    # Test placing an order
    order_id = bridge.place_order(
        symbol="EURUSD",
        order_type="BUY",
        lot=0.1,
        stop_loss=0.95,
        take_profit=1.05
    )
    
    assert order_id is not None
    assert bridge.get_order_status(order_id) == "filled"
    
    # Test advancing time
    initial_time = bridge.current_time
    bridge.advance_time(timedelta(hours=1))
    assert bridge.current_time == initial_time + timedelta(hours=1)
    
    # Test closing an order
    result = bridge.close_order(order_id)
    assert result is True
    assert bridge.get_order_status(order_id) == "closed"
    
    # Test account info
    account_info = bridge.get_account_info()
    assert "balance" in account_info
    assert "equity" in account_info