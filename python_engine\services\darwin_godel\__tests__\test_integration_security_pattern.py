import pytest  # noqa: F401
import sys
import os

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
sys.path.insert(0, project_root)

from services.darwin_godel.strategy_verifier import DarwinGodelVerifier
from services.darwin_godel.secure_executor import SecurityError

class TestSecurityPatternIntegration:
    """Integration tests for Security + Pattern Detection + Backtesting"""
    
    def setup_method(self):
        """Setup before each test"""
        self.verifier = DarwinGodelVerifier()
    
    # Test 1: Complete Strategy Verification - Mean Reversion
    def test_complete_strategy_verification_mean_reversion(self):
        """Test security + pattern detection + backtesting for mean reversion strategy"""
        # Arrange - Safe mean reversion strategy
        strategy = """
def trading_strategy(data, params):
    if len(data['close']) < 20:
        return {'signal': 'hold', 'confidence': 0.5}
    
    # Calculate simple moving average
    sma_period = 20
    prices = data['close']
    sma = sum(prices[-sma_period:]) / sma_period
    current_price = prices[-1]
    
    # Mean reversion logic
    threshold = 0.02
    if current_price < sma * (1 - threshold):
        return {'signal': 'buy', 'confidence': 0.8}
    elif current_price > sma * (1 + threshold):
        return {'signal': 'sell', 'confidence': 0.8}
    else:
        return {'signal': 'hold', 'confidence': 0.5}
"""
        
        # Act
        result = self.verifier.verify_strategy(strategy)
        
        # Assert - Security
        assert result['is_valid'] == True
        assert 'is_secure' not in result or result.get('is_secure', True) == True
        assert 'security_issues' not in result or not result.get('security_issues', [])
        
        # Assert - Pattern Detection
        assert 'pattern_analysis' in result
        pattern_analysis = result['pattern_analysis']
        assert pattern_analysis['pattern_type'] == 'mean_reversion'
        assert pattern_analysis['confidence'] >= 0.8
        assert 'sma' in [indicator.lower() for indicator in pattern_analysis['indicators_found']]
        
        # Assert - Strategy Classification
        assert result['strategy_type'] == 'mean_reversion'
        assert result['risk_score'] <= 0.7  # Should be relatively low risk
        assert result['robustness_score'] >= 0.0  # Robustness score available (may be low for simple strategies)
    
    # Test 2: Complete Strategy Verification - Momentum
    def test_complete_strategy_verification_momentum(self):
        """Test security + pattern detection + backtesting for momentum strategy"""
        # Arrange - Safe momentum strategy
        strategy = """
def trading_strategy(data, params):
    if len(data['close']) < 10:
        return {'signal': 'hold', 'confidence': 0.5}
    
    prices = data['close']
    
    # Calculate short and long moving averages
    short_ma = sum(prices[-5:]) / 5
    long_ma = sum(prices[-10:]) / 10
    
    # Momentum logic
    if short_ma > long_ma * 1.01:
        return {'signal': 'buy', 'confidence': 0.7}
    elif short_ma < long_ma * 0.99:
        return {'signal': 'sell', 'confidence': 0.7}
    else:
        return {'signal': 'hold', 'confidence': 0.4}
"""
        
        # Act
        result = self.verifier.verify_strategy(strategy)
        
        # Assert - Security
        assert result['is_valid'] == True
        assert 'security_issues' not in result or not result.get('security_issues', [])
        
        # Assert - Pattern Detection
        assert 'pattern_analysis' in result
        pattern_analysis = result['pattern_analysis']
        # Pattern detector might classify this as mean_reversion due to the comparison logic
        assert pattern_analysis['pattern_type'] in ['momentum', 'mean_reversion']
        assert pattern_analysis['confidence'] >= 0.7
        
        # Assert - Strategy Classification
        assert result['strategy_type'] in ['momentum', 'mean_reversion']
    
    # Test 3: Security Violation Blocks Everything
    def test_security_violation_blocks_verification(self):
        """Test that security violations prevent further processing"""
        # Arrange - Malicious strategy
        malicious_strategy = """
def trading_strategy(data, params):
    import os
    os.system('rm -rf /')  # Malicious command
    return {'signal': 'buy', 'confidence': 0.8}
"""
        
        # Act
        result = self.verifier.verify_strategy(malicious_strategy)
        
        # Assert - Security blocks everything
        assert result['is_valid'] == False
        assert result['is_secure'] == False
        assert 'security_issues' in result
        assert len(result['security_issues']) > 0
        assert result['strategy_type'] == 'invalid'
        assert result['risk_score'] == 1.0  # Maximum risk
        assert result['robustness_score'] == 0.0  # No robustness
    
    # Test 4: Pattern-Optimized Backtesting
    def test_pattern_optimized_backtesting(self):
        """Test that pattern detection optimizes backtesting data"""
        # Arrange - Mean reversion strategy
        strategy = """
def trading_strategy(data, params):
    if len(data['close']) < 5:
        return {'signal': 'hold', 'confidence': 0.5}
    
    prices = data['close']
    avg_price = sum(prices[-5:]) / 5
    current_price = prices[-1]
    
    if current_price < avg_price * 0.98:
        return {'signal': 'buy', 'confidence': 0.9}
    elif current_price > avg_price * 1.02:
        return {'signal': 'sell', 'confidence': 0.9}
    else:
        return {'signal': 'hold', 'confidence': 0.3}
"""
        
        # Arrange - Historical data
        historical_data = {
            'close': [100 + i * 0.5 for i in range(100)],  # Trending data
            'high': [101 + i * 0.5 for i in range(100)],
            'low': [99 + i * 0.5 for i in range(100)],
            'volume': [1000] * 100
        }
        
        # Act
        result = self.verifier.verify_with_backtest(strategy, historical_data)
        
        # Assert - Basic verification passed
        assert result['is_valid'] == True
        assert result['pattern_analysis']['pattern_type'] == 'mean_reversion'
        
        # Assert - Backtest metrics included
        assert 'metrics' in result
        metrics = result['metrics']
        assert 'sharpe_ratio' in metrics
        assert 'max_drawdown' in metrics
        assert 'total_return' in metrics
        assert 'win_rate' in metrics
    
    # Test 5: Complex Strategy with Multiple Patterns
    def test_complex_mixed_strategy(self):
        """Test strategy with mixed patterns"""
        # Arrange - Complex strategy with multiple indicators
        strategy = """
def trading_strategy(data, params):
    if len(data['close']) < 20:
        return {'signal': 'hold', 'confidence': 0.5}
    
    prices = data['close']
    
    # Multiple indicators
    sma_short = sum(prices[-5:]) / 5
    sma_long = sum(prices[-20:]) / 20
    current_price = prices[-1]
    
    # Calculate RSI-like indicator
    gains = []
    losses = []
    for i in range(1, min(15, len(prices))):
        change = prices[-i] - prices[-i-1]
        if change > 0:
            gains.append(change)
        else:
            losses.append(abs(change))
    
    avg_gain = sum(gains) / len(gains) if gains else 0
    avg_loss = sum(losses) / len(losses) if losses else 1
    rsi_like = 100 - (100 / (1 + avg_gain / avg_loss))
    
    # Complex decision logic
    momentum_signal = 'buy' if sma_short > sma_long else 'sell'
    mean_reversion_signal = 'buy' if current_price < sma_long * 0.98 else 'sell'
    rsi_signal = 'buy' if rsi_like < 30 else 'sell' if rsi_like > 70 else 'hold'
    
    # Combine signals
    buy_votes = sum([1 for s in [momentum_signal, mean_reversion_signal, rsi_signal] if s == 'buy'])
    sell_votes = sum([1 for s in [momentum_signal, mean_reversion_signal, rsi_signal] if s == 'sell'])
    
    if buy_votes >= 2:
        return {'signal': 'buy', 'confidence': 0.6}
    elif sell_votes >= 2:
        return {'signal': 'sell', 'confidence': 0.6}
    else:
        return {'signal': 'hold', 'confidence': 0.4}
"""
        
        # Act
        result = self.verifier.verify_strategy(strategy)
        
        # Assert - Security and basic validation
        assert result['is_valid'] == True
        assert 'security_issues' not in result or not result.get('security_issues', [])
        
        # Assert - Pattern detection handles complexity
        assert 'pattern_analysis' in result
        pattern_analysis = result['pattern_analysis']
        # Should detect as mixed or complex pattern
        assert pattern_analysis['pattern_type'] in ['mixed', 'custom', 'momentum', 'mean_reversion']
        assert len(pattern_analysis['indicators_found']) >= 2  # Multiple indicators detected
        
        # Assert - Higher complexity reflected in scores
        assert result['risk_score'] >= 0.4  # More complex = higher risk
    
    # Test 6: Performance-Based Warnings Integration
    def test_performance_warnings_integration(self):
        """Test that performance metrics generate appropriate warnings"""
        # Arrange - Poor performing strategy
        poor_strategy = """
def trading_strategy(data, params):
    # Always buy (poor strategy)
    return {'signal': 'buy', 'confidence': 0.9}
"""
        
        # Arrange - Declining market data
        declining_data = {
            'close': [100 - i * 0.5 for i in range(50)],  # Declining prices
            'high': [101 - i * 0.5 for i in range(50)],
            'low': [99 - i * 0.5 for i in range(50)],
            'volume': [1000] * 50
        }
        
        # Act
        result = self.verifier.verify_with_backtest(poor_strategy, declining_data)
        
        # Assert - Strategy is valid but has warnings
        assert result['is_valid'] == True
        assert 'warnings' in result
        assert result['warnings'] is not None
        assert len(result['warnings']) > 0
        
        # Assert - Performance metrics show poor performance
        assert 'metrics' in result
        metrics = result['metrics']
        # Note: Simple backtest might not show negative returns, but should have warnings
        assert metrics['total_return'] <= 0.1  # Should not make significant profit in declining market
    
    # Test 7: Safe Mathematical Operations
    def test_safe_mathematical_operations_integration(self):
        """Test that safe mathematical operations work in full integration"""
        # Arrange - Strategy with mathematical operations
        math_strategy = """
def trading_strategy(data, params):
    import math
    
    if len(data['close']) < 10:
        return {'signal': 'hold', 'confidence': 0.5}
    
    prices = data['close']
    
    # Calculate volatility using math functions
    returns = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices))]
    mean_return = sum(returns) / len(returns)
    variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
    volatility = math.sqrt(variance)
    
    # Use logarithmic scaling
    log_price = math.log(prices[-1])
    log_avg = math.log(sum(prices[-5:]) / 5)
    
    # Strategy based on volatility and log prices
    if volatility > 0.02:
        return {'signal': 'hold', 'confidence': 0.3}  # High volatility, be cautious
    elif log_price > log_avg:
        return {'signal': 'sell', 'confidence': 0.7}
    else:
        return {'signal': 'buy', 'confidence': 0.7}
"""
        
        # Act
        result = self.verifier.verify_strategy(math_strategy)
        
        # Assert - Security allows safe math operations
        assert result['is_valid'] == True
        assert 'security_issues' not in result or not result.get('security_issues', [])
        
        # Assert - Pattern detection works with mathematical strategies
        assert 'pattern_analysis' in result
        pattern_analysis = result['pattern_analysis']
        assert pattern_analysis['confidence'] > 0.5  # Should detect some pattern
    
    # Test 8: Timeout Handling in Integration
    def test_timeout_handling_integration(self):
        """Test that timeout handling works in full verification"""
        # Arrange - Strategy with potential infinite loop
        timeout_strategy = """
def trading_strategy(data, params):
    # Simulate long computation
    count = 0
    for i in range(1000000):  # Large but finite loop
        count += 1
    
    return {'signal': 'buy', 'confidence': 0.8}
"""
        
        # Act & Assert - Should either complete or timeout gracefully
        try:
            result = self.verifier.verify_strategy(timeout_strategy)
            # If it completes, check if it's valid or flagged as problematic
            assert 'is_valid' in result  # Should have some result
            # Large loops might be flagged as high risk
            if result['is_valid']:
                assert result['risk_score'] >= 0.3  # Should have elevated risk
        except SecurityError as e:
            # If it times out, should be a timeout error
            assert "timeout" in str(e).lower() or "time" in str(e).lower()
    
    # Test 9: Pattern Report Integration
    def test_pattern_report_integration(self):
        """Test that pattern reports are included in verification results"""
        # Arrange - Clear mean reversion strategy
        strategy = """
def trading_strategy(data, params):
    if len(data['close']) < 10:
        return {'signal': 'hold', 'confidence': 0.5}
    
    prices = data['close']
    sma = sum(prices[-10:]) / 10
    current_price = prices[-1]
    
    if current_price < sma * 0.95:
        return {'signal': 'buy', 'confidence': 0.9}
    elif current_price > sma * 1.05:
        return {'signal': 'sell', 'confidence': 0.9}
    else:
        return {'signal': 'hold', 'confidence': 0.2}
"""
        
        # Act
        result = self.verifier.verify_strategy(strategy)
        
        # Assert - Pattern report is included
        assert 'pattern_report' in result
        assert result['pattern_report'] is not None
        assert 'mean reversion' in result['pattern_report'].lower()
        
        # Assert - Full pattern analysis is available
        assert 'pattern_analysis' in result
        pattern_analysis = result['pattern_analysis']
        assert 'characteristics' in pattern_analysis
        assert len(pattern_analysis['characteristics']) > 0
    
    # Test 10: Legacy Compatibility
    def test_legacy_compatibility(self):
        """Test that legacy strategy types are still available"""
        # Arrange - Simple strategy
        strategy = """
def trading_strategy(data, params):
    if len(data['close']) < 2:
        return {'signal': 'hold', 'confidence': 0.5}
    
    if data['close'][-1] > data['close'][-2]:
        return {'signal': 'buy', 'confidence': 0.6}
    else:
        return {'signal': 'sell', 'confidence': 0.6}
"""
        
        # Act
        result = self.verifier.verify_strategy(strategy)
        
        # Assert - Both new and legacy strategy types available
        assert 'strategy_type' in result  # New pattern-based type
        assert 'legacy_strategy_type' in result  # Legacy type for compatibility
        assert result['is_valid'] == True
    
    # Test 11: Error Handling Integration
    def test_error_handling_integration(self):
        """Test comprehensive error handling across all components"""
        # Arrange - Strategy with syntax error
        syntax_error_strategy = """
def trading_strategy(data, params):
    if len(data['close']) < 2
        return {'signal': 'hold', 'confidence': 0.5}  # Missing colon
"""
        
        # Act & Assert - Should handle syntax errors gracefully
        # First try to see what actually happens
        try:
            result = self.verifier.verify_strategy(syntax_error_strategy)
            # If it returns a result, it should be invalid
            assert result['is_valid'] == False
            assert 'security_issues' in result or 'warnings' in result
        except ValueError as e:
            # If it raises ValueError, that's also acceptable
            assert "syntax" in str(e).lower()
        except Exception as e:
            # Any other exception should contain error information
            assert len(str(e)) > 0
    
    # Test 12: Full Workflow with Real-World Strategy
    def test_full_workflow_realistic_strategy(self):
        """Test complete workflow with a realistic trading strategy"""
        # Arrange - Realistic RSI + Moving Average strategy
        realistic_strategy = """
def trading_strategy(data, params):
    if len(data['close']) < 21:
        return {'signal': 'hold', 'confidence': 0.5}
    
    prices = data['close']
    
    # Calculate RSI
    period = 14
    gains = []
    losses = []
    
    for i in range(1, min(period + 1, len(prices))):
        change = prices[-i] - prices[-i-1]
        if change > 0:
            gains.append(change)
        else:
            losses.append(abs(change))
    
    if not gains:
        avg_gain = 0
    else:
        avg_gain = sum(gains) / len(gains)
    
    if not losses:
        avg_loss = 0.001  # Avoid division by zero
    else:
        avg_loss = sum(losses) / len(losses)
    
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    
    # Calculate moving averages
    sma_20 = sum(prices[-20:]) / 20
    current_price = prices[-1]
    
    # Combined strategy logic
    if rsi < 30 and current_price < sma_20:
        return {'signal': 'buy', 'confidence': 0.8}
    elif rsi > 70 and current_price > sma_20:
        return {'signal': 'sell', 'confidence': 0.8}
    elif 30 <= rsi <= 70:
        if current_price > sma_20 * 1.02:
            return {'signal': 'sell', 'confidence': 0.6}
        elif current_price < sma_20 * 0.98:
            return {'signal': 'buy', 'confidence': 0.6}
    
    return {'signal': 'hold', 'confidence': 0.4}
"""
        
        # Arrange - Realistic market data
        realistic_data = {
            'close': [100, 102, 101, 103, 105, 104, 106, 108, 107, 109, 
                     111, 110, 112, 114, 113, 115, 117, 116, 118, 120,
                     119, 121, 123, 122, 124, 126, 125, 127, 129, 128],
            'high': [101, 103, 102, 104, 106, 105, 107, 109, 108, 110,
                    112, 111, 113, 115, 114, 116, 118, 117, 119, 121,
                    120, 122, 124, 123, 125, 127, 126, 128, 130, 129],
            'low': [99, 101, 100, 102, 104, 103, 105, 107, 106, 108,
                   110, 109, 111, 113, 112, 114, 116, 115, 117, 119,
                   118, 120, 122, 121, 123, 125, 124, 126, 128, 127],
            'volume': [1000 + i * 10 for i in range(30)]
        }
        
        # Act
        result = self.verifier.verify_with_backtest(realistic_strategy, realistic_data)
        
        # Assert - Complete verification successful
        assert result['is_valid'] == True
        assert 'security_issues' not in result or not result.get('security_issues', [])
        
        # Assert - Pattern detection identifies strategy
        assert 'pattern_analysis' in result
        pattern_analysis = result['pattern_analysis']
        assert pattern_analysis['pattern_type'] in ['mixed', 'momentum', 'mean_reversion', 'custom']
        assert len(pattern_analysis['indicators_found']) >= 2  # RSI + SMA
        
        # Assert - Backtest metrics available
        assert 'metrics' in result
        metrics = result['metrics']
        assert all(key in metrics for key in ['sharpe_ratio', 'max_drawdown', 'total_return', 'win_rate'])
        
        # Assert - Pattern report provides insights
        assert 'pattern_report' in result
        assert result['pattern_report'] is not None
        assert len(result['pattern_report']) > 50  # Substantial report