# Test Execution Guide

## Quick Start

```bash
# Run complete test suite
python run_tests.py

# Or use platform-specific scripts
./run_tests.sh      # Unix/Linux/Mac
run_tests.bat       # Windows
```

## Command Options

```bash
# Quick basic tests
python run_tests.py quick

# Coverage analysis
python run_tests.py coverage

# Check dependencies
python run_tests.py deps
```

## Test Categories

The script automatically runs tests in these categories:
- **Security Tests**: `tests/test_secure_executor.py`
- **ML Model Tests**: `tests/test_ml_models.py`
- **Data Pipeline Tests**: `tests/test_data_pipeline.py`
- **Trading Service Tests**: `tests/test_trading_services.py`
- **Integration Tests**: `tests/test_integration.py`

## Output Files

- **JSON Report**: `test_report_YYYYMMDD_HHMMSS.json`
- **HTML Coverage**: `htmlcov/index.html`
- **Coverage Data**: `coverage.json`

## Troubleshooting

### Missing Dependencies
```bash
python run_tests.py deps
```

### Test Failures
Check the detailed JSON report for specific failure information.

### Coverage Issues
Open `htmlcov/index.html` in your browser for detailed coverage analysis.

## Expected Results

✅ **Success Criteria**:
- All basic tests passing
- Coverage > 70% (target: 90%+)
- All security tests passing
- No critical failures

## Integration with Existing Tests

This script complements the existing critical test suite:
- Use `python run_tests.py critical` for the 49 critical tests
- Use `python run_tests.py` for comprehensive testing including new categories

## CI/CD Integration

```bash
# For continuous integration
python run_tests.py quick && echo "Tests passed"

# For deployment validation
python run_tests.py coverage
```