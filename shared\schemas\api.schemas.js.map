{"version": 3, "file": "api.schemas.js", "sourceRoot": "", "sources": ["api.schemas.ts"], "names": [], "mappings": ";;;AAAA,6BAAwB;AACxB,qDAAwG;AACxG,yDAAwG;AACxG,iDAAmH;AACnH,qDAA+G;AAC/G,uDAAiH;AAEjH,mBAAmB;AACN,QAAA,qBAAqB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC5C,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE;IACzB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;CAC5B,CAAC,CAAC;AAEU,QAAA,sBAAsB,GAAG,IAAA,kCAAiB,EAAC,OAAC,CAAC,MAAM,CAAC;IAC/D,IAAI,EAAE,OAAC,CAAC,MAAM,CAAC;QACb,EAAE,EAAE,OAAC,CAAC,MAAM,EAAE;QACd,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE;QACjB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QAC/B,gBAAgB,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;KAChE,CAAC;IACF,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;QACf,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE;QACvB,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE;QACxB,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE;KACtB,CAAC;CACH,CAAC,CAAC,CAAC;AAES,QAAA,wBAAwB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC/C,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE;IACzB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAC3B,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC/B,WAAW,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,IAAI,EAAE;QACnD,OAAO,EAAE,kCAAkC;KAC5C,CAAC;CACH,CAAC,CAAC;AAEU,QAAA,yBAAyB,GAAG,8BAAsB,CAAC;AAEhE,sBAAsB;AACT,QAAA,2BAA2B,GAAG,oCAAkB,CAAC;AACjD,QAAA,4BAA4B,GAAG,IAAA,kCAAiB,EAAC,mCAAiB,CAAC,CAAC;AAEpE,QAAA,+BAA+B,GAAG,IAAA,kCAAiB,EAAC,mCAAiB,CAAC,CAAC;AAEvE,QAAA,6BAA6B,GAAG,IAAA,kCAAiB,EAAC,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,CAAC;IAC9E,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE;IACvB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;IAClB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;IAClB,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE;IACtB,aAAa,EAAE,OAAC,CAAC,MAAM,EAAE;IACzB,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE;IACf,UAAU,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACnC,SAAS,EAAE,OAAC,CAAC,IAAI,EAAE;CACpB,CAAC,CAAC,CAAC,CAAC;AAEL,yBAAyB;AACZ,QAAA,8BAA8B,GAAG,8CAA2B,CAAC;AAC7D,QAAA,+BAA+B,GAAG,IAAA,kCAAiB,EAAC,iCAAc,CAAC,CAAC;AAEpE,QAAA,4BAA4B,GAAG,wCAAuB,CAAC,MAAM,CAAC;IACzE,MAAM,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC,QAAQ,EAAE;IACvE,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CAC9B,CAAC,CAAC;AAEU,QAAA,6BAA6B,GAAG,IAAA,kCAAiB,EAAC,OAAC,CAAC,MAAM,CAAC;IACtE,SAAS,EAAE,OAAC,CAAC,KAAK,CAAC,iCAAc,CAAC;IAClC,UAAU,EAAE,yCAAwB;CACrC,CAAC,CAAC,CAAC;AAES,QAAA,4BAA4B,GAAG,IAAA,kCAAiB,EAAC,iCAAc,CAAC,CAAC;AAEjE,QAAA,mCAAmC,GAAG,IAAA,kCAAiB,EAAC,wCAAqB,CAAC,CAAC;AAE/E,QAAA,+BAA+B,GAAG,IAAA,kCAAiB,EAAC,OAAC,CAAC,MAAM,CAAC;IACxE,OAAO,EAAE,OAAC,CAAC,OAAO,EAAE;CACrB,CAAC,CAAC,CAAC;AAEJ,mBAAmB;AACN,QAAA,iCAAiC,GAAG,OAAC,CAAC,MAAM,CAAC;IACxD,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;IAC5C,OAAO,EAAE,OAAC,CAAC,MAAM,CAAC;QAChB,eAAe,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;QAC/C,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QAChC,cAAc,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QACrC,cAAc,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,QAAQ,EAAE;KAC7D,CAAC,CAAC,QAAQ,EAAE;CACd,CAAC,CAAC;AAEU,QAAA,kCAAkC,GAAG,IAAA,kCAAiB,EAAC,gCAAiB,CAAC,CAAC;AAE1E,QAAA,+BAA+B,GAAG,wCAAuB,CAAC;AAE1D,QAAA,gCAAgC,GAAG,IAAA,kCAAiB,EAAC,OAAC,CAAC,MAAM,CAAC;IACzE,QAAQ,EAAE,OAAC,CAAC,KAAK,CAAC,gCAAiB,CAAC;IACpC,UAAU,EAAE,yCAAwB;CACrC,CAAC,CAAC,CAAC;AAES,QAAA,+BAA+B,GAAG,wCAAuB,CAAC;AAE1D,QAAA,gCAAgC,GAAG,IAAA,kCAAiB,EAAC,OAAC,CAAC,MAAM,CAAC;IACzE,QAAQ,EAAE,OAAC,CAAC,KAAK,CAAC,gCAAiB,CAAC;IACpC,UAAU,EAAE,yCAAwB;CACrC,CAAC,CAAC,CAAC;AAES,QAAA,+BAA+B,GAAG,OAAC,CAAC,MAAM,CAAC;IACtD,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAC1B,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE;CAC9B,CAAC,CAAC;AAEU,QAAA,gCAAgC,GAAG,IAAA,kCAAiB,EAAC,iCAAkB,CAAC,CAAC;AAEtF,qBAAqB;AACR,QAAA,4BAA4B,GAAG,0CAAyB,CAAC;AACzD,QAAA,6BAA6B,GAAG,IAAA,kCAAiB,EAAC,qCAAoB,CAAC,CAAC;AAExE,QAAA,0BAA0B,GAAG,wCAAuB,CAAC,MAAM,CAAC;IACvE,MAAM,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,QAAQ,EAAE;IACzG,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CAC9B,CAAC,CAAC;AAEU,QAAA,2BAA2B,GAAG,IAAA,kCAAiB,EAAC,OAAC,CAAC,MAAM,CAAC;IACpE,OAAO,EAAE,OAAC,CAAC,KAAK,CAAC,qCAAoB,CAAC;IACtC,UAAU,EAAE,yCAAwB;CACrC,CAAC,CAAC,CAAC;AAES,QAAA,0BAA0B,GAAG,IAAA,kCAAiB,EAAC,qCAAoB,CAAC,CAAC;AAErE,QAAA,mCAAmC,GAAG,2CAA0B,CAAC;AACjE,QAAA,oCAAoC,GAAG,IAAA,kCAAiB,EAAC,qCAAoB,CAAC,CAAC;AAE/E,QAAA,+BAA+B,GAAG,IAAA,kCAAiB,EAAC,OAAC,CAAC,MAAM,CAAC;IACxE,OAAO,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC;IAC5B,WAAW,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC;IACzC,kBAAkB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACzC,iBAAiB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACxC,cAAc,EAAE,OAAC,CAAC,MAAM,EAAE;CAC3B,CAAC,CAAC,CAAC;AAES,QAAA,6BAA6B,GAAG,IAAA,kCAAiB,EAAC,OAAC,CAAC,MAAM,CAAC;IACtE,OAAO,EAAE,OAAC,CAAC,OAAO,EAAE;CACrB,CAAC,CAAC,CAAC;AAEJ,4BAA4B;AACf,QAAA,+BAA+B,GAAG,OAAC,CAAC,MAAM,CAAC;IACtD,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;IAClB,eAAe,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;IAC7D,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE;CACtB,CAAC,CAAC;AAEU,QAAA,gCAAgC,GAAG,IAAA,kCAAiB,EAAC,oCAAkB,CAAC,CAAC;AAEzE,QAAA,gCAAgC,GAAG,wCAAuB,CAAC,MAAM,CAAC;IAC7E,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC7B,eAAe,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC,QAAQ,EAAE;IACxE,cAAc,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;CACpD,CAAC,CAAC;AAEU,QAAA,iCAAiC,GAAG,IAAA,kCAAiB,EAAC,OAAC,CAAC,MAAM,CAAC;IAC1E,WAAW,EAAE,OAAC,CAAC,KAAK,CAAC,oCAAkB,CAAC;IACxC,UAAU,EAAE,yCAAwB;CACrC,CAAC,CAAC,CAAC;AAEJ,wBAAwB;AACX,QAAA,iCAAiC,GAAG,IAAA,kCAAiB,EAAC,OAAC,CAAC,MAAM,CAAC;IAC1E,eAAe,EAAE,OAAC,CAAC,MAAM,CAAC;QACxB,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE;QACnB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;QAClB,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE;QACvB,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE;QACvB,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE;KACxB,CAAC;IACF,gBAAgB,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,CAAC;QACjC,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;QAClB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;QAClB,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE;QACf,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE;QACtB,aAAa,EAAE,OAAC,CAAC,MAAM,EAAE;KAC1B,CAAC,CAAC;IACH,aAAa,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,CAAC;QAC9B,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;QAClB,UAAU,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QACnC,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;QAClB,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE;QACf,UAAU,EAAE,OAAC,CAAC,IAAI,EAAE;KACrB,CAAC,CAAC;IACH,mBAAmB,EAAE,OAAC,CAAC,MAAM,CAAC;QAC5B,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE;QACrB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE;QACpB,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE;QACrB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE;QACpB,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE;KACzB,CAAC;IACF,eAAe,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,CAAC;QAChC,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;QAClB,aAAa,EAAE,OAAC,CAAC,MAAM,EAAE;QACzB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;QAClB,cAAc,EAAE,OAAC,CAAC,MAAM,EAAE;KAC3B,CAAC,CAAC;CACJ,CAAC,CAAC,CAAC;AAEJ,wBAAwB;AACX,QAAA,sBAAsB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC7C,IAAI,EAAE,OAAC,CAAC,IAAI,CAAC;QACX,cAAc,EAAE,iBAAiB,EAAE,cAAc;QACjD,mBAAmB,EAAE,eAAe,EAAE,OAAO;KAC9C,CAAC;IACF,OAAO,EAAE,OAAC,CAAC,GAAG,EAAE;IAChB,SAAS,EAAE,OAAC,CAAC,IAAI,EAAE;IACnB,EAAE,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;CACjC,CAAC,CAAC;AAEU,QAAA,2BAA2B,GAAG,OAAC,CAAC,MAAM,CAAC;IAClD,IAAI,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;IAC1C,OAAO,EAAE,OAAC,CAAC,IAAI,CAAC;QACd,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,mBAAmB,EAAE,cAAc;KACrE,CAAC;IACF,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC7B,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;CACzC,CAAC,CAAC;AAEH,yBAAyB;AACZ,QAAA,6BAA6B,GAAG,IAAA,kCAAiB,EAAC,OAAC,CAAC,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC;IAC/E,KAAK,EAAE,OAAC,CAAC,MAAM,CAAC;QACd,IAAI,EAAE,OAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC;QACnC,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE;QACnB,OAAO,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,CAAC;YACxB,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE;YACjB,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE;YACnB,KAAK,EAAE,OAAC,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;SAC1B,CAAC,CAAC;KACJ,CAAC;CACH,CAAC,CAAC;AAEU,QAAA,uBAAuB,GAAG,IAAA,kCAAiB,EAAC,OAAC,CAAC,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC;IACzE,KAAK,EAAE,OAAC,CAAC,MAAM,CAAC;QACd,IAAI,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,WAAW,EAAE,eAAe,CAAC,CAAC;QAC5D,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE;QACnB,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;KAC/B,CAAC;CACH,CAAC,CAAC;AAEU,QAAA,4BAA4B,GAAG,IAAA,kCAAiB,EAAC,OAAC,CAAC,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC;IAC9E,KAAK,EAAE,OAAC,CAAC,MAAM,CAAC;QACd,IAAI,EAAE,OAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC;QACtC,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE;QACnB,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;KACnC,CAAC;CACH,CAAC,CAAC;AAEH,sBAAsB;AACT,QAAA,yBAAyB,GAAG,OAAC,CAAC,MAAM,CAAC;IAChD,MAAM,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;IACpD,SAAS,EAAE,OAAC,CAAC,IAAI,EAAE;IACnB,QAAQ,EAAE,OAAC,CAAC,MAAM,CAAC;QACjB,QAAQ,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;QAC1C,aAAa,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;QAC/C,KAAK,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,EAAE;KACnD,CAAC;IACF,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE;IACnB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;CACnB,CAAC,CAAC"}