"""
Optimized Strategy Template Manager - Lazy Loading Implementation
Provides efficient template management with on-demand loading
"""

import os
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from pathlib import Path

from .models import StrategyType, StrategyTemplate, GeneratedStrategy, TestCase


class TemplateNotFoundError(Exception):
    """Raised when requested template is not found"""
    pass


class TemplateValidationError(Exception):
    """Raised when template validation fails"""
    pass


@dataclass
class TemplateMetadata:
    """Lightweight template metadata for fast loading"""
    name: str
    description: str
    strategy_type: StrategyType
    file_path: str
    difficulty_level: str = "intermediate"  # Fixed attribute name
    
    # Template metadata
    required_libraries: List[str] = field(default_factory=list)
    required_indicators: List[str] = field(default_factory=list)
    required_symbols: List[str] = field(default_factory=list)
    
    # Quality assurance
    has_unit_tests: bool = True
    backtest_ready: bool = True
    example_params: Dict[str, Any] = field(default_factory=dict)
    
    # Template classification
    estimated_performance: Dict[str, float] = field(default_factory=dict)
    tags: List[str] = field(default_factory=list)
    file_size_kb: float = 0.0
    
    @property
    def difficulty(self) -> str:
        """Alias for difficulty_level for backward compatibility"""
        return self.difficulty_level


class OptimizedTemplateManager:
    """
    Optimized Strategy Template Manager with lazy loading
    Only loads template code when actually requested
    """
    
    def __init__(self):
        """Initialize the template manager with metadata only"""
        self.template_metadata = self._initialize_metadata()
        self.loaded_templates: Dict[str, str] = {}  # Cache for loaded templates
        self.templates_dir = Path(__file__).parent / "templates"
    
    def _initialize_metadata(self) -> Dict[str, TemplateMetadata]:
        """Initialize template metadata without loading full code"""
        metadata = {}
        
        # Twin Range Filter Template
        metadata["twin_range_filter"] = TemplateMetadata(
            name="Twin Range Filter Strategy",
            description="Dynamic range-based strategy using high/low price channels",
            strategy_type=StrategyType.BREAKOUT,
            file_path="twin_range_filter.py",
            required_libraries=["pandas", "numpy"],
            required_indicators=["high_range", "low_range", "middle_line"],
            has_unit_tests=True,
            backtest_ready=True,
            example_params={
                "symbols": ["GBPUSD"],
                "period": 20,
                "risk_per_trade": 0.015
            },
            difficulty_level="intermediate",
            estimated_performance={
                "sharpe_ratio": 1.3,
                "max_drawdown": 0.16,
                "win_rate": 0.52
            },
            tags=["range_filter", "breakout", "intermediate", "forex"],
            file_size_kb=3.2
        )
        
        # Mean Reversion RSI Template
        metadata["mean_reversion_rsi"] = TemplateMetadata(
            name="Mean Reversion RSI Strategy",
            description="Classic mean reversion strategy using RSI for overbought/oversold detection",
            strategy_type=StrategyType.MEAN_REVERSION,
            file_path="mean_reversion_rsi.py",
            required_libraries=["pandas", "numpy"],
            required_indicators=["rsi"],
            has_unit_tests=True,
            backtest_ready=True,
            example_params={
                "symbols": ["EURUSD"],
                "rsi_period": 14,
                "oversold_level": 30,
                "overbought_level": 70,
                "risk_per_trade": 0.02
            },
            difficulty_level="beginner",
            estimated_performance={
                "sharpe_ratio": 1.2,
                "max_drawdown": 0.15,
                "win_rate": 0.55
            },
            tags=["mean_reversion", "rsi", "beginner", "forex"],
            file_size_kb=2.8
        )
        
        # Momentum MACD Template
        metadata["momentum_macd"] = TemplateMetadata(
            name="Momentum MACD Strategy",
            description="Trend-following strategy using MACD crossovers for momentum detection",
            strategy_type=StrategyType.MOMENTUM,
            file_path="momentum_macd.py",
            required_libraries=["pandas", "numpy"],
            required_indicators=["macd"],
            has_unit_tests=True,
            backtest_ready=True,
            example_params={
                "symbols": ["EURUSD"],
                "macd_fast": 12,
                "macd_slow": 26,
                "macd_signal": 9,
                "risk_per_trade": 0.02
            },
            difficulty_level="intermediate",
            estimated_performance={
                "sharpe_ratio": 1.0,
                "max_drawdown": 0.20,
                "win_rate": 0.48
            },
            tags=["momentum", "macd", "intermediate", "trend_following"],
            file_size_kb=3.1
        )
        
        # Machine Learning Template
        metadata["machine_learning"] = TemplateMetadata(
            name="Machine Learning Random Forest Strategy",
            description="Advanced ML strategy using Random Forest for signal generation",
            strategy_type=StrategyType.MACHINE_LEARNING,
            file_path="machine_learning.py",
            required_libraries=["pandas", "numpy", "sklearn"],
            required_indicators=["rsi", "macd"],
            has_unit_tests=True,
            backtest_ready=True,
            example_params={
                "symbols": ["EURUSD"],
                "n_estimators": 100,
                "training_bars": 1000,
                "risk_per_trade": 0.02
            },
            difficulty_level="advanced",
            estimated_performance={
                "sharpe_ratio": 1.5,
                "max_drawdown": 0.18,
                "win_rate": 0.58
            },
            tags=["machine_learning", "random_forest", "advanced", "ai"],
            file_size_kb=5.7
        )
        
        return metadata
    
    def get_template_metadata(self, template_id: str) -> Optional[TemplateMetadata]:
        """Get template metadata without loading the full code"""
        return self.template_metadata.get(template_id)
    
    def list_templates(self) -> List[TemplateMetadata]:
        """List all available templates with metadata"""
        return list(self.template_metadata.values())
    
    def get_template_code(self, template_id: str) -> str:
        """Get template code with lazy loading and caching"""
        if template_id not in self.template_metadata:
            raise TemplateNotFoundError(f"Template '{template_id}' not found")
        
        # Check if template is already cached
        if template_id in self.loaded_templates:
            return self.loaded_templates[template_id]
        
        # Load template from file
        metadata = self.template_metadata[template_id]
        template_path = self.templates_dir / metadata.file_path
        
        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                template_code = f.read()
            
            # Cache the loaded template
            self.loaded_templates[template_id] = template_code
            
            return template_code
            
        except FileNotFoundError:
            raise TemplateNotFoundError(f"Template file not found: {template_path}")
        except Exception as e:
            raise TemplateValidationError(f"Error loading template {template_id}: {str(e)}")
    
    def get_template_size(self, template_id: str) -> float:
        """Get template file size in KB without loading the full content"""
        if template_id not in self.template_metadata:
            return 0.0
        
        metadata = self.template_metadata[template_id]
        return metadata.file_size_kb
    
    def filter_by_difficulty(self, difficulty: str) -> List[TemplateMetadata]:
        """Filter templates by difficulty level"""
        return [metadata for metadata in self.template_metadata.values() 
                if metadata.difficulty_level == difficulty]
    
    def filter_by_strategy_type(self, strategy_type: StrategyType) -> List[TemplateMetadata]:
        """Filter templates by strategy type"""
        return [metadata for metadata in self.template_metadata.values() 
                if metadata.strategy_type == strategy_type]
    
    def filter_by_tags(self, tags: List[str]) -> List[TemplateMetadata]:
        """Filter templates by tags"""
        filtered = []
        for metadata in self.template_metadata.values():
            if any(tag in metadata.tags for tag in tags):
                filtered.append(metadata)
        return filtered
    
    def get_quick_templates(self) -> List[TemplateMetadata]:
        """Get templates that load quickly (small file size)"""
        return [metadata for metadata in self.template_metadata.values() 
                if metadata.file_size_kb < 4.0]
    
    def preload_template(self, template_id: str) -> bool:
        """Preload a template into cache"""
        try:
            self.get_template_code(template_id)
            return True
        except (TemplateNotFoundError, TemplateValidationError):
            return False
    
    def clear_cache(self):
        """Clear the template cache to free memory"""
        self.loaded_templates.clear()
    
    def get_cache_info(self) -> Dict[str, Any]:
        """Get information about the current cache state"""
        return {
            "cached_templates": list(self.loaded_templates.keys()),
            "cache_size": len(self.loaded_templates),
            "total_templates": len(self.template_metadata)
        }
