#!/bin/sh
#
# Pre-commit hook to run tests before committing
# Copy this file to .git/hooks/pre-commit and make it executable

# Get the current branch name
branch=$(git symbolic-ref --short HEAD)

# Run quick tests
echo "Running quick tests before commit..."
python run_tests.py quick

# Check the exit code
if [ $? -ne 0 ]; then
    echo "❌ Tests failed. Please fix the issues before committing."
    exit 1
fi

# If we got here, tests passed
echo "✅ Tests passed. Proceeding with commit."
exit 0