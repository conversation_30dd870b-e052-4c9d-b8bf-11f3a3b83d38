"""
Test script for frontend-backend integration
This script tests the API endpoints using requests
"""

import requests
import json
import logging
from typing import Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("frontend_backend_test")

# API base URL
BASE_URL = "http://localhost:8000/api/v1"

def test_mt5_status():
    """Test MT5 status endpoint"""
    logger.info("Testing MT5 status endpoint...")
    
    try:
        response = requests.get(f"{BASE_URL}/mt5/status")
        response.raise_for_status()
        
        data = response.json()
        logger.info(f"MT5 status response: {json.dumps(data, indent=2)}")
        
        return data
    except requests.exceptions.RequestException as e:
        logger.error(f"Error testing MT5 status: {str(e)}")
        return None

def test_mt5_connect():
    """Test MT5 connect endpoint"""
    logger.info("Testing MT5 connect endpoint...")
    
    try:
        response = requests.post(f"{BASE_URL}/mt5/connect")
        response.raise_for_status()
        
        data = response.json()
        logger.info(f"MT5 connect response: {json.dumps(data, indent=2)}")
        
        return data
    except requests.exceptions.RequestException as e:
        logger.error(f"Error testing MT5 connect: {str(e)}")
        return None

def test_mt5_place_order():
    """Test MT5 place order endpoint"""
    logger.info("Testing MT5 place order endpoint...")
    
    order_data = {
        "symbol": "EURUSD",
        "orderType": "BUY",
        "volume": 0.1,
        "price": None,
        "stopLoss": None,
        "takeProfit": None
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/mt5/order",
            json=order_data
        )
        response.raise_for_status()
        
        data = response.json()
        logger.info(f"MT5 place order response: {json.dumps(data, indent=2)}")
        
        return data
    except requests.exceptions.RequestException as e:
        logger.error(f"Error testing MT5 place order: {str(e)}")
        return None

def test_mt5_get_positions():
    """Test MT5 get positions endpoint"""
    logger.info("Testing MT5 get positions endpoint...")
    
    try:
        response = requests.get(f"{BASE_URL}/mt5/positions")
        response.raise_for_status()
        
        data = response.json()
        logger.info(f"MT5 get positions response: {json.dumps(data, indent=2)}")
        
        return data
    except requests.exceptions.RequestException as e:
        logger.error(f"Error testing MT5 get positions: {str(e)}")
        return None

def test_mt5_close_position(order_id: int):
    """Test MT5 close position endpoint"""
    logger.info(f"Testing MT5 close position endpoint for order {order_id}...")
    
    try:
        response = requests.post(f"{BASE_URL}/mt5/positions/{order_id}/close")
        response.raise_for_status()
        
        data = response.json()
        logger.info(f"MT5 close position response: {json.dumps(data, indent=2)}")
        
        return data
    except requests.exceptions.RequestException as e:
        logger.error(f"Error testing MT5 close position: {str(e)}")
        return None

def run_tests():
    """Run all tests"""
    logger.info("Starting frontend-backend integration tests...")
    
    # Test MT5 status
    test_mt5_status()
    
    # Test MT5 connect
    test_mt5_connect()
    
    # Test MT5 place order
    order_response = test_mt5_place_order()
    
    # Test MT5 get positions
    positions = test_mt5_get_positions()
    
    # Test MT5 close position
    if order_response and "orderId" in order_response:
        test_mt5_close_position(order_response["orderId"])
    
    logger.info("All tests completed")

if __name__ == "__main__":
    run_tests()