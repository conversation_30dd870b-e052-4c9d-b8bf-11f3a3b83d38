import { z } from 'zod';
export declare const SubscriptionTierSchema: z.Zod<PERSON><["free", "solo", "pro", "enterprise"]>;
export type SubscriptionTier = z.infer<typeof SubscriptionTierSchema>;
export declare const UserIdSchema: z.<PERSON>od<PERSON>ed<z.ZodString, "UserId">;
export type UserId = z.infer<typeof UserIdSchema>;
export declare const UserSchema: z.ZodObject<{
    id: z.ZodBranded<z.ZodString, "UserId">;
    email: z.ZodString;
    fullName: z.ZodOptional<z.ZodString>;
    subscriptionTier: z.Zod<PERSON>num<["free", "solo", "pro", "enterprise"]>;
    apiQuotaUsed: z.ZodNumber;
    apiQuotaLimit: z.ZodNumber;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string & z.BRAND<"UserId">;
    email: string;
    subscriptionTier: "free" | "solo" | "pro" | "enterprise";
    apiQuotaUsed: number;
    apiQuotaLimit: number;
    createdAt: Date;
    updatedAt: Date;
    fullName?: string | undefined;
}, {
    id: string;
    email: string;
    subscriptionTier: "free" | "solo" | "pro" | "enterprise";
    apiQuotaUsed: number;
    apiQuotaLimit: number;
    createdAt: Date;
    updatedAt: Date;
    fullName?: string | undefined;
}>;
export type User = z.infer<typeof UserSchema>;
export declare const UserWithPasswordHashSchema: z.ZodObject<{
    id: z.ZodBranded<z.ZodString, "UserId">;
    email: z.ZodString;
    fullName: z.ZodOptional<z.ZodString>;
    subscriptionTier: z.ZodEnum<["free", "solo", "pro", "enterprise"]>;
    apiQuotaUsed: z.ZodNumber;
    apiQuotaLimit: z.ZodNumber;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
} & {
    passwordHash: z.ZodString;
}, "strip", z.ZodTypeAny, {
    id: string & z.BRAND<"UserId">;
    email: string;
    subscriptionTier: "free" | "solo" | "pro" | "enterprise";
    apiQuotaUsed: number;
    apiQuotaLimit: number;
    createdAt: Date;
    updatedAt: Date;
    passwordHash: string;
    fullName?: string | undefined;
}, {
    id: string;
    email: string;
    subscriptionTier: "free" | "solo" | "pro" | "enterprise";
    apiQuotaUsed: number;
    apiQuotaLimit: number;
    createdAt: Date;
    updatedAt: Date;
    passwordHash: string;
    fullName?: string | undefined;
}>;
export type UserWithPasswordHash = z.infer<typeof UserWithPasswordHashSchema>;
export declare const LoginRequestSchema: z.ZodObject<{
    email: z.ZodString;
    password: z.ZodString;
}, "strip", z.ZodTypeAny, {
    email: string;
    password: string;
}, {
    email: string;
    password: string;
}>;
export type LoginRequest = z.infer<typeof LoginRequestSchema>;
export declare const CreateUserRequestSchema: z.ZodObject<{
    email: z.ZodString;
    password: z.ZodEffects<z.ZodString, string, string>;
    fullName: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    email: string;
    password: string;
    fullName?: string | undefined;
}, {
    email: string;
    password: string;
    fullName?: string | undefined;
}>;
export type CreateUserRequest = z.infer<typeof CreateUserRequestSchema>;
export declare const RefreshTokenRequestSchema: z.ZodObject<{
    refreshToken: z.ZodString;
}, "strip", z.ZodTypeAny, {
    refreshToken: string;
}, {
    refreshToken: string;
}>;
export type RefreshTokenRequest = z.infer<typeof RefreshTokenRequestSchema>;
export declare const ResetPasswordRequestSchema: z.ZodObject<{
    email: z.ZodString;
}, "strip", z.ZodTypeAny, {
    email: string;
}, {
    email: string;
}>;
export type ResetPasswordRequest = z.infer<typeof ResetPasswordRequestSchema>;
export declare const ConfirmResetPasswordRequestSchema: z.ZodObject<{
    token: z.ZodString;
    newPassword: z.ZodString;
}, "strip", z.ZodTypeAny, {
    token: string;
    newPassword: string;
}, {
    token: string;
    newPassword: string;
}>;
export type ConfirmResetPasswordRequest = z.infer<typeof ConfirmResetPasswordRequestSchema>;
export declare const ChangePasswordRequestSchema: z.ZodObject<{
    currentPassword: z.ZodString;
    newPassword: z.ZodString;
}, "strip", z.ZodTypeAny, {
    newPassword: string;
    currentPassword: string;
}, {
    newPassword: string;
    currentPassword: string;
}>;
export type ChangePasswordRequest = z.infer<typeof ChangePasswordRequestSchema>;
export declare const AuthTokensSchema: z.ZodObject<{
    accessToken: z.ZodString;
    refreshToken: z.ZodString;
    expiresIn: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    refreshToken: string;
    accessToken: string;
    expiresIn: number;
}, {
    refreshToken: string;
    accessToken: string;
    expiresIn: number;
}>;
export type AuthTokens = z.infer<typeof AuthTokensSchema>;
export declare const AuthResultSchema: z.ZodObject<{
    user: z.ZodObject<{
        id: z.ZodBranded<z.ZodString, "UserId">;
        email: z.ZodString;
        fullName: z.ZodOptional<z.ZodString>;
        subscriptionTier: z.ZodEnum<["free", "solo", "pro", "enterprise"]>;
        apiQuotaUsed: z.ZodNumber;
        apiQuotaLimit: z.ZodNumber;
        createdAt: z.ZodDate;
        updatedAt: z.ZodDate;
    }, "strip", z.ZodTypeAny, {
        id: string & z.BRAND<"UserId">;
        email: string;
        subscriptionTier: "free" | "solo" | "pro" | "enterprise";
        apiQuotaUsed: number;
        apiQuotaLimit: number;
        createdAt: Date;
        updatedAt: Date;
        fullName?: string | undefined;
    }, {
        id: string;
        email: string;
        subscriptionTier: "free" | "solo" | "pro" | "enterprise";
        apiQuotaUsed: number;
        apiQuotaLimit: number;
        createdAt: Date;
        updatedAt: Date;
        fullName?: string | undefined;
    }>;
    tokens: z.ZodObject<{
        accessToken: z.ZodString;
        refreshToken: z.ZodString;
        expiresIn: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        refreshToken: string;
        accessToken: string;
        expiresIn: number;
    }, {
        refreshToken: string;
        accessToken: string;
        expiresIn: number;
    }>;
}, "strip", z.ZodTypeAny, {
    user: {
        id: string & z.BRAND<"UserId">;
        email: string;
        subscriptionTier: "free" | "solo" | "pro" | "enterprise";
        apiQuotaUsed: number;
        apiQuotaLimit: number;
        createdAt: Date;
        updatedAt: Date;
        fullName?: string | undefined;
    };
    tokens: {
        refreshToken: string;
        accessToken: string;
        expiresIn: number;
    };
}, {
    user: {
        id: string;
        email: string;
        subscriptionTier: "free" | "solo" | "pro" | "enterprise";
        apiQuotaUsed: number;
        apiQuotaLimit: number;
        createdAt: Date;
        updatedAt: Date;
        fullName?: string | undefined;
    };
    tokens: {
        refreshToken: string;
        accessToken: string;
        expiresIn: number;
    };
}>;
export type AuthResult = z.infer<typeof AuthResultSchema>;
export declare const TokenPayloadSchema: z.ZodObject<{
    userId: z.ZodBranded<z.ZodString, "UserId">;
    email: z.ZodOptional<z.ZodString>;
    subscriptionTier: z.ZodOptional<z.ZodEnum<["free", "solo", "pro", "enterprise"]>>;
    type: z.ZodEnum<["access", "refresh"]>;
    iat: z.ZodOptional<z.ZodNumber>;
    exp: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    type: "access" | "refresh";
    userId: string & z.BRAND<"UserId">;
    email?: string | undefined;
    subscriptionTier?: "free" | "solo" | "pro" | "enterprise" | undefined;
    iat?: number | undefined;
    exp?: number | undefined;
}, {
    type: "access" | "refresh";
    userId: string;
    email?: string | undefined;
    subscriptionTier?: "free" | "solo" | "pro" | "enterprise" | undefined;
    iat?: number | undefined;
    exp?: number | undefined;
}>;
export type TokenPayload = z.infer<typeof TokenPayloadSchema>;
export declare const RefreshTokenPayloadSchema: z.ZodObject<{
    userId: z.ZodBranded<z.ZodString, "UserId">;
    type: z.ZodLiteral<"refresh">;
    iat: z.ZodOptional<z.ZodNumber>;
    exp: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    type: "refresh";
    userId: string & z.BRAND<"UserId">;
    iat?: number | undefined;
    exp?: number | undefined;
}, {
    type: "refresh";
    userId: string;
    iat?: number | undefined;
    exp?: number | undefined;
}>;
export type RefreshTokenPayload = z.infer<typeof RefreshTokenPayloadSchema>;
export declare const UpdateUserProfileRequestSchema: z.ZodObject<{
    fullName: z.ZodOptional<z.ZodString>;
    email: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    email?: string | undefined;
    fullName?: string | undefined;
}, {
    email?: string | undefined;
    fullName?: string | undefined;
}>;
export type UpdateUserProfileRequest = z.infer<typeof UpdateUserProfileRequestSchema>;
export declare const UserProfileSchema: z.ZodObject<Omit<{
    id: z.ZodBranded<z.ZodString, "UserId">;
    email: z.ZodString;
    fullName: z.ZodOptional<z.ZodString>;
    subscriptionTier: z.ZodEnum<["free", "solo", "pro", "enterprise"]>;
    apiQuotaUsed: z.ZodNumber;
    apiQuotaLimit: z.ZodNumber;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "apiQuotaUsed" | "apiQuotaLimit"> & {
    lastLoginAt: z.ZodOptional<z.ZodDate>;
    emailVerified: z.ZodDefault<z.ZodBoolean>;
    phoneNumber: z.ZodOptional<z.ZodString>;
    timezone: z.ZodDefault<z.ZodString>;
    preferences: z.ZodDefault<z.ZodObject<{
        theme: z.ZodDefault<z.ZodEnum<["light", "dark"]>>;
        language: z.ZodDefault<z.ZodString>;
        notifications: z.ZodDefault<z.ZodObject<{
            email: z.ZodDefault<z.ZodBoolean>;
            push: z.ZodDefault<z.ZodBoolean>;
            trading: z.ZodDefault<z.ZodBoolean>;
        }, "strip", z.ZodTypeAny, {
            push: boolean;
            email: boolean;
            trading: boolean;
        }, {
            push?: boolean | undefined;
            email?: boolean | undefined;
            trading?: boolean | undefined;
        }>>;
    }, "strip", z.ZodTypeAny, {
        theme: "light" | "dark";
        language: string;
        notifications: {
            push: boolean;
            email: boolean;
            trading: boolean;
        };
    }, {
        theme?: "light" | "dark" | undefined;
        language?: string | undefined;
        notifications?: {
            push?: boolean | undefined;
            email?: boolean | undefined;
            trading?: boolean | undefined;
        } | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    id: string & z.BRAND<"UserId">;
    email: string;
    subscriptionTier: "free" | "solo" | "pro" | "enterprise";
    createdAt: Date;
    updatedAt: Date;
    emailVerified: boolean;
    timezone: string;
    preferences: {
        theme: "light" | "dark";
        language: string;
        notifications: {
            push: boolean;
            email: boolean;
            trading: boolean;
        };
    };
    fullName?: string | undefined;
    lastLoginAt?: Date | undefined;
    phoneNumber?: string | undefined;
}, {
    id: string;
    email: string;
    subscriptionTier: "free" | "solo" | "pro" | "enterprise";
    createdAt: Date;
    updatedAt: Date;
    fullName?: string | undefined;
    lastLoginAt?: Date | undefined;
    emailVerified?: boolean | undefined;
    phoneNumber?: string | undefined;
    timezone?: string | undefined;
    preferences?: {
        theme?: "light" | "dark" | undefined;
        language?: string | undefined;
        notifications?: {
            push?: boolean | undefined;
            email?: boolean | undefined;
            trading?: boolean | undefined;
        } | undefined;
    } | undefined;
}>;
export type UserProfile = z.infer<typeof UserProfileSchema>;
export declare const SessionSchema: z.ZodObject<{
    id: z.ZodString;
    userId: z.ZodBranded<z.ZodString, "UserId">;
    deviceInfo: z.ZodObject<{
        userAgent: z.ZodString;
        ip: z.ZodString;
        location: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        userAgent: string;
        ip: string;
        location?: string | undefined;
    }, {
        userAgent: string;
        ip: string;
        location?: string | undefined;
    }>;
    isActive: z.ZodBoolean;
    lastActivity: z.ZodDate;
    createdAt: z.ZodDate;
    expiresAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    userId: string & z.BRAND<"UserId">;
    deviceInfo: {
        userAgent: string;
        ip: string;
        location?: string | undefined;
    };
    isActive: boolean;
    lastActivity: Date;
    expiresAt: Date;
}, {
    id: string;
    createdAt: Date;
    userId: string;
    deviceInfo: {
        userAgent: string;
        ip: string;
        location?: string | undefined;
    };
    isActive: boolean;
    lastActivity: Date;
    expiresAt: Date;
}>;
export type Session = z.infer<typeof SessionSchema>;
export declare const ApiKeySchema: z.ZodObject<{
    id: z.ZodString;
    userId: z.ZodBranded<z.ZodString, "UserId">;
    name: z.ZodString;
    keyPrefix: z.ZodString;
    hashedKey: z.ZodString;
    permissions: z.ZodArray<z.ZodEnum<["read:account", "read:trades", "write:trades", "read:backtests", "write:backtests", "read:uploads", "write:uploads", "read:chat", "write:chat"]>, "many">;
    lastUsed: z.ZodOptional<z.ZodDate>;
    usageCount: z.ZodDefault<z.ZodNumber>;
    isActive: z.ZodDefault<z.ZodBoolean>;
    createdAt: z.ZodDate;
    expiresAt: z.ZodOptional<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    name: string;
    id: string;
    createdAt: Date;
    userId: string & z.BRAND<"UserId">;
    isActive: boolean;
    keyPrefix: string;
    hashedKey: string;
    permissions: ("read:account" | "read:trades" | "write:trades" | "read:backtests" | "write:backtests" | "read:uploads" | "write:uploads" | "read:chat" | "write:chat")[];
    usageCount: number;
    expiresAt?: Date | undefined;
    lastUsed?: Date | undefined;
}, {
    name: string;
    id: string;
    createdAt: Date;
    userId: string;
    keyPrefix: string;
    hashedKey: string;
    permissions: ("read:account" | "read:trades" | "write:trades" | "read:backtests" | "write:backtests" | "read:uploads" | "write:uploads" | "read:chat" | "write:chat")[];
    isActive?: boolean | undefined;
    expiresAt?: Date | undefined;
    lastUsed?: Date | undefined;
    usageCount?: number | undefined;
}>;
export type ApiKey = z.infer<typeof ApiKeySchema>;
export declare const CreateApiKeyRequestSchema: z.ZodObject<{
    name: z.ZodString;
    permissions: z.ZodArray<z.ZodEnum<["read:account", "read:trades", "write:trades", "read:backtests", "write:backtests", "read:uploads", "write:uploads", "read:chat", "write:chat"]>, "many">;
    expiresAt: z.ZodOptional<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    name: string;
    permissions: ("read:account" | "read:trades" | "write:trades" | "read:backtests" | "write:backtests" | "read:uploads" | "write:uploads" | "read:chat" | "write:chat")[];
    expiresAt?: Date | undefined;
}, {
    name: string;
    permissions: ("read:account" | "read:trades" | "write:trades" | "read:backtests" | "write:backtests" | "read:uploads" | "write:uploads" | "read:chat" | "write:chat")[];
    expiresAt?: Date | undefined;
}>;
export type CreateApiKeyRequest = z.infer<typeof CreateApiKeyRequestSchema>;
export declare const ApiKeyResponseSchema: z.ZodObject<{
    id: z.ZodString;
    name: z.ZodString;
    keyPrefix: z.ZodString;
    fullKey: z.ZodString;
    permissions: z.ZodArray<z.ZodString, "many">;
    createdAt: z.ZodDate;
    expiresAt: z.ZodOptional<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    name: string;
    id: string;
    createdAt: Date;
    keyPrefix: string;
    permissions: string[];
    fullKey: string;
    expiresAt?: Date | undefined;
}, {
    name: string;
    id: string;
    createdAt: Date;
    keyPrefix: string;
    permissions: string[];
    fullKey: string;
    expiresAt?: Date | undefined;
}>;
export type ApiKeyResponse = z.infer<typeof ApiKeyResponseSchema>;
export declare const OAuthProviderSchema: z.ZodEnum<["google", "github", "microsoft"]>;
export type OAuthProvider = z.infer<typeof OAuthProviderSchema>;
export declare const OAuthCallbackRequestSchema: z.ZodObject<{
    code: z.ZodString;
    state: z.ZodString;
    provider: z.ZodEnum<["google", "github", "microsoft"]>;
}, "strip", z.ZodTypeAny, {
    code: string;
    state: string;
    provider: "google" | "github" | "microsoft";
}, {
    code: string;
    state: string;
    provider: "google" | "github" | "microsoft";
}>;
export type OAuthCallbackRequest = z.infer<typeof OAuthCallbackRequestSchema>;
export declare const TwoFactorAuthSchema: z.ZodObject<{
    enabled: z.ZodBoolean;
    backupCodes: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    lastUsed: z.ZodOptional<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    enabled: boolean;
    lastUsed?: Date | undefined;
    backupCodes?: string[] | undefined;
}, {
    enabled: boolean;
    lastUsed?: Date | undefined;
    backupCodes?: string[] | undefined;
}>;
export declare const EnableTwoFactorRequestSchema: z.ZodObject<{
    password: z.ZodString;
}, "strip", z.ZodTypeAny, {
    password: string;
}, {
    password: string;
}>;
export type EnableTwoFactorRequest = z.infer<typeof EnableTwoFactorRequestSchema>;
export declare const VerifyTwoFactorRequestSchema: z.ZodObject<{
    code: z.ZodString;
}, "strip", z.ZodTypeAny, {
    code: string;
}, {
    code: string;
}>;
export type VerifyTwoFactorRequest = z.infer<typeof VerifyTwoFactorRequestSchema>;
export declare const PasswordStrengthSchema: z.ZodObject<{
    score: z.ZodNumber;
    feedback: z.ZodObject<{
        warning: z.ZodOptional<z.ZodString>;
        suggestions: z.ZodArray<z.ZodString, "many">;
    }, "strip", z.ZodTypeAny, {
        suggestions: string[];
        warning?: string | undefined;
    }, {
        suggestions: string[];
        warning?: string | undefined;
    }>;
    crackTime: z.ZodString;
}, "strip", z.ZodTypeAny, {
    score: number;
    feedback: {
        suggestions: string[];
        warning?: string | undefined;
    };
    crackTime: string;
}, {
    score: number;
    feedback: {
        suggestions: string[];
        warning?: string | undefined;
    };
    crackTime: string;
}>;
export type PasswordStrength = z.infer<typeof PasswordStrengthSchema>;
//# sourceMappingURL=auth.schemas.d.ts.map