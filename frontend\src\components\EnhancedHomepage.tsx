/**
 * Enhanced Homepage with Integrated Chatbot
 * Combines marketing homepage with functional chatbot interface
 */

import React, { useState, useEffect } from 'react';
import { Bot, MessageCircle, X, Send, Zap, TrendingUp, Shield, Brain } from 'lucide-react';
import StrategyChatbot from './StrategyChatbot';
import './Homepage.css';
import './EnhancedHomepage.css';

const EnhancedHomepage: React.FC = () => {
  const [activeSection, setActiveSection] = useState('hero');
  const [showChatbot, setShowChatbot] = useState(false);
  const [chatMode, setChatMode] = useState<'demo' | 'full'>('demo');

  useEffect(() => {
    const handleScroll = () => {
      const sections = ['hero', 'demo', 'features', 'how-it-works', 'pricing', 'contact'];
      const scrollPosition = window.scrollY + 100;

      for (const section of sections) {
        const element = document.getElementById(section);
        if (element) {
          const offsetTop = element.offsetTop;
          const offsetBottom = offsetTop + element.offsetHeight;
          
          if (scrollPosition >= offsetTop && scrollPosition < offsetBottom) {
            setActiveSection(section);
            break;
          }
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const samplePrompts = [
    {
      text: "Act as an experienced day trader. Analyze NVIDIA's price and volume patterns to identify potential buying or selling opportunities",
      category: "technical_analysis",
      icon: <TrendingUp size={20} />
    },
    {
      text: "Create a mean reversion strategy using RSI for EUR/USD with 2% risk management and position sizing rules",
      category: "strategy_development", 
      icon: <Brain size={20} />
    },
    {
      text: "Act as a risk management specialist. Calculate optimal position sizing for a TSLA trade with $50,000 account and 2% risk",
      category: "trade_execution",
      icon: <Shield size={20} />
    },
    {
      text: "Analyze market sentiment and news for cryptocurrency markets, focusing on Bitcoin and major altcoins",
      category: "market_analysis",
      icon: <MessageCircle size={20} />
    }
  ];

  return (
    <div className="enhanced-homepage">
      {/* Navigation */}
      <nav className={`navbar ${activeSection !== 'hero' ? 'scrolled' : ''}`}>
        <div className="nav-container">
          <div className="logo">
            <Bot className="logo-icon" />
            TradeBuilder AI
          </div>
          <div className="nav-links">
            <button 
              type="button"
              onClick={() => scrollToSection('demo')}
              className={activeSection === 'demo' ? 'active' : ''}
            >
              Try Demo
            </button>
            <button 
              type="button"
              onClick={() => scrollToSection('features')}
              className={activeSection === 'features' ? 'active' : ''}
            >
              Features
            </button>
            <button 
              type="button"
              onClick={() => scrollToSection('how-it-works')}
              className={activeSection === 'how-it-works' ? 'active' : ''}
            >
              How It Works
            </button>
            <button 
              type="button"
              onClick={() => scrollToSection('pricing')}
              className={activeSection === 'pricing' ? 'active' : ''}
            >
              Pricing
            </button>
            <button 
              type="button"
              onClick={() => setShowChatbot(true)}
              className="btn btn-primary"
            >
              <MessageCircle size={16} />
              Start Building
            </button>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section id="hero" className="hero">
        <div className="hero-content">
          <h1>Build Trading Strategies with AI</h1>
          <p>Describe your trading idea in plain English. Our AI generates complete Python code, backtests it, and connects to MT5 automatically.</p>
          <div className="hero-buttons">
            <button 
              type="button"
              onClick={() => scrollToSection('demo')} 
              className="btn btn-primary"
            >
              <Zap size={20} />
              Try It Now
            </button>
            <button 
              type="button"
              onClick={() => scrollToSection('features')} 
              className="btn btn-secondary"
            >
              See Features
            </button>
          </div>
          
          {/* Live Demo Preview */}
          <div className="hero-demo-preview">
            <div className="demo-chat-bubble">
              <Bot size={24} className="demo-bot-icon" />
              <p>"Create a momentum strategy using MACD for EUR/USD with 2% risk"</p>
            </div>
            <div className="demo-arrow">→</div>
            <div className="demo-result">
              <div className="demo-code">
                <code>class MACDMomentumStrategy...</code>
              </div>
              <div className="demo-stats">
                <span className="stat">✓ Code Generated</span>
                <span className="stat">✓ Backtested</span>
                <span className="stat">✓ Ready to Trade</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Interactive Demo Section */}
      <section id="demo" className="demo-section">
        <div className="section-container">
          <h2>Try the AI Strategy Builder</h2>
          <p>Click on any prompt below to see how our AI creates complete trading strategies:</p>
          
          <div className="prompt-gallery">
            {samplePrompts.map((prompt, index) => (
              <div key={index} className="prompt-card" onClick={() => {
                setChatMode('demo');
                setShowChatbot(true);
              }}>
                {prompt.icon}
                <div className="prompt-content">
                  <span className="prompt-category">{prompt.category.replace('_', ' ')}</span>
                  <span className="prompt-text">"{prompt.text}"</span>
                </div>
                <div className="prompt-arrow">→</div>
              </div>
            ))}
          </div>

          <div className="demo-cta">
            <button 
              type="button"
              className="btn btn-primary btn-large"
              onClick={() => {
                setChatMode('full');
                setShowChatbot(true);
              }}
            >
              <Bot size={24} />
              Start Building Your Strategy
            </button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="features">
        <div className="section-container">
          <h2>Everything You Need to Trade Better</h2>
          <div className="feature-grid">
            {[
              {
                icon: <MessageCircle size={48} />,
                title: 'Natural Language Input',
                description: 'Just describe your strategy idea in plain English. No programming knowledge needed.'
              },
              {
                icon: <Brain size={48} />,
                title: 'AI Code Generation',
                description: 'Advanced AI generates complete, tested Python trading strategies from your description.'
              },
              {
                icon: <TrendingUp size={48} />,
                title: 'Instant Backtesting',
                description: 'Automatically backtest strategies with real historical data and performance metrics.'
              },
              {
                icon: <Zap size={48} />,
                title: 'MT5 Integration',
                description: 'Deploy strategies directly to MetaTrader 5 with one click. No MQL5 coding required.'
              },
              {
                icon: <Shield size={48} />,
                title: 'Risk Management',
                description: 'Built-in risk controls, position sizing, and stop-loss management for every strategy.'
              },
              {
                icon: <Bot size={48} />,
                title: 'Continuous Learning',
                description: 'AI learns from market data and strategy performance to suggest improvements.'
              }
            ].map((feature, index) => (
              <div key={index} className="feature">
                <div className="feature-icon">{feature.icon}</div>
                <h3>{feature.title}</h3>
                <p>{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section id="how-it-works" className="how-it-works">
        <div className="section-container">
          <h2>How It Works</h2>
          <div className="steps">
            {[
              {
                step: '1',
                title: 'Describe Your Strategy',
                description: 'Tell our AI chatbot what trading strategy you want in natural language.',
                icon: <MessageCircle size={32} />
              },
              {
                step: '2',
                title: 'AI Generates Code',
                description: 'Our AI creates complete Python code with all necessary components and tests.',
                icon: <Brain size={32} />
              },
              {
                step: '3',
                title: 'Automatic Backtesting',
                description: 'Strategy is automatically backtested with historical data and performance analysis.',
                icon: <TrendingUp size={32} />
              },
              {
                step: '4',
                title: 'Deploy to MT5',
                description: 'One-click deployment to MetaTrader 5 for live trading with risk management.',
                icon: <Zap size={32} />
              }
            ].map((step, index) => (
              <div key={index} className="step">
                <div className="step-number">{step.step}</div>
                <div className="step-icon">{step.icon}</div>
                <h3>{step.title}</h3>
                <p>{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="pricing">
        <div className="section-container">
          <h2>Simple Pricing</h2>
          <div className="pricing-cards">
            {[
              {
                name: 'Free',
                price: '$0',
                period: '/month',
                features: [
                  'Basic strategy building',
                  'Limited backtesting',
                  'Community support',
                  'Paper trading only'
                ],
                featured: false,
                buttonText: 'Start Free'
              },
              {
                name: 'Pro',
                price: '$49',
                period: '/month',
                features: [
                  'Unlimited strategies',
                  'Full backtesting',
                  'Live trading on MT5',
                  'AI optimization',
                  'Priority support'
                ],
                featured: true,
                buttonText: 'Start Free Trial'
              },
              {
                name: 'Team',
                price: '$99',
                period: '/month',
                features: [
                  'Everything in Pro',
                  'Team collaboration',
                  'Advanced analytics',
                  'Custom integrations',
                  'Dedicated support'
                ],
                featured: false,
                buttonText: 'Contact Sales'
              }
            ].map((plan, index) => (
              <div key={index} className={`pricing-card ${plan.featured ? 'featured' : ''}`}>
                <h3>{plan.name}</h3>
                <div className="price">{plan.price}<span>{plan.period}</span></div>
                <ul>
                  {plan.features.map((feature, idx) => (
                    <li key={idx}>{feature}</li>
                  ))}
                </ul>
                <button 
                  type="button"
                  className={`btn ${plan.featured ? 'btn-primary' : 'btn-secondary'}`}
                >
                  {plan.buttonText}
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer id="contact" className="footer">
        <div className="footer-content">
          <div className="footer-links">
            <a href="#" aria-label="Privacy Policy">Privacy</a>
            <a href="#" aria-label="Terms of Service">Terms</a>
            <a href="#" aria-label="Documentation">Documentation</a>
            <a href="#" aria-label="Support">Support</a>
          </div>
          <p>&copy; 2025 TradeBuilder AI. Built for traders, by traders.</p>
        </div>
      </footer>

      {/* Floating Chat Button */}
      {!showChatbot && (
        <button 
          className="floating-chat-btn"
          onClick={() => setShowChatbot(true)}
          aria-label="Open chat"
        >
          <MessageCircle size={24} />
        </button>
      )}

      {/* Chatbot Modal */}
      {showChatbot && (
        <div className="chatbot-modal">
          <div className="chatbot-container">
            <div className="chatbot-header">
              <h3>
                <Bot size={20} />
                AI Strategy Builder
              </h3>
              <button 
                className="close-btn"
                onClick={() => setShowChatbot(false)}
                aria-label="Close chat"
              >
                <X size={20} />
              </button>
            </div>
            <div className="chatbot-content">
              <StrategyChatbot />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedHomepage;
