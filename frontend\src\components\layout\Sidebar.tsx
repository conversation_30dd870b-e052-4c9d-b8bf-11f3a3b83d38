﻿﻿/**
 * Sidebar Component
 * Navigation sidebar with menu items and status indicators
 */

// import React from 'react'; // Not needed with new JSX transform
import { NavLink, useLocation } from 'react-router-dom';
import { 
  X, 
  Home, 
  Upload, 
  Target, 
  MessageSquare, 
  // BarChart3, // Not currently used 
  Beaker, 
  Settings, 
  Shield,
  Crown,
  Activity,
  TrendingUp
} from 'lucide-react';

import { useAuth, useSubscriptionTier } from '@/hooks/useAuth';

interface SidebarProps {
  open: boolean;
  onClose: () => void;
}

interface NavItem {
  name: string;
  href: string;
  icon: any;
  feature?: string;
  adminOnly?: boolean;
  badge?: string;
}

const navigation: NavItem[] = [
  { name: 'Dashboard', href: '/', icon: Home },
  { name: 'Upload Data', href: '/upload', icon: Upload },
  { name: 'Backtesting', href: '/backtesting', icon: Target },
  { name: 'Strategies', href: '/strategies', icon: Target },
  { name: 'Live Trading', href: '/live-trading', icon: Activity },
  { name: 'MT5 Accounts', href: '/mt5-accounts', icon: TrendingUp },
  { name: 'AI Chat', href: '/chat', icon: MessageSquare },
  { name: 'Portfolio', href: '/portfolio', icon: TrendingUp },
  { name: 'DGM Experiments', href: '/dgm', icon: Beaker, feature: 'dgm_experiments', badge: 'Pro' },
  { name: 'MT5 Test', href: '/mt5-test', icon: TrendingUp, badge: 'Test' },
  { name: 'Settings', href: '/settings', icon: Settings },
  { name: 'Admin', href: '/admin', icon: Shield, adminOnly: true },
];

export function Sidebar({ open, onClose }: SidebarProps) {
  const location = useLocation();
  const { user } = useAuth();
  const { hasFeature, tier } = useSubscriptionTier();

  const isActive = (href: string) => {
    if (href === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(href);
  };

  const canAccessItem = (item: NavItem) => {
    if (item.adminOnly && user?.subscriptionTier !== 'enterprise') return false; // Use subscription tier as admin check
    if (item.feature && !hasFeature(item.feature)) return false;
    return true;
  };

  return (
    <>
      {/* Mobile overlay */}
      {open && (
        <div
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${
          open ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                AI
              </div>
              <div>
                <h1 className="text-lg font-bold text-gray-900">Trading Platform</h1>
                <p className="text-xs text-gray-500">AI-Powered Analytics</p>
              </div>
            </div>
            
            <button
              onClick={onClose}
              className="lg:hidden p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
            {navigation.map((item) => {
              const accessible = canAccessItem(item);
              const active = isActive(item.href);
              
              return (
                <div key={item.name} className="relative">
                  {accessible ? (
                    <NavLink
                      to={item.href}
                      className={`group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 ${
                        active
                          ? 'bg-primary-100 text-primary-700 shadow-sm'
                          : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                      }`}
                      onClick={() => onClose()}
                    >
                      <item.icon
                        className={`mr-3 h-5 w-5 transition-colors ${
                          active ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-600'
                        }`}
                      />
                      <span className="flex-1">{item.name}</span>
                      
                      {item.badge && (
                        <span className="badge-primary text-xs ml-2">
                          {item.badge}
                        </span>
                      )}
                      
                      {/* Active indicator */}
                      {active && (
                        <div className="absolute left-0 top-0 bottom-0 w-1 bg-primary-600 rounded-r-full"></div>
                      )}
                    </NavLink>
                  ) : (
                    <div className="group flex items-center px-3 py-2.5 text-sm font-medium text-gray-400 cursor-not-allowed rounded-lg">
                      <item.icon className="mr-3 h-5 w-5 text-gray-300" />
                      <span className="flex-1">{item.name}</span>
                      {item.badge && (
                        <span className="badge-gray text-xs ml-2">
                          {item.badge}
                        </span>
                      )}
                    </div>
                  )}
                </div>
              );
            })}
          </nav>

          {/* Status Section */}
          <div className="p-4 border-t border-gray-200 space-y-3">
            {/* Subscription Status */}
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-3 border border-blue-200">
              <div className="flex items-center space-x-2 mb-2">
                <Crown className="w-4 h-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-900 capitalize">
                  {tier} Plan
                </span>
              </div>
              
              <div className="space-y-1 text-xs text-blue-700">
                <div className="flex justify-between">
                  <span>API Usage:</span>
                  <span>{user?.apiQuotaUsed || 0}/{user?.apiQuotaLimit || 100}</span>
                </div>
                <div className="w-full bg-blue-200 rounded-full h-1">
                  <div
                    className="bg-blue-600 h-1 rounded-full transition-all duration-300"
                    style={{
                      width: `${user ? (user.apiQuotaUsed / user.apiQuotaLimit) * 100 : 0}%`,
                    }}
                  />
                </div>
              </div>
              
              {tier === 'free' && (
                <button className="w-full mt-2 px-3 py-1.5 bg-blue-600 text-white rounded text-xs font-medium hover:bg-blue-700 transition-colors">
                  Upgrade Now
                </button>
              )}
            </div>

            {/* System Status */}
            <div className="bg-green-50 rounded-lg p-3 border border-green-200">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-green-900">System Online</span>
              </div>
              <div className="flex items-center space-x-1 mt-1">
                <Activity className="w-3 h-3 text-green-600" />
                <span className="text-xs text-green-700">All services operational</span>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                {user?.fullName ? user.fullName.charAt(0).toUpperCase() : user?.email?.charAt(0).toUpperCase()}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {user?.fullName || user?.email?.split('@')[0]}
                </p>
                <p className="text-xs text-gray-500">{user?.email}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
