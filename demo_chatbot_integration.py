"""
Demo Script for Strategy Chatbot Integration
Shows how the chatbot can generate Python trading strategies from natural language
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.chatbot.strategy_chatbot import StrategyChatbot
from src.chatbot.strategy_templates import Strategy<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def demo_basic_strategy_generation():
    """Demo basic strategy generation from natural language"""
    print("🤖 Strategy Chatbot Demo")
    print("=" * 50)
    
    # Initialize chatbot
    chatbot = StrategyChatbot()
    
    # Example user requests
    user_requests = [
        "Create a mean reversion strategy for EUR/USD using RSI with 2% risk per trade",
        "Build a momentum strategy for GBP/USD and USD/JPY using MACD on 4-hour timeframe",
        "I want a machine learning strategy using Random Forest with RSI and MACD features"
    ]
    
    for i, request in enumerate(user_requests, 1):
        print(f"\n📝 User Request {i}:")
        print(f"'{request}'")
        print("\n🔄 Processing...")
        
        try:
            response = chatbot.generate_strategy_from_text(request)
            
            if response.success:
                print(f"✅ Success! Generated: {response.strategy_name}")
                print(f"⏱️  Processing time: {response.processing_time:.2f}s")
                print(f"🎯 Confidence: {response.confidence_score:.1%}")
                
                if response.validation_warnings:
                    print(f"⚠️  Warnings: {len(response.validation_warnings)}")
                
                # Show first few lines of generated code
                code_lines = response.generated_code.split('\n')[:10]
                print("\n📄 Generated Code Preview:")
                for line in code_lines:
                    print(f"    {line}")
                print("    ...")
                
            else:
                print(f"❌ Failed: {response.error_message}")
                if response.suggestions:
                    print("💡 Suggestions:")
                    for suggestion in response.suggestions:
                        print(f"  • {suggestion}")
        
        except Exception as e:
            print(f"❌ Error: {str(e)}")
        
        print("-" * 50)


def demo_code_explanation():
    """Demo code explanation feature"""
    print("\n🔍 Code Explanation Demo")
    print("=" * 50)
    
    chatbot = StrategyChatbot()
    
    sample_code = '''
class MeanReversionStrategy(StrategyBase):
    def calculate_rsi(self, prices, period=14):
        # RSI calculation logic
        pass
    
    def generate_signal(self, data):
        rsi = self.calculate_rsi(data['close'])
        if rsi < 30:
            return {"signal": "buy", "confidence": 0.8}
        elif rsi > 70:
            return {"signal": "sell", "confidence": 0.8}
        return {"signal": "hold", "confidence": 0.5}
'''
    
    print("📄 Sample Code:")
    print(sample_code)
    
    print("\n🤖 Chatbot Explanation:")
    explanation = chatbot.explain_code(sample_code)
    print(explanation)


def demo_strategy_templates():
    """Demo strategy templates"""
    print("\n📋 Strategy Templates Demo")
    print("=" * 50)
    
    template_manager = StrategyTemplateManager()
    
    # Show available templates
    templates = template_manager.get_available_templates()
    print(f"📚 Available Templates ({len(templates)}):")
    
    for template in templates:
        print(f"  • {template.name} ({template.difficulty_level})")
        print(f"    {template.description}")
        print(f"    Tags: {', '.join(template.tags)}")
        print()
    
    # Customize a template
    print("🔧 Customizing Mean Reversion RSI Template:")
    template = template_manager.get_template("mean_reversion_rsi")
    
    if template:
        custom_params = {
            "symbols": ["GBPUSD", "USDJPY"],
            "rsi_period": 21,
            "oversold_level": 25,
            "overbought_level": 75,
            "risk_per_trade": 0.015
        }
        
        customized_strategy = template_manager.customize_template(template, custom_params)
        
        print(f"✅ Customized Strategy: {customized_strategy.strategy_name}")
        print(f"📊 Test Cases: {len(customized_strategy.test_cases)}")
        print(f"📦 Dependencies: {', '.join(customized_strategy.dependencies)}")


def demo_strategy_suggestions():
    """Demo strategy suggestions"""
    print("\n💡 Strategy Suggestions Demo")
    print("=" * 50)
    
    chatbot = StrategyChatbot()
    
    scenarios = [
        ("beginner", "volatile market"),
        ("intermediate", "trending market"),
        ("advanced", "sideways market")
    ]
    
    for experience, market_condition in scenarios:
        print(f"\n👤 Experience: {experience.title()}")
        print(f"📈 Market: {market_condition.title()}")
        
        suggestions = chatbot.get_strategy_suggestions(market_condition, experience)
        
        print("💡 Suggestions:")
        for suggestion in suggestions:
            print(f"  • {suggestion}")


if __name__ == "__main__":
    try:
        print("🚀 AI Trading Strategy Chatbot Demo")
        print("This demo shows how users can describe trading strategies in natural language")
        print("and get complete, tested Python code in return.\n")
        
        # Run demos
        demo_basic_strategy_generation()
        demo_code_explanation()
        demo_strategy_templates()
        demo_strategy_suggestions()
        
        print("\n🎉 Demo completed successfully!")
        print("\nKey Benefits of this approach:")
        print("✅ Natural language to Python code generation")
        print("✅ Comprehensive code validation and security checks")
        print("✅ Pre-built templates for common strategies")
        print("✅ Automatic test case generation")
        print("✅ Code explanation in plain English")
        print("✅ Strategy customization and modification")
        print("✅ TDD-compliant implementation")
        
    except Exception as e:
        print(f"❌ Demo failed: {str(e)}")
        import traceback
        traceback.print_exc()