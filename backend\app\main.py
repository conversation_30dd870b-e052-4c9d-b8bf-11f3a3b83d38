"""
Main FastAPI application for AI Enhanced Trading Platform
"""

import logging
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from backend.app.mt5_bridge.mt5 import router as mt5_router
from backend.app.backtesting.backtest import router as backtest_router
from backend.app.api.routes.auth import router as auth_router
from backend.app.api.routes.homepage import router as homepage_router
from backend.app.strategies.strategy_helper_api import router as strategy_router
from backend.app.chatbot.ollama_api import router as ollama_router

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("api")

# Create FastAPI app
app = FastAPI(
    title="AI Enhanced Trading Platform API",
    description="API for AI Enhanced Trading Platform",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For development - restrict in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(auth_router, prefix="/api/v1/auth", tags=["Authentication"])
app.include_router(mt5_router, prefix="/api/v1/mt5", tags=["MT5 Bridge"])
app.include_router(backtest_router, prefix="/api/v1/backtest", tags=["Backtesting"])
app.include_router(ollama_router, prefix="/api/ollama", tags=["Ollama AI Chatbot"])
app.include_router(homepage_router, tags=["Homepage"])
# Include the strategy helper API endpoints
try:
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
    from backend.strategy_helper_api import app as strategy_app
    app.mount("/", strategy_app)
    logger.info("Strategy Helper API mounted successfully")
except ImportError as e:
    logger.warning(f"Could not import strategy_helper_api: {str(e)}")
    
    # Add a simple API endpoint for prompts as fallback
    from fastapi import APIRouter
    
    prompts_router = APIRouter()
    
    @prompts_router.get("/ai-prompts")
    async def get_ai_prompts():
        """Get default AI prompts"""
        return [
            {
                "id": "trend_following",
                "title": "Trend Following Strategy",
                "description": "Simple yet effective trend following strategy using moving averages",
                "category": "technical_analysis",
                "prompt": "Create a trend following strategy using 20 and 50 period moving averages for EUR/USD on the H1 timeframe."
            },
            {
                "id": "rsi_strategy",
                "title": "RSI Reversal Strategy",
                "description": "Mean reversion strategy using RSI indicator to identify overbought/oversold conditions",
                "category": "technical_analysis",
                "prompt": "Create a mean reversion strategy using the RSI indicator for EUR/USD. Buy when RSI goes below 30 and sell when it goes above 70."
            },
            {
                "id": "breakout_strategy",
                "title": "Breakout Strategy",
                "description": "Strategy to capture price breakouts from consolidation patterns",
                "category": "trade_execution", 
                "prompt": "Create a breakout strategy that identifies consolidation patterns and enters trades when price breaks out with increased volume."
            },
            {
                "id": "mql5_indicator",
                "title": "Custom MQL5 Indicator",
                "description": "Create a custom indicator in MQL5 without paying marketplace fees",
                "category": "trade_execution",
                "prompt": "Generate a custom MQL5 indicator that identifies divergences between price and RSI."
            }
        ]
    
    app.include_router(prompts_router)

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "AI Enhanced Trading Platform API",
        "version": "1.0.0",
        "docs_url": "/docs"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "ok"}