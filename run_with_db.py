"""
Run the frontend and backend with database integration
"""

import os
import sys
import time
import threading
import subprocess
from pathlib import Path

def print_banner(text, char="="):
    """Print a banner with the given text"""
    print(f"\n{char * 60}")
    print(f" {text}")
    print(f"{char * 60}\n")

def run_backend():
    """Run the backend server with database integration"""
    print_banner("STARTING BACKEND SERVER WITH DATABASE", "=")
    
    # Get the project root directory
    project_root = Path.cwd()
    
    # Path to the server script
    server_script = os.path.join(project_root, "backend", "server.py")
    
    # Run the server
    try:
        subprocess.run([sys.executable, server_script], check=True)
    except KeyboardInterrupt:
        print("Backend server stopped by user")
    except Exception as e:
        print(f"Error running backend server: {e}")

def run_frontend():
    """Run the frontend server"""
    print_banner("STARTING FRONTEND SERVER", "=")
    
    # Get the project root directory
    project_root = Path.cwd()
    
    # Path to the frontend directory
    frontend_dir = os.path.join(project_root, "frontend")
    
    # Run the frontend server
    try:
        # Change to the frontend directory
        os.chdir(frontend_dir)
        
        # Run npm run dev
        subprocess.run(["npm", "run", "dev"], check=True)
    except KeyboardInterrupt:
        print("Frontend server stopped by user")
    except Exception as e:
        print(f"Error running frontend server: {e}")

def main():
    """Main execution function"""
    print("🚀 AI Enhanced Trading Platform - Development Environment with Database")
    
    # Start the backend server in a separate thread
    backend_thread = threading.Thread(target=run_backend)
    backend_thread.daemon = True
    backend_thread.start()
    
    # Wait for the backend server to start
    print("Waiting for backend server to start...")
    time.sleep(2)
    
    # Run the frontend server in the main thread
    run_frontend()

if __name__ == "__main__":
    main()