#!/usr/bin/env python
"""Debug integration test results"""

import sys
import os

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

from services.darwin_godel.strategy_verifier import DarwinGodelVerifier

def main():
    verifier = DarwinGodelVerifier()
    
    strategy = """
def trading_strategy(data, params):
    if len(data['close']) < 20:
        return {'signal': 'hold', 'confidence': 0.5}
    
    # Calculate simple moving average
    sma_period = 20
    prices = data['close']
    sma = sum(prices[-sma_period:]) / sma_period
    current_price = prices[-1]
    
    # Mean reversion logic
    threshold = 0.02
    if current_price < sma * (1 - threshold):
        return {'signal': 'buy', 'confidence': 0.8}
    elif current_price > sma * (1 + threshold):
        return {'signal': 'sell', 'confidence': 0.8}
    else:
        return {'signal': 'hold', 'confidence': 0.5}
"""
    
    result = verifier.verify_strategy(strategy)
    
    print("=== VERIFICATION RESULT ===")
    for key, value in result.items():
        if key == 'pattern_analysis':
            print(f"{key}:")
            for sub_key, sub_value in value.items():
                print(f"  {sub_key}: {sub_value}")
        else:
            print(f"{key}: {value}")

if __name__ == "__main__":
    main()