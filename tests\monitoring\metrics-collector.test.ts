// tests/monitoring/metrics-collector.test.ts
import { describe, it, expect, beforeEach } from '@jest/globals';
import { MetricsCollector } from '../../src/monitoring/metrics-collector';

describe('MetricsCollector', () => {
  let metricsCollector: MetricsCollector;

  beforeEach(() => {
    metricsCollector = new MetricsCollector();
  });

  describe('counter metrics', () => {
    it('should increment counter metrics', () => {
      // Arrange
      const metricName = 'trades_total';
      
      // Act
      metricsCollector.incrementCounter(metricName, { status: 'success' });
      metricsCollector.incrementCounter(metricName, { status: 'success' });
      metricsCollector.incrementCounter(metricName, { status: 'failed' });

      // Assert
      const metrics = metricsCollector.getMetrics();
      console.log('Debug - metrics:', JSON.stringify(metrics, null, 2));
      console.log('Debug - metricName:', metricName);
      expect(metrics[metricName]).toBeDefined();
      const counterMetric = metrics[metricName] as any;
      expect(counterMetric.value).toBe(3);
      expect(counterMetric.labels).toEqual({
        success: 2,
        failed: 1,
      });
    });
  });

  describe('gauge metrics', () => {
    it('should set gauge values', () => {
      // Arrange
      const metricName = 'active_connections';
      const value = 12000;
      
      // Act
      metricsCollector.setGauge(metricName, value, { server: 'web-01' });

      // Assert
      const metrics = metricsCollector.getMetrics();
      expect(metrics[metricName]).toBeDefined();
      const gaugeMetric = metrics[metricName] as any;
      expect(gaugeMetric.value).toBe(value);
      expect(gaugeMetric.labels).toEqual({ server: 'web-01' });
    });
  });

  describe('histogram metrics', () => {
    it('should record histogram observations', () => {
      // Arrange
      const metricName = 'request_duration';
      const values = [10, 20, 30, 40, 50];
      
      // Act
      values.forEach(value => {
        metricsCollector.recordHistogram(metricName, value, { endpoint: '/api/trades' });
      });

      // Assert
      const metrics = metricsCollector.getMetrics();
      expect(metrics[metricName]).toBeDefined();
      const histogramMetric = metrics[metricName] as any;
      expect(histogramMetric.count).toBe(5);
      expect(histogramMetric.sum).toBe(150);
      expect(histogramMetric.buckets).toBeDefined();
    });
  });

  describe('metric export', () => {
    it('should export metrics in Prometheus format', () => {
      // Arrange
      metricsCollector.incrementCounter('http_requests_total', { method: 'GET' });
      metricsCollector.setGauge('memory_usage_bytes', 1024000);

      // Act
      const exported = metricsCollector.exportPrometheusMetrics();

      // Assert
      expect(exported).toContain('# TYPE http_requests_total counter');
      expect(exported).toContain('http_requests_total 1');
      expect(exported).toContain('# TYPE memory_usage_bytes gauge');
      expect(exported).toContain('memory_usage_bytes 1024000');
    });
  });
});