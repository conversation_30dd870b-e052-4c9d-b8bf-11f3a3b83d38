"use strict";
/**
 * Worker Management Schemas
 * Defines types for Python background worker integration
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkerSchemas = exports.SystemResourceUsageSchema = exports.WorkerConfigSchema = exports.WorkerEventSchema = exports.DGMExperimentProgressSchema = exports.FileProcessingProgressSchema = exports.WorkerBridgeStatusSchema = exports.WorkerManagementResponseSchema = exports.WorkerManagementRequestSchema = exports.DGMExperimentSchema = exports.BacktestJobSchema = exports.FileUploadSessionSchema = exports.WorkerHealthCheckSchema = exports.WorkerStatsSchema = exports.WorkerStatusSchema = void 0;
const zod_1 = require("zod");
const backtest_schemas_1 = require("./backtest.schemas");
// Worker Status Schema
exports.WorkerStatusSchema = zod_1.z.object({
    name: zod_1.z.string(),
    status: zod_1.z.enum(['running', 'stopped', 'error', 'restarting']),
    pid: zod_1.z.number().optional(),
    start_time: zod_1.z.date().optional(),
    last_activity: zod_1.z.date().optional(),
    error_message: zod_1.z.string().optional(),
});
// Worker Stats Schema
exports.WorkerStatsSchema = zod_1.z.object({
    manager_status: zod_1.z.enum(['running', 'stopped', 'starting', 'stopping']),
    uptime_seconds: zod_1.z.number(),
    start_time: zod_1.z.string(),
    active_workers: zod_1.z.number(),
    active_tasks: zod_1.z.number(),
    worker_stats: zod_1.z.record(zod_1.z.string(), zod_1.z.object({
        status: zod_1.z.string(),
        running_jobs: zod_1.z.number().optional(),
        max_concurrent: zod_1.z.number().optional(),
        poll_interval: zod_1.z.number().optional(),
        job_ids: zod_1.z.array(zod_1.z.string()).optional(),
    })),
});
// Worker Health Check Schema
exports.WorkerHealthCheckSchema = zod_1.z.object({
    healthy: zod_1.z.boolean(),
    timestamp: zod_1.z.date(),
    workers: zod_1.z.record(zod_1.z.string(), zod_1.z.object({
        healthy: zod_1.z.boolean(),
        status: zod_1.z.string().optional(),
        reason: zod_1.z.string().optional(),
    })),
    uptime: zod_1.z.number(),
    error: zod_1.z.string().optional(),
});
// File Upload Session Schema
exports.FileUploadSessionSchema = zod_1.z.object({
    id: zod_1.z.string(),
    user_id: zod_1.z.string(),
    original_filename: zod_1.z.string(),
    file_size: zod_1.z.number(),
    status: zod_1.z.enum(['pending', 'mapping_confirmed', 'parsing_in_progress', 'ready', 'error']),
    final_mapping: zod_1.z.record(zod_1.z.string(), zod_1.z.string()).optional(),
    timezone: zod_1.z.string().optional(),
    rows_processed: zod_1.z.number().optional(),
    error_message: zod_1.z.string().optional(),
    created_at: zod_1.z.date(),
    updated_at: zod_1.z.date(),
    temporary_file_path: zod_1.z.string().optional(),
});
// Backtest Job Schema
exports.BacktestJobSchema = zod_1.z.object({
    id: zod_1.z.string(),
    user_id: zod_1.z.string(),
    name: zod_1.z.string(),
    status: zod_1.z.enum(['pending', 'running', 'completed', 'error', 'cancelled']),
    symbol: zod_1.z.string(),
    start_date: zod_1.z.date(),
    end_date: zod_1.z.date(),
    strategy_config: zod_1.z.record(zod_1.z.string(), zod_1.z.any()),
    progress: zod_1.z.number().min(0).max(100).optional(),
    started_at: zod_1.z.date().optional(),
    completed_at: zod_1.z.date().optional(),
    error_message: zod_1.z.string().optional(),
    results: zod_1.z.record(zod_1.z.string(), zod_1.z.any()).optional(),
    created_at: zod_1.z.date(),
});
// DGM Experiment Schema
exports.DGMExperimentSchema = zod_1.z.object({
    id: zod_1.z.string(),
    user_id: zod_1.z.string(),
    experiment_name: zod_1.z.string(),
    status: zod_1.z.enum(['pending', 'running', 'completed', 'error', 'deployed']),
    base_strategy: zod_1.z.record(zod_1.z.string(), zod_1.z.any()),
    generated_strategy: zod_1.z.record(zod_1.z.string(), zod_1.z.any()).optional(),
    fitness_improvement: zod_1.z.number().optional(),
    deployed_backtest_id: zod_1.z.string().optional(),
    started_at: zod_1.z.date().optional(),
    completed_at: zod_1.z.date().optional(),
    error_message: zod_1.z.string().optional(),
    created_at: zod_1.z.date(),
});
// Worker Management Request Schema
exports.WorkerManagementRequestSchema = zod_1.z.object({
    action: zod_1.z.enum(['status', 'health', 'restart', 'stats']),
    worker_name: zod_1.z.string().optional(),
    timestamp: zod_1.z.date(),
    request_id: zod_1.z.string(),
});
// Worker Management Response Schema
exports.WorkerManagementResponseSchema = zod_1.z.object({
    success: zod_1.z.boolean(),
    data: zod_1.z.any().optional(),
    error: zod_1.z.object({
        code: zod_1.z.string(),
        message: zod_1.z.string(),
        details: zod_1.z.string().optional(),
    }).optional(),
    timestamp: zod_1.z.date(),
    request_id: zod_1.z.string(),
});
// Worker Bridge Status Schema
exports.WorkerBridgeStatusSchema = zod_1.z.object({
    bridge_healthy: zod_1.z.boolean(),
    python_workers_healthy: zod_1.z.boolean(),
    last_health_check: zod_1.z.date().optional(),
    last_known_status: exports.WorkerStatsSchema.optional(),
    monitoring_config: zod_1.z.object({
        health_check_interval: zod_1.z.number(),
        status_poll_interval: zod_1.z.number(),
        auto_restart: zod_1.z.boolean(),
    }),
    active_jobs: zod_1.z.object({
        file_parsing: zod_1.z.number(),
        backtests: zod_1.z.number(),
        dgm_experiments: zod_1.z.number(),
    }),
});
// File Processing Progress Schema
exports.FileProcessingProgressSchema = zod_1.z.object({
    session_id: zod_1.z.string(),
    status: zod_1.z.enum(['pending', 'parsing', 'validating', 'inserting', 'completed', 'error']),
    progress_percent: zod_1.z.number().min(0).max(100),
    rows_processed: zod_1.z.number(),
    total_rows: zod_1.z.number().optional(),
    current_step: zod_1.z.string(),
    error_message: zod_1.z.string().optional(),
    timestamp: zod_1.z.date(),
});
// Backtest Progress Schema - Using the one from backtest.schemas.ts to avoid conflicts
// export const BacktestProgressSchema = z.object({
//   backtest_id: z.string(),
//   status: z.enum(['pending', 'initializing', 'running', 'analyzing', 'completed', 'error']),
//   progress_percent: z.number().min(0).max(100),
//   current_step: z.string(),
//   trades_processed: z.number().optional(),
//   total_trades: z.number().optional(),
//   elapsed_time: z.number().optional(),
//   estimated_completion: z.date().optional(),
//   error_message: z.string().optional(),
//   timestamp: z.date(),
// });
// export type BacktestProgress = z.infer<typeof BacktestProgressSchema>;
// DGM Experiment Progress Schema
exports.DGMExperimentProgressSchema = zod_1.z.object({
    experiment_id: zod_1.z.string(),
    status: zod_1.z.enum(['pending', 'initializing', 'evolving', 'testing', 'completed', 'error']),
    progress_percent: zod_1.z.number().min(0).max(100),
    current_generation: zod_1.z.number().optional(),
    total_generations: zod_1.z.number().optional(),
    best_fitness: zod_1.z.number().optional(),
    fitness_improvement: zod_1.z.number().optional(),
    current_step: zod_1.z.string(),
    elapsed_time: zod_1.z.number().optional(),
    estimated_completion: zod_1.z.date().optional(),
    error_message: zod_1.z.string().optional(),
    timestamp: zod_1.z.date(),
});
// Worker Event Schema
exports.WorkerEventSchema = zod_1.z.object({
    event_type: zod_1.z.enum([
        'worker_started',
        'worker_stopped',
        'worker_error',
        'worker_restarted',
        'job_started',
        'job_completed',
        'job_failed',
        'job_progress',
        'health_check',
        'status_update'
    ]),
    worker_name: zod_1.z.string().optional(),
    job_id: zod_1.z.string().optional(),
    job_type: zod_1.z.enum(['file_parsing', 'backtest', 'dgm_experiment']).optional(),
    data: zod_1.z.record(zod_1.z.string(), zod_1.z.any()).optional(),
    timestamp: zod_1.z.date(),
});
// Worker Configuration Schema
exports.WorkerConfigSchema = zod_1.z.object({
    file_parser: zod_1.z.object({
        poll_interval: zod_1.z.number().default(10),
        max_file_size: zod_1.z.number().default(100 * 1024 * 1024), // 100MB
        chunk_size: zod_1.z.number().default(10000),
        supported_formats: zod_1.z.array(zod_1.z.string()).default(['.csv', '.xlsx', '.json']),
    }),
    backtest_runner: zod_1.z.object({
        poll_interval: zod_1.z.number().default(10),
        max_concurrent: zod_1.z.number().default(3),
        timeout_hours: zod_1.z.number().default(24),
    }),
    dgm_monitor: zod_1.z.object({
        poll_interval: zod_1.z.number().default(20),
        max_concurrent: zod_1.z.number().default(2),
        enabled: zod_1.z.boolean().default(false),
        fitness_threshold: zod_1.z.number().default(0.05),
        timeout_hours: zod_1.z.number().default(1),
    }),
    general: zod_1.z.object({
        log_level: zod_1.z.enum(['DEBUG', 'INFO', 'WARNING', 'ERROR']).default('INFO'),
        health_check_interval: zod_1.z.number().default(30),
        status_poll_interval: zod_1.z.number().default(60),
        auto_restart: zod_1.z.boolean().default(true),
    }),
});
// System Resource Usage Schema
exports.SystemResourceUsageSchema = zod_1.z.object({
    cpu_percent: zod_1.z.number().min(0).max(100),
    memory_percent: zod_1.z.number().min(0).max(100),
    memory_used_mb: zod_1.z.number(),
    disk_usage_percent: zod_1.z.number().min(0).max(100),
    active_connections: zod_1.z.number(),
    timestamp: zod_1.z.date(),
});
// Export all schemas
exports.WorkerSchemas = {
    WorkerStatus: exports.WorkerStatusSchema,
    WorkerStats: exports.WorkerStatsSchema,
    WorkerHealthCheck: exports.WorkerHealthCheckSchema,
    FileUploadSession: exports.FileUploadSessionSchema,
    BacktestJob: exports.BacktestJobSchema,
    DGMExperiment: exports.DGMExperimentSchema,
    WorkerManagementRequest: exports.WorkerManagementRequestSchema,
    WorkerManagementResponse: exports.WorkerManagementResponseSchema,
    WorkerBridgeStatus: exports.WorkerBridgeStatusSchema,
    FileProcessingProgress: exports.FileProcessingProgressSchema,
    BacktestProgress: backtest_schemas_1.BacktestProgressSchema,
    DGMExperimentProgress: exports.DGMExperimentProgressSchema,
    WorkerEvent: exports.WorkerEventSchema,
    WorkerConfig: exports.WorkerConfigSchema,
    SystemResourceUsage: exports.SystemResourceUsageSchema,
};
//# sourceMappingURL=workers.js.map