# tests/test_mt5_bridge.py
import pytest
from unittest.mock import patch, MagicMock, PropertyMock
from datetime import datetime
import json

from python_engine.mt5_bridge import (
    MT5Bridge, 
    MT5BridgeException, 
    ConnectionState, 
    OrderType,
    OrderRequest,
    OrderResult,
    Position,
    SymbolInfo
)

class TestMT5BridgeConnection:
    """Test MT5 Bridge connection functionality"""
    
    @patch("python_engine.mt5_bridge.mt5")
    def test_connect_success_live_mode(self, mock_mt5):
        """Test successful connection in live mode"""
        # Arrange
        mock_mt5.initialize.return_value = True
        bridge = MT5Bridge(offline_mode=False)
        
        # Act
        result = bridge.connect()
        
        # Assert
        assert result is True
        assert bridge.connection_state == ConnectionState.CONNECTED
        assert bridge.is_connected() is True
        mock_mt5.initialize.assert_called_once()
    
    @patch("python_engine.mt5_bridge.mt5")
    def test_connect_failure_mt5_not_available(self, mock_mt5):
        """Test connection failure when MT5 terminal is not available"""
        # Arrange
        mock_mt5.initialize.return_value = False
        mock_mt5.last_error.return_value = (1, "Terminal not found")
        bridge = MT5Bridge(offline_mode=False)
        
        # Act & Assert
        with pytest.raises(MT5BridgeException) as exc_info:
            # Try to connect multiple times to trigger max attempts
            for _ in range(4):  # More than max_connection_attempts
                try:
                    bridge.connect()
                except MT5BridgeException:
                    continue
        
        assert "Failed to connect to MT5 after" in str(exc_info.value)
        assert bridge.connection_state == ConnectionState.ERROR
    
    @patch("python_engine.mt5_bridge.mt5")
    def test_connect_with_login_success(self, mock_mt5):
        """Test successful connection with login credentials"""
        # Arrange
        mock_mt5.initialize.return_value = True
        mock_mt5.login.return_value = True
        bridge = MT5Bridge(
            login=12345,
            password="password",
            server="MetaQuotes-Demo",
            offline_mode=False
        )
        
        # Act
        result = bridge.connect()
        
        # Assert
        assert result is True
        mock_mt5.initialize.assert_called_once()
        mock_mt5.login.assert_called_once_with(12345, "password", "MetaQuotes-Demo")
    
    @patch("python_engine.mt5_bridge.mt5")
    def test_connect_login_failure(self, mock_mt5):
        """Test connection failure due to login error"""
        # Arrange
        mock_mt5.initialize.return_value = True
        mock_mt5.login.return_value = False
        mock_mt5.last_error.return_value = (2, "Invalid credentials")
        bridge = MT5Bridge(
            login=12345,
            password="wrong_password",
            server="MetaQuotes-Demo",
            offline_mode=False
        )
        
        # Act & Assert
        with pytest.raises(MT5BridgeException) as exc_info:
            bridge.connect()
        
        assert "Failed to login to MT5" in str(exc_info.value)
        assert bridge.connection_state == ConnectionState.ERROR
    
    def test_connect_offline_mode(self):
        """Test connection in offline mode"""
        # Arrange
        bridge = MT5Bridge(offline_mode=True)
        
        # Act
        result = bridge.connect()
        
        # Assert
        assert result is True
        assert bridge.connection_state == ConnectionState.CONNECTED
        assert bridge.is_connected() is True
    
    @patch("python_engine.mt5_bridge.mt5")
    def test_disconnect(self, mock_mt5):
        """Test disconnection from MT5"""
        # Arrange
        mock_mt5.initialize.return_value = True
        bridge = MT5Bridge(offline_mode=False)
        bridge.connect()
        
        # Act
        bridge.disconnect()
        
        # Assert
        assert bridge.connection_state == ConnectionState.DISCONNECTED
        assert bridge.is_connected() is False
        mock_mt5.shutdown.assert_called_once()
    
    def test_disconnect_offline_mode(self):
        """Test disconnection in offline mode"""
        # Arrange
        bridge = MT5Bridge(offline_mode=True)
        bridge.connect()
        
        # Act
        bridge.disconnect()
        
        # Assert
        assert bridge.connection_state == ConnectionState.DISCONNECTED
        assert bridge.is_connected() is False
    
    def test_mt5bridge_connection_error(monkeypatch):
        class ErrorMT5:
            @staticmethod
            def initialize():
                raise ConnectionError("MT5 not reachable")
        monkeypatch.setattr("python_engine.mt5_bridge.mt5", ErrorMT5)
        bridge = MT5Bridge(offline_mode=False)
        assert not bridge.connected
        assert bridge.last_error == "MT5 not reachable"


class TestMT5BridgeOrderPlacement:
    """Test MT5 Bridge order placement functionality"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.bridge = MT5Bridge(offline_mode=True)
        self.bridge.connect()
    
    def test_place_order_buy_market_success(self):
        """Test successful BUY market order placement"""
        # Act
        result = self.bridge.place_order(
            symbol="EURUSD",
            lot=0.1,
            order_type="BUY",
            comment="Test buy order"
        )
        
        # Assert
        assert result["retcode"] == 10009  # TRADE_RETCODE_DONE
        assert result["ticket"] > 0
        assert result["volume"] == 0.1
        assert result["comment"] == "Test buy order"
    
    def test_place_order_sell_market_success(self):
        """Test successful SELL market order placement"""
        # Act
        result = self.bridge.place_order(
            symbol="EURUSD",
            lot=0.2,
            order_type="SELL",
            sl=1.0950,
            tp=1.0850
        )
        
        # Assert
        assert result["retcode"] == 10009
        assert result["ticket"] > 0
        assert result["volume"] == 0.2
    
    def test_place_order_buy_limit_success(self):
        """Test successful BUY LIMIT order placement"""
        # Act
        result = self.bridge.place_order(
            symbol="EURUSD",
            lot=0.1,
            order_type="BUY_LIMIT",
            price=1.0950,
            sl=1.0900,
            tp=1.1050
        )
        
        # Assert
        assert result["retcode"] == 10009
        assert result["ticket"] > 0
        assert result["volume"] == 0.1
    
    def test_place_order_invalid_symbol(self):
        """Test order placement with invalid symbol"""
        # Act & Assert
        with pytest.raises(MT5BridgeException) as exc_info:
            self.bridge.place_order(
                symbol="INVALID",
                lot=0.1,
                order_type="BUY"
            )
        
        assert "Invalid symbol" in str(exc_info.value)
    
    def test_place_order_invalid_lot_size(self):
        """Test order placement with invalid lot size"""
        # Act & Assert
        with pytest.raises(MT5BridgeException) as exc_info:
            self.bridge.place_order(
                symbol="EURUSD",
                lot=0.0,  # Invalid lot size
                order_type="BUY"
            )
        
        assert "Invalid lot size" in str(exc_info.value)
    
    def test_place_order_invalid_order_type(self):
        """Test order placement with invalid order type"""
        # Act & Assert
        with pytest.raises(MT5BridgeException) as exc_info:
            self.bridge.place_order(
                symbol="EURUSD",
                lot=0.1,
                order_type="INVALID_TYPE"
            )
        
        assert "Invalid order type" in str(exc_info.value)
    
    def test_place_order_pending_without_price(self):
        """Test pending order placement without price"""
        # Act & Assert
        with pytest.raises(MT5BridgeException) as exc_info:
            self.bridge.place_order(
                symbol="EURUSD",
                lot=0.1,
                order_type="BUY_LIMIT"
                # Missing price parameter
            )
        
        assert "Price required for pending orders" in str(exc_info.value)
    
    def test_place_order_invalid_stop_loss(self):
        """Test order placement with invalid stop loss"""
        # Act & Assert
        with pytest.raises(MT5BridgeException) as exc_info:
            self.bridge.place_order(
                symbol="EURUSD",
                lot=0.1,
                order_type="BUY",
                sl=-1.0  # Invalid stop loss
            )
        
        assert "Invalid stop loss" in str(exc_info.value)
    
    def test_place_order_invalid_take_profit(self):
        """Test order placement with invalid take profit"""
        # Act & Assert
        with pytest.raises(MT5BridgeException) as exc_info:
            self.bridge.place_order(
                symbol="EURUSD",
                lot=0.1,
                order_type="BUY",
                tp=0.0  # Invalid take profit
            )
        
        assert "Invalid take profit" in str(exc_info.value)
    
    def test_place_order_not_connected(self):
        """Test order placement when not connected"""
        # Arrange
        bridge = MT5Bridge(offline_mode=True)
        # Don't connect
        
        # Act & Assert
        with pytest.raises(MT5BridgeException) as exc_info:
            bridge.place_order(
                symbol="EURUSD",
                lot=0.1,
                order_type="BUY"
            )
        
        assert "Not connected to MT5" in str(exc_info.value)
    
    @patch("python_engine.mt5_bridge.mt5")
    def test_place_order_live_mode_success(self, mock_mt5):
        """Test successful order placement in live mode"""
        # Arrange
        mock_mt5.initialize.return_value = True
        mock_result = MagicMock()
        mock_result.retcode = 10009
        mock_result.order = 1111
        mock_result.deal = 1111
        mock_result.volume = 0.1
        mock_result.price = 1.1000
        mock_result.bid = 1.0998
        mock_result.ask = 1.1002
        mock_result.comment = "Test order"
        mock_mt5.order_send.return_value = mock_result
        
        bridge = MT5Bridge(offline_mode=False)
        bridge.connect()
        
        # Act
        result = bridge.place_order(
            symbol="EURUSD",
            lot=0.1,
            order_type="BUY"
        )
        
        # Assert
        assert result["retcode"] == 10009
        mock_mt5.order_send.assert_called_once()
    
    @patch("python_engine.mt5_bridge.mt5")
    def test_place_order_live_mode_failure(self, mock_mt5):
        """Test order placement failure in live mode"""
        # Arrange
        mock_mt5.initialize.return_value = True
        mock_mt5.order_send.return_value = None
        mock_mt5.last_error.return_value = (10013, "Invalid request")
        
        bridge = MT5Bridge(offline_mode=False)
        bridge.connect()
        
        # Act & Assert
        with pytest.raises(MT5BridgeException) as exc_info:
            bridge.place_order(
                symbol="EURUSD",
                lot=0.1,
                order_type="BUY"
            )
        
        assert "Order send failed" in str(exc_info.value)
    
    @patch("python_engine.mt5_bridge.mt5")
    def test_place_order_live_mode_rejected(self, mock_mt5):
        """Test order rejection in live mode"""
        # Arrange
        mock_mt5.initialize.return_value = True
        mock_result = MagicMock()
        mock_result.retcode = 10013  # TRADE_RETCODE_INVALID
        mock_mt5.order_send.return_value = mock_result
        
        bridge = MT5Bridge(offline_mode=False)
        bridge.connect()
        
        # Act & Assert
        with pytest.raises(MT5BridgeException) as exc_info:
            bridge.place_order(
                symbol="EURUSD",
                lot=0.1,
                order_type="BUY"
            )
        
        assert "Order rejected with retcode" in str(exc_info.value)


class TestMT5BridgePositionsAndOrders:
    """Test MT5 Bridge positions and orders functionality"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.bridge = MT5Bridge(offline_mode=True)
        self.bridge.connect()
    
    def test_get_positions_empty(self):
        """Test getting positions when none exist"""
        # Act
        positions = self.bridge.get_positions()
        
        # Assert
        assert isinstance(positions, list)
        assert len(positions) == 0
    
    def test_get_positions_with_open_positions(self):
        """Test getting positions after placing orders"""
        # Arrange - Place some orders first
        self.bridge.place_order("EURUSD", 0.1, "BUY")
        self.bridge.place_order("GBPUSD", 0.2, "SELL")
        
        # Act
        positions = self.bridge.get_positions()
        
        # Assert
        assert len(positions) == 2
        assert any(pos["symbol"] == "EURUSD" for pos in positions)
        assert any(pos["symbol"] == "GBPUSD" for pos in positions)
    
    def test_get_orders_empty(self):
        """Test getting orders when none exist"""
        # Act
        orders = self.bridge.get_orders()
        
        # Assert
        assert isinstance(orders, list)
        # In offline mode, market orders are executed immediately
        # so pending orders list might be empty
    
    def test_get_positions_not_connected(self):
        """Test getting positions when not connected"""
        # Arrange
        bridge = MT5Bridge(offline_mode=True)
        # Don't connect
        
        # Act & Assert
        with pytest.raises(MT5BridgeException) as exc_info:
            bridge.get_positions()
        
        assert "Not connected to MT5" in str(exc_info.value)
    
    def test_get_orders_not_connected(self):
        """Test getting orders when not connected"""
        # Arrange
        bridge = MT5Bridge(offline_mode=True)
        # Don't connect
        
        # Act & Assert
        with pytest.raises(MT5BridgeException) as exc_info:
            bridge.get_orders()
        
        assert "Not connected to MT5" in str(exc_info.value)
    
    @patch("python_engine.mt5_bridge.mt5")
    def test_get_positions_live_mode(self, mock_mt5):
        """Test getting positions in live mode"""
        # Arrange
        mock_mt5.initialize.return_value = True
        mock_positions = [MagicMock(), MagicMock()]
        mock_mt5.positions_get.return_value = mock_positions
        
        bridge = MT5Bridge(offline_mode=False)
        bridge.connect()
        
        # Act
        positions = bridge.get_positions()
        
        # Assert
        mock_mt5.positions_get.assert_called_once()
        assert isinstance(positions, list)
    
    @patch("python_engine.mt5_bridge.mt5")
    def test_get_positions_live_mode_error(self, mock_mt5):
        """Test getting positions error in live mode"""
        # Arrange
        mock_mt5.initialize.return_value = True
        mock_mt5.positions_get.return_value = None
        mock_mt5.last_error.return_value = (1, "Error getting positions")
        
        bridge = MT5Bridge(offline_mode=False)
        bridge.connect()
        
        # Act
        positions = bridge.get_positions()
        
        # Assert
        assert positions == []


class TestMT5BridgeSymbolInfo:
    """Test MT5 Bridge symbol information functionality"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.bridge = MT5Bridge(offline_mode=True)
        self.bridge.connect()
    
    def test_get_symbol_info_valid_symbol(self):
        """Test getting symbol info for valid symbol"""
        # Act
        symbol_info = self.bridge.get_symbol_info("EURUSD")
        
        # Assert
        assert symbol_info is not None
        assert symbol_info["name"] == "EURUSD"
        assert "bid" in symbol_info
        assert "ask" in symbol_info
        assert "spread" in symbol_info
        assert "digits" in symbol_info
    
    def test_get_symbol_info_invalid_symbol(self):
        """Test getting symbol info for invalid symbol"""
        # Act
        symbol_info = self.bridge.get_symbol_info("INVALID")
        
        # Assert
        assert symbol_info is None
    
    def test_get_symbol_info_not_connected(self):
        """Test getting symbol info when not connected"""
        # Arrange
        bridge = MT5Bridge(offline_mode=True)
        # Don't connect
        
        # Act & Assert
        with pytest.raises(MT5BridgeException) as exc_info:
            bridge.get_symbol_info("EURUSD")
        
        assert "Not connected to MT5" in str(exc_info.value)
    
    @patch("python_engine.mt5_bridge.mt5")
    def test_get_symbol_info_live_mode(self, mock_mt5):
        """Test getting symbol info in live mode"""
        # Arrange
        mock_mt5.initialize.return_value = True
        mock_symbol_info = MagicMock()
        mock_mt5.symbol_info.return_value = mock_symbol_info
        
        bridge = MT5Bridge(offline_mode=False)
        bridge.connect()
        
        # Act
        symbol_info = bridge.get_symbol_info("EURUSD")
        
        # Assert
        mock_mt5.symbol_info.assert_called_once_with("EURUSD")


class TestMT5BridgePositionManagement:
    """Test MT5 Bridge position management functionality"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.bridge = MT5Bridge(offline_mode=True)
        self.bridge.connect()
    
    def test_close_position_success(self):
        """Test successful position closing"""
        # Arrange - Place an order first
        order_result = self.bridge.place_order("EURUSD", 0.1, "BUY")
        ticket = order_result["ticket"]
        
        # Act
        close_result = self.bridge.close_position(ticket)
        
        # Assert
        assert close_result["retcode"] == 10009
        assert close_result["ticket"] > 0
        
        # Verify position is closed
        positions = self.bridge.get_positions()
        assert not any(pos["ticket"] == ticket for pos in positions)
    
    def test_close_position_not_found(self):
        """Test closing non-existent position"""
        # Act & Assert
        with pytest.raises(MT5BridgeException) as exc_info:
            self.bridge.close_position(99999)  # Non-existent ticket
        
        assert "Position 99999 not found" in str(exc_info.value)
    
    def test_close_position_not_connected(self):
        """Test closing position when not connected"""
        # Arrange
        bridge = MT5Bridge(offline_mode=True)
        # Don't connect
        
        # Act & Assert
        with pytest.raises(MT5BridgeException) as exc_info:
            bridge.close_position(1000)
        
        assert "Not connected to MT5" in str(exc_info.value)


class TestMT5BridgeAuditAndLogging:
    """Test MT5 Bridge audit trail and logging functionality"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.bridge = MT5Bridge(offline_mode=True)
        self.bridge.connect()
    
    def test_operation_log_connection(self):
        """Test operation logging for connection"""
        # Act
        log = self.bridge.get_operation_log()
        
        # Assert
        assert len(log) > 0
        connect_log = next((entry for entry in log if entry["operation"] == "connected"), None)
        assert connect_log is not None
        assert connect_log["details"]["mode"] == "offline"
    
    def test_operation_log_order_placement(self):
        """Test operation logging for order placement"""
        # Act
        self.bridge.place_order("EURUSD", 0.1, "BUY", comment="Test order")
        log = self.bridge.get_operation_log()
        
        # Assert
        order_log = next((entry for entry in log if entry["operation"] == "order_placed_offline"), None)
        assert order_log is not None
        assert order_log["details"]["request"]["symbol"] == "EURUSD"
        assert order_log["details"]["request"]["volume"] == 0.1
    
    def test_operation_log_position_closing(self):
        """Test operation logging for position closing"""
        # Arrange
        order_result = self.bridge.place_order("EURUSD", 0.1, "BUY")
        ticket = order_result["ticket"]
        
        # Act
        self.bridge.close_position(ticket)
        log = self.bridge.get_operation_log()
        
        # Assert
        close_log = next((entry for entry in log if entry["operation"] == "position_closed_offline"), None)
        assert close_log is not None
        assert close_log["details"]["ticket"] == ticket
    
    def test_get_connection_info(self):
        """Test getting connection information"""
        # Act
        info = self.bridge.get_connection_info()
        
        # Assert
        assert info["state"] == "connected"
        assert info["offline_mode"] is True
        assert "connection_attempts" in info
        assert "last_error" in info
        assert "mt5_available" in info


class TestMT5BridgeEdgeCases:
    """Test MT5 Bridge edge cases and error conditions"""
    
    def test_multiple_connections(self):
        """Test multiple connection attempts"""
        # Arrange
        bridge = MT5Bridge(offline_mode=True)
        
        # Act
        result1 = bridge.connect()
        result2 = bridge.connect()  # Second connection attempt
        
        # Assert
        assert result1 is True
        assert result2 is True  # Should handle gracefully
        assert bridge.is_connected() is True
    
    def test_disconnect_without_connection(self):
        """Test disconnecting without being connected"""
        # Arrange
        bridge = MT5Bridge(offline_mode=True)
        
        # Act
        bridge.disconnect()  # Disconnect without connecting
        
        # Assert
        assert bridge.connection_state == ConnectionState.DISCONNECTED
    
    def test_large_order_volume(self):
        """Test placing order with large volume"""
        # Arrange
        bridge = MT5Bridge(offline_mode=True)
        bridge.connect()
        
        # Act
        result = bridge.place_order("EURUSD", 10.0, "BUY")  # Large volume
        
        # Assert
        assert result["retcode"] == 10009
        assert result["volume"] == 10.0
    
    def test_order_with_all_parameters(self):
        """Test placing order with all possible parameters"""
        # Arrange
        bridge = MT5Bridge(offline_mode=True)
        bridge.connect()
        
        # Act
        result = bridge.place_order(
            symbol="EURUSD",
            lot=0.1,
            order_type="BUY_LIMIT",
            price=1.0950,
            sl=1.0900,
            tp=1.1050,
            comment="Full parameter test",
            magic=12345
        )
        
        # Assert
        assert result["retcode"] == 10009
        assert result["volume"] == 0.1
        assert result["comment"] == "Full parameter test"
    
    def test_concurrent_operations(self):
        """Test concurrent operations (simplified)"""
        # Arrange
        bridge = MT5Bridge(offline_mode=True)
        bridge.connect()
        
        # Act - Simulate concurrent operations
        results = []
        for i in range(5):
            result = bridge.place_order("EURUSD", 0.1, "BUY", comment=f"Order {i}")
            results.append(result)
        
        # Assert
        assert len(results) == 5
        assert all(result["retcode"] == 10009 for result in results)
        
        # All orders should have unique tickets
        tickets = [result["ticket"] for result in results]
        assert len(set(tickets)) == 5  # All unique
    
    def test_log_size_management(self):
        """Test operation log size management"""
        # Arrange
        bridge = MT5Bridge(offline_mode=True)
        bridge.connect()
        
        # Act - Generate many log entries
        for i in range(1200):  # More than the 1000 limit
            bridge._log_operation(f"test_operation_{i}", {"data": i})
        
        # Assert
        log = bridge.get_operation_log()
        assert len(log) <= 500  # Should be trimmed to 500


# Integration Tests
class TestMT5BridgeIntegration:
    """Integration tests for MT5 Bridge"""
    
    def test_complete_trading_workflow_offline(self):
        """Test complete trading workflow in offline mode"""
        # Arrange
        bridge = MT5Bridge(offline_mode=True)
        
        # Act & Assert - Complete workflow
        
        # 1. Connect
        assert bridge.connect() is True
        assert bridge.is_connected() is True
        
        # 2. Get symbol info
        symbol_info = bridge.get_symbol_info("EURUSD")
        assert symbol_info is not None
        
        # 3. Place buy order
        buy_result = bridge.place_order("EURUSD", 0.1, "BUY", sl=1.0900, tp=1.1100)
        assert buy_result["retcode"] == 10009
        buy_ticket = buy_result["ticket"]
        
        # 4. Place sell order
        sell_result = bridge.place_order("GBPUSD", 0.2, "SELL", sl=1.2600, tp=1.2400)
        assert sell_result["retcode"] == 10009
        sell_ticket = sell_result["ticket"]
        
        # 5. Check positions
        positions = bridge.get_positions()
        assert len(positions) == 2
        
        # 6. Close positions
        close_buy_result = bridge.close_position(buy_ticket)
        assert close_buy_result["retcode"] == 10009
        
        close_sell_result = bridge.close_position(sell_ticket)
        assert close_sell_result["retcode"] == 10009
        
        # 7. Verify positions closed
        positions_after = bridge.get_positions()
        assert len(positions_after) == 0
        
        # 8. Check audit trail
        log = bridge.get_operation_log()
        assert len(log) > 0
        
        # 9. Disconnect
        bridge.disconnect()
        assert bridge.is_connected() is False
    
    def test_error_recovery_workflow(self):
        """Test error recovery workflow"""
        # Arrange
        bridge = MT5Bridge(offline_mode=True)
        bridge.connect()
        
        # Act & Assert - Error scenarios
        
        # 1. Try invalid operations
        with pytest.raises(MT5BridgeException):
            bridge.place_order("INVALID", 0.1, "BUY")
        
        with pytest.raises(MT5BridgeException):
            bridge.place_order("EURUSD", -0.1, "BUY")
        
        with pytest.raises(MT5BridgeException):
            bridge.close_position(99999)
        
        # 2. Verify bridge still works after errors
        result = bridge.place_order("EURUSD", 0.1, "BUY")
        assert result["retcode"] == 10009
        
        # 3. Check error logging
        log = bridge.get_operation_log()
        error_logs = [entry for entry in log if "error" in entry["operation"]]
        assert len(error_logs) > 0


# Property-based testing examples
class TestMT5BridgeProperties:
    """Property-based tests for MT5 Bridge"""
    
    @pytest.mark.parametrize("symbol", ["EURUSD", "GBPUSD", "USDJPY", "USDCHF", "AUDUSD"])
    def test_valid_symbols_work(self, symbol):
        """Test that all valid symbols work"""
        # Arrange
        bridge = MT5Bridge(offline_mode=True)
        bridge.connect()
        
        # Act
        result = bridge.place_order(symbol, 0.1, "BUY")
        
        # Assert
        assert result["retcode"] == 10009
        assert result["volume"] == 0.1
    
    @pytest.mark.parametrize("lot_size", [0.01, 0.1, 0.5, 1.0, 2.0, 10.0])
    def test_valid_lot_sizes_work(self, lot_size):
        """Test that valid lot sizes work"""
        # Arrange
        bridge = MT5Bridge(offline_mode=True)
        bridge.connect()
        
        # Act
        result = bridge.place_order("EURUSD", lot_size, "BUY")
        
        # Assert
        assert result["retcode"] == 10009
        assert result["volume"] == lot_size
    
    @pytest.mark.parametrize("order_type", ["BUY", "SELL"])
    def test_market_order_types_work(self, order_type):
        """Test that market order types work"""
        # Arrange
        bridge = MT5Bridge(offline_mode=True)
        bridge.connect()
        
        # Act
        result = bridge.place_order("EURUSD", 0.1, order_type)
        
        # Assert
        assert result["retcode"] == 10009
        assert result["volume"] == 0.1
    
    @pytest.mark.parametrize("invalid_lot", [-0.1, 0.0, -1.0])
    def test_invalid_lot_sizes_fail(self, invalid_lot):
        """Test that invalid lot sizes fail"""
        # Arrange
        bridge = MT5Bridge(offline_mode=True)
        bridge.connect()
        
        # Act & Assert
        with pytest.raises(MT5BridgeException):
            bridge.place_order("EURUSD", invalid_lot, "BUY")


# Performance tests
class TestMT5BridgePerformance:
    """Performance tests for MT5 Bridge"""
    
    def test_order_placement_performance(self):
        """Test order placement performance"""
        import time
        
        # Arrange
        bridge = MT5Bridge(offline_mode=True)
        bridge.connect()
        
        # Act
        start_time = time.time()
        for i in range(100):
            bridge.place_order("EURUSD", 0.01, "BUY", comment=f"Perf test {i}")
        end_time = time.time()
        
        # Assert
        execution_time = end_time - start_time
        assert execution_time < 5.0  # Should complete in less than 5 seconds
        
        # Verify all orders were placed
        positions = bridge.get_positions()
        assert len(positions) == 100
    
    def test_position_retrieval_performance(self):
        """Test position retrieval performance"""
        import time
        
        # Arrange
        bridge = MT5Bridge(offline_mode=True)
        bridge.connect()
        
        # Place some positions
        for i in range(50):
            bridge.place_order("EURUSD", 0.01, "BUY")
        
        # Act
        start_time = time.time()
        for _ in range(100):
            positions = bridge.get_positions()
        end_time = time.time()
        
        # Assert
        execution_time = end_time - start_time
        assert execution_time < 2.0  # Should complete in less than 2 seconds
        assert len(positions) == 50


def test_submit_order_invalid(monkeypatch):
    bridge = MT5Bridge(offline_mode=True)
    order = OrderRequest(symbol="", volume=-1, order_type="buy")
    result = bridge.submit_order(order)
    assert not result.success
    assert "Invalid parameters" in result.error_message


def test_error_object_returned():
    bridge = MT5Bridge(offline_mode=True)
    order = OrderRequest(symbol="", volume=-1, order_type="buy")
    result = bridge.submit_order(order)
    assert hasattr(result, "error_code")
    assert hasattr(result, "error_message")


if __name__ == "__main__":
    # Run specific test classes or all tests
    pytest.main([__file__, "-v", "--tb=short"])