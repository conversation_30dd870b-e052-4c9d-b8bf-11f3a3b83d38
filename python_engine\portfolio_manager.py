# python_engine/portfolio_manager.py
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json

logger = logging.getLogger(__name__)

class PortfolioManagerException(Exception):
    """Custom exception for portfolio management errors"""
    pass

class TradeStatus(Enum):
    PENDING = "pending"
    EXECUTED = "executed"
    CANCELLED = "cancelled"
    FAILED = "failed"

@dataclass
class Trade:
    id: str
    symbol: str
    volume: float
    order_type: str  # BUY, SELL
    entry_price: Optional[float] = None
    exit_price: Optional[float] = None
    timestamp: datetime = field(default_factory=datetime.now)
    status: TradeStatus = TradeStatus.PENDING
    profit_loss: float = 0.0
    commission: float = 0.0
    swap: float = 0.0
    comment: str = ""

@dataclass
class PortfolioMetrics:
    total_value: float
    total_pnl: float
    realized_pnl: float
    unrealized_pnl: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    average_win: float
    average_loss: float
    max_drawdown: float
    sharpe_ratio: float
    profit_factor: float

@dataclass
class PortfolioPosition:
    symbol: str
    volume: float
    average_price: float
    current_price: float
    unrealized_pnl: float
    trades: List[Trade] = field(default_factory=list)

class PortfolioManager:
    """Portfolio management system with comprehensive tracking"""
    
    def __init__(self, initial_balance: float = 10000.0, offline_mode: bool = True):
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        self.offline_mode = offline_mode
        self.positions: Dict[str, PortfolioPosition] = {}
        self.trade_history: List[Trade] = []
        self.daily_balances: List[Tuple[datetime, float]] = []
        self.logger = logger
        
        # Mock market data for offline mode
        self.mock_prices = {
            "EURUSD": 1.0850,
            "GBPUSD": 1.2650,
            "USDJPY": 149.50,
            "USDCHF": 0.8950,
            "AUDUSD": 0.6750,
            "NZDUSD": 0.6150,
            "USDCAD": 1.3650,
            "EURJPY": 162.25
        }
        
        # Commission structure
        self.commission_per_lot = 7.0  # $7 per standard lot
        
        self.logger.info(f"PortfolioManager initialized - Balance: {initial_balance}, Offline: {offline_mode}")
    
    def add_trade(self, symbol: str, volume: float, order_type: str, 
                  entry_price: Optional[float] = None, comment: str = "") -> Trade:
        """Add a new trade to the portfolio"""
        try:
            # Generate trade ID
            trade_id = f"T{len(self.trade_history) + 1:06d}"
            
            # Use mock price if not provided and in offline mode
            if entry_price is None and self.offline_mode:
                entry_price = self.mock_prices.get(symbol, 1.0)
            
            # Calculate commission
            commission = abs(volume) * self.commission_per_lot
            
            # Create trade
            trade = Trade(
                id=trade_id,
                symbol=symbol,
                volume=volume,
                order_type=order_type.upper(),
                entry_price=entry_price,
                timestamp=datetime.now(),
                status=TradeStatus.EXECUTED,
                commission=commission,
                comment=comment
            )
            
            # Add to trade history
            self.trade_history.append(trade)
            
            # Update position
            self._update_position(trade)
            
            # Update balance for commission
            self.current_balance -= commission
            
            self.logger.info(f"Trade added: {trade_id} {symbol} {volume} {order_type} @ {entry_price}")
            return trade
            
        except Exception as e:
            self.logger.error(f"Error adding trade: {str(e)}")
            raise PortfolioManagerException(f"Failed to add trade: {str(e)}")
    
    def close_trade(self, trade_id: str, exit_price: Optional[float] = None) -> bool:
        """Close a specific trade"""
        try:
            # Find the trade
            trade = next((t for t in self.trade_history if t.id == trade_id), None)
            if not trade:
                self.logger.warning(f"Trade not found: {trade_id}")
                return False
            
            if trade.status != TradeStatus.EXECUTED:
                self.logger.warning(f"Trade {trade_id} is not in executed status")
                return False
            
            # Use mock price if not provided and in offline mode
            if exit_price is None and self.offline_mode:
                exit_price = self.mock_prices.get(trade.symbol, trade.entry_price)
            
            # Calculate P&L
            if trade.order_type == "BUY":
                pnl = (exit_price - trade.entry_price) * trade.volume * 100000
            else:  # SELL
                pnl = (trade.entry_price - exit_price) * trade.volume * 100000
            
            # Update trade
            trade.exit_price = exit_price
            trade.profit_loss = pnl
            trade.status = TradeStatus.CANCELLED  # Mark as closed
            
            # Calculate commission for closing
            close_commission = abs(trade.volume) * self.commission_per_lot
            trade.commission += close_commission
            
            # Update balance
            self.current_balance += pnl - close_commission
            
            # Update position
            self._close_position_trade(trade)
            
            self.logger.info(f"Trade closed: {trade_id} P&L: {pnl:.2f}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error closing trade: {str(e)}")
            raise PortfolioManagerException(f"Failed to close trade: {str(e)}")
    
    def close_position(self, symbol: str) -> bool:
        """Close entire position for a symbol"""
        try:
            if symbol not in self.positions:
                self.logger.warning(f"No position found for {symbol}")
                return False
            
            position = self.positions[symbol]
            
            # Close all open trades for this symbol
            open_trades = [t for t in self.trade_history 
                          if t.symbol == symbol and t.status == TradeStatus.EXECUTED]
            
            success_count = 0
            for trade in open_trades:
                if self.close_trade(trade.id):
                    success_count += 1
            
            # Remove position if all trades closed
            if success_count == len(open_trades):
                del self.positions[symbol]
                self.logger.info(f"Position closed: {symbol}")
                return True
            else:
                self.logger.warning(f"Only {success_count}/{len(open_trades)} trades closed for {symbol}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error closing position: {str(e)}")
            raise PortfolioManagerException(f"Failed to close position: {str(e)}")
    
    def _update_position(self, trade: Trade) -> None:
        """Update position based on new trade"""
        symbol = trade.symbol
        
        if symbol not in self.positions:
            # Create new position
            self.positions[symbol] = PortfolioPosition(
                symbol=symbol,
                volume=0.0,
                average_price=0.0,
                current_price=trade.entry_price,
                unrealized_pnl=0.0,
                trades=[]
            )
        
        position = self.positions[symbol]
        position.trades.append(trade)
        
        # Calculate new average price and volume
        if trade.order_type == "BUY":
            new_volume = position.volume + trade.volume
        else:  # SELL
            new_volume = position.volume - trade.volume
        
        if new_volume != 0:
            # Update average price
            if (position.volume > 0 and trade.order_type == "BUY") or \
               (position.volume < 0 and trade.order_type == "SELL"):
                # Adding to existing position
                total_cost = (position.average_price * abs(position.volume) + 
                             trade.entry_price * trade.volume)
                total_volume = abs(position.volume) + trade.volume
                position.average_price = total_cost / total_volume
            else:
                # Reducing or reversing position
                position.average_price = trade.entry_price
        
        position.volume = new_volume
        position.current_price = trade.entry_price
        
        # Remove position if volume is zero
        if abs(position.volume) < 0.001:  # Essentially zero
            del self.positions[symbol]
    
    def _close_position_trade(self, trade: Trade) -> None:
        """Update position when a trade is closed"""
        symbol = trade.symbol
        
        if symbol in self.positions:
            position = self.positions[symbol]
            
            # Remove trade from position
            position.trades = [t for t in position.trades if t.id != trade.id]
            
            # Recalculate position if there are remaining trades
            if position.trades:
                total_volume = 0.0
                weighted_price = 0.0
                total_weight = 0.0
                
                for t in position.trades:
                    if t.status == TradeStatus.EXECUTED:
                        if t.order_type == "BUY":
                            total_volume += t.volume
                        else:
                            total_volume -= t.volume
                        
                        weight = abs(t.volume)
                        weighted_price += t.entry_price * weight
                        total_weight += weight
                
                position.volume = total_volume
                if total_weight > 0:
                    position.average_price = weighted_price / total_weight
            else:
                # No more trades, remove position
                del self.positions[symbol]
    
    def update_prices(self, price_updates: Dict[str, float]) -> None:
        """Update current prices and recalculate unrealized P&L"""
        try:
            # Update mock prices
            self.mock_prices.update(price_updates)
            
            # Update positions
            for symbol, position in self.positions.items():
                if symbol in price_updates:
                    position.current_price = price_updates[symbol]
                    
                    # Calculate unrealized P&L
                    if position.volume > 0:  # Long position
                        position.unrealized_pnl = (position.current_price - position.average_price) * position.volume * 100000
                    elif position.volume < 0:  # Short position
                        position.unrealized_pnl = (position.average_price - position.current_price) * abs(position.volume) * 100000
                    else:
                        position.unrealized_pnl = 0.0
            
            self.logger.debug(f"Prices updated for {len(price_updates)} symbols")
            
        except Exception as e:
            self.logger.error(f"Error updating prices: {str(e)}")
            raise PortfolioManagerException(f"Failed to update prices: {str(e)}")
    
    def get_portfolio_metrics(self) -> PortfolioMetrics:
        """Calculate comprehensive portfolio metrics"""
        try:
            # Calculate realized P&L from closed trades
            closed_trades = [t for t in self.trade_history if t.status == TradeStatus.CANCELLED]
            realized_pnl = sum(t.profit_loss for t in closed_trades)
            
            # Calculate unrealized P&L from open positions
            unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
            
            # Total P&L
            total_pnl = realized_pnl + unrealized_pnl
            
            # Total portfolio value
            total_value = self.current_balance + unrealized_pnl
            
            # Trade statistics
            total_trades = len(closed_trades)
            winning_trades = len([t for t in closed_trades if t.profit_loss > 0])
            losing_trades = len([t for t in closed_trades if t.profit_loss < 0])
            
            # Win rate
            win_rate = winning_trades / total_trades if total_trades > 0 else 0.0
            
            # Average win/loss
            wins = [t.profit_loss for t in closed_trades if t.profit_loss > 0]
            losses = [t.profit_loss for t in closed_trades if t.profit_loss < 0]
            
            average_win = sum(wins) / len(wins) if wins else 0.0
            average_loss = sum(losses) / len(losses) if losses else 0.0
            
            # Max drawdown
            max_drawdown = self._calculate_max_drawdown()
            
            # Sharpe ratio
            sharpe_ratio = self._calculate_sharpe_ratio()
            
            # Profit factor
            total_wins = sum(wins) if wins else 0.0
            total_losses = abs(sum(losses)) if losses else 0.0
            profit_factor = total_wins / total_losses if total_losses > 0 else float('inf')
            
            return PortfolioMetrics(
                total_value=total_value,
                total_pnl=total_pnl,
                realized_pnl=realized_pnl,
                unrealized_pnl=unrealized_pnl,
                total_trades=total_trades,
                winning_trades=winning_trades,
                losing_trades=losing_trades,
                win_rate=win_rate,
                average_win=average_win,
                average_loss=average_loss,
                max_drawdown=max_drawdown,
                sharpe_ratio=sharpe_ratio,
                profit_factor=profit_factor
            )
            
        except Exception as e:
            self.logger.error(f"Error calculating portfolio metrics: {str(e)}")
            raise PortfolioManagerException(f"Failed to calculate metrics: {str(e)}")
    
    def _calculate_max_drawdown(self) -> float:
        """Calculate maximum drawdown"""
        if not self.daily_balances:
            return 0.0
        
        balances = [balance for _, balance in self.daily_balances]
        max_drawdown = 0.0
        peak = balances[0]
        
        for balance in balances:
            if balance > peak:
                peak = balance
            drawdown = peak - balance
            if drawdown > max_drawdown:
                max_drawdown = drawdown
        
        return max_drawdown
    
    def _calculate_sharpe_ratio(self) -> float:
        """Calculate Sharpe ratio"""
        if len(self.daily_balances) < 2:
            return 0.0
        
        # Calculate daily returns
        returns = []
        for i in range(1, len(self.daily_balances)):
            prev_balance = self.daily_balances[i-1][1]
            curr_balance = self.daily_balances[i][1]
            daily_return = (curr_balance - prev_balance) / prev_balance
            returns.append(daily_return)
        
        if not returns:
            return 0.0
        
        # Calculate mean and standard deviation
        mean_return = sum(returns) / len(returns)
        variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
        std_dev = variance ** 0.5
        
        # Annualized Sharpe ratio (assuming 252 trading days)
        if std_dev > 0:
            return (mean_return * 252) / (std_dev * (252 ** 0.5))
        else:
            return 0.0
    
    def get_position_summary(self) -> Dict:
        """Get summary of all positions"""
        try:
            summary = {
                "total_positions": len(self.positions),
                "positions": {},
                "total_unrealized_pnl": 0.0,
                "total_volume": 0.0
            }
            
            for symbol, position in self.positions.items():
                summary["positions"][symbol] = {
                    "volume": position.volume,
                    "average_price": position.average_price,
                    "current_price": position.current_price,
                    "unrealized_pnl": position.unrealized_pnl,
                    "open_trades": len([t for t in position.trades if t.status == TradeStatus.EXECUTED])
                }
                
                summary["total_unrealized_pnl"] += position.unrealized_pnl
                summary["total_volume"] += abs(position.volume)
            
            return summary
            
        except Exception as e:
            self.logger.error(f"Error getting position summary: {str(e)}")
            return {}
    
    def get_trade_history(self, symbol: Optional[str] = None, 
                         limit: Optional[int] = None) -> List[Trade]:
        """Get trade history with optional filtering"""
        try:
            trades = self.trade_history
            
            # Filter by symbol if specified
            if symbol:
                trades = [t for t in trades if t.symbol == symbol]
            
            # Sort by timestamp (most recent first)
            trades = sorted(trades, key=lambda t: t.timestamp, reverse=True)
            
            # Apply limit if specified
            if limit:
                trades = trades[:limit]
            
            return trades
            
        except Exception as e:
            self.logger.error(f"Error getting trade history: {str(e)}")
            return []
    
    def record_daily_balance(self) -> None:
        """Record current balance for daily tracking"""
        try:
            today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            
            # Check if we already have a record for today
            if self.daily_balances and self.daily_balances[-1][0].date() == today.date():
                # Update today's balance
                self.daily_balances[-1] = (today, self.current_balance)
            else:
                # Add new daily balance record
                self.daily_balances.append((today, self.current_balance))
            
            # Keep only last 365 days
            if len(self.daily_balances) > 365:
                self.daily_balances = self.daily_balances[-365:]
            
            self.logger.debug(f"Daily balance recorded: {self.current_balance}")
            
        except Exception as e:
            self.logger.error(f"Error recording daily balance: {str(e)}")
    
    def export_portfolio_data(self) -> Dict:
        """Export portfolio data for backup/analysis"""
        try:
            return {
                "initial_balance": self.initial_balance,
                "current_balance": self.current_balance,
                "positions": {
                    symbol: {
                        "volume": pos.volume,
                        "average_price": pos.average_price,
                        "current_price": pos.current_price,
                        "unrealized_pnl": pos.unrealized_pnl
                    }
                    for symbol, pos in self.positions.items()
                },
                "trade_history": [
                    {
                        "id": t.id,
                        "symbol": t.symbol,
                        "volume": t.volume,
                        "order_type": t.order_type,
                        "entry_price": t.entry_price,
                        "exit_price": t.exit_price,
                        "timestamp": t.timestamp.isoformat(),
                        "status": t.status.value,
                        "profit_loss": t.profit_loss,
                        "commission": t.commission,
                        "comment": t.comment
                    }
                    for t in self.trade_history
                ],
                "daily_balances": [
                    {
                        "date": date.isoformat(),
                        "balance": balance
                    }
                    for date, balance in self.daily_balances
                ]
            }
            
        except Exception as e:
            self.logger.error(f"Error exporting portfolio data: {str(e)}")
            return {}
    
    def reset_portfolio(self) -> None:
        """Reset portfolio to initial state"""
        try:
            self.current_balance = self.initial_balance
            self.positions.clear()
            self.trade_history.clear()
            self.daily_balances.clear()
            
            self.logger.info("Portfolio reset to initial state")
            
        except Exception as e:
            self.logger.error(f"Error resetting portfolio: {str(e)}")
            raise PortfolioManagerException(f"Failed to reset portfolio: {str(e)}")