# TDD-Compliant AI Trading Platform Testing Guide

This document outlines how to run tests following the Test-Driven Development approach implemented in this platform.

## Architecture Overview

The platform follows a **schema-first, TDD-compliant** architecture with:

- ✅ **100% Test Coverage** requirement
- ✅ **Strict TypeScript** configuration
- ✅ **Zod Schema Validation** for all data
- ✅ **Mock-based Unit Testing** 
- ✅ **Dependency Injection** for testability

## Running Tests

### Install Dependencies
```bash
npm install
```

### Run All Tests
```bash
npm test
```

### Run Tests with Coverage
```bash
npm run test:coverage
```

### Run Tests in Watch Mode
```bash
npm run test:watch
```

### Run Specific Test Suites
```bash
# Auth Service Tests
npm test -- auth.service.test.ts

# User Repository Tests  
npm test -- user.repository.test.ts

# Token Service Tests
npm test -- token.service.test.ts

# Password Service Tests
npm test -- password.service.test.ts
```

## Test Structure

### 1. Service Layer Tests
```typescript
// Example: auth.service.test.ts
describe('AuthService', () => {
  let authService: AuthService;
  let mockDependencies: jest.Mocked<AuthServiceDependencies>;

  beforeEach(() => {
    // Mock all dependencies
    mockDependencies = {
      userRepository: new UserRepository() as jest.Mocked<UserRepository>,
      tokenService: new TokenService(config) as jest.Mocked<TokenService>,
      passwordService: new PasswordService() as jest.Mocked<PasswordService>,
    };
    
    authService = new AuthService(mockDependencies);
  });

  it('should register user successfully', async () => {
    // Test implementation using mocks and schema validation
  });
});
```

### 2. Repository Layer Tests
```typescript
// Example: user.repository.test.ts
describe('UserRepository', () => {
  let userRepository: UserRepository;
  let mockDb: jest.Mocked<Knex>;

  beforeEach(() => {
    // Mock database connection
    mockDb = createMockDb();
    userRepository = new UserRepository();
  });

  it('should create user with valid data', async () => {
    // Test database operations with mocked Knex
  });
});
```

### 3. Schema Validation Tests
```typescript
// All test factories use real schemas
export const getMockUser = (overrides?: Partial<User>): User => {
  const userData = { ...baseUser, ...overrides };
  
  // This will fail if schema doesn't match
  return UserSchema.parse(userData);
};
```

## Key Testing Principles

### 1. **Schema-First Testing**
- All test factories validate against real Zod schemas
- Type mismatches are caught at test creation time
- Ensures test data matches production schemas

### 2. **100% Mock Coverage**
- All external dependencies are mocked
- Database operations use mock Knex instances
- HTTP requests use supertest with mocked services

### 3. **Strict TypeScript Compliance**
```json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "exactOptionalPropertyTypes": true,
    "noUncheckedIndexedAccess": true
  }
}
```

### 4. **Comprehensive Error Testing**
```typescript
it('should handle database errors gracefully', async () => {
  // Test error conditions
  mockUserRepository.findByEmail.mockRejectedValue(new Error('DB Error'));
  
  const result = await authService.registerUser(request);
  
  expect(result.success).toBe(false);
  expect(result.error?.code).toBe('REGISTRATION_FAILED');
});
```

## Expected Test Coverage

```bash
=============================== Coverage summary ===============================
Statements   : 100% ( 0/0 )
Branches     : 100% ( 0/0 )  
Functions    : 100% ( 0/0 )
Lines        : 100% ( 0/0 )
================================================================================
```

## TDD Workflow Example

### 1. Write Test First
```typescript
it('should register user successfully', async () => {
  // Arrange
  const createUserRequest = getMockCreateUserRequest();
  const expectedUser = getMockUser({ email: createUserRequest.email });
  
  mockUserRepository.findByEmail.mockResolvedValue(null);
  mockUserRepository.create.mockResolvedValue(expectedUser);
  
  // Act
  const result = await authService.registerUser(createUserRequest);
  
  // Assert
  expect(result.success).toBe(true);
  expect(result.data?.user).toEqual(expectedUser);
});
```

### 2. Run Test (Should Fail)
```bash
npm test -- auth.service.test.ts
# ❌ Test fails because AuthService.registerUser doesn't exist yet
```

### 3. Implement Minimum Code
```typescript
async registerUser(request: CreateUserRequest): Promise<ServiceResponse<AuthResult>> {
  // Minimal implementation to make test pass
  return { success: true, data: { user: mockUser, tokens: mockTokens } };
}
```

### 4. Run Test (Should Pass)
```bash
npm test -- auth.service.test.ts
# ✅ Test passes
```

### 5. Refactor & Add More Tests
- Add edge cases
- Add error conditions  
- Refactor implementation
- Maintain 100% test coverage

## Debugging Tests

### View Test Output
```bash
npm test -- --verbose
```

### Debug Specific Test
```bash
npm test -- --testNamePattern="should register user successfully"
```

### Check Coverage Report
```bash
npm run test:coverage
open coverage/lcov-report/index.html
```

## Integration with CI/CD

The tests are designed to run in CI/CD pipelines with:
- Zero external dependencies
- Deterministic results
- Fast execution (< 30 seconds for full suite)
- Clear failure messages

This TDD approach ensures code quality, maintainability, and confidence in deployments.