// tests/integration/complete-platform.test.ts
import { describe, it, expect, beforeEach } from '@jest/globals';
import { ForexDataProvider } from '../../src/data-management/forex-data-provider';
import { ZeroHallucinationChatbot } from '../../src/ai/zero-hallucination-chatbot';
import { DarwinGodelMachine } from '../../src/ai/darwin-godel-machine';

// Integration Tests for Complete Platform
describe('Complete Trading Platform Integration - TDD', () => {
  let dataProvider: ForexDataProvider;
  let chatbot: ZeroHallucinationChatbot;
  let darwin: DarwinGodelMachine;

  beforeEach(async () => {
    dataProvider = new ForexDataProvider('./test-integration-data');
    await dataProvider.initialize();
    
    chatbot = new ZeroHallucinationChatbot(dataProvider);
    darwin = new DarwinGodelMachine(dataProvider);
  });

  describe('End-to-End User Workflow', () => {
    it('should complete full trading strategy development workflow', async () => {
      // Step 1: User asks about available data
      const dataQuery = await chatbot.processQuery("What historical data is available?");
      
      expect(dataQuery.intent).toBe('data_query');
      expect(dataQuery.verificationStatus.noHallucination).toBe(true);
      
      // Step 2: Load some test data
      const testData = {
        pair: 'EUR/USD',
        timeframe: 'H1',
        candles: generateValidCandles(1000),
        source: 'test_integration'
      };
      
      await dataProvider.loadDataset(testData);
      
      // Step 3: User asks about data again (should now show loaded data)
      const dataQueryAfterLoad = await chatbot.processQuery("What data do you have?");
      
      expect(dataQueryAfterLoad.message).toContain('EUR/USD');
      expect(dataQueryAfterLoad.sources![0].dataPoints).toBe(1000);
      
      // Step 4: User creates a strategy
      const strategyCreation = await chatbot.processQuery("Create an RSI strategy");
      
      expect(strategyCreation.intent).toBe('create_strategy');
      expect(strategyCreation.codeTemplate).toBeDefined();
      expect(strategyCreation.templateVerification).toBeDefined();
      
      // Step 5: User optimizes strategy with Darwin
      const strategy = {
        id: 'user_rsi_strategy',
        parameters: { rsi_period: 14, stop_loss: 20 },
        parameterRanges: {
          rsi_period: { min: 5, max: 50 },
          stop_loss: { min: 10, max: 50 }
        }
      };
      
      const evolutionResult = await darwin.evolve(strategy, testData, {
        seed: 'integration_test',
        generations: 5
      });
      
      expect(evolutionResult.auditTrail.evolutionId).toBeDefined();
      expect(evolutionResult.improvementPercentage).toBeGreaterThanOrEqual(0);
      
      // Step 6: User queries about optimization results
      const optimizationQuery = await chatbot.processQueryWithData(
        "Show my optimization results",
        {
          strategyId: strategy.id,
          totalTrades: 150,
          winRate: 62.5,
          auditTrailId: evolutionResult.auditTrail.evolutionId
        }
      );
      
      expect(optimizationQuery.verificationStatus.dataVerified).toBe(true);
      expect(optimizationQuery.sources![0].id).toBe(evolutionResult.auditTrail.evolutionId);
    });

    it('should maintain data integrity throughout workflow', async () => {
      // Load data with known hash
      const testData = {
        pair: 'GBP/USD',
        timeframe: 'H4',
        candles: generateValidCandles(500),
        source: 'integrity_test'
      };
      
      await dataProvider.loadDataset(testData);
      const originalHash = dataProvider.calculateDataHash(testData.candles);
      
      // Run evolution
      const strategy = {
        id: 'integrity_test_strategy',
        parameters: { rsi_period: 14 }
      };
      
      const result = await darwin.evolve(strategy, testData, { seed: 'integrity_test' });
      
      // Verify data hash is preserved in audit trail
      expect(result.auditTrail.dataHash).toBe(originalHash);
      
      // Verify data hasn't been corrupted
      const retrievedData = await dataProvider.getData('GBP/USD', 'H4');
      expect(retrievedData.success).toBe(true);
      expect(retrievedData.data!.verification.originalHash).toBe(originalHash);
    });

    it('should handle errors gracefully without hallucination', async () => {
      // Test chatbot with no data
      const noDataQuery = await chatbot.processQuery("What's my strategy performance?");
      
      expect(noDataQuery.message).toContain("I don't have any backtest data");
      expect(noDataQuery.verificationStatus.noHallucination).toBe(true);
      
      // Test Darwin with insufficient data
      const insufficientData = {
        pair: 'USD/JPY',
        timeframe: 'M1',
        candles: generateValidCandles(10), // Too few
        source: 'error_test'
      };
      
      const strategy = { id: 'error_test', parameters: { rsi_period: 14 } };
      
      await expect(darwin.evolve(strategy, insufficientData))
        .rejects.toThrow('Insufficient data for reliable evolution');
    });
  });

  describe('Zero Hallucination Verification', () => {
    it('should never provide unverified performance claims', async () => {
      // Test various queries that might tempt hallucination
      const queries = [
        "What's a typical win rate for forex strategies?",
        "How much money can I make with this strategy?",
        "What's the best RSI period to use?",
        "Will EUR/USD go up tomorrow?"
      ];
      
      for (const query of queries) {
        const response = await chatbot.processQuery(query);
        
        // Should not contain made-up statistics
        expect(response.message).not.toMatch(/\d+%.*win rate/);
        expect(response.message).not.toMatch(/\$\d+.*profit/);
        expect(response.message).not.toMatch(/will (rise|fall|go up|go down)/i);
        
        expect(response.verificationStatus.noHallucination).toBe(true);
      }
    });

    it('should provide source citations for all claims', async () => {
      // Load test data
      const testData = {
        pair: 'EUR/USD',
        timeframe: 'H1',
        candles: generateValidCandles(200),
        source: 'citation_test'
      };
      
      await dataProvider.loadDataset(testData);
      
      // Query about data
      const response = await chatbot.processQuery("What EUR/USD data do you have?");
      
      expect(response.sources).toBeDefined();
      expect(response.sources!.length).toBeGreaterThan(0);
      expect(response.sources![0].type).toBe('data_inventory');
      expect(response.verificationStatus.sourcesProvided).toBe(true);
    });
  });

  describe('Reproducibility Verification', () => {
    it('should produce identical results with same inputs', async () => {
      // Load identical data
      const testData = {
        pair: 'USD/CHF',
        timeframe: 'H1',
        candles: generateValidCandles(300),
        source: 'reproducibility_test'
      };
      
      await dataProvider.loadDataset(testData);
      
      const strategy = {
        id: 'reproducibility_test',
        parameters: { rsi_period: 14, stop_loss: 25 }
      };
      
      // Run evolution twice with same seed
      const result1 = await darwin.evolve(strategy, testData, {
        seed: 'reproducibility_seed',
        generations: 3
      });
      
      const result2 = await darwin.evolve(strategy, testData, {
        seed: 'reproducibility_seed',
        generations: 3
      });
      
      // Results should be identical
      expect(result1.optimizedParameters).toEqual(result2.optimizedParameters);
      expect(result1.finalFitness).toBe(result2.finalFitness);
      expect(result1.auditTrail.backtestsPerformed).toBe(result2.auditTrail.backtestsPerformed);
    });
  });

  describe('Audit Trail Completeness', () => {
    it('should maintain complete audit trail across all operations', async () => {
      // Load data
      const testData = {
        pair: 'AUD/USD',
        timeframe: 'H1',
        candles: generateValidCandles(400),
        source: 'audit_test'
      };
      
      await dataProvider.loadDataset(testData);
      
      // Check data loading audit
      const dataAudit = await dataProvider.getAuditTrail('AUD/USD', 'H1');
      expect(dataAudit.operations).toHaveLength(1);
      expect(dataAudit.operations[0].operation).toBe('load_dataset');
      
      // Run chatbot query
      await chatbot.processQuery("What AUD/USD data is available?");
      
      // Check chatbot audit
      const chatbotAudit = await chatbot.getAuditLog();
      expect(chatbotAudit.interactions.length).toBeGreaterThan(0);
      
      // Run evolution
      const strategy = { id: 'audit_test', parameters: { rsi_period: 14 } };
      const evolutionResult = await darwin.evolve(strategy, testData, { seed: 'audit_test' });
      
      // Verify evolution audit trail
      expect(evolutionResult.auditTrail.evolutionId).toBeDefined();
      expect(evolutionResult.auditTrail.dataHash).toBeDefined();
      expect(evolutionResult.auditTrail.mutations.length).toBeGreaterThanOrEqual(0);
    });
  });
});

// Helper function for integration tests
function generateValidCandles(count: number): any[] {
  const candles = [];
  const baseTime = new Date('2024-01-01').getTime();
  
  for (let i = 0; i < count; i++) {
    const open = 1.1 + Math.random() * 0.01;
    const close = open + (Math.random() - 0.5) * 0.005;
    const high = Math.max(open, close) + Math.random() * 0.002;
    const low = Math.min(open, close) - Math.random() * 0.002;
    
    candles.push({
      timestamp: new Date(baseTime + i * 3600000), // Hourly candles
      open: parseFloat(open.toFixed(5)),
      high: parseFloat(high.toFixed(5)),
      low: parseFloat(low.toFixed(5)),
      close: parseFloat(close.toFixed(5)),
      volume: Math.floor(Math.random() * 1000000)
    });
  }
  
  return candles;
}