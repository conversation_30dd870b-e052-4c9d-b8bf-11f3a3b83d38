"""
Main FastAPI server with database integration
"""

import os
import sys
import logging
import asyncio
from fastapi import FastAPI, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.ext.asyncio import AsyncSession
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables
load_dotenv()

# Import database modules
from app.db.config import get_db, Base, engine
from app.db.models import User, MT5Account, Position, Order, Strategy, Backtest, ApiKey

# Import API routes
from app.api.routes import auth, backtest, mt5

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("server")

# Create FastAPI app
app = FastAPI(
    title="AI Enhanced Trading Platform API",
    description="API for AI Enhanced Trading Platform",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For development - restrict in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(auth.router, prefix="/api/v1/auth", tags=["auth"])
app.include_router(backtest.router, prefix="/api/v1/backtest", tags=["backtest"])
app.include_router(mt5.router, prefix="/api/v1/mt5", tags=["mt5"])

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "AI Enhanced Trading Platform API",
        "version": "1.0.0"
    }

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "ok",
        "version": "1.0.0"
    }

# Database initialization
@app.on_event("startup")
async def init_db():
    """Initialize database on startup"""
    try:
        # Create database tables
        async with engine.begin() as conn:
            # Create tables automatically if they don't exist
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")

# Run the server
if __name__ == "__main__":
    import uvicorn
    
    # Get port from environment variable or use default
    port = int(os.getenv("PORT", "8000"))
    
    # Run the server
    uvicorn.run("server:app", host="0.0.0.0", port=port, reload=True)