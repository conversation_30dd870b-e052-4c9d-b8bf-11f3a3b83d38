#!/usr/bin/env python3
"""
Secure MT5 Bridge with Zero-Hallucination Validation
Enterprise-grade trading bridge with cryptographic security and data integrity verification.
"""

import hashlib
import hmac
import json
import time
import logging
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, asdict, field
from enum import Enum
from decimal import Decimal, ROUND_HALF_UP
import threading
from contextlib import contextmanager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SecurityLevel(Enum):
    """Security levels for different trading environments"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"


class OrderType(Enum):
    """Trading order types"""
    BUY = "buy"
    SELL = "sell"
    BUY_LIMIT = "buy_limit"
    SELL_LIMIT = "sell_limit"
    BUY_STOP = "buy_stop"
    SELL_STOP = "sell_stop"


class OrderStatus(Enum):
    """Order execution status"""
    PENDING = "pending"
    VALIDATED = "validated"
    EXECUTED = "executed"
    REJECTED = "rejected"
    CANCELLED = "cancelled"
    FAILED = "failed"


class SecurityError(Exception):
    """Base class for security-related errors"""
    pass


class InvalidOrderError(SecurityError):
    """Raised when order validation fails"""
    pass


class MarketConditionError(SecurityError):
    """Raised when market conditions are unsafe"""
    pass


class DataIntegrityError(SecurityError):
    """Raised when data integrity verification fails"""
    pass


class CryptographicError(SecurityError):
    """Raised when cryptographic operations fail"""
    pass


@dataclass
class TradeOrder:
    """Secure trade order with integrity verification"""
    symbol: str
    order_type: OrderType
    volume: Decimal
    price: Optional[Decimal] = None
    stop_loss: Optional[Decimal] = None
    take_profit: Optional[Decimal] = None
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    order_id: str = field(default_factory=lambda: f"ORD_{int(time.time() * 1000000)}")
    magic_number: int = 12345
    comment: str = ""
    
    def __post_init__(self):
        """Validate order data on creation"""
        if not isinstance(self.volume, Decimal):
            self.volume = Decimal(str(self.volume))
        if self.price and not isinstance(self.price, Decimal):
            self.price = Decimal(str(self.price))
        if self.stop_loss and not isinstance(self.stop_loss, Decimal):
            self.stop_loss = Decimal(str(self.stop_loss))
        if self.take_profit and not isinstance(self.take_profit, Decimal):
            self.take_profit = Decimal(str(self.take_profit))
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for hashing"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['order_type'] = self.order_type.value
        # Convert Decimal to string for JSON serialization
        for key, value in data.items():
            if isinstance(value, Decimal):
                data[key] = str(value)
        return data
    
    def get_hash(self) -> str:
        """Generate SHA-256 hash of order data"""
        order_data = self.to_dict()
        order_json = json.dumps(order_data, sort_keys=True)
        return hashlib.sha256(order_json.encode()).hexdigest()


@dataclass
class TradeReceipt:
    """Secure trade receipt with audit trail"""
    order_id: str
    status: OrderStatus
    execution_price: Optional[Decimal] = None
    execution_time: Optional[datetime] = None
    commission: Decimal = Decimal('0')
    slippage: Decimal = Decimal('0')
    error_message: Optional[str] = None
    security_hash: Optional[str] = None
    audit_trail: List[Dict[str, Any]] = field(default_factory=list)
    
    def __post_init__(self):
        """Generate security hash on creation"""
        if self.execution_time is None:
            self.execution_time = datetime.now(timezone.utc)
        self.security_hash = self._generate_security_hash()
    
    def _generate_security_hash(self) -> str:
        """Generate security hash for receipt verification"""
        receipt_data = {
            'order_id': self.order_id,
            'status': self.status.value,
            'execution_price': str(self.execution_price) if self.execution_price else None,
            'execution_time': self.execution_time.isoformat() if self.execution_time else None,
            'commission': str(self.commission),
            'slippage': str(self.slippage)
        }
        receipt_json = json.dumps(receipt_data, sort_keys=True)
        return hashlib.sha256(receipt_json.encode()).hexdigest()
    
    def verify_integrity(self) -> bool:
        """Verify receipt integrity"""
        if self.security_hash is None:
            return False
        expected_hash = self._generate_security_hash()
        return hmac.compare_digest(self.security_hash, expected_hash)


@dataclass
class MarketData:
    """Secure market data with integrity verification"""
    symbol: str
    bid: Decimal
    ask: Decimal
    timestamp: datetime
    spread: Decimal = field(init=False)
    data_hash: str = field(init=False)
    
    def __post_init__(self):
        """Calculate spread and generate data hash"""
        self.spread = self.ask - self.bid
        self.data_hash = self._generate_data_hash()
    
    def _generate_data_hash(self) -> str:
        """Generate SHA-256 hash of market data"""
        data = {
            'symbol': self.symbol,
            'bid': str(self.bid),
            'ask': str(self.ask),
            'timestamp': self.timestamp.isoformat()
        }
        data_json = json.dumps(data, sort_keys=True)
        return hashlib.sha256(data_json.encode()).hexdigest()
    
    def verify_integrity(self) -> bool:
        """Verify market data integrity"""
        expected_hash = self._generate_data_hash()
        return hmac.compare_digest(self.data_hash, expected_hash)


class DataIntegrityValidator:
    """Cryptographic data integrity validation system"""
    
    def __init__(self, secret_key: Optional[str] = None):
        self.secret_key = secret_key or "secure_mt5_bridge_key_2024"
        self.price_history: Dict[str, List[Dict[str, Any]]] = {}
        self.integrity_log: List[Dict[str, Any]] = []
        self._lock = threading.Lock()
    
    def store_price_data(self, symbol: str, price_data: Dict[str, Any]) -> str:
        """Store price data with integrity hash"""
        with self._lock:
            timestamp = datetime.now(timezone.utc)
            
            # Create data entry with timestamp
            data_entry = {
                'symbol': symbol,
                'data': price_data,
                'timestamp': timestamp.isoformat(),
                'sequence': len(self.price_history.get(symbol, []))
            }
            
            # Generate HMAC for data integrity
            data_json = json.dumps(data_entry, sort_keys=True)
            data_hash = hmac.new(
                self.secret_key.encode(),
                data_json.encode(),
                hashlib.sha256
            ).hexdigest()
            
            data_entry['hash'] = data_hash
            
            # Store in history
            if symbol not in self.price_history:
                self.price_history[symbol] = []
            
            self.price_history[symbol].append(data_entry)
            
            # Log integrity action
            self.integrity_log.append({
                'action': 'store_price_data',
                'symbol': symbol,
                'hash': data_hash,
                'timestamp': timestamp.isoformat()
            })
            
            return data_hash
    
    def verify_price_data(self, symbol: str, timestamp: datetime, tolerance_seconds: int = 60) -> bool:
        """Verify price data integrity within time tolerance"""
        with self._lock:
            if symbol not in self.price_history:
                logger.warning(f"No price history found for symbol: {symbol}")
                return False
            
            # Find data within time tolerance
            target_time = timestamp.timestamp()
            
            for entry in self.price_history[symbol]:
                entry_time = datetime.fromisoformat(entry['timestamp']).timestamp()
                
                if abs(entry_time - target_time) <= tolerance_seconds:
                    # Verify hash integrity
                    entry_copy = entry.copy()
                    stored_hash = entry_copy.pop('hash')
                    
                    data_json = json.dumps(entry_copy, sort_keys=True)
                    expected_hash = hmac.new(
                        self.secret_key.encode(),
                        data_json.encode(),
                        hashlib.sha256
                    ).hexdigest()
                    
                    is_valid = hmac.compare_digest(stored_hash, expected_hash)
                    
                    # Log verification attempt
                    self.integrity_log.append({
                        'action': 'verify_price_data',
                        'symbol': symbol,
                        'timestamp': timestamp.isoformat(),
                        'result': is_valid,
                        'verification_time': datetime.now(timezone.utc).isoformat()
                    })
                    
                    return is_valid
            
            logger.warning(f"No price data found within tolerance for {symbol} at {timestamp}")
            return False
    
    def check_hash(self, timestamp: datetime) -> bool:
        """Check if price data hash exists and is valid for given timestamp"""
        # This is a simplified implementation
        # In production, this would check against a secure hash database
        current_time = datetime.now(timezone.utc)
        time_diff = abs((current_time - timestamp).total_seconds())
        
        # Allow data within 5 minutes
        return time_diff <= 300
    
    def get_integrity_report(self) -> Dict[str, Any]:
        """Generate integrity verification report"""
        with self._lock:
            total_entries = sum(len(history) for history in self.price_history.values())
            symbols_tracked = len(self.price_history)
            
            return {
                'total_price_entries': total_entries,
                'symbols_tracked': symbols_tracked,
                'integrity_checks_performed': len(self.integrity_log),
                'last_check': self.integrity_log[-1] if self.integrity_log else None,
                'report_timestamp': datetime.now(timezone.utc).isoformat()
            }


class SecureMarketDataProvider:
    """Secure market data provider with spread verification"""
    
    def __init__(self, max_spread_pct: float = 0.1):
        self.max_spread_pct = max_spread_pct  # 0.1% maximum spread
        self.market_data_cache: Dict[str, MarketData] = {}
        self.spread_violations: List[Dict[str, Any]] = []
        self._lock = threading.Lock()
    
    def get_market_data(self, symbol: str) -> Optional[MarketData]:
        """Get current market data for symbol"""
        # In production, this would connect to real market data feed
        # For now, simulate realistic market data
        
        base_price = self._get_base_price(symbol)
        spread = base_price * Decimal(str(self.max_spread_pct / 2))  # Half spread each side
        
        bid = base_price - spread
        ask = base_price + spread
        
        market_data = MarketData(
            symbol=symbol,
            bid=bid,
            ask=ask,
            timestamp=datetime.now(timezone.utc)
        )
        
        with self._lock:
            self.market_data_cache[symbol] = market_data
        
        return market_data
    
    def _get_base_price(self, symbol: str) -> Decimal:
        """Get base price for symbol (simulated)"""
        # Simulate different price levels for different symbols
        price_map = {
            'EURUSD': Decimal('1.0850'),
            'GBPUSD': Decimal('1.2650'),
            'USDJPY': Decimal('149.50'),
            'AUDUSD': Decimal('0.6750'),
            'USDCAD': Decimal('1.3450')
        }
        return price_map.get(symbol, Decimal('1.0000'))
    
    def verify_spread(self, symbol: str) -> bool:
        """Verify that spread is within acceptable limits"""
        market_data = self.get_market_data(symbol)
        if not market_data:
            return False
        
        # Calculate spread percentage
        mid_price = (market_data.bid + market_data.ask) / 2
        spread_pct = (market_data.spread / mid_price) * 100
        
        is_valid = spread_pct <= Decimal(str(self.max_spread_pct))
        
        if not is_valid:
            violation = {
                'symbol': symbol,
                'spread_pct': float(spread_pct),
                'max_allowed_pct': self.max_spread_pct,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'market_data': market_data.to_dict() if hasattr(market_data, 'to_dict') else str(market_data)
            }
            
            with self._lock:
                self.spread_violations.append(violation)
            
            logger.warning(f"Spread violation for {symbol}: {spread_pct}% > {self.max_spread_pct}%")
        
        return is_valid
    
    def get_spread_violations(self) -> List[Dict[str, Any]]:
        """Get list of spread violations"""
        with self._lock:
            return self.spread_violations.copy()


class ZeroHallucinationValidator:
    """Zero-hallucination order validation system"""
    
    def __init__(self):
        self.validation_rules = [
            self._validate_volume_positive,
            self._validate_symbol_format,
            self._validate_price_logic,
            self._validate_stop_loss_logic,
            self._validate_take_profit_logic,
            self._validate_timestamp_recent,
            self._validate_order_id_format
        ]
        self.validation_log: List[Dict[str, Any]] = []
        self._lock = threading.Lock()
    
    def validate_order(self, order: TradeOrder) -> Tuple[bool, List[str]]:
        """Comprehensive zero-hallucination order validation"""
        errors = []
        
        for rule in self.validation_rules:
            try:
                is_valid, error_msg = rule(order)
                if not is_valid:
                    errors.append(error_msg)
            except Exception as e:
                errors.append(f"Validation rule error: {str(e)}")
        
        is_valid = len(errors) == 0
        
        # Log validation attempt
        with self._lock:
            self.validation_log.append({
                'order_id': order.order_id,
                'is_valid': is_valid,
                'errors': errors,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'order_hash': order.get_hash()
            })
        
        return is_valid, errors
    
    def _validate_volume_positive(self, order: TradeOrder) -> Tuple[bool, str]:
        """Validate that volume is positive"""
        if order.volume <= 0:
            return False, f"Volume must be positive, got {order.volume}"
        return True, ""
    
    def _validate_symbol_format(self, order: TradeOrder) -> Tuple[bool, str]:
        """Validate symbol format"""
        if not order.symbol or len(order.symbol) < 6:
            return False, f"Invalid symbol format: {order.symbol}"
        
        # Check for common forex pairs
        valid_symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCAD', 'USDCHF', 'NZDUSD']
        if order.symbol not in valid_symbols:
            return False, f"Symbol {order.symbol} not in approved list"
        
        return True, ""
    
    def _validate_price_logic(self, order: TradeOrder) -> Tuple[bool, str]:
        """Validate price logic for different order types"""
        if order.order_type in [OrderType.BUY_LIMIT, OrderType.SELL_LIMIT, 
                               OrderType.BUY_STOP, OrderType.SELL_STOP]:
            if not order.price or order.price <= 0:
                return False, f"Price required for {order.order_type.value} orders"
        
        return True, ""
    
    def _validate_stop_loss_logic(self, order: TradeOrder) -> Tuple[bool, str]:
        """Validate stop loss logic"""
        if order.stop_loss and order.price:
            if order.order_type == OrderType.BUY:
                if order.stop_loss >= order.price:
                    return False, "Stop loss must be below entry price for BUY orders"
            elif order.order_type == OrderType.SELL:
                if order.stop_loss <= order.price:
                    return False, "Stop loss must be above entry price for SELL orders"
        
        return True, ""
    
    def _validate_take_profit_logic(self, order: TradeOrder) -> Tuple[bool, str]:
        """Validate take profit logic"""
        if order.take_profit and order.price:
            if order.order_type == OrderType.BUY:
                if order.take_profit <= order.price:
                    return False, "Take profit must be above entry price for BUY orders"
            elif order.order_type == OrderType.SELL:
                if order.take_profit >= order.price:
                    return False, "Take profit must be below entry price for SELL orders"
        
        return True, ""
    
    def _validate_timestamp_recent(self, order: TradeOrder) -> Tuple[bool, str]:
        """Validate that timestamp is recent (within 1 minute)"""
        now = datetime.now(timezone.utc)
        time_diff = abs((now - order.timestamp).total_seconds())
        
        if time_diff > 60:  # 1 minute tolerance
            return False, f"Order timestamp too old: {time_diff} seconds"
        
        return True, ""
    
    def _validate_order_id_format(self, order: TradeOrder) -> Tuple[bool, str]:
        """Validate order ID format"""
        if not order.order_id or not order.order_id.startswith('ORD_'):
            return False, f"Invalid order ID format: {order.order_id}"
        
        return True, ""
    
    def get_validation_stats(self) -> Dict[str, Any]:
        """Get validation statistics"""
        with self._lock:
            total_validations = len(self.validation_log)
            successful_validations = sum(1 for log in self.validation_log if log['is_valid'])
            
            return {
                'total_validations': total_validations,
                'successful_validations': successful_validations,
                'failure_rate': (total_validations - successful_validations) / max(total_validations, 1),
                'last_validation': self.validation_log[-1] if self.validation_log else None
            }


class SecureMT5Bridge:
    """
    Enterprise-grade secure MT5 bridge with zero-hallucination validation
    and cryptographic data integrity verification.
    """
    
    def __init__(self, security_level: SecurityLevel = SecurityLevel.DEVELOPMENT):
        self.security_level = security_level
        self.data_integrity = DataIntegrityValidator()
        self.market_data = SecureMarketDataProvider()
        self.order_validator = ZeroHallucinationValidator()
        
        # Security configuration based on level
        self.security_config = self._get_security_config(security_level)
        
        # Execution tracking
        self.executed_orders: List[TradeReceipt] = []
        self.security_violations: List[Dict[str, Any]] = []
        self._lock = threading.Lock()
        
        logger.info(f"SecureMT5Bridge initialized with security level: {security_level.value}")
    
    def _get_security_config(self, level: SecurityLevel) -> Dict[str, Any]:
        """Get security configuration based on security level"""
        configs = {
            SecurityLevel.DEVELOPMENT: {
                'require_data_integrity': False,
                'require_spread_verification': False,
                'max_order_age_seconds': 300,
                'enable_audit_logging': True
            },
            SecurityLevel.TESTING: {
                'require_data_integrity': True,
                'require_spread_verification': True,
                'max_order_age_seconds': 120,
                'enable_audit_logging': True
            },
            SecurityLevel.STAGING: {
                'require_data_integrity': True,
                'require_spread_verification': True,
                'max_order_age_seconds': 60,
                'enable_audit_logging': True
            },
            SecurityLevel.PRODUCTION: {
                'require_data_integrity': True,
                'require_spread_verification': True,
                'max_order_age_seconds': 30,
                'enable_audit_logging': True
            }
        }
        return configs[level]
    
    @contextmanager
    def security_context(self, operation: str):
        """Security context manager for operations"""
        start_time = datetime.now(timezone.utc)
        try:
            logger.info(f"Starting secure operation: {operation}")
            yield
            logger.info(f"Completed secure operation: {operation}")
        except SecurityError as e:
            logger.error(f"Security error in {operation}: {e}")
            self._log_security_violation(operation, str(e))
            raise
        except Exception as e:
            logger.error(f"Unexpected error in {operation}: {e}")
            raise
        finally:
            duration = (datetime.now(timezone.utc) - start_time).total_seconds()
            logger.info(f"Operation {operation} completed in {duration:.3f}s")
    
    def _log_security_violation(self, operation: str, error: str):
        """Log security violation"""
        violation = {
            'operation': operation,
            'error': error,
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'security_level': self.security_level.value
        }
        
        with self._lock:
            self.security_violations.append(violation)
    
    def _validate_order_safety(self, order: TradeOrder) -> None:
        """Zero-hallucination order validation with comprehensive security checks"""
        
        # 1. Basic order validation
        is_valid, errors = self.order_validator.validate_order(order)
        if not is_valid:
            raise InvalidOrderError(f"Order validation failed: {'; '.join(errors)}")
        
        # 2. Market condition verification
        if self.security_config['require_spread_verification']:
            if not self.market_data.verify_spread(order.symbol):
                raise MarketConditionError("Spread exceeds safety limits")
        
        # 3. Data integrity verification
        if self.security_config['require_data_integrity']:
            if not self.data_integrity.check_hash(order.timestamp):
                raise DataIntegrityError("Price data verification failed")
        
        # 4. Order age verification
        now = datetime.now(timezone.utc)
        order_age = (now - order.timestamp).total_seconds()
        if order_age > self.security_config['max_order_age_seconds']:
            raise InvalidOrderError(f"Order too old: {order_age}s > {self.security_config['max_order_age_seconds']}s")
        
        logger.info(f"Order {order.order_id} passed all security validations")
    
    def execute_order(self, order: TradeOrder) -> TradeReceipt:
        """
        Execute order with comprehensive security validation
        
        Args:
            order: TradeOrder to execute
            
        Returns:
            TradeReceipt with execution details and security verification
            
        Raises:
            InvalidOrderError: When order validation fails
            MarketConditionError: When market conditions are unsafe
            DataIntegrityError: When data integrity verification fails
        """
        
        with self.security_context(f"execute_order_{order.order_id}"):
            # Validate order safety
            self._validate_order_safety(order)
            
            # Store price data for integrity verification
            market_data = self.market_data.get_market_data(order.symbol)
            if market_data:
                price_data = {
                    'bid': str(market_data.bid),
                    'ask': str(market_data.ask),
                    'spread': str(market_data.spread)
                }
                data_hash = self.data_integrity.store_price_data(order.symbol, price_data)
                logger.info(f"Stored price data for {order.symbol} with hash: {data_hash[:16]}...")
            
            # Execute order (simulated for now)
            receipt = self._execute_simulated_order(order, market_data)
            
            # Store execution record
            with self._lock:
                self.executed_orders.append(receipt)
            
            # Verify receipt integrity
            if not receipt.verify_integrity():
                raise CryptographicError("Receipt integrity verification failed")
            
            logger.info(f"Order {order.order_id} executed successfully")
            return receipt
    
    def _execute_simulated_order(self, order: TradeOrder, market_data: Optional[MarketData]) -> TradeReceipt:
        """Execute order in simulation mode"""
        
        # Determine execution price
        if market_data:
            if order.order_type == OrderType.BUY:
                execution_price = market_data.ask
            else:
                execution_price = market_data.bid
        else:
            execution_price = order.price or Decimal('1.0000')
        
        # Calculate commission (0.01% of trade value)
        trade_value = execution_price * order.volume
        commission = trade_value * Decimal('0.0001')
        
        # Simulate small slippage (0.1 pip)
        slippage = Decimal('0.00001')
        
        # Create audit trail
        audit_trail = [
            {
                'action': 'order_received',
                'timestamp': order.timestamp.isoformat(),
                'order_hash': order.get_hash()
            },
            {
                'action': 'validation_passed',
                'timestamp': datetime.now(timezone.utc).isoformat()
            },
            {
                'action': 'order_executed',
                'execution_price': str(execution_price),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
        ]
        
        receipt = TradeReceipt(
            order_id=order.order_id,
            status=OrderStatus.EXECUTED,
            execution_price=execution_price,
            commission=commission,
            slippage=slippage,
            audit_trail=audit_trail
        )
        
        return receipt
    
    def get_security_report(self) -> Dict[str, Any]:
        """Generate comprehensive security report"""
        with self._lock:
            return {
                'security_level': self.security_level.value,
                'security_config': self.security_config,
                'executed_orders_count': len(self.executed_orders),
                'security_violations_count': len(self.security_violations),
                'data_integrity_report': self.data_integrity.get_integrity_report(),
                'validation_stats': self.order_validator.get_validation_stats(),
                'spread_violations': len(self.market_data.get_spread_violations()),
                'report_timestamp': datetime.now(timezone.utc).isoformat()
            }
    
    def get_executed_orders(self) -> List[TradeReceipt]:
        """Get list of executed orders"""
        with self._lock:
            return self.executed_orders.copy()
    
    def get_security_violations(self) -> List[Dict[str, Any]]:
        """Get list of security violations"""
        with self._lock:
            return self.security_violations.copy()


# Factory function for easy instantiation
def create_secure_bridge(security_level: SecurityLevel = SecurityLevel.DEVELOPMENT) -> SecureMT5Bridge:
    """Create a secure MT5 bridge with specified security level"""
    return SecureMT5Bridge(security_level=security_level)


if __name__ == "__main__":
    # Quick demonstration
    print("🔒 Secure MT5 Bridge Demo")
    print("=" * 50)
    
    # Create secure bridge
    bridge = create_secure_bridge(SecurityLevel.TESTING)
    
    # Create test order
    order = TradeOrder(
        symbol="EURUSD",
        order_type=OrderType.BUY,
        volume=Decimal('0.1'),
        price=Decimal('1.0850')
    )
    
    try:
        # Execute order
        receipt = bridge.execute_order(order)
        print(f"✅ Order executed: {receipt.order_id}")
        print(f"   Status: {receipt.status.value}")
        print(f"   Execution Price: {receipt.execution_price}")
        print(f"   Security Hash: {receipt.security_hash[:16]}...")
        
        # Generate security report
        report = bridge.get_security_report()
        print(f"\n📊 Security Report:")
        print(f"   Security Level: {report['security_level']}")
        print(f"   Executed Orders: {report['executed_orders_count']}")
        print(f"   Security Violations: {report['security_violations_count']}")
        
    except SecurityError as e:
        print(f"❌ Security Error: {e}")
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")