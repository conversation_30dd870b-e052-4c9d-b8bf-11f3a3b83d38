#!/usr/bin/env python3
"""
TDD Toolkit - A unified interface for TDD tools

This script provides a command-line interface to run various TDD tools
and generate reports for the AI Enhanced Trading Platform.
"""

import argparse
import os
import subprocess
import sys
from pathlib import Path


def print_banner(text, char="="):
    """Print a formatted banner"""
    print(f"\n{char * 60}")
    print(f" {text}")
    print(f"{char * 60}\n")


def run_command(command, description):
    """Run a command and capture output"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=300  # 5 minute timeout
        )
        print(result.stdout)
        if result.stderr:
            print(f"⚠️ Warnings/Errors:")
            print(result.stderr)
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print(f"❌ {description} timed out after 5 minutes")
        return False
    except Exception as e:
        print(f"❌ Error running {description}: {e}")
        return False


def setup_directories():
    """Set up necessary directories"""
    dirs = ["reports", "reports/coverage", "reports/mutation"]
    for dir_path in dirs:
        Path(dir_path).mkdir(exist_ok=True, parents=True)
    return True


def run_tests(args):
    """Run tests with the specified options"""
    print_banner("RUNNING TESTS")
    
    # Build the command
    command = "python run_tests.py"
    if args.quick:
        command += " quick"
    elif args.coverage:
        command += " coverage"
    
    return run_command(command, "Running tests")


def run_mutation_tests(args):
    """Run mutation tests"""
    print_banner("RUNNING MUTATION TESTS")
    
    # Check if target module is specified
    target = args.target if args.target else ""
    
    command = f"python tests/mutation_testing.py {target}"
    return run_command(command, "Running mutation tests")


def generate_dashboard():
    """Generate the test dashboard"""
    print_banner("GENERATING TEST DASHBOARD")
    
    command = "python tests/dashboard.py"
    return run_command(command, "Generating test dashboard")


def track_test_history():
    """Track test history"""
    print_banner("TRACKING TEST HISTORY")
    
    command = "python tests/test_history_tracker.py"
    return run_command(command, "Tracking test history")


def install_git_hooks():
    """Install git hooks"""
    print_banner("INSTALLING GIT HOOKS")
    
    command = "python scripts/install_git_hooks.py"
    return run_command(command, "Installing git hooks")


def create_new_test(args):
    """Create a new test file from template"""
    print_banner("CREATING NEW TEST")
    
    if not args.name:
        print("❌ Test name is required")
        return False
    
    # Normalize the name
    test_name = args.name
    if not test_name.startswith("test_"):
        test_name = f"test_{test_name}"
    if not test_name.endswith(".py"):
        test_name = f"{test_name}.py"
    
    # Determine the target directory
    if args.category:
        target_dir = Path(f"tests/{args.category}")
        if not target_dir.exists():
            print(f"⚠️ Category directory {args.category} doesn't exist, creating it")
            target_dir.mkdir(exist_ok=True, parents=True)
    else:
        target_dir = Path("tests")
    
    # Full path to the new test file
    target_file = target_dir / test_name
    
    # Check if file already exists
    if target_file.exists() and not args.force:
        print(f"❌ Test file {target_file} already exists. Use --force to overwrite.")
        return False
    
    # Copy the template
    template_file = Path("tests/templates/tdd_template.py")
    if not template_file.exists():
        print(f"❌ Template file {template_file} not found")
        return False
    
    # Read the template
    with open(template_file, "r") as f:
        template_content = f.read()
    
    # Replace placeholders
    feature_name = args.name.replace("test_", "").replace(".py", "")
    feature_class = "".join(word.capitalize() for word in feature_name.split("_"))
    
    content = template_content.replace("<FeatureName>", feature_class)
    
    # Write the new test file
    with open(target_file, "w") as f:
        f.write(content)
    
    print(f"✅ Created new test file: {target_file}")
    return True


def run_pilot_tests(args):
    """Run pilot tests with enhanced reporting"""
    print_banner("RUNNING PILOT TESTS")
    
    # Build the command
    command = "python scripts/run_pilot_tests.py"
    if args.target:
        command += f" {args.target}"
    
    return run_command(command, "Running pilot tests")


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="TDD Toolkit for AI Enhanced Trading Platform")
    subparsers = parser.add_subparsers(dest="command", help="Command to run")
    
    # Test command
    test_parser = subparsers.add_parser("test", help="Run tests")
    test_parser.add_argument("--quick", action="store_true", help="Run quick tests")
    test_parser.add_argument("--coverage", action="store_true", help="Run tests with coverage")
    
    # Mutation command
    mutation_parser = subparsers.add_parser("mutation", help="Run mutation tests")
    mutation_parser.add_argument("--target", help="Target module to test")
    
    # Dashboard command
    subparsers.add_parser("dashboard", help="Generate test dashboard")
    
    # History command
    subparsers.add_parser("history", help="Track test history")
    
    # Hooks command
    subparsers.add_parser("hooks", help="Install git hooks")
    
    # New test command
    new_test_parser = subparsers.add_parser("new", help="Create a new test file from template")
    new_test_parser.add_argument("name", help="Name of the test file")
    new_test_parser.add_argument("--category", help="Category directory for the test")
    new_test_parser.add_argument("--force", action="store_true", help="Force overwrite if file exists")
    
    # Pilot command
    pilot_parser = subparsers.add_parser("pilot", help="Run pilot tests with enhanced reporting")
    pilot_parser.add_argument("--target", help="Target test file or directory")
    
    # All command
    subparsers.add_parser("all", help="Run all TDD tools")
    
    # Parse arguments
    args = parser.parse_args()
    
    # Set up directories
    setup_directories()
    
    # Execute the command
    if args.command == "test":
        run_tests(args)
    elif args.command == "mutation":
        run_mutation_tests(args)
    elif args.command == "dashboard":
        generate_dashboard()
    elif args.command == "history":
        track_test_history()
    elif args.command == "hooks":
        install_git_hooks()
    elif args.command == "new":
        create_new_test(args)
    elif args.command == "pilot":
        run_pilot_tests(args)
    elif args.command == "all":
        # Run all tools
        run_tests(argparse.Namespace(quick=False, coverage=True))
        run_mutation_tests(argparse.Namespace(target=None))
        generate_dashboard()
        track_test_history()
    else:
        # Show help if no command is specified
        parser.print_help()


if __name__ == "__main__":
    main()