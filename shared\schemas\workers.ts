/**
 * Worker Management Schemas
 * Defines types for Python background worker integration
 */

import { z } from 'zod';
import { BacktestProgressSchema } from './backtest.schemas';

// Worker Status Schema
export const WorkerStatusSchema = z.object({
  name: z.string(),
  status: z.enum(['running', 'stopped', 'error', 'restarting']),
  pid: z.number().optional(),
  start_time: z.date().optional(),
  last_activity: z.date().optional(),
  error_message: z.string().optional(),
});

type WorkerStatus = z.infer<typeof WorkerStatusSchema>;

// Worker Stats Schema
export const WorkerStatsSchema = z.object({
  manager_status: z.enum(['running', 'stopped', 'starting', 'stopping']),
  uptime_seconds: z.number(),
  start_time: z.string(),
  active_workers: z.number(),
  active_tasks: z.number(),
  worker_stats: z.record(z.string(), z.object({
    status: z.string(),
    running_jobs: z.number().optional(),
    max_concurrent: z.number().optional(),
    poll_interval: z.number().optional(),
    job_ids: z.array(z.string()).optional(),
  })),
});

type WorkerStats = z.infer<typeof WorkerStatsSchema>;

// Worker Health Check Schema
export const WorkerHealthCheckSchema = z.object({
  healthy: z.boolean(),
  timestamp: z.date(),
  workers: z.record(z.string(), z.object({
    healthy: z.boolean(),
    status: z.string().optional(),
    reason: z.string().optional(),
  })),
  uptime: z.number(),
  error: z.string().optional(),
});

type WorkerHealthCheck = z.infer<typeof WorkerHealthCheckSchema>;

// File Upload Session Schema
export const FileUploadSessionSchema = z.object({
  id: z.string(),
  user_id: z.string(),
  original_filename: z.string(),
  file_size: z.number(),
  status: z.enum(['pending', 'mapping_confirmed', 'parsing_in_progress', 'ready', 'error']),
  final_mapping: z.record(z.string(), z.string()).optional(),
  timezone: z.string().optional(),
  rows_processed: z.number().optional(),
  error_message: z.string().optional(),
  created_at: z.date(),
  updated_at: z.date(),
  temporary_file_path: z.string().optional(),
});

type FileUploadSession = z.infer<typeof FileUploadSessionSchema>;

// Backtest Job Schema
export const BacktestJobSchema = z.object({
  id: z.string(),
  user_id: z.string(),
  name: z.string(),
  status: z.enum(['pending', 'running', 'completed', 'error', 'cancelled']),
  symbol: z.string(),
  start_date: z.date(),
  end_date: z.date(),
  strategy_config: z.record(z.string(), z.any()),
  progress: z.number().min(0).max(100).optional(),
  started_at: z.date().optional(),
  completed_at: z.date().optional(),
  error_message: z.string().optional(),
  results: z.record(z.string(), z.any()).optional(),
  created_at: z.date(),
});

type BacktestJob = z.infer<typeof BacktestJobSchema>;

// DGM Experiment Schema
export const DGMExperimentSchema = z.object({
  id: z.string(),
  user_id: z.string(),
  experiment_name: z.string(),
  status: z.enum(['pending', 'running', 'completed', 'error', 'deployed']),
  base_strategy: z.record(z.string(), z.any()),
  generated_strategy: z.record(z.string(), z.any()).optional(),
  fitness_improvement: z.number().optional(),
  deployed_backtest_id: z.string().optional(),
  started_at: z.date().optional(),
  completed_at: z.date().optional(),
  error_message: z.string().optional(),
  created_at: z.date(),
});

type DGMExperiment = z.infer<typeof DGMExperimentSchema>;

// Worker Management Request Schema
export const WorkerManagementRequestSchema = z.object({
  action: z.enum(['status', 'health', 'restart', 'stats']),
  worker_name: z.string().optional(),
  timestamp: z.date(),
  request_id: z.string(),
});

type WorkerManagementRequest = z.infer<typeof WorkerManagementRequestSchema>;

// Worker Management Response Schema
export const WorkerManagementResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.object({
    code: z.string(),
    message: z.string(),
    details: z.string().optional(),
  }).optional(),
  timestamp: z.date(),
  request_id: z.string(),
});

type WorkerManagementResponse = z.infer<typeof WorkerManagementResponseSchema>;

// Worker Bridge Status Schema
export const WorkerBridgeStatusSchema = z.object({
  bridge_healthy: z.boolean(),
  python_workers_healthy: z.boolean(),
  last_health_check: z.date().optional(),
  last_known_status: WorkerStatsSchema.optional(),
  monitoring_config: z.object({
    health_check_interval: z.number(),
    status_poll_interval: z.number(),
    auto_restart: z.boolean(),
  }),
  active_jobs: z.object({
    file_parsing: z.number(),
    backtests: z.number(),
    dgm_experiments: z.number(),
  }),
});

type WorkerBridgeStatus = z.infer<typeof WorkerBridgeStatusSchema>;

// File Processing Progress Schema
export const FileProcessingProgressSchema = z.object({
  session_id: z.string(),
  status: z.enum(['pending', 'parsing', 'validating', 'inserting', 'completed', 'error']),
  progress_percent: z.number().min(0).max(100),
  rows_processed: z.number(),
  total_rows: z.number().optional(),
  current_step: z.string(),
  error_message: z.string().optional(),
  timestamp: z.date(),
});

type FileProcessingProgress = z.infer<typeof FileProcessingProgressSchema>;

// Backtest Progress Schema - Using the one from backtest.schemas.ts to avoid conflicts
// export const BacktestProgressSchema = z.object({
//   backtest_id: z.string(),
//   status: z.enum(['pending', 'initializing', 'running', 'analyzing', 'completed', 'error']),
//   progress_percent: z.number().min(0).max(100),
//   current_step: z.string(),
//   trades_processed: z.number().optional(),
//   total_trades: z.number().optional(),
//   elapsed_time: z.number().optional(),
//   estimated_completion: z.date().optional(),
//   error_message: z.string().optional(),
//   timestamp: z.date(),
// });

// export type BacktestProgress = z.infer<typeof BacktestProgressSchema>;

// DGM Experiment Progress Schema
export const DGMExperimentProgressSchema = z.object({
  experiment_id: z.string(),
  status: z.enum(['pending', 'initializing', 'evolving', 'testing', 'completed', 'error']),
  progress_percent: z.number().min(0).max(100),
  current_generation: z.number().optional(),
  total_generations: z.number().optional(),
  best_fitness: z.number().optional(),
  fitness_improvement: z.number().optional(),
  current_step: z.string(),
  elapsed_time: z.number().optional(),
  estimated_completion: z.date().optional(),
  error_message: z.string().optional(),
  timestamp: z.date(),
});

type DGMExperimentProgress = z.infer<typeof DGMExperimentProgressSchema>;

// Worker Event Schema
export const WorkerEventSchema = z.object({
  event_type: z.enum([
    'worker_started',
    'worker_stopped', 
    'worker_error',
    'worker_restarted',
    'job_started',
    'job_completed',
    'job_failed',
    'job_progress',
    'health_check',
    'status_update'
  ]),
  worker_name: z.string().optional(),
  job_id: z.string().optional(),
  job_type: z.enum(['file_parsing', 'backtest', 'dgm_experiment']).optional(),
  data: z.record(z.string(), z.any()).optional(),
  timestamp: z.date(),
});

type WorkerEvent = z.infer<typeof WorkerEventSchema>;

// Worker Configuration Schema
export const WorkerConfigSchema = z.object({
  file_parser: z.object({
    poll_interval: z.number().default(10),
    max_file_size: z.number().default(100 * 1024 * 1024), // 100MB
    chunk_size: z.number().default(10000),
    supported_formats: z.array(z.string()).default(['.csv', '.xlsx', '.json']),
  }),
  backtest_runner: z.object({
    poll_interval: z.number().default(10),
    max_concurrent: z.number().default(3),
    timeout_hours: z.number().default(24),
  }),
  dgm_monitor: z.object({
    poll_interval: z.number().default(20),
    max_concurrent: z.number().default(2),
    enabled: z.boolean().default(false),
    fitness_threshold: z.number().default(0.05),
    timeout_hours: z.number().default(1),
  }),
  general: z.object({
    log_level: z.enum(['DEBUG', 'INFO', 'WARNING', 'ERROR']).default('INFO'),
    health_check_interval: z.number().default(30),
    status_poll_interval: z.number().default(60),
    auto_restart: z.boolean().default(true),
  }),
});

type WorkerConfig = z.infer<typeof WorkerConfigSchema>;

// System Resource Usage Schema
export const SystemResourceUsageSchema = z.object({
  cpu_percent: z.number().min(0).max(100),
  memory_percent: z.number().min(0).max(100),
  memory_used_mb: z.number(),
  disk_usage_percent: z.number().min(0).max(100),
  active_connections: z.number(),
  timestamp: z.date(),
});

type SystemResourceUsage = z.infer<typeof SystemResourceUsageSchema>;

// Export all schemas
export const WorkerSchemas = {
  WorkerStatus: WorkerStatusSchema,
  WorkerStats: WorkerStatsSchema,
  WorkerHealthCheck: WorkerHealthCheckSchema,
  FileUploadSession: FileUploadSessionSchema,
  BacktestJob: BacktestJobSchema,
  DGMExperiment: DGMExperimentSchema,
  WorkerManagementRequest: WorkerManagementRequestSchema,
  WorkerManagementResponse: WorkerManagementResponseSchema,
  WorkerBridgeStatus: WorkerBridgeStatusSchema,
  FileProcessingProgress: FileProcessingProgressSchema,
  BacktestProgress: BacktestProgressSchema,
  DGMExperimentProgress: DGMExperimentProgressSchema,
  WorkerEvent: WorkerEventSchema,
  WorkerConfig: WorkerConfigSchema,
  SystemResourceUsage: SystemResourceUsageSchema,
};

// Export all types (excluding conflicting ones)
export type {
  WorkerStatus,
  WorkerStats,
  WorkerHealthCheck,
  FileUploadSession,
  BacktestJob,
  DGMExperiment,
  WorkerManagementRequest,
  WorkerManagementResponse,
  WorkerBridgeStatus,
  FileProcessingProgress,
  // BacktestProgress, // Conflicts with backtest.schemas
  DGMExperimentProgress,
  WorkerEvent,
  WorkerConfig,
  SystemResourceUsage,
};