# MT5 Bridge TDD Implementation

This directory contains a Test-Driven Development (TDD) implementation of an MT5 Bridge for the AI Enhanced Trading Platform.

## Overview

The MT5 Bridge provides a clean interface to interact with MetaTrader 5, allowing trading strategies to place orders, manage positions, and monitor market data without directly dealing with the MT5 API complexities.

## Files

- `src/trading/mt5_bridge_tdd.py`: The main MT5 Bridge implementation
- `tests/test_mt5_bridge_tdd.py`: Basic TDD tests for the MT5 Bridge
- `tests/test_mt5_bridge_property.py`: Property-based tests using hypothesis
- `tests/test_mt5_bridge_integration.py`: Integration tests with a simple trading strategy
- `run_mt5_tdd_tests.py`: Script to run all MT5 Bridge TDD tests
- `test_mt5_bridge_standalone.py`: Standalone test script that doesn't require pytest

## Features

- Connect/disconnect to MT5 terminal
- Place market and pending orders
- Get order status and manage positions
- Close orders and positions
- Offline mode for testing without MT5 terminal
- Comprehensive error handling
- Logging of all operations

## Testing Approach

The MT5 Bridge was developed using a TDD approach:

1. **Basic Tests**: Simple unit tests for core functionality
2. **Property-Based Tests**: Using hypothesis to generate test data and verify properties
3. **State Machine Tests**: Testing complex state transitions and invariants
4. **Integration Tests**: Testing with a simple trading strategy

## Mock Implementation

For testing without a live MT5 connection, we use a mock implementation:

```python
class MT5BridgeMock:
    def __init__(self):
        self.connected = True
        self.orders = []
        self.simulate_error = False
    
    def connect(self):
        self.connected = True
        return True
    
    def is_connected(self):
        return self.connected
    
    def place_order(self, symbol, order_type, lot, price=None, stop_loss=None, take_profit=None):
        if self.simulate_error:
            raise Exception("API error")
        
        if not self.connected:
            self.connect()
        
        if symbol == "INVALID":
            raise ValueError("Invalid symbol")
        
        order_id = len(self.orders) + 1
        self.orders.append({
            "id": order_id,
            "symbol": symbol,
            "type": order_type,
            "lot": lot,
            "price": price,
            "status": "filled"
        })
        return order_id
    
    def get_order_status(self, order_id):
        for order in self.orders:
            if order["id"] == order_id:
                return order["status"]
        return "not_found"
    
    def close_order(self, order_id):
        for order in self.orders:
            if order["id"] == order_id:
                order["status"] = "closed"
                return True
        return False
    
    def simulate_connection_loss(self):
        self.connected = False
    
    def simulate_api_error(self):
        self.simulate_error = True
```

## Usage Example

```python
from src.trading.mt5_bridge_tdd import MT5Bridge

# Create MT5 Bridge instance
bridge = MT5Bridge(offline_mode=True)  # Use offline_mode=False for live trading

# Connect to MT5
bridge.connect()

# Place a market buy order
order_id = bridge.place_order(
    symbol="EURUSD",
    order_type="BUY",
    lot=0.1
)

# Get order status
status = bridge.get_order_status(order_id)
print(f"Order {order_id} status: {status}")

# Get all positions
positions = bridge.get_positions()
for pos in positions:
    print(f"Position: {pos['symbol']} {pos['type']} {pos['lot']}")

# Close the order
bridge.close_order(order_id)

# Disconnect from MT5
bridge.disconnect()
```

## Running Tests

### Using pytest (recommended)

To run the tests with pytest, make sure you have pytest and hypothesis installed:

```bash
pip install pytest hypothesis
```

Then run the test script:

```bash
python run_mt5_tdd_tests.py
```

Or run individual test files:

```bash
pytest tests/test_mt5_bridge_tdd.py -v
pytest tests/test_mt5_bridge_property.py -v
pytest tests/test_mt5_bridge_integration.py -v
```

### Using standalone test script

If you don't have pytest installed, you can run the standalone test script:

```bash
python test_mt5_bridge_standalone.py
```

This script includes the core tests for the MT5 Bridge and doesn't require any external dependencies.