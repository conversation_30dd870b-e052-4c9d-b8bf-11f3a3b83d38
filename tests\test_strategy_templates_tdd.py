"""
TDD Tests for Strategy Template System
Pure Test-Driven Development approach - Tests written first!
"""

import pytest
from unittest.mock import Mock, patch
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.chatbot.models import StrategyType, StrategyTemplate, GeneratedStrategy, TestCase
from src.chatbot.strategy_template_manager import StrategyTemplateManager, TemplateNotFoundError, TemplateValidationError


class TestStrategyTemplateManager:
    """Test Strategy Template Manager with TDD approach"""
    
    def test_should_provide_ml_strategy_template(self):
        """RED: Test template generation for ML strategies"""
        template_manager = StrategyTemplateManager()
        
        ml_template = template_manager.get_template("machine_learning_basic")
        
        assert ml_template is not None
        assert "sklearn" in ml_template.code
        assert "def trading_strategy(" in ml_template.code
        assert ml_template.required_libraries == ["pandas", "numpy", "sklearn"]
        assert ml_template.description.startswith("Basic ML strategy")
    
    def test_should_validate_template_completeness(self):
        """RED: Ensure templates are production-ready"""
        template_manager = StrategyTemplateManager()
        
        for template_name in template_manager.list_templates():
            template = template_manager.get_template(template_name)
            
            # Every template must be testable
            assert template.has_unit_tests == True
            assert template.backtest_ready == True
            assert len(template.example_params) > 0
    
    def test_should_provide_momentum_strategy_template(self):
        """RED: Test momentum strategy template"""
        template_manager = StrategyTemplateManager()
        
        momentum_template = template_manager.get_template("momentum_macd")
        
        assert momentum_template is not None
        assert momentum_template.strategy_type == StrategyType.MOMENTUM
        assert "macd" in momentum_template.code.lower()
        assert "def trading_strategy(" in momentum_template.code
        assert momentum_template.difficulty_level in ["beginner", "intermediate", "advanced"]
    
    def test_should_provide_mean_reversion_template(self):
        """RED: Test mean reversion template"""
        template_manager = StrategyTemplateManager()
        
        mean_rev_template = template_manager.get_template("mean_reversion_rsi")
        
        assert mean_rev_template is not None
        assert mean_rev_template.strategy_type == StrategyType.MEAN_REVERSION
        assert "rsi" in mean_rev_template.code.lower()
        assert mean_rev_template.required_libraries == ["pandas", "numpy", "talib"]
    
    def test_should_customize_template_parameters(self):
        """RED: Test template parameter customization"""
        template_manager = StrategyTemplateManager()
        
        template = template_manager.get_template("momentum_macd")
        custom_params = {
            "symbols": ["GBPUSD", "EURUSD"],
            "macd_fast": 8,
            "macd_slow": 21,
            "risk_per_trade": 0.01
        }
        
        customized = template_manager.customize_template(template, custom_params)
        
        assert isinstance(customized, GeneratedStrategy)
        assert "GBPUSD" in customized.code
        assert "EURUSD" in customized.code
        assert "macd_fast = 8" in customized.code or "macd_fast: int = 8" in customized.code
        assert len(customized.test_cases) > 0
    
    def test_should_generate_template_with_tests(self):
        """RED: Test template test generation"""
        template_manager = StrategyTemplateManager()
        
        template = template_manager.get_template("mean_reversion_rsi")
        customized = template_manager.customize_template(template, {"symbols": ["EURUSD"]})
        
        assert len(customized.test_cases) >= 2  # At least basic + strategy-specific test
        
        # Check test case structure
        for test_case in customized.test_cases:
            assert isinstance(test_case, TestCase)
            assert test_case.name.startswith("test_")
            assert len(test_case.description) > 0
            assert len(test_case.test_code) > 0
            assert test_case.expected_signal in ["buy", "sell", "hold"]
    
    def test_should_filter_templates_by_difficulty(self):
        """RED: Test template filtering by difficulty"""
        template_manager = StrategyTemplateManager()
        
        beginner_templates = template_manager.get_templates_by_difficulty("beginner")
        intermediate_templates = template_manager.get_templates_by_difficulty("intermediate")
        advanced_templates = template_manager.get_templates_by_difficulty("advanced")
        
        assert len(beginner_templates) > 0
        assert len(intermediate_templates) > 0
        assert len(advanced_templates) > 0
        
        # Verify filtering works correctly
        for template in beginner_templates:
            assert template.difficulty_level == "beginner"
    
    def test_should_filter_templates_by_strategy_type(self):
        """RED: Test template filtering by strategy type"""
        template_manager = StrategyTemplateManager()
        
        ml_templates = template_manager.get_templates_by_strategy_type(StrategyType.MACHINE_LEARNING)
        momentum_templates = template_manager.get_templates_by_strategy_type(StrategyType.MOMENTUM)
        
        assert len(ml_templates) > 0
        assert len(momentum_templates) > 0
        
        # Verify filtering works correctly
        for template in ml_templates:
            assert template.strategy_type == StrategyType.MACHINE_LEARNING
    
    def test_should_search_templates_by_keyword(self):
        """RED: Test template search functionality"""
        template_manager = StrategyTemplateManager()
        
        # Search by strategy name
        rsi_templates = template_manager.search_templates("rsi")
        assert len(rsi_templates) > 0
        
        # Search by description
        ml_templates = template_manager.search_templates("ML")
        assert len(ml_templates) > 0
        
        # Search by tags
        beginner_templates = template_manager.search_templates("beginner")
        assert len(beginner_templates) > 0
    
    def test_should_validate_template_code_syntax(self):
        """RED: Test template code syntax validation"""
        template_manager = StrategyTemplateManager()
        
        for template_name in template_manager.list_templates():
            template = template_manager.get_template(template_name)
            
            # Template code should be syntactically valid
            try:
                compile(template.code, f"<template_{template_name}>", "exec")
            except SyntaxError:
                pytest.fail(f"Template {template_name} has syntax errors")
    
    def test_should_provide_template_documentation(self):
        """RED: Test template documentation generation"""
        template_manager = StrategyTemplateManager()
        
        template = template_manager.get_template("momentum_macd")
        customized = template_manager.customize_template(template, {"symbols": ["EURUSD"]})
        
        assert len(customized.documentation) > 0
        assert "MACD" in customized.documentation
        assert "momentum" in customized.documentation.lower()
        assert "parameters" in customized.documentation.lower()
    
    def test_should_handle_invalid_template_name(self):
        """RED: Test invalid template name handling"""
        template_manager = StrategyTemplateManager()
        
        with pytest.raises(TemplateNotFoundError, match="Template 'nonexistent' not found"):
            template_manager.get_template("nonexistent")
    
    def test_should_validate_custom_parameters(self):
        """RED: Test custom parameter validation"""
        template_manager = StrategyTemplateManager()
        
        template = template_manager.get_template("mean_reversion_rsi")
        
        # Invalid parameters should raise error
        invalid_params = {
            "rsi_period": -5,  # Invalid negative period
            "risk_per_trade": 1.5  # Invalid risk > 100%
        }
        
        with pytest.raises(TemplateValidationError, match="Invalid parameter values"):
            template_manager.customize_template(template, invalid_params)
    
    def test_should_provide_breakout_strategy_template(self):
        """RED: Test breakout strategy template"""
        template_manager = StrategyTemplateManager()
        
        breakout_template = template_manager.get_template("bollinger_breakout")
        
        assert breakout_template is not None
        assert breakout_template.strategy_type == StrategyType.BREAKOUT
        assert "bollinger" in breakout_template.code.lower()
        assert "breakout" in breakout_template.description.lower()
    
    def test_should_provide_grid_trading_template(self):
        """RED: Test grid trading template"""
        template_manager = StrategyTemplateManager()
        
        grid_template = template_manager.get_template("grid_trading_basic")
        
        assert grid_template is not None
        assert grid_template.strategy_type == StrategyType.GRID_TRADING
        assert "grid" in grid_template.code.lower()
        assert grid_template.difficulty_level == "advanced"
    
    def test_should_provide_pairs_trading_template(self):
        """RED: Test pairs trading template"""
        template_manager = StrategyTemplateManager()
        
        pairs_template = template_manager.get_template("pairs_trading_correlation")
        
        assert pairs_template is not None
        assert pairs_template.strategy_type == StrategyType.PAIRS_TRADING
        assert "correlation" in pairs_template.code.lower()
        assert len(pairs_template.required_symbols) >= 2  # Pairs need at least 2 symbols


class TestTemplateCustomization:
    """Test template customization functionality"""
    
    def test_should_customize_risk_parameters(self):
        """RED: Test risk parameter customization"""
        template_manager = StrategyTemplateManager()
        
        template = template_manager.get_template("momentum_macd")
        custom_params = {
            "risk_per_trade": 0.005,  # 0.5% risk
            "max_positions": 2,
            "stop_loss_pips": 50,
            "take_profit_pips": 100
        }
        
        customized = template_manager.customize_template(template, custom_params)
        
        assert "risk_per_trade = 0.005" in customized.code or "risk_per_trade: float = 0.005" in customized.code
        assert "max_positions = 2" in customized.code or "max_positions: int = 2" in customized.code
    
    def test_should_customize_indicator_parameters(self):
        """RED: Test indicator parameter customization"""
        template_manager = StrategyTemplateManager()
        
        template = template_manager.get_template("mean_reversion_rsi")
        custom_params = {
            "rsi_period": 21,
            "oversold_level": 25,
            "overbought_level": 75
        }
        
        customized = template_manager.customize_template(template, custom_params)
        
        assert "rsi_period = 21" in customized.code or "rsi_period: int = 21" in customized.code
        assert "oversold_level = 25" in customized.code or "oversold_level: float = 25" in customized.code
    
    def test_should_customize_symbol_list(self):
        """RED: Test symbol list customization"""
        template_manager = StrategyTemplateManager()
        
        template = template_manager.get_template("momentum_macd")
        custom_params = {
            "symbols": ["GBPUSD", "USDJPY", "AUDUSD"]
        }
        
        customized = template_manager.customize_template(template, custom_params)
        
        assert "GBPUSD" in customized.code
        assert "USDJPY" in customized.code
        assert "AUDUSD" in customized.code
    
    def test_should_preserve_template_structure(self):
        """RED: Test template structure preservation"""
        template_manager = StrategyTemplateManager()
        
        template = template_manager.get_template("machine_learning_basic")
        custom_params = {"symbols": ["EURUSD"]}
        
        customized = template_manager.customize_template(template, custom_params)
        
        # Should preserve core structure
        assert "def trading_strategy(" in customized.code
        assert "return {" in customized.code
        assert "'signal':" in customized.code
        assert "'confidence':" in customized.code
    
    def test_should_generate_appropriate_test_cases(self):
        """RED: Test appropriate test case generation"""
        template_manager = StrategyTemplateManager()
        
        # ML template should have ML-specific tests
        ml_template = template_manager.get_template("machine_learning_basic")
        ml_customized = template_manager.customize_template(ml_template, {"symbols": ["EURUSD"]})
        
        ml_test_names = [test.name for test in ml_customized.test_cases]
        assert any("ml" in name.lower() or "model" in name.lower() for name in ml_test_names)
        
        # Mean reversion template should have mean reversion tests
        mr_template = template_manager.get_template("mean_reversion_rsi")
        mr_customized = template_manager.customize_template(mr_template, {"symbols": ["EURUSD"]})
        
        mr_test_names = [test.name for test in mr_customized.test_cases]
        assert any("reversion" in name.lower() or "rsi" in name.lower() for name in mr_test_names)


class TestTemplateValidation:
    """Test template validation and quality assurance"""
    
    def test_should_validate_all_templates_are_executable(self):
        """RED: Test all templates are executable"""
        template_manager = StrategyTemplateManager()
        
        for template_name in template_manager.list_templates():
            template = template_manager.get_template(template_name)
            
            # Should be able to customize without errors
            try:
                customized = template_manager.customize_template(template, {"symbols": ["EURUSD"]})
                assert customized is not None
            except Exception as e:
                pytest.fail(f"Template {template_name} failed customization: {e}")
    
    def test_should_validate_template_dependencies(self):
        """RED: Test template dependency validation"""
        template_manager = StrategyTemplateManager()
        
        for template_name in template_manager.list_templates():
            template = template_manager.get_template(template_name)
            
            # Required libraries should be specified
            assert len(template.required_libraries) > 0
            
            # Should include basic dependencies
            assert "pandas" in template.required_libraries
            assert "numpy" in template.required_libraries
    
    def test_should_validate_template_performance_estimates(self):
        """RED: Test template performance estimates"""
        template_manager = StrategyTemplateManager()
        
        for template_name in template_manager.list_templates():
            template = template_manager.get_template(template_name)
            
            # Should have performance estimates
            assert "sharpe_ratio" in template.estimated_performance
            assert "max_drawdown" in template.estimated_performance
            assert "win_rate" in template.estimated_performance
            
            # Estimates should be reasonable
            assert 0 <= template.estimated_performance["win_rate"] <= 1
            assert 0 <= template.estimated_performance["max_drawdown"] <= 1
    
    def test_should_validate_template_tags(self):
        """RED: Test template tag validation"""
        template_manager = StrategyTemplateManager()
        
        for template_name in template_manager.list_templates():
            template = template_manager.get_template(template_name)
            
            # Should have meaningful tags
            assert len(template.tags) > 0
            
            # Should include difficulty level in tags
            assert template.difficulty_level in template.tags
    
    def test_should_validate_template_code_quality(self):
        """RED: Test template code quality"""
        template_manager = StrategyTemplateManager()
        
        for template_name in template_manager.list_templates():
            template = template_manager.get_template(template_name)
            
            # Code should have proper structure
            assert "def trading_strategy" in template.code
            assert "return" in template.code
            
            # Should have comments/documentation
            assert "#" in template.code or '"""' in template.code
            
            # Should not have hardcoded values (should use parameters)
            lines = template.code.split('\n')
            hardcoded_numbers = [line for line in lines if any(char.isdigit() for char in line) 
                               and "params" not in line and "#" not in line]
            # Allow some hardcoded values but not too many
            assert len(hardcoded_numbers) < 25  # More lenient for template code


class TestTemplateIntegration:
    """Test template integration with other systems"""
    
    def test_should_integrate_with_strategy_builder(self):
        """RED: Test integration with strategy builder"""
        template_manager = StrategyTemplateManager()
        
        # This test will require the StrategyBuilder to be implemented
        # For now, we'll test the interface
        template = template_manager.get_template("momentum_macd")
        customized = template_manager.customize_template(template, {"symbols": ["EURUSD"]})
        
        # Generated code should be valid for strategy builder
        assert customized.code is not None
        assert len(customized.code) > 0
        assert "def trading_strategy" in customized.code
    
    def test_should_provide_backtest_ready_code(self):
        """RED: Test backtest-ready code generation"""
        template_manager = StrategyTemplateManager()
        
        for template_name in template_manager.list_templates():
            template = template_manager.get_template(template_name)
            customized = template_manager.customize_template(template, {"symbols": ["EURUSD"]})
            
            # Should be backtest ready
            assert template.backtest_ready == True
            
            # Should have proper return format for backtesting
            assert "return {" in customized.code
            assert "'signal'" in customized.code
            assert "'confidence'" in customized.code