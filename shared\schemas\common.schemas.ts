import { z } from 'zod';

// Common ID schemas
export const IdSchema = z.string().uuid();
export type Id = z.infer<typeof IdSchema>;

// Pagination schemas
export const PaginationRequestSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});
export type PaginationRequest = z.infer<typeof PaginationRequestSchema>;

export const PaginationResponseSchema = z.object({
  page: z.number().int().min(1),
  limit: z.number().int().min(1),
  total: z.number().int().min(0),
  totalPages: z.number().int().min(0),
  hasNext: z.boolean(),
  hasPrev: z.boolean(),
});
export type PaginationResponse = z.infer<typeof PaginationResponseSchema>;

// API Response schemas
export const ApiErrorSchema = z.object({
  code: z.string(),
  message: z.string(),
  details: z.string().optional(),
  timestamp: z.date().default(() => new Date()),
});
export type ApiError = z.infer<typeof ApiErrorSchema>;

export const ApiResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.object({
    success: z.boolean(),
    data: dataSchema.optional(),
    error: ApiErrorSchema.optional(),
    timestamp: z.date().default(() => new Date()),
  });

export type ApiResponse<T = unknown> = {
  success: boolean;
  data?: T;
  error?: ApiError;
  timestamp: Date;
};

// Date range schema
export const DateRangeSchema = z.object({
  startDate: z.date(),
  endDate: z.date(),
}).refine(
  (data) => data.startDate <= data.endDate,
  {
    message: "Start date must be before or equal to end date",
    path: ["endDate"],
  }
);
export type DateRange = z.infer<typeof DateRangeSchema>;

// File upload schemas
export const FileUploadSchema = z.object({
  filename: z.string().min(1),
  mimetype: z.string(),
  size: z.number().int().min(0),
  buffer: z.instanceof(Buffer).optional(),
});
export type FileUpload = z.infer<typeof FileUploadSchema>;

// Search schemas
export const SearchRequestSchema = z.object({
  query: z.string().min(1),
  filters: z.record(z.string(), z.any()).optional(),
  ...PaginationRequestSchema.shape,
});
export type SearchRequest = z.infer<typeof SearchRequestSchema>;

// Status schemas
export const StatusSchema = z.enum(['active', 'inactive', 'pending', 'completed', 'error']);
export type Status = z.infer<typeof StatusSchema>;