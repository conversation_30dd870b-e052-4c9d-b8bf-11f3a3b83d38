#!/usr/bin/env python3
"""
AI Trading Platform - Automated Test Execution Script
Provides comprehensive testing automation with detailed reporting
"""

import subprocess
import sys
import os
import json
import time
from datetime import datetime
from pathlib import Path

class TestExecutor:
    def __init__(self):
        self.project_root = Path.cwd()
        self.test_results = {}
        self.start_time = None

    def print_banner(self, text, char="="):
        """Print a formatted banner"""
        print(f"\n{char * 60}")
        print(f" {text}")
        print(f"{char * 60}\n")

    def run_command(self, command, description):
        """Run a command and capture output"""
        print(f"🔄 {description}...")
        try:
            result = subprocess.run(
                command, 
                shell=True, 
                capture_output=True, 
                text=True,
                timeout=300  # 5 minute timeout
            )
            return result
        except subprocess.TimeoutExpired:
            print(f"❌ {description} timed out after 5 minutes")
            return None
        except Exception as e:
            print(f"❌ Error running {description}: {e}")
            return None

    def check_dependencies(self):
        """Check if required testing dependencies are installed"""
        self.print_banner("CHECKING DEPENDENCIES", "=")

        required_packages = [
            "pytest", "pytest-cov", "pytest-asyncio", 
            "pytest-mock", "coverage"
        ]

        missing_packages = []
        for package in required_packages:
            result = self.run_command(f"pip show {package}", f"Checking {package}")
            if result and result.returncode != 0:
                missing_packages.append(package)

        if missing_packages:
            print(f"❌ Missing packages: {', '.join(missing_packages)}")
            print("\n📦 Installing missing packages...")
            install_cmd = f"pip install {' '.join(missing_packages)}"
            result = self.run_command(install_cmd, "Installing dependencies")
            if result and result.returncode == 0:
                print("✅ Dependencies installed successfully")
            else:
                print("❌ Failed to install dependencies")
                return False
        else:
            print("✅ All dependencies are installed")

        return True

    def run_basic_tests(self):
        """Run basic test suite"""
        self.print_banner("RUNNING BASIC TESTS", "=")

        # Run tests with basic output
        result = self.run_command(
            "python -m pytest tests/ -v --tb=short",
            "Basic test execution"
        )

        if result:
            print("\n📊 Basic Test Results:")
            print(result.stdout)
            if result.stderr:
                print("\n⚠️  Warnings/Errors:")
                print(result.stderr)

            self.test_results['basic_tests'] = {
                'returncode': result.returncode,
                'passed': result.returncode == 0
            }

        return result and result.returncode == 0

    def run_coverage_tests(self):
        """Run tests with coverage analysis"""
        self.print_banner("RUNNING COVERAGE ANALYSIS", "=")

        # Run tests with coverage
        result = self.run_command(
            "python -m pytest tests/ --cov=src --cov-report=term-missing --cov-report=html --cov-report=json",
            "Coverage analysis"
        )

        if result:
            print("\n📈 Coverage Results:")
            print(result.stdout)

            # Try to read coverage data
            try:
                if os.path.exists("coverage.json"):
                    with open("coverage.json", "r") as f:
                        coverage_data = json.load(f)
                        total_coverage = coverage_data.get("totals", {}).get("percent_covered", 0)
                        print(f"\n🎯 Total Coverage: {total_coverage:.1f}%")

                        self.test_results['coverage'] = {
                            'total_coverage': total_coverage,
                            'html_report': 'htmlcov/index.html'
                        }
            except Exception as e:
                print(f"⚠️  Could not read coverage data: {e}")

        return result and result.returncode == 0

    def run_specific_test_categories(self):
        """Run tests by category"""
        self.print_banner("RUNNING CATEGORIZED TESTS", "=")

        test_categories = {
            "Security Tests": "tests/test_secure_executor.py",
            "ML Model Tests": "tests/test_ml_models.py", 
            "Data Pipeline Tests": "tests/test_data_pipeline.py",
            "Trading Service Tests": "tests/test_trading_services.py",
            "Integration Tests": "tests/test_integration.py"
        }

        category_results = {}

        for category, test_file in test_categories.items():
            if os.path.exists(test_file):
                print(f"\n🧪 Running {category}...")
                result = self.run_command(
                    f"python -m pytest {test_file} -v",
                    f"{category}"
                )

                if result:
                    passed = result.returncode == 0
                    category_results[category] = passed
                    status = "✅ PASSED" if passed else "❌ FAILED"
                    print(f"{status} - {category}")
                else:
                    category_results[category] = False
                    print(f"❌ ERROR - {category}")
            else:
                print(f"⚠️  SKIPPED - {category} (file not found)")
                category_results[category] = None

        self.test_results['categories'] = category_results
        return category_results

    def generate_report(self):
        """Generate a comprehensive test report"""
        self.print_banner("TEST EXECUTION SUMMARY", "=")

        end_time = time.time()
        duration = end_time - self.start_time if self.start_time else 0

        print(f"🕐 Execution Time: {duration:.1f} seconds")
        print(f"📅 Completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # Basic test results
        if 'basic_tests' in self.test_results:
            status = "✅ PASSED" if self.test_results['basic_tests']['passed'] else "❌ FAILED"
            print(f"\n🧪 Basic Tests: {status}")

        # Coverage results
        if 'coverage' in self.test_results:
            coverage = self.test_results['coverage']['total_coverage']
            print(f"📊 Test Coverage: {coverage:.1f}%")

            if coverage < 40:
                print("🔴 Coverage is critically low - immediate attention needed")
            elif coverage < 70:
                print("🟡 Coverage needs improvement")
            else:
                print("🟢 Coverage is good")

        # Category results
        if 'categories' in self.test_results:
            print("\n📋 Test Categories:")
            for category, result in self.test_results['categories'].items():
                if result is True:
                    print(f"  ✅ {category}")
                elif result is False:
                    print(f"  ❌ {category}")
                else:
                    print(f"  ⚠️  {category} (skipped)")

        # Next steps
        print("\n🚀 Next Steps:")
        if 'coverage' in self.test_results:
            coverage = self.test_results['coverage']['total_coverage']
            if coverage < 90:
                print(f"  1. Improve test coverage from {coverage:.1f}% to 90%+")

        failed_categories = [cat for cat, result in self.test_results.get('categories', {}).items() if result is False]
        if failed_categories:
            print(f"  2. Fix failing test categories: {', '.join(failed_categories)}")

        print("  3. Review HTML coverage report: htmlcov/index.html")
        print("  4. Address any security test failures immediately")

        # Save results to file
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(report_file, 'w') as f:
                json.dump({
                    'timestamp': datetime.now().isoformat(),
                    'duration': duration,
                    'results': self.test_results
                }, f, indent=2)
            print(f"\n💾 Detailed report saved to: {report_file}")
        except Exception as e:
            print(f"⚠️  Could not save report: {e}")

    def run_all_tests(self):
        """Execute the complete test suite"""
        self.start_time = time.time()

        print("🚀 AI Trading Platform - Automated Test Execution")
        print(f"📁 Project Root: {self.project_root}")

        # Step 1: Check dependencies
        if not self.check_dependencies():
            print("❌ Cannot proceed without required dependencies")
            return False

        # Step 2: Run basic tests
        basic_success = self.run_basic_tests()

        # Step 3: Run coverage analysis
        coverage_success = self.run_coverage_tests()

        # Step 4: Run categorized tests
        self.run_specific_test_categories()

        # Step 5: Generate report
        self.generate_report()

        return basic_success and coverage_success

def main():
    """Main execution function"""
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        executor = TestExecutor()

        if command == "quick":
            executor.run_basic_tests()
        elif command == "coverage":
            executor.run_coverage_tests()
        elif command == "deps":
            executor.check_dependencies()
        else:
            print("Usage: python run_tests.py [quick|coverage|deps]")
            print("  quick    - Run basic tests only")
            print("  coverage - Run coverage analysis only") 
            print("  deps     - Check dependencies only")
            print("  (no args) - Run complete test suite")
    else:
        # Run complete test suite
        executor = TestExecutor()
        success = executor.run_all_tests()
        sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
