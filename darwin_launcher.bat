@echo off
REM 🧬 Darwin Strategy Verification Platform Launcher (Windows)
REM Hybrid Python Backend + Web Frontend

echo 🧬 Darwin Strategy Verification Platform
echo ========================================

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python 3 is required but not installed.
    echo Please install Python 3.8+ from python.org and try again.
    pause
    exit /b 1
)

REM Create virtual environment if it doesn't exist
if not exist "darwin_env" (
    echo 📦 Creating virtual environment...
    python -m venv darwin_env
)

REM Activate virtual environment
echo 🔧 Activating virtual environment...
call darwin_env\Scripts\activate.bat

REM Install dependencies
echo 📥 Installing Python dependencies...
pip install -r requirements.txt

REM Check if backend file exists
if not exist "darwin_backend.py" (
    echo ❌ darwin_backend.py not found!
    echo Please ensure the backend file is in the current directory.
    pause
    exit /b 1
)

REM Check if frontend file exists
if not exist "darwin_platform.html" (
    echo ❌ darwin_platform.html not found!
    echo Please ensure the frontend file is in the current directory.
    pause
    exit /b 1
)

REM Start the backend
echo 🚀 Starting Darwin Backend...
echo Backend will be available at: http://localhost:5000
echo Frontend will be available at: darwin_platform.html
echo.
echo 🔧 Backend Status:
echo - Real market data: ✅ Enabled
echo - Professional backtesting: ✅ Ready
echo - Monte Carlo simulations: ✅ Ready
echo - Technical indicators: ✅ Ready
echo.
echo 📊 Open darwin_platform.html in your browser to use the platform
echo Press Ctrl+C to stop the backend
echo.

REM Start Python backend
python darwin_backend.py

pause