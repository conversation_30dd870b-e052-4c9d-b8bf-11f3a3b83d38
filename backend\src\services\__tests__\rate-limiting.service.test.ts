import { describe, it, expect, beforeEach, jest, afterEach } from '@jest/globals';
import { RateLimitingService, RateLimitConfig } from '../rate-limiting.service';

describe('RateLimitingService', () => {
  let rateLimitingService: RateLimitingService;
  let mockCacheService: any;

  beforeEach(() => {
    mockCacheService = {
      get: jest.fn(),
      set: jest.fn(),
      delete: jest.fn(),
      exists: jest.fn(),
    };

    rateLimitingService = new RateLimitingService(mockCacheService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('checkRateLimit', () => {
    it('should allow request when under rate limit', async () => {
      // Arrange
      const config: RateLimitConfig = {
        windowMs: 60000, // 1 minute
        maxRequests: 100,
        keyGenerator: (identifier) => `rate_limit:${identifier}`,
      };
      const identifier = 'user_123';

      mockCacheService.get.mockResolvedValue(null); // No existing data

      // Act
      const result = await rateLimitingService.checkRateLimit(identifier, config);

      // Assert
      expect(result.allowed).toBe(true);
      expect(result.remaining).toBe(99);
      expect(result.resetTime).toBeInstanceOf(Date);
      expect(mockCacheService.set).toHaveBeenCalled();
    });

    it('should track multiple requests within window', async () => {
      // Arrange
      const config: RateLimitConfig = {
        windowMs: 60000,
        maxRequests: 100,
        keyGenerator: (identifier) => `rate_limit:${identifier}`,
      };
      const identifier = 'user_123';

      const existingData = {
        count: 5,
        resetTime: Date.now() + 30000, // 30 seconds from now
      };
      mockCacheService.get.mockResolvedValue(JSON.stringify(existingData));

      // Act
      const result = await rateLimitingService.checkRateLimit(identifier, config);

      // Assert
      expect(result.allowed).toBe(true);
      expect(result.remaining).toBe(94); // 100 - 6 (5 existing + 1 current)
      expect(result.current).toBe(6);
    });

    it('should deny request when rate limit exceeded', async () => {
      // Arrange
      const config: RateLimitConfig = {
        windowMs: 60000,
        maxRequests: 100,
        keyGenerator: (identifier) => `rate_limit:${identifier}`,
      };
      const identifier = 'user_123';

      const existingData = {
        count: 100,
        resetTime: Date.now() + 30000,
      };
      mockCacheService.get.mockResolvedValue(JSON.stringify(existingData));

      // Act
      const result = await rateLimitingService.checkRateLimit(identifier, config);

      // Assert
      expect(result.allowed).toBe(false);
      expect(result.remaining).toBe(0);
      expect(result.current).toBe(101); // 100 existing + 1 current request
      expect(result.retryAfter).toBeGreaterThan(0);
    });

    it('should reset window when expired', async () => {
      // Arrange
      const config: RateLimitConfig = {
        windowMs: 60000,
        maxRequests: 100,
        keyGenerator: (identifier) => `rate_limit:${identifier}`,
      };
      const identifier = 'user_123';

      const expiredData = {
        count: 100,
        resetTime: Date.now() - 1000, // 1 second ago (expired)
      };
      mockCacheService.get.mockResolvedValue(JSON.stringify(expiredData));

      // Act
      const result = await rateLimitingService.checkRateLimit(identifier, config);

      // Assert
      expect(result.allowed).toBe(true);
      expect(result.remaining).toBe(99);
      expect(result.current).toBe(1);
    });

    it('should handle cache errors gracefully', async () => {
      // Arrange
      const config: RateLimitConfig = {
        windowMs: 60000,
        maxRequests: 100,
        keyGenerator: (identifier) => `rate_limit:${identifier}`,
      };
      const identifier = 'user_123';

      mockCacheService.get.mockRejectedValue(new Error('Cache error'));

      // Act
      const result = await rateLimitingService.checkRateLimit(identifier, config);

      // Assert
      expect(result.allowed).toBe(true); // Fail open for availability
      expect(result.remaining).toBe(99);
    });
  });

  describe('API endpoint rate limiting', () => {
    it('should apply different limits for different endpoints', async () => {
      // Arrange
      const identifier = 'user_123';
      mockCacheService.get.mockResolvedValue(null);

      // Act
      const loginResult = await rateLimitingService.checkLoginRateLimit(identifier);
      const apiResult = await rateLimitingService.checkApiRateLimit(identifier);
      const backtestResult = await rateLimitingService.checkBacktestRateLimit(identifier);

      // Assert
      expect(loginResult.allowed).toBe(true);
      expect(apiResult.allowed).toBe(true);
      expect(backtestResult.allowed).toBe(true);

      // Different limits should be applied
      expect(loginResult.remaining).toBeLessThan(apiResult.remaining);
      expect(backtestResult.remaining).toBeLessThan(apiResult.remaining);
    });

    it('should enforce strict login rate limiting', async () => {
      // Arrange
      const identifier = 'user_123';
      const existingData = {
        count: 4, // One below login limit (5), so next request will exceed
        resetTime: Date.now() + 30000,
      };
      mockCacheService.get.mockResolvedValue(JSON.stringify(existingData));

      // Act
      const result = await rateLimitingService.checkLoginRateLimit(identifier);

      // Assert
      expect(result.allowed).toBe(false);
      expect(result.retryAfter).toBeGreaterThan(0);
    });

    it('should enforce backtest rate limiting', async () => {
      // Arrange
      const identifier = 'user_123';
      const existingData = {
        count: 9, // One below backtest limit (10), so next request will exceed
        resetTime: Date.now() + 30000,
      };
      mockCacheService.get.mockResolvedValue(JSON.stringify(existingData));

      // Act
      const result = await rateLimitingService.checkBacktestRateLimit(identifier);

      // Assert
      expect(result.allowed).toBe(false);
      expect(result.retryAfter).toBeGreaterThan(0);
    });
  });

  describe('IP-based rate limiting', () => {
    it('should track requests by IP address', async () => {
      // Arrange
      const ipAddress = '*************';
      mockCacheService.get.mockResolvedValue(null);

      // Act
      const result = await rateLimitingService.checkIpRateLimit(ipAddress);

      // Assert
      expect(result.allowed).toBe(true);
      expect(mockCacheService.set).toHaveBeenCalledWith(
        expect.stringContaining(ipAddress),
        expect.any(String),
        expect.any(Number)
      );
    });

    it('should block suspicious IP addresses', async () => {
      // Arrange
      const ipAddress = '*************';
      const existingData = {
        count: 999, // One below IP limit (1000), so next request will exceed
        resetTime: Date.now() + 30000,
      };
      mockCacheService.get.mockResolvedValue(JSON.stringify(existingData));

      // Act
      const result = await rateLimitingService.checkIpRateLimit(ipAddress);

      // Assert
      expect(result.allowed).toBe(false);
      expect(result.retryAfter).toBeGreaterThan(0);
    });
  });

  describe('subscription tier rate limiting', () => {
    it('should apply different limits based on subscription tier', async () => {
      // Arrange
      const identifier = 'user_123';
      mockCacheService.get.mockResolvedValue(null);

      // Act
      const freeResult = await rateLimitingService.checkApiRateLimitByTier(identifier, 'free');
      const proResult = await rateLimitingService.checkApiRateLimitByTier(identifier, 'pro');
      const enterpriseResult = await rateLimitingService.checkApiRateLimitByTier(identifier, 'enterprise');

      // Assert
      expect(freeResult.remaining).toBeLessThan(proResult.remaining);
      expect(proResult.remaining).toBeLessThan(enterpriseResult.remaining);
    });

    it('should enforce free tier limits strictly', async () => {
      // Arrange
      const identifier = 'user_123';
      const existingData = {
        count: 99, // One below free tier limit (100), so next request will exceed
        resetTime: Date.now() + 30000,
      };
      mockCacheService.get.mockResolvedValue(JSON.stringify(existingData));

      // Act
      const result = await rateLimitingService.checkApiRateLimitByTier(identifier, 'free');

      // Assert
      expect(result.allowed).toBe(false);
    });
  });

  describe('burst protection', () => {
    it('should detect and prevent burst attacks', async () => {
      // Arrange
      const identifier = 'user_123';
      let callCount = 0;
      
      // Mock progressive cache responses to simulate burst detection
      mockCacheService.get.mockImplementation(() => {
        callCount++;
        if (callCount === 1) return Promise.resolve(null);
        
        const data = {
          count: Math.min(callCount - 1, 15), // Simulate increasing count
          resetTime: Date.now() + 60000,
        };
        return Promise.resolve(JSON.stringify(data));
      });

      // Act - Simulate rapid requests
      const results = [];
      for (let i = 0; i < 15; i++) {
        const result = await rateLimitingService.checkBurstProtection(identifier);
        results.push(result);
      }

      // Assert
      const allowedCount = results.filter(r => r.allowed).length;
      const deniedCount = results.filter(r => !r.allowed).length;

      expect(deniedCount).toBeGreaterThan(0); // Some requests should be denied
      expect(allowedCount).toBeLessThan(15); // Not all requests should be allowed
    });

    it('should temporarily block after burst detection', async () => {
      // Arrange
      const identifier = 'user_123';
      const burstData = {
        count: 15, // Above burst threshold
        resetTime: Date.now() + 5000, // 5 seconds
      };
      mockCacheService.get.mockResolvedValue(JSON.stringify(burstData));

      // Act
      const result = await rateLimitingService.checkBurstProtection(identifier);

      // Assert
      expect(result.allowed).toBe(false);
      expect(result.retryAfter).toBeGreaterThan(0);
    });
  });

  describe('rate limit reset', () => {
    it('should reset rate limit for user', async () => {
      // Arrange
      const identifier = 'user_123';
      mockCacheService.delete.mockResolvedValue(true);

      // Act
      const result = await rateLimitingService.resetRateLimit(identifier, 'api');

      // Assert
      expect(result).toBe(true);
      expect(mockCacheService.delete).toHaveBeenCalledWith(
        expect.stringContaining(identifier)
      );
    });

    it('should reset all rate limits for user', async () => {
      // Arrange
      const identifier = 'user_123';
      mockCacheService.delete.mockResolvedValue(true);

      // Act
      const result = await rateLimitingService.resetAllRateLimits(identifier);

      // Assert
      expect(result).toBe(true);
      expect(mockCacheService.delete).toHaveBeenCalledTimes(4); // login, api, backtest, burst
    });
  });

  describe('rate limit monitoring', () => {
    it('should get current rate limit status', async () => {
      // Arrange
      const identifier = 'user_123';
      const existingData = {
        count: 50,
        resetTime: Date.now() + 30000,
      };
      mockCacheService.get.mockResolvedValue(JSON.stringify(existingData));

      // Act
      const status = await rateLimitingService.getRateLimitStatus(identifier, 'api');

      // Assert
      expect(status.current).toBe(50);
      expect(status.remaining).toBeGreaterThan(0);
      expect(status.resetTime).toBeInstanceOf(Date);
    });

    it('should return empty status when no data exists', async () => {
      // Arrange
      const identifier = 'user_123';
      mockCacheService.get.mockResolvedValue(null);

      // Act
      const status = await rateLimitingService.getRateLimitStatus(identifier, 'api');

      // Assert
      expect(status.current).toBe(0);
      expect(status.remaining).toBeGreaterThan(0);
    });
  });

  describe('whitelist and blacklist', () => {
    it('should bypass rate limits for whitelisted users', async () => {
      // Arrange
      const identifier = 'admin_user';
      await rateLimitingService.addToWhitelist(identifier);

      // Act
      const result = await rateLimitingService.checkApiRateLimit(identifier);

      // Assert
      expect(result.allowed).toBe(true);
      expect(result.remaining).toBe(Infinity);
    });

    it('should block blacklisted users immediately', async () => {
      // Arrange
      const identifier = 'banned_user';
      await rateLimitingService.addToBlacklist(identifier);

      // Act
      const result = await rateLimitingService.checkApiRateLimit(identifier);

      // Assert
      expect(result.allowed).toBe(false);
      expect(result.remaining).toBe(0);
    });

    it('should remove users from whitelist', async () => {
      // Arrange
      const identifier = 'temp_admin';
      await rateLimitingService.addToWhitelist(identifier);
      await rateLimitingService.removeFromWhitelist(identifier);

      // Act
      const result = await rateLimitingService.checkApiRateLimit(identifier);

      // Assert
      expect(result.remaining).not.toBe(Infinity);
    });
  });

  describe('custom rate limit configurations', () => {
    it('should apply custom rate limit configuration', async () => {
      // Arrange
      const identifier = 'user_123';
      const customConfig: RateLimitConfig = {
        windowMs: 30000, // 30 seconds
        maxRequests: 50,
        keyGenerator: (id) => `custom:${id}`,
      };
      mockCacheService.get.mockResolvedValue(null);

      // Act
      const result = await rateLimitingService.checkRateLimit(identifier, customConfig);

      // Assert
      expect(result.allowed).toBe(true);
      expect(result.remaining).toBe(49);
      expect(mockCacheService.set).toHaveBeenCalledWith(
        'custom:user_123',
        expect.any(String),
        expect.any(Number)
      );
    });
  });
});