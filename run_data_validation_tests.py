#!/usr/bin/env python3
"""
Data Validation & Audit Layer Test Runner
==========================================

Comprehensive test runner for property-based testing of OHLC data validation
and audit trail functionality using Hypothesis.

Features:
- Property-based testing with Hypothesis
- OHLC data consistency validation
- Data integrity and hash verification
- Audit trail testing
- Performance benchmarking
- Comprehensive reporting

Usage:
    python run_data_validation_tests.py [options]

Options:
    --test-type: Type of tests to run (property, unit, integrity, all)
    --examples: Number of examples for property-based tests (default: 100)
    --verbose: Enable verbose output
    --benchmark: Run performance benchmarks
    --coverage: Generate coverage report
"""

import sys
import os
import time
import argparse
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

import pytest
from hypothesis import settings, Verbosity


class DataValidationTestRunner:
    """Test runner for data validation and audit layer tests"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.test_file = self.project_root / "tests" / "test_data_validation.py"
        self.results = {}
    
    def run_property_tests(self, max_examples=100, verbose=False):
        """Run property-based tests with Hypothesis"""
        print("🔬 Running Property-Based Tests for OHLC Data Validation...")
        print("=" * 60)
        
        # Configure Hypothesis settings
        verbosity = Verbosity.verbose if verbose else Verbosity.normal
        settings.register_profile("data_validation", 
                                max_examples=max_examples, 
                                verbosity=verbosity,
                                deadline=None)
        settings.load_profile("data_validation")
        
        # Run property-based tests
        test_classes = [
            "TestPropertyBasedOHLCValidation::test_valid_ohlc_always_passes_validation",
            "TestPropertyBasedOHLCValidation::test_ohlc_consistency_property", 
            "TestPropertyBasedOHLCValidation::test_invalid_ohlc_always_fails_validation",
            "TestPropertyBasedOHLCValidation::test_data_hash_integrity_property",
            "TestPropertyBasedOHLCValidation::test_batch_validation_property",
            "TestPropertyBasedOHLCValidation::test_price_spread_property",
            "TestPropertyBasedOHLCValidation::test_volume_property"
        ]
        
        start_time = time.time()
        
        for test_class in test_classes:
            print(f"\n📊 Running: {test_class.split('::')[1]}")
            result = pytest.main([
                str(self.test_file) + "::" + test_class,
                "-v",
                "--tb=short"
            ])
            
            self.results[test_class] = "PASSED" if result == 0 else "FAILED"
            print(f"   Result: {'✅ PASSED' if result == 0 else '❌ FAILED'}")
        
        duration = time.time() - start_time
        print(f"\n⏱️  Property-based tests completed in {duration:.2f} seconds")
        return all(result == "PASSED" for result in self.results.values())
    
    def run_unit_tests(self):
        """Run traditional unit tests"""
        print("\n🧪 Running Unit Tests for Data Validation...")
        print("=" * 50)
        
        test_classes = [
            "TestDataValidation",
            "TestDataSourceManager"
        ]
        
        start_time = time.time()
        
        for test_class in test_classes:
            print(f"\n📋 Running: {test_class}")
            result = pytest.main([
                str(self.test_file) + "::" + test_class,
                "-v",
                "--tb=short"
            ])
            
            self.results[test_class] = "PASSED" if result == 0 else "FAILED"
            print(f"   Result: {'✅ PASSED' if result == 0 else '❌ FAILED'}")
        
        duration = time.time() - start_time
        print(f"\n⏱️  Unit tests completed in {duration:.2f} seconds")
        return all(result == "PASSED" for result in self.results.values())
    
    def run_integrity_tests(self):
        """Run data integrity and audit trail tests"""
        print("\n🔐 Running Data Integrity & Audit Trail Tests...")
        print("=" * 55)
        
        test_classes = [
            "TestDataIntegrityAuditTrail",
            "TestPlatformDataIntegrity"
        ]
        
        start_time = time.time()
        
        for test_class in test_classes:
            print(f"\n🛡️  Running: {test_class}")
            result = pytest.main([
                str(self.test_file) + "::" + test_class,
                "-v",
                "--tb=short"
            ])
            
            self.results[test_class] = "PASSED" if result == 0 else "FAILED"
            print(f"   Result: {'✅ PASSED' if result == 0 else '❌ FAILED'}")
        
        duration = time.time() - start_time
        print(f"\n⏱️  Integrity tests completed in {duration:.2f} seconds")
        return all(result == "PASSED" for result in self.results.values())
    
    def run_benchmark_tests(self):
        """Run performance benchmarks"""
        print("\n⚡ Running Performance Benchmarks...")
        print("=" * 40)
        
        # Run with pytest-benchmark
        result = pytest.main([
            str(self.test_file),
            "--benchmark-only",
            "--benchmark-sort=mean",
            "--benchmark-columns=min,max,mean,stddev,rounds,iterations"
        ])
        
        return result == 0
    
    def run_coverage_analysis(self):
        """Run tests with coverage analysis"""
        print("\n📈 Running Coverage Analysis...")
        print("=" * 35)
        
        result = pytest.main([
            str(self.test_file),
            "--cov=src/validation",
            "--cov-report=html:htmlcov/data_validation",
            "--cov-report=term-missing",
            "--cov-fail-under=85"
        ])
        
        if result == 0:
            print("📊 Coverage report generated in htmlcov/data_validation/")
        
        return result == 0
    
    def print_summary(self):
        """Print test execution summary"""
        print("\n" + "=" * 70)
        print("🎯 DATA VALIDATION TEST EXECUTION SUMMARY")
        print("=" * 70)
        
        total_tests = len(self.results)
        passed_tests = sum(1 for result in self.results.values() if result == "PASSED")
        failed_tests = total_tests - passed_tests
        
        print(f"📊 Total Test Suites: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"📈 Success Rate: {(passed_tests/total_tests)*100:.1f}%" if total_tests > 0 else "📈 Success Rate: N/A")
        
        if failed_tests > 0:
            print(f"\n❌ Failed Test Suites:")
            for test_name, result in self.results.items():
                if result == "FAILED":
                    print(f"   - {test_name}")
        
        print("\n🔍 Test Categories Covered:")
        print("   ✅ Property-Based Testing (Hypothesis)")
        print("   ✅ OHLC Data Consistency Validation")
        print("   ✅ Data Integrity & Hash Verification")
        print("   ✅ Audit Trail Testing")
        print("   ✅ Source Verification")
        print("   ✅ Edge Case Handling")
        
        print("\n🎉 Data Validation & Audit Layer Testing Complete!")
        return failed_tests == 0


def main():
    """Main test runner function"""
    parser = argparse.ArgumentParser(description="Data Validation & Audit Layer Test Runner")
    parser.add_argument("--test-type", 
                       choices=["property", "unit", "integrity", "all"], 
                       default="all",
                       help="Type of tests to run")
    parser.add_argument("--examples", 
                       type=int, 
                       default=100,
                       help="Number of examples for property-based tests")
    parser.add_argument("--verbose", 
                       action="store_true",
                       help="Enable verbose output")
    parser.add_argument("--benchmark", 
                       action="store_true",
                       help="Run performance benchmarks")
    parser.add_argument("--coverage", 
                       action="store_true",
                       help="Generate coverage report")
    
    args = parser.parse_args()
    
    print("🚀 AI Enhanced Trading Platform - Data Validation Test Suite")
    print("=" * 65)
    print(f"📅 Test Run: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 Test Type: {args.test_type.upper()}")
    print(f"🔢 Property Test Examples: {args.examples}")
    print(f"📢 Verbose Mode: {'ON' if args.verbose else 'OFF'}")
    
    runner = DataValidationTestRunner()
    overall_success = True
    
    try:
        if args.test_type in ["property", "all"]:
            success = runner.run_property_tests(args.examples, args.verbose)
            overall_success = overall_success and success
        
        if args.test_type in ["unit", "all"]:
            success = runner.run_unit_tests()
            overall_success = overall_success and success
        
        if args.test_type in ["integrity", "all"]:
            success = runner.run_integrity_tests()
            overall_success = overall_success and success
        
        if args.benchmark:
            success = runner.run_benchmark_tests()
            overall_success = overall_success and success
        
        if args.coverage:
            success = runner.run_coverage_analysis()
            overall_success = overall_success and success
        
        # Print final summary
        final_success = runner.print_summary()
        overall_success = overall_success and final_success
        
    except KeyboardInterrupt:
        print("\n⚠️  Test execution interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Test execution failed with error: {e}")
        return 1
    
    return 0 if overall_success else 1


if __name__ == "__main__":
    sys.exit(main())