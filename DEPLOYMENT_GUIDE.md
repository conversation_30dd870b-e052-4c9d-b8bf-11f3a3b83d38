# AI Enhanced Trading Platform - Deployment Guide

This guide provides instructions for deploying the AI Enhanced Trading Platform MVP in various environments.

## Prerequisites

- Python 3.10+ installed
- Node.js 16+ installed (for frontend)
- Git (optional, for cloning the repository)
- MetaTrader 5 (optional, for live trading)

## Local Development Deployment

### Backend Setup

1. Clone the repository (if not already done):
   ```bash
   git clone <repository-url>
   cd AI-Enhanced-Trading-Platform
   ```

2. Create and activate a virtual environment:
   ```bash
   # Windows
   python -m venv venv
   venv\Scripts\activate

   # macOS/Linux
   python -m venv venv
   source venv/bin/activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Create logs directory:
   ```bash
   mkdir logs
   ```

5. Start the backend server:
   ```bash
   python backend/minimal_server.py
   ```

   The server will start on http://localhost:8000

### Frontend Setup

1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev:mvp
   ```

   The frontend will be available at http://localhost:5173

## Production Deployment

### Option 1: Docker Deployment

1. Build the Docker image:
   ```bash
   docker build -t trading-platform-mvp .
   ```

2. Run the container:
   ```bash
   docker run -p 8000:8000 -p 5173:5173 trading-platform-mvp
   ```

### Option 2: Server Deployment

#### Backend Deployment

1. Set up a server with Python 3.10+
2. Clone the repository and install dependencies as in the local setup
3. Use a production ASGI server like Uvicorn or Gunicorn:
   ```bash
   # Install Gunicorn
   pip install gunicorn uvicorn

   # Run with Gunicorn
   gunicorn -w 4 -k uvicorn.workers.UvicornWorker backend.minimal_server:app
   ```

4. Set up a reverse proxy (Nginx or Apache) to forward requests to the backend server

#### Frontend Deployment

1. Build the frontend for production:
   ```bash
   cd frontend
   npm run build
   ```

2. Serve the static files from the `dist` directory using Nginx or another web server

## Security Considerations

1. **HTTPS**: Always use HTTPS in production environments
2. **Authentication**: The MVP uses basic authentication. In production, consider using more secure authentication methods
3. **Environment Variables**: Store sensitive information like API keys and database credentials as environment variables
4. **Firewall**: Configure a firewall to restrict access to the server
5. **Regular Updates**: Keep all dependencies updated to patch security vulnerabilities

## Monitoring and Maintenance

1. **Logging**: The application logs to the `logs` directory. Consider using a log management solution in production
2. **Backups**: Regularly backup any persistent data
3. **Health Checks**: Use the `/health` endpoint to monitor the application's status
4. **Alerts**: Set up alerts for application errors and performance issues

## Troubleshooting

### Common Issues

1. **Connection Refused**: Ensure the server is running and the port is not blocked by a firewall
2. **Authentication Errors**: Verify the correct username and password are being used
3. **CORS Errors**: If accessing the API from a different domain, ensure CORS is properly configured
4. **Missing Dependencies**: Ensure all dependencies are installed with the correct versions

### Getting Help

If you encounter issues not covered in this guide, please:
1. Check the logs for error messages
2. Consult the API documentation
3. Contact the development team for support

## Scaling Considerations

The MVP is designed for small-scale usage. For larger deployments, consider:

1. **Database**: Replace the in-memory storage with a proper database
2. **Load Balancing**: Deploy multiple instances behind a load balancer
3. **Caching**: Implement caching for frequently accessed data
4. **Microservices**: Split the application into microservices for better scalability

## Updating the Application

To update to a new version:

1. Pull the latest changes:
   ```bash
   git pull origin main
   ```

2. Install any new dependencies:
   ```bash
   pip install -r requirements.txt
   cd frontend && npm install
   ```

3. Restart the application

## Conclusion

This deployment guide covers the basics of deploying the AI Enhanced Trading Platform MVP. For more advanced deployment scenarios or specific requirements, please contact the development team.