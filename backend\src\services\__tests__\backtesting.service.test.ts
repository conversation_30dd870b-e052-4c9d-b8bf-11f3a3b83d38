import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { BacktestingService } from '../backtesting.service';
import { MarketDataService } from '../market-data.service';
import { StrategyExecutor } from '../strategy-executor.service';
import { MetricsCalculator } from '../metrics-calculator.service';
import { BacktestConfig, BacktestResults } from '../../../../shared/schemas/backtest.schemas';

// Mock dependencies
jest.mock('../market-data.service');
jest.mock('../strategy-executor.service');
jest.mock('../metrics-calculator.service');

describe('BacktestingService', () => {
  let backtestingService: BacktestingService;
  let mockMarketDataService: jest.Mocked<MarketDataService>;
  let mockStrategyExecutor: jest.Mocked<StrategyExecutor>;
  let mockMetricsCalculator: jest.Mocked<MetricsCalculator>;

  const mockBacktestConfig: BacktestConfig = {
    name: 'Test Strategy',
    symbols: ['EURUSD'],
    start_date: new Date('2024-01-01'),
    end_date: new Date('2024-06-30'),
    initial_balance: 10000,
    strategy: {
      name: 'sma_crossover',
      parameters: {
        fast_period: 10,
        slow_period: 20,
      },
    },
    risk_management: {
      max_risk_per_trade: 0.02,
      max_concurrent_trades: 3,
    },
  };

  const mockMarketData = [
    {
      symbol: 'EURUSD' as const,
      timestamp: new Date('2024-01-01T00:00:00Z'),
      open: 1.1000,
      high: 1.1010,
      low: 1.0990,
      close: 1.1005,
      volume: 1000,
    },
    {
      symbol: 'EURUSD' as const,
      timestamp: new Date('2024-01-01T01:00:00Z'),
      open: 1.1005,
      high: 1.1015,
      low: 1.0995,
      close: 1.1010,
      volume: 1200,
    },
  ];

  beforeEach(() => {
    // Create mocks
    mockMarketDataService = {
      fetchHistoricalData: jest.fn(),
      validateSymbol: jest.fn(),
    } as any;

    mockStrategyExecutor = {
      execute: jest.fn(),
      validateStrategy: jest.fn(),
    } as any;

    mockMetricsCalculator = {
      calculateMetrics: jest.fn(),
      calculateDrawdown: jest.fn(),
      calculateSharpeRatio: jest.fn(),
    } as any;

    // Initialize service with mocks
    backtestingService = new BacktestingService(
      mockMarketDataService,
      mockStrategyExecutor,
      mockMetricsCalculator
    );
  });

  describe('runBacktest', () => {
    it('should successfully run a complete backtest', async () => {
      // Arrange
      mockMarketDataService.fetchHistoricalData.mockResolvedValue(mockMarketData);
      mockStrategyExecutor.validateStrategy.mockResolvedValue(true);
      mockStrategyExecutor.execute.mockResolvedValue({ signal: 'buy', confidence: 0.8 });
      mockMetricsCalculator.calculateMetrics.mockReturnValue({
        total_trades: 5,
        winning_trades: 3,
        losing_trades: 2,
        win_rate: 0.6,
        total_pnl: 500,
        gross_profit: 800,
        gross_loss: -300,
        profit_factor: 2.67,
        max_drawdown: -100,
        max_drawdown_percent: -0.01,
        average_win: 266.67,
        average_loss: -150,
        largest_win: 400,
        largest_loss: -200,
        expectancy: 100,
      });

      // Act
      const result = await backtestingService.runBacktest(mockBacktestConfig);

      // Assert
      expect(result).toBeDefined();
      expect(result.config).toEqual(mockBacktestConfig);
      expect(result.metrics.total_trades).toBe(5);
      expect(result.metrics.win_rate).toBe(0.6);
      expect(mockMarketDataService.fetchHistoricalData).toHaveBeenCalledWith(
        mockBacktestConfig.symbols,
        mockBacktestConfig.start_date,
        mockBacktestConfig.end_date
      );
      expect(mockStrategyExecutor.validateStrategy).toHaveBeenCalled();
    });

    it('should throw error for invalid strategy', async () => {
      // Arrange
      mockMarketDataService.fetchHistoricalData.mockResolvedValue(mockMarketData);
      mockStrategyExecutor.validateStrategy.mockResolvedValue(false);

      // Act & Assert
      await expect(backtestingService.runBacktest(mockBacktestConfig))
        .rejects.toThrow('Invalid strategy configuration');
    });

    it('should handle market data fetch errors', async () => {
      // Arrange
      mockMarketDataService.fetchHistoricalData.mockRejectedValue(
        new Error('Failed to fetch market data')
      );

      // Act & Assert
      await expect(backtestingService.runBacktest(mockBacktestConfig))
        .rejects.toThrow('Failed to fetch market data');
    });

    it('should validate date range', async () => {
      // Arrange
      const invalidConfig = {
        ...mockBacktestConfig,
        start_date: new Date('2024-12-31'),
        end_date: new Date('2024-01-01'), // End before start
      };

      // Act & Assert
      await expect(backtestingService.runBacktest(invalidConfig))
        .rejects.toThrow('Invalid date range');
    });

    it('should enforce minimum data requirements', async () => {
      // Arrange
      mockMarketDataService.fetchHistoricalData.mockResolvedValue([]);

      // Act & Assert
      await expect(backtestingService.runBacktest(mockBacktestConfig))
        .rejects.toThrow('Insufficient market data');
    });
  });

  describe('validateStrategy', () => {
    it('should validate strategy parameters', async () => {
      // Arrange
      const strategy = {
        name: 'sma_crossover',
        parameters: {
          fast_period: 10,
          slow_period: 20,
        },
      };

      mockStrategyExecutor.validateStrategy.mockResolvedValue(true);

      // Act
      const result = await backtestingService.validateStrategy(strategy);

      // Assert
      expect(result).toBe(true);
      expect(mockStrategyExecutor.validateStrategy).toHaveBeenCalledWith(strategy);
    });

    it('should reject invalid strategy parameters', async () => {
      // Arrange
      const invalidStrategy = {
        name: 'sma_crossover',
        parameters: {
          fast_period: 20,
          slow_period: 10, // Fast period should be less than slow period
        },
      };

      mockStrategyExecutor.validateStrategy.mockResolvedValue(false);

      // Act
      const result = await backtestingService.validateStrategy(invalidStrategy);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('calculateRiskMetrics', () => {
    it('should calculate comprehensive risk metrics', () => {
      // Arrange
      const trades = [
        { 
          pnl: 100, 
          pnl_pips: 10,
          entry_time: new Date(), 
          exit_time: new Date(),
          symbol: 'EURUSD' as const, 
          order_type: 'buy' as const, 
          entry_price: 1.1000, 
          exit_price: 1.1010,
          volume: 0.01 
        },
        { 
          pnl: -50, 
          pnl_pips: -5,
          entry_time: new Date(), 
          exit_time: new Date(),
          symbol: 'EURUSD' as const, 
          order_type: 'sell' as const, 
          entry_price: 1.1010, 
          exit_price: 1.1005,
          volume: 0.01 
        },
        { 
          pnl: 200, 
          pnl_pips: 20,
          entry_time: new Date(), 
          exit_time: new Date(),
          symbol: 'EURUSD' as const, 
          order_type: 'buy' as const, 
          entry_price: 1.1020, 
          exit_price: 1.1040,
          volume: 0.01 
        },
      ];

      const balanceCurve = [
        { timestamp: new Date(), balance: 10000, equity: 10000, drawdown: 0 },
        { timestamp: new Date(), balance: 10100, equity: 10100, drawdown: 0 },
        { timestamp: new Date(), balance: 10050, equity: 10050, drawdown: -50 },
        { timestamp: new Date(), balance: 10250, equity: 10250, drawdown: 0 },
      ];

      mockMetricsCalculator.calculateMetrics.mockReturnValue({
        total_trades: 3,
        winning_trades: 2,
        losing_trades: 1,
        win_rate: 0.67,
        total_pnl: 250,
        gross_profit: 300,
        gross_loss: -50,
        profit_factor: 6.0,
        max_drawdown: -50,
        max_drawdown_percent: -0.005,
        average_win: 150,
        average_loss: -50,
        largest_win: 200,
        largest_loss: -50,
        expectancy: 83.33,
      });

      // Act
      const metrics = backtestingService.calculateRiskMetrics(trades, balanceCurve);

      // Assert
      expect(metrics.total_trades).toBe(3);
      expect(metrics.win_rate).toBe(0.67);
      expect(metrics.profit_factor).toBe(6.0);
      expect(metrics.max_drawdown).toBe(-50);
    });
  });

  describe('generateReport', () => {
    it('should generate comprehensive backtest report', async () => {
      // Arrange
      const mockResults: BacktestResults = {
        backtest_id: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
        config: mockBacktestConfig,
        metrics: {
          total_trades: 10,
          winning_trades: 6,
          losing_trades: 4,
          win_rate: 0.6,
          total_pnl: 1000,
          gross_profit: 1500,
          gross_loss: -500,
          profit_factor: 3.0,
          max_drawdown: -200,
          max_drawdown_percent: -0.02,
          average_win: 250,
          average_loss: -125,
          largest_win: 400,
          largest_loss: -200,
          expectancy: 100,
        },
        trades: [],
        balance_curve: [],
        monthly_returns: [],
        created_at: new Date(),
      };

      // Act
      const report = await backtestingService.generateReport(mockResults);

      // Assert
      expect(report).toBeDefined();
      expect(report.summary).toBeDefined();
      expect(report.performance_metrics).toBeDefined();
      expect(report.risk_analysis).toBeDefined();
      expect(report.recommendations).toBeDefined();
    });
  });

  describe('optimizeStrategy', () => {
    it('should perform parameter optimization', async () => {
      // Arrange
      const parameterRanges = {
        fast_period: { min: 5, max: 15, step: 1 },
        slow_period: { min: 20, max: 30, step: 2 },
      };

      mockMarketDataService.fetchHistoricalData.mockResolvedValue(mockMarketData);
      mockStrategyExecutor.validateStrategy.mockResolvedValue(true);
      mockStrategyExecutor.execute.mockResolvedValue({ signal: 'buy', confidence: 0.8 });
      mockMetricsCalculator.calculateMetrics.mockReturnValue({
        total_trades: 5,
        winning_trades: 3,
        losing_trades: 2,
        win_rate: 0.6,
        total_pnl: 500,
        gross_profit: 800,
        gross_loss: -300,
        profit_factor: 2.67,
        max_drawdown: -100,
        max_drawdown_percent: -0.01,
        average_win: 266.67,
        average_loss: -150,
        largest_win: 400,
        largest_loss: -200,
        expectancy: 100,
      });

      // Act
      const optimizationResult = await backtestingService.optimizeStrategy(
        mockBacktestConfig,
        parameterRanges
      );

      // Assert
      expect(optimizationResult).toBeDefined();
      expect(optimizationResult.best_parameters).toBeDefined();
      expect(optimizationResult.optimization_results).toBeDefined();
      expect(optimizationResult.optimization_results.length).toBeGreaterThan(0);
    });
  });
});