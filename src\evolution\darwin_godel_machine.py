# src/evolution/darwin_godel_machine.py
"""
Darwin-Gödel Machine (DGM): Deterministic Evolution with Complete Audit Trails

This implementation provides:
- Deterministic reproducibility with seed-based randomness
- Complete audit trails with cryptographic hashing
- Tamper-proof evolution logs
- Full lineage tracking for all genomes
- Integrity verification for all operations
"""

import random
import numpy as np
import hashlib
import json
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum


class OperationType(Enum):
    """Types of operations that can be audited"""
    INITIALIZATION = "initialization"
    MUTATION = "mutation"
    CROSSOVER = "crossover"
    FITNESS_CALCULATION = "fitness_calculation"
    SELECTION = "selection"
    GENERATION_EVOLUTION = "generation_evolution"


@dataclass
class AuditLogEntry:
    """Single audit log entry with cryptographic integrity"""
    timestamp: str
    operation_type: OperationType
    generation: int
    genome_id: Optional[str]
    parameters: Dict[str, Any]
    result: Dict[str, Any]
    parent_hash: Optional[str]
    hash: str
    
    def __post_init__(self):
        """Generate hash after initialization if not provided"""
        if not self.hash:
            self.hash = self._generate_hash()
    
    def _generate_hash(self) -> str:
        """Generate SHA-256 hash of the entry"""
        # Create deterministic string representation
        data = {
            'timestamp': self.timestamp,
            'operation_type': self.operation_type.value,
            'generation': self.generation,
            'genome_id': self.genome_id,
            'parameters': self.parameters,
            'result': self.result,
            'parent_hash': self.parent_hash
        }
        
        # Sort keys for deterministic hashing
        json_str = json.dumps(data, sort_keys=True, default=str)
        return hashlib.sha256(json_str.encode()).hexdigest()
    
    def verify_integrity(self) -> bool:
        """Verify the integrity of this audit entry"""
        expected_hash = self._generate_hash()
        return self.hash == expected_hash


@dataclass
class StrategyGenome:
    """Enhanced genome with audit trail integration"""
    parameters: Dict[str, float]
    fitness: float
    generation: int
    parent_ids: List[str]
    mutation_log: List[str]
    backtest_results: Dict[str, Any]
    id: str
    creation_timestamp: str
    integrity_hash: str = ""
    
    def __post_init__(self):
        """Generate integrity hash after initialization"""
        if not self.integrity_hash:
            self.integrity_hash = self._generate_integrity_hash()
    
    def _generate_integrity_hash(self) -> str:
        """Generate SHA-256 hash for genome integrity verification"""
        # Create deterministic representation excluding the hash itself
        data = {
            'parameters': self.parameters,
            'fitness': self.fitness,
            'generation': self.generation,
            'parent_ids': self.parent_ids,
            'mutation_log': self.mutation_log,
            'backtest_results': self.backtest_results,
            'id': self.id,
            'creation_timestamp': self.creation_timestamp
        }
        
        json_str = json.dumps(data, sort_keys=True, default=str)
        return hashlib.sha256(json_str.encode()).hexdigest()
    
    def verify_integrity(self) -> bool:
        """Verify genome integrity"""
        expected_hash = self._generate_integrity_hash()
        return self.integrity_hash == expected_hash
    
    def update_integrity_hash(self):
        """Update integrity hash after modifications"""
        self.integrity_hash = self._generate_integrity_hash()

class DarwinGodelMachine:
    """
    Darwin-Gödel Machine with deterministic evolution and complete audit trails
    
    Features:
    - Deterministic reproducibility with seed-based randomness
    - Complete audit trail with cryptographic hashing
    - Tamper-proof evolution logs
    - Full lineage tracking
    - Integrity verification
    """
    
    def __init__(self, seed: int = None, enable_audit: bool = True):
        self.seed = seed if seed is not None else int(time.time())
        self.enable_audit = enable_audit
        self._id_counter = 0
        self._operation_counter = 0
        
        # Store random state for reproducibility
        self._random_state = random.Random(self.seed)
        self._np_random_state = np.random.RandomState(self.seed)
        
        # Set global seeds for compatibility
        random.seed(self.seed)
        np.random.seed(self.seed)
        
        # Evolution state
        self.generation_history: List[List[StrategyGenome]] = []
        self.current_generation = 0
        
        # Audit trail
        self.audit_log: List[AuditLogEntry] = []
        self.last_audit_hash: Optional[str] = None
        
        # Configuration parameters
        self.fitness_function_params = {
            'return_weight': 0.3,
            'sharpe_weight': 0.25,
            'drawdown_weight': 0.25,
            'consistency_weight': 0.2
        }
        self.mutation_rates = {
            'parameter_mutation': 0.1,
            'crossover_rate': 0.7,
            'elite_preservation': 0.1
        }
        
        # Log initialization
        if self.enable_audit:
            self._log_operation(
                operation_type=OperationType.INITIALIZATION,
                parameters={
                    'seed': self.seed,
                    'fitness_function_params': self.fitness_function_params,
                    'mutation_rates': self.mutation_rates
                },
                result={'status': 'initialized', 'timestamp': datetime.now().isoformat()}
            )
    
    def _log_operation(self, operation_type: OperationType, parameters: Dict[str, Any], 
                      result: Dict[str, Any], genome_id: Optional[str] = None) -> str:
        """Log an operation to the audit trail"""
        if not self.enable_audit:
            return ""
        
        self._operation_counter += 1
        timestamp = datetime.now().isoformat()
        
        entry = AuditLogEntry(
            timestamp=timestamp,
            operation_type=operation_type,
            generation=self.current_generation,
            genome_id=genome_id,
            parameters=parameters,
            result=result,
            parent_hash=self.last_audit_hash,
            hash=""  # Will be generated in __post_init__
        )
        
        self.audit_log.append(entry)
        self.last_audit_hash = entry.hash
        return entry.hash
    
    def verify_audit_integrity(self) -> Tuple[bool, List[str]]:
        """Verify the integrity of the entire audit log"""
        errors = []
        
        if not self.audit_log:
            return True, []
        
        # Verify each entry's hash
        for i, entry in enumerate(self.audit_log):
            if not entry.verify_integrity():
                errors.append(f"Entry {i} failed hash verification")
        
        # Verify chain integrity
        for i in range(1, len(self.audit_log)):
            current_entry = self.audit_log[i]
            previous_entry = self.audit_log[i-1]
            
            if current_entry.parent_hash != previous_entry.hash:
                errors.append(f"Chain broken at entry {i}")
        
        return len(errors) == 0, errors
    
    def get_audit_log_summary(self) -> Dict[str, Any]:
        """Get summary of audit log"""
        if not self.audit_log:
            return {'total_entries': 0, 'operations': {}}
        
        operations_count = {}
        for entry in self.audit_log:
            op_type = entry.operation_type.value
            operations_count[op_type] = operations_count.get(op_type, 0) + 1
        
        return {
            'total_entries': len(self.audit_log),
            'operations': operations_count,
            'first_entry': self.audit_log[0].timestamp,
            'last_entry': self.audit_log[-1].timestamp,
            'integrity_verified': self.verify_audit_integrity()[0]
        }
    
    def initialize_population(self, size: int = 50) -> List[StrategyGenome]:
        """Initialize population with deterministic randomness and full audit trail"""
        population = []
        
        parameter_ranges = {
            'rsi_period': (10, 30),
            'rsi_oversold': (20, 40),
            'rsi_overbought': (60, 80),
            'ma_period': (10, 50),
            'stop_loss': (0.01, 0.05),
            'take_profit': (0.02, 0.10)
        }
        
        # Log population initialization
        if self.enable_audit:
            self._log_operation(
                operation_type=OperationType.INITIALIZATION,
                parameters={'population_size': size, 'parameter_ranges': parameter_ranges},
                result={'status': 'starting_population_creation'}
            )
        
        for i in range(size):
            parameters = {}
            for param, (min_val, max_val) in parameter_ranges.items():
                if param.endswith('_period'):
                    parameters[param] = self._random_state.randint(int(min_val), int(max_val))
                else:
                    parameters[param] = self._random_state.uniform(min_val, max_val)
            
            genome_id = self._generate_deterministic_id()
            timestamp = datetime.now().isoformat()
            
            genome = StrategyGenome(
                parameters=parameters,
                fitness=0.0,
                generation=0,
                parent_ids=[],
                mutation_log=[f"Initial generation - seed: {self.seed}"],
                backtest_results={},
                id=genome_id,
                creation_timestamp=timestamp
            )
            population.append(genome)
            
            # Log each genome creation
            if self.enable_audit:
                self._log_operation(
                    operation_type=OperationType.INITIALIZATION,
                    parameters={'genome_index': i, 'parameters': parameters},
                    result={'genome_id': genome_id, 'integrity_hash': genome.integrity_hash},
                    genome_id=genome_id
                )
        
        self.generation_history.append(population.copy())
        
        # Log completion
        if self.enable_audit:
            self._log_operation(
                operation_type=OperationType.INITIALIZATION,
                parameters={'population_size': size},
                result={
                    'status': 'population_created',
                    'genome_ids': [g.id for g in population],
                    'total_genomes': len(population)
                }
            )
        
        return population
    
    def calculate_fitness(self, genome: StrategyGenome, backtest_results: Dict[str, Any]) -> float:
        """Calculate fitness based only on verified backtest results with full audit trail"""
        if not self._verify_backtest_integrity(backtest_results):
            error_msg = "Backtest results failed integrity check"
            if self.enable_audit:
                self._log_operation(
                    operation_type=OperationType.FITNESS_CALCULATION,
                    parameters={'genome_id': genome.id, 'backtest_results_keys': list(backtest_results.keys())},
                    result={'status': 'failed', 'error': error_msg},
                    genome_id=genome.id
                )
            raise ValueError(error_msg)
        
        # Store original fitness for comparison
        original_fitness = genome.fitness
        
        # Update genome with verified results
        genome.backtest_results = backtest_results.copy()
        
        # Multi-objective fitness calculation
        total_return = backtest_results.get('total_return', 0)
        sharpe_ratio = backtest_results.get('sharpe_ratio', 0)
        max_drawdown = backtest_results.get('max_drawdown', 1)  # Lower is better
        win_rate = backtest_results.get('win_rate', 0)
        
        # Normalize metrics (0-1 scale)
        return_score = min(total_return / 0.5, 1.0)  # Cap at 50% return
        sharpe_score = min(sharpe_ratio / 3.0, 1.0)  # Cap at Sharpe 3.0
        drawdown_score = max(0, 1 - (max_drawdown / 0.3))  # Penalize >30% drawdown
        consistency_score = win_rate
        
        # Weighted fitness
        fitness = (
            return_score * self.fitness_function_params['return_weight'] +
            sharpe_score * self.fitness_function_params['sharpe_weight'] +
            drawdown_score * self.fitness_function_params['drawdown_weight'] +
            consistency_score * self.fitness_function_params['consistency_weight']
        )
        
        # Update genome
        genome.fitness = fitness
        genome.update_integrity_hash()
        
        # Log fitness calculation
        if self.enable_audit:
            self._log_operation(
                operation_type=OperationType.FITNESS_CALCULATION,
                parameters={
                    'genome_id': genome.id,
                    'backtest_metrics': {
                        'total_return': total_return,
                        'sharpe_ratio': sharpe_ratio,
                        'max_drawdown': max_drawdown,
                        'win_rate': win_rate
                    },
                    'fitness_weights': self.fitness_function_params
                },
                result={
                    'original_fitness': original_fitness,
                    'new_fitness': fitness,
                    'fitness_change': fitness - original_fitness,
                    'component_scores': {
                        'return_score': return_score,
                        'sharpe_score': sharpe_score,
                        'drawdown_score': drawdown_score,
                        'consistency_score': consistency_score
                    }
                },
                genome_id=genome.id
            )
        
        return fitness
    
    def _verify_backtest_integrity(self, backtest_results: Dict[str, Any]) -> bool:
        """Verify backtest results haven't been tampered with"""
        required_fields = ['total_return', 'sharpe_ratio', 'max_drawdown', 'trades_count']
        
        for field in required_fields:
            if field not in backtest_results:
                return False
        
        # Check data integrity hash if provided
        if 'data_integrity_hash' in backtest_results:
            # Additional verification logic here
            pass
        
        return True
    
    def mutate(self, parent: StrategyGenome) -> StrategyGenome:
        """Create mutated offspring with full auditability and integrity verification"""
        # Verify parent integrity
        if not parent.verify_integrity():
            raise ValueError(f"Parent genome {parent.id} failed integrity check")
        
        child_parameters = parent.parameters.copy()
        mutation_log = []
        mutations_applied = []
        
        for param, value in child_parameters.items():
            if self._random_state.random() < self.mutation_rates['parameter_mutation']:
                if param.endswith('_period'):
                    # Integer parameters
                    old_value = value
                    new_value = max(1, int(value + self._random_state.gauss(0, 2)))
                    child_parameters[param] = new_value
                    mutation_entry = f"{param}: {old_value} -> {new_value}"
                    mutation_log.append(mutation_entry)
                    mutations_applied.append({
                        'parameter': param,
                        'old_value': old_value,
                        'new_value': new_value,
                        'type': 'integer'
                    })
                else:
                    # Float parameters
                    old_value = value
                    mutation_factor = self._random_state.gauss(1.0, 0.1)
                    new_value = max(0.001, value * mutation_factor)
                    child_parameters[param] = new_value
                    mutation_entry = f"{param}: {old_value:.4f} -> {new_value:.4f}"
                    mutation_log.append(mutation_entry)
                    mutations_applied.append({
                        'parameter': param,
                        'old_value': old_value,
                        'new_value': new_value,
                        'mutation_factor': mutation_factor,
                        'type': 'float'
                    })
        
        child_id = self._generate_deterministic_id()
        timestamp = datetime.now().isoformat()
        
        child = StrategyGenome(
            parameters=child_parameters,
            fitness=0.0,
            generation=parent.generation + 1,
            parent_ids=[parent.id],
            mutation_log=parent.mutation_log + mutation_log,
            backtest_results={},
            id=child_id,
            creation_timestamp=timestamp
        )
        
        # Log mutation operation
        if self.enable_audit:
            self._log_operation(
                operation_type=OperationType.MUTATION,
                parameters={
                    'parent_id': parent.id,
                    'parent_fitness': parent.fitness,
                    'mutation_rate': self.mutation_rates['parameter_mutation'],
                    'mutations_applied': mutations_applied
                },
                result={
                    'child_id': child_id,
                    'child_parameters': child_parameters,
                    'mutations_count': len(mutations_applied),
                    'child_integrity_hash': child.integrity_hash
                },
                genome_id=child_id
            )
        
        return child
    
    def crossover(self, parent1: StrategyGenome, parent2: StrategyGenome) -> StrategyGenome:
        """Create offspring from two parents with full audit trail"""
        # Verify parent integrity
        if not parent1.verify_integrity():
            raise ValueError(f"Parent1 genome {parent1.id} failed integrity check")
        if not parent2.verify_integrity():
            raise ValueError(f"Parent2 genome {parent2.id} failed integrity check")
        
        child_parameters = {}
        crossover_log = []
        parameter_sources = []
        
        for param in parent1.parameters.keys():
            if self._random_state.random() < 0.5:
                child_parameters[param] = parent1.parameters[param]
                crossover_log.append(f"{param}: from parent1")
                parameter_sources.append({
                    'parameter': param,
                    'value': parent1.parameters[param],
                    'source': 'parent1',
                    'source_id': parent1.id
                })
            else:
                child_parameters[param] = parent2.parameters[param]
                crossover_log.append(f"{param}: from parent2")
                parameter_sources.append({
                    'parameter': param,
                    'value': parent2.parameters[param],
                    'source': 'parent2',
                    'source_id': parent2.id
                })
        
        child_id = self._generate_deterministic_id()
        timestamp = datetime.now().isoformat()
        
        child = StrategyGenome(
            parameters=child_parameters,
            fitness=0.0,
            generation=max(parent1.generation, parent2.generation) + 1,
            parent_ids=[parent1.id, parent2.id],
            mutation_log=crossover_log,
            backtest_results={},
            id=child_id,
            creation_timestamp=timestamp
        )
        
        # Log crossover operation
        if self.enable_audit:
            self._log_operation(
                operation_type=OperationType.CROSSOVER,
                parameters={
                    'parent1_id': parent1.id,
                    'parent1_fitness': parent1.fitness,
                    'parent2_id': parent2.id,
                    'parent2_fitness': parent2.fitness,
                    'parameter_sources': parameter_sources
                },
                result={
                    'child_id': child_id,
                    'child_parameters': child_parameters,
                    'child_generation': child.generation,
                    'child_integrity_hash': child.integrity_hash
                },
                genome_id=child_id
            )
        
        return child
    
    def run_evolution(self, generations: int, population_size: int = 50, 
                     return_audit: bool = False) -> Tuple[StrategyGenome, Optional[List[AuditLogEntry]]]:
        """
        Run complete evolution process with deterministic reproducibility
        
        Args:
            generations: Number of generations to evolve
            population_size: Size of population
            return_audit: Whether to return audit log
            
        Returns:
            Tuple of (best_genome, audit_log if requested)
        """
        # Initialize population
        population = self.initialize_population(population_size)
        
        # Mock fitness calculation for demonstration
        # In real implementation, this would call actual backtesting
        for genome in population:
            mock_backtest_results = self._generate_mock_backtest_results(genome)
            self.calculate_fitness(genome, mock_backtest_results)
        
        # Evolve for specified generations
        for gen in range(generations):
            self.current_generation = gen + 1
            population = self.evolve_generation(population)
            
            # Calculate fitness for new genomes
            for genome in population:
                if genome.fitness == 0.0:  # New genome needs fitness calculation
                    mock_backtest_results = self._generate_mock_backtest_results(genome)
                    self.calculate_fitness(genome, mock_backtest_results)
        
        # Get best genome
        best_genome = max(population, key=lambda x: x.fitness)
        
        if return_audit:
            return best_genome, self.audit_log.copy()
        else:
            return best_genome, None
    
    def _generate_mock_backtest_results(self, genome: StrategyGenome) -> Dict[str, Any]:
        """Generate mock backtest results for demonstration purposes"""
        # Use genome parameters to create deterministic but varied results
        param_sum = sum(float(v) for v in genome.parameters.values())
        
        # Create deterministic results based on parameters using separate random state
        mock_random = random.Random(hash(genome.id) % (2**32))
        
        results = {
            'total_return': max(0.01, min(0.5, param_sum * 0.01 + mock_random.uniform(-0.1, 0.1))),
            'sharpe_ratio': max(0.1, min(3.0, param_sum * 0.02 + mock_random.uniform(-0.5, 0.5))),
            'max_drawdown': max(0.01, min(0.5, 0.15 + mock_random.uniform(-0.1, 0.1))),
            'win_rate': max(0.3, min(0.8, 0.5 + mock_random.uniform(-0.2, 0.2))),
            'trades_count': mock_random.randint(50, 200),
            'data_integrity_hash': hashlib.sha256(str(genome.parameters).encode()).hexdigest()
        }
        
        return results
    
    def evolve_generation(self, population: List[StrategyGenome]) -> List[StrategyGenome]:
        """Evolve to next generation with full auditability and integrity verification"""
        # Verify population integrity
        for genome in population:
            if not genome.verify_integrity():
                raise ValueError(f"Genome {genome.id} failed integrity check before evolution")
        
        # Sort by fitness
        population.sort(key=lambda x: x.fitness, reverse=True)
        
        # Log generation evolution start
        if self.enable_audit:
            self._log_operation(
                operation_type=OperationType.GENERATION_EVOLUTION,
                parameters={
                    'generation': self.current_generation,
                    'population_size': len(population),
                    'elite_preservation_rate': self.mutation_rates['elite_preservation'],
                    'crossover_rate': self.mutation_rates['crossover_rate'],
                    'best_fitness': population[0].fitness if population else 0,
                    'avg_fitness': sum(g.fitness for g in population) / len(population) if population else 0
                },
                result={'status': 'starting_evolution'}
            )
        
        # Preserve elite
        elite_count = int(len(population) * self.mutation_rates['elite_preservation'])
        next_generation = population[:elite_count].copy()
        
        offspring_created = []
        selection_log = []
        
        # Generate offspring
        while len(next_generation) < len(population):
            if self._random_state.random() < self.mutation_rates['crossover_rate']:
                # Crossover
                parent1 = self._tournament_selection(population)
                parent2 = self._tournament_selection(population)
                child = self.crossover(parent1, parent2)
                offspring_created.append({
                    'type': 'crossover',
                    'parents': [parent1.id, parent2.id],
                    'child_id': child.id
                })
                selection_log.append(f"Crossover: {parent1.id} + {parent2.id} -> {child.id}")
            else:
                # Mutation
                parent = self._tournament_selection(population)
                child = self.mutate(parent)
                offspring_created.append({
                    'type': 'mutation',
                    'parent': parent.id,
                    'child_id': child.id
                })
                selection_log.append(f"Mutation: {parent.id} -> {child.id}")
            
            next_generation.append(child)
        
        # Log generation evolution completion
        if self.enable_audit:
            self._log_operation(
                operation_type=OperationType.GENERATION_EVOLUTION,
                parameters={
                    'generation': self.current_generation,
                    'elite_count': elite_count,
                    'offspring_count': len(offspring_created)
                },
                result={
                    'status': 'evolution_completed',
                    'next_generation_size': len(next_generation),
                    'offspring_created': offspring_created,
                    'elite_preserved': [g.id for g in next_generation[:elite_count]],
                    'selection_log': selection_log
                }
            )
        
        self.generation_history.append(next_generation.copy())
        return next_generation
    
    def _tournament_selection(self, population: List[StrategyGenome], tournament_size: int = 3) -> StrategyGenome:
        """Tournament selection for parent selection with audit logging"""
        tournament = self._random_state.sample(population, min(tournament_size, len(population)))
        selected = max(tournament, key=lambda x: x.fitness)
        
        # Log selection
        if self.enable_audit:
            self._log_operation(
                operation_type=OperationType.SELECTION,
                parameters={
                    'tournament_size': tournament_size,
                    'candidates': [{'id': g.id, 'fitness': g.fitness} for g in tournament]
                },
                result={
                    'selected_id': selected.id,
                    'selected_fitness': selected.fitness
                },
                genome_id=selected.id
            )
        
        return selected
    
    def _generate_deterministic_id(self) -> str:
        """Generate deterministic ID for reproducible evolution"""
        self._id_counter += 1
        return f"genome_{self.seed}_{self._id_counter:06d}"
    
    def get_evolution_audit_trail(self) -> Dict[str, Any]:
        """Get complete audit trail of evolution process with integrity verification"""
        integrity_valid, integrity_errors = self.verify_audit_integrity()
        
        return {
            'seed': self.seed,
            'generations_count': len(self.generation_history),
            'fitness_function_params': self.fitness_function_params,
            'mutation_rates': self.mutation_rates,
            'audit_log_summary': self.get_audit_log_summary(),
            'integrity_status': {
                'valid': integrity_valid,
                'errors': integrity_errors
            },
            'generation_stats': [
                {
                    'generation': i,
                    'population_size': len(gen),
                    'best_fitness': max(g.fitness for g in gen) if gen else 0,
                    'avg_fitness': sum(g.fitness for g in gen) / len(gen) if gen else 0,
                    'genome_ids': [g.id for g in gen]
                }
                for i, gen in enumerate(self.generation_history)
            ],
            'total_operations': len(self.audit_log),
            'last_operation_hash': self.last_audit_hash
        }
    
    def get_best_genome(self) -> Optional[StrategyGenome]:
        """Get the best genome from all generations"""
        if not self.generation_history:
            return None
        
        all_genomes = []
        for generation in self.generation_history:
            all_genomes.extend(generation)
        
        if not all_genomes:
            return None
        
        return max(all_genomes, key=lambda x: x.fitness)
    
    def export_genome_lineage(self, genome_id: str) -> Dict[str, Any]:
        """Export complete lineage of a specific genome"""
        lineage = []
        
        # Find the genome
        target_genome = None
        for generation in self.generation_history:
            for genome in generation:
                if genome.id == genome_id:
                    target_genome = genome
                    break
            if target_genome:
                break
        
        if not target_genome:
            return {"error": "Genome not found"}
        
        # Trace lineage
        current_genome = target_genome
        while current_genome:
            lineage.append({
                'id': current_genome.id,
                'generation': current_genome.generation,
                'fitness': current_genome.fitness,
                'parameters': current_genome.parameters,
                'mutation_log': current_genome.mutation_log,
                'parent_ids': current_genome.parent_ids
            })
            
            # Find parent (simplified - just take first parent)
            if current_genome.parent_ids:
                parent_id = current_genome.parent_ids[0]
                current_genome = self._find_genome_by_id(parent_id)
            else:
                current_genome = None
        
        return {
            'genome_id': genome_id,
            'lineage': lineage,
            'total_generations': len(lineage)
        }
    
    def _find_genome_by_id(self, genome_id: str) -> Optional[StrategyGenome]:
        """Find genome by ID across all generations"""
        for generation in self.generation_history:
            for genome in generation:
                if genome.id == genome_id:
                    return genome
        return None
    
    def export_complete_audit_log(self, filepath: str) -> bool:
        """Export complete audit log to file with integrity verification"""
        try:
            audit_data = {
                'metadata': {
                    'export_timestamp': datetime.now().isoformat(),
                    'dgm_seed': self.seed,
                    'total_entries': len(self.audit_log),
                    'integrity_verified': self.verify_audit_integrity()[0]
                },
                'audit_log': [
                    {
                        'timestamp': entry.timestamp,
                        'operation_type': entry.operation_type.value,  # Use .value to get string
                        'generation': entry.generation,
                        'genome_id': entry.genome_id,
                        'parameters': entry.parameters,
                        'result': entry.result,
                        'parent_hash': entry.parent_hash,
                        'hash': entry.hash
                    }
                    for entry in self.audit_log
                ],
                'evolution_summary': self.get_evolution_audit_trail()
            }
            
            with open(filepath, 'w') as f:
                json.dump(audit_data, f, indent=2, default=str)
            
            return True
        except Exception as e:
            print(f"Failed to export audit log: {e}")
            return False
    
    def import_and_verify_audit_log(self, filepath: str) -> Tuple[bool, List[str]]:
        """Import and verify audit log from file"""
        errors = []
        try:
            with open(filepath, 'r') as f:
                audit_data = json.load(f)
            
            # Reconstruct audit log
            imported_log = []
            for entry_dict in audit_data['audit_log']:
                # Handle OperationType reconstruction
                op_type_str = entry_dict['operation_type']
                if isinstance(op_type_str, str):
                    # Try to find the enum value by string
                    operation_type = None
                    for op_type in OperationType:
                        if op_type.value == op_type_str:
                            operation_type = op_type
                            break
                    if operation_type is None:
                        errors.append(f"Unknown operation type: {op_type_str}")
                        continue
                else:
                    operation_type = OperationType(op_type_str)
                
                entry = AuditLogEntry(
                    timestamp=entry_dict['timestamp'],
                    operation_type=operation_type,
                    generation=entry_dict['generation'],
                    genome_id=entry_dict['genome_id'],
                    parameters=entry_dict['parameters'],
                    result=entry_dict['result'],
                    parent_hash=entry_dict['parent_hash'],
                    hash=entry_dict['hash']
                )
                imported_log.append(entry)
            
            # Verify integrity
            for i, entry in enumerate(imported_log):
                if not entry.verify_integrity():
                    errors.append(f"Entry {i} failed integrity check")
            
            # Verify chain
            for i in range(1, len(imported_log)):
                if imported_log[i].parent_hash != imported_log[i-1].hash:
                    errors.append(f"Chain broken at entry {i}")
            
            return len(errors) == 0, errors
            
        except Exception as e:
            return False, [f"Import failed: {e}"]