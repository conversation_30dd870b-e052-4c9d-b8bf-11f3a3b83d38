import { describe, it, expect, beforeEach, jest, afterEach } from '@jest/globals';
import { DarwinGodelBridgeService } from '../darwin-godel-bridge.service';
import { spawn } from 'child_process';
import { EventEmitter } from 'events';

// Mock child_process
jest.mock('child_process');

describe('DarwinGodelBridgeService', () => {
  let service: DarwinGodelBridgeService;
  let mockSpawn: jest.MockedFunction<typeof spawn>;

  beforeEach(() => {
    mockSpawn = spawn as jest.MockedFunction<typeof spawn>;
    service = new DarwinGodelBridgeService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('verifyStrategy', () => {
    it('should successfully verify a valid strategy', async () => {
      // Arrange
      const strategyCode = `
def trading_strategy(data, params):
    sma = calculate_sma(data['close'], params['period'])
    current_price = data['close'][-1]
    threshold = params.get('threshold', 0.02)
    
    if current_price < sma[-1] * (1 - threshold):
        return {'signal': 'buy', 'confidence': 0.8}
    elif current_price > sma[-1] * (1 + threshold):
        return {'signal': 'sell', 'confidence': 0.8}
    else:
        return {'signal': 'hold', 'confidence': 0.5}
`;

      const mockProcess = new EventEmitter() as any;
      mockProcess.stdout = new EventEmitter();
      mockProcess.stderr = new EventEmitter();
      mockProcess.stdin = { write: jest.fn(), end: jest.fn() };

      mockSpawn.mockReturnValue(mockProcess);

      // Mock successful response
      const mockResponse = {
        is_valid: true,
        strategy_type: 'mean_reversion',
        risk_score: 0.3,
        robustness_score: 0.8,
        warnings: null
      };

      // Act
      const verificationPromise = service.verifyStrategy(strategyCode);

      // Simulate Python process response
      setTimeout(() => {
        mockProcess.stdout.emit('data', JSON.stringify(mockResponse) + '\n');
        mockProcess.emit('close', 0);
      }, 10);

      const result = await verificationPromise;

      // Assert
      expect(result.isValid).toBe(true);
      expect(result.strategyType).toBe('mean_reversion');
      expect(result.riskScore).toBe(0.3);
      expect(result.robustnessScore).toBe(0.8);
      expect(result.warnings).toBeNull();
      expect(mockSpawn).toHaveBeenCalledWith('python', expect.arrayContaining(['-c']), expect.any(Object));
    });

    it('should handle strategy verification with warnings', async () => {
      // Arrange
      const strategyCode = `
def trading_strategy(data, params):
    if data['close'][-1] == 150.23:
        return {'signal': 'buy', 'confidence': 1.0}
    return {'signal': 'hold', 'confidence': 0.1}
`;

      const mockProcess = new EventEmitter() as any;
      mockProcess.stdout = new EventEmitter();
      mockProcess.stderr = new EventEmitter();
      mockProcess.stdin = { write: jest.fn(), end: jest.fn() };

      mockSpawn.mockReturnValue(mockProcess);

      const mockResponse = {
        is_valid: true,
        strategy_type: 'custom',
        risk_score: 0.2,
        robustness_score: 0.1,
        warnings: ['High overfitting risk - strategy uses very specific conditions']
      };

      // Act
      const verificationPromise = service.verifyStrategy(strategyCode);

      setTimeout(() => {
        mockProcess.stdout.emit('data', JSON.stringify(mockResponse) + '\n');
        mockProcess.emit('close', 0);
      }, 10);

      const result = await verificationPromise;

      // Assert
      expect(result.isValid).toBe(true);
      expect(result.warnings).toHaveLength(1);
      expect(result.warnings![0]).toContain('overfitting risk');
      expect(result.robustnessScore).toBe(0.1);
    });

    it('should reject malicious code', async () => {
      // Arrange
      const maliciousCode = `
import os
os.system('rm -rf /')
def trading_strategy(data, params):
    return {'signal': 'buy'}
`;

      const mockProcess = new EventEmitter() as any;
      mockProcess.stdout = new EventEmitter();
      mockProcess.stderr = new EventEmitter();
      mockProcess.stdin = { write: jest.fn(), end: jest.fn() };

      mockSpawn.mockReturnValue(mockProcess);

      // Act & Assert
      const verificationPromise = service.verifyStrategy(maliciousCode);

      setTimeout(() => {
        mockProcess.stderr.emit('data', 'SecurityError: Restricted code detected\n');
        mockProcess.emit('close', 1);
      }, 10);

      await expect(verificationPromise).rejects.toThrow('SecurityError: Restricted code detected');
    });

    it('should handle Python process errors', async () => {
      // Arrange
      const strategyCode = 'def trading_strategy(): pass';

      const mockProcess = new EventEmitter() as any;
      mockProcess.stdout = new EventEmitter();
      mockProcess.stderr = new EventEmitter();
      mockProcess.stdin = { write: jest.fn(), end: jest.fn() };

      mockSpawn.mockReturnValue(mockProcess);

      // Act & Assert
      const verificationPromise = service.verifyStrategy(strategyCode);

      setTimeout(() => {
        mockProcess.emit('error', new Error('Python process failed'));
      }, 10);

      await expect(verificationPromise).rejects.toThrow('Python process failed');
    });

    it('should handle invalid JSON response', async () => {
      // Arrange
      const strategyCode = 'def trading_strategy(): pass';

      const mockProcess = new EventEmitter() as any;
      mockProcess.stdout = new EventEmitter();
      mockProcess.stderr = new EventEmitter();
      mockProcess.stdin = { write: jest.fn(), end: jest.fn() };

      mockSpawn.mockReturnValue(mockProcess);

      // Act & Assert
      const verificationPromise = service.verifyStrategy(strategyCode);

      setTimeout(() => {
        mockProcess.stdout.emit('data', 'invalid json response\n');
        mockProcess.emit('close', 0);
      }, 10);

      await expect(verificationPromise).rejects.toThrow('Failed to parse verification response');
    });
  });

  describe('verifyWithBacktest', () => {
    it('should run backtest verification with historical data', async () => {
      // Arrange
      const strategyCode = `
def trading_strategy(data, params):
    returns = calculate_returns(data['close'])
    if returns[-1] > 0:
        return {'signal': 'buy', 'confidence': 0.7}
    else:
        return {'signal': 'sell', 'confidence': 0.7}
`;

      const historicalData = {
        close: [100, 101, 102, 101, 103, 104, 103, 105],
        high: [101, 102, 103, 102, 104, 105, 104, 106],
        low: [99, 100, 101, 100, 102, 103, 102, 104],
        volume: [1000, 1100, 1200, 1050, 1300, 1400, 1250, 1500]
      };

      const mockProcess = new EventEmitter() as any;
      mockProcess.stdout = new EventEmitter();
      mockProcess.stderr = new EventEmitter();
      mockProcess.stdin = { write: jest.fn(), end: jest.fn() };

      mockSpawn.mockReturnValue(mockProcess);

      const mockResponse = {
        is_valid: true,
        strategy_type: 'momentum',
        risk_score: 0.4,
        robustness_score: 0.7,
        warnings: null,
        metrics: {
          total_return: 0.15,
          sharpe_ratio: 1.2,
          max_drawdown: 0.05,
          win_rate: 0.65,
          num_trades: 8
        }
      };

      // Act
      const verificationPromise = service.verifyWithBacktest(strategyCode, historicalData, 10000);

      setTimeout(() => {
        mockProcess.stdout.emit('data', JSON.stringify(mockResponse) + '\n');
        mockProcess.emit('close', 0);
      }, 10);

      const result = await verificationPromise;

      // Assert
      expect(result.isValid).toBe(true);
      expect(result.metrics).toBeDefined();
      expect(result.metrics!.totalReturn).toBe(0.15);
      expect(result.metrics!.sharpeRatio).toBe(1.2);
      expect(result.metrics!.maxDrawdown).toBe(0.05);
      expect(result.metrics!.winRate).toBe(0.65);
      expect(result.metrics!.numTrades).toBe(8);
    });
  });

  describe('runMonteCarloValidation', () => {
    it('should run Monte Carlo simulations', async () => {
      // Arrange
      const strategyCode = `
def trading_strategy(data, params):
    sma_short = calculate_sma(data['close'], params['short_period'])
    sma_long = calculate_sma(data['close'], params['long_period'])
    
    if len(sma_short) == 0 or len(sma_long) == 0:
        return {'signal': 'hold', 'confidence': 0.1}
    
    if sma_short[-1] > sma_long[-1]:
        return {'signal': 'buy', 'confidence': 0.75}
    else:
        return {'signal': 'sell', 'confidence': 0.75}
`;

      const mockProcess = new EventEmitter() as any;
      mockProcess.stdout = new EventEmitter();
      mockProcess.stderr = new EventEmitter();
      mockProcess.stdin = { write: jest.fn(), end: jest.fn() };

      mockSpawn.mockReturnValue(mockProcess);

      const mockResponse = {
        success_rate: 0.75,
        avg_sharpe: 0.8,
        avg_return: 0.12,
        consistency_score: 0.85,
        distribution: [
          { total_return: 0.10, sharpe_ratio: 0.7 },
          { total_return: 0.15, sharpe_ratio: 0.9 },
          { total_return: 0.11, sharpe_ratio: 0.8 }
        ]
      };

      // Act
      const validationPromise = service.runMonteCarloValidation(strategyCode, 100, 0.02);

      setTimeout(() => {
        mockProcess.stdout.emit('data', JSON.stringify(mockResponse) + '\n');
        mockProcess.emit('close', 0);
      }, 10);

      const result = await validationPromise;

      // Assert
      expect(result.successRate).toBe(0.75);
      expect(result.avgSharpe).toBe(0.8);
      expect(result.avgReturn).toBe(0.12);
      expect(result.consistencyScore).toBe(0.85);
      expect(result.distribution).toHaveLength(3);
    });
  });

  describe('health check', () => {
    it('should verify Python engine is available', async () => {
      // Arrange
      const mockProcess = new EventEmitter() as any;
      mockProcess.stdout = new EventEmitter();
      mockProcess.stderr = new EventEmitter();
      mockProcess.stdin = { write: jest.fn(), end: jest.fn() };

      mockSpawn.mockReturnValue(mockProcess);

      // Act
      const healthPromise = service.checkHealth();

      setTimeout(() => {
        mockProcess.stdout.emit('data', 'Python engine ready\n');
        mockProcess.emit('close', 0);
      }, 10);

      const isHealthy = await healthPromise;

      // Assert
      expect(isHealthy).toBe(true);
    });

    it('should return false when Python engine is not available', async () => {
      // Arrange
      const mockProcess = new EventEmitter() as any;
      mockProcess.stdout = new EventEmitter();
      mockProcess.stderr = new EventEmitter();
      mockProcess.stdin = { write: jest.fn(), end: jest.fn() };

      mockSpawn.mockReturnValue(mockProcess);

      // Act
      const healthPromise = service.checkHealth();

      setTimeout(() => {
        mockProcess.emit('error', new Error('Python not found'));
      }, 10);

      const isHealthy = await healthPromise;

      // Assert
      expect(isHealthy).toBe(false);
    });
  });
});