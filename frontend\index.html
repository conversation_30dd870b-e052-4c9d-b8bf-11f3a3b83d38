<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    
    <!-- Primary Meta Tags -->
    <title>AI Trading Platform - Advanced Trading Intelligence</title>
    <meta name="title" content="AI Trading Platform - Advanced Trading Intelligence" />
    <meta name="description" content="Professional AI-powered trading platform with advanced backtesting, strategy optimization, and real-time market analysis." />
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet" />
    
    <!-- Theme color -->
    <meta name="theme-color" content="#0ea5e9" />
    
    <!-- Styles for loading screen -->
    <style>
      /* Loading screen styles */
      #initial-loader {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        color: white;
        font-family: 'Inter', sans-serif;
      }
      
      .loader-logo {
        width: 64px;
        height: 64px;
        margin-bottom: 24px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        font-weight: 600;
      }
      
      .loader-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 16px;
      }
      
      .loader-text {
        font-size: 16px;
        font-weight: 500;
        opacity: 0.9;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Hide loader when app loads */
      .app-loaded #initial-loader {
        display: none;
      }
    </style>
  </head>
  
  <body>
    <!-- Loading screen -->
    <div id="initial-loader">
      <div class="loader-logo">
        🤖
      </div>
      <div class="loader-spinner"></div>
      <div class="loader-text">Loading AI Trading Platform...</div>
    </div>
    
    <!-- React root -->
    <div id="root"></div>
    
    <!-- Remove loading screen when app loads -->
    <script>
      window.addEventListener('load', function() {
        setTimeout(function() {
          document.body.classList.add('app-loaded');
        }, 500);
      });
    </script>
    
    <!-- Main app script -->
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>