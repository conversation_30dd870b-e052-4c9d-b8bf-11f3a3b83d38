"""
Configuration Service Implementation

Concrete implementation of IConfigurationService with dependency injection support.
"""

import json
import os
from typing import Dict, Any, Optional
from pathlib import Path

from core.interfaces import IConfigurationService

class FileConfigurationService(IConfigurationService):
    """File-based configuration service"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self._config: Dict[str, Any] = {}
        self._load_config()
    
    def _load_config(self):
        """Load configuration from file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    self._config = json.load(f)
            else:
                # Create default configuration
                self._config = self._get_default_config()
                self._save_config()
        except Exception as e:
            print(f"Error loading config: {e}")
            self._config = self._get_default_config()
    
    def _save_config(self):
        """Save configuration to file"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self._config, f, indent=2)
        except Exception as e:
            print(f"Error saving config: {e}")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            "trading": {
                "max_position_size": 1000,
                "risk_tolerance": 0.02,
                "stop_loss_percentage": 0.05,
                "take_profit_percentage": 0.10,
                "max_daily_trades": 10
            },
            "market_data": {
                "cache_timeout": 60,
                "update_interval": 30,
                "default_period": "1d",
                "default_interval": "1m"
            },
            "strategy": {
                "execution_timeout": 30,
                "max_memory_usage": 100,  # MB
                "allowed_imports": ["math", "statistics", "datetime"]
            },
            "risk_management": {
                "max_portfolio_risk": 0.20,
                "max_single_position_risk": 0.05,
                "correlation_threshold": 0.7
            },
            "logging": {
                "level": "INFO",
                "file": "trading.log",
                "max_size": 10,  # MB
                "backup_count": 5
            },
            "notifications": {
                "enabled": True,
                "email_alerts": False,
                "trade_notifications": True
            }
        }
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """Get configuration value using dot notation"""
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set_config(self, key: str, value: Any) -> None:
        """Set configuration value using dot notation"""
        keys = key.split('.')
        config = self._config
        
        # Navigate to the parent dictionary
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # Set the value
        config[keys[-1]] = value
        self._save_config()
    
    def get_trading_parameters(self) -> Dict[str, Any]:
        """Get trading parameters"""
        return self._config.get('trading', {})
    
    def get_all_config(self) -> Dict[str, Any]:
        """Get all configuration"""
        return self._config.copy()

class EnvironmentConfigurationService(IConfigurationService):
    """Environment variable-based configuration service"""
    
    def __init__(self):
        self._config = self._load_from_environment()
    
    def _load_from_environment(self) -> Dict[str, Any]:
        """Load configuration from environment variables"""
        config = {}
        
        # Trading configuration
        config['trading'] = {
            'max_position_size': int(os.getenv('TRADING_MAX_POSITION_SIZE', '1000')),
            'risk_tolerance': float(os.getenv('TRADING_RISK_TOLERANCE', '0.02')),
            'stop_loss_percentage': float(os.getenv('TRADING_STOP_LOSS', '0.05')),
            'take_profit_percentage': float(os.getenv('TRADING_TAKE_PROFIT', '0.10')),
            'max_daily_trades': int(os.getenv('TRADING_MAX_DAILY_TRADES', '10'))
        }
        
        # Market data configuration
        config['market_data'] = {
            'cache_timeout': int(os.getenv('MARKET_DATA_CACHE_TIMEOUT', '60')),
            'update_interval': int(os.getenv('MARKET_DATA_UPDATE_INTERVAL', '30')),
            'default_period': os.getenv('MARKET_DATA_DEFAULT_PERIOD', '1d'),
            'default_interval': os.getenv('MARKET_DATA_DEFAULT_INTERVAL', '1m')
        }
        
        # Strategy configuration
        config['strategy'] = {
            'execution_timeout': int(os.getenv('STRATEGY_EXECUTION_TIMEOUT', '30')),
            'max_memory_usage': int(os.getenv('STRATEGY_MAX_MEMORY', '100')),
            'allowed_imports': os.getenv('STRATEGY_ALLOWED_IMPORTS', 'math,statistics,datetime').split(',')
        }
        
        return config
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """Get configuration value using dot notation"""
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set_config(self, key: str, value: Any) -> None:
        """Set configuration value (not supported for environment config)"""
        raise NotImplementedError("Environment configuration is read-only")
    
    def get_trading_parameters(self) -> Dict[str, Any]:
        """Get trading parameters"""
        return self._config.get('trading', {})

class MockConfigurationService(IConfigurationService):
    """Mock configuration service for testing"""
    
    def __init__(self, initial_config: Optional[Dict[str, Any]] = None):
        self._config = initial_config or {
            "trading": {
                "max_position_size": 100,
                "risk_tolerance": 0.01,
                "stop_loss_percentage": 0.02,
                "take_profit_percentage": 0.05
            },
            "market_data": {
                "cache_timeout": 10,
                "update_interval": 5
            },
            "strategy": {
                "execution_timeout": 10,
                "max_memory_usage": 50
            }
        }
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """Get configuration value"""
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set_config(self, key: str, value: Any) -> None:
        """Set configuration value"""
        keys = key.split('.')
        config = self._config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def get_trading_parameters(self) -> Dict[str, Any]:
        """Get trading parameters"""
        return self._config.get('trading', {})