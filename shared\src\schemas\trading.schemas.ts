import { z } from 'zod';

// Trading Instrument Schema
export const TradingInstrumentSchema = z.object({
  symbol: z.string().min(1),
  name: z.string().min(1),
  type: z.enum(['stock', 'crypto', 'forex', 'commodity', 'index']),
  exchange: z.string().min(1),
  currency: z.string().length(3),
  precision: z.number().int().min(0).max(8),
});

// OHLCV Data Schema
export const OHLCVSchema = z.object({
  timestamp: z.date(),
  open: z.number().positive(),
  high: z.number().positive(),
  low: z.number().positive(),
  close: z.number().positive(),
  volume: z.number().nonnegative(),
  symbol: z.string().min(1),
});

// Trading Signal Schema
export const TradingSignalSchema = z.object({
  id: z.string().uuid(),
  symbol: z.string().min(1),
  type: z.enum(['buy', 'sell', 'hold']),
  strength: z.number().min(0).max(1),
  price: z.number().positive(),
  timestamp: z.date(),
  source: z.string().min(1),
  metadata: z.record(z.unknown()).optional(),
});

// Portfolio Position Schema
export const PortfolioPositionSchema = z.object({
  id: z.string().uuid(),
  symbol: z.string().min(1),
  quantity: z.number(),
  averagePrice: z.number().positive(),
  currentPrice: z.number().positive(),
  unrealizedPnL: z.number(),
  realizedPnL: z.number(),
  openDate: z.date(),
  lastUpdated: z.date(),
});

// Trade Execution Schema
export const TradeExecutionSchema = z.object({
  id: z.string().uuid(),
  symbol: z.string().min(1),
  side: z.enum(['buy', 'sell']),
  quantity: z.number().positive(),
  price: z.number().positive(),
  executedAt: z.date(),
  orderId: z.string().optional(),
  commission: z.number().nonnegative(),
  status: z.enum(['pending', 'filled', 'cancelled', 'rejected']),
});

// Risk Metrics Schema
export const RiskMetricsSchema = z.object({
  sharpeRatio: z.number().optional(),
  maxDrawdown: z.number().min(0).max(1),
  volatility: z.number().nonnegative(),
  beta: z.number().optional(),
  var95: z.number().optional(), // Value at Risk 95%
  expectedShortfall: z.number().optional(),
  calmarRatio: z.number().optional(),
});

// Performance Metrics Schema
export const PerformanceMetricsSchema = z.object({
  totalReturn: z.number(),
  annualizedReturn: z.number(),
  totalTrades: z.number().nonnegative(),
  winRate: z.number().min(0).max(1),
  profitFactor: z.number().nonnegative(),
  averageWin: z.number(),
  averageLoss: z.number(),
  largestWin: z.number(),
  largestLoss: z.number(),
  consecutiveWins: z.number().nonnegative(),
  consecutiveLosses: z.number().nonnegative(),
});

// Strategy Performance Schema
export const StrategyPerformanceSchema = z.object({
  strategyId: z.string().uuid(),
  period: z.object({
    start: z.date(),
    end: z.date(),
  }),
  performance: PerformanceMetricsSchema,
  risk: RiskMetricsSchema,
  trades: z.array(TradeExecutionSchema),
  equity: z.array(z.object({
    timestamp: z.date(),
    value: z.number(),
  })),
});

// Market Data Request Schema
export const MarketDataRequestSchema = z.object({
  symbols: z.array(z.string().min(1)),
  startDate: z.date(),
  endDate: z.date(),
  interval: z.enum(['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w', '1M']),
  dataType: z.enum(['ohlcv', 'trades', 'orderbook']).default('ohlcv'),
});

// Types
export type TradingInstrument = z.infer<typeof TradingInstrumentSchema>;
export type OHLCV = z.infer<typeof OHLCVSchema>;
export type TradingSignal = z.infer<typeof TradingSignalSchema>;
export type PortfolioPosition = z.infer<typeof PortfolioPositionSchema>;
export type TradeExecution = z.infer<typeof TradeExecutionSchema>;
export type RiskMetrics = z.infer<typeof RiskMetricsSchema>;
export type PerformanceMetrics = z.infer<typeof PerformanceMetricsSchema>;
export type StrategyPerformance = z.infer<typeof StrategyPerformanceSchema>;
export type MarketDataRequest = z.infer<typeof MarketDataRequestSchema>;