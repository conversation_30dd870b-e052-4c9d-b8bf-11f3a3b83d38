/**
 * API Hook - Simplified API state management
 * Provides loading, error, and data states for API calls
 */

import { useState, useCallback, useRef } from 'react';

interface UseApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

interface UseApiOptions {
  onSuccess?: (data: any) => void;
  onError?: (error: string) => void;
  resetOnExecute?: boolean;
}

export function useApi<T>(options: UseApiOptions = {}) {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: false,
    error: null,
    lastUpdated: null,
  });

  const abortControllerRef = useRef<AbortController | null>(null);
  const { onSuccess, onError, resetOnExecute = true } = options;

  const execute = useCallback(async (apiCall: () => Promise<T>): Promise<T | null> => {
    // Cancel any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    if (resetOnExecute) {
      setState(prev => ({ 
        ...prev, 
        loading: true, 
        error: null 
      }));
    } else {
      setState(prev => ({ 
        ...prev, 
        loading: true 
      }));
    }
    
    try {
      const result = await apiCall();
      
      // Check if request was aborted
      if (abortControllerRef.current?.signal.aborted) {
        return null;
      }

      setState({
        data: result,
        loading: false,
        error: null,
        lastUpdated: new Date(),
      });

      onSuccess?.(result);
      return result;
    } catch (error: any) {
      // Check if request was aborted
      if (abortControllerRef.current?.signal.aborted) {
        return null;
      }

      const errorMessage = error.response?.data?.error?.message || 
                          error.response?.data?.detail || 
                          error.message || 
                          'An error occurred';

      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage,
        lastUpdated: new Date(),
      }));

      onError?.(errorMessage);
      throw error;
    } finally {
      abortControllerRef.current = null;
    }
  }, [onSuccess, onError, resetOnExecute]);

  const reset = useCallback(() => {
    // Cancel any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    setState({
      data: null,
      loading: false,
      error: null,
      lastUpdated: null,
    });
  }, []);

  const setData = useCallback((data: T | null) => {
    setState(prev => ({
      ...prev,
      data,
      lastUpdated: new Date(),
    }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setState(prev => ({
      ...prev,
      error,
      lastUpdated: new Date(),
    }));
  }, []);

  const abort = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  return {
    ...state,
    execute,
    reset,
    setData,
    setError,
    abort,
    isLoading: state.loading,
    hasData: state.data !== null,
    hasError: state.error !== null,
  };
}

// Specialized hook for paginated API calls
export function usePaginatedApi<T>(options: UseApiOptions = {}) {
  const api = useApi<{
    items: T[];
    total: number;
    page: number;
    totalPages: number;
    hasMore: boolean;
  }>(options);

  const [allItems, setAllItems] = useState<T[]>([]);

  const loadPage = useCallback(async (
    apiCall: (page: number, limit: number) => Promise<{
      items: T[];
      total: number;
      page: number;
      totalPages: number;
      hasMore: boolean;
    }>,
    page = 1,
    limit = 20,
    append = false
  ) => {
    const result = await api.execute(() => apiCall(page, limit));
    
    if (result) {
      if (append && page > 1) {
        setAllItems(prev => [...prev, ...result.items]);
      } else {
        setAllItems(result.items);
      }
    }

    return result;
  }, [api]);

  const reset = useCallback(() => {
    api.reset();
    setAllItems([]);
  }, [api]);

  return {
    ...api,
    allItems,
    loadPage,
    reset: reset,
    itemCount: allItems.length,
  };
}

// Hook for optimistic updates
export function useOptimisticApi<T>(options: UseApiOptions = {}) {
  const api = useApi<T>(options);
  const [optimisticData, setOptimisticData] = useState<T | null>(null);

  const executeOptimistic = useCallback(async (
    apiCall: () => Promise<T>,
    optimisticValue: T
  ): Promise<T | null> => {
    // Set optimistic value immediately
    setOptimisticData(optimisticValue);

    try {
      const result = await api.execute(apiCall);
      setOptimisticData(null); // Clear optimistic data on success
      return result;
    } catch (error) {
      setOptimisticData(null); // Clear optimistic data on error
      throw error;
    }
  }, [api]);

  const reset = useCallback(() => {
    api.reset();
    setOptimisticData(null);
  }, [api]);

  return {
    ...api,
    executeOptimistic,
    reset,
    displayData: optimisticData || api.data,
    isOptimistic: optimisticData !== null,
  };
}

// Hook for automatic retries
export function useRetryApi<T>(
  maxRetries = 3,
  retryDelay = 1000,
  options: UseApiOptions = {}
) {
  const api = useApi<T>(options);
  const [retryCount, setRetryCount] = useState(0);

  const executeWithRetry = useCallback(async (
    apiCall: () => Promise<T>
  ): Promise<T | null> => {
    let attempt = 0;
    
    while (attempt <= maxRetries) {
      try {
        setRetryCount(attempt);
        const result = await api.execute(apiCall);
        setRetryCount(0); // Reset on success
        return result;
      } catch (error) {
        attempt++;
        
        if (attempt > maxRetries) {
          setRetryCount(0);
          throw error;
        }
        
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
      }
    }
    
    return null;
  }, [api, maxRetries, retryDelay]);

  return {
    ...api,
    executeWithRetry,
    retryCount,
    isRetrying: retryCount > 0,
  };
}

// Hook for caching API results
export function useCachedApi<T>(
  cacheKey: string,
  ttl = 5 * 60 * 1000, // 5 minutes default
  options: UseApiOptions = {}
) {
  const api = useApi<T>(options);
  
  const executeWithCache = useCallback(async (
    apiCall: () => Promise<T>,
    forceRefresh = false
  ): Promise<T | null> => {
    if (!forceRefresh) {
      // Check cache first
      const cached = getCachedData<T>(cacheKey, ttl);
      if (cached) {
        api.setData(cached);
        return cached;
      }
    }

    const result = await api.execute(apiCall);
    
    if (result) {
      setCachedData(cacheKey, result);
    }

    return result;
  }, [api, cacheKey, ttl]);

  const invalidateCache = useCallback(() => {
    removeCachedData(cacheKey);
  }, [cacheKey]);

  return {
    ...api,
    executeWithCache,
    invalidateCache,
  };
}

// Cache utilities
interface CachedItem<T> {
  data: T;
  timestamp: number;
}

function getCachedData<T>(key: string, ttl: number): T | null {
  try {
    const cached = localStorage.getItem(`api_cache_${key}`);
    if (!cached) return null;

    const item: CachedItem<T> = JSON.parse(cached);
    const now = Date.now();

    if (now - item.timestamp > ttl) {
      localStorage.removeItem(`api_cache_${key}`);
      return null;
    }

    return item.data;
  } catch {
    return null;
  }
}

function setCachedData<T>(key: string, data: T): void {
  try {
    const item: CachedItem<T> = {
      data,
      timestamp: Date.now(),
    };
    localStorage.setItem(`api_cache_${key}`, JSON.stringify(item));
  } catch {
    // Ignore storage errors
  }
}

function removeCachedData(key: string): void {
  try {
    localStorage.removeItem(`api_cache_${key}`);
  } catch {
    // Ignore storage errors
  }
}