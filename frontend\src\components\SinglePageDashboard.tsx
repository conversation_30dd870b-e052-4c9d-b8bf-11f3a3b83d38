import React, { useState } from 'react';
import { 
  <PERSON>t, 
  BarChart3, 
  DollarSign, 
  Target, 
  Activity, 
  Zap,
  User,
  MessageCircle,
  Send,
  TestTube,
  Save,
  Play,
  Pause,
  Copy,
  TrendingUp
} from 'lucide-react';

interface Strategy {
  id: string;
  name: string;
  status: 'active' | 'paused' | 'stopped';
  pnl: number;
  trades: number;
  winRate: number;
  type: string;
}

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  code?: string;
  timestamp: Date;
}

interface AIPrompt {
  id: string;
  title: string;
  description: string;
  category: string;
  prompt_template: string;
  variables: string[];
  example_usage: string;
  detailed_info: string;
}

const SinglePageDashboard: React.FC = () => {
  const [chatMessages, setChatMessages] = useState<Message[]>([
    {
      id: '1',
      role: 'assistant',
      content: "Hello! I'm your AI trading strategy assistant. Describe the strategy you'd like to create and I'll generate complete Python code for you.\n\nFor example: 'Create a mean reversion strategy for EUR/USD using RSI with 2% risk per trade'",
      timestamp: new Date()
    }
  ]);
  const [chatInput, setChatInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const [copiedPromptId, setCopiedPromptId] = useState<string | null>(null);

  // Professional AI Prompts
  const aiPrompts: AIPrompt[] = [
    {
      id: "market_scanner",
      title: "Market Analysis & Asset Scanner",
      description: "Identify trading assets that meet specific criteria",
      category: "Market Analysis",
      prompt_template: "Act as a day trading assistant. Your task is to identify trading assets that meet the specified {criteria}. Utilize your expertise and available market analysis tools to scan, filter, and evaluate potential assets for trading opportunities.\n\nPlease provide:\n1. A list of 5-10 assets that match the criteria\n2. Brief analysis of why each asset meets the requirements\n3. Current market conditions affecting these assets\n4. Risk factors to consider\n5. Recommended timeframes for analysis\n\nCriteria: {criteria}",
      variables: ["criteria"],
      example_usage: "criteria: 'tech stocks with strong momentum and AI exposure'",
      detailed_info: "This prompt helps you systematically scan markets for opportunities. It provides structured analysis including risk assessment and timing recommendations."
    },
    {
      id: "technical_analyzer",
      title: "Comprehensive Technical Analysis",
      description: "Detailed technical analysis with entry/exit points",
      category: "Technical Analysis",
      prompt_template: "Act as an experienced day trader. Your objective is to analyze the price and volume patterns of {trading_asset} to identify potential buying or selling opportunities.\n\nProvide a comprehensive analysis including:\n1. Current trend analysis (short, medium, long-term)\n2. Key support and resistance levels\n3. Technical indicators analysis (RSI, MACD, Moving Averages)\n4. Volume analysis and patterns\n5. Entry points with specific price levels\n6. Stop-loss recommendations\n7. Take-profit targets\n8. Risk-reward ratio assessment\n9. Market sentiment indicators\n10. Timeframe recommendations\n\nAsset: {trading_asset}",
      variables: ["trading_asset"],
      example_usage: "trading_asset: 'NVDA (NVIDIA Corporation)'",
      detailed_info: "Provides comprehensive technical analysis with specific entry/exit points, risk management, and multi-timeframe perspective."
    },
    {
      id: "trade_execution",
      title: "Optimal Trade Execution Strategy",
      description: "Determine optimal entry, stop-loss, and target points",
      category: "Trade Execution",
      prompt_template: "Act as an experienced day trader. Based on your comprehensive analysis of current market conditions for {asset}, provide an optimal trade execution strategy.\n\nInclude:\n1. Precise entry price and entry conditions\n2. Stop-loss placement with reasoning\n3. Multiple take-profit targets\n4. Position sizing recommendations\n5. Risk management rules\n6. Market timing considerations\n7. Alternative scenarios (if trade goes against you)\n8. Exit strategy for different market conditions\n\nAsset: {asset}\nAccount Size: {account_size}\nRisk Tolerance: {risk_tolerance}%",
      variables: ["asset", "account_size", "risk_tolerance"],
      example_usage: "asset: 'TSLA', account_size: '$50000', risk_tolerance: '2'",
      detailed_info: "Creates detailed execution plans with precise entry/exit criteria, position sizing, and contingency planning."
    },
    {
      id: "strategy_backtest",
      title: "Strategy Backtesting & Validation",
      description: "Backtest and validate trading strategies",
      category: "Strategy Development",
      prompt_template: "Act as a quantitative analyst specializing in strategy backtesting. Help me design a comprehensive backtesting framework for {strategy_type}.\n\nInclude:\n1. Backtesting methodology and approach\n2. Data requirements and sources\n3. Performance metrics to track\n4. Risk metrics and drawdown analysis\n5. Statistical significance testing\n6. Out-of-sample validation methods\n7. Parameter optimization guidelines\n8. Overfitting prevention techniques\n9. Walk-forward analysis framework\n10. Strategy robustness assessment\n\nStrategy: {strategy_type}\nAsset Class: {asset_class}\nTimeframe: {timeframe}",
      variables: ["strategy_type", "asset_class", "timeframe"],
      example_usage: "strategy_type: 'RSI mean reversion', asset_class: 'forex', timeframe: '1 hour'",
      detailed_info: "Provides comprehensive backtesting framework with statistical validation and robustness testing."
    }
  ];

  const strategies: Strategy[] = [
    { id: '1', name: 'RSI Mean Reversion', status: 'active', pnl: 1247, trades: 23, winRate: 65, type: 'Mean Reversion' },
    { id: '2', name: 'MACD Momentum', status: 'active', pnl: 892, trades: 18, winRate: 72, type: 'Momentum' },
    { id: '3', name: 'Bollinger Bands', status: 'paused', pnl: 456, trades: 12, winRate: 58, type: 'Volatility' }
  ];

  const handleChatSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!chatInput.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: chatInput,
      timestamp: new Date()
    };

    setChatMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    // Simulate AI response
    setTimeout(() => {
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: `Here's a Python trading strategy based on your request: "${chatInput}"`,
        code: `# Generated Trading Strategy
import pandas as pd
import numpy as np

class TradingStrategy:
    def __init__(self, symbol='EURUSD'):
        self.symbol = symbol
        self.position = 0
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        # Strategy logic based on your requirements
        data['signal'] = 0
        # Add your specific indicators and conditions here
        return data
    
    def execute_trade(self, signal: int, price: float):
        # Execute trade based on signal
        if signal == 1:  # Buy signal
            print(f"BUY {self.symbol} at {price}")
        elif signal == -1:  # Sell signal
            print(f"SELL {self.symbol} at {price}")`,
        timestamp: new Date()
      };

      setChatMessages(prev => [...prev, aiResponse]);
      setIsLoading(false);
    }, 2000);

    setChatInput('');
  };



  const copyPromptToClipboard = async (prompt: string, promptId: string) => {
    try {
      await navigator.clipboard.writeText(prompt);
      setCopiedPromptId(promptId);
      setTimeout(() => setCopiedPromptId(null), 2000);
    } catch (err) {
      console.error('Failed to copy prompt:', err);
    }
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      'Market Analysis': 'bg-blue-100 text-blue-800',
      'Technical Analysis': 'bg-green-100 text-green-800',
      'Trade Execution': 'bg-purple-100 text-purple-800',
      'Strategy Development': 'bg-indigo-100 text-indigo-800',
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-100';
      case 'paused': return 'text-yellow-600 bg-yellow-100';
      case 'stopped': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation Header */}
      <nav className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Bot className="w-6 h-6 text-blue-600" />
              </div>
              <span className="text-xl font-bold text-gray-900">AI Trading Platform</span>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>MT5 Connected</span>
              </div>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 space-y-8">
        {/* Hero Section */}
        <section className="bg-gradient-to-r from-blue-600 to-indigo-700 rounded-xl p-8 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2">🚀 AI-Enhanced Trading Platform</h1>
              <p className="text-blue-100 text-lg">Transform natural language into production-ready Python trading strategies</p>
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold">12</div>
              <div className="text-blue-100">Active Strategies</div>
            </div>
          </div>
        </section>

        {/* Key Metrics */}
        <section>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">📊 Key Metrics</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total P&L</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">$2,847</p>
                  <p className="text-sm text-gray-500 mt-1">+$127 today</p>
                </div>
                <div className="p-3 rounded-lg bg-green-500">
                  <DollarSign className="w-6 h-6 text-white" />
                </div>
              </div>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Win Rate</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">68%</p>
                  <p className="text-sm text-gray-500 mt-1">+2% this week</p>
                </div>
                <div className="p-3 rounded-lg bg-blue-500">
                  <Target className="w-6 h-6 text-white" />
                </div>
              </div>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Strategies</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">12</p>
                  <p className="text-sm text-gray-500 mt-1">+3 this month</p>
                </div>
                <div className="p-3 rounded-lg bg-purple-500">
                  <Activity className="w-6 h-6 text-white" />
                </div>
              </div>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">MT5 Status</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">Connected</p>
                  <p className="text-sm text-gray-500 mt-1">Stable connection</p>
                </div>
                <div className="p-3 rounded-lg bg-green-500">
                  <Zap className="w-6 h-6 text-white" />
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Professional AI Prompts */}
        <section>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">🧠 Professional AI Prompts</h2>
          <div className="grid gap-6 md:grid-cols-2">
            {aiPrompts.map((prompt) => (
              <div key={prompt.id} className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-1">{prompt.title}</h3>
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 text-xs rounded-full ${getCategoryColor(prompt.category)}`}>
                        {prompt.category}
                      </span>
                    </div>
                  </div>
                  <button
                    onClick={() => copyPromptToClipboard(prompt.prompt_template, prompt.id)}
                    className="p-2 rounded-lg transition-colors hover:bg-gray-100"
                    title="Copy prompt"
                  >
                    <Copy className="h-4 w-4" />
                  </button>
                </div>
                
                <p className="text-sm text-gray-600 mb-4">{prompt.description}</p>
                
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-700 font-mono leading-relaxed">
                    {prompt.prompt_template.substring(0, 200)}...
                  </p>
                </div>
                
                {copiedPromptId === prompt.id && (
                  <div className="mt-2 text-sm text-green-600 font-medium">
                    ✓ Copied to clipboard!
                  </div>
                )}
              </div>
            ))}
          </div>
        </section>

        {/* AI Strategy Helper */}
        <section>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">💬 AI Strategy Helper</h2>
          <div className="bg-white rounded-xl shadow-sm border border-gray-200">
            <div className="p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <MessageCircle className="w-5 h-5 text-blue-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900">Strategy Generator</h3>
                <div className="flex-1"></div>
                <div className="text-sm text-gray-500">Powered by AI</div>
              </div>
              
              {/* Chat Messages */}
              <div className="h-96 overflow-y-auto mb-4 p-4 bg-gray-50 rounded-lg">
                <div className="space-y-4">
                  {chatMessages.map((message) => (
                    <div key={message.id} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                      <div className={`max-w-3xl ${message.role === 'user' ? 'bg-blue-600 text-white' : 'bg-white border border-gray-200'} rounded-lg p-4`}>
                        <div className="flex items-start space-x-3">
                          <div className={`p-1 rounded-full ${message.role === 'user' ? 'bg-blue-500' : 'bg-gray-100'}`}>
                            {message.role === 'user' ? 
                              <User className="w-4 h-4 text-white" /> : 
                              <Bot className="w-4 h-4 text-gray-600" />
                            }
                          </div>
                          <div className="flex-1">
                            <div className="whitespace-pre-wrap text-sm">{message.content}</div>
                            {message.code && (
                              <div className="mt-3">
                                <div className="bg-gray-900 text-green-400 p-4 rounded-lg text-sm font-mono overflow-x-auto">
                                  <pre>{message.code}</pre>
                                </div>
                                <div className="flex space-x-2 mt-3">
                                  <button className="flex items-center space-x-1 px-3 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700">
                                    <Save className="w-3 h-3" />
                                    <span>Save</span>
                                  </button>
                                  <button className="flex items-center space-x-1 px-3 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700">
                                    <TestTube className="w-3 h-3" />
                                    <span>Test</span>
                                  </button>
                                  <button className="flex items-center space-x-1 px-3 py-1 bg-purple-600 text-white rounded text-xs hover:bg-purple-700">
                                    <TrendingUp className="w-3 h-3" />
                                    <span>Backtest</span>
                                  </button>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                  {isLoading && (
                    <div className="flex justify-start">
                      <div className="bg-white border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center space-x-2">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                          <span className="text-sm text-gray-600">AI is generating your strategy...</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Chat Input */}
              <form onSubmit={handleChatSubmit} className="flex space-x-3">
                <input
                  type="text"
                  value={chatInput}
                  onChange={(e) => setChatInput(e.target.value)}
                  placeholder="Describe your trading strategy idea..."
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={isLoading}
                />
                <button
                  type="submit"
                  disabled={isLoading || !chatInput.trim()}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  <Send className="w-4 h-4" />
                  <span>Send</span>
                </button>
              </form>
            </div>
          </div>
        </section>

        {/* Active Strategies */}
        <section>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">📈 Active Strategies</h2>
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Strategy</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">P&L</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trades</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Win Rate</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {strategies.map((strategy) => (
                    <tr key={strategy.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{strategy.name}</div>
                          <div className="text-sm text-gray-500">{strategy.type}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(strategy.status)}`}>
                          <span className="ml-1 capitalize">{strategy.status}</span>
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`text-sm font-medium ${strategy.pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          ${strategy.pnl >= 0 ? '+' : ''}{strategy.pnl}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {strategy.trades}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {strategy.winRate}%
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        <button className="text-blue-600 hover:text-blue-900">
                          {strategy.status === 'active' ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                        </button>
                        <button className="text-green-600 hover:text-green-900">
                          <BarChart3 className="w-4 h-4" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default SinglePageDashboard;