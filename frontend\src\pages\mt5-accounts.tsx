/**
 * MT5 Accounts Page
 * Allows users to manage their MT5 accounts
 */

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { PlusIcon, TrashIcon } from '@heroicons/react/24/outline';

import { PageHeader } from '@/components/ui/page-header';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Spinner } from '@/components/ui/spinner';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';

import { api } from '@/services/api';

interface MT5Account {
  id: string;
  name: string;
  server: string;
  login: string;
  is_demo: boolean;
  is_active: boolean;
  created_at: string;
  last_connected_at: string | null;
}

export default function MT5AccountsPage() {
  const queryClient = useQueryClient();
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [newAccount, setNewAccount] = useState({
    name: '',
    server: '',
    login: '',
    password: '',
    is_demo: true
  });

  // Fetch MT5 accounts
  const { data: accounts, isLoading, error } = useQuery({
    queryKey: ['mt5-accounts'],
    queryFn: async () => {
      try {
        // Add MT5 accounts endpoint to API service
        const accounts = await api.getMT5Accounts();
        return accounts;
      } catch (error) {
        console.error('Error fetching MT5 accounts:', error);
        throw error;
      }
    }
  });

  // Create MT5 account mutation
  const createAccountMutation = useMutation({
    mutationFn: async (accountData: typeof newAccount) => {
      try {
        // Add create MT5 account endpoint to API service
        const result = await api.createMT5Account(accountData);
        return result;
      } catch (error) {
        console.error('Error creating MT5 account:', error);
        throw error;
      }
    },
    onSuccess: () => {
      toast.success('MT5 account created successfully');
      setIsAddDialogOpen(false);
      setNewAccount({
        name: '',
        server: '',
        login: '',
        password: '',
        is_demo: true
      });
      queryClient.invalidateQueries({ queryKey: ['mt5-accounts'] });
    },
    onError: (error: any) => {
      toast.error(`Failed to create MT5 account: ${error.message}`);
    }
  });

  // Delete MT5 account mutation
  const deleteAccountMutation = useMutation({
    mutationFn: async (accountId: string) => {
      try {
        // Add delete MT5 account endpoint to API service
        const result = await api.deleteMT5Account(accountId);
        return result;
      } catch (error) {
        console.error('Error deleting MT5 account:', error);
        throw error;
      }
    },
    onSuccess: () => {
      toast.success('MT5 account deleted successfully');
      queryClient.invalidateQueries({ queryKey: ['mt5-accounts'] });
    },
    onError: (error: any) => {
      toast.error(`Failed to delete MT5 account: ${error.message}`);
    }
  });

  const handleCreateAccount = (e: React.FormEvent) => {
    e.preventDefault();
    createAccountMutation.mutate(newAccount);
  };

  const handleDeleteAccount = (accountId: string) => {
    if (confirm('Are you sure you want to delete this MT5 account?')) {
      deleteAccountMutation.mutate(accountId);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <PageHeader
        title="MT5 Accounts"
        description="Manage your MetaTrader 5 accounts"
        actions={
          <Button onClick={() => setIsAddDialogOpen(true)}>
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Account
          </Button>
        }
      />

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <Spinner size="lg" />
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Error!</strong>
          <span className="block sm:inline"> Failed to load MT5 accounts.</span>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-6">
          {accounts && accounts.length > 0 ? (
            accounts.map((account: MT5Account) => (
              <Card key={account.id} className="p-6">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-lg font-semibold">{account.name}</h3>
                    <p className="text-sm text-gray-500">Login: {account.login}</p>
                    <p className="text-sm text-gray-500">Server: {account.server}</p>
                    <div className="mt-2 space-x-2">
                      <Badge variant={account.is_demo ? "secondary" : "default"}>
                        {account.is_demo ? 'Demo' : 'Live'}
                      </Badge>
                      <Badge variant={account.is_active ? "success" : "outline"}>
                        {account.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                    <p className="text-xs text-gray-400 mt-4">
                      Created: {new Date(account.created_at).toLocaleDateString()}
                    </p>
                    {account.last_connected_at && (
                      <p className="text-xs text-gray-400">
                        Last connected: {new Date(account.last_connected_at).toLocaleString()}
                      </p>
                    )}
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleDeleteAccount(account.id)}
                    className="text-red-500 hover:text-red-700 hover:bg-red-50"
                  >
                    <TrashIcon className="h-5 w-5" />
                  </Button>
                </div>
              </Card>
            ))
          ) : (
            <div className="col-span-full text-center py-12 bg-gray-50 rounded-lg">
              <p className="text-gray-500">No MT5 accounts found. Add your first account to get started.</p>
            </div>
          )}
        </div>
      )}

      {/* Add Account Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add MT5 Account</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleCreateAccount}>
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Account Name</Label>
                  <Input
                    id="name"
                    placeholder="My MT5 Account"
                    value={newAccount.name}
                    onChange={(e) => setNewAccount({ ...newAccount, name: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="server">Server</Label>
                  <Input
                    id="server"
                    placeholder="demo.examplebroker.com"
                    value={newAccount.server}
                    onChange={(e) => setNewAccount({ ...newAccount, server: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="login">Login</Label>
                  <Input
                    id="login"
                    placeholder="********"
                    value={newAccount.login}
                    onChange={(e) => setNewAccount({ ...newAccount, login: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    placeholder="••••••••"
                    value={newAccount.password}
                    onChange={(e) => setNewAccount({ ...newAccount, password: e.target.value })}
                    required
                  />
                </div>
                <div className="flex items-center space-x-2 pt-2">
                  <Checkbox
                    id="is_demo"
                    checked={newAccount.is_demo}
                    onCheckedChange={(checked) => 
                      setNewAccount({ ...newAccount, is_demo: checked as boolean })
                    }
                  />
                  <Label htmlFor="is_demo">This is a demo account</Label>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsAddDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={createAccountMutation.isPending}
              >
                {createAccountMutation.isPending ? (
                  <>
                    <Spinner size="sm" className="mr-2" />
                    Creating...
                  </>
                ) : (
                  'Add Account'
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}