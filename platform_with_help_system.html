<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Trading Platform - With Help System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header-left h1 {
            font-size: 2rem;
            margin-bottom: 5px;
        }
        
        .header-left p {
            opacity: 0.9;
            font-size: 1rem;
        }
        
        .header-right {
            display: flex;
            gap: 15px;
            align-items: center;
        }
        
        .help-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 2px solid rgba(255,255,255,0.3);
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .help-btn:hover {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
        }
        
        .nav-tabs {
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            padding: 0 30px;
            display: flex;
            gap: 0;
        }
        
        .nav-tab {
            padding: 15px 25px;
            background: none;
            border: none;
            cursor: pointer;
            font-weight: 600;
            color: #6c757d;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
            position: relative;
        }
        
        .nav-tab.active {
            color: #3498db;
            border-bottom-color: #3498db;
            background: white;
        }
        
        .nav-tab:hover {
            color: #3498db;
        }
        
        .help-indicator {
            position: absolute;
            top: 5px;
            right: 5px;
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        
        .tab-content {
            display: none;
            padding: 30px;
            position: relative;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            border: 1px solid #e9ecef;
            position: relative;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        
        /* Help System Styles */
        .help-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
        }
        
        .help-modal {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
        }
        
        .help-header {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 20px 30px;
            border-radius: 12px 12px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .help-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 5px;
            border-radius: 4px;
            transition: background 0.3s;
        }
        
        .help-close:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .help-content {
            padding: 30px;
        }
        
        .help-nav {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .help-nav-btn {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            color: #6c757d;
            transition: all 0.3s;
        }
        
        .help-nav-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }
        
        .help-section {
            display: none;
        }
        
        .help-section.active {
            display: block;
        }
        
        .help-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        
        .help-section h4 {
            color: #3498db;
            margin: 20px 0 10px 0;
            font-size: 1.1rem;
        }
        
        .help-section p {
            color: #6c757d;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .help-section ul, .help-section ol {
            color: #6c757d;
            line-height: 1.6;
            margin-left: 20px;
            margin-bottom: 15px;
        }
        
        .help-code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            color: #2c3e50;
        }
        
        .help-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        
        .help-tip {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        
        .help-search {
            width: 100%;
            padding: 12px 20px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 1rem;
            margin-bottom: 20px;
            outline: none;
        }
        
        .help-search:focus {
            border-color: #3498db;
        }
        
        .tooltip {
            position: relative;
            display: inline-block;
            cursor: help;
        }
        
        .tooltip .tooltiptext {
            visibility: hidden;
            width: 200px;
            background-color: #2c3e50;
            color: white;
            text-align: center;
            border-radius: 6px;
            padding: 8px 12px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -100px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 0.9rem;
        }
        
        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }
        
        .quick-help {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
            transition: all 0.3s;
            z-index: 100;
        }
        
        .quick-help:hover {
            background: #2980b9;
            transform: scale(1.1);
        }
        
        .signal-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #3498db;
            position: relative;
        }
        
        .btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }
        
        @media (max-width: 768px) {
            .help-modal {
                width: 95%;
                margin: 20px;
            }
            
            .help-content {
                padding: 20px;
            }
            
            .help-nav {
                flex-direction: column;
            }
            
            .quick-help {
                bottom: 20px;
                right: 20px;
                width: 50px;
                height: 50px;
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-left">
                <h1>🚀 AI Trading Platform</h1>
                <p>Signal Provider + Data Upload + AI Assistant + Analytics</p>
            </div>
            <div class="header-right">
                <button class="help-btn" onclick="openHelp()">
                    📚 Help & Documentation
                </button>
                <button class="help-btn" onclick="openQuickGuide()">
                    ⚡ Quick Guide
                </button>
            </div>
        </div>
        
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('signals')">
                📊 Live Signals
                <div class="help-indicator tooltip" onclick="showSectionHelp('signals')">
                    ?
                    <span class="tooltiptext">Click for help with Live Signals</span>
                </div>
            </button>
            <button class="nav-tab" onclick="showTab('upload')">
                📁 Data Upload
                <div class="help-indicator tooltip" onclick="showSectionHelp('upload')">
                    ?
                    <span class="tooltiptext">Click for help with Data Upload</span>
                </div>
            </button>
            <button class="nav-tab" onclick="showTab('analytics')">
                📈 Analytics
                <div class="help-indicator tooltip" onclick="showSectionHelp('analytics')">
                    ?
                    <span class="tooltiptext">Click for help with Analytics</span>
                </div>
            </button>
            <button class="nav-tab" onclick="showTab('chat')">
                🤖 AI Assistant
                <div class="help-indicator tooltip" onclick="showSectionHelp('chat')">
                    ?
                    <span class="tooltiptext">Click for help with AI Assistant</span>
                </div>
            </button>
        </div>
        
        <!-- Signals Tab -->
        <div id="signals-tab" class="tab-content active">
            <div class="section">
                <h2>🔥 Live Trading Signals 
                    <span class="tooltip">
                        ℹ️
                        <span class="tooltiptext">AI-generated trading recommendations for educational purposes</span>
                    </span>
                </h2>
                
                <div class="signal-item">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <span style="font-size: 1.2rem; font-weight: bold;">EURUSD</span>
                        <span style="background: #d4edda; color: #155724; padding: 6px 12px; border-radius: 20px; font-weight: 600;">BUY</span>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px; margin-bottom: 15px;">
                        <div style="text-align: center; padding: 8px; background: #f8f9fa; border-radius: 4px;">
                            <div style="font-size: 0.8rem; color: #6c757d;">Entry</div>
                            <div style="font-weight: bold;">1.1234</div>
                        </div>
                        <div style="text-align: center; padding: 8px; background: #f8f9fa; border-radius: 4px;">
                            <div style="font-size: 0.8rem; color: #6c757d;">Stop Loss</div>
                            <div style="font-weight: bold;">1.1200</div>
                        </div>
                        <div style="text-align: center; padding: 8px; background: #f8f9fa; border-radius: 4px;">
                            <div style="font-size: 0.8rem; color: #6c757d;">Take Profit</div>
                            <div style="font-weight: bold;">1.1300</div>
                        </div>
                    </div>
                    <div style="color: #6c757d;">
                        <strong>Strategy:</strong> MA Crossover | <strong>Confidence:</strong> 85%
                    </div>
                </div>
                
                <button class="btn" onclick="showMT5Guide()">📋 How to Execute in MT5</button>
            </div>
        </div>
        
        <!-- Upload Tab -->
        <div id="upload-tab" class="tab-content">
            <div class="section">
                <h2>📁 Upload Trading Data
                    <span class="tooltip">
                        ℹ️
                        <span class="tooltiptext">Upload CSV, Excel, or JSON files for analysis</span>
                    </span>
                </h2>
                <div style="border: 2px dashed #3498db; border-radius: 12px; padding: 40px; text-align: center; background: #f8f9fa;">
                    <div style="font-size: 3rem; color: #3498db; margin-bottom: 15px;">📁</div>
                    <div style="font-size: 1.1rem; color: #2c3e50; margin-bottom: 10px;">Drop your files here or click to browse</div>
                    <div style="color: #6c757d; font-size: 0.9rem;">Supports CSV, Excel (.xlsx, .xls), and JSON files up to 500MB</div>
                </div>
                <button class="btn" onclick="showUploadGuide()" style="margin-top: 20px;">📖 Upload Instructions</button>
            </div>
        </div>
        
        <!-- Analytics Tab -->
        <div id="analytics-tab" class="tab-content">
            <div class="section">
                <h2>📈 Analytics & Backtesting
                    <span class="tooltip">
                        ℹ️
                        <span class="tooltiptext">Analyze your trading data and strategy performance</span>
                    </span>
                </h2>
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;">
                    <div style="background: white; padding: 20px; border-radius: 8px; text-align: center;">
                        <div style="font-size: 2rem; font-weight: bold; color: #27ae60;">+24.7%</div>
                        <div style="color: #6c757d;">Total Return</div>
                    </div>
                    <div style="background: white; padding: 20px; border-radius: 8px; text-align: center;">
                        <div style="font-size: 2rem; font-weight: bold; color: #3498db;">68.5%</div>
                        <div style="color: #6c757d;">Win Rate</div>
                    </div>
                    <div style="background: white; padding: 20px; border-radius: 8px; text-align: center;">
                        <div style="font-size: 2rem; font-weight: bold; color: #e74c3c;">-8.2%</div>
                        <div style="color: #6c757d;">Max Drawdown</div>
                    </div>
                </div>
                <button class="btn" onclick="showAnalyticsGuide()" style="margin-top: 20px;">📊 Understanding Results</button>
            </div>
        </div>
        
        <!-- Chat Tab -->
        <div id="chat-tab" class="tab-content">
            <div class="section">
                <h2>🤖 AI Trading Assistant
                    <span class="tooltip">
                        ℹ️
                        <span class="tooltiptext">Ask questions about trading, signals, and platform features</span>
                    </span>
                </h2>
                <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <div style="background: #f5f5f5; padding: 12px 16px; border-radius: 12px; margin-bottom: 15px;">
                        👋 Hello! I'm your AI Trading Assistant. I can help you with signals, strategies, and platform features. What would you like to know?
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <input type="text" placeholder="Ask me anything about trading..." style="flex: 1; padding: 10px 15px; border: 2px solid #e9ecef; border-radius: 25px; outline: none;">
                        <button class="btn">Send</button>
                    </div>
                </div>
                <button class="btn" onclick="showChatGuide()">💬 How to Use AI Assistant</button>
            </div>
        </div>
    </div>
    
    <!-- Help System Modal -->
    <div id="help-overlay" class="help-overlay">
        <div class="help-modal">
            <div class="help-header">
                <h2 id="help-title">📚 Platform Documentation</h2>
                <button class="help-close" onclick="closeHelp()">×</button>
            </div>
            <div class="help-content">
                <input type="text" class="help-search" placeholder="Search documentation..." onkeyup="searchHelp(this.value)">
                
                <div class="help-nav">
                    <button class="help-nav-btn active" onclick="showHelpSection('overview')">Overview</button>
                    <button class="help-nav-btn" onclick="showHelpSection('signals')">Live Signals</button>
                    <button class="help-nav-btn" onclick="showHelpSection('upload')">Data Upload</button>
                    <button class="help-nav-btn" onclick="showHelpSection('analytics')">Analytics</button>
                    <button class="help-nav-btn" onclick="showHelpSection('chat')">AI Assistant</button>
                    <button class="help-nav-btn" onclick="showHelpSection('troubleshooting')">Troubleshooting</button>
                </div>
                
                <div id="help-content-area">
                    <!-- Help content will be populated here -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Help Button -->
    <button class="quick-help" onclick="openQuickHelp()" title="Quick Help">?</button>

    <script>
        // Tab switching functionality
        function showTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.getElementById(tabName + '-tab').classList.add('active');
            event.target.classList.add('active');
        }
        
        // Help system functionality
        function openHelp() {
            document.getElementById('help-overlay').style.display = 'flex';
            showHelpSection('overview');
        }
        
        function closeHelp() {
            document.getElementById('help-overlay').style.display = 'none';
        }
        
        function openQuickGuide() {
            document.getElementById('help-overlay').style.display = 'flex';
            document.getElementById('help-title').textContent = '⚡ Quick Reference Guide';
            showHelpSection('quick-guide');
        }
        
        function openQuickHelp() {
            document.getElementById('help-overlay').style.display = 'flex';
            showHelpSection('quick-help');
        }
        
        function showSectionHelp(section) {
            event.stopPropagation();
            document.getElementById('help-overlay').style.display = 'flex';
            showHelpSection(section);
        }
        
        function showHelpSection(section) {
            // Update nav buttons
            document.querySelectorAll('.help-nav-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event?.target?.classList.add('active');
            
            // Show content
            const contentArea = document.getElementById('help-content-area');
            contentArea.innerHTML = getHelpContent(section);
        }
        
        function getHelpContent(section) {
            const content = {
                'overview': `
                    <div class="help-section active">
                        <h3>🎯 Platform Overview</h3>
                        <p>Welcome to the AI Trading Platform - your comprehensive solution for trading signals, data analysis, and AI-powered assistance.</p>
                        
                        <h4>🔥 Key Features</h4>
                        <ul>
                            <li><strong>Live Trading Signals:</strong> AI-generated trading recommendations</li>
                            <li><strong>Data Upload:</strong> Upload and analyze your trading data</li>
                            <li><strong>Analytics:</strong> Comprehensive backtesting and performance analysis</li>
                            <li><strong>AI Assistant:</strong> 24/7 trading support and guidance</li>
                        </ul>
                        
                        <div class="help-warning">
                            <strong>⚠️ Important:</strong> This platform provides educational signals only. You execute trades on your own MT5 account.
                        </div>
                        
                        <h4>🚀 Getting Started</h4>
                        <ol>
                            <li>Explore the Live Signals tab for current opportunities</li>
                            <li>Upload your trading data for analysis</li>
                            <li>Review analytics and performance metrics</li>
                            <li>Ask the AI Assistant any questions</li>
                        </ol>
                    </div>
                `,
                'signals': `
                    <div class="help-section active">
                        <h3>📊 Live Trading Signals</h3>
                        <p>Get AI-generated trading recommendations with detailed analysis and execution instructions.</p>
                        
                        <h4>📋 Reading a Signal</h4>
                        <div class="help-code">
🔥 BUY EURUSD @ 1.1234
   Stop Loss: 1.1200 | Take Profit: 1.1300
   Strategy: MA Crossover | Confidence: 85%
   Analysis: Fast MA crossed above slow MA with momentum
                        </div>
                        
                        <h4>🎯 Signal Components</h4>
                        <ul>
                            <li><strong>Symbol:</strong> Currency pair (EURUSD, GBPUSD, etc.)</li>
                            <li><strong>Type:</strong> BUY or SELL recommendation</li>
                            <li><strong>Entry Price:</strong> Suggested entry point</li>
                            <li><strong>Stop Loss:</strong> Risk management exit</li>
                            <li><strong>Take Profit:</strong> Profit target level</li>
                            <li><strong>Confidence:</strong> AI confidence (0-100%)</li>
                            <li><strong>Strategy:</strong> Algorithm that generated signal</li>
                        </ul>
                        
                        <h4>📱 MT5 Execution Steps</h4>
                        <ol>
                            <li>Open MetaTrader 5 platform</li>
                            <li>Navigate to the currency pair</li>
                            <li>Right-click → Trading → New Order</li>
                            <li>Select Buy Market or Sell Market</li>
                            <li>Set Stop Loss and Take Profit levels</li>
                            <li>Enter lot size (start small!)</li>
                            <li>Click Buy or Sell to execute</li>
                        </ol>
                        
                        <div class="help-tip">
                            <strong>💡 Pro Tip:</strong> Always use stop losses and never risk more than 2% of your account per trade.
                        </div>
                    </div>
                `,
                'upload': `
                    <div class="help-section active">
                        <h3>📁 Data Upload & Processing</h3>
                        <p>Upload your trading data for backtesting, analysis, and custom strategy development.</p>
                        
                        <h4>📄 Supported Formats</h4>
                        <ul>
                            <li><strong>CSV:</strong> Comma-separated values (.csv)</li>
                            <li><strong>Excel:</strong> Microsoft Excel files (.xlsx, .xls)</li>
                            <li><strong>JSON:</strong> JavaScript Object Notation (.json)</li>
                            <li><strong>Size Limit:</strong> Up to 500MB per file</li>
                        </ul>
                        
                        <h4>📊 Required Data Columns</h4>
                        <div class="help-code">
Date/Time, Symbol, Open, High, Low, Close, Volume
2024-01-01 09:00, EURUSD, 1.1234, 1.1245, 1.1230, 1.1240, 1000
                        </div>
                        
                        <h4>🔄 Upload Process</h4>
                        <ol>
                            <li><strong>Select File:</strong> Drag & drop or click to browse</li>
                            <li><strong>File Analysis:</strong> System analyzes rows, columns, size</li>
                            <li><strong>Column Mapping:</strong> Map your columns to standard format</li>
                            <li><strong>Processing:</strong> Data validation and preparation</li>
                            <li><strong>Completion:</strong> Data ready for analysis</li>
                        </ol>
                        
                        <h4>🔧 Troubleshooting</h4>
                        <ul>
                            <li><strong>File too large:</strong> Split into smaller files</li>
                            <li><strong>Wrong format:</strong> Convert to CSV or Excel</li>
                            <li><strong>Missing columns:</strong> Ensure all required data is present</li>
                            <li><strong>Date format:</strong> Use YYYY-MM-DD HH:MM format</li>
                        </ul>
                        
                        <div class="help-warning">
                            <strong>🔒 Security:</strong> Your data is processed securely and never shared with third parties.
                        </div>
                    </div>
                `,
                'analytics': `
                    <div class="help-section active">
                        <h3>📈 Analytics & Backtesting</h3>
                        <p>Analyze your trading performance and backtest strategies against historical data.</p>
                        
                        <h4>📊 Key Metrics</h4>
                        <ul>
                            <li><strong>Total Return:</strong> Overall profit/loss percentage</li>
                            <li><strong>Win Rate:</strong> Percentage of profitable trades</li>
                            <li><strong>Profit Factor:</strong> Gross profit ÷ gross loss</li>
                            <li><strong>Max Drawdown:</strong> Largest peak-to-trough decline</li>
                            <li><strong>Sharpe Ratio:</strong> Risk-adjusted return measure</li>
                        </ul>
                        
                        <h4>✅ Good Strategy Indicators</h4>
                        <div class="help-tip">
                            <ul>
                                <li><strong>Win Rate:</strong> >60%</li>
                                <li><strong>Profit Factor:</strong> >1.5</li>
                                <li><strong>Sharpe Ratio:</strong> >1.0</li>
                                <li><strong>Max Drawdown:</strong> <20%</li>
                            </ul>
                        </div>
                        
                        <h4>⚠️ Warning Signs</h4>
                        <div class="help-warning">
                            <ul>
                                <li><strong>Win Rate:</strong> <50%</li>
                                <li><strong>Profit Factor:</strong> <1.0</li>
                                <li><strong>Max Drawdown:</strong> >30%</li>
                                <li><strong>Few Trades:</strong> <100 trades</li>
                            </ul>
                        </div>
                        
                        <h4>📈 Chart Types</h4>
                        <ul>
                            <li><strong>Equity Curve:</strong> Account balance over time</li>
                            <li><strong>Drawdown Chart:</strong> Risk visualization</li>
                            <li><strong>Monthly Returns:</strong> Performance by month</li>
                            <li><strong>Trade Distribution:</strong> Profit/loss histogram</li>
                        </ul>
                    </div>
                `,
                'chat': `
                    <div class="help-section active">
                        <h3>🤖 AI Trading Assistant</h3>
                        <p>Get instant help with trading questions, strategy explanations, and platform guidance.</p>
                        
                        <h4>💬 How to Use</h4>
                        <ol>
                            <li>Type your question in the chat input</li>
                            <li>Use quick action buttons for common queries</li>
                            <li>Ask follow-up questions for deeper understanding</li>
                            <li>Request specific analysis or explanations</li>
                        </ol>
                        
                        <h4>❓ Example Questions</h4>
                        <div class="help-code">
"Analyze EURUSD current conditions"
"What's the best trading opportunity today?"
"How does the RSI strategy work?"
"Explain this signal's confidence level"
"What's a good stop loss for GBPUSD?"
"How do I upload trading data?"
                        </div>
                        
                        <h4>⚡ Quick Actions</h4>
                        <ul>
                            <li><strong>📁 Upload Help:</strong> Data upload guidance</li>
                            <li><strong>📈 Backtest Help:</strong> Results interpretation</li>
                            <li><strong>⚠️ Risk Tips:</strong> Risk management advice</li>
                            <li><strong>🔧 Platform Help:</strong> Feature explanations</li>
                        </ul>
                        
                        <h4>🎯 Best Practices</h4>
                        <ul>
                            <li>Be specific in your questions</li>
                            <li>Ask follow-up questions if needed</li>
                            <li>Use examples when possible</li>
                            <li>Remember responses are educational</li>
                        </ul>
                        
                        <div class="help-tip">
                            <strong>💡 Pro Tip:</strong> The AI learns from your questions and provides increasingly personalized responses.
                        </div>
                    </div>
                `,
                'troubleshooting': `
                    <div class="help-section active">
                        <h3>🔧 Troubleshooting</h3>
                        <p>Common issues and solutions to keep your platform running smoothly.</p>
                        
                        <h4>📊 Signal Issues</h4>
                        <ul>
                            <li><strong>No signals showing:</strong> Check internet connection, refresh page</li>
                            <li><strong>Old signals:</strong> Click refresh button to generate new signals</li>
                            <li><strong>Missing information:</strong> Ensure all required data is loaded</li>
                        </ul>
                        
                        <h4>📁 Upload Problems</h4>
                        <ul>
                            <li><strong>File won't upload:</strong> Check file size (<500MB) and format</li>
                            <li><strong>Processing stuck:</strong> Refresh page and try again</li>
                            <li><strong>Column mapping issues:</strong> Ensure all required columns are mapped</li>
                        </ul>
                        
                        <h4>🤖 Chat Not Working</h4>
                        <ul>
                            <li><strong>No response:</strong> Check internet connection</li>
                            <li><strong>Slow responses:</strong> High server load, please wait</li>
                            <li><strong>Unclear answers:</strong> Try rephrasing your question</li>
                        </ul>
                        
                        <h4>🌐 Browser Requirements</h4>
                        <ul>
                            <li><strong>Chrome:</strong> Version 90+ (recommended)</li>
                            <li><strong>Firefox:</strong> Version 88+</li>
                            <li><strong>Safari:</strong> Version 14+</li>
                            <li><strong>Edge:</strong> Version 90+</li>
                        </ul>
                        
                        <h4>⚡ Performance Tips</h4>
                        <ul>
                            <li>Close unused browser tabs</li>
                            <li>Clear browser cache if experiencing issues</li>
                            <li>Use latest browser version</li>
                            <li>Ensure stable internet connection</li>
                        </ul>
                        
                        <div class="help-warning">
                            <strong>🆘 Still Need Help?</strong> Contact our support team or ask the AI Assistant for immediate assistance.
                        </div>
                    </div>
                `,
                'quick-guide': `
                    <div class="help-section active">
                        <h3>⚡ Quick Reference Guide</h3>
                        
                        <h4>📊 Get a Signal (10 seconds)</h4>
                        <div class="help-code">
1. Check Live Signals tab
2. Read signal details
3. Note confidence level
4. Copy to MT5
                        </div>
                        
                        <h4>📱 Execute in MT5 (30 seconds)</h4>
                        <div class="help-code">
1. Open MT5 → Find pair
2. Right-click → New Order
3. Set SL/TP levels
4. Click Buy/Sell
                        </div>
                        
                        <h4>📁 Upload Data (2 minutes)</h4>
                        <div class="help-code">
1. Drag & drop file
2. Review analysis
3. Map columns
4. Confirm processing
                        </div>
                        
                        <h4>🤖 Ask AI (30 seconds)</h4>
                        <div class="help-code">
1. Type question
2. Get instant answer
3. Ask follow-ups
4. Use quick actions
                        </div>
                        
                        <h4>⚠️ Safety Checklist</h4>
                        <ul>
                            <li>✅ Never risk more than 2% per trade</li>
                            <li>✅ Always use stop losses</li>
                            <li>✅ Start with demo accounts</li>
                            <li>✅ Check signal confidence</li>
                            <li>✅ Understand the strategy</li>
                        </ul>
                        
                        <h4>📈 Good Strategy Metrics</h4>
                        <ul>
                            <li><strong>Win Rate:</strong> >60%</li>
                            <li><strong>Profit Factor:</strong> >1.5</li>
                            <li><strong>Max Drawdown:</strong> <20%</li>
                            <li><strong>Sharpe Ratio:</strong> >1.0</li>
                        </ul>
                    </div>
                `,
                'quick-help': `
                    <div class="help-section active">
                        <h3>🆘 Quick Help</h3>
                        
                        <h4>🔥 Most Common Questions</h4>
                        
                        <h4>❓ "How do I execute a signal?"</h4>
                        <p>Copy the signal details → Open MT5 → New Order → Set levels → Execute</p>
                        
                        <h4>❓ "Why isn't my file uploading?"</h4>
                        <p>Check: File size (<500MB), Format (CSV/Excel), Internet connection</p>
                        
                        <h4>❓ "What does confidence mean?"</h4>
                        <p>AI confidence in the signal (0-100%). Higher = more reliable.</p>
                        
                        <h4>❓ "How much should I risk?"</h4>
                        <p>Never more than 2% of your account per trade. Start smaller!</p>
                        
                        <h4>❓ "Are signals guaranteed?"</h4>
                        <p>No! All signals are educational. Trading involves risk of loss.</p>
                        
                        <div class="help-tip">
                            <strong>💡 Still stuck?</strong> Ask the AI Assistant - it's available 24/7 and can help with any question!
                        </div>
                        
                        <h4>🚨 Emergency Contacts</h4>
                        <ul>
                            <li><strong>Technical Issues:</strong> Use AI Assistant</li>
                            <li><strong>Account Problems:</strong> Check browser/connection</li>
                            <li><strong>Trading Questions:</strong> Ask AI for guidance</li>
                        </ul>
                    </div>
                `
            };
            
            return content[section] || content['overview'];
        }
        
        function searchHelp(query) {
            // Simple search functionality
            const contentArea = document.getElementById('help-content-area');
            if (query.length < 3) return;
            
            // This would implement actual search in a real application
            console.log('Searching for:', query);
        }
        
        // Specific guide functions
        function showMT5Guide() {
            openHelp();
            showHelpSection('signals');
        }
        
        function showUploadGuide() {
            openHelp();
            showHelpSection('upload');
        }
        
        function showAnalyticsGuide() {
            openHelp();
            showHelpSection('analytics');
        }
        
        function showChatGuide() {
            openHelp();
            showHelpSection('chat');
        }
        
        // Close help with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeHelp();
            }
        });
        
        // Close help when clicking outside modal
        document.getElementById('help-overlay').addEventListener('click', function(e) {
            if (e.target === this) {
                closeHelp();
            }
        });
    </script>
</body>
</html>