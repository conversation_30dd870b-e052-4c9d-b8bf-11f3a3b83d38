import { Knex, knex } from 'knex';
import { <PERSON>gger, DatabaseConfig } from '../types';

class DatabaseConnection {
  private static instance: DatabaseConnection;
  private _connection: Knex;

  private constructor(
    private config: DatabaseConfig,
    private logger?: Logger
  ) {
    this._connection = knex(this.config);
    this.setupEventHandlers();
  }

  public static getInstance(logger?: Logger, config?: DatabaseConfig): DatabaseConnection {
    if (!DatabaseConnection.instance) {
      if (!config) {
        // In a real app, this would come from environment config
        throw new Error('Database configuration required for first initialization');
      }
      DatabaseConnection.instance = new DatabaseConnection(config, logger);
    }
    return DatabaseConnection.instance;
  }

  public get connection(): Knex {
    return this._connection;
  }

  private setupEventHandlers(): void {
    this._connection.on('query', (query) => {
      if (this.logger && process.env.NODE_ENV === 'development') {
        this.logger.debug('Database Query', {
          sql: query.sql,
          bindings: query.bindings,
        });
      }
    });

    this._connection.on('query-error', (error, query) => {
      if (this.logger) {
        this.logger.error('Database Query Error', {
          error: error.message,
          sql: query.sql,
          bindings: query.bindings,
        });
      }
    });
  }

  public async testConnection(): Promise<boolean> {
    try {
      await this._connection.raw('SELECT 1');
      return true;
    } catch (error) {
      if (this.logger) {
        this.logger.error('Database connection test failed', { error });
      }
      return false;
    }
  }

  public async disconnect(): Promise<void> {
    if (this._connection) {
      await this._connection.destroy();
    }
  }

  public async runMigrations(): Promise<void> {
    try {
      await this._connection.migrate.latest();
      if (this.logger) {
        this.logger.info('Database migrations completed successfully');
      }
    } catch (error) {
      if (this.logger) {
        this.logger.error('Database migration failed', { error });
      }
      throw error;
    }
  }

  public async rollbackMigrations(steps?: number): Promise<void> {
    try {
      if (steps) {
        for (let i = 0; i < steps; i++) {
          await this._connection.migrate.rollback();
        }
      } else {
        await this._connection.migrate.rollback();
      }
      if (this.logger) {
        this.logger.info('Database rollback completed successfully');
      }
    } catch (error) {
      if (this.logger) {
        this.logger.error('Database rollback failed', { error });
      }
      throw error;
    }
  }

  public async runSeeds(): Promise<void> {
    try {
      await this._connection.seed.run();
      if (this.logger) {
        this.logger.info('Database seeds completed successfully');
      }
    } catch (error) {
      if (this.logger) {
        this.logger.error('Database seed failed', { error });
      }
      throw error;
    }
  }

  public transaction<T>(
    callback: (trx: Knex.Transaction) => Promise<T>
  ): Promise<T> {
    return this._connection.transaction(callback);
  }
}

// Export singleton instance getter
export { DatabaseConnection };