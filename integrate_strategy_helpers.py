#!/usr/bin/env python3
"""
Integration Script for Strategy Helpers
Adds Strategy Helper functionality to your existing MVP
"""

import os
import sys
from pathlib import Path

def update_frontend_routing():
    """Add Strategy Helper route to frontend"""
    app_tsx_path = Path("frontend/src/App.tsx")
    
    if not app_tsx_path.exists():
        print("⚠️  App.tsx not found. Please add the route manually:")
        print("   import StrategyHelper from './components/strategy/StrategyHelper';")
        print("   <Route path='/strategy-helper' element={<StrategyHelper />} />")
        return False
    
    try:
        with open(app_tsx_path, 'r') as f:
            content = f.read()
        
        # Check if already integrated
        if 'StrategyHelper' in content:
            print("✅ Strategy Helper already integrated in App.tsx")
            return True
        
        # Add import
        if "import React" in content:
            import_line = "import StrategyHelper from './components/strategy/StrategyHelper';"
            content = content.replace(
                "import React",
                f"{import_line}\nimport React"
            )
        
        # Add route (this is a simple approach - might need manual adjustment)
        if "<Routes>" in content and "</Routes>" in content:
            route_line = "        <Route path='/strategy-helper' element={<StrategyHelper />} />"
            content = content.replace(
                "</Routes>",
                f"        {route_line}\n      </Routes>"
            )
        
        with open(app_tsx_path, 'w') as f:
            f.write(content)
        
        print("✅ Updated App.tsx with Strategy Helper route")
        return True
        
    except Exception as e:
        print(f"❌ Error updating App.tsx: {e}")
        return False

def update_backend_server():
    """Add Strategy Helper API to backend"""
    minimal_server_path = Path("backend/minimal_server.py")
    
    if not minimal_server_path.exists():
        print("⚠️  minimal_server.py not found. Please add the API mount manually:")
        print("   from strategy_helper_api import app as strategy_app")
        print("   app.mount('/api/strategy', strategy_app)")
        return False
    
    try:
        with open(minimal_server_path, 'r') as f:
            content = f.read()
        
        # Check if already integrated
        if 'strategy_helper_api' in content:
            print("✅ Strategy Helper API already integrated in minimal_server.py")
            return True
        
        # Add import
        if "from fastapi" in content:
            import_line = "from strategy_helper_api import app as strategy_app"
            content = content.replace(
                "from fastapi",
                f"{import_line}\nfrom fastapi"
            )
        
        # Add mount (before if __name__ == "__main__")
        if 'if __name__ == "__main__"' in content:
            mount_line = "\n# Mount Strategy Helper API\napp.mount('/api/strategy', strategy_app)\n"
            content = content.replace(
                'if __name__ == "__main__"',
                f"{mount_line}\nif __name__ == '__main__'"
            )
        
        with open(minimal_server_path, 'w') as f:
            f.write(content)
        
        print("✅ Updated minimal_server.py with Strategy Helper API")
        return True
        
    except Exception as e:
        print(f"❌ Error updating minimal_server.py: {e}")
        return False

def create_navigation_component():
    """Create or update navigation to include Strategy Helper"""
    nav_path = Path("frontend/src/components/Navigation.tsx")
    
    nav_content = '''import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Brain, TrendingUp, BarChart3, Settings } from 'lucide-react';

const Navigation: React.FC = () => {
  const location = useLocation();
  
  const navItems = [
    { path: '/', label: 'Dashboard', icon: BarChart3 },
    { path: '/strategies', label: 'Strategies', icon: TrendingUp },
    { path: '/strategy-helper', label: 'Strategy Helper', icon: Brain },
    { path: '/settings', label: 'Settings', icon: Settings },
  ];

  return (
    <nav className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex">
            <div className="flex-shrink-0 flex items-center">
              <h1 className="text-xl font-bold text-gray-900">AI Trading Platform</h1>
            </div>
            <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
              {navItems.map((item) => {
                const Icon = item.icon;
                const isActive = location.pathname === item.path;
                return (
                  <Link
                    key={item.path}
                    to={item.path}
                    className={`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium ${
                      isActive
                        ? 'border-blue-500 text-gray-900'
                        : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                    }`}
                  >
                    <Icon className="h-4 w-4 mr-2" />
                    {item.label}
                  </Link>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;'''
    
    try:
        os.makedirs(nav_path.parent, exist_ok=True)
        with open(nav_path, 'w') as f:
            f.write(nav_content)
        print("✅ Created Navigation component with Strategy Helper link")
        return True
    except Exception as e:
        print(f"❌ Error creating Navigation component: {e}")
        return False

def create_package_json_updates():
    """Show required package.json updates"""
    print("\n📦 Required Frontend Dependencies:")
    print("Add these to your package.json if not already present:")
    print("""
{
  "dependencies": {
    "@radix-ui/react-tabs": "^1.0.4",
    "@radix-ui/react-select": "^2.0.0",
    "class-variance-authority": "^0.7.0",
    "lucide-react": "^0.263.1"
  }
}
""")
    print("Run: npm install @radix-ui/react-tabs @radix-ui/react-select class-variance-authority lucide-react")

def create_startup_script():
    """Create a startup script for the complete system"""
    startup_script = '''#!/bin/bash
# AI Enhanced Trading Platform - Complete Startup Script

echo "🚀 Starting AI Enhanced Trading Platform with Strategy Helpers..."

# Start backend servers
echo "📡 Starting main backend server..."
cd backend
python minimal_server.py &
MAIN_PID=$!

echo "🎯 Starting Strategy Helper API..."
python strategy_helper_api.py &
STRATEGY_PID=$!

# Start frontend
echo "🌐 Starting frontend..."
cd ../frontend
npm run dev &
FRONTEND_PID=$!

echo "✅ All services started!"
echo "📊 Main API: http://localhost:8000"
echo "🎯 Strategy Helper API: http://localhost:8001"
echo "🌐 Frontend: http://localhost:5173"
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for interrupt
trap "echo 'Stopping services...'; kill $MAIN_PID $STRATEGY_PID $FRONTEND_PID; exit" INT
wait
'''
    
    try:
        with open("start_platform.sh", 'w') as f:
            f.write(startup_script)
        os.chmod("start_platform.sh", 0o755)
        print("✅ Created startup script: start_platform.sh")
        
        # Also create Windows batch file
        windows_script = '''@echo off
echo 🚀 Starting AI Enhanced Trading Platform with Strategy Helpers...

echo 📡 Starting main backend server...
cd backend
start "Main API" python minimal_server.py

echo 🎯 Starting Strategy Helper API...
start "Strategy API" python strategy_helper_api.py

echo 🌐 Starting frontend...
cd ../frontend
start "Frontend" npm run dev

echo ✅ All services started!
echo 📊 Main API: http://localhost:8000
echo 🎯 Strategy Helper API: http://localhost:8001
echo 🌐 Frontend: http://localhost:5173
pause
'''
        
        with open("start_platform.bat", 'w') as f:
            f.write(windows_script)
        print("✅ Created Windows startup script: start_platform.bat")
        
        return True
    except Exception as e:
        print(f"❌ Error creating startup scripts: {e}")
        return False

def main():
    """Main integration function"""
    print("🎯 AI Enhanced Trading Platform - Strategy Helpers Integration")
    print("=" * 70)
    
    # Change to project directory
    project_root = Path(__file__).parent
    os.chdir(project_root)
    
    print(f"📁 Working directory: {os.getcwd()}")
    print()
    
    # Run integration steps
    steps = [
        ("Frontend Routing", update_frontend_routing),
        ("Backend API", update_backend_server),
        ("Navigation Component", create_navigation_component),
        ("Startup Scripts", create_startup_script),
    ]
    
    results = {}
    for step_name, step_func in steps:
        print(f"🔧 {step_name}...")
        results[step_name] = step_func()
        print()
    
    # Show package dependencies
    create_package_json_updates()
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 INTEGRATION SUMMARY")
    print("=" * 70)
    
    passed = sum(results.values())
    total = len(results)
    
    for step_name, result in results.items():
        status = "✅ SUCCESS" if result else "⚠️  MANUAL"
        print(f"{step_name:<20} {status}")
    
    print(f"\nIntegration Result: {passed}/{total} steps completed automatically")
    
    if passed == total:
        print("\n🎉 INTEGRATION COMPLETE!")
        print("\n🚀 Next Steps:")
        print("1. Install frontend dependencies: npm install")
        print("2. Start the platform: ./start_platform.sh (Linux/Mac) or start_platform.bat (Windows)")
        print("3. Visit http://localhost:5173 to see your enhanced platform")
        print("4. Navigate to Strategy Helper to explore the new features")
    else:
        print(f"\n⚠️  {total - passed} step(s) require manual integration.")
        print("Please check the messages above and complete the manual steps.")
    
    print("\n📚 Documentation:")
    print("- Full integration guide: STRATEGY_HELPERS_INTEGRATION_GUIDE.md")
    print("- Test the system: python test_strategy_helpers.py")
    
    print("\n" + "=" * 70)

if __name__ == "__main__":
    main()