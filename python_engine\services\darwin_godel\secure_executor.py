"""
Secure Strategy Executor using RestrictedPython
Provides sandboxed execution environment for trading strategies
"""

import ast
import sys
import time
import threading
import platform
from typing import Dict, Any, Optional
from RestrictedPython import compile_restricted, safe_globals, limited_builtins
from RestrictedPython.Guards import safe_builtins, guarded_iter_unpack_sequence
from RestrictedPython.transformer import RestrictingNodeTransformer

# Platform-specific timeout handling
IS_WINDOWS = platform.system() == 'Windows'
if not IS_WINDOWS:
    import signal

class SecurityError(Exception):
    """Raised when a security violation is detected"""
    pass

class TimeoutError(SecurityError):
    """Raised when strategy execution times out"""
    pass

class SecureStrategyExecutor:
    """
    Secure executor for trading strategies using RestrictedPython
    Prevents malicious code execution while allowing safe trading logic
    """
    
    def __init__(self, max_execution_time: int = 5):
        """
        Initialize secure executor
        
        Args:
            max_execution_time: Maximum execution time in seconds
        """
        self.max_execution_time = max_execution_time
        self.allowed_imports = {
            'math': ['sqrt', 'log', 'exp', 'sin', 'cos', 'tan', 'pi', 'e'],
            'statistics': ['mean', 'median', 'stdev', 'variance'],
        }
        
        # Dangerous modules/functions to block
        self.blocked_imports = {
            'os', 'sys', 'subprocess', 'socket', 'urllib', 'requests',
            'pickle', 'marshal', 'shelve', 'dbm', 'sqlite3',
            'threading', 'multiprocessing', 'asyncio',
            'importlib', '__import__', 'eval', 'exec', 'compile',
            'open', 'file', 'input', 'raw_input'
        }
        
        # Dangerous built-ins to block
        self.blocked_builtins = {
            'eval', 'exec', 'compile', '__import__', 'open', 'file',
            'input', 'raw_input', 'reload', 'vars', 'locals', 'globals',
            'dir', 'getattr', 'setattr', 'delattr', 'hasattr'
        }
    
    def _create_safe_globals(self) -> Dict[str, Any]:
        """Create safe global namespace for strategy execution"""
        # Start with RestrictedPython safe globals
        safe_ns = safe_globals.copy()
        
        # Add safe built-ins
        safe_ns['__builtins__'] = {
            # Safe built-in functions
            'len': len,
            'max': max,
            'min': min,
            'sum': sum,
            'abs': abs,
            'round': round,
            'int': int,
            'float': float,
            'str': str,
            'bool': bool,
            'list': list,
            'dict': dict,
            'tuple': tuple,
            'set': set,
            'range': range,
            'enumerate': enumerate,
            'zip': zip,
            'sorted': sorted,
            'reversed': reversed,
            'any': any,
            'all': all,
            
            # Safe iteration and attribute access
            '_iter_unpack_sequence_': guarded_iter_unpack_sequence,
            '_getiter_': iter,
            '_getitem_': lambda obj, key: obj[key],
            '_getattr_': getattr,
            
            # Block dangerous functions
            '__import__': self._blocked_import,
            'eval': self._blocked_function,
            'exec': self._blocked_function,
            'compile': self._blocked_function,
            'open': self._blocked_function,
        }
        
        # Add safe math module
        import math
        safe_math = {}
        for func_name in self.allowed_imports['math']:
            if hasattr(math, func_name):
                safe_math[func_name] = getattr(math, func_name)
        safe_ns['math'] = type('math', (), safe_math)()
        
        return safe_ns
    
    def _blocked_import(self, name, *args, **kwargs):
        """Block dangerous imports"""
        if name in self.blocked_imports:
            raise SecurityError(f"Import of module '{name}' is not allowed")
        
        # Only allow specific safe imports
        if name == 'math':
            import math
            safe_math = {}
            for func_name in self.allowed_imports['math']:
                if hasattr(math, func_name):
                    safe_math[func_name] = getattr(math, func_name)
            return type('math', (), safe_math)()
        
        raise SecurityError(f"Import of module '{name}' is not allowed")
    
    def _blocked_function(self, *args, **kwargs):
        """Block dangerous functions"""
        raise SecurityError("This function is restricted for security reasons")
    
    def _validate_code_ast(self, code: str) -> None:
        """Validate code AST for dangerous patterns"""
        try:
            tree = ast.parse(code)
        except SyntaxError as e:
            raise SecurityError(f"Syntax error in strategy code: {e}")
        
        # Check for dangerous patterns
        for node in ast.walk(tree):
            # Block dangerous function calls
            if isinstance(node, ast.Call):
                if isinstance(node.func, ast.Name):
                    if node.func.id in self.blocked_builtins:
                        raise SecurityError(f"Function '{node.func.id}' is not allowed")
            
            # Block dangerous imports
            if isinstance(node, ast.Import):
                for alias in node.names:
                    if alias.name in self.blocked_imports:
                        raise SecurityError(f"Import of module '{alias.name}' is not allowed")
            
            if isinstance(node, ast.ImportFrom):
                if node.module in self.blocked_imports:
                    raise SecurityError(f"Import from module '{node.module}' is not allowed")
    
    def _timeout_handler(self, signum=None, frame=None):
        """Handle execution timeout"""
        raise TimeoutError(f"Strategy execution timed out after {self.max_execution_time} seconds")
    
    def _validate_result(self, result: Any) -> Dict[str, Any]:
        """Validate strategy execution result"""
        # Must return a dictionary
        if not isinstance(result, dict):
            raise SecurityError("Strategy must return a dictionary")
        
        # Must have 'signal' field
        if 'signal' not in result:
            raise SecurityError("Strategy result must contain 'signal' field")
        
        # Signal must be valid
        valid_signals = ['buy', 'sell', 'hold']
        if result['signal'] not in valid_signals:
            raise SecurityError(f"Signal must be one of {valid_signals}, got '{result['signal']}'")
        
        # If confidence is provided, it must be in valid range
        if 'confidence' in result:
            confidence = result['confidence']
            if not isinstance(confidence, (int, float)):
                raise SecurityError("Confidence must be a number")
            if not (0 <= confidence <= 1):
                raise SecurityError("Confidence must be between 0 and 1")
        
        return result
    
    def execute_strategy(self, 
                        strategy_code: str, 
                        market_data: Dict[str, Any], 
                        parameters: Dict[str, Any],
                        timeout: Optional[int] = None) -> Dict[str, Any]:
        """
        Execute trading strategy in secure environment
        
        Args:
            strategy_code: Python code defining the trading strategy
            market_data: Market data to pass to strategy
            parameters: Strategy parameters
            timeout: Execution timeout (uses default if None)
            
        Returns:
            Strategy execution result
            
        Raises:
            SecurityError: If security violation detected
            TimeoutError: If execution times out
        """
        # Use provided timeout or default
        exec_timeout = timeout if timeout is not None else self.max_execution_time
        
        # Validate code AST first
        self._validate_code_ast(strategy_code)
        
        # Compile with RestrictedPython
        try:
            compiled_code = compile_restricted(strategy_code, '<strategy>', 'exec')
            if compiled_code is None:
                raise SecurityError("Failed to compile strategy code - security violation detected")
        except Exception as e:
            raise SecurityError(f"Failed to compile strategy code: {e}")
        
        # Create safe execution environment
        safe_globals = self._create_safe_globals()
        safe_locals = {}
        
        # Cross-platform timeout handling
        result_container = {'result': None, 'error': None, 'completed': False}
        
        def execute_with_timeout():
            try:
                # Execute the compiled code
                exec(compiled_code, safe_globals, safe_locals)
                
                # Get the trading_strategy function
                if 'trading_strategy' not in safe_locals:
                    result_container['error'] = SecurityError("Strategy code must define a 'trading_strategy' function")
                    return
                
                strategy_func = safe_locals['trading_strategy']
                
                # Execute the strategy function
                start_time = time.time()
                result = strategy_func(market_data, parameters)
                execution_time = time.time() - start_time
                
                # Validate result
                validated_result = self._validate_result(result)
                
                # Add execution metadata
                validated_result['_execution_time'] = execution_time
                validated_result['_security_passed'] = True
                
                result_container['result'] = validated_result
                result_container['completed'] = True
                
            except Exception as e:
                if isinstance(e, (SecurityError, TimeoutError)):
                    result_container['error'] = e
                else:
                    result_container['error'] = SecurityError(f"Strategy execution error: {e}")
        
        # Execute with timeout
        if exec_timeout > 0:
            execution_thread = threading.Thread(target=execute_with_timeout)
            execution_thread.daemon = True
            execution_thread.start()
            execution_thread.join(timeout=exec_timeout)
            
            if execution_thread.is_alive():
                # Thread is still running, timeout occurred
                raise TimeoutError(f"Strategy execution timed out after {exec_timeout} seconds")
        else:
            execute_with_timeout()
        
        # Check results
        if result_container['error']:
            raise result_container['error']
        
        if not result_container['completed']:
            raise SecurityError("Strategy execution did not complete properly")
        
        return result_container['result']
    
    def validate_strategy_code(self, strategy_code: str) -> Dict[str, Any]:
        """
        Validate strategy code without executing it
        
        Args:
            strategy_code: Python code to validate
            
        Returns:
            Validation result with security analysis
        """
        validation_result = {
            'is_secure': False,
            'security_issues': [],
            'warnings': [],
            'analysis': {}
        }
        
        try:
            # AST validation
            self._validate_code_ast(strategy_code)
            
            # Compilation test
            compiled_code = compile_restricted(strategy_code, '<strategy>', 'exec')
            if compiled_code is None:
                validation_result['security_issues'].append("Code compilation failed - security violation")
                return validation_result
            
            # Check for trading_strategy function
            tree = ast.parse(strategy_code)
            has_trading_function = False
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef) and node.name == 'trading_strategy':
                    has_trading_function = True
                    break
            
            if not has_trading_function:
                validation_result['security_issues'].append("Missing required 'trading_strategy' function")
                return validation_result
            
            # If we get here, code is secure
            validation_result['is_secure'] = True
            validation_result['analysis'] = {
                'compilation_successful': True,
                'has_trading_function': True,
                'ast_validation_passed': True
            }
            
        except SecurityError as e:
            validation_result['security_issues'].append(str(e))
        except Exception as e:
            validation_result['security_issues'].append(f"Validation error: {e}")
        
        return validation_result