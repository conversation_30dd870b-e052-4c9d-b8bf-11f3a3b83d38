"""
Code Validator for Generated Trading Strategies
Validates syntax, security, and interface compliance of generated Python code
"""

import ast
import re
import inspect
from typing import List, Dict, Any, Optional, Set
from dataclasses import dataclass

from .models import ValidationResult


@dataclass
class SecurityIssue:
    """Represents a security issue found in code"""
    severity: str  # "high", "medium", "low"
    message: str
    line_number: Optional[int] = None
    code_snippet: Optional[str] = None


class CodeValidator:
    """Validates generated trading strategy code"""
    
    def __init__(self):
        # Dangerous imports and functions that should be blocked
        self.dangerous_imports = {
            'os', 'subprocess', 'sys', 'eval', 'exec', 'compile',
            'open', '__import__', 'globals', 'locals', 'vars',
            'dir', 'getattr', 'setattr', 'delattr', 'hasattr'
        }
        
        # Dangerous function calls
        self.dangerous_functions = {
            'eval', 'exec', 'compile', '__import__', 'open',
            'input', 'raw_input', 'file', 'execfile'
        }
        
        # Required methods for strategy interface
        self.required_methods = {
            '__init__', 'generate_signal'
        }
        
        # Optional but recommended methods
        self.recommended_methods = {
            'calculate_position_size', 'validate_signal', 'get_strategy_info'
        }
        
        # Allowed imports for trading strategies
        self.allowed_imports = {
            'numpy', 'pandas', 'typing', 'datetime', 'math', 'statistics',
            'ta', 'sklearn', 'tensorflow', 'keras', 'xgboost', 'joblib',
            'src.strategies.strategy_base', 'src.trading.mt5_bridge_tdd'
        }
    
    def validate_all(self, code: str) -> ValidationResult:
        """Perform comprehensive validation of strategy code"""
        result = ValidationResult(is_valid=True)
        
        # Syntax validation
        syntax_result = self.validate_syntax(code)
        result.has_syntax_errors = not syntax_result.is_valid
        result.errors.extend(syntax_result.errors)
        
        if result.has_syntax_errors:
            result.is_valid = False
            return result
        
        # Security validation
        security_result = self.validate_security(code)
        result.has_security_issues = not security_result.is_valid
        result.errors.extend(security_result.errors)
        
        # Interface validation
        interface_result = self.validate_interface(code)
        result.has_required_methods = interface_result.has_required_methods
        result.inherits_from_base = interface_result.inherits_from_base
        result.errors.extend(interface_result.errors)
        result.warnings.extend(interface_result.warnings)
        
        # Performance analysis
        performance_result = self.analyze_performance(code)
        result.estimated_complexity = performance_result.get('complexity')
        result.memory_usage_estimate = performance_result.get('memory_usage')
        result.warnings.extend(performance_result.get('warnings', []))
        
        # Import validation
        import_result = self.validate_imports(code)
        result.errors.extend(import_result.errors)
        result.warnings.extend(import_result.warnings)
        
        # Final validation
        result.is_valid = (
            not result.has_syntax_errors and 
            not result.has_security_issues and 
            result.has_required_methods and
            len(result.errors) == 0
        )
        
        return result
    
    def validate_syntax(self, code: str) -> ValidationResult:
        """Validate Python syntax"""
        try:
            ast.parse(code)
            return ValidationResult(is_valid=True)
        except SyntaxError as e:
            return ValidationResult(
                is_valid=False,
                has_syntax_errors=True,
                errors=[f"Syntax error at line {e.lineno}: {e.msg}"]
            )
        except Exception as e:
            return ValidationResult(
                is_valid=False,
                has_syntax_errors=True,
                errors=[f"Code parsing error: {str(e)}"]
            )
    
    def validate_security(self, code: str) -> ValidationResult:
        """Validate code for security issues"""
        issues = []
        
        try:
            tree = ast.parse(code)
            
            # Check for dangerous imports
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        if alias.name in self.dangerous_imports:
                            issues.append(f"Dangerous import detected: {alias.name}")
                
                elif isinstance(node, ast.ImportFrom):
                    if node.module in self.dangerous_imports:
                        issues.append(f"Dangerous import detected: {node.module}")
                    
                    # Check for dangerous functions from allowed modules
                    if node.names:
                        for alias in node.names:
                            if alias.name in self.dangerous_functions:
                                issues.append(f"Dangerous function import: {alias.name}")
                
                elif isinstance(node, ast.Call):
                    # Check for dangerous function calls
                    if isinstance(node.func, ast.Name):
                        if node.func.id in self.dangerous_functions:
                            issues.append(f"Dangerous function call: {node.func.id}")
                    
                    # Check for eval/exec in string form
                    elif isinstance(node.func, ast.Attribute):
                        if node.func.attr in self.dangerous_functions:
                            issues.append(f"Dangerous method call: {node.func.attr}")
            
            # Check for suspicious string patterns
            suspicious_patterns = [
                r'__.*__',  # Dunder methods (except common ones)
                r'exec\s*\(',
                r'eval\s*\(',
                r'os\.system',
                r'subprocess\.',
                r'import\s+os',
                r'from\s+os\s+import'
            ]
            
            for pattern in suspicious_patterns:
                if re.search(pattern, code, re.IGNORECASE):
                    issues.append(f"Suspicious pattern detected: {pattern}")
            
        except Exception as e:
            issues.append(f"Security analysis error: {str(e)}")
        
        return ValidationResult(
            is_valid=len(issues) == 0,
            has_security_issues=len(issues) > 0,
            errors=issues
        )
    
    def validate_interface(self, code: str) -> ValidationResult:
        """Validate strategy interface compliance"""
        errors = []
        warnings = []
        has_required_methods = True
        inherits_from_base = False
        
        try:
            tree = ast.parse(code)
            
            # Find strategy class
            strategy_classes = []
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    # Check if it inherits from StrategyBase
                    for base in node.bases:
                        if isinstance(base, ast.Name) and base.id == 'StrategyBase':
                            inherits_from_base = True
                            strategy_classes.append(node)
                        elif isinstance(base, ast.Attribute) and base.attr == 'StrategyBase':
                            inherits_from_base = True
                            strategy_classes.append(node)
            
            if not strategy_classes:
                errors.append("No strategy class found that inherits from StrategyBase")
                has_required_methods = False
            else:
                # Check each strategy class
                for class_node in strategy_classes:
                    class_methods = set()
                    
                    # Get all methods in the class
                    for node in class_node.body:
                        if isinstance(node, ast.FunctionDef):
                            class_methods.add(node.name)
                    
                    # Check required methods
                    missing_methods = self.required_methods - class_methods
                    if missing_methods:
                        errors.extend([f"Missing required method: {method}" for method in missing_methods])
                        has_required_methods = False
                    
                    # Check recommended methods
                    missing_recommended = self.recommended_methods - class_methods
                    if missing_recommended:
                        warnings.extend([f"Missing recommended method: {method}" for method in missing_recommended])
                    
                    # Validate method signatures
                    for node in class_node.body:
                        if isinstance(node, ast.FunctionDef):
                            if node.name == 'generate_signal':
                                # Check generate_signal signature
                                if len(node.args.args) < 3:  # self, symbol, data
                                    errors.append("generate_signal method must accept (self, symbol, data) parameters")
                            
                            elif node.name == '__init__':
                                # Check __init__ signature
                                if len(node.args.args) < 2:  # self, symbols
                                    errors.append("__init__ method must accept at least (self, symbols) parameters")
        
        except Exception as e:
            errors.append(f"Interface validation error: {str(e)}")
            has_required_methods = False
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            has_required_methods=has_required_methods,
            inherits_from_base=inherits_from_base
        )
    
    def validate_imports(self, code: str) -> ValidationResult:
        """Validate that imports are allowed and necessary"""
        errors = []
        warnings = []
        
        try:
            tree = ast.parse(code)
            used_imports = set()
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        module_name = alias.name.split('.')[0]
                        if module_name not in self.allowed_imports and module_name not in self.dangerous_imports:
                            warnings.append(f"Unusual import detected: {alias.name}")
                        used_imports.add(alias.name)
                
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        module_name = node.module.split('.')[0]
                        if module_name not in self.allowed_imports and module_name not in self.dangerous_imports:
                            warnings.append(f"Unusual import detected: {node.module}")
                        used_imports.add(node.module)
            
            # Check for unused imports (basic check)
            code_without_imports = re.sub(r'^(import|from)\s+.*$', '', code, flags=re.MULTILINE)
            for import_name in used_imports:
                if import_name and import_name not in code_without_imports:
                    warnings.append(f"Potentially unused import: {import_name}")
        
        except Exception as e:
            errors.append(f"Import validation error: {str(e)}")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    def analyze_performance(self, code: str) -> Dict[str, Any]:
        """Analyze code for potential performance issues"""
        warnings = []
        complexity = "medium"
        memory_usage = "medium"
        
        try:
            tree = ast.parse(code)
            
            # Count various constructs
            loop_count = 0
            nested_loop_count = 0
            function_count = 0
            class_count = 0
            
            for node in ast.walk(tree):
                if isinstance(node, (ast.For, ast.While)):
                    loop_count += 1
                    # Check for nested loops
                    for child in ast.walk(node):
                        if child != node and isinstance(child, (ast.For, ast.While)):
                            nested_loop_count += 1
                
                elif isinstance(node, ast.FunctionDef):
                    function_count += 1
                    
                    # Check function complexity
                    func_lines = len([n for n in ast.walk(node) if isinstance(n, ast.stmt)])
                    if func_lines > 50:
                        warnings.append(f"Function '{node.name}' is quite long ({func_lines} statements)")
                
                elif isinstance(node, ast.ClassDef):
                    class_count += 1
            
            # Estimate complexity
            if nested_loop_count > 2 or loop_count > 10:
                complexity = "high"
                warnings.append("High algorithmic complexity detected (many loops)")
            elif loop_count > 5:
                complexity = "medium-high"
            elif loop_count == 0 and function_count < 5:
                complexity = "low"
            
            # Check for memory-intensive operations
            memory_intensive_patterns = [
                r'\.copy\(\)',
                r'pd\.DataFrame\(',
                r'np\.array\(',
                r'\.values\.tolist\(\)',
                r'list\(.*\.values\(\)\)'
            ]
            
            memory_operations = 0
            for pattern in memory_intensive_patterns:
                memory_operations += len(re.findall(pattern, code))
            
            if memory_operations > 10:
                memory_usage = "high"
                warnings.append("Many memory-intensive operations detected")
            elif memory_operations > 5:
                memory_usage = "medium-high"
            elif memory_operations < 2:
                memory_usage = "low"
            
            # Check for potential optimization opportunities
            if 'for' in code and 'pandas' in code:
                if '.iterrows()' in code:
                    warnings.append("Consider using vectorized operations instead of iterrows()")
                if '.apply(' in code:
                    warnings.append("Consider using vectorized operations instead of apply() where possible")
            
        except Exception as e:
            warnings.append(f"Performance analysis error: {str(e)}")
        
        return {
            'complexity': complexity,
            'memory_usage': memory_usage,
            'warnings': warnings
        }
    
    def validate_test_code(self, test_code: str) -> ValidationResult:
        """Validate generated test code"""
        result = self.validate_syntax(test_code)
        
        if not result.is_valid:
            return result
        
        # Additional test-specific validations
        warnings = []
        
        # Check for proper test structure
        if 'def test_' not in test_code:
            result.errors.append("Test code must contain test functions starting with 'test_'")
            result.is_valid = False
        
        # Check for assertions
        if 'assert' not in test_code:
            warnings.append("Test code should contain assertions")
        
        # Check for proper test data setup
        if 'pd.Series' not in test_code and 'DataFrame' not in test_code:
            warnings.append("Test should include proper market data setup")
        
        result.warnings.extend(warnings)
        return result
    
    def suggest_improvements(self, code: str, validation_result: ValidationResult) -> List[str]:
        """Suggest improvements based on validation results"""
        suggestions = []
        
        if validation_result.has_syntax_errors:
            suggestions.append("Fix syntax errors before proceeding")
        
        if validation_result.has_security_issues:
            suggestions.append("Remove dangerous imports and function calls")
        
        if not validation_result.has_required_methods:
            suggestions.append("Implement all required methods: " + ", ".join(self.required_methods))
        
        if not validation_result.inherits_from_base:
            suggestions.append("Ensure strategy class inherits from StrategyBase")
        
        if validation_result.estimated_complexity == "high":
            suggestions.append("Consider simplifying the strategy logic to improve performance")
        
        if validation_result.memory_usage_estimate == "high":
            suggestions.append("Optimize memory usage by reducing data copying and using vectorized operations")
        
        # Code quality suggestions
        if 'TODO' in code or 'FIXME' in code:
            suggestions.append("Complete all TODO and FIXME items")
        
        if len(code.split('\n')) > 500:
            suggestions.append("Consider breaking down large strategies into smaller, more manageable components")
        
        # Documentation suggestions
        if '"""' not in code:
            suggestions.append("Add docstrings to classes and methods for better documentation")
        
        return suggestions
    
    def get_validation_summary(self, validation_result: ValidationResult) -> str:
        """Generate a human-readable validation summary"""
        if validation_result.is_valid:
            summary = "✅ Code validation passed successfully!\n"
        else:
            summary = "❌ Code validation failed.\n"
        
        if validation_result.errors:
            summary += f"\n🚨 Errors ({len(validation_result.errors)}):\n"
            for error in validation_result.errors:
                summary += f"  • {error}\n"
        
        if validation_result.warnings:
            summary += f"\n⚠️  Warnings ({len(validation_result.warnings)}):\n"
            for warning in validation_result.warnings:
                summary += f"  • {warning}\n"
        
        # Performance summary
        if validation_result.estimated_complexity:
            summary += f"\n📊 Estimated Complexity: {validation_result.estimated_complexity}"
        
        if validation_result.memory_usage_estimate:
            summary += f"\n💾 Memory Usage: {validation_result.memory_usage_estimate}"
        
        return summary