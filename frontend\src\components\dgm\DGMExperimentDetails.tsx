/**
 * DGM Experiment Details Component
 * Detailed view of a DGM experiment with results and progress
 */

// import React from 'react'; // Not needed with new JSX transform
import { ArrowLeft, Trophy, TrendingUp, <PERSON><PERSON><PERSON>, Zap, Clock, Target } from 'lucide-react';

import type { DGMExperiment, DGMExperimentProgress } from '@shared/schemas';

interface DGMExperimentDetailsProps {
  experiment: DGMExperiment;
  progress?: DGMExperimentProgress;
  onBack: () => void;
}

export function DGMExperimentDetails({ experiment, progress, onBack }: DGMExperimentDetailsProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      'pending': 'yellow',
      'running': 'blue',
      'completed': 'green',
      'deployed': 'purple',
      'error': 'red'
    };
    return colors[status] || 'gray';
  };

  const statusColor = getStatusColor(experiment.status);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <button
          onClick={onBack}
          className="btn-secondary"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </button>
        
        <div className="flex-1">
          <h1 className="text-2xl font-bold text-gray-900">
            {experiment.experiment_name}
          </h1>
          <div className="flex items-center space-x-4 mt-2">
            <span className={`badge-${statusColor} capitalize`}>
              {experiment.status}
            </span>
            <span className="text-sm text-gray-600">
              Created: {formatDate(experiment.created_at.toISOString())}
            </span>
          </div>
        </div>
      </div>

      {/* Progress Bar for Running Experiments */}
      {progress && experiment.status === 'running' && (
        <div className="card p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            <Clock className="w-5 h-5 inline mr-2" />
            Progress
          </h2>
          
          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>{progress.current_step}</span>
                <span>{progress.progress_percent}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${progress.progress_percent}%` }}
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Generation:</span>
                <span className="ml-2 font-medium">
                  {progress.current_generation || 0}
                </span>
              </div>
              <div>
                <span className="text-gray-600">Current Generation:</span>
                <span className="ml-2 font-medium">
                  {progress.current_generation || 0}
                </span>
              </div>
              <div>
                <span className="text-gray-600">Best Fitness:</span>
                <span className="ml-2 font-medium">
                  {progress.best_fitness?.toFixed(4) || 'N/A'}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Results Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="card p-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
              <Trophy className="w-5 h-5 text-yellow-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Fitness Improvement</p>
              <p className="text-xl font-bold text-gray-900">
                {experiment.fitness_improvement?.toFixed(4) || 'N/A'}
              </p>
            </div>
          </div>
        </div>
        
        <div className="card p-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Improvement</p>
              <p className="text-xl font-bold text-gray-900">
                {experiment.fitness_improvement 
                  ? `${(experiment.fitness_improvement * 100).toFixed(2)}%`
                  : 'N/A'
                }
              </p>
            </div>
          </div>
        </div>
        
        <div className="card p-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <BarChart className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Status</p>
              <p className="text-xl font-bold text-gray-900 capitalize">
                {experiment.status}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Base Strategy Configuration */}
      <div className="card p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          <Target className="w-5 h-5 inline mr-2" />
          Base Strategy Configuration
        </h2>
        
        <div className="bg-gray-50 rounded-lg p-4">
          <pre className="text-sm text-gray-700 overflow-auto">
            {JSON.stringify(experiment.base_strategy, null, 2)}
          </pre>
        </div>
      </div>

      {/* Best Evolved Strategy */}
      {experiment.status === 'completed' && experiment.generated_strategy && (
        <div className="card p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            <Trophy className="w-5 h-5 inline mr-2" />
            Best Evolved Strategy
          </h2>
          
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <pre className="text-sm text-gray-700 overflow-auto">
              {JSON.stringify(experiment.generated_strategy, null, 2)}
            </pre>
          </div>
          
          <div className="mt-4 flex justify-end">
            <button className="btn-primary">
              <Zap className="w-4 h-4 mr-2" />
              Deploy Strategy
            </button>
          </div>
        </div>
      )}

      {/* Evolution Logs - TODO: Add evolutionLogs to DGMExperiment schema */}
      {false && (
        <div className="card p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            Evolution Progress
          </h2>
          
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {[].map((log: any, index: number) => (
              <div key={index} className="text-sm p-2 bg-gray-50 rounded border-l-2 border-blue-500">
                <span className="text-gray-500 text-xs">
                  {formatDate(log.timestamp)}
                </span>
                <p className="text-gray-700">{log.message}</p>
                {log.fitness && (
                  <span className="text-blue-600 font-medium">
                    Fitness: {log.fitness.toFixed(4)}
                  </span>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Performance Metrics - TODO: Add performanceMetrics to DGMExperiment schema */}
      {false && (
        <div className="card p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            Performance Metrics
          </h2>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Object.entries({}).map(([key, value]: [string, any]) => (
              <div key={key} className="text-center p-3 bg-gray-50 rounded-lg">
                <p className="text-xs text-gray-600 capitalize">
                  {key.replace(/_/g, ' ')}
                </p>
                <p className="text-lg font-semibold text-gray-900">
                  {typeof value === 'number' ? value.toFixed(4) : String(value)}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}