# MVP Requirements - Minimal Dependencies for Core Functionality
# This file contains only the essential dependencies needed for the MVP

# Core Web Framework
fastapi==0.104.1
uvicorn==0.24.0

# Database (SQLite for simplicity)
sqlalchemy>=2.0.0
aiosqlite>=0.19.0

# Data Validation
pydantic>=2.4.2

# Environment Configuration
python-dotenv>=1.0.0

# Data Processing (minimal set)
pandas>=2.2.0
numpy>=1.26.0

# HTTP Client
requests>=2.31.0

# Testing (essential only)
pytest>=8.0.0
pytest-cov>=4.1.0
pytest-mock>=3.10.0

# MT5 Integration (if available)
# MetaTrader5>=5.0.45  # Uncomment if MT5 is available

# Optional: For enhanced testing
# hypothesis>=6.135.0  # Uncomment for property-based testing
# faker>=19.0.0        # Uncomment for test data generation