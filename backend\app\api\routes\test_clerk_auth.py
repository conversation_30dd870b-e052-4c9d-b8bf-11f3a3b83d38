import pytest
from unittest.mock import Mock, patch, AsyncMock
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, Request
from .auth import clerk_auth_middleware, get_current_user, ClerkAuth

@pytest.mark.asyncio
def test_clerk_auth_middleware_missing_token():
    """Test middleware rejects requests without token"""
    request = Mock()
    request.headers = {}
    
    with pytest.raises(HTTPException) as exc_info:
        import asyncio
        asyncio.run(clerk_auth_middleware(request))
    
    assert exc_info.value.status_code == 401
    assert "Unauthorized" in exc_info.value.detail

@pytest.mark.asyncio
def test_clerk_auth_middleware_invalid_token():
    """Test middleware rejects invalid tokens"""
    request = Mock()
    request.headers = {"Authorization": "Bearer invalid_token"}
    
    with patch('backend.app.api.routes.auth.Clerk') as mock_clerk:
        mock_instance = mock_clerk.return_value
        mock_instance.get_user = AsyncMock(side_effect=Exception("Invalid token"))
        import asyncio
        with pytest.raises(HTTPException) as exc_info:
            asyncio.run(clerk_auth_middleware(request))
        assert exc_info.value.status_code == 401

@pytest.mark.asyncio
def test_clerk_auth_middleware_valid_token():
    """Test middleware accepts valid tokens and returns user data"""
    request = Mock()
    request.headers = {"Authorization": "Bearer valid_token"}
    
    mock_user_data = {
        "id": "user_123",
        "email": "<EMAIL>",
        "first_name": "John",
        "last_name": "Trader"
    }
    
    with patch('backend.app.api.routes.auth.Clerk') as mock_clerk:
        mock_instance = mock_clerk.return_value
        mock_instance.get_user = AsyncMock(return_value=mock_user_data)
        import asyncio
        user = asyncio.run(clerk_auth_middleware(request))
        assert user["id"] == "user_123"
        assert user["email"] == "<EMAIL>"

@pytest.mark.asyncio
def test_get_current_user_integration():
    """Test the complete flow from request to user data"""
    request = Mock()
    request.headers = {"Authorization": "Bearer valid_token"}
    
    with patch('backend.app.api.routes.auth.clerk_auth_middleware') as mock_middleware:
        mock_middleware.return_value = {"id": "user_123", "email": "<EMAIL>"}
        import asyncio
        user = asyncio.run(get_current_user(request))
        assert user["id"] == "user_123"
        mock_middleware.assert_called_once_with(request)
