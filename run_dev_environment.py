"""
Run the development environment
This script runs both the frontend and backend servers
"""

import os
import sys
import subprocess
import threading
import time

def run_backend():
    """Run the backend server"""
    # Get the directory of this script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Path to the simple server script
    server_script = os.path.join(script_dir, "backend", "simple_server.py")
    
    # Run the server
    try:
        print("Starting backend server...")
        subprocess.run([sys.executable, server_script], check=True)
    except KeyboardInterrupt:
        print("Backend server stopped by user")
    except Exception as e:
        print(f"Error running backend server: {e}")

def run_frontend():
    """Run the frontend server"""
    # Get the directory of this script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Path to the frontend directory
    frontend_dir = os.path.join(script_dir, "frontend")
    
    # Run the frontend server
    try:
        print("Starting frontend server...")
        # Change to the frontend directory
        os.chdir(frontend_dir)
        # Run npm start
        subprocess.run(["npm", "start"], check=True)
    except KeyboardInterrupt:
        print("Frontend server stopped by user")
    except Exception as e:
        print(f"Error running frontend server: {e}")

def main():
    """Run both servers"""
    # Start the backend server in a separate thread
    backend_thread = threading.Thread(target=run_backend)
    backend_thread.daemon = True
    backend_thread.start()
    
    # Wait for the backend server to start
    print("Waiting for backend server to start...")
    time.sleep(2)
    
    # Run the frontend server in the main thread
    run_frontend()

if __name__ == "__main__":
    main()