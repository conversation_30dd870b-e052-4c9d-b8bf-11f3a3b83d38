﻿/**
 * Settings Page
 * User settings and preferences
 */

// import React from 'react'; // Not needed with new JSX transform
import { Settings } from 'lucide-react';

export function SettingsPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
        <p className="text-gray-600 mt-2">
          Manage your account settings and preferences
        </p>
      </div>

      {/* Coming Soon Placeholder */}
      <div className="card p-12 text-center">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Settings className="w-8 h-8 text-gray-600" />
        </div>
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          Settings Panel Coming Soon
        </h2>
        <p className="text-gray-600 max-w-md mx-auto">
          User settings, preferences, and account management 
          features are being developed for a complete 
          customization experience.
        </p>
      </div>
    </div>
  );
}