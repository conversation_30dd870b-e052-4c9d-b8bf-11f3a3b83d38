"""
Tests for Moving Average Crossover Strategy
"""

import pytest
import logging
from src.strategies.moving_average_crossover import MovingAverageCrossover
from src.trading.mt5_bridge_tdd import MT5Bridge

# Configure logging
logger = logging.getLogger(__name__)


@pytest.fixture
def mt5_bridge():
    """Fixture to create an MT5 Bridge instance in offline mode"""
    bridge = MT5Bridge(offline_mode=True)
    # Ensure it's connected
    bridge.connect()
    yield bridge
    # Clean up
    if bridge.is_connected():
        bridge.disconnect()


@pytest.fixture
def ma_strategy(mt5_bridge):
    """Fixture to create a Moving Average Crossover strategy"""
    strategy = MovingAverageCrossover(
        symbols=["EURUSD", "USDJPY"],
        fast_period=5,
        slow_period=10,
        mt5_bridge=mt5_bridge,
        offline_mode=True
    )
    yield strategy
    # Clean up
    if strategy.active:
        strategy.stop()


@pytest.mark.unit
def test_ma_strategy_initialization(ma_strategy):
    """Test strategy initialization"""
    assert ma_strategy.name == "MA Crossover"
    assert ma_strategy.symbols == ["EURUSD", "USDJPY"]
    assert ma_strategy.fast_period == 5
    assert ma_strategy.slow_period == 10
    assert ma_strategy.active is False
    assert ma_strategy.mt5_bridge.is_connected() is True


@pytest.mark.unit
def test_ma_strategy_start_stop(ma_strategy):
    """Test strategy start and stop"""
    # Start the strategy
    result = ma_strategy.start()
    assert result is True
    assert ma_strategy.active is True
    
    # Stop the strategy
    result = ma_strategy.stop()
    assert result is True
    assert ma_strategy.active is False


@pytest.mark.unit
def test_ma_strategy_generate_signals(ma_strategy):
    """Test signal generation"""
    # Start the strategy
    ma_strategy.start()
    
    # Force a crossover by manipulating the historical data
    # First, get some historical data
    eurusd_data = ma_strategy._get_historical_prices("EURUSD")
    
    # Modify the last few prices to create a crossover
    # Make the fast MA cross above the slow MA
    for i in range(1, 6):  # Last 5 prices for fast MA
        eurusd_data[-i] = eurusd_data[-i] * 1.01  # Increase by 1%
    
    # Generate signals
    signals = ma_strategy.generate_signals()
    
    # Since we're using random data, we might not always get signals
    # So we'll just check that the function runs without errors
    # and returns a list (even if empty)
    assert isinstance(signals, list)
    
    # If we have signals, check their structure
    for signal in signals:
        assert "symbol" in signal
        assert "action" in signal
        assert signal["symbol"] in ["EURUSD", "USDJPY"]
        assert signal["action"] in ["BUY", "SELL", "CLOSE"]
        
        # If it's a BUY or SELL signal, it should have lot, stop_loss, and take_profit
        if signal["action"] in ["BUY", "SELL"]:
            assert "lot" in signal
            assert "stop_loss" in signal
            assert "take_profit" in signal


@pytest.mark.unit
def test_ma_strategy_execute_signal(ma_strategy):
    """Test signal execution"""
    # Start the strategy
    ma_strategy.start()
    
    # Create a test signal
    signal = {
        "symbol": "EURUSD",
        "action": "BUY",
        "lot": 0.1,
        "stop_loss": 1.05,
        "take_profit": 1.15
    }
    
    # Execute the signal
    order_id = ma_strategy.execute_signal(signal)
    
    # Check that the order was placed
    assert order_id is not None
    assert ma_strategy.mt5_bridge.get_order_status(order_id) == "filled"
    
    # Check that the position is tracked
    ma_strategy.update()
    assert len(ma_strategy.positions) == 1
    
    # Create a close signal
    close_signal = {
        "symbol": "EURUSD",
        "action": "CLOSE"
    }
    
    # Execute the close signal
    result = ma_strategy.execute_signal(close_signal)
    
    # Check that the position was closed
    assert result is not None
    assert ma_strategy.mt5_bridge.get_order_status(order_id) == "closed"
    
    # Update positions
    ma_strategy.update()
    assert len(ma_strategy.positions) == 0


@pytest.mark.unit
def test_ma_strategy_performance(ma_strategy):
    """Test performance tracking"""
    # Start the strategy
    ma_strategy.start()
    
    # Execute a few trades
    for i in range(5):
        signal = {
            "symbol": "EURUSD",
            "action": "BUY",
            "lot": 0.1,
            "stop_loss": 1.05,
            "take_profit": 1.15
        }
        order_id = ma_strategy.execute_signal(signal)
        
        # Close the order
        close_signal = {
            "symbol": "EURUSD",
            "action": "CLOSE"
        }
        ma_strategy.execute_signal(close_signal)
    
    # Check performance metrics
    performance = ma_strategy.get_performance()
    # The strategy might generate additional trades during initialization,
    # so we'll just check that the total_trades is at least 5
    assert performance["total_trades"] >= 5
    
    # Win rate should be calculated
    assert "win_rate" in performance