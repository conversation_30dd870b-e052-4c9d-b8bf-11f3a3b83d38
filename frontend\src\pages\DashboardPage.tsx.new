/**
 * Dashboard Page
 * Main dashboard with overview metrics and quick actions
 */

import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Target, 
  Upload, 
  MessageSquare,
  Zap,
  BarChart3,
  Activity,
  Users
} from 'lucide-react';

import { EquityCurve } from '@/components/backtest/EquityCurve';
import { ChatWidget } from '@/components/chat/ChatWidget';
import { apiService } from '@/services/api';
import { useAuth, useSubscriptionTier, useApiQuota } from '@/hooks/useAuth';
// import { useApi } from '@/hooks/useApi'; // Not currently used

interface DashboardStats {
  totalReturn: number;
  totalBacktests: number;
  winRate: number;
  bestPerformer: any;
  activeExperiments: number;
  deployedStrategies: number;
}

interface EquityPoint {
  timestamp: string;
  equity: number;
  drawdownPct?: number;
}

export function DashboardPage() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { tier, hasFeature } = useSubscriptionTier();
  const { quotaUsed, quotaLimit, quotaPercentage, isNearLimit } = useApiQuota();
  
  const [stats, setStats] = useState<DashboardStats>({
    totalReturn: 0,
    totalBacktests: 0,
    winRate: 0,
    bestPerformer: null,
    activeExperiments: 0,
    deployedStrategies: 0,
  });
  const [recentEquity, setRecentEquity] = useState<EquityPoint[]>([]);
  const [showChat, setShowChat] = useState(false);

  // const { loading: statsLoading } = useApi(); // Not currently used

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      const [overview, metrics] = await Promise.all([
        apiService.getPortfolioOverview(),
        apiService.getPerformanceMetrics('1M')
      ]);
      
      setStats({
        totalReturn: overview.totalReturn || 0,
        totalBacktests: overview.totalBacktests || 0,
        winRate: overview.winRate || 0,
        bestPerformer: overview.bestPerformer || null,
        activeExperiments: overview.activeExperiments || 0,
        deployedStrategies: overview.deployedStrategies || 0,
      });
      
      setRecentEquity(metrics.equityCurve || []);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      // Set default values on error
      setStats({
        totalReturn: 0,
        totalBacktests: 0,
        winRate: 0,
        bestPerformer: null,
        activeExperiments: 0,
        deployedStrategies: 0,
      });
    }
  };

  const StatCard = ({ 
    title, 
    value, 
    change, 
    icon: Icon, 
    color, 
    subtitle 
  }: {
    title: string;
    value: string | number;
    change?: number;
    icon: any;
    color: string;
    subtitle?: string;
  }) => (
    <div className="card-hover p-6">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${color.replace('text-', 'bg-').replace('-600', '-100')}`}>
            <Icon className={`w-6 h-6 ${color}`} />
          </div>
        </div>
        <div className="ml-4 flex-1">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {subtitle && (
            <p className="text-xs text-gray-500 mt-1">{subtitle}</p>
          )}
          {change !== undefined && (
            <div className="flex items-center mt-1">
              {change >= 0 ? (
                <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
              ) : (
                <TrendingDown className="w-4 h-4 text-red-500 mr-1" />
              )}
              <span className={`text-sm font-medium ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {change >= 0 ? '+' : ''}{change.toFixed(2)}%
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  const QuickActionCard = ({ 
    title, 
    description, 
    icon: Icon, 
    color, 
    onClick, 
    disabled = false,
    badge
  }: {
    title: string;
    description: string;
    icon: any;
    color: string;
    onClick: () => void;
    disabled?: boolean;
    badge?: string;
  }) => (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`w-full text-left p-4 rounded-lg border-2 transition-all duration-200 ${
        disabled 
          ? 'border-gray-200 bg-gray-50 cursor-not-allowed' 
          : `border-gray-200 hover:border-${color.split('-')[1]}-300 hover:bg-${color.split('-')[1]}-50`
      }`}
    >
      <div className="flex items-start space-x-3">
        <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
          disabled ? 'bg-gray-200' : color.replace('text-', 'bg-').replace('-600', '-100')
        }`}>
          <Icon className={`w-5 h-5 ${disabled ? 'text-gray-400' : color}`} />
        </div>
        <div className="flex-1">
          <div className="flex items-center space-x-2">
            <h3 className={`font-medium ${disabled ? 'text-gray-400' : 'text-gray-900'}`}>
              {title}
            </h3>
            {badge && (
              <span className="badge-primary text-xs">{badge}</span>
            )}
          </div>
          <p className={`text-sm mt-1 ${disabled ? 'text-gray-400' : 'text-gray-600'}`}>
            {description}
          </p>
        </div>
      </div>
    </button>
  );

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="gradient-bg rounded-lg shadow-lg p-6 text-white">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold mb-2">
              Welcome back, {user?.fullName || user?.email?.split('@')[0]}!
            </h1>
            <p className="text-blue-100 text-lg">
              Your AI-powered trading platform is ready. Let's analyze your strategies.
            </p>
            <div className="mt-4 flex items-center space-x-6 text-sm">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span>Platform Online</span>
              </div>
              <div className="flex items-center space-x-2">
                <Users className="w-4 h-4" />
                <span className="capitalize">{tier} Plan</span>
              </div>
              <div className="flex items-center space-x-2">
                <Activity className="w-4 h-4" />
                <span>{quotaUsed}/{quotaLimit} API calls</span>
              </div>
            </div>
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={() => setShowChat(!showChat)}
              className="px-4 py-2 bg-white/20 backdrop-blur-sm text-white rounded-lg hover:bg-white/30 flex items-center space-x-2 transition-colors"
            >
              <MessageSquare className="w-4 h-4" />
              <span>AI Assistant</span>
            </button>
          </div>
        </div>
      </div>

      {/* API Quota Warning */}
      {isNearLimit && (
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
              <Zap className="w-4 h-4 text-orange-600" />
            </div>
            <div>
              <h3 className="text-sm font-medium text-orange-900">
                API Quota Warning
              </h3>
              <p className="text-sm text-orange-700 mt-1">
                You've used {quotaPercentage}% of your monthly API quota. Consider upgrading your plan for higher limits.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Return"
          value={`${stats.totalReturn >= 0 ? '+' : ''}${stats.totalReturn.toFixed(2)}%`}
          change={stats.totalReturn}
          icon={DollarSign}
          color="text-green-600"
          subtitle="All-time performance"
        />
        
        <StatCard
          title="Active Backtests"
          value={stats.totalBacktests}
          icon={Target}
          color="text-blue-600"
          subtitle="Running strategies"
        />
        
        <StatCard
          title="Win Rate"
          value={`${stats.winRate.toFixed(1)}%`}
          icon={TrendingUp}
          color="text-purple-600"
          subtitle="Successful trades"
        />
        
        <StatCard
          title="DGM Experiments"
          value={stats.activeExperiments}
          icon={BarChart3}
          color="text-orange-600"
          subtitle="AI-evolved strategies"
        />
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Equity Curve */}
        <div className="lg:col-span-2">
          {recentEquity.length > 0 ? (
            <EquityCurve 
              data={recentEquity} 
              initialBalance={100000}
              height={400}
            />
          ) : (
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Chart</h3>
              <div className="flex flex-col items-center justify-center h-64 text-gray-500">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                  <TrendingUp className="w-8 h-8 text-gray-400" />
                </div>
                <p className="text-lg font-medium mb-2">No performance data yet</p>
                <p className="text-sm text-center text-gray-600 max-w-xs">
                  Upload trading data and run backtests to see your performance chart and analytics
                </p>
                <button className="btn-primary mt-4">
                  <Upload className="w-4 h-4 mr-2" />
                  Upload Data
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div className="space-y-3">
              <QuickActionCard
                title="Upload Data"
                description="Import new trading data files"
                icon={Upload}
                color="text-blue-600"
                onClick={() => navigate('/upload')}
              />
              
              <QuickActionCard
                title="New Backtest"
                description="Test a trading strategy"
                icon={Target}
                color="text-green-600"
                onClick={() => navigate('/backtesting')}
              />
              
              <QuickActionCard
                title="DGM Experiment"
                description="Evolve strategies with AI"
                icon={BarChart3}
                color="text-purple-600"
                onClick={() => navigate('/dgm')}
                disabled={!hasFeature('dgm_experiments')}
                badge={hasFeature('dgm_experiments') ? undefined : 'Pro'}
              />
              
              <QuickActionCard
                title="Portfolio Analysis"
                description="Analyze your performance"
                icon={TrendingUp}
                color="text-orange-600"
                onClick={() => navigate('/portfolio')}
              />
            </div>
          </div>

          {/* Subscription Status */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Account Status</h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Current Plan</span>
                <span className="text-sm font-medium text-gray-900 capitalize badge-primary">
                  {tier}
                </span>
              </div>
              
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm text-gray-600">API Usage</span>
                  <span className="text-sm font-medium text-gray-900">
                    {quotaUsed} / {quotaLimit}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                      quotaPercentage >= 90 ? 'bg-red-500' :
                      quotaPercentage >= 75 ? 'bg-orange-500' :
                      'bg-blue-600'
                    }`}
                    style={{ width: `${Math.min(quotaPercentage, 100)}%` }}
                  />
                </div>
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>{quotaPercentage}% used</span>
                  <span>Resets monthly</span>
                </div>
              </div>
              
              {tier === 'free' && (
                <div className="pt-3 border-t border-gray-200">
                  <button className="w-full btn-primary text-sm">
                    <Zap className="w-4 h-4 mr-2" />
                    Upgrade Plan
                  </button>
                  <p className="text-xs text-gray-500 mt-2 text-center">
                    Unlock advanced features and higher limits
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Recent Activity */}
          {stats.bestPerformer && (
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Best Performer</h3>
              <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4 border border-green-200">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-xs font-bold">
                    1
                  </div>
                  <span className="font-medium text-gray-900">{stats.bestPerformer.name}</span>
                </div>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Return:</span>
                    <span className="font-medium text-green-600">
                      +{(stats.bestPerformer.return * 100).toFixed(2)}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Win Rate:</span>
                    <span className="font-medium">{(stats.bestPerformer.winRate * 100).toFixed(1)}%</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Chat Widget */}
      {showChat && (
        <div className="fixed bottom-4 right-4 w-96 h-96 z-50 shadow-2xl">
          <ChatWidget 
            className="h-full rounded-lg overflow-hidden" 
            onClose={() => setShowChat(false)}
          />
        </div>
      )}
    </div>
  );
}
