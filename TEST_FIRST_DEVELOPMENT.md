# Test-First Development Guide

This guide provides practical steps for implementing new features using a test-first approach, following Test-Driven Development (TDD) principles.

## Why Test-First Development?

Test-first development offers several benefits:

- **Clearer requirements**: Writing tests first forces you to clarify what the code should do
- **Better design**: Tests guide you toward more modular, loosely coupled code
- **Fewer bugs**: Tests catch issues early in the development process
- **Regression protection**: Tests prevent future changes from breaking existing functionality
- **Documentation**: Tests serve as executable documentation of how the code should behave

## The Test-First Workflow

### 1. Start with a Test

Begin by writing a test that describes the behavior you want to implement:

```python
# tests/test_risk_manager.py
import pytest
from trading.risk_manager import RiskManager

def test_calculate_position_size_based_on_risk_percentage():
    # Arrange
    risk_manager = RiskManager()
    account_balance = 10000
    risk_percentage = 2  # 2% risk
    entry_price = 100
    stop_loss = 95
    
    # Act
    position_size = risk_manager.calculate_position_size(
        account_balance=account_balance,
        risk_percentage=risk_percentage,
        entry_price=entry_price,
        stop_loss=stop_loss
    )
    
    # Assert
    expected_position_size = 40  # (10000 * 0.02) / (100 - 95)
    assert position_size == expected_position_size
```

### 2. Run the Test (It Should Fail)

Run the test to verify that it fails. This is the "Red" phase of TDD:

```bash
python -m pytest tests/test_risk_manager.py -v
```

The test should fail because the `RiskManager` class doesn't exist yet.

### 3. Implement the Minimum Code to Pass the Test

Write just enough code to make the test pass. This is the "Green" phase of TDD:

```python
# trading/risk_manager.py
class RiskManager:
    def calculate_position_size(self, account_balance, risk_percentage, entry_price, stop_loss):
        risk_amount = account_balance * (risk_percentage / 100)
        price_difference = abs(entry_price - stop_loss)
        position_size = risk_amount / price_difference
        return position_size
```

### 4. Run the Test Again (It Should Pass)

Run the test again to verify that it passes:

```bash
python -m pytest tests/test_risk_manager.py -v
```

### 5. Refactor the Code

Improve the code while keeping the tests passing. This is the "Refactor" phase of TDD:

```python
# trading/risk_manager.py
class RiskManager:
    def calculate_position_size(self, account_balance, risk_percentage, entry_price, stop_loss):
        """
        Calculate position size based on risk percentage.
        
        Args:
            account_balance (float): The account balance
            risk_percentage (float): The risk percentage (e.g., 2 for 2%)
            entry_price (float): The entry price
            stop_loss (float): The stop loss price
            
        Returns:
            float: The position size
        """
        if risk_percentage <= 0:
            raise ValueError("Risk percentage must be positive")
            
        if entry_price == stop_loss:
            raise ValueError("Entry price cannot equal stop loss price")
            
        risk_amount = account_balance * (risk_percentage / 100)
        price_difference = abs(entry_price - stop_loss)
        position_size = risk_amount / price_difference
        
        return position_size
```

### 6. Add More Tests for Edge Cases

Add tests for edge cases and error conditions:

```python
def test_calculate_position_size_with_zero_risk_percentage():
    risk_manager = RiskManager()
    account_balance = 10000
    risk_percentage = 0
    entry_price = 100
    stop_loss = 95
    
    with pytest.raises(ValueError, match="Risk percentage must be positive"):
        risk_manager.calculate_position_size(
            account_balance=account_balance,
            risk_percentage=risk_percentage,
            entry_price=entry_price,
            stop_loss=stop_loss
        )

def test_calculate_position_size_with_equal_entry_and_stop_loss():
    risk_manager = RiskManager()
    account_balance = 10000
    risk_percentage = 2
    entry_price = 100
    stop_loss = 100
    
    with pytest.raises(ValueError, match="Entry price cannot equal stop loss price"):
        risk_manager.calculate_position_size(
            account_balance=account_balance,
            risk_percentage=risk_percentage,
            entry_price=entry_price,
            stop_loss=stop_loss
        )
```

### 7. Repeat for Each Feature

Repeat this process for each feature or behavior you want to implement.

## Tips for Effective Test-First Development

### Write Testable Code

- **Dependency Injection**: Pass dependencies to classes rather than creating them internally
- **Single Responsibility**: Each class should have a single responsibility
- **Pure Functions**: Functions that return the same output for the same input are easier to test

### Test Behavior, Not Implementation

- Focus on testing what the code does, not how it does it
- Avoid testing private methods directly
- Test the public API of your classes

### Use Test Doubles

- **Mocks**: Objects that record method calls for verification
- **Stubs**: Objects that provide predefined responses
- **Fakes**: Simplified implementations of complex dependencies
- **Spies**: Objects that record method calls but don't affect behavior

Example:

```python
from unittest.mock import patch, MagicMock

@patch('trading.data_provider.DataProvider')
def test_strategy_uses_data_provider(mock_data_provider):
    # Configure the mock
    mock_data_provider.return_value.get_price_data.return_value = [
        {"time": "2023-01-01", "open": 100, "high": 105, "low": 95, "close": 102},
        {"time": "2023-01-02", "open": 102, "high": 107, "low": 101, "close": 106}
    ]
    
    # Create the strategy with the mock
    strategy = Strategy(data_provider=mock_data_provider.return_value)
    
    # Call the method being tested
    signals = strategy.generate_signals("EURUSD")
    
    # Verify the result
    assert len(signals) == 1
    assert signals[0]["action"] == "BUY"
    
    # Verify the interaction with the mock
    mock_data_provider.return_value.get_price_data.assert_called_once_with("EURUSD")
```

### Use Parameterized Tests

Use parameterized tests to test multiple scenarios with the same test function:

```python
@pytest.mark.parametrize("account_balance,risk_percentage,entry_price,stop_loss,expected_size", [
    (10000, 2, 100, 95, 40),    # Standard case
    (10000, 1, 100, 95, 20),    # Lower risk
    (20000, 2, 100, 95, 80),    # Higher balance
    (10000, 2, 100, 90, 20),    # Wider stop
])
def test_calculate_position_size_scenarios(
    account_balance, risk_percentage, entry_price, stop_loss, expected_size
):
    risk_manager = RiskManager()
    position_size = risk_manager.calculate_position_size(
        account_balance=account_balance,
        risk_percentage=risk_percentage,
        entry_price=entry_price,
        stop_loss=stop_loss
    )
    assert position_size == expected_size
```

### Use Property-Based Testing

For more complex behaviors, use property-based testing to verify properties that should hold for all inputs:

```python
from hypothesis import given, strategies as st

@given(
    account_balance=st.floats(min_value=100, max_value=1000000),
    risk_percentage=st.floats(min_value=0.1, max_value=10),
    entry_price=st.floats(min_value=1, max_value=1000),
    stop_loss_diff=st.floats(min_value=0.1, max_value=100)
)
def test_position_size_proportional_to_account_balance(
    account_balance, risk_percentage, entry_price, stop_loss_diff
):
    risk_manager = RiskManager()
    stop_loss = entry_price - stop_loss_diff
    
    position_size1 = risk_manager.calculate_position_size(
        account_balance=account_balance,
        risk_percentage=risk_percentage,
        entry_price=entry_price,
        stop_loss=stop_loss
    )
    
    position_size2 = risk_manager.calculate_position_size(
        account_balance=account_balance * 2,  # Double the account balance
        risk_percentage=risk_percentage,
        entry_price=entry_price,
        stop_loss=stop_loss
    )
    
    # Position size should be proportional to account balance
    assert abs(position_size2 / position_size1 - 2) < 1e-10
```

## Conclusion

Test-first development is a powerful approach that leads to better code quality, fewer bugs, and more maintainable software. By following the steps in this guide, you can implement new features with confidence, knowing that they are well-tested and meet the requirements.