// test/helpers/test-factory.ts
import { z } from 'zod';
import { faker } from '@faker-js/faker';

/**
 * Test Factory Pattern for consistent test data generation
 * Following TDD principles: Write tests first, then implementation
 */

// Define schemas first (Schema-First Development)
export const TradeSchema = z.object({
  id: z.string().uuid(),
  symbol: z.string().min(1).max(10),
  quantity: z.number().positive(),
  price: z.number().positive(),
  side: z.enum(['buy', 'sell']),
  status: z.enum(['pending', 'executed', 'cancelled', 'failed']),
  timestamp: z.date(),
  userId: z.string().uuid(),
});

export const MarketDataSchema = z.object({
  symbol: z.string(),
  price: z.number().positive(),
  volume: z.number().nonnegative(),
  bid: z.number().positive(),
  ask: z.number().positive(),
  timestamp: z.date(),
});

// Test data factories
export class TestFactory {
  static createTrade(overrides?: Partial<z.infer<typeof TradeSchema>>) {
    const trade = {
      id: faker.string.uuid(),
      symbol: faker.helpers.arrayElement(['AAPL', 'GOOGL', 'MSFT', 'AMZN']),
      quantity: faker.number.int({ min: 1, max: 1000 }),
      price: faker.number.float({ min: 10, max: 500, precision: 0.01 }),
      side: faker.helpers.arrayElement(['buy', 'sell'] as const),
      status: 'pending' as const,
      timestamp: faker.date.recent(),
      userId: faker.string.uuid(),
      ...overrides,
    };
    
    return TradeSchema.parse(trade);
  }

  static createMarketData(overrides?: Partial<z.infer<typeof MarketDataSchema>>) {
    const price = faker.number.float({ min: 10, max: 500, precision: 0.01 });
    const spread = faker.number.float({ min: 0.01, max: 0.5, precision: 0.01 });
    
    const marketData = {
      symbol: faker.helpers.arrayElement(['AAPL', 'GOOGL', 'MSFT', 'AMZN']),
      price,
      volume: faker.number.int({ min: 100000, max: 10000000 }),
      bid: price - spread,
      ask: price + spread,
      timestamp: faker.date.recent(),
      ...overrides,
    };
    
    return MarketDataSchema.parse(marketData);
  }
}