# Strategy Development Guide

This guide provides instructions for developing Python-based trading strategies for the AI Enhanced Trading Platform.

## Overview

Trading strategies in this platform follow a simple, consistent format that makes them easy to develop, test, and deploy. Each strategy is a Python module that implements a standardized interface for analyzing market data and making trading decisions.

## Strategy Structure

A typical strategy consists of the following components:

1. **Initialization** - Setup parameters, indicators, and state variables
2. **Data Analysis** - Calculate technical indicators and identify patterns
3. **Signal Generation** - Determine trading signals based on analysis
4. **Trade Execution** - Execute trades through the MT5 bridge
5. **Monitoring** - Track open positions and adjust as needed

## Basic Strategy Template

```python
"""
Strategy Name and Description
"""

import numpy as np
import pandas as pd
import logging
from app.mt5_bridge.mt5_client import MT5Client

# Configure logging
logger = logging.getLogger(__name__)

class StrategyName:
    """
    Strategy description
    """
    
    def __init__(self, client, symbol, timeframe, param1=default1, param2=default2):
        """
        Initialize the strategy
        """
        self.client = client
        self.symbol = symbol
        self.timeframe = timeframe
        self.param1 = param1
        self.param2 = param2
        
        # Get symbol info
        symbol_info = self.client.get_symbol_info(symbol)
        self.point = symbol_info.get("point", 0.00001)
        
        # State variables
        self.last_signal = None
    
    def calculate_indicators(self, data):
        """
        Calculate technical indicators
        """
        # Add your indicator calculations here
        return data
    
    def check_for_signals(self, data):
        """
        Check for trading signals
        """
        # Add your signal generation logic here
        return signal  # 'buy', 'sell', or None
    
    def get_historical_data(self):
        """
        Get historical data for analysis
        """
        # Get data from MT5
        bars = self.client.get_historical_data(
            symbol=self.symbol,
            timeframe=self.timeframe,
            count=100  # Adjust as needed
        )
        
        # Convert to DataFrame
        df = pd.DataFrame(bars)
        
        # Calculate indicators
        df = self.calculate_indicators(df)
        
        return df
    
    def execute_trade(self, signal):
        """
        Execute a trade based on the signal
        """
        # Add your trade execution logic here
        pass
    
    def run(self, once=False):
        """
        Run the strategy
        """
        logger.info(f"Starting strategy for {self.symbol}")
        
        while True:
            try:
                # Get historical data
                data = self.get_historical_data()
                
                # Check for signals
                signal = self.check_for_signals(data)
                
                # If we have a signal
                if signal:
                    self.execute_trade(signal)
                
                # If running just once, exit after checking
                if once:
                    break
                
                # Sleep before next check
                time.sleep(60)  # Adjust as needed
                
            except Exception as e:
                logger.error(f"Error in strategy execution: {str(e)}")
                if once:
                    break
                time.sleep(60)  # Sleep before retrying
        
        logger.info("Strategy execution completed")

# Entry point when run directly
def run_strategy(login, password, server, symbol="EURUSD", timeframe="H1", param1=default1):
    """
    Run the strategy
    """
    client = MT5Client()
    
    if client.connect(login, password, server):
        strategy = StrategyName(client, symbol, timeframe, param1)
        strategy.run()
        client.disconnect()
```

## Best Practices

1. **Clean, Well-Documented Code**:
   - Add docstrings to explain functionality
   - Use meaningful variable names
   - Comment complex logic

2. **Risk Management**:
   - Always implement stop losses
   - Control position sizing
   - Monitor total exposure

3. **Error Handling**:
   - Catch and log exceptions
   - Implement graceful failure modes
   - Don't assume data availability

4. **Performance Optimization**:
   - Minimize repeated calculations
   - Cache results when possible
   - Use vectorized operations (numpy/pandas)

5. **Testing**:
   - Test on historical data before live trading
   - Validate signal generation logic
   - Check edge cases and error handling

## Technical Indicators

The platform provides easy access to calculate common technical indicators:

- Moving Averages (SMA, EMA, WMA)
- Oscillators (RSI, Stochastic, MACD)
- Volatility measures (Bollinger Bands, ATR)
- Trend indicators (ADX, Ichimoku)
- Volume indicators (OBV, Money Flow)

Example for calculating RSI:

```python
def calculate_rsi(data, period=14):
    delta = data['close'].diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    
    avg_gain = gain.rolling(window=period).mean()
    avg_loss = loss.rolling(window=period).mean()
    
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    
    data['rsi'] = rsi
    return data
```

## Advanced Strategies

As you gain experience, consider incorporating more sophisticated elements:

1. **Multi-timeframe analysis**
2. **Machine learning models**
3. **Sentiment analysis**
4. **Correlation between markets**
5. **Portfolio-level risk management**

## Deploying Your Strategy

Once your strategy is ready:

1. Save it as a `.py` file
2. Upload it via the MT5 Bridge API
3. Execute it on your MT5 account
4. Monitor its performance

## Testing Your Strategy

Before deploying a strategy to live trading:

1. Use the backtesting module to test on historical data
2. Analyze performance metrics (profit factor, drawdown, win rate)
3. Run in simulation mode first
4. Start with small position sizes when going live
