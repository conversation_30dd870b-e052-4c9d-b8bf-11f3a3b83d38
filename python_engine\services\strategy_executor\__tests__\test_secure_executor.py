import pytest
import numpy as np
from unittest.mock import Mock, patch
import sys
import os

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

from services.strategy_executor.secure_executor import SecureStrategyExecutor, SecurityError


class TestSecureStrategyExecutor:
    def setup_method(self):
        self.executor = SecureStrategyExecutor()
        self.safe_strategy = """
def trading_strategy(data, params):
    sma = calculate_sma(data['close'], params['sma_period'])
    if len(sma) == 0:
        return {'signal': 'hold'}
    return {'signal': 'buy' if data['close'][-1] > sma[-1] else 'sell'}
"""
        self.malicious_strategy = """
import os
os.system('rm -rf /')
def trading_strategy(data, params):
    return {'signal': 'buy'}
"""

    def test_executes_safe_strategy_successfully(self):
        """Test that valid strategy code executes correctly"""
        # Arrange
        data = {'close': [100, 102, 104, 103, 105]}
        params = {'sma_period': 3}
        
        # Act
        result = self.executor.execute(self.safe_strategy, data, params)
        
        # Assert
        assert result['signal'] in ['buy', 'sell', 'hold']
        assert 'error' not in result

    def test_blocks_malicious_code_execution(self):
        """Test that malicious code is blocked"""
        # Arrange
        data = {'close': [100, 102, 104]}
        params = {}
        
        # Act & Assert
        with pytest.raises(SecurityError) as exc_info:
            self.executor.execute(self.malicious_strategy, data, params)
        
        assert "not allowed" in str(exc_info.value) or "Restricted code" in str(exc_info.value)

    def test_validates_strategy_function_exists(self):
        """Test that strategy must define trading_strategy function"""
        # Arrange
        invalid_strategy = "x = 1"
        data = {'close': [100, 102, 104]}
        params = {}
        
        # Act & Assert
        with pytest.raises(ValueError) as exc_info:
            self.executor.execute(invalid_strategy, data, params)
        
        assert "must define 'trading_strategy'" in str(exc_info.value)

    def test_blocks_file_system_access(self):
        """Test that file system access is blocked"""
        # Arrange
        file_access_strategy = """
def trading_strategy(data, params):
    with open('/etc/passwd', 'r') as f:
        content = f.read()
    return {'signal': 'buy'}
"""
        data = {'close': [100, 102, 104]}
        params = {}
        
        # Act & Assert
        with pytest.raises(SecurityError):
            self.executor.execute(file_access_strategy, data, params)

    def test_blocks_network_access(self):
        """Test that network access is blocked"""
        # Arrange
        network_strategy = """
import urllib.request
def trading_strategy(data, params):
    urllib.request.urlopen('http://malicious.com')
    return {'signal': 'buy'}
"""
        data = {'close': [100, 102, 104]}
        params = {}
        
        # Act & Assert
        with pytest.raises(SecurityError):
            self.executor.execute(network_strategy, data, params)

    def test_allows_safe_math_operations(self):
        """Test that safe mathematical operations are allowed"""
        # Arrange
        math_strategy = """
def trading_strategy(data, params):
    import math
    prices = data['close']
    avg = sum(prices) / len(prices)
    std = math.sqrt(sum((x - avg) ** 2 for x in prices) / len(prices))
    return {'signal': 'buy' if prices[-1] > avg + std else 'sell'}
"""
        data = {'close': [100, 102, 104, 103, 105]}
        params = {}
        
        # Act
        result = self.executor.execute(math_strategy, data, params)
        
        # Assert
        assert result['signal'] in ['buy', 'sell']

    def test_provides_technical_indicators(self):
        """Test that technical indicators are available"""
        # Arrange
        indicator_strategy = """
def trading_strategy(data, params):
    sma = calculate_sma(data['close'], 5)
    rsi = calculate_rsi(data['close'], 14)
    macd = calculate_macd(data['close'])
    bollinger = calculate_bollinger(data['close'], 20, 2)
    
    return {
        'signal': 'buy',
        'indicators': {
            'sma': len(sma),
            'rsi': len(rsi) if rsi else 0,
            'macd': len(macd) if macd else 0,
            'bollinger': len(bollinger) if bollinger else 0
        }
    }
"""
        data = {'close': [100 + i for i in range(50)]}  # 50 data points
        params = {}
        
        # Act
        result = self.executor.execute(indicator_strategy, data, params)
        
        # Assert
        assert result['signal'] == 'buy'
        assert 'indicators' in result
        assert result['indicators']['sma'] > 0

    def test_handles_execution_timeout(self):
        """Test that long-running strategies are terminated"""
        # Arrange
        infinite_loop_strategy = """
def trading_strategy(data, params):
    while True:
        pass
    return {'signal': 'buy'}
"""
        data = {'close': [100, 102, 104]}
        params = {}
        
        # Act & Assert
        with pytest.raises(SecurityError) as exc_info:
            self.executor.execute(infinite_loop_strategy, data, params, timeout=1)
        
        assert "execution timeout" in str(exc_info.value).lower()

    def test_limits_memory_usage(self):
        """Test that memory usage is limited"""
        import platform
        
        # Skip on Windows as memory limiting is not fully supported
        if platform.system() == 'Windows':
            pytest.skip("Memory limiting not fully supported on Windows")
        
        # Arrange
        memory_hog_strategy = """
def trading_strategy(data, params):
    # Try to allocate large amounts of memory
    big_list = [0] * (10**8)  # 100 million integers
    return {'signal': 'buy'}
"""
        data = {'close': [100, 102, 104]}
        params = {}
        
        # Act & Assert
        with pytest.raises(SecurityError):
            self.executor.execute(memory_hog_strategy, data, params)

    def test_validates_input_data_types(self):
        """Test that input data is validated"""
        # Arrange
        strategy = """
def trading_strategy(data, params):
    return {'signal': 'buy'}
"""
        
        # Act & Assert - Invalid data type
        with pytest.raises(ValueError) as exc_info:
            self.executor.execute(strategy, "invalid_data", {})
        
        assert "data must be a dictionary" in str(exc_info.value)

    def test_validates_params_data_types(self):
        """Test that parameters are validated"""
        # Arrange
        strategy = """
def trading_strategy(data, params):
    return {'signal': 'buy'}
"""
        
        # Act & Assert - Invalid params type
        with pytest.raises(ValueError) as exc_info:
            self.executor.execute(strategy, {}, "invalid_params")
        
        assert "params must be a dictionary" in str(exc_info.value)

    def test_returns_execution_metadata(self):
        """Test that execution metadata is returned"""
        # Arrange
        strategy = """
def trading_strategy(data, params):
    return {'signal': 'buy', 'confidence': 0.8}
"""
        data = {'close': [100, 102, 104]}
        params = {'test': True}
        
        # Act
        result = self.executor.execute(strategy, data, params)
        
        # Assert
        assert 'signal' in result
        assert 'confidence' in result
        assert result['signal'] == 'buy'
        assert result['confidence'] == 0.8