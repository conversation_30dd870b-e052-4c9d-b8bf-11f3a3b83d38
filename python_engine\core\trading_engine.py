"""
Trading Engine with Dependency Injection

Modern trading engine implementation using dependency injection
for improved testability and maintainability.
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from core.interfaces import (
    IMarketDataService, IStrategyService, ITradingService, 
    IRiskManagementService, IPortfolioService, INotificationService,
    IDataStorageService, ILoggingService, IConfigurationService,
    TradingSignal, Order, Position, MarketData
)

@dataclass
class TradingEngineConfig:
    """Trading engine configuration"""
    auto_trading_enabled: bool = False
    max_concurrent_strategies: int = 5
    signal_processing_interval: int = 1  # seconds
    risk_check_enabled: bool = True
    notification_enabled: bool = True

class TradingEngine:
    """
    Modern trading engine with dependency injection
    
    Before (Hard to test):
        def __init__(self):
            self.market_data = MarketDataService()  # Hard-coded dependency
    
    After (Easy to test):
        def __init__(self, market_data_service: IMarketDataService):
            self.market_data = market_data_service  # Injected dependency
    """
    
    def __init__(self,
                 market_data_service: IMarketDataService,
                 strategy_service: IStrategyService,
                 trading_service: ITradingService,
                 risk_management_service: IRiskManagementService,
                 portfolio_service: IPortfolioService,
                 notification_service: INotificationService,
                 data_storage_service: IDataStorageService,
                 logging_service: ILoggingService,
                 config_service: IConfigurationService):
        
        # Injected dependencies
        self.market_data = market_data_service
        self.strategy = strategy_service
        self.trading = trading_service
        self.risk_management = risk_management_service
        self.portfolio = portfolio_service
        self.notifications = notification_service
        self.data_storage = data_storage_service
        self.logger = logging_service
        self.config = config_service
        
        # Engine state
        self._running = False
        self._active_strategies: Dict[str, Dict[str, Any]] = {}
        self._signal_queue: asyncio.Queue = asyncio.Queue()
        self._engine_config = self._load_engine_config()
        
        self.logger.log_info("Trading engine initialized with dependency injection")
    
    def _load_engine_config(self) -> TradingEngineConfig:
        """Load engine configuration"""
        return TradingEngineConfig(
            auto_trading_enabled=self.config.get_config('engine.auto_trading_enabled', False),
            max_concurrent_strategies=self.config.get_config('engine.max_concurrent_strategies', 5),
            signal_processing_interval=self.config.get_config('engine.signal_processing_interval', 1),
            risk_check_enabled=self.config.get_config('engine.risk_check_enabled', True),
            notification_enabled=self.config.get_config('engine.notification_enabled', True)
        )
    
    async def start(self):
        """Start the trading engine"""
        if self._running:
            self.logger.log_warning("Trading engine is already running")
            return
        
        self._running = True
        self.logger.log_info("Starting trading engine")
        
        # Start background tasks
        tasks = [
            asyncio.create_task(self._signal_processing_loop()),
            asyncio.create_task(self._portfolio_monitoring_loop()),
            asyncio.create_task(self._risk_monitoring_loop())
        ]
        
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            self.logger.log_error(f"Error in trading engine: {e}")
            await self.stop()
    
    async def stop(self):
        """Stop the trading engine"""
        if not self._running:
            return
        
        self._running = False
        self.logger.log_info("Stopping trading engine")
        
        # Cancel all active strategies
        for strategy_name in list(self._active_strategies.keys()):
            await self.remove_strategy(strategy_name)
        
        # Send shutdown notification
        if self._engine_config.notification_enabled:
            await self.notifications.send_alert("Trading engine stopped", "info")
    
    async def add_strategy(self, strategy_name: str, strategy_code: str, symbols: List[str], params: Dict[str, Any] = None):
        """Add a trading strategy"""
        try:
            self.logger.log_info(f"Adding strategy: {strategy_name}")
            
            # Validate strategy
            validation_result = await self.strategy.validate_strategy(strategy_code)
            if not validation_result.get('is_valid', False):
                raise ValueError(f"Strategy validation failed: {validation_result.get('errors', [])}")
            
            # Store strategy configuration
            self._active_strategies[strategy_name] = {
                'code': strategy_code,
                'symbols': symbols,
                'params': params or {},
                'active': True,
                'last_execution': None,
                'performance': {'signals_generated': 0, 'trades_executed': 0}
            }
            
            # Subscribe to market data for strategy symbols
            for symbol in symbols:
                await self.market_data.subscribe_to_updates(symbol, 
                    lambda sym, price: self._on_market_data_update(sym, price, strategy_name))
            
            self.logger.log_info(f"Strategy {strategy_name} added successfully")
            
            if self._engine_config.notification_enabled:
                await self.notifications.send_alert(f"Strategy {strategy_name} added", "info")
                
        except Exception as e:
            self.logger.log_error(f"Error adding strategy {strategy_name}: {e}")
            raise
    
    async def remove_strategy(self, strategy_name: str):
        """Remove a trading strategy"""
        if strategy_name not in self._active_strategies:
            self.logger.log_warning(f"Strategy {strategy_name} not found")
            return
        
        try:
            strategy_config = self._active_strategies[strategy_name]
            
            # Unsubscribe from market data
            for symbol in strategy_config['symbols']:
                await self.market_data.unsubscribe_from_updates(symbol)
            
            # Remove strategy
            del self._active_strategies[strategy_name]
            
            self.logger.log_info(f"Strategy {strategy_name} removed")
            
            if self._engine_config.notification_enabled:
                await self.notifications.send_alert(f"Strategy {strategy_name} removed", "info")
                
        except Exception as e:
            self.logger.log_error(f"Error removing strategy {strategy_name}: {e}")
    
    async def _on_market_data_update(self, symbol: str, price: float, strategy_name: str):
        """Handle market data updates"""
        try:
            if strategy_name not in self._active_strategies:
                return
            
            strategy_config = self._active_strategies[strategy_name]
            if not strategy_config['active']:
                return
            
            # Get historical data for strategy execution
            historical_data = await self.market_data.get_historical_data(symbol, "1d", "1m")
            
            # Convert to strategy format
            market_data_dict = {
                'close': [data.close for data in historical_data],
                'high': [data.high for data in historical_data],
                'low': [data.low for data in historical_data],
                'volume': [data.volume for data in historical_data]
            }
            
            # Execute strategy
            signal = await self.strategy.execute_strategy(
                strategy_config['code'],
                market_data_dict,
                strategy_config['params']
            )
            
            # Update strategy performance
            strategy_config['performance']['signals_generated'] += 1
            strategy_config['last_execution'] = datetime.now()
            
            # Queue signal for processing
            await self._signal_queue.put((strategy_name, signal))
            
        except Exception as e:
            self.logger.log_error(f"Error processing market data update for {strategy_name}: {e}")
    
    async def _signal_processing_loop(self):
        """Process trading signals"""
        while self._running:
            try:
                # Wait for signals with timeout
                try:
                    strategy_name, signal = await asyncio.wait_for(
                        self._signal_queue.get(),
                        timeout=self._engine_config.signal_processing_interval
                    )
                except asyncio.TimeoutError:
                    continue
                
                await self._process_signal(strategy_name, signal)
                
            except Exception as e:
                self.logger.log_error(f"Error in signal processing loop: {e}")
                await asyncio.sleep(1)
    
    async def _process_signal(self, strategy_name: str, signal: TradingSignal):
        """Process a trading signal"""
        try:
            self.logger.log_info(f"Processing signal from {strategy_name}: {signal.signal} {signal.symbol}")
            
            # Store signal
            await self.data_storage.store_trading_signal(signal)
            
            # Skip if not auto-trading
            if not self._engine_config.auto_trading_enabled:
                self.logger.log_info("Auto-trading disabled, signal logged only")
                return
            
            # Skip hold signals
            if signal.signal == 'hold':
                return
            
            # Get current positions and account balance
            positions = await self.trading.get_positions()
            account_balance = await self.trading.get_account_balance()
            
            # Calculate position size
            position_size = await self.risk_management.calculate_position_size(
                signal.symbol, signal, account_balance['cash']
            )
            
            if position_size <= 0:
                self.logger.log_info(f"Position size calculation returned {position_size}, skipping trade")
                return
            
            # Create order
            current_price = await self.market_data.get_current_price(signal.symbol)
            
            order = Order(
                id=f"{strategy_name}_{signal.symbol}_{datetime.now().timestamp()}",
                symbol=signal.symbol,
                side=signal.signal,
                quantity=position_size,
                price=current_price,
                order_type='market',
                status='pending',
                timestamp=datetime.now()
            )
            
            # Risk check
            if self._engine_config.risk_check_enabled:
                risk_approved = await self.risk_management.check_risk_limits(order, positions)
                if not risk_approved:
                    self.logger.log_warning(f"Risk check failed for order: {order.id}")
                    return
            
            # Place order
            order_id = await self.trading.place_order(order)
            order.id = order_id
            order.status = 'submitted'
            
            # Log trade
            self.logger.log_trade(order)
            
            # Store order
            await self.data_storage.store_order(order)
            
            # Update strategy performance
            if strategy_name in self._active_strategies:
                self._active_strategies[strategy_name]['performance']['trades_executed'] += 1
            
            # Send notification
            if self._engine_config.notification_enabled:
                await self.notifications.send_trade_notification(order)
            
            self.logger.log_info(f"Order placed successfully: {order_id}")
            
        except Exception as e:
            self.logger.log_error(f"Error processing signal from {strategy_name}: {e}")
    
    async def _portfolio_monitoring_loop(self):
        """Monitor portfolio performance"""
        while self._running:
            try:
                # Get portfolio metrics
                portfolio_value = await self.portfolio.get_portfolio_value()
                portfolio_allocation = await self.portfolio.get_portfolio_allocation()
                
                # Log portfolio status
                self.logger.log_info(f"Portfolio value: ${portfolio_value:.2f}")
                
                # Check for rebalancing needs
                # (Implementation would depend on specific rebalancing strategy)
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                self.logger.log_error(f"Error in portfolio monitoring: {e}")
                await asyncio.sleep(60)
    
    async def _risk_monitoring_loop(self):
        """Monitor risk metrics"""
        while self._running:
            try:
                # Get current positions
                positions = await self.trading.get_positions()
                
                # Calculate portfolio risk
                risk_metrics = await self.risk_management.calculate_portfolio_risk(positions)
                
                # Check risk thresholds
                max_portfolio_risk = self.config.get_config('risk_management.max_portfolio_risk', 0.20)
                current_risk = risk_metrics.get('total_risk', 0)
                
                if current_risk > max_portfolio_risk:
                    await self.notifications.send_alert(
                        f"Portfolio risk ({current_risk:.2%}) exceeds threshold ({max_portfolio_risk:.2%})",
                        "warning"
                    )
                
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                self.logger.log_error(f"Error in risk monitoring: {e}")
                await asyncio.sleep(300)
    
    def get_engine_status(self) -> Dict[str, Any]:
        """Get engine status"""
        return {
            'running': self._running,
            'active_strategies': len(self._active_strategies),
            'strategies': {
                name: {
                    'symbols': config['symbols'],
                    'active': config['active'],
                    'last_execution': config['last_execution'].isoformat() if config['last_execution'] else None,
                    'performance': config['performance']
                }
                for name, config in self._active_strategies.items()
            },
            'config': {
                'auto_trading_enabled': self._engine_config.auto_trading_enabled,
                'max_concurrent_strategies': self._engine_config.max_concurrent_strategies,
                'risk_check_enabled': self._engine_config.risk_check_enabled
            }
        }
    
    async def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        total_signals = sum(
            config['performance']['signals_generated'] 
            for config in self._active_strategies.values()
        )
        
        total_trades = sum(
            config['performance']['trades_executed'] 
            for config in self._active_strategies.values()
        )
        
        portfolio_value = await self.portfolio.get_portfolio_value()
        
        return {
            'total_signals_generated': total_signals,
            'total_trades_executed': total_trades,
            'signal_to_trade_ratio': total_trades / total_signals if total_signals > 0 else 0,
            'portfolio_value': portfolio_value,
            'active_strategies': len(self._active_strategies)
        }