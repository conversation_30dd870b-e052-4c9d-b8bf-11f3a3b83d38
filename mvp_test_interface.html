<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Trading Platform - MVP Test Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .status {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        
        .status-item {
            display: inline-block;
            margin-right: 30px;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
        }
        
        .status-online {
            background: #d4edda;
            color: #155724;
        }
        
        .status-offline {
            background: #f8d7da;
            color: #721c24;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }
        
        .section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            border: 1px solid #e9ecef;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #3498db;
        }
        
        .btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }
        
        .results {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .results-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .results-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .trades-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .trade-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 10px;
        }
        
        .trade-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .trade-symbol {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .trade-type {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .trade-buy {
            background: #d4edda;
            color: #155724;
        }
        
        .trade-sell {
            background: #f8d7da;
            color: #721c24;
        }
        
        .trade-details {
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #6c757d;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 AI Trading Platform</h1>
            <p>MVP Test Interface - Live Trading Simulation</p>
        </div>
        
        <div class="status">
            <div id="api-status" class="status-item status-offline">API: Checking...</div>
            <div id="mt5-status" class="status-item status-offline">MT5: Offline Mode</div>
            <div id="trades-count" class="status-item status-online">Trades: 0</div>
        </div>
        
        <div class="main-content">
            <!-- Trading Section -->
            <div class="section">
                <h2>📈 Place Trade</h2>
                <form id="trade-form">
                    <div class="form-group">
                        <label for="symbol">Trading Symbol</label>
                        <select id="symbol" required>
                            <option value="EURUSD">EUR/USD</option>
                            <option value="GBPUSD">GBP/USD</option>
                            <option value="USDJPY">USD/JPY</option>
                            <option value="AUDUSD">AUD/USD</option>
                            <option value="USDCAD">USD/CAD</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="lot">Lot Size</label>
                        <input type="number" id="lot" step="0.01" min="0.01" max="10" value="0.1" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="order_type">Order Type</label>
                        <select id="order_type" required>
                            <option value="BUY">BUY (Long)</option>
                            <option value="SELL">SELL (Short)</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn">Execute Trade</button>
                </form>
                
                <div id="trade-result" class="results" style="display: none;"></div>
            </div>
            
            <!-- Portfolio Section -->
            <div class="section">
                <h2>💼 Portfolio & Trades</h2>
                <button id="refresh-trades" class="btn btn-success">Refresh Trades</button>
                <button id="clear-trades" class="btn btn-danger" style="margin-top: 10px;">Clear All Trades</button>
                
                <div id="trades-container" class="trades-list">
                    <div class="loading">No trades yet. Place your first trade!</div>
                </div>
            </div>
        </div>
        
        <!-- API Testing Section -->
        <div style="padding: 30px;">
            <div class="section">
                <h2>🔧 API Testing</h2>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <button onclick="testHealth()" class="btn">Test Health</button>
                    <button onclick="testStrategies()" class="btn">Get Strategies</button>
                    <button onclick="testBacktests()" class="btn">Get Backtests</button>
                    <button onclick="testTrades()" class="btn">Get Trades</button>
                </div>
                
                <div id="api-result" class="results" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        let tradesCount = 0;
        
        // Check API status on load
        window.addEventListener('load', function() {
            checkApiStatus();
            loadTrades();
        });
        
        // Check API status
        async function checkApiStatus() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                if (response.ok) {
                    document.getElementById('api-status').textContent = 'API: Online';
                    document.getElementById('api-status').className = 'status-item status-online';
                } else {
                    throw new Error('API not responding');
                }
            } catch (error) {
                document.getElementById('api-status').textContent = 'API: Offline';
                document.getElementById('api-status').className = 'status-item status-offline';
            }
        }
        
        // Handle trade form submission
        document.getElementById('trade-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const symbol = document.getElementById('symbol').value;
            const lot = parseFloat(document.getElementById('lot').value);
            const order_type = document.getElementById('order_type').value;
            
            const resultDiv = document.getElementById('trade-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Executing trade...';
            resultDiv.className = 'results';
            
            try {
                const response = await fetch(`${API_BASE}/api/mvp/trade`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ symbol, lot, order_type })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.textContent = `✅ Trade Executed Successfully!\n\nTicket: #${data.ticket}\nSymbol: ${data.symbol}\nType: ${data.order_type}\nLot Size: ${data.lot}\nPrice: ${data.open_price}\nTime: ${new Date(data.time).toLocaleString()}`;
                    resultDiv.className = 'results results-success';
                    
                    // Update trades count
                    tradesCount++;
                    document.getElementById('trades-count').textContent = `Trades: ${tradesCount}`;
                    
                    // Refresh trades list
                    setTimeout(loadTrades, 500);
                } else {
                    throw new Error(data.detail || 'Trade execution failed');
                }
            } catch (error) {
                resultDiv.textContent = `❌ Trade Failed!\n\nError: ${error.message}`;
                resultDiv.className = 'results results-error';
            }
        });
        
        // Load trades
        async function loadTrades() {
            try {
                const response = await fetch(`${API_BASE}/api/mvp/trades`);
                const trades = await response.json();
                
                const container = document.getElementById('trades-container');
                
                if (trades.length === 0) {
                    container.innerHTML = '<div class="loading">No trades yet. Place your first trade!</div>';
                    return;
                }
                
                container.innerHTML = trades.map(trade => `
                    <div class="trade-item">
                        <div class="trade-header">
                            <span class="trade-symbol">${trade.symbol}</span>
                            <span class="trade-type trade-${trade.order_type.toLowerCase()}">${trade.order_type}</span>
                        </div>
                        <div class="trade-details">
                            Ticket: #${trade.ticket} | Lot: ${trade.lot} | Price: ${trade.open_price}<br>
                            Time: ${new Date(trade.time).toLocaleString()}
                        </div>
                    </div>
                `).join('');
                
                tradesCount = trades.length;
                document.getElementById('trades-count').textContent = `Trades: ${tradesCount}`;
                
            } catch (error) {
                console.error('Failed to load trades:', error);
            }
        }
        
        // Refresh trades button
        document.getElementById('refresh-trades').addEventListener('click', loadTrades);
        
        // Clear trades button (this would need a backend endpoint in real implementation)
        document.getElementById('clear-trades').addEventListener('click', function() {
            if (confirm('Are you sure you want to clear all trades? This will restart the server session.')) {
                // In a real implementation, this would call an API endpoint
                // For now, we'll just refresh the page
                location.reload();
            }
        });
        
        // API Testing Functions
        async function testHealth() {
            await testApiEndpoint('/health', 'GET');
        }
        
        async function testStrategies() {
            await testApiEndpoint('/api/strategies', 'GET');
        }
        
        async function testBacktests() {
            await testApiEndpoint('/api/backtests', 'GET');
        }
        
        async function testTrades() {
            await testApiEndpoint('/api/mvp/trades', 'GET');
        }
        
        async function testApiEndpoint(endpoint, method = 'GET') {
            const resultDiv = document.getElementById('api-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = `Testing ${method} ${endpoint}...`;
            resultDiv.className = 'results';
            
            try {
                const response = await fetch(`${API_BASE}${endpoint}`, { method });
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.textContent = `✅ ${method} ${endpoint}\n\nStatus: ${response.status}\nResponse:\n${JSON.stringify(data, null, 2)}`;
                    resultDiv.className = 'results results-success';
                } else {
                    throw new Error(`HTTP ${response.status}: ${data.detail || 'Request failed'}`);
                }
            } catch (error) {
                resultDiv.textContent = `❌ ${method} ${endpoint}\n\nError: ${error.message}`;
                resultDiv.className = 'results results-error';
            }
        }
        
        // Auto-refresh API status every 30 seconds
        setInterval(checkApiStatus, 30000);
    </script>
</body>
</html>