"""
Simple FastAPI server with Ollama integration for testing
"""

import sys
import os
import logging
import asyncio
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ollama_server")

# Add paths for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '../backend'))

# Create FastAPI app
app = FastAPI(
    title="AI Enhanced Trading Platform - Ollama Integration",
    description="Simple API with Ollama chatbot integration",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For development - restrict in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Import Ollama components
try:
    from backend.app.chatbot.ollama_client import OllamaClient
    from backend.app.chatbot.enhanced_strategy_chatbot import EnhancedStrategyChatbot
    from backend.app.mt5_bridge.mt5_strategy_api import router as mt5_router
    logger.info("Successfully imported Ollama and MT5 components")
except ImportError as e:
    logger.warning(f"Could not import components: {e}")
    OllamaClient = None
    EnhancedStrategyChatbot = None
    mt5_router = None

# Include MT5 strategy API if available
if mt5_router:
    app.include_router(mt5_router, prefix="/api/mt5", tags=["MT5 Strategy"])
    logger.info("MT5 strategy router included successfully")

# Global chatbot instance
chatbot = None

# Request/Response models
class ChatRequest(BaseModel):
    message: str = Field(..., description="User message for strategy generation")
    conversation_id: str = Field(default="default", description="Conversation ID")
    use_llm: bool = Field(default=True, description="Whether to use LLM")

class ChatResponse(BaseModel):
    response: str
    success: bool
    template_data: Optional[Dict[str, Any]] = None
    is_template_fallback: bool = False
    error: Optional[str] = None

class StatusResponse(BaseModel):
    available: bool
    models: List[str] = []
    message: str
    error: Optional[str] = None

# Startup event
@app.on_event("startup")
async def startup_event():
    """Initialize chatbot on startup"""
    global chatbot
    if EnhancedStrategyChatbot:
        try:
            chatbot = EnhancedStrategyChatbot()
            status = await chatbot.initialize()
            logger.info(f"Chatbot initialized: {status}")
        except Exception as e:
            logger.error(f"Failed to initialize chatbot: {e}")

# Root endpoint
@app.get("/")
async def root():
    return {
        "message": "AI Enhanced Trading Platform - Ollama Integration",
        "version": "1.0.0",
        "docs": "/docs",
        "ollama_available": chatbot is not None
    }

# Health check
@app.get("/health")
async def health_check():
    return {"status": "ok", "ollama_available": chatbot is not None}

# Ollama status endpoint
@app.get("/api/ollama/status", response_model=StatusResponse)
async def get_ollama_status():
    """Get Ollama status"""
    try:
        if not chatbot:
            return StatusResponse(
                available=False,
                message="Chatbot not initialized",
                error="Chatbot instance not available"
            )
            
        health_status = await chatbot.get_health_status()
        
        return StatusResponse(
            available=health_status.get('available', False),
            models=health_status.get('models', []),
            message=health_status.get('message', 'Status unknown'),
            error=health_status.get('error')
        )
        
    except Exception as e:
        logger.error(f"Status check failed: {e}")
        return StatusResponse(
            available=False,
            message="Status check failed",
            error=str(e)
        )

# Chat endpoint
@app.post("/api/ollama/chat", response_model=ChatResponse)
async def chat_completion(request: ChatRequest):
    """Chat completion with Ollama"""
    try:
        if not chatbot:
            # Fallback response when chatbot not available
            return ChatResponse(
                response="Sorry, the AI chatbot is not available. Please try the template-based responses instead.",
                success=False,
                is_template_fallback=True,
                error="Chatbot not initialized"
            )
            
        # Process the message
        result = await chatbot.process_message(
            user_message=request.message,
            conversation_id=request.conversation_id,
            use_llm=request.use_llm
        )
        
        return ChatResponse(
            response=result.get('response', 'No response generated'),
            success=result.get('success', False),
            template_data=result.get('template_data'),
            is_template_fallback=result.get('is_template_fallback', False),
            error=result.get('error')
        )
        
    except Exception as e:
        logger.error(f"Chat completion failed: {e}")
        return ChatResponse(
            response=f"Sorry, I encountered an error: {str(e)}",
            success=False,
            error=str(e)
        )

# Strategy templates endpoint (fallback)
@app.post("/api/strategy-templates")
async def strategy_templates(request: dict):
    """Fallback strategy templates endpoint"""
    try:
        query = request.get('query', '')
        
        # Simple template response
        response_text = f"""Based on your request: "{query}"

I've generated a basic trading strategy template. Here's what I recommend:

**Strategy Overview:**
- This is a template-based response since the AI chatbot may not be available
- The strategy focuses on risk management and proper entry/exit conditions
- Includes basic technical indicators for signal generation

**Key Features:**
- 2% risk per trade (adjustable)
- Proper stop loss and take profit levels
- Entry and exit signal logic
- Basic error handling

**Next Steps:**
1. Customize the parameters for your needs
2. Test the strategy on historical data
3. Validate with paper trading before going live

For more advanced AI-generated strategies, please ensure the Ollama service is running."""
        
        return {
            "response": response_text,
            "template_data": {
                "strategy_name": "Basic Template Strategy",
                "category": "template",
                "code": """
class BasicTradingStrategy:
    def __init__(self, symbol="EURUSD", risk_percent=2.0):
        self.symbol = symbol
        self.risk_percent = risk_percent / 100
        
    def generate_signals(self, data):
        # Template signal generation logic
        buy_signal = False
        sell_signal = False
        return buy_signal, sell_signal
        
    def calculate_position_size(self, account_balance, stop_loss_pips):
        risk_amount = account_balance * self.risk_percent
        pip_value = 10
        position_size = risk_amount / (stop_loss_pips * pip_value)
        return position_size
""",
                "description": "Basic template strategy",
                "parameters": {"risk_percent": 2.0}
            }
        }
        
    except Exception as e:
        logger.error(f"Template endpoint error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    
    # Check if port 8002 is available, otherwise use 8003
    import socket
    def is_port_available(port):
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            try:
                s.bind(('', port))
                return True
            except OSError:
                return False
    
    port = 8002 if is_port_available(8002) else 8003
    logger.info(f"Starting server on port {port}")
    
    uvicorn.run(app, host="0.0.0.0", port=port)
