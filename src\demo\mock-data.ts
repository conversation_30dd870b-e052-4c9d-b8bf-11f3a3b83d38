// src/demo/mock-data.ts
export const mockForexData: Record<string, { bid: number; ask: number; spread: number }> = {
  'EUR/USD': { bid: 1.0856, ask: 1.0858, spread: 2 },
  'GBP/USD': { bid: 1.2634, ask: 1.2636, spread: 2 },
  'USD/JPY': { bid: 149.85, ask: 149.87, spread: 2 },
  'USD/CHF': { bid: 0.8812, ask: 0.8814, spread: 2 },
  'AUD/USD': { bid: 0.6523, ask: 0.6525, spread: 2 },
  'USD/CAD': { bid: 1.3698, ask: 1.3700, spread: 2 },
  'NZD/USD': { bid: 0.5934, ask: 0.5936, spread: 2 },
  'EUR/GBP': { bid: 0.8593, ask: 0.8595, spread: 2 },
  'EUR/JPY': { bid: 162.74, ask: 162.77, spread: 3 },
  'GBP/JPY': { bid: 189.42, ask: 189.45, spread: 3 }
};

export const mockCryptoData: Record<string, number> = {
  'BTC/USD': 45250.00,
  'ETH/USD': 2380.50,
  'XRP/USD': 0.6234
};