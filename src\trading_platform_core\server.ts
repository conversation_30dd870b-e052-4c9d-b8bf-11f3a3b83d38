// src/platform/server.ts
// Complete Trading Platform Server Entry Point
import { CompleteTradingPlatform } from './complete-trading-platform';

async function startPlatform() {
  const platform = new CompleteTradingPlatform({
    dataPath: './platform-data',
    port: 3001,
    enableCORS: true
  });

  await platform.initialize();
  await platform.start(3001);
}

// Handle graceful shutdown
process.on('SIGTERM', () => {
  console.log('Received SIGTERM, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('Received SIGINT, shutting down gracefully...');
  process.exit(0);
});

startPlatform().catch((error) => {
  console.error('Failed to start platform:', error);
  process.exit(1);
});