import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { BridgeServiceRegistry, BridgeServiceRegistryDependencies } from './bridge-service-registry';
import { createMockLogger } from '@/shared/test-utils';

// Mock all bridge services
jest.mock('./python-engine.service');
jest.mock('./trading-bridge.service');
jest.mock('./backtest-bridge.service');
jest.mock('./chat-bridge.service');

describe('BridgeServiceRegistry', () => {
  let registry: BridgeServiceRegistry;
  let mockDependencies: BridgeServiceRegistryDependencies;

  beforeEach(() => {
    mockDependencies = {
      logger: createMockLogger(),
      config: {
        pythonEngine: {
          baseUrl: 'http://localhost:8000',
          timeout: 30000,
          retryAttempts: 3,
          retryDelay: 1000,
        },
        healthCheckInterval: 1000, // 1 second for testing
        cleanupInterval: 2000, // 2 seconds for testing
      },
    };

    registry = new BridgeServiceRegistry(mockDependencies);
  });

  afterEach(async () => {
    if (registry.isReady()) {
      await registry.shutdown();
    }
    jest.clearAllMocks();
  });

  describe('Initialization', () => {
    it('should initialize all services successfully', async () => {
      await registry.initialize();

      expect(registry.isReady()).toBe(true);
      expect(registry.getPythonEngineService()).toBeDefined();
      expect(registry.getTradingBridgeService()).toBeDefined();
      expect(registry.getBacktestBridgeService()).toBeDefined();
      expect(registry.getChatBridgeService()).toBeDefined();
    });

    it('should emit initialized event on successful initialization', async () => {
      const initListener = jest.fn();
      registry.on('initialized', initListener);

      await registry.initialize();

      expect(initListener).toHaveBeenCalled();
    });

    it('should throw error when trying to initialize twice', async () => {
      await registry.initialize();

      await expect(registry.initialize()).rejects.toThrow(
        'Bridge service registry is already initialized'
      );
    });

    it('should cleanup on initialization failure', async () => {
      // Mock a service that throws during initialization
      jest.doMock('./python-engine.service', () => ({
        PythonEngineService: jest.fn().mockImplementation(() => {
          throw new Error('Initialization failed');
        }),
      }));

      await expect(registry.initialize()).rejects.toThrow();
      expect(registry.isReady()).toBe(false);
    });
  });

  describe('Service Access', () => {
    beforeEach(async () => {
      await registry.initialize();
    });

    it('should provide access to all bridge services', () => {
      expect(registry.getPythonEngineService()).toBeDefined();
      expect(registry.getTradingBridgeService()).toBeDefined();
      expect(registry.getBacktestBridgeService()).toBeDefined();
      expect(registry.getChatBridgeService()).toBeDefined();
    });

    it('should throw error when accessing services before initialization', async () => {
      const uninitializedRegistry = new BridgeServiceRegistry(mockDependencies);

      expect(() => uninitializedRegistry.getPythonEngineService()).toThrow(
        'Bridge service registry is not initialized'
      );
      expect(() => uninitializedRegistry.getTradingBridgeService()).toThrow(
        'Bridge service registry is not initialized'
      );
      expect(() => uninitializedRegistry.getBacktestBridgeService()).toThrow(
        'Bridge service registry is not initialized'
      );
      expect(() => uninitializedRegistry.getChatBridgeService()).toThrow(
        'Bridge service registry is not initialized'
      );
    });
  });

  describe('Health Monitoring', () => {
    beforeEach(async () => {
      await registry.initialize();
    });

    it('should return system health status', async () => {
      // Mock health status
      const mockHealthStatus = {
        healthy: true,
        lastCheck: new Date(),
      };

      jest.spyOn(registry.getPythonEngineService(), 'getHealthStatus')
        .mockReturnValue(mockHealthStatus);

      const health = await registry.getSystemHealth();

      expect(health.healthy).toBe(true);
      expect(health.services.pythonEngine).toEqual(mockHealthStatus);
      expect(health.services.trading).toBe(true);
      expect(health.services.backtest).toBe(true);
      expect(health.services.chat).toBe(true);
      expect(health.timestamp).toBeInstanceOf(Date);
    });

    it('should reflect unhealthy python engine in system health', async () => {
      const mockHealthStatus = {
        healthy: false,
        lastCheck: new Date(),
      };

      jest.spyOn(registry.getPythonEngineService(), 'getHealthStatus')
        .mockReturnValue(mockHealthStatus);

      const health = await registry.getSystemHealth();

      expect(health.healthy).toBe(false);
      expect(health.services.trading).toBe(false);
      expect(health.services.backtest).toBe(false);
      expect(health.services.chat).toBe(false);
    });

    it('should emit health check events', (done) => {
      const healthListener = jest.fn();
      registry.on('health_check_completed', healthListener);

      // Wait for health check to complete
      setTimeout(() => {
        expect(healthListener).toHaveBeenCalled();
        done();
      }, 1500); // Wait longer than health check interval
    }, 10000);
  });

  describe('Service Statistics', () => {
    beforeEach(async () => {
      await registry.initialize();
    });

    it('should return service statistics', () => {
      // Mock service methods
      jest.spyOn(registry.getBacktestBridgeService(), 'getActiveBacktests')
        .mockReturnValue([
          { backtestId: 'test1', status: 'running' },
          { backtestId: 'test2', status: 'pending' },
        ]);

      jest.spyOn(registry.getChatBridgeService(), 'getUserSessions')
        .mockReturnValue([]);

      jest.spyOn(registry.getPythonEngineService(), 'getHealthStatus')
        .mockReturnValue({ healthy: true, lastCheck: new Date() });

      const stats = registry.getServiceStatistics();

      expect(stats.activeBacktests).toBe(2);
      expect(stats.activeChatSessions).toBe(0);
      expect(stats.pythonEngineHealth.healthy).toBe(true);
    });
  });

  describe('Event Forwarding', () => {
    beforeEach(async () => {
      await registry.initialize();
    });

    it('should forward python engine events', () => {
      const healthListener = jest.fn();
      registry.on('python_engine_health_changed', healthListener);

      // Simulate python engine event
      registry.getPythonEngineService().emit('health_check_completed', {
        healthy: true,
        data: { status: 'healthy' },
      });

      expect(healthListener).toHaveBeenCalledWith({
        healthy: true,
        data: { status: 'healthy' },
      });
    });

    it('should forward trading events', () => {
      const orderListener = jest.fn();
      registry.on('order_submitted', orderListener);

      // Simulate trading event
      registry.getTradingBridgeService().emit('order_submitted', {
        orderRequest: { symbol: 'EURUSD' },
        orderResult: { success: true, order_id: 123 },
      });

      expect(orderListener).toHaveBeenCalledWith({
        orderRequest: { symbol: 'EURUSD' },
        orderResult: { success: true, order_id: 123 },
      });
    });

    it('should forward backtest events', () => {
      const backtestListener = jest.fn();
      registry.on('backtest_status_changed', backtestListener);

      // Simulate backtest event
      registry.getBacktestBridgeService().emit('backtest_status_changed', {
        backtestId: 'test123',
        status: 'completed',
        progress: 100,
      });

      expect(backtestListener).toHaveBeenCalledWith({
        backtestId: 'test123',
        status: 'completed',
        progress: 100,
      });
    });

    it('should forward chat events', () => {
      const chatListener = jest.fn();
      registry.on('chat_message_processed', chatListener);

      // Simulate chat event
      registry.getChatBridgeService().emit('message_processed', {
        sessionId: 'session123',
        userMessage: { content: 'Hello' },
        assistantMessage: { content: 'Hi there!' },
      });

      expect(chatListener).toHaveBeenCalledWith({
        sessionId: 'session123',
        userMessage: { content: 'Hello' },
        assistantMessage: { content: 'Hi there!' },
      });
    });
  });

  describe('Cleanup Tasks', () => {
    beforeEach(async () => {
      await registry.initialize();
    });

    it('should perform cleanup tasks periodically', (done) => {
      const cleanupSpy = jest.spyOn(registry.getBacktestBridgeService(), 'cleanupCompletedBacktests');
      const chatCleanupSpy = jest.spyOn(registry.getChatBridgeService(), 'cleanupInactiveSessions');

      // Wait for cleanup interval to trigger
      setTimeout(() => {
        expect(cleanupSpy).toHaveBeenCalled();
        expect(chatCleanupSpy).toHaveBeenCalledWith(24);
        done();
      }, 2500); // Wait longer than cleanup interval
    }, 10000);
  });

  describe('Shutdown', () => {
    it('should shutdown all services gracefully', async () => {
      await registry.initialize();

      const pythonEngineStopSpy = jest.spyOn(registry.getPythonEngineService(), 'stop');
      const tradingStopSpy = jest.spyOn(registry.getTradingBridgeService(), 'stop');
      const backtestStopSpy = jest.spyOn(registry.getBacktestBridgeService(), 'stop');
      const chatStopSpy = jest.spyOn(registry.getChatBridgeService(), 'stop');

      const shutdownListener = jest.fn();
      registry.on('shutdown_completed', shutdownListener);

      await registry.shutdown();

      expect(pythonEngineStopSpy).toHaveBeenCalled();
      expect(tradingStopSpy).toHaveBeenCalled();
      expect(backtestStopSpy).toHaveBeenCalled();
      expect(chatStopSpy).toHaveBeenCalled();
      expect(shutdownListener).toHaveBeenCalled();
      expect(registry.isReady()).toBe(false);
    });

    it('should handle shutdown errors gracefully', async () => {
      await registry.initialize();

      // Mock a service that throws during shutdown
      jest.spyOn(registry.getPythonEngineService(), 'stop')
        .mockRejectedValue(new Error('Shutdown failed'));

      await expect(registry.shutdown()).rejects.toThrow('Shutdown failed');
    });
  });

  describe('Error Handling', () => {
    beforeEach(async () => {
      await registry.initialize();
    });

    it('should handle health check errors gracefully', () => {
      // Mock health check to throw
      jest.spyOn(registry.getPythonEngineService(), 'getHealthStatus')
        .mockImplementation(() => {
          throw new Error('Health check failed');
        });

      // Health check should not crash the registry
      expect(() => registry.getServiceStatistics()).not.toThrow();
    });
  });
});