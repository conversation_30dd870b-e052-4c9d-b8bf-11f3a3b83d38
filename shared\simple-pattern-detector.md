# Simple Pattern Detector - TDD Implementation

## 🎯 Let's Build a Basic Pattern Detector

We'll start simple - just detect mean reversion vs momentum strategies.

## Step 1: Write Tests First (TDD!)

```python
# services/darwin_godel/__tests__/test_pattern_detector.py

import pytest
from services.darwin_godel.pattern_detector import StrategyPatternDetector

class TestStrategyPatternDetector:
    """Test pattern detection for trading strategies"""
    
    def setup_method(self):
        """Setup before each test"""
        self.detector = StrategyPatternDetector()
    
    # Test 1: Detect Mean Reversion Pattern
    def test_detects_mean_reversion_strategy(self):
        """It should identify mean reversion strategies"""
        # Arrange - A typical mean reversion strategy
        mean_reversion_code = """
def trading_strategy(data, params):
    # Calculate 20-period moving average
    sma = calculate_sma(data['close'], 20)
    current_price = data['close'][-1]
    
    # Buy when price is below average (expecting reversion up)
    if current_price < sma[-1] * 0.98:
        return {'signal': 'buy', 'confidence': 0.8}
    # Sell when price is above average (expecting reversion down)
    elif current_price > sma[-1] * 1.02:
        return {'signal': 'sell', 'confidence': 0.8}
    else:
        return {'signal': 'hold', 'confidence': 0.5}
"""
        
        # Act
        result = self.detector.analyze_strategy(mean_reversion_code)
        
        # Assert
        assert result['pattern_type'] == 'mean_reversion'
        assert 'sma' in result['indicators_found']
        assert result['confidence'] >= 0.8
        assert 'buying below average' in result['characteristics']
    
    # Test 2: Detect Momentum Pattern
    def test_detects_momentum_strategy(self):
        """It should identify momentum/trend following strategies"""
        # Arrange - A typical momentum strategy
        momentum_code = """
def trading_strategy(data, params):
    # Calculate short and long moving averages
    sma_fast = calculate_sma(data['close'], 10)
    sma_slow = calculate_sma(data['close'], 30)
    
    # Buy when fast MA crosses above slow MA (momentum up)
    if sma_fast[-1] > sma_slow[-1] and sma_fast[-2] <= sma_slow[-2]:
        return {'signal': 'buy', 'confidence': 0.9}
    # Sell when fast MA crosses below slow MA (momentum down)
    elif sma_fast[-1] < sma_slow[-1] and sma_fast[-2] >= sma_slow[-2]:
        return {'signal': 'sell', 'confidence': 0.9}
    else:
        return {'signal': 'hold', 'confidence': 0.3}
"""
        
        # Act
        result = self.detector.analyze_strategy(momentum_code)
        
        # Assert
        assert result['pattern_type'] == 'momentum'
        assert 'crossover' in result['characteristics']
        assert result['confidence'] >= 0.85
        assert len(result['indicators_found']) >= 2  # At least 2 MAs
    
    # Test 3: Detect RSI-based Patterns
    def test_detects_rsi_patterns(self):
        """It should identify RSI-based strategies correctly"""
        # Arrange - RSI strategy (could be mean reversion or momentum)
        rsi_code = """
def trading_strategy(data, params):
    rsi = calculate_rsi(data['close'], 14)
    
    # Classic RSI: Buy oversold, sell overbought (mean reversion)
    if rsi[-1] < 30:
        return {'signal': 'buy', 'confidence': 0.7}
    elif rsi[-1] > 70:
        return {'signal': 'sell', 'confidence': 0.7}
    else:
        return {'signal': 'hold', 'confidence': 0.5}
"""
        
        # Act
        result = self.detector.analyze_strategy(rsi_code)
        
        # Assert
        assert result['pattern_type'] == 'mean_reversion'
        assert 'rsi' in result['indicators_found']
        assert 'oversold/overbought' in result['characteristics']
    
    # Test 4: Detect Mixed/Unclear Patterns
    def test_handles_mixed_patterns(self):
        """It should handle strategies with mixed patterns"""
        # Arrange - Strategy with both patterns
        mixed_code = """
def trading_strategy(data, params):
    sma = calculate_sma(data['close'], 20)
    rsi = calculate_rsi(data['close'], 14)
    
    # Mixed logic
    if rsi[-1] < 30 and data['close'][-1] > sma[-1]:
        return {'signal': 'buy', 'confidence': 0.6}
"""
        
        # Act
        result = self.detector.analyze_strategy(mixed_code)
        
        # Assert
        assert result['pattern_type'] in ['mixed', 'custom']
        assert result['confidence'] < 0.7  # Lower confidence for mixed
        assert len(result['warnings']) > 0
    
    # Test 5: Extract All Indicators Used
    def test_extracts_all_indicators(self):
        """It should find all technical indicators used"""
        # Arrange
        multi_indicator_code = """
def trading_strategy(data, params):
    sma = calculate_sma(data['close'], 20)
    ema = calculate_ema(data['close'], 12)
    rsi = calculate_rsi(data['close'], 14)
    macd, signal = calculate_macd(data['close'])
    upper, lower = calculate_bollinger_bands(data['close'], 20, 2)
    
    # Some logic here...
    return {'signal': 'hold'}
"""
        
        # Act
        result = self.detector.analyze_strategy(multi_indicator_code)
        
        # Assert
        expected_indicators = ['sma', 'ema', 'rsi', 'macd', 'bollinger']
        for indicator in expected_indicators:
            assert indicator in result['indicators_found']
    
    # Test 6: Detect Simple Patterns
    def test_detects_price_action_patterns(self):
        """It should detect simple price action strategies"""
        # Arrange
        price_action_code = """
def trading_strategy(data, params):
    # Simple price action - no indicators
    if data['close'][-1] > data['close'][-2]:
        return {'signal': 'buy', 'confidence': 0.5}
    else:
        return {'signal': 'sell', 'confidence': 0.5}
"""
        
        # Act
        result = self.detector.analyze_strategy(price_action_code)
        
        # Assert
        assert result['pattern_type'] == 'price_action'
        assert len(result['indicators_found']) == 0
        assert 'simple' in result['characteristics']
```

## Step 2: Implement the Pattern Detector

Now let's make these tests pass!

```python
# services/darwin_godel/pattern_detector.py

import re
from typing import Dict, List, Set, Tuple
from dataclasses import dataclass

@dataclass
class PatternAnalysis:
    pattern_type: str
    confidence: float
    indicators_found: List[str]
    characteristics: List[str]
    warnings: List[str] = None

class StrategyPatternDetector:
    """
    Detects common trading strategy patterns
    Helps classify and understand user strategies
    """
    
    def __init__(self):
        # Define indicator patterns to look for
        self.indicators = {
            'sma': r'calculate_sma|sma|simple.*moving.*average',
            'ema': r'calculate_ema|ema|exponential.*moving.*average',
            'rsi': r'calculate_rsi|rsi|relative.*strength',
            'macd': r'calculate_macd|macd',
            'bollinger': r'bollinger|calculate_bollinger',
            'stochastic': r'stochastic|stoch',
            'atr': r'atr|average.*true.*range',
            'volume': r'volume|vol',
        }
        
        # Define pattern characteristics
        self.mean_reversion_keywords = [
            'oversold', 'overbought', 'below.*average', 'above.*average',
            'reversion', 'mean', 'bounce', 'extreme'
        ]
        
        self.momentum_keywords = [
            'crossover', 'cross.*above', 'cross.*below', 'trend',
            'momentum', 'breakout', 'acceleration', 'strength'
        ]
    
    def analyze_strategy(self, strategy_code: str) -> Dict:
        """
        Analyze a strategy and detect its pattern
        
        Args:
            strategy_code: The Python strategy code
            
        Returns:
            Dictionary with pattern analysis results
        """
        # Extract features from code
        indicators = self._extract_indicators(strategy_code)
        characteristics = self._extract_characteristics(strategy_code)
        logic_patterns = self._analyze_logic_patterns(strategy_code)
        
        # Determine pattern type
        pattern_type, confidence = self._classify_pattern(
            indicators, characteristics, logic_patterns, strategy_code
        )
        
        # Generate warnings if needed
        warnings = self._generate_warnings(pattern_type, indicators, strategy_code)
        
        return {
            'pattern_type': pattern_type,
            'confidence': confidence,
            'indicators_found': indicators,
            'characteristics': characteristics,
            'warnings': warnings if warnings else []
        }
    
    def _extract_indicators(self, code: str) -> List[str]:
        """Extract all technical indicators used in the strategy"""
        found_indicators = []
        code_lower = code.lower()
        
        for indicator, pattern in self.indicators.items():
            if re.search(pattern, code_lower):
                found_indicators.append(indicator)
        
        return found_indicators
    
    def _extract_characteristics(self, code: str) -> List[str]:
        """Extract strategy characteristics from the code"""
        characteristics = []
        code_lower = code.lower()
        
        # Check for mean reversion characteristics
        if re.search(r'<.*\*\s*0\.\d+', code):  # price < sma * 0.98
            characteristics.append('buying below average')
        if re.search(r'>.*\*\s*1\.\d+', code):  # price > sma * 1.02
            characteristics.append('selling above average')
            
        # Check for momentum characteristics
        if 'crossover' in code_lower or (
            re.search(r'>\s*.*and.*<=', code) or 
            re.search(r'<\s*.*and.*>=', code)
        ):
            characteristics.append('crossover')
            
        # Check for RSI levels
        if re.search(r'rsi.*<\s*3\d', code_lower):  # RSI < 30
            characteristics.append('oversold/overbought')
            
        # Check for simple price action
        if re.search(r'close.*>.*close', code) and len(self._extract_indicators(code)) == 0:
            characteristics.append('simple')
            characteristics.append('price action only')
        
        return characteristics
    
    def _analyze_logic_patterns(self, code: str) -> Dict[str, bool]:
        """Analyze the trading logic patterns"""
        patterns = {
            'has_mean_reversion': False,
            'has_momentum': False,
            'has_breakout': False,
            'is_simple': False
        }
        
        code_lower = code.lower()
        
        # Mean reversion patterns
        if any(keyword in code_lower for keyword in self.mean_reversion_keywords):
            patterns['has_mean_reversion'] = True
        
        # Check for "buy low, sell high" pattern
        if re.search(r'if.*<.*:.*buy', code_lower) and re.search(r'if.*>.*:.*sell', code_lower):
            patterns['has_mean_reversion'] = True
            
        # Momentum patterns
        if any(keyword in code_lower for keyword in self.momentum_keywords):
            patterns['has_momentum'] = True
            
        # Simple pattern (few conditions)
        if_count = code.count('if')
        if if_count <= 2:
            patterns['is_simple'] = True
            
        return patterns
    
    def _classify_pattern(self, indicators: List[str], 
                         characteristics: List[str],
                         logic_patterns: Dict[str, bool],
                         code: str) -> Tuple[str, float]:
        """
        Classify the strategy pattern based on extracted features
        
        Returns:
            (pattern_type, confidence)
        """
        confidence = 0.0
        pattern_scores = {
            'mean_reversion': 0,
            'momentum': 0,
            'price_action': 0,
            'mixed': 0
        }
        
        # Score based on indicators
        if 'rsi' in indicators and 'oversold/overbought' in characteristics:
            pattern_scores['mean_reversion'] += 3
        
        if len([ind for ind in indicators if ind in ['sma', 'ema']]) >= 2:
            if 'crossover' in characteristics:
                pattern_scores['momentum'] += 3
        
        # Score based on characteristics
        if 'buying below average' in characteristics:
            pattern_scores['mean_reversion'] += 2
        if 'selling above average' in characteristics:
            pattern_scores['mean_reversion'] += 2
            
        if 'crossover' in characteristics:
            pattern_scores['momentum'] += 2
            
        # Score based on logic patterns
        if logic_patterns['has_mean_reversion']:
            pattern_scores['mean_reversion'] += 2
        if logic_patterns['has_momentum']:
            pattern_scores['momentum'] += 2
            
        # Check for price action only
        if len(indicators) == 0 and logic_patterns['is_simple']:
            pattern_scores['price_action'] = 5
            
        # Determine winner
        max_score = max(pattern_scores.values())
        if max_score == 0:
            return 'custom', 0.5
            
        # Check for mixed patterns
        high_scores = [k for k, v in pattern_scores.items() if v >= max_score * 0.7]
        if len(high_scores) > 1 and 'price_action' not in high_scores:
            return 'mixed', 0.6
            
        # Get pattern with highest score
        pattern_type = max(pattern_scores, key=pattern_scores.get)
        
        # Calculate confidence
        total_score = sum(pattern_scores.values())
        if total_score > 0:
            confidence = min(0.95, max_score / total_score + 0.3)
        else:
            confidence = 0.5
            
        return pattern_type, confidence
    
    def _generate_warnings(self, pattern_type: str, 
                          indicators: List[str], 
                          code: str) -> List[str]:
        """Generate warnings based on pattern analysis"""
        warnings = []
        
        # Warn about mixed patterns
        if pattern_type == 'mixed':
            warnings.append("Strategy uses mixed patterns - consider focusing on one approach")
        
        # Warn about too many indicators
        if len(indicators) > 4:
            warnings.append(f"Using {len(indicators)} indicators - consider simplifying")
        
        # Warn about no stop loss
        if 'stop' not in code.lower() and 'risk' not in code.lower():
            warnings.append("No stop loss or risk management detected")
            
        # Pattern-specific warnings
        if pattern_type == 'mean_reversion':
            warnings.append("Mean reversion works best in ranging markets")
        elif pattern_type == 'momentum':
            warnings.append("Momentum strategies need trend filters to avoid whipsaws")
            
        return warnings
```

## Step 3: Create a User-Friendly Report Generator

```python
# services/darwin_godel/pattern_report.py

class PatternReport:
    """Generate user-friendly reports from pattern analysis"""
    
    @staticmethod
    def generate_report(analysis: Dict) -> str:
        """Create a formatted report from pattern analysis"""
        
        pattern_emoji = {
            'mean_reversion': '↕️',
            'momentum': '📈',
            'price_action': '📊',
            'mixed': '🔀',
            'custom': '🔧'
        }
        
        pattern_descriptions = {
            'mean_reversion': 'Your strategy buys low and sells high, expecting prices to return to average',
            'momentum': 'Your strategy follows trends, buying strength and selling weakness',
            'price_action': 'Your strategy uses pure price movement without technical indicators',
            'mixed': 'Your strategy combines multiple approaches',
            'custom': 'Your strategy uses a unique approach'
        }
        
        # Build report
        report = f"""
╔══════════════════════════════════════════════════════╗
║          STRATEGY PATTERN ANALYSIS REPORT            ║
╚══════════════════════════════════════════════════════╝

Pattern Type: {pattern_emoji.get(analysis['pattern_type'], '❓')} {analysis['pattern_type'].upper().replace('_', ' ')}
Confidence: {'█' * int(analysis['confidence'] * 10)}{'░' * (10 - int(analysis['confidence'] * 10))} {analysis['confidence']:.0%}

📝 Description:
{pattern_descriptions.get(analysis['pattern_type'], 'Custom strategy pattern')}

📊 Technical Indicators Found: {len(analysis['indicators_found'])}
{' • ' + chr(10).join(analysis['indicators_found']) if analysis['indicators_found'] else ' • None (Price action only)'}

🎯 Key Characteristics:
{' • ' + chr(10).join(analysis['characteristics']) if analysis['characteristics'] else ' • Simple strategy'}

"""
        
        if analysis['warnings']:
            report += f"""⚠️  Warnings & Suggestions:
{' • ' + chr(10).join(analysis['warnings'])}

"""
        
        # Add pattern-specific tips
        tips = PatternReport._get_pattern_tips(analysis['pattern_type'])
        if tips:
            report += f"""💡 Tips for {analysis['pattern_type'].replace('_', ' ').title()} Strategies:
{tips}
"""
        
        return report
    
    @staticmethod
    def _get_pattern_tips(pattern_type: str) -> str:
        """Get tips specific to each pattern type"""
        tips = {
            'mean_reversion': """• Works best in sideways/ranging markets
• Consider adding volatility filters (ATR)
• Use wider stops in trending markets
• Test with different reversion thresholds""",
            
            'momentum': """• Add trend strength filters (ADX)
• Consider multiple timeframe confirmation
• Use trailing stops to capture trends
• Avoid trading in choppy markets""",
            
            'price_action': """• Simple can be effective!
• Consider adding volume confirmation
• Test on different timeframes
• May benefit from market regime filters""",
            
            'mixed': """• Consider simplifying to one main approach
• Test each component separately
• Ensure signals don't conflict
• Document your logic clearly"""
        }
        
        return tips.get(pattern_type, "• Keep testing and refining your approach")
```

## Step 4: Simple Test Script

```python
# scripts/test_pattern_detection.py

#!/usr/bin/env python
"""Quick script to test pattern detection"""

from services.darwin_godel.pattern_detector import StrategyPatternDetector
from services.darwin_godel.pattern_report import PatternReport

def test_your_strategy():
    detector = StrategyPatternDetector()
    
    # Example strategy to test
    test_strategy = """
def trading_strategy(data, params):
    # Calculate 20-period SMA
    sma = calculate_sma(data['close'], 20)
    current_price = data['close'][-1]
    
    # Buy when price is 2% below SMA
    if current_price < sma[-1] * 0.98:
        return {'signal': 'buy', 'confidence': 0.8}
    # Sell when price is 2% above SMA
    elif current_price > sma[-1] * 1.02:
        return {'signal': 'sell', 'confidence': 0.8}
    else:
        return {'signal': 'hold', 'confidence': 0.5}
"""
    
    # Analyze the strategy
    print("Analyzing strategy...")
    analysis = detector.analyze_strategy(test_strategy)
    
    # Generate report
    report = PatternReport.generate_report(analysis)
    print(report)
    
    # Also print raw analysis
    print("\nRaw Analysis Data:")
    print(f"Pattern Type: {analysis['pattern_type']}")
    print(f"Confidence: {analysis['confidence']:.2%}")
    print(f"Indicators: {analysis['indicators_found']}")
    print(f"Characteristics: {analysis['characteristics']}")

if __name__ == "__main__":
    test_your_strategy()
```

## 🎯 Using Pattern Detection in Your Darwin Godel Verifier

```python
# Integrate into your main verifier
class DarwinGodelVerifier:
    def __init__(self):
        self.pattern_detector = StrategyPatternDetector()
        # ... other initialization
    
    def verify_strategy(self, strategy_code: str) -> Dict:
        # Security checks first
        self._check_code_safety(strategy_code)
        
        # Pattern detection
        pattern_analysis = self.pattern_detector.analyze_strategy(strategy_code)
        
        # Adjust verification based on pattern
        if pattern_analysis['pattern_type'] == 'mean_reversion':
            # Test with ranging market data
            test_data = self._generate_ranging_market()
        elif pattern_analysis['pattern_type'] == 'momentum':
            # Test with trending market data
            test_data = self._generate_trending_market()
        
        # Return comprehensive results
        return {
            'is_valid': True,
            'pattern_analysis': pattern_analysis,
            'recommendations': self._get_pattern_recommendations(pattern_analysis),
            # ... other verification results
        }
```

## 🚀 Next Steps

1. **Run the tests** to see them fail (TDD!)
2. **Implement the detector** to make tests pass
3. **Add more patterns** as you learn about them
4. **Refine detection** based on real strategies

This gives you a working pattern detector that can identify basic strategy types and provide meaningful feedback to users!