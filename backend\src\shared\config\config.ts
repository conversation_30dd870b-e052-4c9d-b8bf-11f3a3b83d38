import { config } from 'dotenv';
import { join } from 'path';
import { z } from 'zod';
import { EnvironmentConfig } from '../types/common.types';

// Load environment variables
const envFile = process.env.NODE_ENV === 'test' ? '.env.test' : '.env';
config({ path: join(__dirname, '../../../', envFile) });

// Environment validation schema
const EnvironmentSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().transform(Number).pipe(z.number().int().min(1).max(65535)).default('3001'),
  APP_NAME: z.string().default('AI Trading Platform API'),
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug', 'trace']).default('info'),
  
  // Database
  DB_HOST: z.string().default('localhost'),
  DB_PORT: z.string().transform(Number).pipe(z.number().int().min(1).max(65535)).default('5432'),
  DB_NAME: z.string().min(1),
  DB_USERNAME: z.string().min(1),
  DB_PASSWORD: z.string().min(1),
  DB_SSL: z.string().transform((val) => val === 'true').pipe(z.boolean()).default('false'),
  DB_POOL_SIZE: z.string().transform(Number).pipe(z.number().int().min(1)).default('10'),
  
  // Redis (optional)
  REDIS_HOST: z.string().optional(),
  REDIS_PORT: z.string().transform(Number).pipe(z.number().int().min(1).max(65535)).optional(),
  REDIS_PASSWORD: z.string().optional(),
  REDIS_DB: z.string().transform(Number).pipe(z.number().int().min(0)).optional(),
  
  // Security
  JWT_SECRET: z.string().min(32),
  JWT_EXPIRES_IN: z.string().default('24h'),
  REFRESH_TOKEN_EXPIRES_IN: z.string().default('7d'),
  BCRYPT_ROUNDS: z.string().transform(Number).pipe(z.number().int().min(4).max(15)).default('12'),
  
  // CORS
  CORS_ORIGINS: z.string().default('http://localhost:3000'),
  
  // Rate Limiting
  RATE_LIMIT_WINDOW_MS: z.string().transform(Number).pipe(z.number().int().positive()).default('900000'),
  RATE_LIMIT_MAX_REQUESTS: z.string().transform(Number).pipe(z.number().int().positive()).default('100'),
  
  // File Upload
  UPLOAD_MAX_SIZE: z.string().transform(Number).pipe(z.number().int().positive()).default('10485760'),
  UPLOAD_ALLOWED_TYPES: z.string().default('text/csv,application/vnd.ms-excel'),
  
  // API Keys (optional)
  OPENAI_API_KEY: z.string().optional(),
  ANTHROPIC_API_KEY: z.string().optional(),
  GOOGLE_API_KEY: z.string().optional(),
  COHERE_API_KEY: z.string().optional(),
  
  // Trading Data (optional)
  ALPHA_VANTAGE_API_KEY: z.string().optional(),
  POLYGON_API_KEY: z.string().optional(),
  IEX_CLOUD_API_KEY: z.string().optional(),
  
  // Python Scripts
  PYTHON_EXECUTABLE: z.string().default('python'),
  XLLM_SCRIPT_PATH: z.string().default('../scripts/xllm-integration.py'),
  DGM_SCRIPT_PATH: z.string().default('../scripts/dgm-runner.py'),
});

// Validate and parse environment variables
const parseEnvironment = (): EnvironmentConfig => {
  try {
    const result = EnvironmentSchema.parse(process.env);
    return result as EnvironmentConfig;
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.errors
        .map((err) => `${err.path.join('.')}: ${err.message}`)
        .join('\n');
      throw new Error(`Environment validation failed:\n${missingVars}`);
    }
    throw error;
  }
};

// Export configuration
export const env = parseEnvironment();

// Database configuration
export const databaseConfig = {
  client: 'postgresql',
  connection: {
    host: env.DB_HOST,
    port: env.DB_PORT,
    database: env.DB_NAME,
    user: env.DB_USERNAME,
    password: env.DB_PASSWORD,
    ssl: env.DB_SSL ? { rejectUnauthorized: false } : false,
  },
  pool: {
    min: 2,
    max: env.DB_POOL_SIZE,
  },
  migrations: {
    directory: './src/shared/database/migrations',
    tableName: 'knex_migrations',
  },
  seeds: {
    directory: './src/shared/database/seeds',
  },
};

// Redis configuration
export const redisConfig = env.REDIS_HOST
  ? {
      host: env.REDIS_HOST,
      port: env.REDIS_PORT || 6379,
      password: env.REDIS_PASSWORD,
      db: env.REDIS_DB || 0,
    }
  : null;

// JWT configuration
export const jwtConfig = {
  secret: env.JWT_SECRET,
  expiresIn: env.JWT_EXPIRES_IN,
  refreshExpiresIn: env.REFRESH_TOKEN_EXPIRES_IN,
};

// CORS configuration
export const corsConfig = {
  origin: env.CORS_ORIGINS.split(',').map((origin) => origin.trim()),
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
};

// Rate limiting configuration
export const rateLimitConfig = {
  windowMs: env.RATE_LIMIT_WINDOW_MS,
  max: env.RATE_LIMIT_MAX_REQUESTS,
  message: {
    error: 'Too many requests from this IP, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
};

// File upload configuration
export const uploadConfig = {
  maxSize: env.UPLOAD_MAX_SIZE,
  allowedTypes: env.UPLOAD_ALLOWED_TYPES.split(',').map((type) => type.trim()),
  destination: 'uploads/',
};

// External API configuration
export const apiConfig = {
  openai: {
    apiKey: env.OPENAI_API_KEY,
    baseURL: 'https://api.openai.com/v1',
  },
  anthropic: {
    apiKey: env.ANTHROPIC_API_KEY,
    baseURL: 'https://api.anthropic.com',
  },
  google: {
    apiKey: env.GOOGLE_API_KEY,
    baseURL: 'https://generativelanguage.googleapis.com/v1',
  },
  cohere: {
    apiKey: env.COHERE_API_KEY,
    baseURL: 'https://api.cohere.ai/v1',
  },
  alphaVantage: {
    apiKey: env.ALPHA_VANTAGE_API_KEY,
    baseURL: 'https://www.alphavantage.co/query',
  },
  polygon: {
    apiKey: env.POLYGON_API_KEY,
    baseURL: 'https://api.polygon.io',
  },
  iexCloud: {
    apiKey: env.IEX_CLOUD_API_KEY,
    baseURL: 'https://cloud.iexapis.com/stable',
  },
};

// Python scripts configuration
export const pythonConfig = {
  executable: env.PYTHON_EXECUTABLE,
  scripts: {
    xllm: env.XLLM_SCRIPT_PATH,
    dgm: env.DGM_SCRIPT_PATH,
  },
};