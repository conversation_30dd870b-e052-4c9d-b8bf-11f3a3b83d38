// src/infrastructure/websocket-manager.ts
import { WebSocketServer } from 'ws';
import { mockForexData } from '../demo/mock-data';

export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: string;
}

export class WebSocketManager {
  private wss: WebSocketServer;
  private clients = new Set<any>();
  private priceUpdateInterval?: NodeJS.Timeout;
  private marketEventInterval?: NodeJS.Timeout;

  constructor(port: number = 8080) {
    this.wss = new WebSocketServer({ port });
    this.setupWebSocketServer();
    this.startPriceUpdates();
    this.startMarketEvents();
    console.log(`WebSocket server running on ws://localhost:${port}`);
  }

  private setupWebSocketServer() {
    this.wss.on('connection', (ws) => {
      this.clients.add(ws);
      console.log('New WebSocket client connected. Total clients:', this.clients.size);

      // Send initial data
      ws.send(JSON.stringify({
        type: 'connection_established',
        pairs: Object.keys(mockForexData),
        serverTime: new Date().toISOString()
      }));

      ws.on('message', (message) => {
        try {
          const data = JSON.parse(message.toString());
          this.handleMessage(ws, data);
        } catch (error) {
          console.error('Error processing message:', error);
        }
      });

      ws.on('close', () => {
        this.clients.delete(ws);
        console.log('Client disconnected. Total clients:', this.clients.size);
      });

      ws.on('error', (error) => {
        console.error('WebSocket error:', error);
      });
    });
  }

  private handleMessage(ws: any, data: any) {
    switch (data.action) {
      case 'subscribe':
        ws.send(JSON.stringify({
          type: 'subscription_confirmed',
          pairs: data.pairs || Object.keys(mockForexData)
        }));
        break;
        
      case 'ping':
        ws.send(JSON.stringify({ type: 'pong' }));
        break;
    }
  }

  private startPriceUpdates() {
    this.priceUpdateInterval = setInterval(() => {
      const updates: any[] = [];

      Object.entries(mockForexData).forEach(([pair, data]) => {
        // Simulate realistic price movements
        const movement = (Math.random() - 0.5) * 0.0002;
        const spread = data.spread / 10000;
        
        const newBid = data.bid + movement;
        const newAsk = newBid + spread;
        
        // Update mock data
        mockForexData[pair].bid = newBid;
        mockForexData[pair].ask = newAsk;

        updates.push({
          type: 'price_update',
          pair,
          bid: newBid,
          ask: newAsk,
          spread: data.spread,
          timestamp: new Date().toISOString()
        });
      });

      this.broadcast({
        type: 'market_data',
        updates,
        serverTime: new Date().toISOString()
      });
    }, 500);
  }

  private startMarketEvents() {
    this.marketEventInterval = setInterval(() => {
      const eventTypes = ['news', 'economic_data', 'market_alert'];
      const event = {
        type: 'market_event',
        eventType: eventTypes[Math.floor(Math.random() * eventTypes.length)],
        severity: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)],
        message: this.generateMarketEvent(),
        timestamp: new Date().toISOString()
      };

      this.broadcast(event);
    }, 15000);
  }

  private generateMarketEvent(): string {
    const events = [
      'ECB announces interest rate decision',
      'USD strengthens on positive employment data',
      'Breaking: Major central bank intervention detected',
      'Volatility spike detected in EUR/USD',
      'London session opening - increased liquidity expected',
      'Technical breakout detected on GBP/JPY'
    ];
    return events[Math.floor(Math.random() * events.length)];
  }

  private broadcast(message: any) {
    const messageStr = JSON.stringify(message);
    this.clients.forEach(client => {
      if (client.readyState === 1) { // WebSocket.OPEN
        client.send(messageStr);
      }
    });
  }

  public close() {
    if (this.priceUpdateInterval) {
      clearInterval(this.priceUpdateInterval);
    }
    if (this.marketEventInterval) {
      clearInterval(this.marketEventInterval);
    }
    this.wss.close();
  }
}