@echo off
REM 🧬 Darwin Strategy Verification Platform Launcher (Windows)
REM Hybrid Python Backend + Web Frontend

echo 🧬 Darwin Strategy Verification Platform
echo ========================================

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python 3 is required but not installed.
    echo Please install Python 3.8+ from python.org and try again.
    pause
    exit /b 1
)

REM Create virtual environment if it doesn't exist
if not exist "darwin_env" (
    echo 📦 Creating virtual environment...
    python -m venv darwin_env
)

REM Activate virtual environment
echo 🔧 Activating virtual environment...
call darwin_env\Scripts\activate.bat

REM Create requirements.txt if it doesn't exist
if not exist "requirements.txt" (
    echo 📋 Creating requirements.txt...
    echo Flask==2.3.3> requirements.txt
    echo Flask-CORS==4.0.0>> requirements.txt
    echo pandas==2.1.0>> requirements.txt
    echo numpy==1.24.3>> requirements.txt
    echo yfinance==0.2.21>> requirements.txt
    echo python-dateutil==2.8.2>> requirements.txt
    echo requests==2.31.0>> requirements.txt
)

REM Install dependencies
echo 📥 Installing Python dependencies...
pip install -r requirements.txt

REM Check if backend file exists
if not exist "darwin_backend.py" (
    echo ❌ darwin_backend.py not found!
    echo Please save the Python backend code as 'darwin_backend.py'
    pause
    exit /b 1
)

REM Check if frontend file exists
if not exist "darwin_platform.html" (
    echo ❌ darwin_platform.html not found!
    echo Please save the web frontend code as 'darwin_platform.html'
    pause
    exit /b 1
)

REM Start the backend
echo 🚀 Starting Darwin Backend...
echo Backend will be available at: http://localhost:5000
echo Frontend will be available at: darwin_platform.html
echo.
echo 🔧 Backend Status:
echo - Real market data: ✅ Enabled
echo - Professional backtesting: ✅ Ready
echo - Monte Carlo simulations: ✅ Ready
echo - Technical indicators: ✅ Ready
echo.
echo 📊 Open darwin_platform.html in your browser to use the platform
echo Press Ctrl+C to stop the backend
echo.

REM Start Python backend
python darwin_backend.py

pause
