"use strict";
/**
 * User schemas - re-exports from auth.schemas.ts for compatibility
 * This maintains backward compatibility while centralizing user-related schemas
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateUserProfileRequestSchema = exports.CreateUserRequestSchema = exports.UserProfileSchema = exports.UserWithPasswordHashSchema = exports.SubscriptionTierSchema = exports.UserIdSchema = exports.UserSchema = void 0;
var auth_schemas_1 = require("./auth.schemas");
// Core schemas
Object.defineProperty(exports, "UserSchema", { enumerable: true, get: function () { return auth_schemas_1.UserSchema; } });
Object.defineProperty(exports, "UserIdSchema", { enumerable: true, get: function () { return auth_schemas_1.UserIdSchema; } });
Object.defineProperty(exports, "SubscriptionTierSchema", { enumerable: true, get: function () { return auth_schemas_1.SubscriptionTierSchema; } });
Object.defineProperty(exports, "UserWithPasswordHashSchema", { enumerable: true, get: function () { return auth_schemas_1.UserWithPasswordHashSchema; } });
Object.defineProperty(exports, "UserProfileSchema", { enumerable: true, get: function () { return auth_schemas_1.UserProfileSchema; } });
// Request/Response schemas
Object.defineProperty(exports, "CreateUserRequestSchema", { enumerable: true, get: function () { return auth_schemas_1.CreateUserRequestSchema; } });
Object.defineProperty(exports, "UpdateUserProfileRequestSchema", { enumerable: true, get: function () { return auth_schemas_1.UpdateUserProfileRequestSchema; } });
//# sourceMappingURL=user.schemas.js.map