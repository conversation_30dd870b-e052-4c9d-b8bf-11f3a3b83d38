"""
Strategy CRUD operations
"""

import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update, delete

from ..models import Strategy

async def get_strategy_by_id(db: AsyncSession, strategy_id: uuid.UUID) -> Optional[Strategy]:
    """Get strategy by ID"""
    result = await db.execute(select(Strategy).where(Strategy.id == strategy_id))
    return result.scalars().first()

async def get_strategies_by_user(db: AsyncSession, user_id: uuid.UUID) -> List[Strategy]:
    """Get all strategies for a user"""
    result = await db.execute(select(Strategy).where(Strategy.user_id == user_id))
    return result.scalars().all()

async def create_strategy(
    db: AsyncSession,
    user_id: uuid.UUID,
    name: str,
    config: Dict[str, Any],
    description: Optional[str] = None
) -> Strategy:
    """Create a new strategy"""
    db_strategy = Strategy(
        user_id=user_id,
        name=name,
        description=description,
        config=config,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    db.add(db_strategy)
    await db.commit()
    await db.refresh(db_strategy)
    return db_strategy

async def update_strategy(
    db: AsyncSession,
    strategy_id: uuid.UUID,
    strategy_data: Dict[str, Any]
) -> Optional[Strategy]:
    """Update strategy"""
    # Add updated_at timestamp
    strategy_data["updated_at"] = datetime.utcnow()
    
    # Update the strategy
    await db.execute(
        update(Strategy)
        .where(Strategy.id == strategy_id)
        .values(**strategy_data)
    )
    await db.commit()
    
    # Return the updated strategy
    return await get_strategy_by_id(db, strategy_id)

async def delete_strategy(db: AsyncSession, strategy_id: uuid.UUID) -> bool:
    """Delete strategy"""
    result = await db.execute(delete(Strategy).where(Strategy.id == strategy_id))
    await db.commit()
    return result.rowcount > 0