import { v4 as uuidv4 } from 'uuid';
import { EventEmitter } from 'events';
import { Logger, ServiceResponse } from '@/shared/types';
import {
  // ChatRequest, // Not currently used
  ChatResponse,
  PythonChatRequest,
  // PythonChatResponse, // Not currently used
  ChatMessage,
  ChatSession,
  // MessageRole, // Not currently used
} from '../../../../shared/schemas';
import { PythonEngineService } from './python-engine.service';

export interface ChatBridgeServiceDependencies {
  pythonEngineService: PythonEngineService;
  logger: Logger;
}

/**
 * Service for managing AI chat interactions through the Python engine
 * Handles chat sessions, message processing, and RAG integration
 */
export class ChatBridgeService extends EventEmitter {
  private activeSessions: Map<string, ChatSession> = new Map();
  private conversationHistory: Map<string, ChatMessage[]> = new Map();

  constructor(private dependencies: ChatBridgeServiceDependencies) {
    super();
    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    // Forward Python engine chat events
    this.dependencies.pythonEngineService.on('chat_query_sent', (data) => {
      this.emit('chat_query_processed', data);
    });
  }

  /**
   * Send a chat message and get AI response
   */
  async sendMessage(
    sessionId: string,
    message: string,
    userId: string,
    context?: any
  ): Promise<ServiceResponse<ChatResponse>> {
    try {
      // Validate input
      if (!message.trim()) {
        return {
          success: false,
          error: {
            code: 'INVALID_MESSAGE',
            message: 'Message cannot be empty',
          },
        };
      }

      // Get or create session
      let session = this.activeSessions.get(sessionId);
      if (!session) {
        session = await this.createSession(sessionId, userId);
      }

      // Get conversation history
      const history = this.conversationHistory.get(sessionId) || [];

      // Create user message
      const userMessage: ChatMessage = {
        id: uuidv4(),
        role: 'user',
        content: message,
        type: 'text',
        metadata: {
          timestamp: new Date(),
        },
        created_at: new Date(),
      };

      // Add user message to history
      history.push(userMessage);
      this.conversationHistory.set(sessionId, history);

      // Prepare request for Python engine
      const request: PythonChatRequest = {
        request_id: uuidv4(),
        query: message,
        session_id: sessionId,
        user_context: {
          user_id: userId,
          trading_data: context?.trading_data,
          preferences: context?.preferences,
        },
        conversation_history: history.slice(-10).map(msg => ({
          role: msg.role,
          content: msg.content,
          timestamp: msg.created_at,
        })),
        rag_config: {
          use_knowledge_graph: true,
          use_market_data: true,
          max_context_length: 4000,
        },
      };

      // Send to Python engine
      const response = await this.dependencies.pythonEngineService.sendChatQuery(request);

      if (!response.success || !response.data) {
        return {
          success: false,
          error: response.error || {
            code: 'CHAT_ENGINE_ERROR',
            message: 'Failed to process chat message',
          },
        };
      }

      if (!response.data.success || !response.data.response) {
        return {
          success: false,
          error: {
            code: 'CHAT_PROCESSING_ERROR',
            message: response.data.error || 'Failed to generate response',
          },
        };
      }

      const aiResponse = response.data.response;

      // Create assistant message
      const assistantMessage: ChatMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: aiResponse.message,
        type: aiResponse.type,
        metadata: {
          timestamp: new Date(),
          confidence: aiResponse.confidence,
          sources: aiResponse.sources?.map((s: any) => s.content),
          attachments: aiResponse.attachments as any, // Type casting to handle attachment type mismatch
        },
        created_at: new Date(),
      };

      // Add assistant message to history
      history.push(assistantMessage);
      this.conversationHistory.set(sessionId, history);

      // Update session last activity
      session.last_activity = new Date();
      session.updated_at = new Date();
      this.activeSessions.set(sessionId, session);

      this.dependencies.logger.info('Chat message processed successfully', {
        sessionId,
        userId,
        requestId: request.request_id,
        messageLength: message.length,
        responseLength: aiResponse.message.length,
        confidence: aiResponse.confidence,
        processingTime: response.data.processing_time_ms,
      });

      this.emit('message_processed', {
        sessionId,
        userMessage,
        assistantMessage,
        processingTime: response.data.processing_time_ms,
      });

      return {
        success: true,
        data: aiResponse,
      };
    } catch (error) {
      this.dependencies.logger.error('Chat message processing failed', {
        sessionId,
        userId,
        messagePreview: message.substring(0, 100),
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return {
        success: false,
        error: {
          code: 'CHAT_PROCESSING_ERROR',
          message: 'Failed to process chat message',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * Get conversation history for a session
   */
  getConversationHistory(sessionId: string, limit: number = 50): ChatMessage[] {
    const history = this.conversationHistory.get(sessionId) || [];
    return history.slice(-limit);
  }

  /**
   * Create a new chat session
   */
  private async createSession(sessionId: string, userId: string): Promise<ChatSession> {
    const session: ChatSession = {
      id: sessionId,
      user_id: userId,
      title: 'New Chat Session',
      context: {},
      created_at: new Date(),
      updated_at: new Date(),
      last_activity: new Date(),
    };

    this.activeSessions.set(sessionId, session);
    this.conversationHistory.set(sessionId, []);

    this.dependencies.logger.info('New chat session created', {
      sessionId,
      userId,
    });

    return session;
  }

  /**
   * Update session context (trading symbols, preferences, etc.)
   */
  async updateSessionContext(
    sessionId: string,
    context: ChatSession['context']
  ): Promise<ServiceResponse<ChatSession>> {
    try {
      const session = this.activeSessions.get(sessionId);
      
      if (!session) {
        return {
          success: false,
          error: {
            code: 'SESSION_NOT_FOUND',
            message: 'Chat session not found',
          },
        };
      }

      session.context = { ...session.context, ...context };
      session.updated_at = new Date();
      this.activeSessions.set(sessionId, session);

      this.dependencies.logger.debug('Session context updated', {
        sessionId,
        contextKeys: Object.keys(context),
      });

      return {
        success: true,
        data: session,
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'SESSION_UPDATE_ERROR',
          message: 'Failed to update session context',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * Get active session
   */
  getSession(sessionId: string): ChatSession | undefined {
    return this.activeSessions.get(sessionId);
  }

  /**
   * Clear conversation history for a session
   */
  clearConversationHistory(sessionId: string): void {
    this.conversationHistory.set(sessionId, []);
    
    const session = this.activeSessions.get(sessionId);
    if (session) {
      session.updated_at = new Date();
      this.activeSessions.set(sessionId, session);
    }

    this.dependencies.logger.info('Conversation history cleared', { sessionId });
  }

  /**
   * End a chat session
   */
  async endSession(sessionId: string): Promise<ServiceResponse<{ ended: boolean }>> {
    try {
      const session = this.activeSessions.get(sessionId);
      
      if (!session) {
        return {
          success: false,
          error: {
            code: 'SESSION_NOT_FOUND',
            message: 'Chat session not found',
          },
        };
      }

      // In a real implementation, you might want to persist session data
      this.activeSessions.delete(sessionId);
      this.conversationHistory.delete(sessionId);

      this.dependencies.logger.info('Chat session ended', {
        sessionId,
        userId: session.user_id,
        duration: Date.now() - session.created_at.getTime(),
      });

      this.emit('session_ended', { sessionId, session });

      return {
        success: true,
        data: { ended: true },
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'SESSION_END_ERROR',
          message: 'Failed to end session',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * Get all active sessions for a user
   */
  getUserSessions(userId: string): ChatSession[] {
    return Array.from(this.activeSessions.values())
      .filter(session => session.user_id === userId);
  }

  /**
   * Clean up inactive sessions
   */
  cleanupInactiveSessions(maxInactiveHours: number = 24): void {
    const cutoffTime = new Date(Date.now() - (maxInactiveHours * 60 * 60 * 1000));
    const inactiveSessions: string[] = [];

    for (const [sessionId, session] of this.activeSessions.entries()) {
      if (session.last_activity < cutoffTime) {
        inactiveSessions.push(sessionId);
      }
    }

    for (const sessionId of inactiveSessions) {
      this.activeSessions.delete(sessionId);
      this.conversationHistory.delete(sessionId);
    }

    if (inactiveSessions.length > 0) {
      this.dependencies.logger.info('Cleaned up inactive chat sessions', {
        count: inactiveSessions.length,
        cutoffTime,
      });
    }
  }

  /**
   * Generate a summary of conversation for the session title
   */
  async generateSessionTitle(sessionId: string): Promise<ServiceResponse<string>> {
    try {
      const history = this.conversationHistory.get(sessionId);
      
      if (!history || history.length === 0) {
        return {
          success: false,
          error: {
            code: 'NO_CONVERSATION_HISTORY',
            message: 'No conversation history available',
          },
        };
      }

      // Get first user message as a simple title
      const firstUserMessage = history.find(msg => msg.role === 'user');
      if (firstUserMessage) {
        const title = firstUserMessage.content.substring(0, 50).trim();
        return {
          success: true,
          data: title + (firstUserMessage.content.length > 50 ? '...' : ''),
        };
      }

      return {
        success: true,
        data: 'New Chat Session',
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'TITLE_GENERATION_ERROR',
          message: 'Failed to generate session title',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * Get session statistics
   */
  getSessionStats(sessionId: string): {
    messageCount: number;
    userMessages: number;
    assistantMessages: number;
    averageResponseTime?: number;
  } | null {
    const history = this.conversationHistory.get(sessionId);
    
    if (!history) {
      return null;
    }

    const userMessages = history.filter(msg => msg.role === 'user').length;
    const assistantMessages = history.filter(msg => msg.role === 'assistant').length;

    return {
      messageCount: history.length,
      userMessages,
      assistantMessages,
    };
  }

  /**
   * Stop the service and cleanup
   */
  async stop(): Promise<void> {
    this.activeSessions.clear();
    this.conversationHistory.clear();
    this.removeAllListeners();
    this.dependencies.logger.info('Chat bridge service stopped');
  }
}