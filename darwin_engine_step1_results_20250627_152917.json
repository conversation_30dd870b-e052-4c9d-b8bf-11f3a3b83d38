{"best_strategy": {"id": "18a92efc-3051-4e7c-b01f-bac615b9c23c", "name": "RSI_Oversold_569f8105_mut_mut_mut_mut_mut_mut_mut_mut_mut", "description": "Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Auto-generated RSI_Oversold strategy", "conditions": [{"indicator": "RSI", "operator": "<=", "value": 50.23311041118319, "period": 14}], "action": "sell", "risk_management": {"stop_loss_pct": 2.206732832435735, "take_profit_pct": 2.858752836955578, "position_size_pct": 1.9235746111662912, "max_risk_per_trade": 0.04209280148782746}, "generation": 9, "fitness_score": 1.1963081180388926, "parent_ids": ["cb4a79e3-d37a-4b58-a155-d1c273db9c12"], "backtest_results": {"fitness": 1.1963081180388926, "win_rate": 0.4172232837396326, "total_trades": 176, "sharpe_ratio": 2.1243446148344898, "max_drawdown": 21.061656710728936}}, "final_population": [{"id": "18a92efc-3051-4e7c-b01f-bac615b9c23c", "name": "RSI_Oversold_569f8105_mut_mut_mut_mut_mut_mut_mut_mut_mut", "description": "Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Auto-generated RSI_Oversold strategy", "conditions": [{"indicator": "RSI", "operator": "<=", "value": 50.23311041118319, "period": 14}], "action": "sell", "risk_management": {"stop_loss_pct": 2.206732832435735, "take_profit_pct": 2.858752836955578, "position_size_pct": 1.9235746111662912, "max_risk_per_trade": 0.04209280148782746}, "generation": 9, "fitness_score": 1.1963081180388926, "parent_ids": ["cb4a79e3-d37a-4b58-a155-d1c273db9c12"], "backtest_results": {"fitness": 1.1963081180388926, "win_rate": 0.4172232837396326, "total_trades": 176, "sharpe_ratio": 2.1243446148344898, "max_drawdown": 21.061656710728936}}, {"id": "66c7a624-8ef7-46b6-a628-deabb1127e34", "name": "RSI_Oversold_59f6e76b_mut_mut_mut_mut_mut_mut_mut_mut_mut", "description": "Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Auto-generated RSI_Oversold strategy", "conditions": [{"indicator": "RSI", "operator": "<", "value": 30.370992705356073, "period": 21}], "action": "close", "risk_management": {"stop_loss_pct": 2.9439282073492663, "take_profit_pct": 3.2016411624998007, "position_size_pct": 1.3682088834131823, "max_risk_per_trade": 0.047865063918763405}, "generation": 9, "fitness_score": 1.1937521681702743, "parent_ids": ["a71a3ab4-71e5-4101-bc82-eda7d0b46918"], "backtest_results": {"fitness": 1.1937521681702743, "win_rate": 0.6875192032139885, "total_trades": 134, "sharpe_ratio": 1.1055873713462763, "max_drawdown": 23.907833327862953}}, {"id": "ecbd1417-b785-4716-a997-262f65df6710", "name": "Multi_Indicator_d17b4fa4_mut_mut_mut_mut_mut_mut_mut", "description": "Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Auto-generated Multi_Indicator strategy", "conditions": [{"indicator": "RSI", "operator": "<=", "value": 41.860404040337585, "period": 200}, {"indicator": "MACD", "operator": "crossunder", "value": 0, "period": 50}, {"indicator": "RSI", "operator": ">", "value": 41.75193082683038, "period": 50}], "action": "close", "risk_management": {"stop_loss_pct": 3.336977848638745, "take_profit_pct": 5.9837630952581415, "position_size_pct": 0.5867944168744625, "max_risk_per_trade": 0.034583134390356304}, "generation": 7, "fitness_score": 1.1760433758029538, "parent_ids": ["c5b6fbd8-bb68-4bf1-b951-a99f99626e0a"], "backtest_results": {"fitness": 1.1760433758029538, "win_rate": 0.6166226827152799, "total_trades": 157, "sharpe_ratio": 2.2529137897896323, "max_drawdown": 24.89936346915055}}, {"id": "7e9f0abf-c61c-4929-a11e-4abd8e8ce4a8", "name": "Multi_Indicator_f7b10723_mut_mut_mut_mut_mut_mut_mut_mut", "description": "Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Auto-generated Multi_Indicator strategy", "conditions": [{"indicator": "RSI", "operator": "<", "value": 51.390010851791516, "period": 50}, {"indicator": "MACD", "operator": ">", "value": 0, "period": 14}], "action": "buy", "risk_management": {"stop_loss_pct": 4.6600140561932815, "take_profit_pct": 6.5878022049609894, "position_size_pct": 2.1595229870265835, "max_risk_per_trade": 0.04222920278226934}, "generation": 8, "fitness_score": 1.1648011219044465, "parent_ids": ["ef6e594d-5132-4826-8b6a-c2a917b59f31"], "backtest_results": {"fitness": 1.1648011219044465, "win_rate": 0.5990293078461377, "total_trades": 191, "sharpe_ratio": 2.222622800982941, "max_drawdown": 5.33530557413159}}, {"id": "0f3967d8-80df-406c-8cd4-e9bc07afa794", "name": "RSI_Oversold_59f6e76b_mut_mut_mut_mut_mut_mut_mut_mut", "description": "Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Auto-generated RSI_Oversold strategy", "conditions": [{"indicator": "RSI", "operator": "<", "value": 30.370992705356073, "period": 21}], "action": "close", "risk_management": {"stop_loss_pct": 2.9439282073492663, "take_profit_pct": 3.2016411624998007, "position_size_pct": 1.3682088834131823, "max_risk_per_trade": 0.047865063918763405}, "generation": 8, "fitness_score": 1.1642448489083312, "parent_ids": ["13c6a5dd-89fc-4891-951a-c403049a3ef7"], "backtest_results": {"fitness": 1.1642448489083312, "win_rate": 0.6667840487467367, "total_trades": 74, "sharpe_ratio": 0.9443035651329685, "max_drawdown": 24.801266720683774}}, {"id": "b4206f2c-b950-4c55-8a31-ba6504072622", "name": "Multi_Indicator_f7b10723_mut_mut_mut_mut_mut_mut_mut_mut", "description": "Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Auto-generated Multi_Indicator strategy", "conditions": [{"indicator": "RSI", "operator": "<", "value": 51.390010851791516, "period": 50}, {"indicator": "MACD", "operator": ">", "value": 0, "period": 14}], "action": "sell", "risk_management": {"stop_loss_pct": 4.6600140561932815, "take_profit_pct": 6.5878022049609894, "position_size_pct": 1.7213442768792302, "max_risk_per_trade": 0.04222920278226934}, "generation": 8, "fitness_score": 1.141525902712109, "parent_ids": ["ab80cd30-ecb7-4885-a0fc-a34c2af55da1"], "backtest_results": {"fitness": 1.141525902712109, "win_rate": 0.4187703707434798, "total_trades": 146, "sharpe_ratio": 0.9439894298186298, "max_drawdown": 22.51072608666177}}, {"id": "bce2bac1-bfb5-466f-b86c-9b6fb0f188f9", "name": "RSI_Oversold_569f8105_mut_mut_mut_mut_mut_mut_mut_mut_mut", "description": "Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Auto-generated RSI_Oversold strategy", "conditions": [{"indicator": "RSI", "operator": "<=", "value": 50.23311041118319, "period": 14}], "action": "sell", "risk_management": {"stop_loss_pct": 2.206732832435735, "take_profit_pct": 2.858752836955578, "position_size_pct": 1.9235746111662912, "max_risk_per_trade": 0.04209280148782746}, "generation": 9, "fitness_score": 1.13627842469641, "parent_ids": ["cb4a79e3-d37a-4b58-a155-d1c273db9c12"], "backtest_results": {"fitness": 1.13627842469641, "win_rate": 0.4912060436059608, "total_trades": 77, "sharpe_ratio": 1.4921666464331433, "max_drawdown": 8.80788870232535}}, {"id": "cb4a79e3-d37a-4b58-a155-d1c273db9c12", "name": "RSI_Oversold_569f8105_mut_mut_mut_mut_mut_mut_mut_mut", "description": "Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Auto-generated RSI_Oversold strategy", "conditions": [{"indicator": "RSI", "operator": "<=", "value": 50.23311041118319, "period": 14}], "action": "sell", "risk_management": {"stop_loss_pct": 2.206732832435735, "take_profit_pct": 2.858752836955578, "position_size_pct": 1.9235746111662912, "max_risk_per_trade": 0.04209280148782746}, "generation": 8, "fitness_score": 1.0920014437131378, "parent_ids": ["6d4f58c2-95e8-47fd-8389-8819982a6940"], "backtest_results": {"fitness": 1.0920014437131378, "win_rate": 0.6888867365085551, "total_trades": 59, "sharpe_ratio": 0.9711217856321566, "max_drawdown": 18.75622421449033}}, {"id": "9d5cee48-7cd1-47f8-aa1c-3c6ac579fbb9", "name": "Multi_Indicator_f7b10723_mut_mut_mut_mut_mut_mut_mut_mut", "description": "Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Auto-generated Multi_Indicator strategy", "conditions": [{"indicator": "RSI", "operator": "<", "value": 51.390010851791516, "period": 50}, {"indicator": "MACD", "operator": ">", "value": 0, "period": 14}], "action": "hold", "risk_management": {"stop_loss_pct": 4.6600140561932815, "take_profit_pct": 6.5878022049609894, "position_size_pct": 1.7213442768792302, "max_risk_per_trade": 0.04222920278226934}, "generation": 8, "fitness_score": 1.0757479623884212, "parent_ids": ["ef6e594d-5132-4826-8b6a-c2a917b59f31"], "backtest_results": {"fitness": 1.0757479623884212, "win_rate": 0.7009248918200047, "total_trades": 200, "sharpe_ratio": 1.1645980252076253, "max_drawdown": 14.85250627805206}}, {"id": "85cefbf5-cda6-4dc6-b145-43524261c3e7", "name": "RSI_Oversold_569f8105_mut_mut_mut_mut_mut_mut_mut_mut_mut", "description": "Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Mutation of Auto-generated RSI_Oversold strategy", "conditions": [{"indicator": "RSI", "operator": "<=", "value": 50.23311041118319, "period": 14}], "action": "sell", "risk_management": {"stop_loss_pct": 2.206732832435735, "take_profit_pct": 2.858752836955578, "position_size_pct": 1.9235746111662912, "max_risk_per_trade": 0.0774525441010003}, "generation": 9, "fitness_score": 1.065642008671516, "parent_ids": ["cb4a79e3-d37a-4b58-a155-d1c273db9c12"], "backtest_results": {"fitness": 1.065642008671516, "win_rate": 0.6398739113218104, "total_trades": 131, "sharpe_ratio": 2.3516301897277767, "max_drawdown": 5.597777631303273}}], "evolution_history": [{"generation": 1, "best_fitness": 1.1679460738371534, "average_fitness": 0.9008563928649103, "best_strategy_id": "9566a0e9-0f35-4ad7-81ec-fd33b7b3af2d"}, {"generation": 2, "best_fitness": 1.1940450761049244, "average_fitness": 0.9936249400066972, "best_strategy_id": "f4d5ecf3-7582-4f37-9304-4ec51ae541ed"}, {"generation": 3, "best_fitness": 1.1974540503254014, "average_fitness": 1.045694802910179, "best_strategy_id": "0641f167-2146-4f04-a391-1b93bc30939b"}, {"generation": 4, "best_fitness": 1.1944016125816752, "average_fitness": 1.0305621649957728, "best_strategy_id": "a4b502d0-03f4-4f58-82fa-aca59de9fdd9"}, {"generation": 5, "best_fitness": 1.1970623575785169, "average_fitness": 1.0234602795203764, "best_strategy_id": "e4b74f7a-53c8-4e54-80e7-b6499b85669f"}, {"generation": 6, "best_fitness": 1.197160855507992, "average_fitness": 1.0606158985447127, "best_strategy_id": "1f8573a9-edce-4bf3-b171-771317815345"}, {"generation": 7, "best_fitness": 1.197224059080298, "average_fitness": 1.0353786805911374, "best_strategy_id": "045eaf5b-15eb-49be-aaeb-ec0dc27e9423"}, {"generation": 8, "best_fitness": 1.1819094748041474, "average_fitness": 1.0319405505289883, "best_strategy_id": "4aa14932-ee99-41e3-9b3f-2974f9ac7189"}, {"generation": 9, "best_fitness": 1.1622804922271013, "average_fitness": 1.0554717361275592, "best_strategy_id": "c5b6fbd8-bb68-4bf1-b951-a99f99626e0a"}, {"generation": 10, "best_fitness": 1.1963081180388926, "average_fitness": 1.0622747958043965, "best_strategy_id": "18a92efc-3051-4e7c-b01f-bac615b9c23c"}], "total_evaluations": 200, "total_time_seconds": 0.007073640823364258, "generations_completed": 10}