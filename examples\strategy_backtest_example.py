"""
Strategy Backtest Example

This script demonstrates how to use the trading strategy, backtesting engine,
and risk management components of the AI Enhanced Trading Platform.
"""

import logging
import sys
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np

# Add the project root to the Python path
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.strategies.moving_average_crossover import MovingAverageCrossover
from src.backtest.backtest_engine import BacktestEngine, generate_sample_data
from src.risk.risk_manager import RiskManager2
from src.trading.mt5_bridge_tdd import MT5Bridge

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('backtest_example.log')
    ]
)

logger = logging.getLogger(__name__)


def run_backtest_example():
    """Run a backtest example with the Moving Average Crossover strategy"""
    logger.info("Starting backtest example")
    
    # Generate sample data
    symbols = ["EURUSD", "USDJPY"]
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2023, 3, 31)
    
    logger.info(f"Generating sample data for {symbols} from {start_date} to {end_date}")
    historical_data = generate_sample_data(symbols, start_date, end_date)
    
    # Create MT5 Bridge in offline mode
    bridge = MT5Bridge(offline_mode=True)
    
    # Create Risk Manager
    risk_manager = RiskManager2(
        mt5_bridge=bridge,
        risk_per_trade=0.02,
        max_risk_per_symbol=0.05,
        max_total_risk=0.2,
        max_drawdown=0.1,
        position_sizing_method="fixed_risk"
    )
    
    # Create strategy
    strategy = MovingAverageCrossover(
        symbols=symbols,
        fast_period=10,
        slow_period=30,
        mt5_bridge=bridge,
        risk_per_trade=0.02,
        max_open_positions=5,
        offline_mode=True
    )
    
    # Create backtest engine
    backtest_engine = BacktestEngine(
        strategy=strategy,
        historical_data=historical_data,
        start_date=start_date,
        end_date=end_date,
        initial_balance=10000.0
    )
    
    # Run backtest
    logger.info("Running backtest")
    results = backtest_engine.run(update_interval=timedelta(hours=4))
    
    # Print performance metrics
    metrics = results["performance_metrics"]
    logger.info("Backtest Results:")
    logger.info(f"Total Trades: {metrics['total_trades']}")
    logger.info(f"Win Rate: {metrics['win_rate']:.2%}")
    logger.info(f"Total Profit: ${metrics['total_profit']:.2f}")
    logger.info(f"Max Drawdown: ${metrics['max_drawdown']:.2f}")
    logger.info(f"Sharpe Ratio: {metrics['sharpe_ratio']:.2f}")
    logger.info(f"Profit Factor: {metrics['profit_factor']:.2f}")
    
    # Plot results
    logger.info("Plotting results")
    backtest_engine.plot_results(save_path="backtest_results.png")
    
    logger.info("Backtest example completed")
    
    return results


def run_live_trading_example():
    """Run a live trading example with the Moving Average Crossover strategy"""
    logger.info("Starting live trading example (simulated)")
    
    # Create MT5 Bridge in offline mode (for simulation)
    bridge = MT5Bridge(offline_mode=True)
    
    # Create Risk Manager
    risk_manager = RiskManager2(
        mt5_bridge=bridge,
        risk_per_trade=0.01,  # Lower risk for live trading
        max_risk_per_symbol=0.03,
        max_total_risk=0.1,
        max_drawdown=0.05,
        position_sizing_method="fixed_risk"
    )
    
    # Create strategy
    strategy = MovingAverageCrossover(
        symbols=["EURUSD", "USDJPY"],
        fast_period=10,
        slow_period=30,
        mt5_bridge=bridge,
        risk_per_trade=0.01,
        max_open_positions=3,
        offline_mode=True
    )
    
    # Start the strategy
    logger.info("Starting strategy")
    strategy.start()
    
    # Simulate market updates
    logger.info("Simulating market updates")
    for i in range(10):
        logger.info(f"Update {i+1}/10")
        strategy.update()
        
        # Print current positions
        positions = bridge.get_positions()
        if positions:
            logger.info(f"Current positions: {len(positions)}")
            for pos in positions:
                logger.info(f"  {pos['symbol']} {pos['type']} {pos['lot']}")
        else:
            logger.info("No open positions")
        
        # Simulate time passing
        # In a real implementation, this would be handled by a scheduler
    
    # Stop the strategy
    logger.info("Stopping strategy")
    strategy.stop()
    
    # Print performance
    performance = strategy.get_performance()
    logger.info("Strategy Performance:")
    logger.info(f"Total Trades: {performance['total_trades']}")
    logger.info(f"Win Rate: {performance['win_rate']:.2%}")
    
    logger.info("Live trading example completed")


if __name__ == "__main__":
    # Run the backtest example
    backtest_results = run_backtest_example()
    
    # Run the live trading example
    run_live_trading_example()