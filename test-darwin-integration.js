// Simple test to verify Darwin integration
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧬 Testing Darwin Gödel Machine Integration...\n');

// Test 1: Check if shared schemas are built correctly
console.log('1. Testing shared schemas build...');
try {
  const schemaIndexPath = path.join(__dirname, 'shared', 'dist', 'schemas', 'index.d.ts');
  const schemaContent = fs.readFileSync(schemaIndexPath, 'utf8');
  
  if (schemaContent.includes('darwin.schemas')) {
    console.log('✅ Darwin schemas are properly exported');
  } else {
    console.log('❌ Darwin schemas not found in exports');
  }
} catch (error) {
  console.log('❌ Failed to read schema exports:', error.message);
}

// Test 2: Check if Darwin schemas file exists
console.log('\n2. Testing Darwin schema files...');
try {
  const darwinSchemaPath = path.join(__dirname, 'shared', 'dist', 'schemas', 'darwin.schemas.d.ts');
  if (fs.existsSync(darwinSchemaPath)) {
    console.log('✅ Darwin schema definitions exist');
    
    const content = fs.readFileSync(darwinSchemaPath, 'utf8');
    const expectedTypes = [
      'EvolutionStatus',
      'TradingStrategy', 
      'DarwinEvolutionRequest',
      'ForexGenome'
    ];
    
    let allTypesFound = true;
    expectedTypes.forEach(type => {
      if (content.includes(type)) {
        console.log(`  ✅ ${type} type found`);
      } else {
        console.log(`  ❌ ${type} type missing`);
        allTypesFound = false;
      }
    });
    
    if (allTypesFound) {
      console.log('✅ All expected Darwin types are available');
    }
  } else {
    console.log('❌ Darwin schema definitions not found');
  }
} catch (error) {
  console.log('❌ Error checking Darwin schemas:', error.message);
}

// Test 3: Check if Python files exist
console.log('\n3. Testing Python Darwin files...');
const pythonFiles = [
  'shared/darwin_godel_orchestrator.py',
  'shared/enhanced_darwin_godel_core.py', 
  'shared/darwin_bridge.py'
];

pythonFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file} exists`);
  } else {
    console.log(`❌ ${file} missing`);
  }
});

// Test 4: Check if backend service files exist
console.log('\n4. Testing backend service files...');
const backendFiles = [
  'backend/src/services/bridge/darwin-bridge.service.ts',
  'backend/src/routes/darwin.routes.ts'
];

backendFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file} exists`);
  } else {
    console.log(`❌ ${file} missing`);
  }
});

// Test 5: Try to import Darwin schemas in Node.js
console.log('\n5. Testing schema imports...');
try {
  // This would test if the schemas can be imported
  const schemaPath = path.join(__dirname, 'shared', 'dist', 'schemas', 'index.js');
  if (fs.existsSync(schemaPath)) {
    console.log('✅ Schema JavaScript files are available for import');
  } else {
    console.log('❌ Schema JavaScript files not found');
  }
} catch (error) {
  console.log('❌ Error testing schema imports:', error.message);
}

console.log('\n🎉 Darwin Integration Test Complete!');
console.log('\n📋 Next Steps:');
console.log('1. Start the Python Darwin bridge: python shared/darwin_bridge.py');
console.log('2. Start the backend server: npm run dev (in backend directory)');
console.log('3. Test the API endpoints:');
console.log('   POST /api/darwin-evolution/evolve');
console.log('   GET  /api/darwin-evolution/status/:jobId');
console.log('   GET  /api/darwin-evolution/results/:jobId');
console.log('\n🔗 API Documentation available at: http://localhost:3001/api/docs');