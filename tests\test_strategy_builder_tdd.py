"""
TDD Tests for Strategy Builder Core
Pure Test-Driven Development approach - Tests written first!
"""

import pytest
from unittest.mock import Mock, patch
import ast
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.chatbot.models import StrategyType, ValidationResult
from src.chatbot.strategy_builder import StrategyBuilder, StrategyValidationError, SecurityError


class TestStrategyBuilder:
    """Test Strategy Builder Core functionality with TDD approach"""
    
    def test_should_validate_basic_strategy_structure(self):
        """RED: Write failing test first - validate basic strategy structure"""
        builder = StrategyBuilder()
        
        # Invalid strategy - missing required function
        invalid_strategy = "print('hello')"  # Missing trading_strategy function
        
        with pytest.raises(StrategyValidationError, match="Missing trading_strategy function"):
            builder.validate_strategy_code(invalid_strategy)
    
    def test_should_accept_valid_strategy_with_required_signature(self):
        """RED: This will fail initially - test valid strategy acceptance"""
        builder = StrategyBuilder()
        
        valid_strategy = """
def trading_strategy(data, params):
    return {'signal': 'hold', 'confidence': 0.5}
"""
        
        result = builder.validate_strategy_code(valid_strategy)
        assert result.is_valid == True
        assert result.strategy_type == "basic"
    
    def test_should_detect_ml_strategy_type(self):
        """RED: Test for ML strategy detection"""
        builder = StrategyBuilder()
        
        ml_strategy = """
import pandas as pd
from sklearn.ensemble import RandomForestClassifier

def trading_strategy(data, params):
    # This should be detected as ML strategy
    model = RandomForestClassifier()
    return {'signal': 'buy', 'confidence': 0.8}
"""
        
        result = builder.validate_strategy_code(ml_strategy)
        assert result.strategy_type == "machine_learning"
        assert any("sklearn" in lib for lib in result.detected_libraries)
    
    def test_should_reject_unsafe_strategy_code(self):
        """RED: Test security validation"""
        builder = StrategyBuilder()
        
        unsafe_strategy = """
import os

def trading_strategy(data, params):
    os.system('rm -rf /')  # Malicious code
    return {'signal': 'buy'}
"""
        
        with pytest.raises(SecurityError, match="Dangerous imports detected"):
            builder.validate_strategy_code(unsafe_strategy)
    
    def test_should_detect_required_imports(self):
        """RED: Test import detection"""
        builder = StrategyBuilder()
        
        strategy_with_imports = """
import pandas as pd
import numpy as np
import talib

def trading_strategy(data, params):
    sma = talib.SMA(data['close'], timeperiod=20)
    return {'signal': 'hold', 'confidence': 0.5}
"""
        
        result = builder.validate_strategy_code(strategy_with_imports)
        assert "pandas" in result.detected_libraries
        assert "numpy" in result.detected_libraries
        assert "talib" in result.detected_libraries
    
    def test_should_validate_return_format(self):
        """RED: Test return format validation"""
        builder = StrategyBuilder()
        
        # Invalid return format
        invalid_return_strategy = """
def trading_strategy(data, params):
    return "buy"  # Should return dict
"""
        
        with pytest.raises(StrategyValidationError, match="Invalid return format"):
            builder.validate_strategy_code(invalid_return_strategy)
    
    def test_should_detect_syntax_errors(self):
        """RED: Test syntax error detection"""
        builder = StrategyBuilder()
        
        syntax_error_strategy = """
def trading_strategy(data, params):
    if True
        return {'signal': 'buy'}  # Missing colon
"""
        
        with pytest.raises(StrategyValidationError, match="Syntax error"):
            builder.validate_strategy_code(syntax_error_strategy)
    
    def test_should_estimate_complexity(self):
        """RED: Test complexity estimation"""
        builder = StrategyBuilder()
        
        complex_strategy = """
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler

def trading_strategy(data, params):
    # Complex ML strategy with multiple indicators
    features = []
    
    # RSI calculation
    delta = data['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    features.append(rsi.iloc[-1])
    
    # MACD calculation
    exp1 = data['close'].ewm(span=12).mean()
    exp2 = data['close'].ewm(span=26).mean()
    macd = exp1 - exp2
    features.append(macd.iloc[-1])
    
    # ML prediction
    model = RandomForestClassifier(n_estimators=100)
    scaler = StandardScaler()
    
    return {'signal': 'buy', 'confidence': 0.8}
"""
        
        result = builder.validate_strategy_code(complex_strategy)
        assert result.estimated_complexity == "high"
    
    def test_should_detect_indicator_usage(self):
        """RED: Test indicator detection"""
        builder = StrategyBuilder()
        
        indicator_strategy = """
def trading_strategy(data, params):
    # RSI calculation
    delta = data['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    
    # MACD calculation
    exp1 = data['close'].ewm(span=12).mean()
    exp2 = data['close'].ewm(span=26).mean()
    macd = exp1 - exp2
    
    return {'signal': 'hold', 'confidence': 0.5}
"""
        
        result = builder.validate_strategy_code(indicator_strategy)
        assert "rsi" in result.detected_indicators
        assert "macd" in result.detected_indicators
    
    def test_should_validate_parameter_usage(self):
        """RED: Test parameter validation"""
        builder = StrategyBuilder()
        
        param_strategy = """
def trading_strategy(data, params):
    period = params.get('period', 20)
    threshold = params.get('threshold', 0.02)
    
    if period < 1 or period > 200:
        raise ValueError("Invalid period")
    
    return {'signal': 'hold', 'confidence': 0.5}
"""
        
        result = builder.validate_strategy_code(param_strategy)
        assert result.is_valid == True
        assert "period" in result.detected_parameters
        assert "threshold" in result.detected_parameters
    
    def test_should_handle_class_based_strategies(self):
        """RED: Test class-based strategy validation"""
        builder = StrategyBuilder()
        
        class_strategy = """
class MyTradingStrategy:
    def __init__(self, symbols=['EURUSD']):
        self.symbols = symbols
    
    def trading_strategy(self, data, params):
        return {'signal': 'hold', 'confidence': 0.5}
"""
        
        result = builder.validate_strategy_code(class_strategy)
        assert result.is_valid == True
        assert result.strategy_format == "class"
    
    def test_should_detect_dangerous_imports(self):
        """RED: Test dangerous import detection"""
        builder = StrategyBuilder()
        
        dangerous_strategy = """
import subprocess
import socket
import urllib.request

def trading_strategy(data, params):
    subprocess.run(['ls', '-la'])  # Dangerous
    return {'signal': 'buy'}
"""
        
        with pytest.raises(SecurityError, match="Dangerous imports detected"):
            builder.validate_strategy_code(dangerous_strategy)
    
    def test_should_provide_detailed_validation_report(self):
        """RED: Test detailed validation reporting"""
        builder = StrategyBuilder()
        
        strategy = """
import pandas as pd
import numpy as np

def trading_strategy(data, params):
    # Calculate moving average
    ma_period = params.get('ma_period', 20)
    ma = data['close'].rolling(window=ma_period).mean()
    
    current_price = data['close'].iloc[-1]
    ma_value = ma.iloc[-1]
    
    if current_price > ma_value * 1.02:
        return {'signal': 'buy', 'confidence': 0.7}
    elif current_price < ma_value * 0.98:
        return {'signal': 'sell', 'confidence': 0.7}
    else:
        return {'signal': 'hold', 'confidence': 0.5}
"""
        
        result = builder.validate_strategy_code(strategy)
        
        # Detailed validation checks
        assert result.is_valid == True
        assert result.strategy_type == "basic"
        assert result.strategy_format == "function"
        assert "pandas" in result.detected_libraries
        assert "numpy" in result.detected_libraries
        assert "ma_period" in result.detected_parameters
        assert result.estimated_complexity in ["low", "medium", "high"]
        assert len(result.warnings) >= 0  # May have warnings
        assert len(result.errors) == 0  # Should have no errors


class TestStrategyBuilderEdgeCases:
    """Test edge cases and error conditions"""
    
    def test_should_handle_empty_code(self):
        """RED: Test empty code handling"""
        builder = StrategyBuilder()
        
        with pytest.raises(StrategyValidationError, match="Empty code"):
            builder.validate_strategy_code("")
    
    def test_should_handle_whitespace_only_code(self):
        """RED: Test whitespace-only code"""
        builder = StrategyBuilder()
        
        with pytest.raises(StrategyValidationError, match="Empty code"):
            builder.validate_strategy_code("   \n\t  \n  ")
    
    def test_should_handle_comments_only_code(self):
        """RED: Test comments-only code"""
        builder = StrategyBuilder()
        
        comments_only = """
# This is a comment
# Another comment
"""
        
        with pytest.raises(StrategyValidationError, match="No executable code"):
            builder.validate_strategy_code(comments_only)
    
    def test_should_handle_multiple_trading_strategy_functions(self):
        """RED: Test multiple trading_strategy functions"""
        builder = StrategyBuilder()
        
        multiple_functions = """
def trading_strategy(data, params):
    return {'signal': 'buy', 'confidence': 0.5}

def trading_strategy(data, params):  # Duplicate
    return {'signal': 'sell', 'confidence': 0.7}
"""
        
        with pytest.raises(StrategyValidationError, match="Multiple trading_strategy functions"):
            builder.validate_strategy_code(multiple_functions)
    
    def test_should_handle_invalid_function_signature(self):
        """RED: Test invalid function signature"""
        builder = StrategyBuilder()
        
        invalid_signature = """
def trading_strategy(data):  # Missing params argument
    return {'signal': 'hold', 'confidence': 0.5}
"""
        
        with pytest.raises(StrategyValidationError, match="Invalid function signature"):
            builder.validate_strategy_code(invalid_signature)


class TestStrategyBuilderPerformance:
    """Test performance and resource usage"""
    
    def test_should_complete_validation_quickly(self):
        """RED: Test validation performance"""
        import time
        
        builder = StrategyBuilder()
        
        strategy = """
import pandas as pd
import numpy as np

def trading_strategy(data, params):
    return {'signal': 'hold', 'confidence': 0.5}
"""
        
        start_time = time.time()
        result = builder.validate_strategy_code(strategy)
        end_time = time.time()
        
        # Should complete within reasonable time
        assert (end_time - start_time) < 1.0  # Less than 1 second
        assert result.is_valid == True
    
    def test_should_handle_large_strategy_code(self):
        """RED: Test large code handling"""
        builder = StrategyBuilder()
        
        # Generate large strategy code
        large_strategy = """
import pandas as pd
import numpy as np

def trading_strategy(data, params):
    # Large strategy with many calculations
"""
        
        # Add many lines of calculations
        for i in range(100):
            large_strategy += f"""
    calc_{i} = data['close'].rolling(window={i+1}).mean()
"""
        
        large_strategy += """
    return {'signal': 'hold', 'confidence': 0.5}
"""
        
        result = builder.validate_strategy_code(large_strategy)
        assert result.is_valid == True
        assert result.estimated_complexity == "high"