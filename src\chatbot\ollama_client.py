"""
Ollama Integration Service
Provides LLM-powered chatbot functionality using local Ollama models
"""

import asyncio
import json
import logging
import aiohttp
from typing import Dict, List, Optional, Any, AsyncGenerator
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class OllamaConfig:
    """Ollama configuration settings"""
    base_url: str = "http://localhost:11434"
    model: str = "llama3.1:8b"  # Default model
    temperature: float = 0.7
    max_tokens: int = 2048
    timeout: int = 30
    stream: bool = True
    
@dataclass 
class ChatMessage:
    """Chat message structure"""
    role: str  # 'system', 'user', 'assistant'
    content: str
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

@dataclass
class OllamaResponse:
    """Ollama API response structure"""
    content: str
    model: str
    done: bool
    total_duration: Optional[int] = None
    load_duration: Optional[int] = None
    prompt_eval_count: Optional[int] = None
    prompt_eval_duration: Optional[int] = None
    eval_count: Optional[int] = None
    eval_duration: Optional[int] = None

class OllamaClient:
    """Ollama API client for LLM interactions"""
    
    def __init__(self, config: OllamaConfig = None):
        self.config = config or OllamaConfig()
        self.session: Optional[aiohttp.ClientSession] = None
        self.conversation_history: List[ChatMessage] = []
        
        # System prompt for trading strategy generation
        self.system_prompt = """You are an expert trading strategy assistant specializing in algorithmic trading and Python code generation. Your role is to:

1. Understand trading strategy requirements from natural language descriptions
2. Generate complete, executable Python trading strategies
3. Provide clear explanations of strategy logic and risk management
4. Suggest improvements and optimizations
5. Answer questions about technical analysis, indicators, and trading concepts

Key guidelines:
- Always generate complete, runnable Python code for trading strategies
- Include proper risk management (stop-loss, take-profit, position sizing)
- Use popular libraries like pandas, numpy, talib for technical indicators
- Provide clear comments and documentation in code
- Explain strategy logic in simple terms
- Include backtesting capabilities when possible
- Be cautious about risk and emphasize proper testing

Available strategy types:
- Mean Reversion (RSI, Bollinger Bands)
- Momentum (MACD, Moving Averages) 
- Machine Learning (Random Forest, Neural Networks)
- Breakout (Support/Resistance, Volatility)
- Grid Trading
- Pairs Trading

Always respond with both explanation and complete Python code."""

    async def __aenter__(self):
        """Async context manager entry"""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.disconnect()
    
    async def connect(self):
        """Initialize connection to Ollama"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.config.timeout)
        )
        
        # Test connection
        try:
            await self.health_check()
            logger.info(f"Connected to Ollama at {self.config.base_url}")
        except Exception as e:
            logger.error(f"Failed to connect to Ollama: {e}")
            raise ConnectionError(f"Cannot connect to Ollama at {self.config.base_url}")
    
    async def disconnect(self):
        """Close connection"""
        if self.session:
            await self.session.close()
            self.session = None
    
    async def health_check(self) -> bool:
        """Check if Ollama is running and accessible"""
        try:
            async with self.session.get(f"{self.config.base_url}/api/tags") as response:
                if response.status == 200:
                    data = await response.json()
                    models = [model['name'] for model in data.get('models', [])]
                    logger.info(f"Available models: {models}")
                    
                    # Check if our configured model is available
                    if self.config.model not in models:
                        logger.warning(f"Model {self.config.model} not found. Available: {models}")
                        if models:
                            self.config.model = models[0]
                            logger.info(f"Switched to available model: {self.config.model}")
                    
                    return True
                return False
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False
    
    async def pull_model(self, model_name: str = None) -> bool:
        """Pull/download a model if not available"""
        model = model_name or self.config.model
        
        try:
            payload = {"name": model}
            
            async with self.session.post(
                f"{self.config.base_url}/api/pull",
                json=payload
            ) as response:
                if response.status == 200:
                    # Stream the download progress
                    async for line in response.content:
                        if line:
                            data = json.loads(line.decode())
                            if 'status' in data:
                                logger.info(f"Pull progress: {data['status']}")
                            if data.get('status') == 'success':
                                logger.info(f"Successfully pulled model: {model}")
                                return True
                return False
        except Exception as e:
            logger.error(f"Failed to pull model {model}: {e}")
            return False
    
    async def chat_completion(
        self, 
        messages: List[ChatMessage],
        stream: bool = None
    ) -> AsyncGenerator[OllamaResponse, None]:
        """Send chat completion request to Ollama"""
        
        if not self.session:
            await self.connect()
        
        # Prepare messages for Ollama API
        ollama_messages = []
        
        # Add system message
        ollama_messages.append({
            "role": "system",
            "content": self.system_prompt
        })
        
        # Add conversation messages
        for msg in messages:
            ollama_messages.append({
                "role": msg.role,
                "content": msg.content
            })
        
        payload = {
            "model": self.config.model,
            "messages": ollama_messages,
            "stream": stream if stream is not None else self.config.stream,
            "options": {
                "temperature": self.config.temperature,
                "num_predict": self.config.max_tokens
            }
        }
        
        try:
            async with self.session.post(
                f"{self.config.base_url}/api/chat",
                json=payload
            ) as response:
                
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"Ollama API error: {response.status} - {error_text}")
                
                if payload["stream"]:
                    # Handle streaming response
                    full_content = ""
                    async for line in response.content:
                        if line:
                            try:
                                data = json.loads(line.decode())
                                
                                content = data.get('message', {}).get('content', '')
                                full_content += content
                                
                                yield OllamaResponse(
                                    content=full_content,
                                    model=data.get('model', self.config.model),
                                    done=data.get('done', False),
                                    total_duration=data.get('total_duration'),
                                    load_duration=data.get('load_duration'),
                                    prompt_eval_count=data.get('prompt_eval_count'),
                                    prompt_eval_duration=data.get('prompt_eval_duration'),
                                    eval_count=data.get('eval_count'),
                                    eval_duration=data.get('eval_duration')
                                )
                                
                                if data.get('done', False):
                                    break
                                    
                            except json.JSONDecodeError:
                                continue
                else:
                    # Handle non-streaming response
                    data = await response.json()
                    content = data.get('message', {}).get('content', '')
                    
                    yield OllamaResponse(
                        content=content,
                        model=data.get('model', self.config.model),
                        done=True,
                        total_duration=data.get('total_duration'),
                        load_duration=data.get('load_duration'),
                        prompt_eval_count=data.get('prompt_eval_count'),
                        prompt_eval_duration=data.get('prompt_eval_duration'),
                        eval_count=data.get('eval_count'),
                        eval_duration=data.get('eval_duration')
                    )
                    
        except Exception as e:
            logger.error(f"Chat completion failed: {e}")
            raise
    
    async def simple_chat(self, user_message: str) -> str:
        """Simple chat interface for single messages"""
        
        messages = [ChatMessage(role="user", content=user_message)]
        
        full_response = ""
        async for response in self.chat_completion(messages, stream=False):
            full_response = response.content
            break
        
        return full_response
    
    def add_to_conversation(self, message: ChatMessage):
        """Add message to conversation history"""
        self.conversation_history.append(message)
        
        # Keep conversation history manageable (last 20 messages)
        if len(self.conversation_history) > 20:
            self.conversation_history = self.conversation_history[-20:]
    
    async def chat_with_history(self, user_message: str) -> str:
        """Chat with conversation history"""
        
        # Add user message to history
        user_msg = ChatMessage(role="user", content=user_message)
        self.add_to_conversation(user_msg)
        
        # Get response from Ollama
        full_response = ""
        async for response in self.chat_completion(self.conversation_history, stream=False):
            full_response = response.content
            break
        
        # Add assistant response to history
        assistant_msg = ChatMessage(role="assistant", content=full_response)
        self.add_to_conversation(assistant_msg)
        
        return full_response
    
    def clear_conversation(self):
        """Clear conversation history"""
        self.conversation_history.clear()
    
    async def get_available_models(self) -> List[str]:
        """Get list of available models"""
        try:
            async with self.session.get(f"{self.config.base_url}/api/tags") as response:
                if response.status == 200:
                    data = await response.json()
                    return [model['name'] for model in data.get('models', [])]
                return []
        except Exception as e:
            logger.error(f"Failed to get models: {e}")
            return []
