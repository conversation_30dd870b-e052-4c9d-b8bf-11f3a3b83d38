#!/usr/bin/env python3
"""
🛡️ Data Validation & Audit Layer - Interactive Demonstration

This script demonstrates the comprehensive data validation and audit trail capabilities
of the AI Enhanced Trading Platform's Data Validation & Audit Layer.

Features demonstrated:
- OHLC data validation with comprehensive rules
- Data integrity verification with SHA-256 hashing
- Complete audit trail with detailed logging
- Source verification and management
- Property-based testing integration
- Performance benchmarking
- Error handling and recovery
"""

import sys
import os
import time
import json
from datetime import datetime, timedelta
from decimal import Decimal
from typing import List, Dict, Any
import random

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from validation.data_validator import (
    DataValidator, 
    DataSourceManager, 
    OHLCData, 
    DataValidationError,
    ValidationSeverity,
    ValidationResult,
    AuditLogEntry
)

class DataValidationDemo:
    """Interactive demonstration of the Data Validation & Audit Layer"""
    
    def __init__(self):
        self.validator = DataValidator(enable_audit_trail=True)
        self.source_manager = DataSourceManager(enable_audit_trail=True)
        self.demo_data: List[OHLCData] = []
        
        print("🛡️ Data Validation & Audit Layer - Interactive Demo")
        print("=" * 60)
        print()
    
    def generate_sample_data(self, count: int = 10) -> List[OHLCData]:
        """Generate sample OHLC data for demonstration"""
        print(f"📊 Generating {count} sample OHLC records...")
        
        sample_data = []
        base_time = datetime.now() - timedelta(hours=count)
        
        for i in range(count):
            # Generate realistic forex-like data
            base_price = random.uniform(1.0, 2.0)
            spread = base_price * random.uniform(0.001, 0.01)  # 0.1% to 1% spread
            
            low = base_price - spread/2
            high = base_price + spread/2
            open_price = random.uniform(low, high)
            close_price = random.uniform(low, high)
            
            data = OHLCData(
                timestamp=base_time + timedelta(minutes=i),
                open=Decimal(str(round(open_price, 5))),
                high=Decimal(str(round(high, 5))),
                low=Decimal(str(round(low, 5))),
                close=Decimal(str(round(close_price, 5))),
                volume=random.randint(100, 10000),
                source=random.choice(["dukascopy", "forexsb", "test_source"]),
                hash="",
                symbol=random.choice(["EURUSD", "GBPUSD", "USDJPY", "AUDUSD"]),
                timeframe=random.choice(["M1", "M5", "M15", "H1"])
            )
            
            # Generate hash for integrity verification
            data.hash = self.validator.generate_data_hash(data)
            sample_data.append(data)
        
        print(f"✅ Generated {len(sample_data)} OHLC records")
        return sample_data
    
    def demonstrate_basic_validation(self):
        """Demonstrate basic OHLC validation"""
        print("\n🔍 BASIC VALIDATION DEMONSTRATION")
        print("-" * 40)
        
        # Generate sample data
        self.demo_data = self.generate_sample_data(5)
        
        print("\n📋 Validating OHLC records:")
        for i, data in enumerate(self.demo_data, 1):
            try:
                is_valid = self.validator.validate_ohlc(data)
                print(f"  {i}. {data.symbol} {data.timeframe} @ {data.timestamp.strftime('%H:%M:%S')} - ✅ VALID")
                print(f"     OHLC: {data.open}/{data.high}/{data.low}/{data.close}, Vol: {data.volume}")
            except DataValidationError as e:
                print(f"  {i}. {data.symbol} {data.timeframe} @ {data.timestamp.strftime('%H:%M:%S')} - ❌ INVALID")
                print(f"     Error: {e}")
    
    def demonstrate_detailed_validation(self):
        """Demonstrate detailed validation with results breakdown"""
        print("\n🔬 DETAILED VALIDATION DEMONSTRATION")
        print("-" * 40)
        
        if not self.demo_data:
            self.demo_data = self.generate_sample_data(3)
        
        print("\n📊 Detailed validation results:")
        for i, data in enumerate(self.demo_data[:3], 1):
            print(f"\n  Record {i}: {data.symbol} {data.timeframe}")
            
            is_valid, results = self.validator.validate_ohlc_detailed(data)
            print(f"  Overall Result: {'✅ VALID' if is_valid else '❌ INVALID'}")
            
            for result in results:
                status_icon = "✅" if result.is_valid else "❌"
                severity_icon = {
                    ValidationSeverity.INFO: "ℹ️",
                    ValidationSeverity.WARNING: "⚠️",
                    ValidationSeverity.ERROR: "❌",
                    ValidationSeverity.CRITICAL: "🚨"
                }.get(result.severity, "❓")
                
                print(f"    {status_icon} {severity_icon} {result.field}: {result.message}")
    
    def demonstrate_invalid_data_detection(self):
        """Demonstrate detection of invalid OHLC data"""
        print("\n🚨 INVALID DATA DETECTION DEMONSTRATION")
        print("-" * 40)
        
        print("\n🧪 Creating intentionally invalid OHLC data:")
        
        # Create various types of invalid data
        invalid_cases = [
            {
                "name": "High < Low",
                "data": OHLCData(
                    timestamp=datetime.now(),
                    open=Decimal('1.2000'),
                    high=Decimal('1.1900'),  # Invalid: high < low
                    low=Decimal('1.1950'),
                    close=Decimal('1.2020'),
                    volume=1000,
                    source="test_source",
                    hash="",
                    symbol="EURUSD",
                    timeframe="M1"
                )
            },
            {
                "name": "Open outside range",
                "data": OHLCData(
                    timestamp=datetime.now(),
                    open=Decimal('1.2100'),  # Invalid: open > high
                    high=Decimal('1.2050'),
                    low=Decimal('1.1950'),
                    close=Decimal('1.2020'),
                    volume=1000,
                    source="test_source",
                    hash="",
                    symbol="EURUSD",
                    timeframe="M1"
                )
            },
            {
                "name": "Future timestamp",
                "data": OHLCData(
                    timestamp=datetime.now() + timedelta(days=1),  # Invalid: future
                    open=Decimal('1.2000'),
                    high=Decimal('1.2050'),
                    low=Decimal('1.1950'),
                    close=Decimal('1.2020'),
                    volume=1000,
                    source="test_source",
                    hash="",
                    symbol="EURUSD",
                    timeframe="M1"
                )
            },
            {
                "name": "Negative volume",
                "data": OHLCData(
                    timestamp=datetime.now(),
                    open=Decimal('1.2000'),
                    high=Decimal('1.2050'),
                    low=Decimal('1.1950'),
                    close=Decimal('1.2020'),
                    volume=-100,  # Invalid: negative volume
                    source="test_source",
                    hash="",
                    symbol="EURUSD",
                    timeframe="M1"
                )
            }
        ]
        
        for case in invalid_cases:
            print(f"\n  Testing: {case['name']}")
            try:
                self.validator.validate_ohlc(case['data'])
                print(f"    ⚠️  Unexpected: Validation passed (should have failed)")
            except DataValidationError as e:
                print(f"    ✅ Correctly detected invalid data: {e}")
    
    def demonstrate_data_integrity(self):
        """Demonstrate data integrity verification"""
        print("\n🔐 DATA INTEGRITY DEMONSTRATION")
        print("-" * 40)
        
        if not self.demo_data:
            self.demo_data = self.generate_sample_data(3)
        
        print("\n🛡️ Testing data integrity verification:")
        
        for i, data in enumerate(self.demo_data[:3], 1):
            print(f"\n  Record {i}: {data.symbol} {data.timeframe}")
            
            # Test original data integrity
            original_integrity = self.validator.verify_integrity(data)
            print(f"    Original data integrity: {'✅ VALID' if original_integrity else '❌ COMPROMISED'}")
            
            # Tamper with data and test detection
            original_close = data.close
            data.close = Decimal('9.9999')  # Tamper with close price
            
            tampered_integrity = self.validator.verify_integrity(data)
            print(f"    After tampering: {'✅ VALID' if tampered_integrity else '❌ COMPROMISED (correctly detected)'}")
            
            # Restore original data
            data.close = original_close
            
            # Regenerate hash for restored data
            data.hash = self.validator.generate_data_hash(data)
            restored_integrity = self.validator.verify_integrity(data)
            print(f"    After restoration: {'✅ VALID' if restored_integrity else '❌ COMPROMISED'}")
    
    def demonstrate_source_verification(self):
        """Demonstrate data source verification"""
        print("\n🌐 SOURCE VERIFICATION DEMONSTRATION")
        print("-" * 40)
        
        print("\n📡 Testing data source verification:")
        
        test_sources = [
            ("dukascopy", "EURUSD_M1.csv", True),
            ("forexsb", "historical_data.txt", True),
            ("test_source", "sample.json", True),
            ("unknown_source", "data.csv", False),
            ("untrusted_feed", "suspicious.dat", False)
        ]
        
        for source, filename, expected in test_sources:
            result = self.source_manager.verify_source_authenticity(source, filename)
            status = "✅ TRUSTED" if result else "❌ UNTRUSTED"
            expectation = "✅ Expected" if result == expected else "⚠️ Unexpected"
            
            print(f"    {source:15} | {filename:20} | {status:12} | {expectation}")
        
        print("\n📊 Source verification statistics:")
        stats = self.source_manager.get_source_statistics()
        print(f"    Total sources configured: {stats['total_sources']}")
        print(f"    Total verifications: {stats['total_verifications']}")
        print(f"    Success rate: {stats['success_rate']:.1f}%")
        print(f"    Cache size: {stats['cache_size']}")
    
    def demonstrate_audit_trail(self):
        """Demonstrate audit trail functionality"""
        print("\n📋 AUDIT TRAIL DEMONSTRATION")
        print("-" * 40)
        
        print("\n📝 Audit trail entries (last 10):")
        
        # Get recent audit entries
        recent_entries = self.validator.get_audit_log()[-10:]
        
        if not recent_entries:
            print("    No audit entries found. Running some operations...")
            # Generate some audit entries
            sample_data = self.generate_sample_data(2)
            for data in sample_data:
                try:
                    self.validator.validate_ohlc(data)
                except DataValidationError:
                    pass
            recent_entries = self.validator.get_audit_log()[-5:]
        
        for i, entry in enumerate(recent_entries, 1):
            print(f"\n  {i}. Operation: {entry.operation}")
            print(f"     Timestamp: {entry.timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"     Source: {entry.source}")
            print(f"     Data Hash: {entry.data_hash[:16]}...")
            if entry.metadata:
                print(f"     Metadata: {json.dumps(entry.metadata, indent=8, default=str)}")
        
        print("\n📊 Validation statistics:")
        stats = self.validator.get_validation_statistics()
        if stats['total_validations'] > 0:
            print(f"    Total validations: {stats['total_validations']}")
            print(f"    Passed: {stats['passed']}")
            print(f"    Failed: {stats['failed']}")
            print(f"    Success rate: {stats['success_rate']:.1f}%")
            
            if stats['most_common_failures']:
                print(f"    Most common failures:")
                for field, count in stats['most_common_failures']:
                    print(f"      - {field}: {count} times")
        else:
            print("    No validation statistics available yet")
    
    def demonstrate_batch_processing(self):
        """Demonstrate batch processing capabilities"""
        print("\n⚡ BATCH PROCESSING DEMONSTRATION")
        print("-" * 40)
        
        print("\n🔄 Processing large batch of OHLC data...")
        
        # Generate larger dataset
        batch_size = 50
        batch_data = self.generate_sample_data(batch_size)
        
        # Add some intentionally invalid records
        invalid_indices = [10, 25, 40]
        for idx in invalid_indices:
            if idx < len(batch_data):
                # Make high < low to create invalid data
                batch_data[idx].high = batch_data[idx].low - Decimal('0.0001')
        
        print(f"📦 Processing batch of {len(batch_data)} records...")
        
        # Process batch and measure performance
        start_time = time.time()
        
        valid_count = 0
        invalid_count = 0
        errors = []
        
        for i, data in enumerate(batch_data):
            try:
                self.validator.validate_ohlc(data)
                valid_count += 1
            except DataValidationError as e:
                invalid_count += 1
                errors.append((i, str(e)))
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"\n📊 Batch processing results:")
        print(f"    Total records: {len(batch_data)}")
        print(f"    Valid records: {valid_count}")
        print(f"    Invalid records: {invalid_count}")
        print(f"    Success rate: {(valid_count/len(batch_data))*100:.1f}%")
        print(f"    Processing time: {processing_time:.3f} seconds")
        print(f"    Records per second: {len(batch_data)/processing_time:.1f}")
        
        if errors:
            print(f"\n❌ Validation errors found:")
            for idx, error in errors[:5]:  # Show first 5 errors
                print(f"    Record {idx+1}: {error}")
            if len(errors) > 5:
                print(f"    ... and {len(errors)-5} more errors")
    
    def demonstrate_performance_benchmarks(self):
        """Demonstrate performance benchmarking"""
        print("\n⚡ PERFORMANCE BENCHMARKS")
        print("-" * 40)
        
        print("\n🏃 Running performance benchmarks...")
        
        # Generate test data
        test_data = self.generate_sample_data(100)
        
        benchmarks = {}
        
        # Benchmark validation
        print("  📊 Benchmarking validation...")
        start_time = time.time()
        for data in test_data:
            try:
                self.validator.validate_ohlc(data)
            except DataValidationError:
                pass
        benchmarks['validation'] = time.time() - start_time
        
        # Benchmark hash generation
        print("  🔐 Benchmarking hash generation...")
        start_time = time.time()
        for data in test_data:
            self.validator.generate_data_hash(data)
        benchmarks['hash_generation'] = time.time() - start_time
        
        # Benchmark integrity verification
        print("  🛡️ Benchmarking integrity verification...")
        start_time = time.time()
        for data in test_data:
            self.validator.verify_integrity(data)
        benchmarks['integrity_verification'] = time.time() - start_time
        
        # Benchmark source verification
        print("  🌐 Benchmarking source verification...")
        start_time = time.time()
        for data in test_data:
            self.source_manager.verify_source_authenticity(data.source, "test.csv")
        benchmarks['source_verification'] = time.time() - start_time
        
        print(f"\n📈 Performance Results (100 records):")
        for operation, total_time in benchmarks.items():
            avg_time_ms = (total_time / len(test_data)) * 1000
            records_per_sec = len(test_data) / total_time
            print(f"    {operation.replace('_', ' ').title():25}: {avg_time_ms:.2f}ms/record, {records_per_sec:.0f} records/sec")
    
    def demonstrate_export_functionality(self):
        """Demonstrate audit log export functionality"""
        print("\n💾 EXPORT FUNCTIONALITY DEMONSTRATION")
        print("-" * 40)
        
        print("\n📤 Exporting audit logs...")
        
        # Ensure we have some audit data
        if not self.validator.audit_log:
            print("  Generating audit data...")
            sample_data = self.generate_sample_data(5)
            for data in sample_data:
                try:
                    self.validator.validate_ohlc(data)
                except DataValidationError:
                    pass
        
        # Export to JSON
        json_filename = "demo_audit_log.json"
        try:
            self.validator.export_audit_log(json_filename, format="json")
            print(f"  ✅ Exported audit log to {json_filename}")
            
            # Show file size
            if os.path.exists(json_filename):
                file_size = os.path.getsize(json_filename)
                print(f"     File size: {file_size} bytes")
        except Exception as e:
            print(f"  ❌ Failed to export JSON: {e}")
        
        # Export to CSV
        csv_filename = "demo_audit_log.csv"
        try:
            self.validator.export_audit_log(csv_filename, format="csv")
            print(f"  ✅ Exported audit log to {csv_filename}")
            
            # Show file size
            if os.path.exists(csv_filename):
                file_size = os.path.getsize(csv_filename)
                print(f"     File size: {file_size} bytes")
        except Exception as e:
            print(f"  ❌ Failed to export CSV: {e}")
        
        print(f"\n📊 Export summary:")
        print(f"    Total audit entries: {len(self.validator.audit_log)}")
        print(f"    JSON export: {'✅ Success' if os.path.exists(json_filename) else '❌ Failed'}")
        print(f"    CSV export: {'✅ Success' if os.path.exists(csv_filename) else '❌ Failed'}")
    
    def run_interactive_demo(self):
        """Run the complete interactive demonstration"""
        print("🚀 Starting comprehensive demonstration...\n")
        
        try:
            # Run all demonstrations
            self.demonstrate_basic_validation()
            self.demonstrate_detailed_validation()
            self.demonstrate_invalid_data_detection()
            self.demonstrate_data_integrity()
            self.demonstrate_source_verification()
            self.demonstrate_audit_trail()
            self.demonstrate_batch_processing()
            self.demonstrate_performance_benchmarks()
            self.demonstrate_export_functionality()
            
            print("\n" + "=" * 60)
            print("🎉 DEMONSTRATION COMPLETE!")
            print("=" * 60)
            
            # Final summary
            print("\n📊 Final Statistics:")
            validation_stats = self.validator.get_validation_statistics()
            source_stats = self.source_manager.get_source_statistics()
            
            print(f"  Total validations performed: {validation_stats.get('total_validations', 0)}")
            print(f"  Validation success rate: {validation_stats.get('success_rate', 0):.1f}%")
            print(f"  Total source verifications: {source_stats.get('total_verifications', 0)}")
            print(f"  Source verification success rate: {source_stats.get('success_rate', 0):.1f}%")
            print(f"  Audit log entries: {len(self.validator.audit_log)}")
            print(f"  Source audit entries: {len(self.source_manager.source_audit_log)}")
            
            print("\n✨ The Data Validation & Audit Layer is working perfectly!")
            print("   All OHLC data validation, integrity checks, and audit trails")
            print("   are functioning as expected with comprehensive coverage.")
            
        except Exception as e:
            print(f"\n❌ Demo encountered an error: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            # Cleanup demo files
            for filename in ["demo_audit_log.json", "demo_audit_log.csv"]:
                if os.path.exists(filename):
                    try:
                        os.remove(filename)
                        print(f"🧹 Cleaned up {filename}")
                    except:
                        pass

def main():
    """Main entry point for the demonstration"""
    print("🛡️ AI Enhanced Trading Platform")
    print("Data Validation & Audit Layer - Interactive Demonstration")
    print("=" * 70)
    print()
    print("This demonstration showcases:")
    print("  ✅ Comprehensive OHLC data validation")
    print("  ✅ Data integrity verification with SHA-256 hashing")
    print("  ✅ Complete audit trail with detailed logging")
    print("  ✅ Source verification and authentication")
    print("  ✅ Performance benchmarking and optimization")
    print("  ✅ Batch processing capabilities")
    print("  ✅ Export functionality for audit logs")
    print()
    
    demo = DataValidationDemo()
    demo.run_interactive_demo()

if __name__ == "__main__":
    main()