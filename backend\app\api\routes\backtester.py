import pandas as pd
import numpy as np
from typing import Dict, Any

class BacktestValidationError(Exception):
    pass

class Backtester:
    def __init__(self, initial_capital=10000):
        self.initial_capital = initial_capital
    
    def run_backtest(self, strategy_code: str, data: pd.DataFrame) -> Dict[str, Any]:
        self._validate_backtest_data(data)
        
        # Execute strategy code safely
        strategy = self._compile_strategy(strategy_code)
        
        # Run backtest logic
        results = self._execute_backtest(strategy, data)
        
        return results
    
    def _validate_backtest_data(self, data: pd.DataFrame):
        if len(data) < 2:
            raise BacktestValidationError("Insufficient data for backtesting")
        
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in required_columns:
            if col not in data.columns:
                raise BacktestValidationError(f"Missing required column: {col}")
    
    def _compile_strategy(self, code: str):
        # Use RestrictedPython for safe execution
        restricted_globals = RestrictedPython.safe_builtins.copy()
        
        try:
            exec(code, restricted_globals)
            return restricted_globals.get('strategy')
        except Exception as e:
            raise BacktestValidationError(f"Strategy compilation failed: {e}")
    
    def _execute_backtest(self, strategy, data: pd.DataFrame) -> Dict[str, Any]:
        # Simplified backtest execution
        signals = []
        equity_curve = [self.initial_capital]
        
        for i in range(1, len(data)):
            current_data = data.iloc[:i]
            signal = strategy(current_data)
            signals.append(signal)
            
            # Simplified equity calculation
            if signal:
                returns = data['close'].iloc[i] / data['close'].iloc[i-1] - 1
                equity_curve.append(equity_curve[-1] * (1 + returns))
            else:
                equity_curve.append(equity_curve[-1])
        
        return {
            'signals': signals,
            'equity_curve': equity_curve,
            'performance_metrics': self._calculate_metrics(equity_curve)
        }
    
    def _calculate_metrics(self, equity_curve):
        total_return = (equity_curve[-1] / equity_curve[0] - 1) * 100
        return {'total_return': total_return}
