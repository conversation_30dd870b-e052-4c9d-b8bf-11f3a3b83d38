import { TokenService } from './token.service';
import { getMockUser } from '../../test-factories/user.factory';
import jwt from 'jsonwebtoken';

// Mock jsonwebtoken
jest.mock('jsonwebtoken');

describe('TokenService', () => {
  let tokenService: TokenService;
  const mockJwtSecret = 'test-jwt-secret-key-for-testing-purposes-only';
  const mockAccessTokenExpiry = '1h';
  const mockRefreshTokenExpiry = '7d';
  const mockJwt = jest.mocked(jwt);

  beforeEach(() => {
    jest.clearAllMocks();
    tokenService = new TokenService({
      jwtSecret: mockJwtSecret,
      accessTokenExpiry: mockAccessTokenExpiry,
      refreshTokenExpiry: mockRefreshTokenExpiry,
    });
  });

  describe('generateTokens', () => {
    it('should generate access and refresh tokens', async () => {
      // Arrange
      const user = getMockUser();
      const mockAccessToken = 'mock-access-token';
      const mockRefreshToken = 'mock-refresh-token';

      mockJwt.sign
        .mockReturnValueOnce(mockAccessToken)
        .mockReturnValueOnce(mockRefreshToken);

      // Act
      const result = await tokenService.generateTokens(user);

      // Assert
      expect(result).toEqual({
        accessToken: mockAccessToken,
        refreshToken: mockRefreshToken,
        expiresIn: 3600, // 1 hour in seconds
      });

      expect(mockJwt.sign).toHaveBeenCalledTimes(2);
      
      // Check access token generation
      expect(mockJwt.sign).toHaveBeenNthCalledWith(
        1,
        {
          userId: user.id,
          email: user.email,
          subscriptionTier: user.subscriptionTier,
          type: 'access',
        },
        mockJwtSecret,
        { expiresIn: mockAccessTokenExpiry }
      );

      // Check refresh token generation
      expect(mockJwt.sign).toHaveBeenNthCalledWith(
        2,
        {
          userId: user.id,
          type: 'refresh',
        },
        mockJwtSecret,
        { expiresIn: mockRefreshTokenExpiry }
      );
    });

    it('should handle token generation errors', async () => {
      // Arrange
      const user = getMockUser();
      const error = new Error('Token generation failed');
      mockJwt.sign.mockImplementation(() => {
        throw error;
      });

      // Act & Assert
      await expect(tokenService.generateTokens(user)).rejects.toThrow('Token generation failed');
    });
  });

  describe('verifyToken', () => {
    it('should verify valid access token', async () => {
      // Arrange
      const token = 'valid-access-token';
      const payload = {
        userId: 'user_123e4567-e89b-12d3-a456-426614174000',
        email: '<EMAIL>',
        subscriptionTier: 'free',
        type: 'access',
      };
      mockJwt.verify.mockReturnValue(payload as any);

      // Act
      const result = await tokenService.verifyToken(token);

      // Assert
      expect(result).toEqual(payload);
      expect(mockJwt.verify).toHaveBeenCalledWith(token, mockJwtSecret);
    });

    it('should throw on invalid token', async () => {
      // Arrange
      const token = 'invalid-token';
      const error = new Error('Invalid token');
      mockJwt.verify.mockImplementation(() => {
        throw error;
      });

      // Act & Assert
      await expect(tokenService.verifyToken(token)).rejects.toThrow('Invalid token');
    });

    it('should throw on expired token', async () => {
      // Arrange
      const token = 'expired-token';
      const error = new jwt.TokenExpiredError('Token expired', new Date());
      mockJwt.verify.mockImplementation(() => {
        throw error;
      });

      // Act & Assert
      await expect(tokenService.verifyToken(token)).rejects.toThrow('Token expired');
    });

    it('should throw on malformed token', async () => {
      // Arrange
      const token = 'malformed-token';
      const error = new jwt.JsonWebTokenError('Malformed token');
      mockJwt.verify.mockImplementation(() => {
        throw error;
      });

      // Act & Assert
      await expect(tokenService.verifyToken(token)).rejects.toThrow('Invalid token');
    });
  });

  describe('verifyRefreshToken', () => {
    it('should verify valid refresh token', async () => {
      // Arrange
      const token = 'valid-refresh-token';
      const payload = {
        userId: 'user_123e4567-e89b-12d3-a456-426614174000',
        type: 'refresh',
      };
      mockJwt.verify.mockReturnValue(payload as any);

      // Act
      const result = await tokenService.verifyRefreshToken(token);

      // Assert
      expect(result).toEqual(payload);
      expect(mockJwt.verify).toHaveBeenCalledWith(token, mockJwtSecret);
    });

    it('should throw on invalid refresh token type', async () => {
      // Arrange
      const token = 'access-token-instead-of-refresh';
      const payload = {
        userId: 'user_123e4567-e89b-12d3-a456-426614174000',
        type: 'access', // Wrong type
      };
      mockJwt.verify.mockReturnValue(payload as any);

      // Act & Assert
      await expect(tokenService.verifyRefreshToken(token)).rejects.toThrow('Invalid token type');
    });
  });

  describe('extractTokenFromHeader', () => {
    it('should extract token from Bearer header', () => {
      // Arrange
      const authHeader = 'Bearer valid-token';

      // Act
      const result = tokenService.extractTokenFromHeader(authHeader);

      // Assert
      expect(result).toBe('valid-token');
    });

    it('should return null for invalid header format', () => {
      // Arrange
      const authHeader = 'InvalidFormat token';

      // Act
      const result = tokenService.extractTokenFromHeader(authHeader);

      // Assert
      expect(result).toBeNull();
    });

    it('should return null for undefined header', () => {
      // Arrange
      const authHeader = undefined;

      // Act
      const result = tokenService.extractTokenFromHeader(authHeader);

      // Assert
      expect(result).toBeNull();
    });

    it('should return null for empty header', () => {
      // Arrange
      const authHeader = '';

      // Act
      const result = tokenService.extractTokenFromHeader(authHeader);

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('getTokenExpiry', () => {
    it('should calculate expiry time in seconds for access token', () => {
      // Act
      const result = tokenService.getTokenExpiry('access');

      // Assert
      expect(result).toBe(3600); // 1 hour in seconds
    });

    it('should calculate expiry time in seconds for refresh token', () => {
      // Act
      const result = tokenService.getTokenExpiry('refresh');

      // Assert
      expect(result).toBe(604800); // 7 days in seconds
    });
  });
});