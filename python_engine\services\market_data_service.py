"""
Market Data Service Implementation

Concrete implementation of IMarketDataService with dependency injection support.
"""

import asyncio
import yfinance as yf
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass

from core.interfaces import IMarketDataService, ILoggingService, IConfigurationService, MarketData

class YFinanceMarketDataService(IMarketDataService):
    """Yahoo Finance implementation of market data service"""
    
    def __init__(self, 
                 logging_service: ILoggingService,
                 config_service: IConfigurationService):
        self.logger = logging_service
        self.config = config_service
        self._subscribers: Dict[str, List[Callable]] = {}
        self._cache: Dict[str, MarketData] = {}
        self._cache_timeout = self.config.get_config('market_data.cache_timeout', 60)  # seconds
    
    async def get_current_price(self, symbol: str) -> float:
        """Get current price for a symbol"""
        try:
            self.logger.log_info(f"Fetching current price for {symbol}")
            
            # Check cache first
            cached_data = self._get_cached_data(symbol)
            if cached_data:
                return cached_data.close
            
            # Fetch from Yahoo Finance
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            if 'currentPrice' in info:
                price = float(info['currentPrice'])
            elif 'regularMarketPrice' in info:
                price = float(info['regularMarketPrice'])
            else:
                # Fallback to recent history
                hist = ticker.history(period="1d", interval="1m")
                if not hist.empty:
                    price = float(hist['Close'].iloc[-1])
                else:
                    raise ValueError(f"No price data available for {symbol}")
            
            # Cache the result
            market_data = MarketData(
                symbol=symbol,
                timestamp=datetime.now(),
                open=price,
                high=price,
                low=price,
                close=price,
                volume=0
            )
            self._cache[symbol] = market_data
            
            self.logger.log_info(f"Current price for {symbol}: ${price:.2f}")
            return price
            
        except Exception as e:
            self.logger.log_error(f"Error fetching current price for {symbol}: {e}")
            raise
    
    async def get_historical_data(self, symbol: str, period: str, interval: str) -> List[MarketData]:
        """Get historical market data"""
        try:
            self.logger.log_info(f"Fetching historical data for {symbol}, period: {period}, interval: {interval}")
            
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period=period, interval=interval)
            
            if hist.empty:
                self.logger.log_warning(f"No historical data found for {symbol}")
                return []
            
            market_data_list = []
            for timestamp, row in hist.iterrows():
                market_data = MarketData(
                    symbol=symbol,
                    timestamp=timestamp.to_pydatetime(),
                    open=float(row['Open']),
                    high=float(row['High']),
                    low=float(row['Low']),
                    close=float(row['Close']),
                    volume=int(row['Volume'])
                )
                market_data_list.append(market_data)
            
            self.logger.log_info(f"Retrieved {len(market_data_list)} data points for {symbol}")
            return market_data_list
            
        except Exception as e:
            self.logger.log_error(f"Error fetching historical data for {symbol}: {e}")
            raise
    
    async def subscribe_to_updates(self, symbol: str, callback: Callable) -> None:
        """Subscribe to real-time market data updates"""
        if symbol not in self._subscribers:
            self._subscribers[symbol] = []
        
        self._subscribers[symbol].append(callback)
        self.logger.log_info(f"Subscribed to updates for {symbol}")
        
        # Start background task for this symbol if not already running
        asyncio.create_task(self._update_loop(symbol))
    
    async def unsubscribe_from_updates(self, symbol: str) -> None:
        """Unsubscribe from market data updates"""
        if symbol in self._subscribers:
            del self._subscribers[symbol]
            self.logger.log_info(f"Unsubscribed from updates for {symbol}")
    
    async def _update_loop(self, symbol: str):
        """Background loop for real-time updates"""
        update_interval = self.config.get_config('market_data.update_interval', 30)  # seconds
        
        while symbol in self._subscribers:
            try:
                current_price = await self.get_current_price(symbol)
                
                # Notify subscribers
                for callback in self._subscribers.get(symbol, []):
                    try:
                        await callback(symbol, current_price)
                    except Exception as e:
                        self.logger.log_error(f"Error in subscriber callback for {symbol}: {e}")
                
                await asyncio.sleep(update_interval)
                
            except Exception as e:
                self.logger.log_error(f"Error in update loop for {symbol}: {e}")
                await asyncio.sleep(update_interval)
    
    def _get_cached_data(self, symbol: str) -> Optional[MarketData]:
        """Get cached data if still valid"""
        if symbol not in self._cache:
            return None
        
        cached_data = self._cache[symbol]
        age = (datetime.now() - cached_data.timestamp).total_seconds()
        
        if age > self._cache_timeout:
            del self._cache[symbol]
            return None
        
        return cached_data

class MockMarketDataService(IMarketDataService):
    """Mock implementation for testing"""
    
    def __init__(self, 
                 logging_service: ILoggingService,
                 config_service: IConfigurationService):
        self.logger = logging_service
        self.config = config_service
        self._mock_prices: Dict[str, float] = {}
        self._mock_data: Dict[str, List[MarketData]] = {}
        self._subscribers: Dict[str, List[Callable]] = {}
    
    def set_mock_price(self, symbol: str, price: float):
        """Set mock price for testing"""
        self._mock_prices[symbol] = price
    
    def set_mock_data(self, symbol: str, data: List[MarketData]):
        """Set mock historical data for testing"""
        self._mock_data[symbol] = data
    
    def add_historical_data(self, symbol: str, data: MarketData):
        """Add single historical data point"""
        if symbol not in self._mock_data:
            self._mock_data[symbol] = []
        self._mock_data[symbol].append(data)
    
    async def get_current_price(self, symbol: str) -> float:
        """Get mock current price"""
        if symbol in self._mock_prices:
            return self._mock_prices[symbol]
        
        # Default mock price
        return 100.0
    
    async def get_historical_data(self, symbol: str, period: str, interval: str) -> List[MarketData]:
        """Get mock historical data"""
        if symbol in self._mock_data:
            return self._mock_data[symbol]
        
        # Generate default mock data
        mock_data = []
        base_price = 100.0
        
        for i in range(10):
            mock_data.append(MarketData(
                symbol=symbol,
                timestamp=datetime.now() - timedelta(days=i),
                open=base_price + i,
                high=base_price + i + 1,
                low=base_price + i - 1,
                close=base_price + i + 0.5,
                volume=10000
            ))
        
        return mock_data
    
    async def subscribe_to_updates(self, symbol: str, callback: Callable) -> None:
        """Mock subscription"""
        if symbol not in self._subscribers:
            self._subscribers[symbol] = []
        self._subscribers[symbol].append(callback)
    
    async def unsubscribe_from_updates(self, symbol: str) -> None:
        """Mock unsubscription"""
        if symbol in self._subscribers:
            del self._subscribers[symbol]