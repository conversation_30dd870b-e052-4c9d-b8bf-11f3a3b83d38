"""
API Key CRUD operations
"""

import uuid
import secrets
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update, delete

from ..models import <PERSON><PERSON><PERSON>ey

async def get_api_key_by_id(db: AsyncSession, api_key_id: uuid.UUID) -> Optional[ApiKey]:
    """Get API key by ID"""
    result = await db.execute(select(ApiKey).where(ApiKey.id == api_key_id))
    return result.scalars().first()

async def get_api_key_by_key(db: AsyncSession, key: str) -> Optional[ApiKey]:
    """Get API key by key"""
    result = await db.execute(select(ApiKey).where(ApiKey.key == key))
    return result.scalars().first()

async def get_api_keys_by_user(db: AsyncSession, user_id: uuid.UUID) -> List[<PERSON><PERSON><PERSON><PERSON>]:
    """Get all API keys for a user"""
    result = await db.execute(select(ApiKey).where(ApiKey.user_id == user_id))
    return result.scalars().all()

async def create_api_key(
    db: AsyncSession,
    user_id: uuid.UUID,
    name: str,
    expires_in_days: Optional[int] = None
) -> ApiKey:
    """Create a new API key"""
    # Generate a random API key
    key = secrets.token_hex(32)
    
    # Calculate expiration date if provided
    expires_at = None
    if expires_in_days:
        expires_at = datetime.utcnow() + timedelta(days=expires_in_days)
    
    db_api_key = ApiKey(
        user_id=user_id,
        name=name,
        key=key,
        expires_at=expires_at,
        created_at=datetime.utcnow()
    )
    db.add(db_api_key)
    await db.commit()
    await db.refresh(db_api_key)
    return db_api_key

async def update_api_key(
    db: AsyncSession,
    api_key_id: uuid.UUID,
    api_key_data: Dict[str, Any]
) -> Optional[ApiKey]:
    """Update API key"""
    # Update the API key
    await db.execute(
        update(ApiKey)
        .where(ApiKey.id == api_key_id)
        .values(**api_key_data)
    )
    await db.commit()
    
    # Return the updated API key
    return await get_api_key_by_id(db, api_key_id)

async def delete_api_key(db: AsyncSession, api_key_id: uuid.UUID) -> bool:
    """Delete API key"""
    result = await db.execute(delete(ApiKey).where(ApiKey.id == api_key_id))
    await db.commit()
    return result.rowcount > 0

async def validate_api_key(db: AsyncSession, key: str) -> Optional[ApiKey]:
    """Validate API key"""
    api_key = await get_api_key_by_key(db, key)
    if not api_key:
        return None
    
    # Check if the API key is active
    if not api_key.is_active:
        return None
    
    # Check if the API key has expired
    if api_key.expires_at and api_key.expires_at < datetime.utcnow():
        return None
    
    # Update last used timestamp
    api_key.last_used_at = datetime.utcnow()
    await db.commit()
    
    return api_key