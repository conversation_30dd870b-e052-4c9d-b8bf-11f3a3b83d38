{"version": 3, "file": "trading.schemas.js", "sourceRoot": "", "sources": ["trading.schemas.ts"], "names": [], "mappings": ";;;AAAA,6BAAwB;AAExB,qBAAqB;AACR,QAAA,eAAe,GAAG,OAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;AAG1C,QAAA,mBAAmB,GAAG,OAAC,CAAC,IAAI,CAAC;IACxC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ;IACtC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ;CACvC,CAAC,CAAC;AAGH,kDAAkD;AACrC,QAAA,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;IACzC,MAAM,EAAE,2BAAmB;IAC3B,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC7B,UAAU,EAAE,uBAAe;IAC3B,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC5B,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;IAC3C,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;CAC9C,CAAC,CAAC;AAGU,QAAA,iBAAiB,GAAG,OAAC,CAAC,MAAM,CAAC;IACxC,OAAO,EAAE,OAAC,CAAC,OAAO,EAAE;IACpB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;IACrC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CAC7B,CAAC,CAAC;AAGH,kBAAkB;AACL,QAAA,iBAAiB,GAAG,OAAC,CAAC,MAAM,CAAC;IACxC,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE;IACnB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;IAClB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;IAClB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;CACpC,CAAC,CAAC;AAGH,uCAAuC;AAC1B,QAAA,0BAA0B,GAAG,OAAC,CAAC,MAAM,CAAC;IACjD,MAAM,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,cAAc,EAAE,aAAa,EAAE,eAAe,CAAC,CAAC;IAC/E,OAAO,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;IACrC,SAAS,EAAE,OAAC,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;IAC7C,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE;CAC9B,CAAC,CAAC;AAGU,QAAA,2BAA2B,GAAG,OAAC,CAAC,MAAM,CAAC;IAClD,OAAO,EAAE,OAAC,CAAC,OAAO,EAAE;IACpB,IAAI,EAAE,OAAC,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;IACxB,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC5B,SAAS,EAAE,OAAC,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;IAC7C,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE;CAC9B,CAAC,CAAC;AAGH,mBAAmB;AACN,QAAA,cAAc,GAAG,OAAC,CAAC,MAAM,CAAC;IACrC,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE;IAC7B,MAAM,EAAE,2BAAmB;IAC3B,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;IAClB,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE;IACtB,aAAa,EAAE,OAAC,CAAC,MAAM,EAAE;IACzB,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE;IACf,UAAU,EAAE,uBAAe;IAC3B,SAAS,EAAE,OAAC,CAAC,IAAI,EAAE;CACpB,CAAC,CAAC;AAGH,sBAAsB;AACT,QAAA,cAAc,GAAG,OAAC,CAAC,MAAM,CAAC;IACrC,MAAM,EAAE,2BAAmB;IAC3B,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC1B,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC1B,SAAS,EAAE,OAAC,CAAC,IAAI,EAAE;CACpB,CAAC,CAAC;AAGU,QAAA,cAAc,GAAG,OAAC,CAAC,MAAM,CAAC;IACrC,MAAM,EAAE,2BAAmB;IAC3B,SAAS,EAAE,OAAC,CAAC,IAAI,EAAE;IACnB,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC3B,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC3B,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC1B,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC5B,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,WAAW,EAAE,CAAC,QAAQ,EAAE;CAC5C,CAAC,CAAC;AAGH,0DAA0D;AAC7C,QAAA,mBAAmB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC1C,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE;IAC5C,cAAc,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE;IAC9C,aAAa,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE;IAC7C,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAClC,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE;IACrB,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE;IACvB,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE;IACxB,aAAa,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACpC,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CACpC,CAAC,CAAC;AAGH,wBAAwB;AACX,QAAA,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;IACzC,MAAM,EAAE,2BAAmB;IAC3B,eAAe,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;IAC7D,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE;IACjB,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACpC,SAAS,EAAE,OAAC,CAAC,IAAI,EAAE;IACnB,aAAa,EAAE,OAAC,CAAC,MAAM,EAAE;CAC1B,CAAC,CAAC;AAGH,8BAA8B;AACjB,QAAA,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;IACzC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IACxB,OAAO,EAAE,OAAC,CAAC,MAAM,CAAC;QAChB,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE;QACnB,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE;QACtB,YAAY,EAAE,OAAC,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;KACjC,CAAC;IACF,SAAS,EAAE,OAAC,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;CAC9C,CAAC,CAAC;AAGU,QAAA,qBAAqB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC5C,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE;IACpB,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACpC,OAAO,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IACvC,iBAAiB,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IACjD,SAAS,EAAE,OAAC,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;CAC9C,CAAC,CAAC;AAGH,qBAAqB;AACR,QAAA,sBAAsB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC7C,eAAe,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IACrD,UAAU,EAAE,OAAC,CAAC,IAAI,EAAE;IACpB,QAAQ,EAAE,OAAC,CAAC,IAAI,EAAE;IAClB,OAAO,EAAE,OAAC,CAAC,KAAK,CAAC,2BAAmB,CAAC;IACrC,eAAe,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,GAAG,EAAE,CAAC;CACnC,CAAC,CAAC;AAGU,QAAA,sBAAsB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC7C,MAAM,EAAE,8BAAsB;IAC9B,QAAQ,EAAE,2BAAmB;IAC7B,MAAM,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,CAAC;QACvB,MAAM,EAAE,2BAAmB;QAC3B,UAAU,EAAE,OAAC,CAAC,IAAI,EAAE;QACpB,SAAS,EAAE,OAAC,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;QAC9B,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE;QACvB,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QACjC,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;QAClB,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QAC1B,UAAU,EAAE,uBAAe;KAC5B,CAAC,CAAC;IACH,eAAe,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,CAAC;QAChC,SAAS,EAAE,OAAC,CAAC,IAAI,EAAE;QACnB,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE;KACpB,CAAC,CAAC;IACH,UAAU,EAAE,OAAC,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;CAC/C,CAAC,CAAC"}