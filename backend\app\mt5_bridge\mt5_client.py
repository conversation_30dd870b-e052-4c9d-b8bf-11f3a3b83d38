"""
MT5 Python Client Module

This module provides a client for interacting with MetaTrader 5 from Python strategies.
It handles connection, data retrieval, and trading operations.
"""

import os
import time
import logging
import json
from enum import Enum
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime, timedelta

# Configure logging
logger = logging.getLogger(__name__)

# Try to import MetaTrader5 module
try:
    import MetaTrader5 as mt5
    MT5_AVAILABLE = True
except ImportError:
    logger.warning("MetaTrader5 module not available. Using simulation mode.")
    MT5_AVAILABLE = False

# Enum for order types mapping
class OrderType(Enum):
    BUY = 0
    SELL = 1
    BUY_LIMIT = 2
    SELL_LIMIT = 3
    BUY_STOP = 4
    SELL_STOP = 5

# Enum for timeframes mapping
class Timeframe(Enum):
    M1 = 1
    M5 = 5
    M15 = 15
    M30 = 30
    H1 = 60
    H4 = 240
    D1 = 1440
    W1 = 10080
    MN1 = 43200

class MT5Client:
    """
    Client for interacting with MetaTrader 5
    """
    
    def __init__(self, login: Optional[str] = None, password: Optional[str] = None, server: Optional[str] = None):
        """
        Initialize the MT5 client
        """
        self.connected = False
        self.login = login
        self.server = server
        self.account_info = {}
        
        # Connect to MT5 if credentials are provided
        if login and password and server:
            self.connect(login, password, server)
    
    def connect(self, login: str, password: str, server: str) -> bool:
        """
        Connect to MT5 platform
        """
        if MT5_AVAILABLE:
            # Initialize MT5
            if not mt5.initialize():
                logger.error(f"MT5 initialization failed: {mt5.last_error()}")
                return False
            
            # Login to MT5
            if not mt5.login(int(login), password, server):
                logger.error(f"MT5 login failed: {mt5.last_error()}")
                mt5.shutdown()
                return False
            
            # Get account info
            account_info = mt5.account_info()
            if account_info is None:
                logger.error("Failed to get account info")
                mt5.shutdown()
                return False
            
            # Convert account info to dict
            self.account_info = {
                "login": account_info.login,
                "server": self.server,
                "balance": account_info.balance,
                "equity": account_info.equity,
                "margin": account_info.margin,
                "free_margin": account_info.margin_free,
                "margin_level": account_info.margin_level,
                "currency": account_info.currency,
                "leverage": account_info.leverage,
                "name": account_info.name,
            }
            
            self.connected = True
            self.login = login
            self.server = server
            
            logger.info(f"Connected to MT5 server {server} with login {login}")
            return True
        else:
            # Simulation mode
            logger.info(f"Simulation mode: Connected to MT5 server {server} with login {login}")
            self.connected = True
            self.login = login
            self.server = server
            
            # Simulate account info
            self.account_info = {
                "login": login,
                "server": server,
                "balance": 10000.0,
                "equity": 10050.0,
                "margin": 200.0,
                "free_margin": 9850.0,
                "margin_level": 5025.0,
                "currency": "USD",
                "leverage": 100,
                "name": f"Demo Account {login}",
            }
            
            return True
    
    def disconnect(self) -> bool:
        """
        Disconnect from MT5 platform
        """
        if MT5_AVAILABLE and self.connected:
            mt5.shutdown()
        
        self.connected = False
        logger.info("Disconnected from MT5")
        return True
    
    def get_account_info(self) -> Dict[str, Any]:
        """
        Get account information
        """
        if not self.connected:
            logger.error("Not connected to MT5")
            return {}
        
        if MT5_AVAILABLE:
            account_info = mt5.account_info()
            if account_info is None:
                logger.error("Failed to get account info")
                return {}
            
            # Update account info
            self.account_info = {
                "login": account_info.login,
                "server": self.server,
                "balance": account_info.balance,
                "equity": account_info.equity,
                "margin": account_info.margin,
                "free_margin": account_info.margin_free,
                "margin_level": account_info.margin_level,
                "currency": account_info.currency,
                "leverage": account_info.leverage,
                "name": account_info.name,
            }
        
        return self.account_info
    
    def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """
        Get symbol information
        """
        if not self.connected:
            logger.error("Not connected to MT5")
            return {}
        
        if MT5_AVAILABLE:
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                logger.error(f"Failed to get info for symbol {symbol}")
                return {}
            
            # Convert to dict
            info = {
                "name": symbol_info.name,
                "point": symbol_info.point,
                "digits": symbol_info.digits,
                "spread": symbol_info.spread,
                "ask": symbol_info.ask,
                "bid": symbol_info.bid,
                "tick_value": symbol_info.trade_tick_value,
                "contract_size": symbol_info.trade_contract_size,
                "volume_min": symbol_info.volume_min,
                "volume_max": symbol_info.volume_max,
                "volume_step": symbol_info.volume_step,
            }
            
            return info
        else:
            # Simulation mode
            return {
                "name": symbol,
                "point": 0.00001 if "JPY" not in symbol else 0.001,
                "digits": 5 if "JPY" not in symbol else 3,
                "spread": 2,
                "ask": 1.1234 if "JPY" not in symbol else 112.34,
                "bid": 1.1232 if "JPY" not in symbol else 112.32,
                "tick_value": 0.1,
                "contract_size": 100000,
                "volume_min": 0.01,
                "volume_max": 100.0,
                "volume_step": 0.01,
            }
    
    def get_symbol_price(self, symbol: str) -> Dict[str, float]:
        """
        Get current price for a symbol
        """
        if not self.connected:
            logger.error("Not connected to MT5")
            return {"ask": 0.0, "bid": 0.0}
        
        if MT5_AVAILABLE:
            # Get tick info
            tick = mt5.symbol_info_tick(symbol)
            if tick is None:
                logger.error(f"Failed to get tick for symbol {symbol}")
                return {"ask": 0.0, "bid": 0.0}
            
            return {"ask": tick.ask, "bid": tick.bid}
        else:
            # Simulation mode - generate random prices
            import random
            base = 1.1234 if "JPY" not in symbol else 112.34
            variation = random.uniform(-0.0010, 0.0010) if "JPY" not in symbol else random.uniform(-0.10, 0.10)
            bid = base + variation
            ask = bid + (0.0002 if "JPY" not in symbol else 0.02)
            return {"ask": ask, "bid": bid}
    
    def get_historical_data(
        self, 
        symbol: str, 
        timeframe: Union[str, int],
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        count: int = 1000
    ) -> List[Dict[str, Any]]:
        """
        Get historical price data for a symbol
        """
        if not self.connected:
            logger.error("Not connected to MT5")
            return []
        
        # Convert timeframe string to MT5 timeframe value
        tf = self._get_timeframe(timeframe)
        
        # Set default dates if not provided
        if end_date is None:
            end_date = datetime.now()
        
        if start_date is None:
            # Calculate start date based on timeframe and count
            if tf <= 60:  # M1 to H1
                start_date = end_date - timedelta(days=count // (24 * 60 // tf) + 1)
            elif tf == 240:  # H4
                start_date = end_date - timedelta(days=count // 6 + 1)
            elif tf == 1440:  # D1
                start_date = end_date - timedelta(days=count)
            else:  # W1, MN1
                start_date = end_date - timedelta(days=count * (7 if tf == 10080 else 30))
        
        if MT5_AVAILABLE:
            # Convert datetime to MT5 format
            start_ts = int(start_date.timestamp())
            end_ts = int(end_date.timestamp())
            
            # Get historical data
            rates = mt5.copy_rates_range(symbol, tf, start_ts, end_ts)
            
            if rates is None or len(rates) == 0:
                logger.error(f"Failed to get historical data for {symbol}")
                return []
            
            # Convert rates to list of dicts
            result = []
            for rate in rates:
                result.append({
                    "time": datetime.fromtimestamp(rate["time"]),
                    "open": rate["open"],
                    "high": rate["high"],
                    "low": rate["low"],
                    "close": rate["close"],
                    "tick_volume": rate["tick_volume"],
                    "spread": rate["spread"],
                    "real_volume": rate["real_volume"],
                })
            
            return result
        else:
            # Simulation mode - generate random historical data
            import random
            
            result = []
            current_time = end_date
            base_price = 1.1234 if "JPY" not in symbol else 112.34
            price = base_price
            
            # Determine time delta based on timeframe
            if tf <= 60:  # M1 to H1
                delta = timedelta(minutes=tf)
            elif tf == 240:  # H4
                delta = timedelta(hours=4)
            elif tf == 1440:  # D1
                delta = timedelta(days=1)
            elif tf == 10080:  # W1
                delta = timedelta(weeks=1)
            else:  # MN1
                delta = timedelta(days=30)
            
            # Generate data points
            for _ in range(min(count, 1000)):
                current_time -= delta
                
                # Add some random price movement
                change = random.uniform(-0.002, 0.002) if "JPY" not in symbol else random.uniform(-0.2, 0.2)
                price += change
                
                # Calculate OHLC with some randomness
                open_price = price
                high_price = price + abs(random.uniform(0, 0.001)) if "JPY" not in symbol else price + abs(random.uniform(0, 0.1))
                low_price = price - abs(random.uniform(0, 0.001)) if "JPY" not in symbol else price - abs(random.uniform(0, 0.1))
                close_price = price + random.uniform(-0.0005, 0.0005) if "JPY" not in symbol else price + random.uniform(-0.05, 0.05)
                
                result.append({
                    "time": current_time,
                    "open": open_price,
                    "high": high_price,
                    "low": low_price,
                    "close": close_price,
                    "tick_volume": random.randint(10, 1000),
                    "spread": 2,
                    "real_volume": random.randint(1000, 100000),
                })
                
                # Update base for next iteration
                price = close_price
            
            # Reverse to get chronological order
            return list(reversed(result))
    
    def place_order(
        self,
        symbol: str,
        order_type: Union[str, int],
        volume: float,
        price: Optional[float] = None,
        stop_loss: Optional[float] = None,
        take_profit: Optional[float] = None,
        comment: str = "",
        magic: int = 0
    ) -> Dict[str, Any]:
        """
        Place an order
        """
        if not self.connected:
            logger.error("Not connected to MT5")
            return {"success": False, "message": "Not connected to MT5"}
        
        # Convert order type string to MT5 order type
        order_type_value = self._get_order_type(order_type)
        
        # For market orders, get current price if not provided
        if price is None and order_type_value in [0, 1]:  # BUY or SELL
            current_price = self.get_symbol_price(symbol)
            if order_type_value == 0:  # BUY
                price = current_price["ask"]
            else:  # SELL
                price = current_price["bid"]
        
        if MT5_AVAILABLE:
            # Prepare request
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": volume,
                "type": order_type_value,
                "price": price,
                "sl": stop_loss,
                "tp": take_profit,
                "deviation": 10,
                "magic": magic,
                "comment": comment,
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }
            
            # Send order
            result = mt5.order_send(request)
            
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                logger.error(f"Order failed with error code: {result.retcode}")
                return {
                    "success": False,
                    "message": f"Order failed with error code: {result.retcode}",
                    "error_code": result.retcode
                }
            
            return {
                "success": True,
                "message": f"Order placed successfully",
                "order_id": result.order,
                "details": {
                    "order_id": result.order,
                    "symbol": symbol,
                    "type": order_type,
                    "volume": volume,
                    "price": price,
                    "stop_loss": stop_loss,
                    "take_profit": take_profit,
                    "time": time.time(),
                }
            }
        else:
            # Simulation mode
            import random
            import uuid
            
            # Generate a random order ID
            order_id = random.randint(1000000, 9999999)
            
            return {
                "success": True,
                "message": f"Order placed successfully (simulation)",
                "order_id": order_id,
                "details": {
                    "order_id": order_id,
                    "symbol": symbol,
                    "type": order_type,
                    "volume": volume,
                    "price": price,
                    "stop_loss": stop_loss,
                    "take_profit": take_profit,
                    "time": time.time(),
                }
            }
    
    def get_positions(self) -> List[Dict[str, Any]]:
        """
        Get open positions
        """
        if not self.connected:
            logger.error("Not connected to MT5")
            return []
        
        if MT5_AVAILABLE:
            # Get open positions
            positions = mt5.positions_get()
            
            if positions is None:
                logger.error("Failed to get positions")
                return []
            
            # Convert positions to list of dicts
            result = []
            for pos in positions:
                result.append({
                    "ticket": pos.ticket,
                    "time": datetime.fromtimestamp(pos.time),
                    "symbol": pos.symbol,
                    "type": "buy" if pos.type == 0 else "sell",
                    "volume": pos.volume,
                    "open_price": pos.price_open,
                    "current_price": pos.price_current,
                    "stop_loss": pos.sl,
                    "take_profit": pos.tp,
                    "profit": pos.profit,
                    "magic": pos.magic,
                    "comment": pos.comment,
                })
            
            return result
        else:
            # Simulation mode
            import random
            
            # Generate some random positions
            result = []
            for i in range(random.randint(0, 3)):
                symbol = f"EUR/USD" if i % 2 == 0 else "GBP/USD"
                position_type = "buy" if i % 2 == 0 else "sell"
                open_price = 1.1234 if "JPY" not in symbol else 112.34
                current_price = open_price + random.uniform(-0.01, 0.01)
                profit = (current_price - open_price) * 100000 * 0.1 if position_type == "buy" else (open_price - current_price) * 100000 * 0.1
                
                result.append({
                    "ticket": 1000000 + i,
                    "time": datetime.now() - timedelta(hours=random.randint(1, 24)),
                    "symbol": symbol,
                    "type": position_type,
                    "volume": 0.1,
                    "open_price": open_price,
                    "current_price": current_price,
                    "stop_loss": open_price - 0.01 if position_type == "buy" else open_price + 0.01,
                    "take_profit": open_price + 0.02 if position_type == "buy" else open_price - 0.02,
                    "profit": profit,
                    "magic": 12345,
                    "comment": "Python strategy",
                })
            
            return result
    
    def close_position(self, ticket: int) -> Dict[str, Any]:
        """
        Close an open position
        """
        if not self.connected:
            logger.error("Not connected to MT5")
            return {"success": False, "message": "Not connected to MT5"}
        
        if MT5_AVAILABLE:
            # Get position info
            position = mt5.positions_get(ticket=ticket)
            
            if position is None or len(position) == 0:
                logger.error(f"Position {ticket} not found")
                return {"success": False, "message": f"Position {ticket} not found"}
            
            position = position[0]
            
            # Prepare close request
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": position.symbol,
                "volume": position.volume,
                "type": mt5.ORDER_TYPE_SELL if position.type == 0 else mt5.ORDER_TYPE_BUY,
                "position": ticket,
                "price": mt5.symbol_info_tick(position.symbol).bid if position.type == 0 else mt5.symbol_info_tick(position.symbol).ask,
                "deviation": 10,
                "magic": position.magic,
                "comment": "Close position",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }
            
            # Send close request
            result = mt5.order_send(request)
            
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                logger.error(f"Close position failed with error code: {result.retcode}")
                return {
                    "success": False,
                    "message": f"Close position failed with error code: {result.retcode}",
                    "error_code": result.retcode
                }
            
            return {
                "success": True,
                "message": f"Position {ticket} closed successfully",
                "close_price": request["price"],
                "profit": position.profit
            }
        else:
            # Simulation mode
            import random
            
            # Simulate closing a position
            return {
                "success": True,
                "message": f"Position {ticket} closed successfully (simulation)",
                "close_price": 1.1234,
                "profit": random.uniform(-100, 100)
            }
    
    def _get_timeframe(self, timeframe: Union[str, int]) -> int:
        """
        Convert timeframe string to MT5 timeframe value
        """
        if isinstance(timeframe, int):
            return timeframe
        
        timeframe_map = {
            "M1": 1,
            "M5": 5,
            "M15": 15,
            "M30": 30,
            "H1": 60,
            "H4": 240,
            "D1": 1440,
            "W1": 10080,
            "MN1": 43200,
        }
        
        return timeframe_map.get(timeframe.upper(), 15)  # Default to M15 if not found
    
    def _get_order_type(self, order_type: Union[str, int]) -> int:
        """
        Convert order type string to MT5 order type value
        """
        if isinstance(order_type, int):
            return order_type
        
        order_type_map = {
            "BUY": 0,
            "SELL": 1,
            "BUY_LIMIT": 2,
            "SELL_LIMIT": 3,
            "BUY_STOP": 4,
            "SELL_STOP": 5,
            "buy": 0,
            "sell": 1,
            "buy_limit": 2,
            "sell_limit": 3,
            "buy_stop": 4,
            "sell_stop": 5,
        }
        
        return order_type_map.get(order_type, 0)  # Default to BUY if not found
