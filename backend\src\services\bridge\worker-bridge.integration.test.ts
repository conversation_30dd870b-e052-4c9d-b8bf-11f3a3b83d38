/**
 * Integration tests for Worker Bridge Service
 * Tests the bridge between Node.js backend and Python background workers
 */

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import {
  setupIntegrationTest,
  waitForEvent,
  TestDataBuilder,
  assertServiceResponse,
  TEST_FIXTURES,
  type IntegrationTestContext,
} from '@/shared/test-utils';
import { WorkerBridgeService } from './worker-bridge.service';

describe('Worker Bridge Service Integration Tests', () => {
  let testContext: IntegrationTestContext;
  let workerBridgeService: WorkerBridgeService;

  beforeEach(async () => {
    testContext = await setupIntegrationTest({
      pythonEngine: {
        simulateNetworkDelay: true,
        networkDelayMs: 50,
        simulateErrors: false,
        healthyStatus: true,
      },
      enableRealTimeEvents: true,
      testTimeout: 10000,
    });

    // Create worker bridge service
    workerBridgeService = new WorkerBridgeService({
      pythonEngineService: testContext.pythonEngineMock as any,
      logger: testContext.logger,
      monitoring: {
        healthCheckInterval: 1000, // Fast for testing
        statusPollInterval: 2000,
        autoRestart: true,
      },
    });
  });

  afterEach(async () => {
    await workerBridgeService.stop();
    await testContext.cleanup();
  });

  describe('Worker Status Monitoring', () => {
    it('should get worker status successfully', async () => {
      // Mock Python engine response
      const mockWorkerStats = {
        manager_status: 'running',
        uptime_seconds: 3600,
        start_time: new Date().toISOString(),
        active_workers: 3,
        active_tasks: 3,
        worker_stats: {
          FileParserWorker: {
            status: 'running',
            running_jobs: 2,
            max_concurrent: 10,
            poll_interval: 10,
            job_ids: ['job1', 'job2'],
          },
          BacktestRunner: {
            status: 'running',
            running_jobs: 1,
            max_concurrent: 3,
            poll_interval: 10,
            job_ids: ['backtest1'],
          },
          DGMMonitor: {
            status: 'running',
            running_jobs: 0,
            max_concurrent: 2,
            poll_interval: 20,
            job_ids: [],
          },
        },
      };

      // Mock the HTTP request
      (testContext.pythonEngineMock as any).processCommand = jest.fn().mockResolvedValue({
        success: true,
        data: mockWorkerStats,
        timestamp: new Date(),
        request_id: 'test-123',
      });

      const result = await workerBridgeService.getWorkerStatus();
      
      assertServiceResponse.success(result);
      expect(result.data.manager_status).toBe('running');
      expect(result.data.active_workers).toBe(3);
      expect(result.data.worker_stats.FileParserWorker.running_jobs).toBe(2);
    });

    it('should handle worker status errors gracefully', async () => {
      // Mock Python engine error
      (testContext.pythonEngineMock as any).processCommand = jest.fn().mockResolvedValue({
        success: false,
        error: {
          code: 'WORKER_UNAVAILABLE',
          message: 'Workers are not responding',
        },
        timestamp: new Date(),
        request_id: 'test-123',
      });

      const result = await workerBridgeService.getWorkerStatus();
      
      assertServiceResponse.failure(result);
      expect(result.error.code).toBe('WORKER_UNAVAILABLE');
    });

    it('should emit events when worker status changes', async () => {
      const statusPromise = waitForEvent(workerBridgeService, 'worker_status_updated', 5000);

      // Mock successful status response
      (testContext.pythonEngineMock as any).processCommand = jest.fn().mockResolvedValue({
        success: true,
        data: { manager_status: 'running', active_workers: 3 },
        timestamp: new Date(),
        request_id: 'test-123',
      });

      await workerBridgeService.getWorkerStatus();
      
      const statusEvent = await statusPromise;
      expect(statusEvent.manager_status).toBe('running');
      expect(statusEvent.active_workers).toBe(3);
    });
  });

  describe('Worker Health Checks', () => {
    it('should perform health check on all workers', async () => {
      const mockHealthCheck = {
        healthy: true,
        timestamp: new Date(),
        workers: {
          FileParserWorker: { healthy: true, status: 'running' },
          BacktestRunner: { healthy: true, status: 'running' },
          DGMMonitor: { healthy: true, status: 'running' },
        },
        uptime_seconds: 3600,
      };

      (testContext.pythonEngineMock as any).processCommand = jest.fn().mockResolvedValue({
        success: true,
        data: mockHealthCheck,
        timestamp: new Date(),
        request_id: 'test-123',
      });

      const healthCheck = await workerBridgeService.performHealthCheck();
      
      expect(healthCheck.healthy).toBe(true);
      expect(healthCheck.workers.FileParserWorker.healthy).toBe(true);
      expect(healthCheck.workers.BacktestRunner.healthy).toBe(true);
      expect(healthCheck.workers.DGMMonitor.healthy).toBe(true);
    });

    it('should detect unhealthy workers', async () => {
      const mockHealthCheck = {
        healthy: false,
        timestamp: new Date(),
        workers: {
          FileParserWorker: { healthy: false, status: 'error', reason: 'Process crashed' },
          BacktestRunner: { healthy: true, status: 'running' },
          DGMMonitor: { healthy: true, status: 'running' },
        },
        uptime_seconds: 3600,
      };

      (testContext.pythonEngineMock as any).processCommand = jest.fn().mockResolvedValue({
        success: true,
        data: mockHealthCheck,
        timestamp: new Date(),
        request_id: 'test-123',
      });

      const healthCheck = await workerBridgeService.performHealthCheck();
      
      expect(healthCheck.healthy).toBe(false);
      expect(healthCheck.workers.FileParserWorker.healthy).toBe(false);
      expect(healthCheck.workers.FileParserWorker.reason).toBe('Process crashed');
    });

    it('should emit health check events', async () => {
      const healthPromise = waitForEvent(workerBridgeService, 'worker_health_updated', 5000);

      (testContext.pythonEngineMock as any).processCommand = jest.fn().mockResolvedValue({
        success: true,
        data: { healthy: true, workers: {}, uptime_seconds: 3600 },
        timestamp: new Date(),
        request_id: 'test-123',
      });

      await workerBridgeService.performHealthCheck();
      
      const healthEvent = await healthPromise;
      expect(healthEvent.healthy).toBe(true);
    });
  });

  describe('Worker Management', () => {
    it('should restart a specific worker', async () => {
      (testContext.pythonEngineMock as any).processCommand = jest.fn().mockResolvedValue({
        success: true,
        data: { worker_name: 'FileParserWorker', restarted: true },
        timestamp: new Date(),
        request_id: 'test-123',
      });

      const result = await workerBridgeService.restartWorker('FileParserWorker');
      
      assertServiceResponse.success(result);
      expect(result.data.worker_name).toBe('FileParserWorker');
      expect(result.data.restarted).toBe(true);
    });

    it('should handle worker restart failures', async () => {
      (testContext.pythonEngineMock as any).processCommand = jest.fn().mockResolvedValue({
        success: false,
        error: {
          code: 'RESTART_FAILED',
          message: 'Worker could not be restarted',
        },
        timestamp: new Date(),
        request_id: 'test-123',
      });

      const result = await workerBridgeService.restartWorker('FileParserWorker');
      
      assertServiceResponse.failure(result);
      expect(result.error.code).toBe('RESTART_FAILED');
    });

    it('should emit worker restart events', async () => {
      const restartPromise = waitForEvent(workerBridgeService, 'worker_restarted', 5000);

      (testContext.pythonEngineMock as any).processCommand = jest.fn().mockResolvedValue({
        success: true,
        data: { worker_name: 'BacktestRunner', restarted: true },
        timestamp: new Date(),
        request_id: 'test-123',
      });

      await workerBridgeService.restartWorker('BacktestRunner');
      
      const restartEvent = await restartPromise;
      expect(restartEvent.workerName).toBe('BacktestRunner');
      expect(restartEvent.success).toBe(true);
    });
  });

  describe('Job Monitoring', () => {
    it('should get file upload sessions', async () => {
      const mockSessions = [
        {
          id: 'session1',
          user_id: 'user1',
          original_filename: 'data.csv',
          status: 'parsing_in_progress',
          rows_processed: 1000,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          id: 'session2',
          user_id: 'user1',
          original_filename: 'trades.xlsx',
          status: 'ready',
          rows_processed: 5000,
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      (testContext.pythonEngineMock as any).processCommand = jest.fn().mockResolvedValue({
        success: true,
        data: mockSessions,
        timestamp: new Date(),
        request_id: 'test-123',
      });

      const result = await workerBridgeService.getFileUploadSessions();
      
      assertServiceResponse.success(result);
      expect(result.data).toHaveLength(2);
      expect(result.data[0].status).toBe('parsing_in_progress');
      expect(result.data[1].status).toBe('ready');
    });

    it('should get running backtests', async () => {
      const mockBacktests = [
        {
          id: 'backtest1',
          user_id: 'user1',
          name: 'SMA Strategy Test',
          status: 'running',
          progress: 45,
          started_at: new Date(),
          created_at: new Date(),
        },
        {
          id: 'backtest2',
          user_id: 'user2',
          name: 'RSI Strategy Test',
          status: 'running',
          progress: 78,
          started_at: new Date(),
          created_at: new Date(),
        },
      ];

      (testContext.pythonEngineMock as any).processCommand = jest.fn().mockResolvedValue({
        success: true,
        data: mockBacktests,
        timestamp: new Date(),
        request_id: 'test-123',
      });

      const result = await workerBridgeService.getRunningBacktests();
      
      assertServiceResponse.success(result);
      expect(result.data).toHaveLength(2);
      expect(result.data[0].status).toBe('running');
      expect(result.data[0].progress).toBe(45);
    });

    it('should get DGM experiments', async () => {
      const mockExperiments = [
        {
          id: 'dgm1',
          user_id: 'user1',
          experiment_name: 'Strategy Evolution v1',
          status: 'running',
          base_strategy: { name: 'sma_crossover' },
          started_at: new Date(),
          created_at: new Date(),
        },
      ];

      (testContext.pythonEngineMock as any).processCommand = jest.fn().mockResolvedValue({
        success: true,
        data: mockExperiments,
        timestamp: new Date(),
        request_id: 'test-123',
      });

      const result = await workerBridgeService.getDGMExperiments('running');
      
      assertServiceResponse.success(result);
      expect(result.data).toHaveLength(1);
      expect(result.data[0].status).toBe('running');
      expect(result.data[0].experiment_name).toBe('Strategy Evolution v1');
    });
  });

  describe('Auto-Restart Functionality', () => {
    it('should automatically restart unhealthy workers', async () => {
      // Mock unhealthy worker
      const unhealthyCheck = {
        healthy: false,
        timestamp: new Date(),
        workers: {
          FileParserWorker: { healthy: false, status: 'error', reason: 'Process crashed' },
          BacktestRunner: { healthy: true, status: 'running' },
        },
        uptime_seconds: 3600,
      };

      // Mock restart success
      const restartResponse = {
        success: true,
        data: { worker_name: 'FileParserWorker', restarted: true },
        timestamp: new Date(),
        request_id: 'test-123',
      };

      (testContext.pythonEngineMock as any).processCommand = jest.fn()
        .mockResolvedValueOnce({ success: true, data: unhealthyCheck })
        .mockResolvedValueOnce(restartResponse);

      const restartPromise = waitForEvent(workerBridgeService, 'worker_restarted', 5000);

      // Trigger health check
      await workerBridgeService.performHealthCheck();

      // Wait for auto-restart
      const restartEvent = await restartPromise;
      expect(restartEvent.workerName).toBe('FileParserWorker');
      expect(restartEvent.success).toBe(true);
    });

    it('should emit restart failure events', async () => {
      // Mock unhealthy worker
      const unhealthyCheck = {
        healthy: false,
        timestamp: new Date(),
        workers: {
          BacktestRunner: { healthy: false, status: 'error' },
        },
        uptime_seconds: 3600,
      };

      // Mock restart failure
      const restartResponse = {
        success: false,
        error: { code: 'RESTART_FAILED', message: 'Could not restart worker' },
        timestamp: new Date(),
        request_id: 'test-123',
      };

      (testContext.pythonEngineMock as any).processCommand = jest.fn()
        .mockResolvedValueOnce({ success: true, data: unhealthyCheck })
        .mockResolvedValueOnce(restartResponse);

      const failPromise = waitForEvent(workerBridgeService, 'worker_restart_failed', 5000);

      // Trigger health check
      await workerBridgeService.performHealthCheck();

      // Wait for restart failure
      const failEvent = await failPromise;
      expect(failEvent.workerName).toBe('BacktestRunner');
      expect(failEvent.error.code).toBe('RESTART_FAILED');
    });
  });

  describe('Service Health Information', () => {
    it('should provide service health information', async () => {
      // Perform a health check first
      (testContext.pythonEngineMock as any).processCommand = jest.fn().mockResolvedValue({
        success: true,
        data: { healthy: true, workers: {}, uptime_seconds: 3600 },
        timestamp: new Date(),
        request_id: 'test-123',
      });

      await workerBridgeService.performHealthCheck();

      const health = workerBridgeService.getServiceHealth();
      
      expect(health.healthy).toBe(true);
      expect(health.lastHealthCheck).toBeInstanceOf(Date);
      expect(health.monitoring.healthCheckInterval).toBe(1000);
      expect(health.monitoring.autoRestart).toBe(true);
    });

    it('should track unhealthy status', async () => {
      // Mock unhealthy response
      (testContext.pythonEngineMock as any).processCommand = jest.fn().mockResolvedValue({
        success: false,
        error: { code: 'CONNECTION_ERROR', message: 'Cannot connect to workers' },
        timestamp: new Date(),
        request_id: 'test-123',
      });

      await workerBridgeService.performHealthCheck();

      const health = workerBridgeService.getServiceHealth();
      
      expect(health.healthy).toBe(false);
      expect(health.lastHealthCheck).toBeInstanceOf(Date);
    });
  });

  describe('Worker Statistics', () => {
    it('should get detailed worker statistics', async () => {
      const mockStats = {
        system_resources: {
          cpu_percent: 45.2,
          memory_percent: 62.8,
          memory_used_mb: 1024,
          disk_usage_percent: 78.5,
          active_connections: 15,
        },
        worker_performance: {
          FileParserWorker: {
            files_processed_today: 25,
            avg_processing_time: 120.5,
            success_rate: 0.96,
          },
          BacktestRunner: {
            backtests_completed_today: 8,
            avg_execution_time: 1800.2,
            success_rate: 0.95,
          },
        },
      };

      (testContext.pythonEngineMock as any).processCommand = jest.fn().mockResolvedValue({
        success: true,
        data: mockStats,
        timestamp: new Date(),
        request_id: 'test-123',
      });

      const result = await workerBridgeService.getWorkerStats();
      
      assertServiceResponse.success(result);
      expect(result.data.system_resources.cpu_percent).toBe(45.2);
      expect(result.data.worker_performance.FileParserWorker.files_processed_today).toBe(25);
    });
  });
});