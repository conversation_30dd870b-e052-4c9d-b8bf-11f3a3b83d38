"""
Backtesting API routes
"""

import logging
import uuid
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, Depends, Request
from pydantic import BaseModel, Field
from sqlalchemy.ext.asyncio import AsyncSession

# Import JWT Bearer for authentication
from ...auth.jwt_handler import JWTBearer
from ...db.config import get_db
from ...db.crud import backtest as backtest_crud
from ...db.crud import strategy as strategy_crud

# Configure logging
logger = logging.getLogger("api.backtest")

# Create router
router = APIRouter(dependencies=[Depends(JWTBearer())])

# Request/Response models
class IndicatorConfig(BaseModel):
    """Technical indicator configuration"""
    rsi: Optional[Dict[str, Any]] = None
    macd: Optional[Dict[str, Any]] = None
    sma: Optional[Dict[str, Any]] = None

class RiskManagementConfig(BaseModel):
    """Risk management configuration"""
    stopLoss: float = Field(..., description="Stop loss percentage")
    takeProfit: float = Field(..., description="Take profit percentage")
    positionSize: float = Field(..., description="Position size percentage")

class StrategyConfig(BaseModel):
    """Strategy configuration"""
    indicators: IndicatorConfig
    riskManagement: RiskManagementConfig
    rules: List[str] = Field([], description="Trading rules")

class BacktestConfig(BaseModel):
    """Backtest configuration"""
    name: str = Field(..., description="Backtest name")
    symbol: str = Field(..., description="Trading symbol")
    startDate: str = Field(..., description="Start date (YYYY-MM-DD)")
    endDate: str = Field(..., description="End date (YYYY-MM-DD)")
    initialBalance: float = Field(..., gt=0, description="Initial balance")
    modelName: Optional[str] = Field(None, description="AI model name")
    strategyConfig: StrategyConfig

class BacktestResponse(BaseModel):
    """Backtest response"""
    backtestId: str = Field(..., description="Backtest ID")

class BacktestStatus(BaseModel):
    """Backtest status"""
    id: str = Field(..., description="Backtest ID")
    name: str = Field(..., description="Backtest name")
    symbol: str = Field(..., description="Trading symbol")
    startDate: str = Field(..., description="Start date")
    endDate: str = Field(..., description="End date")
    status: str = Field(..., description="Status (running, completed, failed)")
    createdAt: str = Field(..., description="Creation timestamp")
    completedAt: Optional[str] = Field(None, description="Completion timestamp")

# Helper functions
def generate_mock_backtest_results(config: BacktestConfig):
    """Generate mock backtest results for demo purposes"""
    # Parse dates
    start_date = datetime.fromisoformat(config.startDate)
    end_date = datetime.fromisoformat(config.endDate)
    
    # Generate equity curve
    days = (end_date - start_date).days
    equity_curve = []
    current_date = start_date
    current_equity = config.initialBalance
    
    # Simple random walk for equity
    import random
    random.seed(hash(config.name))  # Use name as seed for reproducibility
    
    for _ in range(days):
        # Random daily return between -1% and +1.5%
        daily_return = random.uniform(-0.01, 0.015)
        current_equity *= (1 + daily_return)
        
        equity_curve.append({
            "date": current_date.isoformat(),
            "value": current_equity
        })
        
        current_date += timedelta(days=1)
    
    # Generate trades
    num_trades = random.randint(days // 5, days // 2)  # Roughly 1 trade every 2-5 days
    trades = []
    
    for i in range(num_trades):
        # Random entry date
        entry_date = start_date + timedelta(days=random.randint(0, days-2))
        # Exit 1-3 days later
        exit_date = entry_date + timedelta(days=random.randint(1, 3))
        
        # Ensure exit date is within range
        if exit_date > end_date:
            exit_date = end_date
        
        # Random side
        side = random.choice(["buy", "sell"])
        
        # Random prices
        if config.symbol == "EURUSD":
            base_price = 1.1000
        elif config.symbol == "GBPUSD":
            base_price = 1.3000
        elif config.symbol == "USDJPY":
            base_price = 110.00
        else:
            base_price = 1.0000
        
        entry_price = base_price * random.uniform(0.98, 1.02)
        
        # Exit price depends on side for more realistic P&L
        if side == "buy":
            exit_price = entry_price * random.uniform(0.99, 1.03)  # More likely to be profitable
        else:
            exit_price = entry_price * random.uniform(0.97, 1.01)  # More likely to be profitable
        
        # Calculate P&L
        if side == "buy":
            pnl = (exit_price - entry_price) / entry_price * 100  # Percentage
        else:
            pnl = (entry_price - exit_price) / entry_price * 100  # Percentage
        
        # Position size based on risk management
        position_size = config.initialBalance * config.strategyConfig.riskManagement.positionSize
        
        trades.append({
            "id": str(uuid.uuid4()),
            "symbol": config.symbol,
            "side": side,
            "entryPrice": entry_price,
            "exitPrice": exit_price,
            "quantity": position_size / entry_price,
            "entryTime": entry_date.isoformat(),
            "exitTime": exit_date.isoformat(),
            "pnl": pnl,
            "pnlPercent": pnl
        })
    
    # Calculate metrics
    winning_trades = [t for t in trades if t["pnl"] > 0]
    losing_trades = [t for t in trades if t["pnl"] <= 0]
    
    total_return = (current_equity - config.initialBalance) / config.initialBalance * 100
    win_rate = len(winning_trades) / len(trades) * 100 if trades else 0
    
    # Max drawdown calculation
    max_equity = config.initialBalance
    max_drawdown = 0
    
    for point in equity_curve:
        if point["value"] > max_equity:
            max_equity = point["value"]
        
        drawdown = (max_equity - point["value"]) / max_equity * 100
        if drawdown > max_drawdown:
            max_drawdown = drawdown
    
    # Sharpe ratio (simplified)
    sharpe_ratio = total_return / (max_drawdown + 0.1)  # Add 0.1 to avoid division by zero
    
    # Profit factor
    total_profit = sum([t["pnl"] for t in winning_trades])
    total_loss = abs(sum([t["pnl"] for t in losing_trades]))
    profit_factor = total_profit / total_loss if total_loss > 0 else total_profit
    
    return {
        "id": str(uuid.uuid4()),
        "name": config.name,
        "symbol": config.symbol,
        "startDate": config.startDate,
        "endDate": config.endDate,
        "initialBalance": config.initialBalance,
        "finalBalance": current_equity,
        "totalTrades": len(trades),
        "winningTrades": len(winning_trades),
        "losingTrades": len(losing_trades),
        "winRate": win_rate,
        "totalReturn": total_return,
        "maxDrawdown": max_drawdown,
        "sharpeRatio": sharpe_ratio,
        "profitFactor": profit_factor,
        "equityCurve": equity_curve,
        "trades": trades,
        "status": "completed",
        "createdAt": datetime.now().isoformat()
    }

# Endpoints
@router.post("", response_model=BacktestResponse)
async def create_backtest(
    config: BacktestConfig,
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """Create a new backtest"""
    try:
        # Get user from request state
        user = request.state.user
        
        # Parse dates
        start_date = datetime.fromisoformat(config.startDate)
        end_date = datetime.fromisoformat(config.endDate)
        
        # Create strategy if needed
        strategy_config = {
            "indicators": config.strategyConfig.indicators.dict(),
            "riskManagement": config.strategyConfig.riskManagement.dict(),
            "rules": config.strategyConfig.rules
        }
        
        strategy = await strategy_crud.create_strategy(
            db,
            user_id=user.id,
            name=f"Strategy for {config.name}",
            config=strategy_config
        )
        
        # Create backtest
        backtest = await backtest_crud.create_backtest(
            db,
            user_id=user.id,
            strategy_id=strategy.id,
            name=config.name,
            symbol=config.symbol,
            timeframe="D1",  # Default to daily timeframe
            start_date=start_date,
            end_date=end_date,
            initial_balance=config.initialBalance
        )
        
        # In a real implementation, you would queue the backtest job
        # For now, we'll generate mock results immediately
        results = generate_mock_backtest_results(config)
        
        # Update backtest with results
        await backtest_crud.update_backtest_status(
            db,
            backtest.id,
            "completed",
            results
        )
        
        return {"backtestId": str(backtest.id)}
    except Exception as e:
        logger.error(f"Error creating backtest: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("", response_model=List[BacktestStatus])
async def get_backtests(
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """Get all backtests for the current user"""
    try:
        # Get user from request state
        user = request.state.user
        
        # Get backtests
        backtests = await backtest_crud.get_backtests_by_user(db, user.id)
        
        # Format response
        return [
            {
                "id": str(backtest.id),
                "name": backtest.name,
                "symbol": backtest.symbol,
                "startDate": backtest.start_date.isoformat(),
                "endDate": backtest.end_date.isoformat(),
                "status": backtest.status,
                "createdAt": backtest.created_at.isoformat(),
                "completedAt": backtest.completed_at.isoformat() if backtest.completed_at else None
            }
            for backtest in backtests
        ]
    except Exception as e:
        logger.error(f"Error getting backtests: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{backtest_id}", response_model=Dict[str, Any])
async def get_backtest(
    backtest_id: uuid.UUID,
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """Get a specific backtest"""
    try:
        # Get user from request state
        user = request.state.user
        
        # Get backtest
        backtest = await backtest_crud.get_backtest_by_id(db, backtest_id)
        
        # Check if backtest exists and belongs to the user
        if not backtest or backtest.user_id != user.id:
            raise HTTPException(status_code=404, detail=f"Backtest {backtest_id} not found")
        
        # Format response
        response = {
            "id": str(backtest.id),
            "name": backtest.name,
            "symbol": backtest.symbol,
            "startDate": backtest.start_date.isoformat(),
            "endDate": backtest.end_date.isoformat(),
            "initialBalance": backtest.initial_balance,
            "status": backtest.status,
            "createdAt": backtest.created_at.isoformat(),
            "completedAt": backtest.completed_at.isoformat() if backtest.completed_at else None
        }
        
        # Add results if available
        if backtest.results:
            response.update(backtest.results)
        
        return response
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting backtest: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{backtest_id}/results", response_model=Dict[str, Any])
async def get_backtest_results(
    backtest_id: uuid.UUID,
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """Get backtest results"""
    try:
        # Get user from request state
        user = request.state.user
        
        # Get backtest
        backtest = await backtest_crud.get_backtest_by_id(db, backtest_id)
        
        # Check if backtest exists and belongs to the user
        if not backtest or backtest.user_id != user.id:
            raise HTTPException(status_code=404, detail=f"Backtest {backtest_id} not found")
        
        # Check if results are available
        if not backtest.results:
            raise HTTPException(status_code=404, detail=f"Results for backtest {backtest_id} not found")
        
        return backtest.results
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting backtest results: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{backtest_id}", response_model=Dict[str, bool])
async def delete_backtest(
    backtest_id: uuid.UUID,
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """Delete a backtest"""
    try:
        # Get user from request state
        user = request.state.user
        
        # Get backtest
        backtest = await backtest_crud.get_backtest_by_id(db, backtest_id)
        
        # Check if backtest exists and belongs to the user
        if not backtest or backtest.user_id != user.id:
            raise HTTPException(status_code=404, detail=f"Backtest {backtest_id} not found")
        
        # Delete backtest
        result = await backtest_crud.delete_backtest(db, backtest_id)
        
        return {"success": result}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting backtest: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))