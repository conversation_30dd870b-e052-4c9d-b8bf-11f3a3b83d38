"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PasswordStrengthSchema = exports.VerifyTwoFactorRequestSchema = exports.EnableTwoFactorRequestSchema = exports.TwoFactorAuthSchema = exports.OAuthCallbackRequestSchema = exports.OAuthProviderSchema = exports.ApiKeyResponseSchema = exports.CreateApiKeyRequestSchema = exports.ApiKeySchema = exports.SessionSchema = exports.UserProfileSchema = exports.UpdateUserProfileRequestSchema = exports.RefreshTokenPayloadSchema = exports.TokenPayloadSchema = exports.AuthResultSchema = exports.AuthTokensSchema = exports.ChangePasswordRequestSchema = exports.ConfirmResetPasswordRequestSchema = exports.ResetPasswordRequestSchema = exports.RefreshTokenRequestSchema = exports.CreateUserRequestSchema = exports.LoginRequestSchema = exports.UserWithPasswordHashSchema = exports.UserSchema = exports.UserIdSchema = exports.SubscriptionTierSchema = void 0;
const zod_1 = require("zod");
const common_schemas_1 = require("./common.schemas");
// Subscription tiers
exports.SubscriptionTierSchema = zod_1.z.enum(['free', 'solo', 'pro', 'enterprise']);
// User ID with proper branding
exports.UserIdSchema = common_schemas_1.IdSchema.brand();
// Core User Schema
exports.UserSchema = zod_1.z.object({
    id: exports.UserIdSchema,
    email: zod_1.z.string().email(),
    fullName: zod_1.z.string().optional(),
    subscriptionTier: exports.SubscriptionTierSchema,
    apiQuotaUsed: zod_1.z.number().int().nonnegative(),
    apiQuotaLimit: zod_1.z.number().int().positive(),
    createdAt: zod_1.z.date(),
    updatedAt: zod_1.z.date(),
});
// User with password hash (for backend use)
exports.UserWithPasswordHashSchema = exports.UserSchema.extend({
    passwordHash: zod_1.z.string().min(1),
});
// Authentication request schemas
exports.LoginRequestSchema = zod_1.z.object({
    email: zod_1.z.string().email(),
    password: zod_1.z.string().min(1),
});
exports.CreateUserRequestSchema = zod_1.z.object({
    email: zod_1.z.string().email(),
    password: zod_1.z.string().min(8).refine((password) => {
        const hasUpperCase = /[A-Z]/.test(password);
        const hasLowerCase = /[a-z]/.test(password);
        const hasNumbers = /\d/.test(password);
        const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
        return hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar;
    }, {
        message: "Password must contain uppercase, lowercase, number, and special character",
    }),
    fullName: zod_1.z.string().min(1).optional(),
});
exports.RefreshTokenRequestSchema = zod_1.z.object({
    refreshToken: zod_1.z.string().min(1),
});
exports.ResetPasswordRequestSchema = zod_1.z.object({
    email: zod_1.z.string().email(),
});
exports.ConfirmResetPasswordRequestSchema = zod_1.z.object({
    token: zod_1.z.string().min(1),
    newPassword: zod_1.z.string().min(8),
});
exports.ChangePasswordRequestSchema = zod_1.z.object({
    currentPassword: zod_1.z.string().min(1),
    newPassword: zod_1.z.string().min(8),
});
// Authentication response schemas
exports.AuthTokensSchema = zod_1.z.object({
    accessToken: zod_1.z.string(),
    refreshToken: zod_1.z.string(),
    expiresIn: zod_1.z.number().int().positive(),
});
exports.AuthResultSchema = zod_1.z.object({
    user: exports.UserSchema,
    tokens: exports.AuthTokensSchema,
});
// JWT Token Payload schemas
exports.TokenPayloadSchema = zod_1.z.object({
    userId: exports.UserIdSchema,
    email: zod_1.z.string().email().optional(),
    subscriptionTier: exports.SubscriptionTierSchema.optional(),
    type: zod_1.z.enum(['access', 'refresh']),
    iat: zod_1.z.number().optional(),
    exp: zod_1.z.number().optional(),
});
exports.RefreshTokenPayloadSchema = zod_1.z.object({
    userId: exports.UserIdSchema,
    type: zod_1.z.literal('refresh'),
    iat: zod_1.z.number().optional(),
    exp: zod_1.z.number().optional(),
});
// User profile schemas
exports.UpdateUserProfileRequestSchema = zod_1.z.object({
    fullName: zod_1.z.string().min(1).optional(),
    email: zod_1.z.string().email().optional(),
});
exports.UserProfileSchema = exports.UserSchema.omit({
    apiQuotaUsed: true,
    apiQuotaLimit: true
}).extend({
    lastLoginAt: zod_1.z.date().optional(),
    emailVerified: zod_1.z.boolean().default(false),
    phoneNumber: zod_1.z.string().optional(),
    timezone: zod_1.z.string().default('UTC'),
    preferences: zod_1.z.object({
        theme: zod_1.z.enum(['light', 'dark']).default('light'),
        language: zod_1.z.string().default('en'),
        notifications: zod_1.z.object({
            email: zod_1.z.boolean().default(true),
            push: zod_1.z.boolean().default(true),
            trading: zod_1.z.boolean().default(true),
        }).default({}),
    }).default({}),
});
// Session schemas
exports.SessionSchema = zod_1.z.object({
    id: common_schemas_1.IdSchema,
    userId: exports.UserIdSchema,
    deviceInfo: zod_1.z.object({
        userAgent: zod_1.z.string(),
        ip: zod_1.z.string(),
        location: zod_1.z.string().optional(),
    }),
    isActive: zod_1.z.boolean(),
    lastActivity: zod_1.z.date(),
    createdAt: zod_1.z.date(),
    expiresAt: zod_1.z.date(),
});
// API Key schemas (for programmatic access)
exports.ApiKeySchema = zod_1.z.object({
    id: common_schemas_1.IdSchema,
    userId: exports.UserIdSchema,
    name: zod_1.z.string().min(1),
    keyPrefix: zod_1.z.string(), // First 8 chars for identification
    hashedKey: zod_1.z.string(),
    permissions: zod_1.z.array(zod_1.z.enum([
        'read:account', 'read:trades', 'write:trades',
        'read:backtests', 'write:backtests',
        'read:uploads', 'write:uploads',
        'read:chat', 'write:chat'
    ])),
    lastUsed: zod_1.z.date().optional(),
    usageCount: zod_1.z.number().int().nonnegative().default(0),
    isActive: zod_1.z.boolean().default(true),
    createdAt: zod_1.z.date(),
    expiresAt: zod_1.z.date().optional(),
});
exports.CreateApiKeyRequestSchema = zod_1.z.object({
    name: zod_1.z.string().min(1).max(255),
    permissions: zod_1.z.array(zod_1.z.enum([
        'read:account', 'read:trades', 'write:trades',
        'read:backtests', 'write:backtests',
        'read:uploads', 'write:uploads',
        'read:chat', 'write:chat'
    ])).min(1),
    expiresAt: zod_1.z.date().optional(),
});
exports.ApiKeyResponseSchema = zod_1.z.object({
    id: common_schemas_1.IdSchema,
    name: zod_1.z.string(),
    keyPrefix: zod_1.z.string(),
    fullKey: zod_1.z.string(), // Only returned on creation
    permissions: zod_1.z.array(zod_1.z.string()),
    createdAt: zod_1.z.date(),
    expiresAt: zod_1.z.date().optional(),
});
// OAuth schemas (for third-party integration)
exports.OAuthProviderSchema = zod_1.z.enum(['google', 'github', 'microsoft']);
exports.OAuthCallbackRequestSchema = zod_1.z.object({
    code: zod_1.z.string(),
    state: zod_1.z.string(),
    provider: exports.OAuthProviderSchema,
});
// Two-factor authentication schemas
exports.TwoFactorAuthSchema = zod_1.z.object({
    enabled: zod_1.z.boolean(),
    backupCodes: zod_1.z.array(zod_1.z.string()).optional(),
    lastUsed: zod_1.z.date().optional(),
});
exports.EnableTwoFactorRequestSchema = zod_1.z.object({
    password: zod_1.z.string(),
});
exports.VerifyTwoFactorRequestSchema = zod_1.z.object({
    code: zod_1.z.string().length(6),
});
// Password strength validation
exports.PasswordStrengthSchema = zod_1.z.object({
    score: zod_1.z.number().int().min(0).max(4), // 0-4 strength score
    feedback: zod_1.z.object({
        warning: zod_1.z.string().optional(),
        suggestions: zod_1.z.array(zod_1.z.string()),
    }),
    crackTime: zod_1.z.string(), // Human readable time estimate
});
//# sourceMappingURL=auth.schemas.js.map