"""
Enhanced Strategy Chatbot with Ollama LLM Integration
Combines intelligent LLM responses with template fallback
"""

import asyncio
import logging
import re
from typing import Dict, Any, Optional, List
from datetime import datetime

from .ollama_client import OllamaClient

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedStrategyChatbot:
    """
    Intelligent trading strategy chatbot that combines:
    1. Ollama LLM for natural language understanding and response generation
    2. Template system fallback for reliability
    3. Strategy extraction and code generation
    4. Context-aware conversations
    """
    
    def __init__(self, ollama_base_url: str = "http://localhost:11435"):
        self.ollama_client = OllamaClient(ollama_base_url)
        self.template_manager = None  # Will be initialized with fallback system
        self.conversation_context: Dict[str, List[Dict[str, str]]] = {}
        
        # Strategy-specific prompts and contexts
        self.system_prompt = """You are an expert quantitative trading strategy assistant with MT5 integration capabilities. Your role is to help users create Python trading strategies and deploy them directly to MetaTrader 5.

Key capabilities:
- Analyze user requests for trading strategy requirements
- Generate complete Python trading strategy code
- Explain strategy logic and parameters
- Suggest risk management approaches
- Provide backtesting recommendations
- Deploy strategies directly to MT5 for live/demo trading

When generating code, always include:
- Clear class structure with descriptive names
- Proper entry and exit conditions
- Risk management parameters (default 2% risk per trade)
- Position sizing logic based on account balance
- Error handling and logging
- Comments explaining the strategy logic

Required methods for MT5 deployment:
- __init__(self, symbol="EURUSD", risk_percent=2.0): Initialize strategy
- generate_signals(self, data): Return buy_signal, sell_signal based on market data
- calculate_position_size(self, account_balance, stop_loss_pips): Calculate position size

Popular strategy types you can help with:
- Mean reversion (RSI, Bollinger Bands)
- Momentum (MACD, Moving Average Crossover)
- Range trading (Support/Resistance)
- Machine learning based strategies
- Multi-timeframe strategies

MT5 Integration Features:
- Automatic strategy validation and deployment
- Real-time position monitoring
- P&L tracking and performance analytics
- Risk management with automatic stop losses
- Demo and live trading modes

Always prioritize code quality, readability, and risk management. Include deployment instructions and risk warnings."""

        self.strategy_keywords = {
            'mean_reversion': ['mean reversion', 'rsi', 'bollinger', 'oversold', 'overbought', 'revert'],
            'momentum': ['momentum', 'macd', 'trend', 'breakout', 'moving average', 'crossover'],
            'range_trading': ['range', 'support', 'resistance', 'channel', 'twin range'],
            'machine_learning': ['ml', 'machine learning', 'random forest', 'neural', 'ai', 'predict'],
            'scalping': ['scalp', 'quick', 'fast', 'minute', 'second'],
            'swing': ['swing', 'daily', 'weekly', 'position']
        }
        
    async def initialize(self):
        """Initialize the chatbot and check Ollama connectivity"""
        try:
            # Initialize Ollama client
            await self.ollama_client._ensure_session()
            
            # Check health and available models
            health_status = await self.ollama_client.health_check()
            logger.info(f"Ollama health status: {health_status}")
            
            return health_status
            
        except Exception as e:
            logger.error(f"Failed to initialize chatbot: {str(e)}")
            return {
                'status': 'error',
                'available': False,
                'error': str(e),
                'message': 'Failed to initialize enhanced chatbot'
            }
            
    async def process_message(
        self,
        user_message: str,
        conversation_id: str = "default",
        use_llm: bool = True,
        model: str = "llama3.2"
    ) -> Dict[str, Any]:
        """
        Process user message and generate intelligent response
        
        Args:
            user_message: User's trading strategy request
            conversation_id: Unique conversation identifier
            use_llm: Whether to use LLM (True) or fall back to templates
            model: Ollama model to use
            
        Returns:
            Dict with response, template data, and metadata
        """
        try:
            # Initialize conversation context if needed
            if conversation_id not in self.conversation_context:
                self.conversation_context[conversation_id] = []
                
            # Extract strategy information from user message
            strategy_info = self._extract_strategy_info(user_message)
            
            # Try LLM first if available and requested
            if use_llm:
                try:
                    llm_response = await self._generate_llm_response(
                        user_message, conversation_id, strategy_info, model
                    )
                    if llm_response['success']:
                        return llm_response
                    else:
                        logger.warning(f"LLM response failed: {llm_response.get('error', 'Unknown error')}")
                except Exception as e:
                    logger.warning(f"LLM processing failed: {str(e)}")
                    
            # Fallback to template system
            logger.info("Falling back to template system")
            template_response = await self._generate_template_response(user_message, strategy_info)
            template_response['is_template_fallback'] = True
            
            return template_response
            
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            return {
                'response': f"I'm sorry, I encountered an error processing your request: {str(e)}",
                'success': False,
                'error': str(e),
                'conversation_id': conversation_id,
                'is_template_fallback': True
            }
            
    def _extract_strategy_info(self, user_message: str) -> Dict[str, Any]:
        """Extract strategy-related information from user message"""
        message_lower = user_message.lower()
        
        # Determine strategy type
        strategy_type = 'custom'
        for strategy, keywords in self.strategy_keywords.items():
            if any(keyword in message_lower for keyword in keywords):
                strategy_type = strategy
                break
                
        # Extract currency pairs
        currency_pairs = re.findall(r'\b([A-Z]{3}[/\\-]?[A-Z]{3})\b', user_message.upper())
        
        # Extract timeframes
        timeframes = re.findall(r'\b(\d+(?:m|h|d|w|M))\b', user_message.lower())
        
        # Extract risk percentage
        risk_match = re.search(r'(\d+(?:\.\d+)?)\s*%?\s*risk', message_lower)
        risk_percent = float(risk_match.group(1)) if risk_match else 2.0
        
        # Extract indicators mentioned
        indicators = []
        indicator_patterns = {
            'rsi': r'\brsi\b',
            'macd': r'\bmacd\b',
            'bollinger': r'\bbollinger\b',
            'ema': r'\bema\b',
            'sma': r'\bsma\b',
            'stochastic': r'\bstochastic\b',
            'atr': r'\batr\b'
        }
        
        for indicator, pattern in indicator_patterns.items():
            if re.search(pattern, message_lower):
                indicators.append(indicator)
                
        return {
            'strategy_type': strategy_type,
            'currency_pairs': currency_pairs,
            'timeframes': timeframes,
            'risk_percent': risk_percent,
            'indicators': indicators,
            'original_message': user_message
        }
        
    async def _generate_llm_response(
        self,
        user_message: str,
        conversation_id: str,
        strategy_info: Dict[str, Any],
        model: str
    ) -> Dict[str, Any]:
        """Generate response using Ollama LLM"""
        try:
            # Create enhanced prompt with strategy context
            enhanced_prompt = self._create_enhanced_prompt(user_message, strategy_info)
            
            # Get LLM response
            llm_result = await self.ollama_client.chat_completion(
                message=enhanced_prompt,
                model=model,
                conversation_id=conversation_id,
                system_prompt=self.system_prompt,
                temperature=0.7
            )
            
            if llm_result.get('success', False):
                # Extract code from response if present
                code = self._extract_code_from_response(llm_result['response'])
                
                # Update conversation context
                self.conversation_context[conversation_id].append({
                    'user': user_message,
                    'assistant': llm_result['response'],
                    'timestamp': datetime.now().isoformat(),
                    'strategy_info': strategy_info
                })
                
                return {
                    'response': llm_result['response'],
                    'success': True,
                    'conversation_id': conversation_id,
                    'model': model,
                    'template_data': {
                        'strategy_name': strategy_info.get('strategy_type', 'Custom Strategy'),
                        'category': strategy_info.get('strategy_type', 'custom'),
                        'code': code,
                        'description': f"LLM-generated {strategy_info.get('strategy_type', 'custom')} strategy",
                        'parameters': strategy_info
                    },
                    'is_template_fallback': False
                }
            else:
                return {
                    'response': f"LLM Error: {llm_result.get('error', 'Unknown error')}",
                    'success': False,
                    'error': llm_result.get('error', 'Unknown error'),
                    'conversation_id': conversation_id,
                    'model': model
                }
                
        except Exception as e:
            logger.error(f"LLM response generation failed: {str(e)}")
            return {
                'response': f"LLM processing error: {str(e)}",
                'success': False,
                'error': str(e),
                'conversation_id': conversation_id,
                'model': model
            }
            
    def _create_enhanced_prompt(self, user_message: str, strategy_info: Dict[str, Any]) -> str:
        """Create enhanced prompt with extracted strategy information"""
        prompt_parts = [
            f"User Request: {user_message}",
            "",
            "Extracted Strategy Information:",
            f"- Strategy Type: {strategy_info.get('strategy_type', 'Not specified')}",
            f"- Currency Pairs: {', '.join(strategy_info.get('currency_pairs', ['Not specified']))}",
            f"- Timeframes: {', '.join(strategy_info.get('timeframes', ['Not specified']))}",
            f"- Risk Percentage: {strategy_info.get('risk_percent', 'Not specified')}%",
            f"- Indicators: {', '.join(strategy_info.get('indicators', ['Not specified']))}",
            "",
            "Please provide a comprehensive response that includes:",
            "1. A detailed explanation of the strategy approach",
            "2. Complete Python code implementation",
            "3. Risk management considerations",
            "4. Usage instructions",
            "",
            "Format the code in proper Python class structure with clear comments."
        ]
        
        return "\n".join(prompt_parts)
        
    def _extract_code_from_response(self, response: str) -> Optional[str]:
        """Extract Python code from LLM response"""
        # Look for code blocks marked with ```python or ```
        code_patterns = [
            r'```python\n(.*?)\n```',
            r'```\n(.*?)\n```',
            r'class\s+\w+.*?(?=\n\n|\Z)',  # Class definitions
        ]
        
        for pattern in code_patterns:
            matches = re.findall(pattern, response, re.DOTALL)
            if matches:
                return matches[0].strip()
                
        # If no code blocks found, look for class definitions
        lines = response.split('\n')
        code_lines = []
        in_code = False
        
        for line in lines:
            if line.strip().startswith('class ') or line.strip().startswith('def '):
                in_code = True
            if in_code:
                code_lines.append(line)
            if in_code and line.strip() == '' and len(code_lines) > 10:
                break
                
        return '\n'.join(code_lines) if code_lines else None
        
    async def _generate_template_response(self, user_message: str, strategy_info: Dict[str, Any]) -> Dict[str, Any]:
        """Generate response using template system fallback"""
        # This would integrate with the existing template manager
        # For now, provide a basic response structure
        
        strategy_type = strategy_info.get('strategy_type', 'custom')
        
        template_responses = {
            'mean_reversion': {
                'description': 'A mean reversion strategy that identifies overbought/oversold conditions',
                'code': self._get_mean_reversion_template(strategy_info),
                'category': 'mean_reversion'
            },
            'momentum': {
                'description': 'A momentum strategy that follows trend direction',
                'code': self._get_momentum_template(strategy_info),
                'category': 'momentum'
            },
            'range_trading': {
                'description': 'A range trading strategy that trades between support and resistance',
                'code': self._get_range_trading_template(strategy_info),
                'category': 'range_trading'
            }
        }
        
        template_data = template_responses.get(strategy_type, {
            'description': 'A custom trading strategy based on your requirements',
            'code': self._get_custom_template(strategy_info),
            'category': 'custom'
        })
        
        response_text = f"""I've created a {strategy_type.replace('_', ' ')} strategy based on your request.

**Strategy Overview:**
{template_data['description']}

**Key Features:**
- Risk management with {strategy_info.get('risk_percent', 2)}% risk per trade
- Supports multiple timeframes
- Includes proper entry and exit conditions
- Built-in error handling and logging

**Usage:**
1. Copy the code and save it as a Python file
2. Adjust parameters as needed
3. Run backtests before live trading
4. Monitor performance and adjust risk settings

The code includes detailed comments to help you understand and customize the strategy."""

        return {
            'response': response_text,
            'success': True,
            'template_data': {
                'strategy_name': f"{strategy_type.replace('_', ' ').title()} Strategy",
                'category': template_data['category'],
                'code': template_data['code'],
                'description': template_data['description'],
                'parameters': strategy_info
            },
            'is_template_fallback': True
        }
        
    def _get_mean_reversion_template(self, strategy_info: Dict[str, Any]) -> str:
        """Generate mean reversion strategy template"""
        return '''
class MeanReversionStrategy:
    """Mean Reversion Strategy using RSI and Bollinger Bands"""
    
    def __init__(self, symbol="EURUSD", risk_percent=2.0):
        self.symbol = symbol
        self.risk_percent = risk_percent / 100
        self.rsi_period = 14
        self.bb_period = 20
        self.bb_std = 2
        
    def calculate_indicators(self, data):
        """Calculate RSI and Bollinger Bands"""
        # RSI calculation
        delta = data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=self.rsi_period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=self.rsi_period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        # Bollinger Bands
        ma = data['close'].rolling(window=self.bb_period).mean()
        std = data['close'].rolling(window=self.bb_period).std()
        upper_band = ma + (std * self.bb_std)
        lower_band = ma - (std * self.bb_std)
        
        return rsi, upper_band, lower_band
        
    def generate_signals(self, data):
        """Generate buy/sell signals"""
        rsi, upper_band, lower_band = self.calculate_indicators(data)
        
        # Entry conditions
        buy_signal = (rsi < 30) & (data['close'] < lower_band)
        sell_signal = (rsi > 70) & (data['close'] > upper_band)
        
        return buy_signal, sell_signal
        
    def calculate_position_size(self, account_balance, stop_loss_pips):
        """Calculate position size based on risk management"""
        risk_amount = account_balance * self.risk_percent
        pip_value = 10  # Adjust based on currency pair
        position_size = risk_amount / (stop_loss_pips * pip_value)
        return position_size
'''
        
    def _get_momentum_template(self, strategy_info: Dict[str, Any]) -> str:
        """Generate momentum strategy template"""
        return '''
class MomentumStrategy:
    """Momentum Strategy using MACD and Moving Averages"""
    
    def __init__(self, symbol="EURUSD", risk_percent=2.0):
        self.symbol = symbol
        self.risk_percent = risk_percent / 100
        self.fast_ma = 12
        self.slow_ma = 26
        self.signal_ma = 9
        
    def calculate_macd(self, data):
        """Calculate MACD indicator"""
        ema_fast = data['close'].ewm(span=self.fast_ma).mean()
        ema_slow = data['close'].ewm(span=self.slow_ma).mean()
        macd_line = ema_fast - ema_slow
        signal_line = macd_line.ewm(span=self.signal_ma).mean()
        histogram = macd_line - signal_line
        
        return macd_line, signal_line, histogram
        
    def generate_signals(self, data):
        """Generate momentum-based signals"""
        macd_line, signal_line, histogram = self.calculate_macd(data)
        
        # Entry conditions
        buy_signal = (macd_line > signal_line) & (histogram > 0)
        sell_signal = (macd_line < signal_line) & (histogram < 0)
        
        return buy_signal, sell_signal
        
    def calculate_position_size(self, account_balance, stop_loss_pips):
        """Calculate position size based on risk management"""
        risk_amount = account_balance * self.risk_percent
        pip_value = 10  # Adjust based on currency pair
        position_size = risk_amount / (stop_loss_pips * pip_value)
        return position_size
'''
        
    def _get_range_trading_template(self, strategy_info: Dict[str, Any]) -> str:
        """Generate range trading strategy template"""
        return '''
class RangeTradingStrategy:
    """Range Trading Strategy using Support and Resistance"""
    
    def __init__(self, symbol="EURUSD", risk_percent=2.0):
        self.symbol = symbol
        self.risk_percent = risk_percent / 100
        self.lookback_period = 20
        
    def identify_support_resistance(self, data):
        """Identify support and resistance levels"""
        high_rolling = data['high'].rolling(window=self.lookback_period)
        low_rolling = data['low'].rolling(window=self.lookback_period)
        
        resistance = high_rolling.max()
        support = low_rolling.min()
        
        return support, resistance
        
    def generate_signals(self, data):
        """Generate range trading signals"""
        support, resistance = self.identify_support_resistance(data)
        
        # Entry conditions
        buy_signal = data['close'] <= support * 1.001  # Near support
        sell_signal = data['close'] >= resistance * 0.999  # Near resistance
        
        return buy_signal, sell_signal
        
    def calculate_position_size(self, account_balance, stop_loss_pips):
        """Calculate position size based on risk management"""
        risk_amount = account_balance * self.risk_percent
        pip_value = 10  # Adjust based on currency pair
        position_size = risk_amount / (stop_loss_pips * pip_value)
        return position_size
'''
        
    def _get_custom_template(self, strategy_info: Dict[str, Any]) -> str:
        """Generate custom strategy template"""
        return '''
class CustomTradingStrategy:
    """Custom Trading Strategy Template"""
    
    def __init__(self, symbol="EURUSD", risk_percent=2.0):
        self.symbol = symbol
        self.risk_percent = risk_percent / 100
        # Add your custom parameters here
        
    def calculate_indicators(self, data):
        """Calculate your custom indicators"""
        # Implement your indicator calculations
        pass
        
    def generate_signals(self, data):
        """Generate trading signals based on your logic"""
        # Implement your signal generation logic
        buy_signal = False  # Replace with your buy condition
        sell_signal = False  # Replace with your sell condition
        
        return buy_signal, sell_signal
        
    def calculate_position_size(self, account_balance, stop_loss_pips):
        """Calculate position size based on risk management"""
        risk_amount = account_balance * self.risk_percent
        pip_value = 10  # Adjust based on currency pair
        position_size = risk_amount / (stop_loss_pips * pip_value)
        return position_size
        
    def execute_trade(self, signal_type, position_size):
        """Execute the trading signal"""
        # Implement your trade execution logic
        pass
'''
        
    async def get_conversation_history(self, conversation_id: str) -> List[Dict[str, Any]]:
        """Get conversation history for given ID"""
        return self.conversation_context.get(conversation_id, [])
        
    async def clear_conversation(self, conversation_id: str) -> bool:
        """Clear conversation history"""
        try:
            if conversation_id in self.conversation_context:
                del self.conversation_context[conversation_id]
            await self.ollama_client.clear_conversation(conversation_id)
            return True
        except Exception as e:
            logger.error(f"Error clearing conversation {conversation_id}: {str(e)}")
            return False
            
    async def get_health_status(self) -> Dict[str, Any]:
        """Get current health status of the chatbot"""
        return await self.ollama_client.health_check()
        
    async def close(self):
        """Clean up resources"""
        await self.ollama_client.close()
