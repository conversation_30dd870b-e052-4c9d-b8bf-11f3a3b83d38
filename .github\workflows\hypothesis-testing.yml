name: Property-Based Testing with Hypothesis

on:
  push:
    branches: [ "main", "develop" ]
  pull_request:
    branches: [ "main", "develop" ]
  schedule:
    # Run hypothesis tests daily at 2 AM UTC
    - cron: '0 2 * * *'

jobs:
  hypothesis-tests:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.9", "3.11"]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest hypothesis faker
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
        if [ -f requirements-dev.txt ]; then pip install -r requirements-dev.txt; fi
    
    - name: Run property-based tests with Hypothesis
      run: |
        # Run with increased examples for thorough testing
        pytest tests/ -k "hypothesis" --hypothesis-show-statistics -v
      env:
        HYPOTHESIS_PROFILE: ci
    
    - name: Run chatbot regression tests with Hypothesis
      run: |
        pytest tests/test_chatbot_regression.py --hypothesis-show-statistics -v
    
    - name: Run extended Hypothesis testing (scheduled only)
      if: github.event_name == 'schedule'
      run: |
        # Extended testing with more examples for scheduled runs
        pytest tests/ -k "hypothesis" --hypothesis-show-statistics --hypothesis-seed=random -v
      env:
        HYPOTHESIS_PROFILE: extensive

  fuzz-testing:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest hypothesis atheris
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
    
    - name: Run fuzzing tests
      run: |
        # Create and run basic fuzzing tests for chatbot
        python -c "
import sys
sys.path.append('src')
from chatbot.knowledge_base import TradingChatbot, KnowledgeBase
import tempfile
import os
import random
import string

def fuzz_chatbot():
    temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
    temp_db.close()
    
    try:
        kb = KnowledgeBase(temp_db.name)
        chatbot = TradingChatbot(kb)
        chatbot.add_trading_knowledge()
        
        # Generate random inputs
        for _ in range(100):
            # Random string input
            random_input = ''.join(random.choices(string.ascii_letters + string.digits + string.punctuation + ' ', k=random.randint(0, 200)))
            
            try:
                response = chatbot.answer(random_input)
                # Verify no-hallucination guarantee
                has_source = 'source' in response.lower() or 'hash' in response.lower()
                has_idk = 'i don\\'t know' in response.lower()
                assert has_source or has_idk, f'Regression violation with input: {random_input[:50]}'
                print(f'✅ Fuzz test passed for input length {len(random_input)}')
            except Exception as e:
                print(f'⚠️  Exception with input {random_input[:50]}: {e}')
                # Exceptions are acceptable, but responses must still be compliant
                pass
    
    finally:
        if os.path.exists(temp_db.name):
            os.unlink(temp_db.name)

if __name__ == '__main__':
    fuzz_chatbot()
    print('🎯 Fuzzing tests completed successfully!')
        "

  stress-testing:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest hypothesis psutil memory-profiler
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
    
    - name: Run stress tests
      run: |
        python -c "
import sys
sys.path.append('src')
from chatbot.knowledge_base import TradingChatbot, KnowledgeBase
import tempfile
import os
import time
import psutil
import threading

def stress_test_chatbot():
    temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
    temp_db.close()
    
    try:
        kb = KnowledgeBase(temp_db.name)
        chatbot = TradingChatbot(kb)
        chatbot.add_trading_knowledge()
        
        # Stress test queries
        queries = [
            'What is RSI?',
            'Show me GBPUSD backtest',
            'Who won the World Series?',
            'Tell me about MACD',
            'What is quantum physics?'
        ]
        
        start_time = time.time()
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Run 1000 queries rapidly
        for i in range(1000):
            query = queries[i % len(queries)]
            response = chatbot.answer(query)
            
            # Verify compliance
            has_source = 'source' in response.lower() or 'hash' in response.lower()
            has_idk = 'i don\\'t know' in response.lower()
            assert has_source or has_idk, f'Regression violation at iteration {i}'
            
            if i % 100 == 0:
                current_memory = process.memory_info().rss / 1024 / 1024
                print(f'✅ Completed {i} queries, Memory: {current_memory:.1f}MB')
        
        end_time = time.time()
        final_memory = process.memory_info().rss / 1024 / 1024
        
        print(f'🎯 Stress test completed!')
        print(f'   Total queries: 1000')
        print(f'   Total time: {end_time - start_time:.2f}s')
        print(f'   Queries/sec: {1000 / (end_time - start_time):.1f}')
        print(f'   Memory usage: {initial_memory:.1f}MB -> {final_memory:.1f}MB')
        print(f'   Memory increase: {final_memory - initial_memory:.1f}MB')
    
    finally:
        if os.path.exists(temp_db.name):
            os.unlink(temp_db.name)

if __name__ == '__main__':
    stress_test_chatbot()
        "

  concurrent-testing:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest hypothesis threading
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
    
    - name: Run concurrent tests
      run: |
        python -c "
import sys
sys.path.append('src')
from chatbot.knowledge_base import TradingChatbot, KnowledgeBase
import tempfile
import os
import threading
import time

def concurrent_test_chatbot():
    temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
    temp_db.close()
    
    try:
        kb = KnowledgeBase(temp_db.name)
        chatbot = TradingChatbot(kb)
        chatbot.add_trading_knowledge()
        
        results = []
        errors = []
        
        def worker_thread(thread_id, num_queries):
            for i in range(num_queries):
                try:
                    query = f'What is RSI? (thread {thread_id}, query {i})'
                    response = chatbot.answer(query)
                    
                    # Verify compliance
                    has_source = 'source' in response.lower() or 'hash' in response.lower()
                    has_idk = 'i don\\'t know' in response.lower()
                    
                    if not (has_source or has_idk):
                        errors.append(f'Thread {thread_id}, Query {i}: Regression violation')
                    else:
                        results.append(f'Thread {thread_id}, Query {i}: OK')
                        
                except Exception as e:
                    errors.append(f'Thread {thread_id}, Query {i}: Exception - {e}')
        
        # Start 10 concurrent threads
        threads = []
        for thread_id in range(10):
            thread = threading.Thread(target=worker_thread, args=(thread_id, 50))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        print(f'🎯 Concurrent test completed!')
        print(f'   Successful queries: {len(results)}')
        print(f'   Errors: {len(errors)}')
        
        if errors:
            print('❌ Errors found:')
            for error in errors[:10]:  # Show first 10 errors
                print(f'   {error}')
            if len(errors) > 10:
                print(f'   ... and {len(errors) - 10} more errors')
        else:
            print('✅ No errors found in concurrent testing!')
    
    finally:
        if os.path.exists(temp_db.name):
            os.unlink(temp_db.name)

if __name__ == '__main__':
    concurrent_test_chatbot()
        "