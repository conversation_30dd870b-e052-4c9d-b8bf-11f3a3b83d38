# src/risk/correlation_analyzer.py
import logging
from typing import Dict, List, Tuple
from dataclasses import dataclass
from .models import Position

logger = logging.getLogger(__name__)

@dataclass
class CorrelationRisk:
    overall_score: float
    pair_correlations: Dict[str, float]
    warnings: List[str]
    diversification_score: float

class CorrelationAnalyzer:
    """Analyzes correlation risk in forex portfolios"""
    
    def __init__(self):
        self.logger = logger
        # Simplified correlation matrix for major forex pairs
        # In production, this would be dynamically updated from market data
        self._correlation_matrix = self._initialize_correlation_matrix()
    
    def _initialize_correlation_matrix(self) -> Dict[str, Dict[str, float]]:
        """Initialize correlation matrix for major forex pairs"""
        # Simplified correlation data - in production this would come from historical analysis
        return {
            'EURUSD': {
                'EURUSD': 1.0, 'GBPUSD': 0.75, 'USDJPY': -0.65, 'USDCHF': -0.85,
                'AUDUSD': 0.70, 'NZDUSD': 0.68, 'USDCAD': -0.72, 'EURJPY': 0.45,
                'EURGBP': 0.25, 'EURAUD': 0.55, 'EURCHF': 0.90
            },
            'GBPUSD': {
                'EURUSD': 0.75, 'GBPUSD': 1.0, 'USDJPY': -0.55, 'USDCHF': -0.70,
                'AUDUSD': 0.65, 'NZDUSD': 0.60, 'USDCAD': -0.60, 'EURJPY': 0.35,
                'EURGBP': -0.45, 'GBPJPY': 0.40, 'GBPCHF': 0.65
            },
            'USDJPY': {
                'EURUSD': -0.65, 'GBPUSD': -0.55, 'USDJPY': 1.0, 'USDCHF': 0.45,
                'AUDUSD': -0.50, 'NZDUSD': -0.45, 'USDCAD': 0.40, 'EURJPY': 0.75,
                'GBPJPY': 0.70, 'AUDJPY': 0.60, 'NZDJPY': 0.55
            },
            'USDCHF': {
                'EURUSD': -0.85, 'GBPUSD': -0.70, 'USDJPY': 0.45, 'USDCHF': 1.0,
                'AUDUSD': -0.60, 'NZDUSD': -0.55, 'USDCAD': 0.50, 'EURCHF': -0.95,
                'GBPCHF': -0.80, 'AUDCHF': -0.65, 'NZDCHF': -0.60
            },
            'AUDUSD': {
                'EURUSD': 0.70, 'GBPUSD': 0.65, 'USDJPY': -0.50, 'USDCHF': -0.60,
                'AUDUSD': 1.0, 'NZDUSD': 0.85, 'USDCAD': -0.55, 'EURAUD': 0.40,
                'GBPAUD': 0.35, 'AUDJPY': 0.45, 'AUDCHF': 0.55
            },
            'NZDUSD': {
                'EURUSD': 0.68, 'GBPUSD': 0.60, 'USDJPY': -0.45, 'USDCHF': -0.55,
                'AUDUSD': 0.85, 'NZDUSD': 1.0, 'USDCAD': -0.50, 'EURNZD': 0.35,
                'GBPNZD': 0.30, 'NZDJPY': 0.40, 'NZDCHF': 0.50
            },
            'USDCAD': {
                'EURUSD': -0.72, 'GBPUSD': -0.60, 'USDJPY': 0.40, 'USDCHF': 0.50,
                'AUDUSD': -0.55, 'NZDUSD': -0.50, 'USDCAD': 1.0, 'EURCAD': -0.45,
                'GBPCAD': -0.40, 'AUDCAD': -0.50, 'NZDCAD': -0.45
            }
        }
    
    def calculate_portfolio_correlation_risk(self, positions: List[Position]) -> CorrelationRisk:
        """Calculate overall correlation risk for a portfolio"""
        if len(positions) < 2:
            return CorrelationRisk(
                overall_score=0.0,
                pair_correlations={},
                warnings=[],
                diversification_score=1.0
            )
        
        pair_correlations = {}
        correlation_scores = []
        warnings = []
        
        # Calculate pairwise correlations weighted by position sizes
        for i, pos1 in enumerate(positions):
            for j, pos2 in enumerate(positions[i+1:], i+1):
                correlation = self._get_pair_correlation(pos1.symbol, pos2.symbol)
                
                # Weight correlation by position sizes
                weight1 = abs(pos1.size)
                weight2 = abs(pos2.size)
                weighted_correlation = correlation * (weight1 * weight2)
                
                pair_key = f"{pos1.symbol}-{pos2.symbol}"
                pair_correlations[pair_key] = correlation
                correlation_scores.append(abs(weighted_correlation))
                
                # Check for high correlation warnings
                if abs(correlation) > 0.8:
                    warnings.append(f"HIGH_CORRELATION: {pair_key} ({correlation:.2f})")
        
        # Calculate overall correlation score
        overall_score = sum(correlation_scores) / len(correlation_scores) if correlation_scores else 0.0
        
        # Calculate diversification score
        diversification_score = max(0.0, 1.0 - overall_score)
        
        # Check for currency concentration
        currency_exposure = self._analyze_currency_concentration(positions)
        for currency, exposure in currency_exposure.items():
            if exposure > 0.6:  # More than 60% exposure to single currency
                warnings.append(f"HIGH_CURRENCY_CONCENTRATION: {currency} ({exposure:.1%})")
        
        return CorrelationRisk(
            overall_score=overall_score,
            pair_correlations=pair_correlations,
            warnings=warnings,
            diversification_score=diversification_score
        )
    
    def _get_pair_correlation(self, symbol1: str, symbol2: str) -> float:
        """Get correlation between two currency pairs"""
        # Direct lookup
        if symbol1 in self._correlation_matrix and symbol2 in self._correlation_matrix[symbol1]:
            return self._correlation_matrix[symbol1][symbol2]
        
        # Reverse lookup
        if symbol2 in self._correlation_matrix and symbol1 in self._correlation_matrix[symbol2]:
            return self._correlation_matrix[symbol2][symbol1]
        
        # If not found, calculate based on common currencies
        return self._estimate_correlation(symbol1, symbol2)
    
    def _estimate_correlation(self, symbol1: str, symbol2: str) -> float:
        """Estimate correlation between two pairs based on common currencies"""
        if len(symbol1) != 6 or len(symbol2) != 6:
            return 0.0
        
        base1, quote1 = symbol1[:3], symbol1[3:]
        base2, quote2 = symbol2[:3], symbol2[3:]
        
        # Same pair
        if symbol1 == symbol2:
            return 1.0
        
        # Inverse pairs (e.g., EURUSD vs USDEUR)
        if base1 == quote2 and quote1 == base2:
            return -1.0
        
        # Common base currency
        if base1 == base2:
            return 0.7
        
        # Common quote currency
        if quote1 == quote2:
            return 0.6
        
        # One currency in common
        if base1 == quote2 or quote1 == base2:
            return -0.5
        
        # No common currencies
        return 0.1
    
    def _analyze_currency_concentration(self, positions: List[Position]) -> Dict[str, float]:
        """Analyze concentration of individual currencies"""
        currency_exposure = {}
        total_exposure = sum(abs(pos.size) for pos in positions)
        
        if total_exposure == 0:
            return currency_exposure
        
        for position in positions:
            if len(position.symbol) == 6:
                base_currency = position.symbol[:3]
                quote_currency = position.symbol[3:]
                
                # Base currency exposure (positive for long, negative for short)
                base_exposure = position.size / total_exposure
                quote_exposure = -position.size / total_exposure  # Opposite exposure
                
                currency_exposure[base_currency] = currency_exposure.get(base_currency, 0) + abs(base_exposure)
                currency_exposure[quote_currency] = currency_exposure.get(quote_currency, 0) + abs(quote_exposure)
        
        return currency_exposure
    
    def get_correlation_matrix(self) -> Dict[str, Dict[str, float]]:
        """Get the current correlation matrix"""
        return self._correlation_matrix.copy()
    
    def update_correlation(self, symbol1: str, symbol2: str, correlation: float) -> None:
        """Update correlation between two pairs"""
        if not -1.0 <= correlation <= 1.0:
            raise ValueError("Correlation must be between -1 and 1")
        
        # Ensure both symbols exist in matrix
        if symbol1 not in self._correlation_matrix:
            self._correlation_matrix[symbol1] = {}
        if symbol2 not in self._correlation_matrix:
            self._correlation_matrix[symbol2] = {}
        
        # Update both directions
        self._correlation_matrix[symbol1][symbol2] = correlation
        self._correlation_matrix[symbol2][symbol1] = correlation
        
        self.logger.info(f"Updated correlation {symbol1}-{symbol2}: {correlation}")
    
    def get_diversification_suggestions(self, positions: List[Position]) -> List[str]:
        """Get suggestions for improving portfolio diversification"""
        suggestions = []
        
        if len(positions) < 2:
            suggestions.append("Consider adding more positions to improve diversification")
            return suggestions
        
        correlation_risk = self.calculate_portfolio_correlation_risk(positions)
        
        if correlation_risk.overall_score > 0.7:
            suggestions.append("Portfolio has high correlation risk - consider reducing correlated positions")
        
        if correlation_risk.diversification_score < 0.3:
            suggestions.append("Poor diversification - consider adding uncorrelated instruments")
        
        # Analyze currency concentration
        currency_exposure = self._analyze_currency_concentration(positions)
        dominant_currencies = [
            currency for currency, exposure in currency_exposure.items() 
            if exposure > 0.5
        ]
        
        if dominant_currencies:
            suggestions.append(f"High concentration in {', '.join(dominant_currencies)} - consider diversifying")
        
        # Suggest specific pairs for diversification
        existing_symbols = {pos.symbol for pos in positions}
        diversification_candidates = self._get_diversification_candidates(existing_symbols)
        
        if diversification_candidates:
            suggestions.append(f"Consider adding: {', '.join(diversification_candidates[:3])}")
        
        return suggestions
    
    def _get_diversification_candidates(self, existing_symbols: set) -> List[str]:
        """Get currency pairs that would improve diversification"""
        candidates = []
        
        # Major pairs that provide good diversification
        diversification_pairs = [
            'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'NZDUSD', 'USDCAD'
        ]
        
        for pair in diversification_pairs:
            if pair not in existing_symbols:
                # Check if this pair would reduce overall correlation
                avg_correlation = self._calculate_average_correlation(pair, existing_symbols)
                if avg_correlation < 0.5:  # Low correlation with existing positions
                    candidates.append(pair)
        
        return sorted(candidates, key=lambda p: self._calculate_average_correlation(p, existing_symbols))
    
    def _calculate_average_correlation(self, new_pair: str, existing_symbols: set) -> float:
        """Calculate average correlation of new pair with existing positions"""
        if not existing_symbols:
            return 0.0
        
        correlations = []
        for existing_pair in existing_symbols:
            correlation = abs(self._get_pair_correlation(new_pair, existing_pair))
            correlations.append(correlation)
        
        return sum(correlations) / len(correlations)