# Darwin Gödel Machine - Quick Start Guide

## 🚀 **Ready to Evolve Trading Strategies!**

Your Darwin Gödel Machine integration is complete and ready to use. Follow these steps to start evolving profitable trading strategies.

## ⚡ **Quick Start (5 minutes)**

### 1. **Install Python Dependencies**
```bash
# Navigate to shared directory
cd shared/

# Install required packages
pip install asyncio pandas numpy websockets uuid logging dataclasses typing datetime concurrent.futures
```

### 2. **Start the Darwin Bridge**
```bash
# From the shared directory
python darwin_bridge.py
```
*Keep this terminal open - it's your Python ↔ Node.js bridge*

### 3. **Start the Backend Server**
```bash
# In a new terminal, navigate to backend
cd backend/

# Start the development server
npm run dev
```

### 4. **Test the Integration**
```bash
# In a new terminal, run the integration test
cd "c:/Users/<USER>/Projects/AI Enhanced Trading Platform-Sonnet-GPTmini"
node test-darwin-integration.js
```

## 🧪 **First Evolution Test**

### Start Your First Evolution
```bash
curl -X POST http://localhost:3001/api/darwin-evolution/evolve \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "pair": "EURUSD",
    "timeframe": "1H",
    "evolution_params": {
      "population_size": 20,
      "max_generations": 10,
      "mutation_rate": 0.15,
      "fitness_objective": "sharpe_ratio"
    }
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "data": {
    "job_id": "abc123-def456-ghi789",
    "status": "started",
    "message": "Darwin evolution process initiated successfully",
    "estimated_duration_minutes": 15
  }
}
```

### Monitor Progress
```bash
curl -X GET http://localhost:3001/api/darwin-evolution/status/abc123-def456-ghi789 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Get Results
```bash
curl -X GET http://localhost:3001/api/darwin-evolution/results/abc123-def456-ghi789 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 🎯 **What Happens During Evolution**

1. **Population Creation**: Darwin creates 20 random trading strategies
2. **Fitness Testing**: Each strategy is backtested and scored
3. **Selection**: Best strategies are selected for breeding
4. **Crossover**: Strategies exchange genetic material (trading rules)
5. **Mutation**: Random variations are introduced
6. **Verification**: Top strategies are mathematically verified with Coq
7. **Iteration**: Process repeats for 10 generations
8. **Results**: Best evolved strategies are returned

## 📊 **Expected Output**

After evolution completes, you'll get:

- **Best Strategies**: Top-performing trading strategies
- **Forex Genome**: Market behavior analysis for the pair
- **Verification Proofs**: Mathematical certainty guarantees
- **Performance Metrics**: Sharpe ratio, profit factor, win rate
- **Strategy Details**: Entry/exit conditions, risk management

## 🔧 **Configuration Options**

### Evolution Parameters
```json
{
  "population_size": 50,      // Number of strategies per generation
  "max_generations": 30,      // How many evolution cycles
  "mutation_rate": 0.15,      // How much random variation (0-1)
  "fitness_objective": "sharpe_ratio", // What to optimize for
  "verification_enabled": true // Enable mathematical proofs
}
```

### Fitness Objectives
- `sharpe_ratio` - Risk-adjusted returns
- `profit_factor` - Gross profit / gross loss
- `win_rate` - Percentage of winning trades
- `max_drawdown` - Maximum loss from peak

## 🚨 **Troubleshooting**

### Python Bridge Not Starting
```bash
# Check Python version (3.7+ required)
python --version

# Install missing dependencies
pip install -r requirements.txt  # If you have one
```

### Backend Compilation Errors
```bash
# Rebuild shared schemas
cd shared/
npm run build

# Rebuild backend
cd ../backend/
npm run build
```

### Authentication Issues
- Make sure you have a valid JWT token
- Check that auth middleware is properly configured
- For testing, you can temporarily disable auth on Darwin routes

### No Evolution Progress
- Check that Python bridge is running
- Verify PYTHON_ENGINE_URL environment variable
- Check logs in `logs/combined.log`

## 🎉 **Success Indicators**

You'll know everything is working when:

✅ Python bridge starts without errors  
✅ Backend server starts on port 3001  
✅ Integration test passes all checks  
✅ First evolution request returns a job ID  
✅ Status endpoint shows "running" then "completed"  
✅ Results endpoint returns evolved strategies  

## 🔗 **API Endpoints**

| Endpoint | Method | Purpose |
|----------|--------|---------|
| `/api/darwin-evolution/evolve` | POST | Start evolution |
| `/api/darwin-evolution/status/:id` | GET | Check progress |
| `/api/darwin-evolution/results/:id` | GET | Get final results |
| `/api/darwin-evolution/strategies/:id` | GET | Get best strategies |
| `/api/darwin-evolution/genome/:id` | GET | Get forex genome |
| `/api/darwin-evolution/jobs` | GET | List active jobs |

## 📈 **Next Steps**

1. **Run Your First Evolution** (15 minutes)
2. **Analyze the Results** - Look at evolved strategies
3. **Try Different Parameters** - Experiment with population size
4. **Test Different Pairs** - GBPUSD, USDJPY, etc.
5. **Enable Verification** - Get mathematical proofs
6. **Build Frontend UI** - Create evolution management interface

## 🧬 **The Darwin Advantage**

Unlike traditional trading systems, Darwin:

- **Discovers** strategies instead of coding them
- **Evolves** continuously improving solutions  
- **Verifies** strategies with mathematical certainty
- **Adapts** to changing market conditions
- **Optimizes** for multiple objectives simultaneously

**Ready to evolve the future of trading? Start your first evolution now!** 🚀