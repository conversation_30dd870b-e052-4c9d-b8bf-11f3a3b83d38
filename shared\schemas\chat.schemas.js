"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MLPredictionResponseSchema = exports.MLPredictionRequestSchema = exports.KnowledgeResultSchema = exports.KnowledgeQuerySchema = exports.KnowledgeRelationshipSchema = exports.KnowledgeNodeSchema = exports.PythonChatResponseSchema = exports.PythonChatRequestSchema = exports.ChatResponseSchema = exports.ChatRequestSchema = exports.ChatSessionSchema = exports.ChatMessageSchema = exports.MessageTypeSchema = exports.MessageRoleSchema = void 0;
const zod_1 = require("zod");
const common_schemas_1 = require("./common.schemas");
const trading_schemas_1 = require("./trading.schemas");
// Chat Message Types
exports.MessageRoleSchema = zod_1.z.enum(['user', 'assistant', 'system']);
exports.MessageTypeSchema = zod_1.z.enum([
    'text', 'analysis', 'prediction', 'strategy', 'market_data', 'error'
]);
// Core Chat Schemas
exports.ChatMessageSchema = zod_1.z.object({
    id: common_schemas_1.IdSchema,
    role: exports.MessageRoleSchema,
    content: zod_1.z.string().min(1),
    type: exports.MessageTypeSchema.default('text'),
    metadata: zod_1.z.object({
        timestamp: zod_1.z.date(),
        confidence: zod_1.z.number().min(0).max(1).optional(),
        sources: zod_1.z.array(zod_1.z.string()).optional(),
        attachments: zod_1.z.array(zod_1.z.object({
            type: zod_1.z.enum(['chart', 'table', 'document']),
            url: zod_1.z.string().optional(),
            data: zod_1.z.any().optional(),
        })).optional(),
    }),
    created_at: zod_1.z.date(),
});
exports.ChatSessionSchema = zod_1.z.object({
    id: common_schemas_1.IdSchema,
    user_id: common_schemas_1.IdSchema,
    title: zod_1.z.string().min(1).max(255),
    context: zod_1.z.object({
        trading_symbols: zod_1.z.array(trading_schemas_1.TradingSymbolSchema).optional(),
        timeframe: zod_1.z.string().optional(),
        strategy_focus: zod_1.z.string().optional(),
        risk_tolerance: zod_1.z.enum(['low', 'medium', 'high']).optional(),
    }),
    created_at: zod_1.z.date(),
    updated_at: zod_1.z.date(),
    last_activity: zod_1.z.date(),
});
// Request/Response Schemas for Python Engine
exports.ChatRequestSchema = zod_1.z.object({
    session_id: common_schemas_1.IdSchema,
    message: zod_1.z.string().min(1),
    user_id: common_schemas_1.IdSchema,
    context: zod_1.z.object({
        // Trading context
        current_positions: zod_1.z.array(zod_1.z.object({
            symbol: trading_schemas_1.TradingSymbolSchema,
            volume: zod_1.z.number(),
            pnl: zod_1.z.number(),
        })).optional(),
        // Market context
        market_data: zod_1.z.object({
            symbols: zod_1.z.array(trading_schemas_1.TradingSymbolSchema).optional(),
            timeframe: zod_1.z.string().optional(),
        }).optional(),
        // User preferences
        preferences: zod_1.z.object({
            risk_tolerance: zod_1.z.enum(['low', 'medium', 'high']).optional(),
            preferred_symbols: zod_1.z.array(trading_schemas_1.TradingSymbolSchema).optional(),
            notification_settings: zod_1.z.any().optional(),
        }).optional(),
        // Previous conversation
        conversation_history: zod_1.z.array(exports.ChatMessageSchema).optional(),
    }),
    timestamp: zod_1.z.date().default(() => new Date()),
});
exports.ChatResponseSchema = zod_1.z.object({
    message: zod_1.z.string(),
    type: exports.MessageTypeSchema,
    confidence: zod_1.z.number().min(0).max(1),
    // AI-generated content
    analysis: zod_1.z.object({
        market_sentiment: zod_1.z.enum(['bullish', 'bearish', 'neutral']).optional(),
        key_levels: zod_1.z.array(zod_1.z.object({
            symbol: trading_schemas_1.TradingSymbolSchema,
            level: zod_1.z.number(),
            type: zod_1.z.enum(['support', 'resistance']),
        })).optional(),
        risk_assessment: zod_1.z.string().optional(),
    }).optional(),
    // Suggested actions
    suggested_actions: zod_1.z.array(zod_1.z.object({
        type: zod_1.z.enum(['buy', 'sell', 'hold', 'analyze', 'backtest']),
        symbol: trading_schemas_1.TradingSymbolSchema.optional(),
        reasoning: zod_1.z.string(),
        confidence: zod_1.z.number().min(0).max(1),
    })).optional(),
    // Knowledge sources
    sources: zod_1.z.array(zod_1.z.object({
        type: zod_1.z.enum(['knowledge_graph', 'market_data', 'historical_analysis']),
        content: zod_1.z.string(),
        relevance: zod_1.z.number().min(0).max(1),
    })).optional(),
    // Attachments (charts, tables, etc.)
    attachments: zod_1.z.array(zod_1.z.object({
        type: zod_1.z.enum(['chart', 'table', 'document', 'analysis']),
        title: zod_1.z.string(),
        data: zod_1.z.any(),
    })).optional(),
    timestamp: zod_1.z.date().default(() => new Date()),
});
// Python Engine Integration
exports.PythonChatRequestSchema = zod_1.z.object({
    request_id: zod_1.z.string().uuid(),
    query: zod_1.z.string().min(1),
    session_id: common_schemas_1.IdSchema,
    user_context: zod_1.z.object({
        user_id: common_schemas_1.IdSchema,
        trading_data: zod_1.z.any().optional(),
        preferences: zod_1.z.any().optional(),
    }),
    conversation_history: zod_1.z.array(zod_1.z.object({
        role: exports.MessageRoleSchema,
        content: zod_1.z.string(),
        timestamp: zod_1.z.date(),
    })).optional(),
    rag_config: zod_1.z.object({
        use_knowledge_graph: zod_1.z.boolean().default(true),
        use_market_data: zod_1.z.boolean().default(true),
        max_context_length: zod_1.z.number().int().positive().default(4000),
    }),
});
exports.PythonChatResponseSchema = zod_1.z.object({
    request_id: zod_1.z.string().uuid(),
    success: zod_1.z.boolean(),
    response: exports.ChatResponseSchema.optional(),
    error: zod_1.z.string().optional(),
    processing_time_ms: zod_1.z.number().nonnegative(),
    tokens_used: zod_1.z.number().int().nonnegative().optional(),
});
// Knowledge Graph Integration
exports.KnowledgeNodeSchema = zod_1.z.object({
    id: zod_1.z.string(),
    type: zod_1.z.enum(['concept', 'strategy', 'indicator', 'market_event']),
    label: zod_1.z.string(),
    properties: zod_1.z.record(zod_1.z.any()),
    confidence: zod_1.z.number().min(0).max(1),
});
exports.KnowledgeRelationshipSchema = zod_1.z.object({
    source_id: zod_1.z.string(),
    target_id: zod_1.z.string(),
    type: zod_1.z.string(),
    weight: zod_1.z.number().min(0).max(1),
    properties: zod_1.z.record(zod_1.z.any()).optional(),
});
exports.KnowledgeQuerySchema = zod_1.z.object({
    query: zod_1.z.string().min(1),
    context: zod_1.z.array(zod_1.z.string()).optional(),
    max_results: zod_1.z.number().int().positive().default(10),
    min_confidence: zod_1.z.number().min(0).max(1).default(0.5),
});
exports.KnowledgeResultSchema = zod_1.z.object({
    nodes: zod_1.z.array(exports.KnowledgeNodeSchema),
    relationships: zod_1.z.array(exports.KnowledgeRelationshipSchema),
    confidence: zod_1.z.number().min(0).max(1),
    explanation: zod_1.z.string().optional(),
});
// ML Prediction Integration
exports.MLPredictionRequestSchema = zod_1.z.object({
    symbol: trading_schemas_1.TradingSymbolSchema,
    prediction_type: zod_1.z.enum(['price', 'direction', 'volatility']),
    timeframe: zod_1.z.string(), // e.g., '1h', '1d', '1w'
    features: zod_1.z.array(zod_1.z.string()).optional(), // specific features to use
    confidence_threshold: zod_1.z.number().min(0).max(1).default(0.7),
});
exports.MLPredictionResponseSchema = zod_1.z.object({
    symbol: trading_schemas_1.TradingSymbolSchema,
    prediction: zod_1.z.number(),
    confidence: zod_1.z.number().min(0).max(1),
    features_used: zod_1.z.array(zod_1.z.string()),
    model_info: zod_1.z.object({
        name: zod_1.z.string(),
        version: zod_1.z.string(),
        last_trained: zod_1.z.date(),
    }),
    explanation: zod_1.z.string().optional(),
    timestamp: zod_1.z.date(),
});
//# sourceMappingURL=chat.schemas.js.map