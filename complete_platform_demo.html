<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Trading Platform - Complete Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px 30px;
        }
        
        .header h1 {
            font-size: 2rem;
            margin-bottom: 5px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1rem;
        }
        
        .nav-tabs {
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            padding: 0 30px;
            display: flex;
            gap: 0;
        }
        
        .nav-tab {
            padding: 15px 25px;
            background: none;
            border: none;
            cursor: pointer;
            font-weight: 600;
            color: #6c757d;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        
        .nav-tab.active {
            color: #3498db;
            border-bottom-color: #3498db;
            background: white;
        }
        
        .nav-tab:hover {
            color: #3498db;
        }
        
        .tab-content {
            display: none;
            padding: 30px;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .disclaimer {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            margin-bottom: 30px;
            border-radius: 8px;
            font-weight: 600;
        }
        
        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 30px;
        }
        
        .grid-3 {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 30px;
        }
        
        .section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            border: 1px solid #e9ecef;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        
        .upload-zone {
            border: 2px dashed #3498db;
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .upload-zone:hover {
            border-color: #2980b9;
            background: #e3f2fd;
        }
        
        .upload-zone.dragover {
            border-color: #27ae60;
            background: #e8f5e8;
        }
        
        .upload-icon {
            font-size: 3rem;
            color: #3498db;
            margin-bottom: 15px;
        }
        
        .upload-text {
            font-size: 1.1rem;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .upload-subtext {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .file-input {
            display: none;
        }
        
        .btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }
        
        .signal-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #3498db;
        }
        
        .signal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .signal-symbol {
            font-size: 1.2rem;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .signal-type {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }
        
        .signal-buy {
            background: #d4edda;
            color: #155724;
        }
        
        .signal-sell {
            background: #f8d7da;
            color: #721c24;
        }
        
        .chat-section {
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            display: flex;
            flex-direction: column;
            height: 500px;
        }
        
        .chat-header {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 8px 8px 0 0;
        }
        
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: white;
        }
        
        .chat-message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 12px;
            max-width: 85%;
        }
        
        .chat-message.user {
            background: #e3f2fd;
            margin-left: auto;
            text-align: right;
        }
        
        .chat-message.assistant {
            background: #f5f5f5;
            margin-right: auto;
        }
        
        .chat-input-container {
            padding: 15px 20px;
            border-top: 1px solid #e9ecef;
            background: white;
            border-radius: 0 0 8px 8px;
        }
        
        .chat-input-form {
            display: flex;
            gap: 10px;
        }
        
        .chat-input {
            flex: 1;
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            outline: none;
        }
        
        .chat-input:focus {
            border-color: #3498db;
        }
        
        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .progress-fill {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            height: 100%;
            transition: width 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .file-info {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .file-info h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .file-stats {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .file-stat {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        
        .file-stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #3498db;
        }
        
        .file-stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .column-mapping {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .mapping-row {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        
        .mapping-label {
            font-weight: 600;
            color: #2c3e50;
            min-width: 120px;
        }
        
        .mapping-select {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            background: white;
        }
        
        @media (max-width: 1024px) {
            .grid-2 {
                grid-template-columns: 1fr;
            }
            
            .grid-3 {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 AI Trading Platform - Complete Demo</h1>
            <p>Signal Provider + Data Upload + AI Assistant + Analytics</p>
        </div>
        
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('signals')">📊 Live Signals</button>
            <button class="nav-tab" onclick="showTab('upload')">📁 Data Upload</button>
            <button class="nav-tab" onclick="showTab('analytics')">📈 Analytics</button>
            <button class="nav-tab" onclick="showTab('chat')">🤖 AI Assistant</button>
        </div>
        
        <!-- Signals Tab -->
        <div id="signals-tab" class="tab-content active">
            <div class="disclaimer">
                ⚠️ SIGNAL PROVIDER PLATFORM: We provide educational trading signals only. You execute trades on your own MT5 account.
            </div>
            
            <div class="grid-2">
                <div>
                    <div class="section">
                        <h2>🔥 Live Trading Signals</h2>
                        <div id="signals-container">
                            <!-- Signals populated by JavaScript -->
                        </div>
                        <button class="btn" onclick="generateNewSignal()">🔄 Generate New Signal</button>
                    </div>
                    
                    <div class="section" style="margin-top: 30px;">
                        <h2>🎯 Strategy Performance</h2>
                        <div id="strategies-container">
                            <!-- Strategies populated by JavaScript -->
                        </div>
                    </div>
                </div>
                
                <div class="chat-section">
                    <div class="chat-header">
                        <h2>🤖 AI Trading Assistant</h2>
                    </div>
                    <div class="chat-messages" id="chat-messages-signals">
                        <div class="chat-message assistant">
                            👋 I can help you understand our trading signals and strategies. Ask me anything!
                        </div>
                    </div>
                    <div class="chat-input-container">
                        <div class="chat-input-form">
                            <input type="text" class="chat-input" placeholder="Ask about signals..." id="chat-input-signals">
                            <button class="btn" onclick="sendChatMessage('signals')">Send</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Upload Tab -->
        <div id="upload-tab" class="tab-content">
            <div class="disclaimer">
                📁 DATA UPLOAD: Upload your trading data for backtesting and analysis. Supports CSV, Excel, and JSON formats.
            </div>
            
            <div class="grid-2">
                <div>
                    <div class="section">
                        <h2>📁 Upload Trading Data</h2>
                        
                        <div class="upload-zone" id="upload-zone" onclick="document.getElementById('file-input').click()">
                            <div class="upload-icon">📁</div>
                            <div class="upload-text">Drop your files here or click to browse</div>
                            <div class="upload-subtext">Supports CSV, Excel (.xlsx, .xls), and JSON files up to 500MB</div>
                        </div>
                        
                        <input type="file" id="file-input" class="file-input" accept=".csv,.xlsx,.xls,.json" onchange="handleFileSelect(event)">
                        
                        <div id="upload-progress" style="display: none;">
                            <div class="progress-bar">
                                <div class="progress-fill" id="progress-fill" style="width: 0%">0%</div>
                            </div>
                        </div>
                        
                        <div id="file-info" class="file-info" style="display: none;">
                            <h3>📊 File Analysis</h3>
                            <div class="file-stats">
                                <div class="file-stat">
                                    <div class="file-stat-value" id="row-count">0</div>
                                    <div class="file-stat-label">Rows</div>
                                </div>
                                <div class="file-stat">
                                    <div class="file-stat-value" id="column-count">0</div>
                                    <div class="file-stat-label">Columns</div>
                                </div>
                                <div class="file-stat">
                                    <div class="file-stat-value" id="file-size">0 MB</div>
                                    <div class="file-stat-label">Size</div>
                                </div>
                            </div>
                            
                            <div class="column-mapping" id="column-mapping" style="display: none;">
                                <h4>🔗 Column Mapping</h4>
                                <p style="color: #6c757d; margin-bottom: 15px;">Map your file columns to our standard format:</p>
                                <div id="mapping-rows">
                                    <!-- Mapping rows populated by JavaScript -->
                                </div>
                                <button class="btn btn-success" onclick="confirmMapping()">✅ Confirm Mapping & Process</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div>
                    <div class="section">
                        <h2>📋 Upload Instructions</h2>
                        <div style="background: white; padding: 20px; border-radius: 8px;">
                            <h4 style="color: #2c3e50; margin-bottom: 15px;">📝 Required Data Format</h4>
                            <ul style="color: #6c757d; line-height: 1.6; margin-left: 20px;">
                                <li><strong>Date/Time:</strong> Trading timestamp</li>
                                <li><strong>Symbol:</strong> Currency pair (e.g., EURUSD)</li>
                                <li><strong>Open/High/Low/Close:</strong> OHLC prices</li>
                                <li><strong>Volume:</strong> Trading volume (optional)</li>
                                <li><strong>Action:</strong> BUY/SELL (for trade data)</li>
                            </ul>
                            
                            <h4 style="color: #2c3e50; margin: 20px 0 15px 0;">📊 Supported Formats</h4>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                                <div style="padding: 10px; background: #f8f9fa; border-radius: 4px; text-align: center;">
                                    <strong>CSV</strong><br>
                                    <span style="color: #6c757d;">Comma-separated values</span>
                                </div>
                                <div style="padding: 10px; background: #f8f9fa; border-radius: 4px; text-align: center;">
                                    <strong>Excel</strong><br>
                                    <span style="color: #6c757d;">.xlsx, .xls files</span>
                                </div>
                            </div>
                            
                            <h4 style="color: #2c3e50; margin: 20px 0 15px 0;">🔄 Processing Steps</h4>
                            <ol style="color: #6c757d; line-height: 1.6; margin-left: 20px;">
                                <li>Upload your file</li>
                                <li>Review detected columns</li>
                                <li>Map columns to standard format</li>
                                <li>Confirm and start processing</li>
                                <li>View results in Analytics tab</li>
                            </ol>
                        </div>
                    </div>
                    
                    <div class="section" style="margin-top: 30px;">
                        <h2>📈 Recent Uploads</h2>
                        <div id="recent-uploads">
                            <div style="background: white; padding: 15px; border-radius: 8px; margin-bottom: 10px;">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <strong>EURUSD_2024.csv</strong><br>
                                        <span style="color: #6c757d; font-size: 0.9rem;">1,250 rows • Processed</span>
                                    </div>
                                    <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 0.8rem;">View</button>
                                </div>
                            </div>
                            <div style="background: white; padding: 15px; border-radius: 8px; margin-bottom: 10px;">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <strong>Trading_History.xlsx</strong><br>
                                        <span style="color: #6c757d; font-size: 0.9rem;">3,420 rows • Processing...</span>
                                    </div>
                                    <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 0.8rem;">View</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Analytics Tab -->
        <div id="analytics-tab" class="tab-content">
            <div class="disclaimer">
                📈 ANALYTICS: Analyze your uploaded data and backtest strategies. All analysis is performed on your data locally.
            </div>
            
            <div class="grid-3">
                <div class="section">
                    <h2>📊 Data Overview</h2>
                    <div style="background: white; padding: 20px; border-radius: 8px;">
                        <div style="text-align: center; margin-bottom: 20px;">
                            <div style="font-size: 2rem; font-weight: bold; color: #3498db;">4,670</div>
                            <div style="color: #6c757d;">Total Records</div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                            <div style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                                <div style="font-weight: bold; color: #27ae60;">12</div>
                                <div style="font-size: 0.8rem; color: #6c757d;">Currency Pairs</div>
                            </div>
                            <div style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                                <div style="font-weight: bold; color: #e74c3c;">365</div>
                                <div style="font-size: 0.8rem; color: #6c757d;">Trading Days</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="section">
                    <h2>🎯 Backtest Results</h2>
                    <div style="background: white; padding: 20px; border-radius: 8px;">
                        <div style="text-align: center; margin-bottom: 20px;">
                            <div style="font-size: 2rem; font-weight: bold; color: #27ae60;">+24.7%</div>
                            <div style="color: #6c757d;">Total Return</div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                            <div style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                                <div style="font-weight: bold; color: #3498db;">68.5%</div>
                                <div style="font-size: 0.8rem; color: #6c757d;">Win Rate</div>
                            </div>
                            <div style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                                <div style="font-weight: bold; color: #f39c12;">1.45</div>
                                <div style="font-size: 0.8rem; color: #6c757d;">Profit Factor</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="section">
                    <h2>⚠️ Risk Metrics</h2>
                    <div style="background: white; padding: 20px; border-radius: 8px;">
                        <div style="text-align: center; margin-bottom: 20px;">
                            <div style="font-size: 2rem; font-weight: bold; color: #e74c3c;">-8.2%</div>
                            <div style="color: #6c757d;">Max Drawdown</div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                            <div style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                                <div style="font-weight: bold; color: #3498db;">1.8</div>
                                <div style="font-size: 0.8rem; color: #6c757d;">Sharpe Ratio</div>
                            </div>
                            <div style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                                <div style="font-weight: bold; color: #27ae60;">0.95</div>
                                <div style="font-size: 0.8rem; color: #6c757d;">Profit Factor</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="section" style="margin-top: 30px;">
                <h2>📈 Performance Chart</h2>
                <div style="background: white; padding: 30px; border-radius: 8px; text-align: center;">
                    <div style="height: 300px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #6c757d;">
                        📊 Interactive Performance Chart<br>
                        <span style="font-size: 0.9rem;">(Chart visualization would be rendered here with real data)</span>
                    </div>
                    <div style="margin-top: 20px; display: flex; justify-content: center; gap: 15px;">
                        <button class="btn btn-secondary">📊 Equity Curve</button>
                        <button class="btn btn-secondary">📈 Drawdown</button>
                        <button class="btn btn-secondary">📉 Monthly Returns</button>
                        <button class="btn">📋 Export Report</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Chat Tab -->
        <div id="chat-tab" class="tab-content">
            <div class="disclaimer">
                🤖 AI ASSISTANT: Get help with trading strategies, data analysis, and platform features. All responses are educational only.
            </div>
            
            <div class="grid-2">
                <div class="chat-section" style="height: 600px;">
                    <div class="chat-header">
                        <h2>🤖 AI Trading Assistant</h2>
                        <div style="font-size: 0.9rem; opacity: 0.9;">Advanced trading AI with market analysis capabilities</div>
                    </div>
                    <div class="chat-messages" id="chat-messages-main">
                        <div class="chat-message assistant">
                            👋 <strong>Welcome to the AI Trading Assistant!</strong><br><br>
                            I can help you with:<br>
                            • 📊 Market analysis and signals<br>
                            • 🎯 Trading strategy guidance<br>
                            • 📁 Data upload and processing<br>
                            • 📈 Backtest interpretation<br>
                            • ⚠️ Risk management advice<br>
                            • 🔧 Platform features and usage<br><br>
                            What would you like to explore today?
                        </div>
                    </div>
                    <div class="chat-input-container">
                        <div style="display: flex; flex-wrap: wrap; gap: 8px; margin-bottom: 15px;">
                            <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 0.8rem;" onclick="sendQuickMessage('main', 'How do I upload trading data?')">📁 Upload Help</button>
                            <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 0.8rem;" onclick="sendQuickMessage('main', 'Explain backtest results')">📈 Backtest Help</button>
                            <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 0.8rem;" onclick="sendQuickMessage('main', 'Best risk management practices')">⚠️ Risk Tips</button>
                        </div>
                        <div class="chat-input-form">
                            <input type="text" class="chat-input" placeholder="Ask me anything about trading..." id="chat-input-main">
                            <button class="btn" onclick="sendChatMessage('main')">Send</button>
                        </div>
                    </div>
                </div>
                
                <div>
                    <div class="section">
                        <h2>🎯 Quick Actions</h2>
                        <div style="display: grid; gap: 15px;">
                            <button class="btn" onclick="sendQuickMessage('main', 'Analyze my latest upload')">📊 Analyze Latest Data</button>
                            <button class="btn" onclick="sendQuickMessage('main', 'Show me the best performing strategy')">🏆 Best Strategy</button>
                            <button class="btn" onclick="sendQuickMessage('main', 'What are the current market conditions?')">🌍 Market Overview</button>
                            <button class="btn" onclick="sendQuickMessage('main', 'How can I improve my trading performance?')">📈 Improve Performance</button>
                        </div>
                    </div>
                    
                    <div class="section" style="margin-top: 30px;">
                        <h2>📚 Knowledge Base</h2>
                        <div style="background: white; padding: 20px; border-radius: 8px;">
                            <h4 style="color: #2c3e50; margin-bottom: 15px;">Popular Topics</h4>
                            <div style="display: flex; flex-direction: column; gap: 10px;">
                                <a href="#" style="color: #3498db; text-decoration: none; padding: 8px; background: #f8f9fa; border-radius: 4px;">📊 How to interpret backtest results</a>
                                <a href="#" style="color: #3498db; text-decoration: none; padding: 8px; background: #f8f9fa; border-radius: 4px;">📁 Data upload format requirements</a>
                                <a href="#" style="color: #3498db; text-decoration: none; padding: 8px; background: #f8f9fa; border-radius: 4px;">🎯 Understanding trading signals</a>
                                <a href="#" style="color: #3498db; text-decoration: none; padding: 8px; background: #f8f9fa; border-radius: 4px;">⚠️ Risk management best practices</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Tab switching functionality
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Remove active class from all nav tabs
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName + '-tab').classList.add('active');
            
            // Add active class to clicked nav tab
            event.target.classList.add('active');
        }
        
        // Mock signals data
        const mockSignals = [
            {
                symbol: "EURUSD",
                type: "BUY",
                entry_price: 1.1234,
                stop_loss: 1.1200,
                take_profit: 1.1300,
                confidence: 85,
                strategy: "MA Crossover"
            },
            {
                symbol: "GBPUSD",
                type: "SELL",
                entry_price: 1.3145,
                stop_loss: 1.3180,
                take_profit: 1.3080,
                confidence: 78,
                strategy: "RSI Reversal"
            }
        ];
        
        // Initialize signals
        function loadSignals() {
            const container = document.getElementById('signals-container');
            container.innerHTML = mockSignals.map(signal => `
                <div class="signal-item">
                    <div class="signal-header">
                        <span class="signal-symbol">${signal.symbol}</span>
                        <span class="signal-type signal-${signal.type.toLowerCase()}">${signal.type}</span>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px; margin-bottom: 15px;">
                        <div style="text-align: center; padding: 8px; background: #f8f9fa; border-radius: 4px;">
                            <div style="font-size: 0.8rem; color: #6c757d;">Entry</div>
                            <div style="font-weight: bold;">${signal.entry_price}</div>
                        </div>
                        <div style="text-align: center; padding: 8px; background: #f8f9fa; border-radius: 4px;">
                            <div style="font-size: 0.8rem; color: #6c757d;">Stop Loss</div>
                            <div style="font-weight: bold;">${signal.stop_loss}</div>
                        </div>
                        <div style="text-align: center; padding: 8px; background: #f8f9fa; border-radius: 4px;">
                            <div style="font-size: 0.8rem; color: #6c757d;">Take Profit</div>
                            <div style="font-weight: bold;">${signal.take_profit}</div>
                        </div>
                    </div>
                    <div style="color: #6c757d;">
                        <strong>Strategy:</strong> ${signal.strategy} | <strong>Confidence:</strong> ${signal.confidence}%
                    </div>
                </div>
            `).join('');
        }
        
        // Initialize strategies
        function loadStrategies() {
            const strategies = [
                { name: "MA Crossover", win_rate: 68.5, monthly_return: 12.3 },
                { name: "RSI Reversal", win_rate: 72.1, monthly_return: 15.7 }
            ];
            
            const container = document.getElementById('strategies-container');
            container.innerHTML = strategies.map(strategy => `
                <div style="background: white; padding: 15px; border-radius: 8px; margin-bottom: 10px;">
                    <div style="font-weight: bold; margin-bottom: 10px;">${strategy.name}</div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                        <div style="text-align: center; padding: 8px; background: #f8f9fa; border-radius: 4px;">
                            <div style="font-size: 0.8rem; color: #6c757d;">Win Rate</div>
                            <div style="font-weight: bold; color: #27ae60;">${strategy.win_rate}%</div>
                        </div>
                        <div style="text-align: center; padding: 8px; background: #f8f9fa; border-radius: 4px;">
                            <div style="font-size: 0.8rem; color: #6c757d;">Monthly Return</div>
                            <div style="font-weight: bold; color: #27ae60;">${strategy.monthly_return}%</div>
                        </div>
                    </div>
                </div>
            `).join('');
        }
        
        // File upload functionality
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                processFile(file);
            }
        }
        
        function processFile(file) {
            // Show file info
            document.getElementById('file-info').style.display = 'block';
            document.getElementById('row-count').textContent = Math.floor(Math.random() * 5000) + 1000;
            document.getElementById('column-count').textContent = Math.floor(Math.random() * 10) + 5;
            document.getElementById('file-size').textContent = (file.size / (1024 * 1024)).toFixed(2) + ' MB';
            
            // Show progress
            document.getElementById('upload-progress').style.display = 'block';
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 20;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);
                    showColumnMapping();
                }
                document.getElementById('progress-fill').style.width = progress + '%';
                document.getElementById('progress-fill').textContent = Math.floor(progress) + '%';
            }, 200);
        }
        
        function showColumnMapping() {
            const mappingContainer = document.getElementById('column-mapping');
            mappingContainer.style.display = 'block';
            
            const detectedColumns = ['Date', 'Symbol', 'Open', 'High', 'Low', 'Close', 'Volume'];
            const standardColumns = ['timestamp', 'symbol', 'open', 'high', 'low', 'close', 'volume'];
            
            const mappingRows = document.getElementById('mapping-rows');
            mappingRows.innerHTML = standardColumns.map((standard, index) => `
                <div class="mapping-row">
                    <div class="mapping-label">${standard.charAt(0).toUpperCase() + standard.slice(1)}:</div>
                    <select class="mapping-select">
                        ${detectedColumns.map(col => `<option value="${col}" ${col.toLowerCase() === standard ? 'selected' : ''}>${col}</option>`).join('')}
                    </select>
                </div>
            `).join('');
        }
        
        function confirmMapping() {
            alert('✅ Column mapping confirmed! Your data is being processed and will be available in the Analytics tab.');
            document.getElementById('upload-progress').style.display = 'none';
            document.getElementById('file-info').style.display = 'none';
        }
        
        // Chat functionality
        function sendChatMessage(chatType) {
            const inputId = 'chat-input-' + chatType;
            const messagesId = 'chat-messages-' + chatType;
            const input = document.getElementById(inputId);
            const message = input.value.trim();
            
            if (message) {
                addChatMessage(messagesId, 'user', message);
                input.value = '';
                
                setTimeout(() => {
                    const response = generateAIResponse(message);
                    addChatMessage(messagesId, 'assistant', response);
                }, 1000);
            }
        }
        
        function sendQuickMessage(chatType, message) {
            const messagesId = 'chat-messages-' + chatType;
            addChatMessage(messagesId, 'user', message);
            
            setTimeout(() => {
                const response = generateAIResponse(message);
                addChatMessage(messagesId, 'assistant', response);
            }, 1000);
        }
        
        function addChatMessage(containerId, type, content) {
            const container = document.getElementById(containerId);
            const messageDiv = document.createElement('div');
            messageDiv.className = `chat-message ${type}`;
            messageDiv.innerHTML = content;
            container.appendChild(messageDiv);
            container.scrollTop = container.scrollHeight;
        }
        
        function generateAIResponse(message) {
            const lowerMessage = message.toLowerCase();
            
            if (lowerMessage.includes('upload') || lowerMessage.includes('data')) {
                return `📁 <strong>Data Upload Guide:</strong><br><br>
                1. <strong>Prepare your file:</strong> CSV or Excel format<br>
                2. <strong>Required columns:</strong> Date, Symbol, OHLC, Volume<br>
                3. <strong>Upload process:</strong> Drag & drop or click to browse<br>
                4. <strong>Column mapping:</strong> Map your columns to our format<br>
                5. <strong>Processing:</strong> We'll analyze and prepare your data<br><br>
                Your data stays secure and is processed locally.`;
            }
            
            if (lowerMessage.includes('backtest') || lowerMessage.includes('results')) {
                return `📈 <strong>Backtest Results Explanation:</strong><br><br>
                • <strong>Total Return:</strong> Overall profit/loss percentage<br>
                • <strong>Win Rate:</strong> Percentage of profitable trades<br>
                • <strong>Profit Factor:</strong> Gross profit ÷ gross loss<br>
                • <strong>Max Drawdown:</strong> Largest peak-to-trough decline<br>
                • <strong>Sharpe Ratio:</strong> Risk-adjusted return measure<br><br>
                A good strategy typically has >60% win rate and >1.5 profit factor.`;
            }
            
            if (lowerMessage.includes('risk') || lowerMessage.includes('management')) {
                return `⚠️ <strong>Risk Management Essentials:</strong><br><br>
                1. <strong>Position Sizing:</strong> Never risk >2% per trade<br>
                2. <strong>Stop Losses:</strong> Always define your exit point<br>
                3. <strong>Diversification:</strong> Don't put all eggs in one basket<br>
                4. <strong>Risk-Reward:</strong> Aim for 1:2 minimum ratio<br>
                5. <strong>Emotional Control:</strong> Stick to your plan<br><br>
                Remember: Protecting capital is more important than making profits!`;
            }
            
            return `🤖 I understand you're asking about "${message}".<br><br>
            I can help with:<br>
            • 📁 Data upload and processing<br>
            • 📊 Signal analysis and interpretation<br>
            • 📈 Backtest result explanation<br>
            • ⚠️ Risk management guidance<br>
            • 🔧 Platform features and usage<br><br>
            What specific aspect would you like to explore?`;
        }
        
        function generateNewSignal() {
            alert('🔄 New signal generated! Check the signals section for the latest trading opportunity.');
        }
        
        // Drag and drop functionality
        const uploadZone = document.getElementById('upload-zone');
        
        uploadZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadZone.classList.add('dragover');
        });
        
        uploadZone.addEventListener('dragleave', () => {
            uploadZone.classList.remove('dragover');
        });
        
        uploadZone.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadZone.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                processFile(files[0]);
            }
        });
        
        // Enter key support for chat
        document.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                if (e.target.id === 'chat-input-signals') {
                    sendChatMessage('signals');
                } else if (e.target.id === 'chat-input-main') {
                    sendChatMessage('main');
                }
            }
        });
        
        // Initialize the page
        window.addEventListener('load', () => {
            loadSignals();
            loadStrategies();
        });
    </script>
</body>
</html>