// server.ts
// Main server file integrating Darwin Gödel Machine with existing platform
// Combines all services: Auth, Bridge Services, and Darwin Gödel Machine

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
// import { createServer } from 'http'; // Unused for now
import WebSocket from 'ws';
import dotenv from 'dotenv';
import winston from 'winston';

// Import existing services
// import { AuthRouter } from './features/auth/auth.router'; // Unused for now
import { BridgeServiceRegistry } from './services/bridge/bridge-service-registry';
import { PythonEngineService } from './services/bridge/python-engine.service';
// import { BacktestBridgeService } from './services/bridge/backtest-bridge.service'; // Not currently used
// import { ChatBridgeService } from './services/bridge/chat-bridge.service'; // Not currently used
// import { TradingBridgeService } from './services/bridge/trading-bridge.service'; // Not currently used
// import { WorkerBridgeService } from './services/bridge/worker-bridge.service'; // Not currently used

// Import Darwin Gödel Machine components
import DarwinGodelAPI from './services/darwin-godel-api';
import S3CoreEngine from './services/s3-core-nlp-engine';
import StrategyVerificationEngine from './services/strategy-verification-engine';
import { createDarwinRoutes } from './routes/darwin.routes';

// Import configuration
// import { appConfig } from './shared/config/app.config'; // Unused for now
import { authMiddleware } from './shared/middleware/auth.middleware';

// Load environment variables
dotenv.config();

// Configure logging
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'ai-trading-platform' },
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

class AITradingPlatformServer {
  private app: express.Application;
  private server: any;
  private wss: WebSocket.Server | null = null;
  private port: number;
  
  // Service instances
  private bridgeRegistry!: BridgeServiceRegistry;
  private darwinGodelAPI!: DarwinGodelAPI;
  private s3CoreEngine!: S3CoreEngine;
  private verificationEngine!: StrategyVerificationEngine;
  
  // Bridge services
  private pythonEngine!: PythonEngineService; // Used for evolution jobs
  // private backtestBridge!: BacktestBridgeService; // Not currently used
  // private chatBridge!: ChatBridgeService; // Not currently used
  // private tradingBridge!: TradingBridgeService; // Not currently used
  // private workerBridge!: WorkerBridgeService; // Not currently used

  constructor() {
    this.port = parseInt(process.env.PORT || '3001');
    this.app = express();
    
    // Initialize services
    this.initializeServices();
    
    // Setup middleware and routes
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  private initializeServices(): void {
    // Create logger with trace method
    const winstonLogger = winston.createLogger({
      level: 'info',
      format: winston.format.json(),
      transports: [
        new winston.transports.Console(),
      ],
    });

    // Add trace method to match our Logger interface
    const logger = {
      ...winstonLogger,
      trace: (message: string, meta?: Record<string, any>) => winstonLogger.debug(message, meta)
    };

    // Initialize bridge services with proper dependencies
    const pythonEngineConfig: any = {
      baseUrl: process.env.PYTHON_ENGINE_URL || 'http://localhost:8001',
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000
    };
    
    if (process.env.PYTHON_ENGINE_API_KEY) {
      pythonEngineConfig.apiKey = process.env.PYTHON_ENGINE_API_KEY;
    }

    this.pythonEngine = new PythonEngineService({
      logger,
      config: pythonEngineConfig
    });

    // this.backtestBridge = new BacktestBridgeService({
    //   pythonEngineService: this.pythonEngine,
    //   logger
    // });

    // this.chatBridge = new ChatBridgeService({
    //   pythonEngineService: this.pythonEngine,
    //   logger
    // });

    // this.tradingBridge = new TradingBridgeService({
    //   pythonEngineService: this.pythonEngine,
    //   logger
    // });

    // this.workerBridge = new WorkerBridgeService({
    //   pythonEngineService: this.pythonEngine,
    //   logger,
    //   monitoring: {
    //     healthCheckInterval: 30000,
    //     statusPollInterval: 10000,
    //     autoRestart: true
    //   }
    // });
    
    // Initialize bridge registry
    const bridgeRegistryConfig: any = {
      pythonEngine: {
        baseUrl: process.env.PYTHON_ENGINE_URL || 'http://localhost:8001',
        timeout: 30000,
        retryAttempts: 3,
        retryDelay: 1000
      },
      healthCheckInterval: 30000,
      cleanupInterval: 300000
    };
    
    if (process.env.PYTHON_ENGINE_API_KEY) {
      bridgeRegistryConfig.pythonEngine.apiKey = process.env.PYTHON_ENGINE_API_KEY;
    }

    this.bridgeRegistry = new BridgeServiceRegistry({
      logger,
      config: bridgeRegistryConfig
    });
    
    // Initialize the registry (this will set up all bridge services internally)
    // Note: We'll handle this in the start() method since it's async
    
    // Initialize Darwin Gödel Machine components
    this.s3CoreEngine = new S3CoreEngine(process.env.OPENAI_API_KEY || '');
    this.verificationEngine = new StrategyVerificationEngine(
      process.env.COQ_PATH || 'coqc',
      process.env.COQ_WORKSPACE_DIR || './coq_workspace'
    );
    this.darwinGodelAPI = new DarwinGodelAPI(
      this.port,
      process.env.OPENAI_API_KEY,
      process.env.COQ_PATH || 'coqc'
    );
    
    logger.info('All services initialized successfully');
  }

  private setupMiddleware(): void {
    // Security middleware
    this.app.use(helmet());

    // CORS configuration
    this.app.use(cors());

    // Compression
    this.app.use(compression() as any);

    // Rate limiting
    const limiter = rateLimit({
      windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'),
      max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
      message: 'Too many requests from this IP, please try again later.',
      standardHeaders: true,
      legacyHeaders: false,
    });

    this.app.use('/api/', limiter as any);

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Request logging
    this.app.use((req, _res, next) => {
      logger.info(`${req.method} ${req.path}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date().toISOString()
      });
      next();
    });
  }

  private setupRoutes(): void {
    // Health check endpoint
    this.app.get('/health', (_req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        services: {
          auth: 'active',
          bridgeServices: this.bridgeRegistry ? 'active' : 'inactive',
          darwinGodelMachine: this.darwinGodelAPI ? 'active' : 'inactive',
          s3Core: this.s3CoreEngine ? 'active' : 'inactive',
          verificationEngine: this.verificationEngine ? 'active' : 'inactive',
          database: 'active'
        },
        uptime: process.uptime()
      });
    });

    // API documentation
    this.app.get('/api/docs', (_req, res) => {
      res.json({
        title: 'AI Enhanced Trading Platform API',
        version: '2.0.0',
        description: 'Revolutionary AI trading platform with Darwin Gödel Machine integration',
        features: [
          'Natural Language Trading Interface (S3 Core)',
          'Formal Mathematical Verification (Coq)',
          'Evolutionary Strategy Optimization (Darwin Engine)',
          'Real-time Market Analysis',
          'Backtesting and Performance Analytics',
          'Multi-model AI Chat Integration'
        ],
        endpoints: {
          // Authentication
          auth: {
            login: 'POST /api/auth/login',
            register: 'POST /api/auth/register',
            refresh: 'POST /api/auth/refresh',
            logout: 'POST /api/auth/logout'
          },
          // Darwin Gödel Machine
          darwin: {
            chat: 'POST /api/darwin/chat',
            evolve: 'POST /api/darwin/evolve-strategies',
            verify: 'POST /api/darwin/verify-strategy',
            strategies: 'GET /api/darwin/proven-strategies',
            genome: 'GET /api/darwin/forex-genome/:pair/:timeframe'
          },
          // Bridge Services
          bridge: {
            backtest: 'POST /api/bridge/backtest',
            chat: 'POST /api/bridge/chat',
            trading: 'POST /api/bridge/trading',
            worker: 'POST /api/bridge/worker'
          }
        },
        websocket: {
          url: `ws://localhost:${this.port}`,
          events: ['evolution_update', 'market_analysis', 'strategy_verified']
        }
      });
    });

    // Authentication routes (commented out until AuthRouter is properly configured)
    // this.app.use('/api/auth', authRouter);

    // Darwin Gödel Machine routes (with authentication)
    this.app.use('/api/darwin', authMiddleware, this.createDarwinRoutes());
    
    // New Darwin Evolution routes (with authentication)
    const loggerWithTrace = {
      ...logger,
      trace: (message: string, meta?: Record<string, any>) => logger.debug(message, meta)
    };
    this.app.use('/api/darwin-evolution', authMiddleware, createDarwinRoutes(loggerWithTrace));

    // Bridge service routes (with authentication)
    this.app.use('/api/bridge', authMiddleware, this.createBridgeRoutes());

    // Public Darwin Gödel Machine routes (for demo/testing)
    this.app.use('/api/public/darwin', this.createPublicDarwinRoutes());
  }

  private createDarwinRoutes(): express.Router {
    const router = express.Router();

    // Chat endpoint - Main S3 Core interface
    router.post('/chat', async (req, res) => {
      try {
        const { message } = req.body;
        
        // Translate natural language query
        const query = await this.s3CoreEngine.translateQuery(message);
        
        let response: any = {
          query,
          timestamp: new Date().toISOString(),
          userId: (req as any).user?.id
        };

        // Handle different query types
        switch (query.type) {
          case 'analysis':
            if (query.pair && query.timeframe) {
              const analysis = await this.s3CoreEngine.generateAnalysis(query);
              response.analysis = analysis;
            } else {
              response.message = "Please specify a currency pair and timeframe for analysis.";
            }
            break;

          case 'scan':
            const scanResults = await this.s3CoreEngine.scanMarkets(message);
            response.scanResults = scanResults;
            break;

          case 'indicator':
            const explanation = await this.s3CoreEngine.explainIndicator(
              query.indicators?.[0] || 'RSI',
              query.pair,
              query.timeframe
            );
            response.explanation = explanation;
            break;

          case 'strategy':
            const validation = await this.s3CoreEngine.validateStrategy(message);
            response.validation = validation;
            break;

          default:
            response.message = "I understand your query. How can I help you with trading analysis?";
        }

        res.json(response);

      } catch (error) {
        logger.error('Darwin chat endpoint error:', error);
        res.status(500).json({
          error: 'Failed to process chat message',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    });

    // Strategy verification
    router.post('/verify-strategy', async (req, res) => {
      try {
        const { strategy, pair } = req.body;
        
        const verificationResult = await this.verificationEngine.verifyStrategy(strategy, pair);
        
        res.json({
          success: true,
          verification: verificationResult,
          timestamp: new Date().toISOString(),
          userId: (req as any).user?.id
        });

      } catch (error) {
        logger.error('Strategy verification error:', error);
        res.status(500).json({
          error: 'Failed to verify strategy',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    });

    // Evolution endpoint
    router.post('/evolve-strategies', async (req, res) => {
      try {
        const evolutionParams = req.body;
        
        // Start evolution job using Python bridge
        const jobResult = await this.startEvolutionJob(evolutionParams, (req as any).user?.id);
        
        res.json({
          success: true,
          job: jobResult,
          timestamp: new Date().toISOString(),
          userId: (req as any).user?.id
        });

      } catch (error) {
        logger.error('Evolution endpoint error:', error);
        res.status(500).json({
          error: 'Failed to start evolution job',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    });

    // Get proven strategies
    router.get('/proven-strategies', async (req, res) => {
      try {
        const { pair, limit = '10' } = req.query;
        
        // This would query your database in production
        const strategies = await this.getProvenStrategies(
          pair as string,
          parseInt(limit as string),
          (req as any).user?.id
        );
        
        res.json({
          success: true,
          strategies,
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        logger.error('Get proven strategies error:', error);
        res.status(500).json({
          error: 'Failed to get proven strategies',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    });

    // Get forex genome
    router.get('/forex-genome/:pair/:timeframe', async (req, res) => {
      try {
        const { pair, timeframe } = req.params;
        
        const genome = await this.getForexGenome(pair, timeframe, (req as any).user?.id);
        
        res.json({
          success: true,
          genome,
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        logger.error('Get forex genome error:', error);
        res.status(500).json({
          error: 'Failed to get forex genome',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    });

    return router;
  }

  private createBridgeRoutes(): express.Router {
    const router = express.Router();

    // Backtest endpoint
    router.post('/backtest', async (_req, res) => {
      try {
        // TODO: Implement runBacktest method in BacktestBridgeService
        const result = { message: 'Backtest functionality not yet implemented' };
        res.json({ success: true, result });
      } catch (error) {
        logger.error('Backtest bridge error:', error);
        res.status(500).json({ error: 'Backtest failed' });
      }
    });

    // Chat bridge endpoint (for existing chat functionality)
    router.post('/chat', async (_req, res) => {
      try {
        // TODO: Implement processMessage method in ChatBridgeService
        const result = { message: 'Chat bridge functionality not yet implemented' };
        res.json({ success: true, result });
      } catch (error) {
        logger.error('Chat bridge error:', error);
        res.status(500).json({ error: 'Chat processing failed' });
      }
    });

    // Trading bridge endpoint
    router.post('/trading', async (_req, res) => {
      try {
        // TODO: Implement executeTrade method in TradingBridgeService
        const result = { message: 'Trading bridge functionality not yet implemented' };
        res.json({ success: true, result });
      } catch (error) {
        logger.error('Trading bridge error:', error);
        res.status(500).json({ error: 'Trading execution failed' });
      }
    });

    // Worker bridge endpoint
    router.post('/worker', async (_req, res) => {
      try {
        // TODO: Implement processTask method in WorkerBridgeService
        const result = { message: 'Worker bridge functionality not yet implemented' };
        res.json({ success: true, result });
      } catch (error) {
        logger.error('Worker bridge error:', error);
        res.status(500).json({ error: 'Worker task failed' });
      }
    });

    return router;
  }

  private createPublicDarwinRoutes(): express.Router {
    const router = express.Router();

    // Public chat endpoint for demo
    router.post('/chat', async (req, res) => {
      try {
        const { message } = req.body;
        
        // Simple demo response
        const query = await this.s3CoreEngine.translateQuery(message);
        
        res.json({
          query,
          message: "This is a demo response. Please authenticate for full functionality.",
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        logger.error('Public Darwin chat error:', error);
        res.status(500).json({ error: 'Demo chat failed' });
      }
    });

    return router;
  }

  private async startEvolutionJob(params: any, _userId?: string): Promise<any> {
    // Use Python bridge to start evolution
    const pythonScript = `
import sys
import os
sys.path.append('${process.cwd()}/../shared')

from darwin_engine_integration import DarwinGodelMachine
import asyncio
import json

async def main():
    dgm = DarwinGodelMachine(
        population_size=${params.populationSize || 50}
    )
    
    result = await dgm.evolve_strategies(
        pair="${params.pair}",
        timeframe="${params.timeframe}",
        generations=${params.generations || 20},
        fitness_objective="${params.fitnessGoal || 'sharpe'}"
    )
    
    print(json.dumps(result))

if __name__ == "__main__":
    asyncio.run(main())
    `;

    try {
      // TODO: Implement executePythonScript method in PythonEngineService
      // const result = await this.pythonEngine.executePythonScript(pythonScript);
      // return JSON.parse(result);
      
      // For now, return a placeholder response
      // Note: pythonEngine will be used here when executePythonScript is implemented
      const engineStatus = this.pythonEngine ? 'available' : 'unavailable';
      return { 
        message: 'Evolution job functionality not yet implemented', 
        script: pythonScript,
        engineStatus 
      };
    } catch (error) {
      logger.error('Evolution job failed:', error);
      throw new Error('Failed to start evolution job');
    }
  }

  private async getProvenStrategies(pair?: string, limit: number = 10, userId?: string): Promise<any[]> {
    // Mock implementation - replace with actual database query
    return [
      {
        id: 'strategy_001',
        name: 'RSI Mean Reversion',
        description: 'Buy when RSI < 30, sell when RSI > 70',
        fitnessScore: 0.85,
        verified: true,
        pair: pair || 'EURUSD',
        userId,
        performance: {
          sharpeRatio: 1.8,
          winRate: 0.65,
          maxDrawdown: 0.12
        }
      }
    ].slice(0, limit);
  }

  private async getForexGenome(pair: string, timeframe: string, userId?: string): Promise<any> {
    // Mock implementation - replace with actual database query
    return {
      pair,
      timeframe,
      userId,
      sessionPatterns: {
        asianSession: { performance: 0.7, preferredIndicators: ['RSI', 'MACD'] },
        londonSession: { performance: 0.8, preferredIndicators: ['EMA', 'SMA'] },
        newYorkSession: { performance: 0.9, preferredIndicators: ['STOCH', 'RSI'] }
      },
      volatilityProfile: {
        lowVolatility: { preferredRisk: 1.5, optimalTimeframes: ['4H', '1D'] },
        mediumVolatility: { preferredRisk: 2.0, optimalTimeframes: ['1H', '4H'] },
        highVolatility: { preferredRisk: 3.0, optimalTimeframes: ['15M', '1H'] }
      },
      confidenceScore: 0.82,
      lastUpdated: new Date().toISOString()
    };
  }

  private setupWebSocket(): void {
    if (!this.server) return;

    this.wss = new WebSocket.Server({ server: this.server });

    this.wss.on('connection', (ws: WebSocket, _req) => {
      logger.info('New WebSocket connection established');

      ws.on('message', (message: string) => {
        try {
          const data = JSON.parse(message);
          this.handleWebSocketMessage(ws, data);
        } catch (error) {
          logger.error('Invalid WebSocket message:', error);
        }
      });

      ws.on('close', () => {
        logger.info('WebSocket connection closed');
      });

      ws.on('error', (error) => {
        logger.error('WebSocket error:', error);
      });

      // Send welcome message
      ws.send(JSON.stringify({
        type: 'welcome',
        message: 'Connected to AI Trading Platform with Darwin Gödel Machine',
        timestamp: new Date().toISOString(),
        features: ['S3 Core NLP', 'Strategy Verification', 'Evolution Engine', 'Bridge Services']
      }));
    });
  }

  private handleWebSocketMessage(ws: WebSocket, data: any): void {
    switch (data.type) {
      case 'subscribe_evolution':
        ws.send(JSON.stringify({
          type: 'subscription_confirmed',
          jobId: data.jobId,
          timestamp: new Date().toISOString()
        }));
        break;

      case 'ping':
        ws.send(JSON.stringify({
          type: 'pong',
          timestamp: new Date().toISOString()
        }));
        break;

      default:
        ws.send(JSON.stringify({
          type: 'error',
          message: 'Unknown message type',
          timestamp: new Date().toISOString()
        }));
    }
  }

  private setupErrorHandling(): void {
    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: 'Endpoint not found',
        message: `The requested endpoint ${req.method} ${req.originalUrl} was not found.`,
        availableEndpoints: [
          'GET /health',
          'GET /api/docs',
          'POST /api/auth/login',
          'POST /api/darwin/chat',
          'POST /api/bridge/backtest'
        ]
      });
    });

    // Global error handler
    this.app.use((error: any, req: express.Request, res: express.Response, _next: express.NextFunction) => {
      logger.error('Unhandled error:', {
        error: error.message,
        stack: error.stack,
        url: req.url,
        method: req.method,
        ip: req.ip
      });

      res.status(error.status || 500).json({
        error: 'Internal server error',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong',
        timestamp: new Date().toISOString()
      });
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception:', error);
      process.exit(1);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
      process.exit(1);
    });
  }

  async start(): Promise<void> {
    try {
      // Initialize verification engine
      await this.verificationEngine.initialize();
      logger.info('Strategy Verification Engine initialized');

      // Start HTTP server
      this.server = this.app.listen(this.port, () => {
        logger.info(`🚀 AI Trading Platform Server running on port ${this.port}`);
      });

      // Setup WebSocket server
      this.setupWebSocket();
      logger.info('WebSocket server initialized');

      // Log successful startup
      logger.info('🎉 AI Enhanced Trading Platform with Darwin Gödel Machine is ready!');
      logger.info(`📊 Authentication: Active`);
      logger.info(`🌉 Bridge Services: Active`);
      logger.info(`🧬 Darwin Gödel Machine: Active`);
      logger.info(`🤖 S3 Core NLP Engine: Active`);
      logger.info(`🔬 Strategy Verification Engine: Active`);
      logger.info(`🌐 WebSocket Server: Active`);
      logger.info(`🔗 Health check: http://localhost:${this.port}/health`);
      logger.info(`📖 API docs: http://localhost:${this.port}/api/docs`);

    } catch (error) {
      logger.error('Failed to start AI Trading Platform Server:', error);
      throw error;
    }
  }

  async stop(): Promise<void> {
    logger.info('Shutting down AI Trading Platform Server...');

    try {
      // Close WebSocket server
      if (this.wss) {
        this.wss.close();
      }

      // Close HTTP server
      if (this.server) {
        this.server.close();
      }

      // Cleanup verification engine
      await this.verificationEngine.cleanup();

      logger.info('✅ AI Trading Platform Server shutdown complete');
    } catch (error) {
      logger.error('Error during shutdown:', error);
      throw error;
    }
  }
}

// Export for use in other modules
export default AITradingPlatformServer;

// If running directly, start the server
if (require.main === module) {
  const server = new AITradingPlatformServer();

  server.start().catch((error) => {
    console.error('Failed to start AI Trading Platform Server:', error);
    process.exit(1);
  });

  // Graceful shutdown handlers
  const gracefulShutdown = async (signal: string) => {
    console.log(`\nReceived ${signal}, shutting down gracefully...`);
    try {
      await server.stop();
      process.exit(0);
    } catch (error) {
      console.error('Error during graceful shutdown:', error);
      process.exit(1);
    }
  };

  process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
}