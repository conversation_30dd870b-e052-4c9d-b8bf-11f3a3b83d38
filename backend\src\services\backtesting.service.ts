import { 
  BacktestConfig, 
  BacktestResults, 
  BacktestMetrics, 
  BacktestTrade 
} from '../../../shared/schemas/backtest.schemas';
import { OHLCData } from '../../../shared/schemas/trading.schemas';
import { MarketDataService } from './market-data.service';
import { StrategyExecutor } from './strategy-executor.service';
import { MetricsCalculator } from './metrics-calculator.service';

export interface BacktestReport {
  summary: {
    strategy_name: string;
    period: string;
    total_return: number;
    annualized_return: number;
    volatility: number;
  };
  performance_metrics: BacktestMetrics;
  risk_analysis: {
    var_95: number;
    expected_shortfall: number;
    calmar_ratio: number;
    sortino_ratio: number;
  };
  recommendations: string[];
}

export interface OptimizationResult {
  best_parameters: Record<string, any>;
  best_score: number;
  optimization_results: Array<{
    parameters: Record<string, any>;
    metrics: BacktestMetrics;
    score: number;
  }>;
}

export interface ParameterRange {
  min: number;
  max: number;
  step: number;
}

export class BacktestingService {
  constructor(
    private marketDataService: MarketDataService,
    private strategyExecutor: StrategyExecutor,
    private metricsCalculator: MetricsCalculator
  ) {}

  async runBacktest(config: BacktestConfig): Promise<BacktestResults> {
    // Validate configuration
    this.validateBacktestConfig(config);

    // Fetch market data
    const marketData = await this.marketDataService.fetchHistoricalData(
      config.symbols,
      config.start_date,
      config.end_date
    );

    if (!marketData || marketData.length === 0) {
      throw new Error('Insufficient market data');
    }

    // Validate strategy
    const isValidStrategy = await this.strategyExecutor.validateStrategy(config.strategy);
    if (!isValidStrategy) {
      throw new Error('Invalid strategy configuration');
    }

    // Run simulation
    const { trades, balanceCurve } = await this.runSimulation(config, marketData);

    // Calculate metrics
    const metrics = this.calculateRiskMetrics(trades, balanceCurve);

    // Calculate monthly returns
    const monthlyReturns = this.calculateMonthlyReturns(balanceCurve);

    return {
      backtest_id: this.generateBacktestId(),
      config,
      metrics,
      trades,
      balance_curve: balanceCurve,
      monthly_returns: monthlyReturns,
      created_at: new Date(),
    };
  }

  async validateStrategy(strategy: { name: string; parameters: Record<string, any> }): Promise<boolean> {
    return await this.strategyExecutor.validateStrategy(strategy);
  }

  calculateRiskMetrics(
    trades: BacktestTrade[], 
    balanceCurve: Array<{ timestamp: Date; balance: number; equity: number; drawdown: number }>
  ): BacktestMetrics {
    return this.metricsCalculator.calculateMetrics(trades, balanceCurve);
  }

  async generateReport(results: BacktestResults): Promise<BacktestReport> {
    const { config, metrics } = results;
    
    // Calculate additional metrics for report
    const totalDays = Math.ceil(
      (config.end_date.getTime() - config.start_date.getTime()) / (1000 * 60 * 60 * 24)
    );
    const totalReturn = (metrics.total_pnl / config.initial_balance) * 100;
    const annualizedReturn = (Math.pow(1 + totalReturn / 100, 365 / totalDays) - 1) * 100;

    // Generate recommendations based on metrics
    const recommendations = this.generateRecommendations(metrics);

    return {
      summary: {
        strategy_name: config.strategy.name,
        period: `${config.start_date.toISOString().split('T')[0]} to ${config.end_date.toISOString().split('T')[0]}`,
        total_return: totalReturn,
        annualized_return: annualizedReturn,
        volatility: this.calculateVolatility(results.balance_curve),
      },
      performance_metrics: metrics,
      risk_analysis: {
        var_95: this.calculateVaR(results.balance_curve, 0.95),
        expected_shortfall: this.calculateExpectedShortfall(results.balance_curve, 0.95),
        calmar_ratio: metrics.calmar_ratio || 0,
        sortino_ratio: metrics.sortino_ratio || 0,
      },
      recommendations,
    };
  }

  async optimizeStrategy(
    config: BacktestConfig,
    parameterRanges: Record<string, ParameterRange>
  ): Promise<OptimizationResult> {
    const optimizationResults: OptimizationResult['optimization_results'] = [];
    let bestScore = -Infinity;
    let bestParameters: Record<string, any> = {};

    // Generate parameter combinations
    const parameterCombinations = this.generateParameterCombinations(parameterRanges);

    for (const parameters of parameterCombinations) {
      try {
        // Create config with new parameters
        const testConfig: BacktestConfig = {
          ...config,
          strategy: {
            ...config.strategy,
            parameters: { ...config.strategy.parameters, ...parameters },
          },
        };

        // Run backtest
        const results = await this.runBacktest(testConfig);
        
        // Calculate optimization score (using Sharpe ratio or custom metric)
        const score = this.calculateOptimizationScore(results.metrics);

        optimizationResults.push({
          parameters,
          metrics: results.metrics,
          score,
        });

        // Track best result
        if (score > bestScore) {
          bestScore = score;
          bestParameters = parameters;
        }
      } catch (error) {
        // Skip invalid parameter combinations
        console.warn(`Skipping parameter combination due to error:`, parameters, error);
      }
    }

    return {
      best_parameters: bestParameters,
      best_score: bestScore,
      optimization_results: optimizationResults.sort((a, b) => b.score - a.score),
    };
  }

  private validateBacktestConfig(config: BacktestConfig): void {
    if (config.start_date >= config.end_date) {
      throw new Error('Invalid date range: start date must be before end date');
    }

    if (config.symbols.length === 0) {
      throw new Error('At least one symbol must be specified');
    }

    if (config.initial_balance <= 0) {
      throw new Error('Initial balance must be positive');
    }
  }

  private async runSimulation(
    config: BacktestConfig,
    marketData: OHLCData[]
  ): Promise<{
    trades: BacktestTrade[];
    balanceCurve: Array<{ timestamp: Date; balance: number; equity: number; drawdown: number }>;
  }> {
    const trades: BacktestTrade[] = [];
    const balanceCurve: Array<{ timestamp: Date; balance: number; equity: number; drawdown: number }> = [];
    
    let currentBalance = config.initial_balance;
    let maxBalance = currentBalance;
    let openPositions: Array<{
      symbol: string;
      entry_price: number;
      volume: number;
      order_type: 'buy' | 'sell';
      entry_time: Date;
    }> = [];

    // Process each data point
    for (let i = 0; i < marketData.length; i++) {
      const currentData = marketData[i];
      const historicalData = marketData.slice(0, i + 1);

      // Prepare data for strategy execution
      const strategyData = {
        close: historicalData.map(d => d.close),
        open: historicalData.map(d => d.open),
        high: historicalData.map(d => d.high),
        low: historicalData.map(d => d.low),
        volume: historicalData.map(d => d.volume || 0),
      };

      try {
        // Execute strategy
        const signal = await this.strategyExecutor.execute(
          config.strategy,
          strategyData,
          config.strategy.parameters
        );

        // Process signal
        if (signal.signal === 'buy' && openPositions.length < config.risk_management.max_concurrent_trades) {
          // Calculate position size based on risk management
          const riskAmount = currentBalance * config.risk_management.max_risk_per_trade;
          const volume = this.calculatePositionSize(riskAmount, currentData.close);

          openPositions.push({
            symbol: currentData.symbol,
            entry_price: currentData.close,
            volume,
            order_type: 'buy',
            entry_time: currentData.timestamp,
          });
        } else if (signal.signal === 'sell' && openPositions.length > 0) {
          // Close oldest position
          const position = openPositions.shift();
          if (position) {
            const pnl = this.calculatePnL(position, currentData.close);
            currentBalance += pnl;

            trades.push({
              entry_time: position.entry_time,
              exit_time: currentData.timestamp,
              symbol: currentData.symbol,
              order_type: position.order_type,
              entry_price: position.entry_price,
              exit_price: currentData.close,
              volume: position.volume,
              pnl,
              pnl_pips: this.calculatePips(position.entry_price, currentData.close, currentData.symbol),
              duration_minutes: Math.floor(
                (currentData.timestamp.getTime() - position.entry_time.getTime()) / (1000 * 60)
              ),
              reason: 'strategy_exit',
            });
          }
        }

        // Update balance curve
        const equity = currentBalance + this.calculateUnrealizedPnL(openPositions, currentData.close);
        maxBalance = Math.max(maxBalance, equity);
        const drawdown = equity - maxBalance;

        balanceCurve.push({
          timestamp: currentData.timestamp,
          balance: currentBalance,
          equity,
          drawdown,
        });
      } catch (error) {
        console.warn(`Strategy execution error at ${currentData.timestamp}:`, error);
      }
    }

    // Close any remaining positions
    if (openPositions.length > 0 && marketData.length > 0) {
      const lastData = marketData[marketData.length - 1];
      for (const position of openPositions) {
        const pnl = this.calculatePnL(position, lastData.close);
        currentBalance += pnl;

        trades.push({
          entry_time: position.entry_time,
          exit_time: lastData.timestamp,
          symbol: lastData.symbol,
          order_type: position.order_type,
          entry_price: position.entry_price,
          exit_price: lastData.close,
          volume: position.volume,
          pnl,
          pnl_pips: this.calculatePips(position.entry_price, lastData.close, lastData.symbol),
          duration_minutes: Math.floor(
            (lastData.timestamp.getTime() - position.entry_time.getTime()) / (1000 * 60)
          ),
          reason: 'timeout',
        });
      }
    }

    return { trades, balanceCurve };
  }

  private calculatePositionSize(riskAmount: number, price: number): number {
    // Simple position sizing - can be enhanced with more sophisticated methods
    return Math.min(riskAmount / price, 0.01); // Max 0.01 lots
  }

  private calculatePnL(
    position: { entry_price: number; volume: number; order_type: 'buy' | 'sell' },
    exitPrice: number
  ): number {
    const priceDiff = position.order_type === 'buy' 
      ? exitPrice - position.entry_price 
      : position.entry_price - exitPrice;
    
    return priceDiff * position.volume * 100000; // Assuming forex with 100k base units
  }

  private calculateUnrealizedPnL(
    positions: Array<{ entry_price: number; volume: number; order_type: 'buy' | 'sell' }>,
    currentPrice: number
  ): number {
    return positions.reduce((total, position) => {
      return total + this.calculatePnL(position, currentPrice);
    }, 0);
  }

  private calculatePips(entryPrice: number, exitPrice: number, symbol: string): number {
    const pipValue = symbol.includes('JPY') ? 0.01 : 0.0001;
    return Math.abs(exitPrice - entryPrice) / pipValue;
  }

  private calculateMonthlyReturns(
    balanceCurve: Array<{ timestamp: Date; balance: number; equity: number; drawdown: number }>
  ): Array<{ year: number; month: number; return_percent: number }> {
    const monthlyReturns: Array<{ year: number; month: number; return_percent: number }> = [];
    const monthlyData = new Map<string, { start: number; end: number }>();

    for (const point of balanceCurve) {
      const key = `${point.timestamp.getFullYear()}-${point.timestamp.getMonth() + 1}`;
      
      if (!monthlyData.has(key)) {
        monthlyData.set(key, { start: point.equity, end: point.equity });
      } else {
        monthlyData.get(key)!.end = point.equity;
      }
    }

    for (const [key, data] of monthlyData) {
      const [year, month] = key.split('-').map(Number);
      const returnPercent = ((data.end - data.start) / data.start) * 100;
      
      monthlyReturns.push({
        year,
        month,
        return_percent: returnPercent,
      });
    }

    return monthlyReturns;
  }

  private generateRecommendations(metrics: BacktestMetrics): string[] {
    const recommendations: string[] = [];

    if (metrics.win_rate < 0.5) {
      recommendations.push('Consider improving strategy accuracy - win rate is below 50%');
    }

    if (metrics.profit_factor < 1.5) {
      recommendations.push('Profit factor is low - consider optimizing risk/reward ratio');
    }

    if (Math.abs(metrics.max_drawdown_percent) > 0.1) {
      recommendations.push('Maximum drawdown exceeds 10% - consider implementing stricter risk management');
    }

    if (metrics.total_trades < 30) {
      recommendations.push('Low number of trades - consider testing over a longer period for statistical significance');
    }

    if (metrics.sharpe_ratio && metrics.sharpe_ratio < 1.0) {
      recommendations.push('Sharpe ratio is below 1.0 - strategy may not provide adequate risk-adjusted returns');
    }

    return recommendations;
  }

  private calculateVolatility(
    balanceCurve: Array<{ timestamp: Date; balance: number; equity: number; drawdown: number }>
  ): number {
    if (balanceCurve.length < 2) return 0;

    const returns = [];
    for (let i = 1; i < balanceCurve.length; i++) {
      const returnRate = (balanceCurve[i].equity - balanceCurve[i - 1].equity) / balanceCurve[i - 1].equity;
      returns.push(returnRate);
    }

    const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / returns.length;
    
    return Math.sqrt(variance) * Math.sqrt(252); // Annualized volatility
  }

  private calculateVaR(
    balanceCurve: Array<{ timestamp: Date; balance: number; equity: number; drawdown: number }>,
    confidence: number
  ): number {
    if (balanceCurve.length < 2) return 0;

    const returns = [];
    for (let i = 1; i < balanceCurve.length; i++) {
      const returnRate = (balanceCurve[i].equity - balanceCurve[i - 1].equity) / balanceCurve[i - 1].equity;
      returns.push(returnRate);
    }

    returns.sort((a, b) => a - b);
    const index = Math.floor((1 - confidence) * returns.length);
    
    return returns[index] || 0;
  }

  private calculateExpectedShortfall(
    balanceCurve: Array<{ timestamp: Date; balance: number; equity: number; drawdown: number }>,
    confidence: number
  ): number {
    const var95 = this.calculateVaR(balanceCurve, confidence);
    
    if (balanceCurve.length < 2) return 0;

    const returns = [];
    for (let i = 1; i < balanceCurve.length; i++) {
      const returnRate = (balanceCurve[i].equity - balanceCurve[i - 1].equity) / balanceCurve[i - 1].equity;
      returns.push(returnRate);
    }

    const tailReturns = returns.filter(r => r <= var95);
    
    return tailReturns.length > 0 
      ? tailReturns.reduce((sum, r) => sum + r, 0) / tailReturns.length 
      : 0;
  }

  private generateParameterCombinations(
    parameterRanges: Record<string, ParameterRange>
  ): Array<Record<string, any>> {
    const combinations: Array<Record<string, any>> = [];
    const parameterNames = Object.keys(parameterRanges);
    
    if (parameterNames.length === 0) return combinations;

    const generateCombinations = (index: number, current: Record<string, any>) => {
      if (index === parameterNames.length) {
        combinations.push({ ...current });
        return;
      }

      const paramName = parameterNames[index];
      const range = parameterRanges[paramName];
      
      for (let value = range.min; value <= range.max; value += range.step) {
        current[paramName] = value;
        generateCombinations(index + 1, current);
      }
    };

    generateCombinations(0, {});
    return combinations;
  }

  private calculateOptimizationScore(metrics: BacktestMetrics): number {
    // Custom scoring function - can be adjusted based on preferences
    const sharpeWeight = 0.4;
    const profitFactorWeight = 0.3;
    const winRateWeight = 0.2;
    const drawdownWeight = 0.1;

    const sharpeScore = (metrics.sharpe_ratio || 0) * sharpeWeight;
    const profitFactorScore = Math.min(metrics.profit_factor / 2, 1) * profitFactorWeight;
    const winRateScore = metrics.win_rate * winRateWeight;
    const drawdownScore = (1 - Math.abs(metrics.max_drawdown_percent)) * drawdownWeight;

    return sharpeScore + profitFactorScore + winRateScore + drawdownScore;
  }

  private generateBacktestId(): string {
    return 'backtest_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }
}