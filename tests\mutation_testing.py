#!/usr/bin/env python3
"""
Mutation Testing Script for AI Trading Platform

This script runs mutation tests on the codebase to identify weaknesses in the test suite.
Mutation testing works by making small changes to the code (mutations) and checking if tests catch these changes.
If tests pass despite the mutation, it indicates a weakness in the test suite.

Usage:
    python tests/mutation_testing.py [module_path]

Example:
    python tests/mutation_testing.py python_engine/mt5_bridge.py
"""

import sys
import subprocess
import os
import json
from datetime import datetime
from pathlib import Path


def run_mutation_tests(target_module=None):
    """Run mutation tests on the specified module or the entire codebase"""
    print("🧬 Running mutation tests...")
    
    # Ensure mutmut is installed
    try:
        import mutmut
    except ImportError:
        print("Installing mutmut...")
        subprocess.run(["pip", "install", "mutmut"], check=True)
    
    # Build the command
    cmd = ["mutmut", "run"]
    if target_module:
        cmd.extend(["--paths", target_module])
    
    # Run mutation tests
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            check=False
        )
        
        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)
        
        # Generate HTML report
        subprocess.run(["mutmut", "html"], check=False)
        
        # Show results
        show_result = subprocess.run(
            ["mutmut", "results"],
            capture_output=True,
            text=True,
            check=False
        )
        
        print("\n📊 Mutation Test Results:")
        print(show_result.stdout)
        
        # Save results to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"mutation_report_{timestamp}.txt"
        
        with open(report_file, "w") as f:
            f.write("Mutation Test Results\n")
            f.write("====================\n\n")
            f.write(show_result.stdout)
            f.write("\n\nRaw Output:\n")
            f.write(result.stdout)
        
        print(f"\n💾 Detailed report saved to: {report_file}")
        print("📄 HTML report available at: html/index.html")
        
        return True
    except Exception as e:
        print(f"❌ Error running mutation tests: {e}")
        return False


def main():
    """Main function"""
    target_module = sys.argv[1] if len(sys.argv) > 1 else None
    run_mutation_tests(target_module)


if __name__ == "__main__":
    main()