import pytest
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from pattern_detector import StrategyPatternDetector

class TestStrategyPatternDetector:
    """Test pattern detection for trading strategies"""
    
    def setup_method(self):
        """Setup before each test"""
        self.detector = StrategyPatternDetector()
    
    # Test 1: Detect Mean Reversion Pattern
    def test_detects_mean_reversion_strategy(self):
        """It should identify mean reversion strategies"""
        # Arrange - A typical mean reversion strategy
        mean_reversion_code = """
def trading_strategy(data, params):
    # Calculate 20-period moving average
    sma = calculate_sma(data['close'], 20)
    current_price = data['close'][-1]
    
    # Buy when price is below average (expecting reversion up)
    if current_price < sma[-1] * 0.98:
        return {'signal': 'buy', 'confidence': 0.8}
    # Sell when price is above average (expecting reversion down)
    elif current_price > sma[-1] * 1.02:
        return {'signal': 'sell', 'confidence': 0.8}
    else:
        return {'signal': 'hold', 'confidence': 0.5}
"""
        
        # Act
        result = self.detector.analyze_strategy(mean_reversion_code)
        
        # Assert
        assert result['pattern_type'] == 'mean_reversion'
        assert 'sma' in result['indicators_found']
        assert result['confidence'] >= 0.8
        assert 'buying below average' in result['characteristics']
    
    # Test 2: Detect Momentum Pattern
    def test_detects_momentum_strategy(self):
        """It should identify momentum/trend following strategies"""
        # Arrange - A typical momentum strategy
        momentum_code = """
def trading_strategy(data, params):
    # Calculate short and long moving averages
    sma_fast = calculate_sma(data['close'], 10)
    sma_slow = calculate_sma(data['close'], 30)
    
    # Buy when fast MA crosses above slow MA (momentum up)
    if sma_fast[-1] > sma_slow[-1] and sma_fast[-2] <= sma_slow[-2]:
        return {'signal': 'buy', 'confidence': 0.9}
    # Sell when fast MA crosses below slow MA (momentum down)
    elif sma_fast[-1] < sma_slow[-1] and sma_fast[-2] >= sma_slow[-2]:
        return {'signal': 'sell', 'confidence': 0.9}
    else:
        return {'signal': 'hold', 'confidence': 0.3}
"""
        
        # Act
        result = self.detector.analyze_strategy(momentum_code)
        
        # Assert
        assert result['pattern_type'] == 'momentum'
        assert 'crossover' in result['characteristics']
        assert result['confidence'] >= 0.85
        assert len(result['indicators_found']) >= 2  # At least 2 MAs
    
    # Test 3: Detect RSI-based Patterns
    def test_detects_rsi_patterns(self):
        """It should identify RSI-based strategies correctly"""
        # Arrange - RSI strategy (could be mean reversion or momentum)
        rsi_code = """
def trading_strategy(data, params):
    rsi = calculate_rsi(data['close'], 14)
    
    # Classic RSI: Buy oversold, sell overbought (mean reversion)
    if rsi[-1] < 30:
        return {'signal': 'buy', 'confidence': 0.7}
    elif rsi[-1] > 70:
        return {'signal': 'sell', 'confidence': 0.7}
    else:
        return {'signal': 'hold', 'confidence': 0.5}
"""
        
        # Act
        result = self.detector.analyze_strategy(rsi_code)
        
        # Assert
        assert result['pattern_type'] == 'mean_reversion'
        assert 'rsi' in result['indicators_found']
        assert 'oversold/overbought' in result['characteristics']
    
    # Test 4: Detect Mixed/Unclear Patterns
    def test_handles_mixed_patterns(self):
        """It should handle strategies with mixed patterns"""
        # Arrange - Strategy with both patterns
        mixed_code = """
def trading_strategy(data, params):
    sma = calculate_sma(data['close'], 20)
    rsi = calculate_rsi(data['close'], 14)
    
    # Mixed logic
    if rsi[-1] < 30 and data['close'][-1] > sma[-1]:
        return {'signal': 'buy', 'confidence': 0.6}
"""
        
        # Act
        result = self.detector.analyze_strategy(mixed_code)
        
        # Assert
        assert result['pattern_type'] in ['mixed', 'custom']
        assert result['confidence'] < 0.7  # Lower confidence for mixed
        assert len(result['warnings']) > 0
    
    # Test 5: Extract All Indicators Used
    def test_extracts_all_indicators(self):
        """It should find all technical indicators used"""
        # Arrange
        multi_indicator_code = """
def trading_strategy(data, params):
    sma = calculate_sma(data['close'], 20)
    ema = calculate_ema(data['close'], 12)
    rsi = calculate_rsi(data['close'], 14)
    macd, signal = calculate_macd(data['close'])
    upper, lower = calculate_bollinger_bands(data['close'], 20, 2)
    
    # Some logic here...
    return {'signal': 'hold'}
"""
        
        # Act
        result = self.detector.analyze_strategy(multi_indicator_code)
        
        # Assert
        expected_indicators = ['sma', 'ema', 'rsi', 'macd', 'bollinger']
        for indicator in expected_indicators:
            assert indicator in result['indicators_found']
    
    # Test 6: Detect Simple Patterns
    def test_detects_price_action_patterns(self):
        """It should detect simple price action strategies"""
        # Arrange
        price_action_code = """
def trading_strategy(data, params):
    # Simple price action - no indicators
    if data['close'][-1] > data['close'][-2]:
        return {'signal': 'buy', 'confidence': 0.5}
    else:
        return {'signal': 'sell', 'confidence': 0.5}
"""
        
        # Act
        result = self.detector.analyze_strategy(price_action_code)
        
        # Assert
        assert result['pattern_type'] == 'price_action'
        assert len(result['indicators_found']) == 0
        assert 'simple' in result['characteristics']

    # Test 7: Detect Breakout Patterns
    def test_detects_breakout_patterns(self):
        """It should identify breakout strategies"""
        # Arrange
        breakout_code = """
def trading_strategy(data, params):
    # Breakout strategy using recent highs/lows
    recent_high = max(data['high'][-20:])
    recent_low = min(data['low'][-20:])
    current_price = data['close'][-1]
    
    if current_price > recent_high:
        return {'signal': 'buy', 'confidence': 0.85}
    elif current_price < recent_low:
        return {'signal': 'sell', 'confidence': 0.85}
    else:
        return {'signal': 'hold', 'confidence': 0.3}
"""
        
        # Act
        result = self.detector.analyze_strategy(breakout_code)
        
        # Assert
        assert result['pattern_type'] == 'breakout'
        assert 'breakout levels' in result['characteristics']
        assert result['confidence'] >= 0.7

    # Test 8: Detect Complex Strategies
    def test_detects_complex_strategies(self):
        """It should identify overly complex strategies"""
        # Arrange
        complex_code = """
def trading_strategy(data, params):
    sma = calculate_sma(data['close'], 20)
    rsi = calculate_rsi(data['close'], 14)
    macd, signal = calculate_macd(data['close'])
    volume_sma = calculate_sma(data['volume'], 10)
    
    if (rsi[-1] > 70 and 
        macd[-1] > signal[-1] and 
        data['close'][-1] > sma[-1] and
        data['volume'][-1] > volume_sma[-1] * 1.5 and
        data['high'][-1] - data['low'][-1] > 2.0 and
        params['magic_number'] == 42):
        return {'signal': 'buy', 'confidence': 0.9}
    elif (rsi[-1] < 30 and 
          macd[-1] < signal[-1] and 
          data['close'][-1] < sma[-1] and
          data['volume'][-1] < volume_sma[-1] * 0.5):
        return {'signal': 'sell', 'confidence': 0.9}
    else:
        return {'signal': 'hold', 'confidence': 0.1}
"""
        
        # Act
        result = self.detector.analyze_strategy(complex_code)
        
        # Assert
        assert len(result['indicators_found']) >= 4
        assert 'too many conditions' in ' '.join(result['warnings']).lower()
        assert result['complexity_score'] > 0.7

    # Test 9: Warning Generation
    def test_generates_appropriate_warnings(self):
        """It should generate helpful warnings"""
        # Arrange - Strategy with no risk management
        risky_code = """
def trading_strategy(data, params):
    if data['close'][-1] > data['close'][-2]:
        return {'signal': 'buy', 'confidence': 1.0}
    else:
        return {'signal': 'sell', 'confidence': 1.0}
"""
        
        # Act
        result = self.detector.analyze_strategy(risky_code)
        
        # Assert
        warnings_text = ' '.join(result['warnings']).lower()
        assert 'stop' in warnings_text or 'risk' in warnings_text

    # Test 10: Confidence Scoring
    def test_confidence_scoring_accuracy(self):
        """It should provide accurate confidence scores"""
        # Arrange - Very clear mean reversion strategy
        clear_strategy = """
def trading_strategy(data, params):
    sma = calculate_sma(data['close'], 20)
    current_price = data['close'][-1]
    
    # Classic mean reversion with clear signals
    if current_price < sma[-1] * 0.95:  # 5% below SMA
        return {'signal': 'buy', 'confidence': 0.8}
    elif current_price > sma[-1] * 1.05:  # 5% above SMA
        return {'signal': 'sell', 'confidence': 0.8}
    else:
        return {'signal': 'hold', 'confidence': 0.2}
"""
        
        # Act
        result = self.detector.analyze_strategy(clear_strategy)
        
        # Assert
        assert result['confidence'] >= 0.8  # High confidence for clear pattern
        assert result['pattern_type'] == 'mean_reversion'