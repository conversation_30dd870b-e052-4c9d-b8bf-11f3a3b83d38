// tests/ai/zero-hallucination-chatbot.test.ts
import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { ZeroHallucinationChatbot } from '../../src/ai/zero-hallucination-chatbot';
import { ForexDataProvider } from '../../src/data-management/forex-data-provider';

// TDD Tests for Zero Hallucination Architecture
describe('ZeroHallucinationChatbot - TDD Implementation', () => {
  let chatbot: ZeroHallucinationChatbot;
  let mockDataProvider: jest.Mocked<ForexDataProvider>;

  beforeEach(() => {
    mockDataProvider = {
      getAvailableData: jest.fn(),
      getData: jest.fn(),
      getAuditTrail: jest.fn()
    } as any;
    
    chatbot = new ZeroHallucinationChatbot(mockDataProvider);
  });

  describe('No Hallucination Guarantee', () => {
    it('should say "I don\'t know" when no data exists', async () => {
      // Arrange
      mockDataProvider.getAvailableData.mockResolvedValue({
        pairs: [],
        totalDataPoints: 0
      });
      
      // Act
      const response = await chatbot.processQuery("What's my win rate?");
      
      // Assert
      expect(response.message).toContain("I don't have any backtest data");
      expect(response.message).not.toMatch(/\d+%/); // No made-up percentages
      expect(response.verificationStatus.noHallucination).toBe(true);
    });

    it('should never make up performance numbers', async () => {
      // Arrange
      mockDataProvider.getAvailableData.mockResolvedValue({
        pairs: [{ pair: 'EUR/USD', timeframes: ['H1'] }],
        totalDataPoints: 1000
      });
      
      // Act
      const response = await chatbot.processQuery("What's the typical win rate for RSI strategies?");
      
      // Assert
      expect(response.message).not.toMatch(/\d+%.*win rate/); // No made-up win rates
      expect(response.message).toContain("I can only provide data from actual backtests");
      expect(response.verificationStatus.noHallucination).toBe(true);
    });

    it('should only reference verified data sources', async () => {
      // Arrange
      const mockBacktestData = {
        strategyId: 'rsi_001',
        totalTrades: 234,
        winRate: 56.8,
        auditTrailId: 'bt_20240120_abc123'
      };
      
      mockDataProvider.getAuditTrail.mockResolvedValue({
        pair: 'EUR/USD',
        timeframe: 'H1',
        operations: [{
          operation: 'backtest_completed',
          timestamp: new Date(),
          dataHash: 'sha256:abc123'
        }]
      });
      
      // Act
      const response = await chatbot.processQueryWithData("Show my strategy performance", mockBacktestData);
      
      // Assert
      expect(response.sources).toBeDefined();
      expect(response.sources).toHaveLength(1);
      expect(response.sources![0].type).toBe('backtest');
      expect(response.sources![0].id).toBe('bt_20240120_abc123');
      expect(response.verificationStatus.sourcesProvided).toBe(true);
    });

    it('should admit uncertainty explicitly', async () => {
      // Act
      const response = await chatbot.processQuery("Will EUR/USD go up tomorrow?");
      
      // Assert
      expect(response.message).toContain("I cannot predict future price movements");
      expect(response.message).not.toMatch(/will (go up|rise|fall|drop)/i);
      expect(response.verificationStatus.noHallucination).toBe(true);
    });
  });

  describe('Intent Recognition - No Guessing', () => {
    it('should recognize known intents accurately', async () => {
      // Act
      const response1 = await chatbot.processQuery("create a strategy");
      const response2 = await chatbot.processQuery("build me an RSI strategy");
      const response3 = await chatbot.processQuery("make a trading bot");
      
      // Assert
      expect(response1.intent).toBe('create_strategy');
      expect(response2.intent).toBe('create_strategy');
      expect(response3.intent).toBe('create_strategy');
    });

    it('should handle unknown intents honestly', async () => {
      // Act
      const response = await chatbot.processQuery("xyz random gibberish abc");
      
      // Assert
      expect(response.intent).toBe('unknown');
      expect(response.message).toContain("I don't understand that request");
      expect(response.message).toContain("Here's what I can help with");
    });

    it('should not guess at ambiguous requests', async () => {
      // Act
      const response = await chatbot.processQuery("it");
      
      // Assert
      expect(response.intent).toBe('clarification_needed');
      expect(response.message).toContain("Could you be more specific");
    });
  });

  describe('Data Validation Before Response', () => {
    it('should validate data integrity before using it', async () => {
      // Arrange
      const corruptedData = {
        candles: [
          { open: 1.1, high: 1.05, low: 1.09, close: 1.08 } // Invalid: high < low
        ]
      };
      
      mockDataProvider.getData.mockResolvedValue({
        success: true,
        data: corruptedData
      });
      
      // Act
      const response = await chatbot.processQuery("Analyze EUR/USD data");
      
      // Assert
      expect(response.message).toContain("Data validation failed");
      expect(response.verificationStatus.dataVerified).toBe(false);
    });

    it('should include data hash in responses for verification', async () => {
      // Arrange
      const validData = {
        candles: [
          { open: 1.1, high: 1.11, low: 1.09, close: 1.105 }
        ],
        verification: {
          originalHash: 'sha256:abc123def456',
          dataPoints: 1000
        }
      };
      
      mockDataProvider.getData.mockResolvedValue({
        success: true,
        data: validData
      });
      
      // Act
      const response = await chatbot.processQuery("Show EUR/USD statistics");
      
      // Assert
      expect(response.dataIntegrityHash).toBe('sha256:abc123def456');
      expect(response.verificationStatus.dataVerified).toBe(true);
    });
  });

  describe('Template-Based Responses', () => {
    it('should use verified code templates only', async () => {
      // Act
      const response = await chatbot.processQuery("Create an RSI strategy");
      
      // Assert
      expect(response.codeTemplate).toBeDefined();
      expect(response.templateVerification).toBeDefined();
      expect(response.templateVerification!.templateId).toBe('rsi_v1');
      expect(response.templateVerification!.lastTested).toBeDefined();
    });

    it('should not generate code without templates', async () => {
      // Act
      const response = await chatbot.processQuery("Create a quantum trading strategy");
      
      // Assert
      expect(response.codeTemplate).toBeUndefined();
      expect(response.message).toContain("I don't have a verified template");
    });
  });

  describe('Audit Trail Integration', () => {
    it('should log all interactions for audit', async () => {
      // Act
      await chatbot.processQuery("What data is available?");
      
      const auditLog = await chatbot.getAuditLog();
      
      // Assert
      expect(auditLog.interactions).toHaveLength(1);
      expect(auditLog.interactions[0].query).toBe("What data is available?");
      expect(auditLog.interactions[0].timestamp).toBeDefined();
      expect(auditLog.interactions[0].verificationStatus).toBeDefined();
    });

    it('should track data sources used in responses', async () => {
      // Arrange
      mockDataProvider.getAvailableData.mockResolvedValue({
        pairs: [{ pair: 'EUR/USD', timeframes: ['H1'] }],
        totalDataPoints: 5000
      });
      
      // Act
      const response = await chatbot.processQuery("What data do you have?");
      
      // Assert
      expect(response.sources).toBeDefined();
      expect(response.sources![0].type).toBe('data_inventory');
      expect(response.sources![0].dataPoints).toBe(5000);
    });
  });

  describe('Response Verification', () => {
    it('should include verification status in all responses', async () => {
      // Act
      const response = await chatbot.processQuery("Hello");
      
      // Assert
      expect(response.verificationStatus).toBeDefined();
      expect(response.verificationStatus.noHallucination).toBe(true);
      expect(response.verificationStatus.sourcesProvided).toBeDefined();
      expect(response.verificationStatus.dataVerified).toBeDefined();
    });

    it('should mark responses as unverified when appropriate', async () => {
      // Arrange - simulate data provider error
      mockDataProvider.getAvailableData.mockRejectedValue(new Error('Connection failed'));
      
      // Act
      const response = await chatbot.processQuery("What data is available?");
      
      // Assert
      expect(response.verificationStatus.dataVerified).toBe(false);
      expect(response.message).toContain("Unable to verify data availability");
    });
  });
});