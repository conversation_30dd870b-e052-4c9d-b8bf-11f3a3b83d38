{"timestamp": ["2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00"], "open": [1.1, 1.1001, 1.1002, 1.1003, 1.1004, 1.1005, 1.1006, 1.1007, 1.1008, 1.1009, 1.101, 1.1011000000000002, 1.1012000000000002, 1.1013000000000002, 1.1014000000000002, 1.1015000000000001, 1.1016000000000001, 1.1017000000000001, 1.1018000000000001, 1.1019, 1.102, 1.1021, 1.1022, 1.1023, 1.1024, 1.1025, 1.1026, 1.1027, 1.1028, 1.1029, 1.103, 1.1031000000000002, 1.1032000000000002, 1.1033000000000002, 1.1034000000000002, 1.1035000000000001, 1.1036000000000001, 1.1037000000000001, 1.1038000000000001, 1.1039, 1.104, 1.1041, 1.1042, 1.1043, 1.1044, 1.1045, 1.1046, 1.1047, 1.1048, 1.1049, 1.105, 1.1051000000000002, 1.1052000000000002, 1.1053000000000002, 1.1054000000000002, 1.1055000000000001, 1.1056000000000001, 1.1057000000000001, 1.1058000000000001, 1.1059, 1.106, 1.1061, 1.1062, 1.1063, 1.1064, 1.1065, 1.1066, 1.1067, 1.1068, 1.1069, 1.107, 1.1071000000000002, 1.1072000000000002, 1.1073000000000002, 1.1074000000000002, 1.1075000000000002, 1.1076000000000001, 1.1077000000000001, 1.1078000000000001, 1.1079, 1.108, 1.1081, 1.1082, 1.1083, 1.1084, 1.1085, 1.1086, 1.1087, 1.1088, 1.1089, 1.109, 1.1091000000000002, 1.1092000000000002, 1.1093000000000002, 1.1094000000000002, 1.1095000000000002, 1.1096000000000001, 1.1097000000000001, 1.1098000000000001, 1.1099], "high": [1.105, 1.1051, 1.1052, 1.1053, 1.1054, 1.1055, 1.1056, 1.1057, 1.1058, 1.1058999999999999, 1.1059999999999999, 1.1061, 1.1062, 1.1063, 1.1064, 1.1065, 1.1066, 1.1067, 1.1068, 1.1069, 1.107, 1.1071, 1.1072, 1.1073, 1.1074, 1.1075, 1.1076, 1.1077, 1.1078, 1.1078999999999999, 1.1079999999999999, 1.1081, 1.1082, 1.1083, 1.1084, 1.1085, 1.1086, 1.1087, 1.1088, 1.1089, 1.109, 1.1091, 1.1092, 1.1093, 1.1094, 1.1095, 1.1096, 1.1097, 1.1098, 1.1098999999999999, 1.1099999999999999, 1.1101, 1.1102, 1.1103, 1.1104, 1.1105, 1.1106, 1.1107, 1.1108, 1.1109, 1.111, 1.1111, 1.1112, 1.1113, 1.1114, 1.1115, 1.1116, 1.1117, 1.1118, 1.1118999999999999, 1.1119999999999999, 1.1121, 1.1122, 1.1123, 1.1124, 1.1125, 1.1126, 1.1127, 1.1128, 1.1129, 1.113, 1.1131, 1.1132, 1.1133, 1.1134, 1.1135, 1.1136, 1.1137, 1.1138, 1.1139, 1.1139999999999999, 1.1141, 1.1142, 1.1143, 1.1144, 1.1145, 1.1146, 1.1147, 1.1148, 1.1149], "low": [1.095, 1.0951, 1.0952, 1.0953, 1.0954, 1.0955, 1.0956, 1.0957, 1.0957999999999999, 1.0958999999999999, 1.0959999999999999, 1.0961, 1.0962, 1.0963, 1.0964, 1.0965, 1.0966, 1.0967, 1.0968, 1.0969, 1.097, 1.0971, 1.0972, 1.0973, 1.0974, 1.0975, 1.0976, 1.0977, 1.0977999999999999, 1.0978999999999999, 1.0979999999999999, 1.0981, 1.0982, 1.0983, 1.0984, 1.0985, 1.0986, 1.0987, 1.0988, 1.0989, 1.099, 1.0991, 1.0992, 1.0993, 1.0994, 1.0995, 1.0996, 1.0997, 1.0997999999999999, 1.0998999999999999, 1.0999999999999999, 1.1001, 1.1002, 1.1003, 1.1004, 1.1005, 1.1006, 1.1007, 1.1008, 1.1009, 1.101, 1.1011, 1.1012, 1.1013, 1.1014, 1.1015, 1.1016, 1.1017, 1.1018, 1.1018999999999999, 1.1019999999999999, 1.1021, 1.1022, 1.1023, 1.1024, 1.1025, 1.1026, 1.1027, 1.1028, 1.1029, 1.103, 1.1031, 1.1032, 1.1033, 1.1034, 1.1035, 1.1036, 1.1037, 1.1038, 1.1038999999999999, 1.1039999999999999, 1.1041, 1.1042, 1.1043, 1.1044, 1.1045, 1.1046, 1.1047, 1.1048, 1.1049], "close": [1.102, 1.1021, 1.1022, 1.1023, 1.1024, 1.1025, 1.1026, 1.1027, 1.1028, 1.1029, 1.103, 1.1031000000000002, 1.1032000000000002, 1.1033000000000002, 1.1034000000000002, 1.1035000000000001, 1.1036000000000001, 1.1037000000000001, 1.1038000000000001, 1.1039, 1.104, 1.1041, 1.1042, 1.1043, 1.1044, 1.1045, 1.1046, 1.1047, 1.1048, 1.1049, 1.105, 1.1051000000000002, 1.1052000000000002, 1.1053000000000002, 1.1054000000000002, 1.1055000000000001, 1.1056000000000001, 1.1057000000000001, 1.1058000000000001, 1.1059, 1.106, 1.1061, 1.1062, 1.1063, 1.1064, 1.1065, 1.1066, 1.1067, 1.1068, 1.1069, 1.107, 1.1071000000000002, 1.1072000000000002, 1.1073000000000002, 1.1074000000000002, 1.1075000000000002, 1.1076000000000001, 1.1077000000000001, 1.1078000000000001, 1.1079, 1.108, 1.1081, 1.1082, 1.1083, 1.1084, 1.1085, 1.1086, 1.1087, 1.1088, 1.1089, 1.109, 1.1091000000000002, 1.1092000000000002, 1.1093000000000002, 1.1094000000000002, 1.1095000000000002, 1.1096000000000001, 1.1097000000000001, 1.1098000000000001, 1.1099, 1.11, 1.1101, 1.1102, 1.1103, 1.1104, 1.1105, 1.1106, 1.1107, 1.1108, 1.1109, 1.111, 1.1111000000000002, 1.1112000000000002, 1.1113000000000002, 1.1114000000000002, 1.1115000000000002, 1.1116000000000001, 1.1117000000000001, 1.1118000000000001, 1.1119], "volume": [1000, 1010, 1020, 1030, 1040, 1050, 1060, 1070, 1080, 1090, 1100, 1110, 1120, 1130, 1140, 1150, 1160, 1170, 1180, 1190, 1200, 1210, 1220, 1230, 1240, 1250, 1260, 1270, 1280, 1290, 1300, 1310, 1320, 1330, 1340, 1350, 1360, 1370, 1380, 1390, 1400, 1410, 1420, 1430, 1440, 1450, 1460, 1470, 1480, 1490, 1500, 1510, 1520, 1530, 1540, 1550, 1560, 1570, 1580, 1590, 1600, 1610, 1620, 1630, 1640, 1650, 1660, 1670, 1680, 1690, 1700, 1710, 1720, 1730, 1740, 1750, 1760, 1770, 1780, 1790, 1800, 1810, 1820, 1830, 1840, 1850, 1860, 1870, 1880, 1890, 1900, 1910, 1920, 1930, 1940, 1950, 1960, 1970, 1980, 1990]}