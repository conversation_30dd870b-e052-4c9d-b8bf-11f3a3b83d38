/**
 * Strategies Page
 * Allows users to manage their trading strategies
 */

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { PlusIcon, TrashIcon, PencilIcon } from '@heroicons/react/24/outline';

import { PageHeader } from '@/components/ui/page-header';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Spinner } from '@/components/ui/spinner';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

import { api } from '@/services/api';

interface Strategy {
  id: string;
  name: string;
  description: string;
  config: any;
  created_at: string;
  updated_at: string;
}

export default function StrategiesPage() {
  const queryClient = useQueryClient();
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedStrategy, setSelectedStrategy] = useState<Strategy | null>(null);
  const [newStrategy, setNewStrategy] = useState({
    name: '',
    description: '',
    config: {
      indicators: {
        rsi: { period: 14, overbought: 70, oversold: 30 },
        macd: { fastPeriod: 12, slowPeriod: 26, signalPeriod: 9 },
        sma: { period: 20 }
      },
      riskManagement: {
        stopLoss: 2.0,
        takeProfit: 4.0,
        positionSize: 2.0
      },
      rules: [
        "Buy when RSI < oversold and MACD histogram turns positive",
        "Sell when RSI > overbought and MACD histogram turns negative"
      ]
    }
  });

  // Fetch strategies
  const { data: strategies, isLoading, error } = useQuery({
    queryKey: ['strategies'],
    queryFn: async () => {
      try {
        // Add strategies endpoint to API service
        const strategies = await api.getStrategies();
        return strategies;
      } catch (error) {
        console.error('Error fetching strategies:', error);
        throw error;
      }
    }
  });

  // Create strategy mutation
  const createStrategyMutation = useMutation({
    mutationFn: async (strategyData: typeof newStrategy) => {
      try {
        // Add create strategy endpoint to API service
        const result = await api.createStrategy(strategyData);
        return result;
      } catch (error) {
        console.error('Error creating strategy:', error);
        throw error;
      }
    },
    onSuccess: () => {
      toast.success('Strategy created successfully');
      setIsAddDialogOpen(false);
      setNewStrategy({
        name: '',
        description: '',
        config: {
          indicators: {
            rsi: { period: 14, overbought: 70, oversold: 30 },
            macd: { fastPeriod: 12, slowPeriod: 26, signalPeriod: 9 },
            sma: { period: 20 }
          },
          riskManagement: {
            stopLoss: 2.0,
            takeProfit: 4.0,
            positionSize: 2.0
          },
          rules: [
            "Buy when RSI < oversold and MACD histogram turns positive",
            "Sell when RSI > overbought and MACD histogram turns negative"
          ]
        }
      });
      queryClient.invalidateQueries({ queryKey: ['strategies'] });
    },
    onError: (error: any) => {
      toast.error(`Failed to create strategy: ${error.message}`);
    }
  });

  // Update strategy mutation
  const updateStrategyMutation = useMutation({
    mutationFn: async (strategyData: Strategy) => {
      try {
        // Add update strategy endpoint to API service
        const result = await api.updateStrategy(strategyData.id, strategyData);
        return result;
      } catch (error) {
        console.error('Error updating strategy:', error);
        throw error;
      }
    },
    onSuccess: () => {
      toast.success('Strategy updated successfully');
      setIsEditDialogOpen(false);
      setSelectedStrategy(null);
      queryClient.invalidateQueries({ queryKey: ['strategies'] });
    },
    onError: (error: any) => {
      toast.error(`Failed to update strategy: ${error.message}`);
    }
  });

  // Delete strategy mutation
  const deleteStrategyMutation = useMutation({
    mutationFn: async (strategyId: string) => {
      try {
        // Add delete strategy endpoint to API service
        const result = await api.deleteStrategy(strategyId);
        return result;
      } catch (error) {
        console.error('Error deleting strategy:', error);
        throw error;
      }
    },
    onSuccess: () => {
      toast.success('Strategy deleted successfully');
      queryClient.invalidateQueries({ queryKey: ['strategies'] });
    },
    onError: (error: any) => {
      toast.error(`Failed to delete strategy: ${error.message}`);
    }
  });

  const handleCreateStrategy = (e: React.FormEvent) => {
    e.preventDefault();
    createStrategyMutation.mutate(newStrategy);
  };

  const handleUpdateStrategy = (e: React.FormEvent) => {
    e.preventDefault();
    if (selectedStrategy) {
      updateStrategyMutation.mutate(selectedStrategy);
    }
  };

  const handleDeleteStrategy = (strategyId: string) => {
    if (confirm('Are you sure you want to delete this strategy?')) {
      deleteStrategyMutation.mutate(strategyId);
    }
  };

  const handleEditStrategy = (strategy: Strategy) => {
    setSelectedStrategy(strategy);
    setIsEditDialogOpen(true);
  };

  return (
    <div className="container mx-auto py-8">
      <PageHeader
        title="Trading Strategies"
        description="Manage your trading strategies"
        actions={
          <Button onClick={() => setIsAddDialogOpen(true)}>
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Strategy
          </Button>
        }
      />

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <Spinner size="lg" />
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Error!</strong>
          <span className="block sm:inline"> Failed to load strategies.</span>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-6">
          {strategies && strategies.length > 0 ? (
            strategies.map((strategy: Strategy) => (
              <Card key={strategy.id} className="p-6">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-lg font-semibold">{strategy.name}</h3>
                    <p className="text-sm text-gray-500 mt-1">{strategy.description}</p>
                    
                    <div className="mt-4">
                      <h4 className="text-sm font-medium">Indicators:</h4>
                      <div className="flex flex-wrap gap-2 mt-1">
                        {strategy.config.indicators.rsi && (
                          <Badge variant="outline">RSI ({strategy.config.indicators.rsi.period})</Badge>
                        )}
                        {strategy.config.indicators.macd && (
                          <Badge variant="outline">MACD</Badge>
                        )}
                        {strategy.config.indicators.sma && (
                          <Badge variant="outline">SMA ({strategy.config.indicators.sma.period})</Badge>
                        )}
                      </div>
                    </div>
                    
                    <div className="mt-2">
                      <h4 className="text-sm font-medium">Risk Management:</h4>
                      <div className="grid grid-cols-3 gap-2 mt-1 text-xs text-gray-600">
                        <div>SL: {strategy.config.riskManagement.stopLoss}%</div>
                        <div>TP: {strategy.config.riskManagement.takeProfit}%</div>
                        <div>Size: {strategy.config.riskManagement.positionSize}%</div>
                      </div>
                    </div>
                    
                    <p className="text-xs text-gray-400 mt-4">
                      Created: {new Date(strategy.created_at).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="flex flex-col space-y-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleEditStrategy(strategy)}
                      className="text-blue-500 hover:text-blue-700 hover:bg-blue-50"
                    >
                      <PencilIcon className="h-5 w-5" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleDeleteStrategy(strategy.id)}
                      className="text-red-500 hover:text-red-700 hover:bg-red-50"
                    >
                      <TrashIcon className="h-5 w-5" />
                    </Button>
                  </div>
                </div>
              </Card>
            ))
          ) : (
            <div className="col-span-full text-center py-12 bg-gray-50 rounded-lg">
              <p className="text-gray-500">No strategies found. Create your first strategy to get started.</p>
            </div>
          )}
        </div>
      )}

      {/* Add Strategy Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Add Strategy</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleCreateStrategy}>
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Strategy Name</Label>
                  <Input
                    id="name"
                    placeholder="My Trading Strategy"
                    value={newStrategy.name}
                    onChange={(e) => setNewStrategy({ ...newStrategy, name: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="A brief description of your strategy"
                    value={newStrategy.description}
                    onChange={(e) => setNewStrategy({ ...newStrategy, description: e.target.value })}
                    rows={3}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>Risk Management</Label>
                  <div className="grid grid-cols-3 gap-2">
                    <div>
                      <Label htmlFor="stopLoss" className="text-xs">Stop Loss (%)</Label>
                      <Input
                        id="stopLoss"
                        type="number"
                        step="0.1"
                        min="0.1"
                        value={newStrategy.config.riskManagement.stopLoss}
                        onChange={(e) => setNewStrategy({
                          ...newStrategy,
                          config: {
                            ...newStrategy.config,
                            riskManagement: {
                              ...newStrategy.config.riskManagement,
                              stopLoss: parseFloat(e.target.value)
                            }
                          }
                        })}
                      />
                    </div>
                    <div>
                      <Label htmlFor="takeProfit" className="text-xs">Take Profit (%)</Label>
                      <Input
                        id="takeProfit"
                        type="number"
                        step="0.1"
                        min="0.1"
                        value={newStrategy.config.riskManagement.takeProfit}
                        onChange={(e) => setNewStrategy({
                          ...newStrategy,
                          config: {
                            ...newStrategy.config,
                            riskManagement: {
                              ...newStrategy.config.riskManagement,
                              takeProfit: parseFloat(e.target.value)
                            }
                          }
                        })}
                      />
                    </div>
                    <div>
                      <Label htmlFor="positionSize" className="text-xs">Position Size (%)</Label>
                      <Input
                        id="positionSize"
                        type="number"
                        step="0.1"
                        min="0.1"
                        max="100"
                        value={newStrategy.config.riskManagement.positionSize}
                        onChange={(e) => setNewStrategy({
                          ...newStrategy,
                          config: {
                            ...newStrategy.config,
                            riskManagement: {
                              ...newStrategy.config.riskManagement,
                              positionSize: parseFloat(e.target.value)
                            }
                          }
                        })}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsAddDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={createStrategyMutation.isPending}
              >
                {createStrategyMutation.isPending ? (
                  <>
                    <Spinner size="sm" className="mr-2" />
                    Creating...
                  </>
                ) : (
                  'Add Strategy'
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Edit Strategy Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Strategy</DialogTitle>
          </DialogHeader>
          {selectedStrategy && (
            <form onSubmit={handleUpdateStrategy}>
              <div className="space-y-4 py-4">
                <div className="grid grid-cols-1 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="edit-name">Strategy Name</Label>
                    <Input
                      id="edit-name"
                      placeholder="My Trading Strategy"
                      value={selectedStrategy.name}
                      onChange={(e) => setSelectedStrategy({ ...selectedStrategy, name: e.target.value })}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-description">Description</Label>
                    <Textarea
                      id="edit-description"
                      placeholder="A brief description of your strategy"
                      value={selectedStrategy.description}
                      onChange={(e) => setSelectedStrategy({ ...selectedStrategy, description: e.target.value })}
                      rows={3}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Risk Management</Label>
                    <div className="grid grid-cols-3 gap-2">
                      <div>
                        <Label htmlFor="edit-stopLoss" className="text-xs">Stop Loss (%)</Label>
                        <Input
                          id="edit-stopLoss"
                          type="number"
                          step="0.1"
                          min="0.1"
                          value={selectedStrategy.config.riskManagement.stopLoss}
                          onChange={(e) => setSelectedStrategy({
                            ...selectedStrategy,
                            config: {
                              ...selectedStrategy.config,
                              riskManagement: {
                                ...selectedStrategy.config.riskManagement,
                                stopLoss: parseFloat(e.target.value)
                              }
                            }
                          })}
                        />
                      </div>
                      <div>
                        <Label htmlFor="edit-takeProfit" className="text-xs">Take Profit (%)</Label>
                        <Input
                          id="edit-takeProfit"
                          type="number"
                          step="0.1"
                          min="0.1"
                          value={selectedStrategy.config.riskManagement.takeProfit}
                          onChange={(e) => setSelectedStrategy({
                            ...selectedStrategy,
                            config: {
                              ...selectedStrategy.config,
                              riskManagement: {
                                ...selectedStrategy.config.riskManagement,
                                takeProfit: parseFloat(e.target.value)
                              }
                            }
                          })}
                        />
                      </div>
                      <div>
                        <Label htmlFor="edit-positionSize" className="text-xs">Position Size (%)</Label>
                        <Input
                          id="edit-positionSize"
                          type="number"
                          step="0.1"
                          min="0.1"
                          max="100"
                          value={selectedStrategy.config.riskManagement.positionSize}
                          onChange={(e) => setSelectedStrategy({
                            ...selectedStrategy,
                            config: {
                              ...selectedStrategy.config,
                              riskManagement: {
                                ...selectedStrategy.config.riskManagement,
                                positionSize: parseFloat(e.target.value)
                              }
                            }
                          })}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsEditDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={updateStrategyMutation.isPending}
                >
                  {updateStrategyMutation.isPending ? (
                    <>
                      <Spinner size="sm" className="mr-2" />
                      Updating...
                    </>
                  ) : (
                    'Update Strategy'
                  )}
                </Button>
              </DialogFooter>
            </form>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}