/**
 * Shared testing utilities
 */

export * from './mock-helpers';

// Test fixtures
export { TEST_FIXTURES, default as TestFixtures } from './fixtures';

// Mock implementations
export { PythonEngineMock } from './python-engine-mock';

// Integration test utilities
export {
  setupIntegrationTest,
  waitForEvent,
  waitForEvents,
  TimeSimulator,
  TestDataBuilder,
  PerformanceTestUtils,
  createMockLogger,
  MockHttpServer,
  type IntegrationTestConfig,
  type IntegrationTestContext,
} from './integration-test-utils';

// Test assertion helpers
export const assertServiceResponse = {
  success: <T>(response: { success: boolean; data?: T; error?: any }): asserts response is { success: true; data: T } => {
    expect(response.success).toBe(true);
    expect(response.data).toBeDefined();
    expect(response.error).toBeUndefined();
  },

  failure: (response: { success: boolean; data?: any; error?: any }): asserts response is { success: false; error: any } => {
    expect(response.success).toBe(false);
    expect(response.error).toBeDefined();
    expect(response.error.code).toBeDefined();
    expect(response.error.message).toBeDefined();
  },

  validationError: (response: { success: boolean; data?: any; error?: any }): void => {
    assertServiceResponse.failure(response);
    expect(['VALIDATION_ERROR', 'INVALID_ORDER', 'INVALID_BACKTEST_CONFIG', 'INVALID_MESSAGE'])
      .toContain(response.error.code);
  },
};

// Test data validation helpers
export const validateTestData = {
  orderRequest: (data: any): boolean => {
    return (
      typeof data.symbol === 'string' &&
      typeof data.volume === 'number' &&
      typeof data.order_type === 'string' &&
      typeof data.price === 'number' &&
      data.volume > 0 &&
      data.price > 0 &&
      ['buy', 'sell'].includes(data.order_type)
    );
  },

  backtestConfig: (data: any): boolean => {
    return (
      typeof data.name === 'string' &&
      Array.isArray(data.symbols) &&
      data.symbols.length > 0 &&
      data.start_date instanceof Date &&
      data.end_date instanceof Date &&
      data.start_date < data.end_date &&
      typeof data.initial_balance === 'number' &&
      data.initial_balance > 0
    );
  },

  chatMessage: (data: any): boolean => {
    return (
      typeof data.content === 'string' &&
      data.content.trim().length > 0 &&
      ['user', 'assistant'].includes(data.role) &&
      data.created_at instanceof Date
    );
  },
};

// Mock data generators
export const generateMockData = {
  randomOrderRequest: () => ({
    symbol: ['EURUSD', 'GBPUSD', 'USDJPY'][Math.floor(Math.random() * 3)],
    volume: Math.round((Math.random() * 0.1 + 0.01) * 100) / 100,
    order_type: Math.random() > 0.5 ? 'buy' : 'sell',
    price: Math.round((Math.random() * 0.5 + 1.0) * 10000) / 10000,
  }),

  randomBacktestConfig: () => ({
    name: `Test Strategy ${Math.floor(Math.random() * 1000)}`,
    symbols: ['EURUSD'],
    start_date: new Date('2024-01-01'),
    end_date: new Date('2024-06-30'),
    initial_balance: Math.floor(Math.random() * 50000) + 10000,
    strategy: {
      name: 'random_strategy',
      parameters: {},
    },
    risk_management: {
      max_risk_per_trade: Math.round((Math.random() * 0.05 + 0.01) * 100) / 100,
      max_concurrent_trades: Math.floor(Math.random() * 5) + 1,
    },
  }),

  randomChatMessage: () => ({
    content: [
      'What is the current market trend?',
      'Should I buy or sell now?',
      'Analyze my trading performance',
      'What are the best trading strategies?',
    ][Math.floor(Math.random() * 4)],
    role: 'user',
    type: 'text',
    created_at: new Date(),
  }),
};

// Test environment helpers
export const testEnvironment = {
  isCI: () => process.env.CI === 'true',
  isDebug: () => process.env.DEBUG === 'true',
  getTestTimeout: () => parseInt(process.env.TEST_TIMEOUT || '5000', 10),
  
  skipIfCI: (testFn: () => void | Promise<void>) => {
    return testEnvironment.isCI() ? testFn : jest.fn();
  },
  
  onlyInDebug: (testFn: () => void | Promise<void>) => {
    return testEnvironment.isDebug() ? testFn : jest.fn();
  },
};

// Test timing utilities
export const timing = {
  delay: (ms: number): Promise<void> => new Promise(resolve => setTimeout(resolve, ms)),
  
  timeout: <T>(promise: Promise<T>, ms: number): Promise<T> => {
    return Promise.race([
      promise,
      new Promise<never>((_, reject) => 
        setTimeout(() => reject(new Error(`Timeout after ${ms}ms`)), ms)
      ),
    ]);
  },
  
  retry: async <T>(
    operation: () => Promise<T>,
    maxAttempts: number = 3,
    delayMs: number = 100
  ): Promise<T> => {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        if (attempt < maxAttempts) {
          await timing.delay(delayMs * attempt);
        }
      }
    }
    
    throw lastError!;
  },
};

// Schema validation helpers for testing
export const schemaValidation = {
  expectValidSchema: <T>(schema: any, data: any): T => {
    const result = schema.safeParse(data);
    expect(result.success).toBe(true);
    if (result.success) {
      return result.data;
    }
    throw new Error(`Schema validation failed: ${result.error.message}`);
  },
  
  expectInvalidSchema: (schema: any, data: any, expectedErrorPath?: string): void => {
    const result = schema.safeParse(data);
    expect(result.success).toBe(false);
    if (!result.success && expectedErrorPath) {
      expect(result.error.errors.some(e => e.path.join('.').includes(expectedErrorPath))).toBe(true);
    }
  },
};

// Export types for TypeScript
export type MockLogger = ReturnType<typeof createMockLogger>;
export type TestFixture = typeof TEST_FIXTURES;
export type ServiceResponse<T> = { success: true; data: T } | { success: false; error: any };
export * from './test-database';
export * from './api-helpers';
export * from './assertion-helpers';