"""
Enhanced Strategy Chatbot with Ollama Integration
Provides intelligent trading strategy generation using local LLM models
"""

import asyncio
import logging
import re
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

from .ollama_client import OllamaClient, OllamaConfig, ChatMessage
from .optimized_template_manager import OptimizedTemplateManager
from .models import ChatbotResponse, StrategyRequest, GeneratedStrategy

logger = logging.getLogger(__name__)

class EnhancedStrategyChatbot:
    """
    Enhanced chatbot with Ollama LLM integration for intelligent strategy generation
    Combines template-based responses with AI-powered natural language understanding
    """
    
    def __init__(self, 
                 ollama_config: Optional[OllamaConfig] = None,
                 template_manager: Optional[OptimizedTemplateManager] = None,
                 fallback_to_templates: bool = True):
        
        self.ollama_config = ollama_config or OllamaConfig()
        self.template_manager = template_manager or OptimizedTemplateManager()
        self.fallback_to_templates = fallback_to_templates
        
        self.ollama_client: Optional[OllamaClient] = None
        self.ollama_available = False
        
        # Conversation context
        self.active_conversations: Dict[str, List[ChatMessage]] = {}
        
        # Strategy extraction patterns
        self.strategy_patterns = {
            'symbols': r'\b([A-Z]{6}|[A-Z]{3}USD|USD[A-Z]{3}|[A-Z]{3}JPY|JPY[A-Z]{3})\b',
            'timeframes': r'\b(M1|M5|M15|M30|H1|H4|D1|W1|MN1|1min|5min|15min|30min|1hour|4hour|daily|weekly|monthly)\b',
            'indicators': r'\b(RSI|MACD|EMA|SMA|Bollinger|Stochastic|ATR|ADX|CCI|Williams|Fibonacci)\b',
            'strategy_types': r'\b(mean reversion|momentum|breakout|grid|scalping|swing|trend following|arbitrage|pairs trading)\b',
            'risk_percent': r'\b(\d+\.?\d*)\s*%\s*(risk|per trade|position size)\b'
        }
        
        # Quick templates for fallback
        self.quick_templates = {
            'rsi': 'mean_reversion_rsi',
            'macd': 'momentum_macd', 
            'machine learning': 'machine_learning',
            'ml': 'machine_learning',
            'ai': 'machine_learning',
            'twin range': 'twin_range_filter',
            'range filter': 'twin_range_filter',
            'breakout': 'twin_range_filter'
        }
    
    async def initialize(self):
        """Initialize the chatbot and check Ollama availability"""
        try:
            self.ollama_client = OllamaClient(self.ollama_config)
            await self.ollama_client.connect()
            
            # Test with a simple message
            test_response = await self.ollama_client.simple_chat("Hello, are you working?")
            if test_response:
                self.ollama_available = True
                logger.info("✅ Ollama integration active")
                return True
                
        except Exception as e:
            logger.warning(f"⚠️ Ollama not available, falling back to templates: {e}")
            self.ollama_available = False
            
            if not self.fallback_to_templates:
                raise ConnectionError("Ollama required but not available")
        
        return self.ollama_available
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.ollama_client:
            await self.ollama_client.disconnect()
    
    def extract_strategy_info(self, text: str) -> Dict[str, Any]:
        """Extract structured information from user text"""
        info = {
            'symbols': [],
            'timeframes': [],
            'indicators': [],
            'strategy_types': [],
            'risk_percent': None,
            'original_text': text
        }
        
        text_lower = text.lower()
        
        for key, pattern in self.strategy_patterns.items():
            matches = re.findall(pattern, text_lower, re.IGNORECASE)
            if key == 'risk_percent' and matches:
                try:
                    info[key] = float(matches[0])
                except ValueError:
                    pass
            else:
                info[key] = list(set(matches))  # Remove duplicates
        
        return info
    
    def get_fallback_template(self, user_input: str) -> Optional[str]:
        """Get appropriate template ID based on user input"""
        user_lower = user_input.lower()
        
        for keyword, template_id in self.quick_templates.items():
            if keyword in user_lower:
                return template_id
        
        # Default fallback
        if any(word in user_lower for word in ['strategy', 'trading', 'forex']):
            return 'mean_reversion_rsi'  # Beginner-friendly default
            
        return None
    
    async def generate_strategy_response(self, 
                                       user_input: str,
                                       conversation_id: str = "default") -> Dict[str, Any]:
        """
        Generate intelligent strategy response using Ollama or templates
        """
        
        # Extract basic info for context
        strategy_info = self.extract_strategy_info(user_input)
        
        # Try Ollama first if available
        if self.ollama_available and self.ollama_client:
            try:
                return await self._generate_with_ollama(user_input, strategy_info, conversation_id)
            except Exception as e:
                logger.error(f"Ollama generation failed: {e}")
                if not self.fallback_to_templates:
                    raise
        
        # Fallback to template-based generation
        return await self._generate_with_templates(user_input, strategy_info)
    
    async def _generate_with_ollama(self, 
                                  user_input: str, 
                                  strategy_info: Dict[str, Any],
                                  conversation_id: str) -> Dict[str, Any]:
        """Generate response using Ollama LLM"""
        
        # Enhance prompt with extracted information
        enhanced_prompt = self._create_enhanced_prompt(user_input, strategy_info)
        
        # Get conversation history
        if conversation_id not in self.active_conversations:
            self.active_conversations[conversation_id] = []
        
        conversation = self.active_conversations[conversation_id]
        
        # Add current message
        user_message = ChatMessage(role="user", content=enhanced_prompt)
        conversation.append(user_message)
        
        # Keep conversation manageable
        if len(conversation) > 10:
            conversation = conversation[-10:]
            self.active_conversations[conversation_id] = conversation
        
        # Get response from Ollama
        response_content = ""
        async for response in self.ollama_client.chat_completion(conversation):
            response_content = response.content
            if response.done:
                break
        
        # Add assistant response to conversation
        assistant_message = ChatMessage(role="assistant", content=response_content)
        conversation.append(assistant_message)
        
        # Parse the response to extract code and explanation
        parsed_response = self._parse_ollama_response(response_content, strategy_info)
        
        return {
            'message': parsed_response['explanation'],
            'code': parsed_response['code'],
            'strategy_type': parsed_response['strategy_type'],
            'confidence': 0.9,  # High confidence for LLM responses
            'source': 'ollama',
            'model': self.ollama_config.model,
            'strategy_info': strategy_info
        }
    
    def _create_enhanced_prompt(self, user_input: str, strategy_info: Dict[str, Any]) -> str:
        """Create an enhanced prompt with extracted context"""
        
        context_parts = []
        
        if strategy_info['symbols']:
            context_parts.append(f"Symbols: {', '.join(strategy_info['symbols'])}")
        
        if strategy_info['timeframes']:
            context_parts.append(f"Timeframes: {', '.join(strategy_info['timeframes'])}")
            
        if strategy_info['indicators']:
            context_parts.append(f"Indicators: {', '.join(strategy_info['indicators'])}")
            
        if strategy_info['strategy_types']:
            context_parts.append(f"Strategy Types: {', '.join(strategy_info['strategy_types'])}")
            
        if strategy_info['risk_percent']:
            context_parts.append(f"Risk per trade: {strategy_info['risk_percent']}%")
        
        context_str = "\n".join(context_parts) if context_parts else ""
        
        enhanced_prompt = f"""
User Request: {user_input}

Extracted Context:
{context_str}

Please generate a complete Python trading strategy that:
1. Addresses the user's specific requirements
2. Includes proper risk management 
3. Uses appropriate technical indicators
4. Has clear entry/exit rules
5. Includes complete, executable code
6. Provides a clear explanation of the strategy logic

Format your response as:
EXPLANATION:
[Clear explanation of the strategy]

CODE:
```python
[Complete Python code for the strategy]
```

STRATEGY_TYPE: [Type of strategy, e.g., "Mean Reversion", "Momentum", etc.]
"""
        
        return enhanced_prompt
    
    def _parse_ollama_response(self, response: str, strategy_info: Dict[str, Any]) -> Dict[str, str]:
        """Parse Ollama response to extract explanation, code, and strategy type"""
        
        # Default values
        explanation = ""
        code = ""
        strategy_type = "Custom Strategy"
        
        # Try to extract sections
        lines = response.split('\n')
        current_section = None
        code_block = False
        
        for line in lines:
            line_clean = line.strip()
            
            if line_clean.startswith('EXPLANATION:'):
                current_section = 'explanation'
                explanation += line_clean.replace('EXPLANATION:', '').strip() + '\n'
            elif line_clean.startswith('CODE:'):
                current_section = 'code'
            elif line_clean.startswith('STRATEGY_TYPE:'):
                strategy_type = line_clean.replace('STRATEGY_TYPE:', '').strip()
                current_section = None
            elif '```python' in line_clean:
                code_block = True
                current_section = 'code'
            elif '```' in line_clean and code_block:
                code_block = False
                current_section = None
            elif current_section == 'explanation' and not code_block:
                explanation += line + '\n'
            elif current_section == 'code' or code_block:
                if not line_clean.startswith('```'):
                    code += line + '\n'
        
        # If parsing failed, use the whole response as explanation
        if not explanation and not code:
            explanation = response
            
            # Try to extract any code blocks
            code_pattern = r'```(?:python)?\s*\n(.*?)\n```'
            code_matches = re.findall(code_pattern, response, re.DOTALL)
            if code_matches:
                code = code_matches[0]
        
        # Clean up
        explanation = explanation.strip()
        code = code.strip()
        
        return {
            'explanation': explanation or "Strategy generated successfully.",
            'code': code,
            'strategy_type': strategy_type
        }
    
    async def _generate_with_templates(self, 
                                     user_input: str, 
                                     strategy_info: Dict[str, Any]) -> Dict[str, Any]:
        """Generate response using template fallback"""
        
        template_id = self.get_fallback_template(user_input)
        
        if template_id:
            try:
                template_code = self.template_manager.get_template_code(template_id)
                metadata = self.template_manager.get_template_metadata(template_id)
                
                # Create explanation
                explanation = f"✅ **{metadata.name} Generated (Template Mode)**\n\n"
                explanation += f"Since Ollama is not available, I've generated a pre-built {metadata.strategy_type.lower()} strategy.\n\n"
                explanation += f"**Strategy Details:**\n"
                explanation += f"- Type: {metadata.strategy_type}\n"
                explanation += f"- Difficulty: {metadata.difficulty}\n"
                explanation += f"- File Size: {metadata.file_size_kb} KB\n"
                explanation += f"- Indicators: {', '.join(metadata.required_indicators)}\n\n"
                explanation += metadata.description
                
                return {
                    'message': explanation,
                    'code': template_code,
                    'strategy_type': metadata.strategy_type,
                    'confidence': 0.7,  # Lower confidence for templates
                    'source': 'template',
                    'template_id': template_id,
                    'strategy_info': strategy_info
                }
                
            except Exception as e:
                logger.error(f"Template generation failed: {e}")
        
        # Ultimate fallback
        return {
            'message': "I'm having trouble generating a strategy right now. Please check that Ollama is running or try a more specific request.",
            'code': "# Strategy generation failed\n# Please try again with a more specific request",
            'strategy_type': "Error",
            'confidence': 0.0,
            'source': 'fallback',
            'strategy_info': strategy_info
        }
    
    async def chat(self, user_input: str, conversation_id: str = "default") -> Dict[str, Any]:
        """Main chat interface"""
        
        try:
            # Handle help/greeting requests
            if any(word in user_input.lower() for word in ['help', 'hello', 'hi', 'what can you do']):
                return await self._generate_help_response()
            
            # Generate strategy response
            return await self.generate_strategy_response(user_input, conversation_id)
            
        except Exception as e:
            logger.error(f"Chat error: {e}")
            return {
                'message': f"Sorry, I encountered an error: {str(e)}",
                'code': "",
                'strategy_type': "Error",
                'confidence': 0.0,
                'source': 'error',
                'strategy_info': {}
            }
    
    async def _generate_help_response(self) -> Dict[str, Any]:
        """Generate help/introduction response"""
        
        model_info = f" using {self.ollama_config.model}" if self.ollama_available else " (template mode)"
        
        help_message = f"""
🤖 **AI Trading Strategy Assistant{model_info}**

I can help you create sophisticated trading strategies from natural language descriptions!

**What I can do:**
- Generate complete Python trading strategies
- Explain strategy logic and risk management
- Create code for multiple strategy types
- Provide backtesting frameworks
- Answer trading and technical analysis questions

**Strategy Types I Support:**
- 📈 **Mean Reversion** (RSI, Bollinger Bands)
- 🚀 **Momentum** (MACD, Moving Averages)  
- 🧠 **Machine Learning** (Random Forest, Neural Networks)
- ⚡ **Breakout** (Support/Resistance, Volatility)
- 🔄 **Grid Trading** (Range-bound markets)
- 🔗 **Pairs Trading** (Statistical arbitrage)

**Example Requests:**
- "Create a mean reversion strategy for EUR/USD using RSI"
- "Build a momentum strategy with MACD for multiple pairs"
- "Generate a machine learning strategy with multiple indicators"
- "I want a scalping strategy for 5-minute charts"

**Just describe what you want in plain English!**
        """
        
        return {
            'message': help_message.strip(),
            'code': "",
            'strategy_type': "Help",
            'confidence': 1.0,
            'source': 'help',
            'strategy_info': {}
        }
    
    def clear_conversation(self, conversation_id: str = "default"):
        """Clear conversation history"""
        if conversation_id in self.active_conversations:
            del self.active_conversations[conversation_id]
    
    async def get_ollama_status(self) -> Dict[str, Any]:
        """Get Ollama connection status and model info"""
        
        if not self.ollama_available or not self.ollama_client:
            return {
                'available': False,
                'model': None,
                'models': [],
                'message': 'Ollama not connected'
            }
        
        try:
            available_models = await self.ollama_client.get_available_models()
            
            return {
                'available': True,
                'model': self.ollama_config.model,
                'models': available_models,
                'base_url': self.ollama_config.base_url,
                'message': f'Connected to {self.ollama_config.model}'
            }
            
        except Exception as e:
            return {
                'available': False,
                'model': self.ollama_config.model,
                'models': [],
                'error': str(e),
                'message': f'Connection error: {e}'
            }
