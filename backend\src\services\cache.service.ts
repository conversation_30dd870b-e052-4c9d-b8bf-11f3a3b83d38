import { createClient, RedisClientType } from 'redis';
import { MarketDataService } from './market-data.service';

export interface CacheConfig {
  host: string;
  port: number;
  password?: string;
  database?: number;
}

export interface CacheStats {
  hits: number;
  misses: number;
  hitRate: number;
}

export class CacheService {
  private client: RedisClientType;
  private stats: { hits: number; misses: number } = { hits: 0, misses: 0 };

  // Cache TTL constants (in seconds)
  private readonly TTL = {
    MARKET_DATA: 3600,        // 1 hour
    TECHNICAL_INDICATORS: 1800, // 30 minutes
    BACKTEST_RESULTS: 86400,   // 24 hours
    USER_SESSION: 7200,        // 2 hours
    API_RESPONSE: 300,         // 5 minutes
  };

  constructor(config: CacheConfig) {
    this.client = createClient({
      socket: {
        host: config.host,
        port: config.port,
      },
      password: config.password,
      database: config.database || 0,
    });

    // Handle Redis errors
    this.client.on('error', (err) => {
      console.error('Redis Client Error:', err);
    });

    this.client.on('connect', () => {
      console.log('Connected to Redis');
    });

    this.client.on('disconnect', () => {
      console.log('Disconnected from Redis');
    });
  }

  async connect(): Promise<void> {
    try {
      await this.client.connect();
    } catch (error) {
      console.error('Failed to connect to Redis:', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    try {
      await this.client.disconnect();
    } catch (error) {
      console.error('Failed to disconnect from Redis:', error);
      throw error;
    }
  }

  async isHealthy(): Promise<boolean> {
    try {
      const response = await this.client.ping();
      return response === 'PONG';
    } catch (error) {
      console.error('Redis health check failed:', error);
      return false;
    }
  }

  // Basic cache operations
  async get(key: string): Promise<string | null> {
    try {
      const value = await this.client.get(key);
      if (value !== null) {
        this.stats.hits++;
      } else {
        this.stats.misses++;
      }
      return value;
    } catch (error) {
      console.error(`Failed to get key ${key}:`, error);
      throw error;
    }
  }

  async set(key: string, value: string, ttl?: number): Promise<void> {
    try {
      if (ttl) {
        await this.client.set(key, value, { EX: ttl });
      } else {
        await this.client.set(key, value);
      }
    } catch (error) {
      console.error(`Failed to set key ${key}:`, error);
      throw error;
    }
  }

  async getJSON<T>(key: string): Promise<T | null> {
    try {
      const value = await this.get(key);
      if (value === null) {
        return null;
      }
      
      try {
        return JSON.parse(value) as T;
      } catch (parseError) {
        console.error(`Failed to parse JSON for key ${key}:`, parseError);
        return null;
      }
    } catch (error) {
      console.error(`Failed to get JSON for key ${key}:`, error);
      throw error;
    }
  }

  async setJSON<T>(key: string, value: T, ttl?: number): Promise<void> {
    try {
      const serialized = JSON.stringify(value);
      await this.set(key, serialized, ttl);
    } catch (error) {
      console.error(`Failed to set JSON for key ${key}:`, error);
      throw error;
    }
  }

  async delete(key: string): Promise<boolean> {
    try {
      const result = await this.client.del(key);
      return result > 0;
    } catch (error) {
      console.error(`Failed to delete key ${key}:`, error);
      throw error;
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.client.exists(key);
      return result > 0;
    } catch (error) {
      console.error(`Failed to check existence of key ${key}:`, error);
      throw error;
    }
  }

  async expire(key: string, ttl: number): Promise<boolean> {
    try {
      const result = await this.client.expire(key, ttl);
      return result;
    } catch (error) {
      console.error(`Failed to set expiration for key ${key}:`, error);
      throw error;
    }
  }

  // Market data caching
  async cacheMarketData(symbol: string, timeframe: string, data: any[]): Promise<void> {
    const key = `market_data:${symbol}:${timeframe}`;
    await this.setJSON(key, data, this.TTL.MARKET_DATA);
  }

  async getMarketData(symbol: string, timeframe: string): Promise<any[] | null> {
    const key = `market_data:${symbol}:${timeframe}`;
    return await this.getJSON<any[]>(key);
  }

  // Technical indicators caching
  async cacheTechnicalIndicator(
    symbol: string, 
    indicator: string, 
    period: number, 
    data: number[]
  ): Promise<void> {
    const key = `indicator:${symbol}:${indicator}:${period}`;
    await this.setJSON(key, data, this.TTL.TECHNICAL_INDICATORS);
  }

  async getTechnicalIndicator(
    symbol: string, 
    indicator: string, 
    period: number
  ): Promise<number[] | null> {
    const key = `indicator:${symbol}:${indicator}:${period}`;
    return await this.getJSON<number[]>(key);
  }

  // Backtest results caching
  async cacheBacktestResults(backtestId: string, results: any): Promise<void> {
    const key = `backtest:${backtestId}`;
    await this.setJSON(key, results, this.TTL.BACKTEST_RESULTS);
  }

  async getBacktestResults(backtestId: string): Promise<any | null> {
    const key = `backtest:${backtestId}`;
    return await this.getJSON<any>(key);
  }

  // User session caching
  async cacheUserSession(sessionId: string, sessionData: any): Promise<void> {
    const key = `session:${sessionId}`;
    await this.setJSON(key, sessionData, this.TTL.USER_SESSION);
  }

  async getUserSession(sessionId: string): Promise<any | null> {
    const key = `session:${sessionId}`;
    return await this.getJSON<any>(key);
  }

  async invalidateUserSession(sessionId: string): Promise<boolean> {
    const key = `session:${sessionId}`;
    return await this.delete(key);
  }

  // API response caching
  async cacheApiResponse(endpoint: string, params: string, response: any): Promise<void> {
    const key = `api:${endpoint}:${params}`;
    await this.setJSON(key, response, this.TTL.API_RESPONSE);
  }

  async getApiResponse(endpoint: string, params: string): Promise<any | null> {
    const key = `api:${endpoint}:${params}`;
    return await this.getJSON<any>(key);
  }

  // Cache invalidation
  async invalidateMarketData(symbol?: string): Promise<number> {
    try {
      const pattern = symbol ? `market_data:${symbol}:*` : 'market_data:*';
      const keys = await this.client.keys(pattern);
      
      if (keys.length === 0) {
        return 0;
      }
      
      const result = await this.client.del(...keys);
      return result;
    } catch (error) {
      console.error('Failed to invalidate market data:', error);
      throw error;
    }
  }

  async invalidateTechnicalIndicators(symbol?: string): Promise<number> {
    try {
      const pattern = symbol ? `indicator:${symbol}:*` : 'indicator:*';
      const keys = await this.client.keys(pattern);
      
      if (keys.length === 0) {
        return 0;
      }
      
      const result = await this.client.del(...keys);
      return result;
    } catch (error) {
      console.error('Failed to invalidate technical indicators:', error);
      throw error;
    }
  }

  async invalidateBacktestResults(backtestId?: string): Promise<number> {
    try {
      const pattern = backtestId ? `backtest:${backtestId}` : 'backtest:*';
      const keys = await this.client.keys(pattern);
      
      if (keys.length === 0) {
        return 0;
      }
      
      const result = await this.client.del(...keys);
      return result;
    } catch (error) {
      console.error('Failed to invalidate backtest results:', error);
      throw error;
    }
  }

  async clearAll(): Promise<void> {
    try {
      await this.client.flushAll();
    } catch (error) {
      console.error('Failed to clear all cache:', error);
      throw error;
    }
  }

  // Cache warming
  async warmMarketDataCache(
    symbols: string[], 
    timeframes: string[], 
    marketDataService: MarketDataService
  ): Promise<void> {
    const promises: Promise<void>[] = [];

    for (const symbol of symbols) {
      for (const timeframe of timeframes) {
        const promise = this.warmSingleMarketData(symbol, timeframe, marketDataService);
        promises.push(promise);
      }
    }

    try {
      await Promise.all(promises);
      console.log(`Warmed cache for ${symbols.length} symbols and ${timeframes.length} timeframes`);
    } catch (error) {
      console.error('Failed to warm market data cache:', error);
      throw error;
    }
  }

  private async warmSingleMarketData(
    symbol: string, 
    timeframe: string, 
    marketDataService: MarketDataService
  ): Promise<void> {
    try {
      // Check if data is already cached
      const cached = await this.getMarketData(symbol, timeframe);
      if (cached) {
        return; // Already cached
      }

      // Fetch fresh data (last 30 days)
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 30);

      const data = await marketDataService.fetchHistoricalData([symbol as any], startDate, endDate);
      
      if (data && data.length > 0) {
        await this.cacheMarketData(symbol, timeframe, data);
      }
    } catch (error) {
      console.error(`Failed to warm cache for ${symbol}:${timeframe}:`, error);
      // Don't throw - continue warming other data
    }
  }

  // Cache statistics
  getStats(): CacheStats {
    const total = this.stats.hits + this.stats.misses;
    const hitRate = total > 0 ? this.stats.hits / total : 0;
    
    return {
      hits: this.stats.hits,
      misses: this.stats.misses,
      hitRate: Math.round(hitRate * 100) / 100, // Round to 2 decimal places
    };
  }

  resetStats(): void {
    this.stats = { hits: 0, misses: 0 };
  }

  // Utility methods
  async getKeysByPattern(pattern: string): Promise<string[]> {
    try {
      return await this.client.keys(pattern);
    } catch (error) {
      console.error(`Failed to get keys by pattern ${pattern}:`, error);
      throw error;
    }
  }

  async getMemoryUsage(): Promise<any> {
    try {
      const info = await this.client.info('memory');
      return this.parseRedisInfo(info);
    } catch (error) {
      console.error('Failed to get memory usage:', error);
      throw error;
    }
  }

  private parseRedisInfo(info: string): Record<string, string> {
    const result: Record<string, string> = {};
    const lines = info.split('\r\n');
    
    for (const line of lines) {
      if (line.includes(':')) {
        const [key, value] = line.split(':');
        result[key] = value;
      }
    }
    
    return result;
  }

  // Batch operations
  async mget(keys: string[]): Promise<(string | null)[]> {
    try {
      return await this.client.mGet(keys);
    } catch (error) {
      console.error('Failed to execute mget:', error);
      throw error;
    }
  }

  async mset(keyValuePairs: Record<string, string>): Promise<void> {
    try {
      await this.client.mSet(keyValuePairs);
    } catch (error) {
      console.error('Failed to execute mset:', error);
      throw error;
    }
  }

  // Lock mechanism for preventing cache stampede
  async acquireLock(lockKey: string, ttl: number = 30): Promise<boolean> {
    try {
      const result = await this.client.set(lockKey, '1', { NX: true, EX: ttl });
      return result === 'OK';
    } catch (error) {
      console.error(`Failed to acquire lock ${lockKey}:`, error);
      return false;
    }
  }

  async releaseLock(lockKey: string): Promise<boolean> {
    try {
      const result = await this.client.del(lockKey);
      return result > 0;
    } catch (error) {
      console.error(`Failed to release lock ${lockKey}:`, error);
      return false;
    }
  }
}