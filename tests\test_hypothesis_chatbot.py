#!/usr/bin/env python3
"""
Property-Based Testing for Trading Chatbot using Hypothesis

This module contains property-based tests that verify the chatbot's behavior
across a wide range of inputs, ensuring the no-hallucination guarantee holds
under all conditions.
"""

import pytest
import tempfile
import os
from hypothesis import given, strategies as st, settings, assume, example
from hypothesis.stateful import RuleBasedStateMachine, rule, initialize, invariant
from faker import Faker

from src.chatbot.knowledge_base import KnowledgeBase, TradingChatbot, KnowledgeEntry
from datetime import datetime


fake = Faker()


class TestChatbotHypothesis:
    """Property-based tests for chatbot functionality"""
    
    def setup_method(self):
        """Set up test environment"""
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.kb = KnowledgeBase(self.temp_db.name)
        self.chatbot = TradingChatbot(self.kb)
        self.chatbot.add_trading_knowledge()
    
    def teardown_method(self):
        """Clean up test environment"""
        if os.path.exists(self.temp_db.name):
            os.unlink(self.temp_db.name)
    
    @pytest.mark.hypothesis
    @given(query=st.text(min_size=0, max_size=1000))
    @settings(max_examples=200, deadline=5000)
    def test_no_hallucination_property(self, query):
        """
        Property: Every response must either include sources/hashes OR "I don't know"
        This is the core no-hallucination guarantee.
        """
        # Skip null bytes and other problematic characters
        assume('\x00' not in query)
        
        response = self.chatbot.answer(query)
        
        # Core property: must have sources+hashes OR "I don't know"
        has_source = "source" in response.lower() or "Source:" in response
        has_hash = "hash" in response.lower() or "Verification hash:" in response
        has_idk = "i don't know" in response.lower()
        
        # The fundamental property: either verified OR honest
        assert (has_source and has_hash) or has_idk, (
            f"No-hallucination violation!\n"
            f"Query: {repr(query)}\n"
            f"Response: {response}\n"
            f"Has source: {has_source}, Has hash: {has_hash}, Has IDK: {has_idk}"
        )
    
    @pytest.mark.hypothesis
    @given(
        query=st.text(
            alphabet=st.characters(whitelist_categories=('Lu', 'Ll', 'Nd', 'Po', 'Zs')),
            min_size=1,
            max_size=200
        )
    )
    @settings(max_examples=100)
    def test_trading_query_consistency(self, query):
        """
        Property: Trading-related queries should consistently return sources
        when they match known trading concepts.
        """
        trading_terms = ['rsi', 'macd', 'moving average', 'support', 'resistance', 
                        'bollinger', 'stochastic', 'fibonacci', 'trend']
        
        query_lower = query.lower()
        has_trading_term = any(term in query_lower for term in trading_terms)
        
        response = self.chatbot.answer(query)
        
        has_source = "Source:" in response
        has_hash = "hash:" in response.lower()
        has_idk = "i don't know" in response.lower()
        
        # If it has trading terms and we have knowledge, should get sources
        if has_trading_term and not has_idk:
            # If not IDK, must have sources for trading queries
            assert has_source and has_hash, (
                f"Trading query without proper attribution!\n"
                f"Query: {query}\n"
                f"Response: {response}"
            )
    
    @pytest.mark.hypothesis
    @given(
        query=st.one_of(
            st.just(""),
            st.text(alphabet=" \t\n\r", min_size=1, max_size=50),
            st.text(min_size=1, max_size=10).filter(lambda x: x.isspace())
        )
    )
    def test_empty_query_handling(self, query):
        """Property: Empty or whitespace-only queries should return "I don't know" """
        response = self.chatbot.answer(query)
        
        # Empty queries should always return "I don't know"
        assert "i don't know" in response.lower(), (
            f"Empty query should return IDK!\n"
            f"Query: {repr(query)}\n"
            f"Response: {response}"
        )
    
    @pytest.mark.hypothesis
    @given(
        base_query=st.sampled_from([
            "What is RSI?",
            "Tell me about MACD",
            "Show me GBPUSD backtest",
            "What is moving average?"
        ]),
        noise=st.text(
            alphabet=st.characters(whitelist_categories=('Po', 'Sm')),
            min_size=0,
            max_size=20
        )
    )
    def test_noise_resilience(self, base_query, noise):
        """Property: Adding punctuation/symbols shouldn't break responses"""
        noisy_query = base_query + noise
        
        response = self.chatbot.answer(noisy_query)
        
        # Should still follow no-hallucination rules
        has_source = "Source:" in response
        has_hash = "hash:" in response.lower()
        has_idk = "i don't know" in response.lower()
        
        assert (has_source and has_hash) or has_idk, (
            f"Noisy query broke no-hallucination guarantee!\n"
            f"Original: {base_query}\n"
            f"Noisy: {noisy_query}\n"
            f"Response: {response}"
        )
    
    @pytest.mark.hypothesis
    @given(
        case_variant=st.sampled_from([
            lambda x: x.upper(),
            lambda x: x.lower(),
            lambda x: x.title(),
            lambda x: x.swapcase()
        ]),
        query=st.sampled_from([
            "What is RSI?",
            "Tell me about moving averages",
            "Show me GBPUSD backtest"
        ])
    )
    def test_case_insensitivity(self, case_variant, query):
        """Property: Case variations should produce consistent results"""
        original_response = self.chatbot.answer(query)
        variant_response = self.chatbot.answer(case_variant(query))
        
        # Both should follow no-hallucination rules
        for response in [original_response, variant_response]:
            has_source = "Source:" in response
            has_hash = "hash:" in response.lower()
            has_idk = "i don't know" in response.lower()
            
            assert (has_source and has_hash) or has_idk, (
                f"Case variant broke no-hallucination guarantee!\n"
                f"Query: {query}\n"
                f"Variant: {case_variant(query)}\n"
                f"Response: {response}"
            )
    
    @pytest.mark.hypothesis
    @given(
        non_trading_query=st.text(min_size=5, max_size=100).filter(
            lambda x: not any(term in x.lower() for term in [
                'rsi', 'macd', 'trading', 'stock', 'forex', 'backtest',
                'moving', 'average', 'support', 'resistance', 'bollinger',
                'stochastic', 'fibonacci', 'trend', 'gbpusd', 'eurusd'
            ])
        )
    )
    @settings(max_examples=50)
    def test_unknown_domain_queries(self, non_trading_query):
        """Property: Queries outside trading domain should return "I don't know" """
        assume(len(non_trading_query.strip()) > 0)
        
        response = self.chatbot.answer(non_trading_query)
        
        # Non-trading queries should typically return "I don't know"
        has_idk = "i don't know" in response.lower()
        has_source = "Source:" in response
        
        # Either IDK or proper sources (but IDK is more likely for non-trading)
        assert has_idk or has_source, (
            f"Non-trading query should return IDK or have sources!\n"
            f"Query: {non_trading_query}\n"
            f"Response: {response}"
        )


class TestChatbotStateMachine(RuleBasedStateMachine):
    """Stateful property-based testing for chatbot"""
    
    def __init__(self):
        super().__init__()
        self.temp_db = None
        self.kb = None
        self.chatbot = None
        self.query_history = []
        self.response_history = []
    
    @initialize()
    def setup_chatbot(self):
        """Initialize chatbot for stateful testing"""
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.kb = KnowledgeBase(self.temp_db.name)
        self.chatbot = TradingChatbot(self.kb)
        self.chatbot.add_trading_knowledge()
    
    @rule(
        query=st.text(min_size=1, max_size=200).filter(
            lambda x: '\x00' not in x and len(x.strip()) > 0
        )
    )
    def query_chatbot(self, query):
        """Rule: Query the chatbot and verify response"""
        response = self.chatbot.answer(query)
        
        # Store history
        self.query_history.append(query)
        self.response_history.append(response)
        
        # Verify no-hallucination property
        has_source = "Source:" in response
        has_hash = "hash:" in response.lower()
        has_idk = "i don't know" in response.lower()
        
        assert (has_source and has_hash) or has_idk, (
            f"Stateful test violation!\n"
            f"Query: {query}\n"
            f"Response: {response}\n"
            f"Query history length: {len(self.query_history)}"
        )
    
    @rule()
    def query_empty_string(self):
        """Rule: Test empty string queries"""
        response = self.chatbot.answer("")
        assert "i don't know" in response.lower()
    
    @rule(
        trading_query=st.sampled_from([
            "What is RSI?",
            "Tell me about MACD",
            "Show me GBPUSD backtest",
            "What is the best RSI period for EURUSD?"
        ])
    )
    def query_known_trading_concepts(self, trading_query):
        """Rule: Test known trading concepts"""
        response = self.chatbot.answer(trading_query)
        
        # Known trading concepts should have sources
        has_source = "Source:" in response
        has_hash = "hash:" in response.lower()
        has_idk = "i don't know" in response.lower()
        
        # Should either have sources or be IDK (but sources preferred for known concepts)
        assert (has_source and has_hash) or has_idk
    
    @invariant()
    def chatbot_always_responds(self):
        """Invariant: Chatbot should always provide a response"""
        if hasattr(self, 'chatbot') and self.chatbot:
            response = self.chatbot.answer("test")
            assert isinstance(response, str)
            assert len(response) > 0
    
    @invariant()
    def no_memory_leaks(self):
        """Invariant: Check for basic memory management"""
        if len(self.query_history) > 100:
            # Clear old history to prevent memory issues in testing
            self.query_history = self.query_history[-50:]
            self.response_history = self.response_history[-50:]
    
    def teardown(self):
        """Clean up after stateful testing"""
        if self.temp_db and os.path.exists(self.temp_db.name):
            os.unlink(self.temp_db.name)


@pytest.mark.hypothesis
class TestChatbotFuzzing:
    """Fuzzing tests for chatbot robustness"""
    
    def setup_method(self):
        """Set up test environment"""
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.kb = KnowledgeBase(self.temp_db.name)
        self.chatbot = TradingChatbot(self.kb)
        self.chatbot.add_trading_knowledge()
    
    def teardown_method(self):
        """Clean up test environment"""
        if os.path.exists(self.temp_db.name):
            os.unlink(self.temp_db.name)
    
    @pytest.mark.hypothesis
    @given(
        malicious_input=st.one_of(
            st.text(alphabet=st.characters(blacklist_categories=('Cc', 'Cs')), max_size=1000),
            st.binary(max_size=100).map(lambda x: x.decode('utf-8', errors='ignore')),
            st.text().map(lambda x: x * 100)  # Repeated text
        )
    )
    @settings(max_examples=50, deadline=10000)
    def test_malicious_input_handling(self, malicious_input):
        """Test chatbot robustness against malicious inputs"""
        assume(len(malicious_input.strip()) > 0)
        assume('\x00' not in malicious_input)
        
        try:
            response = self.chatbot.answer(malicious_input)
            
            # Even with malicious input, must follow no-hallucination rules
            has_source = "Source:" in response
            has_hash = "hash:" in response.lower()
            has_idk = "i don't know" in response.lower()
            
            assert (has_source and has_hash) or has_idk, (
                f"Malicious input broke no-hallucination guarantee!\n"
                f"Input: {repr(malicious_input[:100])}\n"
                f"Response: {response}"
            )
            
        except Exception as e:
            # Exceptions are acceptable for malicious input, but log them
            pytest.skip(f"Exception with malicious input (acceptable): {e}")
    
    @pytest.mark.hypothesis
    @given(
        unicode_query=st.text(
            alphabet=st.characters(
                whitelist_categories=('Lu', 'Ll', 'Nd', 'Po', 'Zs', 'Sm', 'Sc'),
                min_codepoint=32,
                max_codepoint=1000
            ),
            min_size=1,
            max_size=200
        )
    )
    def test_unicode_handling(self, unicode_query):
        """Test chatbot handling of Unicode characters"""
        response = self.chatbot.answer(unicode_query)
        
        # Should handle Unicode gracefully
        assert isinstance(response, str)
        
        # Should still follow no-hallucination rules
        has_source = "Source:" in response
        has_hash = "hash:" in response.lower()
        has_idk = "i don't know" in response.lower()
        
        assert (has_source and has_hash) or has_idk


# Stateful testing
TestChatbotStateful = TestChatbotStateMachine.TestCase


@pytest.mark.hypothesis
@pytest.mark.benchmark
def test_chatbot_performance_property():
    """Property-based performance testing"""
    temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
    temp_db.close()
    
    try:
        kb = KnowledgeBase(temp_db.name)
        chatbot = TradingChatbot(kb)
        chatbot.add_trading_knowledge()
        
        import time
        
        # Test that responses are reasonably fast
        queries = ["What is RSI?", "Unknown query", "Show me GBPUSD backtest"]
        
        for query in queries:
            start_time = time.time()
            response = chatbot.answer(query)
            end_time = time.time()
            
            # Response should be fast (under 1 second)
            response_time = end_time - start_time
            assert response_time < 1.0, f"Response too slow: {response_time:.3f}s for '{query}'"
            
            # Should still follow no-hallucination rules
            has_source = "Source:" in response
            has_hash = "hash:" in response.lower()
            has_idk = "i don't know" in response.lower()
            
            assert (has_source and has_hash) or has_idk
    
    finally:
        if os.path.exists(temp_db.name):
            os.unlink(temp_db.name)


if __name__ == "__main__":
    # Run hypothesis tests
    pytest.main([__file__, "-v", "--hypothesis-show-statistics"])