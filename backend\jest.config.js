module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src'],
  testMatch: [
    '**/__tests__/**/*.+(ts|tsx|js)',
    '**/?(*.)+(spec|test).+(ts|tsx|js)'
  ],
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest'
  },
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@/shared/test-utils$': '<rootDir>/../shared/test-utils',
    '^@/shared/test-utils/(.*)$': '<rootDir>/../shared/test-utils/$1',
    '^@/shared/schemas$': '<rootDir>/../shared/schemas',
    '^@/shared/schemas/(.*)$': '<rootDir>/../shared/schemas/$1',
    '^@shared/(.*)$': '<rootDir>/../shared/src/$1',
    '^@schemas/(.*)$': '<rootDir>/src/schemas/$1',
    '^@features/(.*)$': '<rootDir>/src/features/$1',
    '^@shared-backend/(.*)$': '<rootDir>/src/shared/$1',
    '^@test-factories/(.*)$': '<rootDir>/src/test-factories/$1'
  },
  setupFilesAfterEnv: ['<rootDir>/src/test-setup.ts'],
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.test.{ts,tsx}',
    '!src/**/*.spec.{ts,tsx}',
    '!src/test-factories/**',
    '!src/test-setup.ts',
    '!src/server.ts'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  testTimeout: 10000,
  verbose: true,
  detectOpenHandles: true,
  forceExit: true
};