"""
AI Trading Platform - Web Interface Preview
Complete platform demonstration with chatbot + Python IDE + MT5 integration
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import time
import json

# Set page config
st.set_page_config(
    page_title="AI Trading Platform",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .feature-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 10px;
        margin: 0.5rem 0;
        border-left: 4px solid #1f77b4;
    }
    .success-message {
        background-color: #d4edda;
        color: #155724;
        padding: 1rem;
        border-radius: 5px;
        border: 1px solid #c3e6cb;
    }
    .strategy-code {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 5px;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
    }
</style>
""", unsafe_allow_html=True)

def main():
    """Main platform interface"""
    
    # Header
    st.markdown('<h1 class="main-header">🚀 AI-Enhanced Trading Platform</h1>', unsafe_allow_html=True)
    st.markdown("### Transform Natural Language into Production-Ready Python Trading Strategies")
    
    # Sidebar navigation
    st.sidebar.title("🧭 Navigation")
    page = st.sidebar.selectbox(
        "Choose a section:",
        [
            "🏠 Dashboard",
            "🤖 Strategy Chatbot", 
            "📊 Strategy Manager",
            "🔗 MT5 Integration",
            "📈 Backtesting",
            "📋 Templates",
            "⚙️ Settings"
        ]
    )
    
    # Route to different pages
    if page == "🏠 Dashboard":
        show_dashboard()
    elif page == "🤖 Strategy Chatbot":
        show_chatbot_interface()
    elif page == "📊 Strategy Manager":
        show_strategy_manager()
    elif page == "🔗 MT5 Integration":
        show_mt5_integration()
    elif page == "📈 Backtesting":
        show_backtesting()
    elif page == "📋 Templates":
        show_templates()
    elif page == "⚙️ Settings":
        show_settings()

def show_dashboard():
    """Platform dashboard"""
    
    st.header("📊 Platform Dashboard")
    
    # Key metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Active Strategies", "12", "+3")
    with col2:
        st.metric("Total P&L", "$2,847", "+$127")
    with col3:
        st.metric("Win Rate", "68%", "+2%")
    with col4:
        st.metric("MT5 Status", "Connected", "")
    
    # Recent activity
    st.subheader("📈 Recent Activity")
    
    activity_data = {
        "Time": ["10:30", "10:15", "09:45", "09:30", "09:15"],
        "Strategy": ["MACD Momentum", "RSI Mean Reversion", "ML Random Forest", "Bollinger Breakout", "Multi-Timeframe"],
        "Action": ["Buy Signal", "Sell Executed", "Model Retrained", "Position Closed", "Buy Executed"],
        "Symbol": ["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "EURUSD"],
        "P&L": ["+$45", "+$78", "N/A", "+$23", "+$67"]
    }
    
    df_activity = pd.DataFrame(activity_data)
    st.dataframe(df_activity, use_container_width=True)
    
    # Performance chart
    st.subheader("📊 Portfolio Performance")
    
    # Generate sample performance data
    dates = pd.date_range(start="2024-01-01", end="2024-01-31", freq="D")
    cumulative_returns = np.cumsum(np.random.normal(0.02, 0.5, len(dates)))
    
    fig = go.Figure()
    fig.add_trace(go.Scatter(
        x=dates,
        y=cumulative_returns,
        mode='lines',
        name='Portfolio P&L',
        line=dict(color='#1f77b4', width=2)
    ))
    
    fig.update_layout(
        title="Cumulative P&L Over Time",
        xaxis_title="Date",
        yaxis_title="P&L ($)",
        height=400
    )
    
    st.plotly_chart(fig, use_container_width=True)

def show_chatbot_interface():
    """Strategy chatbot interface"""
    
    st.header("🤖 Strategy Chatbot")
    st.markdown("Describe your trading strategy in natural language and get complete Python code!")
    
    # Chat interface
    if "messages" not in st.session_state:
        st.session_state.messages = [
            {"role": "assistant", "content": "Hello! I'm your trading strategy assistant. Describe the strategy you'd like to create and I'll generate complete Python code for you. For example: 'Create a mean reversion strategy for EUR/USD using RSI with 2% risk per trade'"}
        ]
    
    # Display chat messages
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])
    
    # Chat input
    if prompt := st.chat_input("Describe your trading strategy..."):
        # Add user message
        st.session_state.messages.append({"role": "user", "content": prompt})
        with st.chat_message("user"):
            st.markdown(prompt)
        
        # Generate response
        with st.chat_message("assistant"):
            with st.spinner("Generating your strategy..."):
                time.sleep(2)  # Simulate processing
                
                response = generate_chatbot_response(prompt)
                st.markdown(response["message"])
                
                if response["code"]:
                    st.subheader("📄 Generated Python Code")
                    st.code(response["code"], language="python")
                    
                    # Action buttons
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        if st.button("💾 Save Strategy"):
                            st.success("Strategy saved successfully!")
                    with col2:
                        if st.button("🧪 Run Tests"):
                            st.success("All tests passed! ✅")
                    with col3:
                        if st.button("📈 Backtest"):
                            st.success("Backtest started!")
        
        # Add assistant response
        st.session_state.messages.append({"role": "assistant", "content": response["message"]})

def generate_chatbot_response(prompt):
    """Generate chatbot response based on user input"""
    
    prompt_lower = prompt.lower()
    
    if "mean reversion" in prompt_lower or "rsi" in prompt_lower:
        return {
            "message": "✅ **Mean Reversion RSI Strategy Generated!**\n\n**Strategy Details:**\n- Type: Mean Reversion\n- Indicator: RSI (14 period)\n- Buy: RSI < 30 (oversold)\n- Sell: RSI > 70 (overbought)\n- Risk: 2% per trade\n- Max Positions: 3\n\n**Features:**\n- Complete Python class with MT5 integration\n- Comprehensive test cases\n- Risk management included\n- Ready for deployment",
            "code": '''class MeanReversionRSIStrategy(StrategyBase):
    """Mean Reversion Strategy using RSI indicator"""
    
    def __init__(self, symbols, mt5_bridge=None, risk_per_trade=0.02):
        super().__init__(
            name="Mean Reversion RSI",
            symbols=symbols,
            mt5_bridge=mt5_bridge,
            risk_per_trade=risk_per_trade
        )
        self.rsi_period = 14
        self.oversold_level = 30
        self.overbought_level = 70
    
    def calculate_rsi(self, prices, period=None):
        """Calculate RSI indicator"""
        from ta.momentum import RSIIndicator
        period = period or self.rsi_period
        rsi_indicator = RSIIndicator(close=prices, window=period)
        return rsi_indicator.rsi()
    
    def generate_signal(self, symbol, data):
        """Generate trading signal based on RSI"""
        rsi = self.calculate_rsi(data['close'])
        current_rsi = rsi.iloc[-1]
        
        if current_rsi < self.oversold_level:
            return {
                "signal": "buy",
                "confidence": 0.8,
                "reason": f"RSI oversold: {current_rsi:.2f}"
            }
        elif current_rsi > self.overbought_level:
            return {
                "signal": "sell",
                "confidence": 0.8,
                "reason": f"RSI overbought: {current_rsi:.2f}"
            }
        else:
            return {
                "signal": "hold",
                "confidence": 0.5,
                "reason": f"RSI neutral: {current_rsi:.2f}"
            }
    
    def execute_trade(self, symbol, signal):
        """Execute trade via MT5 Bridge"""
        if not self.mt5_bridge or signal['signal'] == 'hold':
            return None
        
        position_size = self.calculate_position_size(symbol, signal)
        
        return self.mt5_bridge.place_market_order(
            symbol=symbol,
            order_type=signal['signal'],
            volume=position_size,
            comment=f"RSI Strategy - {signal['reason']}"
        )'''
        }
    
    elif "momentum" in prompt_lower or "macd" in prompt_lower:
        return {
            "message": "✅ **Momentum MACD Strategy Generated!**\n\n**Strategy Details:**\n- Type: Momentum/Trend Following\n- Indicator: MACD (12,26,9)\n- Buy: MACD bullish crossover\n- Sell: MACD bearish crossover\n- Risk: 2% per trade\n- Max Positions: 5\n\n**Features:**\n- Advanced crossover detection\n- MT5 integration included\n- Comprehensive testing\n- Performance optimized",
            "code": '''class MomentumMACDStrategy(StrategyBase):
    """Momentum Strategy using MACD crossovers"""
    
    def __init__(self, symbols, mt5_bridge=None, risk_per_trade=0.02):
        super().__init__(
            name="Momentum MACD",
            symbols=symbols,
            mt5_bridge=mt5_bridge,
            risk_per_trade=risk_per_trade
        )
        self.macd_fast = 12
        self.macd_slow = 26
        self.macd_signal = 9
    
    def calculate_macd(self, prices):
        """Calculate MACD indicator"""
        from ta.trend import MACD
        macd = MACD(close=prices, window_fast=self.macd_fast,
                   window_slow=self.macd_slow, window_sign=self.macd_signal)
        return {
            'macd': macd.macd(),
            'signal': macd.macd_signal(),
            'histogram': macd.macd_diff()
        }
    
    def generate_signal(self, symbol, data):
        """Generate MACD-based trading signal"""
        macd_data = self.calculate_macd(data['close'])
        
        current_macd = macd_data['macd'].iloc[-1]
        current_signal = macd_data['signal'].iloc[-1]
        prev_macd = macd_data['macd'].iloc[-2]
        prev_signal = macd_data['signal'].iloc[-2]
        
        # Bullish crossover
        if current_macd > current_signal and prev_macd <= prev_signal:
            return {
                "signal": "buy",
                "confidence": 0.8,
                "reason": "MACD bullish crossover"
            }
        # Bearish crossover
        elif current_macd < current_signal and prev_macd >= prev_signal:
            return {
                "signal": "sell",
                "confidence": 0.8,
                "reason": "MACD bearish crossover"
            }
        else:
            return {
                "signal": "hold",
                "confidence": 0.5,
                "reason": "No MACD crossover"
            }'''
        }
    
    elif "machine learning" in prompt_lower or "ml" in prompt_lower or "ai" in prompt_lower:
        return {
            "message": "✅ **Machine Learning Strategy Generated!**\n\n**Strategy Details:**\n- Type: AI/Machine Learning\n- Algorithm: Random Forest Classifier\n- Features: RSI, MACD, Price Momentum\n- Training: 1000 bars, retrain every 100\n- Risk: 2% per trade\n\n**Advanced Features:**\n- Feature engineering pipeline\n- Model training and retraining\n- Prediction confidence scoring\n- Scikit-learn integration",
            "code": '''class MLRandomForestStrategy(StrategyBase):
    """Machine Learning Strategy using Random Forest"""
    
    def __init__(self, symbols, mt5_bridge=None, risk_per_trade=0.02):
        super().__init__(
            name="ML Random Forest",
            symbols=symbols,
            mt5_bridge=mt5_bridge,
            risk_per_trade=risk_per_trade
        )
        self.features = ["rsi", "macd", "price_momentum"]
        self.training_bars = 1000
        self.model = None
        self.scaler = StandardScaler()
    
    def prepare_features(self, data):
        """Prepare features for ML model"""
        features = {}
        
        # RSI feature
        rsi_indicator = RSIIndicator(close=data['close'], window=14)
        features['rsi'] = rsi_indicator.rsi().iloc[-1]
        
        # MACD features
        macd = MACD(close=data['close'])
        features['macd'] = macd.macd().iloc[-1]
        features['macd_signal'] = macd.macd_signal().iloc[-1]
        
        # Price momentum
        features['price_momentum'] = (
            data['close'].iloc[-1] / data['close'].iloc[-10] - 1
        ) * 100
        
        return features
    
    def train_model(self, historical_data):
        """Train the ML model"""
        from sklearn.ensemble import RandomForestClassifier
        
        X, y = self._prepare_training_data(historical_data)
        X_scaled = self.scaler.fit_transform(X)
        
        self.model = RandomForestClassifier(n_estimators=100, random_state=42)
        self.model.fit(X_scaled, y)
        
        return True
    
    def generate_signal(self, symbol, data):
        """Generate ML-based trading signal"""
        if self.model is None:
            return {"signal": "hold", "confidence": 0.0}
        
        features = self.prepare_features(data)
        X = self.scaler.transform([list(features.values())])
        
        prediction = self.model.predict(X)[0]
        probabilities = self.model.predict_proba(X)[0]
        confidence = max(probabilities)
        
        if prediction == 1 and confidence > 0.6:
            return {"signal": "buy", "confidence": confidence}
        elif prediction == 0 and confidence > 0.6:
            return {"signal": "sell", "confidence": confidence}
        else:
            return {"signal": "hold", "confidence": confidence}'''
        }
    
    else:
        return {
            "message": "I can help you create various types of trading strategies! Here are some examples:\n\n**Popular Strategy Types:**\n- Mean Reversion (RSI, Bollinger Bands)\n- Momentum (MACD, Moving Averages)\n- Machine Learning (Random Forest, Neural Networks)\n- Breakout (Bollinger Bands, Support/Resistance)\n- Multi-timeframe Analysis\n\n**Example Requests:**\n- 'Create a mean reversion strategy using RSI for EUR/USD'\n- 'Build a momentum strategy with MACD for multiple pairs'\n- 'Generate a machine learning strategy with multiple indicators'\n\nWhat type of strategy would you like to create?",
            "code": None
        }

def show_strategy_manager():
    """Strategy management interface"""
    
    st.header("📊 Strategy Manager")
    
    # Strategy list
    strategies = [
        {"Name": "MACD Momentum", "Status": "🟢 Active", "P&L": "+$234", "Trades": 15, "Win Rate": "73%"},
        {"Name": "RSI Mean Reversion", "Status": "🟢 Active", "P&L": "+$156", "Trades": 12, "Win Rate": "67%"},
        {"Name": "ML Random Forest", "Status": "🟡 Training", "P&L": "+$89", "Trades": 8, "Win Rate": "62%"},
        {"Name": "Bollinger Breakout", "Status": "🔴 Stopped", "P&L": "-$23", "Trades": 5, "Win Rate": "40%"},
        {"Name": "Multi-Timeframe", "Status": "🟢 Active", "P&L": "+$178", "Trades": 10, "Win Rate": "70%"}
    ]
    
    df_strategies = pd.DataFrame(strategies)
    
    # Strategy selection
    selected_strategy = st.selectbox("Select Strategy to Manage:", df_strategies["Name"].tolist())
    
    # Strategy details
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.subheader(f"📈 {selected_strategy} Details")
        st.dataframe(df_strategies, use_container_width=True)
        
        # Performance chart for selected strategy
        dates = pd.date_range(start="2024-01-01", end="2024-01-31", freq="D")
        returns = np.cumsum(np.random.normal(0.01, 0.3, len(dates)))
        
        fig = go.Figure()
        fig.add_trace(go.Scatter(x=dates, y=returns, mode='lines', name=selected_strategy))
        fig.update_layout(title=f"{selected_strategy} Performance", height=300)
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        st.subheader("⚙️ Strategy Controls")
        
        if st.button("▶️ Start Strategy", use_container_width=True):
            st.success("Strategy started!")
        
        if st.button("⏸️ Pause Strategy", use_container_width=True):
            st.warning("Strategy paused!")
        
        if st.button("⏹️ Stop Strategy", use_container_width=True):
            st.error("Strategy stopped!")
        
        if st.button("🔧 Modify Strategy", use_container_width=True):
            st.info("Opening strategy editor...")
        
        st.subheader("📊 Quick Stats")
        st.metric("Current Positions", "3")
        st.metric("Daily P&L", "+$45")
        st.metric("Max Drawdown", "8.5%")

def show_mt5_integration():
    """MT5 integration interface"""
    
    st.header("🔗 MT5 Integration")
    
    # Connection status
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Connection Status", "🟢 Connected")
    with col2:
        st.metric("Account Balance", "$10,247")
    with col3:
        st.metric("Open Positions", "7")
    
    # MT5 Terminal info
    st.subheader("💼 MT5 Terminal Information")
    
    terminal_info = {
        "Account Number": "********",
        "Broker": "IC Markets",
        "Server": "ICMarkets-Demo",
        "Currency": "USD",
        "Leverage": "1:500",
        "Margin Level": "245.67%"
    }
    
    col1, col2 = st.columns(2)
    
    with col1:
        for key, value in list(terminal_info.items())[:3]:
            st.text(f"{key}: {value}")
    
    with col2:
        for key, value in list(terminal_info.items())[3:]:
            st.text(f"{key}: {value}")
    
    # Open positions
    st.subheader("📊 Open Positions")
    
    positions_data = {
        "Symbol": ["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD"],
        "Type": ["Buy", "Sell", "Buy", "Buy", "Sell"],
        "Volume": [0.1, 0.2, 0.15, 0.1, 0.25],
        "Open Price": [1.0845, 1.2634, 149.23, 0.6789, 1.3456],
        "Current Price": [1.0867, 1.2612, 149.45, 0.6801, 1.3442],
        "P&L": ["+$22", "-$44", "+$33", "+$12", "+$35"],
        "Strategy": ["MACD Momentum", "RSI Mean Rev", "ML Forest", "Multi-TF", "Bollinger"]
    }
    
    df_positions = pd.DataFrame(positions_data)
    st.dataframe(df_positions, use_container_width=True)
    
    # Bridge controls
    st.subheader("🔧 Bridge Controls")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("🔄 Refresh Connection"):
            st.success("Connection refreshed!")
    
    with col2:
        if st.button("📊 Sync Positions"):
            st.success("Positions synchronized!")
    
    with col3:
        if st.button("⚙️ Bridge Settings"):
            st.info("Opening bridge settings...")
    
    with col4:
        if st.button("📋 Connection Log"):
            st.info("Showing connection log...")

def show_backtesting():
    """Backtesting interface"""
    
    st.header("📈 Strategy Backtesting")
    
    # Backtest configuration
    col1, col2 = st.columns([1, 2])
    
    with col1:
        st.subheader("⚙️ Backtest Configuration")
        
        strategy = st.selectbox("Strategy:", ["MACD Momentum", "RSI Mean Reversion", "ML Random Forest"])
        symbol = st.selectbox("Symbol:", ["EURUSD", "GBPUSD", "USDJPY", "AUDUSD"])
        
        start_date = st.date_input("Start Date:", datetime(2024, 1, 1))
        end_date = st.date_input("End Date:", datetime(2024, 1, 31))
        
        initial_balance = st.number_input("Initial Balance:", value=10000)
        
        if st.button("🚀 Run Backtest", use_container_width=True):
            with st.spinner("Running backtest..."):
                time.sleep(3)
                st.success("Backtest completed!")
    
    with col2:
        st.subheader("📊 Backtest Results")
        
        # Generate sample backtest results
        dates = pd.date_range(start="2024-01-01", end="2024-01-31", freq="D")
        equity_curve = 10000 + np.cumsum(np.random.normal(5, 50, len(dates)))
        
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=dates,
            y=equity_curve,
            mode='lines',
            name='Equity Curve',
            line=dict(color='#1f77b4', width=2)
        ))
        
        fig.update_layout(
            title="Equity Curve",
            xaxis_title="Date",
            yaxis_title="Account Balance ($)",
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    # Performance metrics
    st.subheader("📊 Performance Metrics")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Return", "12.47%", "+2.3%")
        st.metric("Sharpe Ratio", "1.23", "+0.15")
    
    with col2:
        st.metric("Max Drawdown", "8.5%", "-1.2%")
        st.metric("Win Rate", "68%", "+3%")
    
    with col3:
        st.metric("Total Trades", "45", "+12")
        st.metric("Profit Factor", "1.45", "+0.08")
    
    with col4:
        st.metric("Avg Trade", "$27.34", "+$3.21")
        st.metric("Best Trade", "$156", "+$23")

def show_templates():
    """Strategy templates interface"""
    
    st.header("📋 Strategy Templates")
    st.markdown("Choose from pre-built strategy templates and customize them to your needs.")
    
    # Template categories
    categories = st.tabs(["🔰 Beginner", "🎯 Intermediate", "🚀 Advanced", "🤖 AI/ML"])
    
    with categories[0]:  # Beginner
        st.subheader("🔰 Beginner-Friendly Templates")
        
        templates = [
            {
                "name": "Mean Reversion RSI",
                "description": "Classic mean reversion using RSI indicator",
                "difficulty": "Beginner",
                "indicators": ["RSI"],
                "estimated_performance": {"sharpe": 1.2, "drawdown": "15%", "win_rate": "55%"}
            },
            {
                "name": "Simple Moving Average",
                "description": "Basic trend following with moving averages",
                "difficulty": "Beginner", 
                "indicators": ["SMA"],
                "estimated_performance": {"sharpe": 0.9, "drawdown": "20%", "win_rate": "48%"}
            }
        ]
        
        for template in templates:
            with st.expander(f"📊 {template['name']}"):
                st.write(f"**Description:** {template['description']}")
                st.write(f"**Indicators:** {', '.join(template['indicators'])}")
                st.write(f"**Estimated Sharpe Ratio:** {template['estimated_performance']['sharpe']}")
                
                col1, col2 = st.columns(2)
                with col1:
                    if st.button(f"🔧 Customize {template['name']}", key=f"customize_{template['name']}"):
                        st.success(f"Opening customization for {template['name']}")
                with col2:
                    if st.button(f"📄 View Code {template['name']}", key=f"code_{template['name']}"):
                        st.code("class MeanReversionRSIStrategy(StrategyBase):\n    # Template code here...", language="python")
    
    with categories[1]:  # Intermediate
        st.subheader("🎯 Intermediate Templates")
        st.info("More sophisticated strategies with multiple indicators and advanced risk management.")
        
        templates = [
            "Momentum MACD",
            "Bollinger Bands Breakout", 
            "Multi-Timeframe Analysis"
        ]
        
        for template in templates:
            st.write(f"📊 **{template}**")
    
    with categories[2]:  # Advanced
        st.subheader("🚀 Advanced Templates")
        st.warning("Complex strategies requiring deeper understanding of markets and risk management.")
        
        templates = [
            "Statistical Arbitrage",
            "Options Strategies",
            "Portfolio Optimization"
        ]
        
        for template in templates:
            st.write(f"🎯 **{template}**")
    
    with categories[3]:  # AI/ML
        st.subheader("🤖 AI/ML Templates")
        st.error("Machine learning strategies with advanced feature engineering and model training.")
        
        templates = [
            "Random Forest Classifier",
            "Neural Network Predictor",
            "Ensemble Methods"
        ]
        
        for template in templates:
            st.write(f"🤖 **{template}**")

def show_settings():
    """Platform settings"""
    
    st.header("⚙️ Platform Settings")
    
    # User preferences
    st.subheader("👤 User Preferences")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.text_input("Display Name:", value="Trading Pro")
        st.selectbox("Timezone:", ["UTC", "EST", "PST", "GMT"])
        st.selectbox("Theme:", ["Light", "Dark", "Auto"])
    
    with col2:
        st.number_input("Default Risk per Trade (%):", value=2.0, min_value=0.1, max_value=10.0)
        st.number_input("Max Concurrent Strategies:", value=5, min_value=1, max_value=20)
        st.checkbox("Enable Email Notifications", value=True)
    
    # MT5 Settings
    st.subheader("🔗 MT5 Connection Settings")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.text_input("MT5 Path:", value="C:\\Program Files\\MetaTrader 5\\terminal64.exe")
        st.number_input("Connection Timeout (seconds):", value=30)
    
    with col2:
        st.checkbox("Auto-reconnect on disconnect", value=True)
        st.checkbox("Enable trade logging", value=True)
    
    # Risk Management
    st.subheader("⚠️ Risk Management")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.number_input("Max Daily Loss ($):", value=500)
        st.number_input("Max Drawdown (%):", value=20.0)
    
    with col2:
        st.number_input("Position Size Limit (%):", value=10.0)
        st.checkbox("Enable emergency stop", value=True)
    
    # Save settings
    if st.button("💾 Save Settings", use_container_width=True):
        st.success("Settings saved successfully!")

if __name__ == "__main__":
    main()