#!/usr/bin/env python
"""
Live testing script for MT5 Bridge
This script tests the MT5 Bridge implementation with real MT5 connection
"""

import sys
import os
import logging
import time
from datetime import datetime
from typing import Dict, Any, List

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("mt5_bridge_test")

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Import MT5 Bridge
try:
    from trading.mt5_bridge_tdd import MT5Bridge
    logger.info("Successfully imported MT5Bridge")
except ImportError as e:
    logger.error(f"Failed to import MT5Bridge: {e}")
    sys.exit(1)

def test_connection():
    """Test connection to MT5"""
    logger.info("Testing connection to MT5...")
    
    bridge = MT5Bridge(offline_mode=False)
    
    if bridge.is_connected():
        logger.info("✅ Successfully connected to MT5")
    else:
        logger.error("❌ Failed to connect to MT5")
        return False
    
    # Test disconnect and reconnect
    bridge.disconnect()
    logger.info("Disconnected from MT5")
    
    if bridge.is_connected():
        logger.error("❌ Bridge still shows connected after disconnect")
        return False
    
    if bridge.connect():
        logger.info("✅ Successfully reconnected to MT5")
    else:
        logger.error("❌ Failed to reconnect to MT5")
        return False
    
    return True

def test_place_order(bridge: MT5Bridge):
    """Test placing an order"""
    logger.info("Testing order placement...")
    
    try:
        # Place a small buy order for EURUSD
        order_id = bridge.place_order(
            symbol="EURUSD",
            order_type="BUY",
            lot=0.01,  # Minimum lot size
            price=None  # Market order
        )
        
        logger.info(f"✅ Successfully placed order: {order_id}")
        return order_id
    except Exception as e:
        logger.error(f"❌ Failed to place order: {e}")
        return None

def test_get_order_status(bridge: MT5Bridge, order_id: int):
    """Test getting order status"""
    logger.info(f"Testing get order status for order {order_id}...")
    
    try:
        status = bridge.get_order_status(order_id)
        logger.info(f"✅ Order status: {status}")
        return status
    except Exception as e:
        logger.error(f"❌ Failed to get order status: {e}")
        return None

def test_close_order(bridge: MT5Bridge, order_id: int):
    """Test closing an order"""
    logger.info(f"Testing close order for order {order_id}...")
    
    try:
        result = bridge.close_order(order_id)
        if result:
            logger.info(f"✅ Successfully closed order {order_id}")
        else:
            logger.error(f"❌ Failed to close order {order_id}")
        return result
    except Exception as e:
        logger.error(f"❌ Error closing order: {e}")
        return False

def test_get_positions(bridge: MT5Bridge):
    """Test getting positions"""
    logger.info("Testing get positions...")
    
    try:
        positions = bridge.get_positions()
        logger.info(f"✅ Got {len(positions)} positions")
        
        # Print position details
        for pos in positions:
            logger.info(f"  - {pos['symbol']} {pos['type']} {pos['lot']} lots, profit: {pos.get('profit', 'N/A')}")
        
        return positions
    except Exception as e:
        logger.error(f"❌ Failed to get positions: {e}")
        return []

def test_error_handling(bridge: MT5Bridge):
    """Test error handling"""
    logger.info("Testing error handling...")
    
    # Test invalid symbol
    try:
        bridge.place_order(
            symbol="INVALID",
            order_type="BUY",
            lot=0.01
        )
        logger.error("❌ Expected ValueError for invalid symbol, but no exception was raised")
        return False
    except ValueError as e:
        logger.info(f"✅ Correctly raised ValueError for invalid symbol: {e}")
    except Exception as e:
        logger.error(f"❌ Expected ValueError for invalid symbol, but got {type(e).__name__}: {e}")
        return False
    
    # Test invalid lot size
    try:
        bridge.place_order(
            symbol="EURUSD",
            order_type="BUY",
            lot=-0.01
        )
        logger.error("❌ Expected ValueError for invalid lot size, but no exception was raised")
        return False
    except ValueError as e:
        logger.info(f"✅ Correctly raised ValueError for invalid lot size: {e}")
    except Exception as e:
        logger.error(f"❌ Expected ValueError for invalid lot size, but got {type(e).__name__}: {e}")
        return False
    
    # Test API error simulation
    try:
        bridge.simulate_api_error()
        bridge.place_order(
            symbol="EURUSD",
            order_type="BUY",
            lot=0.01
        )
        logger.error("❌ Expected Exception for API error, but no exception was raised")
        return False
    except Exception as e:
        if "API error" in str(e):
            logger.info(f"✅ Correctly raised Exception for API error: {e}")
        else:
            logger.error(f"❌ Expected 'API error' in exception message, but got: {e}")
            return False
    
    return True

def test_reconnection(bridge: MT5Bridge):
    """Test auto-reconnection"""
    logger.info("Testing auto-reconnection...")
    
    # Simulate connection loss
    bridge.simulate_connection_loss()
    
    if bridge.is_connected():
        logger.error("❌ Bridge still shows connected after simulating connection loss")
        return False
    
    logger.info("✅ Successfully simulated connection loss")
    
    # Try to place an order - should auto-reconnect
    try:
        order_id = bridge.place_order(
            symbol="EURUSD",
            order_type="BUY",
            lot=0.01
        )
        
        if bridge.is_connected():
            logger.info("✅ Successfully auto-reconnected")
        else:
            logger.error("❌ Failed to auto-reconnect")
            return False
        
        logger.info(f"✅ Successfully placed order after reconnection: {order_id}")
        
        # Close the order
        bridge.close_order(order_id)
        
        return True
    except Exception as e:
        logger.error(f"❌ Failed to place order after reconnection: {e}")
        return False

def run_tests():
    """Run all tests"""
    logger.info("Starting MT5 Bridge tests...")
    
    # Test connection
    if not test_connection():
        logger.error("Connection test failed, aborting further tests")
        return False
    
    # Create bridge instance
    bridge = MT5Bridge(offline_mode=False)
    
    # Test error handling
    if not test_error_handling(bridge):
        logger.error("Error handling test failed")
    
    # Test reconnection
    if not test_reconnection(bridge):
        logger.error("Reconnection test failed")
    
    # Test getting positions
    positions = test_get_positions(bridge)
    
    # Test placing an order
    order_id = test_place_order(bridge)
    if order_id:
        # Test getting order status
        status = test_get_order_status(bridge, order_id)
        
        # Wait a bit to let the order execute
        logger.info("Waiting 2 seconds for order to execute...")
        time.sleep(2)
        
        # Test getting positions again
        positions_after = test_get_positions(bridge)
        
        # Test closing the order
        test_close_order(bridge, order_id)
    
    # Disconnect
    bridge.disconnect()
    logger.info("Disconnected from MT5")
    
    logger.info("All tests completed")
    return True

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)