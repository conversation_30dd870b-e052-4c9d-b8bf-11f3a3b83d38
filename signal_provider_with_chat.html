<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Trading Signals + Chat Assistant</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .disclaimer {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 20px;
            text-align: center;
            font-weight: 600;
        }
        
        .status {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        
        .status-item {
            display: inline-block;
            margin-right: 30px;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
        }
        
        .status-online {
            background: #d4edda;
            color: #155724;
        }
        
        .status-offline {
            background: #f8d7da;
            color: #721c24;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 30px;
            padding: 30px;
        }
        
        .signals-section {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }
        
        .section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            border: 1px solid #e9ecef;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        
        .signal-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #3498db;
        }
        
        .signal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .signal-symbol {
            font-size: 1.2rem;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .signal-type {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }
        
        .signal-buy {
            background: #d4edda;
            color: #155724;
        }
        
        .signal-sell {
            background: #f8d7da;
            color: #721c24;
        }
        
        .signal-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .signal-detail {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 6px;
            text-align: center;
        }
        
        .signal-detail-label {
            font-size: 0.8rem;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .signal-detail-value {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }
        
        /* Chat Section Styles */
        .chat-section {
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            display: flex;
            flex-direction: column;
            height: 600px;
        }
        
        .chat-header {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 20px;
            border-radius: 8px 8px 0 0;
            text-align: center;
        }
        
        .chat-header h2 {
            margin: 0;
            font-size: 1.3rem;
            border: none;
            padding: 0;
        }
        
        .chat-status {
            font-size: 0.9rem;
            opacity: 0.9;
            margin-top: 5px;
        }
        
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: white;
        }
        
        .chat-message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 12px;
            max-width: 85%;
            word-wrap: break-word;
        }
        
        .chat-message.user {
            background: #e3f2fd;
            margin-left: auto;
            text-align: right;
        }
        
        .chat-message.assistant {
            background: #f5f5f5;
            margin-right: auto;
        }
        
        .chat-message.assistant.typing {
            background: #e8f5e8;
            font-style: italic;
        }
        
        .chat-input-container {
            padding: 20px;
            border-top: 1px solid #e9ecef;
            background: white;
            border-radius: 0 0 8px 8px;
        }
        
        .chat-input-form {
            display: flex;
            gap: 10px;
        }
        
        .chat-input {
            flex: 1;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 1rem;
            outline: none;
            transition: border-color 0.3s;
        }
        
        .chat-input:focus {
            border-color: #3498db;
        }
        
        .chat-send-btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: transform 0.2s;
        }
        
        .chat-send-btn:hover {
            transform: translateY(-1px);
        }
        
        .chat-send-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .quick-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 15px;
        }
        
        .quick-action {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            color: #1976d2;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .quick-action:hover {
            background: #bbdefb;
        }
        
        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .chat-section {
                height: 500px;
            }
        }
        
        @media (max-width: 768px) {
            .signal-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI Trading Signals + Chat Assistant</h1>
            <p>Professional Trading Signal Provider with AI Chat Support</p>
        </div>
        
        <div class="disclaimer">
            ⚠️ IMPORTANT: This platform provides trading signals and AI assistance for educational purposes. You execute trades on your own MT5 account.
        </div>
        
        <div class="status">
            <div id="signal-status" class="status-item status-online">Signal Engine: Active</div>
            <div id="chat-status" class="status-item status-online">AI Assistant: Online</div>
            <div id="signals-count" class="status-item status-online">Today's Signals: 5</div>
        </div>
        
        <div class="main-content">
            <!-- Signals Section -->
            <div class="signals-section">
                <div class="section">
                    <h2>🔥 Live Trading Signals</h2>
                    
                    <div id="signals-container">
                        <!-- Signals will be populated here -->
                    </div>
                    
                    <button id="refresh-signals" class="btn">🔄 Generate New Signal</button>
                </div>
                
                <div class="section">
                    <h2>🎯 Active Strategies</h2>
                    
                    <div id="strategies-container">
                        <!-- Strategies will be populated here -->
                    </div>
                </div>
            </div>
            
            <!-- Chat Section -->
            <div class="chat-section">
                <div class="chat-header">
                    <h2>🤖 AI Trading Assistant</h2>
                    <div class="chat-status">Ask me about signals, strategies, or market analysis</div>
                </div>
                
                <div class="chat-messages" id="chat-messages">
                    <div class="chat-message assistant">
                        👋 Hello! I'm your AI Trading Assistant. I can help you with:
                        <br><br>
                        • Analyze current market signals<br>
                        • Explain trading strategies<br>
                        • Provide risk management advice<br>
                        • Answer questions about MT5 execution<br>
                        <br>
                        What would you like to know?
                    </div>
                </div>
                
                <div class="chat-input-container">
                    <div class="quick-actions">
                        <div class="quick-action" onclick="sendQuickMessage('Analyze EURUSD')">Analyze EURUSD</div>
                        <div class="quick-action" onclick="sendQuickMessage('Best strategy today?')">Best strategy?</div>
                        <div class="quick-action" onclick="sendQuickMessage('Risk management tips')">Risk tips</div>
                        <div class="quick-action" onclick="sendQuickMessage('How to use MT5?')">MT5 help</div>
                    </div>
                    
                    <form class="chat-input-form" id="chat-form">
                        <input 
                            type="text" 
                            class="chat-input" 
                            id="chat-input" 
                            placeholder="Ask me anything about trading..."
                            autocomplete="off"
                        >
                        <button type="submit" class="chat-send-btn" id="chat-send-btn">Send</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8002';
        
        // Mock signals data
        const mockSignals = [
            {
                id: 1,
                symbol: "EURUSD",
                type: "BUY",
                entry_price: 1.1234,
                stop_loss: 1.1200,
                take_profit: 1.1300,
                suggested_lot_size: 0.1,
                confidence: 85,
                strategy: "MA Crossover",
                timestamp: new Date(),
                reasoning: "Fast MA crossed above slow MA with strong momentum"
            },
            {
                id: 2,
                symbol: "GBPUSD",
                type: "SELL",
                entry_price: 1.3145,
                stop_loss: 1.3180,
                take_profit: 1.3080,
                suggested_lot_size: 0.1,
                confidence: 78,
                strategy: "RSI Reversal",
                timestamp: new Date(Date.now() - 15 * 60 * 1000),
                reasoning: "RSI showing overbought conditions with bearish divergence"
            }
        ];
        
        const mockStrategies = [
            {
                id: 1,
                name: "Moving Average Crossover",
                description: "Fast MA crosses slow MA strategy",
                win_rate: 68.5,
                profit_factor: 1.45,
                monthly_return: 12.3,
                active: true
            },
            {
                id: 2,
                name: "RSI Reversal",
                description: "Oversold/Overbought reversal strategy",
                win_rate: 72.1,
                profit_factor: 1.62,
                monthly_return: 15.7,
                active: true
            }
        ];
        
        // Chat functionality
        let chatMessages = [];
        
        // Initialize the page
        window.addEventListener('load', function() {
            loadSignals();
            loadStrategies();
            initializeChat();
        });
        
        // Load signals
        async function loadSignals() {
            const container = document.getElementById('signals-container');
            
            try {
                const response = await fetch(`${API_BASE}/api/signals/live`);
                let signals = [];
                
                if (response.ok) {
                    signals = await response.json();
                } else {
                    signals = mockSignals;
                }
                
                if (signals.length === 0) {
                    signals = mockSignals;
                }
                
                container.innerHTML = signals.map(signal => {
                    const timestamp = signal.timestamp ? new Date(signal.timestamp) : new Date();
                    const lotSize = signal.suggested_lot_size || signal.lot_size || 0.1;
                    
                    return `
                    <div class="signal-item">
                        <div class="signal-header">
                            <span class="signal-symbol">${signal.symbol}</span>
                            <span class="signal-type signal-${signal.type.toLowerCase()}">${signal.type}</span>
                        </div>
                        
                        <div class="signal-details">
                            <div class="signal-detail">
                                <div class="signal-detail-label">Entry Price</div>
                                <div class="signal-detail-value">${signal.entry_price}</div>
                            </div>
                            <div class="signal-detail">
                                <div class="signal-detail-label">Stop Loss</div>
                                <div class="signal-detail-value">${signal.stop_loss}</div>
                            </div>
                            <div class="signal-detail">
                                <div class="signal-detail-label">Take Profit</div>
                                <div class="signal-detail-value">${signal.take_profit}</div>
                            </div>
                            <div class="signal-detail">
                                <div class="signal-detail-label">Suggested Lot</div>
                                <div class="signal-detail-value">${lotSize}</div>
                            </div>
                        </div>
                        
                        <div style="margin-bottom: 15px;">
                            <strong>Strategy:</strong> ${signal.strategy} | 
                            <strong>Confidence:</strong> ${signal.confidence}% | 
                            <strong>Time:</strong> ${timestamp.toLocaleTimeString()}
                        </div>
                        
                        <div style="margin-bottom: 15px; color: #6c757d; font-style: italic;">
                            <strong>Analysis:</strong> ${signal.reasoning}
                        </div>
                    </div>
                `;
                }).join('');
                
            } catch (error) {
                console.error('Failed to load signals:', error);
                // Use mock data as fallback
                container.innerHTML = mockSignals.map(signal => `
                    <div class="signal-item">
                        <div class="signal-header">
                            <span class="signal-symbol">${signal.symbol}</span>
                            <span class="signal-type signal-${signal.type.toLowerCase()}">${signal.type}</span>
                        </div>
                        <div style="padding: 10px;">
                            <strong>Entry:</strong> ${signal.entry_price} | 
                            <strong>SL:</strong> ${signal.stop_loss} | 
                            <strong>TP:</strong> ${signal.take_profit}<br>
                            <strong>Strategy:</strong> ${signal.strategy} | 
                            <strong>Confidence:</strong> ${signal.confidence}%
                        </div>
                    </div>
                `).join('');
            }
        }
        
        // Load strategies
        function loadStrategies() {
            const container = document.getElementById('strategies-container');
            
            container.innerHTML = mockStrategies.map(strategy => `
                <div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 15px; margin-bottom: 10px;">
                    <div style="font-weight: bold; color: #2c3e50; margin-bottom: 8px;">${strategy.name}</div>
                    <div style="color: #6c757d; font-size: 0.9rem; margin-bottom: 10px;">${strategy.description}</div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                        <div style="text-align: center; padding: 8px; background: #f8f9fa; border-radius: 4px;">
                            <div style="font-size: 0.8rem; color: #6c757d;">Win Rate</div>
                            <div style="font-weight: bold; color: #27ae60;">${strategy.win_rate}%</div>
                        </div>
                        <div style="text-align: center; padding: 8px; background: #f8f9fa; border-radius: 4px;">
                            <div style="font-size: 0.8rem; color: #6c757d;">Monthly Return</div>
                            <div style="font-weight: bold; color: #27ae60;">${strategy.monthly_return}%</div>
                        </div>
                    </div>
                </div>
            `).join('');
        }
        
        // Initialize chat
        function initializeChat() {
            const chatForm = document.getElementById('chat-form');
            const chatInput = document.getElementById('chat-input');
            
            chatForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const message = chatInput.value.trim();
                if (message) {
                    sendMessage(message);
                    chatInput.value = '';
                }
            });
        }
        
        // Send chat message
        function sendMessage(message) {
            addChatMessage('user', message);
            
            // Simulate AI response
            setTimeout(() => {
                const response = generateAIResponse(message);
                addChatMessage('assistant', response);
            }, 1000);
        }
        
        // Send quick message
        function sendQuickMessage(message) {
            document.getElementById('chat-input').value = message;
            sendMessage(message);
        }
        
        // Add chat message
        function addChatMessage(type, content) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `chat-message ${type}`;
            
            if (type === 'assistant') {
                messageDiv.innerHTML = content;
            } else {
                messageDiv.textContent = content;
            }
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        // Generate AI response
        function generateAIResponse(message) {
            const lowerMessage = message.toLowerCase();
            
            if (lowerMessage.includes('eurusd') || lowerMessage.includes('eur/usd')) {
                return `📊 <strong>EURUSD Analysis:</strong><br><br>
                Current signals show a <strong>BUY</strong> opportunity at 1.1234<br>
                • <strong>Entry:</strong> 1.1234<br>
                • <strong>Stop Loss:</strong> 1.1200<br>
                • <strong>Take Profit:</strong> 1.1300<br>
                • <strong>Confidence:</strong> 85%<br><br>
                <em>Strategy: Moving Average Crossover showing bullish momentum</em>`;
            }
            
            if (lowerMessage.includes('strategy') || lowerMessage.includes('best')) {
                return `🎯 <strong>Today's Best Strategy:</strong><br><br>
                <strong>RSI Reversal</strong> is performing excellently:<br>
                • <strong>Win Rate:</strong> 72.1%<br>
                • <strong>Monthly Return:</strong> 15.7%<br>
                • <strong>Current Signals:</strong> 3 active<br><br>
                This strategy identifies overbought/oversold conditions with high accuracy.`;
            }
            
            if (lowerMessage.includes('risk') || lowerMessage.includes('management')) {
                return `⚠️ <strong>Risk Management Tips:</strong><br><br>
                1. <strong>Never risk more than 2% per trade</strong><br>
                2. <strong>Always use stop losses</strong><br>
                3. <strong>Diversify across currency pairs</strong><br>
                4. <strong>Follow the 1:2 risk-reward ratio</strong><br>
                5. <strong>Don't trade with emotions</strong><br><br>
                Remember: Preserve capital first, profits second!`;
            }
            
            if (lowerMessage.includes('mt5') || lowerMessage.includes('metatrader')) {
                return `🔧 <strong>MT5 Execution Guide:</strong><br><br>
                1. <strong>Open MT5 platform</strong><br>
                2. <strong>Navigate to the currency pair</strong><br>
                3. <strong>Right-click → Trading → New Order</strong><br>
                4. <strong>Set order type (Buy/Sell)</strong><br>
                5. <strong>Enter lot size and levels</strong><br>
                6. <strong>Click Buy/Sell to execute</strong><br><br>
                <em>Always double-check your order details before execution!</em>`;
            }
            
            if (lowerMessage.includes('hello') || lowerMessage.includes('hi')) {
                return `👋 <strong>Hello!</strong> I'm here to help with your trading questions.<br><br>
                I can assist with:<br>
                • Market analysis and signals<br>
                • Strategy explanations<br>
                • Risk management advice<br>
                • MT5 platform guidance<br><br>
                What would you like to know?`;
            }
            
            // Default response
            return `🤖 I understand you're asking about "${message}".<br><br>
            I can help you with:<br>
            • <strong>Market Analysis:</strong> Ask about specific currency pairs<br>
            • <strong>Trading Strategies:</strong> Learn about our signal strategies<br>
            • <strong>Risk Management:</strong> Get safety tips<br>
            • <strong>Platform Help:</strong> MT5 execution guidance<br><br>
            Try asking something like "Analyze EURUSD" or "Best strategy today?"`;
        }
        
        // Refresh signals button
        document.getElementById('refresh-signals').addEventListener('click', async function() {
            const button = this;
            button.textContent = '🔄 Generating New Signal...';
            button.disabled = true;
            
            try {
                const response = await fetch(`${API_BASE}/api/signals/generate`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({})
                });
                
                if (response.ok) {
                    await loadSignals();
                    button.textContent = '✅ New Signal Generated!';
                    
                    // Add chat notification
                    addChatMessage('assistant', '🔔 <strong>New Signal Generated!</strong><br>Check the signals section for the latest trading opportunity.');
                } else {
                    throw new Error('Failed to generate signal');
                }
            } catch (error) {
                await loadSignals();
                button.textContent = '✅ Signals Refreshed';
            }
            
            setTimeout(() => {
                button.textContent = '🔄 Generate New Signal';
                button.disabled = false;
            }, 2000);
        });
        
        // Auto-refresh signals every 5 minutes
        setInterval(loadSignals, 5 * 60 * 1000);
    </script>
</body>
</html>