<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧬 Darwin Strategy Verification Platform</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            min-height: 100vh;
        }
        
        .platform-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            backdrop-filter: blur(15px);
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #00ff87, #60efff);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.8;
        }
        
        .connection-status {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin-top: 15px;
            padding: 10px;
            border-radius: 10px;
            font-weight: bold;
        }
        
        .connection-status.connected {
            background: rgba(0, 255, 135, 0.2);
            color: #00ff87;
        }
        
        .connection-status.disconnected {
            background: rgba(255, 69, 58, 0.2);
            color: #ff453a;
        }
        
        .workflow-tabs {
            display: flex;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 5px;
        }
        
        .tab {
            flex: 1;
            padding: 15px 20px;
            text-align: center;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            background: transparent;
            color: #fff;
            font-size: 1.1em;
        }
        
        .tab.active {
            background: linear-gradient(45deg, #00ff87, #60efff);
            color: #000;
            font-weight: bold;
        }
        
        .tab:hover:not(.active) {
            background: rgba(255, 255, 255, 0.1);
        }
        
        .tab-content {
            display: none;
            animation: fadeIn 0.5s ease;
        }
        
        .tab-content.active {
            display: block;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .step-card {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .step-card h3 {
            color: #60efff;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 1.5em;
        }
        
        .data-source-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .data-source-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            text-align: center;
        }
        
        .data-source-card:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }
        
        .data-source-card.selected {
            border-color: #00ff87;
            background: rgba(0, 255, 135, 0.1);
        }
        
        .data-source-card h4 {
            color: #60efff;
            margin-bottom: 10px;
        }
        
        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .config-group {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 10px;
        }
        
        .config-group h4 {
            color: #00ff87;
            margin-bottom: 15px;
        }
        
        .form-row {
            margin-bottom: 15px;
        }
        
        .form-row label {
            display: block;
            margin-bottom: 5px;
            opacity: 0.8;
        }
        
        input, select, textarea {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 10px;
            color: #fff;
            font-size: 14px;
        }
        
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #60efff;
            box-shadow: 0 0 10px rgba(96, 239, 255, 0.3);
        }
        
        .btn {
            background: linear-gradient(45deg, #00ff87, #60efff);
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            color: #000;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1em;
            margin: 10px 10px 10px 0;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(96, 239, 255, 0.4);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
        }
        
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .strategy-editor {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            margin: 20px 0;
            overflow: hidden;
        }
        
        .editor-header {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .strategy-textarea {
            width: 100%;
            height: 400px;
            background: transparent;
            border: none;
            color: #fff;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            resize: vertical;
            outline: none;
        }
        
        .template-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .template-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .template-card:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }
        
        .template-card h5 {
            color: #60efff;
            margin-bottom: 8px;
        }
        
        .template-card p {
            font-size: 0.9em;
            opacity: 0.8;
            line-height: 1.4;
        }
        
        .progress-section {
            margin: 30px 0;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00ff87, #60efff);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .verification-results {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .result-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            border-left: 4px solid #00ff87;
        }
        
        .result-card h4 {
            color: #60efff;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .metric:last-child {
            border-bottom: none;
        }
        
        .metric-value {
            color: #00ff87;
            font-weight: bold;
        }
        
        .status {
            padding: 12px 20px;
            border-radius: 10px;
            margin: 15px 0;
            font-weight: bold;
            text-align: center;
        }
        
        .status.success { background: rgba(0, 255, 135, 0.2); color: #00ff87; }
        .status.warning { background: rgba(255, 165, 0, 0.2); color: #ffa500; }
        .status.error { background: rgba(255, 69, 58, 0.2); color: #ff453a; }
        .status.info { background: rgba(96, 239, 255, 0.2); color: #60efff; }
        
        .file-info {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        .report-section {
            background: rgba(255, 255, 255, 0.03);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
        }

        .export-buttons {
            display: flex;
            gap: 15px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #60efff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="platform-container">
        <div class="header">
            <h1>🧬 Darwin Strategy Verification Platform</h1>
            <p>Professional-grade mathematical verification with real market data</p>
            <div id="connection-status" class="connection-status disconnected">
                <span class="loading"></span>
                <span>Connecting to backend...</span>
            </div>
        </div>

        <div class="workflow-tabs">
            <button class="tab active" onclick="showTab('data')">📊 Market Data</button>
            <button class="tab" onclick="showTab('strategy')">⚡ Strategy</button>
            <button class="tab" onclick="showTab('configure')">⚙️ Configure</button>
            <button class="tab" onclick="showTab('verify')">🔬 Verify</button>
            <button class="tab" onclick="showTab('results')">📈 Results</button>
        </div>

        <!-- Tab 1: Market Data -->
        <div id="data-tab" class="tab-content active">
            <div class="step-card">
                <h3>📊 Step 1: Load Market Data</h3>
                
                <div class="data-source-selector">
                    <div class="data-source-card selected" onclick="selectDataSource('live')">
                        <h4>🔴 Live Market Data</h4>
                        <p>Real-time data from Yahoo Finance</p>
                        <p><strong>Forex, Stocks, Crypto</strong></p>
                    </div>
                    <div class="data-source-card" onclick="selectDataSource('sample')">
                        <h4>📋 Sample Data</h4>
                        <p>Pre-loaded datasets for testing</p>
                        <p><strong>EURUSD, AAPL, BTCUSD</strong></p>
                    </div>
                </div>

                <div id="live-data-config" class="config-grid">
                    <div class="config-group">
                        <h4>Market Selection</h4>
                        <div class="form-row">
                            <label for="symbol">Symbol</label>
                            <input type="text" id="symbol" placeholder="e.g., AAPL, EURUSD=X, BTC-USD" value="AAPL">
                        </div>
                        <div class="form-row">
                            <label for="timeframe">Timeframe</label>
                            <select id="timeframe">
                                <option value="1m">1 Minute</option>
                                <option value="5m">5 Minutes</option>
                                <option value="15m">15 Minutes</option>
                                <option value="1h" selected>1 Hour</option>
                                <option value="4h">4 Hours</option>
                                <option value="1d">1 Day</option>
                            </select>
                        </div>
                        <div class="form-row">
                            <label for="period">Period</label>
                            <select id="period">
                                <option value="1mo">1 Month</option>
                                <option value="3mo">3 Months</option>
                                <option value="6mo">6 Months</option>
                                <option value="1y" selected>1 Year</option>
                                <option value="2y">2 Years</option>
                                <option value="5y">5 Years</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="config-group">
                        <h4>Quick Symbols</h4>
                        <p style="margin-bottom: 15px;">Popular trading instruments:</p>
                        <button class="btn btn-secondary" onclick="setSymbol('AAPL')">📈 AAPL</button>
                        <button class="btn btn-secondary" onclick="setSymbol('EURUSD=X')">💱 EUR/USD</button>
                        <button class="btn btn-secondary" onclick="setSymbol('BTC-USD')">₿ Bitcoin</button>
                        <button class="btn btn-secondary" onclick="setSymbol('TSLA')">🚗 Tesla</button>
                    </div>
                </div>

                <div style="text-align: center; margin: 30px 0;">
                    <button class="btn" id="load-data-btn" onclick="loadMarketData()" style="font-size: 1.2em; padding: 20px 40px;">
                        📊 Load Market Data
                    </button>
                </div>

                <div id="data-info" style="display: none;" class="file-info">
                    <h4>📊 Market Data Information</h4>
                    <div id="data-details"></div>
                </div>
            </div>
        </div>

        <!-- Tab 2: Strategy -->
        <div id="strategy-tab" class="tab-content">
            <div class="step-card">
                <h3>⚡ Step 2: Define Your Trading Strategy</h3>
                
                <div class="template-gallery">
                    <div class="template-card" onclick="loadStrategyTemplate('meanReversion')">
                        <h5>📈 Mean Reversion</h5>
                        <p>Buy when price is below moving average, sell when above. Uses real RSI and Bollinger Bands.</p>
                    </div>
                    <div class="template-card" onclick="loadStrategyTemplate('momentum')">
                        <h5>🚀 Momentum Breakout</h5>
                        <p>Trend-following strategy using RSI and moving average crossovers with real indicators.</p>
                    </div>
                    <div class="template-card" onclick="loadStrategyTemplate('bollinger')">
                        <h5>📊 Bollinger Band Strategy</h5>
                        <p>Trade bounces off Bollinger Band boundaries with volatility filters.</p>
                    </div>
                    <div class="template-card" onclick="loadStrategyTemplate('custom')">
                        <h5>✏️ Custom Strategy</h5>
                        <p>Write your own strategy from scratch with access to all technical indicators.</p>
                    </div>
                </div>

                <div class="strategy-editor">
                    <div class="editor-header">
                        <span>Strategy Code Editor - Real Technical Indicators Available</span>
                        <div>
                            <button class="btn btn-secondary" onclick="validateStrategy()">✅ Validate</button>
                            <button class="btn btn-secondary" onclick="saveStrategy()">💾 Save</button>
                        </div>
                    </div>
                    <textarea id="strategy-code" class="strategy-textarea" 
                              placeholder="Loading strategy template..."
                              aria-label="Strategy code editor">
// Your strategy will have access to real technical indicators calculated by the backend
function tradingStrategy(data, params) {
    // data: array of OHLCV with real technical indicators
    // Available indicators: sma_20, sma_50, rsi, bb_upper, bb_lower, bb_middle, macd, macd_signal
    
    const signals = [];
    
    for (let i = 50; i < data.length; i++) {
        const current = data[i];
        const previous = data[i-1];
        
        // Access real technical indicators calculated by Python backend
        const rsi = current.rsi || 50;
        const sma20 = current.sma_20;
        const bbUpper = current.bb_upper;
        const bbLower = current.bb_lower;
        const macd = current.macd;
        
        // Example strategy: RSI + Bollinger Bands
        if (rsi < 30 && current.close < bbLower) {
            signals.push({
                timestamp: current.timestamp,
                action: 'buy',
                size: 0.1,  // 10% of portfolio
                price: current.close
            });
        } else if (rsi > 70 && current.close > bbUpper) {
            signals.push({
                timestamp: current.timestamp,
                action: 'sell',
                size: 0.1,
                price: current.close
            });
        }
    }
    
    return signals;
}
                    </textarea>
                </div>

                <div id="strategy-validation" style="display: none;" class="status">
                    Strategy validation results will appear here...
                </div>
            </div>
        </div>

        <!-- Tab 3: Configure -->
        <div id="configure-tab" class="tab-content">
            <div class="step-card">
                <h3>⚙️ Step 3: Configure Verification Parameters</h3>
                
                <div class="config-grid">
                    <div class="config-group">
                        <h4>Trading Parameters</h4>
                        <div class="form-row">
                            <label for="initial-capital">Initial Capital ($)</label>
                            <input type="number" id="initial-capital" value="100000" min="1000">
                        </div>
                        <div class="form-row">
                            <label for="commission">Commission per Trade ($)</label>
                            <input type="number" id="commission" value="7" min="0" step="0.01">
                        </div>
                        <div class="form-row">
                            <label for="spread">Spread (pips)</label>
                            <input type="number" id="spread" value="2" min="0" step="0.1">
                        </div>
                        <div class="form-row">
                            <label for="max-risk">Max Risk per Trade (%)</label>
                            <input type="number" id="max-risk" value="2" min="0.1" max="10" step="0.1">
                        </div>
                    </div>

                    <div class="config-group">
                        <h4>Monte Carlo Settings</h4>
                        <div class="form-row">
                            <label for="monte-carlo-sims">Number of Simulations</label>
                            <select id="monte-carlo-sims">
                                <option value="100">100 (Fast)</option>
                                <option value="500">500 (Balanced)</option>
                                <option value="1000" selected>1000 (Thorough)</option>
                                <option value="2000">2000 (Comprehensive)</option>
                            </select>
                        </div>
                        <div class="form-row">
                            <label for="confidence-level">Confidence Level (%)</label>
                            <select id="confidence-level">
                                <option value="90">90%</option>
                                <option value="95" selected>95%</option>
                                <option value="99">99%</option>
                            </select>
                        </div>
                    </div>

                    <div class="config-group">
                        <h4>Strategy Parameters</h4>
                        <div class="form-row">
                            <label for="rsi-period">RSI Period</label>
                            <input type="number" id="rsi-period" value="14" min="5" max="50">
                        </div>
                        <div class="form-row">
                            <label for="sma-period">SMA Period</label>
                            <input type="number" id="sma-period" value="20" min="5" max="200">
                        </div>
                        <div class="form-row">
                            <label for="bb-period">Bollinger Band Period</label>
                            <input type="number" id="bb-period" value="20" min="10" max="50">
                        </div>
                    </div>

                    <div class="config-group">
                        <h4>Backend Connection</h4>
                        <div class="form-row">
                            <label for="backend-url">Backend URL</label>
                            <input type="text" id="backend-url" value="http://localhost:5000" readonly>
                        </div>
                        <div class="form-row">
                            <button class="btn btn-secondary" onclick="testBackendConnection()">🔗 Test Connection</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tab 4: Verify -->
        <div id="verify-tab" class="tab-content">
            <div class="step-card">
                <h3>🔬 Step 4: Run Professional Verification</h3>
                
                <div style="text-align: center; margin: 30px 0;">
                    <button class="btn" id="start-verification" onclick="startVerification()" style="font-size: 1.2em; padding: 20px 40px;">
                        🚀 Start Real Market Verification
                    </button>
                </div>

                <div class="progress-section" id="progress-section" style="display: none;">
                    <h4>Verification Progress</h4>
                    
                    <div style="margin: 20px 0;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span id="current-test">Initializing...</span>
                            <span id="progress-percent">0%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="main-progress"></div>
                        </div>
                    </div>

                    <div class="verification-results" id="live-results">
                        <!-- Live results will appear here during verification -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Tab 5: Results -->
        <div id="results-tab" class="tab-content">
            <div class="step-card">
                <h3>📈 Step 5: Verification Results & Professional Report</h3>
                
                <div id="final-results" style="display: none;">
                    <div class="report-section">
                        <h4>🏆 Executive Summary</h4>
                        <div id="executive-summary"></div>
                        
                        <div class="export-buttons">
                            <button class="btn" onclick="exportReport('pdf')">📄 Export PDF Report</button>
                            <button class="btn" onclick="exportReport('excel')">📊 Export Excel Data</button>
                            <button class="btn" onclick="exportReport('json')">📋 Export JSON Results</button>
                            <button class="btn btn-secondary" onclick="shareResults()">🔗 Share Results</button>
                        </div>
                    </div>

                    <div class="verification-results" id="detailed-results">
                        <!-- Detailed verification results -->
                    </div>
                </div>

                <div id="no-results" style="text-align: center; padding: 50px;">
                    <div style="font-size: 4em; margin-bottom: 20px;">📊</div>
                    <h4>No verification results yet</h4>
                    <p style="opacity: 0.7; margin-top: 10px;">Complete the verification process to see detailed results here.</p>
                    <button class="btn btn-secondary" onclick="generateDemoResults()" style="margin-top: 20px;">
                        🎯 Generate Demo Results
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Configuration
        const API_BASE = 'http://localhost:5000/api';
        
        // Platform State Management
        class DarwinPlatform {
            constructor() {
                this.currentTab = 'data';
                this.marketData = null;
                this.userStrategy = null;
                this.verificationResults = {};
                this.isVerifying = false;
                this.backendConnected = false;
                
                this.init();
            }

            async init() {
                await this.checkBackendConnection();
                this.loadStrategyTemplate('meanReversion');
            }

            async checkBackendConnection() {
                try {
                    const response = await fetch(`${API_BASE}/health`);
                    const data = await response.json();
                    
                    if (data.status === 'healthy') {
                        this.backendConnected = true;
                        this.updateConnectionStatus(true, 'Connected to Darwin Backend');
                    } else {
                        throw new Error('Backend unhealthy');
                    }
                } catch (error) {
                    this.backendConnected = false;
                    this.updateConnectionStatus(false, 'Backend connection failed - Start darwin_backend.py');
                }
            }

            updateConnectionStatus(connected, message) {
                const statusEl = document.getElementById('connection-status');
                statusEl.className = `connection-status ${connected ? 'connected' : 'disconnected'}`;
                statusEl.innerHTML = `
                    <span>${connected ? '✅' : '❌'}</span>
                    <span>${message}</span>
                `;
            }

            async loadMarketData() {
                if (!this.backendConnected) {
                    alert('❌ Backend not connected. Please start darwin_backend.py first.');
                    return;
                }

                const symbol = document.getElementById('symbol').value;
                const timeframe = document.getElementById('timeframe').value;
                const period = document.getElementById('period').value;

                if (!symbol) {
                    alert('Please enter a symbol');
                    return;
                }

                const loadBtn = document.getElementById('load-data-btn');
                loadBtn.disabled = true;
                loadBtn.innerHTML = '<span class="loading"></span> Loading Market Data...';

                try {
                    const response = await fetch(`${API_BASE}/market-data`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ symbol, timeframe, period })
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.marketData = result;
                        this.displayDataInfo(result);
                        this.showStatus('success', `✅ Loaded ${result.dataPoints} data points for ${symbol}`);
                    } else {
                        throw new Error(result.error);
                    }
                } catch (error) {
                    this.showStatus('error', `❌ Failed to load data: ${error.message}`);
                } finally {
                    loadBtn.disabled = false;
                    loadBtn.innerHTML = '📊 Load Market Data';
                }
            }

            displayDataInfo(data) {
                document.getElementById('data-info').style.display = 'block';
                document.getElementById('data-details').innerHTML = `
                    <div class="metric"><span>Symbol:</span><span class="metric-value">${data.symbol}</span></div>
                    <div class="metric"><span>Data Points:</span><span class="metric-value">${data.dataPoints.toLocaleString()}</span></div>
                    <div class="metric"><span>Timeframe:</span><span class="metric-value">${data.timeframe}</span></div>
                    <div class="metric"><span>Period:</span><span class="metric-value">${data.period}</span></div>
                    <div class="status success">✅ Real market data loaded with technical indicators!</div>
                `;
            }

            loadStrategyTemplate(template) {
                const templates = {
                    meanReversion: `// Mean Reversion Strategy with Real Technical Indicators
function tradingStrategy(data, params) {
    const signals = [];
    
    for (let i = 50; i < data.length; i++) {
        const current = data[i];
        const rsi = current.rsi || 50;
        const sma20 = current.sma_20;
        const bbLower = current.bb_lower;
        const bbUpper = current.bb_upper;
        
        // Mean reversion: buy when RSI oversold and price below lower Bollinger Band
        if (rsi < 30 && current.close < bbLower && sma20) {
            signals.push({
                timestamp: current.timestamp,
                action: 'buy',
                size: 0.1,
                price: current.close
            });
        }
        // Sell when RSI overbought and price above upper Bollinger Band
        else if (rsi > 70 && current.close > bbUpper) {
            signals.push({
                timestamp: current.timestamp,
                action: 'sell',
                size: 0.1,
                price: current.close
            });
        }
    }
    
    return signals;
}`,
                    momentum: `// Momentum Strategy with Real Technical Indicators
function tradingStrategy(data, params) {
    const signals = [];
    
    for (let i = 50; i < data.length; i++) {
        const current = data[i];
        const previous = data[i-1];
        const rsi = current.rsi || 50;
        const sma20 = current.sma_20;
        const sma50 = current.sma_50;
        const macd = current.macd;
        const macdSignal = current.macd_signal;
        
        // Momentum buy: MACD crossover + RSI not overbought + price above SMA50
        if (macd > macdSignal && previous.macd <= previous.macd_signal && 
            rsi < 70 && current.close > sma50) {
            signals.push({
                timestamp: current.timestamp,
                action: 'buy',
                size: 0.15,
                price: current.close
            });
        }
        // Momentum sell: MACD crossunder + RSI not oversold + price below SMA50
        else if (macd < macdSignal && previous.macd >= previous.macd_signal && 
                 rsi > 30 && current.close < sma50) {
            signals.push({
                timestamp: current.timestamp,
                action: 'sell',
                size: 0.15,
                price: current.close
            });
        }
    }
    
    return signals;
}`,
                    bollinger: `// Bollinger Band Strategy with Real Technical Indicators
function tradingStrategy(data, params) {
    const signals = [];
    
    for (let i = 50; i < data.length; i++) {
        const current = data[i];
        const previous = data[i-1];
        const bbUpper = current.bb_upper;
        const bbLower = current.bb_lower;
        const bbMiddle = current.bb_middle;
        const rsi = current.rsi || 50;
        
        // Buy when price touches lower band and RSI confirms oversold
        if (current.close <= bbLower && previous.close > previous.bb_lower && rsi < 40) {
            signals.push({
                timestamp: current.timestamp,
                action: 'buy',
                size: 0.12,
                price: current.close
            });
        }
        // Sell when price touches upper band and RSI confirms overbought
        else if (current.close >= bbUpper && previous.close < previous.bb_upper && rsi > 60) {
            signals.push({
                timestamp: current.timestamp,
                action: 'sell',
                size: 0.12,
                price: current.close
            });
        }
    }
    
    return signals;
}`,
                    custom: `// Custom Strategy Template with Real Technical Indicators
function tradingStrategy(data, params) {
    const signals = [];
    
    // Available real technical indicators:
    // - sma_20, sma_50: Simple Moving Averages
    // - rsi: Relative Strength Index
    // - bb_upper, bb_lower, bb_middle: Bollinger Bands
    // - macd, macd_signal: MACD and Signal Line
    
    for (let i = 50; i < data.length; i++) {
        const current = data[i];
        const previous = data[i-1];
        
        // Access real indicators calculated by Python backend
        const rsi = current.rsi || 50;
        const sma20 = current.sma_20;
        const sma50 = current.sma_50;
        const bbUpper = current.bb_upper;
        const bbLower = current.bb_lower;
        const macd = current.macd;
        const macdSignal = current.macd_signal;
        
        // Example: Multi-indicator strategy
        // Buy condition: RSI oversold + price below SMA20 + MACD bullish
        if (rsi < 35 && current.close < sma20 && macd > macdSignal) {
            signals.push({
                timestamp: current.timestamp,
                action: 'buy',
                size: 0.1,
                price: current.close
            });
        }
        
        // Sell condition: RSI overbought + price above SMA20 + MACD bearish
        if (rsi > 65 && current.close > sma20 && macd < macdSignal) {
            signals.push({
                timestamp: current.timestamp,
                action: 'sell',
                size: 0.1,
                price: current.close
            });
        }
    }
    
    return signals;
}`
                };

                document.getElementById('strategy-code').value = templates[template];
                this.userStrategy = template;
            }

            validateStrategy() {
                const code = document.getElementById('strategy-code').value;
                const validationDiv = document.getElementById('strategy-validation');
                
                try {
                    // Basic syntax validation
                    new Function(code);
                    
                    // Check for required function
                    if (!code.includes('function tradingStrategy')) {
                        throw new Error('Strategy must include a tradingStrategy function');
                    }
                    
                    validationDiv.className = 'status success';
                    validationDiv.textContent = '✅ Strategy validation passed! Ready for backtesting with real data.';
                    validationDiv.style.display = 'block';
                    
                } catch (error) {
                    validationDiv.className = 'status error';
                    validationDiv.textContent = '❌ Strategy validation failed: ' + error.message;
                    validationDiv.style.display = 'block';
                }
            }

            async startVerification() {
                if (!this.backendConnected) {
                    alert('❌ Backend not connected. Please start darwin_backend.py first.');
                    return;
                }

                if (!this.marketData) {
                    alert('❌ Please load market data first.');
                    return;
                }

                if (this.isVerifying) return;
                
                this.isVerifying = true;
                document.getElementById('start-verification').disabled = true;
                document.getElementById('progress-section').style.display = 'block';
                
                const config = this.getVerificationConfig();
                const strategyCode = document.getElementById('strategy-code').value;
                
                try {
                    // Step 1: Run Backtest
                    await this.runBacktest(strategyCode, config);
                    
                    // Step 2: Run Monte Carlo
                    await this.runMonteCarlo(strategyCode, config);
                    
                    // Complete verification
                    this.generateFinalResults();
                    
                } catch (error) {
                    this.showStatus('error', `❌ Verification failed: ${error.message}`);
                } finally {
                    this.isVerifying = false;
                    document.getElementById('start-verification').disabled = false;
                }
            }

            async runBacktest(strategyCode, config) {
                this.updateProgress('Running Professional Backtest...', 25);
                
                const response = await fetch(`${API_BASE}/backtest`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        symbol: this.marketData.symbol,
                        strategyCode,
                        config
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    this.verificationResults['backtest'] = result;
                    this.addLiveResult('Professional Backtest', result);
                } else {
                    throw new Error(result.error);
                }
            }

            async runMonteCarlo(strategyCode, config) {
                this.updateProgress('Running Monte Carlo Simulation...', 75);
                
                const simulations = parseInt(document.getElementById('monte-carlo-sims').value);
                
                const response = await fetch(`${API_BASE}/monte-carlo`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        symbol: this.marketData.symbol,
                        strategyCode,
                        config,
                        simulations
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    this.verificationResults['monteCarlo'] = result;
                    this.addLiveResult('Monte Carlo Analysis', result);
                } else {
                    throw new Error(result.error);
                }
            }

            getVerificationConfig() {
                return {
                    initialCapital: parseFloat(document.getElementById('initial-capital').value),
                    commission: parseFloat(document.getElementById('commission').value),
                    spread: parseFloat(document.getElementById('spread').value),
                    maxRisk: parseFloat(document.getElementById('max-risk').value),
                    rsiPeriod: parseInt(document.getElementById('rsi-period').value),
                    smaPeriod: parseInt(document.getElementById('sma-period').value),
                    bbPeriod: parseInt(document.getElementById('bb-period').value)
                };
            }

            updateProgress(message, percent) {
                document.getElementById('current-test').textContent = message;
                document.getElementById('progress-percent').textContent = percent + '%';
                document.getElementById('main-progress').style.width = percent + '%';
            }

            addLiveResult(testName, result) {
                const resultDiv = document.createElement('div');
                resultDiv.className = 'result-card';
                
                let content = `<h4>${testName}</h4>`;
                
                if (testName === 'Professional Backtest') {
                    content += `
                        <div class="metric"><span>Total Trades:</span><span class="metric-value">${result.totalTrades}</span></div>
                        <div class="metric"><span>Win Rate:</span><span class="metric-value">${result.winRate}%</span></div>
                        <div class="metric"><span>Total Return:</span><span class="metric-value">${result.totalReturn}%</span></div>
                        <div class="metric"><span>Sharpe Ratio:</span><span class="metric-value">${result.sharpeRatio}</span></div>
                        <div class="metric"><span>Max Drawdown:</span><span class="metric-value">${result.maxDrawdown}%</span></div>
                    `;
                } else if (testName === 'Monte Carlo Analysis') {
                    content += `
                        <div class="metric"><span>Simulations:</span><span class="metric-value">${result.simulations}</span></div>
                        <div class="metric"><span>Success Rate:</span><span class="metric-value">${result.successRate}%</span></div>
                        <div class="metric"><span>Mean Return:</span><span class="metric-value">${result.meanReturn}%</span></div>
                        <div class="metric"><span>Worst Case:</span><span class="metric-value">${result.worstCase}%</span></div>
                        <div class="metric"><span>Best Case:</span><span class="metric-value">${result.bestCase}%</span></div>
                    `;
                }

                resultDiv.innerHTML = content;
                document.getElementById('live-results').appendChild(resultDiv);
            }

            generateFinalResults() {
                this.updateProgress('Verification Complete!', 100);
                
                const backtest = this.verificationResults.backtest;
                const monteCarlo = this.verificationResults.monteCarlo;
                
                // Calculate overall score
                let score = 0;
                if (backtest.totalReturn > 0) score += 25;
                if (backtest.winRate > 50) score += 25;
                if (backtest.sharpeRatio > 1) score += 25;
                if (monteCarlo.successRate > 60) score += 25;
                
                // Generate executive summary
                const execSummary = `
                    <div class="status ${score >= 75 ? 'success' : score >= 50 ? 'warning' : 'error'}">
                        Overall Verification Score: ${score}/100
                        ${score >= 75 ? '🏆 STRATEGY VERIFIED' : score >= 50 ? '⚠️ NEEDS IMPROVEMENT' : '❌ STRATEGY FAILED'}
                    </div>
                    <p style="margin-top: 15px;">
                        Your trading strategy has been verified using real market data from ${this.marketData.symbol}. 
                        The analysis included professional backtesting with ${backtest.totalTrades} trades and 
                        ${monteCarlo.simulations} Monte Carlo simulations for robustness testing.
                    </p>
                `;

                document.getElementById('executive-summary').innerHTML = execSummary;
                document.getElementById('final-results').style.display = 'block';
                document.getElementById('no-results').style.display = 'none';
                
                // Switch to results tab
                this.showTab('results');
            }

            showStatus(type, message) {
                // Create temporary status message
                const statusDiv = document.createElement('div');
                statusDiv.className = `status ${type}`;
                statusDiv.textContent = message;
                statusDiv.style.position = 'fixed';
                statusDiv.style.top = '20px';
                statusDiv.style.right = '20px';
                statusDiv.style.zIndex = '1000';
                statusDiv.style.maxWidth = '400px';
                
                document.body.appendChild(statusDiv);
                
                setTimeout(() => {
                    document.body.removeChild(statusDiv);
                }, 5000);
            }

            // Tab Management
            showTab(tabName) {
                // Hide all tab contents
                document.querySelectorAll('.tab-content').forEach(tab => {
                    tab.classList.remove('active');
                });
                
                // Remove active class from all tabs
                document.querySelectorAll('.tab').forEach(tab => {
                    tab.classList.remove('active');
                });
                
                // Show selected tab content
                document.getElementById(tabName + '-tab').classList.add('active');
                
                // Add active class to selected tab
                document.querySelector(`[onclick="showTab('${tabName}')"]`).classList.add('active');
                
                this.currentTab = tabName;
            }

            // Export functions
            exportReport(format) {
                if (format === 'json') {
                    const data = {
                        marketData: this.marketData,
                        verificationResults: this.verificationResults,
                        timestamp: new Date().toISOString()
                    };
                    
                    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                    this.downloadFile(blob, 'darwin-verification-results.json');
                } else if (format === 'pdf') {
                    this.generatePDFReport();
                } else if (format === 'excel') {
                    this.generateExcelReport();
                }
            }

            generatePDFReport() {
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF();
                
                // Title
                doc.setFontSize(20);
                doc.text('Darwin Strategy Verification Report', 20, 30);
                
                // Market Data Info
                doc.setFontSize(14);
                doc.text('Market Data:', 20, 50);
                doc.setFontSize(12);
                doc.text(`Symbol: ${this.marketData.symbol}`, 20, 65);
                doc.text(`Data Points: ${this.marketData.dataPoints}`, 20, 75);
                doc.text(`Timeframe: ${this.marketData.timeframe}`, 20, 85);
                
                // Backtest Results
                const backtest = this.verificationResults.backtest;
                if (backtest) {
                    doc.setFontSize(14);
                    doc.text('Backtest Results:', 20, 110);
                    doc.setFontSize(12);
                    doc.text(`Total Return: ${backtest.totalReturn}%`, 20, 125);
                    doc.text(`Win Rate: ${backtest.winRate}%`, 20, 135);
                    doc.text(`Sharpe Ratio: ${backtest.sharpeRatio}`, 20, 145);
                    doc.text(`Max Drawdown: ${backtest.maxDrawdown}%`, 20, 155);
                }
                
                // Monte Carlo Results
                const monteCarlo = this.verificationResults.monteCarlo;
                if (monteCarlo) {
                    doc.setFontSize(14);
                    doc.text('Monte Carlo Analysis:', 20, 180);
                    doc.setFontSize(12);
                    doc.text(`Simulations: ${monteCarlo.simulations}`, 20, 195);
                    doc.text(`Success Rate: ${monteCarlo.successRate}%`, 20, 205);
                    doc.text(`Mean Return: ${monteCarlo.meanReturn}%`, 20, 215);
                }
                
                doc.save('darwin-strategy-report.pdf');
            }

            generateExcelReport() {
                let csvContent = "Darwin Strategy Verification Report\n\n";
                csvContent += `Symbol,${this.marketData.symbol}\n`;
                csvContent += `Data Points,${this.marketData.dataPoints}\n`;
                csvContent += `Timeframe,${this.marketData.timeframe}\n\n`;
                
                const backtest = this.verificationResults.backtest;
                if (backtest) {
                    csvContent += "Backtest Results\n";
                    csvContent += `Total Return,${backtest.totalReturn}%\n`;
                    csvContent += `Win Rate,${backtest.winRate}%\n`;
                    csvContent += `Sharpe Ratio,${backtest.sharpeRatio}\n`;
                    csvContent += `Max Drawdown,${backtest.maxDrawdown}%\n\n`;
                }
                
                const blob = new Blob([csvContent], { type: 'text/csv' });
                this.downloadFile(blob, 'darwin-strategy-data.csv');
            }

            downloadFile(blob, filename) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }

            shareResults() {
                const summary = this.createTextSummary();
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(summary).then(() => {
                        alert('✅ Results copied to clipboard!');
                    });
                } else {
                    alert('📋 Copy this summary:\n\n' + summary);
                }
            }

            createTextSummary() {
                const backtest = this.verificationResults.backtest;
                const monteCarlo = this.verificationResults.monteCarlo;
                
                return `🧬 DARWIN STRATEGY VERIFICATION RESULTS
${'='.repeat(50)}

📊 Symbol: ${this.marketData.symbol}
📈 Data Points: ${this.marketData.dataPoints}
⏰ Timeframe: ${this.marketData.timeframe}

💰 BACKTEST RESULTS:
- Total Return: ${backtest?.totalReturn || 'N/A'}%
- Win Rate: ${backtest?.winRate || 'N/A'}%
- Sharpe Ratio: ${backtest?.sharpeRatio || 'N/A'}
- Max Drawdown: ${backtest?.maxDrawdown || 'N/A'}%

🎲 MONTE CARLO ANALYSIS:
- Simulations: ${monteCarlo?.simulations || 'N/A'}
- Success Rate: ${monteCarlo?.successRate || 'N/A'}%
- Mean Return: ${monteCarlo?.meanReturn || 'N/A'}%

Generated by Darwin Strategy Verification Platform`;
            }

            generateDemoResults() {
                // Generate demo results for testing
                this.verificationResults = {
                    backtest: {
                        totalTrades: 156,
                        winRate: 64.1,
                        totalReturn: 12.8,
                        sharpeRatio: 1.34,
                        maxDrawdown: 5.2
                    },
                    monteCarlo: {
                        simulations: 1000,
                        successRate: 71.2,
                        meanReturn: 13.1,
                        worstCase: -8.3,
                        bestCase: 34.7
                    }
                };
                
                this.marketData = {
                    symbol: 'DEMO',
                    dataPoints: 8760,
                    timeframe: '1h'
                };
                
                this.generateFinalResults();
                alert('✅ Demo results generated! Try the export functions.');
            }
        }

        // Initialize platform
        const platform = new DarwinPlatform();

        // Global functions for HTML event handlers
        function showTab(tabName) {
            platform.showTab(tabName);
        }

        function selectDataSource(source) {
            document.querySelectorAll('.data-source-card').forEach(card => {
                card.classList.remove('selected');
            });
            event.target.closest('.data-source-card').classList.add('selected');
        }

        function setSymbol(symbol) {
            document.getElementById('symbol').value = symbol;
        }

        function loadMarketData() {
            platform.loadMarketData();
        }

        function loadStrategyTemplate(template) {
            platform.loadStrategyTemplate(template);
        }

        function validateStrategy() {
            platform.validateStrategy();
        }

        function saveStrategy() {
            const code = document.getElementById('strategy-code').value;
            localStorage.setItem('darwin-strategy', code);
            alert('✅ Strategy saved locally!');
        }

        function testBackendConnection() {
            platform.checkBackendConnection();
        }

        function startVerification() {
            platform.startVerification();
        }

        function exportReport(format) {
            platform.exportReport(format);
        }

        function shareResults() {
            platform.shareResults();
        }

        function generateDemoResults() {
            platform.generateDemoResults();
        }
    </script>
</body>
</html>