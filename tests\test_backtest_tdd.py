"""
TDD tests for the core Backtesting Engine functionality
Focusing on the most critical components for the lean MVP
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime
from unittest.mock import patch, MagicMock

# Import the backtesting components
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src', 'trading'))

from backtest import (
    BacktestEngine, TradingStrategy, BacktestConfig, BacktestResult,
    BacktestStatus, DataIntegrityError, StrategyError
)


class TestBacktestEngineTDD:
    """
    TDD tests for the core functionality of the Backtesting Engine
    """
    
    @pytest.fixture
    def simple_data(self):
        """Simple price data for testing"""
        return pd.DataFrame({
            'close': [100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110]
        })
    
    @pytest.fixture
    def simple_strategy(self):
        """Simple strategy that generates buy/sell signals"""
        class SimpleStrategy(TradingStrategy):
            def generate_signals(self, data):
                signals = pd.Series(0, index=data.index)
                signals.iloc[1] = 1    # Buy signal
                signals.iloc[5] = -1   # Sell signal
                signals.iloc[8] = 1    # Buy signal
                return signals
        
        return SimpleStrategy()
    
    def test_backtest_engine_initialization(self):
        """Test that the backtest engine initializes correctly"""
        engine = BacktestEngine()
        
        # Verify initial state
        assert engine.trades == []
        assert engine.equity_curve == []
        assert engine.current_position == 0.0
        assert engine.current_capital == 0.0
    
    def test_backtest_run_returns_result_object(self, simple_data, simple_strategy):
        """Test that the run method returns a BacktestResult object"""
        engine = BacktestEngine()
        result = engine.run(simple_data, simple_strategy, 10000)
        
        assert isinstance(result, BacktestResult)
        assert result.status == BacktestStatus.COMPLETED
    
    def test_backtest_generates_trades(self, simple_data, simple_strategy):
        """Test that the backtest generates trades based on signals"""
        engine = BacktestEngine()
        result = engine.run(simple_data, simple_strategy, 10000)
        
        # Should have at least 2 trades (buy and sell)
        assert len(result.trades) >= 2
        
        # Verify trade properties
        for trade in result.trades:
            assert trade.action in ['BUY', 'SELL']
            assert trade.quantity > 0
            assert trade.price > 0
    
    def test_backtest_calculates_pnl(self, simple_data, simple_strategy):
        """Test that the backtest calculates P&L correctly"""
        engine = BacktestEngine()
        result = engine.run(simple_data, simple_strategy, 10000)
        
        # Verify metrics are calculated
        assert result.metrics is not None
        assert hasattr(result.metrics, 'total_return')
        
        # In our simple uptrend data with buy-sell strategy, return should be positive
        assert result.metrics.total_return > 0
    
    def test_backtest_respects_initial_capital(self, simple_data, simple_strategy):
        """Test that the backtest respects the initial capital"""
        engine = BacktestEngine()
        
        # Run with different initial capitals
        result1 = engine.run(simple_data, simple_strategy, 10000)
        result2 = engine.run(simple_data, simple_strategy, 20000)
        
        # Verify initial equity matches initial capital
        assert result1.equity_curve[0]['equity'] == 10000
        assert result2.equity_curve[0]['equity'] == 20000
        
        # Position sizes should be proportional to capital
        assert result2.trades[0].quantity > result1.trades[0].quantity
    
    def test_backtest_handles_commission_and_slippage(self, simple_data, simple_strategy):
        """Test that the backtest handles commission and slippage correctly"""
        engine = BacktestEngine()
        
        # Run with and without commission/slippage
        config1 = BacktestConfig(initial_capital=10000, commission=0, slippage=0)
        config2 = BacktestConfig(initial_capital=10000, commission=0.01, slippage=0.005)
        
        result1 = engine.run(simple_data, simple_strategy, config1)
        result2 = engine.run(simple_data, simple_strategy, config2)
        
        # Return should be higher without commission/slippage
        assert result1.metrics.total_return > result2.metrics.total_return
        
        # Verify trades have commission and slippage
        for trade in result2.trades:
            assert trade.commission > 0
            assert trade.slippage >= 0
    
    def test_backtest_generates_equity_curve(self, simple_data, simple_strategy):
        """Test that the backtest generates an equity curve"""
        engine = BacktestEngine()
        result = engine.run(simple_data, simple_strategy, 10000)
        
        # Equity curve should have data points for each day plus initial point
        assert len(result.equity_curve) == len(simple_data) + 1
        
        # Verify equity curve structure
        for point in result.equity_curve:
            assert 'timestamp' in point
            assert 'equity' in point
            assert 'position' in point
            assert 'cash' in point
    
    def test_backtest_calculates_key_metrics(self, simple_data, simple_strategy):
        """Test that the backtest calculates key metrics"""
        engine = BacktestEngine()
        result = engine.run(simple_data, simple_strategy, 10000)
        
        # Verify key metrics are calculated
        metrics = result.metrics
        assert hasattr(metrics, 'total_return')
        assert hasattr(metrics, 'sharpe_ratio')
        assert hasattr(metrics, 'max_drawdown')
        assert hasattr(metrics, 'win_rate')
        
        # Verify metrics are reasonable
        assert metrics.total_return != 0
        assert -100 <= metrics.max_drawdown <= 0
        assert 0 <= metrics.win_rate <= 100
    
    def test_backtest_handles_data_validation(self, simple_strategy):
        """Test that the backtest validates input data"""
        engine = BacktestEngine()
        
        # Test with invalid data
        with pytest.raises(DataIntegrityError):
            engine.run(None, simple_strategy, 10000)
        
        with pytest.raises(DataIntegrityError):
            engine.run(pd.DataFrame(), simple_strategy, 10000)
        
        with pytest.raises(DataIntegrityError):
            engine.run(pd.DataFrame({'wrong_column': [1, 2, 3]}), simple_strategy, 10000)
    
    def test_backtest_handles_strategy_errors(self, simple_data):
        """Test that the backtest handles strategy errors"""
        class BrokenStrategy(TradingStrategy):
            def generate_signals(self, data):
                raise StrategyError("Strategy calculation failed")
        
        engine = BacktestEngine()
        result = engine.run(simple_data, BrokenStrategy(), 10000)
        
        assert result.status == BacktestStatus.FAILED
        assert result.error_message is not None
        assert "Strategy calculation failed" in result.error_message
    
    def test_backtest_with_no_trades(self, simple_data):
        """Test backtest behavior when no trades are generated"""
        class NoTradeStrategy(TradingStrategy):
            def generate_signals(self, data):
                return pd.Series(0, index=data.index)  # No signals
        
        engine = BacktestEngine()
        result = engine.run(simple_data, NoTradeStrategy(), 10000)
        
        assert result.status == BacktestStatus.COMPLETED
        assert len(result.trades) == 0
        assert result.metrics is not None
        assert result.metrics.total_return == 0
        assert result.metrics.total_trades == 0
    
    def test_backtest_with_all_capital_usage(self, simple_data):
        """Test backtest behavior when using all available capital"""
        class AggressiveStrategy(TradingStrategy):
            def generate_signals(self, data):
                return pd.Series(1, index=data.index)  # Always buy
            
            def get_position_size(self, signal, current_price, available_capital):
                return available_capital / current_price if current_price > 0 else 0
        
        engine = BacktestEngine()
        config = BacktestConfig(initial_capital=10000, max_position_size=1.0)
        result = engine.run(simple_data, AggressiveStrategy(), config)
        
        # Should use most of the capital for position
        last_point = result.equity_curve[-1]
        assert last_point['cash'] < 1000  # Most capital should be in position
        assert last_point['position'] > 0  # Should have a position
    
    def test_backtest_with_position_size_limit(self, simple_data):
        """Test that position size limits are respected"""
        class AggressiveStrategy(TradingStrategy):
            def generate_signals(self, data):
                return pd.Series(1, index=data.index)  # Always buy
            
            def get_position_size(self, signal, current_price, available_capital):
                return available_capital / current_price if current_price > 0 else 0
        
        engine = BacktestEngine()
        config = BacktestConfig(initial_capital=10000, max_position_size=0.5)  # 50% max
        result = engine.run(simple_data, AggressiveStrategy(), config)
        
        # Position value should not exceed 50% of portfolio
        for point in result.equity_curve:
            if 'price' in point and point['price'] > 0:
                position_value = point['position'] * point['price']
                portfolio_value = point['equity']
                position_ratio = position_value / portfolio_value if portfolio_value > 0 else 0
                assert position_ratio <= 0.51  # Allow small tolerance for rounding
    
    def test_backtest_with_different_strategies(self, simple_data):
        """Test backtest with different strategies on same data"""
        class BuyStrategy(TradingStrategy):
            def generate_signals(self, data):
                signals = pd.Series(0, index=data.index)
                signals.iloc[1] = 1  # Buy signal
                return signals
        
        class SellStrategy(TradingStrategy):
            def generate_signals(self, data):
                signals = pd.Series(0, index=data.index)
                signals.iloc[1] = -1  # Sell signal
                return signals
        
        engine = BacktestEngine()
        buy_result = engine.run(simple_data, BuyStrategy(), 10000)
        sell_result = engine.run(simple_data, SellStrategy(), 10000)
        
        # Buy strategy should have different trades than sell strategy
        assert buy_result.trades[0].action != sell_result.trades[0].action
        
        # In uptrend data, buy strategy should outperform sell strategy
        assert buy_result.metrics.total_return > sell_result.metrics.total_return


if __name__ == "__main__":
    pytest.main(["-v", __file__])