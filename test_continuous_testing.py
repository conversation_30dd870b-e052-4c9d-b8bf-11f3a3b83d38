#!/usr/bin/env python3
"""
Test runner for Continuous Testing Enhancement Plan
Verify property-based testing and mutation testing setup
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timezone

# Add paths
sys.path.append('tests')
sys.path.append(os.path.join('src', 'data'))

def test_financial_data_generator():
    """Test financial data generator"""
    print("🧪 Testing Financial Data Generator...")
    
    try:
        from generators.financial_data import generate_financial_data
        from hypothesis import strategies as st
        
        # Generate sample data
        data_strategy = generate_financial_data(min_records=50, max_records=100)
        sample_data = data_strategy.example()
        
        print(f"   ✅ Generated {len(sample_data)} records")
        print(f"   ✅ Columns: {list(sample_data.columns)}")
        
        # Verify OHLC relationships
        ohlc_valid = (
            (sample_data['high'] >= sample_data['low']).all() and
            (sample_data['high'] >= sample_data['open']).all() and
            (sample_data['high'] >= sample_data['close']).all() and
            (sample_data['low'] <= sample_data['open']).all() and
            (sample_data['low'] <= sample_data['close']).all()
        )
        
        print(f"   ✅ OHLC relationships valid: {ohlc_valid}")
        
        # Verify positive prices
        positive_prices = (
            (sample_data['open'] > 0).all() and
            (sample_data['high'] > 0).all() and
            (sample_data['low'] > 0).all() and
            (sample_data['close'] > 0).all()
        )
        
        print(f"   ✅ All prices positive: {positive_prices}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_strategy_parameter_generator():
    """Test strategy parameter generator"""
    print("\n🧪 Testing Strategy Parameter Generator...")
    
    try:
        from generators.financial_data import generate_strategy_parameters
        from hypothesis import strategies as st
        
        # Test RSI parameters
        rsi_strategy = generate_strategy_parameters('RSI')
        rsi_params = rsi_strategy.example()
        
        print(f"   ✅ RSI parameters: {rsi_params}")
        
        # Verify RSI parameter constraints
        rsi_valid = (
            rsi_params['overbought'] > rsi_params['oversold'] and
            0 <= rsi_params['oversold'] <= 100 and
            0 <= rsi_params['overbought'] <= 100 and
            rsi_params['period'] > 0
        )
        
        print(f"   ✅ RSI parameters valid: {rsi_valid}")
        
        # Test MACD parameters
        macd_strategy = generate_strategy_parameters('MACD')
        macd_params = macd_strategy.example()
        
        print(f"   ✅ MACD parameters: {macd_params}")
        
        # Verify MACD parameter constraints
        macd_valid = (
            macd_params['fast_period'] < macd_params['slow_period'] and
            macd_params['fast_period'] > 0 and
            macd_params['slow_period'] > 0 and
            macd_params['signal_period'] > 0
        )
        
        print(f"   ✅ MACD parameters valid: {macd_valid}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_property_based_validation():
    """Test property-based validation concepts"""
    print("\n🧪 Testing Property-Based Validation...")
    
    try:
        from hypothesis import given, strategies as st
        
        # Test with simple data
        @given(
            prices=st.lists(
                st.floats(min_value=0.1, max_value=100.0),
                min_size=10,
                max_size=50
            )
        )
        def test_price_properties(prices):
            # Property: All prices should be positive
            assert all(p > 0 for p in prices)
            
            # Property: Price list should not be empty
            assert len(prices) > 0
            
            # Property: Prices should be finite
            assert all(np.isfinite(p) for p in prices)
        
        # Run the property-based test
        test_price_properties()
        
        print("   ✅ Property-based validation working")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_data_integrity_with_properties():
    """Test data integrity validation with property-based approach"""
    print("\n🧪 Testing Data Integrity with Properties...")
    
    try:
        # Generate test data manually
        dates = pd.date_range('2023-01-01', periods=100, freq='h')
        np.random.seed(42)
        
        data = pd.DataFrame({
            'open': np.random.uniform(1.0, 2.0, 100),
            'high': np.random.uniform(1.1, 2.1, 100),
            'low': np.random.uniform(0.9, 1.9, 100),
            'close': np.random.uniform(1.0, 2.0, 100),
            'volume': np.random.randint(1000, 100000, 100)
        }, index=dates)
        
        # Fix OHLC relationships
        for i in range(len(data)):
            prices = [data.iloc[i]['open'], data.iloc[i]['close']]
            data.iloc[i, data.columns.get_loc('high')] = max(prices) * 1.01
            data.iloc[i, data.columns.get_loc('low')] = min(prices) * 0.99
        
        print(f"   ✅ Generated test data: {len(data)} records")
        
        # Test data integrity properties
        try:
            from data_loader import DataValidator, ValidationLevel
            
            validator = DataValidator(ValidationLevel.BASIC)
            report = validator.validate_data(data, "PROPERTY_TEST")
            
            print(f"   ✅ Validation completed: {report.is_valid()}")
            print(f"   ✅ Integrity score: {report.integrity_score:.2%}")
            print(f"   ✅ Checks passed: {len(report.checks_passed)}")
            
        except ImportError:
            print("   ⚠️ Data validator not available, testing properties manually")
            
            # Manual property testing
            ohlc_valid = (
                (data['high'] >= data['low']).all() and
                (data['high'] >= data['open']).all() and
                (data['high'] >= data['close']).all() and
                (data['low'] <= data['open']).all() and
                (data['low'] <= data['close']).all()
            )
            
            print(f"   ✅ OHLC properties valid: {ohlc_valid}")
            
            # Test no null values
            no_nulls = not data.isnull().any().any()
            print(f"   ✅ No null values: {no_nulls}")
            
            # Test monotonic index
            monotonic = data.index.is_monotonic_increasing
            print(f"   ✅ Monotonic index: {monotonic}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_mutation_testing_setup():
    """Test mutation testing configuration"""
    print("\n🧪 Testing Mutation Testing Setup...")
    
    try:
        from mutmut_config import (
            MUTMUT_CONFIG, 
            STRATEGY_MUTATION_TARGETS,
            get_mutation_command,
            setup_mutmut_config
        )
        
        print(f"   ✅ Mutation config loaded")
        print(f"   ✅ Paths to mutate: {len(MUTMUT_CONFIG['paths_to_mutate'])}")
        print(f"   ✅ Strategy targets: {len(STRATEGY_MUTATION_TARGETS)}")
        
        # Test command generation
        cmd = get_mutation_command()
        print(f"   ✅ Generated mutation command: {cmd[:50]}...")
        
        # Test config setup
        setup_mutmut_config()
        print(f"   ✅ Mutmut config file created")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_benchmark_setup():
    """Test benchmark configuration"""
    print("\n🧪 Testing Benchmark Setup...")
    
    try:
        # Test basic benchmark concepts
        import time
        
        def sample_function():
            """Sample function to benchmark"""
            time.sleep(0.001)  # 1ms delay
            return sum(range(1000))
        
        # Simple benchmark
        start_time = time.time()
        result = sample_function()
        end_time = time.time()
        
        execution_time = end_time - start_time
        
        print(f"   ✅ Sample function result: {result}")
        print(f"   ✅ Execution time: {execution_time*1000:.2f}ms")
        
        # Test performance assertion
        assert execution_time < 0.1, "Function should complete in <100ms"
        print(f"   ✅ Performance assertion passed")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def main():
    """Run all continuous testing tests"""
    print("🚀 Continuous Testing Enhancement Plan - Verification")
    print("=" * 60)
    
    tests = [
        test_financial_data_generator,
        test_strategy_parameter_generator,
        test_property_based_validation,
        test_data_integrity_with_properties,
        test_mutation_testing_setup,
        test_benchmark_setup
    ]
    
    results = []
    
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"   ❌ Test failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    
    passed = sum(results)
    total = len(results)
    
    print(f"   Tests Passed: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("   🎉 All tests passed! Continuous testing setup is ready.")
        print("\n🚀 Next Steps:")
        print("   1. Run property-based tests: pytest tests/test_property_based.py")
        print("   2. Run mutation testing: mutmut run --paths-to-mutate=src/")
        print("   3. Run benchmarks: pytest tests/test_benchmarks.py --benchmark-only")
        print("   4. Generate reports: mutmut html")
    else:
        print("   ⚠️ Some tests failed. Please check the implementation.")
    
    print(f"\n✨ Continuous Testing Enhancement Plan Status: {'READY' if passed == total else 'NEEDS ATTENTION'}")

if __name__ == "__main__":
    main()