#!/usr/bin/env python3
"""
Darwin Gödel Machine (DGM) Optimizer with Cryptographic Audit Trails
Advanced evolutionary optimization with mathematical rigor and complete auditability.
"""

import hashlib
import hmac
import json
import time
import uuid
from contextlib import contextmanager
from dataclasses import dataclass, field
from datetime import datetime, timezone
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, List, Optional, Type, Tuple, Iterator, Callable
import logging
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import numpy as np
import pandas as pd
from abc import ABC, abstractmethod

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class OptimizationStatus(Enum):
    """Optimization execution status"""
    INITIALIZING = "initializing"
    RUNNING = "running"
    CONVERGED = "converged"
    COMPLETED = "completed"
    FAILED = "failed"
    TERMINATED = "terminated"


class SelectionMethod(Enum):
    """Genetic algorithm selection methods"""
    TOURNAMENT = "tournament"
    ROULETTE = "roulette"
    RANK = "rank"
    ELITIST = "elitist"


class MutationStrategy(Enum):
    """Parameter mutation strategies"""
    GAUSSIAN = "gaussian"
    UNIFORM = "uniform"
    ADAPTIVE = "adaptive"
    CAUCHY = "cauchy"


@dataclass
class ParameterSpace:
    """Parameter space definition for optimization"""
    name: str
    min_value: float
    max_value: float
    param_type: str = "float"  # float, int, categorical
    categories: Optional[List[Any]] = None
    step_size: Optional[float] = None
    
    def __post_init__(self):
        if self.param_type == "categorical" and not self.categories:
            raise ValueError("Categories must be provided for categorical parameters")
    
    def generate_random_value(self) -> Any:
        """Generate random value within parameter space"""
        if self.param_type == "categorical":
            return np.random.choice(self.categories)
        elif self.param_type == "int":
            return np.random.randint(int(self.min_value), int(self.max_value) + 1)
        else:  # float
            return np.random.uniform(self.min_value, self.max_value)
    
    def mutate_value(self, current_value: Any, mutation_rate: float = 0.1) -> Any:
        """Mutate parameter value"""
        if self.param_type == "categorical":
            if np.random.random() < mutation_rate:
                return np.random.choice(self.categories)
            return current_value
        elif self.param_type == "int":
            if np.random.random() < mutation_rate:
                mutation = int(np.random.normal(0, (self.max_value - self.min_value) * 0.1))
                new_value = int(current_value) + mutation
                return max(int(self.min_value), min(int(self.max_value), new_value))
            return current_value
        else:  # float
            if np.random.random() < mutation_rate:
                mutation = np.random.normal(0, (self.max_value - self.min_value) * 0.1)
                new_value = float(current_value) + mutation
                return max(self.min_value, min(self.max_value, new_value))
            return current_value


@dataclass
class Individual:
    """Individual in genetic algorithm population"""
    parameters: Dict[str, Any]
    id: str = ""
    fitness: Optional[float] = None
    generation: int = 0
    parent_ids: List[str] = field(default_factory=list)
    mutation_history: List[Dict[str, Any]] = field(default_factory=list)
    evaluation_time: Optional[datetime] = None
    
    def __post_init__(self):
        if not self.id:
            self.id = f"IND_{uuid.uuid4().hex[:8]}"
    
    def get_hash(self) -> str:
        """Generate cryptographic hash of individual"""
        individual_data = {
            'id': self.id,
            'parameters': self.parameters,
            'fitness': self.fitness,
            'generation': self.generation,
            'parent_ids': sorted(self.parent_ids)
        }
        individual_json = json.dumps(individual_data, sort_keys=True, default=str)
        return hashlib.sha256(individual_json.encode()).hexdigest()


@dataclass
class GenerationResult:
    """Results from a single generation"""
    generation: int
    population: List[Individual]
    best_individual: Individual
    average_fitness: float
    fitness_std: float
    convergence_metric: float
    execution_time: float
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    def get_hash(self) -> str:
        """Generate cryptographic hash of generation result"""
        generation_data = {
            'generation': self.generation,
            'population_hashes': [ind.get_hash() for ind in self.population],
            'best_fitness': self.best_individual.fitness,
            'average_fitness': self.average_fitness,
            'convergence_metric': self.convergence_metric
        }
        generation_json = json.dumps(generation_data, sort_keys=True)
        return hashlib.sha256(generation_json.encode()).hexdigest()


@dataclass
class AuditEntry:
    """Single audit trail entry"""
    timestamp: datetime
    operation: str
    generation: Optional[int]
    individual_id: Optional[str]
    parameters: Optional[Dict[str, Any]]
    fitness: Optional[float]
    metadata: Dict[str, Any]
    entry_hash: Optional[str] = None
    
    def __post_init__(self):
        if not self.entry_hash:
            self.entry_hash = self._generate_hash()
    
    def _generate_hash(self) -> str:
        """Generate cryptographic hash of audit entry"""
        entry_data = {
            'timestamp': self.timestamp.isoformat(),
            'operation': self.operation,
            'generation': self.generation,
            'individual_id': self.individual_id,
            'parameters': self.parameters,
            'fitness': self.fitness,
            'metadata': self.metadata
        }
        entry_json = json.dumps(entry_data, sort_keys=True, default=str)
        return hashlib.sha256(entry_json.encode()).hexdigest()
    
    def verify_integrity(self) -> bool:
        """Verify audit entry integrity"""
        expected_hash = self._generate_hash()
        return hmac.compare_digest(self.entry_hash or "", expected_hash)


class AuditTrail:
    """Cryptographic audit trail for optimization process"""
    
    def __init__(self, secret_key: str = "dgm_audit_2024"):
        self.secret_key = secret_key
        self.entries: List[AuditEntry] = []
        self.start_time = datetime.now(timezone.utc)
        self.session_id = f"DGM_{uuid.uuid4().hex[:12]}"
        self._lock = threading.Lock()
        
        # Log session start
        self.log_operation("session_start", metadata={
            'session_id': self.session_id,
            'start_time': self.start_time.isoformat()
        })
    
    def log_operation(self, operation: str, generation: Optional[int] = None,
                     individual_id: Optional[str] = None, parameters: Optional[Dict[str, Any]] = None,
                     fitness: Optional[float] = None, **metadata) -> None:
        """Log operation to audit trail"""
        with self._lock:
            entry = AuditEntry(
                timestamp=datetime.now(timezone.utc),
                operation=operation,
                generation=generation,
                individual_id=individual_id,
                parameters=parameters,
                fitness=fitness,
                metadata=metadata
            )
            self.entries.append(entry)
            logger.debug(f"Audit: {operation} - Gen {generation} - {individual_id}")
    
    def log_generation(self, generation: int, population: List[Individual],
                      best_individual: Individual, convergence_metric: float) -> None:
        """Log complete generation results"""
        self.log_operation(
            "generation_complete",
            generation=generation,
            individual_id=best_individual.id,
            fitness=best_individual.fitness,
            population_size=len(population),
            best_fitness=best_individual.fitness,
            convergence_metric=convergence_metric,
            population_hashes=[ind.get_hash() for ind in population]
        )
    
    def log_individual_evaluation(self, individual: Individual) -> None:
        """Log individual evaluation"""
        self.log_operation(
            "individual_evaluation",
            generation=individual.generation,
            individual_id=individual.id,
            parameters=individual.parameters,
            fitness=individual.fitness,
            parent_ids=individual.parent_ids
        )
    
    def log_selection(self, selected_individuals: List[Individual], method: SelectionMethod) -> None:
        """Log selection operation"""
        self.log_operation(
            "selection",
            selected_count=len(selected_individuals),
            selection_method=method.value,
            selected_ids=[ind.id for ind in selected_individuals]
        )
    
    def log_crossover(self, parent1: Individual, parent2: Individual, offspring: Individual) -> None:
        """Log crossover operation"""
        self.log_operation(
            "crossover",
            generation=offspring.generation,
            individual_id=offspring.id,
            parameters=offspring.parameters,
            parent1_id=parent1.id,
            parent2_id=parent2.id,
            parent1_fitness=parent1.fitness,
            parent2_fitness=parent2.fitness
        )
    
    def log_mutation(self, individual: Individual, original_params: Dict[str, Any]) -> None:
        """Log mutation operation"""
        self.log_operation(
            "mutation",
            generation=individual.generation,
            individual_id=individual.id,
            original_parameters=original_params,
            mutated_parameters=individual.parameters
        )
    
    def get_trail(self) -> List[Dict[str, Any]]:
        """Get complete audit trail"""
        return [
            {
                'timestamp': entry.timestamp.isoformat(),
                'operation': entry.operation,
                'generation': entry.generation,
                'individual_id': entry.individual_id,
                'parameters': entry.parameters,
                'fitness': entry.fitness,
                'metadata': entry.metadata,
                'entry_hash': entry.entry_hash
            }
            for entry in self.entries
        ]
    
    def sha256_hash(self) -> str:
        """Generate SHA-256 hash of complete audit trail"""
        trail_data = {
            'session_id': self.session_id,
            'start_time': self.start_time.isoformat(),
            'entries': self.get_trail()
        }
        trail_json = json.dumps(trail_data, sort_keys=True)
        return hashlib.sha256(trail_json.encode()).hexdigest()
    
    def hmac_signature(self) -> str:
        """Generate HMAC signature of audit trail"""
        trail_hash = self.sha256_hash()
        return hmac.new(
            self.secret_key.encode(),
            trail_hash.encode(),
            hashlib.sha256
        ).hexdigest()
    
    def verify_integrity(self) -> bool:
        """Verify complete audit trail integrity"""
        # Verify each entry
        for entry in self.entries:
            if not entry.verify_integrity():
                return False
        
        # Verify trail continuity
        return self._verify_trail_continuity()
    
    def _verify_trail_continuity(self) -> bool:
        """Verify audit trail continuity and ordering"""
        if not self.entries:
            return True
        
        # Check timestamp ordering
        for i in range(1, len(self.entries)):
            if self.entries[i].timestamp < self.entries[i-1].timestamp:
                return False
        
        return True
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get audit trail statistics"""
        operations = {}
        for entry in self.entries:
            operations[entry.operation] = operations.get(entry.operation, 0) + 1
        
        return {
            'session_id': self.session_id,
            'total_entries': len(self.entries),
            'operations': operations,
            'start_time': self.start_time.isoformat(),
            'duration_seconds': (datetime.now(timezone.utc) - self.start_time).total_seconds(),
            'integrity_verified': self.verify_integrity()
        }
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.log_operation("session_end", metadata={
            'end_time': datetime.now(timezone.utc).isoformat(),
            'total_entries': len(self.entries),
            'integrity_verified': self.verify_integrity()
        })


class BaseStrategy(ABC):
    """Base class for trading strategies"""
    
    def __init__(self, parameters: Dict[str, Any]):
        self.parameters = parameters
    
    @classmethod
    @abstractmethod
    def get_params_space(cls) -> List[ParameterSpace]:
        """Get parameter space definition"""
        pass
    
    @abstractmethod
    def generate_signals(self, data: pd.DataFrame) -> pd.Series:
        """Generate trading signals"""
        pass


class BacktestResult:
    """Backtest result container"""
    
    def __init__(self, total_return: float, sharpe_ratio: float, max_drawdown: float,
                 win_rate: float, profit_factor: float, trades_count: int,
                 metadata: Optional[Dict[str, Any]] = None):
        self.total_return = total_return
        self.sharpe_ratio = sharpe_ratio
        self.max_drawdown = max_drawdown
        self.win_rate = win_rate
        self.profit_factor = profit_factor
        self.trades_count = trades_count
        self.metadata = metadata or {}
    
    def get_fitness(self) -> float:
        """Calculate fitness score from backtest results"""
        # Composite fitness function
        fitness = (
            self.total_return * 0.3 +
            self.sharpe_ratio * 0.3 +
            (1 - abs(self.max_drawdown)) * 0.2 +
            self.win_rate * 0.1 +
            min(self.profit_factor / 2.0, 1.0) * 0.1
        )
        return max(0.0, fitness)


class BacktestEngine:
    """Backtesting engine for strategy evaluation"""
    
    def __init__(self, initial_capital: float = 100000.0, commission: float = 0.001):
        self.initial_capital = initial_capital
        self.commission = commission
    
    def run(self, data: pd.DataFrame, strategy: BaseStrategy) -> BacktestResult:
        """Run backtest for strategy"""
        try:
            # Generate signals
            signals = strategy.generate_signals(data)
            
            # Simple backtest simulation
            returns = data['close'].pct_change().fillna(0)
            strategy_returns = signals.shift(1) * returns
            
            # Calculate metrics
            total_return = (1 + strategy_returns).prod() - 1
            sharpe_ratio = strategy_returns.mean() / strategy_returns.std() * np.sqrt(252) if strategy_returns.std() > 0 else 0
            
            # Calculate drawdown
            cumulative = (1 + strategy_returns).cumprod()
            running_max = cumulative.expanding().max()
            drawdown = (cumulative - running_max) / running_max
            max_drawdown = drawdown.min()
            
            # Calculate win rate and profit factor
            positive_returns = strategy_returns[strategy_returns > 0]
            negative_returns = strategy_returns[strategy_returns < 0]
            
            win_rate = len(positive_returns) / len(strategy_returns[strategy_returns != 0]) if len(strategy_returns[strategy_returns != 0]) > 0 else 0
            profit_factor = positive_returns.sum() / abs(negative_returns.sum()) if negative_returns.sum() != 0 else 1.0
            
            trades_count = len(strategy_returns[strategy_returns != 0])
            
            return BacktestResult(
                total_return=total_return,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                win_rate=win_rate,
                profit_factor=profit_factor,
                trades_count=trades_count
            )
            
        except Exception as e:
            logger.error(f"Backtest failed: {e}")
            return BacktestResult(
                total_return=-1.0,
                sharpe_ratio=-1.0,
                max_drawdown=-1.0,
                win_rate=0.0,
                profit_factor=0.0,
                trades_count=0
            )


@dataclass
class OptimizationConfig:
    """Configuration for DGM optimization"""
    population_size: int = 50
    generations: int = 100
    mutation_rate: float = 0.1
    crossover_rate: float = 0.8
    elitism_rate: float = 0.1
    selection_method: SelectionMethod = SelectionMethod.TOURNAMENT
    mutation_strategy: MutationStrategy = MutationStrategy.GAUSSIAN
    convergence_threshold: float = 1e-6
    max_stagnation_generations: int = 20
    parallel_evaluation: bool = True
    max_workers: int = 4
    random_seed: Optional[int] = None


@dataclass
class OptimizationResult:
    """Complete optimization result with audit trail"""
    best_individual: Individual
    best_parameters: Dict[str, Any]
    best_fitness: float
    optimization_status: OptimizationStatus
    generations_completed: int
    total_evaluations: int
    convergence_history: List[float]
    generation_results: List[GenerationResult]
    audit_trail: List[Dict[str, Any]]
    verification_hash: str
    hmac_signature: str
    execution_time: float
    config: OptimizationConfig
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    def verify_integrity(self) -> bool:
        """Verify optimization result integrity"""
        # Recreate hash from audit trail
        trail_data = {
            'best_parameters': self.best_parameters,
            'best_fitness': self.best_fitness,
            'generations_completed': self.generations_completed,
            'audit_trail': self.audit_trail
        }
        trail_json = json.dumps(trail_data, sort_keys=True, default=str)
        expected_hash = hashlib.sha256(trail_json.encode()).hexdigest()
        
        return hmac.compare_digest(self.verification_hash, expected_hash)


class DarwinGodelMachineOptimizer:
    """
    Darwin Gödel Machine Optimizer with Cryptographic Audit Trails
    
    Advanced evolutionary optimization system that combines:
    - Genetic algorithms with mathematical rigor
    - Complete cryptographic audit trails
    - Gödel machine self-improvement capabilities
    - Production-ready error handling and monitoring
    """
    
    def __init__(self, config: Optional[OptimizationConfig] = None):
        self.config = config or OptimizationConfig()
        self.backtest_engine = BacktestEngine()
        self._setup_random_seed()
        
        logger.info(f"DGM Optimizer initialized with config: {self.config}")
    
    def _setup_random_seed(self):
        """Setup random seed for reproducibility"""
        if self.config.random_seed is not None:
            np.random.seed(self.config.random_seed)
    
    def optimize(self, strategy_class: Type[BaseStrategy], data: pd.DataFrame) -> OptimizationResult:
        """
        Main optimization method with complete audit trail
        
        Args:
            strategy_class: Strategy class to optimize
            data: Historical data for backtesting
            
        Returns:
            OptimizationResult with complete audit trail and verification
        """
        start_time = time.time()
        
        with AuditTrail() as audit:
            try:
                audit.log_operation("optimization_start", metadata={
                    'strategy_class': strategy_class.__name__,
                    'data_shape': data.shape,
                    'config': {
                        'population_size': self.config.population_size,
                        'generations': self.config.generations,
                        'mutation_rate': self.config.mutation_rate,
                        'crossover_rate': self.config.crossover_rate,
                        'elitism_rate': self.config.elitism_rate,
                        'selection_method': self.config.selection_method.value,
                        'mutation_strategy': self.config.mutation_strategy.value,
                        'convergence_threshold': self.config.convergence_threshold,
                        'max_stagnation_generations': self.config.max_stagnation_generations,
                        'parallel_evaluation': self.config.parallel_evaluation,
                        'max_workers': self.config.max_workers,
                        'random_seed': self.config.random_seed
                    }
                })
                
                # Get parameter space
                params_space = strategy_class.get_params_space()
                audit.log_operation("parameter_space_defined", metadata={
                    'params_count': len(params_space),
                    'params_space': [
                        {
                            'name': p.name,
                            'type': p.param_type,
                            'min': p.min_value,
                            'max': p.max_value
                        }
                        for p in params_space
                    ]
                })
                
                # Initialize population
                population = self._create_initial_population(params_space, strategy_class, data, audit)
                
                # Evolution loop
                best_individual = max(population, key=lambda x: x.fitness or 0)
                convergence_history = []
                generation_results = []
                stagnation_counter = 0
                
                for generation in range(self.config.generations):
                    generation_start = time.time()
                    
                    audit.log_operation("generation_start", generation=generation)
                    
                    # Evaluate population
                    population = self._evaluate_population(population, strategy_class, data, audit)
                    
                    # Track best individual
                    current_best = max(population, key=lambda x: x.fitness or 0)
                    
                    # Calculate convergence metrics
                    fitnesses = [ind.fitness for ind in population if ind.fitness is not None]
                    avg_fitness = np.mean(fitnesses)
                    fitness_std = np.std(fitnesses)
                    
                    # Check for improvement
                    if current_best.fitness > (best_individual.fitness or 0):
                        best_individual = current_best
                        stagnation_counter = 0
                    else:
                        stagnation_counter += 1
                    
                    # Convergence check
                    convergence_metric = fitness_std / avg_fitness if avg_fitness > 0 else float('inf')
                    convergence_history.append(convergence_metric)
                    
                    # Store generation result
                    generation_time = time.time() - generation_start
                    gen_result = GenerationResult(
                        generation=generation,
                        population=population.copy(),
                        best_individual=current_best,
                        average_fitness=avg_fitness,
                        fitness_std=fitness_std,
                        convergence_metric=convergence_metric,
                        execution_time=generation_time
                    )
                    generation_results.append(gen_result)
                    
                    # Log generation completion
                    audit.log_generation(generation, population, current_best, convergence_metric)
                    
                    # Check convergence
                    if convergence_metric < self.config.convergence_threshold:
                        audit.log_operation("convergence_achieved", generation=generation,
                                          convergence_metric=convergence_metric)
                        status = OptimizationStatus.CONVERGED
                        break
                    
                    # Check stagnation
                    if stagnation_counter >= self.config.max_stagnation_generations:
                        audit.log_operation("stagnation_detected", generation=generation,
                                          stagnation_generations=stagnation_counter)
                        status = OptimizationStatus.TERMINATED
                        break
                    
                    # Create next generation
                    if generation < self.config.generations - 1:
                        population = self._create_next_generation(population, params_space, audit)
                
                else:
                    status = OptimizationStatus.COMPLETED
                
                # Finalize optimization
                execution_time = time.time() - start_time
                total_evaluations = sum(len(gr.population) for gr in generation_results)
                
                audit.log_operation("optimization_complete", metadata={
                    'status': status.value,
                    'best_fitness': best_individual.fitness,
                    'generations_completed': len(generation_results),
                    'total_evaluations': total_evaluations,
                    'execution_time': execution_time
                })
                
                # Create result
                result = OptimizationResult(
                    best_individual=best_individual,
                    best_parameters=best_individual.parameters,
                    best_fitness=best_individual.fitness or 0,
                    optimization_status=status,
                    generations_completed=len(generation_results),
                    total_evaluations=total_evaluations,
                    convergence_history=convergence_history,
                    generation_results=generation_results,
                    audit_trail=audit.get_trail(),
                    verification_hash=audit.sha256_hash(),
                    hmac_signature=audit.hmac_signature(),
                    execution_time=execution_time,
                    config=self.config
                )
                
                logger.info(f"Optimization completed: {status.value} - Best fitness: {best_individual.fitness:.6f}")
                return result
                
            except Exception as e:
                audit.log_operation("optimization_error", metadata={'error': str(e)})
                logger.error(f"Optimization failed: {e}")
                raise
    
    def _create_initial_population(self, params_space: List[ParameterSpace],
                                 strategy_class: Type[BaseStrategy],
                                 data: pd.DataFrame, audit: AuditTrail) -> List[Individual]:
        """Create initial population"""
        population = []
        
        for i in range(self.config.population_size):
            # Generate random parameters
            parameters = {}
            for param in params_space:
                parameters[param.name] = param.generate_random_value()
            
            # Create individual
            individual = Individual(
                id=f"GEN0_IND_{i:03d}",
                parameters=parameters,
                generation=0
            )
            
            population.append(individual)
            audit.log_operation("individual_created", generation=0,
                              individual_id=individual.id, parameters=parameters)
        
        audit.log_operation("initial_population_created", metadata={
            'population_size': len(population)
        })
        
        return population
    
    def _evaluate_population(self, population: List[Individual],
                           strategy_class: Type[BaseStrategy],
                           data: pd.DataFrame, audit: AuditTrail) -> List[Individual]:
        """Evaluate population fitness"""
        if self.config.parallel_evaluation:
            return self._evaluate_population_parallel(population, strategy_class, data, audit)
        else:
            return self._evaluate_population_sequential(population, strategy_class, data, audit)
    
    def _evaluate_population_sequential(self, population: List[Individual],
                                      strategy_class: Type[BaseStrategy],
                                      data: pd.DataFrame, audit: AuditTrail) -> List[Individual]:
        """Sequential population evaluation"""
        for individual in population:
            if individual.fitness is None:
                individual.fitness = self._evaluate_individual(individual, strategy_class, data)
                individual.evaluation_time = datetime.now(timezone.utc)
                audit.log_individual_evaluation(individual)
        
        return population
    
    def _evaluate_population_parallel(self, population: List[Individual],
                                    strategy_class: Type[BaseStrategy],
                                    data: pd.DataFrame, audit: AuditTrail) -> List[Individual]:
        """Parallel population evaluation"""
        unevaluated = [ind for ind in population if ind.fitness is None]
        
        if not unevaluated:
            return population
        
        with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
            # Submit evaluation tasks
            future_to_individual = {
                executor.submit(self._evaluate_individual, ind, strategy_class, data): ind
                for ind in unevaluated
            }
            
            # Collect results
            for future in as_completed(future_to_individual):
                individual = future_to_individual[future]
                try:
                    individual.fitness = future.result()
                    individual.evaluation_time = datetime.now(timezone.utc)
                    audit.log_individual_evaluation(individual)
                except Exception as e:
                    logger.error(f"Individual evaluation failed: {e}")
                    individual.fitness = 0.0
        
        return population
    
    def _evaluate_individual(self, individual: Individual,
                           strategy_class: Type[BaseStrategy],
                           data: pd.DataFrame) -> float:
        """Evaluate single individual"""
        try:
            strategy = strategy_class(individual.parameters)
            result = self.backtest_engine.run(data, strategy)
            return result.get_fitness()
        except Exception as e:
            logger.error(f"Individual {individual.id} evaluation failed: {e}")
            return 0.0
    
    def _create_next_generation(self, population: List[Individual],
                              params_space: List[ParameterSpace],
                              audit: AuditTrail) -> List[Individual]:
        """Create next generation through selection, crossover, and mutation"""
        # Sort by fitness
        population.sort(key=lambda x: x.fitness or 0, reverse=True)
        
        # Elitism - keep best individuals
        elite_count = int(self.config.population_size * self.config.elitism_rate)
        next_generation = population[:elite_count].copy()
        
        # Update generation number for elite
        for individual in next_generation:
            individual.generation += 1
        
        # Selection for breeding
        breeding_pool = self._selection(population, audit)
        
        # Generate offspring
        while len(next_generation) < self.config.population_size:
            # Select parents
            parent1, parent2 = np.random.choice(breeding_pool, 2, replace=False)
            
            # Crossover
            if np.random.random() < self.config.crossover_rate:
                offspring = self._crossover(parent1, parent2, params_space, audit)
            else:
                offspring = Individual(
                    parameters=parent1.parameters.copy(),
                    generation=parent1.generation + 1,
                    parent_ids=[parent1.id]
                )
            
            # Mutation
            if np.random.random() < self.config.mutation_rate:
                offspring = self._mutate(offspring, params_space, audit)
            
            next_generation.append(offspring)
        
        return next_generation[:self.config.population_size]
    
    def _selection(self, population: List[Individual], audit: AuditTrail) -> List[Individual]:
        """Select individuals for breeding"""
        if self.config.selection_method == SelectionMethod.TOURNAMENT:
            selected = self._tournament_selection(population)
        elif self.config.selection_method == SelectionMethod.ROULETTE:
            selected = self._roulette_selection(population)
        elif self.config.selection_method == SelectionMethod.RANK:
            selected = self._rank_selection(population)
        else:  # ELITIST
            selected = self._elitist_selection(population)
        
        audit.log_selection(selected, self.config.selection_method)
        return selected
    
    def _tournament_selection(self, population: List[Individual],
                            tournament_size: int = 3) -> List[Individual]:
        """Tournament selection"""
        selected = []
        for _ in range(len(population) // 2):
            tournament = np.random.choice(population, tournament_size, replace=False)
            winner = max(tournament, key=lambda x: x.fitness or 0)
            selected.append(winner)
        return selected
    
    def _roulette_selection(self, population: List[Individual]) -> List[Individual]:
        """Roulette wheel selection"""
        fitnesses = np.array([ind.fitness or 0 for ind in population])
        # Handle negative fitness
        fitnesses = fitnesses - fitnesses.min() + 1e-6
        probabilities = fitnesses / fitnesses.sum()
        
        selected_indices = np.random.choice(
            len(population), size=len(population) // 2, p=probabilities
        )
        return [population[i] for i in selected_indices]
    
    def _rank_selection(self, population: List[Individual]) -> List[Individual]:
        """Rank-based selection"""
        sorted_pop = sorted(population, key=lambda x: x.fitness or 0)
        ranks = np.arange(1, len(population) + 1)
        probabilities = ranks / ranks.sum()
        
        selected_indices = np.random.choice(
            len(population), size=len(population) // 2, p=probabilities
        )
        return [sorted_pop[i] for i in selected_indices]
    
    def _elitist_selection(self, population: List[Individual]) -> List[Individual]:
        """Elitist selection - select top performers"""
        sorted_pop = sorted(population, key=lambda x: x.fitness or 0, reverse=True)
        return sorted_pop[:len(population) // 2]
    
    def _crossover(self, parent1: Individual, parent2: Individual,
                  params_space: List[ParameterSpace], audit: AuditTrail) -> Individual:
        """Create offspring through crossover"""
        offspring_params = {}
        
        for param in params_space:
            # Random crossover point
            if np.random.random() < 0.5:
                offspring_params[param.name] = parent1.parameters[param.name]
            else:
                offspring_params[param.name] = parent2.parameters[param.name]
        
        offspring = Individual(
            parameters=offspring_params,
            id="",  # Will be auto-generated in __post_init__
            generation=max(parent1.generation, parent2.generation) + 1,
            parent_ids=[parent1.id, parent2.id]
        )
        
        audit.log_crossover(parent1, parent2, offspring)
        return offspring
    
    def _mutate(self, individual: Individual, params_space: List[ParameterSpace],
               audit: AuditTrail) -> Individual:
        """Mutate individual parameters"""
        original_params = individual.parameters.copy()
        
        for param in params_space:
            individual.parameters[param.name] = param.mutate_value(
                individual.parameters[param.name], self.config.mutation_rate
            )
        
        # Record mutation history
        individual.mutation_history.append({
            'generation': individual.generation,
            'original_params': original_params,
            'mutated_params': individual.parameters.copy()
        })
        
        audit.log_mutation(individual, original_params)
        return individual


# Example strategy implementation
class MovingAverageCrossoverStrategy(BaseStrategy):
    """Example moving average crossover strategy"""
    
    @classmethod
    def get_params_space(cls) -> List[ParameterSpace]:
        return [
            ParameterSpace("fast_period", 5, 50, "int"),
            ParameterSpace("slow_period", 20, 200, "int"),
            ParameterSpace("signal_threshold", 0.001, 0.01, "float")
        ]
    
    def generate_signals(self, data: pd.DataFrame) -> pd.Series:
        """Generate trading signals based on moving average crossover"""
        fast_ma = data['close'].rolling(self.parameters['fast_period']).mean()
        slow_ma = data['close'].rolling(self.parameters['slow_period']).mean()
        
        # Generate signals
        signals = pd.Series(0, index=data.index)
        
        # Buy signal when fast MA crosses above slow MA
        buy_condition = (fast_ma > slow_ma) & (fast_ma.shift(1) <= slow_ma.shift(1))
        signals[buy_condition] = 1
        
        # Sell signal when fast MA crosses below slow MA
        sell_condition = (fast_ma < slow_ma) & (fast_ma.shift(1) >= slow_ma.shift(1))
        signals[sell_condition] = -1
        
        return signals


def create_dgm_optimizer(config: Optional[OptimizationConfig] = None) -> DarwinGodelMachineOptimizer:
    """Factory function to create DGM optimizer"""
    return DarwinGodelMachineOptimizer(config)


if __name__ == "__main__":
    # Example usage
    print("🧬 Darwin Gödel Machine Optimizer - Advanced Evolutionary Optimization")
    print("=" * 80)
    
    # Create sample data
    np.random.seed(42)
    dates = pd.date_range('2020-01-01', '2023-12-31', freq='D')
    prices = 100 * np.exp(np.cumsum(np.random.normal(0.0005, 0.02, len(dates))))
    data = pd.DataFrame({
        'close': prices,
        'volume': np.random.randint(1000, 10000, len(dates))
    }, index=dates)
    
    # Configure optimizer
    config = OptimizationConfig(
        population_size=20,
        generations=10,
        mutation_rate=0.1,
        parallel_evaluation=True,
        max_workers=2
    )
    
    # Create optimizer
    optimizer = create_dgm_optimizer(config)
    
    # Run optimization
    print("🔄 Starting optimization...")
    result = optimizer.optimize(MovingAverageCrossoverStrategy, data)
    
    # Display results
    print(f"\n✅ Optimization completed: {result.optimization_status.value}")
    print(f"🏆 Best fitness: {result.best_fitness:.6f}")
    print(f"📊 Best parameters: {result.best_parameters}")
    print(f"⏱️ Execution time: {result.execution_time:.2f}s")
    print(f"🔍 Generations: {result.generations_completed}")
    print(f"📈 Total evaluations: {result.total_evaluations}")
    print(f"🔒 Integrity verified: {result.verify_integrity()}")
    print(f"📋 Audit entries: {len(result.audit_trail)}")