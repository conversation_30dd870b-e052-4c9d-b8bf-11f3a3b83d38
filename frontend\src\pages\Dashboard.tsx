import { useState } from 'react';
import { TrendingUp, Target, Upload, MessageSquare } from 'lucide-react';
import { ChatWidget } from '../components/chat/ChatWidget';
import { TradingStats } from '../components/dashboard/TradingStats';
import { RecentActivity } from '../components/dashboard/RecentActivity';
import { MarketOverview } from '../components/dashboard/MarketOverview';
import { useAuth } from '../hooks/useAuth';

export function Dashboard() {
  const { user } = useAuth();
  const [showChat, setShowChat] = useState(false);

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg shadow-lg p-6 text-white">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-2xl font-bold mb-2">
              Welcome back, {user?.fullName || user?.email?.split('@')[0]}!
            </h1>
            <p className="text-blue-100">
              Your AI-powered trading platform is ready. Let's analyze your strategies.
            </p>
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={() => setShowChat(!showChat)}
              className="px-4 py-2 bg-white/20 backdrop-blur-sm text-white rounded-lg hover:bg-white/30 flex items-center space-x-2"
            >
              <MessageSquare className="w-4 h-4" />
              <span>AI Assistant</span>
            </button>
          </div>
        </div>
      </div>

      {/* Trading Stats */}
      <TradingStats />

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Market Overview */}
        <div className="lg:col-span-2">
          <MarketOverview />
        </div>

        {/* Quick Actions */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div className="space-y-3">
              <button className="w-full text-left p-3 rounded-lg border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-colors">
                <div className="flex items-center space-x-3">
                  <Upload className="w-5 h-5 text-blue-600" />
                  <div>
                    <p className="font-medium text-gray-900">Upload Data</p>
                    <p className="text-sm text-gray-600">Import new trading data</p>
                  </div>
                </div>
              </button>
              
              <button className="w-full text-left p-3 rounded-lg border border-gray-200 hover:border-green-300 hover:bg-green-50 transition-colors">
                <div className="flex items-center space-x-3">
                  <Target className="w-5 h-5 text-green-600" />
                  <div>
                    <p className="font-medium text-gray-900">New Backtest</p>
                    <p className="text-sm text-gray-600">Test a trading strategy</p>
                  </div>
                </div>
              </button>
              
              <button className="w-full text-left p-3 rounded-lg border border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-colors">
                <div className="flex items-center space-x-3">
                  <TrendingUp className="w-5 h-5 text-purple-600" />
                  <div>
                    <p className="font-medium text-gray-900">New Strategy</p>
                    <p className="text-sm text-gray-600">Create a trading strategy</p>
                  </div>
                </div>
              </button>
            </div>
          </div>

          {/* Subscription Status */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Subscription</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Current Plan</span>
                <span className="text-sm font-medium text-gray-900 capitalize">
                  {user?.subscriptionTier || 'free'}
                </span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">API Usage</span>
                <span className="text-sm font-medium text-gray-900">
                  {user?.apiQuotaUsed || 0} / {user?.apiQuotaLimit || 100}
                </span>
              </div>
              
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full"
                  style={{
                    width: `${user ? (user.apiQuotaUsed / user.apiQuotaLimit) * 100 : 0}%`,
                  }}
                />
              </div>
              
              {user?.subscriptionTier === 'free' && (
                <button className="w-full mt-3 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm">
                  Upgrade Plan
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <RecentActivity />

      {/* Chat Widget */}
      {showChat && (
        <div className="fixed bottom-4 right-4 w-96 h-96 z-50">
          <ChatWidget className="h-full" />
        </div>
      )}
    </div>
  );
}