# tests/test_critical_improvements_integration.py
import pytest
import asyncio
from datetime import datetime
from src.events import EventBus
from src.ml import ModelPipeline, PredictionInput, PredictionFeatures
from src.risk import RiskManager, PortfolioState, Position

class TestCriticalImprovementsIntegration:
    """Integration tests for the critical improvements system"""
    
    @pytest.fixture
    def event_bus(self):
        return EventBus()
    
    @pytest.fixture
    def ml_pipeline(self):
        return ModelPipeline()
    
    @pytest.fixture
    def risk_manager(self, event_bus):
        return RiskManager(event_bus)
    
    @pytest.mark.asyncio
    async def test_end_to_end_trading_workflow(self, event_bus, ml_pipeline, risk_manager):
        """Test complete end-to-end trading workflow"""
        # Step 1: Receive market data
        market_data_event = {
            'type': 'MARKET_DATA_RECEIVED',
            'timestamp': datetime.now().isoformat(),
            'payload': {
                'symbol': 'EURUSD',
                'data': {
                    'open': 1.2000,
                    'high': 1.2050,
                    'low': 1.1950,
                    'close': 1.2020,
                    'volume': 150000,
                    'timestamp': datetime.now().isoformat()
                },
                'source': 'dukascopy',
                'integrity_hash': 'a' * 64
            }
        }
        
        await event_bus.publish(market_data_event)
        
        # Step 2: Make ML prediction
        prediction_input = PredictionInput(
            symbol='EURUSD',
            features=PredictionFeatures(
                rsi=65.0,
                macd=0.0012,
                volume=150000,
                sma_20=1.2000,
                ema_12=1.2010
            )
        )
        
        prediction = await ml_pipeline.predict(prediction_input)
        assert prediction is not None
        assert prediction.confidence > 0
        
        # Step 3: Evaluate risk for potential position
        portfolio = PortfolioState(
            balance=10000,
            equity=10000,
            positions=[],
            total_exposure=0
        )
        
        new_position = {
            'symbol': 'EURUSD',
            'size': 1.0,
            'estimated_margin': 1000
        }
        
        risk_evaluation = await risk_manager.evaluate_new_position(portfolio, new_position)
        
        # Step 4: Publish strategy execution event if approved
        if risk_evaluation.approved and not prediction.rejected:
            strategy_event = {
                'type': 'STRATEGY_EXECUTED',
                'timestamp': datetime.now().isoformat(),
                'strategy_id': 'ML_RSI_STRATEGY',
                'payload': {
                    'symbol': 'EURUSD',
                    'action': 'BUY' if prediction.value > 0.5 else 'SELL',
                    'confidence': prediction.confidence,
                    'parameters': {
                        'ml_prediction': prediction.value,
                        'risk_score': risk_evaluation.risk_score
                    }
                },
                'metadata': {
                    'dummy_mode': True,
                    'user_id': '12345678-1234-1234-1234-123456789012'
                }
            }
            
            await event_bus.publish(strategy_event)
        
        # Verify the workflow completed successfully
        history = event_bus.get_event_history()
        assert len(history) >= 2  # At least market data and strategy events
    
    @pytest.mark.asyncio
    async def test_risk_triggered_position_rejection(self, event_bus, ml_pipeline, risk_manager):
        """Test that high-risk positions are rejected even with good ML predictions"""
        # Create high-risk portfolio state
        portfolio = PortfolioState(
            balance=10000,
            equity=8000,  # Already in drawdown
            positions=[
                Position(symbol='EURUSD', size=8.0, unrealized_pnl=-2000)  # Large position
            ],
            total_exposure=8.0,
            max_equity=10000
        )
        
        # Get ML prediction (assume it's positive)
        prediction_input = PredictionInput(
            symbol='GBPUSD',
            features=PredictionFeatures(
                rsi=75.0,
                macd=0.002,
                volume=100000
            )
        )
        
        prediction = await ml_pipeline.predict(prediction_input)
        
        # Try to add another large position
        new_position = {
            'symbol': 'GBPUSD',
            'size': 5.0,  # Would exceed risk limits
            'estimated_margin': 5000
        }
        
        risk_evaluation = await risk_manager.evaluate_new_position(portfolio, new_position)
        
        # Should be rejected due to risk limits, regardless of ML prediction
        assert risk_evaluation.approved is False
        assert len(risk_evaluation.violations) > 0
    
    @pytest.mark.asyncio
    async def test_ml_prediction_rejection_overrides_risk_approval(self, ml_pipeline, risk_manager):
        """Test that low-confidence ML predictions are rejected even if risk is acceptable"""
        # Set very high confidence threshold
        ml_pipeline.set_min_confidence_threshold(0.95)
        
        # Create low-risk portfolio
        portfolio = PortfolioState(
            balance=10000,
            equity=10000,
            positions=[],
            total_exposure=0
        )
        
        # Small, low-risk position
        new_position = {
            'symbol': 'EURUSD',
            'size': 0.5,
            'estimated_margin': 500
        }
        
        # Get ML prediction (likely to be low confidence with neutral values)
        prediction_input = PredictionInput(
            symbol='EURUSD',
            features=PredictionFeatures(
                rsi=50.0,  # Neutral
                macd=0.0,  # Neutral
                volume=1000  # Low volume
            )
        )
        
        prediction = await ml_pipeline.predict(prediction_input)
        risk_evaluation = await risk_manager.evaluate_new_position(portfolio, new_position)
        
        # Risk should approve, but ML prediction might be rejected
        assert risk_evaluation.approved is True
        
        # If ML prediction is rejected, overall decision should be no trade
        if prediction.rejected:
            assert prediction.rejection_reason == 'Confidence below threshold'
    
    @pytest.mark.asyncio
    async def test_event_driven_risk_monitoring(self, event_bus, risk_manager):
        """Test event-driven risk monitoring system"""
        risk_alerts = []
        
        def risk_alert_handler(event):
            risk_alerts.append(event)
        
        event_bus.subscribe('RISK_LIMIT_BREACHED', risk_alert_handler)
        
        # Simulate a series of losing trades leading to critical risk
        portfolio_states = [
            PortfolioState(balance=10000, equity=9000, positions=[], total_exposure=0, max_equity=10000),
            PortfolioState(balance=10000, equity=8000, positions=[], total_exposure=0, max_equity=10000),
            PortfolioState(balance=10000, equity=7000, positions=[], total_exposure=0, max_equity=10000),  # Critical
        ]
        
        for portfolio in portfolio_states:
            await risk_manager.evaluate_portfolio_risk(portfolio)
        
        await asyncio.sleep(0.1)  # Allow async processing
        
        # Should have received at least one critical risk alert
        assert len(risk_alerts) > 0
        critical_alerts = [alert for alert in risk_alerts if alert.payload['severity'] == 'CRITICAL']
        assert len(critical_alerts) > 0
    
    @pytest.mark.asyncio
    async def test_correlation_risk_integration(self, risk_manager):
        """Test correlation risk integration in position evaluation"""
        # Create portfolio with highly correlated EUR positions
        portfolio = PortfolioState(
            balance=10000,
            equity=10000,
            positions=[
                Position(symbol='EURUSD', size=2.0, unrealized_pnl=0),
                Position(symbol='EURJPY', size=2.0, unrealized_pnl=0),
                Position(symbol='EURGBP', size=2.0, unrealized_pnl=0)
            ],
            total_exposure=6.0
        )
        
        # Try to add another EUR position
        new_eur_position = {
            'symbol': 'EURAUD',
            'size': 1.0,
            'estimated_margin': 1000
        }
        
        # Try to add a diversifying position
        new_diverse_position = {
            'symbol': 'USDCAD',
            'size': 1.0,
            'estimated_margin': 1000
        }
        
        eur_evaluation = await risk_manager.evaluate_new_position(portfolio, new_eur_position)
        diverse_evaluation = await risk_manager.evaluate_new_position(portfolio, new_diverse_position)
        
        # EUR position should have higher risk score due to correlation
        assert eur_evaluation.risk_score >= diverse_evaluation.risk_score
        
        # Should get correlation warnings for EUR position
        assert any('CORRELATION' in warning for warning in eur_evaluation.warnings)
    
    @pytest.mark.asyncio
    async def test_model_update_invalidates_cache(self, ml_pipeline):
        """Test that model updates properly invalidate prediction cache"""
        from src.ml.model_pipeline import ModelVersion, PerformanceMetrics
        
        # Make initial prediction
        input_data = PredictionInput(
            symbol='EURUSD',
            features=PredictionFeatures(rsi=60.0, macd=0.001, volume=50000)
        )
        
        prediction1 = await ml_pipeline.predict(input_data)
        
        # Update model
        new_model = ModelVersion(
            id='updated_model',
            version='2.0.0',
            algorithm='XGBoost',
            training_data_hash='new_hash',
            hyperparameters={'n_estimators': 200},
            performance_metrics=PerformanceMetrics(
                accuracy=0.80, precision=0.75, recall=0.85, f1_score=0.80, backtest_sharpe=1.5
            ),
            created_at=datetime.now(),
            model_hash='new_model_hash'
        )
        
        await ml_pipeline.update_model(new_model)
        
        # Make same prediction again
        prediction2 = await ml_pipeline.predict(input_data)
        
        # Should have different model version
        assert prediction1.model_version != prediction2.model_version
        assert prediction2.model_version == 'updated_model'
    
    @pytest.mark.asyncio
    async def test_comprehensive_audit_trail(self, event_bus, ml_pipeline, risk_manager):
        """Test comprehensive audit trail across all systems"""
        audit_events = []
        
        def audit_handler(event):
            audit_events.append({
                'timestamp': datetime.now(),
                'event_type': event.type,
                'event_data': event
            })
        
        # Subscribe to all event types
        for event_type in ['STRATEGY_EXECUTED', 'MARKET_DATA_RECEIVED', 'RISK_LIMIT_BREACHED']:
            event_bus.subscribe(event_type, audit_handler)
        
        # Execute a complete trading workflow
        # 1. Market data
        await event_bus.publish({
            'type': 'MARKET_DATA_RECEIVED',
            'timestamp': datetime.now().isoformat(),
            'payload': {
                'symbol': 'EURUSD',
                'data': {
                    'open': 1.2000, 'high': 1.2050, 'low': 1.1950, 'close': 1.2020,
                    'volume': 100000, 'timestamp': datetime.now().isoformat()
                },
                'source': 'dukascopy',
                'integrity_hash': 'a' * 64
            }
        })
        
        # 2. ML prediction with audit trail
        prediction = await ml_pipeline.predict(PredictionInput(
            symbol='EURUSD',
            features=PredictionFeatures(rsi=70.0, macd=0.002, volume=100000)
        ))
        
        # 3. Risk evaluation
        portfolio = PortfolioState(balance=10000, equity=10000, positions=[], total_exposure=0)
        risk_eval = await risk_manager.evaluate_new_position(portfolio, {
            'symbol': 'EURUSD', 'size': 1.0, 'estimated_margin': 1000
        })
        
        # 4. Strategy execution
        if risk_eval.approved and not prediction.rejected:
            await event_bus.publish({
                'type': 'STRATEGY_EXECUTED',
                'timestamp': datetime.now().isoformat(),
                'strategy_id': 'AUDIT_TEST_STRATEGY',
                'payload': {
                    'symbol': 'EURUSD',
                    'action': 'BUY',
                    'confidence': prediction.confidence,
                    'parameters': {'prediction_hash': prediction.prediction_hash}
                },
                'metadata': {
                    'dummy_mode': True,
                    'user_id': '12345678-1234-1234-1234-123456789012'
                }
            })
        
        await asyncio.sleep(0.1)  # Allow async processing
        
        # Verify audit trail
        assert len(audit_events) >= 2  # At least market data and strategy events
        
        # Check that we can trace the complete workflow
        event_types = [event['event_type'] for event in audit_events]
        assert 'MARKET_DATA_RECEIVED' in event_types
        
        # Verify prediction has complete lineage
        assert prediction.input_hash is not None
        assert prediction.prediction_hash is not None
        assert prediction.model_version is not None
        
        # Verify event history is maintained
        history = event_bus.get_event_history()
        assert len(history) >= len(audit_events)