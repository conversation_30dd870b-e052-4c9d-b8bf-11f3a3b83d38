import { z } from 'zod';
export declare const LoginApiRequestSchema: z.ZodObject<{
    email: z.ZodString;
    password: z.ZodString;
}, "strip", z.ZodTypeAny, {
    email: string;
    password: string;
}, {
    email: string;
    password: string;
}>;
export declare const LoginApiResponseSchema: z.ZodObject<{
    success: z.ZodBoolean;
    data: z.ZodOptional<z.ZodObject<{
        user: z.ZodObject<{
            id: z.ZodString;
            email: z.ZodString;
            fullName: z.ZodOptional<z.ZodString>;
            subscriptionTier: z.ZodEnum<["free", "solo", "pro", "enterprise"]>;
        }, "strip", z.ZodTypeAny, {
            id: string;
            email: string;
            subscriptionTier: "free" | "solo" | "pro" | "enterprise";
            fullName?: string | undefined;
        }, {
            id: string;
            email: string;
            subscriptionTier: "free" | "solo" | "pro" | "enterprise";
            fullName?: string | undefined;
        }>;
        tokens: z.ZodObject<{
            accessToken: z.ZodString;
            refreshToken: z.ZodString;
            expiresIn: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            refreshToken: string;
            accessToken: string;
            expiresIn: number;
        }, {
            refreshToken: string;
            accessToken: string;
            expiresIn: number;
        }>;
    }, "strip", z.ZodTypeAny, {
        user: {
            id: string;
            email: string;
            subscriptionTier: "free" | "solo" | "pro" | "enterprise";
            fullName?: string | undefined;
        };
        tokens: {
            refreshToken: string;
            accessToken: string;
            expiresIn: number;
        };
    }, {
        user: {
            id: string;
            email: string;
            subscriptionTier: "free" | "solo" | "pro" | "enterprise";
            fullName?: string | undefined;
        };
        tokens: {
            refreshToken: string;
            accessToken: string;
            expiresIn: number;
        };
    }>>;
    error: z.ZodOptional<z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    }, {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    }>>;
    timestamp: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    timestamp: Date;
    data?: {
        user: {
            id: string;
            email: string;
            subscriptionTier: "free" | "solo" | "pro" | "enterprise";
            fullName?: string | undefined;
        };
        tokens: {
            refreshToken: string;
            accessToken: string;
            expiresIn: number;
        };
    } | undefined;
    error?: {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    } | undefined;
}, {
    success: boolean;
    data?: {
        user: {
            id: string;
            email: string;
            subscriptionTier: "free" | "solo" | "pro" | "enterprise";
            fullName?: string | undefined;
        };
        tokens: {
            refreshToken: string;
            accessToken: string;
            expiresIn: number;
        };
    } | undefined;
    error?: {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    } | undefined;
    timestamp?: Date | undefined;
}>;
export declare const RegisterApiRequestSchema: z.ZodObject<{
    email: z.ZodString;
    password: z.ZodString;
    fullName: z.ZodOptional<z.ZodString>;
    acceptTerms: z.ZodEffects<z.ZodBoolean, boolean, boolean>;
}, "strip", z.ZodTypeAny, {
    email: string;
    password: string;
    acceptTerms: boolean;
    fullName?: string | undefined;
}, {
    email: string;
    password: string;
    acceptTerms: boolean;
    fullName?: string | undefined;
}>;
export declare const RegisterApiResponseSchema: z.ZodObject<{
    success: z.ZodBoolean;
    data: z.ZodOptional<z.ZodObject<{
        user: z.ZodObject<{
            id: z.ZodString;
            email: z.ZodString;
            fullName: z.ZodOptional<z.ZodString>;
            subscriptionTier: z.ZodEnum<["free", "solo", "pro", "enterprise"]>;
        }, "strip", z.ZodTypeAny, {
            id: string;
            email: string;
            subscriptionTier: "free" | "solo" | "pro" | "enterprise";
            fullName?: string | undefined;
        }, {
            id: string;
            email: string;
            subscriptionTier: "free" | "solo" | "pro" | "enterprise";
            fullName?: string | undefined;
        }>;
        tokens: z.ZodObject<{
            accessToken: z.ZodString;
            refreshToken: z.ZodString;
            expiresIn: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            refreshToken: string;
            accessToken: string;
            expiresIn: number;
        }, {
            refreshToken: string;
            accessToken: string;
            expiresIn: number;
        }>;
    }, "strip", z.ZodTypeAny, {
        user: {
            id: string;
            email: string;
            subscriptionTier: "free" | "solo" | "pro" | "enterprise";
            fullName?: string | undefined;
        };
        tokens: {
            refreshToken: string;
            accessToken: string;
            expiresIn: number;
        };
    }, {
        user: {
            id: string;
            email: string;
            subscriptionTier: "free" | "solo" | "pro" | "enterprise";
            fullName?: string | undefined;
        };
        tokens: {
            refreshToken: string;
            accessToken: string;
            expiresIn: number;
        };
    }>>;
    error: z.ZodOptional<z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    }, {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    }>>;
    timestamp: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    timestamp: Date;
    data?: {
        user: {
            id: string;
            email: string;
            subscriptionTier: "free" | "solo" | "pro" | "enterprise";
            fullName?: string | undefined;
        };
        tokens: {
            refreshToken: string;
            accessToken: string;
            expiresIn: number;
        };
    } | undefined;
    error?: {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    } | undefined;
}, {
    success: boolean;
    data?: {
        user: {
            id: string;
            email: string;
            subscriptionTier: "free" | "solo" | "pro" | "enterprise";
            fullName?: string | undefined;
        };
        tokens: {
            refreshToken: string;
            accessToken: string;
            expiresIn: number;
        };
    } | undefined;
    error?: {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    } | undefined;
    timestamp?: Date | undefined;
}>;
export declare const SubmitOrderApiRequestSchema: z.ZodObject<{
    symbol: z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>;
    volume: z.ZodNumber;
    order_type: z.ZodEnum<["buy", "sell"]>;
    price: z.ZodNumber;
    stop_loss: z.ZodOptional<z.ZodNumber>;
    take_profit: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
    volume: number;
    order_type: "buy" | "sell";
    price: number;
    stop_loss?: number | undefined;
    take_profit?: number | undefined;
}, {
    symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
    volume: number;
    order_type: "buy" | "sell";
    price: number;
    stop_loss?: number | undefined;
    take_profit?: number | undefined;
}>;
export declare const SubmitOrderApiResponseSchema: z.ZodObject<{
    success: z.ZodBoolean;
    data: z.ZodOptional<z.ZodObject<{
        success: z.ZodBoolean;
        order_id: z.ZodOptional<z.ZodNumber>;
        error: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        success: boolean;
        error?: string | undefined;
        order_id?: number | undefined;
    }, {
        success: boolean;
        error?: string | undefined;
        order_id?: number | undefined;
    }>>;
    error: z.ZodOptional<z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    }, {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    }>>;
    timestamp: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    timestamp: Date;
    data?: {
        success: boolean;
        error?: string | undefined;
        order_id?: number | undefined;
    } | undefined;
    error?: {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    } | undefined;
}, {
    success: boolean;
    data?: {
        success: boolean;
        error?: string | undefined;
        order_id?: number | undefined;
    } | undefined;
    error?: {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    } | undefined;
    timestamp?: Date | undefined;
}>;
export declare const GetAccountInfoApiResponseSchema: z.ZodObject<{
    success: z.ZodBoolean;
    data: z.ZodOptional<z.ZodObject<{
        balance: z.ZodNumber;
        equity: z.ZodNumber;
        margin: z.ZodNumber;
        currency: z.ZodDefault<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        balance: number;
        equity: number;
        margin: number;
        currency: string;
    }, {
        balance: number;
        equity: number;
        margin: number;
        currency?: string | undefined;
    }>>;
    error: z.ZodOptional<z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    }, {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    }>>;
    timestamp: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    timestamp: Date;
    data?: {
        balance: number;
        equity: number;
        margin: number;
        currency: string;
    } | undefined;
    error?: {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    } | undefined;
}, {
    success: boolean;
    data?: {
        balance: number;
        equity: number;
        margin: number;
        currency?: string | undefined;
    } | undefined;
    error?: {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    } | undefined;
    timestamp?: Date | undefined;
}>;
export declare const GetPositionsApiResponseSchema: z.ZodObject<{
    success: z.ZodBoolean;
    data: z.ZodOptional<z.ZodArray<z.ZodObject<{
        position_id: z.ZodNumber;
        symbol: z.ZodString;
        volume: z.ZodNumber;
        open_price: z.ZodNumber;
        current_price: z.ZodNumber;
        pnl: z.ZodNumber;
        order_type: z.ZodEnum<["buy", "sell"]>;
        open_time: z.ZodDate;
    }, "strip", z.ZodTypeAny, {
        symbol: string;
        volume: number;
        order_type: "buy" | "sell";
        position_id: number;
        open_price: number;
        current_price: number;
        pnl: number;
        open_time: Date;
    }, {
        symbol: string;
        volume: number;
        order_type: "buy" | "sell";
        position_id: number;
        open_price: number;
        current_price: number;
        pnl: number;
        open_time: Date;
    }>, "many">>;
    error: z.ZodOptional<z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    }, {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    }>>;
    timestamp: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    timestamp: Date;
    data?: {
        symbol: string;
        volume: number;
        order_type: "buy" | "sell";
        position_id: number;
        open_price: number;
        current_price: number;
        pnl: number;
        open_time: Date;
    }[] | undefined;
    error?: {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    } | undefined;
}, {
    success: boolean;
    data?: {
        symbol: string;
        volume: number;
        order_type: "buy" | "sell";
        position_id: number;
        open_price: number;
        current_price: number;
        pnl: number;
        open_time: Date;
    }[] | undefined;
    error?: {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    } | undefined;
    timestamp?: Date | undefined;
}>;
export declare const CreateBacktestApiRequestSchema: z.ZodObject<{
    config: z.ZodEffects<z.ZodObject<{
        name: z.ZodString;
        description: z.ZodOptional<z.ZodString>;
        symbols: z.ZodArray<z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>, "many">;
        start_date: z.ZodDate;
        end_date: z.ZodDate;
        initial_balance: z.ZodDefault<z.ZodNumber>;
        strategy: z.ZodObject<{
            name: z.ZodString;
            parameters: z.ZodRecord<z.ZodString, z.ZodAny>;
        }, "strip", z.ZodTypeAny, {
            name: string;
            parameters: Record<string, any>;
        }, {
            name: string;
            parameters: Record<string, any>;
        }>;
        risk_management: z.ZodObject<{
            max_risk_per_trade: z.ZodDefault<z.ZodNumber>;
            max_concurrent_trades: z.ZodDefault<z.ZodNumber>;
            stop_loss_pips: z.ZodOptional<z.ZodNumber>;
            take_profit_pips: z.ZodOptional<z.ZodNumber>;
        }, "strip", z.ZodTypeAny, {
            max_risk_per_trade: number;
            max_concurrent_trades: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        }, {
            max_risk_per_trade?: number | undefined;
            max_concurrent_trades?: number | undefined;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        }>;
    }, "strip", z.ZodTypeAny, {
        strategy: {
            name: string;
            parameters: Record<string, any>;
        };
        name: string;
        initial_balance: number;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        risk_management: {
            max_risk_per_trade: number;
            max_concurrent_trades: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        description?: string | undefined;
    }, {
        strategy: {
            name: string;
            parameters: Record<string, any>;
        };
        name: string;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        risk_management: {
            max_risk_per_trade?: number | undefined;
            max_concurrent_trades?: number | undefined;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        description?: string | undefined;
        initial_balance?: number | undefined;
    }>, {
        strategy: {
            name: string;
            parameters: Record<string, any>;
        };
        name: string;
        initial_balance: number;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        risk_management: {
            max_risk_per_trade: number;
            max_concurrent_trades: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        description?: string | undefined;
    }, {
        strategy: {
            name: string;
            parameters: Record<string, any>;
        };
        name: string;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        risk_management: {
            max_risk_per_trade?: number | undefined;
            max_concurrent_trades?: number | undefined;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        description?: string | undefined;
        initial_balance?: number | undefined;
    }>;
    data_source: z.ZodEnum<["historical", "uploaded"]>;
    data_file_id: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    config: {
        strategy: {
            name: string;
            parameters: Record<string, any>;
        };
        name: string;
        initial_balance: number;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        risk_management: {
            max_risk_per_trade: number;
            max_concurrent_trades: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        description?: string | undefined;
    };
    data_source: "historical" | "uploaded";
    data_file_id?: string | undefined;
}, {
    config: {
        strategy: {
            name: string;
            parameters: Record<string, any>;
        };
        name: string;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        risk_management: {
            max_risk_per_trade?: number | undefined;
            max_concurrent_trades?: number | undefined;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        description?: string | undefined;
        initial_balance?: number | undefined;
    };
    data_source: "historical" | "uploaded";
    data_file_id?: string | undefined;
}>;
export declare const CreateBacktestApiResponseSchema: z.ZodObject<{
    success: z.ZodBoolean;
    data: z.ZodOptional<z.ZodObject<{
        id: z.ZodString;
        user_id: z.ZodString;
        config: z.ZodEffects<z.ZodObject<{
            name: z.ZodString;
            description: z.ZodOptional<z.ZodString>;
            symbols: z.ZodArray<z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>, "many">;
            start_date: z.ZodDate;
            end_date: z.ZodDate;
            initial_balance: z.ZodDefault<z.ZodNumber>;
            strategy: z.ZodObject<{
                name: z.ZodString;
                parameters: z.ZodRecord<z.ZodString, z.ZodAny>;
            }, "strip", z.ZodTypeAny, {
                name: string;
                parameters: Record<string, any>;
            }, {
                name: string;
                parameters: Record<string, any>;
            }>;
            risk_management: z.ZodObject<{
                max_risk_per_trade: z.ZodDefault<z.ZodNumber>;
                max_concurrent_trades: z.ZodDefault<z.ZodNumber>;
                stop_loss_pips: z.ZodOptional<z.ZodNumber>;
                take_profit_pips: z.ZodOptional<z.ZodNumber>;
            }, "strip", z.ZodTypeAny, {
                max_risk_per_trade: number;
                max_concurrent_trades: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            }, {
                max_risk_per_trade?: number | undefined;
                max_concurrent_trades?: number | undefined;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            }>;
        }, "strip", z.ZodTypeAny, {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            initial_balance: number;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade: number;
                max_concurrent_trades: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
        }, {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade?: number | undefined;
                max_concurrent_trades?: number | undefined;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
            initial_balance?: number | undefined;
        }>, {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            initial_balance: number;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade: number;
                max_concurrent_trades: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
        }, {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade?: number | undefined;
                max_concurrent_trades?: number | undefined;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
            initial_balance?: number | undefined;
        }>;
        status: z.ZodEnum<["pending", "running", "completed", "error"]>;
        progress: z.ZodDefault<z.ZodNumber>;
        started_at: z.ZodOptional<z.ZodDate>;
        completed_at: z.ZodOptional<z.ZodDate>;
        error_message: z.ZodOptional<z.ZodString>;
        created_at: z.ZodDate;
        updated_at: z.ZodDate;
    }, "strip", z.ZodTypeAny, {
        status: "error" | "running" | "pending" | "completed";
        id: string;
        user_id: string;
        config: {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            initial_balance: number;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade: number;
                max_concurrent_trades: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
        };
        created_at: Date;
        progress: number;
        updated_at: Date;
        started_at?: Date | undefined;
        completed_at?: Date | undefined;
        error_message?: string | undefined;
    }, {
        status: "error" | "running" | "pending" | "completed";
        id: string;
        user_id: string;
        config: {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade?: number | undefined;
                max_concurrent_trades?: number | undefined;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
            initial_balance?: number | undefined;
        };
        created_at: Date;
        updated_at: Date;
        progress?: number | undefined;
        started_at?: Date | undefined;
        completed_at?: Date | undefined;
        error_message?: string | undefined;
    }>>;
    error: z.ZodOptional<z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    }, {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    }>>;
    timestamp: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    timestamp: Date;
    data?: {
        status: "error" | "running" | "pending" | "completed";
        id: string;
        user_id: string;
        config: {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            initial_balance: number;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade: number;
                max_concurrent_trades: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
        };
        created_at: Date;
        progress: number;
        updated_at: Date;
        started_at?: Date | undefined;
        completed_at?: Date | undefined;
        error_message?: string | undefined;
    } | undefined;
    error?: {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    } | undefined;
}, {
    success: boolean;
    data?: {
        status: "error" | "running" | "pending" | "completed";
        id: string;
        user_id: string;
        config: {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade?: number | undefined;
                max_concurrent_trades?: number | undefined;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
            initial_balance?: number | undefined;
        };
        created_at: Date;
        updated_at: Date;
        progress?: number | undefined;
        started_at?: Date | undefined;
        completed_at?: Date | undefined;
        error_message?: string | undefined;
    } | undefined;
    error?: {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    } | undefined;
    timestamp?: Date | undefined;
}>;
export declare const GetBacktestsApiRequestSchema: z.ZodObject<{
    page: z.ZodDefault<z.ZodNumber>;
    limit: z.ZodDefault<z.ZodNumber>;
    sortBy: z.ZodOptional<z.ZodString>;
    sortOrder: z.ZodDefault<z.ZodEnum<["asc", "desc"]>>;
} & {
    status: z.ZodOptional<z.ZodEnum<["pending", "running", "completed", "error"]>>;
    symbol: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    limit: number;
    page: number;
    sortOrder: "asc" | "desc";
    symbol?: string | undefined;
    status?: "error" | "running" | "pending" | "completed" | undefined;
    sortBy?: string | undefined;
}, {
    symbol?: string | undefined;
    status?: "error" | "running" | "pending" | "completed" | undefined;
    limit?: number | undefined;
    page?: number | undefined;
    sortBy?: string | undefined;
    sortOrder?: "asc" | "desc" | undefined;
}>;
export declare const GetBacktestsApiResponseSchema: z.ZodObject<{
    success: z.ZodBoolean;
    data: z.ZodOptional<z.ZodObject<{
        backtests: z.ZodArray<z.ZodObject<{
            id: z.ZodString;
            user_id: z.ZodString;
            config: z.ZodEffects<z.ZodObject<{
                name: z.ZodString;
                description: z.ZodOptional<z.ZodString>;
                symbols: z.ZodArray<z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>, "many">;
                start_date: z.ZodDate;
                end_date: z.ZodDate;
                initial_balance: z.ZodDefault<z.ZodNumber>;
                strategy: z.ZodObject<{
                    name: z.ZodString;
                    parameters: z.ZodRecord<z.ZodString, z.ZodAny>;
                }, "strip", z.ZodTypeAny, {
                    name: string;
                    parameters: Record<string, any>;
                }, {
                    name: string;
                    parameters: Record<string, any>;
                }>;
                risk_management: z.ZodObject<{
                    max_risk_per_trade: z.ZodDefault<z.ZodNumber>;
                    max_concurrent_trades: z.ZodDefault<z.ZodNumber>;
                    stop_loss_pips: z.ZodOptional<z.ZodNumber>;
                    take_profit_pips: z.ZodOptional<z.ZodNumber>;
                }, "strip", z.ZodTypeAny, {
                    max_risk_per_trade: number;
                    max_concurrent_trades: number;
                    stop_loss_pips?: number | undefined;
                    take_profit_pips?: number | undefined;
                }, {
                    max_risk_per_trade?: number | undefined;
                    max_concurrent_trades?: number | undefined;
                    stop_loss_pips?: number | undefined;
                    take_profit_pips?: number | undefined;
                }>;
            }, "strip", z.ZodTypeAny, {
                strategy: {
                    name: string;
                    parameters: Record<string, any>;
                };
                name: string;
                initial_balance: number;
                start_date: Date;
                end_date: Date;
                symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
                risk_management: {
                    max_risk_per_trade: number;
                    max_concurrent_trades: number;
                    stop_loss_pips?: number | undefined;
                    take_profit_pips?: number | undefined;
                };
                description?: string | undefined;
            }, {
                strategy: {
                    name: string;
                    parameters: Record<string, any>;
                };
                name: string;
                start_date: Date;
                end_date: Date;
                symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
                risk_management: {
                    max_risk_per_trade?: number | undefined;
                    max_concurrent_trades?: number | undefined;
                    stop_loss_pips?: number | undefined;
                    take_profit_pips?: number | undefined;
                };
                description?: string | undefined;
                initial_balance?: number | undefined;
            }>, {
                strategy: {
                    name: string;
                    parameters: Record<string, any>;
                };
                name: string;
                initial_balance: number;
                start_date: Date;
                end_date: Date;
                symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
                risk_management: {
                    max_risk_per_trade: number;
                    max_concurrent_trades: number;
                    stop_loss_pips?: number | undefined;
                    take_profit_pips?: number | undefined;
                };
                description?: string | undefined;
            }, {
                strategy: {
                    name: string;
                    parameters: Record<string, any>;
                };
                name: string;
                start_date: Date;
                end_date: Date;
                symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
                risk_management: {
                    max_risk_per_trade?: number | undefined;
                    max_concurrent_trades?: number | undefined;
                    stop_loss_pips?: number | undefined;
                    take_profit_pips?: number | undefined;
                };
                description?: string | undefined;
                initial_balance?: number | undefined;
            }>;
            status: z.ZodEnum<["pending", "running", "completed", "error"]>;
            progress: z.ZodDefault<z.ZodNumber>;
            started_at: z.ZodOptional<z.ZodDate>;
            completed_at: z.ZodOptional<z.ZodDate>;
            error_message: z.ZodOptional<z.ZodString>;
            created_at: z.ZodDate;
            updated_at: z.ZodDate;
        }, "strip", z.ZodTypeAny, {
            status: "error" | "running" | "pending" | "completed";
            id: string;
            user_id: string;
            config: {
                strategy: {
                    name: string;
                    parameters: Record<string, any>;
                };
                name: string;
                initial_balance: number;
                start_date: Date;
                end_date: Date;
                symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
                risk_management: {
                    max_risk_per_trade: number;
                    max_concurrent_trades: number;
                    stop_loss_pips?: number | undefined;
                    take_profit_pips?: number | undefined;
                };
                description?: string | undefined;
            };
            created_at: Date;
            progress: number;
            updated_at: Date;
            started_at?: Date | undefined;
            completed_at?: Date | undefined;
            error_message?: string | undefined;
        }, {
            status: "error" | "running" | "pending" | "completed";
            id: string;
            user_id: string;
            config: {
                strategy: {
                    name: string;
                    parameters: Record<string, any>;
                };
                name: string;
                start_date: Date;
                end_date: Date;
                symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
                risk_management: {
                    max_risk_per_trade?: number | undefined;
                    max_concurrent_trades?: number | undefined;
                    stop_loss_pips?: number | undefined;
                    take_profit_pips?: number | undefined;
                };
                description?: string | undefined;
                initial_balance?: number | undefined;
            };
            created_at: Date;
            updated_at: Date;
            progress?: number | undefined;
            started_at?: Date | undefined;
            completed_at?: Date | undefined;
            error_message?: string | undefined;
        }>, "many">;
        pagination: z.ZodObject<{
            page: z.ZodNumber;
            limit: z.ZodNumber;
            total: z.ZodNumber;
            totalPages: z.ZodNumber;
            hasNext: z.ZodBoolean;
            hasPrev: z.ZodBoolean;
        }, "strip", z.ZodTypeAny, {
            limit: number;
            page: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        }, {
            limit: number;
            page: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        }>;
    }, "strip", z.ZodTypeAny, {
        backtests: {
            status: "error" | "running" | "pending" | "completed";
            id: string;
            user_id: string;
            config: {
                strategy: {
                    name: string;
                    parameters: Record<string, any>;
                };
                name: string;
                initial_balance: number;
                start_date: Date;
                end_date: Date;
                symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
                risk_management: {
                    max_risk_per_trade: number;
                    max_concurrent_trades: number;
                    stop_loss_pips?: number | undefined;
                    take_profit_pips?: number | undefined;
                };
                description?: string | undefined;
            };
            created_at: Date;
            progress: number;
            updated_at: Date;
            started_at?: Date | undefined;
            completed_at?: Date | undefined;
            error_message?: string | undefined;
        }[];
        pagination: {
            limit: number;
            page: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
    }, {
        backtests: {
            status: "error" | "running" | "pending" | "completed";
            id: string;
            user_id: string;
            config: {
                strategy: {
                    name: string;
                    parameters: Record<string, any>;
                };
                name: string;
                start_date: Date;
                end_date: Date;
                symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
                risk_management: {
                    max_risk_per_trade?: number | undefined;
                    max_concurrent_trades?: number | undefined;
                    stop_loss_pips?: number | undefined;
                    take_profit_pips?: number | undefined;
                };
                description?: string | undefined;
                initial_balance?: number | undefined;
            };
            created_at: Date;
            updated_at: Date;
            progress?: number | undefined;
            started_at?: Date | undefined;
            completed_at?: Date | undefined;
            error_message?: string | undefined;
        }[];
        pagination: {
            limit: number;
            page: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
    }>>;
    error: z.ZodOptional<z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    }, {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    }>>;
    timestamp: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    timestamp: Date;
    data?: {
        backtests: {
            status: "error" | "running" | "pending" | "completed";
            id: string;
            user_id: string;
            config: {
                strategy: {
                    name: string;
                    parameters: Record<string, any>;
                };
                name: string;
                initial_balance: number;
                start_date: Date;
                end_date: Date;
                symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
                risk_management: {
                    max_risk_per_trade: number;
                    max_concurrent_trades: number;
                    stop_loss_pips?: number | undefined;
                    take_profit_pips?: number | undefined;
                };
                description?: string | undefined;
            };
            created_at: Date;
            progress: number;
            updated_at: Date;
            started_at?: Date | undefined;
            completed_at?: Date | undefined;
            error_message?: string | undefined;
        }[];
        pagination: {
            limit: number;
            page: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
    } | undefined;
    error?: {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    } | undefined;
}, {
    success: boolean;
    data?: {
        backtests: {
            status: "error" | "running" | "pending" | "completed";
            id: string;
            user_id: string;
            config: {
                strategy: {
                    name: string;
                    parameters: Record<string, any>;
                };
                name: string;
                start_date: Date;
                end_date: Date;
                symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
                risk_management: {
                    max_risk_per_trade?: number | undefined;
                    max_concurrent_trades?: number | undefined;
                    stop_loss_pips?: number | undefined;
                    take_profit_pips?: number | undefined;
                };
                description?: string | undefined;
                initial_balance?: number | undefined;
            };
            created_at: Date;
            updated_at: Date;
            progress?: number | undefined;
            started_at?: Date | undefined;
            completed_at?: Date | undefined;
            error_message?: string | undefined;
        }[];
        pagination: {
            limit: number;
            page: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
    } | undefined;
    error?: {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    } | undefined;
    timestamp?: Date | undefined;
}>;
export declare const GetBacktestApiResponseSchema: z.ZodObject<{
    success: z.ZodBoolean;
    data: z.ZodOptional<z.ZodObject<{
        id: z.ZodString;
        user_id: z.ZodString;
        config: z.ZodEffects<z.ZodObject<{
            name: z.ZodString;
            description: z.ZodOptional<z.ZodString>;
            symbols: z.ZodArray<z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>, "many">;
            start_date: z.ZodDate;
            end_date: z.ZodDate;
            initial_balance: z.ZodDefault<z.ZodNumber>;
            strategy: z.ZodObject<{
                name: z.ZodString;
                parameters: z.ZodRecord<z.ZodString, z.ZodAny>;
            }, "strip", z.ZodTypeAny, {
                name: string;
                parameters: Record<string, any>;
            }, {
                name: string;
                parameters: Record<string, any>;
            }>;
            risk_management: z.ZodObject<{
                max_risk_per_trade: z.ZodDefault<z.ZodNumber>;
                max_concurrent_trades: z.ZodDefault<z.ZodNumber>;
                stop_loss_pips: z.ZodOptional<z.ZodNumber>;
                take_profit_pips: z.ZodOptional<z.ZodNumber>;
            }, "strip", z.ZodTypeAny, {
                max_risk_per_trade: number;
                max_concurrent_trades: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            }, {
                max_risk_per_trade?: number | undefined;
                max_concurrent_trades?: number | undefined;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            }>;
        }, "strip", z.ZodTypeAny, {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            initial_balance: number;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade: number;
                max_concurrent_trades: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
        }, {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade?: number | undefined;
                max_concurrent_trades?: number | undefined;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
            initial_balance?: number | undefined;
        }>, {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            initial_balance: number;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade: number;
                max_concurrent_trades: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
        }, {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade?: number | undefined;
                max_concurrent_trades?: number | undefined;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
            initial_balance?: number | undefined;
        }>;
        status: z.ZodEnum<["pending", "running", "completed", "error"]>;
        progress: z.ZodDefault<z.ZodNumber>;
        started_at: z.ZodOptional<z.ZodDate>;
        completed_at: z.ZodOptional<z.ZodDate>;
        error_message: z.ZodOptional<z.ZodString>;
        created_at: z.ZodDate;
        updated_at: z.ZodDate;
    }, "strip", z.ZodTypeAny, {
        status: "error" | "running" | "pending" | "completed";
        id: string;
        user_id: string;
        config: {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            initial_balance: number;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade: number;
                max_concurrent_trades: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
        };
        created_at: Date;
        progress: number;
        updated_at: Date;
        started_at?: Date | undefined;
        completed_at?: Date | undefined;
        error_message?: string | undefined;
    }, {
        status: "error" | "running" | "pending" | "completed";
        id: string;
        user_id: string;
        config: {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade?: number | undefined;
                max_concurrent_trades?: number | undefined;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
            initial_balance?: number | undefined;
        };
        created_at: Date;
        updated_at: Date;
        progress?: number | undefined;
        started_at?: Date | undefined;
        completed_at?: Date | undefined;
        error_message?: string | undefined;
    }>>;
    error: z.ZodOptional<z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    }, {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    }>>;
    timestamp: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    timestamp: Date;
    data?: {
        status: "error" | "running" | "pending" | "completed";
        id: string;
        user_id: string;
        config: {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            initial_balance: number;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade: number;
                max_concurrent_trades: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
        };
        created_at: Date;
        progress: number;
        updated_at: Date;
        started_at?: Date | undefined;
        completed_at?: Date | undefined;
        error_message?: string | undefined;
    } | undefined;
    error?: {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    } | undefined;
}, {
    success: boolean;
    data?: {
        status: "error" | "running" | "pending" | "completed";
        id: string;
        user_id: string;
        config: {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade?: number | undefined;
                max_concurrent_trades?: number | undefined;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
            initial_balance?: number | undefined;
        };
        created_at: Date;
        updated_at: Date;
        progress?: number | undefined;
        started_at?: Date | undefined;
        completed_at?: Date | undefined;
        error_message?: string | undefined;
    } | undefined;
    error?: {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    } | undefined;
    timestamp?: Date | undefined;
}>;
export declare const GetBacktestResultsApiResponseSchema: z.ZodObject<{
    success: z.ZodBoolean;
    data: z.ZodOptional<z.ZodObject<{
        backtest_id: z.ZodString;
        config: z.ZodEffects<z.ZodObject<{
            name: z.ZodString;
            description: z.ZodOptional<z.ZodString>;
            symbols: z.ZodArray<z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>, "many">;
            start_date: z.ZodDate;
            end_date: z.ZodDate;
            initial_balance: z.ZodDefault<z.ZodNumber>;
            strategy: z.ZodObject<{
                name: z.ZodString;
                parameters: z.ZodRecord<z.ZodString, z.ZodAny>;
            }, "strip", z.ZodTypeAny, {
                name: string;
                parameters: Record<string, any>;
            }, {
                name: string;
                parameters: Record<string, any>;
            }>;
            risk_management: z.ZodObject<{
                max_risk_per_trade: z.ZodDefault<z.ZodNumber>;
                max_concurrent_trades: z.ZodDefault<z.ZodNumber>;
                stop_loss_pips: z.ZodOptional<z.ZodNumber>;
                take_profit_pips: z.ZodOptional<z.ZodNumber>;
            }, "strip", z.ZodTypeAny, {
                max_risk_per_trade: number;
                max_concurrent_trades: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            }, {
                max_risk_per_trade?: number | undefined;
                max_concurrent_trades?: number | undefined;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            }>;
        }, "strip", z.ZodTypeAny, {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            initial_balance: number;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade: number;
                max_concurrent_trades: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
        }, {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade?: number | undefined;
                max_concurrent_trades?: number | undefined;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
            initial_balance?: number | undefined;
        }>, {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            initial_balance: number;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade: number;
                max_concurrent_trades: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
        }, {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade?: number | undefined;
                max_concurrent_trades?: number | undefined;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
            initial_balance?: number | undefined;
        }>;
        metrics: z.ZodObject<{
            total_trades: z.ZodNumber;
            winning_trades: z.ZodNumber;
            losing_trades: z.ZodNumber;
            win_rate: z.ZodNumber;
            total_pnl: z.ZodNumber;
            gross_profit: z.ZodNumber;
            gross_loss: z.ZodNumber;
            profit_factor: z.ZodNumber;
            max_drawdown: z.ZodNumber;
            max_drawdown_percent: z.ZodNumber;
            sharpe_ratio: z.ZodOptional<z.ZodNumber>;
            sortino_ratio: z.ZodOptional<z.ZodNumber>;
            average_win: z.ZodNumber;
            average_loss: z.ZodNumber;
            largest_win: z.ZodNumber;
            largest_loss: z.ZodNumber;
            average_trade_duration_minutes: z.ZodOptional<z.ZodNumber>;
            total_time_in_market_minutes: z.ZodOptional<z.ZodNumber>;
            expectancy: z.ZodNumber;
            kelly_criterion: z.ZodOptional<z.ZodNumber>;
            calmar_ratio: z.ZodOptional<z.ZodNumber>;
        }, "strip", z.ZodTypeAny, {
            profit_factor: number;
            win_rate: number;
            max_drawdown: number;
            total_trades: number;
            winning_trades: number;
            losing_trades: number;
            total_pnl: number;
            average_win: number;
            average_loss: number;
            gross_profit: number;
            gross_loss: number;
            max_drawdown_percent: number;
            largest_win: number;
            largest_loss: number;
            expectancy: number;
            sharpe_ratio?: number | undefined;
            sortino_ratio?: number | undefined;
            average_trade_duration_minutes?: number | undefined;
            total_time_in_market_minutes?: number | undefined;
            kelly_criterion?: number | undefined;
            calmar_ratio?: number | undefined;
        }, {
            profit_factor: number;
            win_rate: number;
            max_drawdown: number;
            total_trades: number;
            winning_trades: number;
            losing_trades: number;
            total_pnl: number;
            average_win: number;
            average_loss: number;
            gross_profit: number;
            gross_loss: number;
            max_drawdown_percent: number;
            largest_win: number;
            largest_loss: number;
            expectancy: number;
            sharpe_ratio?: number | undefined;
            sortino_ratio?: number | undefined;
            average_trade_duration_minutes?: number | undefined;
            total_time_in_market_minutes?: number | undefined;
            kelly_criterion?: number | undefined;
            calmar_ratio?: number | undefined;
        }>;
        trades: z.ZodArray<z.ZodObject<{
            entry_time: z.ZodDate;
            exit_time: z.ZodOptional<z.ZodDate>;
            symbol: z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>;
            order_type: z.ZodEnum<["buy", "sell"]>;
            entry_price: z.ZodNumber;
            exit_price: z.ZodOptional<z.ZodNumber>;
            volume: z.ZodNumber;
            pnl: z.ZodNumber;
            pnl_pips: z.ZodNumber;
            duration_minutes: z.ZodOptional<z.ZodNumber>;
            reason: z.ZodOptional<z.ZodEnum<["stop_loss", "take_profit", "strategy_exit", "timeout"]>>;
        }, "strip", z.ZodTypeAny, {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            volume: number;
            order_type: "buy" | "sell";
            pnl: number;
            entry_time: Date;
            entry_price: number;
            pnl_pips: number;
            exit_time?: Date | undefined;
            exit_price?: number | undefined;
            duration_minutes?: number | undefined;
            reason?: "stop_loss" | "take_profit" | "strategy_exit" | "timeout" | undefined;
        }, {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            volume: number;
            order_type: "buy" | "sell";
            pnl: number;
            entry_time: Date;
            entry_price: number;
            pnl_pips: number;
            exit_time?: Date | undefined;
            exit_price?: number | undefined;
            duration_minutes?: number | undefined;
            reason?: "stop_loss" | "take_profit" | "strategy_exit" | "timeout" | undefined;
        }>, "many">;
        balance_curve: z.ZodArray<z.ZodObject<{
            timestamp: z.ZodDate;
            balance: z.ZodNumber;
            equity: z.ZodNumber;
            drawdown: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            timestamp: Date;
            balance: number;
            equity: number;
            drawdown: number;
        }, {
            timestamp: Date;
            balance: number;
            equity: number;
            drawdown: number;
        }>, "many">;
        monthly_returns: z.ZodArray<z.ZodObject<{
            year: z.ZodNumber;
            month: z.ZodNumber;
            return_percent: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            year: number;
            month: number;
            return_percent: number;
        }, {
            year: number;
            month: number;
            return_percent: number;
        }>, "many">;
        created_at: z.ZodDate;
    }, "strip", z.ZodTypeAny, {
        config: {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            initial_balance: number;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade: number;
                max_concurrent_trades: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
        };
        trades: {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            volume: number;
            order_type: "buy" | "sell";
            pnl: number;
            entry_time: Date;
            entry_price: number;
            pnl_pips: number;
            exit_time?: Date | undefined;
            exit_price?: number | undefined;
            duration_minutes?: number | undefined;
            reason?: "stop_loss" | "take_profit" | "strategy_exit" | "timeout" | undefined;
        }[];
        created_at: Date;
        backtest_id: string;
        metrics: {
            profit_factor: number;
            win_rate: number;
            max_drawdown: number;
            total_trades: number;
            winning_trades: number;
            losing_trades: number;
            total_pnl: number;
            average_win: number;
            average_loss: number;
            gross_profit: number;
            gross_loss: number;
            max_drawdown_percent: number;
            largest_win: number;
            largest_loss: number;
            expectancy: number;
            sharpe_ratio?: number | undefined;
            sortino_ratio?: number | undefined;
            average_trade_duration_minutes?: number | undefined;
            total_time_in_market_minutes?: number | undefined;
            kelly_criterion?: number | undefined;
            calmar_ratio?: number | undefined;
        };
        balance_curve: {
            timestamp: Date;
            balance: number;
            equity: number;
            drawdown: number;
        }[];
        monthly_returns: {
            year: number;
            month: number;
            return_percent: number;
        }[];
    }, {
        config: {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade?: number | undefined;
                max_concurrent_trades?: number | undefined;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
            initial_balance?: number | undefined;
        };
        trades: {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            volume: number;
            order_type: "buy" | "sell";
            pnl: number;
            entry_time: Date;
            entry_price: number;
            pnl_pips: number;
            exit_time?: Date | undefined;
            exit_price?: number | undefined;
            duration_minutes?: number | undefined;
            reason?: "stop_loss" | "take_profit" | "strategy_exit" | "timeout" | undefined;
        }[];
        created_at: Date;
        backtest_id: string;
        metrics: {
            profit_factor: number;
            win_rate: number;
            max_drawdown: number;
            total_trades: number;
            winning_trades: number;
            losing_trades: number;
            total_pnl: number;
            average_win: number;
            average_loss: number;
            gross_profit: number;
            gross_loss: number;
            max_drawdown_percent: number;
            largest_win: number;
            largest_loss: number;
            expectancy: number;
            sharpe_ratio?: number | undefined;
            sortino_ratio?: number | undefined;
            average_trade_duration_minutes?: number | undefined;
            total_time_in_market_minutes?: number | undefined;
            kelly_criterion?: number | undefined;
            calmar_ratio?: number | undefined;
        };
        balance_curve: {
            timestamp: Date;
            balance: number;
            equity: number;
            drawdown: number;
        }[];
        monthly_returns: {
            year: number;
            month: number;
            return_percent: number;
        }[];
    }>>;
    error: z.ZodOptional<z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    }, {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    }>>;
    timestamp: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    timestamp: Date;
    data?: {
        config: {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            initial_balance: number;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade: number;
                max_concurrent_trades: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
        };
        trades: {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            volume: number;
            order_type: "buy" | "sell";
            pnl: number;
            entry_time: Date;
            entry_price: number;
            pnl_pips: number;
            exit_time?: Date | undefined;
            exit_price?: number | undefined;
            duration_minutes?: number | undefined;
            reason?: "stop_loss" | "take_profit" | "strategy_exit" | "timeout" | undefined;
        }[];
        created_at: Date;
        backtest_id: string;
        metrics: {
            profit_factor: number;
            win_rate: number;
            max_drawdown: number;
            total_trades: number;
            winning_trades: number;
            losing_trades: number;
            total_pnl: number;
            average_win: number;
            average_loss: number;
            gross_profit: number;
            gross_loss: number;
            max_drawdown_percent: number;
            largest_win: number;
            largest_loss: number;
            expectancy: number;
            sharpe_ratio?: number | undefined;
            sortino_ratio?: number | undefined;
            average_trade_duration_minutes?: number | undefined;
            total_time_in_market_minutes?: number | undefined;
            kelly_criterion?: number | undefined;
            calmar_ratio?: number | undefined;
        };
        balance_curve: {
            timestamp: Date;
            balance: number;
            equity: number;
            drawdown: number;
        }[];
        monthly_returns: {
            year: number;
            month: number;
            return_percent: number;
        }[];
    } | undefined;
    error?: {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    } | undefined;
}, {
    success: boolean;
    data?: {
        config: {
            strategy: {
                name: string;
                parameters: Record<string, any>;
            };
            name: string;
            start_date: Date;
            end_date: Date;
            symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
            risk_management: {
                max_risk_per_trade?: number | undefined;
                max_concurrent_trades?: number | undefined;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            description?: string | undefined;
            initial_balance?: number | undefined;
        };
        trades: {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            volume: number;
            order_type: "buy" | "sell";
            pnl: number;
            entry_time: Date;
            entry_price: number;
            pnl_pips: number;
            exit_time?: Date | undefined;
            exit_price?: number | undefined;
            duration_minutes?: number | undefined;
            reason?: "stop_loss" | "take_profit" | "strategy_exit" | "timeout" | undefined;
        }[];
        created_at: Date;
        backtest_id: string;
        metrics: {
            profit_factor: number;
            win_rate: number;
            max_drawdown: number;
            total_trades: number;
            winning_trades: number;
            losing_trades: number;
            total_pnl: number;
            average_win: number;
            average_loss: number;
            gross_profit: number;
            gross_loss: number;
            max_drawdown_percent: number;
            largest_win: number;
            largest_loss: number;
            expectancy: number;
            sharpe_ratio?: number | undefined;
            sortino_ratio?: number | undefined;
            average_trade_duration_minutes?: number | undefined;
            total_time_in_market_minutes?: number | undefined;
            kelly_criterion?: number | undefined;
            calmar_ratio?: number | undefined;
        };
        balance_curve: {
            timestamp: Date;
            balance: number;
            equity: number;
            drawdown: number;
        }[];
        monthly_returns: {
            year: number;
            month: number;
            return_percent: number;
        }[];
    } | undefined;
    error?: {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    } | undefined;
    timestamp?: Date | undefined;
}>;
export declare const DeleteBacktestApiResponseSchema: z.ZodObject<{
    success: z.ZodBoolean;
    data: z.ZodOptional<z.ZodObject<{
        deleted: z.ZodBoolean;
    }, "strip", z.ZodTypeAny, {
        deleted: boolean;
    }, {
        deleted: boolean;
    }>>;
    error: z.ZodOptional<z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    }, {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    }>>;
    timestamp: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    timestamp: Date;
    data?: {
        deleted: boolean;
    } | undefined;
    error?: {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    } | undefined;
}, {
    success: boolean;
    data?: {
        deleted: boolean;
    } | undefined;
    error?: {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    } | undefined;
    timestamp?: Date | undefined;
}>;
export declare const CreateChatSessionApiRequestSchema: z.ZodObject<{
    title: z.ZodOptional<z.ZodString>;
    context: z.ZodOptional<z.ZodObject<{
        trading_symbols: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        timeframe: z.ZodOptional<z.ZodString>;
        strategy_focus: z.ZodOptional<z.ZodString>;
        risk_tolerance: z.ZodOptional<z.ZodEnum<["low", "medium", "high"]>>;
    }, "strip", z.ZodTypeAny, {
        timeframe?: string | undefined;
        trading_symbols?: string[] | undefined;
        strategy_focus?: string | undefined;
        risk_tolerance?: "low" | "medium" | "high" | undefined;
    }, {
        timeframe?: string | undefined;
        trading_symbols?: string[] | undefined;
        strategy_focus?: string | undefined;
        risk_tolerance?: "low" | "medium" | "high" | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    context?: {
        timeframe?: string | undefined;
        trading_symbols?: string[] | undefined;
        strategy_focus?: string | undefined;
        risk_tolerance?: "low" | "medium" | "high" | undefined;
    } | undefined;
    title?: string | undefined;
}, {
    context?: {
        timeframe?: string | undefined;
        trading_symbols?: string[] | undefined;
        strategy_focus?: string | undefined;
        risk_tolerance?: "low" | "medium" | "high" | undefined;
    } | undefined;
    title?: string | undefined;
}>;
export declare const CreateChatSessionApiResponseSchema: z.ZodObject<{
    success: z.ZodBoolean;
    data: z.ZodOptional<z.ZodObject<{
        id: z.ZodString;
        user_id: z.ZodString;
        title: z.ZodString;
        context: z.ZodObject<{
            trading_symbols: z.ZodOptional<z.ZodArray<z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>, "many">>;
            timeframe: z.ZodOptional<z.ZodString>;
            strategy_focus: z.ZodOptional<z.ZodString>;
            risk_tolerance: z.ZodOptional<z.ZodEnum<["low", "medium", "high"]>>;
        }, "strip", z.ZodTypeAny, {
            timeframe?: string | undefined;
            trading_symbols?: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[] | undefined;
            strategy_focus?: string | undefined;
            risk_tolerance?: "low" | "medium" | "high" | undefined;
        }, {
            timeframe?: string | undefined;
            trading_symbols?: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[] | undefined;
            strategy_focus?: string | undefined;
            risk_tolerance?: "low" | "medium" | "high" | undefined;
        }>;
        created_at: z.ZodDate;
        updated_at: z.ZodDate;
        last_activity: z.ZodDate;
    }, "strip", z.ZodTypeAny, {
        id: string;
        context: {
            timeframe?: string | undefined;
            trading_symbols?: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[] | undefined;
            strategy_focus?: string | undefined;
            risk_tolerance?: "low" | "medium" | "high" | undefined;
        };
        user_id: string;
        created_at: Date;
        updated_at: Date;
        title: string;
        last_activity: Date;
    }, {
        id: string;
        context: {
            timeframe?: string | undefined;
            trading_symbols?: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[] | undefined;
            strategy_focus?: string | undefined;
            risk_tolerance?: "low" | "medium" | "high" | undefined;
        };
        user_id: string;
        created_at: Date;
        updated_at: Date;
        title: string;
        last_activity: Date;
    }>>;
    error: z.ZodOptional<z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    }, {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    }>>;
    timestamp: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    timestamp: Date;
    data?: {
        id: string;
        context: {
            timeframe?: string | undefined;
            trading_symbols?: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[] | undefined;
            strategy_focus?: string | undefined;
            risk_tolerance?: "low" | "medium" | "high" | undefined;
        };
        user_id: string;
        created_at: Date;
        updated_at: Date;
        title: string;
        last_activity: Date;
    } | undefined;
    error?: {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    } | undefined;
}, {
    success: boolean;
    data?: {
        id: string;
        context: {
            timeframe?: string | undefined;
            trading_symbols?: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[] | undefined;
            strategy_focus?: string | undefined;
            risk_tolerance?: "low" | "medium" | "high" | undefined;
        };
        user_id: string;
        created_at: Date;
        updated_at: Date;
        title: string;
        last_activity: Date;
    } | undefined;
    error?: {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    } | undefined;
    timestamp?: Date | undefined;
}>;
export declare const GetChatSessionsApiRequestSchema: z.ZodObject<{
    page: z.ZodDefault<z.ZodNumber>;
    limit: z.ZodDefault<z.ZodNumber>;
    sortBy: z.ZodOptional<z.ZodString>;
    sortOrder: z.ZodDefault<z.ZodEnum<["asc", "desc"]>>;
}, "strip", z.ZodTypeAny, {
    limit: number;
    page: number;
    sortOrder: "asc" | "desc";
    sortBy?: string | undefined;
}, {
    limit?: number | undefined;
    page?: number | undefined;
    sortBy?: string | undefined;
    sortOrder?: "asc" | "desc" | undefined;
}>;
export declare const GetChatSessionsApiResponseSchema: z.ZodObject<{
    success: z.ZodBoolean;
    data: z.ZodOptional<z.ZodObject<{
        sessions: z.ZodArray<z.ZodObject<{
            id: z.ZodString;
            user_id: z.ZodString;
            title: z.ZodString;
            context: z.ZodObject<{
                trading_symbols: z.ZodOptional<z.ZodArray<z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>, "many">>;
                timeframe: z.ZodOptional<z.ZodString>;
                strategy_focus: z.ZodOptional<z.ZodString>;
                risk_tolerance: z.ZodOptional<z.ZodEnum<["low", "medium", "high"]>>;
            }, "strip", z.ZodTypeAny, {
                timeframe?: string | undefined;
                trading_symbols?: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[] | undefined;
                strategy_focus?: string | undefined;
                risk_tolerance?: "low" | "medium" | "high" | undefined;
            }, {
                timeframe?: string | undefined;
                trading_symbols?: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[] | undefined;
                strategy_focus?: string | undefined;
                risk_tolerance?: "low" | "medium" | "high" | undefined;
            }>;
            created_at: z.ZodDate;
            updated_at: z.ZodDate;
            last_activity: z.ZodDate;
        }, "strip", z.ZodTypeAny, {
            id: string;
            context: {
                timeframe?: string | undefined;
                trading_symbols?: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[] | undefined;
                strategy_focus?: string | undefined;
                risk_tolerance?: "low" | "medium" | "high" | undefined;
            };
            user_id: string;
            created_at: Date;
            updated_at: Date;
            title: string;
            last_activity: Date;
        }, {
            id: string;
            context: {
                timeframe?: string | undefined;
                trading_symbols?: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[] | undefined;
                strategy_focus?: string | undefined;
                risk_tolerance?: "low" | "medium" | "high" | undefined;
            };
            user_id: string;
            created_at: Date;
            updated_at: Date;
            title: string;
            last_activity: Date;
        }>, "many">;
        pagination: z.ZodObject<{
            page: z.ZodNumber;
            limit: z.ZodNumber;
            total: z.ZodNumber;
            totalPages: z.ZodNumber;
            hasNext: z.ZodBoolean;
            hasPrev: z.ZodBoolean;
        }, "strip", z.ZodTypeAny, {
            limit: number;
            page: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        }, {
            limit: number;
            page: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        }>;
    }, "strip", z.ZodTypeAny, {
        pagination: {
            limit: number;
            page: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
        sessions: {
            id: string;
            context: {
                timeframe?: string | undefined;
                trading_symbols?: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[] | undefined;
                strategy_focus?: string | undefined;
                risk_tolerance?: "low" | "medium" | "high" | undefined;
            };
            user_id: string;
            created_at: Date;
            updated_at: Date;
            title: string;
            last_activity: Date;
        }[];
    }, {
        pagination: {
            limit: number;
            page: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
        sessions: {
            id: string;
            context: {
                timeframe?: string | undefined;
                trading_symbols?: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[] | undefined;
                strategy_focus?: string | undefined;
                risk_tolerance?: "low" | "medium" | "high" | undefined;
            };
            user_id: string;
            created_at: Date;
            updated_at: Date;
            title: string;
            last_activity: Date;
        }[];
    }>>;
    error: z.ZodOptional<z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    }, {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    }>>;
    timestamp: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    timestamp: Date;
    data?: {
        pagination: {
            limit: number;
            page: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
        sessions: {
            id: string;
            context: {
                timeframe?: string | undefined;
                trading_symbols?: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[] | undefined;
                strategy_focus?: string | undefined;
                risk_tolerance?: "low" | "medium" | "high" | undefined;
            };
            user_id: string;
            created_at: Date;
            updated_at: Date;
            title: string;
            last_activity: Date;
        }[];
    } | undefined;
    error?: {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    } | undefined;
}, {
    success: boolean;
    data?: {
        pagination: {
            limit: number;
            page: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
        sessions: {
            id: string;
            context: {
                timeframe?: string | undefined;
                trading_symbols?: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[] | undefined;
                strategy_focus?: string | undefined;
                risk_tolerance?: "low" | "medium" | "high" | undefined;
            };
            user_id: string;
            created_at: Date;
            updated_at: Date;
            title: string;
            last_activity: Date;
        }[];
    } | undefined;
    error?: {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    } | undefined;
    timestamp?: Date | undefined;
}>;
export declare const GetChatMessagesApiRequestSchema: z.ZodObject<{
    page: z.ZodDefault<z.ZodNumber>;
    limit: z.ZodDefault<z.ZodNumber>;
    sortBy: z.ZodOptional<z.ZodString>;
    sortOrder: z.ZodDefault<z.ZodEnum<["asc", "desc"]>>;
}, "strip", z.ZodTypeAny, {
    limit: number;
    page: number;
    sortOrder: "asc" | "desc";
    sortBy?: string | undefined;
}, {
    limit?: number | undefined;
    page?: number | undefined;
    sortBy?: string | undefined;
    sortOrder?: "asc" | "desc" | undefined;
}>;
export declare const GetChatMessagesApiResponseSchema: z.ZodObject<{
    success: z.ZodBoolean;
    data: z.ZodOptional<z.ZodObject<{
        messages: z.ZodArray<z.ZodObject<{
            id: z.ZodString;
            role: z.ZodEnum<["user", "assistant", "system"]>;
            content: z.ZodString;
            type: z.ZodDefault<z.ZodEnum<["text", "analysis", "prediction", "strategy", "market_data", "error"]>>;
            metadata: z.ZodObject<{
                timestamp: z.ZodDate;
                confidence: z.ZodOptional<z.ZodNumber>;
                sources: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
                attachments: z.ZodOptional<z.ZodArray<z.ZodObject<{
                    type: z.ZodEnum<["chart", "table", "document"]>;
                    url: z.ZodOptional<z.ZodString>;
                    data: z.ZodOptional<z.ZodAny>;
                }, "strip", z.ZodTypeAny, {
                    type: "chart" | "table" | "document";
                    data?: any;
                    url?: string | undefined;
                }, {
                    type: "chart" | "table" | "document";
                    data?: any;
                    url?: string | undefined;
                }>, "many">>;
            }, "strip", z.ZodTypeAny, {
                timestamp: Date;
                confidence?: number | undefined;
                sources?: string[] | undefined;
                attachments?: {
                    type: "chart" | "table" | "document";
                    data?: any;
                    url?: string | undefined;
                }[] | undefined;
            }, {
                timestamp: Date;
                confidence?: number | undefined;
                sources?: string[] | undefined;
                attachments?: {
                    type: "chart" | "table" | "document";
                    data?: any;
                    url?: string | undefined;
                }[] | undefined;
            }>;
            created_at: z.ZodDate;
        }, "strip", z.ZodTypeAny, {
            type: "analysis" | "strategy" | "error" | "market_data" | "text" | "prediction";
            id: string;
            metadata: {
                timestamp: Date;
                confidence?: number | undefined;
                sources?: string[] | undefined;
                attachments?: {
                    type: "chart" | "table" | "document";
                    data?: any;
                    url?: string | undefined;
                }[] | undefined;
            };
            created_at: Date;
            role: "system" | "user" | "assistant";
            content: string;
        }, {
            id: string;
            metadata: {
                timestamp: Date;
                confidence?: number | undefined;
                sources?: string[] | undefined;
                attachments?: {
                    type: "chart" | "table" | "document";
                    data?: any;
                    url?: string | undefined;
                }[] | undefined;
            };
            created_at: Date;
            role: "system" | "user" | "assistant";
            content: string;
            type?: "analysis" | "strategy" | "error" | "market_data" | "text" | "prediction" | undefined;
        }>, "many">;
        pagination: z.ZodObject<{
            page: z.ZodNumber;
            limit: z.ZodNumber;
            total: z.ZodNumber;
            totalPages: z.ZodNumber;
            hasNext: z.ZodBoolean;
            hasPrev: z.ZodBoolean;
        }, "strip", z.ZodTypeAny, {
            limit: number;
            page: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        }, {
            limit: number;
            page: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        }>;
    }, "strip", z.ZodTypeAny, {
        pagination: {
            limit: number;
            page: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
        messages: {
            type: "analysis" | "strategy" | "error" | "market_data" | "text" | "prediction";
            id: string;
            metadata: {
                timestamp: Date;
                confidence?: number | undefined;
                sources?: string[] | undefined;
                attachments?: {
                    type: "chart" | "table" | "document";
                    data?: any;
                    url?: string | undefined;
                }[] | undefined;
            };
            created_at: Date;
            role: "system" | "user" | "assistant";
            content: string;
        }[];
    }, {
        pagination: {
            limit: number;
            page: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
        messages: {
            id: string;
            metadata: {
                timestamp: Date;
                confidence?: number | undefined;
                sources?: string[] | undefined;
                attachments?: {
                    type: "chart" | "table" | "document";
                    data?: any;
                    url?: string | undefined;
                }[] | undefined;
            };
            created_at: Date;
            role: "system" | "user" | "assistant";
            content: string;
            type?: "analysis" | "strategy" | "error" | "market_data" | "text" | "prediction" | undefined;
        }[];
    }>>;
    error: z.ZodOptional<z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    }, {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    }>>;
    timestamp: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    timestamp: Date;
    data?: {
        pagination: {
            limit: number;
            page: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
        messages: {
            type: "analysis" | "strategy" | "error" | "market_data" | "text" | "prediction";
            id: string;
            metadata: {
                timestamp: Date;
                confidence?: number | undefined;
                sources?: string[] | undefined;
                attachments?: {
                    type: "chart" | "table" | "document";
                    data?: any;
                    url?: string | undefined;
                }[] | undefined;
            };
            created_at: Date;
            role: "system" | "user" | "assistant";
            content: string;
        }[];
    } | undefined;
    error?: {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    } | undefined;
}, {
    success: boolean;
    data?: {
        pagination: {
            limit: number;
            page: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
        messages: {
            id: string;
            metadata: {
                timestamp: Date;
                confidence?: number | undefined;
                sources?: string[] | undefined;
                attachments?: {
                    type: "chart" | "table" | "document";
                    data?: any;
                    url?: string | undefined;
                }[] | undefined;
            };
            created_at: Date;
            role: "system" | "user" | "assistant";
            content: string;
            type?: "analysis" | "strategy" | "error" | "market_data" | "text" | "prediction" | undefined;
        }[];
    } | undefined;
    error?: {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    } | undefined;
    timestamp?: Date | undefined;
}>;
export declare const SendChatMessageApiRequestSchema: z.ZodObject<{
    message: z.ZodString;
    session_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    message: string;
    session_id: string;
}, {
    message: string;
    session_id: string;
}>;
export declare const SendChatMessageApiResponseSchema: z.ZodObject<{
    success: z.ZodBoolean;
    data: z.ZodOptional<z.ZodObject<{
        message: z.ZodString;
        type: z.ZodEnum<["text", "analysis", "prediction", "strategy", "market_data", "error"]>;
        confidence: z.ZodNumber;
        analysis: z.ZodOptional<z.ZodObject<{
            market_sentiment: z.ZodOptional<z.ZodEnum<["bullish", "bearish", "neutral"]>>;
            key_levels: z.ZodOptional<z.ZodArray<z.ZodObject<{
                symbol: z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>;
                level: z.ZodNumber;
                type: z.ZodEnum<["support", "resistance"]>;
            }, "strip", z.ZodTypeAny, {
                symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
                type: "support" | "resistance";
                level: number;
            }, {
                symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
                type: "support" | "resistance";
                level: number;
            }>, "many">>;
            risk_assessment: z.ZodOptional<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            key_levels?: {
                symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
                type: "support" | "resistance";
                level: number;
            }[] | undefined;
            market_sentiment?: "bullish" | "bearish" | "neutral" | undefined;
            risk_assessment?: string | undefined;
        }, {
            key_levels?: {
                symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
                type: "support" | "resistance";
                level: number;
            }[] | undefined;
            market_sentiment?: "bullish" | "bearish" | "neutral" | undefined;
            risk_assessment?: string | undefined;
        }>>;
        suggested_actions: z.ZodOptional<z.ZodArray<z.ZodObject<{
            type: z.ZodEnum<["buy", "sell", "hold", "analyze", "backtest"]>;
            symbol: z.ZodOptional<z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>>;
            reasoning: z.ZodString;
            confidence: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            type: "buy" | "sell" | "hold" | "analyze" | "backtest";
            confidence: number;
            reasoning: string;
            symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        }, {
            type: "buy" | "sell" | "hold" | "analyze" | "backtest";
            confidence: number;
            reasoning: string;
            symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        }>, "many">>;
        sources: z.ZodOptional<z.ZodArray<z.ZodObject<{
            type: z.ZodEnum<["knowledge_graph", "market_data", "historical_analysis"]>;
            content: z.ZodString;
            relevance: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            type: "market_data" | "knowledge_graph" | "historical_analysis";
            content: string;
            relevance: number;
        }, {
            type: "market_data" | "knowledge_graph" | "historical_analysis";
            content: string;
            relevance: number;
        }>, "many">>;
        attachments: z.ZodOptional<z.ZodArray<z.ZodObject<{
            type: z.ZodEnum<["chart", "table", "document", "analysis"]>;
            title: z.ZodString;
            data: z.ZodAny;
        }, "strip", z.ZodTypeAny, {
            type: "analysis" | "chart" | "table" | "document";
            title: string;
            data?: any;
        }, {
            type: "analysis" | "chart" | "table" | "document";
            title: string;
            data?: any;
        }>, "many">>;
        timestamp: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        type: "analysis" | "strategy" | "error" | "market_data" | "text" | "prediction";
        message: string;
        confidence: number;
        timestamp: Date;
        analysis?: {
            key_levels?: {
                symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
                type: "support" | "resistance";
                level: number;
            }[] | undefined;
            market_sentiment?: "bullish" | "bearish" | "neutral" | undefined;
            risk_assessment?: string | undefined;
        } | undefined;
        sources?: {
            type: "market_data" | "knowledge_graph" | "historical_analysis";
            content: string;
            relevance: number;
        }[] | undefined;
        suggested_actions?: {
            type: "buy" | "sell" | "hold" | "analyze" | "backtest";
            confidence: number;
            reasoning: string;
            symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        }[] | undefined;
        attachments?: {
            type: "analysis" | "chart" | "table" | "document";
            title: string;
            data?: any;
        }[] | undefined;
    }, {
        type: "analysis" | "strategy" | "error" | "market_data" | "text" | "prediction";
        message: string;
        confidence: number;
        analysis?: {
            key_levels?: {
                symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
                type: "support" | "resistance";
                level: number;
            }[] | undefined;
            market_sentiment?: "bullish" | "bearish" | "neutral" | undefined;
            risk_assessment?: string | undefined;
        } | undefined;
        timestamp?: Date | undefined;
        sources?: {
            type: "market_data" | "knowledge_graph" | "historical_analysis";
            content: string;
            relevance: number;
        }[] | undefined;
        suggested_actions?: {
            type: "buy" | "sell" | "hold" | "analyze" | "backtest";
            confidence: number;
            reasoning: string;
            symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        }[] | undefined;
        attachments?: {
            type: "analysis" | "chart" | "table" | "document";
            title: string;
            data?: any;
        }[] | undefined;
    }>>;
    error: z.ZodOptional<z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    }, {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    }>>;
    timestamp: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    timestamp: Date;
    data?: {
        type: "analysis" | "strategy" | "error" | "market_data" | "text" | "prediction";
        message: string;
        confidence: number;
        timestamp: Date;
        analysis?: {
            key_levels?: {
                symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
                type: "support" | "resistance";
                level: number;
            }[] | undefined;
            market_sentiment?: "bullish" | "bearish" | "neutral" | undefined;
            risk_assessment?: string | undefined;
        } | undefined;
        sources?: {
            type: "market_data" | "knowledge_graph" | "historical_analysis";
            content: string;
            relevance: number;
        }[] | undefined;
        suggested_actions?: {
            type: "buy" | "sell" | "hold" | "analyze" | "backtest";
            confidence: number;
            reasoning: string;
            symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        }[] | undefined;
        attachments?: {
            type: "analysis" | "chart" | "table" | "document";
            title: string;
            data?: any;
        }[] | undefined;
    } | undefined;
    error?: {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    } | undefined;
}, {
    success: boolean;
    data?: {
        type: "analysis" | "strategy" | "error" | "market_data" | "text" | "prediction";
        message: string;
        confidence: number;
        analysis?: {
            key_levels?: {
                symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
                type: "support" | "resistance";
                level: number;
            }[] | undefined;
            market_sentiment?: "bullish" | "bearish" | "neutral" | undefined;
            risk_assessment?: string | undefined;
        } | undefined;
        timestamp?: Date | undefined;
        sources?: {
            type: "market_data" | "knowledge_graph" | "historical_analysis";
            content: string;
            relevance: number;
        }[] | undefined;
        suggested_actions?: {
            type: "buy" | "sell" | "hold" | "analyze" | "backtest";
            confidence: number;
            reasoning: string;
            symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        }[] | undefined;
        attachments?: {
            type: "analysis" | "chart" | "table" | "document";
            title: string;
            data?: any;
        }[] | undefined;
    } | undefined;
    error?: {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    } | undefined;
    timestamp?: Date | undefined;
}>;
export declare const CreateUploadApiRequestSchema: z.ZodObject<{
    filename: z.ZodString;
    file_size: z.ZodNumber;
    mime_type: z.ZodString;
    symbol: z.ZodOptional<z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>>;
    timeframe: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    filename: string;
    file_size: number;
    mime_type: string;
    symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
    timeframe?: string | undefined;
}, {
    filename: string;
    file_size: number;
    mime_type: string;
    symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
    timeframe?: string | undefined;
}>;
export declare const CreateUploadApiResponseSchema: z.ZodObject<{
    success: z.ZodBoolean;
    data: z.ZodOptional<z.ZodObject<{
        id: z.ZodString;
        user_id: z.ZodString;
        filename: z.ZodString;
        original_filename: z.ZodString;
        file_size: z.ZodNumber;
        mime_type: z.ZodString;
        upload_path: z.ZodString;
        symbol: z.ZodOptional<z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>>;
        timeframe: z.ZodOptional<z.ZodString>;
        status: z.ZodEnum<["pending", "uploading", "mapping", "parsing", "validating", "ready", "error"]>;
        progress: z.ZodDefault<z.ZodNumber>;
        column_mapping: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodEnum<["Time", "Open", "High", "Low", "Close", "Volume", "Bid", "Ask", "Ignore"]>>>;
        validation_errors: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        data_quality: z.ZodOptional<z.ZodObject<{
            total_rows: z.ZodNumber;
            valid_rows: z.ZodNumber;
            invalid_rows: z.ZodNumber;
            date_range: z.ZodOptional<z.ZodObject<{
                start: z.ZodDate;
                end: z.ZodDate;
            }, "strip", z.ZodTypeAny, {
                end: Date;
                start: Date;
            }, {
                end: Date;
                start: Date;
            }>>;
            completeness: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            total_rows: number;
            valid_rows: number;
            invalid_rows: number;
            completeness: number;
            date_range?: {
                end: Date;
                start: Date;
            } | undefined;
        }, {
            total_rows: number;
            valid_rows: number;
            invalid_rows: number;
            completeness: number;
            date_range?: {
                end: Date;
                start: Date;
            } | undefined;
        }>>;
        error_message: z.ZodOptional<z.ZodString>;
        error_details: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        created_at: z.ZodDate;
        updated_at: z.ZodDate;
        processed_at: z.ZodOptional<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        status: "error" | "pending" | "uploading" | "mapping" | "parsing" | "validating" | "ready";
        id: string;
        filename: string;
        user_id: string;
        created_at: Date;
        progress: number;
        updated_at: Date;
        original_filename: string;
        file_size: number;
        mime_type: string;
        upload_path: string;
        symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        timeframe?: string | undefined;
        error_message?: string | undefined;
        column_mapping?: Record<string, "Time" | "Open" | "High" | "Low" | "Close" | "Volume" | "Bid" | "Ask" | "Ignore"> | undefined;
        validation_errors?: string[] | undefined;
        data_quality?: {
            total_rows: number;
            valid_rows: number;
            invalid_rows: number;
            completeness: number;
            date_range?: {
                end: Date;
                start: Date;
            } | undefined;
        } | undefined;
        error_details?: string[] | undefined;
        processed_at?: Date | undefined;
    }, {
        status: "error" | "pending" | "uploading" | "mapping" | "parsing" | "validating" | "ready";
        id: string;
        filename: string;
        user_id: string;
        created_at: Date;
        updated_at: Date;
        original_filename: string;
        file_size: number;
        mime_type: string;
        upload_path: string;
        symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        timeframe?: string | undefined;
        progress?: number | undefined;
        error_message?: string | undefined;
        column_mapping?: Record<string, "Time" | "Open" | "High" | "Low" | "Close" | "Volume" | "Bid" | "Ask" | "Ignore"> | undefined;
        validation_errors?: string[] | undefined;
        data_quality?: {
            total_rows: number;
            valid_rows: number;
            invalid_rows: number;
            completeness: number;
            date_range?: {
                end: Date;
                start: Date;
            } | undefined;
        } | undefined;
        error_details?: string[] | undefined;
        processed_at?: Date | undefined;
    }>>;
    error: z.ZodOptional<z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    }, {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    }>>;
    timestamp: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    timestamp: Date;
    data?: {
        status: "error" | "pending" | "uploading" | "mapping" | "parsing" | "validating" | "ready";
        id: string;
        filename: string;
        user_id: string;
        created_at: Date;
        progress: number;
        updated_at: Date;
        original_filename: string;
        file_size: number;
        mime_type: string;
        upload_path: string;
        symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        timeframe?: string | undefined;
        error_message?: string | undefined;
        column_mapping?: Record<string, "Time" | "Open" | "High" | "Low" | "Close" | "Volume" | "Bid" | "Ask" | "Ignore"> | undefined;
        validation_errors?: string[] | undefined;
        data_quality?: {
            total_rows: number;
            valid_rows: number;
            invalid_rows: number;
            completeness: number;
            date_range?: {
                end: Date;
                start: Date;
            } | undefined;
        } | undefined;
        error_details?: string[] | undefined;
        processed_at?: Date | undefined;
    } | undefined;
    error?: {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    } | undefined;
}, {
    success: boolean;
    data?: {
        status: "error" | "pending" | "uploading" | "mapping" | "parsing" | "validating" | "ready";
        id: string;
        filename: string;
        user_id: string;
        created_at: Date;
        updated_at: Date;
        original_filename: string;
        file_size: number;
        mime_type: string;
        upload_path: string;
        symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        timeframe?: string | undefined;
        progress?: number | undefined;
        error_message?: string | undefined;
        column_mapping?: Record<string, "Time" | "Open" | "High" | "Low" | "Close" | "Volume" | "Bid" | "Ask" | "Ignore"> | undefined;
        validation_errors?: string[] | undefined;
        data_quality?: {
            total_rows: number;
            valid_rows: number;
            invalid_rows: number;
            completeness: number;
            date_range?: {
                end: Date;
                start: Date;
            } | undefined;
        } | undefined;
        error_details?: string[] | undefined;
        processed_at?: Date | undefined;
    } | undefined;
    error?: {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    } | undefined;
    timestamp?: Date | undefined;
}>;
export declare const GetUploadsApiRequestSchema: z.ZodObject<{
    page: z.ZodDefault<z.ZodNumber>;
    limit: z.ZodDefault<z.ZodNumber>;
    sortBy: z.ZodOptional<z.ZodString>;
    sortOrder: z.ZodDefault<z.ZodEnum<["asc", "desc"]>>;
} & {
    status: z.ZodOptional<z.ZodEnum<["pending", "uploading", "mapping", "parsing", "validating", "ready", "error"]>>;
    symbol: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    limit: number;
    page: number;
    sortOrder: "asc" | "desc";
    symbol?: string | undefined;
    status?: "error" | "pending" | "uploading" | "mapping" | "parsing" | "validating" | "ready" | undefined;
    sortBy?: string | undefined;
}, {
    symbol?: string | undefined;
    status?: "error" | "pending" | "uploading" | "mapping" | "parsing" | "validating" | "ready" | undefined;
    limit?: number | undefined;
    page?: number | undefined;
    sortBy?: string | undefined;
    sortOrder?: "asc" | "desc" | undefined;
}>;
export declare const GetUploadsApiResponseSchema: z.ZodObject<{
    success: z.ZodBoolean;
    data: z.ZodOptional<z.ZodObject<{
        uploads: z.ZodArray<z.ZodObject<{
            id: z.ZodString;
            user_id: z.ZodString;
            filename: z.ZodString;
            original_filename: z.ZodString;
            file_size: z.ZodNumber;
            mime_type: z.ZodString;
            upload_path: z.ZodString;
            symbol: z.ZodOptional<z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>>;
            timeframe: z.ZodOptional<z.ZodString>;
            status: z.ZodEnum<["pending", "uploading", "mapping", "parsing", "validating", "ready", "error"]>;
            progress: z.ZodDefault<z.ZodNumber>;
            column_mapping: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodEnum<["Time", "Open", "High", "Low", "Close", "Volume", "Bid", "Ask", "Ignore"]>>>;
            validation_errors: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
            data_quality: z.ZodOptional<z.ZodObject<{
                total_rows: z.ZodNumber;
                valid_rows: z.ZodNumber;
                invalid_rows: z.ZodNumber;
                date_range: z.ZodOptional<z.ZodObject<{
                    start: z.ZodDate;
                    end: z.ZodDate;
                }, "strip", z.ZodTypeAny, {
                    end: Date;
                    start: Date;
                }, {
                    end: Date;
                    start: Date;
                }>>;
                completeness: z.ZodNumber;
            }, "strip", z.ZodTypeAny, {
                total_rows: number;
                valid_rows: number;
                invalid_rows: number;
                completeness: number;
                date_range?: {
                    end: Date;
                    start: Date;
                } | undefined;
            }, {
                total_rows: number;
                valid_rows: number;
                invalid_rows: number;
                completeness: number;
                date_range?: {
                    end: Date;
                    start: Date;
                } | undefined;
            }>>;
            error_message: z.ZodOptional<z.ZodString>;
            error_details: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
            created_at: z.ZodDate;
            updated_at: z.ZodDate;
            processed_at: z.ZodOptional<z.ZodDate>;
        }, "strip", z.ZodTypeAny, {
            status: "error" | "pending" | "uploading" | "mapping" | "parsing" | "validating" | "ready";
            id: string;
            filename: string;
            user_id: string;
            created_at: Date;
            progress: number;
            updated_at: Date;
            original_filename: string;
            file_size: number;
            mime_type: string;
            upload_path: string;
            symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
            timeframe?: string | undefined;
            error_message?: string | undefined;
            column_mapping?: Record<string, "Time" | "Open" | "High" | "Low" | "Close" | "Volume" | "Bid" | "Ask" | "Ignore"> | undefined;
            validation_errors?: string[] | undefined;
            data_quality?: {
                total_rows: number;
                valid_rows: number;
                invalid_rows: number;
                completeness: number;
                date_range?: {
                    end: Date;
                    start: Date;
                } | undefined;
            } | undefined;
            error_details?: string[] | undefined;
            processed_at?: Date | undefined;
        }, {
            status: "error" | "pending" | "uploading" | "mapping" | "parsing" | "validating" | "ready";
            id: string;
            filename: string;
            user_id: string;
            created_at: Date;
            updated_at: Date;
            original_filename: string;
            file_size: number;
            mime_type: string;
            upload_path: string;
            symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
            timeframe?: string | undefined;
            progress?: number | undefined;
            error_message?: string | undefined;
            column_mapping?: Record<string, "Time" | "Open" | "High" | "Low" | "Close" | "Volume" | "Bid" | "Ask" | "Ignore"> | undefined;
            validation_errors?: string[] | undefined;
            data_quality?: {
                total_rows: number;
                valid_rows: number;
                invalid_rows: number;
                completeness: number;
                date_range?: {
                    end: Date;
                    start: Date;
                } | undefined;
            } | undefined;
            error_details?: string[] | undefined;
            processed_at?: Date | undefined;
        }>, "many">;
        pagination: z.ZodObject<{
            page: z.ZodNumber;
            limit: z.ZodNumber;
            total: z.ZodNumber;
            totalPages: z.ZodNumber;
            hasNext: z.ZodBoolean;
            hasPrev: z.ZodBoolean;
        }, "strip", z.ZodTypeAny, {
            limit: number;
            page: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        }, {
            limit: number;
            page: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        }>;
    }, "strip", z.ZodTypeAny, {
        uploads: {
            status: "error" | "pending" | "uploading" | "mapping" | "parsing" | "validating" | "ready";
            id: string;
            filename: string;
            user_id: string;
            created_at: Date;
            progress: number;
            updated_at: Date;
            original_filename: string;
            file_size: number;
            mime_type: string;
            upload_path: string;
            symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
            timeframe?: string | undefined;
            error_message?: string | undefined;
            column_mapping?: Record<string, "Time" | "Open" | "High" | "Low" | "Close" | "Volume" | "Bid" | "Ask" | "Ignore"> | undefined;
            validation_errors?: string[] | undefined;
            data_quality?: {
                total_rows: number;
                valid_rows: number;
                invalid_rows: number;
                completeness: number;
                date_range?: {
                    end: Date;
                    start: Date;
                } | undefined;
            } | undefined;
            error_details?: string[] | undefined;
            processed_at?: Date | undefined;
        }[];
        pagination: {
            limit: number;
            page: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
    }, {
        uploads: {
            status: "error" | "pending" | "uploading" | "mapping" | "parsing" | "validating" | "ready";
            id: string;
            filename: string;
            user_id: string;
            created_at: Date;
            updated_at: Date;
            original_filename: string;
            file_size: number;
            mime_type: string;
            upload_path: string;
            symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
            timeframe?: string | undefined;
            progress?: number | undefined;
            error_message?: string | undefined;
            column_mapping?: Record<string, "Time" | "Open" | "High" | "Low" | "Close" | "Volume" | "Bid" | "Ask" | "Ignore"> | undefined;
            validation_errors?: string[] | undefined;
            data_quality?: {
                total_rows: number;
                valid_rows: number;
                invalid_rows: number;
                completeness: number;
                date_range?: {
                    end: Date;
                    start: Date;
                } | undefined;
            } | undefined;
            error_details?: string[] | undefined;
            processed_at?: Date | undefined;
        }[];
        pagination: {
            limit: number;
            page: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
    }>>;
    error: z.ZodOptional<z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    }, {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    }>>;
    timestamp: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    timestamp: Date;
    data?: {
        uploads: {
            status: "error" | "pending" | "uploading" | "mapping" | "parsing" | "validating" | "ready";
            id: string;
            filename: string;
            user_id: string;
            created_at: Date;
            progress: number;
            updated_at: Date;
            original_filename: string;
            file_size: number;
            mime_type: string;
            upload_path: string;
            symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
            timeframe?: string | undefined;
            error_message?: string | undefined;
            column_mapping?: Record<string, "Time" | "Open" | "High" | "Low" | "Close" | "Volume" | "Bid" | "Ask" | "Ignore"> | undefined;
            validation_errors?: string[] | undefined;
            data_quality?: {
                total_rows: number;
                valid_rows: number;
                invalid_rows: number;
                completeness: number;
                date_range?: {
                    end: Date;
                    start: Date;
                } | undefined;
            } | undefined;
            error_details?: string[] | undefined;
            processed_at?: Date | undefined;
        }[];
        pagination: {
            limit: number;
            page: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
    } | undefined;
    error?: {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    } | undefined;
}, {
    success: boolean;
    data?: {
        uploads: {
            status: "error" | "pending" | "uploading" | "mapping" | "parsing" | "validating" | "ready";
            id: string;
            filename: string;
            user_id: string;
            created_at: Date;
            updated_at: Date;
            original_filename: string;
            file_size: number;
            mime_type: string;
            upload_path: string;
            symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
            timeframe?: string | undefined;
            progress?: number | undefined;
            error_message?: string | undefined;
            column_mapping?: Record<string, "Time" | "Open" | "High" | "Low" | "Close" | "Volume" | "Bid" | "Ask" | "Ignore"> | undefined;
            validation_errors?: string[] | undefined;
            data_quality?: {
                total_rows: number;
                valid_rows: number;
                invalid_rows: number;
                completeness: number;
                date_range?: {
                    end: Date;
                    start: Date;
                } | undefined;
            } | undefined;
            error_details?: string[] | undefined;
            processed_at?: Date | undefined;
        }[];
        pagination: {
            limit: number;
            page: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
    } | undefined;
    error?: {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    } | undefined;
    timestamp?: Date | undefined;
}>;
export declare const GetUploadApiResponseSchema: z.ZodObject<{
    success: z.ZodBoolean;
    data: z.ZodOptional<z.ZodObject<{
        id: z.ZodString;
        user_id: z.ZodString;
        filename: z.ZodString;
        original_filename: z.ZodString;
        file_size: z.ZodNumber;
        mime_type: z.ZodString;
        upload_path: z.ZodString;
        symbol: z.ZodOptional<z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>>;
        timeframe: z.ZodOptional<z.ZodString>;
        status: z.ZodEnum<["pending", "uploading", "mapping", "parsing", "validating", "ready", "error"]>;
        progress: z.ZodDefault<z.ZodNumber>;
        column_mapping: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodEnum<["Time", "Open", "High", "Low", "Close", "Volume", "Bid", "Ask", "Ignore"]>>>;
        validation_errors: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        data_quality: z.ZodOptional<z.ZodObject<{
            total_rows: z.ZodNumber;
            valid_rows: z.ZodNumber;
            invalid_rows: z.ZodNumber;
            date_range: z.ZodOptional<z.ZodObject<{
                start: z.ZodDate;
                end: z.ZodDate;
            }, "strip", z.ZodTypeAny, {
                end: Date;
                start: Date;
            }, {
                end: Date;
                start: Date;
            }>>;
            completeness: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            total_rows: number;
            valid_rows: number;
            invalid_rows: number;
            completeness: number;
            date_range?: {
                end: Date;
                start: Date;
            } | undefined;
        }, {
            total_rows: number;
            valid_rows: number;
            invalid_rows: number;
            completeness: number;
            date_range?: {
                end: Date;
                start: Date;
            } | undefined;
        }>>;
        error_message: z.ZodOptional<z.ZodString>;
        error_details: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        created_at: z.ZodDate;
        updated_at: z.ZodDate;
        processed_at: z.ZodOptional<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        status: "error" | "pending" | "uploading" | "mapping" | "parsing" | "validating" | "ready";
        id: string;
        filename: string;
        user_id: string;
        created_at: Date;
        progress: number;
        updated_at: Date;
        original_filename: string;
        file_size: number;
        mime_type: string;
        upload_path: string;
        symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        timeframe?: string | undefined;
        error_message?: string | undefined;
        column_mapping?: Record<string, "Time" | "Open" | "High" | "Low" | "Close" | "Volume" | "Bid" | "Ask" | "Ignore"> | undefined;
        validation_errors?: string[] | undefined;
        data_quality?: {
            total_rows: number;
            valid_rows: number;
            invalid_rows: number;
            completeness: number;
            date_range?: {
                end: Date;
                start: Date;
            } | undefined;
        } | undefined;
        error_details?: string[] | undefined;
        processed_at?: Date | undefined;
    }, {
        status: "error" | "pending" | "uploading" | "mapping" | "parsing" | "validating" | "ready";
        id: string;
        filename: string;
        user_id: string;
        created_at: Date;
        updated_at: Date;
        original_filename: string;
        file_size: number;
        mime_type: string;
        upload_path: string;
        symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        timeframe?: string | undefined;
        progress?: number | undefined;
        error_message?: string | undefined;
        column_mapping?: Record<string, "Time" | "Open" | "High" | "Low" | "Close" | "Volume" | "Bid" | "Ask" | "Ignore"> | undefined;
        validation_errors?: string[] | undefined;
        data_quality?: {
            total_rows: number;
            valid_rows: number;
            invalid_rows: number;
            completeness: number;
            date_range?: {
                end: Date;
                start: Date;
            } | undefined;
        } | undefined;
        error_details?: string[] | undefined;
        processed_at?: Date | undefined;
    }>>;
    error: z.ZodOptional<z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    }, {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    }>>;
    timestamp: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    timestamp: Date;
    data?: {
        status: "error" | "pending" | "uploading" | "mapping" | "parsing" | "validating" | "ready";
        id: string;
        filename: string;
        user_id: string;
        created_at: Date;
        progress: number;
        updated_at: Date;
        original_filename: string;
        file_size: number;
        mime_type: string;
        upload_path: string;
        symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        timeframe?: string | undefined;
        error_message?: string | undefined;
        column_mapping?: Record<string, "Time" | "Open" | "High" | "Low" | "Close" | "Volume" | "Bid" | "Ask" | "Ignore"> | undefined;
        validation_errors?: string[] | undefined;
        data_quality?: {
            total_rows: number;
            valid_rows: number;
            invalid_rows: number;
            completeness: number;
            date_range?: {
                end: Date;
                start: Date;
            } | undefined;
        } | undefined;
        error_details?: string[] | undefined;
        processed_at?: Date | undefined;
    } | undefined;
    error?: {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    } | undefined;
}, {
    success: boolean;
    data?: {
        status: "error" | "pending" | "uploading" | "mapping" | "parsing" | "validating" | "ready";
        id: string;
        filename: string;
        user_id: string;
        created_at: Date;
        updated_at: Date;
        original_filename: string;
        file_size: number;
        mime_type: string;
        upload_path: string;
        symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        timeframe?: string | undefined;
        progress?: number | undefined;
        error_message?: string | undefined;
        column_mapping?: Record<string, "Time" | "Open" | "High" | "Low" | "Close" | "Volume" | "Bid" | "Ask" | "Ignore"> | undefined;
        validation_errors?: string[] | undefined;
        data_quality?: {
            total_rows: number;
            valid_rows: number;
            invalid_rows: number;
            completeness: number;
            date_range?: {
                end: Date;
                start: Date;
            } | undefined;
        } | undefined;
        error_details?: string[] | undefined;
        processed_at?: Date | undefined;
    } | undefined;
    error?: {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    } | undefined;
    timestamp?: Date | undefined;
}>;
export declare const UpdateColumnMappingApiRequestSchema: z.ZodObject<{
    upload_id: z.ZodString;
    mapping: z.ZodRecord<z.ZodString, z.ZodEnum<["Time", "Open", "High", "Low", "Close", "Volume", "Bid", "Ask", "Ignore"]>>;
    symbol: z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>;
    timeframe: z.ZodString;
}, "strip", z.ZodTypeAny, {
    symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
    timeframe: string;
    mapping: Record<string, "Time" | "Open" | "High" | "Low" | "Close" | "Volume" | "Bid" | "Ask" | "Ignore">;
    upload_id: string;
}, {
    symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
    timeframe: string;
    mapping: Record<string, "Time" | "Open" | "High" | "Low" | "Close" | "Volume" | "Bid" | "Ask" | "Ignore">;
    upload_id: string;
}>;
export declare const UpdateColumnMappingApiResponseSchema: z.ZodObject<{
    success: z.ZodBoolean;
    data: z.ZodOptional<z.ZodObject<{
        id: z.ZodString;
        user_id: z.ZodString;
        filename: z.ZodString;
        original_filename: z.ZodString;
        file_size: z.ZodNumber;
        mime_type: z.ZodString;
        upload_path: z.ZodString;
        symbol: z.ZodOptional<z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>>;
        timeframe: z.ZodOptional<z.ZodString>;
        status: z.ZodEnum<["pending", "uploading", "mapping", "parsing", "validating", "ready", "error"]>;
        progress: z.ZodDefault<z.ZodNumber>;
        column_mapping: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodEnum<["Time", "Open", "High", "Low", "Close", "Volume", "Bid", "Ask", "Ignore"]>>>;
        validation_errors: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        data_quality: z.ZodOptional<z.ZodObject<{
            total_rows: z.ZodNumber;
            valid_rows: z.ZodNumber;
            invalid_rows: z.ZodNumber;
            date_range: z.ZodOptional<z.ZodObject<{
                start: z.ZodDate;
                end: z.ZodDate;
            }, "strip", z.ZodTypeAny, {
                end: Date;
                start: Date;
            }, {
                end: Date;
                start: Date;
            }>>;
            completeness: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            total_rows: number;
            valid_rows: number;
            invalid_rows: number;
            completeness: number;
            date_range?: {
                end: Date;
                start: Date;
            } | undefined;
        }, {
            total_rows: number;
            valid_rows: number;
            invalid_rows: number;
            completeness: number;
            date_range?: {
                end: Date;
                start: Date;
            } | undefined;
        }>>;
        error_message: z.ZodOptional<z.ZodString>;
        error_details: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        created_at: z.ZodDate;
        updated_at: z.ZodDate;
        processed_at: z.ZodOptional<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        status: "error" | "pending" | "uploading" | "mapping" | "parsing" | "validating" | "ready";
        id: string;
        filename: string;
        user_id: string;
        created_at: Date;
        progress: number;
        updated_at: Date;
        original_filename: string;
        file_size: number;
        mime_type: string;
        upload_path: string;
        symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        timeframe?: string | undefined;
        error_message?: string | undefined;
        column_mapping?: Record<string, "Time" | "Open" | "High" | "Low" | "Close" | "Volume" | "Bid" | "Ask" | "Ignore"> | undefined;
        validation_errors?: string[] | undefined;
        data_quality?: {
            total_rows: number;
            valid_rows: number;
            invalid_rows: number;
            completeness: number;
            date_range?: {
                end: Date;
                start: Date;
            } | undefined;
        } | undefined;
        error_details?: string[] | undefined;
        processed_at?: Date | undefined;
    }, {
        status: "error" | "pending" | "uploading" | "mapping" | "parsing" | "validating" | "ready";
        id: string;
        filename: string;
        user_id: string;
        created_at: Date;
        updated_at: Date;
        original_filename: string;
        file_size: number;
        mime_type: string;
        upload_path: string;
        symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        timeframe?: string | undefined;
        progress?: number | undefined;
        error_message?: string | undefined;
        column_mapping?: Record<string, "Time" | "Open" | "High" | "Low" | "Close" | "Volume" | "Bid" | "Ask" | "Ignore"> | undefined;
        validation_errors?: string[] | undefined;
        data_quality?: {
            total_rows: number;
            valid_rows: number;
            invalid_rows: number;
            completeness: number;
            date_range?: {
                end: Date;
                start: Date;
            } | undefined;
        } | undefined;
        error_details?: string[] | undefined;
        processed_at?: Date | undefined;
    }>>;
    error: z.ZodOptional<z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    }, {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    }>>;
    timestamp: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    timestamp: Date;
    data?: {
        status: "error" | "pending" | "uploading" | "mapping" | "parsing" | "validating" | "ready";
        id: string;
        filename: string;
        user_id: string;
        created_at: Date;
        progress: number;
        updated_at: Date;
        original_filename: string;
        file_size: number;
        mime_type: string;
        upload_path: string;
        symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        timeframe?: string | undefined;
        error_message?: string | undefined;
        column_mapping?: Record<string, "Time" | "Open" | "High" | "Low" | "Close" | "Volume" | "Bid" | "Ask" | "Ignore"> | undefined;
        validation_errors?: string[] | undefined;
        data_quality?: {
            total_rows: number;
            valid_rows: number;
            invalid_rows: number;
            completeness: number;
            date_range?: {
                end: Date;
                start: Date;
            } | undefined;
        } | undefined;
        error_details?: string[] | undefined;
        processed_at?: Date | undefined;
    } | undefined;
    error?: {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    } | undefined;
}, {
    success: boolean;
    data?: {
        status: "error" | "pending" | "uploading" | "mapping" | "parsing" | "validating" | "ready";
        id: string;
        filename: string;
        user_id: string;
        created_at: Date;
        updated_at: Date;
        original_filename: string;
        file_size: number;
        mime_type: string;
        upload_path: string;
        symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        timeframe?: string | undefined;
        progress?: number | undefined;
        error_message?: string | undefined;
        column_mapping?: Record<string, "Time" | "Open" | "High" | "Low" | "Close" | "Volume" | "Bid" | "Ask" | "Ignore"> | undefined;
        validation_errors?: string[] | undefined;
        data_quality?: {
            total_rows: number;
            valid_rows: number;
            invalid_rows: number;
            completeness: number;
            date_range?: {
                end: Date;
                start: Date;
            } | undefined;
        } | undefined;
        error_details?: string[] | undefined;
        processed_at?: Date | undefined;
    } | undefined;
    error?: {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    } | undefined;
    timestamp?: Date | undefined;
}>;
export declare const GetFilePreviewApiResponseSchema: z.ZodObject<{
    success: z.ZodBoolean;
    data: z.ZodOptional<z.ZodObject<{
        headers: z.ZodArray<z.ZodString, "many">;
        sample_rows: z.ZodArray<z.ZodArray<z.ZodString, "many">, "many">;
        detected_delimiter: z.ZodOptional<z.ZodString>;
        detected_encoding: z.ZodOptional<z.ZodString>;
        estimated_rows: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        headers: string[];
        sample_rows: string[][];
        estimated_rows: number;
        detected_delimiter?: string | undefined;
        detected_encoding?: string | undefined;
    }, {
        headers: string[];
        sample_rows: string[][];
        estimated_rows: number;
        detected_delimiter?: string | undefined;
        detected_encoding?: string | undefined;
    }>>;
    error: z.ZodOptional<z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    }, {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    }>>;
    timestamp: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    timestamp: Date;
    data?: {
        headers: string[];
        sample_rows: string[][];
        estimated_rows: number;
        detected_delimiter?: string | undefined;
        detected_encoding?: string | undefined;
    } | undefined;
    error?: {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    } | undefined;
}, {
    success: boolean;
    data?: {
        headers: string[];
        sample_rows: string[][];
        estimated_rows: number;
        detected_delimiter?: string | undefined;
        detected_encoding?: string | undefined;
    } | undefined;
    error?: {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    } | undefined;
    timestamp?: Date | undefined;
}>;
export declare const DeleteUploadApiResponseSchema: z.ZodObject<{
    success: z.ZodBoolean;
    data: z.ZodOptional<z.ZodObject<{
        deleted: z.ZodBoolean;
    }, "strip", z.ZodTypeAny, {
        deleted: boolean;
    }, {
        deleted: boolean;
    }>>;
    error: z.ZodOptional<z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    }, {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    }>>;
    timestamp: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    timestamp: Date;
    data?: {
        deleted: boolean;
    } | undefined;
    error?: {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    } | undefined;
}, {
    success: boolean;
    data?: {
        deleted: boolean;
    } | undefined;
    error?: {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    } | undefined;
    timestamp?: Date | undefined;
}>;
export declare const GetMLPredictionApiRequestSchema: z.ZodObject<{
    symbol: z.ZodString;
    prediction_type: z.ZodEnum<["price", "direction", "volatility"]>;
    timeframe: z.ZodString;
}, "strip", z.ZodTypeAny, {
    symbol: string;
    timeframe: string;
    prediction_type: "price" | "direction" | "volatility";
}, {
    symbol: string;
    timeframe: string;
    prediction_type: "price" | "direction" | "volatility";
}>;
export declare const GetMLPredictionApiResponseSchema: z.ZodObject<{
    success: z.ZodBoolean;
    data: z.ZodOptional<z.ZodObject<{
        symbol: z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>;
        prediction_type: z.ZodEnum<["price", "direction", "volatility"]>;
        value: z.ZodNumber;
        confidence: z.ZodNumber;
        timestamp: z.ZodDate;
        model_version: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
        value: number;
        confidence: number;
        timestamp: Date;
        prediction_type: "price" | "direction" | "volatility";
        model_version: string;
    }, {
        symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
        value: number;
        confidence: number;
        timestamp: Date;
        prediction_type: "price" | "direction" | "volatility";
        model_version: string;
    }>>;
    error: z.ZodOptional<z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    }, {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    }>>;
    timestamp: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    timestamp: Date;
    data?: {
        symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
        value: number;
        confidence: number;
        timestamp: Date;
        prediction_type: "price" | "direction" | "volatility";
        model_version: string;
    } | undefined;
    error?: {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    } | undefined;
}, {
    success: boolean;
    data?: {
        symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
        value: number;
        confidence: number;
        timestamp: Date;
        prediction_type: "price" | "direction" | "volatility";
        model_version: string;
    } | undefined;
    error?: {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    } | undefined;
    timestamp?: Date | undefined;
}>;
export declare const GetMLPredictionsApiRequestSchema: z.ZodObject<{
    page: z.ZodDefault<z.ZodNumber>;
    limit: z.ZodDefault<z.ZodNumber>;
    sortBy: z.ZodOptional<z.ZodString>;
    sortOrder: z.ZodDefault<z.ZodEnum<["asc", "desc"]>>;
} & {
    symbol: z.ZodOptional<z.ZodString>;
    prediction_type: z.ZodOptional<z.ZodEnum<["price", "direction", "volatility"]>>;
    min_confidence: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    limit: number;
    page: number;
    sortOrder: "asc" | "desc";
    symbol?: string | undefined;
    sortBy?: string | undefined;
    prediction_type?: "price" | "direction" | "volatility" | undefined;
    min_confidence?: number | undefined;
}, {
    symbol?: string | undefined;
    limit?: number | undefined;
    page?: number | undefined;
    sortBy?: string | undefined;
    sortOrder?: "asc" | "desc" | undefined;
    prediction_type?: "price" | "direction" | "volatility" | undefined;
    min_confidence?: number | undefined;
}>;
export declare const GetMLPredictionsApiResponseSchema: z.ZodObject<{
    success: z.ZodBoolean;
    data: z.ZodOptional<z.ZodObject<{
        predictions: z.ZodArray<z.ZodObject<{
            symbol: z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>;
            prediction_type: z.ZodEnum<["price", "direction", "volatility"]>;
            value: z.ZodNumber;
            confidence: z.ZodNumber;
            timestamp: z.ZodDate;
            model_version: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            value: number;
            confidence: number;
            timestamp: Date;
            prediction_type: "price" | "direction" | "volatility";
            model_version: string;
        }, {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            value: number;
            confidence: number;
            timestamp: Date;
            prediction_type: "price" | "direction" | "volatility";
            model_version: string;
        }>, "many">;
        pagination: z.ZodObject<{
            page: z.ZodNumber;
            limit: z.ZodNumber;
            total: z.ZodNumber;
            totalPages: z.ZodNumber;
            hasNext: z.ZodBoolean;
            hasPrev: z.ZodBoolean;
        }, "strip", z.ZodTypeAny, {
            limit: number;
            page: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        }, {
            limit: number;
            page: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        }>;
    }, "strip", z.ZodTypeAny, {
        pagination: {
            limit: number;
            page: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
        predictions: {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            value: number;
            confidence: number;
            timestamp: Date;
            prediction_type: "price" | "direction" | "volatility";
            model_version: string;
        }[];
    }, {
        pagination: {
            limit: number;
            page: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
        predictions: {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            value: number;
            confidence: number;
            timestamp: Date;
            prediction_type: "price" | "direction" | "volatility";
            model_version: string;
        }[];
    }>>;
    error: z.ZodOptional<z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    }, {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    }>>;
    timestamp: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    timestamp: Date;
    data?: {
        pagination: {
            limit: number;
            page: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
        predictions: {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            value: number;
            confidence: number;
            timestamp: Date;
            prediction_type: "price" | "direction" | "volatility";
            model_version: string;
        }[];
    } | undefined;
    error?: {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    } | undefined;
}, {
    success: boolean;
    data?: {
        pagination: {
            limit: number;
            page: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
        predictions: {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            value: number;
            confidence: number;
            timestamp: Date;
            prediction_type: "price" | "direction" | "volatility";
            model_version: string;
        }[];
    } | undefined;
    error?: {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    } | undefined;
    timestamp?: Date | undefined;
}>;
export declare const GetDashboardDataApiResponseSchema: z.ZodObject<{
    success: z.ZodBoolean;
    data: z.ZodOptional<z.ZodObject<{
        account_summary: z.ZodObject<{
            balance: z.ZodNumber;
            equity: z.ZodNumber;
            margin_used: z.ZodNumber;
            free_margin: z.ZodNumber;
            profit_loss: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            balance: number;
            equity: number;
            margin_used: number;
            free_margin: number;
            profit_loss: number;
        }, {
            balance: number;
            equity: number;
            margin_used: number;
            free_margin: number;
            profit_loss: number;
        }>;
        active_positions: z.ZodArray<z.ZodObject<{
            symbol: z.ZodString;
            volume: z.ZodNumber;
            pnl: z.ZodNumber;
            open_price: z.ZodNumber;
            current_price: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            symbol: string;
            volume: number;
            open_price: number;
            current_price: number;
            pnl: number;
        }, {
            symbol: string;
            volume: number;
            open_price: number;
            current_price: number;
            pnl: number;
        }>, "many">;
        recent_trades: z.ZodArray<z.ZodObject<{
            symbol: z.ZodString;
            order_type: z.ZodEnum<["buy", "sell"]>;
            volume: z.ZodNumber;
            pnl: z.ZodNumber;
            close_time: z.ZodDate;
        }, "strip", z.ZodTypeAny, {
            symbol: string;
            volume: number;
            order_type: "buy" | "sell";
            pnl: number;
            close_time: Date;
        }, {
            symbol: string;
            volume: number;
            order_type: "buy" | "sell";
            pnl: number;
            close_time: Date;
        }>, "many">;
        performance_metrics: z.ZodObject<{
            today_pnl: z.ZodNumber;
            week_pnl: z.ZodNumber;
            month_pnl: z.ZodNumber;
            win_rate: z.ZodNumber;
            total_trades: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            win_rate: number;
            total_trades: number;
            today_pnl: number;
            week_pnl: number;
            month_pnl: number;
        }, {
            win_rate: number;
            total_trades: number;
            today_pnl: number;
            week_pnl: number;
            month_pnl: number;
        }>;
        market_overview: z.ZodArray<z.ZodObject<{
            symbol: z.ZodString;
            current_price: z.ZodNumber;
            change: z.ZodNumber;
            change_percent: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            symbol: string;
            current_price: number;
            change: number;
            change_percent: number;
        }, {
            symbol: string;
            current_price: number;
            change: number;
            change_percent: number;
        }>, "many">;
    }, "strip", z.ZodTypeAny, {
        account_summary: {
            balance: number;
            equity: number;
            margin_used: number;
            free_margin: number;
            profit_loss: number;
        };
        active_positions: {
            symbol: string;
            volume: number;
            open_price: number;
            current_price: number;
            pnl: number;
        }[];
        recent_trades: {
            symbol: string;
            volume: number;
            order_type: "buy" | "sell";
            pnl: number;
            close_time: Date;
        }[];
        performance_metrics: {
            win_rate: number;
            total_trades: number;
            today_pnl: number;
            week_pnl: number;
            month_pnl: number;
        };
        market_overview: {
            symbol: string;
            current_price: number;
            change: number;
            change_percent: number;
        }[];
    }, {
        account_summary: {
            balance: number;
            equity: number;
            margin_used: number;
            free_margin: number;
            profit_loss: number;
        };
        active_positions: {
            symbol: string;
            volume: number;
            open_price: number;
            current_price: number;
            pnl: number;
        }[];
        recent_trades: {
            symbol: string;
            volume: number;
            order_type: "buy" | "sell";
            pnl: number;
            close_time: Date;
        }[];
        performance_metrics: {
            win_rate: number;
            total_trades: number;
            today_pnl: number;
            week_pnl: number;
            month_pnl: number;
        };
        market_overview: {
            symbol: string;
            current_price: number;
            change: number;
            change_percent: number;
        }[];
    }>>;
    error: z.ZodOptional<z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    }, {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    }>>;
    timestamp: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    timestamp: Date;
    data?: {
        account_summary: {
            balance: number;
            equity: number;
            margin_used: number;
            free_margin: number;
            profit_loss: number;
        };
        active_positions: {
            symbol: string;
            volume: number;
            open_price: number;
            current_price: number;
            pnl: number;
        }[];
        recent_trades: {
            symbol: string;
            volume: number;
            order_type: "buy" | "sell";
            pnl: number;
            close_time: Date;
        }[];
        performance_metrics: {
            win_rate: number;
            total_trades: number;
            today_pnl: number;
            week_pnl: number;
            month_pnl: number;
        };
        market_overview: {
            symbol: string;
            current_price: number;
            change: number;
            change_percent: number;
        }[];
    } | undefined;
    error?: {
        code: string;
        message: string;
        timestamp: Date;
        details?: string | undefined;
    } | undefined;
}, {
    success: boolean;
    data?: {
        account_summary: {
            balance: number;
            equity: number;
            margin_used: number;
            free_margin: number;
            profit_loss: number;
        };
        active_positions: {
            symbol: string;
            volume: number;
            open_price: number;
            current_price: number;
            pnl: number;
        }[];
        recent_trades: {
            symbol: string;
            volume: number;
            order_type: "buy" | "sell";
            pnl: number;
            close_time: Date;
        }[];
        performance_metrics: {
            win_rate: number;
            total_trades: number;
            today_pnl: number;
            week_pnl: number;
            month_pnl: number;
        };
        market_overview: {
            symbol: string;
            current_price: number;
            change: number;
            change_percent: number;
        }[];
    } | undefined;
    error?: {
        code: string;
        message: string;
        details?: string | undefined;
        timestamp?: Date | undefined;
    } | undefined;
    timestamp?: Date | undefined;
}>;
export declare const WebSocketMessageSchema: z.ZodObject<{
    type: z.ZodEnum<["price_update", "position_update", "order_update", "backtest_progress", "chat_response", "error"]>;
    payload: z.ZodAny;
    timestamp: z.ZodDate;
    id: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    type: "error" | "price_update" | "position_update" | "order_update" | "backtest_progress" | "chat_response";
    timestamp: Date;
    id?: string | undefined;
    payload?: any;
}, {
    type: "error" | "price_update" | "position_update" | "order_update" | "backtest_progress" | "chat_response";
    timestamp: Date;
    id?: string | undefined;
    payload?: any;
}>;
export declare const WebSocketSubscriptionSchema: z.ZodObject<{
    type: z.ZodEnum<["subscribe", "unsubscribe"]>;
    channel: z.ZodEnum<["prices", "positions", "orders", "backtest_progress", "chat_updates"]>;
    symbol: z.ZodOptional<z.ZodString>;
    session_id: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    type: "subscribe" | "unsubscribe";
    channel: "backtest_progress" | "prices" | "positions" | "orders" | "chat_updates";
    symbol?: string | undefined;
    session_id?: string | undefined;
}, {
    type: "subscribe" | "unsubscribe";
    channel: "backtest_progress" | "prices" | "positions" | "orders" | "chat_updates";
    symbol?: string | undefined;
    session_id?: string | undefined;
}>;
export declare const ValidationErrorResponseSchema: z.ZodObject<{
    success: z.ZodBoolean;
    data: z.ZodOptional<z.ZodNever>;
    timestamp: z.ZodDefault<z.ZodDate>;
} & {
    error: z.ZodObject<{
        code: z.ZodLiteral<"VALIDATION_ERROR">;
        message: z.ZodString;
        details: z.ZodArray<z.ZodObject<{
            field: z.ZodString;
            message: z.ZodString;
            value: z.ZodOptional<z.ZodAny>;
        }, "strip", z.ZodTypeAny, {
            message: string;
            field: string;
            value?: any;
        }, {
            message: string;
            field: string;
            value?: any;
        }>, "many">;
    }, "strip", z.ZodTypeAny, {
        code: "VALIDATION_ERROR";
        message: string;
        details: {
            message: string;
            field: string;
            value?: any;
        }[];
    }, {
        code: "VALIDATION_ERROR";
        message: string;
        details: {
            message: string;
            field: string;
            value?: any;
        }[];
    }>;
}, "strip", z.ZodTypeAny, {
    error: {
        code: "VALIDATION_ERROR";
        message: string;
        details: {
            message: string;
            field: string;
            value?: any;
        }[];
    };
    success: boolean;
    timestamp: Date;
    data?: undefined;
}, {
    error: {
        code: "VALIDATION_ERROR";
        message: string;
        details: {
            message: string;
            field: string;
            value?: any;
        }[];
    };
    success: boolean;
    data?: undefined;
    timestamp?: Date | undefined;
}>;
export declare const AuthErrorResponseSchema: z.ZodObject<{
    success: z.ZodBoolean;
    data: z.ZodOptional<z.ZodNever>;
    timestamp: z.ZodDefault<z.ZodDate>;
} & {
    error: z.ZodObject<{
        code: z.ZodEnum<["UNAUTHORIZED", "FORBIDDEN", "TOKEN_EXPIRED"]>;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        code: "UNAUTHORIZED" | "FORBIDDEN" | "TOKEN_EXPIRED";
        message: string;
        details?: string | undefined;
    }, {
        code: "UNAUTHORIZED" | "FORBIDDEN" | "TOKEN_EXPIRED";
        message: string;
        details?: string | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    error: {
        code: "UNAUTHORIZED" | "FORBIDDEN" | "TOKEN_EXPIRED";
        message: string;
        details?: string | undefined;
    };
    success: boolean;
    timestamp: Date;
    data?: undefined;
}, {
    error: {
        code: "UNAUTHORIZED" | "FORBIDDEN" | "TOKEN_EXPIRED";
        message: string;
        details?: string | undefined;
    };
    success: boolean;
    data?: undefined;
    timestamp?: Date | undefined;
}>;
export declare const RateLimitErrorResponseSchema: z.ZodObject<{
    success: z.ZodBoolean;
    data: z.ZodOptional<z.ZodNever>;
    timestamp: z.ZodDefault<z.ZodDate>;
} & {
    error: z.ZodObject<{
        code: z.ZodLiteral<"RATE_LIMIT_EXCEEDED">;
        message: z.ZodString;
        retry_after: z.ZodOptional<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        code: "RATE_LIMIT_EXCEEDED";
        message: string;
        retry_after?: number | undefined;
    }, {
        code: "RATE_LIMIT_EXCEEDED";
        message: string;
        retry_after?: number | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    error: {
        code: "RATE_LIMIT_EXCEEDED";
        message: string;
        retry_after?: number | undefined;
    };
    success: boolean;
    timestamp: Date;
    data?: undefined;
}, {
    error: {
        code: "RATE_LIMIT_EXCEEDED";
        message: string;
        retry_after?: number | undefined;
    };
    success: boolean;
    data?: undefined;
    timestamp?: Date | undefined;
}>;
export declare const HealthCheckResponseSchema: z.ZodObject<{
    status: z.ZodEnum<["healthy", "degraded", "unhealthy"]>;
    timestamp: z.ZodDate;
    services: z.ZodObject<{
        database: z.ZodEnum<["healthy", "unhealthy"]>;
        python_engine: z.ZodEnum<["healthy", "unhealthy"]>;
        redis: z.ZodOptional<z.ZodEnum<["healthy", "unhealthy"]>>;
    }, "strip", z.ZodTypeAny, {
        database: "healthy" | "unhealthy";
        python_engine: "healthy" | "unhealthy";
        redis?: "healthy" | "unhealthy" | undefined;
    }, {
        database: "healthy" | "unhealthy";
        python_engine: "healthy" | "unhealthy";
        redis?: "healthy" | "unhealthy" | undefined;
    }>;
    version: z.ZodString;
    uptime: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    status: "healthy" | "degraded" | "unhealthy";
    timestamp: Date;
    version: string;
    services: {
        database: "healthy" | "unhealthy";
        python_engine: "healthy" | "unhealthy";
        redis?: "healthy" | "unhealthy" | undefined;
    };
    uptime: number;
}, {
    status: "healthy" | "degraded" | "unhealthy";
    timestamp: Date;
    version: string;
    services: {
        database: "healthy" | "unhealthy";
        python_engine: "healthy" | "unhealthy";
        redis?: "healthy" | "unhealthy" | undefined;
    };
    uptime: number;
}>;
export type LoginApiRequest = z.infer<typeof LoginApiRequestSchema>;
export type LoginApiResponse = z.infer<typeof LoginApiResponseSchema>;
export type RegisterApiRequest = z.infer<typeof RegisterApiRequestSchema>;
export type RegisterApiResponse = z.infer<typeof RegisterApiResponseSchema>;
export type CreateBacktestApiRequest = z.infer<typeof CreateBacktestApiRequestSchema>;
export type CreateBacktestApiResponse = z.infer<typeof CreateBacktestApiResponseSchema>;
export type GetBacktestsApiRequest = z.infer<typeof GetBacktestsApiRequestSchema>;
export type GetBacktestsApiResponse = z.infer<typeof GetBacktestsApiResponseSchema>;
export type SendChatMessageApiRequest = z.infer<typeof SendChatMessageApiRequestSchema>;
export type SendChatMessageApiResponse = z.infer<typeof SendChatMessageApiResponseSchema>;
export type WebSocketMessage = z.infer<typeof WebSocketMessageSchema>;
export type WebSocketSubscription = z.infer<typeof WebSocketSubscriptionSchema>;
export type HealthCheckResponse = z.infer<typeof HealthCheckResponseSchema>;
//# sourceMappingURL=api.schemas.d.ts.map