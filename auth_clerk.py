from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, Request, Depends
import os
from typing import Dict, Any

try:
    from clerk import Clerk
except ImportError:
    class Clerk:
        def __init__(self):
            pass
        async def get_user(self, token):
            # Mock for testing
            if token == "valid_token":
                return {"id": "user_123", "email": "<EMAIL>"}
            raise Exception("Invalid token")

class ClerkAuth:
    def __init__(self):
        # Only pass api_key if Clerk supports it
        try:
            self.clerk = Clerk(api_key=os.getenv("CLERK_SECRET_KEY"))
        except TypeError:
            self.clerk = Clerk()
    async def get_user_from_token(self, token: str) -> Dict[str, Any]:
        try:
            user = await self.clerk.get_user(token)
            return user
        except Exception as e:
            raise HTTPException(
                status_code=401,
                detail=f"Authentication failed: {str(e)}"
            )

clerk_auth = ClerkAuth()

async def clerk_auth_middleware(request: Request) -> Dict[str, Any]:
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        raise HTTPException(
            status_code=401,
            detail="Unauthorized - No authentication token provided"
        )
    token = auth_header[7:]  # Remove "Bearer " prefix
    try:
        user = await clerk_auth.get_user_from_token(token)
        return user
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=401,
            detail="Authentication service unavailable"
        )

async def get_current_user(request: Request = Depends(clerk_auth_middleware)) -> Dict[str, Any]:
    # request here is actually the user dict returned by clerk_auth_middleware
    return request
