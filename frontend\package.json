{"name": "ai-trading-platform-frontend", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "dev:mvp": "vite --config vite.config.mvp.ts", "build": "tsc && vite build", "build:mvp": "tsc && vite build --config vite.config.mvp.ts", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:coverage": "vitest --coverage"}, "dependencies": {"@radix-ui/react-select": "^2.2.5", "@radix-ui/react-tabs": "^1.1.12", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^4.36.1", "axios": "^1.6.0", "class-variance-authority": "^0.7.1", "classnames": "^2.3.2", "clsx": "^2.1.1", "date-fns": "^2.30.0", "framer-motion": "^10.16.5", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-datepicker": "^4.23.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.20.1", "react-select": "^5.8.0", "react-table": "^7.8.0", "react-virtualized": "^9.22.5", "react-window": "^1.8.8", "recharts": "^2.8.0", "socket.io-client": "^4.7.4", "tailwind-merge": "^3.3.1", "zustand": "^4.4.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/react-table": "^7.7.14", "@types/react-virtualized": "^9.21.21", "@types/react-window": "^1.8.8", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "jsdom": "^23.2.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.14", "vitest": "^0.34.6"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}