"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BacktestProgressSchema = exports.PythonBacktestResponseSchema = exports.PythonBacktestRequestSchema = exports.BacktestResultsSchema = exports.BacktestMetricsSchema = exports.BacktestTradeSchema = exports.BacktestSchema = exports.CreateBacktestRequestSchema = exports.BacktestConfigSchema = exports.BacktestStatusSchema = void 0;
const zod_1 = require("zod");
const trading_schemas_1 = require("./trading.schemas");
const common_schemas_1 = require("./common.schemas");
// Backtest Status
exports.BacktestStatusSchema = zod_1.z.enum(['pending', 'running', 'completed', 'error']);
// Backtest Configuration
exports.BacktestConfigSchema = zod_1.z.object({
    name: zod_1.z.string().min(1).max(255),
    description: zod_1.z.string().optional(),
    symbols: zod_1.z.array(trading_schemas_1.TradingSymbolSchema).min(1),
    start_date: zod_1.z.date(),
    end_date: zod_1.z.date(),
    initial_balance: zod_1.z.number().positive().default(10000),
    strategy: zod_1.z.object({
        name: zod_1.z.string(),
        parameters: zod_1.z.record(zod_1.z.any()),
    }),
    risk_management: zod_1.z.object({
        max_risk_per_trade: zod_1.z.number().min(0).max(1).default(0.02), // 2%
        max_concurrent_trades: zod_1.z.number().int().positive().default(5),
        stop_loss_pips: zod_1.z.number().positive().optional(),
        take_profit_pips: zod_1.z.number().positive().optional(),
    }),
}).refine((data) => data.start_date < data.end_date, {
    message: "Start date must be before end date",
    path: ["end_date"],
});
// Backtest Request (from frontend to backend)
exports.CreateBacktestRequestSchema = zod_1.z.object({
    config: exports.BacktestConfigSchema,
    data_source: zod_1.z.enum(['historical', 'uploaded']),
    data_file_id: zod_1.z.string().optional(), // If using uploaded data
});
// Backtest Entity
exports.BacktestSchema = zod_1.z.object({
    id: common_schemas_1.IdSchema,
    user_id: common_schemas_1.IdSchema,
    config: exports.BacktestConfigSchema,
    status: exports.BacktestStatusSchema,
    progress: zod_1.z.number().min(0).max(100).default(0),
    started_at: zod_1.z.date().optional(),
    completed_at: zod_1.z.date().optional(),
    error_message: zod_1.z.string().optional(),
    created_at: zod_1.z.date(),
    updated_at: zod_1.z.date(),
});
// Backtest Results (from Python engine)
exports.BacktestTradeSchema = zod_1.z.object({
    entry_time: zod_1.z.date(),
    exit_time: zod_1.z.date().optional(),
    symbol: trading_schemas_1.TradingSymbolSchema,
    order_type: trading_schemas_1.OrderTypeSchema,
    entry_price: zod_1.z.number().positive(),
    exit_price: zod_1.z.number().positive().optional(),
    volume: zod_1.z.number().positive(),
    pnl: zod_1.z.number(),
    pnl_pips: zod_1.z.number(),
    duration_minutes: zod_1.z.number().int().nonnegative().optional(),
    reason: zod_1.z.enum(['stop_loss', 'take_profit', 'strategy_exit', 'timeout']).optional(),
});
exports.BacktestMetricsSchema = zod_1.z.object({
    // Performance Metrics
    total_trades: zod_1.z.number().int().nonnegative(),
    winning_trades: zod_1.z.number().int().nonnegative(),
    losing_trades: zod_1.z.number().int().nonnegative(),
    win_rate: zod_1.z.number().min(0).max(1),
    // PnL Metrics
    total_pnl: zod_1.z.number(),
    gross_profit: zod_1.z.number().nonnegative(),
    gross_loss: zod_1.z.number().nonpositive(),
    profit_factor: zod_1.z.number().nonnegative(),
    // Risk Metrics
    max_drawdown: zod_1.z.number().nonpositive(),
    max_drawdown_percent: zod_1.z.number().min(-1).max(0),
    sharpe_ratio: zod_1.z.number().optional(),
    sortino_ratio: zod_1.z.number().optional(),
    // Trade Metrics
    average_win: zod_1.z.number().nonnegative(),
    average_loss: zod_1.z.number().nonpositive(),
    largest_win: zod_1.z.number().nonnegative(),
    largest_loss: zod_1.z.number().nonpositive(),
    // Time Metrics
    average_trade_duration_minutes: zod_1.z.number().nonnegative().optional(),
    total_time_in_market_minutes: zod_1.z.number().nonnegative().optional(),
    // Additional Metrics
    expectancy: zod_1.z.number(),
    kelly_criterion: zod_1.z.number().optional(),
    calmar_ratio: zod_1.z.number().optional(),
});
exports.BacktestResultsSchema = zod_1.z.object({
    backtest_id: common_schemas_1.IdSchema,
    config: exports.BacktestConfigSchema,
    metrics: exports.BacktestMetricsSchema,
    trades: zod_1.z.array(exports.BacktestTradeSchema),
    balance_curve: zod_1.z.array(zod_1.z.object({
        timestamp: zod_1.z.date(),
        balance: zod_1.z.number(),
        equity: zod_1.z.number(),
        drawdown: zod_1.z.number(),
    })),
    monthly_returns: zod_1.z.array(zod_1.z.object({
        year: zod_1.z.number().int(),
        month: zod_1.z.number().int().min(1).max(12),
        return_percent: zod_1.z.number(),
    })),
    created_at: zod_1.z.date(),
});
// Python Engine Integration Schemas
exports.PythonBacktestRequestSchema = zod_1.z.object({
    request_id: zod_1.z.string().uuid(),
    config: exports.BacktestConfigSchema,
    data: zod_1.z.object({
        market_data: zod_1.z.array(zod_1.z.object({
            symbol: trading_schemas_1.TradingSymbolSchema,
            timestamp: zod_1.z.date(),
            open: zod_1.z.number(),
            high: zod_1.z.number(),
            low: zod_1.z.number(),
            close: zod_1.z.number(),
            volume: zod_1.z.number().optional(),
        })),
    }),
});
exports.PythonBacktestResponseSchema = zod_1.z.object({
    request_id: zod_1.z.string().uuid(),
    success: zod_1.z.boolean(),
    results: exports.BacktestResultsSchema.optional(),
    error: zod_1.z.string().optional(),
    execution_time_seconds: zod_1.z.number().nonnegative(),
});
// Progress Updates from Python Engine
exports.BacktestProgressSchema = zod_1.z.object({
    backtest_id: common_schemas_1.IdSchema,
    progress: zod_1.z.number().min(0).max(100),
    current_date: zod_1.z.date().optional(),
    trades_executed: zod_1.z.number().int().nonnegative(),
    current_balance: zod_1.z.number().optional(),
    status: exports.BacktestStatusSchema,
    message: zod_1.z.string().optional(),
});
//# sourceMappingURL=backtest.schemas.js.map