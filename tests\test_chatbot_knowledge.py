# tests/test_chatbot_knowledge.py
import pytest
import os
import tempfile
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from chatbot.knowledge_base import KnowledgeBase, TradingChatbot, KnowledgeEntry, ChatResponse, VerificationError

class TestChatbotKnowledge:
    def setup_method(self):
        """Setup test database"""
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.kb = KnowledgeBase(db_path=self.temp_db.name)
        self.chatbot = TradingChatbot(self.kb)
    
    def teardown_method(self):
        """Cleanup test database"""
        if os.path.exists(self.temp_db.name):
            os.unlink(self.temp_db.name)
    
    def test_verified_knowledge_retrieval(self):
        """Test that chatbot only uses verified knowledge"""
        # Add verified knowledge
        entry = KnowledgeEntry(
            content="RSI above 70 typically indicates overbought conditions",
            source="Technical Analysis by <PERSON>",
            verification_hash="",  # Will be generated
            confidence_score=0.95,
            last_verified=datetime.now(),
            tags=["rsi", "technical_analysis", "overbought"]
        )
        
        # Generate hash
        entry.verification_hash = self.kb._generate_content_hash(entry)
        self.kb.add_verified_entry(entry)
        
        response = self.chatbot.query("What does RSI above 70 mean?")
        
        assert "overbought conditions" in response.content
        assert response.sources is not None
        assert len(response.sources) > 0
        assert response.confidence_score >= 0.8
    
    def test_unknown_query_handling(self):
        """Test chatbot says 'I don't know' for unverified information"""
        response = self.chatbot.query("What will EUR/USD do tomorrow?")
        
        assert "don't have verified information" in response.content.lower() or "cannot provide information" in response.content.lower()
        assert response.confidence_score == 0.0
    
    def test_source_verification(self):
        """Test that all sources are verifiable"""
        entry = KnowledgeEntry(
            content="Test content",
            source="Non-existent source",
            verification_hash="invalid_hash",
            confidence_score=0.9,
            last_verified=datetime.now(),
            tags=["test"]
        )
        
        with pytest.raises(VerificationError):
            self.kb.add_verified_entry(entry)

    def test_content_hash_verification(self):
        """Test content hash verification"""
        entry = KnowledgeEntry(
            content="Test content for hashing",
            source="Technical Analysis by John Murphy",
            verification_hash="",
            confidence_score=0.9,
            last_verified=datetime.now(),
            tags=["test"]
        )
        
        # Generate correct hash
        correct_hash = self.kb._generate_content_hash(entry)
        entry.verification_hash = correct_hash
        
        # Should verify successfully
        assert self.kb.verify_entry(entry) == True
        
        # Tamper with hash
        entry.verification_hash = "tampered_hash"
        assert self.kb.verify_entry(entry) == False

    def test_confidence_score_validation(self):
        """Test confidence score validation"""
        # Invalid confidence score (> 1.0)
        entry = KnowledgeEntry(
            content="Test content",
            source="Technical Analysis by John Murphy",
            verification_hash="",
            confidence_score=1.5,  # Invalid
            last_verified=datetime.now(),
            tags=["test"]
        )
        entry.verification_hash = self.kb._generate_content_hash(entry)
        
        assert self.kb.verify_entry(entry) == False
        
        # Invalid confidence score (< 0.0)
        entry.confidence_score = -0.1  # Invalid
        entry.verification_hash = self.kb._generate_content_hash(entry)
        
        assert self.kb.verify_entry(entry) == False
        
        # Valid confidence score
        entry.confidence_score = 0.85  # Valid
        entry.verification_hash = self.kb._generate_content_hash(entry)
        
        assert self.kb.verify_entry(entry) == True

    def test_tag_extraction(self):
        """Test tag extraction from user queries"""
        # Test RSI tag extraction
        tags = self.chatbot._extract_tags("What is RSI indicator?")
        assert "rsi" in tags
        
        # Test moving average tag extraction
        tags = self.chatbot._extract_tags("How do moving averages work?")
        assert "moving_average" in tags
        
        # Test MACD tag extraction
        tags = self.chatbot._extract_tags("Explain MACD crossover")
        assert "macd" in tags
        
        # Test multiple tags
        tags = self.chatbot._extract_tags("RSI and MACD signals")
        assert "rsi" in tags
        assert "macd" in tags

    def test_knowledge_search_functionality(self):
        """Test knowledge base search functionality"""
        # Add test entries
        entries = [
            KnowledgeEntry(
                content="RSI is a momentum oscillator",
                source="Technical Analysis by John Murphy",
                verification_hash="",
                confidence_score=0.9,
                last_verified=datetime.now(),
                tags=["rsi", "momentum"]
            ),
            KnowledgeEntry(
                content="MACD is a trend-following indicator",
                source="Technical Analysis by John Murphy",
                verification_hash="",
                confidence_score=0.85,
                last_verified=datetime.now(),
                tags=["macd", "trend"]
            )
        ]
        
        for entry in entries:
            entry.verification_hash = self.kb._generate_content_hash(entry)
            self.kb.add_verified_entry(entry)
        
        # Search for RSI
        results = self.kb.search("RSI")
        assert len(results) == 1
        assert "momentum oscillator" in results[0].content
        
        # Search for MACD
        results = self.kb.search("MACD")
        assert len(results) == 1
        assert "trend-following" in results[0].content
        
        # Search with tags
        results = self.kb.search("oscillator", tags=["rsi"])
        assert len(results) == 1
        assert "RSI" in results[0].content

    def test_confidence_threshold_filtering(self):
        """Test that low confidence entries are filtered out"""
        # Add low confidence entry
        low_confidence_entry = KnowledgeEntry(
            content="Low confidence trading advice",
            source="Technical Analysis by John Murphy",
            verification_hash="",
            confidence_score=0.5,  # Below threshold
            last_verified=datetime.now(),
            tags=["advice"]
        )
        low_confidence_entry.verification_hash = self.kb._generate_content_hash(low_confidence_entry)
        self.kb.add_verified_entry(low_confidence_entry)
        
        # Query should return unknown response due to low confidence
        response = self.chatbot.query("trading advice")
        assert response.confidence_score == 0.0
        assert "don't have verified information" in response.content.lower()

    def test_multiple_source_response(self):
        """Test response generation from multiple sources"""
        # Add multiple entries on same topic
        entries = [
            KnowledgeEntry(
                content="RSI above 70 indicates overbought conditions",
                source="Technical Analysis by John Murphy",
                verification_hash="",
                confidence_score=0.95,
                last_verified=datetime.now(),
                tags=["rsi", "overbought"]
            ),
            KnowledgeEntry(
                content="Overbought RSI suggests potential reversal",
                source="Market Wizards by Jack Schwager",
                verification_hash="",
                confidence_score=0.9,
                last_verified=datetime.now(),
                tags=["rsi", "overbought", "reversal"]
            )
        ]
        
        for entry in entries:
            entry.verification_hash = self.kb._generate_content_hash(entry)
            self.kb.add_verified_entry(entry)
        
        response = self.chatbot.query("RSI overbought")
        
        # Should use highest confidence entry as primary
        assert "overbought conditions" in response.content
        assert len(response.sources) >= 1
        assert response.confidence_score > 0.8

    def test_add_trading_knowledge(self):
        """Test adding predefined trading knowledge"""
        initial_stats = self.chatbot.get_knowledge_stats()
        initial_count = initial_stats["total_entries"]
        
        # Add trading knowledge
        self.chatbot.add_trading_knowledge()
        
        # Check that knowledge was added
        final_stats = self.chatbot.get_knowledge_stats()
        assert final_stats["total_entries"] > initial_count
        assert final_stats["average_confidence"] > 0.8

    def test_knowledge_stats(self):
        """Test knowledge base statistics"""
        # Add some test knowledge
        self.chatbot.add_trading_knowledge()
        
        stats = self.chatbot.get_knowledge_stats()
        
        assert "total_entries" in stats
        assert "average_confidence" in stats
        assert "sources" in stats
        assert "verified_sources_count" in stats
        
        assert stats["total_entries"] > 0
        assert 0 <= stats["average_confidence"] <= 1
        assert stats["verified_sources_count"] > 0

    def test_database_persistence(self):
        """Test that knowledge persists in database"""
        # Add entry
        entry = KnowledgeEntry(
            content="Test persistence content",
            source="Technical Analysis by John Murphy",
            verification_hash="",
            confidence_score=0.9,
            last_verified=datetime.now(),
            tags=["test", "persistence"]
        )
        entry.verification_hash = self.kb._generate_content_hash(entry)
        self.kb.add_verified_entry(entry)
        
        # Create new knowledge base instance with same database
        new_kb = KnowledgeBase(db_path=self.temp_db.name)
        results = new_kb.search("persistence")
        
        assert len(results) == 1
        assert "Test persistence content" in results[0].content

    def test_verified_sources_configuration(self):
        """Test verified sources configuration"""
        # Check that verified sources are properly configured
        assert "Technical Analysis by John Murphy" in self.kb.verified_sources
        assert "Quantitative Trading by Ernest Chan" in self.kb.verified_sources
        
        # Check source properties
        murphy_source = self.kb.verified_sources["Technical Analysis by John Murphy"]
        assert murphy_source["verified"] == True
        assert murphy_source["authority_score"] > 0.8
        assert "isbn" in murphy_source

    def test_query_with_no_knowledge_base(self):
        """Test query behavior with empty knowledge base"""
        # Create temporary empty database
        temp_empty_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        temp_empty_db.close()
        
        try:
            empty_kb = KnowledgeBase(db_path=temp_empty_db.name)
            empty_chatbot = TradingChatbot(empty_kb)
            
            response = empty_chatbot.query("What is RSI?")
            
            assert response.confidence_score == 0.0
            assert len(response.sources) == 0
            assert "don't have verified information" in response.content.lower()
        finally:
            if os.path.exists(temp_empty_db.name):
                os.unlink(temp_empty_db.name)

class TestKnowledgeBase:
    def setup_method(self):
        """Setup test database"""
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.kb = KnowledgeBase(db_path=self.temp_db.name)
    
    def teardown_method(self):
        """Cleanup test database"""
        if os.path.exists(self.temp_db.name):
            os.unlink(self.temp_db.name)
    
    def test_database_initialization(self):
        """Test database initialization"""
        # Database should be created and tables should exist
        import sqlite3
        conn = sqlite3.connect(self.temp_db.name)
        cursor = conn.cursor()
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='knowledge_entries'")
        result = cursor.fetchone()
        assert result is not None
        
        conn.close()
    
    def test_hash_generation_consistency(self):
        """Test that hash generation is consistent"""
        entry = KnowledgeEntry(
            content="Test content",
            source="Test source",
            verification_hash="",
            confidence_score=0.9,
            last_verified=datetime.now(),
            tags=["test"]
        )
        
        hash1 = self.kb._generate_content_hash(entry)
        hash2 = self.kb._generate_content_hash(entry)
        
        assert hash1 == hash2
        assert hash1.startswith("sha256:")
    
    def test_hash_changes_with_content(self):
        """Test that hash changes when content changes"""
        entry1 = KnowledgeEntry(
            content="Test content 1",
            source="Test source",
            verification_hash="",
            confidence_score=0.9,
            last_verified=datetime.now(),
            tags=["test"]
        )
        
        entry2 = KnowledgeEntry(
            content="Test content 2",  # Different content
            source="Test source",
            verification_hash="",
            confidence_score=0.9,
            last_verified=datetime.now(),
            tags=["test"]
        )
        
        hash1 = self.kb._generate_content_hash(entry1)
        hash2 = self.kb._generate_content_hash(entry2)
        
        assert hash1 != hash2