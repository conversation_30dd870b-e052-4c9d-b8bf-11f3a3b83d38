# AI Enhanced Trading Platform - MVP

This document provides an overview of the Minimum Viable Product (MVP) implementation of the AI Enhanced Trading Platform, focusing on the core functionality needed to get started.

## Overview

The MVP implementation focuses on the essential components needed for basic trading functionality:

1. **MT5 Bridge**: A clean interface to interact with MetaTrader 5
2. **Core Trading Logic**: Basic order placement, portfolio management, and risk management
3. **Test Suite**: Comprehensive tests for the MVP components

## Getting Started

### Prerequisites

- Python 3.13.2 or later
- MetaTrader 5 (optional, the MVP can run in offline mode)

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/ai-enhanced-trading-platform.git
   cd ai-enhanced-trading-platform
   ```

2. Run the MVP setup script:
   ```bash
   python mvp_setup.py --all
   ```

   This will:
   - Install required dependencies
   - Set up the environment
   - Run the MVP tests

### Running the MVP Tests

To run the MVP tests:

```bash
python -m tests.mvp_test_suite --verbose
```

Options:
- `--verbose`: Enable verbose output
- `--offline`: Run tests in offline mode (default: True)
- `--all-tests`: Run all available tests, not just MVP tests

## MVP Components

### MT5 Bridge

The MT5 Bridge provides a clean interface to interact with MetaTrader 5, handling:

- Connection management
- Order placement and execution
- Position management
- Error handling

Key features:
- Offline mode for testing without MT5
- Auto-reconnection
- Comprehensive error handling

Example usage:

```python
from src.trading.mt5_bridge_tdd import MT5Bridge

# Create MT5 Bridge instance
bridge = MT5Bridge(offline_mode=True)  # Use offline_mode=False for live trading

# Place a market buy order
order_id = bridge.place_order(
    symbol="EURUSD",
    order_type="BUY",
    lot=0.1
)

# Get order status
status = bridge.get_order_status(order_id)
print(f"Order {order_id} status: {status}")

# Get all positions
positions = bridge.get_positions()
for pos in positions:
    print(f"Position: {pos['symbol']} {pos['type']} {pos['lot']}")

# Close the order
bridge.close_order(order_id)
```

### Core Trading Logic

The MVP includes basic trading functionality:

- Order placement and management
- Portfolio tracking
- Risk management

### Test Suite

The MVP includes a comprehensive test suite:

- Unit tests for core functionality
- Integration tests for MT5 Bridge
- Test fixtures and utilities

## Configuration

The MVP configuration is stored in `config/mvp_config.py` and includes:

- MT5 configuration
- Trading parameters
- Risk management settings
- Logging configuration

## Next Steps

After setting up the MVP, you can:

1. Implement your trading strategies
2. Connect to a live MT5 account
3. Add more advanced features like backtesting and optimization

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.