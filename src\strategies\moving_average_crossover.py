"""
Moving Average Crossover Strategy

This module implements a simple moving average crossover strategy.
"""

import logging
from typing import Dict, List, Any, Optional
import numpy as np
from datetime import datetime, timedelta

from src.strategies.strategy_base import StrategyBase
from src.trading.mt5_bridge_tdd import MT5Bridge

# Configure logging
logger = logging.getLogger(__name__)


class MovingAverageCrossover(StrategyBase):
    """
    Moving Average Crossover Strategy
    
    This strategy generates buy signals when the fast moving average crosses above
    the slow moving average, and sell signals when the fast moving average crosses
    below the slow moving average.
    """
    
    def __init__(self, 
                symbols: List[str], 
                fast_period: int = 10, 
                slow_period: int = 30,
                mt5_bridge: Optional[MT5Bridge] = None,
                risk_per_trade: float = 0.02,
                max_open_positions: int = 5,
                offline_mode: bool = False):
        """
        Initialize the Moving Average Crossover strategy
        
        Args:
            symbols: List of symbols to trade
            fast_period: Fast moving average period
            slow_period: Slow moving average period
            mt5_bridge: MT5 Bridge instance (if None, a new one will be created)
            risk_per_trade: Risk per trade as a fraction of account balance (0.02 = 2%)
            max_open_positions: Maximum number of open positions
            offline_mode: Whether to run in offline mode
        """
        super().__init__(
            name="MA Crossover",
            symbols=symbols,
            mt5_bridge=mt5_bridge,
            risk_per_trade=risk_per_trade,
            max_open_positions=max_open_positions,
            offline_mode=offline_mode
        )
        
        self.fast_period = fast_period
        self.slow_period = slow_period
        
        # Historical data for each symbol
        self.historical_data = {}
        
        # Last signal for each symbol
        self.last_signals = {}
        
        logger.info(f"MA Crossover strategy initialized with fast_period={fast_period}, slow_period={slow_period}")
    
    def generate_signals(self) -> List[Dict[str, Any]]:
        """
        Generate trading signals based on moving average crossovers
        
        Returns:
            List[Dict]: List of trading signals
        """
        signals = []
        
        # Process each symbol
        for symbol in self.symbols:
            # Get historical data (in a real implementation, this would come from MT5)
            prices = self._get_historical_prices(symbol)
            
            if len(prices) < self.slow_period:
                logger.warning(f"Not enough historical data for {symbol}")
                continue
            
            # Calculate moving averages
            fast_ma = self._calculate_ma(prices, self.fast_period)
            slow_ma = self._calculate_ma(prices, self.slow_period)
            
            # Check for crossover
            if len(fast_ma) < 2 or len(slow_ma) < 2:
                continue
            
            # Current and previous values
            current_fast = fast_ma[-1]
            previous_fast = fast_ma[-2]
            current_slow = slow_ma[-1]
            previous_slow = slow_ma[-2]
            
            # Check for crossover
            if previous_fast <= previous_slow and current_fast > current_slow:
                # Bullish crossover (fast crosses above slow)
                signal = {
                    "symbol": symbol,
                    "action": "BUY",
                    "lot": 0.1,  # This would be calculated based on risk in a real implementation
                    "price": None,  # Market order
                    "stop_loss": prices[-1] * 0.99,  # 1% below current price
                    "take_profit": prices[-1] * 1.02  # 2% above current price
                }
                signals.append(signal)
                self.last_signals[symbol] = "BUY"
                logger.info(f"Bullish crossover detected for {symbol}")
                
            elif previous_fast >= previous_slow and current_fast < current_slow:
                # Bearish crossover (fast crosses below slow)
                signal = {
                    "symbol": symbol,
                    "action": "SELL",
                    "lot": 0.1,  # This would be calculated based on risk in a real implementation
                    "price": None,  # Market order
                    "stop_loss": prices[-1] * 1.01,  # 1% above current price
                    "take_profit": prices[-1] * 0.98  # 2% below current price
                }
                signals.append(signal)
                self.last_signals[symbol] = "SELL"
                logger.info(f"Bearish crossover detected for {symbol}")
            
            # Check if we need to close any positions
            for position in self.positions:
                if position["symbol"] == symbol:
                    position_type = position["type"]
                    
                    # Close position if signal is opposite to position
                    if (position_type == "BUY" and self.last_signals.get(symbol) == "SELL") or \
                       (position_type == "SELL" and self.last_signals.get(symbol) == "BUY"):
                        signal = {
                            "symbol": symbol,
                            "action": "CLOSE"
                        }
                        signals.append(signal)
                        logger.info(f"Closing {position_type} position for {symbol}")
        
        return signals
    
    def _get_historical_prices(self, symbol: str) -> List[float]:
        """
        Get historical prices for a symbol
        
        In a real implementation, this would fetch data from MT5.
        For the MVP, we'll generate random price data.
        
        Args:
            symbol: Trading symbol
        
        Returns:
            List[float]: List of closing prices
        """
        # Check if we already have data for this symbol
        if symbol in self.historical_data:
            # Add a new random price
            last_price = self.historical_data[symbol][-1]
            # Generate a random price change between -0.5% and +0.5%
            change = last_price * (1 + (np.random.random() - 0.5) * 0.01)
            self.historical_data[symbol].append(change)
        else:
            # Generate initial random data
            base_price = 1.0 if symbol == "EURUSD" else 100.0
            prices = [base_price]
            
            # Generate 100 random prices
            for _ in range(100):
                last_price = prices[-1]
                # Generate a random price change between -0.5% and +0.5%
                change = last_price * (1 + (np.random.random() - 0.5) * 0.01)
                prices.append(change)
            
            self.historical_data[symbol] = prices
        
        return self.historical_data[symbol]
    
    def _calculate_ma(self, prices: List[float], period: int) -> List[float]:
        """
        Calculate moving average
        
        Args:
            prices: List of prices
            period: Moving average period
        
        Returns:
            List[float]: Moving average values
        """
        if len(prices) < period:
            return []
        
        ma = []
        for i in range(len(prices) - period + 1):
            ma.append(sum(prices[i:i+period]) / period)
        
        return ma