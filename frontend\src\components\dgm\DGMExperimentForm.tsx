/**
 * DGM Experiment Form Component
 * Form for creating new DGM experiments
 */

import React, { useState } from 'react';
import { <PERSON>, Beaker, Setting<PERSON>, Target, Zap, Info } from 'lucide-react';

interface DGMExperimentFormProps {
  onSubmit: (name: string, baseStrategy: Record<string, any>) => void;
  onCancel: () => void;
}

export function DGMExperimentForm({ onSubmit, onCancel }: DGMExperimentFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    baseStrategy: {
      type: 'moving_average_crossover',
      parameters: {
        fast_period: 10,
        slow_period: 30,
        signal_threshold: 0.02,
      },
    },
    populationSize: 50,
    generations: 20,
    mutationRate: 0.1,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData.name, formData.baseStrategy);
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleParameterChange = (param: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      baseStrategy: {
        ...prev.baseStrategy,
        parameters: {
          ...prev.baseStrategy.parameters,
          [param]: value,
        },
      },
    }));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <Beaker className="w-6 h-6 mr-3 text-purple-600" />
            Create DGM Experiment
          </h1>
          <p className="text-gray-600 mt-2">
            Set up a new Darwin Gödel Machine experiment to evolve trading strategies
          </p>
        </div>
        
        <button
          onClick={onCancel}
          className="btn-secondary"
        >
          <X className="w-4 h-4 mr-2" />
          Cancel
        </button>
      </div>

      {/* Form */}
      <div className="card p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Info className="w-5 h-5 mr-2" />
              Experiment Details
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Experiment Name *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className="input"
                  placeholder="My Trading Strategy Evolution"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <input
                  type="text"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  className="input"
                  placeholder="Brief description of the experiment"
                />
              </div>
            </div>
          </div>

          {/* Base Strategy */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Target className="w-5 h-5 mr-2" />
              Base Strategy
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Strategy Type
                </label>
                <select
                  value={formData.baseStrategy.type}
                  onChange={(e) => handleInputChange('baseStrategy', {
                    ...formData.baseStrategy,
                    type: e.target.value,
                  })}
                  className="input"
                >
                  <option value="moving_average_crossover">Moving Average Crossover</option>
                  <option value="rsi_mean_reversion">RSI Mean Reversion</option>
                  <option value="bollinger_bands">Bollinger Bands</option>
                  <option value="macd_divergence">MACD Divergence</option>
                </select>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Fast Period
                  </label>
                  <input
                    type="number"
                    value={formData.baseStrategy.parameters.fast_period}
                    onChange={(e) => handleParameterChange('fast_period', parseInt(e.target.value))}
                    className="input"
                    min="1"
                    max="100"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Slow Period
                  </label>
                  <input
                    type="number"
                    value={formData.baseStrategy.parameters.slow_period}
                    onChange={(e) => handleParameterChange('slow_period', parseInt(e.target.value))}
                    className="input"
                    min="1"
                    max="200"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Signal Threshold
                  </label>
                  <input
                    type="number"
                    step="0.001"
                    value={formData.baseStrategy.parameters.signal_threshold}
                    onChange={(e) => handleParameterChange('signal_threshold', parseFloat(e.target.value))}
                    className="input"
                    min="0"
                    max="1"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Evolution Parameters */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Zap className="w-5 h-5 mr-2" />
              Evolution Parameters
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Population Size
                </label>
                <input
                  type="number"
                  value={formData.populationSize}
                  onChange={(e) => handleInputChange('populationSize', parseInt(e.target.value))}
                  className="input"
                  min="10"
                  max="200"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Number of strategies per generation
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Generations
                </label>
                <input
                  type="number"
                  value={formData.generations}
                  onChange={(e) => handleInputChange('generations', parseInt(e.target.value))}
                  className="input"
                  min="5"
                  max="100"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Number of evolution cycles
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Mutation Rate
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={formData.mutationRate}
                  onChange={(e) => handleInputChange('mutationRate', parseFloat(e.target.value))}
                  className="input"
                  min="0.01"
                  max="0.5"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Probability of parameter mutations
                </p>
              </div>
            </div>
          </div>

          {/* Estimated Runtime */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start space-x-2">
              <Settings className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-blue-900">Estimated Runtime</h4>
                <p className="text-sm text-blue-700 mt-1">
                  This experiment will test approximately{' '}
                  <span className="font-medium">
                    {(formData.populationSize * formData.generations).toLocaleString()}
                  </span>{' '}
                  strategy combinations and should complete in approximately{' '}
                  <span className="font-medium">
                    {Math.ceil((formData.populationSize * formData.generations) / 100)} minutes
                  </span>.
                </p>
              </div>
            </div>
          </div>

          {/* Submit */}
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onCancel}
              className="btn-secondary"
            >
              Cancel
            </button>
            
            <button
              type="submit"
              className="btn-primary"
              disabled={!formData.name.trim()}
            >
              <Beaker className="w-4 h-4 mr-2" />
              Start Experiment
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}