#!/usr/bin/env python3
"""
Git Hooks Installation Script

This script installs Git hooks from the .github/hooks directory to the .git/hooks directory.
"""

import os
import shutil
import stat
from pathlib import Path


def install_hooks():
    """Install Git hooks from .github/hooks to .git/hooks"""
    # Get the project root directory
    project_root = Path.cwd()
    
    # Source and destination directories
    source_dir = project_root / ".github" / "hooks"
    dest_dir = project_root / ".git" / "hooks"
    
    # Check if source directory exists
    if not source_dir.exists():
        print(f"❌ Source directory not found: {source_dir}")
        return False
    
    # Check if destination directory exists
    if not dest_dir.exists():
        print(f"Creating destination directory: {dest_dir}")
        dest_dir.mkdir(parents=True, exist_ok=True)
    
    # Copy each hook file
    hook_files = list(source_dir.glob("*"))
    if not hook_files:
        print("❌ No hook files found in source directory")
        return False
    
    installed_hooks = []
    for hook_file in hook_files:
        if hook_file.is_file():
            # Destination file path
            dest_file = dest_dir / hook_file.name
            
            # Copy the file
            shutil.copy2(hook_file, dest_file)
            
            # Make the file executable
            os.chmod(dest_file, os.stat(dest_file).st_mode | stat.S_IXUSR | stat.S_IXGRP | stat.S_IXOTH)
            
            installed_hooks.append(hook_file.name)
            print(f"✅ Installed hook: {hook_file.name}")
    
    if installed_hooks:
        print(f"\n🎉 Successfully installed {len(installed_hooks)} Git hooks:")
        for hook in installed_hooks:
            print(f"  - {hook}")
        return True
    else:
        print("❌ No hooks were installed")
        return False


if __name__ == "__main__":
    install_hooks()