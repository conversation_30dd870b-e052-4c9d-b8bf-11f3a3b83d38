// src/demo/server.ts - Forex Demo Server Entry Point
import { createForexDemoApp } from './app';

async function start() {
  const app = await createForexDemoApp();
  
  try {
    await app.listen({ port: 3001, host: '0.0.0.0' });
    console.log('🚀 Forex Trading Platform Demo running on http://localhost:3001');
    console.log('📊 Metrics available at http://localhost:3001/metrics');
    console.log('🏥 Health check at http://localhost:3001/health');
    console.log('🔌 WebSocket server running on ws://localhost:8080');
    console.log('🌐 Demo UI available at http://localhost:3000');
  } catch (err) {
    console.error('Failed to start server:', err);
    process.exit(1);
  }
}

start();