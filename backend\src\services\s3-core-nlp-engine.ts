// s3-core-nlp-engine.ts
// S3 Core Natural Language Processing Engine
// Translates natural language queries into trading analysis

import OpenAI from 'openai';
import { z } from 'zod';

// Schema definitions for structured responses
const TradingQuerySchema = z.object({
  type: z.enum(['analysis', 'scan', 'strategy', 'indicator', 'general']),
  pair: z.string().optional(),
  timeframe: z.string().optional(),
  indicators: z.array(z.string()).optional(),
  action: z.enum(['buy', 'sell', 'hold', 'scan']).optional(),
  confidence: z.number().min(0).max(1),
  reasoning: z.string()
});

const TradingAnalysisSchema = z.object({
  pair: z.string(),
  timeframe: z.string(),
  analysis: z.object({
    trend: z.enum(['bullish', 'bearish', 'sideways']),
    strength: z.number().min(0).max(1),
    key_levels: z.object({
      support: z.array(z.number()),
      resistance: z.array(z.number())
    }),
    indicators: z.record(z.any()),
    signals: z.array(z.object({
      type: z.string(),
      strength: z.number(),
      description: z.string()
    }))
  }),
  recommendation: z.object({
    action: z.enum(['buy', 'sell', 'hold']),
    confidence: z.number().min(0).max(1),
    reasoning: z.string(),
    risk_level: z.enum(['low', 'medium', 'high'])
  })
});

export type TradingQuery = z.infer<typeof TradingQuerySchema>;
export type TradingAnalysis = z.infer<typeof TradingAnalysisSchema>;

export class S3CoreEngine {
  private openai: OpenAI;
  private systemPrompt: string;

  constructor(apiKey: string) {
    this.openai = new OpenAI({ apiKey });
    this.systemPrompt = this.buildSystemPrompt();
  }

  private buildSystemPrompt(): string {
    return `You are S3 Core, an advanced trading analysis AI with deep expertise in forex markets and technical analysis.

Your capabilities include:
- Technical analysis using multiple indicators (RSI, MACD, EMAs, SMAs, Bollinger Bands, Stochastic)
- Pattern recognition (support/resistance, trend lines, chart patterns)
- Multi-timeframe analysis
- Risk assessment and position sizing
- Market sentiment analysis
- Real-time market scanning

Key principles:
1. Always provide specific, actionable analysis
2. Include confidence levels for all recommendations
3. Explain your reasoning clearly
4. Consider multiple timeframes when relevant
5. Account for risk management in all recommendations
6. Use proper forex terminology and conventions

Available currency pairs: EURUSD, GBPUSD, USDJPY, AUDUSD, USDCAD, USDCHF, NZDUSD, EURJPY, GBPJPY, EURGBP

Timeframes: 1M, 5M, 15M, 1H, 4H, 1D, 1W

When analyzing queries:
- Extract the specific pair and timeframe if mentioned
- Identify the type of analysis requested
- Provide structured responses with clear recommendations
- Include technical indicator values when relevant
- Suggest appropriate risk management parameters

Always respond in a professional, confident manner while acknowledging market uncertainties.`;
  }

  async translateQuery(userQuery: string): Promise<TradingQuery> {
    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          { role: 'system', content: this.systemPrompt },
          { 
            role: 'user', 
            content: `Analyze this trading query and extract structured information: "${userQuery}"
            
            Respond with a JSON object containing:
            - type: The type of query (analysis, scan, strategy, indicator, general)
            - pair: Currency pair if specified (e.g., "EURUSD")
            - timeframe: Timeframe if specified (e.g., "4H")
            - indicators: Array of indicators mentioned
            - action: Suggested action if applicable
            - confidence: Your confidence in understanding the query (0-1)
            - reasoning: Brief explanation of your interpretation`
          }
        ],
        temperature: 0.3,
        max_tokens: 500
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response from OpenAI');
      }

      // Parse JSON response
      const parsed = JSON.parse(content);
      return TradingQuerySchema.parse(parsed);

    } catch (error) {
      console.error('Error translating query:', error);
      
      // Fallback parsing for simple queries
      return this.fallbackQueryParsing(userQuery);
    }
  }

  async generateAnalysis(query: TradingQuery, marketData?: any): Promise<TradingAnalysis> {
    try {
      const analysisPrompt = this.buildAnalysisPrompt(query, marketData);
      
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          { role: 'system', content: this.systemPrompt },
          { role: 'user', content: analysisPrompt }
        ],
        temperature: 0.2,
        max_tokens: 1000
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No analysis response from OpenAI');
      }

      const parsed = JSON.parse(content);
      return TradingAnalysisSchema.parse(parsed);

    } catch (error) {
      console.error('Error generating analysis:', error);
      throw new Error('Failed to generate trading analysis');
    }
  }

  async scanMarkets(criteria: string): Promise<Array<{ pair: string; signal: string; strength: number }>> {
    try {
      const scanPrompt = `Scan the major forex pairs for opportunities based on: "${criteria}"
      
      Consider these pairs: EURUSD, GBPUSD, USDJPY, AUDUSD, USDCAD, USDCHF, NZDUSD, EURJPY, GBPJPY, EURGBP
      
      For each pair that meets the criteria, provide:
      - pair: Currency pair code
      - signal: Brief description of the signal/opportunity
      - strength: Signal strength (0-1)
      
      Respond with a JSON array of results, maximum 5 pairs.`;

      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          { role: 'system', content: this.systemPrompt },
          { role: 'user', content: scanPrompt }
        ],
        temperature: 0.4,
        max_tokens: 800
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        return [];
      }

      return JSON.parse(content);

    } catch (error) {
      console.error('Error scanning markets:', error);
      return [];
    }
  }

  async explainIndicator(indicator: string, pair?: string, timeframe?: string): Promise<string> {
    try {
      const context = pair && timeframe ? ` for ${pair} on ${timeframe} timeframe` : '';
      
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          { role: 'system', content: this.systemPrompt },
          { 
            role: 'user', 
            content: `Explain the ${indicator} indicator${context}. Include:
            - What it measures
            - How to interpret it
            - Current reading if pair/timeframe specified
            - Trading signals it provides
            - Best practices for using it
            
            Keep the explanation clear and practical for traders.`
          }
        ],
        temperature: 0.3,
        max_tokens: 600
      });

      return response.choices[0]?.message?.content || 'Unable to explain indicator';

    } catch (error) {
      console.error('Error explaining indicator:', error);
      return 'Error explaining indicator';
    }
  }

  async validateStrategy(strategyDescription: string): Promise<{
    isValid: boolean;
    confidence: number;
    suggestions: string[];
    riskAssessment: string;
  }> {
    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          { role: 'system', content: this.systemPrompt },
          { 
            role: 'user', 
            content: `Validate this trading strategy: "${strategyDescription}"
            
            Analyze:
            - Technical soundness
            - Risk management
            - Entry/exit criteria
            - Market conditions suitability
            
            Respond with JSON:
            {
              "isValid": boolean,
              "confidence": number (0-1),
              "suggestions": ["improvement1", "improvement2"],
              "riskAssessment": "risk level and explanation"
            }`
          }
        ],
        temperature: 0.2,
        max_tokens: 700
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No validation response');
      }

      return JSON.parse(content);

    } catch (error) {
      console.error('Error validating strategy:', error);
      return {
        isValid: false,
        confidence: 0,
        suggestions: ['Unable to validate strategy'],
        riskAssessment: 'Unknown risk level'
      };
    }
  }

  private buildAnalysisPrompt(query: TradingQuery, marketData?: any): string {
    let prompt = `Provide comprehensive technical analysis`;
    
    if (query.pair) {
      prompt += ` for ${query.pair}`;
    }
    
    if (query.timeframe) {
      prompt += ` on ${query.timeframe} timeframe`;
    }
    
    prompt += `.\n\n`;
    
    if (marketData) {
      prompt += `Current market data:\n${JSON.stringify(marketData, null, 2)}\n\n`;
    }
    
    prompt += `Provide analysis in this JSON format:
    {
      "pair": "EURUSD",
      "timeframe": "4H",
      "analysis": {
        "trend": "bullish|bearish|sideways",
        "strength": 0.8,
        "key_levels": {
          "support": [1.0850, 1.0820],
          "resistance": [1.0920, 1.0950]
        },
        "indicators": {
          "RSI": {"value": 65, "signal": "neutral"},
          "MACD": {"value": 0.0012, "signal": "bullish"},
          "EMA_50": {"value": 1.0875, "position": "above"}
        },
        "signals": [
          {
            "type": "breakout",
            "strength": 0.7,
            "description": "Price breaking above resistance"
          }
        ]
      },
      "recommendation": {
        "action": "buy|sell|hold",
        "confidence": 0.75,
        "reasoning": "Detailed explanation of recommendation",
        "risk_level": "low|medium|high"
      }
    }`;
    
    return prompt;
  }

  private fallbackQueryParsing(userQuery: string): TradingQuery {
    const query = userQuery.toLowerCase();
    
    // Extract currency pairs
    const pairRegex = /(eur\/usd|eurusd|gbp\/usd|gbpusd|usd\/jpy|usdjpy|aud\/usd|audusd|usd\/cad|usdcad|usd\/chf|usdchf|nzd\/usd|nzdusd|eur\/jpy|eurjpy|gbp\/jpy|gbpjpy|eur\/gbp|eurgbp)/i;
    const pairMatch = query.match(pairRegex);
    const pair = pairMatch ? pairMatch[0].replace('/', '').toUpperCase() : undefined;
    
    // Extract timeframes
    const timeframeRegex = /(1m|5m|15m|1h|4h|1d|1w|daily|hourly|weekly)/i;
    const timeframeMatch = query.match(timeframeRegex);
    const timeframe = timeframeMatch ? timeframeMatch[0].toUpperCase() : undefined;
    
    // Determine query type
    let type: 'analysis' | 'scan' | 'strategy' | 'indicator' | 'general' = 'general';
    
    if (query.includes('scan') || query.includes('find') || query.includes('search')) {
      type = 'scan';
    } else if (query.includes('rsi') || query.includes('macd') || query.includes('ema') || query.includes('sma')) {
      type = 'indicator';
    } else if (query.includes('strategy') || query.includes('system')) {
      type = 'strategy';
    } else if (pair || query.includes('analysis') || query.includes('trend')) {
      type = 'analysis';
    }
    
    // Extract indicators
    const indicators: string[] = [];
    if (query.includes('rsi')) indicators.push('RSI');
    if (query.includes('macd')) indicators.push('MACD');
    if (query.includes('ema')) indicators.push('EMA');
    if (query.includes('sma')) indicators.push('SMA');
    if (query.includes('bollinger')) indicators.push('Bollinger Bands');
    if (query.includes('stochastic')) indicators.push('Stochastic');
    
    // Determine action
    let action: 'buy' | 'sell' | 'hold' | 'scan' | undefined;
    if (query.includes('buy') || query.includes('long')) action = 'buy';
    else if (query.includes('sell') || query.includes('short')) action = 'sell';
    else if (query.includes('hold')) action = 'hold';
    else if (type === 'scan') action = 'scan';
    
    return {
      type,
      pair,
      timeframe,
      indicators: indicators.length > 0 ? indicators : undefined,
      action,
      confidence: 0.6, // Lower confidence for fallback parsing
      reasoning: 'Parsed using fallback pattern matching'
    };
  }
}

export default S3CoreEngine;