# tests/test_risk_management.py
import pytest
from datetime import datetime
from src.risk import RiskManager, RiskLimits, PortfolioState, Position
from src.events import EventBus

class TestRiskManagement:
    """Test suite for the risk management system"""
    
    @pytest.fixture
    def event_bus(self):
        return EventBus()
    
    @pytest.fixture
    def risk_manager(self, event_bus):
        return RiskManager(event_bus)
    
    @pytest.mark.asyncio
    async def test_trigger_position_size_limits(self, risk_manager):
        """Test position size limit enforcement"""
        portfolio = PortfolioState(
            balance=10000,
            equity=10000,
            positions=[
                Position(symbol='EURUSD', size=5.0, unrealized_pnl=-100),
                Position(symbol='GBPUSD', size=3.0, unrealized_pnl=50)
            ],
            total_exposure=8.0
        )
        
        new_position = {
            'symbol': 'USDJPY',
            'size': 3.0,  # This would exceed max exposure
            'estimated_margin': 1000
        }
        
        risk_check = await risk_manager.evaluate_new_position(portfolio, new_position)
        
        assert risk_check.approved is False
        assert 'MAX_EXPOSURE_EXCEEDED' in risk_check.violations
    
    @pytest.mark.asyncio
    async def test_enforce_drawdown_limits(self, risk_manager, event_bus):
        """Test drawdown limit enforcement"""
        portfolio = PortfolioState(
            balance=10000,
            equity=7000,  # 30% drawdown
            positions=[],
            total_exposure=0,
            max_equity=10500
        )
        
        risk_check = await risk_manager.evaluate_portfolio_risk(portfolio)
        
        assert risk_check.critical is True
        assert 'CLOSE_ALL_POSITIONS' in risk_check.recommended_actions
        assert 'MAX_DRAWDOWN_EXCEEDED' in risk_check.violations
    
    @pytest.mark.asyncio
    async def test_calculate_correlation_risk(self, risk_manager):
        """Test correlation risk calculation"""
        portfolio = PortfolioState(
            balance=10000,
            equity=10000,
            positions=[
                Position(symbol='EURUSD', size=2.0, unrealized_pnl=0),
                Position(symbol='EURJPY', size=2.0, unrealized_pnl=0),
                Position(symbol='EURGBP', size=2.0, unrealized_pnl=0)
            ],
            total_exposure=6.0
        )
        
        correlation_risk = risk_manager.calculate_correlation_risk(portfolio)
        
        # High EUR concentration should result in high correlation risk
        assert correlation_risk.overall_score > 0.5
        assert 'HIGH_CURRENCY_CONCENTRATION' in correlation_risk.warnings
    
    @pytest.mark.asyncio
    async def test_margin_requirements(self, risk_manager):
        """Test margin requirement checks"""
        portfolio = PortfolioState(
            balance=10000,
            equity=10000,
            positions=[],
            total_exposure=0,
            margin_used=8000,  # 80% margin used
            free_margin=2000
        )
        
        new_position = {
            'symbol': 'EURUSD',
            'size': 1.0,
            'estimated_margin': 2500  # Would exceed margin limits
        }
        
        risk_check = await risk_manager.evaluate_new_position(portfolio, new_position)
        
        assert 'INSUFFICIENT_MARGIN' in risk_check.violations
        assert risk_check.approved is False
    
    def test_risk_limits_update(self, risk_manager):
        """Test risk limits update"""
        new_limits = RiskLimits(
            max_drawdown_percent=15.0,
            max_daily_loss_percent=3.0,
            max_position_size_percent=5.0,
            max_total_exposure=5.0,
            max_correlation_risk=0.6,
            min_free_margin_percent=40.0
        )
        
        risk_manager.update_risk_limits(new_limits)
        
        updated_limits = risk_manager.get_risk_limits()
        assert updated_limits.max_drawdown_percent == 15.0
        assert updated_limits.max_total_exposure == 5.0
    
    def test_risk_metrics_calculation(self, risk_manager):
        """Test comprehensive risk metrics calculation"""
        portfolio = PortfolioState(
            balance=10000,
            equity=9500,
            positions=[
                Position(symbol='EURUSD', size=2.0, unrealized_pnl=-200),
                Position(symbol='GBPUSD', size=1.5, unrealized_pnl=100)
            ],
            total_exposure=3.5,
            max_equity=10200,
            margin_used=3000
        )
        
        metrics = risk_manager.get_risk_metrics(portfolio)
        
        assert 'current_drawdown' in metrics
        assert 'daily_loss' in metrics
        assert 'margin_utilization' in metrics
        assert 'total_exposure' in metrics
        assert 'correlation_risk' in metrics
        assert 'diversification_score' in metrics
        assert 'risk_limits' in metrics
        
        # Check calculated values
        expected_drawdown = (10200 - 9500) / 10200
        assert abs(metrics['current_drawdown'] - expected_drawdown) < 0.01
    
    @pytest.mark.asyncio
    async def test_daily_loss_limits(self, risk_manager):
        """Test daily loss limit enforcement"""
        portfolio = PortfolioState(
            balance=10000,
            equity=9400,  # 6% loss (exceeds 5% daily limit)
            positions=[
                Position(symbol='EURUSD', size=2.0, unrealized_pnl=-600)
            ],
            total_exposure=2.0
        )
        
        risk_check = await risk_manager.evaluate_portfolio_risk(portfolio)
        
        # Should trigger daily loss warning/violation
        assert risk_check.risk_score > 0
    
    def test_correlation_analyzer_integration(self, risk_manager):
        """Test integration with correlation analyzer"""
        positions = [
            Position(symbol='EURUSD', size=2.0, unrealized_pnl=0),
            Position(symbol='GBPUSD', size=1.5, unrealized_pnl=0),
            Position(symbol='USDJPY', size=1.0, unrealized_pnl=0)
        ]
        
        correlation_risk = risk_manager.correlation_analyzer.calculate_portfolio_correlation_risk(positions)
        
        assert isinstance(correlation_risk.overall_score, float)
        assert 0 <= correlation_risk.overall_score <= 1
        assert isinstance(correlation_risk.diversification_score, float)
        assert isinstance(correlation_risk.pair_correlations, dict)
        assert isinstance(correlation_risk.warnings, list)
    
    def test_diversification_suggestions(self, risk_manager):
        """Test diversification suggestions"""
        positions = [
            Position(symbol='EURUSD', size=2.0, unrealized_pnl=0),
            Position(symbol='EURJPY', size=2.0, unrealized_pnl=0)
        ]
        
        suggestions = risk_manager.correlation_analyzer.get_diversification_suggestions(positions)
        
        assert isinstance(suggestions, list)
        assert len(suggestions) > 0
        
        # Should suggest reducing EUR concentration
        suggestion_text = ' '.join(suggestions).lower()
        assert 'concentration' in suggestion_text or 'diversif' in suggestion_text
    
    @pytest.mark.asyncio
    async def test_risk_alert_system(self, risk_manager, event_bus):
        """Test risk alert system"""
        events_received = []
        
        def alert_handler(event):
            events_received.append(event)
        
        event_bus.subscribe('RISK_LIMIT_BREACHED', alert_handler)
        
        # Create critical risk situation
        portfolio = PortfolioState(
            balance=10000,
            equity=6000,  # 40% drawdown - critical
            positions=[],
            total_exposure=0,
            max_equity=10000
        )
        
        await risk_manager.evaluate_portfolio_risk(portfolio)
        
        # Allow time for async processing
        import asyncio
        await asyncio.sleep(0.1)
        
        # Should have received risk alert
        assert len(events_received) > 0
        alert_event = events_received[0]
        assert alert_event.payload['severity'] == 'CRITICAL'
        assert alert_event.payload['limit_type'] == 'MAX_DRAWDOWN'
    
    def test_position_risk_scoring(self, risk_manager):
        """Test position risk scoring"""
        portfolio = PortfolioState(
            balance=10000,
            equity=10000,
            positions=[],
            total_exposure=0
        )
        
        # Low risk position
        low_risk_position = {
            'symbol': 'EURUSD',
            'size': 0.5,  # Small size
            'estimated_margin': 500
        }
        
        # High risk position
        high_risk_position = {
            'symbol': 'EURUSD',
            'size': 5.0,  # Large size
            'estimated_margin': 5000
        }
        
        import asyncio
        
        async def test_positions():
            low_risk_eval = await risk_manager.evaluate_new_position(portfolio, low_risk_position)
            high_risk_eval = await risk_manager.evaluate_new_position(portfolio, high_risk_position)
            
            # High risk position should have higher risk score
            assert high_risk_eval.risk_score > low_risk_eval.risk_score
            
            # Low risk should be approved, high risk might not be
            assert low_risk_eval.approved is True
        
        asyncio.run(test_positions())
    
    def test_emergency_actions(self, risk_manager):
        """Test emergency action recommendations"""
        critical_actions = risk_manager._get_emergency_actions('CRITICAL', 'MAX_DRAWDOWN')
        high_actions = risk_manager._get_emergency_actions('HIGH', 'MAX_EXPOSURE')
        medium_actions = risk_manager._get_emergency_actions('MEDIUM', 'CORRELATION_RISK')
        
        assert 'STOP_ALL_TRADING' in critical_actions
        assert 'CLOSE_ALL_POSITIONS' in critical_actions
        
        assert len(high_actions) > 0
        assert len(medium_actions) > 0
        
        # Critical actions should be more severe
        assert len(critical_actions) >= len(high_actions)