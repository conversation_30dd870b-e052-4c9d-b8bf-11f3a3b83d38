
// src/components/themes/ProfessionalSinglePage.tsx
import React, { useState, useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { 
  Bot, BarChart3, DollarSign, Target, Activity, Zap, 
  MessageCircle, Send, User, TestTube, Save, Play, 
  Pause, Settings, Brain, Copy, Lightbulb, ChevronDown,
  Shield, Award, Users, TrendingUp, PieChart, LineChart,
  CheckCircle, AlertCircle, Info
} from 'lucide-react';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

export const ProfessionalSinglePage: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([
    { id: '1', role: 'assistant', content: 'Welcome to Darwin Gödel Platform. I'm here to help you optimize your trading strategies with AI-powered insights.', timestamp: new Date() },
    { id: '2', role: 'assistant', content: 'Our platform maintains 99.9% uptime and is SOC 2 compliant for enterprise security standards.', timestamp: new Date() }
  ]);
  const [inputMessage, setInputMessage] = useState('');

  // Refs for smooth scrolling
  const heroRef = useRef(null);
  const strategiesRef = useRef(null);
  const aiChatRef = useRef(null);
  const metricsRef = useRef(null);

  const heroInView = useInView(heroRef, { once: true });
  const strategiesInView = useInView(strategiesRef, { once: true });
  const aiChatInView = useInView(aiChatRef, { once: true });
  const metricsInView = useInView(metricsRef, { once: true });

  const scrollToSection = (ref: React.RefObject<HTMLElement>) => {
    ref.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = () => {
    if (!inputMessage.trim()) return;

    const newMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: inputMessage,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, newMessage]);
    setInputMessage('');

    // Simulate AI response
    setTimeout(() => {
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: `I've analyzed your query about "${inputMessage}". Based on current market data and risk parameters, I recommend a balanced approach with 15% allocation adjustment.`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, aiResponse]);
    }, 1500);
  };

  return (
    <div className="min-h-screen bg-gray-50 text-gray-800">
      {/* Fixed Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 shadow-sm backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                <Bot className="w-5 h-5 text-white" />
              </div>
              <h1 className="text-xl font-bold text-gray-900">Darwin Gödel Platform</h1>
            </div>

            <div className="flex items-center space-x-6">
              <div className="hidden md:flex items-center space-x-4 text-sm text-gray-600">
                <div className="flex items-center">
                  <Shield className="w-4 h-4 mr-1 text-green-600" />
                  SOC 2 Compliant
                </div>
                <div className="flex items-center">
                  <Award className="w-4 h-4 mr-1 text-blue-600" />
                  99.9% Uptime
                </div>
              </div>

              <div className="flex space-x-4 text-sm">
                <button onClick={() => scrollToSection(heroRef)} className="text-gray-600 hover:text-blue-600 transition-colors">
                  Home
                </button>
                <button onClick={() => scrollToSection(strategiesRef)} className="text-gray-600 hover:text-blue-600 transition-colors">
                  Strategies
                </button>
                <button onClick={() => scrollToSection(aiChatRef)} className="text-gray-600 hover:text-blue-600 transition-colors">
                  AI Assistant
                </button>
                <button onClick={() => scrollToSection(metricsRef)} className="text-gray-600 hover:text-blue-600 transition-colors">
                  Analytics
                </button>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section ref={heroRef} className="min-h-screen flex items-center justify-center relative pt-16 bg-gradient-to-br from-blue-50 to-indigo-100">
        <motion.div 
          className="text-center max-w-4xl mx-auto px-4"
          initial={{ opacity: 0, y: 50 }}
          animate={heroInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 1 }}
        >
          <motion.h1 
            className="text-5xl md:text-7xl font-bold text-gray-900 mb-6"
            initial={{ scale: 0.8 }}
            animate={heroInView ? { scale: 1 } : {}}
            transition={{ duration: 1.2, ease: "easeOut" }}
          >
            Enterprise AI Trading
          </motion.h1>

          <motion.p 
            className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto"
            initial={{ opacity: 0 }}
            animate={heroInView ? { opacity: 1 } : {}}
            transition={{ delay: 0.5, duration: 1 }}
          >
            Professional-grade AI-powered trading platform with advanced strategy verification, 
            comprehensive risk management, and enterprise security standards.
          </motion.p>

          <motion.div 
            className="flex flex-col sm:flex-row gap-4 justify-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={heroInView ? { opacity: 1, y: 0 } : {}}
            transition={{ delay: 1, duration: 0.8 }}
          >
            <button 
              onClick={() => scrollToSection(strategiesRef)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              Explore Strategies
            </button>
            <button 
              onClick={() => scrollToSection(aiChatRef)}
              className="bg-white hover:bg-gray-50 text-blue-600 px-8 py-3 rounded-lg font-medium border border-blue-200 transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              Try AI Assistant
            </button>
          </motion.div>

          {/* Trust indicators */}
          <motion.div 
            className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={heroInView ? { opacity: 1, y: 0 } : {}}
            transition={{ delay: 1.2, duration: 0.8 }}
          >
            {[
              { label: 'Test Coverage', value: '95%', icon: CheckCircle },
              { label: 'Active Users', value: '1,247', icon: Users },
              { label: 'Strategies', value: '50+', icon: Target },
              { label: 'Uptime', value: '99.9%', icon: Shield }
            ].map((stat, index) => (
              <div key={stat.label} className="text-center">
                <stat.icon className="w-6 h-6 text-blue-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
                <div className="text-sm text-gray-600">{stat.label}</div>
              </div>
            ))}
          </motion.div>

          <motion.div 
            className="mt-12"
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <ChevronDown className="w-8 h-8 text-blue-600 mx-auto" />
          </motion.div>
        </motion.div>
      </section>

      {/* Strategy Portfolio Section */}
      <section ref={strategiesRef} className="min-h-screen py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: -30 }}
            animate={strategiesInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4 text-center">Strategy Portfolio</h2>
            <p className="text-xl text-gray-600 text-center mb-12 max-w-3xl mx-auto">
              Professionally managed trading strategies with comprehensive risk assessment and performance tracking.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
            {[
              { 
                name: 'Conservative Growth', 
                type: 'Mean Reversion', 
                status: 'Active', 
                performance: '+12.3%', 
                risk: 'Low',
                trades: 156,
                sharpe: '1.8',
                maxDrawdown: '3.2%'
              },
              { 
                name: 'Momentum Capture', 
                type: 'Trend Following', 
                status: 'Active', 
                performance: '+18.7%', 
                risk: 'Medium',
                trades: 89,
                sharpe: '2.1',
                maxDrawdown: '7.8%'
              },
              { 
                name: 'Market Neutral', 
                type: 'Arbitrage', 
                status: 'Paused', 
                performance: '+5.2%', 
                risk: 'Low',
                trades: 234,
                sharpe: '1.4',
                maxDrawdown: '2.1%'
              },
              { 
                name: 'Volatility Harvesting', 
                type: 'Options Strategy', 
                status: 'Active', 
                performance: '+15.4%', 
                risk: 'Medium',
                trades: 67,
                sharpe: '1.9',
                maxDrawdown: '5.6%'
              },
              { 
                name: 'Sector Rotation', 
                type: 'Systematic', 
                status: 'Active', 
                performance: '+22.1%', 
                risk: 'High',
                trades: 123,
                sharpe: '2.3',
                maxDrawdown: '12.4%'
              },
              { 
                name: 'Multi-Asset Balanced', 
                type: 'Diversified', 
                status: 'Active', 
                performance: '+9.8%', 
                risk: 'Low',
                trades: 78,
                sharpe: '1.6',
                maxDrawdown: '4.3%'
              }
            ].map((strategy, index) => (
              <motion.div
                key={strategy.name}
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-300"
                initial={{ opacity: 0, y: 20 }}
                animate={strategiesInView ? { opacity: 1, y: 0 } : {}}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                whileHover={{ scale: 1.02 }}
              >
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">{strategy.name}</h3>
                  <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                    strategy.status === 'Active' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {strategy.status}
                  </span>
                </div>

                <div className="text-sm text-gray-600 mb-4">{strategy.type}</div>

                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Performance</span>
                    <span className="font-semibold text-green-600">{strategy.performance}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Risk Level</span>
                    <span className={`font-medium ${
                      strategy.risk === 'Low' ? 'text-green-600' :
                      strategy.risk === 'Medium' ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {strategy.risk}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Trades</span>
                    <span className="font-medium text-gray-900">{strategy.trades}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Sharpe Ratio</span>
                    <span className="font-medium text-gray-900">{strategy.sharpe}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Max Drawdown</span>
                    <span className="font-medium text-gray-900">{strategy.maxDrawdown}</span>
                  </div>
                </div>

                <div className="flex space-x-2 mt-6">
                  <button className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 text-sm rounded transition-colors">
                    View Details
                  </button>
                  <button className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-3 text-sm rounded transition-colors">
                    Configure
                  </button>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* AI Assistant Section */}
      <section ref={aiChatRef} className="min-h-screen py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: -30 }}
            animate={aiChatInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4 text-center">AI Strategy Assistant</h2>
            <p className="text-xl text-gray-600 text-center mb-12 max-w-3xl mx-auto">
              Get intelligent insights and recommendations from our advanced AI assistant trained on market data and trading strategies.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Chat Interface */}
            <motion.div
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
              initial={{ opacity: 0, x: -50 }}
              animate={aiChatInView ? { opacity: 1, x: 0 } : {}}
              transition={{ delay: 0.3, duration: 0.8 }}
            >
              <div className="flex items-center mb-6">
                <Bot className="w-6 h-6 text-blue-600 mr-2" />
                <h3 className="text-lg font-semibold text-gray-900">Chat with AI Assistant</h3>
              </div>

              <div className="h-96 overflow-y-auto mb-4 bg-gray-50 rounded-lg p-4">
                <div className="space-y-4">
                  {messages.map((message) => (
                    <div key={message.id} className="flex items-start space-x-3">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                        message.role === 'user' ? 'bg-gray-200' : 'bg-blue-100'
                      }`}>
                        {message.role === 'user' ? (
                          <User className="w-4 h-4 text-gray-600" />
                        ) : (
                          <Bot className="w-4 h-4 text-blue-600" />
                        )}
                      </div>
                      <div className={`rounded-lg p-3 max-w-xs ${
                        message.role === 'user' 
                          ? 'bg-blue-600 text-white ml-auto' 
                          : 'bg-white border border-gray-200'
                      }`}>
                        <p className="text-sm">{message.content}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex">
                <input
                  type="text"
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  placeholder="Ask about strategies, risk management, or market analysis..."
                  className="flex-1 border border-gray-300 rounded-l-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <button 
                  onClick={handleSendMessage}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-r-lg transition-colors"
                >
                  Send
                </button>
              </div>
            </motion.div>

            {/* Quick Actions */}
            <motion.div
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
              initial={{ opacity: 0, x: 50 }}
              animate={aiChatInView ? { opacity: 1, x: 0 } : {}}
              transition={{ delay: 0.5, duration: 0.8 }}
            >
              <div className="flex items-center mb-6">
                <Lightbulb className="w-6 h-6 text-blue-600 mr-2" />
                <h3 className="text-lg font-semibold text-gray-900">Quick Insights</h3>
              </div>

              <div className="space-y-3">
                {[
                  { 
                    title: 'Portfolio Analysis', 
                    prompt: 'Analyze my current portfolio allocation and suggest optimizations based on market conditions',
                    icon: PieChart
                  },
                  { 
                    title: 'Risk Assessment', 
                    prompt: 'Evaluate portfolio risk exposure and recommend hedging strategies for current market volatility',
                    icon: Shield
                  },
                  { 
                    title: 'Market Outlook', 
                    prompt: 'Provide market outlook analysis and identify emerging opportunities in different sectors',
                    icon: TrendingUp
                  },
                  { 
                    title: 'Strategy Comparison', 
                    prompt: 'Compare performance of momentum vs mean reversion strategies in current market environment',
                    icon: BarChart3
                  }
                ].map((item, index) => (
                  <motion.button
                    key={item.title}
                    onClick={() => setInputMessage(item.prompt)}
                    className="w-full text-left p-4 bg-gray-50 hover:bg-gray-100 rounded-lg border border-gray-200 transition-all duration-300 group"
                    whileHover={{ scale: 1.02 }}
                  >
                    <div className="flex items-center mb-2">
                      <item.icon className="w-5 h-5 text-blue-600 mr-2" />
                      <div className="font-medium text-gray-900">{item.title}</div>
                    </div>
                    <div className="text-sm text-gray-600 group-hover:text-gray-700 transition-colors">
                      {item.prompt}
                    </div>
                  </motion.button>
                ))}
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Analytics Dashboard Section */}
      <section ref={metricsRef} className="min-h-screen py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: -30 }}
            animate={metricsInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4 text-center">Analytics Dashboard</h2>
            <p className="text-xl text-gray-600 text-center mb-12 max-w-3xl mx-auto">
              Comprehensive performance metrics and analytics to track your trading success and optimize strategies.
            </p>
          </motion.div>

          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {[
              { label: 'Total Return', value: '+23.7%', change: '+5.2%', icon: TrendingUp, color: 'green' },
              { label: 'Active Strategies', value: '12', change: '+2', icon: Target, color: 'blue' },
              { label: 'Risk Score', value: '2.1', change: '-0.3', icon: Shield, color: 'yellow' },
              { label: 'Sharpe Ratio', value: '1.8', change: '+0.2', icon: BarChart3, color: 'purple' }
            ].map((metric, index) => (
              <motion.div
                key={metric.label}
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
                initial={{ opacity: 0, y: 20 }}
                animate={metricsInView ? { opacity: 1, y: 0 } : {}}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                whileHover={{ scale: 1.02 }}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{metric.label}</p>
                    <p className="text-2xl font-bold text-gray-900">{metric.value}</p>
                    <p className={`text-sm ${metric.change.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                      {metric.change} from last month
                    </p>
                  </div>
                  <div className={`w-12 h-12 bg-${metric.color}-100 rounded-lg flex items-center justify-center`}>
                    <metric.icon className={`w-6 h-6 text-${metric.color}-600`} />
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Performance Summary */}
          <motion.div
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
            initial={{ opacity: 0, y: 30 }}
            animate={metricsInView ? { opacity: 1, y: 0 } : {}}
            transition={{ delay: 0.8, duration: 0.8 }}
          >
            <div className="flex items-center mb-6">
              <LineChart className="w-6 h-6 text-blue-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Performance Summary</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Recent Activity</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center text-green-600">
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Strategy "Conservative Growth" executed 12 trades
                  </div>
                  <div className="flex items-center text-blue-600">
                    <Info className="w-4 h-4 mr-2" />
                    Portfolio rebalancing completed successfully
                  </div>
                  <div className="flex items-center text-yellow-600">
                    <AlertCircle className="w-4 h-4 mr-2" />
                    Risk threshold reached for "Sector Rotation"
                  </div>
                  <div className="flex items-center text-green-600">
                    <CheckCircle className="w-4 h-4 mr-2" />
                    AI analysis updated for market conditions
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Top Performers</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Sector Rotation</span>
                    <span className="font-medium text-green-600">+22.1%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Momentum Capture</span>
                    <span className="font-medium text-green-600">+18.7%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Volatility Harvesting</span>
                    <span className="font-medium text-green-600">+15.4%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Conservative Growth</span>
                    <span className="font-medium text-green-600">+12.3%</span>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Risk Metrics</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Portfolio Beta</span>
                    <span className="font-medium text-gray-900">0.85</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Max Drawdown</span>
                    <span className="font-medium text-gray-900">-5.2%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">VaR (95%)</span>
                    <span className="font-medium text-gray-900">-2.1%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Correlation</span>
                    <span className="font-medium text-gray-900">0.42</span>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                  <Bot className="w-5 h-5 text-white" />
                </div>
                <h3 className="text-lg font-bold">Darwin Gödel Platform</h3>
              </div>
              <p className="text-gray-400 text-sm">
                Enterprise AI Trading Solutions with professional-grade security and compliance.
              </p>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Platform</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li>Strategy Management</li>
                <li>AI Assistant</li>
                <li>Risk Analytics</li>
                <li>Performance Tracking</li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Security</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li>SOC 2 Compliance</li>
                <li>Enterprise Security</li>
                <li>Data Encryption</li>
                <li>Audit Logging</li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li>Documentation</li>
                <li>API Reference</li>
                <li>24/7 Support</li>
                <li>Training Resources</li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-sm text-gray-400">
            <p>© 2024 Darwin Gödel Platform. All rights reserved. • Enterprise AI Trading Solutions • 99.9% Uptime SLA</p>
          </div>
        </div>
      </footer>
    </div>
  );
};
