import { 
  User, 
  CreateUserRequest, 
  LoginRequest,
  UserId,
  ServiceResponse 
} from '../../shared/types';
import { UserRepository } from '@/shared/database/repositories/user.repository';
import { TokenService } from './token.service';
import { PasswordService } from './password.service';

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface AuthResult {
  user: User;
  tokens: AuthTokens;
}

export interface AuthServiceDependencies {
  userRepository: UserRepository;
  tokenService: TokenService;
  passwordService: PasswordService;
}

export class AuthService {
  constructor(private dependencies: AuthServiceDependencies) {}

  // Add the missing methods that the router expects
  async register(request: CreateUserRequest): Promise<ServiceResponse<AuthResult>> {
    return this.registerUser(request);
  }

  async login(request: LoginRequest): Promise<ServiceResponse<AuthResult>> {
    return this.loginUser(request);
  }

  async logout(_userId: UserId): Promise<ServiceResponse<void>> {
    try {
      // In a real implementation, you might want to invalidate tokens
      // For now, just return success
      return {
        success: true,
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'LOGOUT_FAILED',
          message: 'Failed to logout',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  async registerUser(request: CreateUserRequest): Promise<ServiceResponse<AuthResult>> {
    try {
      // Check if user already exists
      const existingUser = await this.dependencies.userRepository.findByEmail(request.email);
      if (existingUser) {
        return {
          success: false,
          error: {
            code: 'USER_ALREADY_EXISTS',
            message: 'User with this email already exists',
          },
        };
      }

      // Hash password
      const passwordHash = await this.dependencies.passwordService.hashPassword(request.password);

      // Create user
      const newUserData = await this.dependencies.userRepository.create({
        email: request.email,
        fullName: request.fullName || '',
        passwordHash,
        subscriptionTier: 'free',
        apiQuotaUsed: 0,
        apiQuotaLimit: 100,
      });

      // Convert to User type (remove passwordHash)
      const { passwordHash: _, ...newUser } = newUserData;

      // Generate tokens
      const tokens = await this.dependencies.tokenService.generateTokens(newUser);

      return {
        success: true,
        data: {
          user: newUser,
          tokens,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'REGISTRATION_FAILED',
          message: 'Failed to register user',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  async loginUser(request: LoginRequest): Promise<ServiceResponse<AuthResult>> {
    try {
      // Find user by email
      const userData = await this.dependencies.userRepository.findByEmail(request.email);
      if (!userData) {
        return {
          success: false,
          error: {
            code: 'INVALID_CREDENTIALS',
            message: 'Invalid email or password',
          },
        };
      }

      // Verify password
      const isPasswordValid = await this.dependencies.passwordService.verifyPassword(
        request.password,
        userData.passwordHash
      );
      if (!isPasswordValid) {
        return {
          success: false,
          error: {
            code: 'INVALID_CREDENTIALS',
            message: 'Invalid email or password',
          },
        };
      }

      // Convert to User type (remove passwordHash)
      const { passwordHash: _, ...user } = userData;

      // Generate tokens
      const tokens = await this.dependencies.tokenService.generateTokens(user);

      return {
        success: true,
        data: {
          user,
          tokens,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'LOGIN_FAILED',
          message: 'Failed to login',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  async verifyToken(token: string): Promise<ServiceResponse<User>> {
    try {
      const payload = await this.dependencies.tokenService.verifyToken(token);
      const userData = await this.dependencies.userRepository.findById(payload.userId);
      
      if (!userData) {
        return {
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: 'User not found',
          },
        };
      }

      // Convert to User type (remove passwordHash)
      const { passwordHash: _, ...user } = userData;

      return {
        success: true,
        data: user,
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'INVALID_TOKEN',
          message: 'Invalid token',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  async refreshToken(refreshToken: string): Promise<ServiceResponse<AuthTokens>> {
    try {
      const payload = await this.dependencies.tokenService.verifyRefreshToken(refreshToken);
      const userData = await this.dependencies.userRepository.findById(payload.userId);
      
      if (!userData) {
        return {
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: 'User not found',
          },
        };
      }

      // Convert to User type (remove passwordHash)
      const { passwordHash: _, ...user } = userData;

      const tokens = await this.dependencies.tokenService.generateTokens(user);

      return {
        success: true,
        data: tokens,
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'INVALID_REFRESH_TOKEN',
          message: 'Invalid refresh token',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }
}