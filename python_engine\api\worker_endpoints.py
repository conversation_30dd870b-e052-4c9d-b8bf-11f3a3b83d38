"""
Worker Management API Endpoints
Provides HTTP endpoints for monitoring and managing background workers
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
from typing import Dict, List, Optional, Any
from datetime import datetime
import asyncio
import psutil
import logging

from app.workers import WorkerManager
from app.models.schemas import (
    WorkerManagementRequest,
    WorkerManagementResponse,
    WorkerStats,
    WorkerHealthCheck,
    SystemResourceUsage
)

logger = logging.getLogger(__name__)

# Global worker manager instance
worker_manager: Optional[WorkerManager] = None

# Create router
router = APIRouter(prefix="/api/workers", tags=["workers"])

def get_worker_manager() -> WorkerManager:
    """Get the global worker manager instance"""
    global worker_manager
    if worker_manager is None:
        worker_manager = WorkerManager()
    return worker_manager

@router.post("/status")
async def get_worker_status(request: WorkerManagementRequest) -> WorkerManagementResponse:
    """Get current status of all workers"""
    try:
        manager = get_worker_manager()
        status = manager.get_status()
        
        return WorkerManagementResponse(
            success=True,
            data=status,
            timestamp=datetime.utcnow(),
            request_id=request.request_id
        )
    except Exception as e:
        logger.error(f"Failed to get worker status: {e}")
        return WorkerManagementResponse(
            success=False,
            error={
                "code": "WORKER_STATUS_ERROR",
                "message": "Failed to retrieve worker status",
                "details": str(e)
            },
            timestamp=datetime.utcnow(),
            request_id=request.request_id
        )

@router.post("/health")
async def check_worker_health(request: WorkerManagementRequest) -> WorkerManagementResponse:
    """Perform health check on all workers"""
    try:
        manager = get_worker_manager()
        health = await manager.health_check()
        
        return WorkerManagementResponse(
            success=True,
            data=health,
            timestamp=datetime.utcnow(),
            request_id=request.request_id
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return WorkerManagementResponse(
            success=False,
            error={
                "code": "HEALTH_CHECK_ERROR",
                "message": "Worker health check failed",
                "details": str(e)
            },
            timestamp=datetime.utcnow(),
            request_id=request.request_id
        )

@router.post("/restart")
async def restart_worker(request: WorkerManagementRequest) -> WorkerManagementResponse:
    """Restart a specific worker"""
    if not request.worker_name:
        return WorkerManagementResponse(
            success=False,
            error={
                "code": "MISSING_WORKER_NAME",
                "message": "Worker name is required for restart operation"
            },
            timestamp=datetime.utcnow(),
            request_id=request.request_id
        )
    
    try:
        manager = get_worker_manager()
        success = await manager.restart_worker(request.worker_name)
        
        if success:
            return WorkerManagementResponse(
                success=True,
                data={
                    "worker_name": request.worker_name,
                    "restarted": True,
                    "timestamp": datetime.utcnow().isoformat()
                },
                timestamp=datetime.utcnow(),
                request_id=request.request_id
            )
        else:
            return WorkerManagementResponse(
                success=False,
                error={
                    "code": "RESTART_FAILED",
                    "message": f"Failed to restart worker: {request.worker_name}"
                },
                timestamp=datetime.utcnow(),
                request_id=request.request_id
            )
    except Exception as e:
        logger.error(f"Failed to restart worker {request.worker_name}: {e}")
        return WorkerManagementResponse(
            success=False,
            error={
                "code": "RESTART_ERROR",
                "message": f"Error restarting worker: {request.worker_name}",
                "details": str(e)
            },
            timestamp=datetime.utcnow(),
            request_id=request.request_id
        )

@router.post("/stats")
async def get_worker_stats(request: WorkerManagementRequest) -> WorkerManagementResponse:
    """Get detailed worker statistics"""
    try:
        manager = get_worker_manager()
        status = manager.get_status()
        
        # Get system resource usage
        system_resources = get_system_resources()
        
        # Get worker performance metrics
        worker_performance = await get_worker_performance()
        
        stats = {
            "status": status,
            "system_resources": system_resources,
            "worker_performance": worker_performance,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        return WorkerManagementResponse(
            success=True,
            data=stats,
            timestamp=datetime.utcnow(),
            request_id=request.request_id
        )
    except Exception as e:
        logger.error(f"Failed to get worker stats: {e}")
        return WorkerManagementResponse(
            success=False,
            error={
                "code": "WORKER_STATS_ERROR",
                "message": "Failed to retrieve worker statistics",
                "details": str(e)
            },
            timestamp=datetime.utcnow(),
            request_id=request.request_id
        )

@router.get("/uploads/sessions")
async def get_upload_sessions(status: Optional[str] = None) -> Dict[str, Any]:
    """Get file upload sessions"""
    try:
        from app.database import SessionLocal
        from app.models.database import UploadSessions
        
        db = SessionLocal()
        
        try:
            query = db.query(UploadSessions)
            if status:
                query = query.filter(UploadSessions.status == status)
            
            sessions = query.order_by(UploadSessions.created_at.desc()).limit(100).all()
            
            session_data = []
            for session in sessions:
                session_data.append({
                    "id": str(session.id),
                    "user_id": str(session.user_id),
                    "original_filename": session.original_filename,
                    "file_size": session.file_size,
                    "status": session.status,
                    "rows_processed": session.rows_processed or 0,
                    "error_message": session.error_message,
                    "created_at": session.created_at.isoformat(),
                    "updated_at": session.updated_at.isoformat(),
                })
            
            return {
                "success": True,
                "data": session_data
            }
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Failed to get upload sessions: {e}")
        return {
            "success": False,
            "error": {
                "code": "UPLOAD_SESSIONS_ERROR",
                "message": "Failed to retrieve upload sessions",
                "details": str(e)
            }
        }

@router.get("/backtests/running")
async def get_running_backtests() -> Dict[str, Any]:
    """Get currently running backtests"""
    try:
        from app.database import SessionLocal
        from app.models.database import Backtests
        
        db = SessionLocal()
        
        try:
            backtests = db.query(Backtests).filter(
                Backtests.status.in_(['pending', 'running'])
            ).order_by(Backtests.created_at.desc()).all()
            
            backtest_data = []
            for backtest in backtests:
                backtest_data.append({
                    "id": str(backtest.id),
                    "user_id": str(backtest.user_id),
                    "name": backtest.name,
                    "status": backtest.status,
                    "symbol": backtest.symbol,
                    "start_date": backtest.start_date.isoformat() if backtest.start_date else None,
                    "end_date": backtest.end_date.isoformat() if backtest.end_date else None,
                    "progress": getattr(backtest, 'progress', 0),
                    "started_at": backtest.started_at.isoformat() if backtest.started_at else None,
                    "created_at": backtest.created_at.isoformat(),
                    "error_message": backtest.error_message,
                })
            
            return {
                "success": True,
                "data": backtest_data
            }
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Failed to get running backtests: {e}")
        return {
            "success": False,
            "error": {
                "code": "RUNNING_BACKTESTS_ERROR",
                "message": "Failed to retrieve running backtests",
                "details": str(e)
            }
        }

@router.get("/dgm/experiments")
async def get_dgm_experiments(status: Optional[str] = None) -> Dict[str, Any]:
    """Get DGM experiments"""
    try:
        from app.database import SessionLocal
        from app.models.database import DgmExperiments
        
        db = SessionLocal()
        
        try:
            query = db.query(DgmExperiments)
            if status:
                query = query.filter(DgmExperiments.status == status)
            
            experiments = query.order_by(DgmExperiments.created_at.desc()).limit(100).all()
            
            experiment_data = []
            for experiment in experiments:
                experiment_data.append({
                    "id": str(experiment.id),
                    "user_id": str(experiment.user_id),
                    "experiment_name": experiment.experiment_name,
                    "status": experiment.status,
                    "base_strategy": experiment.base_strategy or {},
                    "generated_strategy": experiment.generated_strategy or {},
                    "fitness_improvement": experiment.fitness_improvement,
                    "deployed_backtest_id": str(experiment.deployed_backtest_id) if experiment.deployed_backtest_id else None,
                    "started_at": experiment.started_at.isoformat() if experiment.started_at else None,
                    "completed_at": experiment.completed_at.isoformat() if experiment.completed_at else None,
                    "error_message": experiment.error_message,
                    "created_at": experiment.created_at.isoformat(),
                })
            
            return {
                "success": True,
                "data": experiment_data
            }
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Failed to get DGM experiments: {e}")
        return {
            "success": False,
            "error": {
                "code": "DGM_EXPERIMENTS_ERROR",
                "message": "Failed to retrieve DGM experiments",
                "details": str(e)
            }
        }

def get_system_resources() -> SystemResourceUsage:
    """Get current system resource usage"""
    try:
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # Memory usage
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_used_mb = memory.used / (1024 * 1024)
        
        # Disk usage
        disk = psutil.disk_usage('/')
        disk_usage_percent = (disk.used / disk.total) * 100
        
        # Network connections
        connections = len(psutil.net_connections())
        
        return {
            "cpu_percent": round(cpu_percent, 1),
            "memory_percent": round(memory_percent, 1),
            "memory_used_mb": round(memory_used_mb, 1),
            "disk_usage_percent": round(disk_usage_percent, 1),
            "active_connections": connections,
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        logger.error(f"Failed to get system resources: {e}")
        return {
            "cpu_percent": 0.0,
            "memory_percent": 0.0,
            "memory_used_mb": 0.0,
            "disk_usage_percent": 0.0,
            "active_connections": 0,
            "timestamp": datetime.utcnow().isoformat()
        }

async def get_worker_performance() -> Dict[str, Any]:
    """Get worker performance metrics"""
    try:
        from app.database import SessionLocal
        from app.models.database import UploadSessions, Backtests
        from sqlalchemy import func
        from datetime import timedelta
        
        db = SessionLocal()
        
        try:
            today = datetime.utcnow().date()
            
            # File parser performance
            files_today = db.query(func.count(UploadSessions.id)).filter(
                func.date(UploadSessions.created_at) == today
            ).scalar() or 0
            
            files_ready = db.query(func.count(UploadSessions.id)).filter(
                func.date(UploadSessions.created_at) == today,
                UploadSessions.status == 'ready'
            ).scalar() or 0
            
            file_success_rate = files_ready / files_today if files_today > 0 else 0
            
            # Backtest runner performance
            backtests_today = db.query(func.count(Backtests.id)).filter(
                func.date(Backtests.created_at) == today
            ).scalar() or 0
            
            backtests_completed = db.query(func.count(Backtests.id)).filter(
                func.date(Backtests.created_at) == today,
                Backtests.status == 'completed'
            ).scalar() or 0
            
            backtest_success_rate = backtests_completed / backtests_today if backtests_today > 0 else 0
            
            # Average processing times (simplified)
            avg_file_processing_time = 120.0  # Would be calculated from actual data
            avg_backtest_time = 1800.0  # Would be calculated from actual data
            
            return {
                "FileParserWorker": {
                    "files_processed_today": files_today,
                    "files_ready_today": files_ready,
                    "success_rate": round(file_success_rate, 3),
                    "avg_processing_time": avg_file_processing_time
                },
                "BacktestRunner": {
                    "backtests_started_today": backtests_today,
                    "backtests_completed_today": backtests_completed,
                    "success_rate": round(backtest_success_rate, 3),
                    "avg_execution_time": avg_backtest_time
                },
                "DGMMonitor": {
                    "experiments_today": 0,  # Would be calculated from actual data
                    "success_rate": 0.0,
                    "avg_experiment_time": 3600.0
                }
            }
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Failed to get worker performance: {e}")
        return {
            "FileParserWorker": {"error": str(e)},
            "BacktestRunner": {"error": str(e)},
            "DGMMonitor": {"error": str(e)}
        }

@router.get("/ping")
async def ping() -> Dict[str, Any]:
    """Simple health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "worker_api"
    }