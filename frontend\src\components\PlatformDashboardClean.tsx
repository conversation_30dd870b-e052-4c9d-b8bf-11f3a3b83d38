import React, { useState } from 'react';
import { 
  <PERSON><PERSON>, 
  BarChart3, 
  DollarSign, 
  Target, 
  Activity, 
  Zap,
  Link,
  User,
  MessageCircle,
  Send,
  TestTube,
  Save,
  Play,
  Pause,
  Settings,
  FileText,
  Brain,
  Copy,
  Lightbulb
} from 'lucide-react';
import { motion } from 'framer-motion';

interface Strategy {
  id: string;
  name: string;
  status: 'active' | 'paused' | 'stopped';
  pnl: number;
  trades: number;
  winRate: number;
  type: string;
}

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  code?: string;
  timestamp: Date;
}

interface AIPrompt {
  id: string;
  title: string;
  description: string;
  category: string;
  prompt_template: string;
  variables: string[];
  example_usage: string;
  detailed_info: string;
}

const MetricCard: React.FC<{
  title: string;
  value: string;
  change: string;
  icon: React.ReactNode;
  color: string;
}> = ({ title, value, change, icon, color }) => (
  <motion.div
    whileHover={{ scale: 1.02 }}
    className="bg-white rounded-xl shadow-sm border border-gray-200 p-6"
  >
    <div className="flex items-center justify-between">
      <div>
        <p className="text-sm font-medium text-gray-600">{title}</p>
        <p className="text-2xl font-bold text-gray-900">{value}</p>
        <p className="text-sm text-green-600">{change}</p>
      </div>
      <div className={`p-3 rounded-lg ${color}`}>
        {icon}
      </div>
    </div>
  </motion.div>
);

const PlatformDashboardClean: React.FC = () => {
  const [activeHelperTab, setActiveHelperTab] = useState('ai-prompts');
  const [selectedPrompt, setSelectedPrompt] = useState<AIPrompt | null>(null);
  const [promptVariables, setPromptVariables] = useState<Record<string, string>>({});
  const [generatedPrompt, setGeneratedPrompt] = useState<string>('');
  const [chatMessages, setChatMessages] = useState<Message[]>([
    {
      id: '1',
      role: 'assistant',
      content: "Hello! I'm your AI trading strategy assistant. Describe the strategy you'd like to create and I'll generate complete Python code for you.\n\nFor example: 'Create a mean reversion strategy for EUR/USD using RSI with 2% risk per trade'",
      timestamp: new Date()
    }
  ]);
  const [chatInput, setChatInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // AI Prompts from promptadvance.club
  const aiPrompts: AIPrompt[] = [
    {
      id: "market_scanner",
      title: "Market Analysis & Asset Scanner",
      description: "Identify trading assets that meet specific criteria",
      category: "Market Analysis",
      prompt_template: "Act as a day trading assistant. Your task is to identify trading assets that meet the specified {criteria}. Utilize your expertise and available market analysis tools to scan, filter, and evaluate potential assets for trading opportunities.\n\nPlease provide:\n1. A list of 5-10 assets that match the criteria\n2. Brief analysis of why each asset meets the requirements\n3. Current market conditions affecting these assets\n4. Risk factors to consider\n5. Recommended timeframes for analysis\n\nCriteria: {criteria}",
      variables: ["criteria"],
      example_usage: "criteria: 'tech stocks with strong momentum and AI exposure'",
      detailed_info: "This prompt helps you systematically scan markets for opportunities. It provides structured analysis including risk assessment and timing recommendations."
    },
    {
      id: "technical_analyzer",
      title: "Comprehensive Technical Analysis",
      description: "Detailed technical analysis with entry/exit points",
      category: "Technical Analysis",
      prompt_template: "Act as an experienced day trader. Your objective is to analyze the price and volume patterns of {trading_asset} to identify potential buying or selling opportunities.\n\nProvide a comprehensive analysis including:\n1. Current trend analysis (short, medium, long-term)\n2. Key support and resistance levels\n3. Technical indicators analysis (RSI, MACD, Moving Averages)\n4. Volume analysis and patterns\n5. Entry points with specific price levels\n6. Stop-loss recommendations\n7. Take-profit targets\n8. Risk-reward ratio assessment\n9. Market sentiment indicators\n10. Timeframe recommendations\n\nAsset: {trading_asset}",
      variables: ["trading_asset"],
      example_usage: "trading_asset: 'NVDA (NVIDIA Corporation)'",
      detailed_info: "Provides comprehensive technical analysis with specific entry/exit points, risk management, and multi-timeframe perspective."
    },
    {
      id: "trade_execution",
      title: "Optimal Trade Execution Strategy",
      description: "Determine optimal entry, stop-loss, and target points",
      category: "Trade Execution",
      prompt_template: "Act as an experienced day trader. Based on your comprehensive analysis of current market conditions for {asset}, provide an optimal trade execution strategy.\n\nInclude:\n1. Precise entry price and entry conditions\n2. Stop-loss placement with reasoning\n3. Multiple take-profit targets\n4. Position sizing recommendations\n5. Risk management rules\n6. Market timing considerations\n7. Alternative scenarios (if trade goes against you)\n8. Exit strategy for different market conditions\n\nAsset: {asset}\nAccount Size: {account_size}\nRisk Tolerance: {risk_tolerance}%",
      variables: ["asset", "account_size", "risk_tolerance"],
      example_usage: "asset: 'TSLA', account_size: '$50000', risk_tolerance: '2'",
      detailed_info: "Creates detailed execution plans with precise entry/exit criteria, position sizing, and contingency planning."
    },
    {
      id: "risk_management",
      title: "Risk Management & Position Sizing",
      description: "Calculate optimal position sizes and risk parameters",
      category: "Risk Management",
      prompt_template: "Act as a professional risk management specialist. Help me calculate optimal position sizing and risk parameters for {trading_strategy}.\n\nProvide detailed analysis including:\n1. Position sizing calculation based on account size\n2. Risk per trade recommendations\n3. Maximum drawdown limits\n4. Correlation risk assessment\n5. Portfolio heat analysis\n6. Stop-loss placement strategies\n7. Risk-reward ratio optimization\n8. Diversification recommendations\n\nStrategy: {trading_strategy}\nAccount Size: {account_size}\nRisk Tolerance: {risk_tolerance}%",
      variables: ["trading_strategy", "account_size", "risk_tolerance"],
      example_usage: "trading_strategy: 'swing trading', account_size: '$100000', risk_tolerance: '1.5'",
      detailed_info: "Provides comprehensive risk management framework with position sizing calculations and portfolio protection strategies."
    },
    {
      id: "market_sentiment",
      title: "Market Sentiment Analysis",
      description: "Analyze market sentiment and crowd psychology",
      category: "Market Analysis",
      prompt_template: "Act as a market sentiment analyst. Analyze the current market sentiment for {market_or_asset} and provide insights on crowd psychology.\n\nInclude analysis of:\n1. Current sentiment indicators (VIX, Put/Call ratios, etc.)\n2. Social media sentiment trends\n3. Institutional vs retail positioning\n4. Fear and greed index interpretation\n5. Contrarian opportunities\n6. Sentiment-based entry/exit signals\n7. Market cycle positioning\n8. Behavioral finance insights\n\nMarket/Asset: {market_or_asset}",
      variables: ["market_or_asset"],
      example_usage: "market_or_asset: 'S&P 500 index'",
      detailed_info: "Delivers comprehensive sentiment analysis with contrarian trading opportunities and behavioral insights."
    },
    {
      id: "backtesting_framework",
      title: "Strategy Backtesting Framework",
      description: "Design comprehensive backtesting methodology",
      category: "Strategy Development",
      prompt_template: "Act as a quantitative analyst. Help me design a robust backtesting framework for {strategy_type}.\n\nCreate a comprehensive framework including:\n1. Data requirements and quality checks\n2. Backtesting methodology and best practices\n3. Performance metrics to track\n4. Statistical significance testing\n5. Out-of-sample validation approach\n6. Walk-forward analysis setup\n7. Overfitting prevention techniques\n8. Realistic transaction cost modeling\n9. Slippage and execution assumptions\n10. Results interpretation guidelines\n\nStrategy Type: {strategy_type}\nTimeframe: {timeframe}",
      variables: ["strategy_type", "timeframe"],
      example_usage: "strategy_type: 'momentum breakout', timeframe: '4-hour'",
      detailed_info: "Provides professional-grade backtesting methodology with statistical validation and realistic performance assessment."
    }
  ];

  const strategies: Strategy[] = [
    { id: '1', name: 'RSI Mean Reversion', status: 'active', pnl: 1247, trades: 23, winRate: 65, type: 'Mean Reversion' },
    { id: '2', name: 'MACD Momentum', status: 'active', pnl: 892, trades: 18, winRate: 72, type: 'Momentum' },
    { id: '3', name: 'Bollinger Bands', status: 'paused', pnl: -156, trades: 12, winRate: 58, type: 'Volatility' },
  ];

  const handleChatSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!chatInput.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: chatInput,
      timestamp: new Date()
    };

    setChatMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    // Simulate AI response
    setTimeout(() => {
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: `I'll help you create a trading strategy based on: "${chatInput}"\n\nHere's a Python implementation:`,
        code: `# Trading Strategy Implementation
import pandas as pd
import numpy as np
from typing import Dict, List

class TradingStrategy:
    def __init__(self, symbol: str, risk_per_trade: float = 0.02):
        self.symbol = symbol
        self.risk_per_trade = risk_per_trade
        self.positions = []
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        # Strategy logic based on your requirements
        data['signal'] = 0
        # Add your specific indicators and conditions here
        return data
    
    def execute_trade(self, signal: int, price: float):
        # Execute trade based on signal
        if signal == 1:  # Buy signal
            print(f"BUY {self.symbol} at {price}")
        elif signal == -1:  # Sell signal
            print(f"SELL {self.symbol} at {price}")

# Usage example
strategy = TradingStrategy("EURUSD", risk_per_trade=0.02)`,
        timestamp: new Date()
      };
      setChatMessages(prev => [...prev, assistantMessage]);
      setIsLoading(false);
    }, 1500);

    setChatInput('');
  };

  const generateAIPrompt = () => {
    if (!selectedPrompt) return;

    let prompt = selectedPrompt.prompt_template;
    
    // Replace variables in the prompt
    selectedPrompt.variables.forEach((variable: string) => {
      const value = promptVariables[variable] || `[${variable}]`;
      prompt = prompt.replace(new RegExp(`{${variable}}`, 'g'), value);
    });

    setGeneratedPrompt(prompt);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      'Market Analysis': 'bg-blue-100 text-blue-800',
      'Technical Analysis': 'bg-green-100 text-green-800',
      'Trade Execution': 'bg-purple-100 text-purple-800',
      'Risk Management': 'bg-red-100 text-red-800',
      'Strategy Development': 'bg-indigo-100 text-indigo-800',
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation Header */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Bot className="w-6 h-6 text-blue-600" />
              </div>
              <span className="text-xl font-bold text-gray-900">Trading Dashboard</span>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>MT5 Connected</span>
              </div>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 space-y-8">
        {/* Hero Section */}
        <section className="bg-gradient-to-r from-blue-600 to-indigo-700 rounded-xl p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold mb-2">Welcome to Your Trading Dashboard</h1>
              <div className="flex items-center space-x-6 text-sm">
                <div className="flex items-center space-x-2">
                  <BarChart3 className="w-4 h-4" />
                  <span>Real-time Analytics</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Bot className="w-4 h-4" />
                  <span>AI Strategy Generation</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Link className="w-4 h-4" />
                  <span>MT5 Integration</span>
                </div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold">12</div>
              <div className="text-blue-100 text-sm">Active Strategies</div>
            </div>
          </div>
        </section>

        {/* Enhanced Strategy Helper - Side by Side Layout */}
        <section>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">🧠 AI Strategy Helper</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            
            {/* Left Side - Professional Prompts */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200">
              <div className="p-6">
                <div className="flex items-center space-x-2 mb-4">
                  <Brain className="w-5 h-5 text-blue-600" />
                  <h3 className="text-lg font-semibold text-gray-900">Professional Trading Prompts</h3>
                </div>
                <p className="text-gray-600 text-sm mb-6">Proven ChatGPT prompts from trading experts - click any prompt to customize and copy</p>
                
                <div className="space-y-4 max-h-96 overflow-y-auto">
                  {aiPrompts.map((prompt) => (
                    <motion.div
                      key={prompt.id}
                      whileHover={{ scale: 1.01 }}
                      className="bg-gray-50 rounded-lg p-4 cursor-pointer hover:bg-gray-100 transition-colors border border-gray-200"
                      onClick={() => setSelectedPrompt(prompt)}
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex-1">
                          <h4 className="font-semibold text-gray-900 text-sm mb-1">{prompt.title}</h4>
                          <span className={`inline-block px-2 py-1 text-xs rounded-full ${getCategoryColor(prompt.category)}`}>
                            {prompt.category}
                          </span>
                        </div>
                        <Brain className="w-4 h-4 text-blue-600 flex-shrink-0 ml-2" />
                      </div>
                      <p className="text-gray-600 text-xs mb-2">{prompt.description}</p>
                      <div className="text-xs text-gray-500 mb-2">
                        <strong>Provides:</strong> {prompt.detailed_info}
                      </div>
                      <div className="text-xs text-blue-600 mb-2">
                        <strong>Example:</strong> {prompt.example_usage}
                      </div>
                      <div className="text-xs text-gray-700 bg-gray-100 p-2 rounded mb-2">
                        <strong>Preview:</strong> {prompt.prompt_template.substring(0, 100)}...
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-500">
                          {prompt.variables.length} variable{prompt.variables.length !== 1 ? 's' : ''}
                        </span>
                        <span className="text-xs text-blue-600 font-medium">Click to customize →</span>
                      </div>
                    </motion.div>
                  ))}
                </div>

                {/* Selected Prompt Details */}
                {selectedPrompt && (
                  <div className="mt-6 bg-blue-50 rounded-lg p-4 border border-blue-200">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-semibold text-gray-900">{selectedPrompt.title}</h4>
                      <button
                        onClick={() => setSelectedPrompt(null)}
                        className="text-gray-500 hover:text-gray-700"
                      >
                        ✕
                      </button>
                    </div>
                    
                    <div className="space-y-3">
                      <div>
                        <h5 className="font-medium text-gray-900 mb-2 text-sm">Full Prompt Template:</h5>
                        <div className="bg-white p-3 rounded-md border border-gray-300 max-h-24 overflow-y-auto">
                          <pre className="whitespace-pre-wrap text-xs text-gray-800">{selectedPrompt.prompt_template}</pre>
                        </div>
                      </div>

                      <div>
                        <h5 className="font-medium text-gray-900 mb-2 text-sm">Customize Variables:</h5>
                        <div className="space-y-2">
                          {selectedPrompt.variables.map((variable: string) => (
                            <div key={variable}>
                              <label className="block text-xs font-medium text-gray-700 mb-1">
                                {variable.replace('_', ' ').toUpperCase()}
                              </label>
                              <input
                                type="text"
                                value={promptVariables[variable] || ''}
                                onChange={(e) => setPromptVariables(prev => ({
                                  ...prev,
                                  [variable]: e.target.value
                                }))}
                                placeholder={`Enter ${variable.replace('_', ' ')}`}
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="flex space-x-2">
                        <button
                          onClick={generateAIPrompt}
                          className="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-1 text-sm"
                        >
                          <Lightbulb className="w-3 h-3" />
                          <span>Generate</span>
                        </button>
                        {generatedPrompt && (
                          <button
                            onClick={() => copyToClipboard(generatedPrompt)}
                            className="px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center space-x-1 text-sm"
                          >
                            <Copy className="w-3 h-3" />
                            <span>Copy</span>
                          </button>
                        )}
                      </div>

                      {generatedPrompt && (
                        <div>
                          <h5 className="font-medium text-gray-900 mb-2 text-sm">Generated Prompt:</h5>
                          <div className="bg-white p-3 rounded-md border border-gray-300 max-h-32 overflow-y-auto">
                            <pre className="whitespace-pre-wrap text-xs text-gray-800">{generatedPrompt}</pre>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Right Side - AI Chat */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200">
              <div className="p-6">
                <div className="flex items-center space-x-2 mb-4">
                  <MessageCircle className="w-5 h-5 text-blue-600" />
                  <h3 className="text-lg font-semibold text-gray-900">AI Strategy Generator</h3>
                </div>
                <p className="text-gray-600 text-sm mb-6">Describe your strategy idea and get complete Python code</p>
                
                {/* Chat Messages */}
                <div className="h-80 overflow-y-auto mb-4 p-4 bg-gray-50 rounded-lg">
                  <div className="space-y-4">
                    {chatMessages.map((message) => (
                      <div key={message.id} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                        <div className={`max-w-full ${message.role === 'user' ? 'bg-blue-600 text-white' : 'bg-white border border-gray-200'} rounded-lg p-3`}>
                          <div className="flex items-start space-x-2">
                            <div className={`p-1 rounded-full ${message.role === 'user' ? 'bg-blue-500' : 'bg-gray-100'}`}>
                              {message.role === 'user' ? 
                                <User className="w-3 h-3 text-white" /> : 
                                <Bot className="w-3 h-3 text-gray-600" />
                              }
                            </div>
                            <div className="flex-1">
                              <div className="whitespace-pre-wrap text-sm">{message.content}</div>
                              {message.code && (
                                <div className="mt-2">
                                  <div className="bg-gray-900 text-green-400 p-3 rounded-lg text-xs font-mono overflow-x-auto">
                                    <pre>{message.code}</pre>
                                  </div>
                                  <div className="flex space-x-2 mt-2">
                                    <button className="flex items-center space-x-1 px-2 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700">
                                      <Save className="w-3 h-3" />
                                      <span>Save</span>
                                    </button>
                                    <button className="flex items-center space-x-1 px-2 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700">
                                      <TestTube className="w-3 h-3" />
                                      <span>Test</span>
                                    </button>
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                    {isLoading && (
                      <div className="flex justify-start">
                        <div className="bg-white border border-gray-200 rounded-lg p-3">
                          <div className="flex items-center space-x-2">
                            <div className="p-1 rounded-full bg-gray-100">
                              <Bot className="w-3 h-3 text-gray-600" />
                            </div>
                            <div className="flex space-x-1">
                              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Chat Input */}
                <form onSubmit={handleChatSubmit} className="flex space-x-3">
                  <input
                    type="text"
                    value={chatInput}
                    onChange={(e) => setChatInput(e.target.value)}
                    placeholder="Describe your trading strategy idea..."
                    className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    disabled={isLoading}
                  />
                  <button
                    type="submit"
                    disabled={isLoading || !chatInput.trim()}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                  >
                    <Send className="w-4 h-4" />
                    <span>Send</span>
                  </button>
                </form>
              </div>
            </div>
          </div>
        </section>

        {/* Key Metrics */}
        <section>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">📊 Key Metrics</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <MetricCard
              title="Total P&L"
              value="$2,847"
              change="+$127 today"
              icon={<DollarSign className="w-6 h-6 text-white" />}
              color="bg-green-500"
            />
            <MetricCard
              title="Win Rate"
              value="68%"
              change="+2% this week"
              icon={<Target className="w-6 h-6 text-white" />}
              color="bg-blue-500"
            />
            <MetricCard
              title="Active Strategies"
              value="12"
              change="+3 this month"
              icon={<Activity className="w-6 h-6 text-white" />}
              color="bg-purple-500"
            />
            <MetricCard
              title="MT5 Status"
              value="Connected"
              change="Stable connection"
              icon={<Zap className="w-6 h-6 text-white" />}
              color="bg-green-500"
            />
          </div>
        </section>

        {/* Active Strategies */}
        <section>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">📈 Active Strategies</h2>
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Strategy</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">P&L</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trades</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Win Rate</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {strategies.map((strategy) => (
                    <tr key={strategy.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{strategy.name}</div>
                          <div className="text-sm text-gray-500">{strategy.type}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          strategy.status === 'active' ? 'bg-green-100 text-green-800' :
                          strategy.status === 'paused' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {strategy.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`text-sm font-medium ${strategy.pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          ${strategy.pnl >= 0 ? '+' : ''}{strategy.pnl}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {strategy.trades}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {strategy.winRate}%
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        <button className="text-blue-600 hover:text-blue-900">
                          {strategy.status === 'active' ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                        </button>
                        <button className="text-gray-600 hover:text-gray-900">
                          <Settings className="w-4 h-4" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </section>

        {/* Coming Soon */}
        <section className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 text-center">
            <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Advanced Analytics</h3>
            <p className="text-gray-600">Detailed performance analytics and risk metrics coming soon...</p>
          </div>
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 text-center">
            <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Strategy Templates</h3>
            <p className="text-gray-600">Pre-built strategy templates and community sharing coming soon...</p>
          </div>
        </section>
      </div>
    </div>
  );
};

export default PlatformDashboardClean;