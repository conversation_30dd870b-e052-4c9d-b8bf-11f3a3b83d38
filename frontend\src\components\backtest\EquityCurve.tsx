/**
 * Equity Curve Component
 * Advanced financial chart with performance metrics
 */

// import React from 'react'; // Not needed with new JSX transform
import { 
  XA<PERSON>s, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer, 
  ReferenceLine,
  Area,
  AreaChart 
} from 'recharts';

interface EquityPoint {
  timestamp: string;
  equity: number;
  drawdownPct?: number;
  returns?: number;
}

interface EquityCurveProps {
  data: EquityPoint[];
  initialBalance: number;
  showDrawdown?: boolean;
  height?: number;
  className?: string;
  compact?: boolean;
}

export function EquityCurve({ 
  data, 
  initialBalance, 
  showDrawdown = false, 
  height = 320,
  className = '',
  compact = false
}: EquityCurveProps) {
  // Use compact mode for simplified display
  const displayHeight = compact ? Math.min(height, 200) : height;
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatPercent = (value: number) => {
    return `${value.toFixed(2)}%`;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Calculate performance metrics
  const currentValue = data.length > 0 ? data[data.length - 1].equity : initialBalance;
  const totalReturn = ((currentValue - initialBalance) / initialBalance) * 100;
  const maxDrawdown = Math.min(...data.map(point => point.drawdownPct || 0));

  // Calculate volatility (standard deviation of returns)
  const returns = data.map(point => point.returns || 0).filter(r => r !== 0);
  const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
  const variance = returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length;
  const volatility = Math.sqrt(variance) * Math.sqrt(252); // Annualized

  // Calculate Sharpe ratio (assuming 2% risk-free rate)
  const riskFreeRate = 0.02;
  const excessReturn = (totalReturn / 100) - riskFreeRate;
  const sharpeRatio = volatility > 0 ? excessReturn / (volatility / 100) : 0;

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-300 rounded-lg shadow-lg">
          <p className="text-sm font-medium text-gray-900">
            {formatDate(label)}
          </p>
          <p className="text-sm text-blue-600">
            Equity: {formatCurrency(payload[0].value)}
          </p>
          {showDrawdown && payload[1] && (
            <p className="text-sm text-red-600">
              Drawdown: {formatPercent(payload[1].value)}
            </p>
          )}
          {payload[0].payload.returns && (
            <p className="text-sm text-gray-600">
              Return: {formatPercent(payload[0].payload.returns)}
            </p>
          )}
        </div>
      );
    }
    return null;
  };

  return (
    <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
      <div className="flex justify-between items-start mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Portfolio Performance</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <p className="text-gray-600">Total Return</p>
              <p className={`font-semibold ${totalReturn >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {totalReturn >= 0 ? '+' : ''}{totalReturn.toFixed(2)}%
              </p>
            </div>
            <div>
              <p className="text-gray-600">Current Value</p>
              <p className="font-semibold text-gray-900">
                {formatCurrency(currentValue)}
              </p>
            </div>
            <div>
              <p className="text-gray-600">Max Drawdown</p>
              <p className="font-semibold text-red-600">
                {maxDrawdown.toFixed(2)}%
              </p>
            </div>
            <div>
              <p className="text-gray-600">Sharpe Ratio</p>
              <p className="font-semibold text-gray-900">
                {sharpeRatio.toFixed(2)}
              </p>
            </div>
          </div>
        </div>
      </div>

      <div style={{ height: `${displayHeight}px` }}>
        <ResponsiveContainer width="100%" height="100%">
          {showDrawdown ? (
            <div className="space-y-4">
              <div style={{ height: `${displayHeight * 0.7}px` }}>
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={data}>
                    <defs>
                      <linearGradient id="equityGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis 
                      dataKey="timestamp" 
                      tickFormatter={formatDate}
                      stroke="#6b7280"
                      fontSize={12}
                    />
                    <YAxis 
                      tickFormatter={formatCurrency}
                      stroke="#6b7280"
                      fontSize={12}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <ReferenceLine y={initialBalance} stroke="#6b7280" strokeDasharray="2 2" />
                    <Area
                      type="monotone"
                      dataKey="equity"
                      stroke="#3b82f6"
                      strokeWidth={2}
                      fill="url(#equityGradient)"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
              
              <div style={{ height: `${displayHeight * 0.3}px` }}>
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={data}>
                    <defs>
                      <linearGradient id="drawdownGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#ef4444" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#ef4444" stopOpacity={0.1}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis 
                      dataKey="timestamp" 
                      tickFormatter={formatDate}
                      stroke="#6b7280"
                      fontSize={12}
                    />
                    <YAxis 
                      tickFormatter={formatPercent}
                      stroke="#6b7280"
                      fontSize={12}
                    />
                    <Tooltip 
                      formatter={(value: number) => [formatPercent(value), 'Drawdown']}
                      labelFormatter={formatDate}
                    />
                    <ReferenceLine y={0} stroke="#6b7280" strokeDasharray="2 2" />
                    <Area
                      type="monotone"
                      dataKey="drawdownPct"
                      stroke="#ef4444"
                      strokeWidth={2}
                      fill="url(#drawdownGradient)"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </div>
          ) : (
            <AreaChart data={data}>
              <defs>
                <linearGradient id="equityGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                  <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis 
                dataKey="timestamp" 
                tickFormatter={formatDate}
                stroke="#6b7280"
                fontSize={12}
              />
              <YAxis 
                tickFormatter={formatCurrency}
                stroke="#6b7280"
                fontSize={12}
              />
              <Tooltip content={<CustomTooltip />} />
              <ReferenceLine y={initialBalance} stroke="#6b7280" strokeDasharray="2 2" />
              <Area
                type="monotone"
                dataKey="equity"
                stroke="#3b82f6"
                strokeWidth={2}
                fill="url(#equityGradient)"
              />
            </AreaChart>
          )}
        </ResponsiveContainer>
      </div>
    </div>
  );
}