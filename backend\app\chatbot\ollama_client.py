"""
Ollama Client for AI Enhanced Trading Platform
Enhanced async client with streaming support and robust error handling
"""

import asyncio
import aiohttp
import logging
from typing import Dict, List, Optional, AsyncGenerator, Any
import json
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OllamaClient:
    """
    Async client for Ollama API with enhanced features:
    - Health monitoring
    - Model management
    - Streaming support
    - Conversation history
    - Error handling with retries
    """
    
    def __init__(self, base_url: str = "http://localhost:11435"):
        self.base_url = base_url.rstrip('/')
        self.session: Optional[aiohttp.ClientSession] = None
        self.models_cache: List[str] = []
        self.last_model_fetch: Optional[datetime] = None
        self.conversation_history: Dict[str, List[Dict[str, str]]] = {}
        
    async def __aenter__(self):
        """Async context manager entry"""
        await self._ensure_session()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
        
    async def _ensure_session(self):
        """Ensure aiohttp session is available"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=60)
            self.session = aiohttp.ClientSession(timeout=timeout)
            
    async def close(self):
        """Close the aiohttp session"""
        if self.session and not self.session.closed:
            await self.session.close()
            
    async def health_check(self) -> Dict[str, Any]:
        """
        Check Ollama server health and return detailed status
        
        Returns:
            Dict with health status, available models, and server info
        """
        try:
            await self._ensure_session()
            if not self.session:
                return {
                    'status': 'error',
                    'available': False,
                    'models': [],
                    'error': 'No session available',
                    'base_url': self.base_url,
                    'message': 'Failed to create HTTP session'
                }
            
            # Test basic connectivity
            async with self.session.get(f"{self.base_url}/api/tags") as response:
                if response.status == 200:
                    data = await response.json()
                    models = [model.get('name', 'unknown') for model in data.get('models', [])]
                    self.models_cache = models
                    self.last_model_fetch = datetime.now()
                    
                    return {
                        'status': 'healthy',
                        'available': True,
                        'models': models,
                        'model_count': len(models),
                        'base_url': self.base_url,
                        'message': f'Ollama server running with {len(models)} models available'
                    }
                else:
                    return {
                        'status': 'unhealthy',
                        'available': False,
                        'models': [],
                        'error': f'Server returned {response.status}',
                        'base_url': self.base_url,
                        'message': 'Ollama server not responding correctly'
                    }
                    
        except aiohttp.ClientError as e:
            logger.warning(f"Ollama connection error: {str(e)}")
            return {
                'status': 'unreachable',
                'available': False,
                'models': [],
                'error': str(e),
                'base_url': self.base_url,
                'message': 'Cannot connect to Ollama server'
            }
        except Exception as e:
            logger.error(f"Ollama health check failed: {str(e)}")
            return {
                'status': 'error',
                'available': False,
                'models': [],
                'error': str(e),
                'base_url': self.base_url,
                'message': 'Unexpected error during health check'
            }
            
    async def get_models(self, force_refresh: bool = False) -> List[str]:
        """
        Get available models with caching
        
        Args:
            force_refresh: If True, bypass cache and fetch fresh data
            
        Returns:
            List of available model names
        """
        # Use cached models if available and not forcing refresh
        if (not force_refresh and 
            self.models_cache and 
            self.last_model_fetch and 
            (datetime.now() - self.last_model_fetch).seconds < 300):  # 5 minute cache
            return self.models_cache
            
        try:
            await self._ensure_session()
            if not self.session:
                return self.models_cache
                
            async with self.session.get(f"{self.base_url}/api/tags") as response:
                if response.status == 200:
                    data = await response.json()
                    models = [model.get('name', 'unknown') for model in data.get('models', [])]
                    self.models_cache = models
                    self.last_model_fetch = datetime.now()
                    return models
                else:
                    logger.warning(f"Failed to fetch models: {response.status}")
                    return self.models_cache  # Return cached if available
                    
        except Exception as e:
            logger.error(f"Error fetching models: {str(e)}")
            return self.models_cache  # Return cached if available
            
    async def chat_completion(
        self,
        message: str,
        model: str = "llama3.2",
        conversation_id: str = "default",
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        stream: bool = False
    ) -> Dict[str, Any]:
        """
        Send a chat completion request to Ollama
        
        Args:
            message: User message
            model: Model name to use
            conversation_id: Unique conversation identifier
            system_prompt: Optional system prompt
            temperature: Sampling temperature (0.0 to 1.0)
            stream: Whether to use streaming response
            
        Returns:
            Response dict with message and metadata
        """
        try:
            await self._ensure_session()
            
            # Initialize conversation history if needed
            if conversation_id not in self.conversation_history:
                self.conversation_history[conversation_id] = []
                
            # Add system prompt if provided and not already in history
            messages = []
            if (system_prompt and 
                (not self.conversation_history[conversation_id] or 
                 self.conversation_history[conversation_id][0].get('role') != 'system')):
                messages.append({"role": "system", "content": system_prompt})
                
            # Add conversation history
            messages.extend(self.conversation_history[conversation_id])
            
            # Add current user message
            messages.append({"role": "user", "content": message})
            
            # Prepare request payload
            payload = {
                "model": model,
                "messages": messages,
                "stream": stream,
                "options": {
                    "temperature": temperature,
                    "top_p": 0.9,
                    "top_k": 40,
                }
            }
            
            if stream:
                return await self._handle_streaming_response(payload, conversation_id, message)
            else:
                return await self._handle_single_response(payload, conversation_id, message)
                
        except Exception as e:
            logger.error(f"Chat completion error: {str(e)}")
            return {
                'response': f"Error: {str(e)}",
                'success': False,
                'error': str(e),
                'conversation_id': conversation_id,
                'model': model
            }
            
    async def _handle_single_response(
        self, 
        payload: Dict[str, Any], 
        conversation_id: str, 
        user_message: str
    ) -> Dict[str, Any]:
        """Handle non-streaming response"""
        await self._ensure_session()
        if not self.session:
            return {
                'response': "Session error",
                'success': False,
                'error': "No session available",
                'conversation_id': conversation_id,
                'model': payload['model']
            }
            
        async with self.session.post(
            f"{self.base_url}/api/chat",
            json=payload
        ) as response:
            if response.status == 200:
                data = await response.json()
                assistant_message = data.get('message', {}).get('content', '')
                
                # Update conversation history
                self.conversation_history[conversation_id].extend([
                    {"role": "user", "content": user_message},
                    {"role": "assistant", "content": assistant_message}
                ])
                
                return {
                    'response': assistant_message,
                    'success': True,
                    'conversation_id': conversation_id,
                    'model': payload['model'],
                    'usage': data.get('usage', {}),
                    'context': data.get('context', '')
                }
            else:
                error_text = await response.text()
                logger.error(f"Ollama API error {response.status}: {error_text}")
                return {
                    'response': f"API Error: {response.status}",
                    'success': False,
                    'error': error_text,
                    'conversation_id': conversation_id,
                    'model': payload['model']
                }
                
    async def _handle_streaming_response(
        self, 
        payload: Dict[str, Any], 
        conversation_id: str, 
        user_message: str
    ) -> Dict[str, Any]:
        """Handle streaming response (non-generator version)"""
        full_response = ""
        
        await self._ensure_session()
        if not self.session:
            return {
                'response': "Session error",
                'success': False,
                'error': "No session available",
                'conversation_id': conversation_id,
                'model': payload['model']
            }
        
        async with self.session.post(
            f"{self.base_url}/api/chat",
            json=payload
        ) as response:
            if response.status == 200:
                async for line in response.content:
                    if line:
                        try:
                            chunk = json.loads(line.decode('utf-8'))
                            if 'message' in chunk and 'content' in chunk['message']:
                                content = chunk['message']['content']
                                full_response += content
                                
                        except json.JSONDecodeError:
                            continue
                            
                # Update conversation history with complete response
                self.conversation_history[conversation_id].extend([
                    {"role": "user", "content": user_message},
                    {"role": "assistant", "content": full_response}
                ])
                
                return {
                    'response': full_response,
                    'success': True,
                    'conversation_id': conversation_id,
                    'model': payload['model']
                }
            else:
                error_text = await response.text()
                logger.error(f"Ollama streaming error {response.status}: {error_text}")
                return {
                    'response': f"Streaming Error: {response.status}",
                    'success': False,
                    'error': error_text,
                    'conversation_id': conversation_id,
                    'model': payload['model']
                }
                
    async def stream_chat(
        self,
        message: str,
        model: str = "llama3.2",
        conversation_id: str = "default",
        system_prompt: Optional[str] = None,
        temperature: float = 0.7
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Stream chat completion response
        
        Args:
            message: User message
            model: Model name to use
            conversation_id: Unique conversation identifier
            system_prompt: Optional system prompt
            temperature: Sampling temperature
            
        Yields:
            Dict with streaming response chunks
        """
        payload = {
            "model": model,
            "messages": [{"role": "user", "content": message}],
            "stream": True,
            "options": {"temperature": temperature}
        }
        
        if system_prompt:
            payload["messages"].insert(0, {"role": "system", "content": system_prompt})
            
        try:
            await self._ensure_session()
            if not self.session:
                yield {
                    'error': "No session available",
                    'done': True,
                    'model': model,
                    'conversation_id': conversation_id
                }
                return
                
            async with self.session.post(
                f"{self.base_url}/api/chat",
                json=payload
            ) as response:
                if response.status == 200:
                    async for line in response.content:
                        if line:
                            try:
                                chunk = json.loads(line.decode('utf-8'))
                                if 'message' in chunk:
                                    yield {
                                        'content': chunk['message'].get('content', ''),
                                        'done': chunk.get('done', False),
                                        'model': model,
                                        'conversation_id': conversation_id
                                    }
                            except json.JSONDecodeError:
                                continue
                else:
                    yield {
                        'error': f"HTTP {response.status}",
                        'done': True,
                        'model': model,
                        'conversation_id': conversation_id
                    }
        except Exception as e:
            yield {
                'error': str(e),
                'done': True,
                'model': model,
                'conversation_id': conversation_id
            }
            
    async def clear_conversation(self, conversation_id: str) -> bool:
        """
        Clear conversation history for given ID
        
        Args:
            conversation_id: Conversation to clear
            
        Returns:
            True if cleared successfully
        """
        try:
            if conversation_id in self.conversation_history:
                del self.conversation_history[conversation_id]
            return True
        except Exception as e:
            logger.error(f"Error clearing conversation {conversation_id}: {str(e)}")
            return False
            
    async def get_conversation_history(self, conversation_id: str) -> List[Dict[str, str]]:
        """
        Get conversation history for given ID
        
        Args:
            conversation_id: Conversation ID to retrieve
            
        Returns:
            List of conversation messages
        """
        return self.conversation_history.get(conversation_id, [])
        
    def get_conversation_ids(self) -> List[str]:
        """
        Get all active conversation IDs
        
        Returns:
            List of conversation IDs
        """
        return list(self.conversation_history.keys())
