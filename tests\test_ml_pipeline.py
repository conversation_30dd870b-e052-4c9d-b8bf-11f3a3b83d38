# tests/test_ml_pipeline.py
import pytest
from datetime import datetime
from src.ml import ModelPipeline, PredictionInput, PredictionFeatures

class TestMLPipeline:
    """Test suite for the ML model pipeline"""
    
    @pytest.fixture
    def pipeline(self):
        return ModelPipeline()
    
    @pytest.mark.asyncio
    async def test_validate_input_data_before_prediction(self, pipeline):
        """Test input validation before prediction"""
        invalid_input = {
            'symbol': 'INVALID',
            'features': {
                'rsi': 150,  # Invalid RSI value
                'macd': None,
                'volume': -100  # Negative volume
            }
        }
        
        with pytest.raises(Exception) as exc_info:
            await pipeline.predict(invalid_input)
        
        # The error could be validation or attribute error
        error_msg = str(exc_info.value).lower()
        assert 'validation' in error_msg or 'attribute' in error_msg
    
    @pytest.mark.asyncio
    async def test_track_complete_prediction_lineage(self, pipeline):
        """Test complete prediction lineage tracking"""
        valid_input = PredictionInput(
            symbol='EURUSD',
            features=PredictionFeatures(
                rsi=65.5,
                macd=0.0012,
                volume=150000,
                sma_20=1.2050,
                ema_12=1.2048
            )
        )
        
        prediction = await pipeline.predict(valid_input)
        
        assert prediction.model_version is not None
        assert prediction.input_hash is not None
        assert prediction.prediction_hash is not None
        assert prediction.timestamp is not None
        assert 0 <= prediction.confidence <= 1
        assert prediction.metadata.processing_time_ms > 0
    
    @pytest.mark.asyncio
    async def test_ensure_deterministic_predictions(self, pipeline):
        """Test that predictions are deterministic"""
        input_data = PredictionInput(
            symbol='EURUSD',
            features=PredictionFeatures(
                rsi=70.0,
                macd=0.001,
                volume=100000
            )
        )
        
        prediction1 = await pipeline.predict(input_data)
        prediction2 = await pipeline.predict(input_data)
        
        # Should be identical due to caching
        assert prediction1.value == prediction2.value
        assert prediction1.input_hash == prediction2.input_hash
        assert prediction1.prediction_hash == prediction2.prediction_hash
    
    @pytest.mark.asyncio
    async def test_reject_predictions_with_insufficient_confidence(self, pipeline):
        """Test rejection of low-confidence predictions"""
        pipeline.set_min_confidence_threshold(0.9)  # Very high threshold
        
        input_data = PredictionInput(
            symbol='EURUSD',
            features=PredictionFeatures(
                rsi=50.0,  # Neutral values likely to produce low confidence
                macd=0.0,
                volume=1000
            )
        )
        
        prediction = await pipeline.predict(input_data)
        
        # With high threshold, prediction should likely be rejected
        if prediction.confidence < 0.9:
            assert prediction.rejected is True
            assert prediction.rejection_reason == 'Confidence below threshold'
    
    def test_confidence_threshold_validation(self, pipeline):
        """Test confidence threshold validation"""
        with pytest.raises(ValueError):
            pipeline.set_min_confidence_threshold(-0.1)  # Invalid negative
        
        with pytest.raises(ValueError):
            pipeline.set_min_confidence_threshold(1.5)   # Invalid > 1
        
        # Valid thresholds should work
        pipeline.set_min_confidence_threshold(0.8)
        assert pipeline.min_confidence_threshold == 0.8
    
    @pytest.mark.asyncio
    async def test_feature_preprocessing(self, pipeline):
        """Test feature preprocessing"""
        input_data = PredictionInput(
            symbol='EURUSD',
            features=PredictionFeatures(
                rsi=75.0,
                macd=0.002,
                volume=50000,
                sma_20=1.2000,
                ema_12=1.2010,
                bollinger_upper=1.2100,
                bollinger_lower=1.1900
            )
        )
        
        # Test preprocessing directly
        processed = pipeline._preprocess_features(input_data.features)
        
        assert 'rsi_normalized' in processed
        assert processed['rsi_normalized'] == 0.75  # 75/100
        assert 'volume_log' in processed
        assert processed['volume_log'] > 0  # log(50000 + 1)
        assert 'ma_ratio' in processed
        assert processed['ma_ratio'] == 1.2010 / 1.2000
        assert 'bb_position' in processed
    
    @pytest.mark.asyncio
    async def test_prediction_caching(self, pipeline):
        """Test prediction result caching"""
        input_data = PredictionInput(
            symbol='EURUSD',
            features=PredictionFeatures(
                rsi=60.0,
                macd=0.001,
                volume=75000
            )
        )
        
        # First prediction
        start_time = datetime.now()
        prediction1 = await pipeline.predict(input_data)
        first_duration = (datetime.now() - start_time).total_seconds()
        
        # Second prediction (should be cached)
        start_time = datetime.now()
        prediction2 = await pipeline.predict(input_data)
        second_duration = (datetime.now() - start_time).total_seconds()
        
        # Results should be identical
        assert prediction1.value == prediction2.value
        assert prediction1.input_hash == prediction2.input_hash
        
        # Second call should be much faster (cached)
        assert second_duration < first_duration
    
    def test_model_validation(self, pipeline):
        """Test model validation before deployment"""
        from src.ml.model_pipeline import ModelVersion, PerformanceMetrics
        
        # Valid model
        valid_model = ModelVersion(
            id='test_model_1',
            version='1.0.0',
            algorithm='RandomForest',
            training_data_hash='hash123',
            hyperparameters={'n_estimators': 100},
            performance_metrics=PerformanceMetrics(
                accuracy=0.75,
                precision=0.70,
                recall=0.80,
                f1_score=0.75,
                backtest_sharpe=1.2
            ),
            created_at=datetime.now(),
            model_hash='model_hash123'
        )
        
        assert pipeline._validate_model(valid_model) is True
        
        # Invalid model (low accuracy)
        invalid_model = ModelVersion(
            id='test_model_2',
            version='1.0.0',
            algorithm='RandomForest',
            training_data_hash='hash123',
            hyperparameters={'n_estimators': 100},
            performance_metrics=PerformanceMetrics(
                accuracy=0.5,  # Below threshold
                precision=0.70,
                recall=0.80,
                f1_score=0.75,
                backtest_sharpe=0.3  # Below threshold
            ),
            created_at=datetime.now(),
            model_hash='model_hash123'
        )
        
        assert pipeline._validate_model(invalid_model) is False
    
    @pytest.mark.asyncio
    async def test_model_update(self, pipeline):
        """Test model update functionality"""
        from src.ml.model_pipeline import ModelVersion, PerformanceMetrics
        
        old_model_id = pipeline.current_model.id if pipeline.current_model else None
        
        new_model = ModelVersion(
            id='new_test_model',
            version='2.0.0',
            algorithm='XGBoost',
            training_data_hash='new_hash123',
            hyperparameters={'n_estimators': 200},
            performance_metrics=PerformanceMetrics(
                accuracy=0.85,
                precision=0.80,
                recall=0.90,
                f1_score=0.85,
                backtest_sharpe=1.8
            ),
            created_at=datetime.now(),
            model_hash='new_model_hash123'
        )
        
        await pipeline.update_model(new_model)
        
        assert pipeline.current_model.id == 'new_test_model'
        assert pipeline.current_model.version == '2.0.0'
        assert len(pipeline.prediction_cache) == 0  # Cache should be cleared
    
    def test_cache_management(self, pipeline):
        """Test prediction cache management"""
        initial_cache_size = len(pipeline.prediction_cache)
        
        # Test cache stats
        stats = pipeline.get_cache_stats()
        assert 'cache_size' in stats
        assert 'max_cache_size' in stats
        
        # Test cache clearing
        pipeline.clear_cache()
        assert len(pipeline.prediction_cache) == 0
    
    @pytest.mark.asyncio
    async def test_error_handling(self, pipeline):
        """Test error handling in prediction pipeline"""
        # Test with completely invalid input
        with pytest.raises(Exception):
            await pipeline.predict("invalid_input")
        
        # Test with missing required fields
        invalid_features = {
            'symbol': 'EURUSD',
            'features': {}  # Missing required fields
        }
        
        with pytest.raises(Exception):
            await pipeline.predict(invalid_features)