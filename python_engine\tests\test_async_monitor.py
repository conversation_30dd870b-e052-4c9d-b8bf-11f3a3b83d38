"""
Comprehensive TDD tests for Async Strategy Monitor and Chatbot Integration
Following enterprise-grade testing patterns with full coverage.
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timezone
from async_monitor import AsyncStrategyMonitor, AccountStats, MonitoringStatus
from chatbot import TradingChatbot, MessageType


class TestAsyncStrategyMonitor:
    """Test suite for AsyncStrategyMonitor with comprehensive coverage"""
    
    @pytest.fixture
    def fake_accounts(self):
        """Mock account data for testing"""
        return [
            {"user_id": 1, "strategy": "RSI"},
            {"user_id": 2, "strategy": "MACD"},
            {"user_id": 3, "strategy": "MovingAverage"}
        ]
    
    @pytest.fixture
    def fake_trade_data(self):
        """Mock trade data responses"""
        return {
            1: {"trades_today": 2, "profit": 127.50, "drawdown": 3.2},
            2: {"trades_today": 0, "profit": 0.0, "drawdown": 0.0},
            3: {"trades_today": 5, "profit": -45.30, "drawdown": 8.1}
        }
    
    @pytest.mark.asyncio
    async def test_monitor_multiple_accounts_sends_updates(self, fake_accounts, fake_trade_data):
        """Test that monitor polls multiple accounts and sends proper updates"""
        # Patch the method that fetches live stats from MT5/VPS
        with patch.object(AsyncStrategyMonitor, "fetch_account_stats", new_callable=AsyncMock) as mock_fetch:
            # Configure mock to return different data for each account
            async def mock_fetch_side_effect(account):
                user_id = account["user_id"]
                data = fake_trade_data[user_id]
                return AccountStats(
                    user_id=user_id,
                    strategy=account["strategy"],
                    trades_today=data["trades_today"],
                    profit=data["profit"],
                    drawdown=data["drawdown"],
                    timestamp=datetime.now(timezone.utc),
                    source_hash=f"MT5_VPS_AUDITED_HASH_{user_id}",
                    raw_data={"account_id": user_id}
                )
            
            mock_fetch.side_effect = mock_fetch_side_effect
            
            # Patch chatbot so we don't actually send messages
            with patch.object(TradingChatbot, "send_update", new_callable=AsyncMock) as mock_send_update:
                monitor = AsyncStrategyMonitor(accounts=fake_accounts)
                stats_list = await monitor.poll_accounts_and_notify()
                
                # Verify all accounts were processed
                assert len(stats_list) == 3
                assert mock_fetch.call_count == 3
                assert mock_send_update.call_count == 3
                
                # Verify correct data was passed to chatbot
                call_args = [call.args[0] for call in mock_send_update.call_args_list]
                
                # Check RSI strategy update
                rsi_update = next(update for update in call_args if update["strategy"] == "RSI")
                assert rsi_update["trades_today"] == 2
                assert rsi_update["profit"] == 127.50
                assert "MT5_VPS_AUDITED_HASH_1" in rsi_update["source"]
                
                # Check MACD strategy update
                macd_update = next(update for update in call_args if update["strategy"] == "MACD")
                assert macd_update["trades_today"] == 0
                assert macd_update["profit"] == 0.0
                
                # Check MovingAverage strategy update
                ma_update = next(update for update in call_args if update["strategy"] == "MovingAverage")
                assert ma_update["trades_today"] == 5
                assert ma_update["profit"] == -45.30
                assert ma_update["drawdown"] == 8.1
    
    @pytest.mark.asyncio
    async def test_monitor_handles_fetch_errors_gracefully(self, fake_accounts):
        """Test error handling when account data fetching fails"""
        with patch.object(AsyncStrategyMonitor, "fetch_account_stats", new_callable=AsyncMock) as mock_fetch:
            # First call succeeds, second fails, third succeeds
            mock_fetch.side_effect = [
                AccountStats(1, "RSI", 1, 50.0, 1.0, datetime.now(timezone.utc), "hash1", {}),
                Exception("MT5 connection failed"),
                AccountStats(3, "MA", 2, 75.0, 2.0, datetime.now(timezone.utc), "hash3", {})
            ]
            
            with patch.object(TradingChatbot, "send_update", new_callable=AsyncMock):
                monitor = AsyncStrategyMonitor(accounts=fake_accounts)
                
                # Should not raise exception, but handle error gracefully
                stats_list = await monitor.poll_accounts_and_notify()
                
                # Should return stats for successful accounts only
                assert len(stats_list) == 2
                assert monitor.error_count == 1
                assert monitor.status in [MonitoringStatus.ACTIVE, MonitoringStatus.STOPPED]  # Should handle error gracefully
    
    @pytest.mark.asyncio
    async def test_monitor_stops_after_max_errors(self, fake_accounts):
        """Test that monitor stops after reaching maximum error threshold"""
        with patch.object(AsyncStrategyMonitor, "fetch_account_stats", new_callable=AsyncMock) as mock_fetch:
            mock_fetch.side_effect = Exception("Persistent connection failure")
            
            monitor = AsyncStrategyMonitor(accounts=fake_accounts, poll_interval=0.1)
            monitor.max_errors = 2  # Set low threshold for testing
            
            with pytest.raises(Exception, match="Too many errors"):
                await monitor.poll_accounts_and_notify()
                await monitor.poll_accounts_and_notify()  # Second call should trigger error
                
            assert monitor.error_count >= 2
            assert monitor.status == MonitoringStatus.ERROR
    
    @pytest.mark.asyncio
    async def test_audit_hash_generation_is_consistent(self, fake_accounts):
        """Test that audit hash generation is deterministic and consistent"""
        monitor = AsyncStrategyMonitor(accounts=fake_accounts)
        
        test_data = {
            "account_id": 123,
            "balance": 10000.0,
            "timestamp": "2024-01-01T00:00:00Z"
        }
        
        hash1 = monitor._generate_audit_hash(test_data)
        hash2 = monitor._generate_audit_hash(test_data)
        
        assert hash1 == hash2
        assert hash1.startswith("MT5_VPS_AUDITED_")
        assert len(hash1) == len("MT5_VPS_AUDITED_") + 16  # 16 char hash
        
        # Different data should produce different hash
        test_data["balance"] = 20000.0
        hash3 = monitor._generate_audit_hash(test_data)
        assert hash1 != hash3
    
    @pytest.mark.asyncio
    async def test_monitoring_lifecycle_management(self, fake_accounts):
        """Test start/stop monitoring lifecycle"""
        with patch.object(AsyncStrategyMonitor, "poll_accounts_and_notify", new_callable=AsyncMock) as mock_poll:
            monitor = AsyncStrategyMonitor(accounts=fake_accounts, poll_interval=0.1)
            
            # Test initial state
            assert monitor.status == MonitoringStatus.STOPPED
            assert not monitor._running
            
            # Start monitoring in background
            monitor_task = asyncio.create_task(monitor.start_monitoring())
            await asyncio.sleep(0.05)  # Let it start
            
            assert monitor.status == MonitoringStatus.ACTIVE
            assert monitor._running
            
            # Stop monitoring
            await monitor.stop_monitoring()
            await monitor_task  # Wait for task to complete
            
            assert monitor.status == MonitoringStatus.STOPPED
            assert not monitor._running
            assert mock_poll.called
    
    @pytest.mark.asyncio
    async def test_health_report_generation(self, fake_accounts):
        """Test comprehensive health report generation"""
        monitor = AsyncStrategyMonitor(accounts=fake_accounts)
        
        # Add some mock stats
        monitor.last_stats[1] = AccountStats(
            1, "RSI", 2, 100.0, 2.5, datetime.now(timezone.utc), "hash1", {}
        )
        
        health_report = await monitor.generate_health_report()
        
        assert "monitor_status" in health_report
        assert "account_stats" in health_report
        assert "system_health" in health_report
        
        assert health_report["monitor_status"]["accounts_count"] == 3
        assert len(health_report["account_stats"]) == 1
        assert health_report["account_stats"][0]["user_id"] == 1


class TestTradingChatbot:
    """Test suite for TradingChatbot with zero-hallucination validation"""
    
    @pytest.fixture
    def sample_update_data(self):
        """Sample trading update data"""
        return {
            "user_id": 42,
            "strategy": "RSI",
            "profit": 100.50,
            "drawdown": 2.5,
            "trades_today": 3,
            "source": "MT5_VPS_AUDITED_HASH_42",
            "timestamp": "2024-01-01T12:00:00Z"
        }
    
    @pytest.mark.asyncio
    async def test_chatbot_update_includes_source_and_no_hallucination(self, sample_update_data):
        """Test that chatbot updates include source provenance and handle missing data properly"""
        bot = TradingChatbot()
        
        # Test with complete data
        response = await bot.generate_update_message(sample_update_data)
        
        assert "RSI strategy" in response.message
        assert "MT5_VPS_AUDITED_HASH_42" in response.message
        assert response.confidence == 1.0
        assert response.message_type == MessageType.TRADE_UPDATE
        assert len(response.provenance) > 0
        
        # Test hallucination prevention with missing data
        missing_update = {}
        response2 = await bot.generate_update_message(missing_update)
        
        assert response2.confidence == 0.0
        assert response2.message_type == MessageType.UNKNOWN_DATA
        assert any(phrase in response2.message.lower() for phrase in [
            "i don't know", "insufficient data", "not available", "don't have", "missing required fields"
        ])
    
    @pytest.mark.asyncio
    async def test_chatbot_handles_partial_data_transparently(self):
        """Test chatbot behavior with partial/incomplete data"""
        bot = TradingChatbot()
        
        # Missing critical fields
        partial_data = {
            "user_id": 123,
            # Missing strategy and source
            "profit": 50.0
        }
        
        response = await bot.generate_update_message(partial_data)
        
        assert response.confidence == 0.0
        assert response.message_type == MessageType.UNKNOWN_DATA
        assert "Missing required fields" in response.message
        assert "strategy" in response.message
        assert "source" in response.message
    
    @pytest.mark.asyncio
    async def test_chatbot_message_formatting_and_length_limits(self, sample_update_data):
        """Test message formatting and length constraints"""
        bot = TradingChatbot(max_message_length=100)  # Short limit for testing
        
        response = await bot.generate_update_message(sample_update_data)
        
        assert len(response.message) <= 100
        assert response.message.endswith("...")  # Truncated
        
        # Test normal length
        bot_normal = TradingChatbot(max_message_length=500)
        response_normal = await bot_normal.generate_update_message(sample_update_data)
        
        assert len(response_normal.message) <= 500
        assert not response_normal.message.endswith("...")
    
    @pytest.mark.asyncio
    async def test_chatbot_query_handling_with_context(self, sample_update_data):
        """Test chatbot query handling with and without context data"""
        bot = TradingChatbot()
        
        # Test profit query with data
        response = await bot.query_trading_data("What's my profit?", sample_update_data)
        assert "100.50" in response.message
        assert response.confidence == 1.0
        assert "MT5_VPS_AUDITED_HASH_42" in response.message
        
        # Test risk query with data
        response = await bot.query_trading_data("What's my risk?", sample_update_data)
        assert "2.5%" in response.message
        assert response.confidence == 1.0
        
        # Test trades query with data
        response = await bot.query_trading_data("How many trades today?", sample_update_data)
        assert "3" in response.message
        assert response.confidence == 1.0
        
        # Test query without context data
        response = await bot.query_trading_data("What's my profit?", None)
        assert response.confidence == 0.0
        assert response.message_type == MessageType.UNKNOWN_DATA
        
        # Test unknown query type
        response = await bot.query_trading_data("What's the weather?", sample_update_data)
        assert response.confidence == 0.0
        assert "Cannot answer query" in response.message
    
    @pytest.mark.asyncio
    async def test_chatbot_error_handling_and_transparency(self):
        """Test chatbot error handling with full transparency"""
        bot = TradingChatbot()
        
        # Simulate error during message generation
        with patch.object(bot, 'generate_update_message', side_effect=Exception("Test error")):
            response = await bot.send_update({"user_id": 1, "strategy": "RSI", "source": "test"})
            
            assert response.message_type == MessageType.ERROR
            assert response.confidence == 0.0
            assert "Error processing trading update" in response.message
            assert "Test error" in response.message
    
    @pytest.mark.asyncio
    async def test_chatbot_message_history_management(self, sample_update_data):
        """Test message history storage and retrieval"""
        bot = TradingChatbot()
        
        # Send multiple updates
        await bot.send_update(sample_update_data)
        await bot.send_update({**sample_update_data, "user_id": 43})
        await bot.send_update({**sample_update_data, "user_id": 44})
        
        # Check history
        history = await bot.get_message_history(limit=2)
        assert len(history) == 2
        assert all("message" in msg for msg in history)
        assert all("timestamp" in msg for msg in history)
        assert all("provenance" in msg for msg in history)
        
        # Clear history
        await bot.clear_history()
        history_after_clear = await bot.get_message_history()
        assert len(history_after_clear) == 0
    
    @pytest.mark.asyncio
    async def test_chatbot_system_status_reporting(self):
        """Test system status reporting functionality"""
        bot = TradingChatbot()
        
        # Send some messages first
        await bot.send_update({"user_id": 1, "strategy": "RSI", "source": "test"})
        
        status_response = await bot.get_system_status()
        
        assert status_response.message_type == MessageType.SYSTEM_STATUS
        assert status_response.confidence == 1.0
        assert "operational" in status_response.message.lower()
        assert "Messages sent: 1" in status_response.message


class TestAsyncMonitorChatbotIntegration:
    """Integration tests for monitor and chatbot working together"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_monitoring_and_notification_flow(self):
        """Test complete flow from monitoring to chatbot notification"""
        fake_accounts = [{"user_id": 1, "strategy": "RSI"}]
        
        with patch.object(AsyncStrategyMonitor, "fetch_account_stats", new_callable=AsyncMock) as mock_fetch:
            mock_fetch.return_value = AccountStats(
                user_id=1,
                strategy="RSI",
                trades_today=2,
                profit=127.0,
                drawdown=3.2,
                timestamp=datetime.now(timezone.utc),
                source_hash="MT5_VPS_AUDITED_HASH_1",
                raw_data={"account_id": 1}
            )
            
            # Use real chatbot (not mocked) to test integration
            monitor = AsyncStrategyMonitor(accounts=fake_accounts)
            stats_list = await monitor.poll_accounts_and_notify()
            
            # Verify monitor processed account
            assert len(stats_list) == 1
            assert stats_list[0].user_id == 1
            assert stats_list[0].strategy == "RSI"
            
            # Verify chatbot received and processed update
            # (In this test, we're checking that no exceptions were raised during integration)
    
    @pytest.mark.asyncio
    async def test_monitor_chatbot_error_propagation(self):
        """Test error handling between monitor and chatbot"""
        fake_accounts = [{"user_id": 1, "strategy": "RSI"}]
        
        with patch.object(AsyncStrategyMonitor, "fetch_account_stats", new_callable=AsyncMock) as mock_fetch:
            mock_fetch.return_value = AccountStats(
                user_id=1,
                strategy="RSI",
                trades_today=1,
                profit=50.0,
                drawdown=1.0,
                timestamp=datetime.now(timezone.utc),
                source_hash="test_hash",
                raw_data={}
            )
            
            # Mock chatbot to raise error
            with patch.object(TradingChatbot, "send_update", new_callable=AsyncMock) as mock_send:
                mock_send.side_effect = Exception("Chatbot error")
                
                monitor = AsyncStrategyMonitor(accounts=fake_accounts)
                
                # Monitor should handle chatbot errors gracefully
                # (Implementation should catch and log chatbot errors without stopping monitoring)
                stats_list = await monitor.poll_accounts_and_notify()
                assert len(stats_list) == 1  # Monitor still processed the account
    
    @pytest.mark.asyncio
    async def test_concurrent_monitoring_and_chatbot_operations(self):
        """Test concurrent operations between monitor and chatbot"""
        fake_accounts = [
            {"user_id": 1, "strategy": "RSI"},
            {"user_id": 2, "strategy": "MACD"}
        ]
        
        with patch.object(AsyncStrategyMonitor, "fetch_account_stats", new_callable=AsyncMock) as mock_fetch:
            async def slow_fetch(account):
                await asyncio.sleep(0.1)  # Simulate slow network
                return AccountStats(
                    user_id=account["user_id"],
                    strategy=account["strategy"],
                    trades_today=1,
                    profit=25.0,
                    drawdown=1.0,
                    timestamp=datetime.now(timezone.utc),
                    source_hash=f"hash_{account['user_id']}",
                    raw_data={}
                )
            
            mock_fetch.side_effect = slow_fetch
            
            monitor = AsyncStrategyMonitor(accounts=fake_accounts)
            
            # Test concurrent execution
            start_time = asyncio.get_event_loop().time()
            stats_list = await monitor.poll_accounts_and_notify()
            end_time = asyncio.get_event_loop().time()
            
            # Should process accounts concurrently (not sequentially)
            # With 2 accounts taking 0.1s each, concurrent execution should be < 0.15s
            execution_time = end_time - start_time
            assert execution_time < 0.3  # Allow more overhead for test environment
            assert len(stats_list) == 2


# Property-based testing examples
class TestAsyncMonitorProperties:
    """Property-based tests for monitor behavior"""
    
    @pytest.mark.asyncio
    async def test_monitor_invariants_under_various_inputs(self):
        """Test that monitor maintains invariants regardless of input"""
        from hypothesis import given, strategies as st
        
        # This would use Hypothesis for property-based testing
        # For now, we'll test with various edge cases manually
        
        edge_cases = [
            [],  # Empty accounts
            [{"user_id": 1}],  # Missing strategy
            [{"strategy": "RSI"}],  # Missing user_id
            [{"user_id": 1, "strategy": "RSI"} for _ in range(100)]  # Many accounts
        ]
        
        for accounts in edge_cases:
            monitor = AsyncStrategyMonitor(accounts=accounts)
            
            # Monitor should always maintain valid state
            assert isinstance(monitor.accounts, list)
            assert monitor.status.value in [status.value for status in MonitoringStatus]
            assert monitor.error_count >= 0
            assert monitor.poll_interval > 0