"""
Backtest CRUD operations
"""

import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update, delete

from ..models import Backtest

async def get_backtest_by_id(db: AsyncSession, backtest_id: uuid.UUID) -> Optional[Backtest]:
    """Get backtest by ID"""
    result = await db.execute(select(Backtest).where(Backtest.id == backtest_id))
    return result.scalars().first()

async def get_backtests_by_user(db: AsyncSession, user_id: uuid.UUID) -> List[Backtest]:
    """Get all backtests for a user"""
    result = await db.execute(select(Backtest).where(Backtest.user_id == user_id))
    return result.scalars().all()

async def get_backtests_by_strategy(db: AsyncSession, strategy_id: uuid.UUID) -> List[Backtest]:
    """Get all backtests for a strategy"""
    result = await db.execute(select(Backtest).where(Backtest.strategy_id == strategy_id))
    return result.scalars().all()

async def create_backtest(
    db: AsyncSession,
    user_id: uuid.UUID,
    name: str,
    symbol: str,
    timeframe: str,
    start_date: datetime,
    end_date: datetime,
    initial_balance: float,
    strategy_id: Optional[uuid.UUID] = None
) -> Backtest:
    """Create a new backtest"""
    db_backtest = Backtest(
        user_id=user_id,
        strategy_id=strategy_id,
        name=name,
        symbol=symbol,
        timeframe=timeframe,
        start_date=start_date,
        end_date=end_date,
        initial_balance=initial_balance,
        status="pending",
        created_at=datetime.utcnow()
    )
    db.add(db_backtest)
    await db.commit()
    await db.refresh(db_backtest)
    return db_backtest

async def update_backtest(
    db: AsyncSession,
    backtest_id: uuid.UUID,
    backtest_data: Dict[str, Any]
) -> Optional[Backtest]:
    """Update backtest"""
    # Update the backtest
    await db.execute(
        update(Backtest)
        .where(Backtest.id == backtest_id)
        .values(**backtest_data)
    )
    await db.commit()
    
    # Return the updated backtest
    return await get_backtest_by_id(db, backtest_id)

async def update_backtest_status(
    db: AsyncSession,
    backtest_id: uuid.UUID,
    status: str,
    results: Optional[Dict[str, Any]] = None
) -> Optional[Backtest]:
    """Update backtest status"""
    backtest = await get_backtest_by_id(db, backtest_id)
    if not backtest:
        return None
    
    backtest.status = status
    if status == "completed" and results:
        backtest.results = results
        backtest.completed_at = datetime.utcnow()
    
    await db.commit()
    await db.refresh(backtest)
    return backtest

async def delete_backtest(db: AsyncSession, backtest_id: uuid.UUID) -> bool:
    """Delete backtest"""
    result = await db.execute(delete(Backtest).where(Backtest.id == backtest_id))
    await db.commit()
    return result.rowcount > 0