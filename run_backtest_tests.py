#!/usr/bin/env python3
"""
Enhanced Backtesting Engine Test Runner
Comprehensive test execution with detailed reporting.
"""

import sys
import os
import subprocess
import time
from datetime import datetime

def run_command(command, description):
    """Run a command and return success status"""
    print(f"\n🔄 {description}")
    print(f"Command: {command}")
    print("-" * 60)
    
    start_time = time.time()
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            cwd=os.path.dirname(os.path.abspath(__file__))
        )
        
        execution_time = time.time() - start_time
        
        if result.returncode == 0:
            print(f"✅ SUCCESS ({execution_time:.2f}s)")
            if result.stdout:
                print("Output:")
                print(result.stdout)
            return True
        else:
            print(f"❌ FAILED ({execution_time:.2f}s)")
            if result.stderr:
                print("Error:")
                print(result.stderr)
            if result.stdout:
                print("Output:")
                print(result.stdout)
            return False
            
    except Exception as e:
        execution_time = time.time() - start_time
        print(f"❌ EXCEPTION ({execution_time:.2f}s): {e}")
        return False


def check_dependencies():
    """Check if required dependencies are installed"""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        'pandas',
        'numpy', 
        'pytest',
        'pytest-asyncio'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ Missing packages: {', '.join(missing_packages)}")
        print("Install with: pip install " + " ".join(missing_packages))
        return False
    
    return True


def run_unit_tests():
    """Run unit tests for backtesting engine"""
    return run_command(
        "python -m pytest tests/test_backtest.py -v --tb=short",
        "Running unit tests for backtesting engine"
    )


def run_integration_tests():
    """Run integration tests"""
    return run_command(
        "python -m pytest tests/test_backtest.py::TestBacktestIntegration -v --tb=short",
        "Running integration tests"
    )


def run_property_tests():
    """Run property-based tests"""
    return run_command(
        "python -m pytest tests/test_backtest.py::TestBacktestProperties -v --tb=short",
        "Running property-based tests"
    )


def run_performance_tests():
    """Run performance benchmarking tests"""
    return run_command(
        "python -m pytest tests/test_backtest.py::TestBacktestIntegration::test_backtest_performance_benchmarking -v --tb=short",
        "Running performance benchmarking tests"
    )


def run_demo():
    """Run the demo script"""
    return run_command(
        "python demo_enhanced_backtest.py",
        "Running enhanced backtesting demo"
    )


def run_code_quality_checks():
    """Run code quality checks if tools are available"""
    print("\n🔍 Code Quality Checks")
    print("=" * 50)
    
    # Check if flake8 is available
    try:
        import flake8
        run_command(
            "python -m flake8 src/trading/backtest.py --max-line-length=100 --ignore=E501,W503",
            "Running flake8 code style check"
        )
    except ImportError:
        print("⚠️ flake8 not available - skipping style check")
    
    # Check if mypy is available
    try:
        import mypy
        run_command(
            "python -m mypy src/trading/backtest.py --ignore-missing-imports",
            "Running mypy type checking"
        )
    except ImportError:
        print("⚠️ mypy not available - skipping type check")


def generate_test_report():
    """Generate comprehensive test report"""
    return run_command(
        "python -m pytest tests/test_backtest.py --tb=short --junit-xml=backtest_test_report.xml",
        "Generating comprehensive test report"
    )


def main():
    """Main test runner"""
    print("🚀 Enhanced Backtesting Engine - Comprehensive Test Suite")
    print("=" * 70)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Track test results
    test_results = []
    
    # Check dependencies first
    if not check_dependencies():
        print("\n❌ Dependency check failed. Please install missing packages.")
        return 1
    
    # Run test suites
    test_suites = [
        ("Unit Tests", run_unit_tests),
        ("Integration Tests", run_integration_tests),
        ("Property Tests", run_property_tests),
        ("Performance Tests", run_performance_tests),
        ("Demo Execution", run_demo),
        ("Test Report Generation", generate_test_report)
    ]
    
    for suite_name, test_function in test_suites:
        success = test_function()
        test_results.append((suite_name, success))
    
    # Run code quality checks (optional)
    run_code_quality_checks()
    
    # Summary report
    print("\n\n📊 Test Execution Summary")
    print("=" * 70)
    
    passed = 0
    failed = 0
    
    for suite_name, success in test_results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{suite_name:<25} {status}")
        if success:
            passed += 1
        else:
            failed += 1
    
    print(f"\nTotal: {len(test_results)} suites")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    
    if failed == 0:
        print("\n🎉 All tests passed! Enhanced Backtesting Engine is ready for production.")
        return 0
    else:
        print(f"\n⚠️ {failed} test suite(s) failed. Please review the errors above.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)