"""
Property-Based Tests for Trading Strategy Invariants

Tests trading strategy behavior across a wide range of inputs
to verify mathematical properties and business logic invariants.
"""

import pytest
import sys
import os
import math
from hypothesis import given, strategies as st, assume, settings, example
from typing import Dict, List, Any

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

from services.darwin_godel.strategy_verifier import DarwinGodelVerifier
from services.darwin_godel.secure_executor import SecureStrategyExecutor
from services.darwin_godel.pattern_detector import StrategyPatternDetector

class TestStrategyProperties:
    """Property-based tests for trading strategy invariants"""
    
    def setup_method(self):
        """Setup before each test"""
        self.verifier = DarwinGodelVerifier()
        self.executor = SecureStrategyExecutor()
        self.pattern_detector = StrategyPatternDetector()
    
    @given(
        st.lists(
            st.floats(min_value=1.0, max_value=1000.0),
            min_size=2,
            max_size=100
        )
    )
    def test_strategy_output_invariants(self, prices):
        """Test that strategy outputs always satisfy basic invariants"""
        assume(all(p > 0 for p in prices))
        assume(len(set(prices)) > 1)  # Some price variation
        
        test_data = {
            'close': prices,
            'high': [p * 1.01 for p in prices],
            'low': [p * 0.99 for p in prices],
            'volume': [10000] * len(prices)
        }
        
        # Generic strategy template
        strategy = """
def trading_strategy(data, params):
    if len(data['close']) < 2:
        return {'signal': 'hold', 'confidence': 0.5}
    
    current_price = data['close'][-1]
    prev_price = data['close'][-2]
    
    if current_price > prev_price * 1.01:
        return {'signal': 'buy', 'confidence': 0.8}
    elif current_price < prev_price * 0.99:
        return {'signal': 'sell', 'confidence': 0.8}
    else:
        return {'signal': 'hold', 'confidence': 0.4}
"""
        
        result = self.executor.execute_strategy(strategy, test_data, {})
        
        # Universal invariants for all strategy outputs
        assert isinstance(result, dict), "Result must be a dictionary"
        assert 'signal' in result, "Result must contain 'signal'"
        assert 'confidence' in result, "Result must contain 'confidence'"
        
        # Signal invariants
        assert result['signal'] in ['buy', 'sell', 'hold'], f"Invalid signal: {result['signal']}"
        
        # Confidence invariants
        assert isinstance(result['confidence'], (int, float)), "Confidence must be numeric"
        assert 0.0 <= result['confidence'] <= 1.0, f"Confidence {result['confidence']} not in [0,1]"
        
        # Logical consistency
        current_price = prices[-1]
        prev_price = prices[-2]
        price_ratio = current_price / prev_price
        
        if price_ratio > 1.01:
            assert result['signal'] == 'buy', f"Should buy on {price_ratio:.4f} increase"
            assert result['confidence'] == 0.8, "Buy signal should have 0.8 confidence"
        elif price_ratio < 0.99:
            assert result['signal'] == 'sell', f"Should sell on {price_ratio:.4f} decrease"
            assert result['confidence'] == 0.8, "Sell signal should have 0.8 confidence"
        else:
            assert result['signal'] == 'hold', f"Should hold on {price_ratio:.4f} change"
            assert result['confidence'] == 0.4, "Hold signal should have 0.4 confidence"
    
    @given(
        st.integers(min_value=5, max_value=50),  # Moving average period
        st.floats(min_value=0.01, max_value=0.1)  # Threshold
    )
    def test_moving_average_strategy_properties(self, ma_period, threshold):
        """Test moving average strategy mathematical properties"""
        assume(5 <= ma_period <= 50)
        assume(0.01 <= threshold <= 0.1)
        
        # Generate test data with enough points
        import random
        prices = [100.0]
        for _ in range(ma_period + 10):
            change = random.uniform(-0.05, 0.05)
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 1.0))
        
        test_data = {
            'close': prices,
            'high': [p * 1.005 for p in prices],
            'low': [p * 0.995 for p in prices],
            'volume': [10000] * len(prices)
        }
        
        # Moving average strategy
        ma_strategy = f"""
def trading_strategy(data, params):
    ma_period = {ma_period}
    threshold = {threshold}
    
    if len(data['close']) < ma_period:
        return {{'signal': 'hold', 'confidence': 0.5}}
    
    prices = data['close']
    current_price = prices[-1]
    
    # Calculate moving average
    ma = sum(prices[-ma_period:]) / ma_period
    
    # Calculate deviation from MA
    deviation = (current_price - ma) / ma if ma > 0 else 0
    
    if deviation > threshold:
        return {{
            'signal': 'buy',
            'confidence': min(0.9, 0.5 + abs(deviation)),
            'ma': ma,
            'deviation': deviation
        }}
    elif deviation < -threshold:
        return {{
            'signal': 'sell',
            'confidence': min(0.9, 0.5 + abs(deviation)),
            'ma': ma,
            'deviation': deviation
        }}
    else:
        return {{
            'signal': 'hold',
            'confidence': 0.4,
            'ma': ma,
            'deviation': deviation
        }}
"""
        
        result = self.executor.execute_strategy(ma_strategy, test_data, {})
        
        # Verify moving average properties
        assert 'signal' in result
        assert 'ma' in result
        assert 'deviation' in result
        
        # Mathematical invariants
        assert result['ma'] > 0, "Moving average should be positive"
        
        # Verify MA calculation
        expected_ma = sum(prices[-ma_period:]) / ma_period
        assert abs(result['ma'] - expected_ma) < 0.001, "Moving average calculation should be accurate"
        
        # Verify deviation calculation
        current_price = prices[-1]
        expected_deviation = (current_price - result['ma']) / result['ma']
        assert abs(result['deviation'] - expected_deviation) < 0.001, "Deviation calculation should be accurate"
        
        # Verify signal logic
        if result['deviation'] > threshold:
            assert result['signal'] == 'buy', f"Should buy when deviation {result['deviation']:.4f} > {threshold}"
        elif result['deviation'] < -threshold:
            assert result['signal'] == 'sell', f"Should sell when deviation {result['deviation']:.4f} < -{threshold}"
        else:
            assert result['signal'] == 'hold', f"Should hold when |deviation| {abs(result['deviation']):.4f} <= {threshold}"
        
        # Confidence should increase with deviation magnitude
        expected_confidence = min(0.9, 0.5 + abs(result['deviation']))
        if result['signal'] != 'hold':
            assert abs(result['confidence'] - expected_confidence) < 0.001, \
                "Confidence should correlate with deviation magnitude"
    
    @given(
        st.floats(min_value=0.001, max_value=0.1),  # Volatility
        st.integers(min_value=10, max_value=30)  # Lookback period
    )
    def test_volatility_strategy_properties(self, target_volatility, lookback_period):
        """Test volatility-based strategy properties"""
        assume(0.001 <= target_volatility <= 0.1)
        assume(10 <= lookback_period <= 30)
        
        # Generate data with controlled volatility
        import random
        prices = [100.0]
        for _ in range(lookback_period + 5):
            change = random.gauss(0, target_volatility)  # Normal distribution
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 1.0))
        
        test_data = {
            'close': prices,
            'high': [p * 1.01 for p in prices],
            'low': [p * 0.99 for p in prices],
            'volume': [10000] * len(prices)
        }
        
        # Volatility-based strategy
        vol_strategy = f"""
def trading_strategy(data, params):
    lookback = {lookback_period}
    target_vol = {target_volatility}
    
    if len(data['close']) < lookback + 1:
        return {{'signal': 'hold', 'confidence': 0.5}}
    
    prices = data['close']
    
    # Calculate returns
    returns = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(-lookback, 0)]
    
    # Calculate volatility (standard deviation of returns)
    mean_return = sum(returns) / len(returns)
    variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
    volatility = variance ** 0.5
    
    # Volatility ratio
    vol_ratio = volatility / target_vol if target_vol > 0 else 1.0
    
    # Trading logic based on volatility
    if vol_ratio < 0.5:  # Low volatility - expect breakout
        return {{
            'signal': 'buy',
            'confidence': 0.7,
            'volatility': volatility,
            'vol_ratio': vol_ratio,
            'regime': 'low_vol'
        }}
    elif vol_ratio > 2.0:  # High volatility - be cautious
        return {{
            'signal': 'hold',
            'confidence': 0.3,
            'volatility': volatility,
            'vol_ratio': vol_ratio,
            'regime': 'high_vol'
        }}
    else:  # Normal volatility
        # Use price momentum
        current_price = prices[-1]
        prev_price = prices[-2]
        
        if current_price > prev_price * 1.005:
            signal = 'buy'
        elif current_price < prev_price * 0.995:
            signal = 'sell'
        else:
            signal = 'hold'
        
        return {{
            'signal': signal,
            'confidence': 0.6,
            'volatility': volatility,
            'vol_ratio': vol_ratio,
            'regime': 'normal_vol'
        }}
"""
        
        result = self.executor.execute_strategy(vol_strategy, test_data, {})
        
        # Verify volatility properties
        assert 'signal' in result
        assert 'volatility' in result
        assert 'vol_ratio' in result
        assert 'regime' in result
        
        # Mathematical invariants
        assert result['volatility'] >= 0, "Volatility should be non-negative"
        assert result['vol_ratio'] >= 0, "Volatility ratio should be non-negative"
        
        # Verify volatility calculation
        returns = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(-lookback_period, 0)]
        mean_return = sum(returns) / len(returns)
        expected_variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
        expected_volatility = expected_variance ** 0.5
        
        assert abs(result['volatility'] - expected_volatility) < 0.001, \
            "Volatility calculation should be accurate"
        
        # Verify regime classification
        vol_ratio = result['vol_ratio']
        if vol_ratio < 0.5:
            assert result['regime'] == 'low_vol', "Should classify as low volatility regime"
            assert result['signal'] == 'buy', "Should buy in low volatility regime"
        elif vol_ratio > 2.0:
            assert result['regime'] == 'high_vol', "Should classify as high volatility regime"
            assert result['signal'] == 'hold', "Should hold in high volatility regime"
        else:
            assert result['regime'] == 'normal_vol', "Should classify as normal volatility regime"
    
    @given(
        st.lists(
            st.floats(min_value=50.0, max_value=200.0),
            min_size=20,
            max_size=100
        ),
        st.floats(min_value=0.01, max_value=0.05)
    )
    def test_mean_reversion_properties(self, prices, reversion_threshold):
        """Test mean reversion strategy properties"""
        assume(all(p > 0 for p in prices))
        assume(len(prices) >= 20)
        assume(0.01 <= reversion_threshold <= 0.05)
        
        test_data = {
            'close': prices,
            'high': [p * 1.01 for p in prices],
            'low': [p * 0.99 for p in prices],
            'volume': [10000] * len(prices)
        }
        
        # Mean reversion strategy
        mr_strategy = f"""
def trading_strategy(data, params):
    threshold = {reversion_threshold}
    lookback = 20
    
    if len(data['close']) < lookback:
        return {{'signal': 'hold', 'confidence': 0.5}}
    
    prices = data['close']
    current_price = prices[-1]
    
    # Calculate long-term mean
    long_mean = sum(prices[-lookback:]) / lookback
    
    # Calculate short-term mean
    short_mean = sum(prices[-5:]) / 5
    
    # Mean reversion signals
    long_deviation = (current_price - long_mean) / long_mean if long_mean > 0 else 0
    short_deviation = (short_mean - long_mean) / long_mean if long_mean > 0 else 0
    
    # Mean reversion logic: buy when price is below mean, sell when above
    if long_deviation < -threshold:  # Price significantly below long-term mean
        return {{
            'signal': 'buy',
            'confidence': min(0.9, 0.6 + abs(long_deviation)),
            'long_mean': long_mean,
            'short_mean': short_mean,
            'long_deviation': long_deviation,
            'short_deviation': short_deviation
        }}
    elif long_deviation > threshold:  # Price significantly above long-term mean
        return {{
            'signal': 'sell',
            'confidence': min(0.9, 0.6 + abs(long_deviation)),
            'long_mean': long_mean,
            'short_mean': short_mean,
            'long_deviation': long_deviation,
            'short_deviation': short_deviation
        }}
    else:
        return {{
            'signal': 'hold',
            'confidence': 0.4,
            'long_mean': long_mean,
            'short_mean': short_mean,
            'long_deviation': long_deviation,
            'short_deviation': short_deviation
        }}
"""
        
        result = self.executor.execute_strategy(mr_strategy, test_data, {})
        
        # Verify mean reversion properties
        assert 'signal' in result
        assert 'long_mean' in result
        assert 'short_mean' in result
        assert 'long_deviation' in result
        
        # Mathematical invariants
        assert result['long_mean'] > 0, "Long-term mean should be positive"
        assert result['short_mean'] > 0, "Short-term mean should be positive"
        
        # Verify mean calculations
        expected_long_mean = sum(prices[-20:]) / 20
        expected_short_mean = sum(prices[-5:]) / 5
        
        assert abs(result['long_mean'] - expected_long_mean) < 0.001, \
            "Long-term mean calculation should be accurate"
        assert abs(result['short_mean'] - expected_short_mean) < 0.001, \
            "Short-term mean calculation should be accurate"
        
        # Verify deviation calculations
        current_price = prices[-1]
        expected_long_deviation = (current_price - result['long_mean']) / result['long_mean']
        assert abs(result['long_deviation'] - expected_long_deviation) < 0.001, \
            "Long deviation calculation should be accurate"
        
        # Verify mean reversion logic
        if result['long_deviation'] < -reversion_threshold:
            assert result['signal'] == 'buy', \
                f"Should buy when price is {result['long_deviation']:.4f} below mean"
        elif result['long_deviation'] > reversion_threshold:
            assert result['signal'] == 'sell', \
                f"Should sell when price is {result['long_deviation']:.4f} above mean"
        else:
            assert result['signal'] == 'hold', \
                f"Should hold when deviation {result['long_deviation']:.4f} is within threshold"
    
    @given(
        st.integers(min_value=5, max_value=20),  # Short MA period
        st.integers(min_value=20, max_value=50)  # Long MA period
    )
    def test_dual_moving_average_properties(self, short_period, long_period):
        """Test dual moving average crossover strategy properties"""
        assume(short_period < long_period)
        assume(5 <= short_period <= 20)
        assume(20 <= long_period <= 50)
        
        # Generate test data
        import random
        prices = [100.0]
        for _ in range(long_period + 10):
            change = random.uniform(-0.02, 0.02)
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 1.0))
        
        test_data = {
            'close': prices,
            'high': [p * 1.005 for p in prices],
            'low': [p * 0.995 for p in prices],
            'volume': [10000] * len(prices)
        }
        
        # Dual MA strategy
        dual_ma_strategy = f"""
def trading_strategy(data, params):
    short_period = {short_period}
    long_period = {long_period}
    
    if len(data['close']) < long_period:
        return {{'signal': 'hold', 'confidence': 0.5}}
    
    prices = data['close']
    
    # Calculate moving averages
    short_ma = sum(prices[-short_period:]) / short_period
    long_ma = sum(prices[-long_period:]) / long_period
    
    # Previous MAs for crossover detection
    if len(prices) >= long_period + 1:
        prev_short_ma = sum(prices[-short_period-1:-1]) / short_period
        prev_long_ma = sum(prices[-long_period-1:-1]) / long_period
    else:
        prev_short_ma = short_ma
        prev_long_ma = long_ma
    
    # Crossover logic
    current_above = short_ma > long_ma
    prev_above = prev_short_ma > prev_long_ma
    
    # Golden cross (bullish)
    if current_above and not prev_above:
        return {{
            'signal': 'buy',
            'confidence': 0.8,
            'short_ma': short_ma,
            'long_ma': long_ma,
            'crossover': 'golden'
        }}
    # Death cross (bearish)
    elif not current_above and prev_above:
        return {{
            'signal': 'sell',
            'confidence': 0.8,
            'short_ma': short_ma,
            'long_ma': long_ma,
            'crossover': 'death'
        }}
    # Trend following
    elif current_above:
        return {{
            'signal': 'buy',
            'confidence': 0.6,
            'short_ma': short_ma,
            'long_ma': long_ma,
            'crossover': 'none'
        }}
    else:
        return {{
            'signal': 'sell',
            'confidence': 0.6,
            'short_ma': short_ma,
            'long_ma': long_ma,
            'crossover': 'none'
        }}
"""
        
        result = self.executor.execute_strategy(dual_ma_strategy, test_data, {})
        
        # Verify dual MA properties
        assert 'signal' in result
        assert 'short_ma' in result
        assert 'long_ma' in result
        assert 'crossover' in result
        
        # Mathematical invariants
        assert result['short_ma'] > 0, "Short MA should be positive"
        assert result['long_ma'] > 0, "Long MA should be positive"
        
        # Verify MA calculations
        expected_short_ma = sum(prices[-short_period:]) / short_period
        expected_long_ma = sum(prices[-long_period:]) / long_period
        
        assert abs(result['short_ma'] - expected_short_ma) < 0.001, \
            "Short MA calculation should be accurate"
        assert abs(result['long_ma'] - expected_long_ma) < 0.001, \
            "Long MA calculation should be accurate"
        
        # Verify crossover logic
        if result['crossover'] == 'golden':
            assert result['signal'] == 'buy', "Golden cross should generate buy signal"
            assert result['confidence'] == 0.8, "Golden cross should have high confidence"
        elif result['crossover'] == 'death':
            assert result['signal'] == 'sell', "Death cross should generate sell signal"
            assert result['confidence'] == 0.8, "Death cross should have high confidence"
        else:
            # Trend following logic
            if result['short_ma'] > result['long_ma']:
                assert result['signal'] == 'buy', "Short MA above long MA should be bullish"
            else:
                assert result['signal'] == 'sell', "Short MA below long MA should be bearish"
            assert result['confidence'] == 0.6, "Trend following should have medium confidence"
    
    @given(st.text(min_size=10, max_size=1000, alphabet='abcdefghijklmnopqrstuvwxyz '))
    def test_strategy_code_properties(self, strategy_text):
        """Test properties of strategy code analysis"""
        assume(len(strategy_text.strip()) > 10)
        
        # Create a valid strategy from the text
        strategy_code = f"""
def trading_strategy(data, params):
    # Generated strategy with text: {strategy_text[:100]}
    if len(data['close']) < 2:
        return {{'signal': 'hold', 'confidence': 0.5}}
    
    # Simple logic based on text length
    text_length = {len(strategy_text)}
    
    if text_length % 3 == 0:
        return {{'signal': 'buy', 'confidence': 0.7}}
    elif text_length % 3 == 1:
        return {{'signal': 'sell', 'confidence': 0.7}}
    else:
        return {{'signal': 'hold', 'confidence': 0.4}}
"""
        
        # Test pattern detection
        pattern_result = self.pattern_detector.analyze_strategy(strategy_code)
        
        # Pattern detection invariants
        assert 'pattern_type' in pattern_result
        assert 'confidence' in pattern_result
        assert 'indicators_found' in pattern_result
        
        assert isinstance(pattern_result['pattern_type'], str)
        assert 0.0 <= pattern_result['confidence'] <= 1.0
        assert isinstance(pattern_result['indicators_found'], list)
        
        # Test strategy verification
        verification_result = self.verifier.verify_strategy(strategy_code)
        
        # Verification invariants
        assert 'is_valid' in verification_result
        assert 'strategy_type' in verification_result
        assert 'risk_score' in verification_result
        
        assert isinstance(verification_result['is_valid'], bool)
        assert 0.0 <= verification_result['risk_score'] <= 1.0