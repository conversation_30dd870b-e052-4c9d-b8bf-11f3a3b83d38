"""
Run the simple FastAPI server
"""

import os
import sys
import subprocess

def main():
    """Run the simple FastAPI server"""
    # Get the directory of this script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Path to the simple server script
    server_script = os.path.join(script_dir, "backend", "simple_server.py")
    
    # Run the server
    try:
        subprocess.run([sys.executable, server_script], check=True)
    except KeyboardInterrupt:
        print("Server stopped by user")
    except Exception as e:
        print(f"Error running server: {e}")

if __name__ == "__main__":
    main()