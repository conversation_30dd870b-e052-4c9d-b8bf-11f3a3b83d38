# tests/test_mt5_mvp.py
"""
MT5 integration tests for the MVP version

This file contains the essential MT5 integration tests for the MVP version.
It focuses on:
1. MT5 connection management
2. Order placement and execution
3. Position management
4. Error handling and recovery
"""

import pytest
import logging
from src.trading.mt5_bridge_tdd import MT5Bridge

# Configure logging
logger = logging.getLogger(__name__)

@pytest.fixture
def mt5_bridge():
    """Fixture to create an MT5 Bridge instance in offline mode"""
    bridge = MT5Bridge(offline_mode=True)
    # Ensure it's connected
    bridge.connect()
    yield bridge
    # Clean up
    if bridge.is_connected():
        bridge.disconnect()

@pytest.mark.integration
def test_mt5_connection():
    """Test MT5 connection in offline mode"""
    # Create MT5 Bridge in offline mode
    bridge = MT5Bridge(offline_mode=True)
    
    # Test connection
    assert bridge.is_connected() is True
    
    # Test disconnection
    bridge.disconnect()
    assert bridge.is_connected() is False
    
    # Test reconnection
    result = bridge.connect()
    assert result is True
    assert bridge.is_connected() is True

@pytest.mark.integration
def test_mt5_order_simulation():
    """Test simulated order placement"""
    # Create MT5 Bridge in offline mode
    bridge = MT5Bridge(offline_mode=True)
    
    # Place a market buy order
    order_id = bridge.place_order(
        symbol="EURUSD",
        order_type="BUY",
        lot=0.1
    )
    
    # Verify order was placed
    assert order_id > 0
    
    # Get positions
    positions = bridge.get_positions()
    assert len(positions) > 0
    
    # Verify position details
    position = next((p for p in positions if p["id"] == order_id), None)
    assert position is not None
    assert position["symbol"] == "EURUSD"
    assert position["type"] == "BUY"
    assert position["lot"] == 0.1
    
    # Close the order
    assert bridge.close_order(order_id) is True
    
    # Verify order was closed
    assert bridge.get_order_status(order_id) == "closed"

@pytest.mark.integration
def test_mt5_multiple_orders():
    """Test placing and managing multiple orders"""
    # Create MT5 Bridge in offline mode
    bridge = MT5Bridge(offline_mode=True)
    
    # Place multiple orders
    orders = []
    symbols = ["EURUSD", "GBPUSD", "USDJPY"]
    order_types = ["BUY", "SELL", "BUY"]
    lot_sizes = [0.1, 0.2, 0.3]
    
    for symbol, order_type, lot in zip(symbols, order_types, lot_sizes):
        order_id = bridge.place_order(
            symbol=symbol,
            order_type=order_type,
            lot=lot
        )
        orders.append(order_id)
    
    # Verify all orders were placed
    assert len(orders) == 3
    
    # Get positions
    positions = bridge.get_positions()
    assert len(positions) == 3
    
    # Verify position details
    for i, (order_id, symbol, order_type, lot) in enumerate(zip(orders, symbols, order_types, lot_sizes)):
        position = next((p for p in positions if p["id"] == order_id), None)
        assert position is not None
        assert position["symbol"] == symbol
        assert position["type"] == order_type
        assert position["lot"] == lot
    
    # Close all orders
    for order_id in orders:
        assert bridge.close_order(order_id) is True
        assert bridge.get_order_status(order_id) == "closed"
    
    # Verify all positions are closed
    positions = bridge.get_positions()
    assert len(positions) == 0

@pytest.mark.integration
def test_mt5_pending_orders():
    """Test placing and managing pending orders"""
    # Create MT5 Bridge in offline mode
    bridge = MT5Bridge(offline_mode=True)
    
    # Place a buy limit order
    buy_limit_id = bridge.place_order(
        symbol="EURUSD",
        order_type="BUY_LIMIT",
        lot=0.1,
        price=1.0500  # Below current price
    )
    
    # Place a sell limit order
    sell_limit_id = bridge.place_order(
        symbol="EURUSD",
        order_type="SELL_LIMIT",
        lot=0.1,
        price=1.2000  # Above current price
    )
    
    # Verify orders were placed
    assert buy_limit_id > 0
    assert sell_limit_id > 0
    
    # Get all orders (in offline mode, these are treated as filled)
    positions = bridge.get_positions()
    assert len(positions) == 2
    
    # Close all orders
    assert bridge.close_order(buy_limit_id) is True
    assert bridge.close_order(sell_limit_id) is True

@pytest.mark.integration
def test_mt5_error_handling():
    """Test error handling in MT5 Bridge"""
    # Create MT5 Bridge in offline mode
    bridge = MT5Bridge(offline_mode=True)
    
    # Test invalid symbol
    with pytest.raises(ValueError):
        bridge.place_order(
            symbol="INVALID",
            order_type="BUY",
            lot=0.1
        )
    
    # Test invalid lot size
    with pytest.raises(ValueError):
        bridge.place_order(
            symbol="EURUSD",
            order_type="BUY",
            lot=0
        )
    
    # Test closing non-existent order
    assert bridge.close_order(9999) is False
    
    # Test getting status of non-existent order
    assert bridge.get_order_status(9999) == "not_found"

@pytest.mark.integration
def test_mt5_reconnection():
    """Test reconnection after connection loss"""
    # Create MT5 Bridge in offline mode
    bridge = MT5Bridge(offline_mode=True)
    
    # Verify initial connection
    assert bridge.is_connected() is True
    
    # Simulate connection loss
    bridge.disconnect()
    assert bridge.is_connected() is False
    
    # Manually reconnect
    result = bridge.connect()
    assert result is True
    assert bridge.is_connected() is True
    
    # Place an order after reconnection
    order_id = bridge.place_order(
        symbol="EURUSD",
        order_type="BUY",
        lot=0.1
    )
    
    # Verify order placement
    assert order_id > 0
    assert bridge.get_order_status(order_id) == "filled"
    
    # Clean up
    bridge.close_order(order_id)