#!/usr/bin/env python3
"""
CI/CD Setup Validation Script

This script validates that the CI/CD pipeline is properly configured
and all components are working correctly.
"""

import os
import sys
import subprocess
import tempfile
from pathlib import Path


def check_file_exists(filepath, description):
    """Check if a file exists and report status"""
    if os.path.exists(filepath):
        print(f"✅ {description}: {filepath}")
        return True
    else:
        print(f"❌ {description}: {filepath} (NOT FOUND)")
        return False


def run_command(command, description, check_return_code=True):
    """Run a command and report status"""
    try:
        print(f"🔄 Running: {description}")
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if check_return_code and result.returncode != 0:
            print(f"❌ {description}: FAILED")
            print(f"   Error: {result.stderr}")
            return False
        else:
            print(f"✅ {description}: SUCCESS")
            if result.stdout:
                # Show first few lines of output
                lines = result.stdout.split('\n')[:3]
                for line in lines:
                    if line.strip():
                        print(f"   {line}")
            return True
    except Exception as e:
        print(f"❌ {description}: EXCEPTION - {e}")
        return False


def validate_cicd_setup():
    """Validate the complete CI/CD setup"""
    print("🚀 CI/CD SETUP VALIDATION")
    print("=" * 50)
    
    validation_results = []
    
    # 1. Check GitHub Actions workflow files
    print("\n📁 GITHUB ACTIONS WORKFLOWS")
    print("-" * 30)
    
    workflows = [
        (".github/workflows/python-app.yml", "Main Python Application Workflow"),
        (".github/workflows/code-quality.yml", "Code Quality Workflow"),
        (".github/workflows/hypothesis-testing.yml", "Property-Based Testing Workflow"),
        (".github/workflows/tdd-workflow.yml", "TDD Workflow")
    ]
    
    for filepath, description in workflows:
        validation_results.append(check_file_exists(filepath, description))
    
    # 2. Check configuration files
    print("\n⚙️ CONFIGURATION FILES")
    print("-" * 25)
    
    configs = [
        ("pytest.ini", "Pytest Configuration"),
        ("requirements-dev.txt", "Development Dependencies"),
        (".pre-commit-config.yaml", "Pre-commit Configuration")
    ]
    
    for filepath, description in configs:
        validation_results.append(check_file_exists(filepath, description))
    
    # 3. Check test files
    print("\n🧪 TEST FILES")
    print("-" * 15)
    
    test_files = [
        ("tests/test_chatbot_regression.py", "Regression Tests"),
        ("tests/test_hypothesis_chatbot.py", "Property-Based Tests")
    ]
    
    for filepath, description in test_files:
        validation_results.append(check_file_exists(filepath, description))
    
    # 4. Validate pytest configuration
    print("\n🔧 PYTEST VALIDATION")
    print("-" * 20)
    
    pytest_commands = [
        ("python -m pytest --version", "Pytest Installation"),
        ("python -m pytest --collect-only tests/ -q", "Test Collection"),
        ("python -c \"import hypothesis; print('Hypothesis version:', hypothesis.__version__)\"", "Hypothesis Installation")
    ]
    
    for command, description in pytest_commands:
        validation_results.append(run_command(command, description))
    
    # 5. Run sample tests
    print("\n🎯 SAMPLE TEST EXECUTION")
    print("-" * 25)
    
    test_commands = [
        ("python -m pytest tests/test_chatbot_regression.py::TestChatbotNoHallucinationRegression::test_chatbot_includes_source_or_idk -v", "Core Regression Test"),
        ("python -m pytest tests/test_hypothesis_chatbot.py::TestChatbotHypothesis::test_no_hallucination_property -v", "Property-Based Test")
    ]
    
    for command, description in test_commands:
        validation_results.append(run_command(command, description))
    
    # 6. Validate chatbot functionality
    print("\n🤖 CHATBOT VALIDATION")
    print("-" * 20)
    
    chatbot_validation = validate_chatbot_functionality()
    validation_results.append(chatbot_validation)
    
    # 7. Check code quality tools
    print("\n📊 CODE QUALITY TOOLS")
    print("-" * 20)
    
    quality_commands = [
        ("python -c \"import black; print('Black version:', black.__version__)\"", "Black Code Formatter", False),
        ("python -c \"import isort; print('isort version:', isort.__version__)\"", "isort Import Sorter", False),
        ("python -c \"import flake8; print('Flake8 available')\"", "Flake8 Linter", False),
        ("python -c \"import mypy; print('MyPy version:', mypy.version.__version__)\"", "MyPy Type Checker", False)
    ]
    
    for command, description, *check_code in quality_commands:
        check_return_code = check_code[0] if check_code else False
        validation_results.append(run_command(command, description, check_return_code))
    
    # 8. Summary
    print("\n🎯 VALIDATION SUMMARY")
    print("=" * 20)
    
    total_checks = len(validation_results)
    passed_checks = sum(validation_results)
    failed_checks = total_checks - passed_checks
    
    print(f"📊 Total Checks: {total_checks}")
    print(f"✅ Passed: {passed_checks}")
    print(f"❌ Failed: {failed_checks}")
    print(f"📈 Success Rate: {(passed_checks/total_checks)*100:.1f}%")
    
    if failed_checks == 0:
        print("\n🎉 CI/CD SETUP VALIDATION: ✅ COMPLETE")
        print("   All components are properly configured and working!")
        return True
    else:
        print(f"\n⚠️ CI/CD SETUP VALIDATION: NEEDS ATTENTION")
        print(f"   {failed_checks} component(s) need to be addressed.")
        return False


def validate_chatbot_functionality():
    """Validate core chatbot functionality"""
    try:
        print("🔄 Running: Chatbot Functionality Validation")
        
        # Import and test chatbot
        sys.path.append('src')
        from chatbot.knowledge_base import TradingChatbot, KnowledgeBase
        
        # Create temporary chatbot
        temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        temp_db.close()
        
        try:
            kb = KnowledgeBase(temp_db.name)
            chatbot = TradingChatbot(kb)
            chatbot.add_trading_knowledge()
            
            # Test critical queries
            test_queries = [
                ("What is RSI?", "should have sources"),
                ("Show me GBPUSD backtest", "should have sources"),
                ("Who won the World Series?", "should return I don't know")
            ]
            
            all_valid = True
            for query, expectation in test_queries:
                response = chatbot.answer(query)
                has_source = "source" in response.lower() or "hash" in response.lower()
                has_idk = "i don't know" in response.lower()
                
                if not (has_source or has_idk):
                    print(f"   ❌ Query failed: {query}")
                    all_valid = False
                else:
                    print(f"   ✅ Query passed: {query}")
            
            if all_valid:
                print("✅ Chatbot Functionality Validation: SUCCESS")
                return True
            else:
                print("❌ Chatbot Functionality Validation: FAILED")
                return False
        
        finally:
            if os.path.exists(temp_db.name):
                os.unlink(temp_db.name)
    
    except Exception as e:
        print(f"❌ Chatbot Functionality Validation: EXCEPTION - {e}")
        return False


def main():
    """Main validation function"""
    print("🚀 AI ENHANCED TRADING PLATFORM")
    print("   CI/CD Setup Validation Script")
    print("=" * 50)
    
    # Change to project directory
    project_root = Path(__file__).parent
    os.chdir(project_root)
    
    print(f"📁 Working Directory: {os.getcwd()}")
    print()
    
    # Run validation
    success = validate_cicd_setup()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 VALIDATION COMPLETE: CI/CD setup is ready for production!")
        sys.exit(0)
    else:
        print("⚠️ VALIDATION INCOMPLETE: Please address the issues above.")
        sys.exit(1)


if __name__ == "__main__":
    main()