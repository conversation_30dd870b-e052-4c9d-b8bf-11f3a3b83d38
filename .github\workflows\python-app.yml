name: Python application

on:
  push:
    branches: [ "main", "develop" ]
  pull_request:
    branches: [ "main", "develop" ]

permissions:
  contents: read

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.9", "3.10", "3.11", "3.12"]

    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Cache pip dependencies
      uses: actions/cache@v4
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements*.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8 pytest pytest-cov pytest-xdist pytest-mock
        pip install hypothesis faker
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
        if [ -f requirements-dev.txt ]; then pip install -r requirements-dev.txt; fi
    
    - name: Lin<PERSON> with flake8
      run: |
        # stop the build if there are Python syntax errors or undefined names
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        # exit-zero treats all errors as warnings. The GitHub editor is 127 chars wide
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
    
    - name: Test with pytest
      run: |
        pytest --cov=src --cov-report=xml --cov-report=html --cov-report=term-missing -v
    
    - name: Upload coverage reports to Codecov
      uses: codecov/codecov-action@v4
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

  security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
    
    - name: Install security tools
      run: |
        python -m pip install --upgrade pip
        pip install bandit safety
    
    - name: Run bandit security linter
      run: |
        bandit -r src/ -f json -o bandit-report.json || true
        bandit -r src/ -f txt
    
    - name: Check dependencies for known security vulnerabilities
      run: |
        safety check --json --output safety-report.json || true
        safety check

  type-check:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install mypy types-requests
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
    
    - name: Type check with mypy
      run: |
        mypy src/ --ignore-missing-imports --no-strict-optional

  integration-tests:
    runs-on: ubuntu-latest
    needs: test
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest pytest-asyncio
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
    
    - name: Run integration tests
      run: |
        pytest tests/integration/ -v --tb=short || echo "No integration tests found"
    
    - name: Run chatbot regression tests
      run: |
        pytest tests/test_chatbot_regression.py -v --tb=short
    
    - name: Run demo validation
      run: |
        python demo_chatbot_regression.py || echo "Demo completed with warnings"

  performance-tests:
    runs-on: ubuntu-latest
    needs: test
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest pytest-benchmark memory-profiler
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
    
    - name: Run performance benchmarks
      run: |
        pytest tests/ -k "benchmark" --benchmark-only --benchmark-json=benchmark.json || echo "No benchmark tests found"
    
    - name: Memory profiling
      run: |
        python -m memory_profiler demo_chatbot_regression.py || echo "Memory profiling completed"

  build-and-package:
    runs-on: ubuntu-latest
    needs: [test, security, type-check]
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
    
    - name: Install build dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build wheel setuptools
    
    - name: Build package
      run: |
        python -m build
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: python-package-distributions
        path: dist/

  deploy-staging:
    runs-on: ubuntu-latest
    needs: [build-and-package, integration-tests, performance-tests]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    steps:
    - uses: actions/checkout@v4
    
    - name: Download build artifacts
      uses: actions/download-artifact@v4
      with:
        name: python-package-distributions
        path: dist/
    
    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # Add your staging deployment commands here
        echo "Staging deployment completed"

  deploy-production:
    runs-on: ubuntu-latest
    needs: [build-and-package, integration-tests, performance-tests]
    if: github.ref == 'refs/heads/main'
    environment: production
    steps:
    - uses: actions/checkout@v4
    
    - name: Download build artifacts
      uses: actions/download-artifact@v4
      with:
        name: python-package-distributions
        path: dist/
    
    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        # Add your production deployment commands here
        echo "Production deployment completed"

  notify:
    runs-on: ubuntu-latest
    needs: [test, security, type-check, integration-tests, performance-tests]
    if: always()
    steps:
    - name: Notify on success
      if: ${{ needs.test.result == 'success' && needs.security.result == 'success' && needs.type-check.result == 'success' }}
      run: |
        echo "✅ All tests passed successfully!"
        echo "Pipeline Status: SUCCESS"
    
    - name: Notify on failure
      if: ${{ needs.test.result == 'failure' || needs.security.result == 'failure' || needs.type-check.result == 'failure' }}
      run: |
        echo "❌ Pipeline failed!"
        echo "Test Result: ${{ needs.test.result }}"
        echo "Security Result: ${{ needs.security.result }}"
        echo "Type Check Result: ${{ needs.type-check.result }}"
        exit 1