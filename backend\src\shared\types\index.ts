export interface ServiceError {
  code: string;
  message: string;
  details?: string;
}

export interface ServiceResponse<T = void> {
  success: boolean;
  data?: T;
  error?: ServiceError;
}

// Define User type locally to avoid import issues
export interface User {
  id: string;
  email: string;
  fullName?: string;
  subscriptionTier: 'free' | 'solo' | 'pro' | 'enterprise';
  apiQuotaUsed: number;
  apiQuotaLimit: number;
  createdAt: Date;
  updatedAt: Date;
}

export type UserId = string;
export type SubscriptionTier = 'free' | 'solo' | 'pro' | 'enterprise';

export interface CreateUserRequest {
  email: string;
  password: string;
  fullName?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface AuthenticatedRequest extends Express.Request {
  user?: User;
}

export interface Logger {
  error(message: string, meta?: Record<string, any>): void;
  warn(message: string, meta?: Record<string, any>): void;
  info(message: string, meta?: Record<string, any>): void;
  debug(message: string, meta?: Record<string, any>): void;
  trace(message: string, meta?: Record<string, any>): void;
}

export interface DatabaseConfig {
  client: string;
  connection: {
    host: string;
    port: number;
    user: string;
    password: string;
    database: string;
  };
  migrations: {
    directory: string;
    extension: string;
  };
  seeds: {
    directory: string;
    extension: string;
  };
  pool: {
    min: number;
    max: number;
  };
}

export interface AppConfig {
  port: number;
  nodeEnv: 'development' | 'production' | 'test';
  corsOrigins: string[];
  jwt: {
    secret: string;
    accessTokenExpiry: string;
    refreshTokenExpiry: string;
  };
  database: DatabaseConfig;
  bcrypt: {
    saltRounds: number;
  };
}