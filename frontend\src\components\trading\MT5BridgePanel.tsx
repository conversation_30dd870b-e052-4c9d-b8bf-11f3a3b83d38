/**
 * MT5 Bridge Panel
 * Interface for interacting with MT5 Bridge
 */

import { useState, useEffect } from 'react';
import { Activity, Link, AlertTriangle, CheckCircle, XCircle, RefreshCw } from 'lucide-react';
import { useApi } from '../../hooks/useApi';
import { apiService } from '../../services/api';

interface MT5BridgeStatus {
  connected: boolean;
  accountInfo?: {
    balance: number;
    equity: number;
    margin: number;
    freeMargin: number;
    leverage: number;
    name: string;
  };
  positions: Array<{
    id: number;
    symbol: string;
    type: string;
    volume: number;
    openPrice: number;
    currentPrice: number;
    profit: number;
    openTime: string;
  }>;
  lastError?: string;
}

interface MT5BridgePanelProps {
  onOrderPlaced?: (orderId: number) => void;
}

export function MT5BridgePanel({ onOrderPlaced }: MT5BridgePanelProps) {
  const [status, setStatus] = useState<MT5BridgeStatus | null>(null);
  const [symbol, setSymbol] = useState('EURUSD');
  const [orderType, setOrderType] = useState('BUY');
  const [volume, setVolume] = useState(0.1);
  const [price, setPrice] = useState<number | null>(null);
  const [stopLoss, setStopLoss] = useState<number | null>(null);
  const [takeProfit, setTakeProfit] = useState<number | null>(null);
  
  const { loading: statusLoading, execute: fetchStatus } = useApi<MT5BridgeStatus>();
  const { loading: connectLoading, execute: executeConnect } = useApi<{ success: boolean }>();
  const { loading: orderLoading, execute: executeOrder } = useApi<{ orderId: number }>();

  // Fetch MT5 Bridge status
  const loadStatus = async () => {
    try {
      const result = await fetchStatus(() => apiService.getMT5Status());
      if (result) {
        setStatus(result);
      }
    } catch (error) {
      console.error('Failed to fetch MT5 status:', error);
    }
  };

  // Connect to MT5
  const handleConnect = async () => {
    try {
      const result = await executeConnect(() => apiService.connectMT5());
      if (result?.success) {
        loadStatus();
      }
    } catch (error) {
      console.error('Failed to connect to MT5:', error);
    }
  };

  // Disconnect from MT5
  const handleDisconnect = async () => {
    try {
      const result = await executeConnect(() => apiService.disconnectMT5());
      if (result?.success) {
        loadStatus();
      }
    } catch (error) {
      console.error('Failed to disconnect from MT5:', error);
    }
  };

  // Place order
  const handlePlaceOrder = async () => {
    try {
      const result = await executeOrder(() => 
        apiService.placeMT5Order({
          symbol,
          orderType,
          volume,
          price: price || undefined,
          stopLoss: stopLoss || undefined,
          takeProfit: takeProfit || undefined
        })
      );
      
      if (result?.orderId) {
        loadStatus();
        if (onOrderPlaced) {
          onOrderPlaced(result.orderId);
        }
      }
    } catch (error) {
      console.error('Failed to place order:', error);
    }
  };

  // Load status on mount
  useEffect(() => {
    loadStatus();
    
    // Refresh status every 10 seconds
    const interval = setInterval(loadStatus, 10000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="bg-white rounded-lg shadow p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Activity className="w-5 h-5 text-blue-600" />
          <h3 className="text-lg font-medium text-gray-900">MT5 Bridge</h3>
        </div>
        
        <div className="flex items-center space-x-2">
          {status?.connected ? (
            <>
              <span className="flex items-center text-green-600 text-sm">
                <CheckCircle className="w-4 h-4 mr-1" />
                Connected
              </span>
              <button 
                onClick={handleDisconnect}
                disabled={connectLoading}
                className="px-3 py-1 text-sm bg-red-100 text-red-700 rounded hover:bg-red-200"
              >
                Disconnect
              </button>
            </>
          ) : (
            <>
              <span className="flex items-center text-red-600 text-sm">
                <XCircle className="w-4 h-4 mr-1" />
                Disconnected
              </span>
              <button 
                onClick={handleConnect}
                disabled={connectLoading}
                className="px-3 py-1 text-sm bg-green-100 text-green-700 rounded hover:bg-green-200"
              >
                Connect
              </button>
            </>
          )}
          
          <button 
            onClick={loadStatus}
            disabled={statusLoading}
            className="p-1 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100"
          >
            <RefreshCw className="w-4 h-4" />
          </button>
        </div>
      </div>
      
      {/* Account Info */}
      {status?.connected && status.accountInfo && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Account Information</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <p className="text-gray-500">Balance</p>
              <p className="font-medium">${status.accountInfo.balance.toLocaleString()}</p>
            </div>
            <div>
              <p className="text-gray-500">Equity</p>
              <p className="font-medium">${status.accountInfo.equity.toLocaleString()}</p>
            </div>
            <div>
              <p className="text-gray-500">Margin</p>
              <p className="font-medium">${status.accountInfo.margin.toLocaleString()}</p>
            </div>
            <div>
              <p className="text-gray-500">Free Margin</p>
              <p className="font-medium">${status.accountInfo.freeMargin.toLocaleString()}</p>
            </div>
          </div>
        </div>
      )}
      
      {/* Order Form */}
      <div className="border-t border-gray-200 pt-4">
        <h4 className="text-sm font-medium text-gray-700 mb-3">Place Order</h4>
        
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {/* Symbol */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Symbol
            </label>
            <select
              value={symbol}
              onChange={(e) => setSymbol(e.target.value)}
              disabled={!status?.connected || orderLoading}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            >
              <option value="EURUSD">EUR/USD</option>
              <option value="GBPUSD">GBP/USD</option>
              <option value="USDJPY">USD/JPY</option>
              <option value="AUDUSD">AUD/USD</option>
              <option value="USDCAD">USD/CAD</option>
            </select>
          </div>
          
          {/* Order Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Order Type
            </label>
            <select
              value={orderType}
              onChange={(e) => setOrderType(e.target.value)}
              disabled={!status?.connected || orderLoading}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            >
              <option value="BUY">Buy</option>
              <option value="SELL">Sell</option>
              <option value="BUY_LIMIT">Buy Limit</option>
              <option value="SELL_LIMIT">Sell Limit</option>
              <option value="BUY_STOP">Buy Stop</option>
              <option value="SELL_STOP">Sell Stop</option>
            </select>
          </div>
          
          {/* Volume */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Volume (Lots)
            </label>
            <input
              type="number"
              value={volume}
              onChange={(e) => setVolume(parseFloat(e.target.value))}
              disabled={!status?.connected || orderLoading}
              min="0.01"
              step="0.01"
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          {/* Price (for pending orders) */}
          {orderType !== 'BUY' && orderType !== 'SELL' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Price
              </label>
              <input
                type="number"
                value={price || ''}
                onChange={(e) => setPrice(e.target.value ? parseFloat(e.target.value) : null)}
                disabled={!status?.connected || orderLoading}
                step="0.00001"
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              />
            </div>
          )}
          
          {/* Stop Loss */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Stop Loss
            </label>
            <input
              type="number"
              value={stopLoss || ''}
              onChange={(e) => setStopLoss(e.target.value ? parseFloat(e.target.value) : null)}
              disabled={!status?.connected || orderLoading}
              step="0.00001"
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          {/* Take Profit */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Take Profit
            </label>
            <input
              type="number"
              value={takeProfit || ''}
              onChange={(e) => setTakeProfit(e.target.value ? parseFloat(e.target.value) : null)}
              disabled={!status?.connected || orderLoading}
              step="0.00001"
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
        
        {/* Submit Button */}
        <div className="mt-4">
          <button
            onClick={handlePlaceOrder}
            disabled={!status?.connected || orderLoading}
            className={`w-full py-2 px-4 rounded-lg font-medium ${
              !status?.connected
                ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {orderLoading ? 'Placing Order...' : 'Place Order'}
          </button>
        </div>
      </div>
      
      {/* Open Positions */}
      {status?.connected && status.positions && status.positions.length > 0 && (
        <div className="border-t border-gray-200 pt-4">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Open Positions</h4>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Symbol
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Volume
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Open Price
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Current Price
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Profit
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {status.positions.map((position) => (
                  <tr key={position.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {position.symbol}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        position.type === 'BUY' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {position.type}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {position.volume}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {position.openPrice.toFixed(5)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {position.currentPrice.toFixed(5)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <span className={position.profit >= 0 ? 'text-green-600' : 'text-red-600'}>
                        {position.profit >= 0 ? '+' : ''}{position.profit.toFixed(2)}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
      
      {/* Error Message */}
      {status?.lastError && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center">
            <AlertTriangle className="w-5 h-5 text-red-500 mr-2" />
            <p className="text-sm text-red-600">{status.lastError}</p>
          </div>
        </div>
      )}
    </div>
  );
}