#!/bin/bash
echo "Starting AI Enhanced Trading Platform MVP..."

# Start the backend server in a new terminal
echo "Starting backend server..."
python backend/minimal_server.py &
BACKEND_PID=$!

# Wait for the backend to start
echo "Waiting for backend to start..."
sleep 3

# Start the frontend in a new terminal
echo "Starting frontend server..."
cd frontend
export VITE_API_URL=http://localhost:8000
npx vite --config vite.config.mvp.ts &
FRONTEND_PID=$!

echo "MVP is running!"
echo "Backend: http://localhost:8000"
echo "Frontend: http://localhost:5173"
echo ""
echo "Press Ctrl+C to stop both servers"

# Wait for user to press Ctrl+C
trap "kill $BACKEND_PID $FRONTEND_PID; exit" INT
wait