// src/components/themes/ProfessionalSinglePage.tsx
import React, { useState, useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { 
  Bot, BarChart3, DollarSign, Target, Activity, Zap, 
  MessageCircle, Send, User, TestTube, Save, Play, 
  Pause, Settings, Brain, Copy, Lightbulb, ChevronDown,
  Shield, Award, Users, TrendingUp, PieChart, LineChart,
  CheckCircle, AlertCircle, Info
} from 'lucide-react';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

export const ProfessionalSinglePage: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([
    { id: '1', role: 'assistant', content: 'Welcome to Darwin Gödel Platform. I\'m here to help you optimize your trading strategies with AI-powered insights.', timestamp: new Date() },
    { id: '2', role: 'assistant', content: 'Our platform maintains 99.9% uptime and is SOC 2 compliant for enterprise security standards.', timestamp: new Date() }
  ]);
  const [inputMessage, setInputMessage] = useState('');

  // Refs for smooth scrolling
  const heroRef = useRef(null);
  const strategiesRef = useRef(null);
  const aiChatRef = useRef(null);
  const metricsRef = useRef(null);

  const heroInView = useInView(heroRef, { once: true });
  const strategiesInView = useInView(strategiesRef, { once: true });
  const aiChatInView = useInView(aiChatRef, { once: true });
  const metricsInView = useInView(metricsRef, { once: true });

  const scrollToSection = (ref: React.RefObject<HTMLElement>) => {
    ref.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = () => {
    if (!inputMessage.trim()) return;

    const newMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: inputMessage,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, newMessage]);
    setInputMessage('');

    // Simulate AI response
    setTimeout(() => {
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: `I've analyzed your query about "${inputMessage}". Based on current market data and risk parameters, I recommend a balanced approach with 15% allocation adjustment.`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, aiResponse]);
    }, 1500);
  };

  const quickPrompts = [
    "Analyze current market volatility",
    "Suggest portfolio rebalancing",
    "Risk assessment for new strategy",
    "Performance optimization recommendations"
  ];

  return (
    <div className="min-h-screen bg-gray-50 text-gray-800">
      {/* Fixed Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 shadow-sm backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                <Bot className="w-5 h-5 text-white" />
              </div>
              <h1 className="text-xl font-bold text-gray-900">Darwin Gödel Platform</h1>
            </div>

            <div className="flex items-center space-x-6">
              <div className="hidden md:flex items-center space-x-4 text-sm text-gray-600">
                <div className="flex items-center">
                  <Shield className="w-4 h-4 mr-1 text-green-600" />
                  SOC 2 Compliant
                </div>
                <div className="flex items-center">
                  <Award className="w-4 h-4 mr-1 text-blue-600" />
                  99.9% Uptime
                </div>
              </div>

              <div className="flex space-x-4 text-sm">
                <button onClick={() => scrollToSection(heroRef)} className="text-gray-600 hover:text-blue-600 transition-colors">
                  Home
                </button>
                <button onClick={() => scrollToSection(strategiesRef)} className="text-gray-600 hover:text-blue-600 transition-colors">
                  Strategies
                </button>
                <button onClick={() => scrollToSection(aiChatRef)} className="text-gray-600 hover:text-blue-600 transition-colors">
                  AI Assistant
                </button>
                <button onClick={() => scrollToSection(metricsRef)} className="text-gray-600 hover:text-blue-600 transition-colors">
                  Analytics
                </button>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section ref={heroRef} className="min-h-screen flex items-center justify-center relative pt-16 bg-gradient-to-br from-blue-50 to-indigo-100">
        <motion.div 
          className="text-center max-w-4xl mx-auto px-4"
          initial={{ opacity: 0, y: 50 }}
          animate={heroInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 1 }}
        >
          <motion.h1 
            className="text-5xl md:text-7xl font-bold text-gray-900 mb-6"
            initial={{ scale: 0.8 }}
            animate={heroInView ? { scale: 1 } : {}}
            transition={{ duration: 1.2, ease: "easeOut" }}
          >
            Enterprise AI Trading
          </motion.h1>

          <motion.p 
            className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto"
            initial={{ opacity: 0 }}
            animate={heroInView ? { opacity: 1 } : {}}
            transition={{ delay: 0.5, duration: 1 }}
          >
            Professional-grade AI-powered trading platform with advanced strategy verification, 
            comprehensive risk management, and enterprise security standards.
          </motion.p>

          <motion.div 
            className="flex flex-col sm:flex-row gap-4 justify-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={heroInView ? { opacity: 1, y: 0 } : {}}
            transition={{ delay: 1, duration: 0.8 }}
          >
            <button 
              onClick={() => scrollToSection(strategiesRef)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              Explore Strategies
            </button>
            <button 
              onClick={() => scrollToSection(aiChatRef)}
              className="bg-white hover:bg-gray-50 text-blue-600 px-8 py-3 rounded-lg font-medium border border-blue-200 transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              Try AI Assistant
            </button>
          </motion.div>

          {/* Trust indicators */}
          <motion.div 
            className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={heroInView ? { opacity: 1, y: 0 } : {}}
            transition={{ delay: 1.2, duration: 0.8 }}
          >
            {[
              { label: 'Test Coverage', value: '95%', icon: CheckCircle },
              { label: 'Active Users', value: '1,247', icon: Users },
              { label: 'Strategies', value: '50+', icon: Target },
              { label: 'Uptime', value: '99.9%', icon: Shield }
            ].map((stat, index) => (
              <div key={stat.label} className="text-center">
                <stat.icon className="w-6 h-6 text-blue-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
                <div className="text-sm text-gray-600">{stat.label}</div>
              </div>
            ))}
          </motion.div>

          <motion.div 
            className="mt-12"
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <ChevronDown className="w-8 h-8 text-blue-600 mx-auto" />
          </motion.div>
        </motion.div>
      </section>

      {/* Strategy Portfolio Section */}
      <section ref={strategiesRef} className="min-h-screen py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: -30 }}
            animate={strategiesInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4 text-center">Strategy Portfolio</h2>
            <p className="text-xl text-gray-600 text-center mb-12 max-w-3xl mx-auto">
              Professionally managed trading strategies with comprehensive risk assessment and performance tracking.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
            {[
              { 
                name: 'Conservative Growth', 
                type: 'Mean Reversion', 
                status: 'Active', 
                performance: '+12.3%', 
                risk: 'Low',
                trades: 156,
                sharpe: '1.8',
                maxDrawdown: '3.2%'
              },
              { 
                name: 'Momentum Capture', 
                type: 'Trend Following', 
                status: 'Active', 
                performance: '+18.7%', 
                risk: 'Medium',
                trades: 89,
                sharpe: '2.1',
                maxDrawdown: '7.8%'
              },
              { 
                name: 'Market Neutral', 
                type: 'Arbitrage', 
                status: 'Paused', 
                performance: '+5.2%', 
                risk: 'Low',
                trades: 234,
                sharpe: '1.4',
                maxDrawdown: '2.1%'
              },
              { 
                name: 'Volatility Harvesting', 
                type: 'Options Strategy', 
                status: 'Active', 
                performance: '+15.4%', 
                risk: 'Medium',
                trades: 67,
                sharpe: '1.9',
                maxDrawdown: '5.6%'
              },
              { 
                name: 'Sector Rotation', 
                type: 'Systematic', 
                status: 'Active', 
                performance: '+22.1%', 
                risk: 'High',
                trades: 45,
                sharpe: '2.3',
                maxDrawdown: '12.4%'
              },
              { 
                name: 'AI Sentiment', 
                type: 'Machine Learning', 
                status: 'Testing', 
                performance: '+8.9%', 
                risk: 'Medium',
                trades: 123,
                sharpe: '1.6',
                maxDrawdown: '6.1%'
              }
            ].map((strategy, index) => (
              <motion.div
                key={strategy.name}
                className="bg-white border border-gray-200 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300"
                initial={{ opacity: 0, y: 30 }}
                animate={strategiesInView ? { opacity: 1, y: 0 } : {}}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                whileHover={{ scale: 1.02 }}
              >
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">{strategy.name}</h3>
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                    strategy.status === 'Active' ? 'bg-green-100 text-green-800' :
                    strategy.status === 'Paused' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-blue-100 text-blue-800'
                  }`}>
                    {strategy.status}
                  </span>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Type:</span>
                    <span className="font-medium">{strategy.type}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Performance:</span>
                    <span className={`font-bold ${strategy.performance.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                      {strategy.performance}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Risk Level:</span>
                    <span className={`font-medium ${
                      strategy.risk === 'Low' ? 'text-green-600' :
                      strategy.risk === 'Medium' ? 'text-yellow-600' :
                      'text-red-600'
                    }`}>
                      {strategy.risk}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Trades:</span>
                    <span className="font-medium">{strategy.trades}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Sharpe Ratio:</span>
                    <span className="font-medium">{strategy.sharpe}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Max Drawdown:</span>
                    <span className="font-medium text-red-600">{strategy.maxDrawdown}</span>
                  </div>
                </div>

                <div className="mt-6 flex space-x-2">
                  <button className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg text-sm font-medium transition-colors">
                    View Details
                  </button>
                  <button className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                    <Settings className="w-4 h-4 text-gray-600" />
                  </button>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* AI Chat Section */}
      <section ref={aiChatRef} className="min-h-screen py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: -30 }}
            animate={aiChatInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4 text-center">AI Trading Assistant</h2>
            <p className="text-xl text-gray-600 text-center mb-12 max-w-3xl mx-auto">
              Get professional trading insights and strategy recommendations from our advanced AI system.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Quick Actions */}
            <motion.div
              className="bg-white rounded-xl p-6 shadow-lg"
              initial={{ opacity: 0, x: -30 }}
              animate={aiChatInView ? { opacity: 1, x: 0 } : {}}
              transition={{ delay: 0.2, duration: 0.8 }}
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Brain className="w-5 h-5 mr-2 text-blue-600" />
                Quick Actions
              </h3>
              <div className="space-y-3">
                {quickPrompts.map((prompt, index) => (
                  <button
                    key={index}
                    onClick={() => setInputMessage(prompt)}
                    className="w-full text-left p-3 bg-gray-50 hover:bg-blue-50 rounded-lg text-sm transition-colors border border-gray-200 hover:border-blue-200"
                  >
                    {prompt}
                  </button>
                ))}
              </div>
            </motion.div>

            {/* Chat Interface */}
            <motion.div
              className="lg:col-span-2 bg-white rounded-xl shadow-lg overflow-hidden"
              initial={{ opacity: 0, y: 30 }}
              animate={aiChatInView ? { opacity: 1, y: 0 } : {}}
              transition={{ delay: 0.4, duration: 0.8 }}
            >
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                  <MessageCircle className="w-5 h-5 mr-2 text-blue-600" />
                  Professional Trading Assistant
                </h3>
              </div>

              <div className="h-96 overflow-y-auto p-6 space-y-4">
                {messages.map((message) => (
                  <div key={message.id} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                      message.role === 'user' 
                        ? 'bg-blue-600 text-white' 
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      <div className="flex items-start space-x-2">
                        <div className={`p-1 rounded-full ${message.role === 'user' ? 'bg-blue-500' : 'bg-gray-200'}`}>
                          {message.role === 'user' ? 
                            <User className="w-3 h-3 text-white" /> : 
                            <Bot className="w-3 h-3 text-gray-600" />
                          }
                        </div>
                        <div className="text-sm">{message.content}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="p-6 border-t border-gray-200">
                <div className="flex space-x-3">
                  <input
                    type="text"
                    value={inputMessage}
                    onChange={(e) => setInputMessage(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                    placeholder="Ask about trading strategies, risk management, or market analysis..."
                    className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <button
                    onClick={handleSendMessage}
                    className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                  >
                    <Send className="w-4 h-4" />
                    <span>Send</span>
                  </button>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Analytics Dashboard */}
      <section ref={metricsRef} className="min-h-screen py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: -30 }}
            animate={metricsInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4 text-center">Performance Analytics</h2>
            <p className="text-xl text-gray-600 text-center mb-12 max-w-3xl mx-auto">
              Comprehensive performance metrics and risk analysis for informed decision making.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {[
              { title: 'Total Portfolio Value', value: '$2,847,392', change: '+12.3%', icon: DollarSign, color: 'text-green-600' },
              { title: 'Monthly Return', value: '8.7%', change: '+2.1%', icon: TrendingUp, color: 'text-blue-600' },
              { title: 'Sharpe Ratio', value: '1.94', change: '+0.3', icon: BarChart3, color: 'text-purple-600' },
              { title: 'Max Drawdown', value: '4.2%', change: '-1.1%', icon: Activity, color: 'text-red-600' }
            ].map((metric, index) => (
              <motion.div
                key={metric.title}
                className="bg-white border border-gray-200 rounded-xl p-6 shadow-lg"
                initial={{ opacity: 0, y: 30 }}
                animate={metricsInView ? { opacity: 1, y: 0 } : {}}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                whileHover={{ scale: 1.02 }}
              >
                <div className="flex items-center justify-between mb-4">
                  <div className={`p-2 rounded-lg bg-gray-100`}>
                    <metric.icon className={`w-6 h-6 ${metric.color}`} />
                  </div>
                  <span className={`text-sm font-medium ${metric.change.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                    {metric.change}
                  </span>
                </div>
                <div className="text-2xl font-bold text-gray-900 mb-1">{metric.value}</div>
                <div className="text-sm text-gray-600">{metric.title}</div>
              </motion.div>
            ))}
          </div>

          {/* System Status */}
          <motion.div
            className="bg-gray-50 rounded-xl p-8"
            initial={{ opacity: 0, y: 30 }}
            animate={metricsInView ? { opacity: 1, y: 0 } : {}}
            transition={{ delay: 0.6, duration: 0.8 }}
          >
            <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">System Status</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">All Systems Operational</h4>
                <p className="text-gray-600">99.9% uptime maintained</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Shield className="w-8 h-8 text-blue-600" />
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">Security Compliant</h4>
                <p className="text-gray-600">SOC 2 Type II certified</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Zap className="w-8 h-8 text-purple-600" />
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">High Performance</h4>
                <p className="text-gray-600">Sub-millisecond execution</p>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex items-center justify-center mb-4">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                <Bot className="w-5 h-5 text-white" />
              </div>
              <h3 className="text-xl font-bold">Darwin Gödel Platform</h3>
            </div>
            <p className="text-gray-400 mb-6">Professional AI-powered trading solutions for institutional and individual investors.</p>
            <div className="flex justify-center space-x-6 text-sm text-gray-400">
              <span>© 2024 Darwin Gödel Platform</span>
              <span>•</span>
              <span>SOC 2 Compliant</span>
              <span>•</span>
              <span>99.9% Uptime SLA</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default ProfessionalSinglePage;