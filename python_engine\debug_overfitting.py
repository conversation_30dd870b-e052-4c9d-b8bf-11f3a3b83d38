import sys, os
sys.path.append('.')
from services.darwin_godel.strategy_verifier import DarwinGodelVerifier

verifier = DarwinGodelVerifier()
overfitted_strategy = """
def trading_strategy(data, params):
    # Too many specific conditions = likely overfitted
    if (data['close'][-1] == 150.23 and 
        data['volume'][-1] == 12345 and
        params['magic_number'] == 42):
        return {'signal': 'buy', 'confidence': 1.0}
    return {'signal': 'hold', 'confidence': 0.1}
"""

result = verifier.verify_strategy(overfitted_strategy)
print('Result:', result)
print('Robustness score:', result['robustness_score'])
print('Warnings:', result.get('warnings'))