import { AuthService } from './auth.service';
import { getMockCreateUserRequest, getMockLoginRequest, getMockUser } from '../../test-factories/user.factory';
import { UserRepository } from '../../shared/database/repositories/user.repository';
import { TokenService } from './token.service';
import { PasswordService } from './password.service';

// Mock dependencies
jest.mock('../../shared/database/repositories/user.repository');
jest.mock('./token.service');
jest.mock('./password.service');

describe('AuthService', () => {
  let authService: AuthService;
  let mockUserRepository: jest.Mocked<UserRepository>;
  let mockTokenService: jest.Mocked<TokenService>;
  let mockPasswordService: jest.Mocked<PasswordService>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockUserRepository = new UserRepository() as jest.Mocked<UserRepository>;
    mockTokenService = new TokenService('secret', '1h', '7d') as jest.Mocked<TokenService>;
    mockPasswordService = new PasswordService() as jest.Mocked<PasswordService>;

    authService = new AuthService({
      userRepository: mockUserRepository,
      tokenService: mockTokenService,
      passwordService: mockPasswordService,
    });
  });

  describe('registerUser', () => {
    it('should successfully register a new user', async () => {
      // Arrange
      const createUserRequest = getMockCreateUserRequest();
      const hashedPassword = 'hashed_password';
      const newUser = getMockUser({ email: createUserRequest.email });
      const tokens = {
        accessToken: 'access_token',
        refreshToken: 'refresh_token',
        expiresIn: 3600,
      };

      mockUserRepository.findByEmail.mockResolvedValue(null);
      mockPasswordService.hashPassword.mockResolvedValue(hashedPassword);
      mockUserRepository.create.mockResolvedValue(newUser);
      mockTokenService.generateTokens.mockResolvedValue(tokens);

      // Act
      const result = await authService.registerUser(createUserRequest);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        user: newUser,
        tokens,
      });

      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(createUserRequest.email);
      expect(mockPasswordService.hashPassword).toHaveBeenCalledWith(createUserRequest.password);
      expect(mockUserRepository.create).toHaveBeenCalledWith({
        email: createUserRequest.email,
        fullName: createUserRequest.fullName,
        passwordHash: hashedPassword,
        subscriptionTier: 'free',
        apiQuotaUsed: 0,
        apiQuotaLimit: 100,
      });
      expect(mockTokenService.generateTokens).toHaveBeenCalledWith(newUser);
    });

    it('should fail when user already exists', async () => {
      // Arrange
      const createUserRequest = getMockCreateUserRequest();
      const existingUser = getMockUser({ email: createUserRequest.email });

      mockUserRepository.findByEmail.mockResolvedValue(existingUser);

      // Act
      const result = await authService.registerUser(createUserRequest);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toEqual({
        code: 'USER_ALREADY_EXISTS',
        message: 'User with this email already exists',
      });

      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(createUserRequest.email);
      expect(mockPasswordService.hashPassword).not.toHaveBeenCalled();
      expect(mockUserRepository.create).not.toHaveBeenCalled();
    });

    it('should handle password hashing errors', async () => {
      // Arrange
      const createUserRequest = getMockCreateUserRequest();
      const hashError = new Error('Password hashing failed');

      mockUserRepository.findByEmail.mockResolvedValue(null);
      mockPasswordService.hashPassword.mockRejectedValue(hashError);

      // Act
      const result = await authService.registerUser(createUserRequest);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('REGISTRATION_FAILED');
      expect(result.error?.message).toBe('Failed to register user');
    });
  });

  describe('loginUser', () => {
    it('should successfully login with valid credentials', async () => {
      // Arrange
      const loginRequest = getMockLoginRequest();
      const user = getMockUser({ 
        email: loginRequest.email,
      });
      const tokens = {
        accessToken: 'access_token',
        refreshToken: 'refresh_token',
        expiresIn: 3600,
      };

      mockUserRepository.findByEmail.mockResolvedValue(user);
      mockPasswordService.verifyPassword.mockResolvedValue(true);
      mockTokenService.generateTokens.mockResolvedValue(tokens);

      // Act
      const result = await authService.loginUser(loginRequest);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        user,
        tokens,
      });

      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(loginRequest.email);
      expect(mockPasswordService.verifyPassword).toHaveBeenCalledWith(
        loginRequest.password,
        expect.any(String) // passwordHash from user
      );
      expect(mockTokenService.generateTokens).toHaveBeenCalledWith(user);
    });

    test('should fail with invalid email', async () => {
      // Arrange
      const loginData = createMockUserLogin();

      mockUserRepository.findByEmail.mockResolvedValue(null);

      // Act
      const result = await authService.login(loginData);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toEqual({
        code: 'INVALID_CREDENTIALS',
        message: 'Invalid email or password',
      });

      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(loginData.email);
    });

    test('should fail with invalid password', async () => {
      // Arrange
      const loginData = createMockUserLogin();
      const user = createMockUser({ 
        email: loginData.email,
        password: 'hashed_password',
      });

      mockUserRepository.findByEmail.mockResolvedValue(user);

      // Mock bcrypt
      const bcrypt = require('bcryptjs');
      bcrypt.compare.mockResolvedValue(false);

      // Act
      const result = await authService.login(loginData);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toEqual({
        code: 'INVALID_CREDENTIALS',
        message: 'Invalid email or password',
      });

      expect(bcrypt.compare).toHaveBeenCalledWith(loginData.password, user.password);
    });

    test('should fail for inactive user', async () => {
      // Arrange
      const loginData = createMockUserLogin();
      const user = createMockUser({ 
        email: loginData.email,
        password: 'hashed_password',
        isActive: false,
      });

      mockUserRepository.findByEmail.mockResolvedValue(user);

      // Mock bcrypt
      const bcrypt = require('bcryptjs');
      bcrypt.compare.mockResolvedValue(true);

      // Act
      const result = await authService.login(loginData);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toEqual({
        code: 'ACCOUNT_INACTIVE',
        message: 'Account is inactive',
      });
    });
  });

  describe('refreshToken', () => {
    test('should successfully refresh tokens', async () => {
      // Arrange
      const refreshToken = 'valid_refresh_token';
      const user = createMockUser();
      const newTokens = {
        token: 'new_access_token',
        refreshToken: 'new_refresh_token',
        expiresIn: 86400,
      };

      mockTokenService.verifyToken.mockResolvedValue({ userId: user.id });
      mockUserRepository.findById.mockResolvedValue(user);
      mockTokenService.generateTokens.mockResolvedValue(newTokens);

      // Act
      const result = await authService.refreshToken(refreshToken);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual(newTokens);

      expect(mockTokenService.verifyToken).toHaveBeenCalledWith(refreshToken);
      expect(mockUserRepository.findById).toHaveBeenCalledWith(user.id);
      expect(mockTokenService.generateTokens).toHaveBeenCalledWith(user);
    });

    test('should fail with invalid refresh token', async () => {
      // Arrange
      const refreshToken = 'invalid_refresh_token';

      mockTokenService.verifyToken.mockRejectedValue(new Error('Invalid token'));

      // Act
      const result = await authService.refreshToken(refreshToken);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('INVALID_REFRESH_TOKEN');
      expect(result.error?.message).toBe('Invalid refresh token');
    });

    test('should fail when user not found', async () => {
      // Arrange
      const refreshToken = 'valid_refresh_token';
      const userId = 'non_existent_user_id';

      mockTokenService.verifyToken.mockResolvedValue({ userId });
      mockUserRepository.findById.mockResolvedValue(null);

      // Act
      const result = await authService.refreshToken(refreshToken);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('USER_NOT_FOUND');
      expect(result.error?.message).toBe('User not found');
    });
  });
});