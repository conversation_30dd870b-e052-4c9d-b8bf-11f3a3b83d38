/**
 * Shared test fixtures for both Jest (TypeScript) and pytest (Python) tests
 * These fixtures ensure consistent test data across both testing frameworks
 */

import {
  OrderRequest,
  OrderResult,
  AccountInfo,
  BacktestConfig,
  BacktestResults,
  ChatMessage,
  ChatSession,
  User,
  TradingEngineRequest,
  PythonBacktestRequest,
  PythonChatRequest,
} from '@/shared/schemas';

// Trading Fixtures
export const TRADING_FIXTURES = {
  // Valid order requests
  VALID_BUY_ORDER: {
    symbol: 'EURUSD',
    volume: 0.01,
    order_type: 'buy',
    price: 1.1000,
    stop_loss: 1.0950,
    take_profit: 1.1050,
  } as OrderRequest,

  VALID_SELL_ORDER: {
    symbol: 'GBPUSD',
    volume: 0.02,
    order_type: 'sell',
    price: 1.2500,
    stop_loss: 1.2550,
    take_profit: 1.2450,
  } as OrderRequest,

  // Invalid order requests (for validation testing)
  INVALID_ORDERS: {
    NEGATIVE_VOLUME: {
      symbol: 'EURUSD',
      volume: -0.01,
      order_type: 'buy',
      price: 1.1000,
    } as OrderRequest,

    ZERO_PRICE: {
      symbol: 'EURUSD',
      volume: 0.01,
      order_type: 'buy',
      price: 0,
    } as OrderRequest,

    INVALID_STOP_LOSS: {
      symbol: 'EURUSD',
      volume: 0.01,
      order_type: 'buy',
      price: 1.1000,
      stop_loss: 1.1050, // Stop loss above entry for buy order
    } as OrderRequest,

    EXCESSIVE_VOLUME: {
      symbol: 'EURUSD',
      volume: 150, // Exceeds maximum
      order_type: 'buy',
      price: 1.1000,
    } as OrderRequest,
  },

  // Order results
  SUCCESSFUL_ORDER_RESULT: {
    success: true,
    order_id: 12345,
  } as OrderResult,

  FAILED_ORDER_RESULT: {
    success: false,
    error: 'Insufficient margin',
  } as OrderResult,

  // Account information
  ACCOUNT_INFO: {
    balance: 10000.50,
    equity: 10050.25,
    margin: 500.00,
    currency: 'USD',
  } as AccountInfo,

  // Market data
  MARKET_DATA: [
    {
      symbol: 'EURUSD',
      timestamp: new Date('2024-01-01T00:00:00Z'),
      open: 1.1000,
      high: 1.1010,
      low: 1.0990,
      close: 1.1005,
      volume: 1000,
    },
    {
      symbol: 'EURUSD',
      timestamp: new Date('2024-01-01T01:00:00Z'),
      open: 1.1005,
      high: 1.1015,
      low: 1.0995,
      close: 1.1010,
      volume: 1200,
    },
  ],

  // Trading engine requests
  GET_ACCOUNT_REQUEST: {
    action: 'get_account',
    timestamp: new Date('2024-01-01T12:00:00Z'),
    request_id: 'test-account-request-123',
  } as TradingEngineRequest,

  SUBMIT_ORDER_REQUEST: {
    action: 'submit_order',
    payload: {
      symbol: 'EURUSD',
      volume: 0.01,
      order_type: 'buy',
      price: 1.1000,
    },
    timestamp: new Date('2024-01-01T12:00:00Z'),
    request_id: 'test-order-request-123',
  } as TradingEngineRequest,
};

// Backtest Fixtures
export const BACKTEST_FIXTURES = {
  // Valid backtest configurations
  SIMPLE_MA_STRATEGY: {
    name: 'Simple Moving Average',
    description: 'Basic SMA crossover strategy',
    symbols: ['EURUSD'],
    start_date: new Date('2024-01-01'),
    end_date: new Date('2024-06-30'),
    initial_balance: 10000,
    strategy: {
      name: 'sma_crossover',
      parameters: {
        fast_period: 10,
        slow_period: 20,
      },
    },
    risk_management: {
      max_risk_per_trade: 0.02,
      max_concurrent_trades: 3,
    },
  } as BacktestConfig,

  MULTI_SYMBOL_STRATEGY: {
    name: 'Multi-Symbol Strategy',
    description: 'Strategy trading multiple currency pairs',
    symbols: ['EURUSD', 'GBPUSD', 'USDJPY'],
    start_date: new Date('2024-01-01'),
    end_date: new Date('2024-12-31'),
    initial_balance: 50000,
    strategy: {
      name: 'rsi_mean_reversion',
      parameters: {
        rsi_period: 14,
        oversold_level: 30,
        overbought_level: 70,
      },
    },
    risk_management: {
      max_risk_per_trade: 0.01,
      max_concurrent_trades: 5,
    },
  } as BacktestConfig,

  // Invalid configurations
  INVALID_CONFIGS: {
    INVALID_DATE_RANGE: {
      name: 'Invalid Dates',
      symbols: ['EURUSD'],
      start_date: new Date('2024-12-31'),
      end_date: new Date('2024-01-01'), // End before start
      strategy: { name: 'test', parameters: {} },
      risk_management: {
        max_risk_per_trade: 0.02,
        max_concurrent_trades: 3,
      },
    } as BacktestConfig,

    LOW_BALANCE: {
      name: 'Low Balance Test',
      symbols: ['EURUSD'],
      start_date: new Date('2024-01-01'),
      end_date: new Date('2024-06-30'),
      initial_balance: 500, // Below minimum
      strategy: { name: 'test', parameters: {} },
      risk_management: {
        max_risk_per_trade: 0.02,
        max_concurrent_trades: 3,
      },
    } as BacktestConfig,

    EXCESSIVE_RISK: {
      name: 'High Risk Test',
      symbols: ['EURUSD'],
      start_date: new Date('2024-01-01'),
      end_date: new Date('2024-06-30'),
      initial_balance: 10000,
      strategy: { name: 'test', parameters: {} },
      risk_management: {
        max_risk_per_trade: 0.15, // 15% risk - too high
        max_concurrent_trades: 3,
      },
    } as BacktestConfig,
  },

  // Backtest results
  SUCCESSFUL_BACKTEST_RESULTS: {
    backtest_id: 'bt_123456789',
    config: {
      name: 'Test Strategy',
      symbols: ['EURUSD'],
      start_date: new Date('2024-01-01'),
      end_date: new Date('2024-06-30'),
    },
    metrics: {
      total_trades: 45,
      winning_trades: 32,
      losing_trades: 13,
      win_rate: 0.711,
      total_pnl: 1250.75,
      max_drawdown: -345.50,
      sharpe_ratio: 1.85,
      profit_factor: 2.3,
      average_win: 67.5,
      average_loss: -42.3,
      largest_win: 156.7,
      largest_loss: -89.2,
    },
    trades: [
      {
        entry_time: new Date('2024-01-15T10:30:00Z'),
        exit_time: new Date('2024-01-15T14:20:00Z'),
        symbol: 'EURUSD',
        side: 'buy',
        volume: 0.01,
        entry_price: 1.1000,
        exit_price: 1.1025,
        pnl: 25.0,
        commission: 0.5,
        swap: 0.0,
      },
    ],
  } as BacktestResults,

  // Python backtest request
  PYTHON_BACKTEST_REQUEST: {
    request_id: 'test-backtest-request-123',
    config: {
      name: 'Test Strategy',
      symbols: ['EURUSD'],
      start_date: new Date('2024-01-01'),
      end_date: new Date('2024-06-30'),
      initial_balance: 10000,
      strategy: {
        name: 'sma_crossover',
        parameters: { fast_period: 10, slow_period: 20 },
      },
      risk_management: {
        max_risk_per_trade: 0.02,
        max_concurrent_trades: 3,
      },
    },
    data: {
      market_data: [
        {
          symbol: 'EURUSD',
          timestamp: new Date('2024-01-01T00:00:00Z'),
          open: 1.1000,
          high: 1.1010,
          low: 1.0990,
          close: 1.1005,
          volume: 1000,
        },
      ],
    },
  } as PythonBacktestRequest,
};

// Chat Fixtures
export const CHAT_FIXTURES = {
  // Chat messages
  USER_MESSAGES: [
    {
      id: 'msg_001',
      role: 'user',
      content: 'What is the current trend for EURUSD?',
      type: 'text',
      metadata: { timestamp: new Date('2024-01-01T12:00:00Z') },
      created_at: new Date('2024-01-01T12:00:00Z'),
    } as ChatMessage,

    {
      id: 'msg_002',
      role: 'user',
      content: 'Should I buy or sell GBPUSD now?',
      type: 'text',
      metadata: { timestamp: new Date('2024-01-01T12:05:00Z') },
      created_at: new Date('2024-01-01T12:05:00Z'),
    } as ChatMessage,

    {
      id: 'msg_003',
      role: 'user',
      content: 'Analyze my recent trading performance',
      type: 'text',
      metadata: { timestamp: new Date('2024-01-01T12:10:00Z') },
      created_at: new Date('2024-01-01T12:10:00Z'),
    } as ChatMessage,
  ],

  ASSISTANT_MESSAGES: [
    {
      id: 'msg_101',
      role: 'assistant',
      content: 'Based on the current technical analysis, EURUSD is showing a bullish trend with strong support at 1.0950 and resistance at 1.1100.',
      type: 'analysis',
      metadata: {
        timestamp: new Date('2024-01-01T12:01:00Z'),
        confidence: 0.85,
        sources: ['Technical Analysis', 'Market Data'],
      },
      created_at: new Date('2024-01-01T12:01:00Z'),
    } as ChatMessage,

    {
      id: 'msg_102',
      role: 'assistant',
      content: 'I recommend waiting for a better entry point. GBPUSD is currently in a consolidation phase. Consider entering after a clear breakout above 1.2550 or below 1.2450.',
      type: 'recommendation',
      metadata: {
        timestamp: new Date('2024-01-01T12:06:00Z'),
        confidence: 0.72,
        sources: ['Price Action', 'Volume Analysis'],
      },
      created_at: new Date('2024-01-01T12:06:00Z'),
    } as ChatMessage,
  ],

  // Chat sessions
  ACTIVE_SESSION: {
    id: 'session_123456',
    user_id: 'user_789',
    title: 'Trading Strategy Discussion',
    context: {
      trading_symbols: ['EURUSD', 'GBPUSD'],
      timeframe: 'H1',
      strategy_focus: 'technical_analysis',
      risk_tolerance: 'medium',
    },
    created_at: new Date('2024-01-01T11:00:00Z'),
    updated_at: new Date('2024-01-01T12:10:00Z'),
    last_activity: new Date('2024-01-01T12:10:00Z'),
  } as ChatSession,

  // Python chat request
  PYTHON_CHAT_REQUEST: {
    request_id: 'test-chat-request-123',
    query: 'What is the current trend for EURUSD?',
    session_id: 'session_123456',
    user_context: {
      user_id: 'user_789',
      trading_data: {
        balance: 10000,
        open_positions: [],
      },
    },
    conversation_history: [
      {
        role: 'user',
        content: 'Hello',
        timestamp: new Date('2024-01-01T11:00:00Z'),
      },
      {
        role: 'assistant',
        content: 'Hello! How can I help you with your trading today?',
        timestamp: new Date('2024-01-01T11:00:30Z'),
      },
    ],
    rag_config: {
      use_knowledge_graph: true,
      use_market_data: true,
      max_context_length: 4000,
    },
  } as PythonChatRequest,
};

// User Fixtures
export const USER_FIXTURES = {
  // Valid users
  FREE_TIER_USER: {
    id: 'user_f47ac10b-58cc-4372-a567-0e02b2c3d479' as any,
    email: '<EMAIL>',
    fullName: 'Free Tier User',
    subscriptionTier: 'free',
    apiQuotaUsed: 25,
    apiQuotaLimit: 100,
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
  } as User,

  PRO_TIER_USER: {
    id: 'user_550e8400-e29b-41d4-a716-446655440000' as any,
    email: '<EMAIL>',
    fullName: 'Pro Tier User',
    subscriptionTier: 'pro',
    apiQuotaUsed: 150,
    apiQuotaLimit: 1000,
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
  } as User,

  ENTERPRISE_USER: {
    id: 'user_6ba7b810-9dad-11d1-80b4-00c04fd430c8' as any,
    email: '<EMAIL>',
    fullName: 'Enterprise User',
    subscriptionTier: 'enterprise',
    apiQuotaUsed: 500,
    apiQuotaLimit: 10000,
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
  } as User,
};

// Error Fixtures
export const ERROR_FIXTURES = {
  NETWORK_ERROR: {
    code: 'NETWORK_ERROR',
    message: 'Network connection failed',
    details: 'Connection timeout after 30 seconds',
  },

  VALIDATION_ERROR: {
    code: 'VALIDATION_ERROR',
    message: 'Input validation failed',
    details: 'Volume must be positive',
  },

  PYTHON_ENGINE_ERROR: {
    code: 'PYTHON_ENGINE_ERROR',
    message: 'Python engine processing failed',
    details: 'Internal server error in trading module',
  },

  AUTHENTICATION_ERROR: {
    code: 'UNAUTHORIZED',
    message: 'Authentication required',
    details: 'Invalid or expired access token',
  },

  RATE_LIMIT_ERROR: {
    code: 'RATE_LIMIT_EXCEEDED',
    message: 'Rate limit exceeded',
    details: 'API quota exhausted for current period',
  },
};

// Time-based fixtures for consistent testing
export const TIME_FIXTURES = {
  FIXED_NOW: new Date('2024-06-15T12:00:00Z'),
  MARKET_OPEN: new Date('2024-06-17T08:00:00Z'), // Monday market open
  MARKET_CLOSE: new Date('2024-06-21T22:00:00Z'), // Friday market close
  WEEKEND: new Date('2024-06-16T12:00:00Z'), // Sunday
};

// Export all fixtures as a single object for easy importing
export const TEST_FIXTURES = {
  TRADING: TRADING_FIXTURES,
  BACKTEST: BACKTEST_FIXTURES,
  CHAT: CHAT_FIXTURES,
  USER: USER_FIXTURES,
  ERROR: ERROR_FIXTURES,
  TIME: TIME_FIXTURES,
};

export default TEST_FIXTURES;