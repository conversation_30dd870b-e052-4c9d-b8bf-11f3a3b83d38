#!/usr/bin/env python3
"""
Darwin Gödel Machine Optimizer Test Runner
Comprehensive test execution with detailed reporting for TDD implementation.
"""

import sys
import os
import subprocess
import time
from datetime import datetime

def run_command(command, description):
    """Run a command and return success status"""
    print(f"\n🔄 {description}")
    print(f"Command: {command}")
    print("-" * 80)
    
    start_time = time.time()
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            cwd=os.path.dirname(os.path.abspath(__file__))
        )
        
        execution_time = time.time() - start_time
        
        if result.returncode == 0:
            print(f"✅ SUCCESS ({execution_time:.2f}s)")
            if result.stdout:
                print("Output:")
                print(result.stdout)
            return True
        else:
            print(f"❌ FAILED ({execution_time:.2f}s)")
            if result.stderr:
                print("Error:")
                print(result.stderr)
            if result.stdout:
                print("Output:")
                print(result.stdout)
            return False
            
    except Exception as e:
        execution_time = time.time() - start_time
        print(f"❌ EXCEPTION ({execution_time:.2f}s): {e}")
        return False


def check_dependencies():
    """Check if required dependencies are installed"""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        'pytest',
        'numpy',
        'pandas'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ Missing packages: {', '.join(missing_packages)}")
        print("Install with: pip install " + " ".join(missing_packages))
        return False
    
    return True


def run_unit_tests():
    """Run unit tests for DGM optimizer"""
    return run_command(
        "python -m pytest tests/test_dgm_optimizer.py -v --tb=short",
        "Running unit tests for Darwin Gödel Machine optimizer"
    )


def run_specific_test_class(class_name):
    """Run specific test class"""
    return run_command(
        f"python -m pytest tests/test_dgm_optimizer.py::{class_name} -v --tb=short",
        f"Running {class_name} tests"
    )


def run_core_component_tests():
    """Run tests for core components"""
    core_classes = [
        "TestParameterSpace",
        "TestIndividual",
        "TestAuditEntry",
        "TestAuditTrail"
    ]
    
    results = []
    for class_name in core_classes:
        success = run_specific_test_class(class_name)
        results.append((class_name, success))
    
    return all(result[1] for result in results)


def run_optimization_tests():
    """Run optimization algorithm tests"""
    optimization_classes = [
        "TestBacktestEngine",
        "TestMovingAverageCrossoverStrategy",
        "TestDarwinGodelMachineOptimizer"
    ]
    
    results = []
    for class_name in optimization_classes:
        success = run_specific_test_class(class_name)
        results.append((class_name, success))
    
    return all(result[1] for result in results)


def run_integration_tests():
    """Run integration tests"""
    return run_command(
        "python -m pytest tests/test_dgm_optimizer.py::TestIntegrationScenarios -v --tb=short",
        "Running integration tests"
    )


def run_demo():
    """Run the DGM optimizer demo"""
    return run_command(
        "python demo_dgm_optimizer.py",
        "Running Darwin Gödel Machine optimizer demo"
    )


def run_performance_tests():
    """Run performance-related tests"""
    performance_tests = [
        "TestDarwinGodelMachineOptimizer::test_parallel_evaluation",
        "TestAuditTrail::test_thread_safety",
        "TestIntegrationScenarios::test_reproducibility_with_seed"
    ]
    
    results = []
    for test in performance_tests:
        success = run_command(
            f"python -m pytest tests/test_dgm_optimizer.py::{test} -v --tb=short",
            f"Running performance test: {test.split('::')[-1]}"
        )
        results.append(success)
    
    return all(results)


def validate_dgm_implementation():
    """Validate DGM implementation"""
    print("\n🧬 Darwin Gödel Machine Implementation Validation")
    print("=" * 80)
    
    try:
        sys.path.append(os.path.join('src', 'optimization'))
        from dgm_optimizer import (
            DarwinGodelMachineOptimizer, OptimizationConfig, AuditTrail,
            MovingAverageCrossoverStrategy, create_dgm_optimizer
        )
        import numpy as np
        import pandas as pd
        
        # Test core components
        print("  🔧 Testing core components...")
        
        # Test audit trail
        with AuditTrail() as audit:
            audit.log_operation("test_operation", generation=1)
            print(f"    ✅ Audit trail: {len(audit.entries)} entries")
            print(f"    ✅ Integrity verified: {audit.verify_integrity()}")
        
        # Test optimizer creation
        config = OptimizationConfig(population_size=5, generations=2)
        optimizer = create_dgm_optimizer(config)
        print(f"    ✅ Optimizer created with config: {config.population_size} pop, {config.generations} gen")
        
        # Test parameter space
        params_space = MovingAverageCrossoverStrategy.get_params_space()
        print(f"    ✅ Parameter space: {len(params_space)} parameters")
        
        # Test with sample data
        np.random.seed(42)
        dates = pd.date_range('2020-01-01', '2020-03-31', freq='D')
        prices = 100 * np.exp(np.cumsum(np.random.normal(0.001, 0.02, len(dates))))
        data = pd.DataFrame({'close': prices}, index=dates)
        
        print(f"    ✅ Sample data created: {data.shape}")
        
        # Quick optimization test
        print("  🚀 Running quick optimization test...")
        result = optimizer.optimize(MovingAverageCrossoverStrategy, data)
        
        print(f"    ✅ Optimization completed: {result.optimization_status.value}")
        print(f"    ✅ Best fitness: {result.best_fitness:.6f}")
        print(f"    ✅ Audit trail: {len(result.audit_trail)} entries")
        print(f"    ✅ Integrity verified: {result.verify_integrity()}")
        
        print("  ✅ DGM implementation validation completed successfully")
        return True
        
    except Exception as e:
        print(f"  ❌ DGM validation failed: {e}")
        return False


def run_code_quality_checks():
    """Run code quality checks if tools are available"""
    print("\n🔍 Code Quality Checks")
    print("=" * 50)
    
    # Check if flake8 is available
    try:
        import flake8
        run_command(
            "python -m flake8 src/optimization/dgm_optimizer.py --max-line-length=100 --ignore=E501,W503",
            "Running flake8 code style check"
        )
    except ImportError:
        print("⚠️ flake8 not available - skipping style check")
    
    # Check if mypy is available
    try:
        import mypy
        run_command(
            "python -m mypy src/optimization/dgm_optimizer.py --ignore-missing-imports",
            "Running mypy type checking"
        )
    except ImportError:
        print("⚠️ mypy not available - skipping type check")


def generate_test_report():
    """Generate comprehensive test report"""
    return run_command(
        "python -m pytest tests/test_dgm_optimizer.py --tb=short --junit-xml=dgm_test_report.xml",
        "Generating comprehensive test report"
    )


def run_coverage_analysis():
    """Run test coverage analysis if available"""
    try:
        import coverage
        return run_command(
            "python -m pytest tests/test_dgm_optimizer.py --cov=src.optimization.dgm_optimizer --cov-report=html --cov-report=term",
            "Running test coverage analysis"
        )
    except ImportError:
        print("⚠️ coverage not available - skipping coverage analysis")
        return True


def main():
    """Main test runner"""
    print("🧬 Darwin Gödel Machine Optimizer - Comprehensive Test Suite")
    print("=" * 80)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Track test results
    test_results = []
    
    # Check dependencies first
    if not check_dependencies():
        print("\n❌ Dependency check failed. Please install missing packages.")
        return 1
    
    # Validate DGM implementation
    dgm_valid = validate_dgm_implementation()
    test_results.append(("DGM Implementation Validation", dgm_valid))
    
    # Run test suites
    test_suites = [
        ("Core Component Tests", run_core_component_tests),
        ("Optimization Algorithm Tests", run_optimization_tests),
        ("Integration Tests", run_integration_tests),
        ("Performance Tests", run_performance_tests),
        ("Unit Tests (Complete)", run_unit_tests),
        ("Demo Execution", run_demo),
        ("Test Report Generation", generate_test_report),
        ("Coverage Analysis", run_coverage_analysis)
    ]
    
    for suite_name, test_function in test_suites:
        success = test_function()
        test_results.append((suite_name, success))
    
    # Run code quality checks (optional)
    run_code_quality_checks()
    
    # Summary report
    print("\n\n📊 Test Execution Summary")
    print("=" * 80)
    
    passed = 0
    failed = 0
    
    for suite_name, success in test_results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{suite_name:<35} {status}")
        if success:
            passed += 1
        else:
            failed += 1
    
    print(f"\nTotal: {len(test_results)} suites")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    
    # DGM features summary
    print(f"\n🧬 Darwin Gödel Machine Features Tested:")
    print("   • Advanced evolutionary optimization algorithms")
    print("   • Cryptographic audit trail generation and verification")
    print("   • Multiple selection methods (Tournament, Roulette, Rank, Elitist)")
    print("   • Genetic operations (Crossover, Mutation) with audit logging")
    print("   • Convergence analysis and early stopping mechanisms")
    print("   • Thread-safe parallel population evaluation")
    print("   • Comprehensive parameter space exploration")
    print("   • Strategy backtesting and fitness evaluation")
    print("   • Reproducible optimization with random seeds")
    print("   • Enterprise-grade error handling and recovery")
    
    if failed == 0:
        print("\n🎉 All tests passed! Darwin Gödel Machine Optimizer is ready for production.")
        print("\n🚀 Key Achievements:")
        print("   ✅ Advanced evolutionary optimization with mathematical rigor")
        print("   ✅ Cryptographic audit trails for complete traceability")
        print("   ✅ Multiple strategy optimization and comparison")
        print("   ✅ Production-ready performance and scalability")
        print("   ✅ Comprehensive TDD test coverage")
        print("   ✅ Enterprise-grade security and compliance features")
        return 0
    else:
        print(f"\n⚠️ {failed} test suite(s) failed. Please review the errors above.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)