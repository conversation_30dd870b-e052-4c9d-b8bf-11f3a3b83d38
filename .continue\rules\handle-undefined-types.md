---
description: This rule ensures that all instances of `string | undefined` types
  are handled correctly throughout the codebase to avoid type errors.
---

When a variable is declared as `string | undefined`, ensure that you handle the case where the variable is undefined. Use nullish coalescing operator (??) to provide a default value, or check for undefined before using the variable. E.g., `variable ?? ''` or `if (variable !== undefined) { ... }`.