"""
Edge Case Generator for Trading Platform Tests

This module provides generators for edge cases in trading data and operations.
"""

import random
from datetime import datetime, timedelta
import string


class EdgeCaseGenerator:
    """Generator for edge cases in trading data"""
    
    @staticmethod
    def extreme_price_movements():
        """Generate extreme price movement scenarios"""
        base_price = 1.0000
        scenarios = [
            # Flash crash (sudden drop)
            [
                {"price": base_price, "time": "09:00:00"},
                {"price": base_price * 0.8, "time": "09:00:01"},
                {"price": base_price * 0.5, "time": "09:00:02"},
                {"price": base_price * 0.3, "time": "09:00:03"},
                {"price": base_price * 0.7, "time": "09:00:10"},
                {"price": base_price * 0.9, "time": "09:01:00"},
            ],
            # Price spike (sudden rise)
            [
                {"price": base_price, "time": "09:00:00"},
                {"price": base_price * 1.2, "time": "09:00:01"},
                {"price": base_price * 1.5, "time": "09:00:02"},
                {"price": base_price * 1.7, "time": "09:00:03"},
                {"price": base_price * 1.3, "time": "09:00:10"},
                {"price": base_price * 1.1, "time": "09:01:00"},
            ],
            # Flat line (no movement)
            [{"price": base_price, "time": f"09:00:{i:02d}"} for i in range(60)],
            # Oscillation (rapid back and forth)
            [
                {"price": base_price * (1 + 0.1 * (-1 if i % 2 else 1)), "time": f"09:00:{i:02d}"}
                for i in range(60)
            ]
        ]
        return scenarios
    
    @staticmethod
    def invalid_orders():
        """Generate invalid order scenarios"""
        return [
            # Empty symbol
            {"symbol": "", "order_type": "BUY", "lot": 0.1, "price": 1.0000},
            # Invalid symbol
            {"symbol": "INVALID", "order_type": "BUY", "lot": 0.1, "price": 1.0000},
            # Zero lot size
            {"symbol": "EURUSD", "order_type": "BUY", "lot": 0, "price": 1.0000},
            # Negative lot size
            {"symbol": "EURUSD", "order_type": "BUY", "lot": -0.1, "price": 1.0000},
            # Invalid order type
            {"symbol": "EURUSD", "order_type": "INVALID", "lot": 0.1, "price": 1.0000},
            # Negative price
            {"symbol": "EURUSD", "order_type": "BUY", "lot": 0.1, "price": -1.0000},
            # Extremely large lot size
            {"symbol": "EURUSD", "order_type": "BUY", "lot": 1000000, "price": 1.0000},
            # Extremely small lot size
            {"symbol": "EURUSD", "order_type": "BUY", "lot": 0.000001, "price": 1.0000},
            # Special characters in symbol
            {"symbol": "EUR/USD", "order_type": "BUY", "lot": 0.1, "price": 1.0000},
            # SQL injection attempt in comment
            {
                "symbol": "EURUSD", 
                "order_type": "BUY", 
                "lot": 0.1, 
                "price": 1.0000,
                "comment": "'; DROP TABLE orders; --"
            },
        ]
    
    @staticmethod
    def connection_scenarios():
        """Generate connection issue scenarios"""
        return [
            # Intermittent connection
            [True, False, True, False, True, True, False, True],
            # Connection timeout
            [True, True, None, None, None, True],
            # Connection refused
            [False, False, False],
            # Delayed response
            ["delay:5", True, "delay:10", True, "delay:30", False],
        ]
    
    @staticmethod
    def market_condition_scenarios():
        """Generate different market condition scenarios"""
        return {
            "normal_hours": {
                "time": "14:00:00",
                "day": "Wednesday",
                "spread": 2,
                "volume": "normal"
            },
            "market_open": {
                "time": "00:00:01",
                "day": "Monday",
                "spread": 5,
                "volume": "high"
            },
            "market_close": {
                "time": "23:59:59",
                "day": "Friday",
                "spread": 8,
                "volume": "high"
            },
            "weekend": {
                "time": "12:00:00",
                "day": "Saturday",
                "spread": None,
                "volume": None
            },
            "high_volatility": {
                "time": "14:30:00",
                "day": "Friday",
                "spread": 10,
                "volume": "very_high"
            },
            "low_liquidity": {
                "time": "03:00:00",
                "day": "Wednesday",
                "spread": 7,
                "volume": "low"
            }
        }
    
    @staticmethod
    def generate_random_string(length=10, include_special=False):
        """Generate a random string of specified length"""
        chars = string.ascii_letters + string.digits
        if include_special:
            chars += string.punctuation
        return ''.join(random.choice(chars) for _ in range(length))
    
    @staticmethod
    def generate_datetime_edge_cases():
        """Generate datetime edge cases"""
        now = datetime.now()
        return [
            # Standard cases
            now,
            # Future
            now + timedelta(days=1),
            now + timedelta(days=365),
            # Past
            now - timedelta(days=1),
            now - timedelta(days=365),
            # Edge of day
            datetime(now.year, now.month, now.day, 0, 0, 0),
            datetime(now.year, now.month, now.day, 23, 59, 59),
            # Weekend
            # Find next Saturday
            now + timedelta(days=(5 - now.weekday()) % 7),
            # Find next Sunday
            now + timedelta(days=(6 - now.weekday()) % 7),
            # Year boundaries
            datetime(now.year, 1, 1, 0, 0, 0),
            datetime(now.year, 12, 31, 23, 59, 59),
            # DST changes (approximate for US)
            datetime(now.year, 3, 14, 2, 0, 0),  # Spring forward
            datetime(now.year, 11, 7, 2, 0, 0),  # Fall back
        ]