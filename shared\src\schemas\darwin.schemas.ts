import { z } from 'zod';

// Evolution Status Enum
export const EvolutionStatusSchema = z.enum([
  'initializing',
  'running', 
  'paused',
  'completed',
  'failed',
  'terminated'
]);
export type EvolutionStatus = z.infer<typeof EvolutionStatusSchema>;

// Fitness Objective Enum
export const FitnessObjectiveSchema = z.enum([
  'sharpe_ratio',
  'profit_factor',
  'win_rate',
  'max_drawdown',
  'custom'
]);
export type FitnessObjective = z.infer<typeof FitnessObjectiveSchema>;

// Trading Condition Schema
export const TradingConditionSchema = z.object({
  indicator: z.string(),
  operator: z.enum(['>', '<', '>=', '<=', '==', 'crossover', 'crossunder']),
  value: z.union([z.number(), z.string()]),
  timeframe: z.string().optional(),
});
export type TradingCondition = z.infer<typeof TradingConditionSchema>;

// Risk Management Schema
export const RiskManagementSchema = z.object({
  stop_loss_pips: z.number().optional(),
  take_profit_pips: z.number().optional(),
  position_size_percent: z.number().min(0).max(1),
  max_daily_loss_percent: z.number().min(0).max(1),
  max_concurrent_trades: z.number().int().positive(),
});
export type RiskManagement = z.infer<typeof RiskManagementSchema>;

// Trading Strategy Schema
export const TradingStrategySchema = z.object({
  id: z.string().uuid(),
  name: z.string(),
  conditions: z.array(TradingConditionSchema),
  risk_management: RiskManagementSchema,
  fitness_score: z.number().optional(),
  is_verified: z.boolean().default(false),
  verification_proof: z.string().optional(),
  generation: z.number().int().nonnegative().optional(),
  parent_ids: z.array(z.string().uuid()).optional(),
  created_at: z.date().default(() => new Date()),
});
export type TradingStrategy = z.infer<typeof TradingStrategySchema>;

// Evolution Parameters Schema
export const EvolutionParametersSchema = z.object({
  population_size: z.number().int().positive().default(50),
  max_generations: z.number().int().positive().default(30),
  mutation_rate: z.number().min(0).max(1).default(0.15),
  crossover_rate: z.number().min(0).max(1).default(0.8),
  fitness_objective: FitnessObjectiveSchema.default('sharpe_ratio'),
  elitism_rate: z.number().min(0).max(1).default(0.1),
  tournament_size: z.number().int().positive().default(3),
  max_strategy_complexity: z.number().int().positive().default(10),
  verification_enabled: z.boolean().default(true),
});
export type EvolutionParameters = z.infer<typeof EvolutionParametersSchema>;

// Evolution State Schema
export const EvolutionStateSchema = z.object({
  job_id: z.string().uuid(),
  status: EvolutionStatusSchema,
  generation: z.number().int().nonnegative(),
  population: z.array(TradingStrategySchema),
  best_fitness: z.number(),
  average_fitness: z.number(),
  verified_count: z.number().int().nonnegative(),
  total_strategies_tested: z.number().int().nonnegative(),
  start_time: z.date(),
  last_update: z.date(),
  estimated_completion: z.date().optional(),
  error_message: z.string().optional(),
});
export type EvolutionState = z.infer<typeof EvolutionStateSchema>;

// Darwin Evolution Request Schema
export const DarwinEvolutionRequestSchema = z.object({
  pair: z.string(),
  timeframe: z.string(),
  evolution_params: EvolutionParametersSchema.optional(),
  data_start_date: z.date().optional(),
  data_end_date: z.date().optional(),
  callback_url: z.string().url().optional(),
});
export type DarwinEvolutionRequest = z.infer<typeof DarwinEvolutionRequestSchema>;

// Darwin Evolution Response Schema
export const DarwinEvolutionResponseSchema = z.object({
  job_id: z.string().uuid(),
  status: z.enum(['started', 'queued', 'failed']),
  message: z.string(),
  estimated_duration_minutes: z.number().optional(),
});
export type DarwinEvolutionResponse = z.infer<typeof DarwinEvolutionResponseSchema>;

// Forex Genome Schema
export const ForexGenomeSchema = z.object({
  pair: z.string(),
  timeframe: z.string(),
  behavioral_patterns: z.record(z.any()),
  volatility_profile: z.object({
    average_volatility: z.number(),
    volatility_clusters: z.array(z.object({
      start_hour: z.number().int().min(0).max(23),
      end_hour: z.number().int().min(0).max(23),
      volatility_multiplier: z.number(),
    })),
  }),
  trend_characteristics: z.object({
    trend_persistence: z.number().min(0).max(1),
    reversal_frequency: z.number(),
    support_resistance_strength: z.number().min(0).max(1),
  }),
  optimal_strategies: z.array(TradingStrategySchema),
  confidence_score: z.number().min(0).max(1),
  generated_at: z.date().default(() => new Date()),
});
export type ForexGenome = z.infer<typeof ForexGenomeSchema>;

// Darwin Job Status Schema
export const DarwinJobStatusSchema = z.object({
  job_id: z.string().uuid(),
  status: EvolutionStatusSchema,
  progress: z.object({
    current_generation: z.number().int().nonnegative(),
    total_generations: z.number().int().positive(),
    completion_percentage: z.number().min(0).max(100),
  }),
  metrics: z.object({
    best_fitness: z.number(),
    average_fitness: z.number(),
    verified_strategies: z.number().int().nonnegative(),
    total_strategies: z.number().int().nonnegative(),
  }),
  runtime_info: z.object({
    start_time: z.date(),
    elapsed_seconds: z.number().nonnegative(),
    estimated_remaining_seconds: z.number().nonnegative().optional(),
  }),
  error: z.string().optional(),
});
export type DarwinJobStatus = z.infer<typeof DarwinJobStatusSchema>;

// Darwin Results Schema
export const DarwinResultsSchema = z.object({
  job_id: z.string().uuid(),
  evolution_params: EvolutionParametersSchema,
  final_state: EvolutionStateSchema,
  best_strategies: z.array(TradingStrategySchema),
  forex_genome: ForexGenomeSchema.optional(),
  performance_summary: z.object({
    total_runtime_seconds: z.number().nonnegative(),
    strategies_evolved: z.number().int().nonnegative(),
    verification_success_rate: z.number().min(0).max(1),
    fitness_improvement: z.number(),
  }),
  completed_at: z.date().default(() => new Date()),
});
export type DarwinResults = z.infer<typeof DarwinResultsSchema>;

// Python Darwin Engine Request Schema (for bridge communication)
export const PythonDarwinRequestSchema = z.object({
  action: z.enum(['start_evolution', 'get_status', 'get_results', 'stop_evolution', 'get_genome']),
  job_id: z.string().uuid().optional(),
  payload: z.record(z.any()).optional(),
  timestamp: z.date().default(() => new Date()),
  request_id: z.string().uuid(),
});
export type PythonDarwinRequest = z.infer<typeof PythonDarwinRequestSchema>;

// Python Darwin Engine Response Schema
export const PythonDarwinResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.string().optional(),
  timestamp: z.date().default(() => new Date()),
  request_id: z.string().uuid(),
});
export type PythonDarwinResponse = z.infer<typeof PythonDarwinResponseSchema>;