import ast
import RestrictedPython
from RestrictedPython import safe_builtins

class InvalidStrategyError(Exception):
    pass

class StrategyBuilder:
    def __init__(self):
        self.allowed_imports = ['numpy', 'pandas', 'ta']  # Technical analysis lib
    
    def generate_strategy(self, natural_language: str) -> str:
        # This would integrate with your LLM/chatbot
        # Simplified example:
        if "RSI" in natural_language:
            return self._generate_rsi_strategy(natural_language)
        elif "moving average" in natural_language.lower():
            return self._generate_ma_strategy(natural_language)
        else:
            raise InvalidStrategyError("Unrecognized strategy pattern")
    
    def validate_strategy(self, code: str) -> bool:
        """Validate strategy code for safety and correctness"""
        try:
            # Syntax check
            ast.parse(code)
            
            # Security check - ensure no dangerous operations
            restricted_globals = safe_builtins.copy()
            restricted_globals['__builtins__'] = safe_builtins
            
            # Execute in restricted environment
            exec(code, restricted_globals)
            
            return True
        except (SyntaxError, ValueError, TypeError) as e:
            raise InvalidStrategyError(f"Invalid strategy code: {e}")
    
    def _generate_rsi_strategy(self, description: str) -> str:
        # Example RSI strategy generator
        return '''
def strategy(data, rsi_period=14, oversold=30, overbought=70):
    """RSI-based mean reversion strategy"""
    from ta.momentum import RSIIndicator
    
    rsi = RSIIndicator(data['close'], window=rsi_period).rsi()
    
    buy_signal = rsi < oversold
    sell_signal = rsi > overbought
    
    return {
        'signals': {
            'buy': buy_signal,
            'sell': sell_signal
        },
        'indicators': {
            'rsi': rsi
        }
    }
'''
    
    def _generate_ma_strategy(self, description: str) -> str:
        # Example moving average strategy generator
        return '''
def strategy(data, window=20):
    """Moving Average crossover strategy"""
    ma = data['close'].rolling(window=window).mean()
    buy_signal = data['close'] > ma
    sell_signal = data['close'] < ma
    return {
        'signals': {
            'buy': buy_signal,
            'sell': sell_signal
        },
        'indicators': {
            'ma': ma
        }
    }
'''
