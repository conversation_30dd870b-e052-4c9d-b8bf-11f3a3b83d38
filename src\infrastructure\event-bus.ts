// src/infrastructure/event-bus.ts

export interface Event {
  type: string;
  payload: any;
  timestamp: Date;
  correlationId: string;
}

export type EventHandler = (event: Event) => Promise<void> | void;

export interface EventStore {
  append(event: Event): Promise<void>;
  getEvents(fromTimestamp?: Date): Promise<Event[]>;
}

/**
 * Event bus for handling domain events
 */
export class EventBus {
  private handlers: Map<string, EventHandler[]> = new Map();
  private eventStore?: EventStore;

  constructor(eventStore?: EventStore) {
    this.eventStore = eventStore;
  }

  /**
   * Subscribe to events of a specific type
   */
  subscribe(eventType: string, handler: EventHandler): void {
    const handlers = this.handlers.get(eventType) || [];
    handlers.push(handler);
    this.handlers.set(eventType, handlers);
  }

  /**
   * Unsubscribe from events
   */
  unsubscribe(eventType: string, handler: EventHandler): void {
    const handlers = this.handlers.get(eventType) || [];
    const index = handlers.indexOf(handler);
    if (index > -1) {
      handlers.splice(index, 1);
      this.handlers.set(eventType, handlers);
    }
  }

  /**
   * Publish an event
   */
  async publish(event: Event): Promise<void> {
    // Store event if event store is available
    if (this.eventStore) {
      await this.eventStore.append(event);
    }

    // Get handlers for this event type
    const handlers = this.handlers.get(event.type) || [];

    // Execute all handlers
    const promises = handlers.map(async (handler) => {
      try {
        await handler(event);
      } catch (error) {
        console.error(`Error in event handler for ${event.type}:`, error);
        // Continue with other handlers even if one fails
      }
    });

    await Promise.all(promises);
  }

  /**
   * Replay events from the event store
   */
  async replay(fromTimestamp?: Date): Promise<void> {
    if (!this.eventStore) {
      throw new Error('Event store not configured');
    }

    const events = await this.eventStore.getEvents(fromTimestamp);
    
    for (const event of events) {
      const handlers = this.handlers.get(event.type) || [];
      
      for (const handler of handlers) {
        try {
          await handler(event);
        } catch (error) {
          console.error(`Error replaying event ${event.type}:`, error);
        }
      }
    }
  }

  /**
   * Get all registered event types
   */
  getEventTypes(): string[] {
    return Array.from(this.handlers.keys());
  }

  /**
   * Clear all handlers
   */
  clear(): void {
    this.handlers.clear();
  }
}

/**
 * Mock event store for testing
 */
export class MockEventStore implements EventStore {
  private events: Event[] = [];

  async append(event: Event): Promise<void> {
    this.events.push(event);
  }

  async getEvents(fromTimestamp?: Date): Promise<Event[]> {
    if (!fromTimestamp) {
      return [...this.events];
    }
    
    return this.events.filter(event => event.timestamp >= fromTimestamp);
  }

  getStoredEvents(): Event[] {
    return [...this.events];
  }

  clear(): void {
    this.events = [];
  }
}