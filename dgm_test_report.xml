<?xml version="1.0" encoding="utf-8"?><testsuites name="pytest tests"><testsuite name="pytest" errors="0" failures="9" skipped="0" tests="53" time="1.442" timestamp="2025-07-08T19:16:14.491688+01:00" hostname="DESKTOP-JOMI8K7"><testcase classname="tests.test_dgm_optimizer.TestParameterSpace" name="test_parameter_space_creation_float" time="0.128" /><testcase classname="tests.test_dgm_optimizer.TestParameterSpace" name="test_parameter_space_creation_int" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestParameterSpace" name="test_parameter_space_creation_categorical" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestParameterSpace" name="test_parameter_space_categorical_without_categories_fails" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestParameterSpace" name="test_generate_random_value_float" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestParameterSpace" name="test_generate_random_value_int" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestParameterSpace" name="test_generate_random_value_categorical" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestParameterSpace" name="test_mutate_value_float" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestParameterSpace" name="test_mutate_value_int" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestParameterSpace" name="test_mutate_value_categorical" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestIndividual" name="test_individual_creation" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestIndividual" name="test_individual_auto_id_generation" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestIndividual" name="test_individual_hash_generation" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestIndividual" name="test_individual_with_parents" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestAuditEntry" name="test_audit_entry_creation" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestAuditEntry" name="test_audit_entry_integrity_verification" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestAuditTrail" name="test_audit_trail_creation" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestAuditTrail" name="test_log_operation" time="0.002" /><testcase classname="tests.test_dgm_optimizer.TestAuditTrail" name="test_log_generation" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestAuditTrail" name="test_log_individual_evaluation" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestAuditTrail" name="test_get_trail" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestAuditTrail" name="test_sha256_hash" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestAuditTrail" name="test_hmac_signature" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestAuditTrail" name="test_verify_integrity" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestAuditTrail" name="test_get_statistics" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestAuditTrail" name="test_context_manager" time="0.002" /><testcase classname="tests.test_dgm_optimizer.TestAuditTrail" name="test_thread_safety" time="0.005" /><testcase classname="tests.test_dgm_optimizer.TestBacktestEngine" name="test_backtest_engine_creation" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestBacktestEngine" name="test_backtest_run_with_strategy" time="0.006" /><testcase classname="tests.test_dgm_optimizer.TestBacktestEngine" name="test_backtest_handles_strategy_errors" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestMovingAverageCrossoverStrategy" name="test_strategy_params_space" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestMovingAverageCrossoverStrategy" name="test_strategy_signal_generation" time="0.003" /><testcase classname="tests.test_dgm_optimizer.TestOptimizationConfig" name="test_default_config" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestOptimizationConfig" name="test_custom_config" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestDarwinGodelMachineOptimizer" name="test_optimizer_creation" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestDarwinGodelMachineOptimizer" name="test_create_initial_population" time="0.002" /><testcase classname="tests.test_dgm_optimizer.TestDarwinGodelMachineOptimizer" name="test_evaluate_individual" time="0.003" /><testcase classname="tests.test_dgm_optimizer.TestDarwinGodelMachineOptimizer" name="test_evaluate_population_sequential" time="0.017" /><testcase classname="tests.test_dgm_optimizer.TestDarwinGodelMachineOptimizer" name="test_tournament_selection" time="0.002" /><testcase classname="tests.test_dgm_optimizer.TestDarwinGodelMachineOptimizer" name="test_crossover" time="0.002"><failure message="TypeError: Individual.__init__() missing 1 required positional argument: 'id'">tests\test_dgm_optimizer.py:678: in test_crossover
    offspring = optimizer._crossover(parent1, parent2, params_space, audit)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src\optimization\dgm_optimizer.py:878: in _crossover
    offspring = Individual(
E   TypeError: Individual.__init__() missing 1 required positional argument: 'id'</failure></testcase><testcase classname="tests.test_dgm_optimizer.TestDarwinGodelMachineOptimizer" name="test_mutation" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestDarwinGodelMachineOptimizer" name="test_full_optimization" time="0.015"><failure message="TypeError: Individual.__init__() missing 1 required positional argument: 'id'">tests\test_dgm_optimizer.py:712: in test_full_optimization
    result = optimizer.optimize(MovingAverageCrossoverStrategy, sample_data)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src\optimization\dgm_optimizer.py:640: in optimize
    population = self._create_next_generation(population, params_space, audit)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src\optimization\dgm_optimizer.py:798: in _create_next_generation
    offspring = self._crossover(parent1, parent2, params_space, audit)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src\optimization\dgm_optimizer.py:878: in _crossover
    offspring = Individual(
E   TypeError: Individual.__init__() missing 1 required positional argument: 'id'</failure></testcase><testcase classname="tests.test_dgm_optimizer.TestDarwinGodelMachineOptimizer" name="test_optimization_with_convergence" time="0.020"><failure message="TypeError: Object of type MutationStrategy is not JSON serializable">tests\test_dgm_optimizer.py:743: in test_optimization_with_convergence
    result = optimizer.optimize(MovingAverageCrossoverStrategy, sample_data)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src\optimization\dgm_optimizer.py:668: in optimize
    verification_hash=audit.sha256_hash(),
                      ^^^^^^^^^^^^^^^^^^^
src\optimization\dgm_optimizer.py:301: in sha256_hash
    trail_json = json.dumps(trail_data, sort_keys=True)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
..\..\AppData\Local\Programs\Python\Python313\Lib\json\__init__.py:238: in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
..\..\AppData\Local\Programs\Python\Python313\Lib\json\encoder.py:200: in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
..\..\AppData\Local\Programs\Python\Python313\Lib\json\encoder.py:261: in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
..\..\AppData\Local\Programs\Python\Python313\Lib\json\encoder.py:180: in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
E   TypeError: Object of type MutationStrategy is not JSON serializable</failure></testcase><testcase classname="tests.test_dgm_optimizer.TestDarwinGodelMachineOptimizer" name="test_optimization_with_stagnation" time="0.020"><failure message="TypeError: Individual.__init__() missing 1 required positional argument: 'id'">tests\test_dgm_optimizer.py:760: in test_optimization_with_stagnation
    result = optimizer.optimize(MovingAverageCrossoverStrategy, sample_data)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src\optimization\dgm_optimizer.py:640: in optimize
    population = self._create_next_generation(population, params_space, audit)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src\optimization\dgm_optimizer.py:798: in _create_next_generation
    offspring = self._crossover(parent1, parent2, params_space, audit)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src\optimization\dgm_optimizer.py:878: in _crossover
    offspring = Individual(
E   TypeError: Individual.__init__() missing 1 required positional argument: 'id'</failure></testcase><testcase classname="tests.test_dgm_optimizer.TestDarwinGodelMachineOptimizer" name="test_parallel_evaluation" time="0.023"><failure message="TypeError: Individual.__init__() missing 1 required positional argument: 'id'">tests\test_dgm_optimizer.py:776: in test_parallel_evaluation
    result = optimizer.optimize(MovingAverageCrossoverStrategy, sample_data)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src\optimization\dgm_optimizer.py:640: in optimize
    population = self._create_next_generation(population, params_space, audit)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src\optimization\dgm_optimizer.py:798: in _create_next_generation
    offspring = self._crossover(parent1, parent2, params_space, audit)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src\optimization\dgm_optimizer.py:878: in _crossover
    offspring = Individual(
E   TypeError: Individual.__init__() missing 1 required positional argument: 'id'</failure></testcase><testcase classname="tests.test_dgm_optimizer.TestDarwinGodelMachineOptimizer" name="test_different_selection_methods" time="0.019"><failure message="TypeError: Individual.__init__() missing 1 required positional argument: 'id'">tests\test_dgm_optimizer.py:799: in test_different_selection_methods
    result = optimizer.optimize(MovingAverageCrossoverStrategy, sample_data)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src\optimization\dgm_optimizer.py:640: in optimize
    population = self._create_next_generation(population, params_space, audit)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src\optimization\dgm_optimizer.py:798: in _create_next_generation
    offspring = self._crossover(parent1, parent2, params_space, audit)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src\optimization\dgm_optimizer.py:878: in _crossover
    offspring = Individual(
E   TypeError: Individual.__init__() missing 1 required positional argument: 'id'</failure></testcase><testcase classname="tests.test_dgm_optimizer.TestOptimizationResult" name="test_optimization_result_creation" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestOptimizationResult" name="test_result_integrity_verification_mock" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestFactoryFunction" name="test_create_dgm_optimizer_default" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestFactoryFunction" name="test_create_dgm_optimizer_with_config" time="0.001" /><testcase classname="tests.test_dgm_optimizer.TestIntegrationScenarios" name="test_complete_optimization_workflow" time="0.037"><failure message="TypeError: Individual.__init__() missing 1 required positional argument: 'id'">tests\test_dgm_optimizer.py:905: in test_complete_optimization_workflow
    result = optimizer.optimize(MovingAverageCrossoverStrategy, integration_data)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src\optimization\dgm_optimizer.py:640: in optimize
    population = self._create_next_generation(population, params_space, audit)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src\optimization\dgm_optimizer.py:798: in _create_next_generation
    offspring = self._crossover(parent1, parent2, params_space, audit)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src\optimization\dgm_optimizer.py:878: in _crossover
    offspring = Individual(
E   TypeError: Individual.__init__() missing 1 required positional argument: 'id'</failure></testcase><testcase classname="tests.test_dgm_optimizer.TestIntegrationScenarios" name="test_optimization_error_handling" time="0.012"><failure message="TypeError: Individual.__init__() missing 1 required positional argument: 'id'">tests\test_dgm_optimizer.py:968: in test_optimization_error_handling
    result = optimizer.optimize(FailingStrategy, integration_data)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src\optimization\dgm_optimizer.py:640: in optimize
    population = self._create_next_generation(population, params_space, audit)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src\optimization\dgm_optimizer.py:798: in _create_next_generation
    offspring = self._crossover(parent1, parent2, params_space, audit)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src\optimization\dgm_optimizer.py:878: in _crossover
    offspring = Individual(
E   TypeError: Individual.__init__() missing 1 required positional argument: 'id'</failure></testcase><testcase classname="tests.test_dgm_optimizer.TestIntegrationScenarios" name="test_reproducibility_with_seed" time="0.024"><failure message="TypeError: Individual.__init__() missing 1 required positional argument: 'id'">tests\test_dgm_optimizer.py:990: in test_reproducibility_with_seed
    result1 = optimizer1.optimize(MovingAverageCrossoverStrategy, integration_data)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src\optimization\dgm_optimizer.py:640: in optimize
    population = self._create_next_generation(population, params_space, audit)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src\optimization\dgm_optimizer.py:800: in _create_next_generation
    offspring = Individual(
E   TypeError: Individual.__init__() missing 1 required positional argument: 'id'</failure></testcase></testsuite></testsuites>