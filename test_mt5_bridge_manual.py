#!/usr/bin/env python
# test_mt5_bridge_manual.py
"""
Manual test script for MT5 Bridge TDD implementation
"""

import sys
import logging
from tests.conftest import MT5BridgeMock

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("mt5_manual_test")

def run_tests():
    """Run manual tests for MT5 Bridge"""
    logger.info("Running manual tests for MT5 Bridge...")
    
    # Create MT5 bridge mock
    bridge = MT5BridgeMock()
    
    # Test 1: Place order success
    logger.info("Test 1: Place order success")
    try:
        order_id = bridge.place_order(
            symbol="EURUSD",
            order_type="buy",
            lot=0.1,
            price=1.1000
        )
        assert order_id == 1, f"Expected order_id 1, got {order_id}"
        logger.info("Test 1: PASSED")
    except Exception as e:
        logger.error(f"Test 1 failed: {str(e)}")
        return 1
    
    # Test 2: Place order with invalid symbol
    logger.info("Test 2: Place order with invalid symbol")
    try:
        try:
            bridge.place_order(
                symbol="INVALID",
                order_type="buy",
                lot=0.1,
                price=1.1000
            )
            logger.error("Test 2 failed: Expected ValueError not raised")
            return 1
        except ValueError as e:
            assert "Invalid symbol" in str(e), f"Expected 'Invalid symbol' in error message, got {str(e)}"
            logger.info("Test 2: PASSED")
    except Exception as e:
        logger.error(f"Test 2 failed: {str(e)}")
        return 1
    
    # Test 3: Auto reconnect on connection loss
    logger.info("Test 3: Auto reconnect on connection loss")
    try:
        # Create a new bridge for this test
        bridge = MT5BridgeMock()
        bridge.simulate_connection_loss()
        assert not bridge.is_connected(), "Expected bridge to be disconnected"
        
        order_id = bridge.place_order("EURUSD", "buy", 0.1, 1.1000)
        assert order_id == 1, f"Expected order_id 1, got {order_id}"
        assert bridge.is_connected(), "Expected bridge to be reconnected"
        logger.info("Test 3: PASSED")
    except Exception as e:
        logger.error(f"Test 3 failed: {str(e)}")
        return 1
    
    # Test 4: Handle API error
    logger.info("Test 4: Handle API error")
    try:
        # Create a new bridge for this test
        bridge = MT5BridgeMock()
        bridge.simulate_api_error()
        
        try:
            bridge.place_order("EURUSD", "buy", 0.1, 1.1000)
            logger.error("Test 4 failed: Expected Exception not raised")
            return 1
        except Exception as e:
            assert "API error" in str(e), f"Expected 'API error' in error message, got {str(e)}"
            logger.info("Test 4: PASSED")
    except Exception as e:
        logger.error(f"Test 4 failed: {str(e)}")
        return 1
    
    logger.info("All tests PASSED!")
    return 0

if __name__ == "__main__":
    sys.exit(run_tests())