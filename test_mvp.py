"""
Simple test script to verify the MVP functionality
"""

import os
import sys
import json
import requests
import logging
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("test_mvp")

# Load environment variables
load_dotenv()

# API base URL
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")

def test_health_check():
    """Test the health check endpoint"""
    try:
        response = requests.get(f"{API_BASE_URL}/health")
        if response.status_code == 200:
            logger.info("✅ Health check passed")
            return True
        else:
            logger.error(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ Health check failed: {str(e)}")
        return False

def test_authentication():
    """Test authentication endpoints"""
    try:
        # Test login
        login_data = {
            "username": "<EMAIL>",
            "password": "user123"
        }
        response = requests.post(
            f"{API_BASE_URL}/api/v1/auth/login", 
            data=login_data
        )
        
        if response.status_code == 200:
            token_data = response.json()
            if "access_token" in token_data:
                logger.info("✅ Authentication test passed")
                return token_data["access_token"]
            else:
                logger.error("❌ Authentication test failed: No access token in response")
                return None
        else:
            logger.error(f"❌ Authentication test failed: {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"❌ Authentication test failed: {str(e)}")
        return None

def test_user_profile(token):
    """Test user profile endpoint"""
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{API_BASE_URL}/api/v1/auth/me", headers=headers)
        
        if response.status_code == 200:
            user_data = response.json()
            logger.info(f"✅ User profile test passed: {user_data}")
            return True
        else:
            logger.error(f"❌ User profile test failed: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ User profile test failed: {str(e)}")
        return False

def main():
    """Main test function"""
    logger.info("Starting MVP tests...")
    
    # Test health check
    if not test_health_check():
        logger.error("Health check failed. Make sure the server is running.")
        return False
    
    # Test authentication
    token = test_authentication()
    if not token:
        logger.error("Authentication test failed. Check your credentials.")
        return False
    
    # Test user profile
    if not test_user_profile(token):
        logger.error("User profile test failed.")
        return False
    
    logger.info("✅ All MVP tests passed!")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)