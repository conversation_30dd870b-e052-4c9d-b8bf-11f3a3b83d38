"""
Strategy Service Implementation with Dependency Injection

Concrete implementation of IStrategyService that integrates with
the existing Darwin-Godel verification system.
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional

from core.interfaces import IStrategyService, ILoggingService, IConfigurationService, TradingSignal
from services.darwin_godel.strategy_verifier import DarwinGodelVerifier
from services.darwin_godel.secure_executor import SecureStrategyExecutor
from services.darwin_godel.pattern_detector import StrategyPatternDetector

class DarwinGodelStrategyService(IStrategyService):
    """Strategy service using Darwin-Godel verification system"""
    
    def __init__(self, 
                 logging_service: ILoggingService,
                 config_service: IConfigurationService):
        self.logger = logging_service
        self.config = config_service
        
        # Initialize Darwin-Godel components
        self.verifier = DarwinGodelVerifier()
        self.executor = SecureStrategyExecutor()
        self.pattern_detector = StrategyPatternDetector()
        
        # Strategy performance tracking
        self._strategy_performance: Dict[str, Dict[str, Any]] = {}
        
        self.logger.log_info("Darwin-Godel strategy service initialized")
    
    async def execute_strategy(self, strategy_code: str, market_data: Dict[str, Any], params: Dict[str, Any]) -> TradingSignal:
        """Execute a trading strategy"""
        try:
            self.logger.log_info("Executing trading strategy")
            
            # Execute strategy using secure executor
            result = self.executor.execute_strategy(strategy_code, market_data, params)
            
            # Validate result format
            if not isinstance(result, dict):
                raise ValueError("Strategy must return a dictionary")
            
            if 'signal' not in result:
                raise ValueError("Strategy result must contain 'signal'")
            
            if 'confidence' not in result:
                result['confidence'] = 0.5  # Default confidence
            
            # Create trading signal
            signal = TradingSignal(
                symbol=params.get('symbol', 'UNKNOWN'),
                signal=result['signal'],
                confidence=float(result['confidence']),
                timestamp=datetime.now(),
                strategy_name=params.get('strategy_name', 'unnamed'),
                metadata=result
            )
            
            # Validate signal
            if signal.signal not in ['buy', 'sell', 'hold']:
                raise ValueError(f"Invalid signal: {signal.signal}")
            
            if not (0.0 <= signal.confidence <= 1.0):
                raise ValueError(f"Invalid confidence: {signal.confidence}")
            
            self.logger.log_info(f"Strategy executed successfully: {signal.signal} with confidence {signal.confidence:.2f}")
            
            return signal
            
        except Exception as e:
            self.logger.log_error(f"Error executing strategy: {e}")
            raise
    
    async def validate_strategy(self, strategy_code: str) -> Dict[str, Any]:
        """Validate strategy code"""
        try:
            self.logger.log_info("Validating strategy code")
            
            # Use Darwin-Godel verifier
            verification_result = self.verifier.verify_strategy(strategy_code)
            
            # Enhance with pattern detection
            pattern_result = self.pattern_detector.analyze_strategy(strategy_code)
            
            # Combine results
            validation_result = {
                'is_valid': verification_result.get('is_valid', False),
                'strategy_type': verification_result.get('strategy_type', 'unknown'),
                'risk_score': verification_result.get('risk_score', 0.5),
                'pattern_type': pattern_result.get('pattern_type', 'unknown'),
                'pattern_confidence': pattern_result.get('confidence', 0.0),
                'indicators_found': pattern_result.get('indicators_found', []),
                'errors': verification_result.get('errors', []),
                'warnings': verification_result.get('warnings', []),
                'complexity_score': verification_result.get('complexity_score', 0.0),
                'security_issues': verification_result.get('security_issues', [])
            }
            
            self.logger.log_info(f"Strategy validation completed: {'VALID' if validation_result['is_valid'] else 'INVALID'}")
            
            return validation_result
            
        except Exception as e:
            self.logger.log_error(f"Error validating strategy: {e}")
            return {
                'is_valid': False,
                'errors': [str(e)],
                'strategy_type': 'unknown',
                'risk_score': 1.0
            }
    
    async def get_strategy_performance(self, strategy_name: str) -> Dict[str, Any]:
        """Get strategy performance metrics"""
        try:
            if strategy_name not in self._strategy_performance:
                return {
                    'strategy_name': strategy_name,
                    'executions': 0,
                    'successful_executions': 0,
                    'failed_executions': 0,
                    'average_execution_time': 0.0,
                    'last_execution': None,
                    'success_rate': 0.0
                }
            
            performance = self._strategy_performance[strategy_name]
            
            return {
                'strategy_name': strategy_name,
                'executions': performance['executions'],
                'successful_executions': performance['successful_executions'],
                'failed_executions': performance['failed_executions'],
                'average_execution_time': performance['total_execution_time'] / performance['executions'] if performance['executions'] > 0 else 0.0,
                'last_execution': performance['last_execution'],
                'success_rate': performance['successful_executions'] / performance['executions'] if performance['executions'] > 0 else 0.0
            }
            
        except Exception as e:
            self.logger.log_error(f"Error getting strategy performance for {strategy_name}: {e}")
            return {'error': str(e)}
    
    def _update_strategy_performance(self, strategy_name: str, execution_time: float, success: bool):
        """Update strategy performance metrics"""
        if strategy_name not in self._strategy_performance:
            self._strategy_performance[strategy_name] = {
                'executions': 0,
                'successful_executions': 0,
                'failed_executions': 0,
                'total_execution_time': 0.0,
                'last_execution': None
            }
        
        performance = self._strategy_performance[strategy_name]
        performance['executions'] += 1
        performance['total_execution_time'] += execution_time
        performance['last_execution'] = datetime.now()
        
        if success:
            performance['successful_executions'] += 1
        else:
            performance['failed_executions'] += 1

class MockStrategyService(IStrategyService):
    """Mock strategy service for testing"""
    
    def __init__(self, 
                 logging_service: ILoggingService,
                 config_service: IConfigurationService):
        self.logger = logging_service
        self.config = config_service
        self._mock_signals: Dict[str, TradingSignal] = {}
        self._mock_validations: Dict[str, Dict[str, Any]] = {}
        self._execution_count = 0
    
    def set_mock_signal(self, strategy_code: str, signal: TradingSignal):
        """Set mock signal for testing"""
        self._mock_signals[strategy_code] = signal
    
    def set_mock_validation(self, strategy_code: str, validation_result: Dict[str, Any]):
        """Set mock validation result for testing"""
        self._mock_validations[strategy_code] = validation_result
    
    async def execute_strategy(self, strategy_code: str, market_data: Dict[str, Any], params: Dict[str, Any]) -> TradingSignal:
        """Execute mock strategy"""
        self._execution_count += 1
        
        if strategy_code in self._mock_signals:
            return self._mock_signals[strategy_code]
        
        # Default mock signal
        return TradingSignal(
            symbol=params.get('symbol', 'TEST'),
            signal='hold',
            confidence=0.5,
            timestamp=datetime.now(),
            strategy_name=params.get('strategy_name', 'mock'),
            metadata={'mock': True, 'execution_count': self._execution_count}
        )
    
    async def validate_strategy(self, strategy_code: str) -> Dict[str, Any]:
        """Validate mock strategy"""
        if strategy_code in self._mock_validations:
            return self._mock_validations[strategy_code]
        
        # Default mock validation
        return {
            'is_valid': True,
            'strategy_type': 'mock',
            'risk_score': 0.1,
            'pattern_type': 'mock_pattern',
            'pattern_confidence': 0.8,
            'indicators_found': ['mock_indicator'],
            'errors': [],
            'warnings': [],
            'complexity_score': 0.2,
            'security_issues': []
        }
    
    async def get_strategy_performance(self, strategy_name: str) -> Dict[str, Any]:
        """Get mock strategy performance"""
        return {
            'strategy_name': strategy_name,
            'executions': self._execution_count,
            'successful_executions': self._execution_count,
            'failed_executions': 0,
            'average_execution_time': 0.001,
            'last_execution': datetime.now(),
            'success_rate': 1.0
        }

class BacktestStrategyService(IStrategyService):
    """Strategy service for backtesting"""
    
    def __init__(self, 
                 logging_service: ILoggingService,
                 config_service: IConfigurationService):
        self.logger = logging_service
        self.config = config_service
        self.verifier = DarwinGodelVerifier()
        self.executor = SecureStrategyExecutor()
        
        # Backtesting specific settings
        self._backtest_mode = True
        self._historical_signals: List[TradingSignal] = []
    
    async def execute_strategy(self, strategy_code: str, market_data: Dict[str, Any], params: Dict[str, Any]) -> TradingSignal:
        """Execute strategy in backtest mode"""
        try:
            # Execute strategy
            result = self.executor.execute_strategy(strategy_code, market_data, params)
            
            # Create signal
            signal = TradingSignal(
                symbol=params.get('symbol', 'BACKTEST'),
                signal=result['signal'],
                confidence=result.get('confidence', 0.5),
                timestamp=params.get('timestamp', datetime.now()),
                strategy_name=params.get('strategy_name', 'backtest'),
                metadata={**result, 'backtest': True}
            )
            
            # Store for backtesting analysis
            self._historical_signals.append(signal)
            
            return signal
            
        except Exception as e:
            self.logger.log_error(f"Error in backtest strategy execution: {e}")
            raise
    
    async def validate_strategy(self, strategy_code: str) -> Dict[str, Any]:
        """Validate strategy for backtesting"""
        return self.verifier.verify_strategy(strategy_code)
    
    async def get_strategy_performance(self, strategy_name: str) -> Dict[str, Any]:
        """Get backtest performance metrics"""
        strategy_signals = [s for s in self._historical_signals if s.strategy_name == strategy_name]
        
        if not strategy_signals:
            return {'error': 'No signals found for strategy'}
        
        # Calculate backtest metrics
        total_signals = len(strategy_signals)
        buy_signals = len([s for s in strategy_signals if s.signal == 'buy'])
        sell_signals = len([s for s in strategy_signals if s.signal == 'sell'])
        hold_signals = len([s for s in strategy_signals if s.signal == 'hold'])
        
        avg_confidence = sum(s.confidence for s in strategy_signals) / total_signals
        
        return {
            'strategy_name': strategy_name,
            'total_signals': total_signals,
            'buy_signals': buy_signals,
            'sell_signals': sell_signals,
            'hold_signals': hold_signals,
            'average_confidence': avg_confidence,
            'signal_distribution': {
                'buy': buy_signals / total_signals,
                'sell': sell_signals / total_signals,
                'hold': hold_signals / total_signals
            },
            'backtest_period': {
                'start': min(s.timestamp for s in strategy_signals),
                'end': max(s.timestamp for s in strategy_signals)
            }
        }
    
    def get_historical_signals(self) -> List[TradingSignal]:
        """Get all historical signals for analysis"""
        return self._historical_signals.copy()
    
    def clear_historical_signals(self):
        """Clear historical signals"""
        self._historical_signals.clear()