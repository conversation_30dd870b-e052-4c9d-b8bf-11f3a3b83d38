import { z } from 'zod';
import { IdSchema } from './common.schemas';
import { TradingSymbolSchema } from './trading.schemas';

// Chat Message Types
export const MessageRoleSchema = z.enum(['user', 'assistant', 'system']);
export type MessageRole = z.infer<typeof MessageRoleSchema>;

export const MessageTypeSchema = z.enum([
  'text', 'analysis', 'prediction', 'strategy', 'market_data', 'error'
]);
export type MessageType = z.infer<typeof MessageTypeSchema>;

// Core Chat Schemas
export const ChatMessageSchema = z.object({
  id: IdSchema,
  role: MessageRoleSchema,
  content: z.string().min(1),
  type: MessageTypeSchema.default('text'),
  metadata: z.object({
    timestamp: z.date(),
    confidence: z.number().min(0).max(1).optional(),
    sources: z.array(z.string()).optional(),
    attachments: z.array(z.object({
      type: z.enum(['chart', 'table', 'document']),
      url: z.string().optional(),
      data: z.any().optional(),
    })).optional(),
  }),
  created_at: z.date(),
});
export type ChatMessage = z.infer<typeof ChatMessageSchema>;

export const ChatSessionSchema = z.object({
  id: IdSchema,
  user_id: IdSchema,
  title: z.string().min(1).max(255),
  context: z.object({
    trading_symbols: z.array(TradingSymbolSchema).optional(),
    timeframe: z.string().optional(),
    strategy_focus: z.string().optional(),
    risk_tolerance: z.enum(['low', 'medium', 'high']).optional(),
  }),
  created_at: z.date(),
  updated_at: z.date(),
  last_activity: z.date(),
});
export type ChatSession = z.infer<typeof ChatSessionSchema>;

// Request/Response Schemas for Python Engine
export const ChatRequestSchema = z.object({
  session_id: IdSchema,
  message: z.string().min(1),
  user_id: IdSchema,
  context: z.object({
    // Trading context
    current_positions: z.array(z.object({
      symbol: TradingSymbolSchema,
      volume: z.number(),
      pnl: z.number(),
    })).optional(),
    
    // Market context
    market_data: z.object({
      symbols: z.array(TradingSymbolSchema).optional(),
      timeframe: z.string().optional(),
    }).optional(),
    
    // User preferences
    preferences: z.object({
      risk_tolerance: z.enum(['low', 'medium', 'high']).optional(),
      preferred_symbols: z.array(TradingSymbolSchema).optional(),
      notification_settings: z.any().optional(),
    }).optional(),
    
    // Previous conversation
    conversation_history: z.array(ChatMessageSchema).optional(),
  }),
  timestamp: z.date().default(() => new Date()),
});
export type ChatRequest = z.infer<typeof ChatRequestSchema>;

export const ChatResponseSchema = z.object({
  message: z.string(),
  type: MessageTypeSchema,
  confidence: z.number().min(0).max(1),
  
  // AI-generated content
  analysis: z.object({
    market_sentiment: z.enum(['bullish', 'bearish', 'neutral']).optional(),
    key_levels: z.array(z.object({
      symbol: TradingSymbolSchema,
      level: z.number(),
      type: z.enum(['support', 'resistance']),
    })).optional(),
    risk_assessment: z.string().optional(),
  }).optional(),
  
  // Suggested actions
  suggested_actions: z.array(z.object({
    type: z.enum(['buy', 'sell', 'hold', 'analyze', 'backtest']),
    symbol: TradingSymbolSchema.optional(),
    reasoning: z.string(),
    confidence: z.number().min(0).max(1),
  })).optional(),
  
  // Knowledge sources
  sources: z.array(z.object({
    type: z.enum(['knowledge_graph', 'market_data', 'historical_analysis']),
    content: z.string(),
    relevance: z.number().min(0).max(1),
  })).optional(),
  
  // Attachments (charts, tables, etc.)
  attachments: z.array(z.object({
    type: z.enum(['chart', 'table', 'document', 'analysis']),
    title: z.string(),
    data: z.any(),
  })).optional(),
  
  timestamp: z.date().default(() => new Date()),
});
export type ChatResponse = z.infer<typeof ChatResponseSchema>;

// Python Engine Integration
export const PythonChatRequestSchema = z.object({
  request_id: z.string().uuid(),
  query: z.string().min(1),
  session_id: IdSchema,
  user_context: z.object({
    user_id: IdSchema,
    trading_data: z.any().optional(),
    preferences: z.any().optional(),
  }),
  conversation_history: z.array(z.object({
    role: MessageRoleSchema,
    content: z.string(),
    timestamp: z.date(),
  })).optional(),
  rag_config: z.object({
    use_knowledge_graph: z.boolean().default(true),
    use_market_data: z.boolean().default(true),
    max_context_length: z.number().int().positive().default(4000),
  }),
});
export type PythonChatRequest = z.infer<typeof PythonChatRequestSchema>;

export const PythonChatResponseSchema = z.object({
  request_id: z.string().uuid(),
  success: z.boolean(),
  response: ChatResponseSchema.optional(),
  error: z.string().optional(),
  processing_time_ms: z.number().nonnegative(),
  tokens_used: z.number().int().nonnegative().optional(),
});
export type PythonChatResponse = z.infer<typeof PythonChatResponseSchema>;

// Knowledge Graph Integration
export const KnowledgeNodeSchema = z.object({
  id: z.string(),
  type: z.enum(['concept', 'strategy', 'indicator', 'market_event']),
  label: z.string(),
  properties: z.record(z.any()),
  confidence: z.number().min(0).max(1),
});
export type KnowledgeNode = z.infer<typeof KnowledgeNodeSchema>;

export const KnowledgeRelationshipSchema = z.object({
  source_id: z.string(),
  target_id: z.string(),
  type: z.string(),
  weight: z.number().min(0).max(1),
  properties: z.record(z.any()).optional(),
});
export type KnowledgeRelationship = z.infer<typeof KnowledgeRelationshipSchema>;

export const KnowledgeQuerySchema = z.object({
  query: z.string().min(1),
  context: z.array(z.string()).optional(),
  max_results: z.number().int().positive().default(10),
  min_confidence: z.number().min(0).max(1).default(0.5),
});
export type KnowledgeQuery = z.infer<typeof KnowledgeQuerySchema>;

export const KnowledgeResultSchema = z.object({
  nodes: z.array(KnowledgeNodeSchema),
  relationships: z.array(KnowledgeRelationshipSchema),
  confidence: z.number().min(0).max(1),
  explanation: z.string().optional(),
});
export type KnowledgeResult = z.infer<typeof KnowledgeResultSchema>;

// ML Prediction Integration
export const MLPredictionRequestSchema = z.object({
  symbol: TradingSymbolSchema,
  prediction_type: z.enum(['price', 'direction', 'volatility']),
  timeframe: z.string(), // e.g., '1h', '1d', '1w'
  features: z.array(z.string()).optional(), // specific features to use
  confidence_threshold: z.number().min(0).max(1).default(0.7),
});
export type MLPredictionRequest = z.infer<typeof MLPredictionRequestSchema>;

export const MLPredictionResponseSchema = z.object({
  symbol: TradingSymbolSchema,
  prediction: z.number(),
  confidence: z.number().min(0).max(1),
  features_used: z.array(z.string()),
  model_info: z.object({
    name: z.string(),
    version: z.string(),
    last_trained: z.date(),
  }),
  explanation: z.string().optional(),
  timestamp: z.date(),
});
export type MLPredictionResponse = z.infer<typeof MLPredictionResponseSchema>;