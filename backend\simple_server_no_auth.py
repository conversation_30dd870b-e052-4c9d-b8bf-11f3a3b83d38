"""
Simple FastAPI server for testing without authentication
"""

import sys
import os
import logging
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("simple_server_no_auth")

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '../src'))

# Import MT5 Bridge
try:
    from trading.mt5_bridge_tdd import MT5Bridge
    logger.info("Successfully imported MT5Bridge")
except ImportError as e:
    logger.error(f"Failed to import MT5Bridge: {e}")
    sys.exit(1)

# Create FastAPI app
app = FastAPI(
    title="AI Enhanced Trading Platform API",
    description="Simple API for testing without authentication",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For development - restrict in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# MT5 Bridge instance (singleton)
_mt5_bridge = MT5Bridge(offline_mode=True)

# Request/Response models
class OrderRequest(BaseModel):
    """Order request model"""
    symbol: str = Field(..., description="Trading symbol (e.g., 'EURUSD')")
    orderType: str = Field(..., description="Order type (BUY, SELL, BUY_LIMIT, etc.)")
    volume: float = Field(..., gt=0, description="Order volume in lots")
    price: Optional[float] = Field(None, description="Order price (for pending orders)")
    stopLoss: Optional[float] = Field(None, description="Stop loss price")
    takeProfit: Optional[float] = Field(None, description="Take profit price")

class OrderResponse(BaseModel):
    """Order response model"""
    orderId: int = Field(..., description="Order ID")
    success: bool = Field(..., description="Success status")

class StatusResponse(BaseModel):
    """Status response model"""
    connected: bool = Field(..., description="Connection status")
    accountInfo: Optional[Dict[str, Any]] = Field(None, description="Account information")
    positions: List[Dict[str, Any]] = Field([], description="Open positions")
    lastError: Optional[str] = Field(None, description="Last error message")

class SuccessResponse(BaseModel):
    """Success response model"""
    success: bool = Field(..., description="Success status")

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "AI Enhanced Trading Platform API",
        "version": "1.0.0"
    }

# MT5 Bridge endpoints
@app.get("/api/v1/mt5/status", response_model=StatusResponse)
async def get_status():
    """Get MT5 Bridge status"""
    try:
        # Get account info if connected
        account_info = {
            "balance": 10000.0,
            "equity": 10050.0,
            "margin": 100.0,
            "freeMargin": 9950.0,
            "leverage": 100,
            "name": "Demo Account"
        }
        
        # Get positions
        positions = _mt5_bridge.get_positions()
        
        # Format positions for response
        formatted_positions = []
        for pos in positions:
            formatted_positions.append({
                "id": pos["id"],
                "symbol": pos["symbol"],
                "type": pos["type"],
                "volume": pos["lot"],
                "openPrice": pos["price"],
                "currentPrice": pos.get("current_price", pos["price"]),  # Might not be available in offline mode
                "profit": pos.get("profit", 0.0),
                "openTime": pos.get("time", "").isoformat() if hasattr(pos.get("time", ""), "isoformat") else pos.get("time", "")
            })
        
        return {
            "connected": _mt5_bridge.is_connected(),
            "accountInfo": account_info,
            "positions": formatted_positions,
            "lastError": None
        }
    except Exception as e:
        logger.error(f"Error getting status: {str(e)}")
        return {
            "connected": _mt5_bridge.is_connected(),
            "accountInfo": None,
            "positions": [],
            "lastError": str(e)
        }

@app.post("/api/v1/mt5/connect", response_model=SuccessResponse)
async def connect():
    """Connect to MT5"""
    try:
        result = _mt5_bridge.connect()
        if not result:
            raise HTTPException(status_code=500, detail="Failed to connect to MT5")
        return {"success": True}
    except Exception as e:
        logger.error(f"Error connecting to MT5: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/mt5/disconnect", response_model=SuccessResponse)
async def disconnect():
    """Disconnect from MT5"""
    try:
        _mt5_bridge.disconnect()
        return {"success": True}
    except Exception as e:
        logger.error(f"Error disconnecting from MT5: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/mt5/order", response_model=OrderResponse)
async def place_order(order: OrderRequest):
    """Place an order"""
    try:
        if not _mt5_bridge.is_connected():
            _mt5_bridge.connect()
            if not _mt5_bridge.is_connected():
                raise HTTPException(status_code=500, detail="Failed to connect to MT5")
        
        order_id = _mt5_bridge.place_order(
            symbol=order.symbol,
            order_type=order.orderType,
            lot=order.volume,
            price=order.price,
            stop_loss=order.stopLoss,
            take_profit=order.takeProfit
        )
        
        return {"orderId": order_id, "success": True}
    except ValueError as e:
        logger.error(f"Invalid order parameters: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error placing order: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/mt5/positions", response_model=List[Dict[str, Any]])
async def get_positions():
    """Get open positions"""
    try:
        if not _mt5_bridge.is_connected():
            _mt5_bridge.connect()
            if not _mt5_bridge.is_connected():
                raise HTTPException(status_code=500, detail="Failed to connect to MT5")
        
        positions = _mt5_bridge.get_positions()
        
        # Format positions for response
        formatted_positions = []
        for pos in positions:
            formatted_positions.append({
                "id": pos["id"],
                "symbol": pos["symbol"],
                "type": pos["type"],
                "volume": pos["lot"],
                "openPrice": pos["price"],
                "currentPrice": pos.get("current_price", pos["price"]),  # Might not be available in offline mode
                "profit": pos.get("profit", 0.0),
                "openTime": pos.get("time", "").isoformat() if hasattr(pos.get("time", ""), "isoformat") else pos.get("time", "")
            })
        
        return formatted_positions
    except Exception as e:
        logger.error(f"Error getting positions: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/mt5/positions/{order_id}/close", response_model=SuccessResponse)
async def close_position(order_id: int):
    """Close a position"""
    try:
        if not _mt5_bridge.is_connected():
            _mt5_bridge.connect()
            if not _mt5_bridge.is_connected():
                raise HTTPException(status_code=500, detail="Failed to connect to MT5")
        
        result = _mt5_bridge.close_order(order_id)
        if not result:
            raise HTTPException(status_code=404, detail=f"Order {order_id} not found or could not be closed")
        
        return {"success": True}
    except Exception as e:
        logger.error(f"Error closing position: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Run the server
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)