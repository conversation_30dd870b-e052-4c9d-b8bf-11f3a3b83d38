import pytest
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import patch, MagicMock

# We need to import the app from your minimal server
# Make sure the path is correct for your project structure
from backend.minimal_server import app

# Use the TestClient to make simulated API requests
client = TestClient(app)

# This is our test for the trade endpoint
@patch('backend.minimal_server.MT5Bridge')  # Patch the MT5Bridge class where it will be used
def test_trade_endpoint_calls_mt5_bridge(mock_mt5_bridge_class):
    """
    RED PHASE: This test checks if the /api/mvp/trade endpoint correctly instantiates 
    the MT5Bridge and calls its place_order method.
    It will fail because the endpoint currently returns mock data.
    """
    # Arrange: Configure the mock so we can check its behavior
    mock_bridge_instance = MagicMock()
    mock_bridge_instance.place_order.return_value = {"status": "success", "ticket": 12345}
    mock_mt5_bridge_class.return_value = mock_bridge_instance
    
    trade_payload = {
        "symbol": "EURUSD",
        "lot": 0.1,
        "order_type": "BUY"
    }
    
    # Act: Make a POST request to the endpoint we want to test
    response = client.post("/api/mvp/trade", json=trade_payload)
    
    # Assert: Verify the endpoint behaved as expected
    # 1. Check that the API returned a success status code
    assert response.status_code == 200
    
    # 2. Check that the MT5Bridge class was instantiated
    mock_mt5_bridge_class.assert_called_once_with(offline_mode=True)
    
    # 3. Check that the place_order method was called with the correct payload
    mock_bridge_instance.place_order.assert_called_once_with(
        symbol="EURUSD",
        lot=0.1,
        order_type="BUY"
    )
    
    # 4. Check that the response from the bridge was returned by the API
    assert response.json() == {"status": "success", "ticket": 12345}