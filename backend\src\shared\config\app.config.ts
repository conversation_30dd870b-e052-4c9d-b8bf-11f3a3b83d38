import { z } from 'zod';
import { AppConfig } from '../types';

const AppConfigSchema = z.object({
  port: z.number().int().min(1).max(65535).default(3000),
  nodeEnv: z.enum(['development', 'production', 'test']).default('development'),
  corsOrigins: z.array(z.string()).default(['http://localhost:3000']),
  jwt: z.object({
    secret: z.string().min(32),
    accessTokenExpiry: z.string().default('1h'),
    refreshTokenExpiry: z.string().default('7d'),
  }),
  database: z.object({
    client: z.string().default('postgresql'),
    connection: z.object({
      host: z.string().default('localhost'),
      port: z.number().int().min(1).max(65535).default(5432),
      user: z.string(),
      password: z.string(),
      database: z.string(),
    }),
    migrations: z.object({
      directory: z.string().default('./migrations'),
      extension: z.string().default('ts'),
    }),
    seeds: z.object({
      directory: z.string().default('./seeds'),
      extension: z.string().default('ts'),
    }),
    pool: z.object({
      min: z.number().int().min(0).default(2),
      max: z.number().int().min(1).default(10),
    }),
  }),
  bcrypt: z.object({
    saltRounds: z.number().int().min(8).max(16).default(12),
  }),
});

function loadConfig(): AppConfig {
  const config = {
    port: parseInt(process.env.PORT ?? '3000'),
    nodeEnv: process.env.NODE_ENV as AppConfig['nodeEnv'] ?? 'development',
    corsOrigins: process.env.CORS_ORIGINS?.split(',') ?? ['http://localhost:3000'],
    jwt: {
      secret: process.env.JWT_SECRET!,
      accessTokenExpiry: process.env.JWT_ACCESS_TOKEN_EXPIRY ?? '1h',
      refreshTokenExpiry: process.env.JWT_REFRESH_TOKEN_EXPIRY ?? '7d',
    },
    database: {
      client: 'postgresql',
      connection: {
        host: process.env.DB_HOST ?? 'localhost',
        port: parseInt(process.env.DB_PORT ?? '5432'),
        user: process.env.DB_USER!,
        password: process.env.DB_PASSWORD!,
        database: process.env.DB_NAME!,
      },
      migrations: {
        directory: './migrations',
        extension: 'ts',
      },
      seeds: {
        directory: './seeds',
        extension: 'ts',
      },
      pool: {
        min: parseInt(process.env.DB_POOL_MIN ?? '2'),
        max: parseInt(process.env.DB_POOL_MAX ?? '10'),
      },
    },
    bcrypt: {
      saltRounds: parseInt(process.env.BCRYPT_SALT_ROUNDS ?? '12'),
    },
  };

  // Validate configuration against schema
  return AppConfigSchema.parse(config);
}

export const appConfig = loadConfig();