#!/usr/bin/env python3
"""
Test script for the Signal Provider API
"""

import requests
import json
import time

def test_signal_provider():
    """Test the signal provider API endpoints"""
    
    base_url = "http://localhost:8002"
    
    print("🧪 Testing Signal Provider API")
    print("=" * 50)
    
    # Test 1: Health check
    print("\n1. Testing Health Check...")
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health Check: {data['status']}")
            print(f"   Service: {data['service']}")
            print(f"   Active Strategies: {data['active_strategies']}")
        else:
            print(f"❌ Health Check Failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Health Check Error: {str(e)}")
        return False
    
    # Test 2: Get live signals
    print("\n2. Testing Live Signals...")
    try:
        response = requests.get(f"{base_url}/api/signals/live", timeout=5)
        if response.status_code == 200:
            signals = response.json()
            print(f"✅ Live Signals: {len(signals)} signals received")
            for i, signal in enumerate(signals[:2]):  # Show first 2 signals
                print(f"   Signal {i+1}: {signal['type']} {signal['symbol']} @ {signal['entry_price']}")
                print(f"   Strategy: {signal['strategy']} | Confidence: {signal['confidence']}%")
        else:
            print(f"❌ Live Signals Failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Live Signals Error: {str(e)}")
    
    # Test 3: Generate new signal
    print("\n3. Testing Signal Generation...")
    try:
        response = requests.post(f"{base_url}/api/signals/generate", 
                               json={}, timeout=5)
        if response.status_code == 200:
            signal = response.json()
            print(f"✅ New Signal Generated:")
            print(f"   {signal['type']} {signal['symbol']} @ {signal['entry_price']}")
            print(f"   SL: {signal['stop_loss']} | TP: {signal['take_profit']}")
            print(f"   Strategy: {signal['strategy']} | Confidence: {signal['confidence']}%")
            print(f"   Analysis: {signal['reasoning']}")
        else:
            print(f"❌ Signal Generation Failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Signal Generation Error: {str(e)}")
    
    # Test 4: Get strategies
    print("\n4. Testing Strategies...")
    try:
        response = requests.get(f"{base_url}/api/strategies", timeout=5)
        if response.status_code == 200:
            strategies = response.json()
            print(f"✅ Strategies: {len(strategies)} strategies available")
            for strategy in strategies:
                perf = strategy['performance']
                print(f"   {strategy['name']}: {perf['win_rate']}% win rate, {perf['monthly_return']}% monthly return")
        else:
            print(f"❌ Strategies Failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Strategies Error: {str(e)}")
    
    # Test 5: Get overall performance
    print("\n5. Testing Overall Performance...")
    try:
        response = requests.get(f"{base_url}/api/performance/overall", timeout=5)
        if response.status_code == 200:
            perf = response.json()
            print(f"✅ Overall Performance:")
            print(f"   Total Signals: {perf['total_signals_generated']}")
            print(f"   Win Rate: {perf['overall_win_rate']}%")
            print(f"   Avg Monthly Return: {perf['average_monthly_return']}%")
            print(f"   Active Strategies: {perf['active_strategies']}")
        else:
            print(f"❌ Performance Failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Performance Error: {str(e)}")
    
    # Test 6: Get disclaimer
    print("\n6. Testing Disclaimer...")
    try:
        response = requests.get(f"{base_url}/api/disclaimer", timeout=5)
        if response.status_code == 200:
            disclaimer = response.json()
            print(f"✅ Disclaimer Retrieved:")
            print(f"   Service Type: {disclaimer['disclaimer']['service_type']}")
            print(f"   Regulatory Compliance: {disclaimer['disclaimer']['regulatory_compliance']}")
        else:
            print(f"❌ Disclaimer Failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Disclaimer Error: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🎉 Signal Provider API Test Complete!")
    print("\n📋 Summary:")
    print("✅ This is a SIGNAL PROVIDER platform")
    print("✅ No trade execution - users trade on their own MT5")
    print("✅ Legally compliant - educational signals only")
    print("✅ No financial services license required")
    print("\n🚀 Ready for production deployment!")
    
    return True

if __name__ == "__main__":
    test_signal_provider()