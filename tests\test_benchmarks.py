#!/usr/bin/env python3
"""
Performance benchmarks for AI Enhanced Trading Platform
Comprehensive benchmarking suite for critical components
"""

import pytest
import pandas as pd
import numpy as np
import time
import sys
import os
from datetime import datetime, timezone, timedelta
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import threading
import multiprocessing

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

# Import test generators
from tests.generators.financial_data import (
    generate_financial_data,
    generate_strategy_parameters,
    generate_trade_sequence
)

# Import system components
try:
    from data.data_loader import (
        ForexDataLoader, DataValidator, DataHashManager, OHLCValidationRules,
        ValidationLevel, DataSource, create_forex_data_loader
    )
    COMPONENTS_AVAILABLE = True
except ImportError:
    COMPONENTS_AVAILABLE = False


class TestDataValidationBenchmarks:
    """Performance benchmarks for data validation components"""
    
    @pytest.fixture(scope="class")
    def validation_datasets(self):
        """Generate datasets of different sizes for benchmarking"""
        datasets = {}
        
        sizes = [100, 500, 1000, 5000, 10000]
        
        for size in sizes:
            # Generate realistic financial data
            dates = pd.date_range('2023-01-01', periods=size, freq='h')
            np.random.seed(42)  # Consistent data for benchmarking
            
            # Generate price data with realistic patterns
            base_price = 1.1000
            returns = np.random.normal(0, 0.01, size)
            prices = base_price * np.exp(np.cumsum(returns))
            
            data = []
            for i, price in enumerate(prices):
                volatility = 0.005
                open_price = price * (1 + np.random.normal(0, volatility))
                close_price = price
                high_price = max(open_price, close_price) * (1 + abs(np.random.normal(0, volatility)))
                low_price = min(open_price, close_price) * (1 - abs(np.random.normal(0, volatility)))
                
                data.append({
                    'open': round(open_price, 5),
                    'high': round(high_price, 5),
                    'low': round(low_price, 5),
                    'close': round(close_price, 5),
                    'volume': int(np.random.lognormal(10, 1))
                })
            
            datasets[size] = pd.DataFrame(data, index=dates)
        
        return datasets
    
    @pytest.mark.benchmark(group="data_validation")
    @pytest.mark.parametrize("data_size", [100, 500, 1000, 5000])
    def test_basic_validation_performance(self, benchmark, validation_datasets, data_size):
        """Benchmark basic validation performance across different data sizes"""
        
        if not COMPONENTS_AVAILABLE:
            pytest.skip("Data validation components not available")
        
        data = validation_datasets[data_size]
        validator = DataValidator(ValidationLevel.BASIC)
        
        # Benchmark the validation
        result = benchmark(validator.validate_data, data, f"BENCHMARK_{data_size}")
        
        # Verify result
        assert result is not None
        assert result.total_records == data_size
        
        # Performance assertions
        stats = benchmark.stats
        mean_time = stats.mean
        
        # Basic validation should be fast
        if data_size <= 1000:
            assert mean_time < 0.01, f"Basic validation too slow for {data_size} records: {mean_time:.4f}s"
        elif data_size <= 5000:
            assert mean_time < 0.05, f"Basic validation too slow for {data_size} records: {mean_time:.4f}s"
    
    @pytest.mark.benchmark(group="data_validation")
    @pytest.mark.parametrize("data_size", [100, 500, 1000])
    def test_strict_validation_performance(self, benchmark, validation_datasets, data_size):
        """Benchmark strict validation performance"""
        
        if not COMPONENTS_AVAILABLE:
            pytest.skip("Data validation components not available")
        
        data = validation_datasets[data_size]
        validator = DataValidator(ValidationLevel.STRICT)
        
        result = benchmark(validator.validate_data, data, f"STRICT_{data_size}")
        
        assert result is not None
        assert result.total_records == data_size
        
        # Strict validation can be slower but should still be reasonable
        stats = benchmark.stats
        mean_time = stats.mean
        
        if data_size <= 1000:
            assert mean_time < 0.05, f"Strict validation too slow for {data_size} records: {mean_time:.4f}s"
    
    @pytest.mark.benchmark(group="cryptographic")
    @pytest.mark.parametrize("data_size", [100, 500, 1000, 5000])
    def test_hash_calculation_performance(self, benchmark, validation_datasets, data_size):
        """Benchmark cryptographic hash calculation performance"""
        
        if not COMPONENTS_AVAILABLE:
            pytest.skip("Hash manager not available")
        
        data = validation_datasets[data_size]
        manager = DataHashManager()
        
        result = benchmark(manager.calculate_data_hash, data)
        
        # Verify result
        assert result is not None
        assert len(result) == 64  # SHA-256 hex length
        
        # Hash calculation should be fast
        stats = benchmark.stats
        mean_time = stats.mean
        
        # Hash should scale reasonably with data size
        expected_max_time = 0.001 + (data_size * 0.000001)  # Linear scaling
        assert mean_time < expected_max_time, f"Hash calculation too slow: {mean_time:.6f}s"
    
    @pytest.mark.benchmark(group="cryptographic")
    @pytest.mark.parametrize("data_size", [100, 500, 1000, 5000])
    def test_hmac_signature_performance(self, benchmark, validation_datasets, data_size):
        """Benchmark HMAC signature calculation performance"""
        
        if not COMPONENTS_AVAILABLE:
            pytest.skip("Hash manager not available")
        
        data = validation_datasets[data_size]
        manager = DataHashManager()
        
        result = benchmark(manager.calculate_hmac_signature, data)
        
        assert result is not None
        assert len(result) == 64
        
        # HMAC should be very fast
        stats = benchmark.stats
        mean_time = stats.mean
        assert mean_time < 0.01, f"HMAC calculation too slow: {mean_time:.6f}s"
    
    @pytest.mark.benchmark(group="cryptographic")
    @pytest.mark.parametrize("data_size", [100, 500, 1000])
    def test_integrity_verification_performance(self, benchmark, validation_datasets, data_size):
        """Benchmark data integrity verification performance"""
        
        if not COMPONENTS_AVAILABLE:
            pytest.skip("Hash manager not available")
        
        data = validation_datasets[data_size]
        manager = DataHashManager()
        
        # Pre-calculate hash and HMAC
        data_hash = manager.calculate_data_hash(data)
        hmac_sig = manager.calculate_hmac_signature(data)
        
        result = benchmark(manager.verify_data_integrity, data, data_hash, hmac_sig)
        
        assert result is True
        
        # Verification should be very fast
        stats = benchmark.stats
        mean_time = stats.mean
        assert mean_time < 0.005, f"Integrity verification too slow: {mean_time:.6f}s"


class TestDataLoadingBenchmarks:
    """Performance benchmarks for data loading operations"""
    
    @pytest.mark.benchmark(group="data_loading")
    def test_single_pair_loading_performance(self, benchmark):
        """Benchmark single currency pair loading performance"""
        
        if not COMPONENTS_AVAILABLE:
            pytest.skip("Data loader not available")
        
        loader = create_forex_data_loader(
            validation_level=ValidationLevel.BASIC,
            data_source=DataSource.MOCK
        )
        
        result = benchmark(loader.load_pair, "EURUSD")
        
        data, report = result
        assert len(data) > 0
        assert report.is_valid()
        
        # Single pair loading should be fast
        stats = benchmark.stats
        mean_time = stats.mean
        assert mean_time < 1.0, f"Single pair loading too slow: {mean_time:.3f}s"
    
    @pytest.mark.benchmark(group="data_loading")
    def test_multiple_pairs_loading_performance(self, benchmark):
        """Benchmark multiple currency pairs loading performance"""
        
        if not COMPONENTS_AVAILABLE:
            pytest.skip("Data loader not available")
        
        loader = create_forex_data_loader(
            validation_level=ValidationLevel.BASIC,
            data_source=DataSource.MOCK
        )
        
        pairs = ["EURUSD", "GBPUSD", "USDJPY"]
        
        result = benchmark(loader.load_multiple_pairs, pairs)
        
        assert len(result) == 3
        for pair, (data, report) in result.items():
            assert len(data) > 0
            assert report.is_valid()
        
        # Multiple pairs should benefit from parallel processing
        stats = benchmark.stats
        mean_time = stats.mean
        assert mean_time < 3.0, f"Multiple pairs loading too slow: {mean_time:.3f}s"
    
    @pytest.mark.benchmark(group="data_loading")
    def test_cached_loading_performance(self, benchmark):
        """Benchmark cached data loading performance"""
        
        if not COMPONENTS_AVAILABLE:
            pytest.skip("Data loader not available")
        
        loader = create_forex_data_loader(
            validation_level=ValidationLevel.BASIC,
            data_source=DataSource.MOCK,
            cache_enabled=True
        )
        
        # Load once to populate cache
        loader.load_pair("EURUSD")
        
        # Benchmark cached loading
        result = benchmark(loader.load_pair, "EURUSD")
        
        data, report = result
        assert len(data) > 0
        
        # Cached loading should be very fast
        stats = benchmark.stats
        mean_time = stats.mean
        assert mean_time < 0.1, f"Cached loading too slow: {mean_time:.3f}s"


class TestConcurrencyBenchmarks:
    """Performance benchmarks for concurrent operations"""
    
    @pytest.mark.benchmark(group="concurrency")
    def test_concurrent_validation_performance(self, benchmark):
        """Benchmark concurrent data validation performance"""
        
        if not COMPONENTS_AVAILABLE:
            pytest.skip("Validation components not available")
        
        # Generate test datasets
        datasets = []
        for i in range(5):
            dates = pd.date_range('2023-01-01', periods=500, freq='h')
            np.random.seed(42 + i)
            
            data = pd.DataFrame({
                'open': np.random.uniform(1.0, 2.0, 500),
                'high': np.random.uniform(1.1, 2.1, 500),
                'low': np.random.uniform(0.9, 1.9, 500),
                'close': np.random.uniform(1.0, 2.0, 500),
                'volume': np.random.randint(1000, 100000, 500)
            }, index=dates)
            
            datasets.append(data)
        
        def validate_concurrent():
            """Validate multiple datasets concurrently"""
            validator = DataValidator(ValidationLevel.STANDARD)
            
            with ThreadPoolExecutor(max_workers=3) as executor:
                futures = []
                for i, data in enumerate(datasets):
                    future = executor.submit(validator.validate_data, data, f"CONCURRENT_{i}")
                    futures.append(future)
                
                results = [future.result() for future in futures]
            
            return results
        
        results = benchmark(validate_concurrent)
        
        assert len(results) == 5
        for result in results:
            assert result is not None
            assert result.total_records == 500
        
        # Concurrent validation should be faster than sequential
        stats = benchmark.stats
        mean_time = stats.mean
        assert mean_time < 0.5, f"Concurrent validation too slow: {mean_time:.3f}s"
    
    @pytest.mark.benchmark(group="concurrency")
    def test_thread_safety_performance(self, benchmark):
        """Benchmark thread safety overhead"""
        
        if not COMPONENTS_AVAILABLE:
            pytest.skip("Components not available")
        
        # Generate test data
        dates = pd.date_range('2023-01-01', periods=1000, freq='h')
        data = pd.DataFrame({
            'open': np.random.uniform(1.0, 2.0, 1000),
            'high': np.random.uniform(1.1, 2.1, 1000),
            'low': np.random.uniform(0.9, 1.9, 1000),
            'close': np.random.uniform(1.0, 2.0, 1000),
            'volume': np.random.randint(1000, 100000, 1000)
        }, index=dates)
        
        validator = DataValidator(ValidationLevel.BASIC)
        
        def thread_safe_validation():
            """Perform validation with thread safety"""
            results = []
            
            def validate_worker():
                result = validator.validate_data(data, "THREAD_SAFE")
                results.append(result)
            
            threads = []
            for _ in range(3):
                thread = threading.Thread(target=validate_worker)
                threads.append(thread)
                thread.start()
            
            for thread in threads:
                thread.join()
            
            return results
        
        results = benchmark(thread_safe_validation)
        
        assert len(results) == 3
        for result in results:
            assert result is not None
            assert result.is_valid()
        
        # Thread safety should not add significant overhead
        stats = benchmark.stats
        mean_time = stats.mean
        assert mean_time < 0.1, f"Thread safety overhead too high: {mean_time:.3f}s"


class TestMemoryBenchmarks:
    """Memory usage benchmarks for large datasets"""
    
    @pytest.mark.benchmark(group="memory")
    @pytest.mark.parametrize("data_size", [1000, 5000, 10000])
    def test_memory_efficient_validation(self, benchmark, data_size):
        """Benchmark memory usage during validation"""
        
        if not COMPONENTS_AVAILABLE:
            pytest.skip("Validation components not available")
        
        # Generate large dataset
        dates = pd.date_range('2023-01-01', periods=data_size, freq='h')
        data = pd.DataFrame({
            'open': np.random.uniform(1.0, 2.0, data_size),
            'high': np.random.uniform(1.1, 2.1, data_size),
            'low': np.random.uniform(0.9, 1.9, data_size),
            'close': np.random.uniform(1.0, 2.0, data_size),
            'volume': np.random.randint(1000, 100000, data_size)
        }, index=dates)
        
        validator = DataValidator(ValidationLevel.STANDARD)
        
        # Monitor memory usage
        import psutil
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        result = benchmark(validator.validate_data, data, f"MEMORY_{data_size}")
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        assert result is not None
        assert result.total_records == data_size
        
        # Memory usage should be reasonable
        max_expected_memory = data_size * 0.001  # 1KB per record max
        assert memory_increase < max_expected_memory, f"Memory usage too high: {memory_increase:.2f}MB"
    
    @pytest.mark.benchmark(group="memory")
    def test_large_dataset_processing(self, benchmark):
        """Benchmark processing of very large datasets"""
        
        if not COMPONENTS_AVAILABLE:
            pytest.skip("Components not available")
        
        # Generate very large dataset (50k records)
        data_size = 50000
        dates = pd.date_range('2023-01-01', periods=data_size, freq='min')
        
        # Generate data in chunks to avoid memory issues
        chunk_size = 10000
        chunks = []
        
        for i in range(0, data_size, chunk_size):
            chunk_end = min(i + chunk_size, data_size)
            chunk_dates = dates[i:chunk_end]
            
            chunk_data = pd.DataFrame({
                'open': np.random.uniform(1.0, 2.0, len(chunk_dates)),
                'high': np.random.uniform(1.1, 2.1, len(chunk_dates)),
                'low': np.random.uniform(0.9, 1.9, len(chunk_dates)),
                'close': np.random.uniform(1.0, 2.0, len(chunk_dates)),
                'volume': np.random.randint(1000, 100000, len(chunk_dates))
            }, index=chunk_dates)
            
            chunks.append(chunk_data)
        
        # Combine chunks
        large_data = pd.concat(chunks)
        
        validator = DataValidator(ValidationLevel.BASIC)
        
        result = benchmark(validator.validate_data, large_data, "LARGE_DATASET")
        
        assert result is not None
        assert result.total_records == data_size
        
        # Large dataset processing should complete in reasonable time
        stats = benchmark.stats
        mean_time = stats.mean
        assert mean_time < 5.0, f"Large dataset processing too slow: {mean_time:.3f}s"


class TestScalabilityBenchmarks:
    """Scalability benchmarks for system components"""
    
    @pytest.mark.benchmark(group="scalability")
    @pytest.mark.parametrize("num_pairs", [1, 5, 10, 20])
    def test_multi_pair_scalability(self, benchmark, num_pairs):
        """Benchmark scalability with increasing number of currency pairs"""
        
        if not COMPONENTS_AVAILABLE:
            pytest.skip("Data loader not available")
        
        # Generate currency pairs
        base_pairs = ["EURUSD", "GBPUSD", "USDJPY", "USDCHF", "AUDUSD", 
                     "USDCAD", "NZDUSD", "EURJPY", "EURGBP", "GBPJPY"]
        pairs = base_pairs[:num_pairs]
        
        loader = create_forex_data_loader(
            validation_level=ValidationLevel.BASIC,
            data_source=DataSource.MOCK
        )
        
        result = benchmark(loader.load_multiple_pairs, pairs)
        
        assert len(result) == num_pairs
        for pair, (data, report) in result.items():
            assert len(data) > 0
            assert report.is_valid()
        
        # Scalability should be roughly linear with parallel processing
        stats = benchmark.stats
        mean_time = stats.mean
        expected_max_time = num_pairs * 0.5  # 0.5s per pair max
        assert mean_time < expected_max_time, f"Scalability issue with {num_pairs} pairs: {mean_time:.3f}s"
    
    @pytest.mark.benchmark(group="scalability")
    @pytest.mark.parametrize("validation_level", [
        ValidationLevel.BASIC,
        ValidationLevel.STANDARD,
        ValidationLevel.STRICT
    ])
    def test_validation_level_scalability(self, benchmark, validation_level):
        """Benchmark scalability across different validation levels"""
        
        if not COMPONENTS_AVAILABLE:
            pytest.skip("Validation components not available")
        
        # Generate consistent test data
        dates = pd.date_range('2023-01-01', periods=2000, freq='h')
        data = pd.DataFrame({
            'open': np.random.uniform(1.0, 2.0, 2000),
            'high': np.random.uniform(1.1, 2.1, 2000),
            'low': np.random.uniform(0.9, 1.9, 2000),
            'close': np.random.uniform(1.0, 2.0, 2000),
            'volume': np.random.randint(1000, 100000, 2000)
        }, index=dates)
        
        validator = DataValidator(validation_level)
        
        result = benchmark(validator.validate_data, data, f"SCALABILITY_{validation_level.value}")
        
        assert result is not None
        assert result.total_records == 2000
        
        # Different validation levels should have predictable performance characteristics
        stats = benchmark.stats
        mean_time = stats.mean
        
        if validation_level == ValidationLevel.BASIC:
            assert mean_time < 0.02, f"Basic validation too slow: {mean_time:.4f}s"
        elif validation_level == ValidationLevel.STANDARD:
            assert mean_time < 0.05, f"Standard validation too slow: {mean_time:.4f}s"
        elif validation_level == ValidationLevel.STRICT:
            assert mean_time < 0.1, f"Strict validation too slow: {mean_time:.4f}s"


class TestRegressionBenchmarks:
    """Regression benchmarks to detect performance degradation"""
    
    @pytest.mark.benchmark(group="regression")
    def test_baseline_validation_performance(self, benchmark):
        """Establish baseline performance for validation operations"""
        
        if not COMPONENTS_AVAILABLE:
            pytest.skip("Validation components not available")
        
        # Standard test dataset
        dates = pd.date_range('2023-01-01', periods=1000, freq='h')
        data = pd.DataFrame({
            'open': np.random.uniform(1.0, 2.0, 1000),
            'high': np.random.uniform(1.1, 2.1, 1000),
            'low': np.random.uniform(0.9, 1.9, 1000),
            'close': np.random.uniform(1.0, 2.0, 1000),
            'volume': np.random.randint(1000, 100000, 1000)
        }, index=dates)
        
        validator = DataValidator(ValidationLevel.STANDARD)
        
        result = benchmark(validator.validate_data, data, "BASELINE")
        
        assert result is not None
        assert result.is_valid()
        
        # Store baseline performance for comparison
        stats = benchmark.stats
        baseline_time = stats.mean
        
        # Baseline should be under 50ms for 1000 records
        assert baseline_time < 0.05, f"Baseline performance regression: {baseline_time:.4f}s"
    
    @pytest.mark.benchmark(group="regression")
    def test_baseline_hash_performance(self, benchmark):
        """Establish baseline performance for cryptographic operations"""
        
        if not COMPONENTS_AVAILABLE:
            pytest.skip("Hash manager not available")
        
        # Standard test dataset
        dates = pd.date_range('2023-01-01', periods=5000, freq='h')
        data = pd.DataFrame({
            'open': np.random.uniform(1.0, 2.0, 5000),
            'high': np.random.uniform(1.1, 2.1, 5000),
            'low': np.random.uniform(0.9, 1.9, 5000),
            'close': np.random.uniform(1.0, 2.0, 5000),
            'volume': np.random.randint(1000, 100000, 5000)
        }, index=dates)
        
        manager = DataHashManager()
        
        result = benchmark(manager.calculate_data_hash, data)
        
        assert len(result) == 64
        
        # Hash calculation should be very fast
        stats = benchmark.stats
        baseline_time = stats.mean
        assert baseline_time < 0.01, f"Hash performance regression: {baseline_time:.6f}s"


if __name__ == "__main__":
    # Run benchmarks
    pytest.main([
        __file__, 
        "-v", 
        "--benchmark-only",
        "--benchmark-sort=mean",
        "--benchmark-group-by=group",
        "--benchmark-columns=min,max,mean,stddev,rounds,iterations",
        "--benchmark-histogram"
    ])