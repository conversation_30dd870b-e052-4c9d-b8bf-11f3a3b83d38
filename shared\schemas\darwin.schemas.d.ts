import { z } from 'zod';
export declare const EvolutionStatusSchema: z.Z<PERSON><["initializing", "running", "paused", "completed", "failed", "terminated"]>;
export type EvolutionStatus = z.infer<typeof EvolutionStatusSchema>;
export declare const FitnessObjectiveSchema: z.<PERSON><["sharpe_ratio", "profit_factor", "win_rate", "max_drawdown", "custom"]>;
export type FitnessObjective = z.infer<typeof FitnessObjectiveSchema>;
export declare const TradingConditionSchema: z.ZodObject<{
    indicator: z.ZodString;
    operator: z.ZodEnum<[">", "<", ">=", "<=", "==", "crossover", "crossunder"]>;
    value: z.<PERSON>n<[z.Zod<PERSON>, z.ZodString]>;
    timeframe: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    value: string | number;
    indicator: string;
    operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
    timeframe?: string | undefined;
}, {
    value: string | number;
    indicator: string;
    operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
    timeframe?: string | undefined;
}>;
export type TradingCondition = z.infer<typeof TradingConditionSchema>;
export declare const RiskManagementSchema: z.ZodObject<{
    stop_loss_pips: z.ZodOptional<z.ZodNumber>;
    take_profit_pips: z.ZodOptional<z.ZodNumber>;
    position_size_percent: z.ZodNumber;
    max_daily_loss_percent: z.ZodNumber;
    max_concurrent_trades: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    max_concurrent_trades: number;
    position_size_percent: number;
    max_daily_loss_percent: number;
    stop_loss_pips?: number | undefined;
    take_profit_pips?: number | undefined;
}, {
    max_concurrent_trades: number;
    position_size_percent: number;
    max_daily_loss_percent: number;
    stop_loss_pips?: number | undefined;
    take_profit_pips?: number | undefined;
}>;
export type RiskManagement = z.infer<typeof RiskManagementSchema>;
export declare const TradingStrategySchema: z.ZodObject<{
    id: z.ZodString;
    name: z.ZodString;
    conditions: z.ZodArray<z.ZodObject<{
        indicator: z.ZodString;
        operator: z.ZodEnum<[">", "<", ">=", "<=", "==", "crossover", "crossunder"]>;
        value: z.ZodUnion<[z.ZodNumber, z.ZodString]>;
        timeframe: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        value: string | number;
        indicator: string;
        operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
        timeframe?: string | undefined;
    }, {
        value: string | number;
        indicator: string;
        operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
        timeframe?: string | undefined;
    }>, "many">;
    risk_management: z.ZodObject<{
        stop_loss_pips: z.ZodOptional<z.ZodNumber>;
        take_profit_pips: z.ZodOptional<z.ZodNumber>;
        position_size_percent: z.ZodNumber;
        max_daily_loss_percent: z.ZodNumber;
        max_concurrent_trades: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        max_concurrent_trades: number;
        position_size_percent: number;
        max_daily_loss_percent: number;
        stop_loss_pips?: number | undefined;
        take_profit_pips?: number | undefined;
    }, {
        max_concurrent_trades: number;
        position_size_percent: number;
        max_daily_loss_percent: number;
        stop_loss_pips?: number | undefined;
        take_profit_pips?: number | undefined;
    }>;
    fitness_score: z.ZodOptional<z.ZodNumber>;
    is_verified: z.ZodDefault<z.ZodBoolean>;
    verification_proof: z.ZodOptional<z.ZodString>;
    generation: z.ZodOptional<z.ZodNumber>;
    parent_ids: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    created_at: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    name: string;
    id: string;
    conditions: {
        value: string | number;
        indicator: string;
        operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
        timeframe?: string | undefined;
    }[];
    created_at: Date;
    risk_management: {
        max_concurrent_trades: number;
        position_size_percent: number;
        max_daily_loss_percent: number;
        stop_loss_pips?: number | undefined;
        take_profit_pips?: number | undefined;
    };
    is_verified: boolean;
    generation?: number | undefined;
    fitness_score?: number | undefined;
    verification_proof?: string | undefined;
    parent_ids?: string[] | undefined;
}, {
    name: string;
    id: string;
    conditions: {
        value: string | number;
        indicator: string;
        operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
        timeframe?: string | undefined;
    }[];
    risk_management: {
        max_concurrent_trades: number;
        position_size_percent: number;
        max_daily_loss_percent: number;
        stop_loss_pips?: number | undefined;
        take_profit_pips?: number | undefined;
    };
    generation?: number | undefined;
    created_at?: Date | undefined;
    fitness_score?: number | undefined;
    is_verified?: boolean | undefined;
    verification_proof?: string | undefined;
    parent_ids?: string[] | undefined;
}>;
export type TradingStrategy = z.infer<typeof TradingStrategySchema>;
export declare const EvolutionParametersSchema: z.ZodObject<{
    population_size: z.ZodDefault<z.ZodNumber>;
    max_generations: z.ZodDefault<z.ZodNumber>;
    mutation_rate: z.ZodDefault<z.ZodNumber>;
    crossover_rate: z.ZodDefault<z.ZodNumber>;
    fitness_objective: z.ZodDefault<z.ZodEnum<["sharpe_ratio", "profit_factor", "win_rate", "max_drawdown", "custom"]>>;
    elitism_rate: z.ZodDefault<z.ZodNumber>;
    tournament_size: z.ZodDefault<z.ZodNumber>;
    max_strategy_complexity: z.ZodDefault<z.ZodNumber>;
    verification_enabled: z.ZodDefault<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    population_size: number;
    max_generations: number;
    mutation_rate: number;
    crossover_rate: number;
    fitness_objective: "custom" | "profit_factor" | "win_rate" | "max_drawdown" | "sharpe_ratio";
    elitism_rate: number;
    tournament_size: number;
    max_strategy_complexity: number;
    verification_enabled: boolean;
}, {
    population_size?: number | undefined;
    max_generations?: number | undefined;
    mutation_rate?: number | undefined;
    crossover_rate?: number | undefined;
    fitness_objective?: "custom" | "profit_factor" | "win_rate" | "max_drawdown" | "sharpe_ratio" | undefined;
    elitism_rate?: number | undefined;
    tournament_size?: number | undefined;
    max_strategy_complexity?: number | undefined;
    verification_enabled?: boolean | undefined;
}>;
export type EvolutionParameters = z.infer<typeof EvolutionParametersSchema>;
export declare const EvolutionStateSchema: z.ZodObject<{
    job_id: z.ZodString;
    status: z.ZodEnum<["initializing", "running", "paused", "completed", "failed", "terminated"]>;
    generation: z.ZodNumber;
    population: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        name: z.ZodString;
        conditions: z.ZodArray<z.ZodObject<{
            indicator: z.ZodString;
            operator: z.ZodEnum<[">", "<", ">=", "<=", "==", "crossover", "crossunder"]>;
            value: z.ZodUnion<[z.ZodNumber, z.ZodString]>;
            timeframe: z.ZodOptional<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            value: string | number;
            indicator: string;
            operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
            timeframe?: string | undefined;
        }, {
            value: string | number;
            indicator: string;
            operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
            timeframe?: string | undefined;
        }>, "many">;
        risk_management: z.ZodObject<{
            stop_loss_pips: z.ZodOptional<z.ZodNumber>;
            take_profit_pips: z.ZodOptional<z.ZodNumber>;
            position_size_percent: z.ZodNumber;
            max_daily_loss_percent: z.ZodNumber;
            max_concurrent_trades: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            max_concurrent_trades: number;
            position_size_percent: number;
            max_daily_loss_percent: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        }, {
            max_concurrent_trades: number;
            position_size_percent: number;
            max_daily_loss_percent: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        }>;
        fitness_score: z.ZodOptional<z.ZodNumber>;
        is_verified: z.ZodDefault<z.ZodBoolean>;
        verification_proof: z.ZodOptional<z.ZodString>;
        generation: z.ZodOptional<z.ZodNumber>;
        parent_ids: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        created_at: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        id: string;
        conditions: {
            value: string | number;
            indicator: string;
            operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
            timeframe?: string | undefined;
        }[];
        created_at: Date;
        risk_management: {
            max_concurrent_trades: number;
            position_size_percent: number;
            max_daily_loss_percent: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        is_verified: boolean;
        generation?: number | undefined;
        fitness_score?: number | undefined;
        verification_proof?: string | undefined;
        parent_ids?: string[] | undefined;
    }, {
        name: string;
        id: string;
        conditions: {
            value: string | number;
            indicator: string;
            operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
            timeframe?: string | undefined;
        }[];
        risk_management: {
            max_concurrent_trades: number;
            position_size_percent: number;
            max_daily_loss_percent: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        generation?: number | undefined;
        created_at?: Date | undefined;
        fitness_score?: number | undefined;
        is_verified?: boolean | undefined;
        verification_proof?: string | undefined;
        parent_ids?: string[] | undefined;
    }>, "many">;
    best_fitness: z.ZodNumber;
    average_fitness: z.ZodNumber;
    verified_count: z.ZodNumber;
    total_strategies_tested: z.ZodNumber;
    start_time: z.ZodDate;
    last_update: z.ZodDate;
    estimated_completion: z.ZodOptional<z.ZodDate>;
    error_message: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    status: "running" | "completed" | "initializing" | "paused" | "failed" | "terminated";
    generation: number;
    start_time: Date;
    best_fitness: number;
    job_id: string;
    population: {
        name: string;
        id: string;
        conditions: {
            value: string | number;
            indicator: string;
            operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
            timeframe?: string | undefined;
        }[];
        created_at: Date;
        risk_management: {
            max_concurrent_trades: number;
            position_size_percent: number;
            max_daily_loss_percent: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        is_verified: boolean;
        generation?: number | undefined;
        fitness_score?: number | undefined;
        verification_proof?: string | undefined;
        parent_ids?: string[] | undefined;
    }[];
    average_fitness: number;
    verified_count: number;
    total_strategies_tested: number;
    last_update: Date;
    error_message?: string | undefined;
    estimated_completion?: Date | undefined;
}, {
    status: "running" | "completed" | "initializing" | "paused" | "failed" | "terminated";
    generation: number;
    start_time: Date;
    best_fitness: number;
    job_id: string;
    population: {
        name: string;
        id: string;
        conditions: {
            value: string | number;
            indicator: string;
            operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
            timeframe?: string | undefined;
        }[];
        risk_management: {
            max_concurrent_trades: number;
            position_size_percent: number;
            max_daily_loss_percent: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        generation?: number | undefined;
        created_at?: Date | undefined;
        fitness_score?: number | undefined;
        is_verified?: boolean | undefined;
        verification_proof?: string | undefined;
        parent_ids?: string[] | undefined;
    }[];
    average_fitness: number;
    verified_count: number;
    total_strategies_tested: number;
    last_update: Date;
    error_message?: string | undefined;
    estimated_completion?: Date | undefined;
}>;
export type EvolutionState = z.infer<typeof EvolutionStateSchema>;
export declare const DarwinEvolutionRequestSchema: z.ZodObject<{
    pair: z.ZodString;
    timeframe: z.ZodString;
    evolution_params: z.ZodOptional<z.ZodObject<{
        population_size: z.ZodDefault<z.ZodNumber>;
        max_generations: z.ZodDefault<z.ZodNumber>;
        mutation_rate: z.ZodDefault<z.ZodNumber>;
        crossover_rate: z.ZodDefault<z.ZodNumber>;
        fitness_objective: z.ZodDefault<z.ZodEnum<["sharpe_ratio", "profit_factor", "win_rate", "max_drawdown", "custom"]>>;
        elitism_rate: z.ZodDefault<z.ZodNumber>;
        tournament_size: z.ZodDefault<z.ZodNumber>;
        max_strategy_complexity: z.ZodDefault<z.ZodNumber>;
        verification_enabled: z.ZodDefault<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        population_size: number;
        max_generations: number;
        mutation_rate: number;
        crossover_rate: number;
        fitness_objective: "custom" | "profit_factor" | "win_rate" | "max_drawdown" | "sharpe_ratio";
        elitism_rate: number;
        tournament_size: number;
        max_strategy_complexity: number;
        verification_enabled: boolean;
    }, {
        population_size?: number | undefined;
        max_generations?: number | undefined;
        mutation_rate?: number | undefined;
        crossover_rate?: number | undefined;
        fitness_objective?: "custom" | "profit_factor" | "win_rate" | "max_drawdown" | "sharpe_ratio" | undefined;
        elitism_rate?: number | undefined;
        tournament_size?: number | undefined;
        max_strategy_complexity?: number | undefined;
        verification_enabled?: boolean | undefined;
    }>>;
    data_start_date: z.ZodOptional<z.ZodDate>;
    data_end_date: z.ZodOptional<z.ZodDate>;
    callback_url: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    pair: string;
    timeframe: string;
    evolution_params?: {
        population_size: number;
        max_generations: number;
        mutation_rate: number;
        crossover_rate: number;
        fitness_objective: "custom" | "profit_factor" | "win_rate" | "max_drawdown" | "sharpe_ratio";
        elitism_rate: number;
        tournament_size: number;
        max_strategy_complexity: number;
        verification_enabled: boolean;
    } | undefined;
    data_start_date?: Date | undefined;
    data_end_date?: Date | undefined;
    callback_url?: string | undefined;
}, {
    pair: string;
    timeframe: string;
    evolution_params?: {
        population_size?: number | undefined;
        max_generations?: number | undefined;
        mutation_rate?: number | undefined;
        crossover_rate?: number | undefined;
        fitness_objective?: "custom" | "profit_factor" | "win_rate" | "max_drawdown" | "sharpe_ratio" | undefined;
        elitism_rate?: number | undefined;
        tournament_size?: number | undefined;
        max_strategy_complexity?: number | undefined;
        verification_enabled?: boolean | undefined;
    } | undefined;
    data_start_date?: Date | undefined;
    data_end_date?: Date | undefined;
    callback_url?: string | undefined;
}>;
export type DarwinEvolutionRequest = z.infer<typeof DarwinEvolutionRequestSchema>;
export declare const DarwinEvolutionResponseSchema: z.ZodObject<{
    job_id: z.ZodString;
    status: z.ZodEnum<["started", "queued", "failed"]>;
    message: z.ZodString;
    estimated_duration_minutes: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    status: "failed" | "started" | "queued";
    message: string;
    job_id: string;
    estimated_duration_minutes?: number | undefined;
}, {
    status: "failed" | "started" | "queued";
    message: string;
    job_id: string;
    estimated_duration_minutes?: number | undefined;
}>;
export type DarwinEvolutionResponse = z.infer<typeof DarwinEvolutionResponseSchema>;
export declare const ForexGenomeSchema: z.ZodObject<{
    pair: z.ZodString;
    timeframe: z.ZodString;
    behavioral_patterns: z.ZodRecord<z.ZodString, z.ZodAny>;
    volatility_profile: z.ZodObject<{
        average_volatility: z.ZodNumber;
        volatility_clusters: z.ZodArray<z.ZodObject<{
            start_hour: z.ZodNumber;
            end_hour: z.ZodNumber;
            volatility_multiplier: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            start_hour: number;
            end_hour: number;
            volatility_multiplier: number;
        }, {
            start_hour: number;
            end_hour: number;
            volatility_multiplier: number;
        }>, "many">;
    }, "strip", z.ZodTypeAny, {
        average_volatility: number;
        volatility_clusters: {
            start_hour: number;
            end_hour: number;
            volatility_multiplier: number;
        }[];
    }, {
        average_volatility: number;
        volatility_clusters: {
            start_hour: number;
            end_hour: number;
            volatility_multiplier: number;
        }[];
    }>;
    trend_characteristics: z.ZodObject<{
        trend_persistence: z.ZodNumber;
        reversal_frequency: z.ZodNumber;
        support_resistance_strength: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        trend_persistence: number;
        reversal_frequency: number;
        support_resistance_strength: number;
    }, {
        trend_persistence: number;
        reversal_frequency: number;
        support_resistance_strength: number;
    }>;
    optimal_strategies: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        name: z.ZodString;
        conditions: z.ZodArray<z.ZodObject<{
            indicator: z.ZodString;
            operator: z.ZodEnum<[">", "<", ">=", "<=", "==", "crossover", "crossunder"]>;
            value: z.ZodUnion<[z.ZodNumber, z.ZodString]>;
            timeframe: z.ZodOptional<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            value: string | number;
            indicator: string;
            operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
            timeframe?: string | undefined;
        }, {
            value: string | number;
            indicator: string;
            operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
            timeframe?: string | undefined;
        }>, "many">;
        risk_management: z.ZodObject<{
            stop_loss_pips: z.ZodOptional<z.ZodNumber>;
            take_profit_pips: z.ZodOptional<z.ZodNumber>;
            position_size_percent: z.ZodNumber;
            max_daily_loss_percent: z.ZodNumber;
            max_concurrent_trades: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            max_concurrent_trades: number;
            position_size_percent: number;
            max_daily_loss_percent: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        }, {
            max_concurrent_trades: number;
            position_size_percent: number;
            max_daily_loss_percent: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        }>;
        fitness_score: z.ZodOptional<z.ZodNumber>;
        is_verified: z.ZodDefault<z.ZodBoolean>;
        verification_proof: z.ZodOptional<z.ZodString>;
        generation: z.ZodOptional<z.ZodNumber>;
        parent_ids: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        created_at: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        id: string;
        conditions: {
            value: string | number;
            indicator: string;
            operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
            timeframe?: string | undefined;
        }[];
        created_at: Date;
        risk_management: {
            max_concurrent_trades: number;
            position_size_percent: number;
            max_daily_loss_percent: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        is_verified: boolean;
        generation?: number | undefined;
        fitness_score?: number | undefined;
        verification_proof?: string | undefined;
        parent_ids?: string[] | undefined;
    }, {
        name: string;
        id: string;
        conditions: {
            value: string | number;
            indicator: string;
            operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
            timeframe?: string | undefined;
        }[];
        risk_management: {
            max_concurrent_trades: number;
            position_size_percent: number;
            max_daily_loss_percent: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        generation?: number | undefined;
        created_at?: Date | undefined;
        fitness_score?: number | undefined;
        is_verified?: boolean | undefined;
        verification_proof?: string | undefined;
        parent_ids?: string[] | undefined;
    }>, "many">;
    confidence_score: z.ZodNumber;
    generated_at: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    pair: string;
    timeframe: string;
    behavioral_patterns: Record<string, any>;
    volatility_profile: {
        average_volatility: number;
        volatility_clusters: {
            start_hour: number;
            end_hour: number;
            volatility_multiplier: number;
        }[];
    };
    trend_characteristics: {
        trend_persistence: number;
        reversal_frequency: number;
        support_resistance_strength: number;
    };
    optimal_strategies: {
        name: string;
        id: string;
        conditions: {
            value: string | number;
            indicator: string;
            operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
            timeframe?: string | undefined;
        }[];
        created_at: Date;
        risk_management: {
            max_concurrent_trades: number;
            position_size_percent: number;
            max_daily_loss_percent: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        is_verified: boolean;
        generation?: number | undefined;
        fitness_score?: number | undefined;
        verification_proof?: string | undefined;
        parent_ids?: string[] | undefined;
    }[];
    confidence_score: number;
    generated_at: Date;
}, {
    pair: string;
    timeframe: string;
    behavioral_patterns: Record<string, any>;
    volatility_profile: {
        average_volatility: number;
        volatility_clusters: {
            start_hour: number;
            end_hour: number;
            volatility_multiplier: number;
        }[];
    };
    trend_characteristics: {
        trend_persistence: number;
        reversal_frequency: number;
        support_resistance_strength: number;
    };
    optimal_strategies: {
        name: string;
        id: string;
        conditions: {
            value: string | number;
            indicator: string;
            operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
            timeframe?: string | undefined;
        }[];
        risk_management: {
            max_concurrent_trades: number;
            position_size_percent: number;
            max_daily_loss_percent: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        generation?: number | undefined;
        created_at?: Date | undefined;
        fitness_score?: number | undefined;
        is_verified?: boolean | undefined;
        verification_proof?: string | undefined;
        parent_ids?: string[] | undefined;
    }[];
    confidence_score: number;
    generated_at?: Date | undefined;
}>;
export type ForexGenome = z.infer<typeof ForexGenomeSchema>;
export declare const DarwinJobStatusSchema: z.ZodObject<{
    job_id: z.ZodString;
    status: z.ZodEnum<["initializing", "running", "paused", "completed", "failed", "terminated"]>;
    progress: z.ZodObject<{
        current_generation: z.ZodNumber;
        total_generations: z.ZodNumber;
        completion_percentage: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        current_generation: number;
        total_generations: number;
        completion_percentage: number;
    }, {
        current_generation: number;
        total_generations: number;
        completion_percentage: number;
    }>;
    metrics: z.ZodObject<{
        best_fitness: z.ZodNumber;
        average_fitness: z.ZodNumber;
        verified_strategies: z.ZodNumber;
        total_strategies: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        best_fitness: number;
        average_fitness: number;
        verified_strategies: number;
        total_strategies: number;
    }, {
        best_fitness: number;
        average_fitness: number;
        verified_strategies: number;
        total_strategies: number;
    }>;
    runtime_info: z.ZodObject<{
        start_time: z.ZodDate;
        elapsed_seconds: z.ZodNumber;
        estimated_remaining_seconds: z.ZodOptional<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        start_time: Date;
        elapsed_seconds: number;
        estimated_remaining_seconds?: number | undefined;
    }, {
        start_time: Date;
        elapsed_seconds: number;
        estimated_remaining_seconds?: number | undefined;
    }>;
    error: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    status: "running" | "completed" | "initializing" | "paused" | "failed" | "terminated";
    progress: {
        current_generation: number;
        total_generations: number;
        completion_percentage: number;
    };
    metrics: {
        best_fitness: number;
        average_fitness: number;
        verified_strategies: number;
        total_strategies: number;
    };
    job_id: string;
    runtime_info: {
        start_time: Date;
        elapsed_seconds: number;
        estimated_remaining_seconds?: number | undefined;
    };
    error?: string | undefined;
}, {
    status: "running" | "completed" | "initializing" | "paused" | "failed" | "terminated";
    progress: {
        current_generation: number;
        total_generations: number;
        completion_percentage: number;
    };
    metrics: {
        best_fitness: number;
        average_fitness: number;
        verified_strategies: number;
        total_strategies: number;
    };
    job_id: string;
    runtime_info: {
        start_time: Date;
        elapsed_seconds: number;
        estimated_remaining_seconds?: number | undefined;
    };
    error?: string | undefined;
}>;
export type DarwinJobStatus = z.infer<typeof DarwinJobStatusSchema>;
export declare const DarwinResultsSchema: z.ZodObject<{
    job_id: z.ZodString;
    evolution_params: z.ZodObject<{
        population_size: z.ZodDefault<z.ZodNumber>;
        max_generations: z.ZodDefault<z.ZodNumber>;
        mutation_rate: z.ZodDefault<z.ZodNumber>;
        crossover_rate: z.ZodDefault<z.ZodNumber>;
        fitness_objective: z.ZodDefault<z.ZodEnum<["sharpe_ratio", "profit_factor", "win_rate", "max_drawdown", "custom"]>>;
        elitism_rate: z.ZodDefault<z.ZodNumber>;
        tournament_size: z.ZodDefault<z.ZodNumber>;
        max_strategy_complexity: z.ZodDefault<z.ZodNumber>;
        verification_enabled: z.ZodDefault<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        population_size: number;
        max_generations: number;
        mutation_rate: number;
        crossover_rate: number;
        fitness_objective: "custom" | "profit_factor" | "win_rate" | "max_drawdown" | "sharpe_ratio";
        elitism_rate: number;
        tournament_size: number;
        max_strategy_complexity: number;
        verification_enabled: boolean;
    }, {
        population_size?: number | undefined;
        max_generations?: number | undefined;
        mutation_rate?: number | undefined;
        crossover_rate?: number | undefined;
        fitness_objective?: "custom" | "profit_factor" | "win_rate" | "max_drawdown" | "sharpe_ratio" | undefined;
        elitism_rate?: number | undefined;
        tournament_size?: number | undefined;
        max_strategy_complexity?: number | undefined;
        verification_enabled?: boolean | undefined;
    }>;
    final_state: z.ZodObject<{
        job_id: z.ZodString;
        status: z.ZodEnum<["initializing", "running", "paused", "completed", "failed", "terminated"]>;
        generation: z.ZodNumber;
        population: z.ZodArray<z.ZodObject<{
            id: z.ZodString;
            name: z.ZodString;
            conditions: z.ZodArray<z.ZodObject<{
                indicator: z.ZodString;
                operator: z.ZodEnum<[">", "<", ">=", "<=", "==", "crossover", "crossunder"]>;
                value: z.ZodUnion<[z.ZodNumber, z.ZodString]>;
                timeframe: z.ZodOptional<z.ZodString>;
            }, "strip", z.ZodTypeAny, {
                value: string | number;
                indicator: string;
                operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
                timeframe?: string | undefined;
            }, {
                value: string | number;
                indicator: string;
                operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
                timeframe?: string | undefined;
            }>, "many">;
            risk_management: z.ZodObject<{
                stop_loss_pips: z.ZodOptional<z.ZodNumber>;
                take_profit_pips: z.ZodOptional<z.ZodNumber>;
                position_size_percent: z.ZodNumber;
                max_daily_loss_percent: z.ZodNumber;
                max_concurrent_trades: z.ZodNumber;
            }, "strip", z.ZodTypeAny, {
                max_concurrent_trades: number;
                position_size_percent: number;
                max_daily_loss_percent: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            }, {
                max_concurrent_trades: number;
                position_size_percent: number;
                max_daily_loss_percent: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            }>;
            fitness_score: z.ZodOptional<z.ZodNumber>;
            is_verified: z.ZodDefault<z.ZodBoolean>;
            verification_proof: z.ZodOptional<z.ZodString>;
            generation: z.ZodOptional<z.ZodNumber>;
            parent_ids: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
            created_at: z.ZodDefault<z.ZodDate>;
        }, "strip", z.ZodTypeAny, {
            name: string;
            id: string;
            conditions: {
                value: string | number;
                indicator: string;
                operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
                timeframe?: string | undefined;
            }[];
            created_at: Date;
            risk_management: {
                max_concurrent_trades: number;
                position_size_percent: number;
                max_daily_loss_percent: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            is_verified: boolean;
            generation?: number | undefined;
            fitness_score?: number | undefined;
            verification_proof?: string | undefined;
            parent_ids?: string[] | undefined;
        }, {
            name: string;
            id: string;
            conditions: {
                value: string | number;
                indicator: string;
                operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
                timeframe?: string | undefined;
            }[];
            risk_management: {
                max_concurrent_trades: number;
                position_size_percent: number;
                max_daily_loss_percent: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            generation?: number | undefined;
            created_at?: Date | undefined;
            fitness_score?: number | undefined;
            is_verified?: boolean | undefined;
            verification_proof?: string | undefined;
            parent_ids?: string[] | undefined;
        }>, "many">;
        best_fitness: z.ZodNumber;
        average_fitness: z.ZodNumber;
        verified_count: z.ZodNumber;
        total_strategies_tested: z.ZodNumber;
        start_time: z.ZodDate;
        last_update: z.ZodDate;
        estimated_completion: z.ZodOptional<z.ZodDate>;
        error_message: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        status: "running" | "completed" | "initializing" | "paused" | "failed" | "terminated";
        generation: number;
        start_time: Date;
        best_fitness: number;
        job_id: string;
        population: {
            name: string;
            id: string;
            conditions: {
                value: string | number;
                indicator: string;
                operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
                timeframe?: string | undefined;
            }[];
            created_at: Date;
            risk_management: {
                max_concurrent_trades: number;
                position_size_percent: number;
                max_daily_loss_percent: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            is_verified: boolean;
            generation?: number | undefined;
            fitness_score?: number | undefined;
            verification_proof?: string | undefined;
            parent_ids?: string[] | undefined;
        }[];
        average_fitness: number;
        verified_count: number;
        total_strategies_tested: number;
        last_update: Date;
        error_message?: string | undefined;
        estimated_completion?: Date | undefined;
    }, {
        status: "running" | "completed" | "initializing" | "paused" | "failed" | "terminated";
        generation: number;
        start_time: Date;
        best_fitness: number;
        job_id: string;
        population: {
            name: string;
            id: string;
            conditions: {
                value: string | number;
                indicator: string;
                operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
                timeframe?: string | undefined;
            }[];
            risk_management: {
                max_concurrent_trades: number;
                position_size_percent: number;
                max_daily_loss_percent: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            generation?: number | undefined;
            created_at?: Date | undefined;
            fitness_score?: number | undefined;
            is_verified?: boolean | undefined;
            verification_proof?: string | undefined;
            parent_ids?: string[] | undefined;
        }[];
        average_fitness: number;
        verified_count: number;
        total_strategies_tested: number;
        last_update: Date;
        error_message?: string | undefined;
        estimated_completion?: Date | undefined;
    }>;
    best_strategies: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        name: z.ZodString;
        conditions: z.ZodArray<z.ZodObject<{
            indicator: z.ZodString;
            operator: z.ZodEnum<[">", "<", ">=", "<=", "==", "crossover", "crossunder"]>;
            value: z.ZodUnion<[z.ZodNumber, z.ZodString]>;
            timeframe: z.ZodOptional<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            value: string | number;
            indicator: string;
            operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
            timeframe?: string | undefined;
        }, {
            value: string | number;
            indicator: string;
            operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
            timeframe?: string | undefined;
        }>, "many">;
        risk_management: z.ZodObject<{
            stop_loss_pips: z.ZodOptional<z.ZodNumber>;
            take_profit_pips: z.ZodOptional<z.ZodNumber>;
            position_size_percent: z.ZodNumber;
            max_daily_loss_percent: z.ZodNumber;
            max_concurrent_trades: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            max_concurrent_trades: number;
            position_size_percent: number;
            max_daily_loss_percent: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        }, {
            max_concurrent_trades: number;
            position_size_percent: number;
            max_daily_loss_percent: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        }>;
        fitness_score: z.ZodOptional<z.ZodNumber>;
        is_verified: z.ZodDefault<z.ZodBoolean>;
        verification_proof: z.ZodOptional<z.ZodString>;
        generation: z.ZodOptional<z.ZodNumber>;
        parent_ids: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        created_at: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        id: string;
        conditions: {
            value: string | number;
            indicator: string;
            operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
            timeframe?: string | undefined;
        }[];
        created_at: Date;
        risk_management: {
            max_concurrent_trades: number;
            position_size_percent: number;
            max_daily_loss_percent: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        is_verified: boolean;
        generation?: number | undefined;
        fitness_score?: number | undefined;
        verification_proof?: string | undefined;
        parent_ids?: string[] | undefined;
    }, {
        name: string;
        id: string;
        conditions: {
            value: string | number;
            indicator: string;
            operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
            timeframe?: string | undefined;
        }[];
        risk_management: {
            max_concurrent_trades: number;
            position_size_percent: number;
            max_daily_loss_percent: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        generation?: number | undefined;
        created_at?: Date | undefined;
        fitness_score?: number | undefined;
        is_verified?: boolean | undefined;
        verification_proof?: string | undefined;
        parent_ids?: string[] | undefined;
    }>, "many">;
    forex_genome: z.ZodOptional<z.ZodObject<{
        pair: z.ZodString;
        timeframe: z.ZodString;
        behavioral_patterns: z.ZodRecord<z.ZodString, z.ZodAny>;
        volatility_profile: z.ZodObject<{
            average_volatility: z.ZodNumber;
            volatility_clusters: z.ZodArray<z.ZodObject<{
                start_hour: z.ZodNumber;
                end_hour: z.ZodNumber;
                volatility_multiplier: z.ZodNumber;
            }, "strip", z.ZodTypeAny, {
                start_hour: number;
                end_hour: number;
                volatility_multiplier: number;
            }, {
                start_hour: number;
                end_hour: number;
                volatility_multiplier: number;
            }>, "many">;
        }, "strip", z.ZodTypeAny, {
            average_volatility: number;
            volatility_clusters: {
                start_hour: number;
                end_hour: number;
                volatility_multiplier: number;
            }[];
        }, {
            average_volatility: number;
            volatility_clusters: {
                start_hour: number;
                end_hour: number;
                volatility_multiplier: number;
            }[];
        }>;
        trend_characteristics: z.ZodObject<{
            trend_persistence: z.ZodNumber;
            reversal_frequency: z.ZodNumber;
            support_resistance_strength: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            trend_persistence: number;
            reversal_frequency: number;
            support_resistance_strength: number;
        }, {
            trend_persistence: number;
            reversal_frequency: number;
            support_resistance_strength: number;
        }>;
        optimal_strategies: z.ZodArray<z.ZodObject<{
            id: z.ZodString;
            name: z.ZodString;
            conditions: z.ZodArray<z.ZodObject<{
                indicator: z.ZodString;
                operator: z.ZodEnum<[">", "<", ">=", "<=", "==", "crossover", "crossunder"]>;
                value: z.ZodUnion<[z.ZodNumber, z.ZodString]>;
                timeframe: z.ZodOptional<z.ZodString>;
            }, "strip", z.ZodTypeAny, {
                value: string | number;
                indicator: string;
                operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
                timeframe?: string | undefined;
            }, {
                value: string | number;
                indicator: string;
                operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
                timeframe?: string | undefined;
            }>, "many">;
            risk_management: z.ZodObject<{
                stop_loss_pips: z.ZodOptional<z.ZodNumber>;
                take_profit_pips: z.ZodOptional<z.ZodNumber>;
                position_size_percent: z.ZodNumber;
                max_daily_loss_percent: z.ZodNumber;
                max_concurrent_trades: z.ZodNumber;
            }, "strip", z.ZodTypeAny, {
                max_concurrent_trades: number;
                position_size_percent: number;
                max_daily_loss_percent: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            }, {
                max_concurrent_trades: number;
                position_size_percent: number;
                max_daily_loss_percent: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            }>;
            fitness_score: z.ZodOptional<z.ZodNumber>;
            is_verified: z.ZodDefault<z.ZodBoolean>;
            verification_proof: z.ZodOptional<z.ZodString>;
            generation: z.ZodOptional<z.ZodNumber>;
            parent_ids: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
            created_at: z.ZodDefault<z.ZodDate>;
        }, "strip", z.ZodTypeAny, {
            name: string;
            id: string;
            conditions: {
                value: string | number;
                indicator: string;
                operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
                timeframe?: string | undefined;
            }[];
            created_at: Date;
            risk_management: {
                max_concurrent_trades: number;
                position_size_percent: number;
                max_daily_loss_percent: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            is_verified: boolean;
            generation?: number | undefined;
            fitness_score?: number | undefined;
            verification_proof?: string | undefined;
            parent_ids?: string[] | undefined;
        }, {
            name: string;
            id: string;
            conditions: {
                value: string | number;
                indicator: string;
                operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
                timeframe?: string | undefined;
            }[];
            risk_management: {
                max_concurrent_trades: number;
                position_size_percent: number;
                max_daily_loss_percent: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            generation?: number | undefined;
            created_at?: Date | undefined;
            fitness_score?: number | undefined;
            is_verified?: boolean | undefined;
            verification_proof?: string | undefined;
            parent_ids?: string[] | undefined;
        }>, "many">;
        confidence_score: z.ZodNumber;
        generated_at: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        pair: string;
        timeframe: string;
        behavioral_patterns: Record<string, any>;
        volatility_profile: {
            average_volatility: number;
            volatility_clusters: {
                start_hour: number;
                end_hour: number;
                volatility_multiplier: number;
            }[];
        };
        trend_characteristics: {
            trend_persistence: number;
            reversal_frequency: number;
            support_resistance_strength: number;
        };
        optimal_strategies: {
            name: string;
            id: string;
            conditions: {
                value: string | number;
                indicator: string;
                operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
                timeframe?: string | undefined;
            }[];
            created_at: Date;
            risk_management: {
                max_concurrent_trades: number;
                position_size_percent: number;
                max_daily_loss_percent: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            is_verified: boolean;
            generation?: number | undefined;
            fitness_score?: number | undefined;
            verification_proof?: string | undefined;
            parent_ids?: string[] | undefined;
        }[];
        confidence_score: number;
        generated_at: Date;
    }, {
        pair: string;
        timeframe: string;
        behavioral_patterns: Record<string, any>;
        volatility_profile: {
            average_volatility: number;
            volatility_clusters: {
                start_hour: number;
                end_hour: number;
                volatility_multiplier: number;
            }[];
        };
        trend_characteristics: {
            trend_persistence: number;
            reversal_frequency: number;
            support_resistance_strength: number;
        };
        optimal_strategies: {
            name: string;
            id: string;
            conditions: {
                value: string | number;
                indicator: string;
                operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
                timeframe?: string | undefined;
            }[];
            risk_management: {
                max_concurrent_trades: number;
                position_size_percent: number;
                max_daily_loss_percent: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            generation?: number | undefined;
            created_at?: Date | undefined;
            fitness_score?: number | undefined;
            is_verified?: boolean | undefined;
            verification_proof?: string | undefined;
            parent_ids?: string[] | undefined;
        }[];
        confidence_score: number;
        generated_at?: Date | undefined;
    }>>;
    performance_summary: z.ZodObject<{
        total_runtime_seconds: z.ZodNumber;
        strategies_evolved: z.ZodNumber;
        verification_success_rate: z.ZodNumber;
        fitness_improvement: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        fitness_improvement: number;
        total_runtime_seconds: number;
        strategies_evolved: number;
        verification_success_rate: number;
    }, {
        fitness_improvement: number;
        total_runtime_seconds: number;
        strategies_evolved: number;
        verification_success_rate: number;
    }>;
    completed_at: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    completed_at: Date;
    job_id: string;
    evolution_params: {
        population_size: number;
        max_generations: number;
        mutation_rate: number;
        crossover_rate: number;
        fitness_objective: "custom" | "profit_factor" | "win_rate" | "max_drawdown" | "sharpe_ratio";
        elitism_rate: number;
        tournament_size: number;
        max_strategy_complexity: number;
        verification_enabled: boolean;
    };
    final_state: {
        status: "running" | "completed" | "initializing" | "paused" | "failed" | "terminated";
        generation: number;
        start_time: Date;
        best_fitness: number;
        job_id: string;
        population: {
            name: string;
            id: string;
            conditions: {
                value: string | number;
                indicator: string;
                operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
                timeframe?: string | undefined;
            }[];
            created_at: Date;
            risk_management: {
                max_concurrent_trades: number;
                position_size_percent: number;
                max_daily_loss_percent: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            is_verified: boolean;
            generation?: number | undefined;
            fitness_score?: number | undefined;
            verification_proof?: string | undefined;
            parent_ids?: string[] | undefined;
        }[];
        average_fitness: number;
        verified_count: number;
        total_strategies_tested: number;
        last_update: Date;
        error_message?: string | undefined;
        estimated_completion?: Date | undefined;
    };
    best_strategies: {
        name: string;
        id: string;
        conditions: {
            value: string | number;
            indicator: string;
            operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
            timeframe?: string | undefined;
        }[];
        created_at: Date;
        risk_management: {
            max_concurrent_trades: number;
            position_size_percent: number;
            max_daily_loss_percent: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        is_verified: boolean;
        generation?: number | undefined;
        fitness_score?: number | undefined;
        verification_proof?: string | undefined;
        parent_ids?: string[] | undefined;
    }[];
    performance_summary: {
        fitness_improvement: number;
        total_runtime_seconds: number;
        strategies_evolved: number;
        verification_success_rate: number;
    };
    forex_genome?: {
        pair: string;
        timeframe: string;
        behavioral_patterns: Record<string, any>;
        volatility_profile: {
            average_volatility: number;
            volatility_clusters: {
                start_hour: number;
                end_hour: number;
                volatility_multiplier: number;
            }[];
        };
        trend_characteristics: {
            trend_persistence: number;
            reversal_frequency: number;
            support_resistance_strength: number;
        };
        optimal_strategies: {
            name: string;
            id: string;
            conditions: {
                value: string | number;
                indicator: string;
                operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
                timeframe?: string | undefined;
            }[];
            created_at: Date;
            risk_management: {
                max_concurrent_trades: number;
                position_size_percent: number;
                max_daily_loss_percent: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            is_verified: boolean;
            generation?: number | undefined;
            fitness_score?: number | undefined;
            verification_proof?: string | undefined;
            parent_ids?: string[] | undefined;
        }[];
        confidence_score: number;
        generated_at: Date;
    } | undefined;
}, {
    job_id: string;
    evolution_params: {
        population_size?: number | undefined;
        max_generations?: number | undefined;
        mutation_rate?: number | undefined;
        crossover_rate?: number | undefined;
        fitness_objective?: "custom" | "profit_factor" | "win_rate" | "max_drawdown" | "sharpe_ratio" | undefined;
        elitism_rate?: number | undefined;
        tournament_size?: number | undefined;
        max_strategy_complexity?: number | undefined;
        verification_enabled?: boolean | undefined;
    };
    final_state: {
        status: "running" | "completed" | "initializing" | "paused" | "failed" | "terminated";
        generation: number;
        start_time: Date;
        best_fitness: number;
        job_id: string;
        population: {
            name: string;
            id: string;
            conditions: {
                value: string | number;
                indicator: string;
                operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
                timeframe?: string | undefined;
            }[];
            risk_management: {
                max_concurrent_trades: number;
                position_size_percent: number;
                max_daily_loss_percent: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            generation?: number | undefined;
            created_at?: Date | undefined;
            fitness_score?: number | undefined;
            is_verified?: boolean | undefined;
            verification_proof?: string | undefined;
            parent_ids?: string[] | undefined;
        }[];
        average_fitness: number;
        verified_count: number;
        total_strategies_tested: number;
        last_update: Date;
        error_message?: string | undefined;
        estimated_completion?: Date | undefined;
    };
    best_strategies: {
        name: string;
        id: string;
        conditions: {
            value: string | number;
            indicator: string;
            operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
            timeframe?: string | undefined;
        }[];
        risk_management: {
            max_concurrent_trades: number;
            position_size_percent: number;
            max_daily_loss_percent: number;
            stop_loss_pips?: number | undefined;
            take_profit_pips?: number | undefined;
        };
        generation?: number | undefined;
        created_at?: Date | undefined;
        fitness_score?: number | undefined;
        is_verified?: boolean | undefined;
        verification_proof?: string | undefined;
        parent_ids?: string[] | undefined;
    }[];
    performance_summary: {
        fitness_improvement: number;
        total_runtime_seconds: number;
        strategies_evolved: number;
        verification_success_rate: number;
    };
    completed_at?: Date | undefined;
    forex_genome?: {
        pair: string;
        timeframe: string;
        behavioral_patterns: Record<string, any>;
        volatility_profile: {
            average_volatility: number;
            volatility_clusters: {
                start_hour: number;
                end_hour: number;
                volatility_multiplier: number;
            }[];
        };
        trend_characteristics: {
            trend_persistence: number;
            reversal_frequency: number;
            support_resistance_strength: number;
        };
        optimal_strategies: {
            name: string;
            id: string;
            conditions: {
                value: string | number;
                indicator: string;
                operator: ">" | "<" | ">=" | "<=" | "==" | "crossover" | "crossunder";
                timeframe?: string | undefined;
            }[];
            risk_management: {
                max_concurrent_trades: number;
                position_size_percent: number;
                max_daily_loss_percent: number;
                stop_loss_pips?: number | undefined;
                take_profit_pips?: number | undefined;
            };
            generation?: number | undefined;
            created_at?: Date | undefined;
            fitness_score?: number | undefined;
            is_verified?: boolean | undefined;
            verification_proof?: string | undefined;
            parent_ids?: string[] | undefined;
        }[];
        confidence_score: number;
        generated_at?: Date | undefined;
    } | undefined;
}>;
export type DarwinResults = z.infer<typeof DarwinResultsSchema>;
export declare const PythonDarwinRequestSchema: z.ZodObject<{
    action: z.ZodEnum<["start_evolution", "get_status", "get_results", "stop_evolution", "get_genome"]>;
    job_id: z.ZodOptional<z.ZodString>;
    payload: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
    timestamp: z.ZodDefault<z.ZodDate>;
    request_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    action: "start_evolution" | "get_status" | "get_results" | "stop_evolution" | "get_genome";
    timestamp: Date;
    request_id: string;
    payload?: Record<string, any> | undefined;
    job_id?: string | undefined;
}, {
    action: "start_evolution" | "get_status" | "get_results" | "stop_evolution" | "get_genome";
    request_id: string;
    timestamp?: Date | undefined;
    payload?: Record<string, any> | undefined;
    job_id?: string | undefined;
}>;
export type PythonDarwinRequest = z.infer<typeof PythonDarwinRequestSchema>;
export declare const PythonDarwinResponseSchema: z.ZodObject<{
    success: z.ZodBoolean;
    data: z.ZodOptional<z.ZodAny>;
    error: z.ZodOptional<z.ZodString>;
    timestamp: z.ZodDefault<z.ZodDate>;
    request_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    timestamp: Date;
    request_id: string;
    data?: any;
    error?: string | undefined;
}, {
    success: boolean;
    request_id: string;
    data?: any;
    error?: string | undefined;
    timestamp?: Date | undefined;
}>;
export type PythonDarwinResponse = z.infer<typeof PythonDarwinResponseSchema>;
//# sourceMappingURL=darwin.schemas.d.ts.map