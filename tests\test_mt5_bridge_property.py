# tests/test_mt5_bridge_property.py
"""
Property-based tests for MT5 Bridge using hypothesis
"""

import pytest
from hypothesis import given, strategies as st, settings, assume
from hypothesis.stateful import RuleBasedStateMachine, rule, invariant

# Import the MT5BridgeMock fixture from conftest.py


# Define strategies for generating test data
symbols = st.sampled_from(["EURUSD", "GBPUSD", "USDJPY", "USDCHF", "AUDUSD"])
order_types = st.sampled_from(["BUY", "SELL"])
lot_sizes = st.floats(min_value=0.01, max_value=10.0).map(lambda x: round(x, 2))
prices = st.floats(min_value=0.5, max_value=200.0).map(lambda x: round(x, 5))
stop_losses = st.floats(min_value=0.5, max_value=200.0).map(lambda x: round(x, 5))
take_profits = st.floats(min_value=0.5, max_value=200.0).map(lambda x: round(x, 5))


class TestMT5BridgeProperties:
    """
    Property-based tests for MT5 Bridge
    These tests use hypothesis to generate test data
    """
    
    @given(
        symbol=symbols,
        order_type=order_types,
        lot=lot_sizes
    )
    def test_order_placement_properties(self, mt5_bridge_mock, symbol, order_type, lot):
        """Test properties of order placement"""
        # Place an order with generated data
        order_id = mt5_bridge_mock.place_order(
            symbol=symbol,
            order_type=order_type,
            lot=lot
        )
        
        # Verify order properties
        assert isinstance(order_id, int)
        assert order_id > 0
        
        # Verify order status
        status = mt5_bridge_mock.get_order_status(order_id)
        assert status == "filled"
        
        # Find the order in the orders list
        order = next((o for o in mt5_bridge_mock.orders if o["id"] == order_id), None)
        assert order is not None
        assert order["symbol"] == symbol
        assert order["type"] == order_type
        assert order["lot"] == lot
    
    @given(
        symbol=symbols,
        order_type=order_types,
        lot=lot_sizes,
        price=prices,
        stop_loss=stop_losses,
        take_profit=take_profits
    )
    def test_order_with_sl_tp_properties(self, mt5_bridge_mock, symbol, order_type, lot, price, stop_loss, take_profit):
        """Test properties of order placement with stop loss and take profit"""
        # Place an order with generated data including SL and TP
        order_id = mt5_bridge_mock.place_order(
            symbol=symbol,
            order_type=order_type,
            lot=lot,
            price=price,
            stop_loss=stop_loss,
            take_profit=take_profit
        )
        
        # Verify order properties
        assert isinstance(order_id, int)
        assert order_id > 0
        
        # Verify order status
        status = mt5_bridge_mock.get_order_status(order_id)
        assert status == "filled"
    
    @given(
        orders=st.lists(
            st.tuples(
                symbols,
                order_types,
                lot_sizes
            ),
            min_size=1,
            max_size=10
        )
    )
    def test_multiple_orders_properties(self, mt5_bridge_mock, orders):
        """Test properties of placing multiple orders"""
        # Place multiple orders with generated data
        order_ids = []
        
        for symbol, order_type, lot in orders:
            order_id = mt5_bridge_mock.place_order(
                symbol=symbol,
                order_type=order_type,
                lot=lot
            )
            order_ids.append(order_id)
        
        # Verify all orders were placed
        assert len(order_ids) == len(orders)
        assert len(mt5_bridge_mock.orders) >= len(orders)
        
        # Verify each order
        for i, ((symbol, order_type, lot), order_id) in enumerate(zip(orders, order_ids)):
            status = mt5_bridge_mock.get_order_status(order_id)
            assert status == "filled"
            
            # Find the order in the orders list
            order = next((o for o in mt5_bridge_mock.orders if o["id"] == order_id), None)
            assert order is not None
            assert order["symbol"] == symbol
            assert order["type"] == order_type
            assert order["lot"] == lot


class MT5BridgeStateMachine(RuleBasedStateMachine):
    """
    State machine for testing MT5 Bridge behavior
    """
    
    def __init__(self):
        super().__init__()
        self.bridge = None
        self.order_ids = []
        self.closed_orders = set()
    
    def setup(self):
        """Set up the state machine"""
        self.bridge = MT5BridgeMock()
        self.order_ids = []
        self.closed_orders = set()
    
    @rule(
        symbol=symbols,
        order_type=order_types,
        lot=lot_sizes
    )
    def place_order(self, symbol, order_type, lot):
        """Rule: Place an order"""
        order_id = self.bridge.place_order(
            symbol=symbol,
            order_type=order_type,
            lot=lot
        )
        self.order_ids.append(order_id)
    
    @rule(
        order_idx=st.integers(min_value=0, max_value=10)
    )
    def close_order(self, order_idx):
        """Rule: Close an order"""
        if not self.order_ids:
            return
        
        # Use modulo to avoid index errors
        idx = order_idx % len(self.order_ids)
        order_id = self.order_ids[idx]
        
        # Only close if not already closed
        if order_id not in self.closed_orders:
            result = self.bridge.close_order(order_id)
            if result:
                self.closed_orders.add(order_id)
    
    @rule()
    def disconnect(self):
        """Rule: Disconnect from MT5"""
        self.bridge.simulate_connection_loss()
    
    @rule()
    def reconnect(self):
        """Rule: Reconnect to MT5"""
        self.bridge.connect()
    
    @invariant()
    def check_orders_consistency(self):
        """Invariant: Check that orders are consistent"""
        # All order IDs should be unique
        assert len(self.order_ids) == len(set(self.order_ids))
        
        # All orders should be in the bridge's orders list
        for order_id in self.order_ids:
            order = next((o for o in self.bridge.orders if o["id"] == order_id), None)
            assert order is not None
        
        # All closed orders should have status "closed"
        for order_id in self.closed_orders:
            status = self.bridge.get_order_status(order_id)
            assert status == "closed"


# Create a test case from the state machine
TestMT5BridgeStateful = MT5BridgeStateMachine.TestCase