"""
MT5 Strategy API - Integration between Ollama Chatbot and MT5 Bridge
Allows users to deploy and execute AI-generated strategies directly on MT5
"""

import asyncio
import logging
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any
import json

from .mt5_strategy_executor import MT5StrategyExecutor, StrategyStatus

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize router
router = APIRouter()

# Global strategy executor instance
strategy_executor: Optional[MT5StrategyExecutor] = None

# Pydantic models for request/response
class MT5CredentialsRequest(BaseModel):
    login: str = Field(..., description="MT5 account login")
    password: str = Field(..., description="MT5 account password") 
    server: str = Field(..., description="MT5 server address")

class StrategyDeployRequest(BaseModel):
    strategy_name: str = Field(..., description="Name of the strategy")
    strategy_code: str = Field(..., description="Python code of the strategy")
    strategy_config: Dict[str, Any] = Field(default_factory=dict, description="Strategy configuration parameters")
    mt5_credentials: Optional[MT5CredentialsRequest] = Field(None, description="MT5 credentials for live trading")
    demo_mode: bool = Field(default=True, description="Whether to run in demo mode")

class StrategyControlRequest(BaseModel):
    strategy_id: str = Field(..., description="ID of the strategy to control")
    action: str = Field(..., description="Action to perform: start, stop, pause")

class StrategyStatusResponse(BaseModel):
    strategy_id: str
    strategy_name: str
    status: str
    created_at: str
    started_at: Optional[str] = None
    stopped_at: Optional[str] = None
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    total_profit: float = 0.0
    max_drawdown: float = 0.0
    current_positions: List[Dict[str, Any]] = []
    error_message: Optional[str] = None

class MT5ConnectionResponse(BaseModel):
    connected: bool
    account_info: Dict[str, Any] = {}
    message: str
    error: Optional[str] = None

# Initialize strategy executor
async def initialize_executor():
    """Initialize the MT5 strategy executor"""
    global strategy_executor
    try:
        strategy_executor = MT5StrategyExecutor(use_secure_bridge=False)
        logger.info("MT5 Strategy Executor initialized")
        return True
    except Exception as e:
        logger.error(f"Failed to initialize strategy executor: {e}")
        return False

# API Routes
@router.get("/status")
async def get_mt5_status():
    """
    Get MT5 bridge status and connection info
    """
    try:
        if not strategy_executor:
            await initialize_executor()
            
        if not strategy_executor:
            return {
                "connected": False,
                "message": "MT5 Strategy Executor not available",
                "error": "Initialization failed"
            }
            
        # Check if MT5 bridge is available and connected
        mt5_available = strategy_executor.mt5_bridge is not None
        connected = False
        
        if mt5_available and hasattr(strategy_executor.mt5_bridge, 'connected'):
            connected = getattr(strategy_executor.mt5_bridge, 'connected', False)
            
        return {
            "connected": connected,
            "mt5_available": mt5_available,
            "executor_available": True,
            "active_strategies": len(strategy_executor.active_strategies),
            "message": "MT5 bridge status retrieved"
        }
        
    except Exception as e:
        logger.error(f"Failed to get MT5 status: {e}")
        return {
            "connected": False,
            "message": "Failed to get MT5 status",
            "error": str(e)
        }

@router.post("/connect", response_model=MT5ConnectionResponse)
async def connect_mt5(credentials: MT5CredentialsRequest):
    """
    Connect to MT5 with provided credentials
    """
    try:
        if not strategy_executor:
            await initialize_executor()
            
        if not strategy_executor:
            raise HTTPException(
                status_code=503,
                detail="MT5 Strategy Executor not available"
            )
            
        # Update credentials
        strategy_executor.mt5_credentials = {
            "login": credentials.login,
            "password": credentials.password,
            "server": credentials.server
        }
        
        # Reinitialize bridge with credentials
        strategy_executor._initialize_mt5_bridge()
        
        # Get account info if connected
        account_info = {}
        if strategy_executor.mt5_bridge and hasattr(strategy_executor.mt5_bridge, 'get_account_info'):
            account_info = strategy_executor.mt5_bridge.get_account_info()
            
        return MT5ConnectionResponse(
            connected=True,
            account_info=account_info,
            message="Connected to MT5 successfully"
        )
        
    except Exception as e:
        logger.error(f"MT5 connection failed: {e}")
        return MT5ConnectionResponse(
            connected=False,
            message="Failed to connect to MT5",
            error=str(e)
        )

@router.post("/strategies/deploy")
async def deploy_strategy(request: StrategyDeployRequest, background_tasks: BackgroundTasks):
    """
    Deploy and start executing a trading strategy
    """
    try:
        if not strategy_executor:
            await initialize_executor()
            
        if not strategy_executor:
            raise HTTPException(
                status_code=503,
                detail="MT5 Strategy Executor not available"
            )
            
        # Update MT5 credentials if provided
        if request.mt5_credentials and not request.demo_mode:
            strategy_executor.mt5_credentials = {
                "login": request.mt5_credentials.login,
                "password": request.mt5_credentials.password,
                "server": request.mt5_credentials.server
            }
            strategy_executor._initialize_mt5_bridge()
            
        # Deploy the strategy
        result = await strategy_executor.deploy_strategy(
            strategy_name=request.strategy_name,
            strategy_code=request.strategy_code,
            strategy_config=request.strategy_config
        )
        
        if result["success"]:
            return {
                "success": True,
                "strategy_id": result["strategy_id"],
                "message": result["message"],
                "validation_warnings": result.get("validation_warnings", []),
                "demo_mode": request.demo_mode
            }
        else:
            raise HTTPException(
                status_code=400,
                detail={
                    "message": result["error"],
                    "validation_errors": result.get("validation_errors", [])
                }
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Strategy deployment failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Strategy deployment failed: {str(e)}"
        )

@router.get("/strategies", response_model=List[StrategyStatusResponse])
async def get_all_strategies():
    """
    Get status of all deployed strategies
    """
    try:
        if not strategy_executor:
            return []
            
        strategies = strategy_executor.get_all_strategies()
        
        # Convert to response model
        response = []
        for strategy in strategies:
            response.append(StrategyStatusResponse(
                strategy_id=strategy["strategy_id"],
                strategy_name=strategy["strategy_name"],
                status=strategy["status"].value if hasattr(strategy["status"], 'value') else str(strategy["status"]),
                created_at=strategy["created_at"].isoformat(),
                started_at=strategy["started_at"].isoformat() if strategy["started_at"] else None,
                stopped_at=strategy["stopped_at"].isoformat() if strategy["stopped_at"] else None,
                total_trades=strategy["total_trades"],
                winning_trades=strategy["winning_trades"],
                losing_trades=strategy["losing_trades"],
                total_profit=strategy["total_profit"],
                max_drawdown=strategy["max_drawdown"],
                current_positions=strategy["current_positions"] or [],
                error_message=strategy["error_message"]
            ))
            
        return response
        
    except Exception as e:
        logger.error(f"Failed to get strategies: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get strategies: {str(e)}"
        )

@router.get("/strategies/{strategy_id}", response_model=StrategyStatusResponse)
async def get_strategy_status(strategy_id: str):
    """
    Get status of a specific strategy
    """
    try:
        if not strategy_executor:
            raise HTTPException(
                status_code=404,
                detail="Strategy executor not available"
            )
            
        strategy = strategy_executor.get_strategy_status(strategy_id)
        
        if not strategy:
            raise HTTPException(
                status_code=404,
                detail="Strategy not found"
            )
            
        return StrategyStatusResponse(
            strategy_id=strategy["strategy_id"],
            strategy_name=strategy["strategy_name"],
            status=strategy["status"].value if hasattr(strategy["status"], 'value') else str(strategy["status"]),
            created_at=strategy["created_at"].isoformat(),
            started_at=strategy["started_at"].isoformat() if strategy["started_at"] else None,
            stopped_at=strategy["stopped_at"].isoformat() if strategy["stopped_at"] else None,
            total_trades=strategy["total_trades"],
            winning_trades=strategy["winning_trades"],
            losing_trades=strategy["losing_trades"],
            total_profit=strategy["total_profit"],
            max_drawdown=strategy["max_drawdown"],
            current_positions=strategy["current_positions"] or [],
            error_message=strategy["error_message"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get strategy status: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get strategy status: {str(e)}"
        )

@router.post("/strategies/{strategy_id}/control")
async def control_strategy(strategy_id: str, action: str):
    """
    Control strategy execution (start, stop, pause)
    """
    try:
        if not strategy_executor:
            raise HTTPException(
                status_code=503,
                detail="Strategy executor not available"
            )
            
        if action.lower() == "stop":
            success = await strategy_executor.stop_strategy(strategy_id)
            if success:
                return {
                    "success": True,
                    "message": f"Strategy {strategy_id} stopped successfully",
                    "action": action
                }
            else:
                raise HTTPException(
                    status_code=400,
                    detail="Failed to stop strategy"
                )
        else:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported action: {action}"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Strategy control failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Strategy control failed: {str(e)}"
        )

@router.post("/strategies/{strategy_id}/close-positions")
async def close_strategy_positions(strategy_id: str):
    """
    Close all positions for a specific strategy
    """
    try:
        if not strategy_executor:
            raise HTTPException(
                status_code=503,
                detail="Strategy executor not available"
            )
            
        success = await strategy_executor.close_all_positions(strategy_id)
        
        if success:
            return {
                "success": True,
                "message": f"All positions closed for strategy {strategy_id}"
            }
        else:
            raise HTTPException(
                status_code=400,
                detail="Failed to close positions"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to close positions: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to close positions: {str(e)}"
        )

@router.get("/positions")
async def get_all_positions():
    """
    Get all current MT5 positions
    """
    try:
        if not strategy_executor or not strategy_executor.mt5_bridge:
            return {
                "positions": [],
                "message": "MT5 bridge not available"
            }
            
        # Get positions from MT5 bridge
        positions = []
        if hasattr(strategy_executor.mt5_bridge, 'get_positions'):
            positions = strategy_executor.mt5_bridge.get_positions() or []
            
        return {
            "positions": positions,
            "count": len(positions),
            "message": "Positions retrieved successfully"
        }
        
    except Exception as e:
        logger.error(f"Failed to get positions: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get positions: {str(e)}"
        )

@router.post("/validate-strategy")
async def validate_strategy_code(request: dict):
    """
    Validate strategy code without deploying
    """
    try:
        if not strategy_executor:
            await initialize_executor()
            
        if not strategy_executor:
            raise HTTPException(
                status_code=503,
                detail="Strategy executor not available"
            )
            
        strategy_code = request.get("strategy_code", "")
        
        if not strategy_code:
            raise HTTPException(
                status_code=400,
                detail="Strategy code is required"
            )
            
        validation_result = await strategy_executor.validate_strategy_code(strategy_code)
        
        return {
            "validation_result": validation_result,
            "message": "Strategy validation completed"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Strategy validation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Strategy validation failed: {str(e)}"
        )
