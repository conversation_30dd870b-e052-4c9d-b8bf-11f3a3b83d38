/**
 * Chat Page
 * Full-page AI chat interface
 */

// import React from 'react'; // Not needed with new JSX transform
import { MessageSquare } from 'lucide-react';
import { ChatWidget } from '@/components/chat/ChatWidget';

export function ChatPage() {
  return (
    <div className="h-full flex flex-col">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 flex items-center">
          <MessageSquare className="w-8 h-8 mr-3 text-blue-600" />
          AI Assistant
        </h1>
        <p className="text-gray-600 mt-2">
          Chat with our AI assistant for trading insights and strategy guidance
        </p>
      </div>

      <div className="flex-1">
        <ChatWidget className="h-full max-h-[600px]" />
      </div>
    </div>
  );
}