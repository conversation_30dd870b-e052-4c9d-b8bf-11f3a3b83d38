"""
Enhanced Backtesting Engine with TDD Focus
Provides comprehensive backtesting capabilities with enterprise-grade features.
"""

import asyncio
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import hashlib
import json

logger = logging.getLogger(__name__)


class BacktestStatus(Enum):
    """Backtest execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class DataIntegrityError(Exception):
    """Raised when data integrity checks fail"""
    pass


class StrategyError(Exception):
    """Raised when strategy execution fails"""
    pass


class BacktestConfigError(Exception):
    """Raised when backtest configuration is invalid"""
    pass


@dataclass
class BacktestConfig:
    """Backtest configuration with validation"""
    initial_capital: float
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    commission: float = 0.001  # 0.1% default commission
    slippage: float = 0.0001   # 0.01% default slippage
    max_position_size: float = 1.0  # 100% of capital max
    risk_free_rate: float = 0.02  # 2% annual risk-free rate
    
    def __post_init__(self):
        """Validate configuration after initialization"""
        if self.initial_capital <= 0:
            raise BacktestConfigError(f"Initial capital must be positive, got {self.initial_capital}")
        
        if self.commission < 0 or self.commission > 1:
            raise BacktestConfigError(f"Commission must be between 0 and 1, got {self.commission}")
        
        if self.slippage < 0 or self.slippage > 1:
            raise BacktestConfigError(f"Slippage must be between 0 and 1, got {self.slippage}")
        
        if self.max_position_size <= 0 or self.max_position_size > 1:
            raise BacktestConfigError(f"Max position size must be between 0 and 1, got {self.max_position_size}")
        
        if self.start_date and self.end_date and self.start_date >= self.end_date:
            raise BacktestConfigError("Start date must be before end date")


@dataclass
class Trade:
    """Individual trade record"""
    timestamp: datetime
    symbol: str
    action: str  # 'BUY' or 'SELL'
    quantity: float
    price: float
    commission: float
    slippage: float
    pnl: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data


@dataclass
class BacktestMetrics:
    """Comprehensive backtest performance metrics"""
    total_return: float
    annualized_return: float
    sharpe_ratio: float
    max_drawdown: float
    max_drawdown_duration: int
    win_rate: float
    profit_factor: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    avg_win: float
    avg_loss: float
    largest_win: float
    largest_loss: float
    volatility: float
    calmar_ratio: float
    sortino_ratio: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses"""
        return asdict(self)


@dataclass
class BacktestResult:
    """Complete backtest result with audit trail"""
    status: BacktestStatus
    config: BacktestConfig
    metrics: Optional[BacktestMetrics]
    trades: List[Trade]
    equity_curve: List[Dict[str, Any]]
    error_message: Optional[str] = None
    execution_time: Optional[float] = None
    data_hash: Optional[str] = None
    strategy_hash: Optional[str] = None
    timestamp: Optional[datetime] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now(timezone.utc)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        data = asdict(self)
        data['status'] = self.status.value
        data['timestamp'] = self.timestamp.isoformat()
        if self.config:
            data['config'] = asdict(self.config)
        if self.metrics:
            data['metrics'] = self.metrics.to_dict()
        data['trades'] = [trade.to_dict() for trade in self.trades]
        return data


class TradingStrategy:
    """Base class for trading strategies"""
    
    def __init__(self, params: Optional[Dict[str, Any]] = None):
        self.params = params or {}
        self.name = self.__class__.__name__
    
    def generate_signals(self, data: pd.DataFrame) -> pd.Series:
        """
        Generate trading signals from market data
        
        Args:
            data: DataFrame with OHLCV data
            
        Returns:
            Series with signals: 1 for BUY, -1 for SELL, 0 for HOLD
        """
        raise NotImplementedError("Strategy must implement generate_signals method")
    
    def get_position_size(self, signal: float, current_price: float, 
                         available_capital: float) -> float:
        """
        Calculate position size based on signal and available capital
        
        Args:
            signal: Trading signal strength
            current_price: Current asset price
            available_capital: Available capital for trading
            
        Returns:
            Position size (number of shares/units)
        """
        if signal == 0:
            return 0
        
        # Simple position sizing: use 10% of available capital per trade
        position_value = available_capital * 0.1
        return position_value / current_price if current_price > 0 else 0
    
    def get_hash(self) -> str:
        """Generate hash for strategy configuration"""
        strategy_data = {
            'name': self.name,
            'params': self.params
        }
        return hashlib.sha256(json.dumps(strategy_data, sort_keys=True).encode()).hexdigest()[:16]


class RSITradingStrategy(TradingStrategy):
    """RSI-based trading strategy implementation"""
    
    def __init__(self, params: Optional[Dict[str, Any]] = None):
        default_params = {
            'rsi_period': 14,
            'overbought': 70,
            'oversold': 30
        }
        if params:
            default_params.update(params)
        super().__init__(default_params)
    
    def generate_signals(self, data: pd.DataFrame) -> pd.Series:
        """Generate RSI-based trading signals"""
        if 'close' not in data.columns:
            raise StrategyError("Data must contain 'close' column")
        
        if len(data) < self.params['rsi_period'] + 1:
            raise StrategyError(f"Insufficient data: need at least {self.params['rsi_period'] + 1} rows")
        
        # Calculate RSI
        rsi_values = self._calculate_rsi(data['close'], self.params['rsi_period'])
        
        # Generate signals
        signals = pd.Series(0, index=data.index)
        signals[rsi_values <= self.params['oversold']] = 1   # BUY signal
        signals[rsi_values >= self.params['overbought']] = -1  # SELL signal
        
        return signals
    
    def _calculate_rsi(self, prices: pd.Series, period: int) -> pd.Series:
        """Calculate RSI indicator"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi


class BacktestEngine:
    """
    Enhanced backtesting engine with comprehensive features:
    - Data integrity validation
    - Multiple strategy support
    - Detailed performance metrics
    - Risk management
    - Audit trail generation
    """
    
    def __init__(self):
        self.trades: List[Trade] = []
        self.equity_curve: List[Dict[str, Any]] = []
        self.current_position = 0.0
        self.current_capital = 0.0
        
    def run(self, data: Union[pd.DataFrame, List[Dict]], 
            strategy: TradingStrategy, 
            config: Optional[Union[BacktestConfig, Dict, float]] = None) -> BacktestResult:
        """
        Run backtest with comprehensive validation and metrics
        
        Args:
            data: Market data (DataFrame or list of dicts)
            strategy: Trading strategy instance
            config: Backtest configuration (BacktestConfig, dict, or float for capital)
            
        Returns:
            BacktestResult with comprehensive metrics and audit trail
        """
        start_time = datetime.now()
        
        try:
            # Validate and prepare configuration
            if isinstance(config, (int, float)):
                config = BacktestConfig(initial_capital=float(config))
            elif isinstance(config, dict):
                config = BacktestConfig(**config)
            elif config is None:
                config = BacktestConfig(initial_capital=10000.0)
            
            # Validate and prepare data
            df = self._validate_and_prepare_data(data)
            
            # Initialize backtest state
            self._initialize_backtest(config)
            
            # Generate data hash for audit trail
            data_hash = self._generate_data_hash(df)
            strategy_hash = strategy.get_hash()
            
            # Run backtest simulation
            self._run_simulation(df, strategy, config)
            
            # Calculate comprehensive metrics
            metrics = self._calculate_metrics(config)
            
            # Calculate execution time
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return BacktestResult(
                status=BacktestStatus.COMPLETED,
                config=config,
                metrics=metrics,
                trades=self.trades.copy(),
                equity_curve=self.equity_curve.copy(),
                execution_time=execution_time,
                data_hash=data_hash,
                strategy_hash=strategy_hash
            )
            
        except Exception as e:
            logger.error(f"Backtest failed: {e}")
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # Ensure config is a BacktestConfig object for the error result
            error_config = None
            if 'config' in locals() and isinstance(config, BacktestConfig):
                error_config = config
            elif 'config' in locals() and config is not None:
                try:
                    if isinstance(config, (int, float)):
                        error_config = BacktestConfig(initial_capital=float(config))
                    elif isinstance(config, dict):
                        error_config = BacktestConfig(**config)
                except:
                    error_config = None
            
            return BacktestResult(
                status=BacktestStatus.FAILED,
                config=error_config,
                metrics=None,
                trades=self.trades.copy(),
                equity_curve=self.equity_curve.copy(),
                error_message=str(e),
                execution_time=execution_time
            )
    
    def _validate_and_prepare_data(self, data: Union[pd.DataFrame, List[Dict]]) -> pd.DataFrame:
        """Validate and prepare market data"""
        if data is None:
            raise DataIntegrityError("Data cannot be None")
        
        if isinstance(data, list):
            if len(data) == 0:
                raise DataIntegrityError("Data cannot be empty")
            df = pd.DataFrame(data)
        elif isinstance(data, pd.DataFrame):
            if data.empty:
                raise DataIntegrityError("Data cannot be empty")
            df = data.copy()
        else:
            raise DataIntegrityError(f"Data must be DataFrame or list, got {type(data)}")
        
        # Validate required columns
        required_columns = ['close']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise DataIntegrityError(f"Missing required columns: {missing_columns}")
        
        # Validate data types and values
        if not pd.api.types.is_numeric_dtype(df['close']):
            raise DataIntegrityError("Close prices must be numeric")
        
        if df['close'].isna().any():
            raise DataIntegrityError("Close prices cannot contain NaN values")
        
        if (df['close'] <= 0).any():
            raise DataIntegrityError("Close prices must be positive")
        
        # Add timestamp index if not present
        if not isinstance(df.index, pd.DatetimeIndex):
            df.index = pd.date_range(start='2020-01-01', periods=len(df), freq='D')
        
        return df
    
    def _initialize_backtest(self, config: BacktestConfig):
        """Initialize backtest state"""
        self.trades.clear()
        self.equity_curve.clear()
        self.current_position = 0.0
        self.current_capital = config.initial_capital
        
        # Add initial equity point
        self.equity_curve.append({
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'equity': self.current_capital,
            'position': self.current_position,
            'cash': self.current_capital
        })
    
    def _run_simulation(self, data: pd.DataFrame, strategy: TradingStrategy, config: BacktestConfig):
        """Run the main backtest simulation"""
        # Generate signals
        signals = strategy.generate_signals(data)
        
        for i, (timestamp, row) in enumerate(data.iterrows()):
            current_price = row['close']
            signal = signals.iloc[i] if i < len(signals) else 0
            
            # Calculate available capital
            portfolio_value = self.current_capital + (self.current_position * current_price)
            available_capital = portfolio_value * config.max_position_size
            
            # Determine position size
            target_position_size = strategy.get_position_size(signal, current_price, available_capital)
            position_change = target_position_size - self.current_position
            
            # Execute trade if position change is significant
            if abs(position_change) > 0.001:  # Minimum trade size threshold
                self._execute_trade(
                    timestamp=timestamp,
                    price=current_price,
                    quantity=position_change,
                    config=config
                )
            
            # Update equity curve
            portfolio_value = self.current_capital + (self.current_position * current_price)
            self.equity_curve.append({
                'timestamp': timestamp.isoformat() if hasattr(timestamp, 'isoformat') else str(timestamp),
                'equity': portfolio_value,
                'position': self.current_position,
                'cash': self.current_capital,
                'price': current_price
            })
    
    def _execute_trade(self, timestamp, price: float, quantity: float, config: BacktestConfig):
        """Execute a trade with commission and slippage"""
        if quantity == 0:
            return
        
        action = 'BUY' if quantity > 0 else 'SELL'
        abs_quantity = abs(quantity)
        
        # Apply slippage
        slippage_cost = price * config.slippage
        execution_price = price + (slippage_cost if quantity > 0 else -slippage_cost)
        
        # Calculate commission
        trade_value = abs_quantity * execution_price
        commission = trade_value * config.commission
        
        # Calculate total cost
        total_cost = trade_value + commission
        if quantity > 0:  # Buying
            total_cost += slippage_cost * abs_quantity
        else:  # Selling
            total_cost -= slippage_cost * abs_quantity
        
        # Update position and capital
        if quantity > 0:  # Buying
            if self.current_capital >= total_cost:
                self.current_position += abs_quantity
                self.current_capital -= total_cost
            else:
                # Insufficient capital - partial fill
                affordable_quantity = self.current_capital / (execution_price + commission/abs_quantity)
                if affordable_quantity > 0.001:
                    self.current_position += affordable_quantity
                    self.current_capital = 0
                    abs_quantity = affordable_quantity
                else:
                    return  # Cannot afford any shares
        else:  # Selling
            if self.current_position >= abs_quantity:
                self.current_position -= abs_quantity
                self.current_capital += (trade_value - commission - slippage_cost * abs_quantity)
            else:
                # Insufficient position - sell what we have
                if self.current_position > 0.001:
                    abs_quantity = self.current_position
                    self.current_capital += (self.current_position * execution_price - commission)
                    self.current_position = 0
                else:
                    return  # No position to sell
        
        # Record trade
        trade = Trade(
            timestamp=timestamp if hasattr(timestamp, 'isoformat') else datetime.now(timezone.utc),
            symbol='ASSET',  # Generic symbol
            action=action,
            quantity=abs_quantity,
            price=execution_price,
            commission=commission,
            slippage=slippage_cost * abs_quantity,
            pnl=0.0  # Will be calculated in metrics
        )
        
        self.trades.append(trade)
    
    def _calculate_metrics(self, config: BacktestConfig) -> BacktestMetrics:
        """Calculate comprehensive performance metrics"""
        if not self.equity_curve or len(self.equity_curve) < 2:
            return self._get_empty_metrics()
        
        # Extract equity values
        equity_values = [point['equity'] for point in self.equity_curve]
        equity_series = pd.Series(equity_values)
        
        # Calculate returns
        returns = equity_series.pct_change().dropna()
        
        # Basic metrics
        total_return = (equity_values[-1] / equity_values[0] - 1) * 100
        
        # Annualized return (assuming daily data)
        days = len(equity_values)
        years = days / 365.25
        annualized_return = ((equity_values[-1] / equity_values[0]) ** (1/years) - 1) * 100 if years > 0 else 0
        
        # Risk metrics
        volatility = returns.std() * np.sqrt(252) * 100  # Annualized volatility
        
        # Sharpe ratio
        excess_returns = returns - (config.risk_free_rate / 252)  # Daily risk-free rate
        sharpe_ratio = excess_returns.mean() / excess_returns.std() * np.sqrt(252) if excess_returns.std() > 0 else 0
        
        # Sortino ratio (downside deviation)
        downside_returns = returns[returns < 0]
        downside_deviation = downside_returns.std() * np.sqrt(252)
        sortino_ratio = (annualized_return / 100 - config.risk_free_rate) / downside_deviation if downside_deviation > 0 else 0
        
        # Drawdown analysis
        peak = equity_series.expanding().max()
        drawdown = (equity_series - peak) / peak * 100
        max_drawdown = drawdown.min()
        
        # Drawdown duration
        drawdown_duration = 0
        current_duration = 0
        for dd in drawdown:
            if dd < 0:
                current_duration += 1
                drawdown_duration = max(drawdown_duration, current_duration)
            else:
                current_duration = 0
        
        # Calmar ratio
        calmar_ratio = annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0
        
        # Trade analysis
        trade_metrics = self._analyze_trades()
        
        return BacktestMetrics(
            total_return=total_return,
            annualized_return=annualized_return,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            max_drawdown_duration=drawdown_duration,
            volatility=volatility,
            calmar_ratio=calmar_ratio,
            sortino_ratio=sortino_ratio,
            **trade_metrics
        )
    
    def _analyze_trades(self) -> Dict[str, Any]:
        """Analyze trade performance"""
        if not self.trades:
            return {
                'win_rate': 0.0,
                'profit_factor': 0.0,
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'avg_win': 0.0,
                'avg_loss': 0.0,
                'largest_win': 0.0,
                'largest_loss': 0.0
            }
        
        # Calculate P&L for each trade pair (buy-sell)
        pnl_list = []
        position = 0
        avg_cost = 0
        
        for trade in self.trades:
            if trade.action == 'BUY':
                if position == 0:
                    avg_cost = trade.price
                else:
                    avg_cost = (avg_cost * position + trade.price * trade.quantity) / (position + trade.quantity)
                position += trade.quantity
            else:  # SELL
                if position > 0:
                    pnl = (trade.price - avg_cost) * min(trade.quantity, position) - trade.commission - trade.slippage
                    pnl_list.append(pnl)
                    position -= min(trade.quantity, position)
        
        if not pnl_list:
            return self._get_empty_trade_metrics()
        
        # Analyze P&L
        winning_trades = [pnl for pnl in pnl_list if pnl > 0]
        losing_trades = [pnl for pnl in pnl_list if pnl < 0]
        
        total_trades = len(pnl_list)
        win_count = len(winning_trades)
        loss_count = len(losing_trades)
        
        win_rate = (win_count / total_trades * 100) if total_trades > 0 else 0
        avg_win = sum(winning_trades) / len(winning_trades) if winning_trades else 0
        avg_loss = sum(losing_trades) / len(losing_trades) if losing_trades else 0
        
        gross_profit = sum(winning_trades) if winning_trades else 0
        gross_loss = abs(sum(losing_trades)) if losing_trades else 0
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf') if gross_profit > 0 else 0
        
        return {
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'total_trades': total_trades,
            'winning_trades': win_count,
            'losing_trades': loss_count,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'largest_win': max(winning_trades) if winning_trades else 0,
            'largest_loss': min(losing_trades) if losing_trades else 0
        }
    
    def _get_empty_metrics(self) -> BacktestMetrics:
        """Return empty metrics for failed backtests"""
        return BacktestMetrics(
            total_return=0.0,
            annualized_return=0.0,
            sharpe_ratio=0.0,
            max_drawdown=0.0,
            max_drawdown_duration=0,
            volatility=0.0,
            calmar_ratio=0.0,
            sortino_ratio=0.0,
            **self._get_empty_trade_metrics()
        )
    
    def _get_empty_trade_metrics(self) -> Dict[str, Any]:
        """Return empty trade metrics"""
        return {
            'win_rate': 0.0,
            'profit_factor': 0.0,
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'avg_win': 0.0,
            'avg_loss': 0.0,
            'largest_win': 0.0,
            'largest_loss': 0.0
        }
    
    def _generate_data_hash(self, data: pd.DataFrame) -> str:
        """Generate hash for data integrity verification"""
        data_str = data.to_string()
        return hashlib.sha256(data_str.encode()).hexdigest()[:16]