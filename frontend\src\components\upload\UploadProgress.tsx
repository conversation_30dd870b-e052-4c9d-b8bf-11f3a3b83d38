/**
 * Upload Progress Component
 * Real-time progress display for file processing
 */

// import React from 'react'; // Not needed with new JSX transform
import { CheckCircle, AlertCircle, Clock, FileText, Database, X } from 'lucide-react';

import type { FileProcessingProgress } from '@shared/schemas';

interface UploadProgressProps {
  progress: FileProcessingProgress;
  onCancel?: () => void;
}

const STEP_ICONS = {
  pending: Clock,
  parsing: FileText,
  validating: CheckCircle,
  inserting: Database,
  completed: CheckCircle,
  error: AlertCircle,
};

const STEP_LABELS = {
  pending: 'Queued for processing',
  parsing: 'Parsing file structure',
  validating: 'Validating data quality',
  inserting: 'Inserting into database',
  completed: 'Processing complete',
  error: 'Processing failed',
};

const STEP_DESCRIPTIONS = {
  pending: 'Your file is in the processing queue',
  parsing: 'Reading and interpreting your file format',
  validating: 'Checking data quality and OHLC relationships',
  inserting: 'Saving validated data to the database',
  completed: 'Your trading data is ready for analysis',
  error: 'An error occurred during processing',
};

export function UploadProgress({ progress, onCancel }: UploadProgressProps) {
  const {
    status,
    progress_percent,
    rows_processed,
    total_rows,
    current_step,
    error_message,
    timestamp,
  } = progress;

  const Icon = STEP_ICONS[status] || Clock;
  const isError = status === 'error';
  const isCompleted = status === 'completed';
  
  const getStatusColor = () => {
    if (isError) return 'red';
    if (isCompleted) return 'green';
    return 'blue';
  };

  const statusColor = getStatusColor();

  return (
    <div className="w-full max-w-2xl mx-auto bg-white rounded-lg shadow-lg overflow-hidden">
      {/* Header */}
      <div className={`px-6 py-4 bg-${statusColor}-50 border-b border-${statusColor}-200`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Icon className={`w-6 h-6 text-${statusColor}-600`} />
            <div>
              <h3 className={`text-lg font-semibold text-${statusColor}-900`}>
                {STEP_LABELS[status]}
              </h3>
              <p className={`text-sm text-${statusColor}-700`}>
                {STEP_DESCRIPTIONS[status]}
              </p>
            </div>
          </div>
          
          {onCancel && !isCompleted && (
            <button
              onClick={onCancel}
              className={`text-${statusColor}-400 hover:text-${statusColor}-600`}
            >
              <X className="w-5 h-5" />
            </button>
          )}
        </div>
      </div>

      {/* Progress Content */}
      <div className="p-6">
        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">
              Overall Progress
            </span>
            <span className="text-sm text-gray-500">
              {progress_percent}%
            </span>
          </div>
          
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div
              className={`h-3 rounded-full transition-all duration-500 ease-out bg-${statusColor}-600`}
              style={{ width: `${progress_percent}%` }}
            />
          </div>
        </div>

        {/* Current Step */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Current Step</h4>
          <div className={`p-3 bg-${statusColor}-50 border border-${statusColor}-200 rounded-lg`}>
            <div className="flex items-center space-x-2">
              <Icon className={`w-4 h-4 text-${statusColor}-600`} />
              <span className={`text-sm font-medium text-${statusColor}-900`}>
                {current_step}
              </span>
            </div>
          </div>
        </div>

        {/* Row Progress */}
        {rows_processed > 0 && (
          <div className="mb-6">
            <h4 className="text-sm font-medium text-gray-900 mb-2">Data Progress</h4>
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="text-2xl font-bold text-gray-900">
                  {rows_processed.toLocaleString()}
                </div>
                <div className="text-sm text-gray-600">Rows Processed</div>
              </div>
              
              {total_rows && (
                <div className="bg-gray-50 p-3 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900">
                    {total_rows.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-600">Total Rows</div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Processing Steps Visual */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Processing Steps</h4>
          <div className="space-y-3">
            {['parsing', 'validating', 'inserting', 'completed'].map((step, index) => {
              const isCurrentStep = status === step;
              const isCompletedStep = ['parsing', 'validating', 'inserting'].indexOf(status) > index;
              const isActive = isCurrentStep || isCompletedStep || (isCompleted && step === 'completed');
              
              const StepIcon = STEP_ICONS[step as keyof typeof STEP_ICONS];
              
              return (
                <div key={step} className="flex items-center space-x-3">
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center transition-colors ${
                      isActive
                        ? `bg-${statusColor}-600 text-white`
                        : 'bg-gray-200 text-gray-400'
                    }`}
                  >
                    {isCompletedStep || (isCompleted && step !== 'completed') ? (
                      <CheckCircle className="w-4 h-4" />
                    ) : (
                      <StepIcon className="w-4 h-4" />
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <div
                      className={`text-sm font-medium ${
                        isActive ? 'text-gray-900' : 'text-gray-500'
                      }`}
                    >
                      {STEP_LABELS[step as keyof typeof STEP_LABELS]}
                    </div>
                    {isCurrentStep && (
                      <div className="text-xs text-gray-600 mt-1">
                        {current_step}
                      </div>
                    )}
                  </div>
                  
                  {isCurrentStep && !isError && !isCompleted && (
                    <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Error Display */}
        {isError && error_message && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-start space-x-2">
              <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
              <div>
                <h5 className="text-sm font-medium text-red-900">Error Details</h5>
                <p className="text-sm text-red-700 mt-1">{error_message}</p>
              </div>
            </div>
          </div>
        )}

        {/* Completion Message */}
        {isCompleted && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <div>
                <h5 className="text-sm font-medium text-green-900">
                  Processing Complete!
                </h5>
                <p className="text-sm text-green-700 mt-1">
                  Your trading data has been successfully processed and is ready for analysis.
                  {rows_processed > 0 && (
                    <span className="ml-1">
                      {rows_processed.toLocaleString()} rows were imported.
                    </span>
                  )}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Timestamp */}
        <div className="text-xs text-gray-500 text-center">
          Last updated: {new Date(timestamp).toLocaleTimeString()}
        </div>
      </div>

      {/* Footer Actions */}
      {(isCompleted || isError) && (
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
          <div className="flex justify-end space-x-3">
            {isError && onCancel && (
              <button
                onClick={onCancel}
                className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Try Again
              </button>
            )}
            
            {isCompleted && (
              <button
                onClick={onCancel}
                className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700"
              >
                Continue
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
}