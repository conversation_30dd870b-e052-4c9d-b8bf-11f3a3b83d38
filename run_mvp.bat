@echo off
echo Starting AI Enhanced Trading Platform MVP...

REM Start the backend server in a new window
start cmd /k "title Backend Server && python backend/minimal_server.py"

REM Wait for the backend to start
echo Waiting for backend to start...
timeout /t 3 /nobreak > nul

REM Start the frontend in a new window
start cmd /k "title Frontend Server && cd frontend && set VITE_API_URL=http://localhost:8000 && npx vite --config vite.config.mvp.ts"

echo MVP is running!
echo Backend: http://localhost:8000
echo Frontend: http://localhost:5173