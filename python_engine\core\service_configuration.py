"""
Service Configuration for Dependency Injection

Centralized configuration for all service registrations and dependency injection setup.
"""

from typing import Dict, Any, Optional
from enum import Enum

from core.dependency_injection import DependencyContainer
from core.interfaces import (
    IMarketDataService, IStrategyService, ITradingService,
    IRiskManagementService, IPortfolioService, INotificationService,
    IDataStorageService, ILoggingService, IConfigurationService
)

class ServiceMode(Enum):
    """Service configuration modes"""
    PRODUCTION = "production"
    DEVELOPMENT = "development"
    TESTING = "testing"
    MOCK = "mock"

class ServiceConfigurator:
    """Centralized service configuration"""
    
    def __init__(self, container: Optional[DependencyContainer] = None):
        self.container = container or DependencyContainer()
        self._configured_mode: Optional[ServiceMode] = None
    
    def configure_for_production(self) -> DependencyContainer:
        """Configure services for production environment"""
        self._configured_mode = ServiceMode.PRODUCTION
        
        # Configuration service (must be first)
        from services.configuration_service import FileConfigurationService
        self.container.register(IConfigurationService, FileConfigurationService, singleton=True)
        
        # Logging service (depends on configuration)
        from services.logging_service import FileLoggingService
        self.container.register(ILoggingService, FileLoggingService, singleton=True)
        
        # Market data service
        from services.market_data_service import YFinanceMarketDataService
        self.container.register(IMarketDataService, YFinanceMarketDataService, singleton=True)
        
        # Strategy service
        from services.strategy_service import DarwinGodelStrategyService
        self.container.register(IStrategyService, DarwinGodelStrategyService, singleton=True)
        
        # Trading service (would be actual broker integration)
        from services.mock_services import MockTradingService  # Replace with real implementation
        self.container.register(ITradingService, MockTradingService, singleton=True)
        
        # Risk management service
        from services.mock_services import MockRiskManagementService  # Replace with real implementation
        self.container.register(IRiskManagementService, MockRiskManagementService, singleton=True)
        
        # Portfolio service
        from services.mock_services import MockPortfolioService  # Replace with real implementation
        self.container.register(IPortfolioService, MockPortfolioService, singleton=True)
        
        # Notification service
        from services.mock_services import MockNotificationService  # Replace with real implementation
        self.container.register(INotificationService, MockNotificationService, singleton=True)
        
        # Data storage service
        from services.mock_services import MockDataStorageService  # Replace with real implementation
        self.container.register(IDataStorageService, MockDataStorageService, singleton=True)
        
        # Trading engine
        from core.trading_engine import TradingEngine
        self.container.register(TradingEngine, TradingEngine, singleton=True)
        
        return self.container
    
    def configure_for_development(self) -> DependencyContainer:
        """Configure services for development environment"""
        self._configured_mode = ServiceMode.DEVELOPMENT
        
        # Configuration service with development settings
        from services.configuration_service import FileConfigurationService
        self.container.register(IConfigurationService, FileConfigurationService, singleton=True)
        
        # Console logging for development
        from services.logging_service import ConsoleLoggingService
        self.container.register(ILoggingService, ConsoleLoggingService, singleton=True)
        
        # Real market data service
        from services.market_data_service import YFinanceMarketDataService
        self.container.register(IMarketDataService, YFinanceMarketDataService, singleton=True)
        
        # Strategy service with Darwin-Godel
        from services.strategy_service import DarwinGodelStrategyService
        self.container.register(IStrategyService, DarwinGodelStrategyService, singleton=True)
        
        # Mock services for development (safer than real trading)
        from services.mock_services import (
            MockTradingService, MockRiskManagementService, MockPortfolioService,
            MockNotificationService, MockDataStorageService
        )
        
        self.container.register(ITradingService, MockTradingService, singleton=True)
        self.container.register(IRiskManagementService, MockRiskManagementService, singleton=True)
        self.container.register(IPortfolioService, MockPortfolioService, singleton=True)
        self.container.register(INotificationService, MockNotificationService, singleton=True)
        self.container.register(IDataStorageService, MockDataStorageService, singleton=True)
        
        # Trading engine
        from core.trading_engine import TradingEngine
        self.container.register(TradingEngine, TradingEngine, singleton=True)
        
        return self.container
    
    def configure_for_testing(self) -> DependencyContainer:
        """Configure services for testing environment"""
        self._configured_mode = ServiceMode.TESTING
        
        # Mock configuration service
        from services.configuration_service import MockConfigurationService
        self.container.register(IConfigurationService, MockConfigurationService, singleton=True)
        
        # Mock logging service
        from services.logging_service import MockLoggingService
        self.container.register(ILoggingService, MockLoggingService, singleton=True)
        
        # Mock market data service
        from services.market_data_service import MockMarketDataService
        self.container.register(IMarketDataService, MockMarketDataService, singleton=True)
        
        # Mock strategy service
        from services.strategy_service import MockStrategyService
        self.container.register(IStrategyService, MockStrategyService, singleton=True)
        
        # All mock services for testing
        from services.mock_services import (
            MockTradingService, MockRiskManagementService, MockPortfolioService,
            MockNotificationService, MockDataStorageService
        )
        
        self.container.register(ITradingService, MockTradingService, singleton=True)
        self.container.register(IRiskManagementService, MockRiskManagementService, singleton=True)
        self.container.register(IPortfolioService, MockPortfolioService, singleton=True)
        self.container.register(INotificationService, MockNotificationService, singleton=True)
        self.container.register(IDataStorageService, MockDataStorageService, singleton=True)
        
        # Trading engine
        from core.trading_engine import TradingEngine
        self.container.register(TradingEngine, TradingEngine, singleton=True)
        
        return self.container
    
    def configure_for_backtesting(self) -> DependencyContainer:
        """Configure services for backtesting"""
        self._configured_mode = ServiceMode.TESTING
        
        # Configuration service
        from services.configuration_service import MockConfigurationService
        config = MockConfigurationService({
            'engine': {
                'auto_trading_enabled': False,  # No real trading in backtest
                'signal_processing_interval': 0.1,  # Faster processing
                'risk_check_enabled': True,
                'notification_enabled': False  # No notifications in backtest
            }
        })
        self.container.register_instance(IConfigurationService, config)
        
        # Mock logging
        from services.logging_service import MockLoggingService
        self.container.register(ILoggingService, MockLoggingService, singleton=True)
        
        # Mock market data (will be fed historical data)
        from services.market_data_service import MockMarketDataService
        self.container.register(IMarketDataService, MockMarketDataService, singleton=True)
        
        # Backtest strategy service
        from services.strategy_service import BacktestStrategyService
        self.container.register(IStrategyService, BacktestStrategyService, singleton=True)
        
        # Mock services configured for backtesting
        from services.mock_services import (
            MockTradingService, MockRiskManagementService, MockPortfolioService,
            MockNotificationService, MockDataStorageService
        )
        
        # Configure mock services for backtesting
        trading_service = MockTradingService()
        trading_service.set_account_balance(100000.0, 100000.0)  # $100k starting capital
        self.container.register_instance(ITradingService, trading_service)
        
        risk_service = MockRiskManagementService()
        risk_service.set_max_position_size(1000)
        self.container.register_instance(IRiskManagementService, risk_service)
        
        portfolio_service = MockPortfolioService()
        portfolio_service.set_portfolio_value(100000.0)
        self.container.register_instance(IPortfolioService, portfolio_service)
        
        notification_service = MockNotificationService()
        notification_service.set_notification_enabled(False)
        self.container.register_instance(INotificationService, notification_service)
        
        self.container.register(IDataStorageService, MockDataStorageService, singleton=True)
        
        return self.container
    
    def configure_custom(self, service_overrides: Dict[type, Any]) -> DependencyContainer:
        """Configure with custom service overrides"""
        # Start with development configuration
        self.configure_for_development()
        
        # Apply overrides
        for interface, implementation in service_overrides.items():
            if isinstance(implementation, type):
                self.container.register(interface, implementation, singleton=True)
            else:
                self.container.register_instance(interface, implementation)
        
        return self.container
    
    def get_configured_mode(self) -> Optional[ServiceMode]:
        """Get the currently configured mode"""
        return self._configured_mode
    
    def validate_configuration(self) -> Dict[str, Any]:
        """Validate the current service configuration"""
        required_services = [
            IConfigurationService,
            ILoggingService,
            IMarketDataService,
            IStrategyService,
            ITradingService,
            IRiskManagementService,
            IPortfolioService,
            INotificationService,
            IDataStorageService
        ]
        
        validation_result = {
            'is_valid': True,
            'missing_services': [],
            'registered_services': [],
            'mode': self._configured_mode.value if self._configured_mode else 'unknown'
        }
        
        for service_interface in required_services:
            if self.container.is_registered(service_interface):
                validation_result['registered_services'].append(service_interface.__name__)
            else:
                validation_result['missing_services'].append(service_interface.__name__)
                validation_result['is_valid'] = False
        
        return validation_result

# Global service configurator
_global_configurator: Optional[ServiceConfigurator] = None

def get_service_configurator() -> ServiceConfigurator:
    """Get the global service configurator"""
    global _global_configurator
    if _global_configurator is None:
        _global_configurator = ServiceConfigurator()
    return _global_configurator

def configure_services_for_mode(mode: ServiceMode) -> DependencyContainer:
    """Configure services for a specific mode"""
    configurator = get_service_configurator()
    
    if mode == ServiceMode.PRODUCTION:
        return configurator.configure_for_production()
    elif mode == ServiceMode.DEVELOPMENT:
        return configurator.configure_for_development()
    elif mode == ServiceMode.TESTING:
        return configurator.configure_for_testing()
    else:
        raise ValueError(f"Unknown service mode: {mode}")

def get_configured_container() -> DependencyContainer:
    """Get the configured container"""
    return get_service_configurator().container

def reset_service_configuration():
    """Reset service configuration (useful for testing)"""
    global _global_configurator
    _global_configurator = None