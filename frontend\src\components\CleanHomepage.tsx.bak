import React, { useState, useEffect } from 'react';
import StrategyChatbot from './StrategyChatbot';
import { aiPromptsService } from '../services/aiPrompts';
import './CleanHomepage.css';

interface AIPrompt {
  id: string;
  title: string;
  description: string;
  category: string;
  prompt: string;
}

const CleanHomepage: React.FC = () => {
  const [prompts, setPrompts] = useState<AIPrompt[]>([]);
  const [loading, setLoading] = useState(true);
  const [activePrompt, setActivePrompt] = useState<string | null>(null);
  const [selectedPrompt, setSelectedPrompt] = useState<string>('');
  const [showChatbot, setShowChatbot] = useState(false);

  useEffect(() => {
    const fetchPrompts = async () => {
      try {
        const fetchedPrompts = await aiPromptsService.getPrompts();
        setPrompts(fetchedPrompts);
      } catch (error) {
        console.error('Failed to fetch prompts:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPrompts();
  }, []);

  const handlePromptSelect = (prompt: AIPrompt) => {
    setActivePrompt(prompt.id);
    setSelectedPrompt(prompt.prompt);
    setShowChatbot(true);
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    element?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <div className="clean-homepage">
      {/* Navigation */}
      <nav className="navbar">
        <div className="nav-container">
          <div className="logo">TradeBuilder</div>
          <div className="nav-links">
            <a href="#features" onClick={(e) => { e.preventDefault(); scrollToSection('features'); }}>Features</a>
            <a href="#how-it-works" onClick={(e) => { e.preventDefault(); scrollToSection('how-it-works'); }}>How It Works</a>
            <a href="#chatbot" onClick={(e) => { e.preventDefault(); scrollToSection('chatbot'); }}>AI Chatbot</a>
            <a href="#mql5-tools" onClick={(e) => { e.preventDefault(); scrollToSection('mql5-tools'); }}>MQL5 Tools</a>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="hero">
        <div className="hero-content">
          <h1>Build Trading Strategies Without Code</h1>
          <p>Describe your trading idea in plain English, and we'll build it for you. Connect directly to MT5 without writing a single line of code.</p>
          <a href="#chatbot" className="btn" onClick={(e) => { e.preventDefault(); scrollToSection('chatbot'); setShowChatbot(true); }}>Try The AI Chatbot</a>
          <a href="#features" className="btn btn-secondary" onClick={(e) => { e.preventDefault(); scrollToSection('features'); }}>Explore Features</a>
        </div>
      </section>

      {/* Chatbot Section */}
      <section className="chatbot-section section" id="chatbot">
        <h2>AI Trading Assistant</h2>
        <div className="chatbot-container">
          <div className="prompt-sidebar">
            <h3>Strategy Templates</h3>
            {loading ? (
              <div className="loading">Loading templates...</div>
            ) : (
              <div className="prompts-list">
                {prompts.map((prompt) => (
                  <div 
                    key={prompt.id}
                    className={`prompt-item ${activePrompt === prompt.id ? 'active' : ''}`}
                    onClick={() => handlePromptSelect(prompt)}
                  >
                    <h4>{prompt.title}</h4>
                    <p>{prompt.description}</p>
                  </div>
                ))}
              </div>
            )}
          </div>
          <div className="chatbot-wrapper">
            {showChatbot ? (
              <StrategyChatbot initialPrompt={selectedPrompt} />
            ) : (
              <div className="chatbot-placeholder">
                <h3>Select a template or start a conversation</h3>
                <p>Choose from one of our strategy templates on the left or click the button below to start with a blank slate.</p>
                <button className="btn" onClick={() => setShowChatbot(true)}>Start New Strategy</button>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="features section" id="features">
        <h2>Platform Features</h2>
        <div className="feature-grid">
          <div className="feature">
            <div className="feature-icon">🐍</div>
            <h3>Python Strategy Building</h3>
            <p>Build complete trading strategies in Python with natural language instructions. No coding experience required.</p>
          </div>
            
          <div className="feature">
            <div className="feature-icon">📊</div>
            <h3>Backtesting Engine</h3>
            <p>Test your strategies with historical market data to evaluate performance before live trading.</p>
          </div>
            
          <div className="feature">
            <div className="feature-icon">�</div>
            <h3>Historical Data Upload</h3>
            <p>Upload and validate your own historical trading data with our advanced data integrity pipeline.</p>
          </div>
            
          <div className="feature">
            <div className="feature-icon">�🚀</div>
            <h3>MT5 Integration</h3>
            <p>Deploy strategies directly to MetaTrader 5 with our secure bridge connection.</p>
          </div>
            
          <div className="feature">
            <div className="feature-icon">🧰</div>
            <h3>Custom MT5 Indicators</h3>
            <p>Create custom MQL5 indicators without paying marketplace fees (premium feature).</p>
          </div>
            
          <div className="feature">
            <div className="feature-icon">📝</div>
            <h3>Ready-to-Use Templates</h3>
            <p>Access professional trading strategy templates for quick deployment.</p>
          </div>
            
          <div className="feature">
            <div className="feature-icon">🔍</div>
            <h3>Performance Analytics</h3>
            <p>Evaluate your strategies with detailed metrics and performance indicators.</p>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="how-it-works section" id="how-it-works">
        <h2>How It Works</h2>
        <div className="steps">
          <div className="step">
            <div className="step-number">1</div>
            <h3>Describe Your Strategy</h3>
            <p>Enter your trading idea in the AI chatbot using plain English or select from our templates.</p>
          </div>
            
          <div className="step">
            <div className="step-number">2</div>
            <h3>Upload Your Data</h3>
            <p>Upload and validate your own historical trading data or use our quality data for testing.</p>
          </div>
            
          <div className="step">
            <div className="step-number">3</div>
            <h3>Generate Code</h3>
            <p>The AI creates Python code implementing your strategy with proper risk management parameters.</p>
          </div>
            
          <div className="step">
            <div className="step-number">4</div>
            <h3>Backtest</h3>
            <p>Test your strategy against historical market data to evaluate performance and optimize.</p>
          </div>
            
          <div className="step">
            <div className="step-number">5</div>
            <h3>Deploy</h3>
            <p>Deploy your strategy to MetaTrader 5 through our secure bridge and monitor performance.</p>
          </div>
        </div>
      </section>

      {/* Chatbot Section */}
      <section className="chatbot-section section" id="chatbot">
        <h2>AI Trading Assistant</h2>
        <div className="chatbot-container">
          <div className="prompt-sidebar">
            <h3>Strategy Templates</h3>
            {loading ? (
              <div className="loading">Loading templates...</div>
            ) : (
              <div className="prompts-list">
                {prompts.map((prompt) => (
                  <div 
                    key={prompt.id}
                    className={`prompt-item ${activePrompt === prompt.id ? 'active' : ''}`}
                    onClick={() => handlePromptSelect(prompt)}
                  >
                    <h4>{prompt.title}</h4>
                    <p>{prompt.description}</p>
                  </div>
                ))}
              </div>
            )}
          </div>
          <div className="chatbot-wrapper">
            {showChatbot ? (
              <StrategyChatbot initialPrompt={selectedPrompt} />
            ) : (
              <div className="chatbot-placeholder">
                <h3>Select a template or start a conversation</h3>
                <p>Choose from one of our strategy templates on the left or click the button below to start with a blank slate.</p>
                <button className="btn" onClick={() => setShowChatbot(true)}>Start New Strategy</button>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* MQL5 Tools Section */}
      <section className="mql5-tools section" id="mql5-tools">
        <h2>MQL5 Tools Without the Cost</h2>
        <div className="mql5-container">
          <div className="mql5-description">
            <h3>Create Custom MT5 Indicators & Expert Advisors</h3>
            <p>Build professional-grade MQL5 indicators and Expert Advisors without paying marketplace fees or hiring developers.</p>
            <ul>
              <li>Generate complete MQL5 code from natural language descriptions</li>
              <li>Customize indicators with your specific requirements</li>
              <li>Export directly to MetaTrader 5</li>
              <li>Free from MQL5 marketplace fees</li>
            </ul>
            <button className="btn" onClick={() => {
              const mql5Prompt = prompts.find(p => p.id === 'mql5_indicator' || p.category === 'trade_execution');
              if (mql5Prompt) {
                handlePromptSelect(mql5Prompt);
              } else {
                setSelectedPrompt("Create a custom MT5 indicator that identifies divergence between price and RSI");
                setShowChatbot(true);
              }
              scrollToSection('chatbot');
            }}>
              Try MQL5 Generator
            </button>
          </div>
          <div className="code-preview">
            <pre>
              <code>
{`//+------------------------------------------------------------------+
//|                                          RSI_Divergence.mq5 |
//|                      Copyright 2025, AI Trading Platform          |
//|                                     https://tradebuilder.ai       |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, AI Trading Platform"
#property link      "https://tradebuilder.ai"
#property version   "1.00"
#property indicator_chart_window
#property indicator_buffers 3
#property indicator_plots   3

//--- plot DiverBull
#property indicator_label1  "Bullish Divergence"
#property indicator_type1   DRAW_ARROW
#property indicator_color1  clrGreen
#property indicator_width1  2
//--- plot DiverBear
#property indicator_label2  "Bearish Divergence"
#property indicator_type2   DRAW_ARROW
#property indicator_color2  clrRed
#property indicator_width2  2
//--- plot RSI
#property indicator_label3  "RSI"
#property indicator_type3   DRAW_LINE
#property indicator_color3  clrBlue
#property indicator_style3  STYLE_SOLID
#property indicator_width3  1

//--- input parameters
input int      RSI_Period=14;       // RSI Period
input double   OverboughtLevel=70;  // Overbought Level
input double   OversoldLevel=30;    // Oversold Level`}
              </code>
            </pre>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="footer-links">
          <a href="#features" onClick={(e) => { e.preventDefault(); scrollToSection('features'); }}>Features</a>
          <a href="#how-it-works" onClick={(e) => { e.preventDefault(); scrollToSection('how-it-works'); }}>How It Works</a>
          <a href="#chatbot" onClick={(e) => { e.preventDefault(); scrollToSection('chatbot'); }}>AI Chatbot</a>
          <a href="#mql5-tools" onClick={(e) => { e.preventDefault(); scrollToSection('mql5-tools'); }}>MQL5 Tools</a>
        </div>
        <p>&copy; 2025 TradeBuilder. Built for traders, by traders.</p>
      </footer>
    </div>
  );
};

export default CleanHomepage;
