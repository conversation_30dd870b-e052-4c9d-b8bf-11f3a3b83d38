"""
Test suite for historical data upload functionality.
Following TDD principles, these tests define the expected behavior
of the historical data endpoints.
"""

import os
import json
import pytest
import shutil
from fastapi.testclient import TestClient
from io import BytesIO
import pandas as pd
from datetime import datetime
import sys

# Add the parent directory to the path so we can import the server
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from backend.minimal_server import app, DATA_DIR

# Create a test client
client = TestClient(app)

# Test credentials
TEST_USERNAME = "admin"
TEST_PASSWORD = "trading123"
auth = (TEST_USERNAME, TEST_PASSWORD)

# Test data directory
TEST_DATA_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), "test_data")


@pytest.fixture(scope="function")
def setup_test_environment():
    """Set up a clean test environment before each test."""
    # Create test data directory if it doesn't exist
    os.makedirs(TEST_DATA_DIR, exist_ok=True)
    
    # Override the DATA_DIR in the server
    import backend.minimal_server
    backend.minimal_server.DATA_DIR = TEST_DATA_DIR
    
    yield
    
    # Clean up after the test
    if os.path.exists(TEST_DATA_DIR):
        shutil.rmtree(TEST_DATA_DIR)


def create_test_csv(filename, rows=10):
    """Create a test CSV file with historical data."""
    data = {
        'timestamp': [datetime(2023, 1, 1, i).isoformat() for i in range(rows)],
        'open': [1.1 + i*0.01 for i in range(rows)],
        'high': [1.15 + i*0.01 for i in range(rows)],
        'low': [1.05 + i*0.01 for i in range(rows)],
        'close': [1.12 + i*0.01 for i in range(rows)],
        'volume': [1000 + i*100 for i in range(rows)]
    }
    df = pd.DataFrame(data)
    csv_file = BytesIO()
    df.to_csv(csv_file, index=False)
    csv_file.seek(0)
    return csv_file


def test_list_historical_data_empty(setup_test_environment):
    """Test listing historical data when none exists."""
    response = client.get("/api/historical-data", auth=auth)
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    # Should return the default sample data
    assert "EURUSD_H1" in response.json()
    assert "GBPUSD_H4" in response.json()


def test_upload_valid_csv(setup_test_environment):
    """Test uploading a valid CSV file."""
    csv_file = create_test_csv("test.csv")
    
    response = client.post(
        "/api/historical-data/upload?symbol=EURUSD&timeframe=H1",
        files={"file": ("test.csv", csv_file, "text/csv")},
        auth=auth
    )
    
    assert response.status_code == 200
    result = response.json()
    assert result["symbol"] == "EURUSD"
    assert result["timeframe"] == "H1"
    assert result["rows"] == 10
    assert "timestamp" in result["columns"]
    assert "open" in result["columns"]
    assert "high" in result["columns"]
    assert "low" in result["columns"]
    assert "close" in result["columns"]
    assert "volume" in result["columns"]
    
    # Verify file was saved to disk
    assert os.path.exists(os.path.join(TEST_DATA_DIR, "EURUSD_H1.json"))


def test_get_uploaded_data(setup_test_environment):
    """Test retrieving uploaded historical data."""
    # First upload some data
    csv_file = create_test_csv("test.csv")
    client.post(
        "/api/historical-data/upload?symbol=EURUSD&timeframe=H1",
        files={"file": ("test.csv", csv_file, "text/csv")},
        auth=auth
    )
    
    # Now retrieve it
    response = client.get("/api/historical-data/EURUSD/H1", auth=auth)
    
    assert response.status_code == 200
    result = response.json()
    assert result["symbol"] == "EURUSD"
    assert result["timeframe"] == "H1"
    assert len(result["data"]["timestamp"]) == 10
    assert len(result["data"]["open"]) == 10
    assert len(result["data"]["high"]) == 10
    assert len(result["data"]["low"]) == 10
    assert len(result["data"]["close"]) == 10
    assert len(result["data"]["volume"]) == 10


def test_get_uploaded_data_with_limit(setup_test_environment):
    """Test retrieving uploaded historical data with a limit."""
    # First upload some data
    csv_file = create_test_csv("test.csv", rows=20)
    client.post(
        "/api/historical-data/upload?symbol=EURUSD&timeframe=H1",
        files={"file": ("test.csv", csv_file, "text/csv")},
        auth=auth
    )
    
    # Now retrieve it with a limit
    response = client.get("/api/historical-data/EURUSD/H1?limit=5", auth=auth)
    
    assert response.status_code == 200
    result = response.json()
    assert result["symbol"] == "EURUSD"
    assert result["timeframe"] == "H1"
    assert len(result["data"]["timestamp"]) == 5
    assert len(result["data"]["open"]) == 5
    assert len(result["data"]["high"]) == 5
    assert len(result["data"]["low"]) == 5
    assert len(result["data"]["close"]) == 5
    assert len(result["data"]["volume"]) == 5


def test_delete_historical_data(setup_test_environment):
    """Test deleting historical data."""
    # First upload some data
    csv_file = create_test_csv("test.csv")
    client.post(
        "/api/historical-data/upload?symbol=EURUSD&timeframe=H1",
        files={"file": ("test.csv", csv_file, "text/csv")},
        auth=auth
    )
    
    # Verify it exists
    assert os.path.exists(os.path.join(TEST_DATA_DIR, "EURUSD_H1.json"))
    
    # Now delete it
    response = client.delete("/api/historical-data/EURUSD/H1", auth=auth)
    
    assert response.status_code == 200
    result = response.json()
    assert "message" in result
    assert "deleted successfully" in result["message"]
    
    # Verify it was deleted from disk
    assert not os.path.exists(os.path.join(TEST_DATA_DIR, "EURUSD_H1.json"))
    
    # Verify it's no longer in the list
    response = client.get("/api/historical-data", auth=auth)
    assert "EURUSD_H1" not in response.json()


def test_upload_invalid_csv_missing_columns(setup_test_environment):
    """Test uploading a CSV file with missing required columns."""
    # Create CSV with missing columns
    data = {
        'timestamp': [datetime(2023, 1, 1, i).isoformat() for i in range(5)],
        'open': [1.1 + i*0.01 for i in range(5)],
        # Missing 'high', 'low', 'close', 'volume'
    }
    df = pd.DataFrame(data)
    csv_file = BytesIO()
    df.to_csv(csv_file, index=False)
    csv_file.seek(0)
    
    response = client.post(
        "/api/historical-data/upload?symbol=EURUSD&timeframe=H1",
        files={"file": ("test.csv", csv_file, "text/csv")},
        auth=auth
    )
    
    assert response.status_code == 400
    assert "Missing required columns" in response.json()["detail"]


def test_upload_invalid_csv_negative_prices(setup_test_environment):
    """Test uploading a CSV file with negative price values."""
    # Create CSV with negative prices
    data = {
        'timestamp': [datetime(2023, 1, 1, i).isoformat() for i in range(5)],
        'open': [1.1, 1.2, -1.3, 1.4, 1.5],  # Negative value
        'high': [1.15, 1.25, 1.35, 1.45, 1.55],
        'low': [1.05, 1.15, 1.25, 1.35, 1.45],
        'close': [1.12, 1.22, 1.32, 1.42, 1.52],
        'volume': [1000, 1100, 1200, 1300, 1400]
    }
    df = pd.DataFrame(data)
    csv_file = BytesIO()
    df.to_csv(csv_file, index=False)
    csv_file.seek(0)
    
    response = client.post(
        "/api/historical-data/upload?symbol=EURUSD&timeframe=H1",
        files={"file": ("test.csv", csv_file, "text/csv")},
        auth=auth
    )
    
    assert response.status_code == 400
    assert "Price values cannot be negative" in response.json()["detail"]


def test_upload_invalid_csv_negative_volume(setup_test_environment):
    """Test uploading a CSV file with negative volume values."""
    # Create CSV with negative volume
    data = {
        'timestamp': [datetime(2023, 1, 1, i).isoformat() for i in range(5)],
        'open': [1.1, 1.2, 1.3, 1.4, 1.5],
        'high': [1.15, 1.25, 1.35, 1.45, 1.55],
        'low': [1.05, 1.15, 1.25, 1.35, 1.45],
        'close': [1.12, 1.22, 1.32, 1.42, 1.52],
        'volume': [1000, 1100, -1200, 1300, 1400]  # Negative value
    }
    df = pd.DataFrame(data)
    csv_file = BytesIO()
    df.to_csv(csv_file, index=False)
    csv_file.seek(0)
    
    response = client.post(
        "/api/historical-data/upload?symbol=EURUSD&timeframe=H1",
        files={"file": ("test.csv", csv_file, "text/csv")},
        auth=auth
    )
    
    assert response.status_code == 400
    assert "Volume values cannot be negative" in response.json()["detail"]


def test_upload_invalid_csv_bad_timestamp(setup_test_environment):
    """Test uploading a CSV file with invalid timestamp format."""
    # Create CSV with bad timestamp format
    data = {
        'timestamp': ['2023-01-01', '2023-01-02', 'not-a-date', '2023-01-04', '2023-01-05'],
        'open': [1.1, 1.2, 1.3, 1.4, 1.5],
        'high': [1.15, 1.25, 1.35, 1.45, 1.55],
        'low': [1.05, 1.15, 1.25, 1.35, 1.45],
        'close': [1.12, 1.22, 1.32, 1.42, 1.52],
        'volume': [1000, 1100, 1200, 1300, 1400]
    }
    df = pd.DataFrame(data)
    csv_file = BytesIO()
    df.to_csv(csv_file, index=False)
    csv_file.seek(0)
    
    response = client.post(
        "/api/historical-data/upload?symbol=EURUSD&timeframe=H1",
        files={"file": ("test.csv", csv_file, "text/csv")},
        auth=auth
    )
    
    assert response.status_code == 400
    assert "Invalid timestamp format" in response.json()["detail"]


def test_upload_invalid_symbol_format(setup_test_environment):
    """Test uploading with an invalid symbol format."""
    csv_file = create_test_csv("test.csv")
    
    response = client.post(
        "/api/historical-data/upload?symbol=EUR/USD&timeframe=H1",  # Invalid symbol with slash
        files={"file": ("test.csv", csv_file, "text/csv")},
        auth=auth
    )
    
    assert response.status_code == 400
    assert "Invalid symbol format" in response.json()["detail"]


def test_upload_invalid_timeframe(setup_test_environment):
    """Test uploading with an invalid timeframe."""
    csv_file = create_test_csv("test.csv")
    
    response = client.post(
        "/api/historical-data/upload?symbol=EURUSD&timeframe=1H",  # Invalid timeframe format
        files={"file": ("test.csv", csv_file, "text/csv")},
        auth=auth
    )
    
    assert response.status_code == 400
    assert "Invalid timeframe" in response.json()["detail"]


def test_get_nonexistent_data(setup_test_environment):
    """Test retrieving historical data that doesn't exist."""
    response = client.get("/api/historical-data/NONEXISTENT/H1", auth=auth)
    
    assert response.status_code == 404
    assert "not found" in response.json()["detail"]


def test_delete_nonexistent_data(setup_test_environment):
    """Test deleting historical data that doesn't exist."""
    response = client.delete("/api/historical-data/NONEXISTENT/H1", auth=auth)
    
    assert response.status_code == 404
    assert "not found" in response.json()["detail"]


def test_authentication_required(setup_test_environment):
    """Test that authentication is required for all endpoints."""
    # List endpoint
    response = client.get("/api/historical-data")
    assert response.status_code == 401
    
    # Get endpoint
    response = client.get("/api/historical-data/EURUSD/H1")
    assert response.status_code == 401
    
    # Upload endpoint
    csv_file = create_test_csv("test.csv")
    response = client.post(
        "/api/historical-data/upload?symbol=EURUSD&timeframe=H1",
        files={"file": ("test.csv", csv_file, "text/csv")}
    )
    assert response.status_code == 401
    
    # Delete endpoint
    response = client.delete("/api/historical-data/EURUSD/H1")
    assert response.status_code == 401


def test_persistence_across_server_restarts(setup_test_environment):
    """Test that data persists across server restarts."""
    # Upload data
    csv_file = create_test_csv("test.csv")
    client.post(
        "/api/historical-data/upload?symbol=EURUSD&timeframe=H1",
        files={"file": ("test.csv", csv_file, "text/csv")},
        auth=auth
    )
    
    # Verify file exists on disk
    assert os.path.exists(os.path.join(TEST_DATA_DIR, "EURUSD_H1.json"))
    
    # Simulate server restart by clearing in-memory data
    import backend.minimal_server
    backend.minimal_server.HISTORICAL_DATA = {}
    
    # Try to retrieve the data - should load from disk
    response = client.get("/api/historical-data/EURUSD/H1", auth=auth)
    
    assert response.status_code == 200
    result = response.json()
    assert result["symbol"] == "EURUSD"
    assert result["timeframe"] == "H1"
    assert len(result["data"]["timestamp"]) == 10


def test_upload_with_different_column_format(setup_test_environment):
    """Test uploading a CSV file with different column names."""
    # Create CSV with different column names
    data = {
        'Date': ['2023-01-01T00:00:00', '2023-01-01T01:00:00', '2023-01-01T02:00:00'],
        'Open': [1.3000, 1.3025, 1.3050],
        'High': [1.3050, 1.3075, 1.3100],
        'Low': [1.2950, 1.2975, 1.3000],
        'Close': [1.3025, 1.3050, 1.3075],
        'Volume': [500, 520, 540]
    }
    df = pd.DataFrame(data)
    csv_file = BytesIO()
    df.to_csv(csv_file, index=False)
    csv_file.seek(0)
    
    # This test will fail with the current implementation
    # It's a placeholder for future enhancement to support column mapping
    response = client.post(
        "/api/historical-data/upload?symbol=USDJPY&timeframe=H1",
        files={"file": ("different_format.csv", csv_file, "text/csv")},
        auth=auth
    )
    
    # Currently, this will fail because column mapping is not implemented
    # This is a "red" test in TDD that would drive the implementation
    # Uncomment when column mapping is implemented
    # assert response.status_code == 200
    # result = response.json()
    # assert result["symbol"] == "USDJPY"
    # assert result["timeframe"] == "H1"
    # assert result["rows"] == 3
    
    # For now, we expect it to fail with a 400 error
    assert response.status_code == 400
    assert "Missing required columns" in response.json()["detail"]
    
    # Note: In a future implementation, we would add column mapping support:
    # column_mapping = {
    #     "Date": "timestamp",
    #     "Open": "open",
    #     "High": "high",
    #     "Low": "low",
    #     "Close": "close",
    #     "Volume": "volume"
    # }
    # And pass it as a parameter to the API


def test_upload_with_invalid_timestamp_format(setup_test_environment):
    """Test uploading a CSV file with an invalid timestamp format."""
    # Create CSV with invalid timestamp format
    data = {
        'timestamp': ['invalid-date', '2023-01-01T01:00:00', '2023-01-01T02:00:00'],
        'open': [1.1, 1.2, 1.3],
        'high': [1.15, 1.25, 1.35],
        'low': [1.05, 1.15, 1.25],
        'close': [1.12, 1.22, 1.32],
        'volume': [1000, 1100, 1200]
    }
    df = pd.DataFrame(data)
    csv_file = BytesIO()
    df.to_csv(csv_file, index=False)
    csv_file.seek(0)
    
    # This should fail with an invalid timestamp format error
    response = client.post(
        "/api/historical-data/upload?symbol=USDJPY&timeframe=H1",
        files={"file": ("test.csv", csv_file, "text/csv")},
        auth=auth
    )
    
    # Verify the error response
    assert response.status_code == 400
    assert "Invalid timestamp format" in response.json()["detail"]
    
    # Note: In a future implementation, we would add date format support:
    # response = client.post(
    #     "/api/historical-data/upload?symbol=USDJPY&timeframe=H1",
    #     files={"file": ("test.csv", csv_file, "text/csv")},
    #     params={"date_format": "%m/%d/%Y %H:%M"},
    #     auth=auth
    # )


def test_upload_with_multiple_files(setup_test_environment):
    """Test uploading multiple files for different symbols."""
    # Create EURUSD test data
    eurusd_data = {
        'timestamp': [datetime(2023, 1, 1, i).isoformat() for i in range(5)],
        'open': [1.1 + i*0.01 for i in range(5)],
        'high': [1.15 + i*0.01 for i in range(5)],
        'low': [1.05 + i*0.01 for i in range(5)],
        'close': [1.12 + i*0.01 for i in range(5)],
        'volume': [1000 + i*100 for i in range(5)]
    }
    eurusd_df = pd.DataFrame(eurusd_data)
    eurusd_csv = BytesIO()
    eurusd_df.to_csv(eurusd_csv, index=False)
    eurusd_csv.seek(0)
    
    # Create GBPUSD test data
    gbpusd_data = {
        'timestamp': [datetime(2023, 1, 1, i*4).isoformat() for i in range(5)],
        'open': [1.25 + i*0.01 for i in range(5)],
        'high': [1.255 + i*0.01 for i in range(5)],
        'low': [1.245 + i*0.01 for i in range(5)],
        'close': [1.252 + i*0.01 for i in range(5)],
        'volume': [800 + i*50 for i in range(5)]
    }
    gbpusd_df = pd.DataFrame(gbpusd_data)
    gbpusd_csv = BytesIO()
    gbpusd_df.to_csv(gbpusd_csv, index=False)
    gbpusd_csv.seek(0)
    
    # Upload EURUSD data
    response1 = client.post(
        "/api/historical-data/upload?symbol=EURUSD&timeframe=H1",
        files={"file": ("sample_eurusd.csv", eurusd_csv, "text/csv")},
        auth=auth
    )
    
    assert response1.status_code == 200
    
    # Upload GBPUSD data
    response2 = client.post(
        "/api/historical-data/upload?symbol=GBPUSD&timeframe=H4",
        files={"file": ("sample_gbpusd.csv", gbpusd_csv, "text/csv")},
        auth=auth
    )
    
    assert response2.status_code == 200
    
    # Check that both datasets are available
    response = client.get("/api/historical-data", auth=auth)
    assert response.status_code == 200
    data_list = response.json()
    assert "EURUSD_H1" in data_list
    assert "GBPUSD_H4" in data_list


if __name__ == "__main__":
    pytest.main(["-xvs", __file__])