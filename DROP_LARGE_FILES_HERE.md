# 📁 Drop Large TDD Files Here

This directory is set up for you to drop in larger TDD files that you want to add to the project.

## 🎯 Purpose

- **Easy file management**: Drop large files directly into the root or appropriate subdirectories
- **TDD compliance**: All files should follow the established TDD patterns
- **Schema validation**: Ensure all data structures use Zod schemas
- **Test coverage**: Include corresponding test files for any new functionality

## 📋 Guidelines for Large Files

### ✅ What to Include

1. **Service Files** (`*.service.ts`) with corresponding test files
2. **Repository Files** (`*.repository.ts`) with full test coverage
3. **Schema Files** (`*.schemas.ts`) with validation logic
4. **Controller Files** (`*.controller.ts`) with integration tests
5. **Middleware Files** (`*.middleware.ts`) with unit tests
6. **Utility Files** (`*.utils.ts`) with comprehensive testing

### 🗂️ Recommended Structure

```
your-large-file.service.ts
your-large-file.service.test.ts
your-large-file.schemas.ts
your-large-file.types.ts
```

### 🧪 TDD Requirements

- **Test First**: Each file should have corresponding test files
- **100% Coverage**: All functions must be tested
- **Schema Validation**: Use Zod schemas from `shared/schemas/`
- **Mock Dependencies**: Follow the established mocking patterns
- **Type Safety**: Use strict TypeScript throughout

## 📂 Directory Structure

Once you drop files here, they can be organized into:

```
shared/
├── schemas/           # Drop schema files here
├── types/            # Drop type definition files here
├── utils/            # Drop utility files here
├── test-factories/   # Drop test factory files here
├── test-utils/       # Drop test utility files here
└── constants/        # Drop constant files here

backend/
├── src/
│   ├── features/     # Drop feature-specific files here
│   ├── shared/       # Drop shared backend files here
│   └── test-factories/ # Drop backend test factories here

frontend/
├── src/
│   ├── components/   # Drop React component files here
│   ├── hooks/        # Drop custom hook files here
│   └── utils/        # Drop frontend utility files here
```

## 🔧 Integration Steps

1. **Drop your files** into the appropriate directories
2. **Update imports** to use the established path aliases (`@/shared`, `@/schemas`, etc.)
3. **Run tests** to ensure everything integrates properly:
   ```bash
   npm test
   npm run test:coverage
   ```
4. **Verify TypeScript** compilation:
   ```bash
   npm run type-check
   ```

## 📝 File Templates

### Service Template
```typescript
import { ServiceResponse } from '@/shared/types';
import { YourSchema } from '@/shared/schemas';

export class YourService {
  constructor(private dependencies: YourServiceDependencies) {}

  async yourMethod(): Promise<ServiceResponse<YourType>> {
    // Implementation
  }
}
```

### Test Template
```typescript
import { YourService } from './your-service';
import { getMockYourType } from '@/shared/test-factories';

describe('YourService', () => {
  let service: YourService;
  let mockDependencies: jest.Mocked<YourServiceDependencies>;

  beforeEach(() => {
    // Setup mocks
    service = new YourService(mockDependencies);
  });

  it('should do something', async () => {
    // Test implementation
  });
});
```

## 🚀 Ready to Use

The project is now set up with:
- ✅ Comprehensive shared folder structure
- ✅ TDD-compliant patterns established
- ✅ Schema-first development approach
- ✅ Test utilities and factories
- ✅ TypeScript strict mode configuration
- ✅ Jest testing framework configured

Simply drop your large files into the appropriate directories and they'll integrate seamlessly with the existing architecture!