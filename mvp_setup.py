#!/usr/bin/env python
# mvp_setup.py
"""
MVP Setup Script for AI Enhanced Trading Platform

This script provides a simplified setup process for the MVP version of the trading platform.
It handles:
1. Dependency installation
2. Environment setup
3. Basic configuration

Usage:
    python mvp_setup.py [--install-deps] [--setup-env] [--run-tests]
"""

import argparse
import os
import sys
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("mvp_setup")

# Define required packages for MVP
MVP_DEPENDENCIES = [
    "pytest",
    "hypothesis",
    "fastapi",
    "uvicorn",
    "sqlalchemy",
    "pydantic",
]

def install_dependencies():
    """Install required dependencies for MVP"""
    logger.info("Installing MVP dependencies...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        subprocess.check_call([sys.executable, "-m", "pip", "install"] + MVP_DEPENDENCIES)
        logger.info("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Failed to install dependencies: {str(e)}")
        return False

def setup_environment():
    """Set up the environment for MVP"""
    logger.info("Setting up MVP environment...")
    
    # Create necessary directories if they don't exist
    dirs_to_create = [
        "logs",
        "data",
        "config",
    ]
    
    for dir_name in dirs_to_create:
        dir_path = Path(dir_name)
        if not dir_path.exists():
            dir_path.mkdir(parents=True)
            logger.info(f"Created directory: {dir_path}")
    
    # Create basic configuration file if it doesn't exist
    config_path = Path("config/mvp_config.py")
    if not config_path.exists():
        config_content = """# MVP Configuration
# This file contains the configuration for the MVP version of the trading platform

# MT5 Configuration
MT5_CONFIG = {
    "offline_mode": True,  # Set to False for live trading
    "terminal_path": "",   # Path to MT5 terminal (leave empty for auto-detection)
    "login": "",           # MT5 account login (not needed in offline mode)
    "password": "",        # MT5 account password (not needed in offline mode)
    "server": "",          # MT5 server (not needed in offline mode)
}

# Trading Configuration
TRADING_CONFIG = {
    "default_lot_size": 0.01,
    "max_lot_size": 1.0,
    "max_positions": 5,
    "default_symbols": ["EURUSD", "GBPUSD", "USDJPY"],
}

# Risk Management Configuration
RISK_CONFIG = {
    "max_drawdown_percent": 5.0,
    "max_risk_per_trade_percent": 2.0,
    "max_daily_loss_percent": 3.0,
}

# Logging Configuration
LOGGING_CONFIG = {
    "log_level": "INFO",
    "log_file": "logs/mvp_trading.log",
    "console_logging": True,
}
"""
        with open(config_path, "w") as f:
            f.write(config_content)
        logger.info(f"Created configuration file: {config_path}")
    
    logger.info("✅ Environment setup completed")
    return True

def run_tests():
    """Run the MVP tests"""
    logger.info("Running MVP tests...")
    
    try:
        # Run the MVP test suite
        result = subprocess.run(
            [sys.executable, "-m", "tests.mvp_test_suite", "--create-tests", "--offline", "--verbose"],
            check=True
        )
        logger.info("✅ Tests completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Tests failed: {str(e)}")
        return False

def main():
    """Main entry point for the MVP setup script"""
    parser = argparse.ArgumentParser(description="MVP Setup Script for AI Enhanced Trading Platform")
    parser.add_argument("--install-deps", action="store_true", help="Install dependencies")
    parser.add_argument("--setup-env", action="store_true", help="Set up environment")
    parser.add_argument("--run-tests", action="store_true", help="Run MVP tests")
    parser.add_argument("--all", action="store_true", help="Perform all setup steps")
    
    args = parser.parse_args()
    
    # If no arguments provided or --all specified, perform all steps
    if len(sys.argv) == 1 or args.all:
        install_dependencies()
        setup_environment()
        run_tests()
        return 0
    
    # Otherwise, perform only the requested steps
    success = True
    
    if args.install_deps:
        success = install_dependencies() and success
    
    if args.setup_env:
        success = setup_environment() and success
    
    if args.run_tests:
        success = run_tests() and success
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())