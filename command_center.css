/* General Styles */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', Arial, sans-serif;
    background-color: #121212;
    color: #e0e0e0;
    margin: 0;
    line-height: 1.6;
}

.container {
    display: flex;
    padding: 20px;
    gap: 20px;
}

.main-content {
    flex: 3;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.sidebar {
    flex: 2;
}

.card {
    background-color: #1e1e1e;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #2d2d2d;
}

/* Header */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #1e1e1e;
    border-bottom: 1px solid #2d2d2d;
}

header h1 {
    margin: 0;
    font-size: 1.5em;
}

#platform-status span {
    margin-left: 20px;
    font-size: 0.9em;
}

.status-ok {
    color: #4caf50;
}

/* Strategy Hub */
#strategy-hub h2 {
    margin-top: 0;
}

#chatbot-interface {
    display: flex;
    flex-direction: column;
}

#chat-input {
    width: 100%;
    height: 100px;
    background-color: #252526;
    border: 1px solid #333;
    border-radius: 4px;
    color: #e0e0e0;
    padding: 10px;
    resize: vertical;
    margin-bottom: 10px;
}

#send-chat-btn {
    align-self: flex-end;
    padding: 10px 20px;
    background-color: #007acc;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

/* Prompt Library */
.prompt-scroller {
    display: flex;
    overflow-x: auto;
    gap: 15px;
    padding-bottom: 15px; /* For scrollbar */
}

.prompt-card {
    flex: 0 0 250px;
    background-color: #2a2d2e;
    padding: 15px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.prompt-card:hover {
    background-color: #3c4043;
}

.prompt-card h4 {
    margin: 0 0 10px 0;
}

.prompt-card p {
    font-size: 0.9em;
    margin: 0;
}

/* MQL5 Studio */
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.pro-badge {
    background-color: #8e44ad;
    color: white;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
}

#mql5-cta-btn {
    margin-top: 15px;
    padding: 10px 20px;
    background-color: #555;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

/* Asset Dashboard */
#asset-dashboard h2 {
    margin-top: 0;
}

#asset-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.asset-item {
    background-color: #2a2d2e;
    padding: 15px;
    border-radius: 6px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.asset-item > div {
    display: flex;
    flex-direction: column;
}

.status-live {
    color: #4caf50;
    font-weight: bold;
}

.asset-actions button {
    background: none;
    border: 1px solid #555;
    color: #e0e0e0;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    margin-left: 5px;
}

/* Modal */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: #1e1e1e;
    padding: 30px;
    border-radius: 8px;
    width: 400px;
    text-align: center;
    position: relative;
}

.close-button {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 1.5em;
    cursor: pointer;
}

#upgrade-btn {
    padding: 10px 20px;
    background-color: #007acc;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 15px;
}

.hidden {
    display: none;
}
