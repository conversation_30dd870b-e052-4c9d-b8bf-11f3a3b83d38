import { z } from 'zod';
export declare const MessageRoleSchema: z.<PERSON><["user", "assistant", "system"]>;
export type MessageRole = z.infer<typeof MessageRoleSchema>;
export declare const MessageTypeSchema: z.<PERSON>od<PERSON><["text", "analysis", "prediction", "strategy", "market_data", "error"]>;
export type MessageType = z.infer<typeof MessageTypeSchema>;
export declare const ChatMessageSchema: z.ZodObject<{
    id: z.ZodString;
    role: z.<PERSON><PERSON><["user", "assistant", "system"]>;
    content: z.ZodString;
    type: z.ZodDefault<z.ZodEnum<["text", "analysis", "prediction", "strategy", "market_data", "error"]>>;
    metadata: z.ZodObject<{
        timestamp: z.ZodDate;
        confidence: z.ZodOptional<z.ZodNumber>;
        sources: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        attachments: z.ZodOptional<z.Zod<PERSON>rray<z.ZodObject<{
            type: z.Z<PERSON><["chart", "table", "document"]>;
            url: z.ZodOptional<z.ZodString>;
            data: z.ZodOptional<z.ZodAny>;
        }, "strip", z.ZodTypeAny, {
            type: "chart" | "table" | "document";
            data?: any;
            url?: string | undefined;
        }, {
            type: "chart" | "table" | "document";
            data?: any;
            url?: string | undefined;
        }>, "many">>;
    }, "strip", z.ZodTypeAny, {
        timestamp: Date;
        confidence?: number | undefined;
        sources?: string[] | undefined;
        attachments?: {
            type: "chart" | "table" | "document";
            data?: any;
            url?: string | undefined;
        }[] | undefined;
    }, {
        timestamp: Date;
        confidence?: number | undefined;
        sources?: string[] | undefined;
        attachments?: {
            type: "chart" | "table" | "document";
            data?: any;
            url?: string | undefined;
        }[] | undefined;
    }>;
    created_at: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    type: "analysis" | "strategy" | "error" | "market_data" | "text" | "prediction";
    id: string;
    metadata: {
        timestamp: Date;
        confidence?: number | undefined;
        sources?: string[] | undefined;
        attachments?: {
            type: "chart" | "table" | "document";
            data?: any;
            url?: string | undefined;
        }[] | undefined;
    };
    created_at: Date;
    role: "system" | "user" | "assistant";
    content: string;
}, {
    id: string;
    metadata: {
        timestamp: Date;
        confidence?: number | undefined;
        sources?: string[] | undefined;
        attachments?: {
            type: "chart" | "table" | "document";
            data?: any;
            url?: string | undefined;
        }[] | undefined;
    };
    created_at: Date;
    role: "system" | "user" | "assistant";
    content: string;
    type?: "analysis" | "strategy" | "error" | "market_data" | "text" | "prediction" | undefined;
}>;
export type ChatMessage = z.infer<typeof ChatMessageSchema>;
export declare const ChatSessionSchema: z.ZodObject<{
    id: z.ZodString;
    user_id: z.ZodString;
    title: z.ZodString;
    context: z.ZodObject<{
        trading_symbols: z.ZodOptional<z.ZodArray<z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>, "many">>;
        timeframe: z.ZodOptional<z.ZodString>;
        strategy_focus: z.ZodOptional<z.ZodString>;
        risk_tolerance: z.ZodOptional<z.ZodEnum<["low", "medium", "high"]>>;
    }, "strip", z.ZodTypeAny, {
        timeframe?: string | undefined;
        trading_symbols?: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[] | undefined;
        strategy_focus?: string | undefined;
        risk_tolerance?: "low" | "medium" | "high" | undefined;
    }, {
        timeframe?: string | undefined;
        trading_symbols?: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[] | undefined;
        strategy_focus?: string | undefined;
        risk_tolerance?: "low" | "medium" | "high" | undefined;
    }>;
    created_at: z.ZodDate;
    updated_at: z.ZodDate;
    last_activity: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    context: {
        timeframe?: string | undefined;
        trading_symbols?: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[] | undefined;
        strategy_focus?: string | undefined;
        risk_tolerance?: "low" | "medium" | "high" | undefined;
    };
    user_id: string;
    created_at: Date;
    updated_at: Date;
    title: string;
    last_activity: Date;
}, {
    id: string;
    context: {
        timeframe?: string | undefined;
        trading_symbols?: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[] | undefined;
        strategy_focus?: string | undefined;
        risk_tolerance?: "low" | "medium" | "high" | undefined;
    };
    user_id: string;
    created_at: Date;
    updated_at: Date;
    title: string;
    last_activity: Date;
}>;
export type ChatSession = z.infer<typeof ChatSessionSchema>;
export declare const ChatRequestSchema: z.ZodObject<{
    session_id: z.ZodString;
    message: z.ZodString;
    user_id: z.ZodString;
    context: z.ZodObject<{
        current_positions: z.ZodOptional<z.ZodArray<z.ZodObject<{
            symbol: z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>;
            volume: z.ZodNumber;
            pnl: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            volume: number;
            pnl: number;
        }, {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            volume: number;
            pnl: number;
        }>, "many">>;
        market_data: z.ZodOptional<z.ZodObject<{
            symbols: z.ZodOptional<z.ZodArray<z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>, "many">>;
            timeframe: z.ZodOptional<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            timeframe?: string | undefined;
            symbols?: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[] | undefined;
        }, {
            timeframe?: string | undefined;
            symbols?: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[] | undefined;
        }>>;
        preferences: z.ZodOptional<z.ZodObject<{
            risk_tolerance: z.ZodOptional<z.ZodEnum<["low", "medium", "high"]>>;
            preferred_symbols: z.ZodOptional<z.ZodArray<z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>, "many">>;
            notification_settings: z.ZodOptional<z.ZodAny>;
        }, "strip", z.ZodTypeAny, {
            risk_tolerance?: "low" | "medium" | "high" | undefined;
            preferred_symbols?: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[] | undefined;
            notification_settings?: any;
        }, {
            risk_tolerance?: "low" | "medium" | "high" | undefined;
            preferred_symbols?: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[] | undefined;
            notification_settings?: any;
        }>>;
        conversation_history: z.ZodOptional<z.ZodArray<z.ZodObject<{
            id: z.ZodString;
            role: z.ZodEnum<["user", "assistant", "system"]>;
            content: z.ZodString;
            type: z.ZodDefault<z.ZodEnum<["text", "analysis", "prediction", "strategy", "market_data", "error"]>>;
            metadata: z.ZodObject<{
                timestamp: z.ZodDate;
                confidence: z.ZodOptional<z.ZodNumber>;
                sources: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
                attachments: z.ZodOptional<z.ZodArray<z.ZodObject<{
                    type: z.ZodEnum<["chart", "table", "document"]>;
                    url: z.ZodOptional<z.ZodString>;
                    data: z.ZodOptional<z.ZodAny>;
                }, "strip", z.ZodTypeAny, {
                    type: "chart" | "table" | "document";
                    data?: any;
                    url?: string | undefined;
                }, {
                    type: "chart" | "table" | "document";
                    data?: any;
                    url?: string | undefined;
                }>, "many">>;
            }, "strip", z.ZodTypeAny, {
                timestamp: Date;
                confidence?: number | undefined;
                sources?: string[] | undefined;
                attachments?: {
                    type: "chart" | "table" | "document";
                    data?: any;
                    url?: string | undefined;
                }[] | undefined;
            }, {
                timestamp: Date;
                confidence?: number | undefined;
                sources?: string[] | undefined;
                attachments?: {
                    type: "chart" | "table" | "document";
                    data?: any;
                    url?: string | undefined;
                }[] | undefined;
            }>;
            created_at: z.ZodDate;
        }, "strip", z.ZodTypeAny, {
            type: "analysis" | "strategy" | "error" | "market_data" | "text" | "prediction";
            id: string;
            metadata: {
                timestamp: Date;
                confidence?: number | undefined;
                sources?: string[] | undefined;
                attachments?: {
                    type: "chart" | "table" | "document";
                    data?: any;
                    url?: string | undefined;
                }[] | undefined;
            };
            created_at: Date;
            role: "system" | "user" | "assistant";
            content: string;
        }, {
            id: string;
            metadata: {
                timestamp: Date;
                confidence?: number | undefined;
                sources?: string[] | undefined;
                attachments?: {
                    type: "chart" | "table" | "document";
                    data?: any;
                    url?: string | undefined;
                }[] | undefined;
            };
            created_at: Date;
            role: "system" | "user" | "assistant";
            content: string;
            type?: "analysis" | "strategy" | "error" | "market_data" | "text" | "prediction" | undefined;
        }>, "many">>;
    }, "strip", z.ZodTypeAny, {
        preferences?: {
            risk_tolerance?: "low" | "medium" | "high" | undefined;
            preferred_symbols?: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[] | undefined;
            notification_settings?: any;
        } | undefined;
        market_data?: {
            timeframe?: string | undefined;
            symbols?: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[] | undefined;
        } | undefined;
        current_positions?: {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            volume: number;
            pnl: number;
        }[] | undefined;
        conversation_history?: {
            type: "analysis" | "strategy" | "error" | "market_data" | "text" | "prediction";
            id: string;
            metadata: {
                timestamp: Date;
                confidence?: number | undefined;
                sources?: string[] | undefined;
                attachments?: {
                    type: "chart" | "table" | "document";
                    data?: any;
                    url?: string | undefined;
                }[] | undefined;
            };
            created_at: Date;
            role: "system" | "user" | "assistant";
            content: string;
        }[] | undefined;
    }, {
        preferences?: {
            risk_tolerance?: "low" | "medium" | "high" | undefined;
            preferred_symbols?: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[] | undefined;
            notification_settings?: any;
        } | undefined;
        market_data?: {
            timeframe?: string | undefined;
            symbols?: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[] | undefined;
        } | undefined;
        current_positions?: {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            volume: number;
            pnl: number;
        }[] | undefined;
        conversation_history?: {
            id: string;
            metadata: {
                timestamp: Date;
                confidence?: number | undefined;
                sources?: string[] | undefined;
                attachments?: {
                    type: "chart" | "table" | "document";
                    data?: any;
                    url?: string | undefined;
                }[] | undefined;
            };
            created_at: Date;
            role: "system" | "user" | "assistant";
            content: string;
            type?: "analysis" | "strategy" | "error" | "market_data" | "text" | "prediction" | undefined;
        }[] | undefined;
    }>;
    timestamp: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    message: string;
    context: {
        preferences?: {
            risk_tolerance?: "low" | "medium" | "high" | undefined;
            preferred_symbols?: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[] | undefined;
            notification_settings?: any;
        } | undefined;
        market_data?: {
            timeframe?: string | undefined;
            symbols?: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[] | undefined;
        } | undefined;
        current_positions?: {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            volume: number;
            pnl: number;
        }[] | undefined;
        conversation_history?: {
            type: "analysis" | "strategy" | "error" | "market_data" | "text" | "prediction";
            id: string;
            metadata: {
                timestamp: Date;
                confidence?: number | undefined;
                sources?: string[] | undefined;
                attachments?: {
                    type: "chart" | "table" | "document";
                    data?: any;
                    url?: string | undefined;
                }[] | undefined;
            };
            created_at: Date;
            role: "system" | "user" | "assistant";
            content: string;
        }[] | undefined;
    };
    timestamp: Date;
    user_id: string;
    session_id: string;
}, {
    message: string;
    context: {
        preferences?: {
            risk_tolerance?: "low" | "medium" | "high" | undefined;
            preferred_symbols?: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[] | undefined;
            notification_settings?: any;
        } | undefined;
        market_data?: {
            timeframe?: string | undefined;
            symbols?: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[] | undefined;
        } | undefined;
        current_positions?: {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            volume: number;
            pnl: number;
        }[] | undefined;
        conversation_history?: {
            id: string;
            metadata: {
                timestamp: Date;
                confidence?: number | undefined;
                sources?: string[] | undefined;
                attachments?: {
                    type: "chart" | "table" | "document";
                    data?: any;
                    url?: string | undefined;
                }[] | undefined;
            };
            created_at: Date;
            role: "system" | "user" | "assistant";
            content: string;
            type?: "analysis" | "strategy" | "error" | "market_data" | "text" | "prediction" | undefined;
        }[] | undefined;
    };
    user_id: string;
    session_id: string;
    timestamp?: Date | undefined;
}>;
export type ChatRequest = z.infer<typeof ChatRequestSchema>;
export declare const ChatResponseSchema: z.ZodObject<{
    message: z.ZodString;
    type: z.ZodEnum<["text", "analysis", "prediction", "strategy", "market_data", "error"]>;
    confidence: z.ZodNumber;
    analysis: z.ZodOptional<z.ZodObject<{
        market_sentiment: z.ZodOptional<z.ZodEnum<["bullish", "bearish", "neutral"]>>;
        key_levels: z.ZodOptional<z.ZodArray<z.ZodObject<{
            symbol: z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>;
            level: z.ZodNumber;
            type: z.ZodEnum<["support", "resistance"]>;
        }, "strip", z.ZodTypeAny, {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            type: "support" | "resistance";
            level: number;
        }, {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            type: "support" | "resistance";
            level: number;
        }>, "many">>;
        risk_assessment: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        key_levels?: {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            type: "support" | "resistance";
            level: number;
        }[] | undefined;
        market_sentiment?: "bullish" | "bearish" | "neutral" | undefined;
        risk_assessment?: string | undefined;
    }, {
        key_levels?: {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            type: "support" | "resistance";
            level: number;
        }[] | undefined;
        market_sentiment?: "bullish" | "bearish" | "neutral" | undefined;
        risk_assessment?: string | undefined;
    }>>;
    suggested_actions: z.ZodOptional<z.ZodArray<z.ZodObject<{
        type: z.ZodEnum<["buy", "sell", "hold", "analyze", "backtest"]>;
        symbol: z.ZodOptional<z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>>;
        reasoning: z.ZodString;
        confidence: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        type: "buy" | "sell" | "hold" | "analyze" | "backtest";
        confidence: number;
        reasoning: string;
        symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
    }, {
        type: "buy" | "sell" | "hold" | "analyze" | "backtest";
        confidence: number;
        reasoning: string;
        symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
    }>, "many">>;
    sources: z.ZodOptional<z.ZodArray<z.ZodObject<{
        type: z.ZodEnum<["knowledge_graph", "market_data", "historical_analysis"]>;
        content: z.ZodString;
        relevance: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        type: "market_data" | "knowledge_graph" | "historical_analysis";
        content: string;
        relevance: number;
    }, {
        type: "market_data" | "knowledge_graph" | "historical_analysis";
        content: string;
        relevance: number;
    }>, "many">>;
    attachments: z.ZodOptional<z.ZodArray<z.ZodObject<{
        type: z.ZodEnum<["chart", "table", "document", "analysis"]>;
        title: z.ZodString;
        data: z.ZodAny;
    }, "strip", z.ZodTypeAny, {
        type: "analysis" | "chart" | "table" | "document";
        title: string;
        data?: any;
    }, {
        type: "analysis" | "chart" | "table" | "document";
        title: string;
        data?: any;
    }>, "many">>;
    timestamp: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    type: "analysis" | "strategy" | "error" | "market_data" | "text" | "prediction";
    message: string;
    confidence: number;
    timestamp: Date;
    analysis?: {
        key_levels?: {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            type: "support" | "resistance";
            level: number;
        }[] | undefined;
        market_sentiment?: "bullish" | "bearish" | "neutral" | undefined;
        risk_assessment?: string | undefined;
    } | undefined;
    sources?: {
        type: "market_data" | "knowledge_graph" | "historical_analysis";
        content: string;
        relevance: number;
    }[] | undefined;
    suggested_actions?: {
        type: "buy" | "sell" | "hold" | "analyze" | "backtest";
        confidence: number;
        reasoning: string;
        symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
    }[] | undefined;
    attachments?: {
        type: "analysis" | "chart" | "table" | "document";
        title: string;
        data?: any;
    }[] | undefined;
}, {
    type: "analysis" | "strategy" | "error" | "market_data" | "text" | "prediction";
    message: string;
    confidence: number;
    analysis?: {
        key_levels?: {
            symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
            type: "support" | "resistance";
            level: number;
        }[] | undefined;
        market_sentiment?: "bullish" | "bearish" | "neutral" | undefined;
        risk_assessment?: string | undefined;
    } | undefined;
    timestamp?: Date | undefined;
    sources?: {
        type: "market_data" | "knowledge_graph" | "historical_analysis";
        content: string;
        relevance: number;
    }[] | undefined;
    suggested_actions?: {
        type: "buy" | "sell" | "hold" | "analyze" | "backtest";
        confidence: number;
        reasoning: string;
        symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
    }[] | undefined;
    attachments?: {
        type: "analysis" | "chart" | "table" | "document";
        title: string;
        data?: any;
    }[] | undefined;
}>;
export type ChatResponse = z.infer<typeof ChatResponseSchema>;
export declare const PythonChatRequestSchema: z.ZodObject<{
    request_id: z.ZodString;
    query: z.ZodString;
    session_id: z.ZodString;
    user_context: z.ZodObject<{
        user_id: z.ZodString;
        trading_data: z.ZodOptional<z.ZodAny>;
        preferences: z.ZodOptional<z.ZodAny>;
    }, "strip", z.ZodTypeAny, {
        user_id: string;
        preferences?: any;
        trading_data?: any;
    }, {
        user_id: string;
        preferences?: any;
        trading_data?: any;
    }>;
    conversation_history: z.ZodOptional<z.ZodArray<z.ZodObject<{
        role: z.ZodEnum<["user", "assistant", "system"]>;
        content: z.ZodString;
        timestamp: z.ZodDate;
    }, "strip", z.ZodTypeAny, {
        timestamp: Date;
        role: "system" | "user" | "assistant";
        content: string;
    }, {
        timestamp: Date;
        role: "system" | "user" | "assistant";
        content: string;
    }>, "many">>;
    rag_config: z.ZodObject<{
        use_knowledge_graph: z.ZodDefault<z.ZodBoolean>;
        use_market_data: z.ZodDefault<z.ZodBoolean>;
        max_context_length: z.ZodDefault<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        use_knowledge_graph: boolean;
        use_market_data: boolean;
        max_context_length: number;
    }, {
        use_knowledge_graph?: boolean | undefined;
        use_market_data?: boolean | undefined;
        max_context_length?: number | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    query: string;
    request_id: string;
    session_id: string;
    user_context: {
        user_id: string;
        preferences?: any;
        trading_data?: any;
    };
    rag_config: {
        use_knowledge_graph: boolean;
        use_market_data: boolean;
        max_context_length: number;
    };
    conversation_history?: {
        timestamp: Date;
        role: "system" | "user" | "assistant";
        content: string;
    }[] | undefined;
}, {
    query: string;
    request_id: string;
    session_id: string;
    user_context: {
        user_id: string;
        preferences?: any;
        trading_data?: any;
    };
    rag_config: {
        use_knowledge_graph?: boolean | undefined;
        use_market_data?: boolean | undefined;
        max_context_length?: number | undefined;
    };
    conversation_history?: {
        timestamp: Date;
        role: "system" | "user" | "assistant";
        content: string;
    }[] | undefined;
}>;
export type PythonChatRequest = z.infer<typeof PythonChatRequestSchema>;
export declare const PythonChatResponseSchema: z.ZodObject<{
    request_id: z.ZodString;
    success: z.ZodBoolean;
    response: z.ZodOptional<z.ZodObject<{
        message: z.ZodString;
        type: z.ZodEnum<["text", "analysis", "prediction", "strategy", "market_data", "error"]>;
        confidence: z.ZodNumber;
        analysis: z.ZodOptional<z.ZodObject<{
            market_sentiment: z.ZodOptional<z.ZodEnum<["bullish", "bearish", "neutral"]>>;
            key_levels: z.ZodOptional<z.ZodArray<z.ZodObject<{
                symbol: z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>;
                level: z.ZodNumber;
                type: z.ZodEnum<["support", "resistance"]>;
            }, "strip", z.ZodTypeAny, {
                symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
                type: "support" | "resistance";
                level: number;
            }, {
                symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
                type: "support" | "resistance";
                level: number;
            }>, "many">>;
            risk_assessment: z.ZodOptional<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            key_levels?: {
                symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
                type: "support" | "resistance";
                level: number;
            }[] | undefined;
            market_sentiment?: "bullish" | "bearish" | "neutral" | undefined;
            risk_assessment?: string | undefined;
        }, {
            key_levels?: {
                symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
                type: "support" | "resistance";
                level: number;
            }[] | undefined;
            market_sentiment?: "bullish" | "bearish" | "neutral" | undefined;
            risk_assessment?: string | undefined;
        }>>;
        suggested_actions: z.ZodOptional<z.ZodArray<z.ZodObject<{
            type: z.ZodEnum<["buy", "sell", "hold", "analyze", "backtest"]>;
            symbol: z.ZodOptional<z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>>;
            reasoning: z.ZodString;
            confidence: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            type: "buy" | "sell" | "hold" | "analyze" | "backtest";
            confidence: number;
            reasoning: string;
            symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        }, {
            type: "buy" | "sell" | "hold" | "analyze" | "backtest";
            confidence: number;
            reasoning: string;
            symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        }>, "many">>;
        sources: z.ZodOptional<z.ZodArray<z.ZodObject<{
            type: z.ZodEnum<["knowledge_graph", "market_data", "historical_analysis"]>;
            content: z.ZodString;
            relevance: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            type: "market_data" | "knowledge_graph" | "historical_analysis";
            content: string;
            relevance: number;
        }, {
            type: "market_data" | "knowledge_graph" | "historical_analysis";
            content: string;
            relevance: number;
        }>, "many">>;
        attachments: z.ZodOptional<z.ZodArray<z.ZodObject<{
            type: z.ZodEnum<["chart", "table", "document", "analysis"]>;
            title: z.ZodString;
            data: z.ZodAny;
        }, "strip", z.ZodTypeAny, {
            type: "analysis" | "chart" | "table" | "document";
            title: string;
            data?: any;
        }, {
            type: "analysis" | "chart" | "table" | "document";
            title: string;
            data?: any;
        }>, "many">>;
        timestamp: z.ZodDefault<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        type: "analysis" | "strategy" | "error" | "market_data" | "text" | "prediction";
        message: string;
        confidence: number;
        timestamp: Date;
        analysis?: {
            key_levels?: {
                symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
                type: "support" | "resistance";
                level: number;
            }[] | undefined;
            market_sentiment?: "bullish" | "bearish" | "neutral" | undefined;
            risk_assessment?: string | undefined;
        } | undefined;
        sources?: {
            type: "market_data" | "knowledge_graph" | "historical_analysis";
            content: string;
            relevance: number;
        }[] | undefined;
        suggested_actions?: {
            type: "buy" | "sell" | "hold" | "analyze" | "backtest";
            confidence: number;
            reasoning: string;
            symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        }[] | undefined;
        attachments?: {
            type: "analysis" | "chart" | "table" | "document";
            title: string;
            data?: any;
        }[] | undefined;
    }, {
        type: "analysis" | "strategy" | "error" | "market_data" | "text" | "prediction";
        message: string;
        confidence: number;
        analysis?: {
            key_levels?: {
                symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
                type: "support" | "resistance";
                level: number;
            }[] | undefined;
            market_sentiment?: "bullish" | "bearish" | "neutral" | undefined;
            risk_assessment?: string | undefined;
        } | undefined;
        timestamp?: Date | undefined;
        sources?: {
            type: "market_data" | "knowledge_graph" | "historical_analysis";
            content: string;
            relevance: number;
        }[] | undefined;
        suggested_actions?: {
            type: "buy" | "sell" | "hold" | "analyze" | "backtest";
            confidence: number;
            reasoning: string;
            symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        }[] | undefined;
        attachments?: {
            type: "analysis" | "chart" | "table" | "document";
            title: string;
            data?: any;
        }[] | undefined;
    }>>;
    error: z.ZodOptional<z.ZodString>;
    processing_time_ms: z.ZodNumber;
    tokens_used: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    request_id: string;
    processing_time_ms: number;
    error?: string | undefined;
    response?: {
        type: "analysis" | "strategy" | "error" | "market_data" | "text" | "prediction";
        message: string;
        confidence: number;
        timestamp: Date;
        analysis?: {
            key_levels?: {
                symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
                type: "support" | "resistance";
                level: number;
            }[] | undefined;
            market_sentiment?: "bullish" | "bearish" | "neutral" | undefined;
            risk_assessment?: string | undefined;
        } | undefined;
        sources?: {
            type: "market_data" | "knowledge_graph" | "historical_analysis";
            content: string;
            relevance: number;
        }[] | undefined;
        suggested_actions?: {
            type: "buy" | "sell" | "hold" | "analyze" | "backtest";
            confidence: number;
            reasoning: string;
            symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        }[] | undefined;
        attachments?: {
            type: "analysis" | "chart" | "table" | "document";
            title: string;
            data?: any;
        }[] | undefined;
    } | undefined;
    tokens_used?: number | undefined;
}, {
    success: boolean;
    request_id: string;
    processing_time_ms: number;
    error?: string | undefined;
    response?: {
        type: "analysis" | "strategy" | "error" | "market_data" | "text" | "prediction";
        message: string;
        confidence: number;
        analysis?: {
            key_levels?: {
                symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
                type: "support" | "resistance";
                level: number;
            }[] | undefined;
            market_sentiment?: "bullish" | "bearish" | "neutral" | undefined;
            risk_assessment?: string | undefined;
        } | undefined;
        timestamp?: Date | undefined;
        sources?: {
            type: "market_data" | "knowledge_graph" | "historical_analysis";
            content: string;
            relevance: number;
        }[] | undefined;
        suggested_actions?: {
            type: "buy" | "sell" | "hold" | "analyze" | "backtest";
            confidence: number;
            reasoning: string;
            symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        }[] | undefined;
        attachments?: {
            type: "analysis" | "chart" | "table" | "document";
            title: string;
            data?: any;
        }[] | undefined;
    } | undefined;
    tokens_used?: number | undefined;
}>;
export type PythonChatResponse = z.infer<typeof PythonChatResponseSchema>;
export declare const KnowledgeNodeSchema: z.ZodObject<{
    id: z.ZodString;
    type: z.ZodEnum<["concept", "strategy", "indicator", "market_event"]>;
    label: z.ZodString;
    properties: z.ZodRecord<z.ZodString, z.ZodAny>;
    confidence: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    type: "strategy" | "indicator" | "concept" | "market_event";
    confidence: number;
    id: string;
    label: string;
    properties: Record<string, any>;
}, {
    type: "strategy" | "indicator" | "concept" | "market_event";
    confidence: number;
    id: string;
    label: string;
    properties: Record<string, any>;
}>;
export type KnowledgeNode = z.infer<typeof KnowledgeNodeSchema>;
export declare const KnowledgeRelationshipSchema: z.ZodObject<{
    source_id: z.ZodString;
    target_id: z.ZodString;
    type: z.ZodString;
    weight: z.ZodNumber;
    properties: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
}, "strip", z.ZodTypeAny, {
    type: string;
    source_id: string;
    target_id: string;
    weight: number;
    properties?: Record<string, any> | undefined;
}, {
    type: string;
    source_id: string;
    target_id: string;
    weight: number;
    properties?: Record<string, any> | undefined;
}>;
export type KnowledgeRelationship = z.infer<typeof KnowledgeRelationshipSchema>;
export declare const KnowledgeQuerySchema: z.ZodObject<{
    query: z.ZodString;
    context: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    max_results: z.ZodDefault<z.ZodNumber>;
    min_confidence: z.ZodDefault<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    query: string;
    max_results: number;
    min_confidence: number;
    context?: string[] | undefined;
}, {
    query: string;
    context?: string[] | undefined;
    max_results?: number | undefined;
    min_confidence?: number | undefined;
}>;
export type KnowledgeQuery = z.infer<typeof KnowledgeQuerySchema>;
export declare const KnowledgeResultSchema: z.ZodObject<{
    nodes: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        type: z.ZodEnum<["concept", "strategy", "indicator", "market_event"]>;
        label: z.ZodString;
        properties: z.ZodRecord<z.ZodString, z.ZodAny>;
        confidence: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        type: "strategy" | "indicator" | "concept" | "market_event";
        confidence: number;
        id: string;
        label: string;
        properties: Record<string, any>;
    }, {
        type: "strategy" | "indicator" | "concept" | "market_event";
        confidence: number;
        id: string;
        label: string;
        properties: Record<string, any>;
    }>, "many">;
    relationships: z.ZodArray<z.ZodObject<{
        source_id: z.ZodString;
        target_id: z.ZodString;
        type: z.ZodString;
        weight: z.ZodNumber;
        properties: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
    }, "strip", z.ZodTypeAny, {
        type: string;
        source_id: string;
        target_id: string;
        weight: number;
        properties?: Record<string, any> | undefined;
    }, {
        type: string;
        source_id: string;
        target_id: string;
        weight: number;
        properties?: Record<string, any> | undefined;
    }>, "many">;
    confidence: z.ZodNumber;
    explanation: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    confidence: number;
    nodes: {
        type: "strategy" | "indicator" | "concept" | "market_event";
        confidence: number;
        id: string;
        label: string;
        properties: Record<string, any>;
    }[];
    relationships: {
        type: string;
        source_id: string;
        target_id: string;
        weight: number;
        properties?: Record<string, any> | undefined;
    }[];
    explanation?: string | undefined;
}, {
    confidence: number;
    nodes: {
        type: "strategy" | "indicator" | "concept" | "market_event";
        confidence: number;
        id: string;
        label: string;
        properties: Record<string, any>;
    }[];
    relationships: {
        type: string;
        source_id: string;
        target_id: string;
        weight: number;
        properties?: Record<string, any> | undefined;
    }[];
    explanation?: string | undefined;
}>;
export type KnowledgeResult = z.infer<typeof KnowledgeResultSchema>;
export declare const MLPredictionRequestSchema: z.ZodObject<{
    symbol: z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>;
    prediction_type: z.ZodEnum<["price", "direction", "volatility"]>;
    timeframe: z.ZodString;
    features: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    confidence_threshold: z.ZodDefault<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
    timeframe: string;
    prediction_type: "price" | "direction" | "volatility";
    confidence_threshold: number;
    features?: string[] | undefined;
}, {
    symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
    timeframe: string;
    prediction_type: "price" | "direction" | "volatility";
    features?: string[] | undefined;
    confidence_threshold?: number | undefined;
}>;
export type MLPredictionRequest = z.infer<typeof MLPredictionRequestSchema>;
export declare const MLPredictionResponseSchema: z.ZodObject<{
    symbol: z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>;
    prediction: z.ZodNumber;
    confidence: z.ZodNumber;
    features_used: z.ZodArray<z.ZodString, "many">;
    model_info: z.ZodObject<{
        name: z.ZodString;
        version: z.ZodString;
        last_trained: z.ZodDate;
    }, "strip", z.ZodTypeAny, {
        name: string;
        version: string;
        last_trained: Date;
    }, {
        name: string;
        version: string;
        last_trained: Date;
    }>;
    explanation: z.ZodOptional<z.ZodString>;
    timestamp: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
    confidence: number;
    timestamp: Date;
    prediction: number;
    features_used: string[];
    model_info: {
        name: string;
        version: string;
        last_trained: Date;
    };
    explanation?: string | undefined;
}, {
    symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
    confidence: number;
    timestamp: Date;
    prediction: number;
    features_used: string[];
    model_info: {
        name: string;
        version: string;
        last_trained: Date;
    };
    explanation?: string | undefined;
}>;
export type MLPredictionResponse = z.infer<typeof MLPredictionResponseSchema>;
//# sourceMappingURL=chat.schemas.d.ts.map