# MVP Configuration
# This file contains the configuration for the MVP version of the trading platform

# MT5 Configuration
MT5_CONFIG = {
    "offline_mode": True,  # Set to False for live trading
    "terminal_path": "",   # Path to MT5 terminal (leave empty for auto-detection)
    "login": "",           # MT5 account login (not needed in offline mode)
    "password": "",        # MT5 account password (not needed in offline mode)
    "server": "",          # MT5 server (not needed in offline mode)
}

# Trading Configuration
TRADING_CONFIG = {
    "default_lot_size": 0.01,
    "max_lot_size": 1.0,
    "max_positions": 5,
    "default_symbols": ["EURUSD", "GBPUSD", "USDJPY"],
}

# Risk Management Configuration
RISK_CONFIG = {
    "max_drawdown_percent": 5.0,
    "max_risk_per_trade_percent": 2.0,
    "max_daily_loss_percent": 3.0,
}

# Logging Configuration
LOGGING_CONFIG = {
    "log_level": "INFO",
    "log_file": "logs/mvp_trading.log",
    "console_logging": True,
}
