export interface StrategySignal {
  signal: 'buy' | 'sell' | 'hold';
  confidence?: number;
  metadata?: Record<string, any>;
}

export interface Strategy {
  name: string;
  parameters: Record<string, any>;
}

export interface StrategyData {
  close: number[];
  open: number[];
  high: number[];
  low: number[];
  volume: number[];
}

export class StrategyExecutor {
  constructor() {}

  async execute(
    strategy: Strategy,
    data: StrategyData,
    parameters: Record<string, any>
  ): Promise<StrategySignal> {
    // Validate inputs
    if (!data.close || data.close.length === 0) {
      throw new Error('Invalid market data: close prices required');
    }

    // Execute strategy based on name
    switch (strategy.name) {
      case 'sma_crossover':
        return this.executeSMACrossover(data, parameters);
      case 'rsi_strategy':
        return this.executeRSIStrategy(data, parameters);
      case 'macd_strategy':
        return this.executeMACDStrategy(data, parameters);
      default:
        throw new Error(`Unknown strategy: ${strategy.name}`);
    }
  }

  async validateStrategy(strategy: Strategy): Promise<boolean> {
    try {
      // Validate strategy name
      const supportedStrategies = ['sma_crossover', 'rsi_strategy', 'macd_strategy'];
      if (!supportedStrategies.includes(strategy.name)) {
        return false;
      }

      // Validate parameters based on strategy
      switch (strategy.name) {
        case 'sma_crossover':
          return this.validateSMACrossoverParameters(strategy.parameters);
        case 'rsi_strategy':
          return this.validateRSIParameters(strategy.parameters);
        case 'macd_strategy':
          return this.validateMACDParameters(strategy.parameters);
        default:
          return false;
      }
    } catch (error) {
      return false;
    }
  }

  private executeSMACrossover(data: StrategyData, parameters: Record<string, any>): StrategySignal {
    const { fast_period = 10, slow_period = 20 } = parameters;
    
    if (data.close.length < slow_period) {
      return { signal: 'hold', confidence: 0 };
    }

    const fastSMA = this.calculateSMA(data.close, fast_period);
    const slowSMA = this.calculateSMA(data.close, slow_period);

    if (fastSMA.length === 0 || slowSMA.length === 0) {
      return { signal: 'hold', confidence: 0 };
    }

    const currentFastSMA = fastSMA[fastSMA.length - 1];
    const currentSlowSMA = slowSMA[slowSMA.length - 1];
    const prevFastSMA = fastSMA.length > 1 ? fastSMA[fastSMA.length - 2] : currentFastSMA;
    const prevSlowSMA = slowSMA.length > 1 ? slowSMA[slowSMA.length - 2] : currentSlowSMA;

    // Check for crossover
    if (prevFastSMA <= prevSlowSMA && currentFastSMA > currentSlowSMA) {
      return { signal: 'buy', confidence: 0.8 };
    } else if (prevFastSMA >= prevSlowSMA && currentFastSMA < currentSlowSMA) {
      return { signal: 'sell', confidence: 0.8 };
    }

    return { signal: 'hold', confidence: 0.5 };
  }

  private executeRSIStrategy(data: StrategyData, parameters: Record<string, any>): StrategySignal {
    const { period = 14, oversold = 30, overbought = 70 } = parameters;
    
    if (data.close.length < period + 1) {
      return { signal: 'hold', confidence: 0 };
    }

    const rsi = this.calculateRSI(data.close, period);
    
    if (rsi.length === 0) {
      return { signal: 'hold', confidence: 0 };
    }

    const currentRSI = rsi[rsi.length - 1];

    if (currentRSI < oversold) {
      return { signal: 'buy', confidence: 0.7 };
    } else if (currentRSI > overbought) {
      return { signal: 'sell', confidence: 0.7 };
    }

    return { signal: 'hold', confidence: 0.5 };
  }

  private executeMACDStrategy(data: StrategyData, parameters: Record<string, any>): StrategySignal {
    const { fast_period = 12, slow_period = 26, signal_period = 9 } = parameters;
    
    if (data.close.length < slow_period) {
      return { signal: 'hold', confidence: 0 };
    }

    const macd = this.calculateMACD(data.close, fast_period, slow_period, signal_period);
    
    if (!macd.macd || !macd.signal || macd.macd.length === 0 || macd.signal.length === 0) {
      return { signal: 'hold', confidence: 0 };
    }

    const currentMACD = macd.macd[macd.macd.length - 1];
    const currentSignal = macd.signal[macd.signal.length - 1];
    const prevMACD = macd.macd.length > 1 ? macd.macd[macd.macd.length - 2] : currentMACD;
    const prevSignal = macd.signal.length > 1 ? macd.signal[macd.signal.length - 2] : currentSignal;

    // Check for MACD crossover
    if (prevMACD <= prevSignal && currentMACD > currentSignal) {
      return { signal: 'buy', confidence: 0.75 };
    } else if (prevMACD >= prevSignal && currentMACD < currentSignal) {
      return { signal: 'sell', confidence: 0.75 };
    }

    return { signal: 'hold', confidence: 0.5 };
  }

  private validateSMACrossoverParameters(parameters: Record<string, any>): boolean {
    const { fast_period, slow_period } = parameters;
    
    if (typeof fast_period !== 'number' || typeof slow_period !== 'number') {
      return false;
    }
    
    if (fast_period <= 0 || slow_period <= 0) {
      return false;
    }
    
    if (fast_period >= slow_period) {
      return false;
    }
    
    return true;
  }

  private validateRSIParameters(parameters: Record<string, any>): boolean {
    const { period, oversold, overbought } = parameters;
    
    if (period && (typeof period !== 'number' || period <= 0)) {
      return false;
    }
    
    if (oversold && (typeof oversold !== 'number' || oversold < 0 || oversold > 100)) {
      return false;
    }
    
    if (overbought && (typeof overbought !== 'number' || overbought < 0 || overbought > 100)) {
      return false;
    }
    
    if (oversold && overbought && oversold >= overbought) {
      return false;
    }
    
    return true;
  }

  private validateMACDParameters(parameters: Record<string, any>): boolean {
    const { fast_period, slow_period, signal_period } = parameters;
    
    if (fast_period && (typeof fast_period !== 'number' || fast_period <= 0)) {
      return false;
    }
    
    if (slow_period && (typeof slow_period !== 'number' || slow_period <= 0)) {
      return false;
    }
    
    if (signal_period && (typeof signal_period !== 'number' || signal_period <= 0)) {
      return false;
    }
    
    if (fast_period && slow_period && fast_period >= slow_period) {
      return false;
    }
    
    return true;
  }

  // Technical indicator calculations
  private calculateSMA(values: number[], period: number): number[] {
    if (values.length < period) return [];
    
    const sma: number[] = [];
    for (let i = period - 1; i < values.length; i++) {
      const sum = values.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0);
      sma.push(sum / period);
    }
    
    return sma;
  }

  private calculateEMA(values: number[], period: number): number[] {
    if (values.length < period) return [];
    
    const ema: number[] = [];
    const multiplier = 2 / (period + 1);
    
    // Start with SMA for first value
    const firstSMA = values.slice(0, period).reduce((a, b) => a + b, 0) / period;
    ema.push(firstSMA);
    
    // Calculate EMA for remaining values
    for (let i = period; i < values.length; i++) {
      const emaValue = (values[i] * multiplier) + (ema[ema.length - 1] * (1 - multiplier));
      ema.push(emaValue);
    }
    
    return ema;
  }

  private calculateRSI(values: number[], period: number): number[] {
    if (values.length < period + 1) return [];
    
    const rsi: number[] = [];
    const gains: number[] = [];
    const losses: number[] = [];
    
    // Calculate price changes
    for (let i = 1; i < values.length; i++) {
      const change = values[i] - values[i - 1];
      gains.push(change > 0 ? change : 0);
      losses.push(change < 0 ? -change : 0);
    }
    
    // Calculate initial averages
    let avgGain = gains.slice(0, period).reduce((a, b) => a + b, 0) / period;
    let avgLoss = losses.slice(0, period).reduce((a, b) => a + b, 0) / period;
    
    // Calculate RSI values
    for (let i = period; i < gains.length; i++) {
      if (avgLoss === 0) {
        rsi.push(100);
      } else {
        const rs = avgGain / avgLoss;
        rsi.push(100 - (100 / (1 + rs)));
      }
      
      // Update averages (Wilder's smoothing)
      avgGain = ((avgGain * (period - 1)) + gains[i]) / period;
      avgLoss = ((avgLoss * (period - 1)) + losses[i]) / period;
    }
    
    return rsi;
  }

  private calculateMACD(
    values: number[], 
    fastPeriod: number, 
    slowPeriod: number, 
    signalPeriod: number
  ): { macd: number[]; signal: number[]; histogram: number[] } {
    if (values.length < slowPeriod) {
      return { macd: [], signal: [], histogram: [] };
    }
    
    const fastEMA = this.calculateEMA(values, fastPeriod);
    const slowEMA = this.calculateEMA(values, slowPeriod);
    
    if (fastEMA.length === 0 || slowEMA.length === 0) {
      return { macd: [], signal: [], histogram: [] };
    }
    
    // Align EMAs (slow EMA starts later)
    const startIndex = slowPeriod - fastPeriod;
    const macdLine: number[] = [];
    
    for (let i = 0; i < slowEMA.length; i++) {
      macdLine.push(fastEMA[i + startIndex] - slowEMA[i]);
    }
    
    const signalLine = this.calculateEMA(macdLine, signalPeriod);
    
    // Calculate histogram
    const histogram: number[] = [];
    const signalStartIndex = macdLine.length - signalLine.length;
    
    for (let i = 0; i < signalLine.length; i++) {
      histogram.push(macdLine[i + signalStartIndex] - signalLine[i]);
    }
    
    return {
      macd: macdLine,
      signal: signalLine,
      histogram,
    };
  }
}