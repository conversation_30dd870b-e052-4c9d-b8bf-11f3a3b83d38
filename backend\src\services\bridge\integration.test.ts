/**
 * Integration tests for bridge services with Python engine mock
 * Demonstrates unified testing strategy across Jest and Python pytest
 */

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import {
  setupIntegrationTest,
  waitForEvent,
  TestDataBuilder,
  PerformanceTestUtils,
  assertServiceResponse,
  validateTestData,
  TEST_FIXTURES,
  type IntegrationTestContext,
} from '@/shared/test-utils';
import { OrderRequest, BacktestConfig } from '@/shared/schemas';

describe('Bridge Services Integration Tests', () => {
  let testContext: IntegrationTestContext;

  beforeEach(async () => {
    testContext = await setupIntegrationTest({
      pythonEngine: {
        simulateNetworkDelay: true,
        networkDelayMs: 50, // Faster for testing
        simulateErrors: false,
        healthyStatus: true,
      },
      enableRealTimeEvents: true,
      testTimeout: 10000,
    });
  });

  afterEach(async () => {
    await testContext.cleanup();
  });

  describe('Trading Integration', () => {
    it('should execute complete trading workflow', async () => {
      const { tradingService } = testContext;

      // 1. Get account info
      const accountResult = await tradingService.getAccountInfo();
      assertServiceResponse.success(accountResult);
      expect(accountResult.data.balance).toBeGreaterThan(0);
      expect(accountResult.data.currency).toBe('USD');

      // 2. Submit valid order
      const orderScenario = TestDataBuilder.createOrderScenario('success');
      const orderResult = await tradingService.submitOrder(orderScenario.request);
      assertServiceResponse.success(orderResult);
      expect(orderResult.data.order_id).toBeDefined();

      // 3. Get positions
      const positionsResult = await tradingService.getPositions();
      assertServiceResponse.success(positionsResult);
      expect(Array.isArray(positionsResult.data)).toBe(true);

      // 4. Close order
      const closeResult = await tradingService.closeOrder(orderResult.data.order_id!);
      assertServiceResponse.success(closeResult);
    });

    it('should handle order validation errors', async () => {
      const { tradingService } = testContext;

      const invalidOrderScenario = TestDataBuilder.createOrderScenario('validation_error');
      const result = await tradingService.submitOrder(invalidOrderScenario.request);
      
      assertServiceResponse.validationError(result);
      expect(result.error.details).toContain('Volume must be positive');
    });

    it('should emit events during trading operations', async () => {
      const { tradingService } = testContext;

      // Setup event listener
      const orderSubmittedPromise = waitForEvent(tradingService, 'order_submitted', 5000);

      // Submit order
      const orderRequest = TEST_FIXTURES.TRADING.VALID_BUY_ORDER;
      const orderResult = await tradingService.submitOrder(orderRequest);
      assertServiceResponse.success(orderResult);

      // Wait for event
      const eventData = await orderSubmittedPromise;
      expect(eventData.orderRequest).toEqual(orderRequest);
      expect(eventData.orderResult.order_id).toBe(orderResult.data.order_id);
    });

    it('should handle engine health checks', async () => {
      const { tradingService } = testContext;

      const healthResult = await tradingService.getEngineHealth();
      assertServiceResponse.success(healthResult);
      expect(healthResult.data.healthy).toBe(true);
      expect(healthResult.data.lastCheck).toBeInstanceOf(Date);
    });
  });

  describe('Backtest Integration', () => {
    it('should execute fast backtest workflow', async () => {
      const { backtestService } = testContext;

      // Submit fast backtest (single symbol)
      const backtestScenario = TestDataBuilder.createBacktestScenario('fast');
      const marketData = TEST_FIXTURES.TRADING.MARKET_DATA;

      const submitResult = await backtestService.submitBacktest(
        backtestScenario.config,
        marketData
      );
      assertServiceResponse.success(submitResult);
      expect(submitResult.data.backtestId).toBeDefined();

      // Check status
      const statusResult = await backtestService.getBacktestStatus(submitResult.data.backtestId);
      assertServiceResponse.success(statusResult);
      expect(['pending', 'running', 'completed']).toContain(statusResult.data.status);
    });

    it('should track backtest progress for complex strategies', async () => {
      const { backtestService } = testContext;

      // Submit slow backtest (multi-symbol)
      const backtestScenario = TestDataBuilder.createBacktestScenario('slow');
      const marketData = TEST_FIXTURES.TRADING.MARKET_DATA;

      const submitResult = await backtestService.submitBacktest(
        backtestScenario.config,
        marketData
      );
      assertServiceResponse.success(submitResult);

      const backtestId = submitResult.data.backtestId;

      // Monitor progress
      let finalStatus;
      let attempts = 0;
      const maxAttempts = 10;

      while (attempts < maxAttempts) {
        const statusResult = await backtestService.getBacktestStatus(backtestId);
        assertServiceResponse.success(statusResult);
        
        finalStatus = statusResult.data;
        
        if (finalStatus.status === 'completed' || finalStatus.status === 'error') {
          break;
        }
        
        await new Promise(resolve => setTimeout(resolve, 200));
        attempts++;
      }

      expect(finalStatus?.status).toBeDefined();
      expect(finalStatus?.progress).toBeGreaterThanOrEqual(0);
    });

    it('should validate backtest configuration', async () => {
      const { backtestService } = testContext;

      const invalidScenario = TestDataBuilder.createBacktestScenario('error');
      const marketData = TEST_FIXTURES.TRADING.MARKET_DATA;

      const result = await backtestService.submitBacktest(
        invalidScenario.config,
        marketData
      );

      assertServiceResponse.validationError(result);
    });

    it('should emit backtest progress events', async () => {
      const { backtestService } = testContext;

      // Setup event listener
      const progressPromise = waitForEvent(backtestService, 'backtest_progress_updated', 10000);

      // Submit complex backtest
      const backtestScenario = TestDataBuilder.createBacktestScenario('slow');
      const marketData = TEST_FIXTURES.TRADING.MARKET_DATA;

      await backtestService.submitBacktest(backtestScenario.config, marketData);

      // Wait for progress event
      const progressData = await progressPromise;
      expect(progressData.backtestId).toBeDefined();
      expect(progressData.progress).toBeGreaterThanOrEqual(0);
      expect(progressData.status).toBeDefined();
    });
  });

  describe('Chat Integration', () => {
    it('should process chat messages with context', async () => {
      const { chatService } = testContext;

      const sessionId = 'test-session-123';
      const userId = 'test-user';
      const chatScenario = TestDataBuilder.createChatScenario('analysis');

      const result = await chatService.sendMessage(
        sessionId,
        chatScenario.query,
        userId,
        {
          trading_data: {
            balance: 10000,
            open_positions: [],
          },
        }
      );

      assertServiceResponse.success(result);
      expect(result.data.message).toBeDefined();
      expect(result.data.type).toBe(chatScenario.expectedResponse.type);
      expect(result.data.confidence).toBeGreaterThan(0);
    });

    it('should maintain conversation history', async () => {
      const { chatService } = testContext;

      const sessionId = 'test-session-456';
      const userId = 'test-user';

      // Send first message
      await chatService.sendMessage(sessionId, 'Hello', userId);
      
      // Send second message
      await chatService.sendMessage(sessionId, 'What is EURUSD trend?', userId);

      // Check conversation history
      const history = chatService.getConversationHistory(sessionId);
      expect(history).toHaveLength(4); // 2 user + 2 assistant messages
      expect(history[0].role).toBe('user');
      expect(history[0].content).toBe('Hello');
      expect(history[2].role).toBe('user');
      expect(history[2].content).toBe('What is EURUSD trend?');
    });

    it('should handle empty messages', async () => {
      const { chatService } = testContext;

      const result = await chatService.sendMessage('session-123', '', 'user-123');
      assertServiceResponse.failure(result);
      expect(result.error.code).toBe('INVALID_MESSAGE');
    });

    it('should update session context', async () => {
      const { chatService } = testContext;

      const sessionId = 'test-session-789';
      const userId = 'test-user';

      // Create session by sending a message
      await chatService.sendMessage(sessionId, 'Hello', userId);

      // Update context
      const contextUpdate = {
        trading_symbols: ['EURUSD', 'GBPUSD'],
        risk_tolerance: 'medium' as const,
      };

      const result = await chatService.updateSessionContext(sessionId, contextUpdate);
      assertServiceResponse.success(result);
      expect(result.data.context).toMatchObject(contextUpdate);
    });
  });

  describe('System Health Integration', () => {
    it('should report overall system health', async () => {
      const { bridgeRegistry } = testContext;

      const health = await bridgeRegistry.getSystemHealth();
      expect(health.healthy).toBe(true);
      expect(health.services.pythonEngine.healthy).toBe(true);
      expect(health.services.trading).toBe(true);
      expect(health.services.backtest).toBe(true);
      expect(health.services.chat).toBe(true);
      expect(health.timestamp).toBeInstanceOf(Date);
    });

    it('should handle unhealthy python engine', async () => {
      const { bridgeRegistry, pythonEngineMock } = testContext;

      // Simulate unhealthy engine
      pythonEngineMock.setHealthStatus(false);

      const health = await bridgeRegistry.getSystemHealth();
      expect(health.healthy).toBe(false);
      expect(health.services.pythonEngine.healthy).toBe(false);
      // All services depend on python engine
      expect(health.services.trading).toBe(false);
      expect(health.services.backtest).toBe(false);
      expect(health.services.chat).toBe(false);
    });

    it('should provide service statistics', async () => {
      const { bridgeRegistry } = testContext;

      const stats = bridgeRegistry.getServiceStatistics();
      expect(stats.activeBacktests).toBeGreaterThanOrEqual(0);
      expect(stats.activeChatSessions).toBeGreaterThanOrEqual(0);
      expect(stats.pythonEngineHealth).toBeDefined();
    });
  });

  describe('Performance Testing', () => {
    it('should handle concurrent trading operations', async () => {
      const { tradingService } = testContext;

      const concurrentOperations = async () => {
        const promises = Array.from({ length: 5 }, () =>
          tradingService.getAccountInfo()
        );
        return await Promise.all(promises);
      };

      const { results, executionTime, throughput } = await PerformanceTestUtils.measureThroughput(
        concurrentOperations,
        3, // 3 iterations
        1  // 1 concurrent batch
      );

      expect(results).toHaveLength(3);
      expect(executionTime).toBeGreaterThan(0);
      expect(throughput).toBeGreaterThan(0);
      
      // All operations should succeed
      results.forEach(batchResults => {
        batchResults.forEach(result => {
          assertServiceResponse.success(result);
        });
      });
    });

    it('should measure backtest submission performance', async () => {
      const { backtestService } = testContext;

      const submitBacktest = async () => {
        const config = TEST_FIXTURES.BACKTEST.SIMPLE_MA_STRATEGY;
        const marketData = TEST_FIXTURES.TRADING.MARKET_DATA;
        return await backtestService.submitBacktest(config, marketData);
      };

      const { result, executionTime } = await PerformanceTestUtils.measureExecutionTime(
        submitBacktest
      );

      assertServiceResponse.success(result);
      expect(executionTime).toBeLessThan(5000); // Should complete within 5 seconds
    });
  });

  describe('Error Recovery', () => {
    it('should recover from temporary python engine failures', async () => {
      const { tradingService, pythonEngineMock } = testContext;

      // Simulate engine failure
      pythonEngineMock.setHealthStatus(false);

      // Operation should fail
      const failedResult = await tradingService.getAccountInfo();
      assertServiceResponse.failure(failedResult);

      // Restore engine health
      pythonEngineMock.setHealthStatus(true);

      // Operation should succeed again
      const successResult = await tradingService.getAccountInfo();
      assertServiceResponse.success(successResult);
    });

    it('should handle network timeouts gracefully', async () => {
      const { tradingService } = testContext;

      // Create a service with very short timeout
      const quickTimeoutContext = await setupIntegrationTest({
        pythonEngine: {
          simulateNetworkDelay: true,
          networkDelayMs: 2000, // 2 second delay
        },
        testTimeout: 1000, // 1 second timeout
      });

      try {
        const result = await quickTimeoutContext.tradingService.getAccountInfo();
        assertServiceResponse.failure(result);
        expect(result.error.message).toContain('timeout');
      } finally {
        await quickTimeoutContext.cleanup();
      }
    });
  });

  describe('Data Validation', () => {
    it('should validate all test fixture data', () => {
      // Validate trading fixtures
      expect(validateTestData.orderRequest(TEST_FIXTURES.TRADING.VALID_BUY_ORDER)).toBe(true);
      expect(validateTestData.orderRequest(TEST_FIXTURES.TRADING.VALID_SELL_ORDER)).toBe(true);
      expect(validateTestData.orderRequest(TEST_FIXTURES.TRADING.INVALID_ORDERS.NEGATIVE_VOLUME)).toBe(false);

      // Validate backtest fixtures
      expect(validateTestData.backtestConfig(TEST_FIXTURES.BACKTEST.SIMPLE_MA_STRATEGY)).toBe(true);
      expect(validateTestData.backtestConfig(TEST_FIXTURES.BACKTEST.MULTI_SYMBOL_STRATEGY)).toBe(true);
      expect(validateTestData.backtestConfig(TEST_FIXTURES.BACKTEST.INVALID_CONFIGS.INVALID_DATE_RANGE)).toBe(false);

      // Validate chat fixtures
      expect(validateTestData.chatMessage(TEST_FIXTURES.CHAT.USER_MESSAGES[0])).toBe(true);
      expect(validateTestData.chatMessage(TEST_FIXTURES.CHAT.ASSISTANT_MESSAGES[0])).toBe(true);
    });

    it('should maintain schema compatibility across systems', () => {
      // This test ensures our TypeScript schemas work with Python data
      const pythonOrderRequest = {
        symbol: 'EURUSD',
        volume: 0.01,
        order_type: 'buy',
        price: 1.1000,
        stop_loss: 1.0950,
        take_profit: 1.1050,
      };

      expect(validateTestData.orderRequest(pythonOrderRequest)).toBe(true);
    });
  });
});