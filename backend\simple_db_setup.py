"""
Simple database setup script without <PERSON>em<PERSON>
"""

import os
import sys
import logging
from dotenv import load_dotenv
from sqlalchemy import create_engine, Column, Integer, String, Boolean, Float, DateTime, ForeignKey, Text, MetaData, Table
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from datetime import datetime
import bcrypt

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("simple_db_setup")

# Load environment variables
load_dotenv()

# Create base class for models
Base = declarative_base()

# Define models
class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True)
    hashed_password = Column(String)
    full_name = Column(String)
    role = Column(String, default="user")
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Strategy(Base):
    __tablename__ = "strategies"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    description = Column(Text)
    parameters = Column(Text)  # JSON string
    user_id = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Backtest(Base):
    __tablename__ = "backtests"
    
    id = Column(Integer, primary_key=True, index=True)
    strategy_id = Column(Integer, ForeignKey("strategies.id"))
    start_date = Column(DateTime)
    end_date = Column(DateTime)
    symbol = Column(String)
    timeframe = Column(String)
    initial_capital = Column(Float)
    results = Column(Text)  # JSON string
    user_id = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime, default=datetime.utcnow)

def check_database_url():
    """Check if DATABASE_URL is set and valid"""
    db_url = os.getenv("DATABASE_URL")
    if not db_url:
        logger.error("DATABASE_URL environment variable is not set")
        return False
    
    if db_url.startswith("sqlite:"):
        logger.info("Using SQLite database")
        return True
    
    if db_url.startswith("postgresql:"):
        logger.info("Using PostgreSQL database")
        return True
    
    logger.error(f"Unsupported database URL: {db_url}")
    return False

def create_tables():
    """Create database tables"""
    db_url = os.getenv("DATABASE_URL")
    if not db_url:
        logger.error("DATABASE_URL not set")
        return False
    
    try:
        # Create engine
        engine = create_engine(db_url)
        
        # Create tables
        Base.metadata.create_all(engine)
        
        logger.info("Database tables created successfully")
        return True
    except Exception as e:
        logger.error(f"Error creating database tables: {e}")
        return False

def create_test_users():
    """Create test users"""
    db_url = os.getenv("DATABASE_URL")
    if not db_url:
        logger.error("DATABASE_URL not set")
        return False
    
    try:
        # Create engine
        engine = create_engine(db_url)
        
        # Create session
        Session = sessionmaker(bind=engine)
        session = Session()
        
        # Check if admin user already exists
        admin_user = session.query(User).filter(User.email == "<EMAIL>").first()
        if not admin_user:
            # Create admin user
            hashed_password = bcrypt.hashpw("admin123".encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            admin_user = User(
                email="<EMAIL>",
                hashed_password=hashed_password,
                full_name="Admin User",
                role="admin"
            )
            session.add(admin_user)
            logger.info("Admin user created")
        else:
            logger.info("Admin user already exists")
        
        # Check if regular user already exists
        regular_user = session.query(User).filter(User.email == "<EMAIL>").first()
        if not regular_user:
            # Create regular user
            hashed_password = bcrypt.hashpw("user123".encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            regular_user = User(
                email="<EMAIL>",
                hashed_password=hashed_password,
                full_name="Regular User",
                role="user"
            )
            session.add(regular_user)
            logger.info("Regular user created")
        else:
            logger.info("Regular user already exists")
        
        # Commit changes
        session.commit()
        session.close()
        
        return True
    except Exception as e:
        logger.error(f"Error creating test users: {e}")
        return False

def create_sample_data():
    """Create sample data for testing"""
    db_url = os.getenv("DATABASE_URL")
    if not db_url:
        logger.error("DATABASE_URL not set")
        return False
    
    try:
        # Create engine
        engine = create_engine(db_url)
        
        # Create session
        Session = sessionmaker(bind=engine)
        session = Session()
        
        # Get admin user
        admin_user = session.query(User).filter(User.email == "<EMAIL>").first()
        if not admin_user:
            logger.error("Admin user not found")
            return False
        
        # Check if sample strategy already exists
        sample_strategy = session.query(Strategy).filter(Strategy.name == "Simple Moving Average Crossover").first()
        if not sample_strategy:
            # Create sample strategy
            sample_strategy = Strategy(
                name="Simple Moving Average Crossover",
                description="Buy when fast MA crosses above slow MA, sell when it crosses below",
                parameters='{"fast_period": 10, "slow_period": 30}',
                user_id=admin_user.id
            )
            session.add(sample_strategy)
            logger.info("Sample strategy created")
        else:
            logger.info("Sample strategy already exists")
        
        # Commit to get the strategy ID
        session.commit()
        
        # Check if sample backtest already exists
        sample_backtest = session.query(Backtest).filter(
            Backtest.strategy_id == sample_strategy.id,
            Backtest.symbol == "EURUSD"
        ).first()
        if not sample_backtest:
            # Create sample backtest
            sample_backtest = Backtest(
                strategy_id=sample_strategy.id,
                start_date=datetime(2023, 1, 1),
                end_date=datetime(2023, 12, 31),
                symbol="EURUSD",
                timeframe="H1",
                initial_capital=10000,
                results='{"final_capital": 12500, "profit_factor": 1.8, "sharpe_ratio": 1.2, "max_drawdown": 8.5, "win_rate": 65.2}',
                user_id=admin_user.id
            )
            session.add(sample_backtest)
            logger.info("Sample backtest created")
        else:
            logger.info("Sample backtest already exists")
        
        # Commit changes
        session.commit()
        session.close()
        
        return True
    except Exception as e:
        logger.error(f"Error creating sample data: {e}")
        return False

def main():
    """Main function"""
    # Check database URL
    if not check_database_url():
        sys.exit(1)
    
    # Create tables
    if not create_tables():
        sys.exit(1)
    
    # Create test users
    if not create_test_users():
        logger.warning("Failed to create test users, but continuing...")
    
    # Create sample data
    if not create_sample_data():
        logger.warning("Failed to create sample data, but continuing...")
    
    logger.info("Database setup completed successfully")

if __name__ == "__main__":
    main()