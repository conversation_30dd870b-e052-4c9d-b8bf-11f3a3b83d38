{"version": 3, "file": "auth.schemas.js", "sourceRoot": "", "sources": ["auth.schemas.ts"], "names": [], "mappings": ";;;AAAA,6BAAwB;AACxB,qDAA4C;AAE5C,qBAAqB;AACR,QAAA,sBAAsB,GAAG,OAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC,CAAC;AAGpF,+BAA+B;AAClB,QAAA,YAAY,GAAG,yBAAQ,CAAC,KAAK,EAAY,CAAC;AAGvD,mBAAmB;AACN,QAAA,UAAU,GAAG,OAAC,CAAC,MAAM,CAAC;IACjC,EAAE,EAAE,oBAAY;IAChB,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE;IACzB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC/B,gBAAgB,EAAE,8BAAsB;IACxC,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE;IAC5C,aAAa,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;IAC1C,SAAS,EAAE,OAAC,CAAC,IAAI,EAAE;IACnB,SAAS,EAAE,OAAC,CAAC,IAAI,EAAE;CACpB,CAAC,CAAC;AAGH,4CAA4C;AAC/B,QAAA,0BAA0B,GAAG,kBAAU,CAAC,MAAM,CAAC;IAC1D,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;CAChC,CAAC,CAAC;AAGH,iCAAiC;AACpB,QAAA,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;IACzC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE;IACzB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;CAC5B,CAAC,CAAC;AAGU,QAAA,uBAAuB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC9C,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE;IACzB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAChC,CAAC,QAAQ,EAAE,EAAE;QACX,MAAM,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5C,MAAM,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5C,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACvC,MAAM,cAAc,GAAG,wBAAwB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/D,OAAO,YAAY,IAAI,YAAY,IAAI,UAAU,IAAI,cAAc,CAAC;IACtE,CAAC,EACD;QACE,OAAO,EAAE,2EAA2E;KACrF,CACF;IACD,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;CACvC,CAAC,CAAC;AAGU,QAAA,yBAAyB,GAAG,OAAC,CAAC,MAAM,CAAC;IAChD,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;CAChC,CAAC,CAAC;AAGU,QAAA,0BAA0B,GAAG,OAAC,CAAC,MAAM,CAAC;IACjD,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE;CAC1B,CAAC,CAAC;AAGU,QAAA,iCAAiC,GAAG,OAAC,CAAC,MAAM,CAAC;IACxD,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IACxB,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;CAC/B,CAAC,CAAC;AAGU,QAAA,2BAA2B,GAAG,OAAC,CAAC,MAAM,CAAC;IAClD,eAAe,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAClC,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;CAC/B,CAAC,CAAC;AAGH,kCAAkC;AACrB,QAAA,gBAAgB,GAAG,OAAC,CAAC,MAAM,CAAC;IACvC,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE;IACvB,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE;IACxB,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;CACvC,CAAC,CAAC;AAGU,QAAA,gBAAgB,GAAG,OAAC,CAAC,MAAM,CAAC;IACvC,IAAI,EAAE,kBAAU;IAChB,MAAM,EAAE,wBAAgB;CACzB,CAAC,CAAC;AAGH,4BAA4B;AACf,QAAA,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;IACzC,MAAM,EAAE,oBAAY;IACpB,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE;IACpC,gBAAgB,EAAE,8BAAsB,CAAC,QAAQ,EAAE;IACnD,IAAI,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;IACnC,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC1B,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CAC3B,CAAC,CAAC;AAGU,QAAA,yBAAyB,GAAG,OAAC,CAAC,MAAM,CAAC;IAChD,MAAM,EAAE,oBAAY;IACpB,IAAI,EAAE,OAAC,CAAC,OAAO,CAAC,SAAS,CAAC;IAC1B,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC1B,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CAC3B,CAAC,CAAC;AAGH,uBAAuB;AACV,QAAA,8BAA8B,GAAG,OAAC,CAAC,MAAM,CAAC;IACrD,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;IACtC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE;CACrC,CAAC,CAAC;AAGU,QAAA,iBAAiB,GAAG,kBAAU,CAAC,IAAI,CAAC;IAC/C,YAAY,EAAE,IAAI;IAClB,aAAa,EAAE,IAAI;CACpB,CAAC,CAAC,MAAM,CAAC;IACR,WAAW,EAAE,OAAC,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;IAChC,aAAa,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IACzC,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAClC,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IACnC,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;QACpB,KAAK,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;QACjD,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;QAClC,aAAa,EAAE,OAAC,CAAC,MAAM,CAAC;YACtB,KAAK,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;YAChC,IAAI,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;YAC/B,OAAO,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;SACnC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;KACf,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;CACf,CAAC,CAAC;AAGH,kBAAkB;AACL,QAAA,aAAa,GAAG,OAAC,CAAC,MAAM,CAAC;IACpC,EAAE,EAAE,yBAAQ;IACZ,MAAM,EAAE,oBAAY;IACpB,UAAU,EAAE,OAAC,CAAC,MAAM,CAAC;QACnB,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE;QACrB,EAAE,EAAE,OAAC,CAAC,MAAM,EAAE;QACd,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;KAChC,CAAC;IACF,QAAQ,EAAE,OAAC,CAAC,OAAO,EAAE;IACrB,YAAY,EAAE,OAAC,CAAC,IAAI,EAAE;IACtB,SAAS,EAAE,OAAC,CAAC,IAAI,EAAE;IACnB,SAAS,EAAE,OAAC,CAAC,IAAI,EAAE;CACpB,CAAC,CAAC;AAGH,4CAA4C;AAC/B,QAAA,YAAY,GAAG,OAAC,CAAC,MAAM,CAAC;IACnC,EAAE,EAAE,yBAAQ;IACZ,MAAM,EAAE,oBAAY;IACpB,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IACvB,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,mCAAmC;IAC1D,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE;IACrB,WAAW,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,IAAI,CAAC;QAC1B,cAAc,EAAE,aAAa,EAAE,cAAc;QAC7C,gBAAgB,EAAE,iBAAiB;QACnC,cAAc,EAAE,eAAe;QAC/B,WAAW,EAAE,YAAY;KAC1B,CAAC,CAAC;IACH,QAAQ,EAAE,OAAC,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;IAC7B,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;IACrD,QAAQ,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;IACnC,SAAS,EAAE,OAAC,CAAC,IAAI,EAAE;IACnB,SAAS,EAAE,OAAC,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;CAC/B,CAAC,CAAC;AAGU,QAAA,yBAAyB,GAAG,OAAC,CAAC,MAAM,CAAC;IAChD,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;IAChC,WAAW,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,IAAI,CAAC;QAC1B,cAAc,EAAE,aAAa,EAAE,cAAc;QAC7C,gBAAgB,EAAE,iBAAiB;QACnC,cAAc,EAAE,eAAe;QAC/B,WAAW,EAAE,YAAY;KAC1B,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACV,SAAS,EAAE,OAAC,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;CAC/B,CAAC,CAAC;AAGU,QAAA,oBAAoB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC3C,EAAE,EAAE,yBAAQ;IACZ,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE;IAChB,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE;IACrB,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,4BAA4B;IACjD,WAAW,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC;IAChC,SAAS,EAAE,OAAC,CAAC,IAAI,EAAE;IACnB,SAAS,EAAE,OAAC,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;CAC/B,CAAC,CAAC;AAGH,8CAA8C;AACjC,QAAA,mBAAmB,GAAG,OAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC;AAGhE,QAAA,0BAA0B,GAAG,OAAC,CAAC,MAAM,CAAC;IACjD,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE;IAChB,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE;IACjB,QAAQ,EAAE,2BAAmB;CAC9B,CAAC,CAAC;AAGH,oCAAoC;AACvB,QAAA,mBAAmB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC1C,OAAO,EAAE,OAAC,CAAC,OAAO,EAAE;IACpB,WAAW,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IAC3C,QAAQ,EAAE,OAAC,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;CAC9B,CAAC,CAAC;AAEU,QAAA,4BAA4B,GAAG,OAAC,CAAC,MAAM,CAAC;IACnD,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE;CACrB,CAAC,CAAC;AAGU,QAAA,4BAA4B,GAAG,OAAC,CAAC,MAAM,CAAC;IACnD,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;CAC3B,CAAC,CAAC;AAGH,+BAA+B;AAClB,QAAA,sBAAsB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC7C,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,qBAAqB;IAC5D,QAAQ,EAAE,OAAC,CAAC,MAAM,CAAC;QACjB,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QAC9B,WAAW,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC;KACjC,CAAC;IACF,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,+BAA+B;CACvD,CAAC,CAAC"}