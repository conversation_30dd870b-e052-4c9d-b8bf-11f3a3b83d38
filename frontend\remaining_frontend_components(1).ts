# Remaining Frontend Components & Pages

## src/components/upload/ColumnMapping.tsx
```typescript
import React, { useState } from 'react';
import { Check, X, Clock } from 'lucide-react';
import { UploadSession } from '../../types/api';

interface ColumnMappingProps {
  uploadSession: UploadSession;
  onConfirm: (finalMapping: Record<string, string>, timezone: string) => void;
  onCancel: () => void;
}

const COLUMN_TYPES = [
  'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'Bid', 'Ask', 'Ignore'
];

const TIMEZONES = [
  'UTC', 'US/Eastern', 'US/Central', 'US/Mountain', 'US/Pacific',
  'Europe/London', 'Europe/Paris', 'Europe/Berlin', 'Asia/Tokyo',
  'Asia/Hong_Kong', 'Asia/Singapore', 'Australia/Sydney'
];

export function ColumnMapping({ uploadSession, onConfirm, onCancel }: ColumnMappingProps) {
  const [mapping, setMapping] = useState<Record<string, string>>(
    uploadSession.inferredColumns
  );
  const [timezone, setTimezone] = useState('UTC');

  const handleMappingChange = (column: string, newType: string) => {
    setMapping(prev => ({ ...prev, [column]: newType }));
  };

  const handleConfirm = () => {
    // Validate that we have at least a time column
    const hasTimeColumn = Object.values(mapping).includes('Time');
    if (!hasTimeColumn) {
      alert('Please map at least one column to "Time"');
      return;
    }

    onConfirm(mapping, timezone);
  };

  const getColumnTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      'Time': 'bg-blue-100 text-blue-800',
      'Open': 'bg-green-100 text-green-800',
      'High': 'bg-emerald-100 text-emerald-800',
      'Low': 'bg-red-100 text-red-800',
      'Close': 'bg-purple-100 text-purple-800',
      'Volume': 'bg-orange-100 text-orange-800',
      'Bid': 'bg-cyan-100 text-cyan-800',
      'Ask': 'bg-teal-100 text-teal-800',
      'Ignore': 'bg-gray-100 text-gray-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="w-full max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Column Mapping</h2>
        <p className="text-gray-600">
          Review and adjust the column mappings for your file: <span className="font-medium">{uploadSession.originalFilename}</span>
        </p>
      </div>

      {/* Preview Table */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-3">Data Preview</h3>
        <div className="overflow-x-auto border border-gray-200 rounded-lg">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {Object.keys(mapping).map((column) => (
                  <th key={column} className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="space-y-2">
                      <span>{column}</span>
                      <div>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getColumnTypeColor(mapping[column])}`}>
                          {mapping[column]}
                        </span>
                      </div>
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {/* Sample data rows would go here */}
              <tr>
                {Object.keys(mapping).map((column, index) => (
                  <td key={column} className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">
                    Sample data {index + 1}
                  </td>
                ))}
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* Column Mapping Controls */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-3">Column Mappings</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(mapping).map(([column, currentType]) => (
            <div key={column} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg">
              <div className="flex-1">
                <span className="text-sm font-medium text-gray-900">{column}</span>
              </div>
              
              <div className="flex-shrink-0">
                <select
                  value={currentType}
                  onChange={(e) => handleMappingChange(column, e.target.value)}
                  className="text-sm border border-gray-300 rounded-md px-3 py-1 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {COLUMN_TYPES.map((type) => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Timezone Selection */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
          <Clock className="w-5 h-5 mr-2" />
          Timezone
        </h3>
        <div className="max-w-xs">
          <select
            value={timezone}
            onChange={(e) => setTimezone(e.target.value)}
            className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {TIMEZONES.map((tz) => (
              <option key={tz} value={tz}>{tz}</option>
            ))}
          </select>
          <p className="text-sm text-gray-500 mt-1">
            Select the timezone of your timestamp data. It will be converted to UTC for storage.
          </p>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3">
        <button
          onClick={onCancel}
          className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 flex items-center space-x-2"
        >
          <X className="w-4 h-4" />
          <span>Cancel</span>
        </button>
        
        <button
          onClick={handleConfirm}
          className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center space-x-2"
        >
          <Check className="w-4 h-4" />
          <span>Confirm & Process</span>
        </button>
      </div>
    </div>
  );
}
```

## src/components/upload/UploadProgress.tsx
```typescript
import React from 'react';
import { FileText, CheckCircle } from 'lucide-react';

interface UploadProgressProps {
  progress: number;
  fileName?: string;
  status?: string;
}

export function UploadProgress({ progress, fileName, status }: UploadProgressProps) {
  return (
    <div className="w-full max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
      <div className="text-center mb-4">
        <FileText className="w-12 h-12 text-blue-600 mx-auto mb-2" />
        <h3 className="text-lg font-semibold text-gray-900">Processing File</h3>
        {fileName && (
          <p className="text-sm text-gray-600">{fileName}</p>
        )}
      </div>

      <div className="mb-4">
        <div className="flex justify-between text-sm text-gray-600 mb-1">
          <span>Progress</span>
          <span>{progress}%</span>
        </div>
        
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-in-out"
            style={{ width: `${progress}%` }}
          />
        </div>
      </div>

      <div className="text-center">
        {progress < 100 ? (
          <p className="text-sm text-gray-600">
            {status || 'Parsing and validating your trading data...'}
          </p>
        ) : (
          <div className="flex items-center justify-center text-green-600">
            <CheckCircle className="w-5 h-5 mr-2" />
            <span className="text-sm font-medium">Processing complete!</span>
          </div>
        )}
      </div>
    </div>
  );
}
```

## src/components/backtest/EquityCurve.tsx
```typescript
import React from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ReferenceLine } from 'recharts';
import { EquityPoint } from '../../types/trading';

interface EquityCurveProps {
  data: EquityPoint[];
  initialBalance: number;
}

export function EquityCurve({ data, initialBalance }: EquityCurveProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleDateString();
  };

  const totalReturn = data.length > 0 
    ? ((data[data.length - 1].equity - initialBalance) / initialBalance) * 100 
    : 0;

  const maxEquity = Math.max(...data.map(d => d.equity));
  const minEquity = Math.min(...data.map(d => d.equity));

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Equity Curve</h3>
        <div className="flex items-center space-x-6 text-sm">
          <div>
            <span className="text-gray-600">Total Return: </span>
            <span className={`font-medium ${totalReturn >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {totalReturn >= 0 ? '+' : ''}{totalReturn.toFixed(2)}%
            </span>
          </div>
          <div>
            <span className="text-gray-600">Final Equity: </span>
            <span className="font-medium text-gray-900">
              {data.length > 0 ? formatCurrency(data[data.length - 1].equity) : formatCurrency(initialBalance)}
            </span>
          </div>
        </div>
      </div>

      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={data}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="timestamp"
              tickFormatter={formatDate}
              stroke="#6b7280"
              fontSize={12}
            />
            <YAxis 
              tickFormatter={formatCurrency}
              stroke="#6b7280"
              fontSize={12}
              domain={[minEquity * 0.98, maxEquity * 1.02]}
            />
            <Tooltip 
              labelFormatter={(value) => `Date: ${formatDate(value as string)}`}
              formatter={(value: number) => [formatCurrency(value), 'Equity']}
              contentStyle={{
                backgroundColor: '#f9fafb',
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                fontSize: '12px'
              }}
            />
            <ReferenceLine 
              y={initialBalance} 
              stroke="#6b7280" 
              strokeDasharray="5 5"
              label={{ value: "Initial Balance", position: "top" }}
            />
            <Line 
              type="monotone" 
              dataKey="equity" 
              stroke="#2563eb" 
              strokeWidth={2}
              dot={false}
              activeDot={{ r: 4, fill: '#2563eb' }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
```

## src/components/dgm/DGMExperiments.tsx
```typescript
import React, { useState, useEffect } from 'react';
import { Play, Beaker, TrendingUp, AlertCircle, CheckCircle, Clock } from 'lucide-react';
import { apiService } from '../../services/api';
import { DGMExperiment } from '../../types/api';
import { useApi } from '../../hooks/useApi';

export function DGMExperiments() {
  const [experiments, setExperiments] = useState<DGMExperiment[]>([]);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const { loading, execute } = useApi();

  useEffect(() => {
    loadExperiments();
  }, []);

  const loadExperiments = async () => {
    try {
      const data = await apiService.getDGMExperiments();
      setExperiments(data);
    } catch (error) {
      console.error('Failed to load experiments:', error);
    }
  };

  const createExperiment = async (name: string, baseStrategy: Record<string, any>) => {
    try {
      await execute(() => apiService.createDGMExperiment(name, baseStrategy));
      await loadExperiments();
      setShowCreateForm(false);
    } catch (error) {
      console.error('Failed to create experiment:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-5 h-5 text-yellow-500" />;
      case 'running':
        return <Play className="w-5 h-5 text-blue-500 animate-pulse" />;
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'deployed':
        return <TrendingUp className="w-5 h-5 text-purple-500" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      'pending': 'bg-yellow-100 text-yellow-800',
      'running': 'bg-blue-100 text-blue-800',
      'completed': 'bg-green-100 text-green-800',
      'deployed': 'bg-purple-100 text-purple-800',
      'error': 'bg-red-100 text-red-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Darwin Gödel Machine</h1>
          <p className="text-gray-600">Evolve and optimize your trading strategies automatically</p>
        </div>
        
        <button
          onClick={() => setShowCreateForm(true)}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center space-x-2"
        >
          <Beaker className="w-4 h-4" />
          <span>New Experiment</span>
        </button>
      </div>

      {/* Experiments List */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Experiments</h2>
        </div>
        
        <div className="divide-y divide-gray-200">
          {experiments.length === 0 ? (
            <div className="px-6 py-8 text-center">
              <Beaker className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No experiments yet</h3>
              <p className="text-gray-600 mb-4">
                Create your first DGM experiment to start evolving trading strategies
              </p>
              <button
                onClick={() => setShowCreateForm(true)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Create Experiment
              </button>
            </div>
          ) : (
            experiments.map((experiment) => (
              <div key={experiment.id} className="px-6 py-4 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(experiment.status)}
                      <h3 className="text-lg font-medium text-gray-900">
                        {experiment.experimentName}
                      </h3>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(experiment.status)}`}>
                        {experiment.status}
                      </span>
                    </div>
                    
                    <div className="mt-2 flex items-center space-x-6 text-sm text-gray-600">
                      <span>Created: {new Date(experiment.createdAt).toLocaleDateString()}</span>
                      
                      {experiment.fitnessScore !== undefined && (
                        <span>
                          Fitness Score: <span className="font-medium">{experiment.fitnessScore.toFixed(3)}</span>
                        </span>
                      )}
                      
                      {experiment.improvementOverBase !== undefined && (
                        <span className={experiment.improvementOverBase > 0 ? 'text-green-600' : 'text-red-600'}>
                          Improvement: {experiment.improvementOverBase > 0 ? '+' : ''}{(experiment.improvementOverBase * 100).toFixed(2)}%
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button className="px-3 py-1 text-sm text-blue-600 hover:bg-blue-50 rounded">
                      View Details
                    </button>
                    
                    {experiment.status === 'completed' && experiment.improvementOverBase && experiment.improvementOverBase > 0.1 && (
                      <button className="px-3 py-1 text-sm text-purple-600 hover:bg-purple-50 rounded">
                        Deploy Strategy
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Beaker className="w-8 h-8 text-blue-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Total Experiments</p>
              <p className="text-2xl font-semibold text-gray-900">{experiments.length}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-semibold text-gray-900">
                {experiments.filter(e => e.status === 'completed').length}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TrendingUp className="w-8 h-8 text-purple-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Deployed</p>
              <p className="text-2xl font-semibold text-gray-900">
                {experiments.filter(e => e.status === 'deployed').length}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Play className="w-8 h-8 text-blue-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Running</p>
              <p className="text-2xl font-semibold text-gray-900">
                {experiments.filter(e => e.status === 'running').length}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
```

## src/pages/Dashboard.tsx
```typescript
import React, { useState, useEffect } from 'react';
import { TrendingUp, TrendingDown, DollarSign, Target, Upload, MessageSquare } from 'lucide-react';
import { EquityCurve } from '../components/backtest/EquityCurve';
import { ChatWidget } from '../components/chat/ChatWidget';
import { apiService } from '../services/api';
import { useAuth } from '../hooks/useAuth';

export function Dashboard() {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    totalReturn: 0,
    totalBacktests: 0,
    winRate: 0,
    bestPerformer: null as any,
  });
  const [recentEquity, setRecentEquity] = useState([]);
  const [showChat, setShowChat] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      const [overview, metrics] = await Promise.all([
        apiService.getPortfolioOverview(),
        apiService.getPerformanceMetrics('1M')
      ]);
      
      setStats(overview);
      setRecentEquity(metrics.equityCurve || []);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    }
  };

  const StatCard = ({ title, value, change, icon: Icon, color }: any) => (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <Icon className={`w-8 h-8 ${color}`} />
        </div>
        <div className="ml-3 flex-1">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-semibold text-gray-900">{value}</p>
          {change !== undefined && (
            <div className="flex items-center mt-1">
              {change >= 0 ? (
                <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
              ) : (
                <TrendingDown className="w-4 h-4 text-red-500 mr-1" />
              )}
              <span className={`text-sm ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {change >= 0 ? '+' : ''}{change.toFixed(2)}%
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg shadow-lg p-6 text-white">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-2xl font-bold mb-2">
              Welcome back, {user?.fullName || user?.email?.split('@')[0]}!
            </h1>
            <p className="text-blue-100">
              Your AI-powered trading platform is ready. Let's analyze your strategies.
            </p>
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={() => setShowChat(!showChat)}
              className="px-4 py-2 bg-white/20 backdrop-blur-sm text-white rounded-lg hover:bg-white/30 flex items-center space-x-2"
            >
              <MessageSquare className="w-4 h-4" />
              <span>AI Assistant</span>
            </button>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Return"
          value={`${stats.totalReturn >= 0 ? '+' : ''}${stats.totalReturn.toFixed(2)}%`}
          change={stats.totalReturn}
          icon={DollarSign}
          color="text-green-600"
        />
        
        <StatCard
          title="Active Backtests"
          value={stats.totalBacktests}
          icon={Target}
          color="text-blue-600"
        />
        
        <StatCard
          title="Win Rate"
          value={`${stats.winRate.toFixed(1)}%`}
          icon={TrendingUp}
          color="text-purple-600"
        />
        
        <StatCard
          title="API Usage"
          value={`${user?.apiQuotaUsed || 0}/${user?.apiQuotaLimit || 100}`}
          change={user ? (user.apiQuotaUsed / user.apiQuotaLimit) * 100 : 0}
          icon={Upload}
          color="text-orange-600"
        />
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Equity Curve */}
        <div className="lg:col-span-2">
          {recentEquity.length > 0 ? (
            <EquityCurve data={recentEquity} initialBalance={100000} />
          ) : (
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Chart</h3>
              <div className="flex flex-col items-center justify-center h-64 text-gray-500">
                <TrendingUp className="w-12 h-12 mb-4" />
                <p className="text-lg font-medium mb-2">No data yet</p>
                <p className="text-sm text-center">
                  Upload trading data and run backtests to see your performance chart
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Quick Actions */}
        <div className="space-y-6">
          {/* Recent Activity */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div className="space-y-3">
              <button className="w-full text-left p-3 rounded-lg border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-colors">
                <div className="flex items-center space-x-3">
                  <Upload className="w-5 h-5 text-blue-600" />
                  <div>
                    <p className="font-medium text-gray-900">Upload Data</p>
                    <p className="text-sm text-gray-600">Import new trading data</p>
                  </div>
                </div>
              </button>
              
              <button className="w-full text-left p-3 rounded-lg border border-gray-200 hover:border-green-300 hover:bg-green-50 transition-colors">
                <div className="flex items-center space-x-3">
                  <Target className="w-5 h-5 text-green-600" />
                  <div>
                    <p className="font-medium text-gray-900">New Backtest</p>
                    <p className="text-sm text-gray-600">Test a trading strategy</p>
                  </div>
                </div>
              </button>
              
              <button className="w-full text-left p-3 rounded-lg border border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-colors">
                <div className="flex items-center space-x-3">
                  <TrendingUp className="w-5 h-5 text-purple-600" />
                  <div>
                    <p className="font-medium text-gray-900">DGM Experiment</p>
                    <p className="text-sm text-gray-600">Evolve strategies with AI</p>
                  </div>
                </div>
              </button>
            </div>
          </div>

          {/* Subscription Status */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Subscription</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Current Plan</span>
                <span className="text-sm font-medium text-gray-900 capitalize">
                  {user?.subscriptionTier || 'free'}
                </span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">API Usage</span>
                <span className="text-sm font-medium text-gray-900">
                  {user?.apiQuotaUsed || 0} / {user?.apiQuotaLimit || 100}
                </span>
              </div>
              
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full"
                  style={{
                    width: `${user ? (user.apiQuotaUsed / user.apiQuotaLimit) * 100 : 0}%`,
                  }}
                />
              </div>
              
              {user?.subscriptionTier === 'free' && (
                <button className="w-full mt-3 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm">
                  Upgrade Plan
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Chat Widget */}
      {showChat && (
        <div className="fixed bottom-4 right-4 w-96 h-96 z-50">
          <ChatWidget className="h-full" />
        </div>
      )}
    </div>
  );
}
```

## src/App.tsx
```typescript
import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from './hooks/useAuth';
import { Layout } from './components/layout/Layout';
import { LoadingSpinner } from './components/common/LoadingSpinner';

// Pages
import { Dashboard } from './pages/Dashboard';
import { Upload } from './pages/Upload';
import { Backtesting } from './pages/Backtesting';
import { Portfolio } from './pages/Portfolio';
import { DGMExperiments } from './pages/DGMExperiments';
import { Settings } from './pages/Settings';
import { Login } from './pages/Login';
import { Register } from './pages/Register';

function PrivateRoute({ children }: { children: React.ReactNode }) {
  const { user, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
}

function PublicRoute({ children }: { children: React.ReactNode }) {
  const { user, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (user) {
    return <Navigate to="/" replace />;
  }

  return <>{children}</>;
}

function AppRoutes() {
  return (
    <Routes>
      {/* Public routes */}
      <Route
        path="/login"
        element={
          <PublicRoute>
            <Login />
          </PublicRoute>
        }
      />
      <Route
        path="/register"
        element={
          <PublicRoute>
            <Register />
          </PublicRoute>
        }
      />

      {/* Private routes */}
      <Route
        path="/"
        element={
          <PrivateRoute>
            <Layout>
              <Dashboard />
            </Layout>
          </PrivateRoute>
        }
      />
      <Route
        path="/upload"
        element={
          <PrivateRoute>
            <Layout>
              <Upload />
            </Layout>
          </PrivateRoute>
        }
      />
      <Route
        path="/backtesting"
        element={
          <PrivateRoute>
            <Layout>
              <Backtesting />
            </Layout>
          </PrivateRoute>
        }
      />
      <Route
        path="/portfolio"
        element={
          <PrivateRoute>
            <Layout>
              <Portfolio />
            </Layout>
          </PrivateRoute>
        }
      />
      <Route
        path="/dgm"
        element={
          <PrivateRoute>
            <Layout>
              <DGMExperiments />
            </Layout>
          </PrivateRoute>
        }
      />
      <Route
        path="/settings"
        element={
          <PrivateRoute>
            <Layout>
              <Settings />
            </Layout>
          </PrivateRoute>
        }
      />

      {/* Catch all route */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
}

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="min-h-screen bg-gray-50">
          <AppRoutes />
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
```

## src/components/layout/Layout.tsx
```typescript
import React, { useState } from 'react';
import { Header } from './Header';
import { Sidebar } from './Sidebar';

interface LayoutProps {
  children: React.ReactNode;
}

export function Layout({ children }: LayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <div className="min-h-screen bg-gray-50">
      <Sidebar open={sidebarOpen} onClose={() => setSidebarOpen(false)} />
      
      <div className="lg:pl-64">
        <Header onMenuClick={() => setSidebarOpen(true)} />
        
        <main className="px-4 sm:px-6 lg:px-8 py-8">
          {children}
        </main>
      </div>
    </div>
  );
}
```

## vite.config.ts
```typescript
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
      },
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
  },
});
```

## tailwind.config.js
```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
        },
      },
    },
  },
  plugins: [],
}
```
