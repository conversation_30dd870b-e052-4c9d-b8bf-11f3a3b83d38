"""
Tests for Dependency Injection System

Comprehensive tests demonstrating the benefits of dependency injection
for testability and maintainability.
"""

import pytest
import asyncio
from datetime import datetime
from typing import Dict, Any

from core.dependency_injection import DependencyContainer, inject
from core.interfaces import (
    IMarketDataService, IStrategyService, ITradingService,
    ILoggingService, IConfigurationService, TradingSignal, Order
)
from core.trading_engine import TradingEngine
from core.service_configuration import ServiceConfigurator, ServiceMode
from services.mock_services import MockServiceCollection

class TestDependencyInjectionContainer:
    """Test the dependency injection container"""
    
    def setup_method(self):
        """Setup before each test"""
        self.container = DependencyContainer()
    
    def test_register_and_resolve_service(self):
        """Test basic service registration and resolution"""
        from services.configuration_service import MockConfigurationService
        
        # Register service
        self.container.register(IConfigurationService, MockConfigurationService)
        
        # Resolve service
        config_service = self.container.resolve(IConfigurationService)
        
        assert isinstance(config_service, MockConfigurationService)
        assert config_service.get_config('test.key', 'default') == 'default'
    
    def test_singleton_behavior(self):
        """Test singleton service behavior"""
        from services.configuration_service import MockConfigurationService
        
        # Register as singleton
        self.container.register(IConfigurationService, MockConfigurationService, singleton=True)
        
        # Resolve multiple times
        service1 = self.container.resolve(IConfigurationService)
        service2 = self.container.resolve(IConfigurationService)
        
        # Should be the same instance
        assert service1 is service2
    
    def test_non_singleton_behavior(self):
        """Test non-singleton service behavior"""
        from services.configuration_service import MockConfigurationService
        
        # Register as non-singleton
        self.container.register(IConfigurationService, MockConfigurationService, singleton=False)
        
        # Resolve multiple times
        service1 = self.container.resolve(IConfigurationService)
        service2 = self.container.resolve(IConfigurationService)
        
        # Should be different instances
        assert service1 is not service2
    
    def test_register_instance(self):
        """Test registering service instances"""
        from services.configuration_service import MockConfigurationService
        
        # Create instance
        config_instance = MockConfigurationService({'test': {'value': 42}})
        
        # Register instance
        self.container.register_instance(IConfigurationService, config_instance)
        
        # Resolve service
        resolved_service = self.container.resolve(IConfigurationService)
        
        assert resolved_service is config_instance
        assert resolved_service.get_config('test.value') == 42
    
    def test_dependency_injection_in_constructor(self):
        """Test automatic dependency injection in constructors"""
        from services.configuration_service import MockConfigurationService
        from services.logging_service import MockLoggingService
        
        # Register dependencies
        self.container.register(IConfigurationService, MockConfigurationService)
        self.container.register(ILoggingService, MockLoggingService)
        
        # Resolve service that depends on others
        logging_service = self.container.resolve(ILoggingService)
        
        assert isinstance(logging_service, MockLoggingService)
        assert hasattr(logging_service, 'config')
    
    def test_missing_dependency_error(self):
        """Test error when dependency is not registered"""
        from services.logging_service import MockLoggingService
        
        # Register service without its dependencies
        self.container.register(ILoggingService, MockLoggingService)
        
        # Should raise error when resolving
        with pytest.raises(ValueError, match="Cannot resolve dependency"):
            self.container.resolve(ILoggingService)
    
    def test_inject_decorator(self):
        """Test the @inject decorator"""
        from services.configuration_service import MockConfigurationService
        
        # Register service
        config_instance = MockConfigurationService({'test': {'value': 'injected'}})
        self.container.register_instance(IConfigurationService, config_instance)
        
        @inject
        def test_function(config: IConfigurationService):
            return config.get_config('test.value')
        
        # Function should receive injected dependency
        result = test_function()
        assert result == 'injected'

class TestServiceConfiguration:
    """Test service configuration for different modes"""
    
    def setup_method(self):
        """Setup before each test"""
        self.configurator = ServiceConfigurator()
    
    def test_configure_for_testing(self):
        """Test testing mode configuration"""
        container = self.configurator.configure_for_testing()
        
        # Validate all services are registered
        validation = self.configurator.validate_configuration()
        assert validation['is_valid']
        assert len(validation['missing_services']) == 0
        
        # Verify mock services are used
        market_data = container.resolve(IMarketDataService)
        assert 'Mock' in market_data.__class__.__name__
    
    def test_configure_for_development(self):
        """Test development mode configuration"""
        container = self.configurator.configure_for_development()
        
        # Validate configuration
        validation = self.configurator.validate_configuration()
        assert validation['is_valid']
        assert validation['mode'] == 'development'
    
    def test_configure_for_backtesting(self):
        """Test backtesting mode configuration"""
        container = self.configurator.configure_for_backtesting()
        
        # Validate configuration
        validation = self.configurator.validate_configuration()
        assert validation['is_valid']
        
        # Verify backtesting-specific settings
        config = container.resolve(IConfigurationService)
        assert not config.get_config('engine.auto_trading_enabled')
        assert not config.get_config('engine.notification_enabled')

class TestTradingEngineWithDI:
    """Test trading engine with dependency injection"""
    
    def setup_method(self):
        """Setup before each test"""
        # Configure services for testing
        configurator = ServiceConfigurator()
        self.container = configurator.configure_for_testing()
        
        # Create mock service collection for easy access
        self.mocks = MockServiceCollection()
        
        # Override container services with our mock collection
        self.container.register_instance(IMarketDataService, self.mocks.market_data)
        self.container.register_instance(IStrategyService, self.mocks.strategy)
        self.container.register_instance(ITradingService, self.mocks.trading)
        
        # Create trading engine with injected dependencies
        self.engine = self.container.resolve(TradingEngine)
    
    def test_engine_initialization_with_di(self):
        """Test that engine initializes with injected dependencies"""
        assert self.engine is not None
        assert self.engine.market_data is self.mocks.market_data
        assert self.engine.strategy is self.mocks.strategy
        assert self.engine.trading is self.mocks.trading
    
    @pytest.mark.asyncio
    async def test_add_strategy_with_mocked_dependencies(self):
        """Test adding strategy with mocked dependencies"""
        # Configure mock strategy validation
        self.mocks.strategy.set_mock_validation(
            "test_strategy",
            {'is_valid': True, 'strategy_type': 'momentum'}
        )
        
        # Add strategy
        await self.engine.add_strategy(
            "test_strategy",
            "def trading_strategy(data, params): return {'signal': 'buy', 'confidence': 0.8}",
            ["AAPL"],
            {"test_param": "value"}
        )
        
        # Verify strategy was added
        status = self.engine.get_engine_status()
        assert "test_strategy" in status['strategies']
        assert status['strategies']['test_strategy']['symbols'] == ["AAPL"]
    
    @pytest.mark.asyncio
    async def test_signal_processing_with_mocked_services(self):
        """Test signal processing with mocked services"""
        # Configure mock services
        self.mocks.market_data.set_mock_price("AAPL", 150.0)
        self.mocks.strategy.set_mock_signal(
            "test_strategy",
            TradingSignal(
                symbol="AAPL",
                signal="buy",
                confidence=0.8,
                timestamp=datetime.now(),
                strategy_name="test_strategy",
                metadata={}
            )
        )
        
        # Configure for auto-trading
        self.mocks.config.set_config('engine.auto_trading_enabled', True)
        
        # Reload engine configuration
        self.engine._engine_config = self.engine._load_engine_config()
        
        # Process a signal directly
        signal = TradingSignal(
            symbol="AAPL",
            signal="buy",
            confidence=0.8,
            timestamp=datetime.now(),
            strategy_name="test_strategy",
            metadata={}
        )
        
        await self.engine._process_signal("test_strategy", signal)
        
        # Verify order was placed
        orders = self.mocks.trading.get_orders()
        assert len(orders) > 0
        
        # Verify signal was stored
        stored_signals = self.mocks.data_storage.get_stored_signals()
        assert len(stored_signals) > 0
        assert stored_signals[0].symbol == "AAPL"
    
    @pytest.mark.asyncio
    async def test_risk_management_integration(self):
        """Test risk management integration through DI"""
        # Configure risk management to reject orders
        self.mocks.risk_management.set_order_rejection(True)
        self.mocks.config.set_config('engine.auto_trading_enabled', True)
        
        # Reload engine configuration
        self.engine._engine_config = self.engine._load_engine_config()
        
        # Process a signal
        signal = TradingSignal(
            symbol="AAPL",
            signal="buy",
            confidence=0.8,
            timestamp=datetime.now(),
            strategy_name="test_strategy",
            metadata={}
        )
        
        await self.engine._process_signal("test_strategy", signal)
        
        # Verify no order was placed due to risk rejection
        orders = self.mocks.trading.get_orders()
        assert len(orders) == 0
        
        # But signal should still be stored
        stored_signals = self.mocks.data_storage.get_stored_signals()
        assert len(stored_signals) > 0

class TestDependencyInjectionBenefits:
    """Demonstrate the benefits of dependency injection"""
    
    def test_easy_mocking_for_unit_tests(self):
        """Demonstrate how DI makes unit testing easier"""
        # Before DI: Hard to test because of hard-coded dependencies
        # class TradingEngine:
        #     def __init__(self):
        #         self.market_data = YFinanceMarketDataService()  # Hard to mock
        
        # After DI: Easy to test with mock dependencies
        container = DependencyContainer()
        
        # Register mock services
        from services.mock_services import MockServiceCollection
        mocks = MockServiceCollection()
        
        container.register_instance(IMarketDataService, mocks.market_data)
        container.register_instance(IStrategyService, mocks.strategy)
        container.register_instance(ITradingService, mocks.trading)
        
        # Now we can easily test with controlled mock behavior
        mocks.market_data.set_mock_price("AAPL", 100.0)
        
        market_data = container.resolve(IMarketDataService)
        price = asyncio.run(market_data.get_current_price("AAPL"))
        
        assert price == 100.0
    
    def test_configuration_flexibility(self):
        """Demonstrate configuration flexibility with DI"""
        # Different configurations for different environments
        
        # Testing configuration
        test_configurator = ServiceConfigurator()
        test_container = test_configurator.configure_for_testing()
        
        market_data_test = test_container.resolve(IMarketDataService)
        assert 'Mock' in market_data_test.__class__.__name__
        
        # Development configuration
        dev_configurator = ServiceConfigurator()
        dev_container = dev_configurator.configure_for_development()
        
        market_data_dev = dev_container.resolve(IMarketDataService)
        # In development, we might use real market data but mock trading
        
        # Same interface, different implementations based on environment
        assert hasattr(market_data_test, 'get_current_price')
        assert hasattr(market_data_dev, 'get_current_price')
    
    def test_loose_coupling_demonstration(self):
        """Demonstrate loose coupling benefits"""
        container = DependencyContainer()
        
        # We can swap implementations without changing dependent code
        from services.configuration_service import MockConfigurationService
        from services.logging_service import MockLoggingService
        
        # Register one implementation
        config1 = MockConfigurationService({'mode': 'test1'})
        container.register_instance(IConfigurationService, config1)
        container.register(ILoggingService, MockLoggingService)
        
        logging1 = container.resolve(ILoggingService)
        assert logging1.config.get_config('mode') == 'test1'
        
        # Swap to different implementation
        config2 = MockConfigurationService({'mode': 'test2'})
        container.register_instance(IConfigurationService, config2)
        
        # Clear singleton cache to get new instance
        container.clear()
        container.register_instance(IConfigurationService, config2)
        container.register(ILoggingService, MockLoggingService)
        
        logging2 = container.resolve(ILoggingService)
        assert logging2.config.get_config('mode') == 'test2'
        
        # Same interface, different behavior - loose coupling achieved!

class TestRealWorldDIScenarios:
    """Test real-world dependency injection scenarios"""
    
    def setup_method(self):
        """Setup for real-world scenarios"""
        self.configurator = ServiceConfigurator()
    
    def test_integration_testing_scenario(self):
        """Test integration testing with selective mocking"""
        container = DependencyContainer()
        
        # Use real strategy service but mock everything else
        from services.strategy_service import DarwinGodelStrategyService
        from services.mock_services import MockServiceCollection
        
        mocks = MockServiceCollection()
        
        # Real strategy service
        container.register_instance(IConfigurationService, mocks.config)
        container.register_instance(ILoggingService, mocks.logging)
        container.register(IStrategyService, DarwinGodelStrategyService)
        
        # Mock other services
        container.register_instance(IMarketDataService, mocks.market_data)
        container.register_instance(ITradingService, mocks.trading)
        
        # Now we can test real strategy execution with controlled inputs
        strategy_service = container.resolve(IStrategyService)
        
        # This uses the real Darwin-Godel verification system
        validation = asyncio.run(strategy_service.validate_strategy(
            "def trading_strategy(data, params): return {'signal': 'hold', 'confidence': 0.5}"
        ))
        
        assert validation['is_valid']
        assert 'strategy_type' in validation
    
    def test_a_b_testing_scenario(self):
        """Test A/B testing different service implementations"""
        # Scenario: Test two different risk management strategies
        
        from services.mock_services import MockRiskManagementService
        
        # Configuration A: Conservative risk management
        container_a = DependencyContainer()
        risk_a = MockRiskManagementService()
        risk_a.set_max_position_size(50)  # Conservative
        risk_a.set_position_size_multiplier(0.5)
        container_a.register_instance(IRiskManagementService, risk_a)
        
        # Configuration B: Aggressive risk management
        container_b = DependencyContainer()
        risk_b = MockRiskManagementService()
        risk_b.set_max_position_size(200)  # Aggressive
        risk_b.set_position_size_multiplier(1.5)
        container_b.register_instance(IRiskManagementService, risk_b)
        
        # Test both configurations
        signal = TradingSignal(
            symbol="AAPL",
            signal="buy",
            confidence=0.8,
            timestamp=datetime.now(),
            strategy_name="test",
            metadata={}
        )
        
        size_a = asyncio.run(risk_a.calculate_position_size("AAPL", signal, 10000))
        size_b = asyncio.run(risk_b.calculate_position_size("AAPL", signal, 10000))
        
        # Conservative should suggest smaller position
        assert size_a < size_b
        
        # Same interface, different behavior - perfect for A/B testing!
    
    def test_environment_specific_configuration(self):
        """Test environment-specific service configuration"""
        # Production: Real services with error handling
        # Development: Mix of real and mock services
        # Testing: All mock services
        
        configs = {}
        
        # Testing environment
        test_container = self.configurator.configure_for_testing()
        configs['testing'] = self.configurator.validate_configuration()
        
        # Development environment
        dev_container = self.configurator.configure_for_development()
        configs['development'] = self.configurator.validate_configuration()
        
        # All environments should be valid
        for env, config in configs.items():
            assert config['is_valid'], f"{env} configuration is invalid"
            assert len(config['missing_services']) == 0, f"{env} has missing services"
        
        # But they should use different service implementations
        test_market_data = test_container.resolve(IMarketDataService)
        dev_market_data = dev_container.resolve(IMarketDataService)
        
        # Testing uses mock, development might use real Yahoo Finance
        assert 'Mock' in test_market_data.__class__.__name__
        # Development configuration varies based on implementation