// test-demo.js - Simple test script for the demo
const http = require('http');

function testEndpoint(path, description) {
  return new Promise((resolve) => {
    const req = http.get(`http://localhost:3001${path}`, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        console.log(`✅ ${description}: ${res.statusCode}`);
        if (res.statusCode === 200) {
          try {
            const json = JSON.parse(data);
            console.log(`   Response: ${JSON.stringify(json, null, 2).substring(0, 100)}...`);
          } catch (e) {
            console.log(`   Response: ${data.substring(0, 100)}...`);
          }
        }
        resolve();
      });
    });
    
    req.on('error', (err) => {
      console.log(`❌ ${description}: ${err.message}`);
      resolve();
    });
    
    req.setTimeout(5000, () => {
      console.log(`⏰ ${description}: Timeout`);
      req.destroy();
      resolve();
    });
  });
}

async function runTests() {
  console.log('🧪 Testing Forex Demo API...\n');
  
  await testEndpoint('/health', 'Health Check');
  await testEndpoint('/api/forex/EUR/USD', 'EUR/USD Price');
  await testEndpoint('/api/forex/portfolio', 'Portfolio Data');
  await testEndpoint('/metrics', 'Metrics Endpoint');
  
  console.log('\n✨ Demo API tests completed!');
  console.log('🌐 Open http://localhost:3000 to view the UI');
  console.log('🔌 WebSocket available at ws://localhost:8080');
}

runTests();