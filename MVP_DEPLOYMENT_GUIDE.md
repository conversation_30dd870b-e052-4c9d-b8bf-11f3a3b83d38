# AI Enhanced Trading Platform - MVP Deployment Guide

This guide provides instructions for deploying the MVP version of the AI Enhanced Trading Platform to various environments.

## Local Deployment

### Prerequisites

- Python 3.13.2 or later
- Node.js and npm (for the frontend)
- MetaTrader 5 (optional, for live trading)

### Steps

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/ai-enhanced-trading-platform.git
   cd ai-enhanced-trading-platform
   ```

2. Run the MVP setup script:
   ```bash
   python mvp_setup_simple.py
   ```

3. Start the backend server:
   ```bash
   python backend/minimal_server.py
   ```

4. In a new terminal, start the frontend:
   ```bash
   cd frontend
   npm install
   npm run dev:mvp
   ```

5. Access the platform at `http://localhost:5173`

## Docker Deployment

### Prerequisites

- Docker
- Docker Compose

### Steps

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/ai-enhanced-trading-platform.git
   cd ai-enhanced-trading-platform
   ```

2. Build and start the containers:
   ```bash
   docker-compose up -d
   ```

3. Access the platform at `http://localhost:5173`

### Docker Configuration

The MVP includes a `docker-compose.yml` file with the following services:

- **backend**: FastAPI server running on port 8000
- **frontend**: React application running on port 5173

## Production Deployment

### Prerequisites

- A server with Python 3.13.2+ and Node.js
- Nginx or another reverse proxy
- SSL certificate for HTTPS

### Backend Deployment

1. Clone the repository on your server:
   ```bash
   git clone https://github.com/yourusername/ai-enhanced-trading-platform.git
   cd ai-enhanced-trading-platform
   ```

2. Set up a Python virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements-mvp.txt
   ```

4. Create a systemd service file (Linux) or Windows service:
   ```ini
   [Unit]
   Description=AI Trading Platform Backend
   After=network.target

   [Service]
   User=username
   WorkingDirectory=/path/to/ai-enhanced-trading-platform
   ExecStart=/path/to/ai-enhanced-trading-platform/venv/bin/python backend/minimal_server.py
   Restart=always

   [Install]
   WantedBy=multi-user.target
   ```

5. Start the service:
   ```bash
   sudo systemctl enable trading-platform-backend
   sudo systemctl start trading-platform-backend
   ```

### Frontend Deployment

1. Build the frontend:
   ```bash
   cd frontend
   npm install
   npm run build:mvp
   ```

2. Configure Nginx to serve the frontend and proxy API requests:
   ```nginx
   server {
       listen 80;
       server_name yourdomain.com;
       
       # Redirect to HTTPS
       return 301 https://$host$request_uri;
   }

   server {
       listen 443 ssl;
       server_name yourdomain.com;
       
       ssl_certificate /path/to/cert.pem;
       ssl_certificate_key /path/to/key.pem;
       
       # Serve frontend
       location / {
           root /path/to/ai-enhanced-trading-platform/frontend/dist;
           try_files $uri $uri/ /index.html;
       }
       
       # Proxy API requests
       location /api {
           proxy_pass http://localhost:8000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }
   ```

3. Restart Nginx:
   ```bash
   sudo systemctl restart nginx
   ```

## Cloud Deployment

### AWS Deployment

1. Create an EC2 instance with Ubuntu
2. Install dependencies:
   ```bash
   sudo apt update
   sudo apt install -y python3-pip python3-venv nodejs npm nginx
   ```

3. Follow the Production Deployment steps above

### Azure Deployment

1. Create an Azure App Service with Python runtime
2. Deploy the backend using Azure CLI:
   ```bash
   az webapp up --runtime "PYTHON|3.13" --sku B1 --name your-app-name
   ```

3. Deploy the frontend to Azure Static Web Apps

## Monitoring and Maintenance

### Logging

- Backend logs are stored in the `logs` directory
- Use a tool like Prometheus or Grafana for monitoring

### Backups

- Regularly backup the database (SQLite file or PostgreSQL)
- Backup configuration files

### Updates

1. Pull the latest code:
   ```bash
   git pull origin main
   ```

2. Run the setup script:
   ```bash
   python mvp_setup_simple.py
   ```

3. Restart services:
   ```bash
   sudo systemctl restart trading-platform-backend
   ```

## Security Considerations

1. Use HTTPS for all communications
2. Implement proper authentication for the API
3. Keep dependencies updated
4. Use environment variables for sensitive configuration

## Troubleshooting

### Common Deployment Issues

1. **Port conflicts**: Check if ports 8000 and 5173 are already in use
2. **Permission issues**: Ensure proper file permissions
3. **CORS errors**: Verify CORS configuration in the backend

### Checking Logs

- Backend logs: `logs/mvp_trading.log`
- Nginx logs: `/var/log/nginx/error.log`
- System logs: `journalctl -u trading-platform-backend`

## Support

For deployment assistance, please contact the development team or open an issue on GitHub.