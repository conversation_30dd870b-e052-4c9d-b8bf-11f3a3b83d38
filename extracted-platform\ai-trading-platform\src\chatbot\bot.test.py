
from chatbot.bot import TradingBot
from knowledge.graph import KnowledgeGraph

def test_chatbot_answers_with_graph():
    kg = KnowledgeGraph()
    kg.add_concept("RSI")
    kg.add_concept("MeanReversion")
    kg.add_relation("RSI", "MeanReversion", "used_by")
    bot = TradingBot(kg)
    response = bot.chat("Tell me about RSI")
    assert "RSI" in response and "MeanReversion" in response
    response2 = bot.chat("Unrelated question")
    assert "don't know" in response2
