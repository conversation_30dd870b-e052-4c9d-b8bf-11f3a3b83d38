"""
Strategy Chatbot - Main Integration
Combines natural language processing, code generation, and validation
"""

import time
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from .models import (
    ChatbotResponse, ConversationContext, StrategyRequest, 
    GeneratedStrategy, StrategyModification, CodeExplanation
)
from .requirements_parser import RequirementsParser
from .strategy_generator import StrategyGenerator
from .code_validator import CodeValidator
from .strategy_templates import StrategyTemplateManager


class StrategyChatbot:
    """Main chatbot for trading strategy generation and assistance"""
    
    def __init__(self, 
                 parser: Optional[RequirementsParser] = None,
                 generator: Optional[StrategyGenerator] = None,
                 validator: Optional[CodeValidator] = None,
                 template_manager: Optional[StrategyTemplateManager] = None):
        
        self.parser = parser or RequirementsParser()
        self.generator = generator or StrategyGenerator()
        self.validator = validator or CodeValidator()
        self.template_manager = template_manager or StrategyTemplateManager()
        
        # Conversation management
        self.active_contexts: Dict[str, ConversationContext] = {}
        
        # Logging
        self.logger = logging.getLogger(__name__)
        
        # Response templates
        self.response_templates = {
            'greeting': "Hello! I'm your trading strategy assistant. I can help you create Python trading strategies from natural language descriptions. What kind of strategy would you like to build?",
            'clarification': "I need more information to create your strategy. Could you please specify:",
            'success': "Great! I've generated your trading strategy. Here's what I created:",
            'error': "I encountered an issue while processing your request:",
            'suggestion': "Here are some suggestions to improve your request:"
        }
    
    def generate_strategy_from_text(self, 
                                  user_input: str, 
                                  user_id: str = "default",
                                  session_id: Optional[str] = None) -> ChatbotResponse:
        """Generate a complete trading strategy from natural language input"""
        
        start_time = time.time()
        session_id = session_id or f"session_{int(time.time())}"
        
        try:
            # Get or create conversation context
            context = self._get_or_create_context(user_id, session_id)
            context.conversation_history.append({
                "timestamp": datetime.now().isoformat(),
                "user": user_input,
                "type": "strategy_request"
            })
            
            # Parse requirements
            try:
                parsed_request = self.parser.parse(user_input)
                context.current_request = parsed_request
                
                self.logger.info(f"Parsed strategy request: {parsed_request.strategy_type}")
                
            except ValueError as e:
                return self._create_error_response(
                    str(e),
                    suggestions=self.parser.get_parsing_suggestions(str(e)),
                    processing_time=time.time() - start_time
                )
            
            # Generate strategy code
            try:
                generated_strategy = self.generator.generate(parsed_request)
                context.current_strategy = generated_strategy
                
                self.logger.info(f"Generated strategy: {generated_strategy.strategy_name}")
                
            except Exception as e:
                return self._create_error_response(
                    f"Code generation failed: {str(e)}",
                    suggestions=["Try simplifying your requirements", "Check if all specified indicators are supported"],
                    processing_time=time.time() - start_time
                )
            
            # Validate generated code
            try:
                validation_result = self.validator.validate_all(generated_strategy.code)
                
                if not validation_result.is_valid:
                    return self._create_error_response(
                        "Generated code failed validation",
                        suggestions=self.validator.suggest_improvements(generated_strategy.code, validation_result),
                        processing_time=time.time() - start_time,
                        validation_errors=validation_result.errors,
                        validation_warnings=validation_result.warnings
                    )
                
                self.logger.info("Code validation passed")
                
            except Exception as e:
                return self._create_error_response(
                    f"Code validation failed: {str(e)}",
                    processing_time=time.time() - start_time
                )
            
            # Create success response
            response = ChatbotResponse(
                success=True,
                message=self._create_success_message(generated_strategy, parsed_request),
                generated_code=generated_strategy.code,
                strategy_name=generated_strategy.strategy_name,
                test_cases=generated_strategy.test_cases,
                validation_warnings=validation_result.warnings,
                processing_time=time.time() - start_time,
                confidence_score=self._calculate_confidence_score(parsed_request, validation_result)
            )
            
            # Update conversation history
            context.conversation_history.append({
                "timestamp": datetime.now().isoformat(),
                "assistant": response.message,
                "type": "strategy_generated",
                "strategy_name": generated_strategy.strategy_name
            })
            
            return response
            
        except Exception as e:
            self.logger.error(f"Unexpected error in strategy generation: {str(e)}")
            return self._create_error_response(
                f"An unexpected error occurred: {str(e)}",
                suggestions=["Please try again with a simpler request", "Contact support if the issue persists"],
                processing_time=time.time() - start_time
            )
    
    def explain_code(self, 
                    code: str, 
                    user_id: str = "default",
                    session_id: Optional[str] = None) -> str:
        """Explain trading strategy code in natural language"""
        
        try:
            # Basic code analysis
            explanation_parts = []
            
            # Identify strategy type
            if "MeanReversion" in code:
                explanation_parts.append("This is a **mean reversion strategy** that looks for prices to return to their average value.")
            elif "Momentum" in code:
                explanation_parts.append("This is a **momentum strategy** that follows price trends and directional movements.")
            elif "ML" in code or "RandomForest" in code:
                explanation_parts.append("This is a **machine learning strategy** that uses AI models to predict price movements.")
            
            # Identify indicators
            indicators = []
            if "calculate_rsi" in code:
                indicators.append("RSI (Relative Strength Index) to identify overbought/oversold conditions")
            if "calculate_macd" in code:
                indicators.append("MACD (Moving Average Convergence Divergence) for trend analysis")
            if "calculate_sma" in code:
                indicators.append("SMA (Simple Moving Average) for trend identification")
            if "calculate_bollinger_bands" in code:
                indicators.append("Bollinger Bands for volatility and mean reversion signals")
            
            if indicators:
                explanation_parts.append(f"**Technical Indicators Used:**\n" + "\n".join(f"- {ind}" for ind in indicators))
            
            # Identify signal logic
            if "rsi < 30" in code or "oversold_level" in code:
                explanation_parts.append("**Buy signals** are generated when RSI indicates oversold conditions (typically below 30).")
            if "rsi > 70" in code or "overbought_level" in code:
                explanation_parts.append("**Sell signals** are generated when RSI indicates overbought conditions (typically above 70).")
            
            if "macd_line > signal_line" in code:
                explanation_parts.append("**Buy signals** occur when MACD line crosses above the signal line (bullish crossover).")
            if "macd_line < signal_line" in code:
                explanation_parts.append("**Sell signals** occur when MACD line crosses below the signal line (bearish crossover).")
            
            # Risk management
            if "risk_per_trade" in code:
                explanation_parts.append("The strategy includes **risk management** with configurable risk per trade limits.")
            if "max_positions" in code:
                explanation_parts.append("**Position limits** prevent overexposure by limiting the number of concurrent trades.")
            
            # Machine learning specifics
            if "train_model" in code:
                explanation_parts.append("The strategy includes **model training** capabilities that learn from historical data.")
            if "predict_signal" in code:
                explanation_parts.append("**AI predictions** are used to generate trading signals based on learned patterns.")
            
            explanation = "\n\n".join(explanation_parts)
            
            if not explanation:
                explanation = "This appears to be a custom trading strategy. The code defines methods for signal generation and risk management."
            
            return explanation
            
        except Exception as e:
            self.logger.error(f"Code explanation error: {str(e)}")
            return f"I encountered an error while analyzing the code: {str(e)}"
    
    def modify_strategy(self, 
                       modification_request: str,
                       user_id: str = "default",
                       session_id: Optional[str] = None) -> ChatbotResponse:
        """Modify an existing strategy based on user request"""
        
        start_time = time.time()
        
        try:
            context = self._get_context(user_id, session_id)
            if not context or not context.current_strategy:
                return self._create_error_response(
                    "No active strategy found to modify. Please generate a strategy first.",
                    suggestions=["Create a new strategy using natural language description"],
                    processing_time=time.time() - start_time
                )
            
            # Parse modification request
            modification = self._parse_modification_request(modification_request, context.current_strategy)
            
            # Apply modifications
            modified_code = self._apply_modifications(context.current_strategy.code, modification)
            
            # Validate modified code
            validation_result = self.validator.validate_all(modified_code)
            
            if not validation_result.is_valid:
                return self._create_error_response(
                    "Modified code failed validation",
                    suggestions=self.validator.suggest_improvements(modified_code, validation_result),
                    processing_time=time.time() - start_time,
                    validation_errors=validation_result.errors
                )
            
            # Update current strategy
            context.current_strategy.code = modified_code
            context.current_strategy.version = f"{context.current_strategy.version}.1"
            
            return ChatbotResponse(
                success=True,
                message=f"Successfully modified your strategy: {modification.modification_type}",
                generated_code=modified_code,
                strategy_name=context.current_strategy.strategy_name,
                validation_warnings=validation_result.warnings,
                processing_time=time.time() - start_time,
                confidence_score=0.8
            )
            
        except Exception as e:
            return self._create_error_response(
                f"Strategy modification failed: {str(e)}",
                processing_time=time.time() - start_time
            )
    
    def get_strategy_suggestions(self, 
                               market_conditions: str = "",
                               experience_level: str = "intermediate") -> List[str]:
        """Get strategy suggestions based on market conditions and user experience"""
        
        suggestions = []
        
        # Base suggestions by experience level
        if experience_level == "beginner":
            suggestions.extend([
                "Start with a simple moving average crossover strategy",
                "Try a basic RSI mean reversion strategy",
                "Consider a momentum strategy using MACD"
            ])
        elif experience_level == "advanced":
            suggestions.extend([
                "Explore machine learning strategies with multiple features",
                "Try pairs trading or statistical arbitrage",
                "Consider multi-timeframe analysis strategies"
            ])
        else:  # intermediate
            suggestions.extend([
                "Combine multiple indicators for better signal quality",
                "Add dynamic position sizing based on volatility",
                "Implement stop-loss and take-profit levels"
            ])
        
        # Market condition specific suggestions
        if "volatile" in market_conditions.lower():
            suggestions.extend([
                "Use Bollinger Bands for volatility-based signals",
                "Implement tighter risk management",
                "Consider breakout strategies"
            ])
        elif "trending" in market_conditions.lower():
            suggestions.extend([
                "Focus on momentum strategies",
                "Use trend-following indicators like MACD",
                "Avoid mean reversion in strong trends"
            ])
        elif "sideways" in market_conditions.lower() or "ranging" in market_conditions.lower():
            suggestions.extend([
                "Use mean reversion strategies",
                "Focus on RSI and Stochastic indicators",
                "Avoid momentum strategies in ranging markets"
            ])
        
        return suggestions[:5]  # Return top 5 suggestions
    
    def _get_or_create_context(self, user_id: str, session_id: str) -> ConversationContext:
        """Get existing context or create new one"""
        context_key = f"{user_id}_{session_id}"
        
        if context_key not in self.active_contexts:
            self.active_contexts[context_key] = ConversationContext(
                user_id=user_id,
                session_id=session_id
            )
        
        # Update last activity
        self.active_contexts[context_key].last_activity = datetime.now()
        
        return self.active_contexts[context_key]
    
    def _get_context(self, user_id: str, session_id: Optional[str]) -> Optional[ConversationContext]:
        """Get existing context"""
        if not session_id:
            return None
        
        context_key = f"{user_id}_{session_id}"
        return self.active_contexts.get(context_key)
    
    def _create_error_response(self, 
                             error_message: str,
                             suggestions: Optional[List[str]] = None,
                             processing_time: float = 0.0,
                             validation_errors: Optional[List[str]] = None,
                             validation_warnings: Optional[List[str]] = None) -> ChatbotResponse:
        """Create standardized error response"""
        
        return ChatbotResponse(
            success=False,
            message=f"{self.response_templates['error']} {error_message}",
            error_message=error_message,
            suggestions=suggestions or [],
            validation_errors=validation_errors or [],
            validation_warnings=validation_warnings or [],
            processing_time=processing_time,
            confidence_score=0.0
        )
    
    def _create_success_message(self, strategy: GeneratedStrategy, request: StrategyRequest) -> str:
        """Create success message for generated strategy"""
        
        message_parts = [
            f"✅ **{strategy.strategy_name}** has been generated successfully!",
            "",
            "**Strategy Details:**",
            f"- Type: {request.strategy_type.replace('_', ' ').title()}",
            f"- Symbols: {', '.join(request.symbols)}",
            f"- Timeframe: {request.timeframe or '1H'}",
        ]
        
        if request.indicators:
            message_parts.append(f"- Indicators: {', '.join(ind.upper() for ind in request.indicators)}")
        
        message_parts.extend([
            f"- Risk per Trade: {request.risk_per_trade * 100:.1f}%",
            f"- Max Positions: {request.max_positions}",
            "",
            "**Next Steps:**",
            "1. Review the generated code below",
            "2. Run the included test cases",
            "3. Backtest with historical data",
            "4. Deploy to your trading environment",
            "",
            "You can ask me to explain any part of the code or make modifications!"
        ])
        
        return "\n".join(message_parts)
    
    def _calculate_confidence_score(self, request: StrategyRequest, validation_result) -> float:
        """Calculate confidence score for generated strategy"""
        
        base_score = 0.7
        
        # Boost confidence for well-defined requests
        if request.indicators:
            base_score += 0.1
        
        if request.timeframe:
            base_score += 0.05
        
        if request.stop_loss_pips or request.take_profit_pips:
            base_score += 0.05
        
        # Reduce confidence for validation issues
        if validation_result.warnings:
            base_score -= len(validation_result.warnings) * 0.02
        
        # Boost for common strategy types
        if request.strategy_type in ["mean_reversion", "momentum"]:
            base_score += 0.05
        
        return min(max(base_score, 0.0), 1.0)
    
    def _parse_modification_request(self, request: str, current_strategy: GeneratedStrategy) -> StrategyModification:
        """Parse modification request"""
        
        request_lower = request.lower()
        
        # Determine modification type
        if "add" in request_lower and ("indicator" in request_lower or "rsi" in request_lower or "macd" in request_lower):
            mod_type = "add_indicator"
        elif "change" in request_lower and "risk" in request_lower:
            mod_type = "change_risk"
        elif "optimize" in request_lower or "improve" in request_lower:
            mod_type = "optimize"
        elif "remove" in request_lower:
            mod_type = "remove_feature"
        else:
            mod_type = "general_modification"
        
        return StrategyModification(
            original_code=current_strategy.code,
            modification_request=request,
            modification_type=mod_type
        )
    
    def _apply_modifications(self, original_code: str, modification: StrategyModification) -> str:
        """Apply modifications to strategy code"""
        
        # This is a simplified implementation
        # In practice, you'd use more sophisticated code modification techniques
        
        modified_code = original_code
        
        if modification.modification_type == "add_indicator":
            # Add new indicator method (simplified)
            if "rsi" in modification.modification_request.lower():
                rsi_method = '''
    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI indicator"""
        rsi_indicator = RSIIndicator(close=prices, window=period)
        return rsi_indicator.rsi()'''
                
                # Insert before the last method
                lines = modified_code.split('\n')
                insert_index = len(lines) - 10  # Insert near the end
                lines.insert(insert_index, rsi_method)
                modified_code = '\n'.join(lines)
        
        elif modification.modification_type == "change_risk":
            # Modify risk parameters
            import re
            modified_code = re.sub(
                r'risk_per_trade: float = [\d.]+',
                'risk_per_trade: float = 0.01',  # Example: reduce to 1%
                modified_code
            )
        
        return modified_code
    
    def cleanup_old_contexts(self, max_age_hours: int = 24):
        """Clean up old conversation contexts"""
        
        cutoff_time = datetime.now().timestamp() - (max_age_hours * 3600)
        
        contexts_to_remove = []
        for key, context in self.active_contexts.items():
            if context.last_activity.timestamp() < cutoff_time:
                contexts_to_remove.append(key)
        
        for key in contexts_to_remove:
            del self.active_contexts[key]
        
        self.logger.info(f"Cleaned up {len(contexts_to_remove)} old conversation contexts")