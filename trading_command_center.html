<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Command Center</title>
    <link rel="stylesheet" href="command_center.css">
</head>
<body>
    <header>
        <h1>Trading Command Center</h1>
        <div id="platform-status">
            <span>Platform: <span class="status-ok">Connected</span></span>
            <span>MT5 Bridge: <span class="status-ok">Active</span></span>
            <span>AI Engine: <span class="status-ok">Online</span></span>
        </div>
    </header>

    <main class="container">
        <div class="main-content">
            <!-- Strategy Generation Hub -->
            <section id="strategy-hub" class="card">
                <h2>Strategy Generation Hub</h2>
                <div id="chatbot-interface">
                    <div id="chat-history" class="hidden"></div>
                    <textarea id="chat-input" placeholder="Describe your trading idea or select a prompt below..."></textarea>
                    <button id="send-chat-btn">Generate</button>
                </div>
            </section>

            <!-- Prompt Library -->
            <section id="prompt-library">
                <h3>Prompt Library</h3>
                <div class="prompt-scroller">
                    <div class="prompt-card" data-prompt="Build a classic momentum strategy that buys when the 50-period moving average crosses above the 200-period.">
                        <h4>Golden Cross Momentum</h4>
                        <p>A classic momentum strategy that buys when the 50-period moving average crosses above the 200-period.</p>
                    </div>
                    <div class="prompt-card" data-prompt="Create a mean-reversion strategy using the RSI indicator. Buy when RSI is below 30 and sell when it's above 70.">
                        <h4>RSI Mean Reversion</h4>
                        <p>A mean-reversion strategy that enters trades when an asset is overbought or oversold based on the RSI indicator.</p>
                    </div>
                    <div class="prompt-card" data-prompt="Generate a scalping strategy based on Bollinger Bands. Enter a trade when the price touches the lower band and exit when it hits the middle band.">
                        <h4>Bollinger Band Scalper</h4>
                        <p>A short-term scalping strategy that aims to profit from small price movements using Bollinger Bands.</p>
                    </div>
                </div>
            </section>

            <!-- MQL5 Studio -->
            <section id="mql5-studio" class="card">
                <div class="card-header">
                    <h2>MQL5 Indicator & EA Studio</h2>
                    <span class="pro-badge">PRO</span>
                </div>
                <p>Generate custom MQL5 indicators and Expert Advisors. Avoid marketplace fees and build tools tailored to your strategy.</p>
                <button id="mql5-cta-btn">Launch Studio</button>
            </section>
        </div>

        <aside class="sidebar">
            <!-- Strategy & Asset Dashboard -->
            <section id="asset-dashboard" class="card">
                <h2>Strategy & Asset Dashboard</h2>
                <div id="asset-list">
                    <!-- Assets will be dynamically added here -->
                    <div class="asset-item">
                        <div>
                            <strong>RSI Scalper - EURUSD</strong>
                            <span>Python Strategy</span>
                        </div>
                        <div>
                            <span class="status-live">Live</span>
                            <span>7-Day P/L: +$150</span>
                        </div>
                        <div class="asset-actions">
                            <button>Pause</button>
                            <button>Edit</button>
                            <button>Delete</button>
                        </div>
                    </div>
                </div>
            </section>
        </aside>
    </main>

    <!-- Modal for PRO feature -->
    <div id="pro-modal" class="modal hidden">
        <div class="modal-content">
            <span class="close-button">&times;</span>
            <h3>MQL5 Studio is a Professional Feature</h3>
            <p>MQL5 generation is available on the Pro plan. Upgrade now to build custom indicators and Expert Advisors without marketplace fees.</p>
            <button id="upgrade-btn">Upgrade to Pro</button>
        </div>
    </div>

    <script src="command_center.js"></script>
</body>
</html>
