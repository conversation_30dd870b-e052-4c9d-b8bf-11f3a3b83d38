import jwt from 'jsonwebtoken';
import { User, UserId } from '../../shared/types';
import { AuthTokens } from './auth.service';

export interface TokenPayload {
  userId: UserId;
  email?: string;
  subscriptionTier?: string;
  type: 'access' | 'refresh';
  iat?: number;
  exp?: number;
}

export interface RefreshTokenPayload {
  userId: UserId;
  type: 'refresh';
  iat?: number;
  exp?: number;
}

export interface TokenServiceConfig {
  jwtSecret: string;
  accessTokenExpiry: string;
  refreshTokenExpiry: string;
}

export class TokenService {
  constructor(private config: TokenServiceConfig) {
    if (!config.jwtSecret) {
      throw new Error('JWT secret is required');
    }
  }

  async generateTokens(user: User): Promise<AuthTokens> {
    try {
      // Generate access token with user info
      const accessTokenPayload: Omit<TokenPayload, 'iat' | 'exp'> = {
        userId: user.id,
        email: user.email,
        subscriptionTier: user.subscriptionTier,
        type: 'access',
      };

      const accessToken = jwt.sign(
        accessTokenPayload,
        this.config.jwtSecret,
        { expiresIn: this.config.accessTokenExpiry } as jwt.SignOptions
      );

      // Generate refresh token with minimal info
      const refreshTokenPayload: Omit<RefreshTokenPayload, 'iat' | 'exp'> = {
        userId: user.id,
        type: 'refresh',
      };

      const refreshToken = jwt.sign(
        refreshTokenPayload,
        this.config.jwtSecret,
        { expiresIn: this.config.refreshTokenExpiry } as jwt.SignOptions
      );

      // Calculate expires in seconds
      const expiresIn = this.getTokenExpiry('access');

      return {
        accessToken,
        refreshToken,
        expiresIn,
      };
    } catch (error) {
      throw new Error(`Token generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async verifyToken(token: string): Promise<TokenPayload> {
    try {
      const payload = jwt.verify(token, this.config.jwtSecret) as TokenPayload;
      return payload;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new Error('Token expired');
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new Error('Invalid token');
      } else {
        throw new Error('Token verification failed');
      }
    }
  }

  async verifyRefreshToken(token: string): Promise<RefreshTokenPayload> {
    try {
      const payload = jwt.verify(token, this.config.jwtSecret) as RefreshTokenPayload;
      
      if (payload.type !== 'refresh') {
        throw new Error('Invalid token type');
      }
      
      return payload;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new Error('Refresh token expired');
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new Error('Invalid refresh token');
      } else {
        throw new Error(error instanceof Error ? error.message : 'Refresh token verification failed');
      }
    }
  }

  extractTokenFromHeader(authHeader: string | undefined): string | null {
    if (!authHeader) {
      return null;
    }

    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return null;
    }

    const token = parts[1]?.trim();
    return token && token.length > 0 ? token : null;
  }

  getTokenExpiry(type: 'access' | 'refresh'): number {
    const expiryString = type === 'refresh' ? this.config.refreshTokenExpiry : this.config.accessTokenExpiry;
    
    // Convert time string to seconds
    const timeUnit = expiryString.slice(-1);
    const timeValue = parseInt(expiryString.slice(0, -1));

    switch (timeUnit) {
      case 's':
        return timeValue;
      case 'm':
        return timeValue * 60;
      case 'h':
        return timeValue * 60 * 60;
      case 'd':
        return timeValue * 24 * 60 * 60;
      case 'w':
        return timeValue * 7 * 24 * 60 * 60;
      default:
        // Default to access token expiry (1 hour)
        return 3600;
    }
  }

  decodeToken(token: string): TokenPayload | null {
    try {
      const decoded = jwt.decode(token) as TokenPayload;
      return decoded;
    } catch (error) {
      return null;
    }
  }

  async isTokenExpired(token: string): Promise<boolean> {
    try {
      await this.verifyToken(token);
      return false;
    } catch (error) {
      return true;
    }
  }
}