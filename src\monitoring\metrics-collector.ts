// src/monitoring/metrics-collector.ts

export interface MetricLabels {
  [key: string]: string | number;
}

export interface CounterMetric {
  value: number;
  labels: { [key: string]: number };
}

export interface GaugeMetric {
  value: number;
  labels: MetricLabels;
}

export interface HistogramMetric {
  count: number;
  sum: number;
  buckets: { [key: string]: number };
  labels: MetricLabels;
}

export interface Metrics {
  [key: string]: CounterMetric | GaugeMetric | HistogramMetric;
}

/**
 * Metrics collector for monitoring application performance
 */
export class MetricsCollector {
  private counters: Map<string, CounterMetric> = new Map();
  private gauges: Map<string, GaugeMetric> = new Map();
  private histograms: Map<string, HistogramMetric> = new Map();

  /**
   * Increment a counter metric
   */
  incrementCounter(name: string, labels: MetricLabels = {}): void {
    const existing = this.counters.get(name);
    
    if (existing) {
      existing.value += 1;
      // Update label counts - use the label values as keys
      for (const [key, value] of Object.entries(labels)) {
        const labelValue = String(value);
        existing.labels[labelValue] = (existing.labels[labelValue] || 0) + 1;
      }
    } else {
      const labelCounts: { [key: string]: number } = {};
      // Initialize label counts - use the label values as keys
      for (const [key, value] of Object.entries(labels)) {
        const labelValue = String(value);
        labelCounts[labelValue] = 1;
      }
      
      this.counters.set(name, {
        value: 1,
        labels: labelCounts
      });
    }
  }

  /**
   * Set a gauge metric value
   */
  setGauge(name: string, value: number, labels: MetricLabels = {}): void {
    this.gauges.set(name, {
      value,
      labels
    });
  }

  /**
   * Record a histogram observation
   */
  recordHistogram(name: string, value: number, labels: MetricLabels = {}): void {
    const existing = this.histograms.get(name);
    
    if (existing) {
      existing.count += 1;
      existing.sum += value;
      // Simple bucket implementation
      const bucket = this.getBucket(value);
      existing.buckets[bucket] = (existing.buckets[bucket] || 0) + 1;
    } else {
      const bucket = this.getBucket(value);
      this.histograms.set(name, {
        count: 1,
        sum: value,
        buckets: { [bucket]: 1 },
        labels
      });
    }
  }

  /**
   * Get all metrics
   */
  getMetrics(): Metrics {
    const metrics: Metrics = {};
    
    // Add counters
    for (const [name, metric] of this.counters) {
      metrics[name] = metric;
    }
    
    // Add gauges
    for (const [name, metric] of this.gauges) {
      metrics[name] = metric;
    }
    
    // Add histograms
    for (const [name, metric] of this.histograms) {
      metrics[name] = metric;
    }
    
    return metrics;
  }

  /**
   * Export metrics in Prometheus format
   */
  exportPrometheusMetrics(): string {
    let output = '';
    
    // Export counters
    for (const [name, metric] of this.counters) {
      output += `# TYPE ${name} counter\n`;
      output += `${name} ${metric.value}\n`;
    }
    
    // Export gauges
    for (const [name, metric] of this.gauges) {
      output += `# TYPE ${name} gauge\n`;
      output += `${name} ${metric.value}\n`;
    }
    
    // Export histograms
    for (const [name, metric] of this.histograms) {
      output += `# TYPE ${name} histogram\n`;
      output += `${name}_count ${metric.count}\n`;
      output += `${name}_sum ${metric.sum}\n`;
      for (const [bucket, count] of Object.entries(metric.buckets)) {
        output += `${name}_bucket{le="${bucket}"} ${count}\n`;
      }
    }
    
    return output;
  }

  /**
   * Reset all metrics
   */
  reset(): void {
    this.counters.clear();
    this.gauges.clear();
    this.histograms.clear();
  }

  private getBucket(value: number): string {
    // Simple bucket logic
    if (value <= 0.1) return '0.1';
    if (value <= 0.5) return '0.5';
    if (value <= 1) return '1';
    if (value <= 5) return '5';
    if (value <= 10) return '10';
    return '+Inf';
  }
}