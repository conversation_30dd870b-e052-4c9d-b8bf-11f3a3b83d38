"""
Simple Dependency Injection Tests

Basic tests to verify dependency injection is working correctly.
"""

import pytest
import asyncio
from datetime import datetime

from core.dependency_injection import Dependency<PERSON>ontainer
from core.service_configuration import ServiceConfigurator
from core.trading_engine import TradingEngine
from core.interfaces import TradingSignal
from services.mock_services import MockServiceCollection

class TestSimpleDependencyInjection:
    """Simple tests for dependency injection"""
    
    def test_basic_service_resolution(self):
        """Test basic service resolution"""
        configurator = ServiceConfigurator()
        container = configurator.configure_for_testing()
        
        # Verify all services can be resolved
        from core.interfaces import (
            IMarketDataService, IStrategyService, ITradingService,
            IRiskManagementService, IPortfolioService, INotificationService,
            IDataStorageService, ILoggingService, IConfigurationService
        )
        
        services = [
            IMarketDataService, IStrategyService, ITradingService,
            IRiskManagementService, IPortfolioService, INotificationService,
            IDataStorageService, ILoggingService, IConfigurationService
        ]
        
        for service_interface in services:
            service = container.resolve(service_interface)
            assert service is not None
            print(f"✅ {service_interface.__name__}: {service.__class__.__name__}")
    
    def test_trading_engine_creation(self):
        """Test trading engine can be created with DI"""
        configurator = ServiceConfigurator()
        container = configurator.configure_for_testing()
        
        # Create trading engine
        engine = container.resolve(TradingEngine)
        assert engine is not None
        
        # Verify dependencies are injected
        assert engine.market_data is not None
        assert engine.strategy is not None
        assert engine.trading is not None
        assert engine.risk_management is not None
        assert engine.portfolio is not None
        assert engine.notifications is not None
        assert engine.data_storage is not None
        assert engine.logger is not None
        assert engine.config is not None
        
        print("✅ Trading engine created with all dependencies injected")
    
    @pytest.mark.asyncio
    async def test_signal_storage(self):
        """Test signal storage works"""
        configurator = ServiceConfigurator()
        container = configurator.configure_for_testing()
        
        # Get data storage service
        from core.interfaces import IDataStorageService
        data_storage = container.resolve(IDataStorageService)
        
        # Create and store a signal
        signal = TradingSignal(
            symbol="AAPL",
            signal="buy",
            confidence=0.8,
            timestamp=datetime.now(),
            strategy_name="test",
            metadata={}
        )
        
        await data_storage.store_trading_signal(signal)
        
        # Verify signal was stored
        stored_signals = data_storage.get_stored_signals()
        assert len(stored_signals) == 1
        assert stored_signals[0].symbol == "AAPL"
        assert stored_signals[0].signal == "buy"
        
        print("✅ Signal storage working correctly")
    
    @pytest.mark.asyncio
    async def test_mock_trading_service(self):
        """Test mock trading service works"""
        configurator = ServiceConfigurator()
        container = configurator.configure_for_testing()
        
        # Get trading service
        from core.interfaces import ITradingService, Order
        trading_service = container.resolve(ITradingService)
        
        # Create and place an order
        order = Order(
            id="test_order",
            symbol="AAPL",
            side="buy",
            quantity=10,
            price=150.0,
            order_type="market",
            status="pending",
            timestamp=datetime.now()
        )
        
        order_id = await trading_service.place_order(order)
        assert order_id is not None
        
        # Verify order was placed
        orders = trading_service.get_orders()
        assert len(orders) == 1
        
        print(f"✅ Order placed successfully: {order_id}")
    
    @pytest.mark.asyncio
    async def test_engine_signal_processing_basic(self):
        """Test basic signal processing"""
        configurator = ServiceConfigurator()
        container = configurator.configure_for_testing()
        
        # Create engine
        engine = container.resolve(TradingEngine)
        
        # Configure for auto-trading
        engine.config.set_config('engine.auto_trading_enabled', True)
        engine._engine_config = engine._load_engine_config()
        
        # Create a simple signal
        signal = TradingSignal(
            symbol="AAPL",
            signal="buy",
            confidence=0.8,
            timestamp=datetime.now(),
            strategy_name="test",
            metadata={}
        )
        
        # Process signal directly
        await engine._process_signal("test", signal)
        
        # Check that signal was stored
        stored_signals = engine.data_storage.get_stored_signals()
        assert len(stored_signals) > 0
        print(f"✅ Signal processed and stored: {len(stored_signals)} signals")
        
        # Check if order was placed (might not be due to risk management)
        orders = engine.trading.get_orders()
        print(f"📊 Orders placed: {len(orders)}")
        
        # Check logs
        logs = engine.logger.get_logs()
        print(f"📝 Log entries: {len(logs)}")
        for log in logs[-3:]:  # Show last 3 logs
            print(f"   {log['level']}: {log['message']}")

if __name__ == "__main__":
    # Run tests directly
    test = TestSimpleDependencyInjection()
    
    print("🧪 Testing Basic Service Resolution...")
    test.test_basic_service_resolution()
    
    print("\n🧪 Testing Trading Engine Creation...")
    test.test_trading_engine_creation()
    
    print("\n🧪 Testing Signal Storage...")
    asyncio.run(test.test_signal_storage())
    
    print("\n🧪 Testing Mock Trading Service...")
    asyncio.run(test.test_mock_trading_service())
    
    print("\n🧪 Testing Engine Signal Processing...")
    asyncio.run(test.test_engine_signal_processing_basic())
    
    print("\n✅ All basic dependency injection tests passed!")