import { z } from 'zod';
import { IdSchema } from './common.schemas';
import { TradingSymbolSchema } from './trading.schemas';

// Upload Status
export const UploadStatusSchema = z.enum([
  'pending', 'uploading', 'mapping', 'parsing', 'validating', 'ready', 'error'
]);
export type UploadStatus = z.infer<typeof UploadStatusSchema>;

// Column Mapping Types
export const ColumnMappingTypeSchema = z.enum([
  'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'Bid', 'Ask', 'Ignore'
]);
export type ColumnMappingType = z.infer<typeof ColumnMappingTypeSchema>;

// File Upload Entity
export const DataFileUploadSchema = z.object({
  id: IdSchema,
  user_id: IdSchema,
  filename: z.string().min(1),
  original_filename: z.string().min(1),
  file_size: z.number().int().nonnegative(),
  mime_type: z.string(),
  upload_path: z.string(),
  
  // File metadata
  symbol: TradingSymbolSchema.optional(),
  timeframe: z.string().optional(), // e.g., 'M1', 'H1', 'D1'
  
  // Processing status
  status: UploadStatusSchema,
  progress: z.number().min(0).max(100).default(0),
  
  // Column mapping
  column_mapping: z.record(ColumnMappingTypeSchema).optional(),
  
  // Validation results
  validation_errors: z.array(z.string()).optional(),
  data_quality: z.object({
    total_rows: z.number().int().nonnegative(),
    valid_rows: z.number().int().nonnegative(),
    invalid_rows: z.number().int().nonnegative(),
    date_range: z.object({
      start: z.date(),
      end: z.date(),
    }).optional(),
    completeness: z.number().min(0).max(1), // Percentage of complete data
  }).optional(),
  
  // Error information
  error_message: z.string().optional(),
  error_details: z.array(z.string()).optional(),
  
  // Timestamps
  created_at: z.date(),
  updated_at: z.date(),
  processed_at: z.date().optional(),
});
export type DataFileUpload = z.infer<typeof DataFileUploadSchema>;

// Upload Request
export const CreateUploadRequestSchema = z.object({
  filename: z.string().min(1),
  file_size: z.number().int().positive(),
  mime_type: z.string(),
  symbol: TradingSymbolSchema.optional(),
  timeframe: z.string().optional(),
});
export type CreateUploadRequest = z.infer<typeof CreateUploadRequestSchema>;

// Column Mapping Request
export const ColumnMappingRequestSchema = z.object({
  upload_id: IdSchema,
  mapping: z.record(ColumnMappingTypeSchema),
  symbol: TradingSymbolSchema,
  timeframe: z.string(),
});
export type ColumnMappingRequest = z.infer<typeof ColumnMappingRequestSchema>;

// File Preview (first few rows for mapping)
export const FilePreviewSchema = z.object({
  headers: z.array(z.string()),
  sample_rows: z.array(z.array(z.string())),
  detected_delimiter: z.string().optional(),
  detected_encoding: z.string().optional(),
  estimated_rows: z.number().int().nonnegative(),
});
export type FilePreview = z.infer<typeof FilePreviewSchema>;

// Parsed Market Data
export const ParsedMarketDataSchema = z.object({
  symbol: TradingSymbolSchema,
  timeframe: z.string(),
  data: z.array(z.object({
    timestamp: z.date(),
    open: z.number().positive(),
    high: z.number().positive(),
    low: z.number().positive(),
    close: z.number().positive(),
    volume: z.number().nonnegative().optional(),
    bid: z.number().positive().optional(),
    ask: z.number().positive().optional(),
  })),
  metadata: z.object({
    total_records: z.number().int().nonnegative(),
    date_range: z.object({
      start: z.date(),
      end: z.date(),
    }),
    completeness: z.number().min(0).max(1),
    gaps: z.array(z.object({
      start: z.date(),
      end: z.date(),
      duration_minutes: z.number().int().positive(),
    })).optional(),
  }),
});
export type ParsedMarketData = z.infer<typeof ParsedMarketDataSchema>;

// Python Engine Data Processing
export const PythonDataProcessingRequestSchema = z.object({
  request_id: z.string().uuid(),
  upload_id: IdSchema,
  file_path: z.string(),
  column_mapping: z.record(ColumnMappingTypeSchema),
  symbol: TradingSymbolSchema,
  timeframe: z.string(),
  validation_config: z.object({
    require_ohlc: z.boolean().default(true),
    min_data_points: z.number().int().positive().default(100),
    max_gap_minutes: z.number().int().positive().default(60),
    price_validation: z.object({
      min_price: z.number().positive().default(0.0001),
      max_price: z.number().positive().default(1000000),
      max_price_change: z.number().min(0).max(1).default(0.2), // 20%
    }),
  }),
});
export type PythonDataProcessingRequest = z.infer<typeof PythonDataProcessingRequestSchema>;

export const PythonDataProcessingResponseSchema = z.object({
  request_id: z.string().uuid(),
  success: z.boolean(),
  data: ParsedMarketDataSchema.optional(),
  validation_errors: z.array(z.string()).optional(),
  warnings: z.array(z.string()).optional(),
  processing_stats: z.object({
    rows_processed: z.number().int().nonnegative(),
    rows_valid: z.number().int().nonnegative(),
    rows_invalid: z.number().int().nonnegative(),
    processing_time_ms: z.number().nonnegative(),
  }),
  error: z.string().optional(),
});
export type PythonDataProcessingResponse = z.infer<typeof PythonDataProcessingResponseSchema>;

// Upload Progress Updates
export const UploadProgressSchema = z.object({
  upload_id: IdSchema,
  status: UploadStatusSchema,
  progress: z.number().min(0).max(100),
  message: z.string().optional(),
  current_step: z.string().optional(),
  estimated_completion: z.date().optional(),
});
export type UploadProgress = z.infer<typeof UploadProgressSchema>;

// Data Validation Rules
export const DataValidationRuleSchema = z.object({
  name: z.string(),
  description: z.string(),
  rule_type: z.enum(['required', 'range', 'format', 'relationship']),
  parameters: z.record(z.any()),
  severity: z.enum(['error', 'warning', 'info']),
});
export type DataValidationRule = z.infer<typeof DataValidationRuleSchema>;

export const DataValidationResultSchema = z.object({
  rule: DataValidationRuleSchema,
  passed: z.boolean(),
  affected_rows: z.number().int().nonnegative(),
  details: z.array(z.object({
    row_number: z.number().int().positive(),
    column: z.string().optional(),
    value: z.any().optional(),
    message: z.string(),
  })).optional(),
});
export type DataValidationResult = z.infer<typeof DataValidationResultSchema>;

// Bulk Upload for Multiple Files
export const BulkUploadRequestSchema = z.object({
  files: z.array(CreateUploadRequestSchema).min(1).max(10),
  default_symbol: TradingSymbolSchema.optional(),
  default_timeframe: z.string().optional(),
  processing_config: z.object({
    parallel_processing: z.boolean().default(true),
    validation_level: z.enum(['strict', 'normal', 'lenient']).default('normal'),
    auto_mapping: z.boolean().default(true),
  }),
});
export type BulkUploadRequest = z.infer<typeof BulkUploadRequestSchema>;

export const BulkUploadResponseSchema = z.object({
  batch_id: IdSchema,
  uploads: z.array(DataFileUploadSchema),
  summary: z.object({
    total_files: z.number().int().nonnegative(),
    successful_uploads: z.number().int().nonnegative(),
    failed_uploads: z.number().int().nonnegative(),
    total_data_points: z.number().int().nonnegative(),
  }),
});
export type BulkUploadResponse = z.infer<typeof BulkUploadResponseSchema>;