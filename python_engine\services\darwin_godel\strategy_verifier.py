from typing import Dict, Any, List, Optional
import ast
import numpy as np
from dataclasses import dataclass
import random

# Import the secure executor for safe code execution
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

# Import pattern detection and security
try:
    from .pattern_detector import StrategyPatternDetector
    from .pattern_report import PatternReport
    from .secure_executor import SecureStrategyExecutor, SecurityError
except ImportError:
    # Fallback for direct execution
    from pattern_detector import StrategyPatternDetector
    from pattern_report import PatternReport
    from secure_executor import SecureStrategyExecutor, SecurityError


@dataclass
class VerificationResult:
    is_valid: bool
    strategy_type: str
    risk_score: float
    robustness_score: float
    warnings: List[str] = None
    metrics: Dict[str, float] = None


class DarwinGodelVerifier:
    """
    Darwin Godel Model for Strategy Verification
    
    This verifier ensures strategies are:
    1. Safe to execute (no malicious code)
    2. Logically sound (proper structure)
    3. Statistically robust (not overfitted)
    4. Performance validated (backtested)
    """
    
    def __init__(self):
        self.secure_executor = SecureStrategyExecutor()
        self.pattern_detector = StrategyPatternDetector()
        
        self.safe_functions = {
            'calculate_sma': self._calculate_sma,
            'calculate_rsi': self._calculate_rsi,
            'calculate_returns': self._calculate_returns,
            'calculate_macd': self._calculate_macd,
            'calculate_bollinger': self._calculate_bollinger,
            'calculate_ema': self._calculate_ema,
        }
        
        self.pattern_detectors = {
            'mean_reversion': self._detect_mean_reversion,
            'momentum': self._detect_momentum,
            'breakout': self._detect_breakout,
        }
    
    def verify_strategy(self, strategy_code: str) -> Dict[str, Any]:
        """
        Verify a trading strategy for safety and validity
        
        Args:
            strategy_code: Python code defining the strategy
            
        Returns:
            VerificationResult with validation details
        """
        # Step 1: Security check using SecureStrategyExecutor
        security_validation = self.secure_executor.validate_strategy_code(strategy_code)
        if not security_validation['is_secure']:
            return {
                'is_valid': False,
                'is_secure': False,
                'security_issues': security_validation['security_issues'],
                'strategy_type': 'invalid',
                'risk_score': 1.0,
                'robustness_score': 0.0,
                'warnings': security_validation['security_issues']
            }
        
        # Step 1b: Test execution with dummy data
        try:
            dummy_data = {
                'close': [100, 101, 102, 101, 103],
                'high': [101, 102, 103, 102, 104],
                'low': [99, 100, 101, 100, 102],
                'volume': [1000, 1100, 1200, 1050, 1300]
            }
            dummy_params = {'period': 3, 'short_period': 5, 'long_period': 10}
            execution_result = self.secure_executor.execute_strategy(strategy_code, dummy_data, dummy_params)
        except SecurityError as e:
            return {
                'is_valid': False,
                'is_secure': False,
                'security_issues': [str(e)],
                'strategy_type': 'invalid',
                'risk_score': 1.0,
                'robustness_score': 0.0,
                'warnings': [str(e)]
            }
        except Exception as e:
            # Other execution errors will be handled later
            execution_result = None
        
        # Step 2: Structure validation
        try:
            strategy_ast = ast.parse(strategy_code)
            self._validate_structure(strategy_ast)
        except SyntaxError as e:
            raise ValueError(f"Strategy code has syntax errors: {str(e)}")
        
        # Step 3: Advanced pattern detection
        pattern_analysis = self.pattern_detector.analyze_strategy(strategy_code)
        
        # Step 4: Legacy pattern detection (for compatibility)
        strategy_type = self._detect_strategy_type(strategy_ast)
        
        # Step 5: Complexity analysis
        risk_score = self._calculate_risk_score(strategy_ast)
        robustness_score = self._calculate_robustness(strategy_ast)
        
        # Step 6: Generate warnings (combine legacy and pattern-based)
        warnings = self._generate_warnings(strategy_ast, risk_score, robustness_score)
        if warnings is None:
            warnings = []
        if pattern_analysis['warnings']:
            warnings.extend(pattern_analysis['warnings'])
        
        return {
            'is_valid': True,
            'strategy_type': pattern_analysis['pattern_type'],  # Use advanced detection
            'legacy_strategy_type': strategy_type,  # Keep legacy for comparison
            'risk_score': risk_score,
            'robustness_score': robustness_score,
            'warnings': warnings if warnings else None,
            'pattern_analysis': pattern_analysis,
            'pattern_report': PatternReport.generate_simple_summary(pattern_analysis)
        }
    
    def verify_with_backtest(self, strategy_code: str, 
                           historical_data: Dict[str, List[float]],
                           initial_capital: float = 10000) -> Dict[str, Any]:
        """
        Verify strategy with historical performance
        """
        # First do basic verification
        basic_result = self.verify_strategy(strategy_code)
        
        if not basic_result['is_valid']:
            return basic_result
        
        # Use pattern analysis to optimize testing
        pattern_type = basic_result['pattern_analysis']['pattern_type']
        optimized_data = self._generate_pattern_optimized_data(pattern_type, historical_data)
        
        # Run backtest
        metrics = self._run_backtest(strategy_code, historical_data, initial_capital)
        
        # Add performance-based warnings
        if basic_result['warnings'] is None:
            basic_result['warnings'] = []
        
        if metrics['sharpe_ratio'] < 0.5:
            basic_result['warnings'].append('Low Sharpe ratio indicates poor risk-adjusted returns')
        
        if metrics['max_drawdown'] > 0.3:
            basic_result['warnings'].append('High maximum drawdown indicates high risk')
        
        basic_result['metrics'] = metrics
        return basic_result
    
    def run_monte_carlo_validation(self, strategy_code: str, 
                                   simulations: int = 100,
                                   data_variations: float = 0.02) -> Dict[str, Any]:
        """
        Run Monte Carlo simulations to test strategy robustness
        """
        results = []
        base_data = self._generate_test_data()
        
        for i in range(simulations):
            # Add random variations to data
            varied_data = self._add_noise_to_data(base_data, data_variations)
            
            # Run backtest on varied data
            try:
                metrics = self._run_backtest(strategy_code, varied_data)
                results.append(metrics)
            except Exception as e:
                # Strategy failed on this variation
                results.append({'failed': True})
        
        # Analyze results
        successful_runs = [r for r in results if not r.get('failed', False)]
        success_rate = len(successful_runs) / len(results)
        
        if successful_runs:
            avg_sharpe = np.mean([r['sharpe_ratio'] for r in successful_runs])
            avg_return = np.mean([r['total_return'] for r in successful_runs])
            consistency = max(0, 1 - np.std([r['total_return'] for r in successful_runs]))
        else:
            avg_sharpe = avg_return = consistency = 0
        
        return {
            'success_rate': success_rate,
            'avg_sharpe': avg_sharpe,
            'avg_return': avg_return,
            'consistency_score': consistency,
            'distribution': successful_runs
        }
    
    def _validate_structure(self, ast_tree: ast.AST) -> None:
        """Validate strategy has required structure"""
        # Check for trading_strategy function
        functions = [node.name for node in ast.walk(ast_tree) 
                    if isinstance(node, ast.FunctionDef)]
        
        if 'trading_strategy' not in functions:
            raise ValueError("Strategy must define 'trading_strategy' function")
    
    def _detect_strategy_type(self, ast_tree: ast.AST) -> str:
        """Detect the type of trading strategy"""
        code_str = ast.unparse(ast_tree)
        
        for strategy_type, detector in self.pattern_detectors.items():
            if detector(code_str):
                return strategy_type
        
        return 'custom'
    
    def _calculate_risk_score(self, ast_tree: ast.AST) -> float:
        """Calculate risk score based on strategy complexity"""
        # Count conditional statements (complexity indicator)
        conditions = sum(1 for node in ast.walk(ast_tree) 
                        if isinstance(node, (ast.If, ast.While, ast.For)))
        
        # More conditions = higher risk of overfitting
        risk_score = min(conditions * 0.15, 1.0)
        return risk_score
    
    def _calculate_robustness(self, ast_tree: ast.AST) -> float:
        """Calculate robustness score"""
        # Look for hard-coded values (bad for robustness)
        hard_coded_numbers = sum(1 for node in ast.walk(ast_tree)
                               if isinstance(node, ast.Constant) 
                               and isinstance(node.value, (int, float))
                               and node.value not in [0, 1, -1])  # Exclude common constants
        
        # Look for very specific comparisons (overfitting indicators)
        code_str = ast.unparse(ast_tree)
        specific_comparisons = 0
        
        # Count exact equality comparisons with specific numbers
        for node in ast.walk(ast_tree):
            if isinstance(node, ast.Compare):
                for op in node.ops:
                    if isinstance(op, ast.Eq):  # Exact equality
                        specific_comparisons += 1
        
        # More hard-coded values and specific comparisons = less robust
        robustness = max(1.0 - (hard_coded_numbers * 0.15) - (specific_comparisons * 0.2), 0.0)
        return robustness
    
    def _generate_warnings(self, ast_tree: ast.AST, 
                         risk_score: float, 
                         robustness_score: float) -> List[str]:
        """Generate warnings based on analysis"""
        warnings = []
        
        if risk_score > 0.7:
            warnings.append("High complexity detected - possible overfitting risk")
        
        if robustness_score < 0.3:
            warnings.append("Low robustness - too many hard-coded values")
        
        if robustness_score < 0.1:
            warnings.append("High overfitting risk - strategy uses very specific conditions")
        
        # Check for specific anti-patterns
        code_str = ast.unparse(ast_tree)
        if code_str.count('if') > 5:
            warnings.append("Too many conditional statements")
        
        return warnings if warnings else None
    
    # Pattern detection methods
    def _detect_mean_reversion(self, code: str) -> bool:
        indicators = ['sma', 'mean', 'average', 'bollinger']
        return any(ind in code.lower() for ind in indicators)
    
    def _detect_momentum(self, code: str) -> bool:
        indicators = ['rsi', 'momentum', 'returns', 'trend']
        return any(ind in code.lower() for ind in indicators)
    
    def _detect_breakout(self, code: str) -> bool:
        indicators = ['high', 'low', 'breakout', 'resistance', 'support']
        return any(ind in code.lower() for ind in indicators)
    
    # Technical indicators (simplified versions)
    def _calculate_sma(self, values: List[float], period: int) -> List[float]:
        """Simple Moving Average calculation"""
        if len(values) < period:
            return []
        
        sma = []
        for i in range(period - 1, len(values)):
            avg = sum(values[i - period + 1:i + 1]) / period
            sma.append(avg)
        
        return sma
    
    def _calculate_rsi(self, values: List[float], period: int = 14) -> List[float]:
        """Relative Strength Index calculation"""
        if len(values) < period + 1:
            return []
        
        deltas = [values[i] - values[i-1] for i in range(1, len(values))]
        gains = [delta if delta > 0 else 0 for delta in deltas]
        losses = [-delta if delta < 0 else 0 for delta in deltas]
        
        # Calculate initial averages
        avg_gain = sum(gains[:period]) / period
        avg_loss = sum(losses[:period]) / period
        
        rsi = []
        
        for i in range(period, len(gains)):
            if avg_loss == 0:
                rsi.append(100)
            else:
                rs = avg_gain / avg_loss
                rsi_value = 100 - (100 / (1 + rs))
                rsi.append(rsi_value)
            
            # Update averages
            avg_gain = ((avg_gain * (period - 1)) + gains[i]) / period
            avg_loss = ((avg_loss * (period - 1)) + losses[i]) / period
        
        return rsi
    
    def _calculate_returns(self, values: List[float]) -> List[float]:
        """Calculate returns"""
        if len(values) < 2:
            return []
        
        returns = []
        for i in range(1, len(values)):
            ret = (values[i] - values[i-1]) / values[i-1]
            returns.append(ret)
        
        return returns
    
    def _calculate_ema(self, values: List[float], period: int) -> List[float]:
        """Exponential Moving Average calculation"""
        if len(values) < period:
            return []
        
        multiplier = 2 / (period + 1)
        ema = [sum(values[:period]) / period]  # Start with SMA
        
        for i in range(period, len(values)):
            ema_value = (values[i] * multiplier) + (ema[-1] * (1 - multiplier))
            ema.append(ema_value)
        
        return ema
    
    def _calculate_macd(self, values: List[float], fast: int = 12, 
                       slow: int = 26, signal: int = 9) -> Dict[str, List[float]]:
        """MACD calculation"""
        if len(values) < slow:
            return {}
        
        ema_fast = self._calculate_ema(values, fast)
        ema_slow = self._calculate_ema(values, slow)
        
        if not ema_fast or not ema_slow:
            return {}
        
        # Align the EMAs
        start_idx = slow - fast
        macd_line = [ema_fast[i + start_idx] - ema_slow[i] 
                     for i in range(len(ema_slow))]
        
        signal_line = self._calculate_ema(macd_line, signal)
        
        return {
            'macd': macd_line,
            'signal': signal_line,
        }
    
    def _calculate_bollinger(self, values: List[float], period: int = 20, 
                           std_dev: float = 2) -> Dict[str, List[float]]:
        """Bollinger Bands calculation"""
        if len(values) < period:
            return {}
        
        sma = self._calculate_sma(values, period)
        if not sma:
            return {}
        
        upper_band = []
        lower_band = []
        
        for i in range(len(sma)):
            data_slice = values[i:i + period]
            std = np.std(data_slice)
            
            upper_band.append(sma[i] + (std_dev * std))
            lower_band.append(sma[i] - (std_dev * std))
        
        return {
            'upper': upper_band,
            'middle': sma,
            'lower': lower_band
        }
    
    def _run_backtest(self, strategy_code: str, 
                     historical_data: Dict[str, List[float]], 
                     initial_capital: float = 10000) -> Dict[str, float]:
        """Run a simple backtest"""
        # Simple backtest implementation
        capital = initial_capital
        position = 0
        trades = []
        equity_curve = [capital]
        
        # Default parameters for testing
        params = {
            'period': 20,
            'short_period': 5,
            'long_period': 20,
            'rsi_period': 14
        }
        
        for i in range(20, len(historical_data['close'])):  # Start after warm-up period
            # Prepare data slice
            data_slice = {
                'close': historical_data['close'][:i+1],
                'high': historical_data.get('high', historical_data['close'])[:i+1],
                'low': historical_data.get('low', historical_data['close'])[:i+1],
                'volume': historical_data.get('volume', [1000] * (i+1))[:i+1]
            }
            
            try:
                # Execute strategy
                result = self.secure_executor.execute(strategy_code, data_slice, params)
                signal = result.get('signal', 'hold')
                
                current_price = historical_data['close'][i]
                
                # Execute trades
                if signal == 'buy' and position <= 0:
                    if position < 0:  # Close short position
                        capital += position * current_price
                        position = 0
                    # Open long position
                    shares_to_buy = capital * 0.95 / current_price  # Use 95% of capital
                    position += shares_to_buy
                    capital -= shares_to_buy * current_price
                    trades.append({'type': 'buy', 'price': current_price, 'shares': shares_to_buy})
                
                elif signal == 'sell' and position >= 0:
                    if position > 0:  # Close long position
                        capital += position * current_price
                        position = 0
                    # Open short position (simplified)
                    shares_to_short = capital * 0.95 / current_price
                    position -= shares_to_short
                    capital += shares_to_short * current_price
                    trades.append({'type': 'sell', 'price': current_price, 'shares': shares_to_short})
                
                # Calculate current equity
                current_equity = capital + position * current_price
                equity_curve.append(current_equity)
                
            except Exception:
                # If strategy fails, hold position
                current_equity = capital + position * historical_data['close'][i]
                equity_curve.append(current_equity)
        
        # Close final position
        if position != 0:
            final_price = historical_data['close'][-1]
            capital += position * final_price
            position = 0
        
        # Calculate metrics
        total_return = (capital - initial_capital) / initial_capital
        
        # Calculate Sharpe ratio (simplified)
        returns = [(equity_curve[i] - equity_curve[i-1]) / equity_curve[i-1] 
                  for i in range(1, len(equity_curve))]
        
        if len(returns) > 0 and np.std(returns) > 0:
            sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252)  # Annualized
        else:
            sharpe_ratio = 0
        
        # Calculate max drawdown
        peak = initial_capital
        max_drawdown = 0
        for equity in equity_curve:
            if equity > peak:
                peak = equity
            drawdown = (peak - equity) / peak
            if drawdown > max_drawdown:
                max_drawdown = drawdown
        
        # Calculate win rate
        winning_trades = sum(1 for trade in trades if trade.get('profit', 0) > 0)
        win_rate = winning_trades / len(trades) if trades else 0
        
        return {
            'total_return': total_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'num_trades': len(trades)
        }
    
    def _generate_test_data(self, length: int = 100) -> Dict[str, List[float]]:
        """Generate synthetic test data"""
        np.random.seed(42)  # For reproducible results
        
        # Generate price series with some trend and noise
        base_price = 100
        prices = [base_price]
        
        for i in range(length - 1):
            # Random walk with slight upward bias
            change = np.random.normal(0.001, 0.02)  # 0.1% drift, 2% volatility
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 1))  # Ensure positive prices
        
        # Generate OHLV data
        highs = [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices]
        lows = [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices]
        volumes = [1000 + np.random.randint(-200, 200) for _ in range(length)]
        
        return {
            'close': prices,
            'high': highs,
            'low': lows,
            'volume': volumes,
            'open': prices  # Simplified
        }
    
    def _add_noise_to_data(self, data: Dict[str, List[float]], 
                          noise_level: float) -> Dict[str, List[float]]:
        """Add random noise to data for Monte Carlo testing"""
        noisy_data = {}
        
        for key, values in data.items():
            if key == 'volume':
                # Add integer noise to volume
                noisy_values = [max(1, int(v * (1 + np.random.normal(0, noise_level)))) 
                               for v in values]
            else:
                # Add percentage noise to prices
                noisy_values = [max(0.01, v * (1 + np.random.normal(0, noise_level))) 
                               for v in values]
            
            noisy_data[key] = noisy_values
        
        return noisy_data
    
    def _generate_pattern_optimized_data(self, pattern_type: str, 
                                       base_data: Dict[str, List[float]]) -> Dict[str, List[float]]:
        """
        Generate test data optimized for specific pattern types
        
        Args:
            pattern_type: Type of pattern detected
            base_data: Base historical data
            
        Returns:
            Optimized test data for the pattern type
        """
        if pattern_type == 'mean_reversion':
            # Generate sideways/ranging market data
            return self._generate_ranging_market_data(base_data)
        elif pattern_type == 'momentum':
            # Generate trending market data
            return self._generate_trending_market_data(base_data)
        elif pattern_type == 'breakout':
            # Generate data with clear support/resistance levels
            return self._generate_breakout_market_data(base_data)
        elif pattern_type == 'price_action':
            # Use clean price data without much noise
            return self._generate_clean_price_data(base_data)
        else:
            # For mixed or custom patterns, use original data
            return base_data
    
    def _generate_ranging_market_data(self, base_data: Dict[str, List[float]]) -> Dict[str, List[float]]:
        """Generate sideways/ranging market data for mean reversion testing"""
        base_price = np.mean(base_data['close'])
        length = len(base_data['close'])
        
        # Create oscillating prices around mean
        prices = []
        for i in range(length):
            # Oscillate between +/- 5% of base price
            oscillation = 0.05 * np.sin(2 * np.pi * i / 20)  # 20-period cycle
            noise = np.random.normal(0, 0.01)  # Small random noise
            price = base_price * (1 + oscillation + noise)
            prices.append(max(0.01, price))
        
        return {
            'close': prices,
            'high': [p * 1.01 for p in prices],
            'low': [p * 0.99 for p in prices],
            'volume': base_data.get('volume', [1000] * length),
            'open': prices
        }
    
    def _generate_trending_market_data(self, base_data: Dict[str, List[float]]) -> Dict[str, List[float]]:
        """Generate trending market data for momentum testing"""
        start_price = base_data['close'][0]
        length = len(base_data['close'])
        
        # Create upward trending prices
        trend_strength = 0.002  # 0.2% per period
        prices = []
        
        for i in range(length):
            # Linear trend with some noise
            trend = start_price * (1 + trend_strength * i)
            noise = np.random.normal(0, 0.01)
            price = trend * (1 + noise)
            prices.append(max(0.01, price))
        
        return {
            'close': prices,
            'high': [p * 1.02 for p in prices],
            'low': [p * 0.98 for p in prices],
            'volume': base_data.get('volume', [1000] * length),
            'open': prices
        }
    
    def _generate_breakout_market_data(self, base_data: Dict[str, List[float]]) -> Dict[str, List[float]]:
        """Generate data with clear breakout patterns"""
        base_price = base_data['close'][0]
        length = len(base_data['close'])
        
        prices = []
        resistance_level = base_price * 1.05
        support_level = base_price * 0.95
        
        for i in range(length):
            if i < length * 0.7:
                # First 70% - consolidation between support and resistance
                range_position = np.random.uniform(0, 1)
                price = support_level + (resistance_level - support_level) * range_position
            else:
                # Last 30% - breakout above resistance
                breakout_progress = (i - length * 0.7) / (length * 0.3)
                price = resistance_level * (1 + 0.1 * breakout_progress)
            
            noise = np.random.normal(0, 0.005)
            price = price * (1 + noise)
            prices.append(max(0.01, price))
        
        return {
            'close': prices,
            'high': [p * 1.01 for p in prices],
            'low': [p * 0.99 for p in prices],
            'volume': base_data.get('volume', [1000] * length),
            'open': prices
        }
    
    def _generate_clean_price_data(self, base_data: Dict[str, List[float]]) -> Dict[str, List[float]]:
        """Generate clean price data for price action testing"""
        # Use original data but smooth it slightly
        prices = base_data['close'].copy()
        
        # Apply simple moving average smoothing
        smoothed_prices = []
        window = 3
        
        for i in range(len(prices)):
            if i < window:
                smoothed_prices.append(prices[i])
            else:
                avg = sum(prices[i-window:i]) / window
                smoothed_prices.append(avg)
        
        return {
            'close': smoothed_prices,
            'high': [p * 1.005 for p in smoothed_prices],
            'low': [p * 0.995 for p in smoothed_prices],
            'volume': base_data.get('volume', [1000] * len(smoothed_prices)),
            'open': smoothed_prices
        }