import { describe, it, expect, beforeEach, jest, afterEach } from '@jest/globals';
import { CacheService } from '../cache.service';
import { createClient } from 'redis';

// Mock Redis client
jest.mock('redis', () => ({
  createClient: jest.fn()
}));

describe('CacheService', () => {
  let cacheService: CacheService;
  let mockRedisClient: any;

  beforeEach(async () => {
    mockRedisClient = {
      connect: jest.fn(),
      disconnect: jest.fn(),
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
      expire: jest.fn(),
      exists: jest.fn(),
      keys: jest.fn(),
      flushAll: jest.fn(),
      ping: jest.fn(),
      mGet: jest.fn(),
      mSet: jest.fn(),
      info: jest.fn(),
    } as any;

    (createClient as jest.Mock).mockReturnValue(mockRedisClient);
    
    cacheService = new CacheService({
      host: 'localhost',
      port: 6379,
    });

    await cacheService.connect();
  });

  afterEach(async () => {
    await cacheService.disconnect();
    jest.clearAllMocks();
  });

  describe('connection management', () => {
    it('should connect to Redis successfully', async () => {
      // Assert
      expect(mockRedisClient.connect).toHaveBeenCalled();
    });

    it('should disconnect from Redis', async () => {
      // Act
      await cacheService.disconnect();

      // Assert
      expect(mockRedisClient.disconnect).toHaveBeenCalled();
    });

    it('should check health status', async () => {
      // Arrange
      mockRedisClient.ping.mockResolvedValue('PONG');

      // Act
      const isHealthy = await cacheService.isHealthy();

      // Assert
      expect(isHealthy).toBe(true);
      expect(mockRedisClient.ping).toHaveBeenCalled();
    });

    it('should handle connection errors gracefully', async () => {
      // Arrange
      mockRedisClient.ping.mockRejectedValue(new Error('Connection failed'));

      // Act
      const isHealthy = await cacheService.isHealthy();

      // Assert
      expect(isHealthy).toBe(false);
    });
  });

  describe('basic cache operations', () => {
    it('should set and get string values', async () => {
      // Arrange
      const key = 'test:string';
      const value = 'test value';
      mockRedisClient.set.mockResolvedValue('OK');
      mockRedisClient.get.mockResolvedValue(value);

      // Act
      await cacheService.set(key, value);
      const result = await cacheService.get(key);

      // Assert
      expect(mockRedisClient.set).toHaveBeenCalledWith(key, value);
      expect(result).toBe(value);
    });

    it('should set and get JSON objects', async () => {
      // Arrange
      const key = 'test:object';
      const value = { name: 'test', value: 123 };
      const serializedValue = JSON.stringify(value);
      mockRedisClient.set.mockResolvedValue('OK');
      mockRedisClient.get.mockResolvedValue(serializedValue);

      // Act
      await cacheService.setJSON(key, value);
      const result = await cacheService.getJSON(key);

      // Assert
      expect(mockRedisClient.set).toHaveBeenCalledWith(key, serializedValue);
      expect(result).toEqual(value);
    });

    it('should set values with TTL', async () => {
      // Arrange
      const key = 'test:ttl';
      const value = 'test value';
      const ttl = 3600; // 1 hour
      mockRedisClient.set.mockResolvedValue('OK');

      // Act
      await cacheService.set(key, value, ttl);

      // Assert
      expect(mockRedisClient.set).toHaveBeenCalledWith(key, value, { EX: ttl });
    });

    it('should delete keys', async () => {
      // Arrange
      const key = 'test:delete';
      mockRedisClient.del.mockResolvedValue(1);

      // Act
      const result = await cacheService.delete(key);

      // Assert
      expect(mockRedisClient.del).toHaveBeenCalledWith(key);
      expect(result).toBe(true);
    });

    it('should check if key exists', async () => {
      // Arrange
      const key = 'test:exists';
      mockRedisClient.exists.mockResolvedValue(1);

      // Act
      const exists = await cacheService.exists(key);

      // Assert
      expect(mockRedisClient.exists).toHaveBeenCalledWith(key);
      expect(exists).toBe(true);
    });

    it('should return null for non-existent keys', async () => {
      // Arrange
      const key = 'test:nonexistent';
      mockRedisClient.get.mockResolvedValue(null);

      // Act
      const result = await cacheService.get(key);

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('market data caching', () => {
    it('should cache market data with appropriate TTL', async () => {
      // Arrange
      const symbol = 'EURUSD';
      const timeframe = '1h';
      const marketData = [
        { timestamp: new Date(), open: 1.1000, high: 1.1010, low: 1.0990, close: 1.1005 }
      ];
      mockRedisClient.set.mockResolvedValue('OK');

      // Act
      await cacheService.cacheMarketData(symbol, timeframe, marketData);

      // Assert
      const expectedKey = `market_data:${symbol}:${timeframe}`;
      expect(mockRedisClient.set).toHaveBeenCalledWith(
        expectedKey,
        JSON.stringify(marketData),
        { EX: 3600 } // 1 hour TTL
      );
    });

    it('should retrieve cached market data', async () => {
      // Arrange
      const symbol = 'EURUSD';
      const timeframe = '1h';
      const marketData = [
        { timestamp: '2024-01-01T00:00:00Z', open: 1.1000, high: 1.1010, low: 1.0990, close: 1.1005 }
      ];
      mockRedisClient.get.mockResolvedValue(JSON.stringify(marketData));

      // Act
      const result = await cacheService.getMarketData(symbol, timeframe);

      // Assert
      const expectedKey = `market_data:${symbol}:${timeframe}`;
      expect(mockRedisClient.get).toHaveBeenCalledWith(expectedKey);
      expect(result).toEqual(marketData);
    });

    it('should return null for cache miss', async () => {
      // Arrange
      const symbol = 'EURUSD';
      const timeframe = '1h';
      mockRedisClient.get.mockResolvedValue(null);

      // Act
      const result = await cacheService.getMarketData(symbol, timeframe);

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('technical indicators caching', () => {
    it('should cache technical indicators', async () => {
      // Arrange
      const symbol = 'EURUSD';
      const indicator = 'sma';
      const period = 20;
      const data = [1.1000, 1.1005, 1.1010];
      mockRedisClient.set.mockResolvedValue('OK');

      // Act
      await cacheService.cacheTechnicalIndicator(symbol, indicator, period, data);

      // Assert
      const expectedKey = `indicator:${symbol}:${indicator}:${period}`;
      expect(mockRedisClient.set).toHaveBeenCalledWith(
        expectedKey,
        JSON.stringify(data),
        { EX: 1800 } // 30 minutes TTL
      );
    });

    it('should retrieve cached technical indicators', async () => {
      // Arrange
      const symbol = 'EURUSD';
      const indicator = 'sma';
      const period = 20;
      const data = [1.1000, 1.1005, 1.1010];
      mockRedisClient.get.mockResolvedValue(JSON.stringify(data));

      // Act
      const result = await cacheService.getTechnicalIndicator(symbol, indicator, period);

      // Assert
      const expectedKey = `indicator:${symbol}:${indicator}:${period}`;
      expect(mockRedisClient.get).toHaveBeenCalledWith(expectedKey);
      expect(result).toEqual(data);
    });
  });

  describe('backtest results caching', () => {
    it('should cache backtest results', async () => {
      // Arrange
      const backtestId = 'backtest_123';
      const results = {
        metrics: { total_trades: 10, win_rate: 0.6 },
        trades: [],
        balance_curve: []
      };
      mockRedisClient.set.mockResolvedValue('OK');

      // Act
      await cacheService.cacheBacktestResults(backtestId, results);

      // Assert
      const expectedKey = `backtest:${backtestId}`;
      expect(mockRedisClient.set).toHaveBeenCalledWith(
        expectedKey,
        JSON.stringify(results),
        { EX: 86400 } // 24 hours TTL
      );
    });

    it('should retrieve cached backtest results', async () => {
      // Arrange
      const backtestId = 'backtest_123';
      const results = {
        metrics: { total_trades: 10, win_rate: 0.6 },
        trades: [],
        balance_curve: []
      };
      mockRedisClient.get.mockResolvedValue(JSON.stringify(results));

      // Act
      const result = await cacheService.getBacktestResults(backtestId);

      // Assert
      const expectedKey = `backtest:${backtestId}`;
      expect(mockRedisClient.get).toHaveBeenCalledWith(expectedKey);
      expect(result).toEqual(results);
    });
  });

  describe('cache invalidation', () => {
    it('should invalidate market data by pattern', async () => {
      // Arrange
      const symbol = 'EURUSD';
      const keys = [`market_data:${symbol}:1h`, `market_data:${symbol}:4h`];
      mockRedisClient.keys.mockResolvedValue(keys);
      mockRedisClient.del.mockResolvedValue(keys.length);

      // Act
      const deletedCount = await cacheService.invalidateMarketData(symbol);

      // Assert
      expect(mockRedisClient.keys).toHaveBeenCalledWith(`market_data:${symbol}:*`);
      expect(mockRedisClient.del).toHaveBeenCalledWith(...keys);
      expect(deletedCount).toBe(keys.length);
    });

    it('should invalidate technical indicators by pattern', async () => {
      // Arrange
      const symbol = 'EURUSD';
      const keys = [`indicator:${symbol}:sma:20`, `indicator:${symbol}:rsi:14`];
      mockRedisClient.keys.mockResolvedValue(keys);
      mockRedisClient.del.mockResolvedValue(keys.length);

      // Act
      const deletedCount = await cacheService.invalidateTechnicalIndicators(symbol);

      // Assert
      expect(mockRedisClient.keys).toHaveBeenCalledWith(`indicator:${symbol}:*`);
      expect(mockRedisClient.del).toHaveBeenCalledWith(...keys);
      expect(deletedCount).toBe(keys.length);
    });

    it('should clear all cache', async () => {
      // Arrange
      mockRedisClient.flushall.mockResolvedValue('OK');

      // Act
      await cacheService.clearAll();

      // Assert
      expect(mockRedisClient.flushall).toHaveBeenCalled();
    });
  });

  describe('cache warming', () => {
    it('should warm cache with market data', async () => {
      // Arrange
      const symbols = ['EURUSD', 'GBPUSD'];
      const timeframes = ['1h', '4h'];
      mockRedisClient.set.mockResolvedValue('OK');

      // Mock market data service
      const mockMarketDataService = {
        fetchHistoricalData: jest.fn()
      };
      (mockMarketDataService.fetchHistoricalData as jest.Mock).mockResolvedValue([
        { timestamp: new Date(), open: 1.1000, high: 1.1010, low: 1.0990, close: 1.1005 }
      ]);

      // Act
      await cacheService.warmMarketDataCache(symbols, timeframes, mockMarketDataService as any);

      // Assert
      expect(mockMarketDataService.fetchHistoricalData).toHaveBeenCalledTimes(symbols.length * timeframes.length);
      expect(mockRedisClient.set).toHaveBeenCalledTimes(symbols.length * timeframes.length);
    });
  });

  describe('error handling', () => {
    it('should handle Redis errors gracefully', async () => {
      // Arrange
      const key = 'test:error';
      mockRedisClient.get.mockRejectedValue(new Error('Redis error'));

      // Act & Assert
      await expect(cacheService.get(key)).rejects.toThrow('Redis error');
    });

    it('should handle JSON parsing errors', async () => {
      // Arrange
      const key = 'test:invalid-json';
      mockRedisClient.get.mockResolvedValue('invalid json');

      // Act
      const result = await cacheService.getJSON(key);

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('cache statistics', () => {
    it('should track cache hits and misses', async () => {
      // Arrange
      const key = 'test:stats';
      mockRedisClient.get.mockResolvedValueOnce('cached value').mockResolvedValueOnce(null);

      // Act
      await cacheService.get(key); // Hit
      await cacheService.get(key); // Miss
      const stats = cacheService.getStats();

      // Assert
      expect(stats.hits).toBe(1);
      expect(stats.misses).toBe(1);
      expect(stats.hitRate).toBe(0.5);
    });

    it('should reset cache statistics', () => {
      // Act
      cacheService.resetStats();
      const stats = cacheService.getStats();

      // Assert
      expect(stats.hits).toBe(0);
      expect(stats.misses).toBe(0);
      expect(stats.hitRate).toBe(0);
    });
  });
});