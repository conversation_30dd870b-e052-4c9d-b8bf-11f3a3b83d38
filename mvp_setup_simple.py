#!/usr/bin/env python3
"""
Simple MVP Setup Script - TDD Approach

This script provides a one-command setup for the MVP version of the trading platform.
It focuses on getting the core functionality running quickly with minimal dependencies.

Key Features:
- Single command setup
- Minimal dependencies
- Quick validation
- Clear error messages
- TDD-focused structure

Usage:
    python mvp_setup_simple.py [--test-only] [--verbose]
"""

import sys
import os
import subprocess
import logging
from pathlib import Path
from typing import List, Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("mvp_setup")


class MVPSetup:
    """Simple MVP setup manager"""
    
    def __init__(self, verbose=False):
        self.verbose = verbose
        self.project_root = Path(__file__).parent
        self.errors = []
        self.warnings = []
    
    def check_python_version(self) -> bool:
        """Check if Python version is compatible"""
        logger.info("Checking Python version...")
        
        version = sys.version_info
        if version.major != 3 or version.minor < 8:
            self.errors.append(f"Python 3.8+ required, found {version.major}.{version.minor}")
            return False
        
        logger.info(f"✓ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    
    def check_virtual_environment(self) -> bool:
        """Check if running in virtual environment"""
        logger.info("Checking virtual environment...")
        
        if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
            logger.info("✓ Running in virtual environment")
            return True
        else:
            self.warnings.append("Not running in virtual environment (recommended)")
            return True
    
    def install_dependencies(self) -> bool:
        """Install MVP dependencies"""
        logger.info("Installing MVP dependencies...")
        
        requirements_file = self.project_root / "requirements-mvp.txt"
        
        if not requirements_file.exists():
            self.errors.append(f"Requirements file not found: {requirements_file}")
            return False
        
        try:
            # Install dependencies
            cmd = [sys.executable, "-m", "pip", "install", "-r", str(requirements_file)]
            if not self.verbose:
                cmd.append("--quiet")
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                self.errors.append(f"Failed to install dependencies: {result.stderr}")
                return False
            
            logger.info("✓ Dependencies installed successfully")
            return True
            
        except Exception as e:
            self.errors.append(f"Error installing dependencies: {e}")
            return False
    
    def create_directory_structure(self) -> bool:
        """Create necessary directory structure"""
        logger.info("Creating directory structure...")
        
        directories = [
            "src",
            "src/trading",
            "tests",
            "tests/mvp",
            "logs",
            "data"
        ]
        
        try:
            for directory in directories:
                dir_path = self.project_root / directory
                dir_path.mkdir(parents=True, exist_ok=True)
                
                # Create __init__.py files for Python packages
                if directory.startswith("src") or directory.startswith("tests"):
                    init_file = dir_path / "__init__.py"
                    if not init_file.exists():
                        init_file.touch()
            
            logger.info("✓ Directory structure created")
            return True
            
        except Exception as e:
            self.errors.append(f"Error creating directories: {e}")
            return False
    
    def create_basic_config(self) -> bool:
        """Create basic configuration files"""
        logger.info("Creating basic configuration...")
        
        try:
            # Create .env file if it doesn't exist
            env_file = self.project_root / ".env"
            if not env_file.exists():
                env_content = """# MVP Configuration
DATABASE_URL=sqlite:///./mvp_trading.db
LOG_LEVEL=INFO
OFFLINE_MODE=true
"""
                env_file.write_text(env_content)
                logger.info("✓ Created .env file")
            
            # Create pytest.ini if it doesn't exist
            pytest_ini = self.project_root / "pytest.ini"
            if not pytest_ini.exists():
                pytest_content = """[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
markers =
    unit: Unit tests
    integration: Integration tests
    live: Tests requiring live connections (skip in offline mode)
addopts = -v --tb=short
"""
                pytest_ini.write_text(pytest_content)
                logger.info("✓ Created pytest.ini")
            
            return True
            
        except Exception as e:
            self.errors.append(f"Error creating configuration: {e}")
            return False
    
    def validate_setup(self) -> bool:
        """Validate the MVP setup"""
        logger.info("Validating MVP setup...")
        
        try:
            # Check if we can import key modules
            import fastapi
            import uvicorn
            import sqlalchemy
            import pytest
            import pandas
            import numpy
            
            logger.info("✓ Core dependencies are importable")
            
            # Check if test files exist
            test_files = [
                "tests/mvp/test_core_trading.py",
                "tests/mvp/test_mt5_integration.py",
                "tests/mvp/test_api_endpoints.py",
                "tests/mvp/test_ui_integration.py"
            ]
            
            missing_tests = []
            for test_file in test_files:
                if not (self.project_root / test_file).exists():
                    missing_tests.append(test_file)
            
            if missing_tests:
                self.warnings.append(f"Missing test files: {', '.join(missing_tests)}")
            else:
                logger.info("✓ All MVP test files exist")
            
            return True
            
        except ImportError as e:
            self.errors.append(f"Failed to import required module: {e}")
            return False
        except Exception as e:
            self.errors.append(f"Validation error: {e}")
            return False
    
    def run_quick_test(self) -> bool:
        """Run a quick test to verify everything works"""
        logger.info("Running quick validation test...")
        
        try:
            # Try to run the MVP test runner
            test_runner = self.project_root / "tests" / "run_mvp_tests.py"
            
            if test_runner.exists():
                cmd = [sys.executable, str(test_runner), "--fast", "--simple"]
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
                
                if result.returncode == 0:
                    logger.info("✓ Quick test passed")
                    return True
                else:
                    self.warnings.append(f"Quick test failed: {result.stderr}")
                    return True  # Don't fail setup for test failures
            else:
                self.warnings.append("MVP test runner not found, skipping quick test")
                return True
                
        except subprocess.TimeoutExpired:
            self.warnings.append("Quick test timed out")
            return True
        except Exception as e:
            self.warnings.append(f"Quick test error: {e}")
            return True
    
    def print_summary(self):
        """Print setup summary"""
        print("\n" + "="*60)
        print("MVP SETUP SUMMARY")
        print("="*60)
        
        if not self.errors:
            print("🎉 MVP setup completed successfully!")
            print("\nNext steps:")
            print("1. Run tests: python tests/run_mvp_tests.py")
            print("2. Start development server: python -m uvicorn backend.app.main:app --reload")
            print("3. Begin TDD development cycle")
        else:
            print("❌ MVP setup failed with errors:")
            for error in self.errors:
                print(f"  • {error}")
        
        if self.warnings:
            print("\n⚠ Warnings:")
            for warning in self.warnings:
                print(f"  • {warning}")
        
        print("\n" + "="*60)


def main():
    """Main entry point for MVP setup"""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Simple MVP Setup for AI Enhanced Trading Platform"
    )
    
    parser.add_argument(
        "--test-only",
        action="store_true",
        help="Only run validation tests, don't install dependencies"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose output"
    )
    
    args = parser.parse_args()
    
    # Create setup manager
    setup = MVPSetup(verbose=args.verbose)
    
    print("🚀 MVP Setup - TDD Approach")
    print("Setting up AI Enhanced Trading Platform MVP...")
    print("-" * 60)
    
    success = True
    
    # Run setup steps
    if not setup.check_python_version():
        success = False
    
    setup.check_virtual_environment()
    
    if not args.test_only:
        if not setup.install_dependencies():
            success = False
        
        if not setup.create_directory_structure():
            success = False
        
        if not setup.create_basic_config():
            success = False
    
    if not setup.validate_setup():
        success = False
    
    if not setup.run_quick_test():
        success = False
    
    # Print summary
    setup.print_summary()
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())