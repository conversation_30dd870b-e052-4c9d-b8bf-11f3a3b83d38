# src/events/event_types.py
from enum import Enum
from typing import Literal

class EventType(str, Enum):
    """Trading platform event types"""
    STRATEGY_EXECUTED = "STRATEGY_EXECUTED"
    MARKET_DATA_RECEIVED = "MARKET_DATA_RECEIVED"
    DGM_EVOLUTION_COMPLETE = "DGM_EVOLUTION_COMPLETE"
    RISK_LIMIT_BREACHED = "RISK_LIMIT_BREACHED"
    POSITION_OPENED = "POSITION_OPENED"
    POSITION_CLOSED = "POSITION_CLOSED"
    MODEL_PREDICTION_MADE = "MODEL_PREDICTION_MADE"

# Type aliases for better type hints
StrategyExecutedType = Literal["STRATEGY_EXECUTED"]
MarketDataReceivedType = Literal["MARKET_DATA_RECEIVED"]
DGMEvolutionCompleteType = Literal["DGM_EVOLUTION_COMPLETE"]
RiskLimitBreachedType = Literal["RISK_LIMIT_BREACHED"]