"""
Dependency Injection Container

A lightweight dependency injection container for managing service
dependencies and improving testability.
"""

import inspect
from typing import Dict, Type, Any, TypeVar, Optional, Callable
from threading import Lock

T = TypeVar('T')

class ServiceRegistration:
    """Service registration information"""
    
    def __init__(self, implementation: Type, singleton: bool = True, factory: Optional[Callable] = None):
        self.implementation = implementation
        self.singleton = singleton
        self.factory = factory
        self.instance = None
        self.lock = Lock()

class DependencyContainer:
    """Lightweight dependency injection container"""
    
    def __init__(self):
        self._services: Dict[Type, ServiceRegistration] = {}
        self._instances: Dict[Type, Any] = {}
        self._lock = Lock()
    
    def register(self, interface: Type[T], implementation: Type[T], singleton: bool = True) -> 'DependencyContainer':
        """
        Register a service implementation
        
        Args:
            interface: The interface/abstract class
            implementation: The concrete implementation
            singleton: Whether to use singleton pattern
        """
        with self._lock:
            self._services[interface] = ServiceRegistration(implementation, singleton)
        return self
    
    def register_factory(self, interface: Type[T], factory: Callable[[], T], singleton: bool = True) -> 'DependencyContainer':
        """
        Register a service factory
        
        Args:
            interface: The interface/abstract class
            factory: Factory function to create instances
            singleton: Whether to use singleton pattern
        """
        with self._lock:
            self._services[interface] = ServiceRegistration(None, singleton, factory)
        return self
    
    def register_instance(self, interface: Type[T], instance: T) -> 'DependencyContainer':
        """
        Register a service instance
        
        Args:
            interface: The interface/abstract class
            instance: The service instance
        """
        with self._lock:
            self._instances[interface] = instance
            # Also register as singleton service
            self._services[interface] = ServiceRegistration(type(instance), True)
            self._services[interface].instance = instance
        return self
    
    def resolve(self, interface: Type[T]) -> T:
        """
        Resolve a service instance
        
        Args:
            interface: The interface/abstract class to resolve
            
        Returns:
            Service instance
            
        Raises:
            ValueError: If service is not registered
        """
        # Check if instance is directly registered
        if interface in self._instances:
            return self._instances[interface]
        
        # Check if service is registered
        if interface not in self._services:
            raise ValueError(f"Service {interface.__name__} is not registered")
        
        registration = self._services[interface]
        
        # Return existing singleton instance
        if registration.singleton and registration.instance is not None:
            return registration.instance
        
        # Create new instance
        with registration.lock:
            # Double-check locking pattern
            if registration.singleton and registration.instance is not None:
                return registration.instance
            
            if registration.factory:
                instance = registration.factory()
            else:
                instance = self._create_instance(registration.implementation)
            
            # Store singleton instance
            if registration.singleton:
                registration.instance = instance
            
            return instance
    
    def _create_instance(self, implementation: Type[T]) -> T:
        """
        Create an instance with dependency injection
        
        Args:
            implementation: The implementation class
            
        Returns:
            Instance with dependencies injected
        """
        # Get constructor signature
        signature = inspect.signature(implementation.__init__)
        parameters = signature.parameters
        
        # Skip 'self' parameter
        param_names = [name for name in parameters.keys() if name != 'self']
        
        if not param_names:
            # No dependencies, create simple instance
            return implementation()
        
        # Resolve dependencies
        kwargs = {}
        for param_name in param_names:
            param = parameters[param_name]
            
            # Get parameter type annotation
            if param.annotation != inspect.Parameter.empty:
                param_type = param.annotation
                
                # Resolve dependency
                try:
                    kwargs[param_name] = self.resolve(param_type)
                except ValueError:
                    # If dependency not found and parameter has default, use default
                    if param.default != inspect.Parameter.empty:
                        kwargs[param_name] = param.default
                    else:
                        raise ValueError(f"Cannot resolve dependency {param_type.__name__} for {implementation.__name__}")
            elif param.default != inspect.Parameter.empty:
                # Use default value if no type annotation
                kwargs[param_name] = param.default
            else:
                raise ValueError(f"Parameter {param_name} in {implementation.__name__} has no type annotation or default value")
        
        return implementation(**kwargs)
    
    def is_registered(self, interface: Type) -> bool:
        """
        Check if a service is registered
        
        Args:
            interface: The interface/abstract class
            
        Returns:
            True if registered, False otherwise
        """
        return interface in self._services or interface in self._instances
    
    def clear(self) -> None:
        """Clear all registrations (useful for testing)"""
        with self._lock:
            self._services.clear()
            self._instances.clear()
    
    def get_registered_services(self) -> Dict[Type, ServiceRegistration]:
        """Get all registered services (for debugging)"""
        return self._services.copy()

# Global container instance
_container = DependencyContainer()

def get_container() -> DependencyContainer:
    """Get the global dependency container"""
    return _container

def register(interface: Type[T], implementation: Type[T], singleton: bool = True) -> DependencyContainer:
    """Register a service in the global container"""
    return _container.register(interface, implementation, singleton)

def register_factory(interface: Type[T], factory: Callable[[], T], singleton: bool = True) -> DependencyContainer:
    """Register a service factory in the global container"""
    return _container.register_factory(interface, factory, singleton)

def register_instance(interface: Type[T], instance: T) -> DependencyContainer:
    """Register a service instance in the global container"""
    return _container.register_instance(interface, instance)

def resolve(interface: Type[T]) -> T:
    """Resolve a service from the global container"""
    return _container.resolve(interface)

def is_registered(interface: Type) -> bool:
    """Check if a service is registered in the global container"""
    return _container.is_registered(interface)

def clear_container() -> None:
    """Clear the global container (useful for testing)"""
    _container.clear()

# Decorator for automatic dependency injection
def inject(func):
    """
    Decorator for automatic dependency injection in functions
    
    Usage:
        @inject
        def my_function(market_data: IMarketDataService, config: IConfigurationService):
            # Dependencies are automatically resolved
            pass
    """
    signature = inspect.signature(func)
    
    def wrapper(*args, **kwargs):
        # Get function parameters
        bound_args = signature.bind_partial(*args, **kwargs)
        
        # Resolve missing dependencies
        for param_name, param in signature.parameters.items():
            if param_name not in bound_args.arguments:
                if param.annotation != inspect.Parameter.empty:
                    try:
                        dependency = resolve(param.annotation)
                        bound_args.arguments[param_name] = dependency
                    except ValueError:
                        # If dependency not found and parameter has default, skip
                        if param.default == inspect.Parameter.empty:
                            raise
        
        return func(**bound_args.arguments)
    
    return wrapper

class ServiceLocator:
    """Service locator pattern implementation"""
    
    def __init__(self, container: DependencyContainer = None):
        self._container = container or get_container()
    
    def get_service(self, interface: Type[T]) -> T:
        """Get a service instance"""
        return self._container.resolve(interface)
    
    def has_service(self, interface: Type) -> bool:
        """Check if service is available"""
        return self._container.is_registered(interface)