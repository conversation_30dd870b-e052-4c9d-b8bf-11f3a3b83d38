"""
<PERSON><PERSON><PERSON> to create the initial database migration
"""

import os
import sys
import subprocess
import logging
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("create_migration")

# Load environment variables
load_dotenv()

def main():
    """Main function"""
    try:
        # Check if DATABASE_URL is set
        if not os.getenv("DATABASE_URL"):
            logger.error("DATABASE_URL environment variable is not set")
            sys.exit(1)
        
        # Create the initial migration
        logger.info("Creating initial migration...")
        subprocess.run(
            ["alembic", "revision", "--autogenerate", "-m", "Initial migration"],
            check=True
        )
        
        logger.info("Migration created successfully")
    except subprocess.CalledProcessError as e:
        logger.error(f"Error creating migration: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()