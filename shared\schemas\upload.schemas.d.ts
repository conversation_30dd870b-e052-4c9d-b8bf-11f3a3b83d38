import { z } from 'zod';
export declare const UploadStatusSchema: z.Z<PERSON><["pending", "uploading", "mapping", "parsing", "validating", "ready", "error"]>;
export type UploadStatus = z.infer<typeof UploadStatusSchema>;
export declare const ColumnMappingTypeSchema: z.Zod<PERSON><["Time", "Open", "High", "Low", "Close", "Volume", "Bid", "Ask", "Ignore"]>;
export type ColumnMappingType = z.infer<typeof ColumnMappingTypeSchema>;
export declare const DataFileUploadSchema: z.ZodObject<{
    id: z.ZodString;
    user_id: z.ZodString;
    filename: z.ZodString;
    original_filename: z.ZodString;
    file_size: z.ZodNumber;
    mime_type: z.ZodString;
    upload_path: z.ZodString;
    symbol: z.ZodOptional<z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>>;
    timeframe: z.ZodOptional<z.ZodString>;
    status: z.ZodEnum<["pending", "uploading", "mapping", "parsing", "validating", "ready", "error"]>;
    progress: z.ZodDefault<z.ZodNumber>;
    column_mapping: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodEnum<["Time", "Open", "High", "Low", "Close", "Volume", "Bid", "Ask", "Ignore"]>>>;
    validation_errors: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    data_quality: z.ZodOptional<z.ZodObject<{
        total_rows: z.ZodNumber;
        valid_rows: z.ZodNumber;
        invalid_rows: z.ZodNumber;
        date_range: z.ZodOptional<z.ZodObject<{
            start: z.ZodDate;
            end: z.ZodDate;
        }, "strip", z.ZodTypeAny, {
            end: Date;
            start: Date;
        }, {
            end: Date;
            start: Date;
        }>>;
        completeness: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        total_rows: number;
        valid_rows: number;
        invalid_rows: number;
        completeness: number;
        date_range?: {
            end: Date;
            start: Date;
        } | undefined;
    }, {
        total_rows: number;
        valid_rows: number;
        invalid_rows: number;
        completeness: number;
        date_range?: {
            end: Date;
            start: Date;
        } | undefined;
    }>>;
    error_message: z.ZodOptional<z.ZodString>;
    error_details: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    created_at: z.ZodDate;
    updated_at: z.ZodDate;
    processed_at: z.ZodOptional<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    status: "error" | "pending" | "uploading" | "mapping" | "parsing" | "validating" | "ready";
    id: string;
    filename: string;
    user_id: string;
    created_at: Date;
    progress: number;
    updated_at: Date;
    original_filename: string;
    file_size: number;
    mime_type: string;
    upload_path: string;
    symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
    timeframe?: string | undefined;
    error_message?: string | undefined;
    column_mapping?: Record<string, "Time" | "Open" | "High" | "Low" | "Close" | "Volume" | "Bid" | "Ask" | "Ignore"> | undefined;
    validation_errors?: string[] | undefined;
    data_quality?: {
        total_rows: number;
        valid_rows: number;
        invalid_rows: number;
        completeness: number;
        date_range?: {
            end: Date;
            start: Date;
        } | undefined;
    } | undefined;
    error_details?: string[] | undefined;
    processed_at?: Date | undefined;
}, {
    status: "error" | "pending" | "uploading" | "mapping" | "parsing" | "validating" | "ready";
    id: string;
    filename: string;
    user_id: string;
    created_at: Date;
    updated_at: Date;
    original_filename: string;
    file_size: number;
    mime_type: string;
    upload_path: string;
    symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
    timeframe?: string | undefined;
    progress?: number | undefined;
    error_message?: string | undefined;
    column_mapping?: Record<string, "Time" | "Open" | "High" | "Low" | "Close" | "Volume" | "Bid" | "Ask" | "Ignore"> | undefined;
    validation_errors?: string[] | undefined;
    data_quality?: {
        total_rows: number;
        valid_rows: number;
        invalid_rows: number;
        completeness: number;
        date_range?: {
            end: Date;
            start: Date;
        } | undefined;
    } | undefined;
    error_details?: string[] | undefined;
    processed_at?: Date | undefined;
}>;
export type DataFileUpload = z.infer<typeof DataFileUploadSchema>;
export declare const CreateUploadRequestSchema: z.ZodObject<{
    filename: z.ZodString;
    file_size: z.ZodNumber;
    mime_type: z.ZodString;
    symbol: z.ZodOptional<z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>>;
    timeframe: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    filename: string;
    file_size: number;
    mime_type: string;
    symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
    timeframe?: string | undefined;
}, {
    filename: string;
    file_size: number;
    mime_type: string;
    symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
    timeframe?: string | undefined;
}>;
export type CreateUploadRequest = z.infer<typeof CreateUploadRequestSchema>;
export declare const ColumnMappingRequestSchema: z.ZodObject<{
    upload_id: z.ZodString;
    mapping: z.ZodRecord<z.ZodString, z.ZodEnum<["Time", "Open", "High", "Low", "Close", "Volume", "Bid", "Ask", "Ignore"]>>;
    symbol: z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>;
    timeframe: z.ZodString;
}, "strip", z.ZodTypeAny, {
    symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
    timeframe: string;
    mapping: Record<string, "Time" | "Open" | "High" | "Low" | "Close" | "Volume" | "Bid" | "Ask" | "Ignore">;
    upload_id: string;
}, {
    symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
    timeframe: string;
    mapping: Record<string, "Time" | "Open" | "High" | "Low" | "Close" | "Volume" | "Bid" | "Ask" | "Ignore">;
    upload_id: string;
}>;
export type ColumnMappingRequest = z.infer<typeof ColumnMappingRequestSchema>;
export declare const FilePreviewSchema: z.ZodObject<{
    headers: z.ZodArray<z.ZodString, "many">;
    sample_rows: z.ZodArray<z.ZodArray<z.ZodString, "many">, "many">;
    detected_delimiter: z.ZodOptional<z.ZodString>;
    detected_encoding: z.ZodOptional<z.ZodString>;
    estimated_rows: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    headers: string[];
    sample_rows: string[][];
    estimated_rows: number;
    detected_delimiter?: string | undefined;
    detected_encoding?: string | undefined;
}, {
    headers: string[];
    sample_rows: string[][];
    estimated_rows: number;
    detected_delimiter?: string | undefined;
    detected_encoding?: string | undefined;
}>;
export type FilePreview = z.infer<typeof FilePreviewSchema>;
export declare const ParsedMarketDataSchema: z.ZodObject<{
    symbol: z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>;
    timeframe: z.ZodString;
    data: z.ZodArray<z.ZodObject<{
        timestamp: z.ZodDate;
        open: z.ZodNumber;
        high: z.ZodNumber;
        low: z.ZodNumber;
        close: z.ZodNumber;
        volume: z.ZodOptional<z.ZodNumber>;
        bid: z.ZodOptional<z.ZodNumber>;
        ask: z.ZodOptional<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        low: number;
        high: number;
        close: number;
        open: number;
        timestamp: Date;
        volume?: number | undefined;
        bid?: number | undefined;
        ask?: number | undefined;
    }, {
        low: number;
        high: number;
        close: number;
        open: number;
        timestamp: Date;
        volume?: number | undefined;
        bid?: number | undefined;
        ask?: number | undefined;
    }>, "many">;
    metadata: z.ZodObject<{
        total_records: z.ZodNumber;
        date_range: z.ZodObject<{
            start: z.ZodDate;
            end: z.ZodDate;
        }, "strip", z.ZodTypeAny, {
            end: Date;
            start: Date;
        }, {
            end: Date;
            start: Date;
        }>;
        completeness: z.ZodNumber;
        gaps: z.ZodOptional<z.ZodArray<z.ZodObject<{
            start: z.ZodDate;
            end: z.ZodDate;
            duration_minutes: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            end: Date;
            duration_minutes: number;
            start: Date;
        }, {
            end: Date;
            duration_minutes: number;
            start: Date;
        }>, "many">>;
    }, "strip", z.ZodTypeAny, {
        date_range: {
            end: Date;
            start: Date;
        };
        completeness: number;
        total_records: number;
        gaps?: {
            end: Date;
            duration_minutes: number;
            start: Date;
        }[] | undefined;
    }, {
        date_range: {
            end: Date;
            start: Date;
        };
        completeness: number;
        total_records: number;
        gaps?: {
            end: Date;
            duration_minutes: number;
            start: Date;
        }[] | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
    timeframe: string;
    metadata: {
        date_range: {
            end: Date;
            start: Date;
        };
        completeness: number;
        total_records: number;
        gaps?: {
            end: Date;
            duration_minutes: number;
            start: Date;
        }[] | undefined;
    };
    data: {
        low: number;
        high: number;
        close: number;
        open: number;
        timestamp: Date;
        volume?: number | undefined;
        bid?: number | undefined;
        ask?: number | undefined;
    }[];
}, {
    symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
    timeframe: string;
    metadata: {
        date_range: {
            end: Date;
            start: Date;
        };
        completeness: number;
        total_records: number;
        gaps?: {
            end: Date;
            duration_minutes: number;
            start: Date;
        }[] | undefined;
    };
    data: {
        low: number;
        high: number;
        close: number;
        open: number;
        timestamp: Date;
        volume?: number | undefined;
        bid?: number | undefined;
        ask?: number | undefined;
    }[];
}>;
export type ParsedMarketData = z.infer<typeof ParsedMarketDataSchema>;
export declare const PythonDataProcessingRequestSchema: z.ZodObject<{
    request_id: z.ZodString;
    upload_id: z.ZodString;
    file_path: z.ZodString;
    column_mapping: z.ZodRecord<z.ZodString, z.ZodEnum<["Time", "Open", "High", "Low", "Close", "Volume", "Bid", "Ask", "Ignore"]>>;
    symbol: z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>;
    timeframe: z.ZodString;
    validation_config: z.ZodObject<{
        require_ohlc: z.ZodDefault<z.ZodBoolean>;
        min_data_points: z.ZodDefault<z.ZodNumber>;
        max_gap_minutes: z.ZodDefault<z.ZodNumber>;
        price_validation: z.ZodObject<{
            min_price: z.ZodDefault<z.ZodNumber>;
            max_price: z.ZodDefault<z.ZodNumber>;
            max_price_change: z.ZodDefault<z.ZodNumber>;
        }, "strip", z.ZodTypeAny, {
            min_price: number;
            max_price: number;
            max_price_change: number;
        }, {
            min_price?: number | undefined;
            max_price?: number | undefined;
            max_price_change?: number | undefined;
        }>;
    }, "strip", z.ZodTypeAny, {
        require_ohlc: boolean;
        min_data_points: number;
        max_gap_minutes: number;
        price_validation: {
            min_price: number;
            max_price: number;
            max_price_change: number;
        };
    }, {
        price_validation: {
            min_price?: number | undefined;
            max_price?: number | undefined;
            max_price_change?: number | undefined;
        };
        require_ohlc?: boolean | undefined;
        min_data_points?: number | undefined;
        max_gap_minutes?: number | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
    timeframe: string;
    request_id: string;
    column_mapping: Record<string, "Time" | "Open" | "High" | "Low" | "Close" | "Volume" | "Bid" | "Ask" | "Ignore">;
    upload_id: string;
    file_path: string;
    validation_config: {
        require_ohlc: boolean;
        min_data_points: number;
        max_gap_minutes: number;
        price_validation: {
            min_price: number;
            max_price: number;
            max_price_change: number;
        };
    };
}, {
    symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
    timeframe: string;
    request_id: string;
    column_mapping: Record<string, "Time" | "Open" | "High" | "Low" | "Close" | "Volume" | "Bid" | "Ask" | "Ignore">;
    upload_id: string;
    file_path: string;
    validation_config: {
        price_validation: {
            min_price?: number | undefined;
            max_price?: number | undefined;
            max_price_change?: number | undefined;
        };
        require_ohlc?: boolean | undefined;
        min_data_points?: number | undefined;
        max_gap_minutes?: number | undefined;
    };
}>;
export type PythonDataProcessingRequest = z.infer<typeof PythonDataProcessingRequestSchema>;
export declare const PythonDataProcessingResponseSchema: z.ZodObject<{
    request_id: z.ZodString;
    success: z.ZodBoolean;
    data: z.ZodOptional<z.ZodObject<{
        symbol: z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>;
        timeframe: z.ZodString;
        data: z.ZodArray<z.ZodObject<{
            timestamp: z.ZodDate;
            open: z.ZodNumber;
            high: z.ZodNumber;
            low: z.ZodNumber;
            close: z.ZodNumber;
            volume: z.ZodOptional<z.ZodNumber>;
            bid: z.ZodOptional<z.ZodNumber>;
            ask: z.ZodOptional<z.ZodNumber>;
        }, "strip", z.ZodTypeAny, {
            low: number;
            high: number;
            close: number;
            open: number;
            timestamp: Date;
            volume?: number | undefined;
            bid?: number | undefined;
            ask?: number | undefined;
        }, {
            low: number;
            high: number;
            close: number;
            open: number;
            timestamp: Date;
            volume?: number | undefined;
            bid?: number | undefined;
            ask?: number | undefined;
        }>, "many">;
        metadata: z.ZodObject<{
            total_records: z.ZodNumber;
            date_range: z.ZodObject<{
                start: z.ZodDate;
                end: z.ZodDate;
            }, "strip", z.ZodTypeAny, {
                end: Date;
                start: Date;
            }, {
                end: Date;
                start: Date;
            }>;
            completeness: z.ZodNumber;
            gaps: z.ZodOptional<z.ZodArray<z.ZodObject<{
                start: z.ZodDate;
                end: z.ZodDate;
                duration_minutes: z.ZodNumber;
            }, "strip", z.ZodTypeAny, {
                end: Date;
                duration_minutes: number;
                start: Date;
            }, {
                end: Date;
                duration_minutes: number;
                start: Date;
            }>, "many">>;
        }, "strip", z.ZodTypeAny, {
            date_range: {
                end: Date;
                start: Date;
            };
            completeness: number;
            total_records: number;
            gaps?: {
                end: Date;
                duration_minutes: number;
                start: Date;
            }[] | undefined;
        }, {
            date_range: {
                end: Date;
                start: Date;
            };
            completeness: number;
            total_records: number;
            gaps?: {
                end: Date;
                duration_minutes: number;
                start: Date;
            }[] | undefined;
        }>;
    }, "strip", z.ZodTypeAny, {
        symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
        timeframe: string;
        metadata: {
            date_range: {
                end: Date;
                start: Date;
            };
            completeness: number;
            total_records: number;
            gaps?: {
                end: Date;
                duration_minutes: number;
                start: Date;
            }[] | undefined;
        };
        data: {
            low: number;
            high: number;
            close: number;
            open: number;
            timestamp: Date;
            volume?: number | undefined;
            bid?: number | undefined;
            ask?: number | undefined;
        }[];
    }, {
        symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
        timeframe: string;
        metadata: {
            date_range: {
                end: Date;
                start: Date;
            };
            completeness: number;
            total_records: number;
            gaps?: {
                end: Date;
                duration_minutes: number;
                start: Date;
            }[] | undefined;
        };
        data: {
            low: number;
            high: number;
            close: number;
            open: number;
            timestamp: Date;
            volume?: number | undefined;
            bid?: number | undefined;
            ask?: number | undefined;
        }[];
    }>>;
    validation_errors: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    warnings: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    processing_stats: z.ZodObject<{
        rows_processed: z.ZodNumber;
        rows_valid: z.ZodNumber;
        rows_invalid: z.ZodNumber;
        processing_time_ms: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        rows_processed: number;
        rows_valid: number;
        rows_invalid: number;
        processing_time_ms: number;
    }, {
        rows_processed: number;
        rows_valid: number;
        rows_invalid: number;
        processing_time_ms: number;
    }>;
    error: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    request_id: string;
    processing_stats: {
        rows_processed: number;
        rows_valid: number;
        rows_invalid: number;
        processing_time_ms: number;
    };
    data?: {
        symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
        timeframe: string;
        metadata: {
            date_range: {
                end: Date;
                start: Date;
            };
            completeness: number;
            total_records: number;
            gaps?: {
                end: Date;
                duration_minutes: number;
                start: Date;
            }[] | undefined;
        };
        data: {
            low: number;
            high: number;
            close: number;
            open: number;
            timestamp: Date;
            volume?: number | undefined;
            bid?: number | undefined;
            ask?: number | undefined;
        }[];
    } | undefined;
    error?: string | undefined;
    validation_errors?: string[] | undefined;
    warnings?: string[] | undefined;
}, {
    success: boolean;
    request_id: string;
    processing_stats: {
        rows_processed: number;
        rows_valid: number;
        rows_invalid: number;
        processing_time_ms: number;
    };
    data?: {
        symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
        timeframe: string;
        metadata: {
            date_range: {
                end: Date;
                start: Date;
            };
            completeness: number;
            total_records: number;
            gaps?: {
                end: Date;
                duration_minutes: number;
                start: Date;
            }[] | undefined;
        };
        data: {
            low: number;
            high: number;
            close: number;
            open: number;
            timestamp: Date;
            volume?: number | undefined;
            bid?: number | undefined;
            ask?: number | undefined;
        }[];
    } | undefined;
    error?: string | undefined;
    validation_errors?: string[] | undefined;
    warnings?: string[] | undefined;
}>;
export type PythonDataProcessingResponse = z.infer<typeof PythonDataProcessingResponseSchema>;
export declare const UploadProgressSchema: z.ZodObject<{
    upload_id: z.ZodString;
    status: z.ZodEnum<["pending", "uploading", "mapping", "parsing", "validating", "ready", "error"]>;
    progress: z.ZodNumber;
    message: z.ZodOptional<z.ZodString>;
    current_step: z.ZodOptional<z.ZodString>;
    estimated_completion: z.ZodOptional<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    status: "error" | "pending" | "uploading" | "mapping" | "parsing" | "validating" | "ready";
    progress: number;
    upload_id: string;
    message?: string | undefined;
    current_step?: string | undefined;
    estimated_completion?: Date | undefined;
}, {
    status: "error" | "pending" | "uploading" | "mapping" | "parsing" | "validating" | "ready";
    progress: number;
    upload_id: string;
    message?: string | undefined;
    current_step?: string | undefined;
    estimated_completion?: Date | undefined;
}>;
export type UploadProgress = z.infer<typeof UploadProgressSchema>;
export declare const DataValidationRuleSchema: z.ZodObject<{
    name: z.ZodString;
    description: z.ZodString;
    rule_type: z.ZodEnum<["required", "range", "format", "relationship"]>;
    parameters: z.ZodRecord<z.ZodString, z.ZodAny>;
    severity: z.ZodEnum<["error", "warning", "info"]>;
}, "strip", z.ZodTypeAny, {
    description: string;
    name: string;
    parameters: Record<string, any>;
    rule_type: "required" | "range" | "format" | "relationship";
    severity: "error" | "warning" | "info";
}, {
    description: string;
    name: string;
    parameters: Record<string, any>;
    rule_type: "required" | "range" | "format" | "relationship";
    severity: "error" | "warning" | "info";
}>;
export type DataValidationRule = z.infer<typeof DataValidationRuleSchema>;
export declare const DataValidationResultSchema: z.ZodObject<{
    rule: z.ZodObject<{
        name: z.ZodString;
        description: z.ZodString;
        rule_type: z.ZodEnum<["required", "range", "format", "relationship"]>;
        parameters: z.ZodRecord<z.ZodString, z.ZodAny>;
        severity: z.ZodEnum<["error", "warning", "info"]>;
    }, "strip", z.ZodTypeAny, {
        description: string;
        name: string;
        parameters: Record<string, any>;
        rule_type: "required" | "range" | "format" | "relationship";
        severity: "error" | "warning" | "info";
    }, {
        description: string;
        name: string;
        parameters: Record<string, any>;
        rule_type: "required" | "range" | "format" | "relationship";
        severity: "error" | "warning" | "info";
    }>;
    passed: z.ZodBoolean;
    affected_rows: z.ZodNumber;
    details: z.ZodOptional<z.ZodArray<z.ZodObject<{
        row_number: z.ZodNumber;
        column: z.ZodOptional<z.ZodString>;
        value: z.ZodOptional<z.ZodAny>;
        message: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        message: string;
        row_number: number;
        value?: any;
        column?: string | undefined;
    }, {
        message: string;
        row_number: number;
        value?: any;
        column?: string | undefined;
    }>, "many">>;
}, "strip", z.ZodTypeAny, {
    rule: {
        description: string;
        name: string;
        parameters: Record<string, any>;
        rule_type: "required" | "range" | "format" | "relationship";
        severity: "error" | "warning" | "info";
    };
    passed: boolean;
    affected_rows: number;
    details?: {
        message: string;
        row_number: number;
        value?: any;
        column?: string | undefined;
    }[] | undefined;
}, {
    rule: {
        description: string;
        name: string;
        parameters: Record<string, any>;
        rule_type: "required" | "range" | "format" | "relationship";
        severity: "error" | "warning" | "info";
    };
    passed: boolean;
    affected_rows: number;
    details?: {
        message: string;
        row_number: number;
        value?: any;
        column?: string | undefined;
    }[] | undefined;
}>;
export type DataValidationResult = z.infer<typeof DataValidationResultSchema>;
export declare const BulkUploadRequestSchema: z.ZodObject<{
    files: z.ZodArray<z.ZodObject<{
        filename: z.ZodString;
        file_size: z.ZodNumber;
        mime_type: z.ZodString;
        symbol: z.ZodOptional<z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>>;
        timeframe: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        filename: string;
        file_size: number;
        mime_type: string;
        symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        timeframe?: string | undefined;
    }, {
        filename: string;
        file_size: number;
        mime_type: string;
        symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        timeframe?: string | undefined;
    }>, "many">;
    default_symbol: z.ZodOptional<z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>>;
    default_timeframe: z.ZodOptional<z.ZodString>;
    processing_config: z.ZodObject<{
        parallel_processing: z.ZodDefault<z.ZodBoolean>;
        validation_level: z.ZodDefault<z.ZodEnum<["strict", "normal", "lenient"]>>;
        auto_mapping: z.ZodDefault<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        parallel_processing: boolean;
        validation_level: "strict" | "normal" | "lenient";
        auto_mapping: boolean;
    }, {
        parallel_processing?: boolean | undefined;
        validation_level?: "strict" | "normal" | "lenient" | undefined;
        auto_mapping?: boolean | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    files: {
        filename: string;
        file_size: number;
        mime_type: string;
        symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        timeframe?: string | undefined;
    }[];
    processing_config: {
        parallel_processing: boolean;
        validation_level: "strict" | "normal" | "lenient";
        auto_mapping: boolean;
    };
    default_symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
    default_timeframe?: string | undefined;
}, {
    files: {
        filename: string;
        file_size: number;
        mime_type: string;
        symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        timeframe?: string | undefined;
    }[];
    processing_config: {
        parallel_processing?: boolean | undefined;
        validation_level?: "strict" | "normal" | "lenient" | undefined;
        auto_mapping?: boolean | undefined;
    };
    default_symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
    default_timeframe?: string | undefined;
}>;
export type BulkUploadRequest = z.infer<typeof BulkUploadRequestSchema>;
export declare const BulkUploadResponseSchema: z.ZodObject<{
    batch_id: z.ZodString;
    uploads: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        user_id: z.ZodString;
        filename: z.ZodString;
        original_filename: z.ZodString;
        file_size: z.ZodNumber;
        mime_type: z.ZodString;
        upload_path: z.ZodString;
        symbol: z.ZodOptional<z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>>;
        timeframe: z.ZodOptional<z.ZodString>;
        status: z.ZodEnum<["pending", "uploading", "mapping", "parsing", "validating", "ready", "error"]>;
        progress: z.ZodDefault<z.ZodNumber>;
        column_mapping: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodEnum<["Time", "Open", "High", "Low", "Close", "Volume", "Bid", "Ask", "Ignore"]>>>;
        validation_errors: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        data_quality: z.ZodOptional<z.ZodObject<{
            total_rows: z.ZodNumber;
            valid_rows: z.ZodNumber;
            invalid_rows: z.ZodNumber;
            date_range: z.ZodOptional<z.ZodObject<{
                start: z.ZodDate;
                end: z.ZodDate;
            }, "strip", z.ZodTypeAny, {
                end: Date;
                start: Date;
            }, {
                end: Date;
                start: Date;
            }>>;
            completeness: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            total_rows: number;
            valid_rows: number;
            invalid_rows: number;
            completeness: number;
            date_range?: {
                end: Date;
                start: Date;
            } | undefined;
        }, {
            total_rows: number;
            valid_rows: number;
            invalid_rows: number;
            completeness: number;
            date_range?: {
                end: Date;
                start: Date;
            } | undefined;
        }>>;
        error_message: z.ZodOptional<z.ZodString>;
        error_details: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        created_at: z.ZodDate;
        updated_at: z.ZodDate;
        processed_at: z.ZodOptional<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        status: "error" | "pending" | "uploading" | "mapping" | "parsing" | "validating" | "ready";
        id: string;
        filename: string;
        user_id: string;
        created_at: Date;
        progress: number;
        updated_at: Date;
        original_filename: string;
        file_size: number;
        mime_type: string;
        upload_path: string;
        symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        timeframe?: string | undefined;
        error_message?: string | undefined;
        column_mapping?: Record<string, "Time" | "Open" | "High" | "Low" | "Close" | "Volume" | "Bid" | "Ask" | "Ignore"> | undefined;
        validation_errors?: string[] | undefined;
        data_quality?: {
            total_rows: number;
            valid_rows: number;
            invalid_rows: number;
            completeness: number;
            date_range?: {
                end: Date;
                start: Date;
            } | undefined;
        } | undefined;
        error_details?: string[] | undefined;
        processed_at?: Date | undefined;
    }, {
        status: "error" | "pending" | "uploading" | "mapping" | "parsing" | "validating" | "ready";
        id: string;
        filename: string;
        user_id: string;
        created_at: Date;
        updated_at: Date;
        original_filename: string;
        file_size: number;
        mime_type: string;
        upload_path: string;
        symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        timeframe?: string | undefined;
        progress?: number | undefined;
        error_message?: string | undefined;
        column_mapping?: Record<string, "Time" | "Open" | "High" | "Low" | "Close" | "Volume" | "Bid" | "Ask" | "Ignore"> | undefined;
        validation_errors?: string[] | undefined;
        data_quality?: {
            total_rows: number;
            valid_rows: number;
            invalid_rows: number;
            completeness: number;
            date_range?: {
                end: Date;
                start: Date;
            } | undefined;
        } | undefined;
        error_details?: string[] | undefined;
        processed_at?: Date | undefined;
    }>, "many">;
    summary: z.ZodObject<{
        total_files: z.ZodNumber;
        successful_uploads: z.ZodNumber;
        failed_uploads: z.ZodNumber;
        total_data_points: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        total_files: number;
        successful_uploads: number;
        failed_uploads: number;
        total_data_points: number;
    }, {
        total_files: number;
        successful_uploads: number;
        failed_uploads: number;
        total_data_points: number;
    }>;
}, "strip", z.ZodTypeAny, {
    batch_id: string;
    uploads: {
        status: "error" | "pending" | "uploading" | "mapping" | "parsing" | "validating" | "ready";
        id: string;
        filename: string;
        user_id: string;
        created_at: Date;
        progress: number;
        updated_at: Date;
        original_filename: string;
        file_size: number;
        mime_type: string;
        upload_path: string;
        symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        timeframe?: string | undefined;
        error_message?: string | undefined;
        column_mapping?: Record<string, "Time" | "Open" | "High" | "Low" | "Close" | "Volume" | "Bid" | "Ask" | "Ignore"> | undefined;
        validation_errors?: string[] | undefined;
        data_quality?: {
            total_rows: number;
            valid_rows: number;
            invalid_rows: number;
            completeness: number;
            date_range?: {
                end: Date;
                start: Date;
            } | undefined;
        } | undefined;
        error_details?: string[] | undefined;
        processed_at?: Date | undefined;
    }[];
    summary: {
        total_files: number;
        successful_uploads: number;
        failed_uploads: number;
        total_data_points: number;
    };
}, {
    batch_id: string;
    uploads: {
        status: "error" | "pending" | "uploading" | "mapping" | "parsing" | "validating" | "ready";
        id: string;
        filename: string;
        user_id: string;
        created_at: Date;
        updated_at: Date;
        original_filename: string;
        file_size: number;
        mime_type: string;
        upload_path: string;
        symbol?: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP" | undefined;
        timeframe?: string | undefined;
        progress?: number | undefined;
        error_message?: string | undefined;
        column_mapping?: Record<string, "Time" | "Open" | "High" | "Low" | "Close" | "Volume" | "Bid" | "Ask" | "Ignore"> | undefined;
        validation_errors?: string[] | undefined;
        data_quality?: {
            total_rows: number;
            valid_rows: number;
            invalid_rows: number;
            completeness: number;
            date_range?: {
                end: Date;
                start: Date;
            } | undefined;
        } | undefined;
        error_details?: string[] | undefined;
        processed_at?: Date | undefined;
    }[];
    summary: {
        total_files: number;
        successful_uploads: number;
        failed_uploads: number;
        total_data_points: number;
    };
}>;
export type BulkUploadResponse = z.infer<typeof BulkUploadResponseSchema>;
//# sourceMappingURL=upload.schemas.d.ts.map