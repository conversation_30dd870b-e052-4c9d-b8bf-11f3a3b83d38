<?xml version="1.0" encoding="utf-8"?><testsuites name="pytest tests"><testsuite name="pytest" errors="0" failures="14" skipped="0" tests="31" time="1.404" timestamp="2025-07-08T16:33:07.784964+01:00" hostname="DESKTOP-JOMI8K7"><testcase classname="tests.test_backtest.TestBacktestEngine" name="test_backtest_returns_comprehensive_metrics" time="0.222" /><testcase classname="tests.test_backtest.TestBacktestEngine" name="test_invalid_data_validation[None]" time="0.001"><failure message="TypeError: BacktestEngine.run() got an unexpected keyword argument 'capital'">tests\test_backtest.py:108: in test_invalid_data_validation
    engine.run(data=invalid_data, strategy=rsi_strategy, capital=10000)
E   TypeError: BacktestEngine.run() got an unexpected keyword argument 'capital'</failure></testcase><testcase classname="tests.test_backtest.TestBacktestEngine" name="test_invalid_data_validation[invalid_data1]" time="0.001"><failure message="TypeError: BacktestEngine.run() got an unexpected keyword argument 'capital'">tests\test_backtest.py:108: in test_invalid_data_validation
    engine.run(data=invalid_data, strategy=rsi_strategy, capital=10000)
E   TypeError: BacktestEngine.run() got an unexpected keyword argument 'capital'</failure></testcase><testcase classname="tests.test_backtest.TestBacktestEngine" name="test_invalid_data_validation[invalid_data2]" time="0.001"><failure message="TypeError: BacktestEngine.run() got an unexpected keyword argument 'capital'">tests\test_backtest.py:108: in test_invalid_data_validation
    engine.run(data=invalid_data, strategy=rsi_strategy, capital=10000)
E   TypeError: BacktestEngine.run() got an unexpected keyword argument 'capital'</failure></testcase><testcase classname="tests.test_backtest.TestBacktestEngine" name="test_invalid_data_validation[invalid_data3]" time="0.001"><failure message="TypeError: BacktestEngine.run() got an unexpected keyword argument 'capital'">tests\test_backtest.py:108: in test_invalid_data_validation
    engine.run(data=invalid_data, strategy=rsi_strategy, capital=10000)
E   TypeError: BacktestEngine.run() got an unexpected keyword argument 'capital'</failure></testcase><testcase classname="tests.test_backtest.TestBacktestEngine" name="test_invalid_data_validation[invalid_data4]" time="0.001"><failure message="TypeError: BacktestEngine.run() got an unexpected keyword argument 'capital'">tests\test_backtest.py:108: in test_invalid_data_validation
    engine.run(data=invalid_data, strategy=rsi_strategy, capital=10000)
E   TypeError: BacktestEngine.run() got an unexpected keyword argument 'capital'</failure></testcase><testcase classname="tests.test_backtest.TestBacktestEngine" name="test_invalid_data_validation[invalid_data5]" time="0.001"><failure message="TypeError: BacktestEngine.run() got an unexpected keyword argument 'capital'">tests\test_backtest.py:108: in test_invalid_data_validation
    engine.run(data=invalid_data, strategy=rsi_strategy, capital=10000)
E   TypeError: BacktestEngine.run() got an unexpected keyword argument 'capital'</failure></testcase><testcase classname="tests.test_backtest.TestBacktestEngine" name="test_invalid_data_validation[invalid_data6]" time="0.001"><failure message="TypeError: BacktestEngine.run() got an unexpected keyword argument 'capital'">tests\test_backtest.py:108: in test_invalid_data_validation
    engine.run(data=invalid_data, strategy=rsi_strategy, capital=10000)
E   TypeError: BacktestEngine.run() got an unexpected keyword argument 'capital'</failure></testcase><testcase classname="tests.test_backtest.TestBacktestEngine" name="test_invalid_data_validation[invalid_data7]" time="0.001"><failure message="TypeError: BacktestEngine.run() got an unexpected keyword argument 'capital'">tests\test_backtest.py:108: in test_invalid_data_validation
    engine.run(data=invalid_data, strategy=rsi_strategy, capital=10000)
E   TypeError: BacktestEngine.run() got an unexpected keyword argument 'capital'</failure></testcase><testcase classname="tests.test_backtest.TestBacktestEngine" name="test_backtest_with_simple_capital_parameter" time="0.001"><failure message="TypeError: BacktestEngine.run() got an unexpected keyword argument 'capital'">tests\test_backtest.py:114: in test_backtest_with_simple_capital_parameter
    result = engine.run(data=sample_data, strategy=rsi_strategy, capital=10000)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
E   TypeError: BacktestEngine.run() got an unexpected keyword argument 'capital'</failure></testcase><testcase classname="tests.test_backtest.TestBacktestEngine" name="test_backtest_with_dict_config" time="0.018" /><testcase classname="tests.test_backtest.TestBacktestEngine" name="test_backtest_config_validation" time="0.001" /><testcase classname="tests.test_backtest.TestBacktestEngine" name="test_trade_execution_with_commission_and_slippage" time="0.006" /><testcase classname="tests.test_backtest.TestBacktestEngine" name="test_equity_curve_generation" time="0.017" /><testcase classname="tests.test_backtest.TestBacktestEngine" name="test_strategy_error_handling" time="0.001"><failure message="TypeError: BacktestEngine.run() got an unexpected keyword argument 'capital'">tests\test_backtest.py:216: in test_strategy_error_handling
    result = engine.run(data=sample_data, strategy=broken_strategy, capital=10000)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
E   TypeError: BacktestEngine.run() got an unexpected keyword argument 'capital'</failure></testcase><testcase classname="tests.test_backtest.TestBacktestEngine" name="test_insufficient_data_for_strategy" time="0.001"><failure message="TypeError: BacktestEngine.run() got an unexpected keyword argument 'capital'">tests\test_backtest.py:231: in test_insufficient_data_for_strategy
    result = engine.run(data=short_data, strategy=rsi_strategy, capital=10000)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
E   TypeError: BacktestEngine.run() got an unexpected keyword argument 'capital'</failure></testcase><testcase classname="tests.test_backtest.TestBacktestEngine" name="test_performance_metrics_calculation" time="0.006" /><testcase classname="tests.test_backtest.TestBacktestEngine" name="test_audit_trail_generation" time="0.001"><failure message="NameError: name 'strategy' is not defined">tests\test_backtest.py:273: in test_audit_trail_generation
    result = engine.run(data=sample_data, strategy=strategy, config=config)
                                                   ^^^^^^^^
E   NameError: name 'strategy' is not defined</failure></testcase><testcase classname="tests.test_backtest.TestBacktestEngine" name="test_position_sizing_limits" time="0.005" /><testcase classname="tests.test_backtest.TestBacktestEngine" name="test_concurrent_backtest_safety" time="0.090" /><testcase classname="tests.test_backtest.TestBacktestEngine" name="test_result_serialization" time="0.029" /><testcase classname="tests.test_backtest.TestRSITradingStrategy" name="test_rsi_strategy_initialization" time="0.001" /><testcase classname="tests.test_backtest.TestRSITradingStrategy" name="test_rsi_signal_generation" time="0.003" /><testcase classname="tests.test_backtest.TestRSITradingStrategy" name="test_rsi_strategy_error_handling" time="0.001" /><testcase classname="tests.test_backtest.TestRSITradingStrategy" name="test_rsi_calculation_accuracy" time="0.002" /><testcase classname="tests.test_backtest.TestRSITradingStrategy" name="test_strategy_hash_consistency" time="0.001" /><testcase classname="tests.test_backtest.TestBacktestIntegration" name="test_end_to_end_backtest_workflow" time="0.010"><failure message="assert -5 &lt;= np.float64(-5.003847341955693)&#10; +  where np.float64(-5.003847341955693) = BacktestMetrics(total_return=np.float64(-0.08744161141643891), annualized_return=np.float64(-0.1744488645291309), shar...loat64(0.42372057916202477), calmar_ratio=np.float64(-0.3616273716336978), sortino_ratio=np.float64(-2.64349103401196)).sharpe_ratio">tests\test_backtest.py:503: in test_end_to_end_backtest_workflow
    assert -5 &lt;= metrics.sharpe_ratio &lt;= 5     # Reasonable Sharpe ratio
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
E   assert -5 &lt;= np.float64(-5.003847341955693)
E    +  where np.float64(-5.003847341955693) = BacktestMetrics(total_return=np.float64(-0.08744161141643891), annualized_return=np.float64(-0.1744488645291309), shar...loat64(0.42372057916202477), calmar_ratio=np.float64(-0.3616273716336978), sortino_ratio=np.float64(-2.64349103401196)).sharpe_ratio</failure></testcase><testcase classname="tests.test_backtest.TestBacktestIntegration" name="test_multiple_strategies_comparison" time="0.021" /><testcase classname="tests.test_backtest.TestBacktestIntegration" name="test_backtest_performance_benchmarking" time="0.088" /><testcase classname="tests.test_backtest.TestBacktestProperties" name="test_equity_curve_monotonic_properties" time="0.001"><failure message="TypeError: BacktestEngine.run() got an unexpected keyword argument 'capital'">tests\test_backtest.py:605: in test_equity_curve_monotonic_properties
    result = engine.run(data=data, strategy=strategy, capital=10000)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
E   TypeError: BacktestEngine.run() got an unexpected keyword argument 'capital'</failure></testcase><testcase classname="tests.test_backtest.TestBacktestProperties" name="test_backtest_invariants_under_various_configs" time="0.015" /></testsuite></testsuites>