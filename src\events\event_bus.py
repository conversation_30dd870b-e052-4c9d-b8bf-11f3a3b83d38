# src/events/event_bus.py
import asyncio
import json
import hashlib
import logging
from typing import Dict, Set, Callable, Any, Optional, List
from datetime import datetime, timedelta
from pydantic import ValidationError
from .event_schemas import TradingEvent, TradingEventSchemas
from .event_types import EventType

logger = logging.getLogger(__name__)

class EventValidationResult:
    def __init__(self, success: bool, data: Optional[Any] = None, errors: Optional[List[str]] = None):
        self.success = success
        self.data = data
        self.errors = errors or []

class EventValidator:
    """Validates trading events using Pydantic schemas"""
    
    def validate(self, event_type: str, event_data: Dict[str, Any]) -> EventValidationResult:
        """Validate event data against schema"""
        try:
            schema_class = TradingEventSchemas.get_schema(event_type)
            if not schema_class:
                return EventValidationResult(
                    success=False, 
                    errors=[f"Unknown event type: {event_type}"]
                )
            
            # Parse and validate the event
            validated_event = schema_class(**event_data)
            return EventValidationResult(success=True, data=validated_event)
            
        except ValidationError as e:
            error_messages = []
            for error in e.errors():
                field_path = '.'.join(str(x) for x in error['loc'])
                error_messages.append(f"{field_path}: {error['msg']}")
            
            return EventValidationResult(
                success=False,
                errors=error_messages
            )
        except Exception as e:
            return EventValidationResult(
                success=False,
                errors=[f"Validation error: {str(e)}"]
            )

class EventBus:
    """Type-safe event bus with validation and idempotency"""
    
    def __init__(self):
        self._subscribers: Dict[str, List[Callable]] = {}
        self._processed_events: Set[str] = set()
        self._validator = EventValidator()
        self._event_history: List[Dict[str, Any]] = []
        self._max_history_size = 1000
        
    async def publish(self, event_data: Dict[str, Any]) -> bool:
        """Publish an event with validation and idempotency"""
        try:
            # Extract event type
            event_type = event_data.get('type')
            if not event_type:
                logger.error("Event missing 'type' field")
                return False
            
            # Generate event ID for idempotency
            event_id = self._generate_event_id(event_data)
            
            # Check idempotency
            if event_id in self._processed_events:
                logger.debug(f"Ignoring duplicate event: {event_id}")
                return False
            
            # Validate event
            validation_result = self._validator.validate(event_type, event_data)
            if not validation_result.success:
                error_msg = f"Event validation failed: {', '.join(validation_result.errors)}"
                logger.error(error_msg)
                raise ValueError(error_msg)
            
            # Mark as processed
            self._processed_events.add(event_id)
            
            # Add to history
            self._add_to_history(event_data, event_id)
            
            # Publish to subscribers
            await self._notify_subscribers(event_type, validation_result.data)
            
            logger.info(f"Event published: {event_type}")
            return True
            
        except Exception as e:
            # Remove from processed if failed
            if 'event_id' in locals():
                self._processed_events.discard(event_id)
            logger.error(f"Failed to publish event: {str(e)}")
            raise
    
    def subscribe(self, event_type: str, handler: Callable) -> None:
        """Subscribe to events of a specific type"""
        if event_type not in self._subscribers:
            self._subscribers[event_type] = []
        
        self._subscribers[event_type].append(handler)
        logger.info(f"Subscribed to event type: {event_type}")
    
    def unsubscribe(self, event_type: str, handler: Callable) -> None:
        """Unsubscribe from events"""
        if event_type in self._subscribers:
            try:
                self._subscribers[event_type].remove(handler)
                logger.info(f"Unsubscribed from event type: {event_type}")
            except ValueError:
                logger.warning(f"Handler not found for event type: {event_type}")
    
    async def _notify_subscribers(self, event_type: str, event_data: Any) -> None:
        """Notify all subscribers of an event"""
        if event_type not in self._subscribers:
            return
        
        # Create tasks for all subscribers
        tasks = []
        for handler in self._subscribers[event_type]:
            try:
                if asyncio.iscoroutinefunction(handler):
                    tasks.append(handler(event_data))
                else:
                    # Run sync handlers in thread pool
                    tasks.append(asyncio.get_event_loop().run_in_executor(
                        None, handler, event_data
                    ))
            except Exception as e:
                logger.error(f"Error creating task for handler: {str(e)}")
        
        # Execute all handlers concurrently
        if tasks:
            try:
                await asyncio.gather(*tasks, return_exceptions=True)
            except Exception as e:
                logger.error(f"Error executing event handlers: {str(e)}")
    
    def _generate_event_id(self, event_data: Dict[str, Any]) -> str:
        """Generate unique ID for event idempotency"""
        if 'id' in event_data and event_data['id']:
            return str(event_data['id'])
        
        # Generate ID from event content
        event_type = event_data.get('type', '')
        timestamp = event_data.get('timestamp', datetime.now().isoformat())
        content_hash = hashlib.md5(
            json.dumps(event_data, sort_keys=True, default=str).encode()
        ).hexdigest()[:10]
        
        return f"{event_type}_{timestamp}_{content_hash}"
    
    def _add_to_history(self, event_data: Dict[str, Any], event_id: str) -> None:
        """Add event to history with size limit"""
        history_entry = {
            'id': event_id,
            'timestamp': datetime.now().isoformat(),
            'event': event_data.copy()
        }
        
        self._event_history.append(history_entry)
        
        # Maintain history size limit
        if len(self._event_history) > self._max_history_size:
            self._event_history = self._event_history[-self._max_history_size:]
    
    def cleanup_processed_events(self, older_than_hours: int = 24) -> int:
        """Clean up old processed events to prevent memory leaks"""
        cutoff_time = datetime.now() - timedelta(hours=older_than_hours)
        
        events_to_remove = []
        for event_id in self._processed_events:
            try:
                # Extract timestamp from event ID if possible
                parts = event_id.split('_')
                if len(parts) >= 2:
                    timestamp_str = parts[1]
                    event_time = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                    if event_time < cutoff_time:
                        events_to_remove.append(event_id)
            except (ValueError, IndexError):
                # Keep event if timestamp extraction fails
                continue
        
        # Remove old events
        for event_id in events_to_remove:
            self._processed_events.discard(event_id)
        
        logger.info(f"Cleaned up {len(events_to_remove)} old processed events")
        return len(events_to_remove)
    
    def get_event_history(self, event_type: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Get event history, optionally filtered by type"""
        history = self._event_history
        
        if event_type:
            history = [
                entry for entry in history 
                if entry['event'].get('type') == event_type
            ]
        
        return history[-limit:] if limit else history
    
    def get_subscriber_count(self, event_type: str) -> int:
        """Get number of subscribers for an event type"""
        return len(self._subscribers.get(event_type, []))
    
    def get_processed_event_count(self) -> int:
        """Get total number of processed events"""
        return len(self._processed_events)
    
    def clear_history(self) -> None:
        """Clear event history"""
        self._event_history.clear()
        logger.info("Event history cleared")
    
    def clear_processed_events(self) -> None:
        """Clear processed events set"""
        self._processed_events.clear()
        logger.info("Processed events cleared")