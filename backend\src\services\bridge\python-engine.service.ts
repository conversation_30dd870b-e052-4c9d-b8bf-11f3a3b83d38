// import { z } from 'zod'; // Not currently used
import axios, { AxiosInstance, AxiosError } from 'axios';
import { EventEmitter } from 'events';
import { Logger } from '@/shared/types';
import { ServiceResponse } from '@/shared/types';
import {
  TradingEngineRequest,
  TradingEngineResponse,
  TradingEngineResponseSchema,
  PythonBacktestRequest,
  PythonBacktestResponse,
  PythonBacktestResponseSchema,
  PythonChatRequest,
  PythonChatResponse,
  PythonChatResponseSchema,
  PythonDataProcessingRequest,
  PythonDataProcessingResponse,
  PythonDataProcessingResponseSchema,
} from '../../../../shared/schemas';

export interface PythonEngineConfig {
  baseUrl: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  apiKey?: string;
}

export interface PythonEngineServiceDependencies {
  logger: Logger;
  config: PythonEngineConfig;
}

/**
 * Service for communicating with the Python AI Trading Engine
 * Handles HTTP requests, WebSocket connections, and error recovery
 */
export class PythonEngineService extends EventEmitter {
  private httpClient!: AxiosInstance;
  private isHealthy: boolean = false;
  private lastHealthCheck: Date | null = null;
  private healthCheckInterval: NodeJS.Timeout | null = null;

  constructor(private dependencies: PythonEngineServiceDependencies) {
    super();
    this.setupHttpClient();
    this.startHealthChecking();
  }

  private setupHttpClient(): void {
    const { config } = this.dependencies;
    
    this.httpClient = axios.create({
      baseURL: config.baseUrl,
      timeout: config.timeout,
      headers: {
        'Content-Type': 'application/json',
        ...(config.apiKey && { 'Authorization': `Bearer ${config.apiKey}` }),
      },
    });

    // Request interceptor for logging
    this.httpClient.interceptors.request.use(
      (config) => {
        this.dependencies.logger.debug('Python engine request', {
          method: config.method?.toUpperCase(),
          url: config.url,
          requestId: config.headers?.['X-Request-ID'],
        });
        return config;
      },
      (error) => {
        this.dependencies.logger.error('Python engine request error', { error: error.message });
        return Promise.reject(error);
      }
    );

    // Response interceptor for logging and error handling
    this.httpClient.interceptors.response.use(
      (response) => {
        this.dependencies.logger.debug('Python engine response', {
          status: response.status,
          requestId: response.config.headers?.['X-Request-ID'],
          responseTime: response.headers['x-response-time'],
        });
        return response;
      },
      (error: AxiosError) => {
        this.dependencies.logger.error('Python engine response error', {
          status: error.response?.status,
          message: error.message,
          requestId: error.config?.headers?.['X-Request-ID'],
        });
        return Promise.reject(this.transformAxiosError(error));
      }
    );
  }

  private transformAxiosError(error: AxiosError): Error {
    if (error.response) {
      // Server responded with error status
      return new Error(`Python engine error: ${error.response.status} - ${error.response.data}`);
    } else if (error.request) {
      // Request made but no response received
      return new Error(`Python engine unreachable: ${error.message}`);
    } else {
      // Something else happened
      return new Error(`Python engine request failed: ${error.message}`);
    }
  }

  /**
   * Send trading command to Python engine
   */
  async sendTradingCommand(request: TradingEngineRequest): Promise<ServiceResponse<TradingEngineResponse>> {
    try {
      const response = await this.httpClient.post('/api/trading/command', request, {
        headers: { 'X-Request-ID': request.request_id },
      });

      const validatedResponse = TradingEngineResponseSchema.parse(response.data);
      
      this.emit('trading_command_sent', { request, response: validatedResponse });
      
      return {
        success: true,
        data: validatedResponse,
      };
    } catch (error) {
      this.dependencies.logger.error('Trading command failed', {
        requestId: request.request_id,
        action: request.action,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return {
        success: false,
        error: {
          code: 'TRADING_ENGINE_ERROR',
          message: 'Failed to execute trading command',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * Submit backtest to Python engine
   */
  async submitBacktest(request: PythonBacktestRequest): Promise<ServiceResponse<PythonBacktestResponse>> {
    try {
      const response = await this.httpClient.post('/api/backtest/submit', request, {
        headers: { 'X-Request-ID': request.request_id },
        timeout: 300000, // 5 minutes for backtest submission
      });

      const validatedResponse = PythonBacktestResponseSchema.parse(response.data);
      
      this.emit('backtest_submitted', { request, response: validatedResponse });
      
      return {
        success: true,
        data: validatedResponse,
      };
    } catch (error) {
      this.dependencies.logger.error('Backtest submission failed', {
        requestId: request.request_id,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return {
        success: false,
        error: {
          code: 'BACKTEST_SUBMISSION_ERROR',
          message: 'Failed to submit backtest',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * Get backtest status from Python engine
   */
  async getBacktestStatus(backtestId: string): Promise<ServiceResponse<{ status: string; progress: number }>> {
    try {
      const response = await this.httpClient.get(`/api/backtest/${backtestId}/status`);
      
      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'BACKTEST_STATUS_ERROR',
          message: 'Failed to get backtest status',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * Send chat query to Python engine
   */
  async sendChatQuery(request: PythonChatRequest): Promise<ServiceResponse<PythonChatResponse>> {
    try {
      const response = await this.httpClient.post('/api/chat/query', request, {
        headers: { 'X-Request-ID': request.request_id },
        timeout: 60000, // 1 minute for chat responses
      });

      const validatedResponse = PythonChatResponseSchema.parse(response.data);
      
      this.emit('chat_query_sent', { request, response: validatedResponse });
      
      return {
        success: true,
        data: validatedResponse,
      };
    } catch (error) {
      this.dependencies.logger.error('Chat query failed', {
        requestId: request.request_id,
        query: request.query.substring(0, 100),
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return {
        success: false,
        error: {
          code: 'CHAT_ENGINE_ERROR',
          message: 'Failed to process chat query',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * Submit data processing request to Python engine
   */
  async processData(request: PythonDataProcessingRequest): Promise<ServiceResponse<PythonDataProcessingResponse>> {
    try {
      const response = await this.httpClient.post('/api/data/process', request, {
        headers: { 'X-Request-ID': request.request_id },
        timeout: 180000, // 3 minutes for data processing
      });

      const validatedResponse = PythonDataProcessingResponseSchema.parse(response.data);
      
      this.emit('data_processing_submitted', { request, response: validatedResponse });
      
      return {
        success: true,
        data: validatedResponse,
      };
    } catch (error) {
      this.dependencies.logger.error('Data processing failed', {
        requestId: request.request_id,
        uploadId: request.upload_id,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return {
        success: false,
        error: {
          code: 'DATA_PROCESSING_ERROR',
          message: 'Failed to process data',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * Health check for Python engine
   */
  async checkHealth(): Promise<ServiceResponse<{ status: string; version: string; uptime: number }>> {
    try {
      const response = await this.httpClient.get('/health', {
        timeout: 5000, // 5 seconds for health check
      });

      this.isHealthy = response.status === 200;
      this.lastHealthCheck = new Date();
      
      this.emit('health_check_completed', { healthy: this.isHealthy, data: response.data });
      
      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      this.isHealthy = false;
      this.lastHealthCheck = new Date();
      
      this.emit('health_check_failed', { error: error instanceof Error ? error.message : 'Unknown error' });
      
      return {
        success: false,
        error: {
          code: 'HEALTH_CHECK_FAILED',
          message: 'Python engine health check failed',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * Get current health status
   */
  getHealthStatus(): { healthy: boolean; lastCheck: Date | null } {
    return {
      healthy: this.isHealthy,
      lastCheck: this.lastHealthCheck,
    };
  }

  /**
   * Start periodic health checking
   */
  private startHealthChecking(): void {
    // Initial health check
    this.checkHealth();
    
    // Schedule periodic health checks every 30 seconds
    this.healthCheckInterval = setInterval(() => {
      this.checkHealth();
    }, 30000);
  }

  /**
   * Generic method to send requests to Python engine
   */
  async sendRequest<T>(endpoint: string, request: any): Promise<T> {
    try {
      const response = await this.httpClient.post(`/api/${endpoint}`, request, {
        headers: { 'X-Request-ID': request.request_id || 'unknown' },
        timeout: 60000, // 1 minute timeout for Darwin requests
      });

      return response.data;
    } catch (error) {
      this.dependencies.logger.error(`Request to ${endpoint} failed`, {
        endpoint,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      throw error;
    }
  }

  /**
   * Stop the service and cleanup resources
   */
  async stop(): Promise<void> {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
    
    this.removeAllListeners();
    this.dependencies.logger.info('Python engine service stopped');
  }

  /**
   * Retry a request with exponential backoff
   * TODO: Use this method for request retry logic
   */
  // private async retryRequest<T>(
  //   operation: () => Promise<T>,
  //   maxAttempts: number = this.dependencies.config.retryAttempts
  // ): Promise<T> {
  //   let lastError: Error;
  //   
  //   for (let attempt = 1; attempt <= maxAttempts; attempt++) {
  //     try {
  //       return await operation();
  //     } catch (error) {
  //       lastError = error as Error;
  //       
  //       if (attempt === maxAttempts) {
  //         break;
  //       }
  //       
  //       const delay = this.dependencies.config.retryDelay * Math.pow(2, attempt - 1);
  //       this.dependencies.logger.warn(`Request failed, retrying in ${delay}ms`, {
  //         attempt,
  //         maxAttempts,
  //         error: error instanceof Error ? error.message : 'Unknown error',
  //       });
  //       
  //       await new Promise(resolve => setTimeout(resolve, delay));
  //     }
  //   }
  //   
  //   throw lastError!;
  // }
}