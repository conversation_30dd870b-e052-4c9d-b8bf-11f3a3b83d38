# AI Enhanced Trading Platform - MVP Setup Guide

This guide provides instructions for setting up a minimal viable product (MVP) version of the AI Enhanced Trading Platform.

## Prerequisites

- Python 3.11+ (Python 3.13 is compatible)
- PostgreSQL database (Neon DB recommended)
- Node.js 18+ (optional, for frontend)

## Setup Steps

### 1. Create and Activate Virtual Environment

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate
```

### 2. Install Core Dependencies

```bash
# Install core Python packages
pip install fastapi uvicorn pydantic python-dotenv sqlalchemy alembic

# Install data science packages
pip install numpy pandas scikit-learn matplotlib seaborn

# Install database drivers
pip install psycopg2-binary

# Install authentication packages
pip install python-jose pyjwt passlib bcrypt
```

### 3. Configure Database Connection

Edit the `.env` file in the `backend` directory:

```
# Database Configuration
# Using PostgreSQL with Neon DB
DATABASE_URL=****************************************************************

# Using SQLite for local development (alternative)
# DATABASE_URL=sqlite:///./app.db

# JWT Configuration
JWT_SECRET=your_secret_key_here
JWT_EXPIRES_IN=3600  # 1 hour
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Server Configuration
PORT=8000
DEBUG=True
```

Replace the PostgreSQL connection string with your actual Neon DB credentials.

### 4. Run the Minimal Server

For the simplest possible setup without database dependencies:

```bash
# Run the minimal server
python backend/minimal_server.py
```

This will start a FastAPI server with mock data for testing.

### 5. Database Setup (Optional)

If you want to use the database:

```bash
# Install Alembic
pip install alembic

# Run database setup script
python backend/setup_db.py
```

### 6. Run the Full Server (Optional)

If you've set up the database:

```bash
# Run the server
python backend/server.py
```

## API Endpoints

The minimal server provides the following endpoints:

- `GET /health` - Health check
- `GET /api/strategies` - List all strategies
- `GET /api/strategies/{id}` - Get strategy by ID
- `POST /api/strategies` - Create a new strategy
- `GET /api/backtests` - List all backtests
- `GET /api/backtests/{id}` - Get backtest by ID
- `POST /api/backtests` - Create a new backtest

## Troubleshooting

### Common Issues

1. **Database Connection Errors**:
   - Check your DATABASE_URL in the .env file
   - Ensure your Neon DB is accessible from your network
   - Try using SQLite for local testing

2. **Package Installation Errors**:
   - For packages with C extensions (like TA-Lib), you may need to install pre-built wheels or skip them for the MVP
   - Try using `pip install --only-binary :all: <package-name>` for problematic packages

3. **Circular Import Errors**:
   - Use the minimal_server.py which doesn't have circular imports
   - If you need database access, use the setup_db.py script to initialize the database

## Next Steps

After getting the MVP running:

1. Connect the frontend (if needed)
2. Add more trading strategies
3. Implement real-time data feeds
4. Set up authentication
5. Add backtesting capabilities