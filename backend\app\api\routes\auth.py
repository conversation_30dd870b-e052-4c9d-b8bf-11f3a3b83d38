"""
Authentication routes
"""

import logging
import uuid
from fastapi import APIRouter, HTTPException, Depends, status, Request
from fastapi.security import OAuth2PasswordRequestForm
from pydantic import BaseModel, EmailStr, Field
from typing import Optional
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession

from ...auth.auth_handler import authenticate_user, create_user, create_token_for_user
from ...auth.jwt_handler import JWTBearer
from ...db.config import get_db

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("auth_routes")

# Create router
router = APIRouter()

# Request/Response models
class UserLogin(BaseModel):
    """User login model"""
    email: EmailStr
    password: str

class UserRegister(BaseModel):
    """User register model"""
    email: EmailStr
    password: str
    full_name: str

class UserResponse(BaseModel):
    """User response model"""
    id: uuid.UUID
    email: EmailStr
    full_name: str
    role: str
    created_at: datetime
    last_login: Optional[datetime] = None

class TokenResponse(BaseModel):
    """Token response model"""
    access_token: str
    token_type: str
    user: UserResponse

# Routes
@router.post("/login", response_model=TokenResponse)
async def login(form_data: OAuth2PasswordRequestForm = Depends(), db: AsyncSession = Depends(get_db)):
    """Login route"""
    user = await authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token, user_data = create_token_for_user(user)
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user": {
            "id": user.id,
            "email": user.email,
            "full_name": user.full_name,
            "role": user.role,
            "created_at": user.created_at,
            "last_login": user.last_login
        }
    }

@router.post("/register", response_model=UserResponse)
async def register(user_data: UserRegister, db: AsyncSession = Depends(get_db)):
    """Register route"""
    try:
        user = await create_user(
            db,
            email=user_data.email,
            password=user_data.password,
            full_name=user_data.full_name
        )
        
        return {
            "id": user.id,
            "email": user.email,
            "full_name": user.full_name,
            "role": user.role,
            "created_at": user.created_at,
            "last_login": user.last_login
        }
    except Exception as e:
        logger.error(f"Error creating user: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User with this email already exists",
        )

@router.get("/me", response_model=UserResponse)
async def get_current_user(request: Request, token: str = Depends(JWTBearer())):
    """Get current user route"""
    # The user is already added to request.state by JWTBearer
    user = request.state.user
    
    return {
        "id": user.id,
        "email": user.email,
        "full_name": user.full_name,
        "role": user.role,
        "created_at": user.created_at,
        "last_login": user.last_login
    }