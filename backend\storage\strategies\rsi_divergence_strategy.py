"""
Advanced RSI Divergence Strategy

This strategy identifies RSI divergence patterns to generate trading signals.
It looks for price-RSI divergence to spot potential reversals.
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import logging
import time
import sys
import os

# Add the parent directory to the path to import the MT5 client
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from app.mt5_bridge.mt5_client import MT5Client

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RSIDivergenceStrategy:
    """
    RSI Divergence Strategy for trend reversals
    """
    
    def __init__(self, 
                 client: MT5Client,
                 symbol: str = "EURUSD",
                 timeframe: str = "H4",
                 rsi_period: int = 14,
                 rsi_overbought: int = 70,
                 rsi_oversold: int = 30,
                 divergence_lookback: int = 5,
                 lot_size: float = 0.1,
                 stop_loss_pips: int = 70,
                 take_profit_pips: int = 150,
                 magic: int = 23456):
        """
        Initialize the strategy
        """
        self.client = client
        self.symbol = symbol
        self.timeframe = timeframe
        self.rsi_period = rsi_period
        self.rsi_overbought = rsi_overbought
        self.rsi_oversold = rsi_oversold
        self.divergence_lookback = divergence_lookback
        self.lot_size = lot_size
        self.stop_loss_pips = stop_loss_pips
        self.take_profit_pips = take_profit_pips
        self.magic = magic
        
        # Get symbol info to calculate pips
        symbol_info = self.client.get_symbol_info(symbol)
        self.point = symbol_info.get("point", 0.00001)
        self.digits = symbol_info.get("digits", 5)
        self.pip_value = 10 ** (-self.digits + 1) if self.digits >= 4 else 0.01
        
        # State variables
        self.last_signal = None
        self.positions = []
    
    def calculate_rsi(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate RSI indicator
        """
        delta = data['close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        
        avg_gain = gain.rolling(window=self.rsi_period).mean()
        avg_loss = loss.rolling(window=self.rsi_period).mean()
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        data['rsi'] = rsi
        return data
    
    def check_divergence(self, data: pd.DataFrame) -> str:
        """
        Check for bullish and bearish divergence
        """
        if len(data) < self.rsi_period + self.divergence_lookback:
            return None
            
        # Get recent price swings
        recent_data = data.iloc[-self.divergence_lookback-1:-1]
        
        # Find local price highs and lows
        price_highs = []
        price_lows = []
        rsi_at_price_highs = []
        rsi_at_price_lows = []
        
        for i in range(1, len(recent_data) - 1):
            # Check for price high
            if (recent_data.iloc[i]['high'] > recent_data.iloc[i-1]['high'] and 
                recent_data.iloc[i]['high'] > recent_data.iloc[i+1]['high']):
                price_highs.append(recent_data.iloc[i]['high'])
                rsi_at_price_highs.append(recent_data.iloc[i]['rsi'])
            
            # Check for price low
            if (recent_data.iloc[i]['low'] < recent_data.iloc[i-1]['low'] and 
                recent_data.iloc[i]['low'] < recent_data.iloc[i+1]['low']):
                price_lows.append(recent_data.iloc[i]['low'])
                rsi_at_price_lows.append(recent_data.iloc[i]['rsi'])
        
        # Check for bearish divergence (price making higher highs, RSI making lower highs)
        if len(price_highs) >= 2 and len(rsi_at_price_highs) >= 2:
            if price_highs[-1] > price_highs[-2] and rsi_at_price_highs[-1] < rsi_at_price_highs[-2]:
                if data.iloc[-1]['rsi'] > self.rsi_overbought:
                    return "sell"
        
        # Check for bullish divergence (price making lower lows, RSI making higher lows)
        if len(price_lows) >= 2 and len(rsi_at_price_lows) >= 2:
            if price_lows[-1] < price_lows[-2] and rsi_at_price_lows[-1] > rsi_at_price_lows[-2]:
                if data.iloc[-1]['rsi'] < self.rsi_oversold:
                    return "buy"
        
        return None
    
    def get_historical_data(self) -> pd.DataFrame:
        """
        Get historical data for analysis
        """
        # Get data from MT5
        bars = self.client.get_historical_data(
            symbol=self.symbol,
            timeframe=self.timeframe,
            count=self.rsi_period + self.divergence_lookback + 20  # Get enough bars for indicators
        )
        
        if not bars:
            logger.error(f"No historical data available for {self.symbol}")
            return pd.DataFrame()
        
        # Convert to DataFrame
        df = pd.DataFrame(bars)
        
        # Calculate RSI
        df = self.calculate_rsi(df)
        
        return df
    
    def calculate_stop_loss(self, order_type: str, price: float) -> float:
        """
        Calculate stop loss level
        """
        if order_type == "buy":
            return price - self.stop_loss_pips * self.pip_value
        else:
            return price + self.stop_loss_pips * self.pip_value
    
    def calculate_take_profit(self, order_type: str, price: float) -> float:
        """
        Calculate take profit level
        """
        if order_type == "buy":
            return price + self.take_profit_pips * self.pip_value
        else:
            return price - self.take_profit_pips * self.pip_value
    
    def execute_trade(self, signal: str) -> bool:
        """
        Execute a trade based on the signal
        """
        if not signal:
            return False
        
        # Check if we already have a position in the same direction
        positions = self.client.get_positions()
        for pos in positions:
            if pos["symbol"] == self.symbol and pos["type"] == signal:
                logger.info(f"Already have a {signal} position open for {self.symbol}")
                return False
        
        # Get current price
        prices = self.client.get_symbol_price(self.symbol)
        if not prices:
            logger.error(f"Failed to get price for {self.symbol}")
            return False
        
        # Set price based on order type
        price = prices["ask"] if signal == "buy" else prices["bid"]
        
        # Calculate stop loss and take profit
        stop_loss = self.calculate_stop_loss(signal, price)
        take_profit = self.calculate_take_profit(signal, price)
        
        # Place order
        result = self.client.place_order(
            symbol=self.symbol,
            order_type=signal,
            volume=self.lot_size,
            price=price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            comment="RSI Divergence Strategy",
            magic=self.magic
        )
        
        if result["success"]:
            logger.info(f"Successfully placed {signal} order for {self.symbol} at {price}")
            logger.info(f"Stop Loss: {stop_loss}, Take Profit: {take_profit}")
            return True
        else:
            logger.error(f"Failed to place order: {result['message']}")
            return False
    
    def run(self, once: bool = False) -> None:
        """
        Run the strategy
        """
        logger.info(f"Starting RSI Divergence strategy for {self.symbol}")
        logger.info(f"RSI period: {self.rsi_period}, Overbought: {self.rsi_overbought}, Oversold: {self.rsi_oversold}")
        
        while True:
            try:
                # Get historical data
                data = self.get_historical_data()
                
                # Check for divergence
                signal = self.check_divergence(data)
                
                # If we have a signal
                if signal and signal != self.last_signal:
                    logger.info(f"RSI Divergence signal: {signal}")
                    
                    # Execute trade
                    if self.execute_trade(signal):
                        self.last_signal = signal
                
                # If running just once, exit after checking
                if once:
                    break
                
                # Sleep for a while
                sleep_time = 900  # 15 minutes by default
                if self.timeframe == "M1":
                    sleep_time = 30
                elif self.timeframe == "M5":
                    sleep_time = 60
                elif self.timeframe == "M15":
                    sleep_time = 180
                elif self.timeframe == "M30":
                    sleep_time = 300
                elif self.timeframe == "H1":
                    sleep_time = 600
                elif self.timeframe == "H4":
                    sleep_time = 1800
                
                logger.info(f"Sleeping for {sleep_time} seconds")
                time.sleep(sleep_time)
                
            except Exception as e:
                logger.error(f"Error in strategy execution: {str(e)}")
                if once:
                    break
                time.sleep(60)  # Sleep for a minute before retrying
        
        logger.info("Strategy execution completed")

# Main function to run the strategy
def run_strategy(login: str, password: str, server: str, symbol: str = "EURUSD", 
                 timeframe: str = "H4", rsi_period: int = 14):
    """
    Run the RSI Divergence strategy
    """
    # Create MT5 client
    client = MT5Client()
    
    # Connect to MT5
    if client.connect(login, password, server):
        # Create and run strategy
        strategy = RSIDivergenceStrategy(
            client=client,
            symbol=symbol,
            timeframe=timeframe,
            rsi_period=rsi_period
        )
        
        # Run the strategy
        strategy.run()
        
        # Disconnect from MT5
        client.disconnect()
    else:
        logger.error("Failed to connect to MT5")

# Entry point when run directly
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="RSI Divergence Strategy")
    parser.add_argument("--login", type=str, required=True, help="MT5 login")
    parser.add_argument("--password", type=str, required=True, help="MT5 password")
    parser.add_argument("--server", type=str, required=True, help="MT5 server")
    parser.add_argument("--symbol", type=str, default="EURUSD", help="Symbol to trade")
    parser.add_argument("--timeframe", type=str, default="H4", help="Timeframe to use")
    parser.add_argument("--rsi_period", type=int, default=14, help="RSI period")
    
    args = parser.parse_args()
    
    run_strategy(
        login=args.login,
        password=args.password,
        server=args.server,
        symbol=args.symbol,
        timeframe=args.timeframe,
        rsi_period=args.rsi_period
    )
