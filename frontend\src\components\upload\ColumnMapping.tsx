/**
 * Column Mapping Component
 * Maps uploaded file columns to trading data schema
 */

import { useState, useEffect } from 'react';
import { ArrowRight, Check, X, Globe, AlertTriangle, Info } from 'lucide-react';
import { toast } from 'react-hot-toast';

import type { FileUploadSession } from '@shared/schemas';

interface ColumnMappingProps {
  uploadSession: FileUploadSession;
  onConfirm: (finalMapping: Record<string, string>, timezone: string) => void;
  onCancel: () => void;
}

const TRADING_COLUMNS = [
  { value: 'Time', label: 'Timestamp', required: true, description: 'Date and time of the price data' },
  { value: 'Open', label: 'Open Price', required: false, description: 'Opening price for the period' },
  { value: 'High', label: 'High Price', required: false, description: 'Highest price for the period' },
  { value: 'Low', label: 'Low Price', required: false, description: 'Lowest price for the period' },
  { value: 'Close', label: 'Close Price', required: false, description: 'Closing price for the period' },
  { value: 'Volume', label: 'Volume', required: false, description: 'Trading volume for the period' },
  { value: 'Bid', label: 'Bid Price', required: false, description: 'Bid price (for FX data)' },
  { value: 'Ask', label: 'Ask Price', required: false, description: 'Ask price (for FX data)' },
  { value: 'Ignore', label: 'Ignore Column', required: false, description: 'Skip this column during import' },
];

const TIMEZONES = [
  { value: 'UTC', label: 'UTC (Coordinated Universal Time)' },
  { value: 'America/New_York', label: 'US Eastern Time (EST/EDT)' },
  { value: 'America/Chicago', label: 'US Central Time (CST/CDT)' },
  { value: 'America/Denver', label: 'US Mountain Time (MST/MDT)' },
  { value: 'America/Los_Angeles', label: 'US Pacific Time (PST/PDT)' },
  { value: 'Europe/London', label: 'London Time (GMT/BST)' },
  { value: 'Europe/Paris', label: 'Central European Time (CET/CEST)' },
  { value: 'Asia/Tokyo', label: 'Japan Standard Time (JST)' },
  { value: 'Asia/Shanghai', label: 'China Standard Time (CST)' },
  { value: 'Asia/Hong_Kong', label: 'Hong Kong Time (HKT)' },
  { value: 'Australia/Sydney', label: 'Australian Eastern Time (AET)' },
];

export function ColumnMapping({ uploadSession, onConfirm, onCancel }: ColumnMappingProps) {
  const [mapping, setMapping] = useState<Record<string, string>>({});
  const [timezone, setTimezone] = useState('UTC');
  const [errors, setErrors] = useState<string[]>([]);

  // Initialize mapping from inferred columns
  useEffect(() => {
    const initialMapping = {}; // TODO: Add inferred columns support
    setMapping(initialMapping);
  }, []); // Remove dependency on non-existent property

  // Validate mapping
  useEffect(() => {
    const newErrors: string[] = [];

    // Check if timestamp column is mapped
    const hasTimestamp = Object.values(mapping).includes('Time');
    if (!hasTimestamp) {
      newErrors.push('At least one column must be mapped to "Timestamp"');
    }

    // Check for duplicate mappings (except 'Ignore')
    const nonIgnoreMappings = Object.values(mapping).filter(val => val !== 'Ignore');
    const uniqueMappings = new Set(nonIgnoreMappings);
    if (nonIgnoreMappings.length !== uniqueMappings.size) {
      newErrors.push('Each trading column can only be mapped once');
    }

    // Warn if no price data
    const hasPriceData = Object.values(mapping).some(val => 
      ['Open', 'High', 'Low', 'Close', 'Bid', 'Ask'].includes(val)
    );
    if (!hasPriceData) {
      newErrors.push('No price columns mapped - at least one price column is recommended');
    }

    setErrors(newErrors);
  }, [mapping]);

  const handleMappingChange = (column: string, targetColumn: string) => {
    setMapping(prev => ({
      ...prev,
      [column]: targetColumn,
    }));
  };

  const handleConfirm = () => {
    if (errors.length > 0) {
      toast.error('Please fix the mapping errors before continuing');
      return;
    }

    onConfirm(mapping, timezone);
  };

  const isValid = errors.length === 0;
  const sourceColumns: string[] = []; // TODO: Add inferred columns support

  return (
    <div className="w-full max-w-4xl mx-auto bg-white rounded-lg shadow-lg">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Column Mapping</h2>
            <p className="text-sm text-gray-600 mt-1">
              Map your file columns to our trading data schema
            </p>
          </div>
          <div className="text-sm text-gray-500">
            <span className="font-medium">{uploadSession.original_filename}</span>
            {uploadSession.file_size && (
              <span className="ml-2">
                ({(uploadSession.file_size / (1024 * 1024)).toFixed(1)} MB)
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Timezone Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Globe className="w-4 h-4 inline mr-1" />
            Timestamp Timezone
          </label>
          <select
            value={timezone}
            onChange={(e) => setTimezone(e.target.value)}
            className="w-full max-w-md p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
            {TIMEZONES.map(tz => (
              <option key={tz.value} value={tz.value}>
                {tz.label}
              </option>
            ))}
          </select>
          <p className="text-xs text-gray-500 mt-1">
            Select the timezone of your timestamp data. All data will be converted to UTC for storage.
          </p>
        </div>

        {/* Error Display */}
        {errors.length > 0 && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-start space-x-2">
              <AlertTriangle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-red-900">Please fix the following issues:</h4>
                <ul className="mt-1 text-sm text-red-700 list-disc list-inside">
                  {errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Mapping Table */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <Info className="w-4 h-4" />
            <span>Map each column from your file to the corresponding trading data field</span>
          </div>

          <div className="border border-gray-200 rounded-lg overflow-hidden">
            <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
              <div className="grid grid-cols-12 gap-4 items-center text-sm font-medium text-gray-700">
                <div className="col-span-4">Your Column</div>
                <div className="col-span-1 text-center">→</div>
                <div className="col-span-4">Trading Data Field</div>
                <div className="col-span-3">Preview</div>
              </div>
            </div>

            <div className="divide-y divide-gray-200">
              {sourceColumns.map((column) => {
                const currentMapping = mapping[column] || 'Ignore';
                const targetColumn = TRADING_COLUMNS.find(tc => tc.value === currentMapping);
                
                return (
                  <div key={column} className="px-4 py-3 hover:bg-gray-50">
                    <div className="grid grid-cols-12 gap-4 items-center">
                      {/* Source Column */}
                      <div className="col-span-4">
                        <div className="font-medium text-gray-900">{column}</div>
                        <div className="text-xs text-gray-500">
                          Auto-detected: Unknown
                        </div>
                      </div>

                      {/* Arrow */}
                      <div className="col-span-1 text-center">
                        <ArrowRight className="w-4 h-4 text-gray-400 mx-auto" />
                      </div>

                      {/* Target Selection */}
                      <div className="col-span-4">
                        <select
                          value={currentMapping}
                          onChange={(e) => handleMappingChange(column, e.target.value)}
                          className="w-full p-2 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                        >
                          {TRADING_COLUMNS.map(tc => (
                            <option key={tc.value} value={tc.value}>
                              {tc.label}
                              {tc.required ? ' *' : ''}
                            </option>
                          ))}
                        </select>
                        {targetColumn && (
                          <div className="text-xs text-gray-500 mt-1">
                            {targetColumn.description}
                          </div>
                        )}
                      </div>

                      {/* Preview */}
                      <div className="col-span-3">
                        <div className="text-sm text-gray-600">
                          {currentMapping !== 'Ignore' ? (
                            <span className="inline-flex items-center px-2 py-1 text-xs bg-green-100 text-green-800 rounded">
                              <Check className="w-3 h-3 mr-1" />
                              Mapped
                            </span>
                          ) : (
                            <span className="inline-flex items-center px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
                              <X className="w-3 h-3 mr-1" />
                              Ignored
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Mapping Summary */}
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="text-sm font-medium text-blue-900 mb-2">Mapping Summary</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-blue-700">Mapped columns:</span>
              <span className="ml-1 font-medium">
                {Object.values(mapping).filter(v => v !== 'Ignore').length}
              </span>
            </div>
            <div>
              <span className="text-blue-700">Ignored columns:</span>
              <span className="ml-1 font-medium">
                {Object.values(mapping).filter(v => v === 'Ignore').length}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="px-6 py-4 border-t border-gray-200 bg-gray-50 flex items-center justify-between">
        <button
          onClick={onCancel}
          className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>

        <div className="flex items-center space-x-3">
          <div className="text-sm text-gray-600">
            {isValid ? (
              <span className="text-green-600">✓ Ready to process</span>
            ) : (
              <span className="text-red-600">Please fix errors above</span>
            )}
          </div>
          
          <button
            onClick={handleConfirm}
            disabled={!isValid}
            className="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Confirm Mapping
          </button>
        </div>
      </div>
    </div>
  );
}