"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BulkUploadResponseSchema = exports.BulkUploadRequestSchema = exports.DataValidationResultSchema = exports.DataValidationRuleSchema = exports.UploadProgressSchema = exports.PythonDataProcessingResponseSchema = exports.PythonDataProcessingRequestSchema = exports.ParsedMarketDataSchema = exports.FilePreviewSchema = exports.ColumnMappingRequestSchema = exports.CreateUploadRequestSchema = exports.DataFileUploadSchema = exports.ColumnMappingTypeSchema = exports.UploadStatusSchema = void 0;
const zod_1 = require("zod");
const common_schemas_1 = require("./common.schemas");
const trading_schemas_1 = require("./trading.schemas");
// Upload Status
exports.UploadStatusSchema = zod_1.z.enum([
    'pending', 'uploading', 'mapping', 'parsing', 'validating', 'ready', 'error'
]);
// Column Mapping Types
exports.ColumnMappingTypeSchema = zod_1.z.enum([
    'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'Bid', 'Ask', 'Ignore'
]);
// File Upload Entity
exports.DataFileUploadSchema = zod_1.z.object({
    id: common_schemas_1.IdSchema,
    user_id: common_schemas_1.IdSchema,
    filename: zod_1.z.string().min(1),
    original_filename: zod_1.z.string().min(1),
    file_size: zod_1.z.number().int().nonnegative(),
    mime_type: zod_1.z.string(),
    upload_path: zod_1.z.string(),
    // File metadata
    symbol: trading_schemas_1.TradingSymbolSchema.optional(),
    timeframe: zod_1.z.string().optional(), // e.g., 'M1', 'H1', 'D1'
    // Processing status
    status: exports.UploadStatusSchema,
    progress: zod_1.z.number().min(0).max(100).default(0),
    // Column mapping
    column_mapping: zod_1.z.record(exports.ColumnMappingTypeSchema).optional(),
    // Validation results
    validation_errors: zod_1.z.array(zod_1.z.string()).optional(),
    data_quality: zod_1.z.object({
        total_rows: zod_1.z.number().int().nonnegative(),
        valid_rows: zod_1.z.number().int().nonnegative(),
        invalid_rows: zod_1.z.number().int().nonnegative(),
        date_range: zod_1.z.object({
            start: zod_1.z.date(),
            end: zod_1.z.date(),
        }).optional(),
        completeness: zod_1.z.number().min(0).max(1), // Percentage of complete data
    }).optional(),
    // Error information
    error_message: zod_1.z.string().optional(),
    error_details: zod_1.z.array(zod_1.z.string()).optional(),
    // Timestamps
    created_at: zod_1.z.date(),
    updated_at: zod_1.z.date(),
    processed_at: zod_1.z.date().optional(),
});
// Upload Request
exports.CreateUploadRequestSchema = zod_1.z.object({
    filename: zod_1.z.string().min(1),
    file_size: zod_1.z.number().int().positive(),
    mime_type: zod_1.z.string(),
    symbol: trading_schemas_1.TradingSymbolSchema.optional(),
    timeframe: zod_1.z.string().optional(),
});
// Column Mapping Request
exports.ColumnMappingRequestSchema = zod_1.z.object({
    upload_id: common_schemas_1.IdSchema,
    mapping: zod_1.z.record(exports.ColumnMappingTypeSchema),
    symbol: trading_schemas_1.TradingSymbolSchema,
    timeframe: zod_1.z.string(),
});
// File Preview (first few rows for mapping)
exports.FilePreviewSchema = zod_1.z.object({
    headers: zod_1.z.array(zod_1.z.string()),
    sample_rows: zod_1.z.array(zod_1.z.array(zod_1.z.string())),
    detected_delimiter: zod_1.z.string().optional(),
    detected_encoding: zod_1.z.string().optional(),
    estimated_rows: zod_1.z.number().int().nonnegative(),
});
// Parsed Market Data
exports.ParsedMarketDataSchema = zod_1.z.object({
    symbol: trading_schemas_1.TradingSymbolSchema,
    timeframe: zod_1.z.string(),
    data: zod_1.z.array(zod_1.z.object({
        timestamp: zod_1.z.date(),
        open: zod_1.z.number().positive(),
        high: zod_1.z.number().positive(),
        low: zod_1.z.number().positive(),
        close: zod_1.z.number().positive(),
        volume: zod_1.z.number().nonnegative().optional(),
        bid: zod_1.z.number().positive().optional(),
        ask: zod_1.z.number().positive().optional(),
    })),
    metadata: zod_1.z.object({
        total_records: zod_1.z.number().int().nonnegative(),
        date_range: zod_1.z.object({
            start: zod_1.z.date(),
            end: zod_1.z.date(),
        }),
        completeness: zod_1.z.number().min(0).max(1),
        gaps: zod_1.z.array(zod_1.z.object({
            start: zod_1.z.date(),
            end: zod_1.z.date(),
            duration_minutes: zod_1.z.number().int().positive(),
        })).optional(),
    }),
});
// Python Engine Data Processing
exports.PythonDataProcessingRequestSchema = zod_1.z.object({
    request_id: zod_1.z.string().uuid(),
    upload_id: common_schemas_1.IdSchema,
    file_path: zod_1.z.string(),
    column_mapping: zod_1.z.record(exports.ColumnMappingTypeSchema),
    symbol: trading_schemas_1.TradingSymbolSchema,
    timeframe: zod_1.z.string(),
    validation_config: zod_1.z.object({
        require_ohlc: zod_1.z.boolean().default(true),
        min_data_points: zod_1.z.number().int().positive().default(100),
        max_gap_minutes: zod_1.z.number().int().positive().default(60),
        price_validation: zod_1.z.object({
            min_price: zod_1.z.number().positive().default(0.0001),
            max_price: zod_1.z.number().positive().default(1000000),
            max_price_change: zod_1.z.number().min(0).max(1).default(0.2), // 20%
        }),
    }),
});
exports.PythonDataProcessingResponseSchema = zod_1.z.object({
    request_id: zod_1.z.string().uuid(),
    success: zod_1.z.boolean(),
    data: exports.ParsedMarketDataSchema.optional(),
    validation_errors: zod_1.z.array(zod_1.z.string()).optional(),
    warnings: zod_1.z.array(zod_1.z.string()).optional(),
    processing_stats: zod_1.z.object({
        rows_processed: zod_1.z.number().int().nonnegative(),
        rows_valid: zod_1.z.number().int().nonnegative(),
        rows_invalid: zod_1.z.number().int().nonnegative(),
        processing_time_ms: zod_1.z.number().nonnegative(),
    }),
    error: zod_1.z.string().optional(),
});
// Upload Progress Updates
exports.UploadProgressSchema = zod_1.z.object({
    upload_id: common_schemas_1.IdSchema,
    status: exports.UploadStatusSchema,
    progress: zod_1.z.number().min(0).max(100),
    message: zod_1.z.string().optional(),
    current_step: zod_1.z.string().optional(),
    estimated_completion: zod_1.z.date().optional(),
});
// Data Validation Rules
exports.DataValidationRuleSchema = zod_1.z.object({
    name: zod_1.z.string(),
    description: zod_1.z.string(),
    rule_type: zod_1.z.enum(['required', 'range', 'format', 'relationship']),
    parameters: zod_1.z.record(zod_1.z.any()),
    severity: zod_1.z.enum(['error', 'warning', 'info']),
});
exports.DataValidationResultSchema = zod_1.z.object({
    rule: exports.DataValidationRuleSchema,
    passed: zod_1.z.boolean(),
    affected_rows: zod_1.z.number().int().nonnegative(),
    details: zod_1.z.array(zod_1.z.object({
        row_number: zod_1.z.number().int().positive(),
        column: zod_1.z.string().optional(),
        value: zod_1.z.any().optional(),
        message: zod_1.z.string(),
    })).optional(),
});
// Bulk Upload for Multiple Files
exports.BulkUploadRequestSchema = zod_1.z.object({
    files: zod_1.z.array(exports.CreateUploadRequestSchema).min(1).max(10),
    default_symbol: trading_schemas_1.TradingSymbolSchema.optional(),
    default_timeframe: zod_1.z.string().optional(),
    processing_config: zod_1.z.object({
        parallel_processing: zod_1.z.boolean().default(true),
        validation_level: zod_1.z.enum(['strict', 'normal', 'lenient']).default('normal'),
        auto_mapping: zod_1.z.boolean().default(true),
    }),
});
exports.BulkUploadResponseSchema = zod_1.z.object({
    batch_id: common_schemas_1.IdSchema,
    uploads: zod_1.z.array(exports.DataFileUploadSchema),
    summary: zod_1.z.object({
        total_files: zod_1.z.number().int().nonnegative(),
        successful_uploads: zod_1.z.number().int().nonnegative(),
        failed_uploads: zod_1.z.number().int().nonnegative(),
        total_data_points: zod_1.z.number().int().nonnegative(),
    }),
});
//# sourceMappingURL=upload.schemas.js.map