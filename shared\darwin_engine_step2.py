# darwin_engine_step2.py
# Step 2: Real Forex Data Integration & Advanced Backtesting
# Building on the successful Step 1 foundation

import pandas as pd
import numpy as np
import yfinance as yf
from datetime import datetime, timedelta
import asyncio
import time
import logging
from typing import List, Dict, Any, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

# Import our Step 1 components
from darwin_engine_step1 import (
    TradingStrategy, TradingCondition, RiskManagement, IndicatorType, 
    OperatorType, ActionType, StrategyFactory, StrategyMutator, 
    EvolutionConfig, BasicDarwinEngine
)

logger = logging.getLogger(__name__)

# ============================================================================
# STEP 2: REAL FOREX DATA PROVIDER
# ============================================================================

class ForexDataProvider:
    """Real forex data provider using yfinance and synthetic data"""
    
    def __init__(self):
        self.data_cache = {}
        self.currency_pairs = {
            'EURUSD': 'EURUSD=X',
            'GBPUSD': 'GBPUSD=X', 
            'USDJPY': 'USDJPY=X',
            'AUDUSD': 'AUDUSD=X',
            'USDCAD': 'USDCAD=X',
            'USDCHF': 'USDCHF=X'
        }
    
    def get_forex_data(self, pair: str, period: str = "1y", interval: str = "1h") -> pd.DataFrame:
        """Get real forex data from yfinance"""
        cache_key = f"{pair}_{period}_{interval}"
        
        if cache_key in self.data_cache:
            return self.data_cache[cache_key]
        
        try:
            # Get real data from yfinance
            ticker_symbol = self.currency_pairs.get(pair, f"{pair}=X")
            ticker = yf.Ticker(ticker_symbol)
            
            data = ticker.history(period=period, interval=interval)
            
            if data.empty:
                logger.warning(f"No data found for {pair}, generating synthetic data")
                data = self._generate_synthetic_data(pair, period, interval)
            else:
                logger.info(f"Downloaded {len(data)} data points for {pair}")
                
        except Exception as e:
            logger.warning(f"Error downloading {pair} data: {e}, using synthetic data")
            data = self._generate_synthetic_data(pair, period, interval)
        
        # Add technical indicators
        data = self._add_technical_indicators(data)
        
        # Cache the data
        self.data_cache[cache_key] = data
        
        return data
    
    def _generate_synthetic_data(self, pair: str, period: str, interval: str) -> pd.DataFrame:
        """Generate realistic synthetic forex data when real data unavailable"""
        
        # Determine number of data points
        period_days = {'1mo': 30, '3mo': 90, '6mo': 180, '1y': 365, '2y': 730}
        interval_hours = {'1m': 1/60, '5m': 5/60, '15m': 15/60, '1h': 1, '1d': 24}
        
        days = period_days.get(period, 365)
        hours_per_point = interval_hours.get(interval, 1)
        n_points = int(days * 24 / hours_per_point)
        
        # Generate dates
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        dates = pd.date_range(start=start_date, end=end_date, periods=n_points)
        
        # Base prices for different pairs
        base_prices = {
            'EURUSD': 1.0850, 'GBPUSD': 1.2650, 'USDJPY': 149.50,
            'AUDUSD': 0.6450, 'USDCAD': 1.3650, 'USDCHF': 0.8950
        }
        
        base_price = base_prices.get(pair, 1.0000)
        
        # Generate price using geometric Brownian motion
        dt = 1.0 / 365  # Daily time step
        volatility = 0.12  # Annual volatility
        drift = 0.02  # Annual drift
        
        # Random walk
        returns = np.random.normal(
            drift * dt, 
            volatility * np.sqrt(dt), 
            n_points
        )
        
        # Add realistic forex patterns
        # 1. Mean reversion
        prices = [base_price]
        for i in range(1, n_points):
            mean_reversion = -0.1 * (prices[-1] - base_price) / base_price
            daily_return = returns[i] + mean_reversion * dt
            new_price = prices[-1] * (1 + daily_return)
            prices.append(new_price)
        
        prices = np.array(prices)
        
        # Generate OHLC from prices
        highs = prices * (1 + np.abs(np.random.normal(0, 0.002, n_points)))
        lows = prices * (1 - np.abs(np.random.normal(0, 0.002, n_points)))
        
        # Opens are previous close + small gap
        opens = np.roll(prices, 1)
        opens[0] = prices[0]
        opens += np.random.normal(0, prices * 0.0005)
        
        # Volume (forex doesn't have real volume, so we simulate tick volume)
        volumes = np.random.lognormal(mean=10, sigma=1, size=n_points)
        
        # Create DataFrame
        df = pd.DataFrame({
            'Open': opens,
            'High': highs, 
            'Low': lows,
            'Close': prices,
            'Volume': volumes
        }, index=dates)
        
        logger.info(f"Generated {len(df)} synthetic data points for {pair}")
        return df
    
    def _add_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add technical indicators to the dataframe"""
        
        # RSI
        delta = df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['RSI'] = 100 - (100 / (1 + rs))
        
        # EMAs
        df['EMA_12'] = df['Close'].ewm(span=12).mean()
        df['EMA_26'] = df['Close'].ewm(span=26).mean()
        df['EMA_50'] = df['Close'].ewm(span=50).mean()
        df['EMA_200'] = df['Close'].ewm(span=200).mean()
        
        # SMAs
        df['SMA_20'] = df['Close'].rolling(window=20).mean()
        df['SMA_50'] = df['Close'].rolling(window=50).mean()
        df['SMA_200'] = df['Close'].rolling(window=200).mean()
        
        # MACD
        df['MACD'] = df['EMA_12'] - df['EMA_26']
        df['MACD_Signal'] = df['MACD'].ewm(span=9).mean()
        df['MACD_Histogram'] = df['MACD'] - df['MACD_Signal']
        
        # Bollinger Bands
        df['BB_Middle'] = df['Close'].rolling(window=20).mean()
        bb_std = df['Close'].rolling(window=20).std()
        df['BB_Upper'] = df['BB_Middle'] + (bb_std * 2)
        df['BB_Lower'] = df['BB_Middle'] - (bb_std * 2)
        
        # Stochastic
        low_14 = df['Low'].rolling(window=14).min()
        high_14 = df['High'].rolling(window=14).max()
        df['Stoch_K'] = 100 * (df['Close'] - low_14) / (high_14 - low_14)
        df['Stoch_D'] = df['Stoch_K'].rolling(window=3).mean()
        
        # ADX (simplified)
        df['ADX'] = 25 + 25 * np.sin(np.arange(len(df)) * 0.1)  # Simplified ADX
        
        return df

# ============================================================================
# STEP 2: ADVANCED BACKTESTING ENGINE  
# ============================================================================

class AdvancedBacktester:
    """Advanced backtesting engine with realistic execution modeling"""
    
    def __init__(self, initial_balance: float = 10000):
        self.initial_balance = initial_balance
        self.spread_pips = 2  # 2 pip spread
        self.commission_per_lot = 7  # $7 per standard lot
        
    def backtest_strategy(self, strategy: TradingStrategy, data: pd.DataFrame, 
                         pair: str = "EURUSD") -> Dict[str, float]:
        """Comprehensive backtesting with realistic execution"""
        
        if data.empty or len(data) < 50:
            return {'fitness': 0.0, 'error': 'Insufficient data'}
        
        # Remove NaN values
        data = data.dropna()
        if len(data) < 50:
            return {'fitness': 0.0, 'error': 'Too many NaN values'}
        
        trades = []
        balance = self.initial_balance
        position = None
        equity_curve = [balance]
        
        pip_value = self._get_pip_value(pair)
        
        for i in range(50, len(data)):  # Start after indicators are calculated
            current_bar = data.iloc[i]
            
            # Evaluate strategy conditions
            signal = self._evaluate_conditions(strategy, current_bar, data.iloc[i-20:i])
            
            if signal and not position:
                # Open position
                entry_price = self._get_execution_price(current_bar, strategy.action)
                position_size = self._calculate_position_size(
                    balance, strategy.risk_management, entry_price, pip_value
                )
                
                if position_size > 0:
                    position = {
                        'type': strategy.action.value,
                        'entry_price': entry_price,
                        'position_size': position_size,
                        'entry_bar': i,
                        'stop_loss': self._calculate_stop_loss(entry_price, strategy, pip_value),
                        'take_profit': self._calculate_take_profit(entry_price, strategy, pip_value)
                    }
            
            elif position:
                # Check exit conditions
                exit_price = None
                exit_reason = None
                
                current_price = current_bar['Close']
                
                # Check stop loss
                if ((position['type'] == 'buy' and current_price <= position['stop_loss']) or
                    (position['type'] == 'sell' and current_price >= position['stop_loss'])):
                    exit_price = position['stop_loss']
                    exit_reason = 'stop_loss'
                
                # Check take profit  
                elif ((position['type'] == 'buy' and current_price >= position['take_profit']) or
                      (position['type'] == 'sell' and current_price <= position['take_profit'])):
                    exit_price = position['take_profit']
                    exit_reason = 'take_profit'
                
                # Check for opposite signal
                elif signal and signal != position['type']:
                    exit_price = self._get_execution_price(current_bar, 'close')
                    exit_reason = 'signal_reversal'
                
                if exit_price:
                    # Close position
                    pnl = self._calculate_pnl(position, exit_price, pip_value)
                    balance += pnl
                    
                    trade = {
                        **position,
                        'exit_price': exit_price,
                        'exit_bar': i,
                        'exit_reason': exit_reason,
                        'pnl': pnl,
                        'duration_bars': i - position['entry_bar']
                    }
                    trades.append(trade)
                    position = None
            
            equity_curve.append(balance)
        
        # Close any remaining position
        if position:
            final_price = data.iloc[-1]['Close']
            pnl = self._calculate_pnl(position, final_price, pip_value)
            balance += pnl
            
            trade = {
                **position,
                'exit_price': final_price,
                'exit_bar': len(data) - 1,
                'exit_reason': 'end_of_data',
                'pnl': pnl,
                'duration_bars': len(data) - 1 - position['entry_bar']
            }
            trades.append(trade)
        
        # Calculate performance metrics
        return self._calculate_performance_metrics(trades, equity_curve, balance)
    
    def _evaluate_conditions(self, strategy: TradingStrategy, current_bar: pd.Series, 
                           history: pd.DataFrame) -> Optional[str]:
        """Evaluate if strategy conditions are met"""
        try:
            for condition in strategy.conditions:
                if not self._check_condition(condition, current_bar, history):
                    return None
            
            # All conditions met
            return strategy.action.value
            
        except Exception as e:
            return None
    
    def _check_condition(self, condition: TradingCondition, current_bar: pd.Series,
                        history: pd.DataFrame) -> bool:
        """Check if a single condition is met"""
        
        indicator_name = condition.indicator.value
        
        # Map indicator names to dataframe columns
        indicator_map = {
            'RSI': 'RSI',
            'MACD': 'MACD', 
            'EMA': f'EMA_{condition.period}',
            'SMA': f'SMA_{condition.period}',
            'STOCHASTIC': 'Stoch_K',
            'ADX': 'ADX'
        }
        
        column_name = indicator_map.get(indicator_name)
        if not column_name or column_name not in current_bar.index:
            return False
        
        current_value = current_bar[column_name]
        
        if pd.isna(current_value):
            return False
        
        # Handle different operators
        if condition.operator == OperatorType.GREATER_THAN:
            return current_value > condition.value
        elif condition.operator == OperatorType.LESS_THAN:
            return current_value < condition.value
        elif condition.operator == OperatorType.GREATER_EQUAL:
            return current_value >= condition.value
        elif condition.operator == OperatorType.LESS_EQUAL:
            return current_value <= condition.value
        elif condition.operator == OperatorType.EQUALS:
            return abs(current_value - condition.value) < 0.0001
        elif condition.operator == OperatorType.CROSSOVER:
            # Check if indicator crossed above value
            if len(history) >= 2 and column_name in history.columns:
                prev_value = history.iloc[-2][column_name]
                return prev_value <= condition.value < current_value
        elif condition.operator == OperatorType.CROSSUNDER:
            # Check if indicator crossed below value
            if len(history) >= 2 and column_name in history.columns:
                prev_value = history.iloc[-2][column_name]
                return prev_value >= condition.value > current_value
        
        return False
    
    def _get_execution_price(self, bar: pd.Series, action: str) -> float:
        """Get realistic execution price with spread"""
        close_price = bar['Close']
        spread = close_price * (self.spread_pips * 0.0001)  # Convert pips to price
        
        if action in ['buy', 'close_sell']:
            return close_price + spread  # Pay the ask
        else:
            return close_price - spread  # Receive the bid
    
    def _get_pip_value(self, pair: str) -> float:
        """Get pip value for position sizing"""
        if 'JPY' in pair:
            return 0.01  # JPY pairs
        else:
            return 0.0001  # Other pairs
    
    def _calculate_position_size(self, balance: float, risk_mgmt: RiskManagement,
                               entry_price: float, pip_value: float) -> float:
        """Calculate position size based on risk management"""
        
        # Risk amount in base currency
        risk_amount = balance * risk_mgmt.max_risk_per_trade
        
        # Stop loss distance in pips
        stop_loss_pips = risk_mgmt.stop_loss_pct * 100
        
        # Position size calculation
        # Risk = Position Size * Stop Loss Pips * Pip Value
        # Position Size = Risk / (Stop Loss Pips * Pip Value)
        
        if stop_loss_pips > 0:
            position_size = risk_amount / (stop_loss_pips * pip_value)
        else:
            position_size = balance * 0.01  # 1% of balance as fallback
        
        # Cap position size
        max_position = balance * (risk_mgmt.position_size_pct / 100)
        return min(position_size, max_position)
    
    def _calculate_stop_loss(self, entry_price: float, strategy: TradingStrategy, 
                           pip_value: float) -> float:
        """Calculate stop loss price"""
        stop_loss_pips = strategy.risk_management.stop_loss_pct * 100
        
        if strategy.action == ActionType.BUY:
            return entry_price - (stop_loss_pips * pip_value)
        else:
            return entry_price + (stop_loss_pips * pip_value)
    
    def _calculate_take_profit(self, entry_price: float, strategy: TradingStrategy,
                             pip_value: float) -> float:
        """Calculate take profit price"""
        take_profit_pips = strategy.risk_management.take_profit_pct * 100
        
        if strategy.action == ActionType.BUY:
            return entry_price + (take_profit_pips * pip_value)
        else:
            return entry_price - (take_profit_pips * pip_value)
    
    def _calculate_pnl(self, position: Dict, exit_price: float, pip_value: float) -> float:
        """Calculate profit/loss for a position"""
        entry_price = position['entry_price']
        position_size = position['position_size']
        
        if position['type'] == 'buy':
            pip_difference = (exit_price - entry_price) / pip_value
        else:
            pip_difference = (entry_price - exit_price) / pip_value
        
        pnl = pip_difference * pip_value * position_size
        
        # Subtract commission
        pnl -= self.commission_per_lot * (position_size / 100000)  # Assuming standard lots
        
        return pnl
    
    def _calculate_performance_metrics(self, trades: List[Dict], 
                                     equity_curve: List[float], 
                                     final_balance: float) -> Dict[str, float]:
        """Calculate comprehensive performance metrics"""
        
        if not trades:
            return {
                'fitness': 0.0,
                'total_trades': 0,
                'win_rate': 0.0,
                'total_return_pct': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown_pct': 0.0,
                'profit_factor': 0.0
            }
        
        # Basic metrics
        total_trades = len(trades)
        winning_trades = [t for t in trades if t['pnl'] > 0]
        losing_trades = [t for t in trades if t['pnl'] <= 0]
        
        win_rate = len(winning_trades) / total_trades
        
        total_return_pct = ((final_balance - self.initial_balance) / self.initial_balance) * 100
        
        # Profit factor
        gross_profit = sum(t['pnl'] for t in winning_trades)
        gross_loss = abs(sum(t['pnl'] for t in losing_trades))
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        # Sharpe ratio (simplified)
        returns = [t['pnl'] / self.initial_balance for t in trades]
        if len(returns) > 1:
            mean_return = np.mean(returns)
            std_return = np.std(returns)
            sharpe_ratio = mean_return / std_return if std_return > 0 else 0
        else:
            sharpe_ratio = 0
        
        # Maximum drawdown
        equity_series = np.array(equity_curve)
        running_max = np.maximum.accumulate(equity_series)
        drawdown = (equity_series - running_max) / running_max
        max_drawdown_pct = abs(np.min(drawdown)) * 100
        
        # Calculate fitness score
        fitness = self._calculate_fitness_score(
            total_return_pct, win_rate, sharpe_ratio, max_drawdown_pct, profit_factor
        )
        
        return {
            'fitness': fitness,
            'total_trades': total_trades,
            'win_rate': win_rate,
            'total_return_pct': total_return_pct,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown_pct': max_drawdown_pct,
            'profit_factor': profit_factor,
            'gross_profit': gross_profit,
            'gross_loss': gross_loss,
            'avg_trade_pnl': np.mean([t['pnl'] for t in trades])
        }
    
    def _calculate_fitness_score(self, total_return: float, win_rate: float,
                               sharpe_ratio: float, max_drawdown: float,
                               profit_factor: float) -> float:
        """Calculate overall fitness score"""
        
        # Multi-objective fitness function
        return_component = min(total_return / 20.0, 2.0) if total_return > 0 else 0
        win_rate_component = win_rate
        sharpe_component = min(sharpe_ratio / 2.0, 1.0) if sharpe_ratio > 0 else 0
        drawdown_component = max(0, 1 - max_drawdown / 20.0)  # Penalty for high drawdown
        profit_factor_component = min(profit_factor / 2.0, 1.0) if profit_factor > 1 else 0
        
        # Weighted combination
        fitness = (
            return_component * 0.25 +
            win_rate_component * 0.20 +
            sharpe_component * 0.25 +
            drawdown_component * 0.15 +
            profit_factor_component * 0.15
        )
        
        return max(0.0, fitness)

# ============================================================================
# STEP 2: ENHANCED DARWIN ENGINE WITH REAL DATA
# ============================================================================

class RealDataDarwinEngine(BasicDarwinEngine):
    """Enhanced Darwin Engine with real forex data and backtesting"""
    
    def __init__(self, config: EvolutionConfig = None, pair: str = "EURUSD"):
        super().__init__(config)
        self.pair = pair
        self.data_provider = ForexDataProvider()
        self.backtester = AdvancedBacktester()
        
        # Get market data
        logger.info(f"Loading market data for {pair}...")
        self.market_data = self.data_provider.get_forex_data(pair, period="1y", interval="1h")
        logger.info(f"Loaded {len(self.market_data)} data points")
        
        # Replace mock evaluator with real backtesting
        self.evaluator = None  # We'll use backtester directly
    
    def _evaluate_population(self):
        """Evaluate fitness using real backtesting"""
        logger.info(f"Backtesting {len(self.population)} strategies...")
        
        for i, strategy in enumerate(self.population):
            try:
                results = self.backtester.backtest_strategy(
                    strategy, self.market_data, self.pair
                )
                
                strategy.fitness_score = results.get('fitness', 0.0)
                strategy.backtest_results = results
                
                if i % 10 == 0:  # Progress update every 10 strategies
                    logger.info(f"  Backtested {i+1}/{len(self.population)} strategies")
                    
            except Exception as e:
                logger.error(f"Backtesting failed for strategy {strategy.id}: {e}")
                strategy.fitness_score = 0.0
                strategy.backtest_results = {'fitness': 0.0, 'error': str(e)}

# ============================================================================
# STEP 2: DEMONSTRATION
# ============================================================================

def demonstrate_real_data_evolution():
    """Demonstrate evolution with real forex data"""
    print("🚀 Darwin Engine Step 2 - Real Forex Data Evolution")
    print("=" * 60)
    
    # Install required package if needed
    try:
        import yfinance
    except ImportError:
        print("Installing yfinance for real data...")
        import subprocess
        subprocess.check_call(["pip", "install", "yfinance"])
        import yfinance
    
    # Configure evolution
    config = EvolutionConfig(
        population_size=15,   # Smaller for real backtesting
        max_generations=8,    # Fewer generations for demo
        mutation_rate=0.2,
        elite_size=3
    )
    
    # Test different currency pairs
    pairs_to_test = ['EURUSD', 'GBPUSD']
    
    for pair in pairs_to_test:
        print(f"\n🧬 Evolving strategies for {pair}")
        print("-" * 40)
        
        # Create and run engine
        engine = RealDataDarwinEngine(config, pair)
        results = engine.run_evolution()
        
        # Display results
        print(f"\n📊 {pair} Evolution Results:")
        print(f"Best fitness: {results['best_strategy']['fitness_score']:.4f}")
        
        best = results['best_strategy']
        backtest = best['backtest_results']
        
        print(f"\n🏆 Best {pair} Strategy:")
        print(f"  Name: {best['name']}")
        print(f"  Action: {best['action']}")
        print(f"  Conditions: {len(best['conditions'])}")
        
        print(f"\n📈 Backtest Results:")
        print(f"  Total Return: {backtest.get('total_return_pct', 0):.2f}%")
        print(f"  Win Rate: {backtest.get('win_rate', 0):.2%}")
        print(f"  Total Trades: {backtest.get('total_trades', 0)}")
        print(f"  Sharpe Ratio: {backtest.get('sharpe_ratio', 0):.2f}")
        print(f"  Max Drawdown: {backtest.get('max_drawdown_pct', 0):.2f}%")
        print(f"  Profit Factor: {backtest.get('profit_factor', 0):.2f}")
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"darwin_step2_{pair}_{timestamp}.json"
        
        import json
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"  💾 Results saved to: {filename}")
    
    print(f"\n✅ Step 2 Complete! Evolution with real market data working!")
    print(f"🎯 Ready for Step 3: Formal verification with Coq")

if __name__ == "__main__":
    demonstrate_real_data_evolution()