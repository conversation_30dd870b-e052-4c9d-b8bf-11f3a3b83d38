import { z } from 'zod';

// Common API Response Schema
export const ApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
  data: z.unknown().optional(),
  error: z.object({
    code: z.string(),
    message: z.string(),
    details: z.unknown().optional(),
  }).optional(),
  timestamp: z.date(),
  requestId: z.string().uuid().optional(),
});

// Pagination Schema
export const PaginationSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(10),
  total: z.number().int().nonnegative(),
  totalPages: z.number().int().nonnegative(),
  hasNext: z.boolean(),
  hasPrev: z.boolean(),
});

// Paginated Response Schema
export const PaginatedResponseSchema = z.object({
  data: z.array(z.unknown()),
  pagination: PaginationSchema,
});

// Sort Schema
export const SortSchema = z.object({
  field: z.string().min(1),
  direction: z.enum(['asc', 'desc']).default('asc'),
});

// Filter Schema
export const FilterSchema = z.object({
  field: z.string().min(1),
  operator: z.enum(['eq', 'ne', 'lt', 'le', 'gt', 'ge', 'in', 'nin', 'contains', 'starts', 'ends']),
  value: z.unknown(),
});

// Query Parameters Schema
export const QueryParamsSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(10),
  sort: z.array(SortSchema).optional(),
  filter: z.array(FilterSchema).optional(),
  search: z.string().optional(),
});

// Date Range Schema
export const DateRangeSchema = z.object({
  start: z.date(),
  end: z.date(),
}).refine((data) => data.start <= data.end, {
  message: "Start date must be before or equal to end date",
  path: ["end"],
});

// Time Period Schema
export const TimePeriodSchema = z.enum([
  '1m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h',
  '1d', '3d', '1w', '2w', '1M', '3M', '6M', '1y'
]);

// Environment Schema
export const EnvironmentSchema = z.enum(['development', 'staging', 'production']);

// Log Level Schema
export const LogLevelSchema = z.enum(['error', 'warn', 'info', 'debug', 'trace']);

// Health Check Schema
export const HealthCheckSchema = z.object({
  status: z.enum(['healthy', 'degraded', 'unhealthy']),
  timestamp: z.date(),
  version: z.string(),
  uptime: z.number().nonnegative(), // in seconds
  services: z.record(z.object({
    status: z.enum(['up', 'down', 'degraded']),
    latency: z.number().nonnegative().optional(), // in milliseconds
    message: z.string().optional(),
  })),
});

// Configuration Schema
export const ConfigurationSchema = z.object({
  app: z.object({
    name: z.string(),
    version: z.string(),
    environment: EnvironmentSchema,
    port: z.number().int().min(1).max(65535),
    logLevel: LogLevelSchema,
  }),
  database: z.object({
    host: z.string(),
    port: z.number().int().min(1).max(65535),
    name: z.string(),
    username: z.string(),
    password: z.string(),
    ssl: z.boolean().default(false),
    poolSize: z.number().int().min(1).default(10),
  }),
  redis: z.object({
    host: z.string(),
    port: z.number().int().min(1).max(65535),
    password: z.string().optional(),
    db: z.number().int().min(0).default(0),
  }).optional(),
  security: z.object({
    jwtSecret: z.string().min(32),
    jwtExpiresIn: z.string(),
    bcryptRounds: z.number().int().min(10).max(15).default(12),
    corsOrigins: z.array(z.string()),
    rateLimiting: z.object({
      windowMs: z.number().int().positive(),
      maxRequests: z.number().int().positive(),
    }),
  }),
});

// Error Code Schema
export const ErrorCodeSchema = z.enum([
  'VALIDATION_ERROR',
  'AUTHENTICATION_ERROR',
  'AUTHORIZATION_ERROR',
  'NOT_FOUND',
  'CONFLICT',
  'RATE_LIMIT_EXCEEDED',
  'INTERNAL_SERVER_ERROR',
  'SERVICE_UNAVAILABLE',
  'BAD_REQUEST',
  'FORBIDDEN',
]);

// Types
export type ApiResponse<T = unknown> = Omit<z.infer<typeof ApiResponseSchema>, 'data'> & {
  data?: T;
};
export type Pagination = z.infer<typeof PaginationSchema>;
export type PaginatedResponse<T = unknown> = Omit<z.infer<typeof PaginatedResponseSchema>, 'data'> & {
  data: T[];
};
export type Sort = z.infer<typeof SortSchema>;
export type Filter = z.infer<typeof FilterSchema>;
export type QueryParams = z.infer<typeof QueryParamsSchema>;
export type DateRange = z.infer<typeof DateRangeSchema>;
export type TimePeriod = z.infer<typeof TimePeriodSchema>;
export type Environment = z.infer<typeof EnvironmentSchema>;
export type LogLevel = z.infer<typeof LogLevelSchema>;
export type HealthCheck = z.infer<typeof HealthCheckSchema>;
export type Configuration = z.infer<typeof ConfigurationSchema>;
export type ErrorCode = z.infer<typeof ErrorCodeSchema>;