/**
 * File Upload Component
 * Advanced file upload with drag & drop, column mapping, and real-time progress
 */

import { useState, useCallback, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, FileText, AlertCircle, CheckCircle, Clock, X } from 'lucide-react';
import { toast } from 'react-hot-toast';

import { apiService } from '@/services/api';
import { webSocketService } from '@/services/websocket';
import { useAuth } from '@/hooks/useAuth';
import { useApi } from '@/hooks/useApi';
import { ColumnMapping } from './ColumnMapping';
import { UploadProgress } from './UploadProgress';

import type { FileUploadSession, FileProcessingProgress } from '@shared/schemas';

interface FileUploadProps {
  onUploadComplete?: (session: FileUploadSession) => void;
  className?: string;
}

export function FileUpload({ onUploadComplete, className = '' }: FileUploadProps) {
  const { user } = useAuth();
  const [uploadSession, setUploadSession] = useState<FileUploadSession | null>(null);
  const [processingProgress, setProcessingProgress] = useState<FileProcessingProgress | null>(null);
  const [showProgress, setShowProgress] = useState(false);
  
  const { loading, error, execute, reset } = useApi<FileUploadSession>(); // data not currently used

  // Subscribe to real-time progress updates
  useEffect(() => {
    const handleProgress = (progress: FileProcessingProgress) => {
      if (uploadSession && progress.session_id === uploadSession.id) {
        setProcessingProgress(progress);
      }
    };

    const handleCompleted = (data: { sessionId: string; success: boolean }) => {
      if (uploadSession && data.sessionId === uploadSession.id) {
        setShowProgress(false);
        setProcessingProgress(null);
        
        if (data.success && onUploadComplete) {
          onUploadComplete(uploadSession);
        }
        
        // Reset state
        setUploadSession(null);
        reset();
      }
    };

    webSocketService.on('job:file_processing_progress', handleProgress);
    webSocketService.on('job:file_processing_completed', handleCompleted);

    return () => {
      webSocketService.off('job:file_processing_progress', handleProgress);
      webSocketService.off('job:file_processing_completed', handleCompleted);
    };
  }, [uploadSession, onUploadComplete, reset]);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (!user || acceptedFiles.length === 0) return;

    const file = acceptedFiles[0];
    
    // Validate file size (500MB limit)
    if (file.size > 500 * 1024 * 1024) {
      toast.error('File too large. Maximum size is 500MB.');
      return;
    }

    try {
      const session = await execute(() => apiService.uploadFile(file, user.id));
      if (session) {
        setUploadSession(session);
        toast.success('File uploaded successfully. Please map your columns.');
      }
    } catch (error) {
      console.error('Upload failed:', error);
    }
  }, [user, execute]);

  const { getRootProps, getInputProps, isDragActive, fileRejections } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/json': ['.json'],
    },
    maxSize: 500 * 1024 * 1024, // 500MB
    multiple: false,
  });

  const handleMappingConfirm = async (
    finalMapping: Record<string, string>,
    timezone: string
  ) => {
    if (!uploadSession) return;

    try {
      await apiService.confirmMapping(
        uploadSession.id,
        finalMapping,
        timezone,
        uploadSession.temporary_file_path || ''
      );
      
      // Start showing progress
      setShowProgress(true);
      
      // Subscribe to this specific job's progress
      webSocketService.subscribeToJob('upload', uploadSession.id);
      
      toast.success('Column mapping confirmed. Processing file...');
    } catch (error) {
      console.error('Mapping confirmation failed:', error);
      toast.error('Failed to confirm column mapping');
    }
  };

  const handleCancel = () => {
    setUploadSession(null);
    setShowProgress(false);
    setProcessingProgress(null);
    reset();
  };

  // Handle file rejection errors
  useEffect(() => {
    if (fileRejections.length > 0) {
      const rejection = fileRejections[0];
      const error = rejection.errors[0];
      
      if (error.code === 'file-too-large') {
        toast.error('File too large. Maximum size is 500MB.');
      } else if (error.code === 'file-invalid-type') {
        toast.error('Unsupported file type. Please upload CSV, Excel, or JSON files.');
      } else {
        toast.error(error.message);
      }
    }
  }, [fileRejections]);

  // Show progress component
  if (showProgress && processingProgress) {
    return (
      <div className={className}>
        <UploadProgress 
          progress={processingProgress} 
          onCancel={handleCancel}
        />
      </div>
    );
  }

  // Show column mapping component
  if (uploadSession && !showProgress) {
    return (
      <div className={className}>
        <ColumnMapping
          uploadSession={uploadSession}
          onConfirm={handleMappingConfirm}
          onCancel={handleCancel}
        />
      </div>
    );
  }

  // Show main upload interface
  return (
    <div className={`w-full max-w-2xl mx-auto ${className}`}>
      {/* Upload Area */}
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200 ${
          isDragActive
            ? 'border-primary-400 bg-primary-50 scale-105'
            : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'
        } ${loading ? 'pointer-events-none opacity-50' : ''}`}
      >
        <input {...getInputProps()} />
        
        <div className="flex flex-col items-center space-y-4">
          {loading ? (
            <div className="w-12 h-12 border-4 border-primary-600 border-t-transparent rounded-full animate-spin" />
          ) : (
            <Upload className={`w-12 h-12 ${isDragActive ? 'text-primary-500' : 'text-gray-400'}`} />
          )}
          
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              {loading ? 'Uploading and analyzing...' :
               isDragActive ? 'Drop your file here' : 'Upload trading data'}
            </h3>
            <p className="text-sm text-gray-500 mt-1">
              {loading ? 'Please wait while we process your file' :
               'Drag and drop your CSV, Excel, or JSON file, or click to browse'}
            </p>
          </div>
          
          {!loading && (
            <div className="text-xs text-gray-400 space-y-1">
              <p>Supported formats: CSV, XLSX, XLS, JSON</p>
              <p>Maximum file size: 500MB</p>
            </div>
          )}
        </div>
      </div>

      {/* Upload Status */}
      {loading && (
        <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center space-x-3">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
            <div>
              <p className="text-sm font-medium text-blue-900">Uploading and analyzing file</p>
              <p className="text-xs text-blue-700">This may take a few moments for larger files</p>
            </div>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-start space-x-3">
            <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-red-900">Upload failed</p>
              <p className="text-xs text-red-700 mt-1">{error}</p>
            </div>
            <button
              onClick={reset}
              className="ml-auto text-red-400 hover:text-red-600"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* Recent Uploads */}
      <RecentUploads className="mt-8" />
    </div>
  );
}

// Recent uploads component
function RecentUploads({ className = '' }: { className?: string }) {
  const [recentSessions, setRecentSessions] = useState<FileUploadSession[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadRecentSessions();
  }, []);

  const loadRecentSessions = async () => {
    try {
      setLoading(true);
      const sessions = await apiService.getUploadSessions();
      setRecentSessions(sessions.slice(0, 5)); // Show last 5 uploads
    } catch (error) {
      console.error('Failed to load recent sessions:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className={`${className}`}>
        <h4 className="text-sm font-medium text-gray-900 mb-3">Recent Uploads</h4>
        <div className="space-y-2">
          {[1, 2, 3].map(i => (
            <div key={i} className="animate-pulse flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
              <div className="w-8 h-8 bg-gray-200 rounded"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded w-1/2 mb-1"></div>
                <div className="h-3 bg-gray-200 rounded w-1/4"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (recentSessions.length === 0) {
    return null;
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ready':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'parsing_in_progress':
      case 'mapping':
        return <Clock className="w-5 h-5 text-blue-500" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return <FileText className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'ready':
        return 'Ready';
      case 'parsing_in_progress':
        return 'Processing';
      case 'mapping':
        return 'Mapping Required';
      case 'error':
        return 'Error';
      default:
        return 'Pending';
    }
  };

  return (
    <div className={className}>
      <div className="flex items-center justify-between mb-3">
        <h4 className="text-sm font-medium text-gray-900">Recent Uploads</h4>
        <button
          onClick={loadRecentSessions}
          className="text-xs text-primary-600 hover:text-primary-700"
        >
          Refresh
        </button>
      </div>
      
      <div className="space-y-2">
        {recentSessions.map((session) => (
          <div key={session.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
            {getStatusIcon(session.status)}
            
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {session.original_filename}
              </p>
              <div className="flex items-center space-x-2 text-xs text-gray-500">
                <span>{getStatusText(session.status)}</span>
                {session.rows_processed && session.rows_processed > 0 && (
                  <>
                    <span>•</span>
                    <span>{session.rows_processed.toLocaleString()} rows</span>
                  </>
                )}
                <span>•</span>
                <span>{new Date(session.created_at).toLocaleDateString()}</span>
              </div>
            </div>
            
            {session.file_size && (
              <div className="text-xs text-gray-400">
                {(session.file_size / (1024 * 1024)).toFixed(1)} MB
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}