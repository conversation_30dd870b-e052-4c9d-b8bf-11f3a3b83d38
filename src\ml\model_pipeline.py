# src/ml/model_pipeline.py
import hashlib
import json
import logging
import math
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field, field_validator
from dataclasses import dataclass

logger = logging.getLogger(__name__)

class PredictionFeatures(BaseModel):
    rsi: float = Field(..., ge=0.0, le=100.0, description="RSI indicator value")
    macd: float = Field(..., description="MACD indicator value")
    volume: float = Field(..., ge=0.0, description="Trading volume")
    sma_20: Optional[float] = Field(None, gt=0.0, description="20-period Simple Moving Average")
    ema_12: Optional[float] = Field(None, gt=0.0, description="12-period Exponential Moving Average")
    bollinger_upper: Optional[float] = Field(None, gt=0.0, description="Bollinger Band upper")
    bollinger_lower: Optional[float] = Field(None, gt=0.0, description="Bollinger Band lower")

class PredictionInput(BaseModel):
    symbol: str = Field(..., pattern=r'^[A-Z]{6}$', description="Currency pair in EURUSD format")
    features: PredictionFeatures
    timestamp: Optional[datetime] = None

    @field_validator('timestamp', mode='before')
    @classmethod
    def set_timestamp(cls, v):
        return v or datetime.now()

@dataclass
class PerformanceMetrics:
    accuracy: float
    precision: float
    recall: float
    f1_score: float
    backtest_sharpe: float

@dataclass
class ModelVersion:
    id: str
    version: str
    algorithm: str
    training_data_hash: str
    hyperparameters: Dict[str, Any]
    performance_metrics: PerformanceMetrics
    created_at: datetime
    model_hash: str

@dataclass
class PredictionMetadata:
    processing_time_ms: float
    features_used: List[str]
    model_confidence: float

@dataclass
class PredictionResult:
    value: float
    confidence: float
    model_version: str
    input_hash: str
    prediction_hash: str
    timestamp: datetime
    rejected: bool
    rejection_reason: Optional[str]
    metadata: PredictionMetadata

class ModelPipeline:
    """Zero-hallucination ML model pipeline with complete auditability"""
    
    def __init__(self, min_confidence_threshold: float = 0.7):
        self.logger = logger
        self.current_model: Optional[ModelVersion] = None
        self.min_confidence_threshold = min_confidence_threshold
        self.prediction_cache: Dict[str, PredictionResult] = {}
        self.max_cache_size = 1000
        
        # Load the latest model
        self._load_latest_model()
    
    def set_min_confidence_threshold(self, threshold: float) -> None:
        """Set minimum confidence threshold for predictions"""
        if not 0.0 <= threshold <= 1.0:
            raise ValueError("Confidence threshold must be between 0 and 1")
        self.min_confidence_threshold = threshold
        self.logger.info(f"Confidence threshold set to {threshold}")
    
    async def predict(self, input_data: PredictionInput) -> PredictionResult:
        """Make a prediction with full lineage tracking"""
        start_time = time.time()
        
        try:
            # Validate input
            if not isinstance(input_data, PredictionInput):
                input_data = PredictionInput(**input_data)
            
            # Generate input hash for caching and lineage
            input_hash = self._generate_input_hash(input_data)
            
            # Check cache
            if input_hash in self.prediction_cache:
                cached_result = self.prediction_cache[input_hash]
                self.logger.debug(f"Returning cached prediction for {input_data.symbol}")
                return cached_result
            
            # Ensure model is loaded
            if not self.current_model:
                raise RuntimeError("No model loaded")
            
            # Preprocess features
            processed_features = self._preprocess_features(input_data.features)
            
            # Make prediction
            raw_prediction = await self._run_model_inference(processed_features)
            
            # Calculate confidence
            confidence = self._calculate_confidence(raw_prediction, processed_features)
            
            # Generate prediction hash for integrity
            timestamp = input_data.timestamp or datetime.now()
            prediction_data = {
                'value': raw_prediction['value'],
                'model_version': self.current_model.id,
                'input_hash': input_hash,
                'timestamp': timestamp.isoformat()
            }
            prediction_hash = self._generate_prediction_hash(prediction_data)
            
            # Create result
            result = PredictionResult(
                value=raw_prediction['value'],
                confidence=confidence,
                model_version=self.current_model.id,
                input_hash=input_hash,
                prediction_hash=prediction_hash,
                timestamp=timestamp,
                rejected=confidence < self.min_confidence_threshold,
                rejection_reason='Confidence below threshold' if confidence < self.min_confidence_threshold else None,
                metadata=PredictionMetadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    features_used=list(processed_features.keys()),
                    model_confidence=raw_prediction['model_confidence']
                )
            )
            
            # Cache result
            self._cache_prediction(input_hash, result)
            
            # Log prediction for audit trail
            self.logger.info(
                f"Prediction made for {input_data.symbol}: "
                f"value={result.value:.4f}, confidence={result.confidence:.3f}, "
                f"rejected={result.rejected}, model={self.current_model.version}"
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Prediction failed for {input_data.symbol}: {str(e)}")
            raise RuntimeError(f"Prediction failed: {str(e)}")
    
    def _load_latest_model(self) -> None:
        """Load the latest model version"""
        # In a real implementation, this would load from a model registry
        # For now, create a mock model
        self.current_model = ModelVersion(
            id=f"model_v1.2.3_{int(time.time())}",
            version="1.2.3",
            algorithm="RandomForest",
            training_data_hash="sha256:training_data_hash_placeholder",
            hyperparameters={
                'n_estimators': 100,
                'max_depth': 10,
                'min_samples_split': 5,
                'random_state': 42
            },
            performance_metrics=PerformanceMetrics(
                accuracy=0.78,
                precision=0.75,
                recall=0.82,
                f1_score=0.78,
                backtest_sharpe=1.34
            ),
            created_at=datetime.now(),
            model_hash="sha256:model_weights_hash_placeholder"
        )
        
        self.logger.info(f"Loaded model {self.current_model.version}")
    
    def _preprocess_features(self, features: PredictionFeatures) -> Dict[str, float]:
        """Preprocess and normalize features"""
        processed = {}
        
        # RSI normalization (0-100 to 0-1)
        processed['rsi_normalized'] = features.rsi / 100.0
        
        # MACD (no normalization needed)
        processed['macd'] = features.macd
        
        # Volume (log transform to handle large values)
        processed['volume_log'] = math.log(features.volume + 1)
        
        # Moving averages ratio (if both provided)
        if features.sma_20 and features.ema_12:
            processed['ma_ratio'] = features.ema_12 / features.sma_20
        
        # Bollinger Band position (if provided)
        if features.bollinger_upper and features.bollinger_lower:
            current_price = features.sma_20 or features.ema_12 or 1.0
            bb_range = features.bollinger_upper - features.bollinger_lower
            if bb_range > 0:
                processed['bb_position'] = (current_price - features.bollinger_lower) / bb_range
            else:
                processed['bb_position'] = 0.5
        
        return processed
    
    async def _run_model_inference(self, features: Dict[str, float]) -> Dict[str, float]:
        """Run model inference (mock implementation)"""
        # In a real implementation, this would interface with actual ML models
        # (TensorFlow, PyTorch, scikit-learn, etc.)
        
        feature_values = list(features.values())
        
        # Simple weighted sum for demonstration
        weights = [0.1 + (i * 0.05) for i in range(len(feature_values))]
        weighted_sum = sum(val * weight for val, weight in zip(feature_values, weights))
        
        # Normalize to 0-1 range using tanh
        prediction = math.tanh(weighted_sum)
        normalized_prediction = (prediction + 1) / 2
        
        # Simulate model confidence based on feature consistency
        feature_variance = self._calculate_variance(feature_values)
        model_confidence = max(0.5, 1.0 - feature_variance)
        
        return {
            'value': normalized_prediction,
            'model_confidence': model_confidence
        }
    
    def _calculate_confidence(self, prediction: Dict[str, float], features: Dict[str, float]) -> float:
        """Calculate prediction confidence"""
        confidence = prediction['model_confidence']
        
        # Reduce confidence for extreme predictions (close to 0 or 1)
        extremeness = abs(prediction['value'] - 0.5) * 2
        confidence *= (1 - extremeness * 0.2)
        
        # Reduce confidence if key features are missing
        expected_features = ['rsi_normalized', 'macd', 'volume_log']
        missing_features = sum(1 for f in expected_features if f not in features)
        confidence *= (0.9 ** missing_features)
        
        return max(0.0, min(1.0, confidence))
    
    def _calculate_variance(self, values: List[float]) -> float:
        """Calculate variance of feature values"""
        if not values:
            return 0.0
        
        mean = sum(values) / len(values)
        variance = sum((val - mean) ** 2 for val in values) / len(values)
        return math.sqrt(variance)
    
    def _generate_input_hash(self, input_data: PredictionInput) -> str:
        """Generate hash for input data"""
        # Create normalized representation for consistent hashing
        normalized_input = {
            'symbol': input_data.symbol,
            'features': {
                k: v for k, v in input_data.features.model_dump().items() 
                if v is not None
            }
        }
        
        input_str = json.dumps(normalized_input, sort_keys=True)
        return hashlib.sha256(input_str.encode()).hexdigest()
    
    def _generate_prediction_hash(self, prediction_data: Dict[str, Any]) -> str:
        """Generate hash for prediction data"""
        prediction_str = json.dumps(prediction_data, sort_keys=True, default=str)
        return hashlib.sha256(prediction_str.encode()).hexdigest()
    
    def _cache_prediction(self, input_hash: str, result: PredictionResult) -> None:
        """Cache prediction result with size limit"""
        self.prediction_cache[input_hash] = result
        
        # Maintain cache size limit
        if len(self.prediction_cache) > self.max_cache_size:
            # Remove oldest entries (simple FIFO)
            oldest_keys = list(self.prediction_cache.keys())[:-self.max_cache_size]
            for key in oldest_keys:
                del self.prediction_cache[key]
    
    async def update_model(self, new_model: ModelVersion) -> None:
        """Update the current model with validation"""
        if not self._validate_model(new_model):
            raise ValueError("Model validation failed")
        
        old_version = self.current_model.version if self.current_model else "None"
        self.current_model = new_model
        self.prediction_cache.clear()  # Clear cache when model changes
        
        self.logger.info(f"Model updated from {old_version} to {new_model.version}")
    
    def _validate_model(self, model: ModelVersion) -> bool:
        """Validate model before deployment"""
        if not all([model.id, model.version, model.algorithm]):
            self.logger.error("Model missing required fields")
            return False
        
        # Check minimum performance standards
        if model.performance_metrics.accuracy < 0.6:
            self.logger.warning(
                f"Model accuracy {model.performance_metrics.accuracy} below minimum threshold 0.6"
            )
            return False
        
        if model.performance_metrics.backtest_sharpe < 0.5:
            self.logger.warning(
                f"Model Sharpe ratio {model.performance_metrics.backtest_sharpe} below minimum threshold 0.5"
            )
            return False
        
        return True
    
    def get_current_model(self) -> Optional[ModelVersion]:
        """Get current model information"""
        return self.current_model
    
    def clear_cache(self) -> None:
        """Clear prediction cache"""
        self.prediction_cache.clear()
        self.logger.info("Prediction cache cleared")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            'cache_size': len(self.prediction_cache),
            'max_cache_size': self.max_cache_size,
            'cache_hit_ratio': 0.0  # Would need to track hits/misses for real ratio
        }