/**
 * MVP API Service - Simplified API client for the MVP backend
 */

import axios, { AxiosInstance } from 'axios';
import { toast } from 'react-hot-toast';

class MvpApiService {
  private api: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = import.meta.env.VITE_API_URL || 'http://localhost:8000';
    
    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: 10000, // 10 seconds
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        // Extract error message
        const message = error.response?.data?.detail || 
                       error.message || 
                       'An unexpected error occurred';

        // Show toast notification
        toast.error(message);
        return Promise.reject(error);
      }
    );
  }

  // Health check
  async getHealth(): Promise<any> {
    try {
      const response = await this.api.get('/health');
      return response.data;
    } catch (error) {
      console.error('Health check error:', error);
      throw error;
    }
  }

  // Strategy endpoints
  async getStrategies(): Promise<any[]> {
    try {
      const response = await this.api.get('/api/strategies');
      return response.data;
    } catch (error) {
      console.error('Error getting strategies:', error);
      throw error;
    }
  }

  async getStrategy(strategyId: number): Promise<any> {
    try {
      const response = await this.api.get(`/api/strategies/${strategyId}`);
      return response.data;
    } catch (error) {
      console.error('Error getting strategy:', error);
      throw error;
    }
  }

  async createStrategy(strategyData: {
    name: string;
    description: string;
    parameters: any;
  }): Promise<any> {
    try {
      const response = await this.api.post('/api/strategies', strategyData);
      return response.data;
    } catch (error) {
      console.error('Error creating strategy:', error);
      throw error;
    }
  }

  // Backtest endpoints
  async getBacktests(): Promise<any[]> {
    try {
      const response = await this.api.get('/api/backtests');
      return response.data;
    } catch (error) {
      console.error('Error getting backtests:', error);
      throw error;
    }
  }

  async getBacktest(backtestId: number): Promise<any> {
    try {
      const response = await this.api.get(`/api/backtests/${backtestId}`);
      return response.data;
    } catch (error) {
      console.error('Error getting backtest:', error);
      throw error;
    }
  }

  async createBacktest(backtestData: {
    strategy_id: number;
    start_date: string;
    end_date: string;
    symbol: string;
    timeframe: string;
    initial_capital: number;
  }): Promise<any> {
    try {
      const response = await this.api.post('/api/backtests', backtestData);
      return response.data;
    } catch (error) {
      console.error('Error creating backtest:', error);
      throw error;
    }
  }
}

export const mvpApi = new MvpApiService();