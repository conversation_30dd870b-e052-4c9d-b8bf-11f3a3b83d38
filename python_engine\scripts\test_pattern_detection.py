#!/usr/bin/env python
"""Quick script to test pattern detection"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from services.darwin_godel.pattern_detector import StrategyPatternDetector
from services.darwin_godel.pattern_report import Pat<PERSON>Report

def test_mean_reversion_strategy():
    """Test a mean reversion strategy"""
    print("🧪 Testing Mean Reversion Strategy...")
    
    detector = StrategyPatternDetector()
    
    strategy = """
def trading_strategy(data, params):
    # Calculate 20-period SMA
    sma = calculate_sma(data['close'], 20)
    current_price = data['close'][-1]
    
    # Buy when price is 2% below SMA
    if current_price < sma[-1] * 0.98:
        return {'signal': 'buy', 'confidence': 0.8}
    # Sell when price is 2% above SMA
    elif current_price > sma[-1] * 1.02:
        return {'signal': 'sell', 'confidence': 0.8}
    else:
        return {'signal': 'hold', 'confidence': 0.5}
"""
    
    # Analyze the strategy
    analysis = detector.analyze_strategy(strategy)
    
    # Generate report
    report = PatternReport.generate_report(analysis)
    print(report)
    
    return analysis

def test_momentum_strategy():
    """Test a momentum strategy"""
    print("🧪 Testing Momentum Strategy...")
    
    detector = StrategyPatternDetector()
    
    strategy = """
def trading_strategy(data, params):
    # Calculate moving averages
    sma_fast = calculate_sma(data['close'], 10)
    sma_slow = calculate_sma(data['close'], 30)
    
    # Golden cross - buy signal
    if sma_fast[-1] > sma_slow[-1] and sma_fast[-2] <= sma_slow[-2]:
        return {'signal': 'buy', 'confidence': 0.9}
    # Death cross - sell signal
    elif sma_fast[-1] < sma_slow[-1] and sma_fast[-2] >= sma_slow[-2]:
        return {'signal': 'sell', 'confidence': 0.9}
    else:
        return {'signal': 'hold', 'confidence': 0.3}
"""
    
    # Analyze the strategy
    analysis = detector.analyze_strategy(strategy)
    
    # Generate report
    report = PatternReport.generate_report(analysis)
    print(report)
    
    return analysis

def test_rsi_strategy():
    """Test an RSI-based strategy"""
    print("🧪 Testing RSI Strategy...")
    
    detector = StrategyPatternDetector()
    
    strategy = """
def trading_strategy(data, params):
    rsi = calculate_rsi(data['close'], 14)
    
    # Classic RSI levels
    if rsi[-1] < 30:  # Oversold
        return {'signal': 'buy', 'confidence': 0.7}
    elif rsi[-1] > 70:  # Overbought
        return {'signal': 'sell', 'confidence': 0.7}
    else:
        return {'signal': 'hold', 'confidence': 0.5}
"""
    
    # Analyze the strategy
    analysis = detector.analyze_strategy(strategy)
    
    # Generate report
    report = PatternReport.generate_report(analysis)
    print(report)
    
    return analysis

def test_breakout_strategy():
    """Test a breakout strategy"""
    print("🧪 Testing Breakout Strategy...")
    
    detector = StrategyPatternDetector()
    
    strategy = """
def trading_strategy(data, params):
    # Calculate recent highs and lows
    lookback = params.get('lookback', 20)
    recent_high = max(data['high'][-lookback:])
    recent_low = min(data['low'][-lookback:])
    current_price = data['close'][-1]
    
    # Breakout above resistance
    if current_price > recent_high:
        return {'signal': 'buy', 'confidence': 0.85}
    # Breakdown below support
    elif current_price < recent_low:
        return {'signal': 'sell', 'confidence': 0.85}
    else:
        return {'signal': 'hold', 'confidence': 0.3}
"""
    
    # Analyze the strategy
    analysis = detector.analyze_strategy(strategy)
    
    # Generate report
    report = PatternReport.generate_report(analysis)
    print(report)
    
    return analysis

def test_complex_strategy():
    """Test a complex multi-indicator strategy"""
    print("🧪 Testing Complex Multi-Indicator Strategy...")
    
    detector = StrategyPatternDetector()
    
    strategy = """
def trading_strategy(data, params):
    # Multiple indicators
    sma = calculate_sma(data['close'], 20)
    rsi = calculate_rsi(data['close'], 14)
    macd, signal = calculate_macd(data['close'])
    upper, lower = calculate_bollinger_bands(data['close'], 20, 2)
    volume_sma = calculate_sma(data['volume'], 10)
    
    # Complex conditions
    if (rsi[-1] > 70 and 
        macd[-1] > signal[-1] and 
        data['close'][-1] > sma[-1] and
        data['close'][-1] > upper[-1] and
        data['volume'][-1] > volume_sma[-1] * 1.5 and
        data['high'][-1] - data['low'][-1] > 2.0):
        return {'signal': 'sell', 'confidence': 0.9}  # Overbought with volume
    elif (rsi[-1] < 30 and 
          macd[-1] < signal[-1] and 
          data['close'][-1] < sma[-1] and
          data['close'][-1] < lower[-1]):
        return {'signal': 'buy', 'confidence': 0.9}   # Oversold
    else:
        return {'signal': 'hold', 'confidence': 0.1}
"""
    
    # Analyze the strategy
    analysis = detector.analyze_strategy(strategy)
    
    # Generate report
    report = PatternReport.generate_report(analysis)
    print(report)
    
    return analysis

def test_simple_price_action():
    """Test a simple price action strategy"""
    print("🧪 Testing Simple Price Action Strategy...")
    
    detector = StrategyPatternDetector()
    
    strategy = """
def trading_strategy(data, params):
    # Pure price action - no indicators
    if data['close'][-1] > data['close'][-2]:
        return {'signal': 'buy', 'confidence': 0.6}
    else:
        return {'signal': 'sell', 'confidence': 0.6}
"""
    
    # Analyze the strategy
    analysis = detector.analyze_strategy(strategy)
    
    # Generate report
    report = PatternReport.generate_report(analysis)
    print(report)
    
    return analysis

def main():
    """Run all pattern detection tests"""
    print("🚀 Pattern Detection Test Suite")
    print("=" * 60)
    
    try:
        # Test different strategy types
        results = []
        
        results.append(test_mean_reversion_strategy())
        print("\n" + "="*60 + "\n")
        
        results.append(test_momentum_strategy())
        print("\n" + "="*60 + "\n")
        
        results.append(test_rsi_strategy())
        print("\n" + "="*60 + "\n")
        
        results.append(test_breakout_strategy())
        print("\n" + "="*60 + "\n")
        
        results.append(test_complex_strategy())
        print("\n" + "="*60 + "\n")
        
        results.append(test_simple_price_action())
        print("\n" + "="*60 + "\n")
        
        # Summary
        print("📊 PATTERN DETECTION SUMMARY")
        print("=" * 60)
        
        for i, result in enumerate(results, 1):
            summary = PatternReport.generate_simple_summary(result)
            print(f"{i}. {summary}")
        
        print("\n🎉 All pattern detection tests completed successfully!")
        print("✅ Pattern detector is working correctly")
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()