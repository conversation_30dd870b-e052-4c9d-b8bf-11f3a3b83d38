# Web Framework
fastapi>=0.104.0
uvicorn>=0.23.2
Flask==3.0.0
Flask-CORS==4.0.0

# Database
sqlalchemy>=2.0.0
asyncpg>=0.28.0
aiosqlite>=0.19.0
alembic>=1.12.0

# Authentication
python-jose>=3.3.0
passlib>=1.7.4
python-multipart>=0.0.6
bcrypt>=4.0.1

# Environment and Configuration
python-dotenv>=1.0.0
pydantic>=2.4.2
pydantic-settings>=2.0.3

# Data Processing
pandas>=2.2.0
numpy>=1.26.0
yfinance>=0.2.28
python-dateutil>=2.8.2
scikit-learn>=1.3.0
TA-Lib>=0.4.19
joblib>=1.3.0

# HTTP and API
requests>=2.31.0
httpx>=0.25.0
aiohttp>=3.8.0

# Testing
pytest>=8.0.0
pytest-cov>=4.1.0
pytest-mock>=3.10.0
pytest-asyncio>=0.21.0
hypothesis>=6.135.0
psutil>=5.9.0
pytest-benchmark>=4.0.0
pytest-xdist>=3.3.0
faker>=19.0.0

# Security
RestrictedPython>=6.0

# Utilities
email-validator>=2.0.0