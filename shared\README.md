# Shared Components and Utilities

This folder contains shared code, schemas, types, and utilities that are used across multiple parts of the AI Trading Platform.

## Folder Structure

```
shared/
├── schemas/           # Zod schemas for validation
├── types/            # TypeScript type definitions
├── utils/            # Utility functions and helpers
├── constants/        # Application constants
├── validators/       # Custom validation logic
├── formatters/       # Data formatting utilities
├── test-factories/   # Shared test data factories
├── test-utils/       # Testing utilities and helpers
├── config/           # Configuration schemas and loaders
├── errors/           # Custom error classes
├── middleware/       # Shared middleware
└── hooks/            # Reusable hooks (if using React)
```

## Usage

Import shared components using the `@/shared` alias:

```typescript
import { UserSchema, CreateUserRequest } from '@/shared/schemas';
import { ApiResponse } from '@/shared/types';
import { validateEmail } from '@/shared/validators';
import { getMockUser } from '@/shared/test-factories';
```

## TDD Guidelines

- All shared components should have corresponding test files
- Test factories should use real schemas for validation
- Utilities should be pure functions when possible
- Error classes should be well-typed and testable