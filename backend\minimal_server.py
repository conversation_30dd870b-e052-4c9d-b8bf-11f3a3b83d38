"""
Minimal FastAPI server for MVP testing
"""

import sys
import os
import logging
import secrets
import shutil
from pathlib import Path
from strategy_helper_api import app as strategy_app
from fastapi import FastAPI, HTTPException, Depends, status, UploadFile, File
from strategy_helper_api import app as strategy_app
from fastapi.security import <PERSON><PERSON><PERSON>Basic, HTTPBasicCredentials
from strategy_helper_api import app as strategy_app
from fastapi.middleware.cors import CORSMiddleware
from strategy_helper_api import app as strategy_app
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional
from datetime import datetime
import csv
import io
import json
import pandas as pd

# MT5Bridge class for handling MetaTrader 5 operations
class MT5Bridge:
    """
    A simplified bridge to MetaTrader 5 for MVP testing.
    In offline mode, it simulates trading operations without connecting to MT5.
    """
    
    def __init__(self, offline_mode=True):
        """
        Initialize the MT5Bridge.
        
        Args:
            offline_mode (bool): If True, operate in offline mode (no actual MT5 connection)
        """
        self.offline_mode = offline_mode
        self.logger = logging.getLogger("MT5Bridge")
        self.logger.info(f"MT5Bridge initialized in {'offline' if offline_mode else 'online'} mode")
        
    def close_order(self, ticket: int) -> Dict[str, Any]:
        """
        Close an existing order by ticket number.
        
        Args:
            ticket (int): The ticket number of the order to close
            
        Returns:
            Dict[str, Any]: Result of the close operation
            
        Raises:
            ValueError: If ticket is invalid
            RuntimeError: If close operation fails
        """
        try:
            self.logger.info(f"Closing order with ticket #{ticket}")
            
            if self.offline_mode:
                # In offline mode, simulate a successful close
                # Check if the order exists in our mock trades
                global MOCK_TRADES
                
                # Find the trade with the given ticket
                trade_to_close = None
                for trade in MOCK_TRADES:
                    if trade.get("ticket") == ticket:
                        trade_to_close = trade
                        break
                
                if trade_to_close:
                    # Mark the trade as closed
                    trade_to_close["closed"] = True
                    trade_to_close["close_time"] = datetime.now().isoformat()
                    
                    self.logger.info(f"Offline mode: Simulated order close for ticket #{ticket}")
                    return {
                        "status": "success",
                        "message": f"Order #{ticket} closed successfully",
                        "ticket": ticket,
                        "close_time": trade_to_close["close_time"]
                    }
                else:
                    self.logger.warning(f"Offline mode: Order with ticket #{ticket} not found")
                    return {
                        "status": "error",
                        "message": f"Order with ticket #{ticket} not found"
                    }
            else:
                # In online mode, this would connect to MT5 and close the order
                raise NotImplementedError("Online mode not implemented for MVP")
                
        except Exception as e:
            self.logger.error(f"Error closing order: {str(e)}")
            raise RuntimeError(f"Failed to close order: {str(e)}")
    
    def place_order(self, symbol: str, lot: float, order_type: str) -> Dict[str, Any]:
        """
        Place a trading order.
        
        Args:
            symbol (str): Trading symbol (e.g., "EURUSD")
            lot (float): Lot size
            order_type (str): Order type (e.g., "BUY", "SELL")
            
        Returns:
            Dict[str, Any]: Order result
            
        Raises:
            ValueError: If parameters are invalid
            RuntimeError: If order placement fails
        """
        try:
            # Validate inputs
            if not symbol or not isinstance(symbol, str):
                raise ValueError("Symbol must be a non-empty string")
            
            if not lot or lot <= 0:
                raise ValueError("Lot size must be greater than zero")
            
            if order_type not in ["BUY", "SELL"]:
                raise ValueError("Order type must be either 'BUY' or 'SELL'")
            
            self.logger.info(f"Placing order: {order_type} {lot} {symbol}")
            
            if self.offline_mode:
                # In offline mode, simulate a successful order
                import random
                ticket = random.randint(10000, 99999)
                
                # Simulate current market prices based on symbol
                prices = {
                    "EURUSD": 1.12345,
                    "GBPUSD": 1.31452,
                    "USDJPY": 107.235,
                    "AUDUSD": 0.75123,
                    "USDCAD": 1.25678
                }
                
                open_price = prices.get(symbol, 1.0)
                
                self.logger.info(f"Offline mode: Simulated order placed with ticket #{ticket}")
                return {
                    "status": "success",
                    "ticket": ticket,
                    "symbol": symbol,
                    "lot": lot,
                    "order_type": order_type,
                    "open_price": open_price,
                    "time": datetime.now().isoformat()
                }
            else:
                # In online mode, we would connect to MT5 here
                # This is just a placeholder for future implementation
                self.logger.error("Online mode not implemented yet")
                raise RuntimeError("Online mode not implemented yet")
        except ValueError as e:
            # Re-raise validation errors
            self.logger.error(f"Validation error: {str(e)}")
            raise
        except Exception as e:
            # Log and wrap other errors
            self.logger.error(f"Error placing order: {str(e)}")
            raise RuntimeError(f"Failed to place order: {str(e)}")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("minimal_server")

# Create data directory for historical data persistence
DATA_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data")
os.makedirs(DATA_DIR, exist_ok=True)
logger.info(f"Data directory: {DATA_DIR}")

# Create FastAPI app
app = FastAPI(
    title="AI Enhanced Trading Platform API",
    description="Minimal API for MVP testing",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For development - restrict in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Set up basic authentication
security = HTTPBasic()

# For MVP, we'll use hardcoded credentials
# In a production environment, these would be stored securely
MVP_USERNAME = "admin"
MVP_PASSWORD = "trading123"

def get_current_username(credentials: HTTPBasicCredentials = Depends(security)):
    """
    Validate credentials and return username if valid.
    
    Args:
        credentials: The HTTP basic credentials
        
    Returns:
        str: The username if credentials are valid
        
    Raises:
        HTTPException: If credentials are invalid
    """
    # In a production environment, this would use a secure password hashing algorithm
    is_username_correct = secrets.compare_digest(credentials.username, MVP_USERNAME)
    is_password_correct = secrets.compare_digest(credentials.password, MVP_PASSWORD)
    
    if not (is_username_correct and is_password_correct):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid credentials",
            headers={"WWW-Authenticate": "Basic"},
        )
    
    return credentials.username

# Mock data for testing
MOCK_STRATEGIES = [
    {
        "id": 1,
        "name": "Simple Moving Average Crossover",
        "description": "Buy when fast MA crosses above slow MA, sell when it crosses below",
        "parameters": {
            "fast_period": 10,
            "slow_period": 30
        },
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat()
    },
    {
        "id": 2,
        "name": "RSI Reversal",
        "description": "Buy when RSI is oversold, sell when overbought",
        "parameters": {
            "rsi_period": 14,
            "oversold": 30,
            "overbought": 70
        },
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat()
    }
]

# Storage for historical data
HISTORICAL_DATA = {
    # "symbol_timeframe": {
    #     "timestamp": [...],
    #     "open": [...],
    #     "high": [...],
    #     "low": [...],
    #     "close": [...],
    #     "volume": [...]
    # }
}

# Sample historical data for demo purposes
HISTORICAL_DATA["EURUSD_H1"] = {
    "timestamp": [datetime(2023, 1, 1, 0, 0).isoformat() for i in range(100)],
    "open": [1.1 + i*0.0001 for i in range(100)],
    "high": [1.105 + i*0.0001 for i in range(100)],
    "low": [1.095 + i*0.0001 for i in range(100)],
    "close": [1.102 + i*0.0001 for i in range(100)],
    "volume": [1000 + i*10 for i in range(100)]
}

HISTORICAL_DATA["GBPUSD_H4"] = {
    "timestamp": [datetime(2023, 1, 1, 0, 0).isoformat() for i in range(100)],
    "open": [1.3 + i*0.0001 for i in range(100)],
    "high": [1.305 + i*0.0001 for i in range(100)],
    "low": [1.295 + i*0.0001 for i in range(100)],
    "close": [1.302 + i*0.0001 for i in range(100)],
    "volume": [800 + i*8 for i in range(100)]
}

# Save sample data to disk for persistence
def save_sample_data_to_disk():
    """Save sample historical data to disk"""
    for key, data in HISTORICAL_DATA.items():
        data_file_path = os.path.join(DATA_DIR, f"{key}.json")
        if not os.path.exists(data_file_path):
            try:
                with open(data_file_path, "w") as f:
                    json.dump(data, f)
                logger.info(f"Saved sample data for {key} to disk")
            except Exception as e:
                logger.error(f"Error saving sample data to disk: {str(e)}")

# Save sample data when server starts
save_sample_data_to_disk()

MOCK_BACKTESTS = [
    {
        "id": 1,
        "strategy_id": 1,
        "start_date": "2023-01-01T00:00:00",
        "end_date": "2023-12-31T23:59:59",
        "symbol": "EURUSD",
        "timeframe": "H1",
        "initial_capital": 10000,
        "results": {
            "final_capital": 12500,
            "profit_factor": 1.8,
            "sharpe_ratio": 1.2,
            "max_drawdown": 8.5,
            "win_rate": 65.2
        },
        "created_at": datetime.now().isoformat()
    }
]

# Mock trades history
MOCK_TRADES = []

# Request/Response models
class StrategyCreate(BaseModel):
    """Strategy creation model"""
    name: str = Field(..., description="Strategy name")
    description: str = Field(..., description="Strategy description")
    parameters: Dict[str, Any] = Field(..., description="Strategy parameters")

class BacktestCreate(BaseModel):
    """Backtest creation model"""
    strategy_id: int = Field(..., description="Strategy ID")
    start_date: str = Field(..., description="Start date (ISO format)")
    end_date: str = Field(..., description="End date (ISO format)")
    symbol: str = Field(..., description="Trading symbol")
    timeframe: str = Field(..., description="Timeframe")
    initial_capital: float = Field(..., gt=0, description="Initial capital")
    
class TradeRequest(BaseModel):
    """Trade request model"""
    symbol: Optional[str] = Field(None, description="Trading symbol (e.g., 'EURUSD')")
    lot: Optional[float] = Field(None, gt=0, description="Lot size")
    order_type: Optional[str] = Field(None, description="Order type (BUY or SELL)")
    action: Optional[str] = Field(None, description="Action (close)")
    ticket: Optional[int] = Field(None, description="Ticket number for close action")

class HistoricalDataInfo(BaseModel):
    """Historical data information model"""
    symbol: str = Field(..., description="Trading symbol (e.g., 'EURUSD')")
    timeframe: str = Field(..., description="Timeframe (e.g., 'H1', 'D1')")
    start_date: str = Field(..., description="Start date of the data")
    end_date: str = Field(..., description="End date of the data")
    rows: int = Field(..., description="Number of data points")
    columns: List[str] = Field(..., description="Column names")

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "AI Enhanced Trading Platform API",
        "version": "1.0.0"
    }

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "ok",
        "timestamp": datetime.now().isoformat()
    }

# Strategy endpoints
@app.get("/api/strategies", response_model=List[Dict[str, Any]])
async def get_strategies(username: str = Depends(get_current_username)):
    """Get all strategies"""
    return MOCK_STRATEGIES

@app.get("/api/strategies/{strategy_id}", response_model=Dict[str, Any])
async def get_strategy(strategy_id: int, username: str = Depends(get_current_username)):
    """Get a strategy by ID"""
    for strategy in MOCK_STRATEGIES:
        if strategy["id"] == strategy_id:
            return strategy
    raise HTTPException(status_code=404, detail="Strategy not found")

@app.post("/api/strategies", response_model=Dict[str, Any])
async def create_strategy(strategy: StrategyCreate, username: str = Depends(get_current_username)):
    """Create a new strategy"""
    new_id = max([s["id"] for s in MOCK_STRATEGIES]) + 1 if MOCK_STRATEGIES else 1
    new_strategy = {
        "id": new_id,
        "name": strategy.name,
        "description": strategy.description,
        "parameters": strategy.parameters,
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat()
    }
    MOCK_STRATEGIES.append(new_strategy)
    return new_strategy

# Backtest endpoints
@app.get("/api/backtests", response_model=List[Dict[str, Any]])
async def get_backtests(username: str = Depends(get_current_username)):
    """Get all backtests"""
    return MOCK_BACKTESTS

@app.get("/api/backtests/{backtest_id}", response_model=Dict[str, Any])
async def get_backtest(backtest_id: int, username: str = Depends(get_current_username)):
    """Get a backtest by ID"""
    for backtest in MOCK_BACKTESTS:
        if backtest["id"] == backtest_id:
            return backtest
    raise HTTPException(status_code=404, detail="Backtest not found")

@app.post("/api/backtests", response_model=Dict[str, Any])
async def create_backtest(backtest: BacktestCreate, username: str = Depends(get_current_username)):
    """Create a new backtest"""
    # Check if strategy exists
    strategy_exists = any(s["id"] == backtest.strategy_id for s in MOCK_STRATEGIES)
    if not strategy_exists:
        raise HTTPException(status_code=404, detail="Strategy not found")
    
    # Create mock backtest results
    import random
    profit = random.uniform(-5, 15)
    final_capital = backtest.initial_capital * (1 + profit/100)
    
    new_id = max([b["id"] for b in MOCK_BACKTESTS]) + 1 if MOCK_BACKTESTS else 1
    new_backtest = {
        "id": new_id,
        "strategy_id": backtest.strategy_id,
        "start_date": backtest.start_date,
        "end_date": backtest.end_date,
        "symbol": backtest.symbol,
        "timeframe": backtest.timeframe,
        "initial_capital": backtest.initial_capital,
        "results": {
            "final_capital": round(final_capital, 2),
            "profit_factor": round(random.uniform(0.8, 2.0), 2),
            "sharpe_ratio": round(random.uniform(0.5, 1.5), 2),
            "max_drawdown": round(random.uniform(5, 15), 2),
            "win_rate": round(random.uniform(40, 70), 2)
        },
        "created_at": datetime.now().isoformat()
    }
    MOCK_BACKTESTS.append(new_backtest)
    return new_backtest

# Historical data endpoints
@app.post("/api/historical-data/upload", response_model=HistoricalDataInfo)
async def upload_historical_data(
    symbol: str,
    timeframe: str,
    file: UploadFile = File(...),
    max_size: int = 10 * 1024 * 1024,  # 10MB limit
    username: str = Depends(get_current_username)
):
    """
    Upload historical price data in CSV format.
    
    Args:
        symbol: Trading symbol (e.g., 'EURUSD')
        timeframe: Timeframe (e.g., 'H1', 'D1')
        file: CSV file with historical data
        max_size: Maximum file size in bytes (default: 10MB)
        username: Authenticated username
        
    Returns:
        HistoricalDataInfo: Information about the uploaded data
        
    Raises:
        HTTPException: If the file format is invalid or processing fails
    """
    try:
        # Validate symbol and timeframe format
        if not symbol.isalnum() and '_' not in symbol:
            raise HTTPException(
                status_code=400,
                detail="Invalid symbol format. Use alphanumeric characters only."
            )
        
        if not timeframe in ['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1', 'W1', 'MN']:
            raise HTTPException(
                status_code=400,
                detail="Invalid timeframe. Use M1, M5, M15, M30, H1, H4, D1, W1, or MN."
            )
        
        # Check file size
        file_size = 0
        temp_file_path = os.path.join(DATA_DIR, f"temp_{symbol}_{timeframe}.csv")
        
        with open(temp_file_path, "wb") as buffer:
            # Read and write in chunks to avoid loading entire file into memory
            chunk_size = 1024 * 1024  # 1MB chunks
            while True:
                chunk = await file.read(chunk_size)
                if not chunk:
                    break
                file_size += len(chunk)
                if file_size > max_size:
                    # Clean up and raise error
                    buffer.close()
                    os.remove(temp_file_path)
                    raise HTTPException(
                        status_code=413,
                        detail=f"File too large. Maximum size is {max_size/1024/1024}MB."
                    )
                buffer.write(chunk)
        
        # Reset file position for reading
        await file.seek(0)
        
        # Read the CSV file
        df = pd.read_csv(temp_file_path)
        
        # Clean up temp file
        os.remove(temp_file_path)
        
        # Validate required columns
        required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            raise HTTPException(
                status_code=400,
                detail=f"Missing required columns: {', '.join(missing_columns)}"
            )
        
        # Validate timestamp format
        try:
            pd.to_datetime(df['timestamp'])
        except Exception as e:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid timestamp format: {str(e)}. Expected ISO format or similar parsable format."
            )
        
        # Validate price values
        if (df[['open', 'high', 'low', 'close']] < 0).any().any():
            raise HTTPException(
                status_code=400,
                detail="Price values cannot be negative."
            )
        
        # Validate volume values
        if (df['volume'] < 0).any():
            raise HTTPException(
                status_code=400,
                detail="Volume values cannot be negative."
            )
        
        # Convert data to dictionary format for storage
        data_dict = {
            "timestamp": df['timestamp'].tolist(),
            "open": df['open'].tolist(),
            "high": df['high'].tolist(),
            "low": df['low'].tolist(),
            "close": df['close'].tolist(),
            "volume": df['volume'].tolist()
        }
        
        # Store the data in memory
        key = f"{symbol}_{timeframe}"
        HISTORICAL_DATA[key] = data_dict
        
        # Save to disk for persistence
        data_file_path = os.path.join(DATA_DIR, f"{symbol}_{timeframe}.json")
        with open(data_file_path, "w") as f:
            json.dump(data_dict, f)
        
        logger.info(f"Historical data for {symbol}_{timeframe} saved to {data_file_path}")
        
        # Return information about the uploaded data
        return HistoricalDataInfo(
            symbol=symbol,
            timeframe=timeframe,
            start_date=df['timestamp'].iloc[0],
            end_date=df['timestamp'].iloc[-1],
            rows=len(df),
            columns=df.columns.tolist()
        )
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error processing historical data upload: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to process historical data: {str(e)}"
        )

@app.get("/api/historical-data", response_model=List[str])
async def list_historical_data(username: str = Depends(get_current_username)):
    """
    Get a list of available historical data sets.
    
    Args:
        username: Authenticated username
        
    Returns:
        List[str]: List of available data sets in format "symbol_timeframe"
    """
    # Check for data files on disk
    data_files = [f.stem for f in Path(DATA_DIR).glob("*.json")]
    
    # Combine with in-memory data
    all_datasets = set(list(HISTORICAL_DATA.keys()) + data_files)
    
    return sorted(list(all_datasets))

@app.get("/api/historical-data/{symbol}/{timeframe}", response_model=Dict[str, Any])
async def get_historical_data(
    symbol: str,
    timeframe: str,
    limit: int = 100,
    username: str = Depends(get_current_username)
):
    """
    Get historical data for a specific symbol and timeframe.
    
    Args:
        symbol: Trading symbol (e.g., 'EURUSD')
        timeframe: Timeframe (e.g., 'H1', 'D1')
        limit: Maximum number of data points to return (default: 100)
        username: Authenticated username
        
    Returns:
        Dict[str, Any]: Historical data
        
    Raises:
        HTTPException: If the requested data is not found
    """
    key = f"{symbol}_{timeframe}"
    
    # Check if data is in memory
    if key not in HISTORICAL_DATA:
        # Try to load from disk
        data_file_path = os.path.join(DATA_DIR, f"{key}.json")
        if os.path.exists(data_file_path):
            try:
                with open(data_file_path, "r") as f:
                    HISTORICAL_DATA[key] = json.load(f)
                logger.info(f"Loaded historical data for {key} from disk")
            except Exception as e:
                logger.error(f"Error loading historical data from disk: {str(e)}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Error loading historical data: {str(e)}"
                )
        else:
            raise HTTPException(
                status_code=404,
                detail=f"Historical data for {symbol} {timeframe} not found"
            )
    
    # Get the data and limit the number of points
    data = HISTORICAL_DATA[key]
    
    if limit and limit < len(data["timestamp"]):
        return {
            "symbol": symbol,
            "timeframe": timeframe,
            "data": {
                "timestamp": data["timestamp"][-limit:],
                "open": data["open"][-limit:],
                "high": data["high"][-limit:],
                "low": data["low"][-limit:],
                "close": data["close"][-limit:],
                "volume": data["volume"][-limit:]
            }
        }
    
    return {
        "symbol": symbol,
        "timeframe": timeframe,
        "data": data
    }

@app.delete("/api/historical-data/{symbol}/{timeframe}")
async def delete_historical_data(
    symbol: str,
    timeframe: str,
    username: str = Depends(get_current_username)
):
    """
    Delete historical data for a specific symbol and timeframe.
    
    Args:
        symbol: Trading symbol (e.g., 'EURUSD')
        timeframe: Timeframe (e.g., 'H1', 'D1')
        username: Authenticated username
        
    Returns:
        Dict[str, str]: Success message
        
    Raises:
        HTTPException: If the requested data is not found
    """
    key = f"{symbol}_{timeframe}"
    
    # Check if data exists
    data_file_path = os.path.join(DATA_DIR, f"{key}.json")
    in_memory = key in HISTORICAL_DATA
    on_disk = os.path.exists(data_file_path)
    
    if not (in_memory or on_disk):
        raise HTTPException(
            status_code=404,
            detail=f"Historical data for {symbol} {timeframe} not found"
        )
    
    # Remove from memory if exists
    if in_memory:
        del HISTORICAL_DATA[key]
    
    # Remove from disk if exists
    if on_disk:
        try:
            os.remove(data_file_path)
        except Exception as e:
            logger.error(f"Error deleting historical data file: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error deleting historical data file: {str(e)}"
            )
    
    return {
        "message": f"Historical data for {symbol} {timeframe} deleted successfully"
    }

# Trade endpoints
@app.post("/api/mvp/trade", response_model=Dict[str, Any])
async def execute_trade(trade_request: TradeRequest, username: str = Depends(get_current_username)):
    """
    Execute a trade using MT5Bridge.
    
    This endpoint instantiates an MT5Bridge in offline mode and uses it to place an order
    or close an existing order.
    
    Args:
        trade_request: The trade request containing action and parameters
        
    Returns:
        Dict[str, Any]: The result of the trade operation
        
    Raises:
        HTTPException: If the trade execution fails
    """
    try:
        # Log the incoming request
        logger.info(f"Trade request received: {trade_request.model_dump()}")
        
        # Create an MT5Bridge instance in offline mode
        bridge = MT5Bridge(offline_mode=True)
        
        # Check if this is a close action
        if trade_request.action == "close" and trade_request.ticket is not None:
            # Close the order using the bridge
            result = bridge.close_order(ticket=trade_request.ticket)
            logger.info(f"Order close request processed: Ticket #{trade_request.ticket}")
            return result
        
        # Otherwise, this is a new order placement
        if not trade_request.symbol or not trade_request.lot or not trade_request.order_type:
            raise ValueError("For new orders, symbol, lot, and order_type are required")
            
        # Place the order using the bridge
        result = bridge.place_order(
            symbol=trade_request.symbol,
            lot=trade_request.lot,
            order_type=trade_request.order_type
        )
        
        # Log the successful result
        logger.info(f"Trade executed successfully: Ticket #{result.get('ticket')}")
        
        # Store the trade in our mock history
        MOCK_TRADES.append(result)
        
        return result
    except ValueError as e:
        # Handle validation errors with 400 Bad Request
        logger.error(f"Validation error in trade request: {str(e)}")
        raise HTTPException(
            status_code=400,
            detail=f"Invalid trade parameters: {str(e)}"
        )
    except RuntimeError as e:
        # Handle runtime errors with 500 Internal Server Error
        logger.error(f"Runtime error in trade execution: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to execute trade: {str(e)}"
        )
    except Exception as e:
        # Handle unexpected errors
        logger.error(f"Unexpected error in trade execution: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while processing your trade"
        )

@app.get("/api/mvp/trades", response_model=List[Dict[str, Any]])
async def get_trades(username: str = Depends(get_current_username)):
    """
    Get the history of executed trades.
    
    Returns:
        List[Dict[str, Any]]: List of executed trades
    """
    return MOCK_TRADES

# Run the server

# Mount Strategy Helper API
app.mount('/api/strategy', strategy_app)

if __name__ == '__main__':
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
