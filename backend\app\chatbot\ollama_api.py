"""
Ollama Chatbot API Endpoints
FastAPI integration for the enhanced strategy chatbot with Ollama
"""

import asyncio
import logging
from fastapi import APIRouter, HTTPException, BackgroundTasks
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any
import json

from ..chatbot.enhanced_strategy_chatbot import EnhancedStrategyChatbot
from ..chatbot.ollama_client import OllamaConfig

logger = logging.getLogger(__name__)

# Global chatbot instance
chatbot_instance: Optional[EnhancedStrategyChatbot] = None

# API Models
class ChatRequest(BaseModel):
    message: str = Field(..., description="User message/strategy request")
    conversation_id: str = Field(default="default", description="Conversation ID for context")
    stream: bool = Field(default=False, description="Stream response")

class ChatResponse(BaseModel):
    message: str
    code: Optional[str] = None
    strategy_type: str
    confidence: float
    source: str  # 'ollama', 'template', 'error'
    model: Optional[str] = None
    strategy_info: Dict[str, Any] = {}
    processing_time: Optional[float] = None

class OllamaStatusResponse(BaseModel):
    available: bool
    model: Optional[str] = None
    models: List[str] = []
    base_url: Optional[str] = None
    message: str
    error: Optional[str] = None

class OllamaConfigRequest(BaseModel):
    base_url: str = Field(default="http://localhost:11435", description="Ollama server URL")
    model: str = Field(default="llama3.1:8b", description="Model name")
    temperature: float = Field(default=0.7, ge=0.0, le=2.0, description="Temperature for generation")
    max_tokens: int = Field(default=2048, ge=1, le=8192, description="Maximum tokens")

# Router
router = APIRouter(prefix="/api/ollama", tags=["Ollama Chatbot"])

async def get_chatbot() -> EnhancedStrategyChatbot:
    """Get or create chatbot instance"""
    global chatbot_instance
    
    if chatbot_instance is None:
        # Default Ollama configuration
        config = OllamaConfig(
            base_url="http://localhost:11435",
            model="llama3.1:8b",  # Popular, capable model
            temperature=0.7,
            max_tokens=2048,
            stream=True
        )
        
        chatbot_instance = EnhancedStrategyChatbot(
            ollama_config=config,
            fallback_to_templates=True
        )
        
        # Initialize the chatbot
        try:
            await chatbot_instance.initialize()
            logger.info("✅ Ollama chatbot initialized successfully")
        except Exception as e:
            logger.warning(f"⚠️ Ollama initialization failed, using template fallback: {e}")
    
    return chatbot_instance

@router.post("/chat", response_model=ChatResponse)
async def chat_with_ollama(request: ChatRequest):
    """
    Chat with the Ollama-powered strategy chatbot
    """
    import time
    start_time = time.time()
    
    try:
        chatbot = await get_chatbot()
        
        # Generate response
        response = await chatbot.chat(request.message, request.conversation_id)
        
        processing_time = time.time() - start_time
        
        return ChatResponse(
            message=response['message'],
            code=response.get('code'),
            strategy_type=response['strategy_type'],
            confidence=response['confidence'],
            source=response['source'],
            model=response.get('model'),
            strategy_info=response.get('strategy_info', {}),
            processing_time=processing_time
        )
        
    except Exception as e:
        logger.error(f"Chat error: {e}")
        raise HTTPException(status_code=500, detail=f"Chat processing failed: {str(e)}")

@router.post("/chat/stream")
async def chat_with_ollama_stream(request: ChatRequest):
    """
    Stream chat responses from Ollama (for real-time typing effect)
    """
    
    async def generate_stream():
        try:
            chatbot = await get_chatbot()
            
            # For streaming, we'll simulate chunk-by-chunk response
            # In a full implementation, this would use Ollama's streaming API
            response = await chatbot.chat(request.message, request.conversation_id)
            
            # Split response into chunks for streaming effect
            message_chunks = response['message'].split(' ')
            code_chunks = response.get('code', '').split('\n') if response.get('code') else []
            
            # Stream message
            for i, chunk in enumerate(message_chunks):
                chunk_data = {
                    'type': 'message_chunk',
                    'content': chunk + ' ',
                    'is_complete': i == len(message_chunks) - 1
                }
                yield f"data: {json.dumps(chunk_data)}\n\n"
                await asyncio.sleep(0.05)  # Small delay for typing effect
            
            # Stream code if present
            if code_chunks:
                yield f"data: {json.dumps({'type': 'code_start'})}\n\n"
                
                for i, line in enumerate(code_chunks):
                    chunk_data = {
                        'type': 'code_chunk',
                        'content': line + '\n',
                        'is_complete': i == len(code_chunks) - 1
                    }
                    yield f"data: {json.dumps(chunk_data)}\n\n"
                    await asyncio.sleep(0.02)
            
            # Final metadata
            final_data = {
                'type': 'complete',
                'strategy_type': response['strategy_type'],
                'confidence': response['confidence'],
                'source': response['source'],
                'strategy_info': response.get('strategy_info', {})
            }
            yield f"data: {json.dumps(final_data)}\n\n"
            
        except Exception as e:
            error_data = {
                'type': 'error',
                'error': str(e)
            }
            yield f"data: {json.dumps(error_data)}\n\n"
    
    return StreamingResponse(
        generate_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )

@router.get("/status", response_model=OllamaStatusResponse)
async def get_ollama_status():
    """
    Get Ollama connection status and available models
    """
    try:
        chatbot = await get_chatbot()
        status = await chatbot.get_ollama_status()
        
        return OllamaStatusResponse(**status)
        
    except Exception as e:
        logger.error(f"Status check error: {e}")
        return OllamaStatusResponse(
            available=False,
            message=f"Status check failed: {str(e)}",
            error=str(e)
        )

@router.post("/configure")
async def configure_ollama(config: OllamaConfigRequest):
    """
    Update Ollama configuration
    """
    global chatbot_instance
    
    try:
        # Create new configuration
        new_config = OllamaConfig(
            base_url=config.base_url,
            model=config.model,
            temperature=config.temperature,
            max_tokens=config.max_tokens
        )
        
        # Cleanup existing chatbot
        if chatbot_instance:
            await chatbot_instance.cleanup()
        
        # Create new chatbot with updated config
        chatbot_instance = EnhancedStrategyChatbot(
            ollama_config=new_config,
            fallback_to_templates=True
        )
        
        # Initialize
        success = await chatbot_instance.initialize()
        
        if success:
            return {
                "success": True,
                "message": f"Successfully configured Ollama with model {config.model}",
                "config": {
                    "base_url": config.base_url,
                    "model": config.model,
                    "temperature": config.temperature,
                    "max_tokens": config.max_tokens
                }
            }
        else:
            return {
                "success": False,
                "message": "Configuration updated but Ollama connection failed. Using template fallback.",
                "config": {
                    "base_url": config.base_url,
                    "model": config.model,
                    "temperature": config.temperature,
                    "max_tokens": config.max_tokens
                }
            }
            
    except Exception as e:
        logger.error(f"Configuration error: {e}")
        raise HTTPException(status_code=500, detail=f"Configuration failed: {str(e)}")

@router.delete("/conversation/{conversation_id}")
async def clear_conversation(conversation_id: str):
    """
    Clear conversation history for a specific conversation ID
    """
    try:
        chatbot = await get_chatbot()
        chatbot.clear_conversation(conversation_id)
        
        return {
            "success": True,
            "message": f"Conversation {conversation_id} cleared"
        }
        
    except Exception as e:
        logger.error(f"Clear conversation error: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to clear conversation: {str(e)}")

@router.get("/models")
async def get_available_models():
    """
    Get list of available Ollama models
    """
    try:
        chatbot = await get_chatbot()
        
        if not chatbot.ollama_available:
            return {
                "success": False,
                "message": "Ollama not available",
                "models": []
            }
        
        models = await chatbot.ollama_client.get_available_models()
        
        return {
            "success": True,
            "models": models,
            "current_model": chatbot.ollama_config.model
        }
        
    except Exception as e:
        logger.error(f"Get models error: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get models: {str(e)}")

@router.post("/pull/{model_name}")
async def pull_model(model_name: str, background_tasks: BackgroundTasks):
    """
    Pull/download a new model (runs in background)
    """
    try:
        chatbot = await get_chatbot()
        
        if not chatbot.ollama_available:
            raise HTTPException(status_code=503, detail="Ollama not available")
        
        # Add background task to pull model
        background_tasks.add_task(pull_model_background, model_name)
        
        return {
            "success": True,
            "message": f"Started downloading model: {model_name}",
            "model": model_name
        }
        
    except Exception as e:
        logger.error(f"Pull model error: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to pull model: {str(e)}")

async def pull_model_background(model_name: str):
    """Background task to pull model"""
    try:
        chatbot = await get_chatbot()
        if chatbot.ollama_client:
            success = await chatbot.ollama_client.pull_model(model_name)
            if success:
                logger.info(f"✅ Successfully pulled model: {model_name}")
            else:
                logger.error(f"❌ Failed to pull model: {model_name}")
    except Exception as e:
        logger.error(f"Background model pull error: {e}")

# Health check endpoint
@router.get("/health")
async def health_check():
    """Simple health check for the Ollama chatbot service"""
    try:
        chatbot = await get_chatbot()
        
        return {
            "status": "healthy",
            "ollama_available": chatbot.ollama_available,
            "fallback_enabled": chatbot.fallback_to_templates,
            "model": chatbot.ollama_config.model if chatbot.ollama_available else None
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }
