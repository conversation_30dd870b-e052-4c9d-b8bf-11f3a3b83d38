#!/usr/bin/env python3
"""
Check if the most recent test files have been implemented
"""

import os
import sys
from pathlib import Path

def check_recent_test_files():
    """Check if the recently requested test files have been implemented"""
    
    print("🔍 CHECKING RECENT TEST FILE IMPLEMENTATION")
    print("=" * 70)
    
    # Define the recently requested test files
    recent_test_files = [
        "shared/test_data_pipeline_comprehensive.py",
        "shared/TEST_EXECUTION_GUIDE.md", 
        "shared/test_trading_services_comprehensive.py",
        "shared/test_integration_comprehensive.py",
        "shared/test_ml_models_comprehensive.py"
    ]
    
    # Define the critical test files from validation script
    critical_test_files = [
        "python_engine/services/darwin_godel/test_strategy_executor_comprehensive.py",
        "python_engine/services/darwin_godel/test_risk_management.py",
        "python_engine/services/darwin_godel/test_portfolio_manager.py",
        "python_engine/services/darwin_godel/test_market_data_processor.py",
        "python_engine/tests/security/test_malicious_strategy_injection.py"
    ]
    
    print("📊 Recent Test Files Status:")
    recent_implemented = []
    recent_missing = []
    recent_total_size = 0
    
    for test_file in recent_test_files:
        full_path = Path(test_file)
        if full_path.exists():
            size = full_path.stat().st_size
            recent_total_size += size
            recent_implemented.append((test_file, size))
            print(f"   ✅ {test_file}: {size:,} bytes")
            
            if size < 500:
                print(f"      ⚠️  File is very small - may be incomplete")
            elif size > 5000:
                print(f"      🎯 Comprehensive implementation")
        else:
            recent_missing.append(test_file)
            print(f"   ❌ {test_file}: MISSING")
    
    print(f"\n📊 Critical Test Files Status (from validation script):")
    critical_implemented = []
    critical_missing = []
    critical_total_size = 0
    
    for test_file in critical_test_files:
        full_path = Path(test_file)
        if full_path.exists():
            size = full_path.stat().st_size
            critical_total_size += size
            critical_implemented.append((test_file, size))
            print(f"   ✅ {test_file}: {size:,} bytes")
            
            if size < 500:
                print(f"      ⚠️  File is very small - may be incomplete")
            elif size > 5000:
                print(f"      🎯 Comprehensive implementation")
        else:
            critical_missing.append(test_file)
            print(f"   ❌ {test_file}: MISSING")
    
    # Summary
    print(f"\n📈 Implementation Summary:")
    print(f"   Recent Files: {len(recent_implemented)}/{len(recent_test_files)} implemented")
    print(f"   Critical Files: {len(critical_implemented)}/{len(critical_test_files)} implemented")
    print(f"   Recent Test Code: {recent_total_size:,} bytes")
    print(f"   Critical Test Code: {critical_total_size:,} bytes")
    
    # Check shared directory structure
    print(f"\n📁 Shared Directory Analysis:")
    shared_path = Path("shared")
    if shared_path.exists():
        test_files = list(shared_path.glob("test_*.py"))
        other_files = list(shared_path.glob("*"))
        print(f"   ✅ shared/ directory exists")
        print(f"   📄 Test files: {len(test_files)}")
        print(f"   📄 Total files: {len(other_files)}")
        
        for test_file in test_files:
            size = test_file.stat().st_size
            print(f"      📄 {test_file.name}: {size:,} bytes")
            
        # Look for the TEST_EXECUTION_GUIDE.md
        guide_path = shared_path / "TEST_EXECUTION_GUIDE.md"
        if guide_path.exists():
            size = guide_path.stat().st_size
            print(f"      📖 TEST_EXECUTION_GUIDE.md: {size:,} bytes")
    else:
        print(f"   ❌ shared/ directory not found")
    
    # Check python_engine directory structure  
    print(f"\n📁 Python Engine Directory Analysis:")
    engine_paths = [
        "python_engine/services/darwin_godel/",
        "python_engine/tests/security/"
    ]
    
    for dir_path in engine_paths:
        path = Path(dir_path)
        if path.exists():
            test_files = list(path.glob("test_*.py"))
            print(f"   ✅ {dir_path}: {len(test_files)} test files")
            for test_file in test_files:
                size = test_file.stat().st_size
                print(f"      📄 {test_file.name}: {size:,} bytes")
        else:
            print(f"   ❌ {dir_path}: Directory not found")
    
    # Provide detailed recommendations
    print(f"\n🎯 Detailed Analysis:")
    
    if len(recent_missing) == 0:
        print("   🎉 All recent test files are implemented!")
        print("   ✅ The shared/ directory contains comprehensive test suites")
    else:
        print(f"   ❌ {len(recent_missing)} recent test files are missing:")
        for missing_file in recent_missing:
            print(f"      - {missing_file}")
    
    if len(critical_missing) == 0:
        print("   🎉 All critical test files from validation script are implemented!")
        print("   ✅ Ready to run emergency TDD validation")
    else:
        print(f"   ❌ {len(critical_missing)} critical test files are missing:")
        for missing_file in critical_missing:
            print(f"      - {missing_file}")
    
    # Overall readiness assessment
    print(f"\n🚀 Overall Readiness Assessment:")
    
    recent_ready = len(recent_missing) == 0
    critical_ready = len(critical_missing) == 0
    
    print(f"   Recent Files: {'✅ COMPLETE' if recent_ready else '❌ INCOMPLETE'}")
    print(f"   Critical Files: {'✅ COMPLETE' if critical_ready else '❌ INCOMPLETE'}")
    
    if recent_ready and critical_ready:
        print(f"\n🎉 IMPLEMENTATION STATUS: FULLY COMPLETE")
        print(f"   ✅ All recent test files implemented")
        print(f"   ✅ All critical test files implemented") 
        print(f"   ✅ Ready to run validation: python validate_emergency_tdd.py")
        return True
    else:
        print(f"\n⚠️  IMPLEMENTATION STATUS: PARTIALLY COMPLETE")
        print(f"   🔧 Next steps:")
        if not recent_ready:
            print(f"      1. Implement missing recent test files")
        if not critical_ready:
            print(f"      2. Implement missing critical test files")
        print(f"      3. Run validation script to verify completeness")
        return False

if __name__ == "__main__":
    success = check_recent_test_files()
    
    if success:
        print(f"\n🎉 ALL TEST FILES IMPLEMENTED - READY FOR VALIDATION!")
    else:
        print(f"\n⚠️  SOME TEST FILES MISSING - IMPLEMENTATION NEEDED")