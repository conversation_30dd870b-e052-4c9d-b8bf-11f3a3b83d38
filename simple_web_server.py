#!/usr/bin/env python3
"""
Simple HTTP server for AI Trading Platform
Uses built-in Python modules only - no external dependencies
"""

import http.server
import socketserver
import json
import urllib.parse
import os
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class TradingPlatformHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        # Set the directory to serve files from
        super().__init__(*args, directory=str(project_root), **kwargs)
    
    def do_GET(self):
        """Handle GET requests"""
        parsed_path = urllib.parse.urlparse(self.path)
        
        # Health check endpoint
        if parsed_path.path == '/health':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            response = {
                "status": "healthy",
                "message": "Simple server running",
                "server": "simple_web_server.py"
            }
            self.wfile.write(json.dumps(response).encode())
            return
        
        # API status endpoint
        if parsed_path.path == '/api/status':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            response = {
                "platform": "AI Trading Platform",
                "server": "Simple HTTP Server",
                "status": "running",
                "features": ["basic_web_interface", "file_serving"]
            }
            self.wfile.write(json.dumps(response).encode())
            return
        
        # Serve index.html for root path
        if parsed_path.path == '/' or parsed_path.path == '':
            self.path = '/index.html'
        
        # Serve static files
        super().do_GET()
    
    def do_POST(self):
        """Handle POST requests"""
        parsed_path = urllib.parse.urlparse(self.path)
        
        # Simple echo endpoint for testing
        if parsed_path.path == '/api/echo':
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length)
            
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            try:
                request_data = json.loads(post_data.decode())
                response = {
                    "echo": request_data,
                    "message": "Data received successfully"
                }
            except:
                response = {
                    "error": "Invalid JSON data",
                    "received": post_data.decode()
                }
            
            self.wfile.write(json.dumps(response).encode())
            return
        
        # Default response for unhandled POST requests
        self.send_response(404)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        response = {"error": "Endpoint not found"}
        self.wfile.write(json.dumps(response).encode())
    
    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

def create_index_html():
    """Create a simple index.html if it doesn't exist"""
    index_path = project_root / "index.html"
    if not index_path.exists():
        html_content = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Trading Platform - Simple Server</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status { padding: 15px; margin: 20px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .endpoint { background: #f8f9fa; padding: 10px; margin: 10px 0; border-left: 4px solid #007bff; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 AI Trading Platform</h1>
        <div class="status success">
            <strong>✅ Simple Server Running!</strong><br>
            This is a basic HTTP server using Python's built-in modules.
        </div>
        
        <h2>Available Endpoints</h2>
        <div class="endpoint">
            <strong>GET /health</strong> - Server health check
        </div>
        <div class="endpoint">
            <strong>GET /api/status</strong> - Platform status
        </div>
        <div class="endpoint">
            <strong>POST /api/echo</strong> - Echo test endpoint
        </div>
        
        <h2>Quick Tests</h2>
        <button onclick="testHealth()">Test Health</button>
        <button onclick="testStatus()">Test Status</button>
        <button onclick="testEcho()">Test Echo</button>
        
        <div id="results"></div>
        
        <h2>Next Steps</h2>
        <div class="info">
            <strong>Server is working!</strong> Now you can:
            <ul>
                <li>Add more API endpoints as needed</li>
                <li>Integrate with your existing components</li>
                <li>Test MT5 integration</li>
                <li>Deploy frontend components</li>
            </ul>
        </div>
    </div>

    <script>
        const results = document.getElementById('results');
        
        async function testHealth() {
            try {
                const response = await fetch('/health');
                const data = await response.json();
                showResult('Health Check', data);
            } catch (error) {
                showResult('Health Check Error', error.message);
            }
        }
        
        async function testStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                showResult('Status Check', data);
            } catch (error) {
                showResult('Status Check Error', error.message);
            }
        }
        
        async function testEcho() {
            try {
                const testData = { message: 'Hello from browser!', timestamp: new Date().toISOString() };
                const response = await fetch('/api/echo', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testData)
                });
                const data = await response.json();
                showResult('Echo Test', data);
            } catch (error) {
                showResult('Echo Test Error', error.message);
            }
        }
        
        function showResult(title, data) {
            const resultDiv = document.createElement('div');
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
            results.appendChild(resultDiv);
        }
    </script>
</body>
</html>"""
        
        with open(index_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"✅ Created index.html at {index_path}")

def find_free_port(start_port=9999):
    """Find a free port starting from start_port"""
    import socket
    for port in range(start_port, start_port + 100):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    return None

def main():
    """Start the simple HTTP server"""
    print("🚀 Starting Simple AI Trading Platform Server...")
    
    # Create index.html if needed
    create_index_html()
    
    # Find a free port
    port = find_free_port(9999)
    if not port:
        print("❌ Could not find a free port!")
        return
    
    print(f"📡 Server will run on http://localhost:{port}")
    print(f"📁 Serving files from: {project_root}")
    
    try:
        with socketserver.TCPServer(("", port), TradingPlatformHandler) as httpd:
            print(f"✅ Server started successfully on port {port}")
            print(f"🌐 Open http://localhost:{port} in your browser")
            print("🔧 Press Ctrl+C to stop the server")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")

if __name__ == "__main__":
    main()
