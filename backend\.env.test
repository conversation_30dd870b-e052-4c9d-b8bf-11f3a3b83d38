# Test Environment Configuration
NODE_ENV=test
PORT=3001
APP_NAME=AI Trading Platform API Test
LOG_LEVEL=error

# Test Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ai_trading_test
DB_USERNAME=postgres
DB_PASSWORD=password
DB_SSL=false
DB_POOL_SIZE=5

# Test Security Configuration
JWT_SECRET=test-jwt-secret-key-for-testing-purposes-only
JWT_EXPIRES_IN=1h
REFRESH_TOKEN_EXPIRES_IN=1d
BCRYPT_ROUNDS=4

# Test CORS Configuration
CORS_ORIGINS=http://localhost:3000

# Test Rate Limiting (more lenient for testing)
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000

# Mock API Keys for Testing
OPENAI_API_KEY=test-openai-key
ANTHROPIC_API_KEY=test-anthropic-key
GOOGLE_API_KEY=test-google-key
COHERE_API_KEY=test-cohere-key

# Test File Upload Configuration
UPLOAD_MAX_SIZE=1048576
UPLOAD_ALLOWED_TYPES=text/csv,application/vnd.ms-excel