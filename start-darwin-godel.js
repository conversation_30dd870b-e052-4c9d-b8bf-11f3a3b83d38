#!/usr/bin/env node

/**
 * Darwin Gödel Machine Startup Script
 * Comprehensive startup script for the AI Enhanced Trading Platform
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

const log = (message, color = 'white') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

const logHeader = (message) => {
  console.log('\n' + '='.repeat(60));
  log(message, 'cyan');
  console.log('='.repeat(60));
};

const logSuccess = (message) => log(`✅ ${message}`, 'green');
const logError = (message) => log(`❌ ${message}`, 'red');
const logWarning = (message) => log(`⚠️  ${message}`, 'yellow');
const logInfo = (message) => log(`ℹ️  ${message}`, 'blue');

class DarwinGodelStarter {
  constructor() {
    this.processes = [];
    this.isShuttingDown = false;
  }

  async start() {
    try {
      logHeader('🚀 Darwin Gödel Machine - AI Enhanced Trading Platform');
      log('Revolutionary AI trading platform with formal mathematical verification', 'magenta');
      
      // Check prerequisites
      await this.checkPrerequisites();
      
      // Setup environment
      await this.setupEnvironment();
      
      // Install dependencies
      await this.installDependencies();
      
      // Start services
      await this.startServices();
      
      // Setup graceful shutdown
      this.setupGracefulShutdown();
      
      logSuccess('Darwin Gödel Machine is now running!');
      this.displayAccessInfo();
      
    } catch (error) {
      logError(`Failed to start Darwin Gödel Machine: ${error.message}`);
      process.exit(1);
    }
  }

  async checkPrerequisites() {
    logHeader('🔍 Checking Prerequisites');
    
    const checks = [
      { name: 'Node.js', command: 'node --version', required: true },
      { name: 'npm', command: 'npm --version', required: true },
      { name: 'Python', command: 'python --version', required: true },
      { name: 'Coq', command: 'coqc --version', required: false }
    ];

    for (const check of checks) {
      try {
        await this.runCommand(check.command);
        logSuccess(`${check.name} is installed`);
      } catch (error) {
        if (check.required) {
          logError(`${check.name} is required but not found`);
          throw new Error(`Missing required dependency: ${check.name}`);
        } else {
          logWarning(`${check.name} is not installed (optional for full functionality)`);
        }
      }
    }
  }

  async setupEnvironment() {
    logHeader('⚙️  Setting up Environment');
    
    // Check if .env exists in backend
    const backendEnvPath = path.join(__dirname, 'backend', '.env');
    const backendEnvExamplePath = path.join(__dirname, 'backend', '.env.example');
    
    if (!fs.existsSync(backendEnvPath) && fs.existsSync(backendEnvExamplePath)) {
      logInfo('Creating .env file from .env.example');
      fs.copyFileSync(backendEnvExamplePath, backendEnvPath);
      logWarning('Please configure your .env file with proper API keys and settings');
    }

    // Create logs directory
    const logsDir = path.join(__dirname, 'backend', 'logs');
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
      logInfo('Created logs directory');
    }

    // Create coq workspace directory
    const coqWorkspaceDir = path.join(__dirname, 'backend', 'coq_workspace');
    if (!fs.existsSync(coqWorkspaceDir)) {
      fs.mkdirSync(coqWorkspaceDir, { recursive: true });
      logInfo('Created Coq workspace directory');
    }

    logSuccess('Environment setup completed');
  }

  async installDependencies() {
    logHeader('📦 Installing Dependencies');
    
    // Install backend dependencies
    logInfo('Installing backend dependencies...');
    await this.runCommand('npm install', { cwd: path.join(__dirname, 'backend') });
    logSuccess('Backend dependencies installed');
    
    // Install frontend dependencies
    logInfo('Installing frontend dependencies...');
    await this.runCommand('npm install', { cwd: path.join(__dirname, 'frontend') });
    logSuccess('Frontend dependencies installed');
    
    // Install Python dependencies
    logInfo('Installing Python dependencies...');
    try {
      await this.runCommand('pip install pandas numpy asyncio subprocess dataclasses typing datetime json random');
      logSuccess('Python dependencies installed');
    } catch (error) {
      logWarning('Some Python dependencies may not have installed correctly');
    }
  }

  async startServices() {
    logHeader('🚀 Starting Services');
    
    // Start backend server
    logInfo('Starting Darwin Gödel Machine backend server...');
    const backendProcess = spawn('npm', ['run', 'dev'], {
      cwd: path.join(__dirname, 'backend'),
      stdio: ['pipe', 'pipe', 'pipe'],
      shell: true
    });
    
    this.processes.push({ name: 'Backend', process: backendProcess });
    
    backendProcess.stdout.on('data', (data) => {
      const output = data.toString().trim();
      if (output) {
        log(`[Backend] ${output}`, 'blue');
      }
    });
    
    backendProcess.stderr.on('data', (data) => {
      const output = data.toString().trim();
      if (output && !output.includes('ExperimentalWarning')) {
        log(`[Backend Error] ${output}`, 'red');
      }
    });
    
    // Wait a bit for backend to start
    await this.sleep(3000);
    
    // Start frontend server
    logInfo('Starting frontend development server...');
    const frontendProcess = spawn('npm', ['run', 'dev'], {
      cwd: path.join(__dirname, 'frontend'),
      stdio: ['pipe', 'pipe', 'pipe'],
      shell: true
    });
    
    this.processes.push({ name: 'Frontend', process: frontendProcess });
    
    frontendProcess.stdout.on('data', (data) => {
      const output = data.toString().trim();
      if (output) {
        log(`[Frontend] ${output}`, 'green');
      }
    });
    
    frontendProcess.stderr.on('data', (data) => {
      const output = data.toString().trim();
      if (output && !output.includes('ExperimentalWarning')) {
        log(`[Frontend Error] ${output}`, 'red');
      }
    });
    
    // Wait for services to fully start
    await this.sleep(5000);
    
    logSuccess('All services started successfully');
  }

  setupGracefulShutdown() {
    const shutdown = async (signal) => {
      if (this.isShuttingDown) return;
      this.isShuttingDown = true;
      
      logHeader(`🛑 Shutting down Darwin Gödel Machine (${signal})`);
      
      for (const { name, process } of this.processes) {
        logInfo(`Stopping ${name}...`);
        process.kill('SIGTERM');
      }
      
      // Wait for processes to terminate
      await this.sleep(2000);
      
      logSuccess('Darwin Gödel Machine shutdown complete');
      process.exit(0);
    };

    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    
    // Handle Windows Ctrl+C
    if (process.platform === 'win32') {
      const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
      });
      
      rl.on('SIGINT', () => shutdown('SIGINT'));
    }
  }

  displayAccessInfo() {
    logHeader('🌐 Access Information');
    
    const info = [
      { service: 'Frontend (React App)', url: 'http://localhost:3000', description: 'Main trading interface' },
      { service: 'Backend API', url: 'http://localhost:3001', description: 'Darwin Gödel Machine API' },
      { service: 'API Documentation', url: 'http://localhost:3001/api/docs', description: 'Complete API reference' },
      { service: 'Health Check', url: 'http://localhost:3001/health', description: 'System health status' },
      { service: 'WebSocket', url: 'ws://localhost:3001', description: 'Real-time updates' }
    ];
    
    info.forEach(({ service, url, description }) => {
      log(`${service}:`, 'cyan');
      log(`  URL: ${url}`, 'white');
      log(`  Description: ${description}`, 'yellow');
      console.log();
    });
    
    logHeader('🎯 Quick Start Guide');
    log('1. Open http://localhost:3000 in your browser', 'white');
    log('2. Navigate to "DGM Experiments" page', 'white');
    log('3. Click on "Trading Oracle" tab', 'white');
    log('4. Try asking: "Analyze EUR/USD 4H chart"', 'white');
    log('5. Or: "Evolve strategies for GBP/JPY 1H"', 'white');
    
    logHeader('🔧 Configuration');
    log('• Edit backend/.env for API keys and settings', 'white');
    log('• OPENAI_API_KEY is required for S3 Core NLP', 'white');
    log('• COQ_PATH for formal verification (optional)', 'white');
    
    logHeader('📚 Features Available');
    log('✅ Natural Language Trading Interface (S3 Core)', 'green');
    log('✅ Strategy Evolution (Darwin Engine)', 'green');
    log('✅ Formal Verification (Coq Integration)', 'green');
    log('✅ Real-time Market Analysis', 'green');
    log('✅ WebSocket Communication', 'green');
    log('✅ Comprehensive API', 'green');
    
    console.log('\n' + '='.repeat(60));
    log('🎉 Darwin Gödel Machine is ready to revolutionize your trading!', 'magenta');
    log('Press Ctrl+C to stop all services', 'yellow');
    console.log('='.repeat(60) + '\n');
  }

  runCommand(command, options = {}) {
    return new Promise((resolve, reject) => {
      exec(command, options, (error, stdout, stderr) => {
        if (error) {
          reject(error);
        } else {
          resolve(stdout);
        }
      });
    });
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Start the Darwin Gödel Machine
if (require.main === module) {
  const starter = new DarwinGodelStarter();
  starter.start().catch((error) => {
    logError(`Startup failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = DarwinGodelStarter;