#!/usr/bin/env python3
"""
AI Enhanced Trading Platform - Interactive Demo
This script demonstrates the platform's capabilities in a safe environment.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.trading_platform_core.trading_platform import create_trading_platform, TradingPlatformConfig
from src.trading.mt5_integration import TradingMode

async def demo_platform():
    """Interactive demo of the AI Enhanced Trading Platform"""
    
    print("🚀 AI Enhanced Trading Platform - Interactive Demo")
    print("=" * 60)
    
    # Create safe configuration for demo
    print("\n📋 Creating platform configuration...")
    config = TradingPlatformConfig()
    
    # Safe demo settings
    config.trading_mode = TradingMode.DUMMY  # Safe dummy mode
    config.max_daily_loss = 100.0           # Conservative limit
    config.max_position_size = 0.01         # Very small positions
    config.max_open_positions = 2           # Limited positions
    config.allowed_symbols = ["EURUSD"]     # Single symbol for demo
    config.ai_population_size = 10          # Smaller population for demo
    config.ai_generations = 5               # Limited generations
    config.monitoring_interval = 2.0       # Slower monitoring for demo
    config.auto_start_trading = False      # Manual control
    config.enable_chatbot = True           # Enable chatbot
    
    print(f"✅ Configuration created:")
    print(f"   - Trading Mode: {config.trading_mode.value}")
    print(f"   - Max Daily Loss: ${config.max_daily_loss}")
    print(f"   - Max Position Size: {config.max_position_size} lots")
    print(f"   - Allowed Symbols: {config.allowed_symbols}")
    print(f"   - AI Population: {config.ai_population_size} strategies")
    
    # Create platform
    print("\n🏗️ Creating trading platform...")
    platform = create_trading_platform(config)
    
    # Initialize platform
    print("\n⚙️ Initializing platform components...")
    if await platform.initialize():
        print("✅ Platform initialized successfully!")
        
        # Display platform status
        status = platform.get_platform_status()
        print(f"\n📊 Platform Status:")
        print(f"   - Initialized: {status['is_initialized']}")
        print(f"   - Running: {status['is_running']}")
        print(f"   - Trading Mode: {status['trading_mode']}")
        print(f"   - Data Sources: {status['data_sources']}")
        
        # Start platform
        print("\n🚀 Starting platform...")
        if await platform.start():
            print("✅ Platform started successfully!")
            
            # Demo various features
            await demo_features(platform)
            
        else:
            print("❌ Failed to start platform")
    else:
        print("❌ Failed to initialize platform")
        return

async def demo_features(platform):
    """Demonstrate various platform features"""
    
    print("\n" + "=" * 60)
    print("🎯 PLATFORM FEATURES DEMONSTRATION")
    print("=" * 60)
    
    # 1. Platform Status
    print("\n1️⃣ Platform Status Check")
    print("-" * 30)
    status = platform.get_platform_status()
    for key, value in status.items():
        print(f"   {key}: {value}")
    
    # 2. AI Evolution Status
    print("\n2️⃣ AI Evolution Status")
    print("-" * 30)
    ai_status = platform.get_ai_evolution_status()
    for key, value in ai_status.items():
        print(f"   {key}: {value}")
    
    # 3. Performance Monitoring
    print("\n3️⃣ Performance Monitoring")
    print("-" * 30)
    performance = platform.get_performance_summary(minutes=5)
    if performance:
        print("   Performance data available:")
        for key, value in performance.items():
            if isinstance(value, dict):
                print(f"   {key}:")
                for sub_key, sub_value in value.items():
                    print(f"     {sub_key}: {sub_value}")
            else:
                print(f"   {key}: {value}")
    else:
        print("   No performance data yet (platform just started)")
    
    # 4. Chatbot Interaction
    print("\n4️⃣ Chatbot Knowledge System")
    print("-" * 30)
    
    questions = [
        "What is a stop loss?",
        "How does risk management work?",
        "What is the Darwin-Gödel Machine?",
        "How do I monitor performance?"
    ]
    
    for question in questions:
        print(f"   Q: {question}")
        try:
            response = await platform.query_chatbot(question)
            # Truncate long responses for demo
            if len(response) > 100:
                response = response[:100] + "..."
            print(f"   A: {response}")
        except Exception as e:
            print(f"   A: Error querying chatbot: {e}")
        print()
    
    # 5. Trading Signals (if any)
    print("\n5️⃣ Trading Signals")
    print("-" * 30)
    signals = platform.get_trading_signals()
    if signals:
        print(f"   Active signals: {len(signals)}")
        for i, signal in enumerate(signals[:3]):  # Show first 3
            print(f"   Signal {i+1}: {signal['symbol']} {signal['action']} (Confidence: {signal['confidence']:.2f})")
    else:
        print("   No active trading signals (platform just started)")
    
    # 6. Wait and show real-time updates
    print("\n6️⃣ Real-Time Monitoring (10 seconds)")
    print("-" * 30)
    
    for i in range(5):
        await asyncio.sleep(2)
        status = platform.get_platform_status()
        signals = platform.get_trading_signals()
        print(f"   Update {i+1}: Active Positions: {status['active_positions']}, Pending Signals: {status['pending_signals']}")
    
    # 7. Graceful shutdown demo
    print("\n7️⃣ Graceful Shutdown")
    print("-" * 30)
    print("   Initiating graceful shutdown...")
    await platform.shutdown()
    print("   ✅ Platform shutdown completed")

def main():
    """Main demo function"""
    try:
        # Run the async demo
        asyncio.run(demo_platform())
        
        print("\n" + "=" * 60)
        print("🎉 DEMO COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print("\n📚 Next Steps:")
        print("   1. Review the comprehensive documentation in README_COMPREHENSIVE.md")
        print("   2. Explore individual component tests in the tests/ directory")
        print("   3. Customize configuration for your specific needs")
        print("   4. Start with dummy mode before considering live trading")
        print("   5. Always prioritize safety and risk management")
        
        print("\n⚠️  IMPORTANT SAFETY REMINDER:")
        print("   - This demo runs in DUMMY mode (no real trades)")
        print("   - Always test thoroughly before live trading")
        print("   - Use conservative risk limits")
        print("   - Monitor all trading activities closely")
        
    except KeyboardInterrupt:
        print("\n\n🛑 Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()