# Python MT5 Strategy Platform - React Project Setup

## Project Overview
A modern trading platform for building, backtesting, and deploying Python strategies directly to MetaTrader 5 (MT5) using a React frontend and Python backend.

---

## 1. Create a New Project Folder
```powershell
mkdir python-mt5-strategy-platform
cd python-mt5-strategy-platform
```

## 2. Initialize a Git Repository
```powershell
git init
```

## 3. Scaffold a React App (Recommended: Vite)
```powershell
npm create vite@latest . -- --template react
```
- When prompted, choose `react` (or `react-ts` for TypeScript).

## 4. Install Dependencies
```powershell
npm install
```

## 5. Add a .gitignore File
Create a `.gitignore` file with:
```
node_modules/
dist/
.env
```

## 6. Run the Development Server
```powershell
npm run dev
```
- Visit the local URL shown in your terminal (usually http://localhost:5173).

## 7. Make Your First Commit
```powershell
git add .
git commit -m "Initial React app setup"
```

## 8. Start Building!
- Edit `src/App.jsx` (or `src/App.tsx` for TypeScript) to begin your UI.
- Add components in `src/components/`.
- Connect to your backend via API calls as needed.

---

## Recommended Next Steps
- Set up backend (Flask or FastAPI) in a separate folder or repo.
- Design core UI components: StrategyBuilder, BacktestPanel, MT5Bridge, Chatbot.
- Document your workflow and architecture as you build.

---

## Notes
- This file serves as a quick reference for project setup and onboarding.
- Update with additional steps, architecture decisions, and useful commands as the project evolves.
