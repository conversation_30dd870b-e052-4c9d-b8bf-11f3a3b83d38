"""
Position CRUD operations
"""

import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update, delete

from ..models import Position

async def get_position_by_id(db: AsyncSession, position_id: uuid.UUID) -> Optional[Position]:
    """Get position by ID"""
    result = await db.execute(select(Position).where(Position.id == position_id))
    return result.scalars().first()

async def get_position_by_mt5_id(db: AsyncSession, mt5_account_id: uuid.UUID, mt5_position_id: int) -> Optional[Position]:
    """Get position by MT5 position ID"""
    result = await db.execute(
        select(Position).where(
            Position.mt5_account_id == mt5_account_id,
            Position.mt5_position_id == mt5_position_id
        )
    )
    return result.scalars().first()

async def get_positions_by_account(db: AsyncSession, mt5_account_id: uuid.UUID, is_closed: bool = False) -> List[Position]:
    """Get all positions for an MT5 account"""
    result = await db.execute(
        select(Position).where(
            Position.mt5_account_id == mt5_account_id,
            Position.is_closed == is_closed
        )
    )
    return result.scalars().all()

async def get_positions_by_strategy(db: AsyncSession, strategy_id: uuid.UUID, is_closed: bool = False) -> List[Position]:
    """Get all positions for a strategy"""
    result = await db.execute(
        select(Position).where(
            Position.strategy_id == strategy_id,
            Position.is_closed == is_closed
        )
    )
    return result.scalars().all()

async def create_position(
    db: AsyncSession,
    mt5_account_id: uuid.UUID,
    mt5_position_id: int,
    symbol: str,
    order_type: str,
    volume: float,
    open_price: float,
    open_time: datetime,
    stop_loss: Optional[float] = None,
    take_profit: Optional[float] = None,
    strategy_id: Optional[uuid.UUID] = None
) -> Position:
    """Create a new position"""
    db_position = Position(
        mt5_account_id=mt5_account_id,
        mt5_position_id=mt5_position_id,
        symbol=symbol,
        order_type=order_type,
        volume=volume,
        open_price=open_price,
        open_time=open_time,
        stop_loss=stop_loss,
        take_profit=take_profit,
        strategy_id=strategy_id,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    db.add(db_position)
    await db.commit()
    await db.refresh(db_position)
    return db_position

async def update_position(
    db: AsyncSession,
    position_id: uuid.UUID,
    position_data: Dict[str, Any]
) -> Optional[Position]:
    """Update position"""
    # Add updated_at timestamp
    position_data["updated_at"] = datetime.utcnow()
    
    # Update the position
    await db.execute(
        update(Position)
        .where(Position.id == position_id)
        .values(**position_data)
    )
    await db.commit()
    
    # Return the updated position
    return await get_position_by_id(db, position_id)

async def close_position(
    db: AsyncSession,
    position_id: uuid.UUID,
    close_price: float,
    close_time: datetime,
    profit: float
) -> Optional[Position]:
    """Close a position"""
    position = await get_position_by_id(db, position_id)
    if not position:
        return None
    
    position.close_price = close_price
    position.close_time = close_time
    position.profit = profit
    position.is_closed = True
    position.updated_at = datetime.utcnow()
    
    await db.commit()
    await db.refresh(position)
    return position