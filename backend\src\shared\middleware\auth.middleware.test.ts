import { Request, Response, NextFunction } from 'express';
import { authMiddleware } from './auth.middleware';
import { AuthService } from '../../features/auth/auth.service';
import { getMockUser } from '../../test-factories/user.factory';
import { createMockRequest, createMockResponse, createMockNext } from '../../test-setup';

// Mock AuthService
jest.mock('../../features/auth/auth.service');

describe('authMiddleware', () => {
  let mockAuthService: jest.Mocked<AuthService>;
  let req: Request;
  let res: Response;
  let next: NextFunction;

  beforeEach(() => {
    mockAuthService = {
      verifyToken: jest.fn(),
    } as any;
    
    req = createMockRequest();
    res = createMockResponse();
    next = createMockNext();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should authenticate user with valid token', async () => {
    // Arrange
    const user = getMockUser();
    const token = 'valid-jwt-token';
    req.headers.authorization = `Bearer ${token}`;
    
    mockAuthService.verifyToken.mockResolvedValue({
      success: true,
      data: user,
    });

    const middleware = authMiddleware(mockAuthService);

    // Act
    await middleware(req, res, next);

    // Assert
    expect(mockAuthService.verifyToken).toHaveBeenCalledWith(token);
    expect(req.user).toEqual(user);
    expect(next).toHaveBeenCalledWith();
    expect(res.status).not.toHaveBeenCalled();
  });

  it('should reject request without authorization header', async () => {
    // Arrange
    delete req.headers.authorization;
    const middleware = authMiddleware(mockAuthService);

    // Act
    await middleware(req, res, next);

    // Assert
    expect(res.status).toHaveBeenCalledWith(401);
    expect(res.json).toHaveBeenCalledWith({
      success: false,
      error: {
        code: 'AUTHENTICATION_ERROR',
        message: 'Authentication token required',
      },
    });
    expect(next).not.toHaveBeenCalled();
  });

  it('should reject request with invalid authorization format', async () => {
    // Arrange
    req.headers.authorization = 'InvalidFormat token';
    const middleware = authMiddleware(mockAuthService);

    // Act
    await middleware(req, res, next);

    // Assert
    expect(res.status).toHaveBeenCalledWith(401);
    expect(res.json).toHaveBeenCalledWith({
      success: false,
      error: {
        code: 'AUTHENTICATION_ERROR',
        message: 'Invalid token format',
      },
    });
    expect(next).not.toHaveBeenCalled();
  });

  it('should reject request with invalid token', async () => {
    // Arrange
    const token = 'invalid-token';
    req.headers.authorization = `Bearer ${token}`;
    
    mockAuthService.verifyToken.mockResolvedValue({
      success: false,
      error: {
        code: 'INVALID_TOKEN',
        message: 'Invalid token',
      },
    });

    const middleware = authMiddleware(mockAuthService);

    // Act
    await middleware(req, res, next);

    // Assert
    expect(mockAuthService.verifyToken).toHaveBeenCalledWith(token);
    expect(res.status).toHaveBeenCalledWith(401);
    expect(res.json).toHaveBeenCalledWith({
      success: false,
      error: {
        code: 'INVALID_TOKEN',
        message: 'Invalid token',
      },
    });
    expect(next).not.toHaveBeenCalled();
  });

  it('should handle token verification errors', async () => {
    // Arrange
    const token = 'valid-token';
    req.headers.authorization = `Bearer ${token}`;
    
    mockAuthService.verifyToken.mockRejectedValue(new Error('Service error'));

    const middleware = authMiddleware(mockAuthService);

    // Act
    await middleware(req, res, next);

    // Assert
    expect(res.status).toHaveBeenCalledWith(500);
    expect(res.json).toHaveBeenCalledWith({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Token verification failed',
      },
    });
    expect(next).not.toHaveBeenCalled();
  });

  it('should handle bearer token with extra spaces', async () => {
    // Arrange
    const user = getMockUser();
    const token = 'valid-token';
    req.headers.authorization = `  Bearer   ${token}  `;
    
    mockAuthService.verifyToken.mockResolvedValue({
      success: true,
      data: user,
    });

    const middleware = authMiddleware(mockAuthService);

    // Act
    await middleware(req, res, next);

    // Assert
    expect(mockAuthService.verifyToken).toHaveBeenCalledWith(token);
    expect(req.user).toEqual(user);
    expect(next).toHaveBeenCalledWith();
  });

  it('should handle empty bearer token', async () => {
    // Arrange
    req.headers.authorization = 'Bearer ';
    const middleware = authMiddleware(mockAuthService);

    // Act
    await middleware(req, res, next);

    // Assert
    expect(res.status).toHaveBeenCalledWith(401);
    expect(res.json).toHaveBeenCalledWith({
      success: false,
      error: {
        code: 'AUTHENTICATION_ERROR',
        message: 'Invalid token format',
      },
    });
    expect(next).not.toHaveBeenCalled();
  });
});