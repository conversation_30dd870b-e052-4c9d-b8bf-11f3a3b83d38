"""
Demo script showing how to use the MT5 bridge with Python strategies
"""

import requests
import json
import sys
import os
from datetime import datetime
import time
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# API URL - change to match your deployment
API_URL = "http://localhost:8000/api/v1"

def connect_to_mt5(login: str, password: str, server: str) -> dict:
    """
    Connect to MT5 server
    """
    url = f"{API_URL}/mt5/connect"
    payload = {
        "server": server,
        "login": login,
        "password": password
    }
    
    response = requests.post(url, json=payload)
    return response.json()

def check_mt5_status(login: str, server: str) -> dict:
    """
    Check MT5 connection status
    """
    url = f"{API_URL}/mt5/status?login={login}&server={server}"
    response = requests.get(url)
    return response.json()

def place_order(login: str, server: str, symbol: str, order_type: str, volume: float, price: float | None = None) -> dict:
    """
    Place an order via MT5
    """
    url = f"{API_URL}/mt5/order"
    
    payload = {
        "symbol": symbol,
        "order_type": order_type,
        "volume": volume,
    }
    
    if price is not None:
        payload["price"] = price
    
    params = {
        "login": login,
        "server": server
    }
    
    response = requests.post(url, params=params, json=payload)
    return response.json()

def upload_strategy(name: str, code_file: str, description: str = "") -> dict:
    """
    Upload a Python strategy
    """
    url = f"{API_URL}/mt5/strategy"
    
    with open(code_file, "r") as f:
        code = f.read()
    
    payload = {
        "name": name,
        "code": code,
        "description": description
    }
    
    response = requests.post(url, json=payload)
    return response.json()

def list_strategies() -> list:
    """
    List all available strategies
    """
    url = f"{API_URL}/mt5/strategies"
    response = requests.get(url)
    return response.json()

def execute_strategy(login: str, server: str, strategy_name: str, symbol: str, timeframe: str = "M15") -> dict:
    """
    Execute a strategy on MT5
    """
    url = f"{API_URL}/mt5/execute_strategy"
    
    params = {
        "login": login,
        "server": server,
        "strategy_name": strategy_name,
        "symbol": symbol,
        "timeframe": timeframe
    }
    
    response = requests.post(url, params=params)
    return response.json()

def demo_mt5_bridge():
    """
    Run a complete demo of the MT5 bridge
    """
    # MT5 account details - use your own test account
    login = "********"
    password = "demo_password"
    server = "ICMarkets-Demo"
    
    # Connect to MT5
    logger.info("Connecting to MT5...")
    connection_result = connect_to_mt5(login, password, server)
    print(json.dumps(connection_result, indent=2))
    
    if connection_result.get("connected"):
        # Check status
        logger.info("Checking MT5 status...")
        status_result = check_mt5_status(login, server)
        print(json.dumps(status_result, indent=2))
        
        # Upload strategies
        ma_strategy_path = os.path.join(os.path.dirname(__file__), "backend/storage/strategies/ma_crossover_strategy.py")
        if os.path.exists(ma_strategy_path):
            logger.info("Uploading MA Crossover strategy...")
            ma_upload_result = upload_strategy(
                "ma_crossover.py", 
                ma_strategy_path, 
                "Moving Average Crossover Strategy"
            )
            print(json.dumps(ma_upload_result, indent=2))
        
        rsi_strategy_path = os.path.join(os.path.dirname(__file__), "backend/storage/strategies/rsi_divergence_strategy.py")
        if os.path.exists(rsi_strategy_path):
            logger.info("Uploading RSI Divergence strategy...")
            rsi_upload_result = upload_strategy(
                "rsi_divergence.py", 
                rsi_strategy_path, 
                "RSI Divergence Strategy"
            )
            print(json.dumps(rsi_upload_result, indent=2))
        
        # List strategies
        logger.info("Listing strategies...")
        strategies = list_strategies()
        print(json.dumps(strategies, indent=2))
        
        # Execute a strategy
        logger.info("Executing MA Crossover strategy...")
        execution_result = execute_strategy(
            login=login,
            server=server,
            strategy_name="ma_crossover.py",
            symbol="EURUSD",
            timeframe="H1"
        )
        print(json.dumps(execution_result, indent=2))
        
        # Place a manual order
        logger.info("Placing a manual buy order...")
        order_result = place_order(
            login=login,
            server=server,
            symbol="EURUSD",
            order_type="buy",
            volume=0.1
        )
        print(json.dumps(order_result, indent=2))
        
    else:
        logger.error("Failed to connect to MT5")

if __name__ == "__main__":
    demo_mt5_bridge()
