"""
Property-Based Tests for Portfolio Management

Tests portfolio allocation, risk management, and trading logic
using property-based testing with Hypothesis to verify invariants.
"""

import pytest
import sys
import os
from hypothesis import given, strategies as st, assume, settings, example
from hypothesis.stateful import RuleBasedStateMachine, rule, invariant, initialize
from typing import Dict, List, Any, Optional
from decimal import Decimal

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

from services.darwin_godel.strategy_verifier import DarwinGodelVerifier
from services.darwin_godel.secure_executor import SecureStrategyExecutor

class TestPortfolioProperties:
    """Property-based tests for portfolio management invariants"""
    
    def setup_method(self):
        """Setup before each test"""
        self.verifier = DarwinGodelVerifier()
        self.executor = SecureStrategyExecutor()
    
    @given(st.floats(min_value=0.01, max_value=1000000.0))
    @example(100.0)
    @example(1000.0)
    @example(10000.0)
    def test_portfolio_allocation_properties(self, allocation_amount):
        """Test that portfolio allocation always maintains invariants"""
        # Assume reasonable allocation amounts
        assume(allocation_amount > 0)
        assume(allocation_amount < 1000000)  # Max 1M for testing
        
        # Portfolio allocation strategy
        allocation_strategy = f"""
def trading_strategy(data, params):
    allocation = {allocation_amount}
    
    if len(data['close']) < 2:
        return {{'signal': 'hold', 'confidence': 0.5, 'allocation': 0}}
    
    current_price = data['close'][-1]
    prev_price = data['close'][-2]
    
    # Calculate position size based on allocation
    if current_price > 0:
        max_shares = allocation / current_price
        
        if prev_price > 0:
            price_change = (current_price - prev_price) / prev_price
            
            if price_change > 0.01:  # 1% increase
                position_size = min(max_shares * 0.5, allocation * 0.1 / current_price)
                return {{'signal': 'buy', 'confidence': 0.8, 'allocation': position_size * current_price}}
            elif price_change < -0.01:  # 1% decrease
                return {{'signal': 'sell', 'confidence': 0.8, 'allocation': 0}}
    
    return {{'signal': 'hold', 'confidence': 0.5, 'allocation': 0}}
"""
        
        # Test data
        test_data = {
            'close': [100.0, 101.0, 102.0, 101.5, 103.0],
            'high': [101.0, 102.0, 103.0, 102.5, 104.0],
            'low': [99.0, 100.0, 101.0, 100.5, 102.0],
            'volume': [10000] * 5
        }
        
        # Execute strategy
        result = self.executor.execute_strategy(allocation_strategy, test_data, {})
        
        # Verify invariants
        assert 'signal' in result
        assert result['signal'] in ['buy', 'sell', 'hold']
        assert 'confidence' in result
        assert 0.0 <= result['confidence'] <= 1.0
        
        # Portfolio-specific invariants
        if 'allocation' in result:
            # Allocation should never exceed the available amount
            assert result['allocation'] >= 0, f"Negative allocation: {result['allocation']}"
            assert result['allocation'] <= allocation_amount, f"Allocation {result['allocation']} exceeds limit {allocation_amount}"
            
            # If buying, allocation should be positive
            if result['signal'] == 'buy':
                assert result['allocation'] >= 0, "Buy signal should have non-negative allocation"
            
            # If selling or holding, allocation can be zero
            if result['signal'] in ['sell', 'hold']:
                assert result['allocation'] >= 0, "Allocation should never be negative"
    
    @given(
        st.lists(
            st.floats(min_value=50.0, max_value=200.0),
            min_size=5,
            max_size=100
        )
    )
    def test_price_data_invariants(self, prices):
        """Test that strategy handles various price sequences correctly"""
        assume(all(p > 0 for p in prices))  # All prices must be positive
        assume(len(set(prices)) > 1)  # Prices should have some variation
        
        # Create market data from price sequence
        test_data = {
            'close': prices,
            'high': [p * 1.01 for p in prices],
            'low': [p * 0.99 for p in prices],
            'volume': [10000] * len(prices)
        }
        
        # Simple momentum strategy
        strategy = """
def trading_strategy(data, params):
    if len(data['close']) < 2:
        return {'signal': 'hold', 'confidence': 0.5}
    
    current_price = data['close'][-1]
    prev_price = data['close'][-2]
    
    if current_price > prev_price * 1.005:  # 0.5% increase
        return {'signal': 'buy', 'confidence': 0.7}
    elif current_price < prev_price * 0.995:  # 0.5% decrease
        return {'signal': 'sell', 'confidence': 0.7}
    else:
        return {'signal': 'hold', 'confidence': 0.4}
"""
        
        result = self.executor.execute_strategy(strategy, test_data, {})
        
        # Verify basic invariants
        assert 'signal' in result
        assert result['signal'] in ['buy', 'sell', 'hold']
        assert 'confidence' in result
        assert 0.0 <= result['confidence'] <= 1.0
        
        # Price-specific invariants
        current_price = prices[-1]
        prev_price = prices[-2]
        price_change_ratio = current_price / prev_price
        
        # Verify signal logic consistency
        if price_change_ratio > 1.005:
            assert result['signal'] == 'buy', f"Should buy on {price_change_ratio:.4f} increase"
        elif price_change_ratio < 0.995:
            assert result['signal'] == 'sell', f"Should sell on {price_change_ratio:.4f} decrease"
        else:
            assert result['signal'] == 'hold', f"Should hold on {price_change_ratio:.4f} change"
    
    @given(
        st.floats(min_value=0.01, max_value=0.99),  # Risk tolerance
        st.floats(min_value=1000.0, max_value=100000.0),  # Portfolio value
        st.floats(min_value=0.001, max_value=0.1)  # Volatility
    )
    def test_risk_management_properties(self, risk_tolerance, portfolio_value, volatility):
        """Test risk management invariants"""
        assume(0 < risk_tolerance < 1)
        assume(portfolio_value > 0)
        assume(volatility > 0)
        
        # Risk-managed strategy
        risk_strategy = f"""
def trading_strategy(data, params):
    risk_tolerance = {risk_tolerance}
    portfolio_value = {portfolio_value}
    expected_volatility = {volatility}
    
    if len(data['close']) < 10:
        return {{'signal': 'hold', 'confidence': 0.5, 'risk_score': 0.5}}
    
    # Calculate recent volatility
    prices = data['close'][-10:]
    returns = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices))]
    avg_return = sum(returns) / len(returns)
    variance = sum((r - avg_return) ** 2 for r in returns) / len(returns)
    actual_volatility = variance ** 0.5
    
    # Risk-adjusted position sizing
    risk_score = min(actual_volatility / expected_volatility, 2.0)  # Cap at 2x expected
    position_size_factor = max(0.1, 1.0 - risk_score * 0.5)  # Reduce size with higher risk
    
    # Trading logic
    current_price = prices[-1]
    sma = sum(prices[-5:]) / 5
    
    max_position_value = portfolio_value * risk_tolerance * position_size_factor
    
    if current_price > sma * 1.02 and risk_score < 1.5:
        return {{
            'signal': 'buy',
            'confidence': 0.8 * (1.0 - risk_score * 0.2),
            'risk_score': risk_score,
            'max_position_value': max_position_value
        }}
    elif current_price < sma * 0.98:
        return {{
            'signal': 'sell',
            'confidence': 0.8,
            'risk_score': risk_score,
            'max_position_value': 0
        }}
    else:
        return {{
            'signal': 'hold',
            'confidence': 0.4,
            'risk_score': risk_score,
            'max_position_value': 0
        }}
"""
        
        # Generate test data with specified volatility characteristics
        import random
        prices = [100.0]
        for _ in range(20):
            change = random.gauss(0, volatility)  # Normal distribution with specified volatility
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 1.0))  # Ensure positive prices
        
        test_data = {
            'close': prices,
            'high': [p * 1.005 for p in prices],
            'low': [p * 0.995 for p in prices],
            'volume': [10000] * len(prices)
        }
        
        result = self.executor.execute_strategy(risk_strategy, test_data, {})
        
        # Verify risk management invariants
        assert 'signal' in result
        assert 'confidence' in result
        assert 'risk_score' in result
        
        # Risk score should be reasonable
        assert result['risk_score'] >= 0, "Risk score should be non-negative"
        assert result['risk_score'] <= 10.0, "Risk score should be bounded"
        
        # Position sizing should respect risk tolerance
        if 'max_position_value' in result:
            max_allowed = portfolio_value * risk_tolerance
            assert result['max_position_value'] <= max_allowed, \
                f"Position value {result['max_position_value']} exceeds risk limit {max_allowed}"
            assert result['max_position_value'] >= 0, "Position value should be non-negative"
        
        # Confidence should be inversely related to risk
        if result['risk_score'] > 1.5:
            assert result['confidence'] <= 0.7, "High risk should reduce confidence"
    
    @given(
        st.lists(
            st.tuples(
                st.floats(min_value=50.0, max_value=200.0),  # price
                st.integers(min_value=1000, max_value=50000)  # volume
            ),
            min_size=10,
            max_size=50
        )
    )
    def test_volume_analysis_properties(self, price_volume_data):
        """Test volume analysis invariants"""
        assume(len(price_volume_data) >= 10)
        
        prices = [pv[0] for pv in price_volume_data]
        volumes = [pv[1] for pv in price_volume_data]
        
        assume(all(p > 0 for p in prices))
        assume(all(v > 0 for v in volumes))
        
        test_data = {
            'close': prices,
            'high': [p * 1.01 for p in prices],
            'low': [p * 0.99 for p in prices],
            'volume': volumes
        }
        
        # Volume-weighted strategy
        volume_strategy = """
def trading_strategy(data, params):
    if len(data['close']) < 5:
        return {'signal': 'hold', 'confidence': 0.5}
    
    prices = data['close'][-5:]
    volumes = data['volume'][-5:]
    
    # Volume-weighted average price
    total_volume = sum(volumes)
    if total_volume == 0:
        return {'signal': 'hold', 'confidence': 0.3}
    
    vwap = sum(p * v for p, v in zip(prices, volumes)) / total_volume
    current_price = prices[-1]
    current_volume = volumes[-1]
    avg_volume = total_volume / len(volumes)
    
    # Volume analysis
    volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
    price_vs_vwap = current_price / vwap if vwap > 0 else 1.0
    
    # Trading logic based on volume and price
    if price_vs_vwap > 1.01 and volume_ratio > 1.2:  # Price above VWAP with high volume
        return {
            'signal': 'buy',
            'confidence': min(0.9, 0.6 + volume_ratio * 0.1),
            'volume_ratio': volume_ratio,
            'price_vs_vwap': price_vs_vwap
        }
    elif price_vs_vwap < 0.99 and volume_ratio > 1.2:  # Price below VWAP with high volume
        return {
            'signal': 'sell',
            'confidence': min(0.9, 0.6 + volume_ratio * 0.1),
            'volume_ratio': volume_ratio,
            'price_vs_vwap': price_vs_vwap
        }
    else:
        return {
            'signal': 'hold',
            'confidence': 0.4,
            'volume_ratio': volume_ratio,
            'price_vs_vwap': price_vs_vwap
        }
"""
        
        result = self.executor.execute_strategy(volume_strategy, test_data, {})
        
        # Verify volume analysis invariants
        assert 'signal' in result
        assert 'confidence' in result
        
        if 'volume_ratio' in result:
            assert result['volume_ratio'] >= 0, "Volume ratio should be non-negative"
            
        if 'price_vs_vwap' in result:
            assert result['price_vs_vwap'] > 0, "Price vs VWAP ratio should be positive"
            
            # Verify signal logic consistency
            if result['volume_ratio'] > 1.2:  # High volume
                if result['price_vs_vwap'] > 1.01:
                    assert result['signal'] == 'buy', "Should buy on high volume price breakout"
                elif result['price_vs_vwap'] < 0.99:
                    assert result['signal'] == 'sell', "Should sell on high volume price breakdown"
    
    @given(
        st.integers(min_value=5, max_value=100),  # lookback period
        st.floats(min_value=0.005, max_value=0.05)  # threshold
    )
    def test_technical_indicator_properties(self, lookback_period, threshold):
        """Test technical indicator calculation invariants"""
        assume(5 <= lookback_period <= 100)
        assume(0.005 <= threshold <= 0.05)
        
        # Generate test data
        import random
        prices = [100.0]
        for _ in range(lookback_period + 10):
            change = random.uniform(-0.02, 0.02)
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 1.0))
        
        test_data = {
            'close': prices,
            'high': [p * 1.01 for p in prices],
            'low': [p * 0.99 for p in prices],
            'volume': [10000] * len(prices)
        }
        
        # Technical indicator strategy
        indicator_strategy = f"""
def trading_strategy(data, params):
    lookback = {lookback_period}
    threshold = {threshold}
    
    if len(data['close']) < lookback:
        return {{'signal': 'hold', 'confidence': 0.5}}
    
    prices = data['close']
    current_price = prices[-1]
    
    # Simple Moving Average
    sma = sum(prices[-lookback:]) / lookback
    
    # Relative Strength Index (simplified)
    gains = []
    losses = []
    for i in range(1, min(lookback, len(prices))):
        change = prices[-i] - prices[-i-1]
        if change > 0:
            gains.append(change)
        else:
            losses.append(abs(change))
    
    avg_gain = sum(gains) / len(gains) if gains else 0
    avg_loss = sum(losses) / len(losses) if losses else 1
    
    if avg_loss == 0:
        rsi = 100
    else:
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
    
    # Trading logic
    price_vs_sma = (current_price - sma) / sma if sma > 0 else 0
    
    if price_vs_sma > threshold and rsi < 70:
        return {{
            'signal': 'buy',
            'confidence': 0.8,
            'sma': sma,
            'rsi': rsi,
            'price_vs_sma': price_vs_sma
        }}
    elif price_vs_sma < -threshold and rsi > 30:
        return {{
            'signal': 'sell',
            'confidence': 0.8,
            'sma': sma,
            'rsi': rsi,
            'price_vs_sma': price_vs_sma
        }}
    else:
        return {{
            'signal': 'hold',
            'confidence': 0.4,
            'sma': sma,
            'rsi': rsi,
            'price_vs_sma': price_vs_sma
        }}
"""
        
        result = self.executor.execute_strategy(indicator_strategy, test_data, {})
        
        # Verify technical indicator invariants
        assert 'signal' in result
        assert 'confidence' in result
        
        if 'sma' in result:
            assert result['sma'] > 0, "SMA should be positive"
            
        if 'rsi' in result:
            assert 0 <= result['rsi'] <= 100, f"RSI should be between 0-100, got {result['rsi']}"
            
        if 'price_vs_sma' in result and 'sma' in result:
            current_price = prices[-1]
            expected_ratio = (current_price - result['sma']) / result['sma']
            assert abs(result['price_vs_sma'] - expected_ratio) < 0.001, \
                "Price vs SMA calculation should be accurate"
            
            # Verify signal logic
            if result['price_vs_sma'] > threshold and result['rsi'] < 70:
                assert result['signal'] == 'buy', "Should buy on positive momentum with low RSI"
            elif result['price_vs_sma'] < -threshold and result['rsi'] > 30:
                assert result['signal'] == 'sell', "Should sell on negative momentum with high RSI"

class PortfolioStateMachine(RuleBasedStateMachine):
    """Stateful property-based testing for portfolio management"""
    
    def __init__(self):
        super().__init__()
        self.executor = SecureStrategyExecutor()
        self.portfolio_value = 10000.0
        self.positions = {}
        self.cash = self.portfolio_value
        self.trade_history = []
    
    @initialize()
    def setup_portfolio(self):
        """Initialize portfolio state"""
        self.portfolio_value = 10000.0
        self.positions = {}
        self.cash = self.portfolio_value
        self.trade_history = []
    
    @rule(
        symbol=st.text(min_size=1, max_size=5, alphabet='ABCDEFGHIJKLMNOPQRSTUVWXYZ'),
        price=st.floats(min_value=1.0, max_value=1000.0),
        quantity=st.integers(min_value=1, max_value=100)
    )
    def buy_stock(self, symbol, price, quantity):
        """Rule: Buy stock"""
        assume(price > 0)
        assume(quantity > 0)
        
        cost = price * quantity
        
        if cost <= self.cash:
            # Execute buy
            self.cash -= cost
            self.positions[symbol] = self.positions.get(symbol, 0) + quantity
            self.trade_history.append({
                'action': 'buy',
                'symbol': symbol,
                'price': price,
                'quantity': quantity,
                'cost': cost
            })
    
    @rule(
        symbol=st.text(min_size=1, max_size=5, alphabet='ABCDEFGHIJKLMNOPQRSTUVWXYZ'),
        price=st.floats(min_value=1.0, max_value=1000.0),
        quantity=st.integers(min_value=1, max_value=100)
    )
    def sell_stock(self, symbol, price, quantity):
        """Rule: Sell stock"""
        assume(price > 0)
        assume(quantity > 0)
        
        if symbol in self.positions and self.positions[symbol] >= quantity:
            # Execute sell
            proceeds = price * quantity
            self.cash += proceeds
            self.positions[symbol] -= quantity
            
            if self.positions[symbol] == 0:
                del self.positions[symbol]
            
            self.trade_history.append({
                'action': 'sell',
                'symbol': symbol,
                'price': price,
                'quantity': quantity,
                'proceeds': proceeds
            })
    
    @invariant()
    def cash_is_non_negative(self):
        """Invariant: Cash should never be negative"""
        assert self.cash >= 0, f"Cash went negative: {self.cash}"
    
    @invariant()
    def positions_are_non_negative(self):
        """Invariant: All positions should be non-negative"""
        for symbol, quantity in self.positions.items():
            assert quantity >= 0, f"Negative position in {symbol}: {quantity}"
    
    @invariant()
    def portfolio_value_consistency(self):
        """Invariant: Portfolio value should be consistent"""
        total_cash = self.cash
        total_position_value = sum(
            quantity * 100.0  # Assume $100 per share for simplicity
            for quantity in self.positions.values()
        )
        
        # Portfolio value should be reasonable
        assert total_cash + total_position_value >= 0, "Total portfolio value should be non-negative"

# Run the stateful test
TestPortfolioStateMachine = PortfolioStateMachine.TestCase

@pytest.mark.property
class TestPropertyBasedEdgeCases:
    """Property-based tests for edge cases and boundary conditions"""
    
    def setup_method(self):
        """Setup before each test"""
        self.executor = SecureStrategyExecutor()
    
    @given(st.floats(min_value=0.0001, max_value=0.0001))
    def test_very_small_prices(self, price):
        """Test behavior with very small prices"""
        test_data = {
            'close': [price, price * 1.01, price * 0.99],
            'high': [price * 1.02, price * 1.03, price * 1.01],
            'low': [price * 0.98, price * 0.99, price * 0.97],
            'volume': [10000, 10000, 10000]
        }
        
        strategy = """
def trading_strategy(data, params):
    if len(data['close']) < 2:
        return {'signal': 'hold', 'confidence': 0.5}
    
    current_price = data['close'][-1]
    prev_price = data['close'][-2]
    
    if current_price > prev_price:
        return {'signal': 'buy', 'confidence': 0.7}
    else:
        return {'signal': 'sell', 'confidence': 0.7}
"""
        
        result = self.executor.execute_strategy(strategy, test_data, {})
        
        # Should handle small prices without errors
        assert 'signal' in result
        assert result['signal'] in ['buy', 'sell', 'hold']
    
    @given(st.floats(min_value=1000000.0, max_value=1000000.0))
    def test_very_large_prices(self, price):
        """Test behavior with very large prices"""
        test_data = {
            'close': [price, price * 1.01, price * 0.99],
            'high': [price * 1.02, price * 1.03, price * 1.01],
            'low': [price * 0.98, price * 0.99, price * 0.97],
            'volume': [10000, 10000, 10000]
        }
        
        strategy = """
def trading_strategy(data, params):
    if len(data['close']) < 2:
        return {'signal': 'hold', 'confidence': 0.5}
    
    current_price = data['close'][-1]
    prev_price = data['close'][-2]
    
    if current_price > prev_price:
        return {'signal': 'buy', 'confidence': 0.7}
    else:
        return {'signal': 'sell', 'confidence': 0.7}
"""
        
        result = self.executor.execute_strategy(strategy, test_data, {})
        
        # Should handle large prices without errors
        assert 'signal' in result
        assert result['signal'] in ['buy', 'sell', 'hold']