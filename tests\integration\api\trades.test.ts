// tests/integration/api/trades.test.ts
import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { TestServer } from '../../helpers/test-server';
import { TestDatabase } from '../../helpers/test-database';
import { TestFactory } from '../../helpers/test-factory';

describe('Trades API Integration Tests', () => {
  let server: TestServer;
  let db: TestDatabase;
  let app: any;

  beforeAll(async () => {
    db = new TestDatabase();
    await db.setup();
    
    server = new TestServer();
    app = await server.start();
  });

  afterAll(async () => {
    await server.stop();
    await db.teardown();
  });

  beforeEach(async () => {
    await db.clean();
  });

  describe('POST /api/trades', () => {
    it('should create a new trade', async () => {
      // Arrange
      const tradeData = TestFactory.createTrade();
      delete (tradeData as any).id; // Remove ID for creation

      // Act
      const response = await app.inject({
        method: 'POST',
        url: '/api/trades',
        headers: {
          'content-type': 'application/json',
          'authorization': 'Bearer test-token',
        },
        body: tradeData,
      });

      // Assert
      expect(response.statusCode).toBe(201);
      const body = JSON.parse(response.body);
      expect(body).toMatchObject({
        symbol: tradeData.symbol,
        quantity: tradeData.quantity,
        side: tradeData.side,
        status: 'pending',
      });
      expect(body.id).toBeDefined();
    });

    it('should validate trade data', async () => {
      // Arrange
      const invalidTrade = {
        symbol: '', // Invalid: empty string
        quantity: -10, // Invalid: negative
        side: 'invalid', // Invalid: not buy/sell
      };

      // Act
      const response = await app.inject({
        method: 'POST',
        url: '/api/trades',
        headers: {
          'content-type': 'application/json',
          'authorization': 'Bearer test-token',
        },
        body: invalidTrade,
      });

      // Assert
      expect(response.statusCode).toBe(400);
      const body = JSON.parse(response.body);
      expect(body.errors).toBeDefined();
    });
  });
});