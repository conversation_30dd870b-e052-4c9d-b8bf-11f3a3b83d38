/**
 * WebSocket Service - Real-time communication with backend
 * Handles worker status updates, job progress, and system events
 */

import { io, Socket } from 'socket.io-client';
import { toast } from 'react-hot-toast';

// Import shared types
import type {
  // WorkerEvent, // Not currently used
  FileProcessingProgress,
  BacktestProgress,
  DGMExperimentProgress,
  WorkerHealthCheck,
} from '@shared/schemas';

interface WebSocketEvents {
  // Worker events
  'worker:status_updated': (data: any) => void;
  'worker:health_updated': (data: WorkerHealthCheck) => void;
  'worker:restarted': (data: { workerName: string; success: boolean }) => void;
  'worker:error': (data: { workerName: string; error: string }) => void;

  // Job progress events
  'job:file_processing_progress': (data: FileProcessingProgress) => void;
  'job:backtest_progress': (data: BacktestProgress) => void;
  'job:dgm_experiment_progress': (data: DGMExperimentProgress) => void;

  // Job completion events
  'job:file_processing_completed': (data: { sessionId: string; success: boolean }) => void;
  'job:backtest_completed': (data: { backtestId: string; success: boolean }) => void;
  'job:dgm_experiment_completed': (data: { experimentId: string; success: boolean }) => void;

  // System events
  'system:notification': (data: { type: 'info' | 'warning' | 'error'; message: string }) => void;
  'system:maintenance': (data: { scheduled: boolean; message: string }) => void;

  // User events
  'user:quota_updated': (data: { used: number; limit: number }) => void;
  'user:subscription_changed': (data: { tier: string }) => void;
}

type EventHandler<T = any> = (data: T) => void;

class WebSocketService {
  private socket: Socket | null = null;
  private baseURL: string;
  private isConnected = false;
  private isConnecting = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 5000;
  private eventHandlers = new Map<string, Set<EventHandler>>();

  constructor() {
    this.baseURL = import.meta.env.VITE_API_URL || 'http://localhost:8000';
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.isConnected || this.isConnecting) {
        resolve();
        return;
      }

      this.isConnecting = true;

      const token = localStorage.getItem('auth_token');
      if (!token) {
        this.isConnecting = false;
        reject(new Error('No authentication token found'));
        return;
      }

      this.socket = io(this.baseURL, {
        auth: {
          token,
        },
        transports: ['websocket', 'polling'],
        timeout: 10000,
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: this.reconnectInterval,
      });

      this.socket.on('connect', () => {
        console.log('WebSocket connected');
        this.isConnected = true;
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        resolve();
      });

      this.socket.on('connect_error', (error) => {
        console.error('WebSocket connection error:', error);
        this.isConnecting = false;
        
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.reconnectAttempts++;
          setTimeout(() => this.connect(), this.reconnectInterval);
        } else {
          reject(error);
        }
      });

      this.socket.on('disconnect', (reason) => {
        console.log('WebSocket disconnected:', reason);
        this.isConnected = false;
        
        if (reason === 'io server disconnect') {
          // Server disconnected - try to reconnect
          setTimeout(() => this.connect(), this.reconnectInterval);
        }
      });

      this.socket.on('error', (error) => {
        console.error('WebSocket error:', error);
        toast.error('Connection error occurred');
      });

      // Set up event forwarding
      this.setupEventForwarding();
    });
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.isConnected = false;
    this.isConnecting = false;
    this.eventHandlers.clear();
  }

  private setupEventForwarding(): void {
    if (!this.socket) return;

    // Worker events
    this.socket.on('worker:status_updated', (data) => {
      this.emit('worker:status_updated', data);
    });

    this.socket.on('worker:health_updated', (data) => {
      this.emit('worker:health_updated', data);
      
      // Show notification for critical health issues
      if (!data.healthy) {
        toast.error('Worker health issue detected');
      }
    });

    this.socket.on('worker:restarted', (data) => {
      this.emit('worker:restarted', data);
      
      if (data.success) {
        toast.success(`Worker ${data.workerName} restarted successfully`);
      } else {
        toast.error(`Failed to restart worker ${data.workerName}`);
      }
    });

    this.socket.on('worker:error', (data) => {
      this.emit('worker:error', data);
      toast.error(`Worker error: ${data.workerName}`);
    });

    // Job progress events
    this.socket.on('job:file_processing_progress', (data) => {
      this.emit('job:file_processing_progress', data);
    });

    this.socket.on('job:backtest_progress', (data) => {
      this.emit('job:backtest_progress', data);
    });

    this.socket.on('job:dgm_experiment_progress', (data) => {
      this.emit('job:dgm_experiment_progress', data);
    });

    // Job completion events
    this.socket.on('job:file_processing_completed', (data) => {
      this.emit('job:file_processing_completed', data);
      
      if (data.success) {
        toast.success('File processing completed');
      } else {
        toast.error('File processing failed');
      }
    });

    this.socket.on('job:backtest_completed', (data) => {
      this.emit('job:backtest_completed', data);
      
      if (data.success) {
        toast.success('Backtest completed');
      } else {
        toast.error('Backtest failed');
      }
    });

    this.socket.on('job:dgm_experiment_completed', (data) => {
      this.emit('job:dgm_experiment_completed', data);
      
      if (data.success) {
        toast.success('DGM experiment completed');
      } else {
        toast.error('DGM experiment failed');
      }
    });

    // System events
    this.socket.on('system:notification', (data) => {
      this.emit('system:notification', data);
      
      switch (data.type) {
        case 'info':
          toast(data.message);
          break;
        case 'warning':
          toast(data.message, { icon: '⚠️' });
          break;
        case 'error':
          toast.error(data.message);
          break;
      }
    });

    this.socket.on('system:maintenance', (data) => {
      this.emit('system:maintenance', data);
      toast(data.message, { icon: '🔧', duration: 8000 });
    });

    // User events
    this.socket.on('user:quota_updated', (data) => {
      this.emit('user:quota_updated', data);
    });

    this.socket.on('user:subscription_changed', (data) => {
      this.emit('user:subscription_changed', data);
      toast.success(`Subscription updated to ${data.tier}`);
    });
  }

  // Event subscription methods
  on<K extends keyof WebSocketEvents>(event: K, handler: WebSocketEvents[K]): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, new Set());
    }
    this.eventHandlers.get(event)!.add(handler);
  }

  off<K extends keyof WebSocketEvents>(event: K, handler: WebSocketEvents[K]): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      handlers.delete(handler);
      if (handlers.size === 0) {
        this.eventHandlers.delete(event);
      }
    }
  }

  once<K extends keyof WebSocketEvents>(event: K, handler: WebSocketEvents[K]): void {
    const onceHandler = (data: any) => {
      handler(data);
      this.off(event, onceHandler);
    };
    this.on(event, onceHandler);
  }

  private emit(event: string, data: any): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`Error in WebSocket event handler for ${event}:`, error);
        }
      });
    }
  }

  // Server communication methods
  send(event: string, data?: any): void {
    if (this.socket && this.isConnected) {
      this.socket.emit(event, data);
    } else {
      console.warn('WebSocket not connected, cannot send:', event);
    }
  }

  // Join/leave rooms for targeted updates
  joinRoom(room: string): void {
    this.send('join_room', { room });
  }

  leaveRoom(room: string): void {
    this.send('leave_room', { room });
  }

  // Subscribe to specific job updates
  subscribeToJob(jobType: 'upload' | 'backtest' | 'dgm', jobId: string): void {
    this.joinRoom(`${jobType}:${jobId}`);
  }

  unsubscribeFromJob(jobType: 'upload' | 'backtest' | 'dgm', jobId: string): void {
    this.leaveRoom(`${jobType}:${jobId}`);
  }

  // Subscribe to worker updates
  subscribeToWorkerUpdates(): void {
    this.joinRoom('workers');
  }

  unsubscribeFromWorkerUpdates(): void {
    this.leaveRoom('workers');
  }

  // Connection status
  isWebSocketConnected(): boolean {
    return this.isConnected;
  }

  getSocket(): Socket | null {
    return this.socket;
  }

  // Heartbeat to keep connection alive
  startHeartbeat(): void {
    if (this.socket) {
      const heartbeat = setInterval(() => {
        if (this.isConnected) {
          this.send('ping');
        } else {
          clearInterval(heartbeat);
        }
      }, 30000); // Ping every 30 seconds
    }
  }
}

// Export singleton instance
export const webSocketService = new WebSocketService();

// Auto-connect when token is available
if (typeof window !== 'undefined' && localStorage.getItem('auth_token')) {
  webSocketService.connect().catch(console.error);
}

export type { WebSocketEvents };