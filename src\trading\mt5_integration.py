# src/trading/mt5_integration.py
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum

class TradingMode(Enum):
    DUMMY = "dummy"
    PAPER = "paper"
    LIVE = "live"

class OrderType(Enum):
    BUY = "buy"
    SELL = "sell"
    BUY_LIMIT = "buy_limit"
    SELL_LIMIT = "sell_limit"
    BUY_STOP = "buy_stop"
    SELL_STOP = "sell_stop"

class OrderStatus(Enum):
    PENDING = "pending"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"

@dataclass
class SafetyLimits:
    max_daily_loss: float
    max_position_size: float
    max_open_positions: int
    max_risk_per_trade: float
    allowed_symbols: List[str]
    trading_hours_start: str  # "09:00"
    trading_hours_end: str    # "17:00"
    emergency_stop_loss: float  # Maximum loss before emergency shutdown

@dataclass
class TradingOrder:
    symbol: str
    order_type: OrderType
    volume: float
    price: Optional[float]
    stop_loss: Optional[float]
    take_profit: Optional[float]
    comment: str
    magic_number: int
    timestamp: datetime
    order_id: str
    status: OrderStatus
    fill_price: Optional[float] = None
    fill_time: Optional[datetime] = None

@dataclass
class Position:
    symbol: str
    volume: float
    open_price: float
    current_price: float
    profit: float
    open_time: datetime
    position_id: str
    order_type: OrderType

class MT5SafetyController:
    def __init__(self, safety_limits: SafetyLimits, trading_mode: TradingMode = TradingMode.DUMMY):
        self.safety_limits = safety_limits
        self.trading_mode = trading_mode
        self.daily_pnl = 0.0
        self.open_positions: Dict[str, Position] = {}
        self.order_history: List[TradingOrder] = []
        self.emergency_stop_triggered = False
        self.last_reset_date = datetime.now().date()
        
        # Audit trail
        self.safety_violations: List[Dict[str, Any]] = []
        self.trading_log: List[Dict[str, Any]] = []
    
    def validate_order(self, order: TradingOrder) -> Tuple[bool, str]:
        """Comprehensive order validation with safety checks"""
        try:
            # Check emergency stop
            if self.emergency_stop_triggered:
                return False, "Emergency stop is active - no new orders allowed"
            
            # Check trading hours
            if not self._is_trading_hours():
                return False, f"Outside trading hours ({self.safety_limits.trading_hours_start}-{self.safety_limits.trading_hours_end})"
            
            # Check symbol whitelist
            if order.symbol not in self.safety_limits.allowed_symbols:
                violation = {
                    "type": "unauthorized_symbol",
                    "symbol": order.symbol,
                    "timestamp": datetime.now(),
                    "order_id": order.order_id
                }
                self.safety_violations.append(violation)
                return False, f"Symbol {order.symbol} not in allowed list"
            
            # Check position size
            if order.volume > self.safety_limits.max_position_size:
                violation = {
                    "type": "position_size_exceeded",
                    "requested_volume": order.volume,
                    "max_allowed": self.safety_limits.max_position_size,
                    "timestamp": datetime.now(),
                    "order_id": order.order_id
                }
                self.safety_violations.append(violation)
                return False, f"Position size {order.volume} exceeds limit {self.safety_limits.max_position_size}"
            
            # Check maximum open positions
            if len(self.open_positions) >= self.safety_limits.max_open_positions:
                return False, f"Maximum open positions ({self.safety_limits.max_open_positions}) reached"
            
            # Check daily loss limit
            if self.daily_pnl <= -self.safety_limits.max_daily_loss:
                violation = {
                    "type": "daily_loss_limit_reached",
                    "current_pnl": self.daily_pnl,
                    "limit": self.safety_limits.max_daily_loss,
                    "timestamp": datetime.now(),
                    "order_id": order.order_id
                }
                self.safety_violations.append(violation)
                return False, f"Daily loss limit reached: {self.daily_pnl}"
            
            # Check risk per trade
            if order.stop_loss and order.price:
                risk_amount = abs(order.price - order.stop_loss) * order.volume
                if risk_amount > self.safety_limits.max_risk_per_trade:
                    return False, f"Risk per trade {risk_amount} exceeds limit {self.safety_limits.max_risk_per_trade}"
            
            # Validate order integrity
            if not self._validate_order_integrity(order):
                return False, "Order failed integrity validation"
            
            return True, "Order validation passed"
            
        except Exception as e:
            return False, f"Validation error: {str(e)}"
    
    def _validate_order_integrity(self, order: TradingOrder) -> bool:
        """Validate order data integrity"""
        # Check required fields
        if not all([order.symbol, order.order_type, order.volume > 0, order.order_id]):
            return False
        
        # Check price logic
        if order.order_type in [OrderType.BUY_LIMIT, OrderType.SELL_LIMIT, OrderType.BUY_STOP, OrderType.SELL_STOP]:
            if not order.price or order.price <= 0:
                return False
        
        # Check stop loss and take profit logic
        if order.stop_loss and order.take_profit:
            if order.order_type == OrderType.BUY:
                if order.stop_loss >= order.price or order.take_profit <= order.price:
                    return False
            elif order.order_type == OrderType.SELL:
                if order.stop_loss <= order.price or order.take_profit >= order.price:
                    return False
        
        return True
    
    def _is_trading_hours(self) -> bool:
        """Check if current time is within trading hours"""
        now = datetime.now().time()
        start_time = datetime.strptime(self.safety_limits.trading_hours_start, "%H:%M").time()
        end_time = datetime.strptime(self.safety_limits.trading_hours_end, "%H:%M").time()
        
        return start_time <= now <= end_time
    
    def execute_order(self, order: TradingOrder) -> Tuple[bool, str, Optional[str]]:
        """Execute order with safety controls"""
        # Reset daily PnL if new day
        self._check_daily_reset()
        
        # Validate order
        is_valid, validation_message = self.validate_order(order)
        if not is_valid:
            self._log_trading_action("order_rejected", {
                "order_id": order.order_id,
                "reason": validation_message,
                "order_details": asdict(order)
            })
            return False, validation_message, None
        
        # Execute based on trading mode
        if self.trading_mode == TradingMode.DUMMY:
            return self._execute_dummy_order(order)
        elif self.trading_mode == TradingMode.PAPER:
            return self._execute_paper_order(order)
        elif self.trading_mode == TradingMode.LIVE:
            return self._execute_live_order(order)
        else:
            return False, "Invalid trading mode", None
    
    def _execute_dummy_order(self, order: TradingOrder) -> Tuple[bool, str, Optional[str]]:
        """Execute order in dummy mode (simulation only)"""
        # Simulate order execution
        order.status = OrderStatus.FILLED
        order.fill_price = order.price or self._get_simulated_price(order.symbol)
        order.fill_time = datetime.now()
        
        # Create position
        position = Position(
            symbol=order.symbol,
            volume=order.volume,
            open_price=order.fill_price,
            current_price=order.fill_price,
            profit=0.0,
            open_time=order.fill_time,
            position_id=f"pos_{order.order_id}",
            order_type=order.order_type
        )
        
        self.open_positions[position.position_id] = position
        self.order_history.append(order)
        
        self._log_trading_action("order_executed_dummy", {
            "order_id": order.order_id,
            "position_id": position.position_id,
            "fill_price": order.fill_price
        })
        
        return True, "Order executed in dummy mode", position.position_id
    
    def _execute_paper_order(self, order: TradingOrder) -> Tuple[bool, str, Optional[str]]:
        """Execute order in paper trading mode"""
        # Similar to dummy but with more realistic simulation
        return self._execute_dummy_order(order)  # Simplified for now
    
    def _execute_live_order(self, order: TradingOrder) -> Tuple[bool, str, Optional[str]]:
        """Execute order in live trading mode"""
        # This would integrate with actual MT5 API
        # For safety, we'll simulate for now
        if self.trading_mode == TradingMode.LIVE:
            # Additional live trading safety checks
            confirmation_required = input(f"LIVE TRADING: Execute {order.order_type.value} {order.volume} {order.symbol}? (yes/no): ")
            if confirmation_required.lower() != "yes":
                return False, "Live order cancelled by user", None
        
        return self._execute_dummy_order(order)  # Fallback to dummy for safety
    
    def _get_simulated_price(self, symbol: str) -> float:
        """Get simulated price for dummy trading"""
        # Simple price simulation based on symbol
        base_prices = {
            "EURUSD": 1.1000,
            "GBPUSD": 1.2500,
            "USDJPY": 150.00,
            "USDCHF": 0.8800,
            "AUDUSD": 0.6500
        }
        
        base_price = base_prices.get(symbol, 1.0000)
        # Add small random variation
        import random
        variation = random.uniform(-0.001, 0.001)
        return round(base_price + variation, 5)
    
    def close_position(self, position_id: str, price: Optional[float] = None) -> Tuple[bool, str]:
        """Close position with safety checks"""
        if position_id not in self.open_positions:
            return False, "Position not found"
        
        position = self.open_positions[position_id]
        close_price = price or self._get_simulated_price(position.symbol)
        
        # Calculate profit
        if position.order_type == OrderType.BUY:
            profit = (close_price - position.open_price) * position.volume
        else:
            profit = (position.open_price - close_price) * position.volume
        
        # Update daily PnL
        self.daily_pnl += profit
        
        # Check emergency stop
        if self.daily_pnl <= -self.safety_limits.emergency_stop_loss:
            self.emergency_stop_triggered = True
            self._log_trading_action("emergency_stop_triggered", {
                "position_id": position_id,
                "daily_pnl": self.daily_pnl,
                "emergency_limit": self.safety_limits.emergency_stop_loss
            })
        
        # Remove position
        del self.open_positions[position_id]
        
        self._log_trading_action("position_closed", {
            "position_id": position_id,
            "close_price": close_price,
            "profit": profit,
            "daily_pnl": self.daily_pnl
        })
        
        return True, f"Position closed with profit: {profit}"
    
    def _check_daily_reset(self):
        """Reset daily counters if new day"""
        current_date = datetime.now().date()
        if current_date > self.last_reset_date:
            self.daily_pnl = 0.0
            self.emergency_stop_triggered = False
            self.last_reset_date = current_date
            self._log_trading_action("daily_reset", {"date": current_date.isoformat()})
    
    def _log_trading_action(self, action: str, details: Dict[str, Any]):
        """Log trading actions for audit trail"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "action": action,
            "trading_mode": self.trading_mode.value,
            "details": details
        }
        self.trading_log.append(log_entry)
    
    def get_safety_status(self) -> Dict[str, Any]:
        """Get current safety status"""
        return {
            "trading_mode": self.trading_mode.value,
            "emergency_stop_active": self.emergency_stop_triggered,
            "daily_pnl": self.daily_pnl,
            "open_positions_count": len(self.open_positions),
            "daily_loss_limit": self.safety_limits.max_daily_loss,
            "remaining_daily_loss": self.safety_limits.max_daily_loss + self.daily_pnl,
            "safety_violations_today": len([v for v in self.safety_violations 
                                          if v["timestamp"].date() == datetime.now().date()]),
            "trading_hours_active": self._is_trading_hours()
        }
    
    def get_audit_trail(self) -> Dict[str, Any]:
        """Get complete audit trail"""
        return {
            "trading_log": self.trading_log,
            "safety_violations": self.safety_violations,
            "order_history": [asdict(order) for order in self.order_history],
            "current_positions": {pid: asdict(pos) for pid, pos in self.open_positions.items()},
            "safety_limits": asdict(self.safety_limits),
            "daily_pnl": self.daily_pnl,
            "emergency_stop_triggered": self.emergency_stop_triggered
        }
    
    def emergency_stop_all(self) -> Dict[str, Any]:
        """Emergency stop all trading activities"""
        self.emergency_stop_triggered = True
        
        # Close all positions
        closed_positions = []
        for position_id in list(self.open_positions.keys()):
            success, message = self.close_position(position_id)
            closed_positions.append({
                "position_id": position_id,
                "closed": success,
                "message": message
            })
        
        self._log_trading_action("emergency_stop_all", {
            "closed_positions": closed_positions,
            "final_daily_pnl": self.daily_pnl
        })
        
        return {
            "emergency_stop_activated": True,
            "closed_positions": closed_positions,
            "final_daily_pnl": self.daily_pnl,
            "timestamp": datetime.now().isoformat()
        }

class MT5Integration:
    def __init__(self, safety_limits: SafetyLimits, trading_mode: TradingMode = TradingMode.DUMMY):
        self.safety_controller = MT5SafetyController(safety_limits, trading_mode)
        self.order_counter = 0
    
    def create_order(self, symbol: str, order_type: OrderType, volume: float, 
                    price: Optional[float] = None, stop_loss: Optional[float] = None,
                    take_profit: Optional[float] = None, comment: str = "") -> TradingOrder:
        """Create a new trading order"""
        self.order_counter += 1
        
        order = TradingOrder(
            symbol=symbol,
            order_type=order_type,
            volume=volume,
            price=price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            comment=comment,
            magic_number=12345,  # Could be configurable
            timestamp=datetime.now(),
            order_id=f"order_{self.order_counter}_{int(time.time())}",
            status=OrderStatus.PENDING
        )
        
        return order
    
    def submit_order(self, order: TradingOrder) -> Tuple[bool, str, Optional[str]]:
        """Submit order through safety controller"""
        return self.safety_controller.execute_order(order)
    
    def close_position(self, position_id: str, price: Optional[float] = None) -> Tuple[bool, str]:
        """Close position through safety controller"""
        return self.safety_controller.close_position(position_id, price)
    
    def get_positions(self) -> Dict[str, Position]:
        """Get all open positions"""
        return self.safety_controller.open_positions.copy()
    
    def get_safety_status(self) -> Dict[str, Any]:
        """Get safety status"""
        return self.safety_controller.get_safety_status()
    
    def emergency_stop(self) -> Dict[str, Any]:
        """Trigger emergency stop"""
        return self.safety_controller.emergency_stop_all()