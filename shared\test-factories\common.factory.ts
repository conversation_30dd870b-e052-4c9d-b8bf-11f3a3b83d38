/**
 * Common test data factories
 */

import { 
  PaginationRequest, 
  PaginationResponse, 
  ApiError, 
  DateRange,
  SearchRequest 
} from '../schemas/common.schemas';

export const getMockPaginationRequest = (
  overrides?: Partial<PaginationRequest>
): PaginationRequest => {
  const base: PaginationRequest = {
    page: 1,
    limit: 20,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  };

  return { ...base, ...overrides };
};

export const getMockPaginationResponse = (
  overrides?: Partial<PaginationResponse>
): PaginationResponse => {
  const base: PaginationResponse = {
    page: 1,
    limit: 20,
    total: 100,
    totalPages: 5,
    hasNext: true,
    hasPrev: false,
  };

  return { ...base, ...overrides };
};

export const getMockApiError = (
  overrides?: Partial<ApiError>
): ApiError => {
  const base: ApiError = {
    code: 'VALIDATION_ERROR',
    message: 'Validation failed',
    details: 'One or more fields are invalid',
    timestamp: new Date('2025-01-01T00:00:00Z'),
  };

  return { ...base, ...overrides };
};

export const getMockDateRange = (
  overrides?: Partial<DateRange>
): DateRange => {
  const base: DateRange = {
    startDate: new Date('2025-01-01T00:00:00Z'),
    endDate: new Date('2025-01-31T23:59:59Z'),
  };

  return { ...base, ...overrides };
};

export const getMockSearchRequest = (
  overrides?: Partial<SearchRequest>
): SearchRequest => {
  const base: SearchRequest = {
    query: 'test search',
    filters: { category: 'trading' },
    page: 1,
    limit: 20,
    sortBy: 'relevance',
    sortOrder: 'desc',
  };

  return { ...base, ...overrides };
};

/**
 * Generate mock UUID
 */
export const getMockUUID = (): string => {
  return 'mock-uuid-123e4567-e89b-12d3-a456-************';
};

/**
 * Generate mock email
 */
export const getMockEmail = (prefix = 'test'): string => {
  return `${prefix}@example.com`;
};

/**
 * Generate mock date
 */
export const getMockDate = (daysOffset = 0): Date => {
  const baseDate = new Date('2025-01-01T00:00:00Z');
  baseDate.setDate(baseDate.getDate() + daysOffset);
  return baseDate;
};

/**
 * Generate array of mock items
 */
export const getMockArray = <T>(
  factory: (index: number) => T,
  count: number
): T[] => {
  return Array.from({ length: count }, (_, index) => factory(index));
};