import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '../ui/tabs';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Textarea } from '../ui/textarea';
import { 
  BookOpen, 
  TrendingUp, 
  Brain, 
  Code, 
  Target, 
  AlertTriangle,
  CheckCircle,
  Copy,
  Play,
  BarChart3,
  Lightbulb
} from 'lucide-react';

interface StrategyRecommendation {
  name: string;
  type: 'ai_prompt' | 'quant_strategy';
  description: string;
  complexity: 'beginner' | 'intermediate' | 'advanced';
  expected_performance: {
    sharpe_ratio?: number;
    max_drawdown?: number;
    win_rate?: number;
  };
  requirements: string[];
  implementation_guide: string;
  ai_prompts: string[];
  code_example?: string;
}

interface UserProfile {
  experience_level: 'beginner' | 'intermediate' | 'advanced';
  asset_classes: string[];
  timeframes: string[];
  risk_tolerance: 'low' | 'medium' | 'high';
  capital: number;
  goals: string[];
}

interface AIPrompt {
  id: string;
  title: string;
  description: string;
  category: string;
  prompt_template: string;
  variables: string[];
  example_usage?: string;
}

const StrategyHelper: React.FC = () => {
  const [activeTab, setActiveTab] = useState('recommendations');
  const [userProfile, setUserProfile] = useState<UserProfile>({
    experience_level: 'beginner',
    asset_classes: ['forex'],
    timeframes: ['1h'],
    risk_tolerance: 'medium',
    capital: 10000,
    goals: ['learning']
  });
  const [recommendations, setRecommendations] = useState<StrategyRecommendation[]>([]);
  const [selectedStrategy, setSelectedStrategy] = useState<StrategyRecommendation | null>(null);
  const [aiPrompts, setAiPrompts] = useState<AIPrompt[]>([]);
  const [selectedPrompt, setSelectedPrompt] = useState<AIPrompt | null>(null);
  const [promptVariables, setPromptVariables] = useState<Record<string, string>>({});
  const [generatedPrompt, setGeneratedPrompt] = useState<string>('');
  const [loading, setLoading] = useState(false);

  // Mock data - in real implementation, this would come from your backend
  const mockRecommendations: StrategyRecommendation[] = [
    {
      name: "RSI Mean Reversion",
      type: "quant_strategy",
      description: "Buy oversold and sell overbought conditions based on RSI indicator",
      complexity: "beginner",
      expected_performance: {
        sharpe_ratio: 1.2,
        max_drawdown: 0.15,
        win_rate: 0.55
      },
      requirements: ["RSI indicator", "Price data", "Basic charting"],
      implementation_guide: "# RSI Mean Reversion Strategy\n\nThis strategy uses the Relative Strength Index (RSI) to identify overbought and oversold conditions...",
      ai_prompts: ["technical_analyzer", "performance_analyzer"],
      code_example: `# RSI Mean Reversion Strategy
import pandas as pd
import numpy as np

def calculate_rsi(prices, period=14):
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))

# Generate signals
def generate_signals(data):
    data['rsi'] = calculate_rsi(data['close'])
    data['signal'] = 0
    data.loc[data['rsi'] < 30, 'signal'] = 1  # Buy
    data.loc[data['rsi'] > 70, 'signal'] = -1  # Sell
    return data`
    },
    {
      name: "AI-Assisted Market Analysis",
      type: "ai_prompt",
      description: "Use AI to conduct comprehensive market research and identify opportunities",
      complexity: "beginner",
      expected_performance: {
        sharpe_ratio: 1.0,
        max_drawdown: 0.12,
        win_rate: 0.60
      },
      requirements: ["Market data access", "AI assistant", "Research skills"],
      implementation_guide: "# AI-Assisted Market Analysis\n\nUse proven AI prompts to analyze markets and identify trading opportunities...",
      ai_prompts: ["market_scanner", "technical_analyzer", "market_research"]
    }
  ];

  const mockAIPrompts: AIPrompt[] = [
    {
      id: "market_scanner",
      title: "Market Analysis & Asset Scanner",
      description: "Identify trading assets that meet specific criteria",
      category: "market_analysis",
      prompt_template: "Act as a day trading assistant. Your task is to identify trading assets that meet the specified {criteria}. Utilize your expertise and available market analysis tools to scan, filter, and evaluate potential assets for trading...",
      variables: ["criteria"],
      example_usage: "criteria: 'tech stocks with strong momentum and AI exposure'"
    },
    {
      id: "technical_analyzer",
      title: "Comprehensive Technical Analysis",
      description: "Detailed technical analysis with entry/exit points",
      category: "technical_analysis",
      prompt_template: "Act as an experienced day trader. Your objective is to analyze the price and volume patterns of {trading_asset} to identify potential buying or selling opportunities...",
      variables: ["trading_asset"],
      example_usage: "trading_asset: 'NVDA (NVIDIA Corporation)'"
    },
    {
      id: "risk_calculator",
      title: "Position Sizing & Risk Calculator",
      description: "Calculate optimal position size and risk parameters",
      category: "trade_execution",
      prompt_template: "Act as a risk management specialist. For the proposed trade on {asset}, calculate optimal position sizing and risk parameters...",
      variables: ["asset", "account_size", "risk_percentage", "entry_price", "stop_loss", "take_profit"],
      example_usage: "asset: 'TSLA', account_size: '$50000', risk_percentage: '2'"
    }
  ];

  useEffect(() => {
    // Simulate API call to get recommendations
    setRecommendations(mockRecommendations);
    setAiPrompts(mockAIPrompts);
  }, [userProfile]);

  const handleProfileUpdate = (field: keyof UserProfile, value: any) => {
    setUserProfile(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const generateAIPrompt = () => {
    if (!selectedPrompt) return;

    let prompt = selectedPrompt.prompt_template;
    
    // Replace variables in the prompt
    selectedPrompt.variables.forEach(variable => {
      const value = promptVariables[variable] || `[${variable}]`;
      prompt = prompt.replace(new RegExp(`{${variable}}`, 'g'), value);
    });

    setGeneratedPrompt(prompt);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    return type === 'ai_prompt' ? <Brain className="h-4 w-4" /> : <BarChart3 className="h-4 w-4" />;
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Strategy Helper</h1>
        <p className="text-gray-600">
          Get personalized trading strategy recommendations and AI-powered analysis tools
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
          <TabsTrigger value="ai-prompts">AI Prompts</TabsTrigger>
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="learning">Learning Path</TabsTrigger>
        </TabsList>

        <TabsContent value="recommendations" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {recommendations.map((rec, index) => (
              <Card key={index} className="cursor-pointer hover:shadow-lg transition-shadow"
                    onClick={() => setSelectedStrategy(rec)}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {getTypeIcon(rec.type)}
                      <CardTitle className="text-lg">{rec.name}</CardTitle>
                    </div>
                    <Badge className={getComplexityColor(rec.complexity)}>
                      {rec.complexity}
                    </Badge>
                  </div>
                  <CardDescription>{rec.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  {rec.expected_performance && (
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Sharpe Ratio:</span>
                        <span className="font-medium">
                          {rec.expected_performance.sharpe_ratio?.toFixed(2) || 'N/A'}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Win Rate:</span>
                        <span className="font-medium">
                          {rec.expected_performance.win_rate ? 
                            `${(rec.expected_performance.win_rate * 100).toFixed(1)}%` : 'N/A'}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Max Drawdown:</span>
                        <span className="font-medium text-red-600">
                          {rec.expected_performance.max_drawdown ? 
                            `${(rec.expected_performance.max_drawdown * 100).toFixed(1)}%` : 'N/A'}
                        </span>
                      </div>
                    </div>
                  )}
                  <div className="mt-3 flex flex-wrap gap-1">
                    {rec.ai_prompts.slice(0, 2).map((prompt, i) => (
                      <Badge key={i} variant="outline" className="text-xs">
                        {prompt.replace('_', ' ')}
                      </Badge>
                    ))}
                    {rec.ai_prompts.length > 2 && (
                      <Badge variant="outline" className="text-xs">
                        +{rec.ai_prompts.length - 2} more
                      </Badge>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {selectedStrategy && (
            <Card className="mt-6">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center space-x-2">
                    {getTypeIcon(selectedStrategy.type)}
                    <span>{selectedStrategy.name}</span>
                  </CardTitle>
                  <Button variant="outline" onClick={() => setSelectedStrategy(null)}>
                    Close
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="overview" className="w-full">
                  <TabsList>
                    <TabsTrigger value="overview">Overview</TabsTrigger>
                    <TabsTrigger value="implementation">Implementation</TabsTrigger>
                    {selectedStrategy.code_example && (
                      <TabsTrigger value="code">Code Example</TabsTrigger>
                    )}
                  </TabsList>
                  
                  <TabsContent value="overview" className="space-y-4">
                    <div>
                      <h4 className="font-semibold mb-2">Description</h4>
                      <p className="text-gray-600">{selectedStrategy.description}</p>
                    </div>
                    
                    <div>
                      <h4 className="font-semibold mb-2">Requirements</h4>
                      <ul className="list-disc list-inside space-y-1">
                        {selectedStrategy.requirements.map((req, i) => (
                          <li key={i} className="text-gray-600">{req}</li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-semibold mb-2">AI Prompts Available</h4>
                      <div className="flex flex-wrap gap-2">
                        {selectedStrategy.ai_prompts.map((prompt, i) => (
                          <Badge key={i} variant="secondary">
                            {prompt.replace('_', ' ')}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="implementation">
                    <div className="prose max-w-none">
                      <pre className="whitespace-pre-wrap bg-gray-50 p-4 rounded-lg text-sm">
                        {selectedStrategy.implementation_guide}
                      </pre>
                    </div>
                  </TabsContent>
                  
                  {selectedStrategy.code_example && (
                    <TabsContent value="code">
                      <div className="relative">
                        <Button
                          variant="outline"
                          size="sm"
                          className="absolute top-2 right-2 z-10"
                          onClick={() => copyToClipboard(selectedStrategy.code_example!)}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm">
                          <code>{selectedStrategy.code_example}</code>
                        </pre>
                      </div>
                    </TabsContent>
                  )}
                </Tabs>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="ai-prompts" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Available AI Prompts</CardTitle>
                <CardDescription>
                  Select a prompt to customize and generate
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {aiPrompts.map((prompt) => (
                    <div
                      key={prompt.id}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedPrompt?.id === prompt.id 
                          ? 'border-blue-500 bg-blue-50' 
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedPrompt(prompt)}
                    >
                      <div className="font-medium">{prompt.title}</div>
                      <div className="text-sm text-gray-600">{prompt.description}</div>
                      <Badge variant="outline" className="mt-1 text-xs">
                        {prompt.category.replace('_', ' ')}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Prompt Generator</CardTitle>
                <CardDescription>
                  Customize the selected prompt with your parameters
                </CardDescription>
              </CardHeader>
              <CardContent>
                {selectedPrompt ? (
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">Variables</h4>
                      {selectedPrompt.variables.map((variable) => (
                        <div key={variable} className="mb-3">
                          <Label htmlFor={variable}>{variable.replace('_', ' ')}</Label>
                          <Input
                            id={variable}
                            placeholder={`Enter ${variable.replace('_', ' ')}`}
                            value={promptVariables[variable] || ''}
                            onChange={(e) => setPromptVariables(prev => ({
                              ...prev,
                              [variable]: e.target.value
                            }))}
                          />
                        </div>
                      ))}
                    </div>

                    <Button onClick={generateAIPrompt} className="w-full">
                      <Play className="h-4 w-4 mr-2" />
                      Generate Prompt
                    </Button>

                    {generatedPrompt && (
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <Label>Generated Prompt</Label>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => copyToClipboard(generatedPrompt)}
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                        </div>
                        <Textarea
                          value={generatedPrompt}
                          readOnly
                          className="min-h-[200px] font-mono text-sm"
                        />
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center text-gray-500 py-8">
                    Select a prompt from the list to get started
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="profile" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Trading Profile</CardTitle>
              <CardDescription>
                Configure your profile to get personalized recommendations
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <Label>Experience Level</Label>
                  <Select
                    value={userProfile.experience_level}
                    onValueChange={(value: any) => handleProfileUpdate('experience_level', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="beginner">Beginner</SelectItem>
                      <SelectItem value="intermediate">Intermediate</SelectItem>
                      <SelectItem value="advanced">Advanced</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Risk Tolerance</Label>
                  <Select
                    value={userProfile.risk_tolerance}
                    onValueChange={(value: any) => handleProfileUpdate('risk_tolerance', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Trading Capital</Label>
                  <Input
                    type="number"
                    value={userProfile.capital}
                    onChange={(e) => handleProfileUpdate('capital', Number(e.target.value))}
                    placeholder="10000"
                  />
                </div>

                <div>
                  <Label>Primary Goal</Label>
                  <Select
                    value={userProfile.goals[0] || 'learning'}
                    onValueChange={(value) => handleProfileUpdate('goals', [value])}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="learning">Learning</SelectItem>
                      <SelectItem value="income">Income Generation</SelectItem>
                      <SelectItem value="growth">Capital Growth</SelectItem>
                      <SelectItem value="diversification">Diversification</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label>Asset Classes (Select multiple)</Label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {['forex', 'stocks', 'crypto', 'commodities', 'indices'].map((asset) => (
                    <Badge
                      key={asset}
                      variant={userProfile.asset_classes.includes(asset) ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => {
                        const newAssets = userProfile.asset_classes.includes(asset)
                          ? userProfile.asset_classes.filter(a => a !== asset)
                          : [...userProfile.asset_classes, asset];
                        handleProfileUpdate('asset_classes', newAssets);
                      }}
                    >
                      {asset}
                    </Badge>
                  ))}
                </div>
              </div>

              <div>
                <Label>Preferred Timeframes</Label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {['5m', '15m', '30m', '1h', '4h', '1d', '1w'].map((tf) => (
                    <Badge
                      key={tf}
                      variant={userProfile.timeframes.includes(tf) ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => {
                        const newTimeframes = userProfile.timeframes.includes(tf)
                          ? userProfile.timeframes.filter(t => t !== tf)
                          : [...userProfile.timeframes, tf];
                        handleProfileUpdate('timeframes', newTimeframes);
                      }}
                    >
                      {tf}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="learning" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Personalized Learning Path</CardTitle>
              <CardDescription>
                Based on your {userProfile.experience_level} level experience
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {userProfile.experience_level === 'beginner' && (
                  <>
                    <div className="border-l-4 border-green-500 pl-4">
                      <h4 className="font-semibold text-green-700">Module 1: Trading Fundamentals</h4>
                      <p className="text-sm text-gray-600 mt-1">
                        Learn basic trading concepts, terminology, and market mechanics
                      </p>
                      <div className="flex items-center mt-2 space-x-2">
                        <Badge variant="outline">2 weeks</Badge>
                        <Badge variant="outline">RSI Strategy</Badge>
                        <Badge variant="outline">MACD Strategy</Badge>
                      </div>
                    </div>
                    
                    <div className="border-l-4 border-yellow-500 pl-4">
                      <h4 className="font-semibold text-yellow-700">Module 2: Technical Analysis</h4>
                      <p className="text-sm text-gray-600 mt-1">
                        Understand charts, indicators, and pattern recognition
                      </p>
                      <div className="flex items-center mt-2 space-x-2">
                        <Badge variant="outline">3 weeks</Badge>
                        <Badge variant="outline">Bollinger Bands</Badge>
                        <Badge variant="outline">Chart Patterns</Badge>
                      </div>
                    </div>
                    
                    <div className="border-l-4 border-blue-500 pl-4">
                      <h4 className="font-semibold text-blue-700">Module 3: Risk Management</h4>
                      <p className="text-sm text-gray-600 mt-1">
                        Learn to manage risk, size positions, and protect capital
                      </p>
                      <div className="flex items-center mt-2 space-x-2">
                        <Badge variant="outline">2 weeks</Badge>
                        <Badge variant="outline">Position Sizing</Badge>
                        <Badge variant="outline">Psychology</Badge>
                      </div>
                    </div>
                  </>
                )}

                {userProfile.experience_level === 'intermediate' && (
                  <>
                    <div className="border-l-4 border-blue-500 pl-4">
                      <h4 className="font-semibold text-blue-700">Module 1: Advanced Technical Analysis</h4>
                      <p className="text-sm text-gray-600 mt-1">
                        Complex patterns, multi-timeframe analysis, and advanced indicators
                      </p>
                      <div className="flex items-center mt-2 space-x-2">
                        <Badge variant="outline">3 weeks</Badge>
                        <Badge variant="outline">London Breakout</Badge>
                        <Badge variant="outline">Dual Thrust</Badge>
                      </div>
                    </div>
                    
                    <div className="border-l-4 border-purple-500 pl-4">
                      <h4 className="font-semibold text-purple-700">Module 2: Quantitative Strategies</h4>
                      <p className="text-sm text-gray-600 mt-1">
                        Statistical approaches, backtesting, and algorithmic thinking
                      </p>
                      <div className="flex items-center mt-2 space-x-2">
                        <Badge variant="outline">4 weeks</Badge>
                        <Badge variant="outline">Pairs Trading</Badge>
                        <Badge variant="outline">Statistical Arbitrage</Badge>
                      </div>
                    </div>
                  </>
                )}

                {userProfile.experience_level === 'advanced' && (
                  <>
                    <div className="border-l-4 border-red-500 pl-4">
                      <h4 className="font-semibold text-red-700">Module 1: Strategy Development</h4>
                      <p className="text-sm text-gray-600 mt-1">
                        Create, optimize, and deploy custom trading strategies
                      </p>
                      <div className="flex items-center mt-2 space-x-2">
                        <Badge variant="outline">6 weeks</Badge>
                        <Badge variant="outline">Custom Strategies</Badge>
                        <Badge variant="outline">AI Integration</Badge>
                      </div>
                    </div>
                    
                    <div className="border-l-4 border-indigo-500 pl-4">
                      <h4 className="font-semibold text-indigo-700">Module 2: Portfolio Management</h4>
                      <p className="text-sm text-gray-600 mt-1">
                        Multi-strategy portfolios, risk parity, and optimization
                      </p>
                      <div className="flex items-center mt-2 space-x-2">
                        <Badge variant="outline">4 weeks</Badge>
                        <Badge variant="outline">Portfolio Theory</Badge>
                        <Badge variant="outline">Risk Parity</Badge>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default StrategyHelper;