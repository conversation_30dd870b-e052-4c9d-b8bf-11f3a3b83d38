import pytest
from unittest.mock import Mock, patch
from fastapi import HTT<PERSON><PERSON>x<PERSON>
from backend.app.api.routes.auth import clerk_auth_middleware, get_current_user
import asyncio

@pytest.mark.asyncio
def test_unauthenticated_user_raises_error():
    """Test that unauthenticated requests are rejected"""
    mock_request = Mock()
    mock_request.headers = {}
    
    with pytest.raises(HTTPException) as exc_info:
        asyncio.run(clerk_auth_middleware(mock_request))
    
    assert exc_info.value.status_code == 401

@pytest.mark.asyncio
def test_authenticated_user_gets_user_data():
    """Test that authenticated requests get user data"""
    mock_request = Mock()
    mock_request.headers = {"Authorization": "Bearer valid_token"}
    
    with patch('backend.app.api.routes.auth.clerk.get_user') as mock_get_user:
        mock_get_user.return_value = {"id": "user_123", "email": "<EMAIL>"}
        user = asyncio.run(get_current_user(mock_request))
    
    assert user["id"] == "user_123"
    assert user["email"] == "<EMAIL>"
