// tests/monitoring/metrics-collector.test.ts
import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { MetricsCollector, MetricType, MetricUnit } from '../../src/monitoring/metrics-collector';
import { PrometheusExporter } from '../../src/monitoring/prometheus-exporter';

describe('MetricsCollector', () => {
  let metricsCollector: MetricsCollector;
  let mockExporter: jest.Mocked<PrometheusExporter>;

  beforeEach(() => {
    mockExporter = {
      export: jest.fn(),
      register: jest.fn(),
    } as any;
    
    metricsCollector = new MetricsCollector(mockExporter);
  });

  describe('counter metrics', () => {
    it('should increment counter metrics', () => {
      // Arrange
      const metricName = 'trades_total';
      
      // Act
      metricsCollector.incrementCounter(metricName, { status: 'success' });
      metricsCollector.incrementCounter(metricName, { status: 'success' });
      metricsCollector.incrementCounter(metricName, { status: 'failed' });

      // Assert
      const metrics = metricsCollector.getMetrics();
      expect(metrics[metricName]).toBeDefined();
      expect(metrics[metricName].value).toBe(3);
      expect(metrics[metricName].labels).toEqual({
        success: 2,
        failed: 1,
      });
    });
  });

  describe('gauge metrics', () => {
    it('should set gauge values', () => {
      // Arrange
      const metricName = 'portfolio_value';
      
      // Act
      metricsCollector.setGauge(metricName, 10000);
      metricsCollector.setGauge(metricName, 12000);

      // Assert
      const metrics = metricsCollector.getMetrics();
      expect(metrics[metricName].value).toBe(12000);
    });
  });

  describe('histogram metrics', () => {
    it('should record histogram observations', () => {
      // Arrange
      const metricName = 'trade_latency';
      const values = [10, 20, 30, 40, 50];
      
      // Act
      values.forEach(value => {
        metricsCollector.recordHistogram(metricName, value);
      });

      // Assert
      const metrics = metricsCollector.getMetrics();
      expect(metrics[metricName].count).toBe(5);
      expect(metrics[metricName].sum).toBe(150);
      expect(metrics[metricName].percentiles['p50']).toBe(30);
      expect(metrics[metricName].percentiles['p95']).toBeCloseTo(50, 1);
    });
  });

  describe('metric export', () => {
    it('should export metrics in Prometheus format', () => {
      // Arrange
      metricsCollector.incrementCounter('api_requests', { endpoint: '/trades' });
      metricsCollector.setGauge('active_connections', 42);
      metricsCollector.recordHistogram('response_time', 125);

      // Act
      const exported = metricsCollector.export();

      // Assert
      expect(exported).toContain('# TYPE api_requests counter');
      expect(exported).toContain('api_requests{endpoint="/trades"} 1');
      expect(exported).toContain('# TYPE active_connections gauge');
      expect(exported).toContain('active_connections 42');
      expect(exported).toContain('# TYPE response_time histogram');
    });
  });
});

// src/monitoring/metrics-collector.ts
export enum MetricType {
  COUNTER = 'counter',
  GAUGE = 'gauge',
  HISTOGRAM = 'histogram',
  SUMMARY = 'summary',
}

export enum MetricUnit {
  NONE = '',
  SECONDS = 's',
  MILLISECONDS = 'ms',
  BYTES = 'bytes',
  PERCENT = 'percent',
}

interface MetricValue {
  value: number;
  labels?: Record<string, any>;
  timestamp: number;
}

interface HistogramValue extends MetricValue {
  count: number;
  sum: number;
  buckets: Map<number, number>;
  percentiles: Record<string, number>;
}

export class MetricsCollector {
  private metrics: Map<string, any> = new Map();
  private exporter: PrometheusExporter;

  constructor(exporter: PrometheusExporter) {
    this.exporter = exporter;
  }

  incrementCounter(name: string, labels?: Record<string, string>): void {
    const key = this.getMetricKey(name, labels);
    const current = this.metrics.get(key) || { value: 0, labels: {} };
    
    current.value += 1;
    if (labels) {
      const labelKey = Object.values(labels).join(':');
      current.labels[labelKey] = (current.labels[labelKey] || 0) + 1;
    }
    
    this.metrics.set(key, current);
  }

  setGauge(name: string, value: number, labels?: Record<string, string>): void {
    const key = this.getMetricKey(name, labels);
    this.metrics.set(key, { value, timestamp: Date.now() });
  }

  recordHistogram(name: string, value: number, labels?: Record<string, string>): void {
    const key = this.getMetricKey(name, labels);
    let histogram = this.metrics.get(key);
    
    if (!histogram) {
      histogram = {
        count: 0,
        sum: 0,
        values: [],
        buckets: new Map(),
        percentiles: {},
      };
    }

    histogram.count += 1;
    histogram.sum += value;
    histogram.values.push(value);
    
    // Update percentiles
    histogram.values.sort((a: number, b: number) => a - b);
    histogram.percentiles = {
      p50: this.percentile(histogram.values, 0.5),
      p95: this.percentile(histogram.values, 0.95),
      p99: this.percentile(histogram.values, 0.99),
    };
    
    this.metrics.set(key, histogram);
  }

  getMetrics(): Record<string, any> {
    const result: Record<string, any> = {};
    
    this.metrics.forEach((value, key) => {
      result[key] = value;
    });
    
    return result;
  }

  export(): string {
    let output = '';
    
    this.metrics.forEach((value, key) => {
      const [name] = key.split(':');
      
      if (value.count !== undefined) {
        // Histogram
        output += `# TYPE ${name} histogram\n`;
        output += `${name}_count ${value.count}\n`;
        output += `${name}_sum ${value.sum}\n`;
      } else if (value.labels) {
        // Counter with labels
        output += `# TYPE ${name} counter\n`;
        Object.entries(value.labels).forEach(([label, count]) => {
          output += `${name}{${this.formatLabels(label)}} ${count}\n`;
        });
      } else {
        // Gauge
        output += `# TYPE ${name} gauge\n`;
        output += `${name} ${value.value}\n`;
      }
    });
    
    return output;
  }

  private getMetricKey(name: string, labels?: Record<string, string>): string {
    if (!labels) return name;
    const labelStr = Object.entries(labels)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([k, v]) => `${k}=${v}`)
      .join(',');
    return `${name}:${labelStr}`;
  }

  private percentile(values: number[], p: number): number {
    const index = Math.ceil(values.length * p) - 1;
    return values[Math.max(0, index)];
  }

  private formatLabels(label: string): string {
    // Convert label format for Prometheus
    return label.split(':').map(part => {
      const [key, value] = part.split('=');
      return `${key}="${value}"`;
    }).join(',');
  }
}

// tests/monitoring/distributed-tracing.test.ts
import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { TracingService, Span, SpanContext } from '../../src/monitoring/tracing-service';

describe('TracingService', () => {
  let tracingService: TracingService;

  beforeEach(() => {
    tracingService = new TracingService({
      serviceName: 'trading-platform',
      endpoint: 'http://localhost:9411/api/v2/spans',
    });
  });

  describe('span creation and management', () => {
    it('should create root span', () => {
      // Act
      const span = tracingService.startSpan('process-trade');

      // Assert
      expect(span.traceId).toBeDefined();
      expect(span.spanId).toBeDefined();
      expect(span.operationName).toBe('process-trade');
      expect(span.parentSpanId).toBeUndefined();
    });

    it('should create child span with parent context', () => {
      // Arrange
      const parentSpan = tracingService.startSpan('api-request');
      
      // Act
      const childSpan = tracingService.startSpan('database-query', {
        parent: parentSpan.context(),
      });

      // Assert
      expect(childSpan.traceId).toBe(parentSpan.traceId);
      expect(childSpan.parentSpanId).toBe(parentSpan.spanId);
    });

    it('should add tags and logs to span', () => {
      // Arrange
      const span = tracingService.startSpan('trade-execution');

      // Act
      span.setTag('trade.symbol', 'AAPL');
      span.setTag('trade.quantity', 100);
      span.log({ event: 'trade-validated', status: 'success' });

      // Assert
      const spanData = span.toJSON();
      expect(spanData.tags['trade.symbol']).toBe('AAPL');
      expect(spanData.tags['trade.quantity']).toBe(100);
      expect(spanData.logs).toHaveLength(1);
      expect(spanData.logs[0].fields.event).toBe('trade-validated');
    });

    it('should measure span duration', async () => {
      // Arrange
      const span = tracingService.startSpan('async-operation');

      // Act
      await new Promise(resolve => setTimeout(resolve, 100));
      span.finish();

      // Assert
      const spanData = span.toJSON();
      expect(spanData.duration).toBeGreaterThan(90000); // microseconds
      expect(spanData.duration).toBeLessThan(150000);
    });
  });

  describe('context propagation', () => {
    it('should serialize and deserialize span context', () => {
      // Arrange
      const span = tracingService.startSpan('http-request');
      const context = span.context();

      // Act
      const headers = tracingService.inject(context);
      const extractedContext = tracingService.extract(headers);

      // Assert
      expect(extractedContext?.traceId).toBe(context.traceId);
      expect(extractedContext?.spanId).toBe(context.spanId);
    });
  });
});

// src/monitoring/tracing-service.ts
import { v4 as uuidv4 } from 'uuid';

export interface SpanContext {
  traceId: string;
  spanId: string;
  flags: number;
}

export interface SpanOptions {
  parent?: SpanContext;
  tags?: Record<string, any>;
}

export class Span {
  traceId: string;
  spanId: string;
  parentSpanId?: string;
  operationName: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  tags: Record<string, any> = {};
  logs: Array<{ timestamp: number; fields: Record<string, any> }> = [];

  constructor(operationName: string, options?: SpanOptions) {
    this.operationName = operationName;
    this.spanId = uuidv4().replace(/-/g, '').substring(0, 16);
    this.startTime = Date.now() * 1000; // microseconds
    
    if (options?.parent) {
      this.traceId = options.parent.traceId;
      this.parentSpanId = options.parent.spanId;
    } else {
      this.traceId = uuidv4().replace(/-/g, '').substring(0, 32);
    }

    if (options?.tags) {
      this.tags = options.tags;
    }
  }

  setTag(key: string, value: any): void {
    this.tags[key] = value;
  }

  log(fields: Record<string, any>): void {
    this.logs.push({
      timestamp: Date.now() * 1000,
      fields,
    });
  }

  finish(): void {
    this.endTime = Date.now() * 1000;
    this.duration = this.endTime - this.startTime;
  }

  context(): SpanContext {
    return {
      traceId: this.traceId,
      spanId: this.spanId,
      flags: 1,
    };
  }

  toJSON(): any {
    return {
      traceId: this.traceId,
      id: this.spanId,
      parentId: this.parentSpanId,
      name: this.operationName,
      timestamp: this.startTime,
      duration: this.duration,
      tags: this.tags,
      logs: this.logs,
    };
  }
}

export class TracingService {
  private serviceName: string;
  private endpoint: string;

  constructor(config: { serviceName: string; endpoint: string }) {
    this.serviceName = config.serviceName;
    this.endpoint = config.endpoint;
  }

  startSpan(operationName: string, options?: SpanOptions): Span {
    const span = new Span(operationName, options);
    span.setTag('service.name', this.serviceName);
    return span;
  }

  inject(context: SpanContext): Record<string, string> {
    return {
      'x-trace-id': context.traceId,
      'x-span-id': context.spanId,
      'x-flags': context.flags.toString(),
    };
  }

  extract(headers: Record<string, string>): SpanContext | null {
    const traceId = headers['x-trace-id'];
    const spanId = headers['x-span-id'];
    const flags = parseInt(headers['x-flags'] || '1');

    if (!traceId || !spanId) {
      return null;
    }

    return { traceId, spanId, flags };
  }

  async reportSpan(span: Span): Promise<void> {
    // Send span to tracing backend (e.g., Jaeger, Zipkin)
    await fetch(this.endpoint, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify([span.toJSON()]),
    });
  }
}

// tests/monitoring/alert-manager.test.ts
import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { AlertManager, AlertRule, AlertSeverity } from '../../src/monitoring/alert-manager';

describe('AlertManager', () => {
  let alertManager: AlertManager;
  let mockNotifier: jest.Mock;

  beforeEach(() => {
    mockNotifier = jest.fn();
    alertManager = new AlertManager({ notifier: mockNotifier });
  });

  describe('alert rule evaluation', () => {
    it('should trigger alert when threshold is exceeded', async () => {
      // Arrange
      const rule: AlertRule = {
        name: 'high-error-rate',
        condition: (metrics) => metrics.errorRate > 0.05,
        severity: AlertSeverity.CRITICAL,
        message: 'Error rate exceeds 5%',
      };

      alertManager.addRule(rule);

      // Act
      await alertManager.evaluate({
        errorRate: 0.08,
        timestamp: Date.now(),
      });

      // Assert
      expect(mockNotifier).toHaveBeenCalledWith(
        expect.objectContaining({
          rule: 'high-error-rate',
          severity: AlertSeverity.CRITICAL,
          message: 'Error rate exceeds 5%',
        })
      );
    });

    it('should not trigger alert when condition is not met', async () => {
      // Arrange
      const rule: AlertRule = {
        name: 'low-throughput',
        condition: (metrics) => metrics.throughput < 100,
        severity: AlertSeverity.WARNING,
        message: 'Throughput below threshold',
      };

      alertManager.addRule(rule);

      // Act
      await alertManager.evaluate({
        throughput: 150,
        timestamp: Date.now(),
      });

      // Assert
      expect(mockNotifier).not.toHaveBeenCalled();
    });

    it('should implement alert cooldown period', async () => {
      // Arrange
      const rule: AlertRule = {
        name: 'repeated-alert',
        condition: () => true,
        severity: AlertSeverity.WARNING,
        message: 'Always triggered',
        cooldownMinutes: 5,
      };

      alertManager.addRule(rule);

      // Act - trigger twice
      await alertManager.evaluate({ timestamp: Date.now() });
      await alertManager.evaluate({ timestamp: Date.now() });

      // Assert - only one notification
      expect(mockNotifier).toHaveBeenCalledTimes(1);
    });
  });
});