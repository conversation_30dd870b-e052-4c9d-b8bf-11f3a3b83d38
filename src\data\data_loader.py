#!/usr/bin/env python3
"""
Historical Data Integrity Pipeline
Advanced forex data loading with zero-hallucination validation and cryptographic verification.
"""

import pandas as pd
import numpy as np
import hashlib
import hmac
import json
import logging
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, field
from enum import Enum
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import sqlite3
import os
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DataIntegrityError(Exception):
    """Exception raised when data integrity validation fails"""
    pass


class ValidationLevel(Enum):
    """Data validation levels"""
    BASIC = "basic"
    STANDARD = "standard"
    STRICT = "strict"
    CRYPTOGRAPHIC = "cryptographic"


class DataSource(Enum):
    """Supported data sources"""
    MOCK = "mock"
    CSV = "csv"
    DATABASE = "database"
    API = "api"


@dataclass
class DataIntegrityReport:
    """Report of data integrity validation results"""
    symbol: str
    validation_level: ValidationLevel
    total_records: int
    validation_timestamp: datetime
    checks_passed: List[str] = field(default_factory=list)
    checks_failed: List[str] = field(default_factory=list)
    data_hash: str = ""
    hmac_signature: str = ""
    integrity_score: float = 0.0
    
    def is_valid(self) -> bool:
        """Check if data passed all integrity checks"""
        return len(self.checks_failed) == 0
    
    def get_summary(self) -> Dict[str, Any]:
        """Get validation summary"""
        return {
            'symbol': self.symbol,
            'validation_level': self.validation_level.value,
            'total_records': self.total_records,
            'checks_passed': len(self.checks_passed),
            'checks_failed': len(self.checks_failed),
            'integrity_score': self.integrity_score,
            'is_valid': self.is_valid(),
            'validation_timestamp': self.validation_timestamp.isoformat()
        }


@dataclass
class OHLCValidationRules:
    """OHLC data validation rules"""
    min_price: float = 0.0001
    max_price: float = 100000.0
    max_spread_ratio: float = 0.1  # 10% max spread
    max_gap_ratio: float = 0.2     # 20% max gap between candles
    min_volume: float = 0.0
    max_volume: float = 1e12
    
    def validate_price_range(self, price: float) -> bool:
        """Validate price is within acceptable range"""
        return self.min_price <= price <= self.max_price
    
    def validate_ohlc_relationship(self, open_price: float, high: float, 
                                 low: float, close: float) -> bool:
        """Validate OHLC price relationships"""
        return (low <= open_price <= high and 
                low <= close <= high and 
                low <= high)
    
    def validate_spread(self, high: float, low: float) -> bool:
        """Validate spread is reasonable"""
        if high == 0:
            return False
        spread_ratio = (high - low) / high
        return spread_ratio <= self.max_spread_ratio


class DataHashManager:
    """Manages cryptographic hashing for data integrity"""
    
    def __init__(self, secret_key: str = "dgm_data_integrity_key"):
        self.secret_key = secret_key.encode('utf-8')
    
    def calculate_data_hash(self, data: pd.DataFrame) -> str:
        """Calculate SHA-256 hash of DataFrame"""
        # Create deterministic string representation
        data_str = self._dataframe_to_string(data)
        return hashlib.sha256(data_str.encode('utf-8')).hexdigest()
    
    def calculate_hmac_signature(self, data: pd.DataFrame) -> str:
        """Calculate HMAC signature for data authentication"""
        data_str = self._dataframe_to_string(data)
        return hmac.new(
            self.secret_key,
            data_str.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def verify_data_integrity(self, data: pd.DataFrame, 
                            expected_hash: str, expected_hmac: str) -> bool:
        """Verify data integrity using hash and HMAC"""
        current_hash = self.calculate_data_hash(data)
        current_hmac = self.calculate_hmac_signature(data)
        
        return (current_hash == expected_hash and 
                current_hmac == expected_hmac)
    
    def _dataframe_to_string(self, data: pd.DataFrame) -> str:
        """Convert DataFrame to deterministic string representation"""
        # Sort by index to ensure consistency
        sorted_data = data.sort_index()
        
        # Create string representation with precision control
        data_dict = {}
        for col in sorted_data.columns:
            if sorted_data[col].dtype in ['float64', 'float32']:
                # Round to 6 decimal places for consistency
                data_dict[col] = sorted_data[col].round(6).tolist()
            else:
                data_dict[col] = sorted_data[col].tolist()
        
        # Add index
        data_dict['index'] = [str(idx) for idx in sorted_data.index]
        
        return json.dumps(data_dict, sort_keys=True)


class DataValidator:
    """Advanced data validation with multiple integrity checks"""
    
    def __init__(self, validation_level: ValidationLevel = ValidationLevel.STANDARD):
        self.validation_level = validation_level
        self.rules = OHLCValidationRules()
        self.hash_manager = DataHashManager()
        self._lock = threading.Lock()
    
    def validate_data(self, data: pd.DataFrame, symbol: str) -> DataIntegrityReport:
        """Perform comprehensive data validation"""
        with self._lock:
            logger.info(f"Starting validation for {symbol} with {len(data)} records")
            
            report = DataIntegrityReport(
                symbol=symbol,
                validation_level=self.validation_level,
                total_records=len(data),
                validation_timestamp=datetime.now(timezone.utc)
            )
            
            # Run validation checks based on level
            validation_methods = self._get_validation_methods()
            
            for method_name, method in validation_methods.items():
                try:
                    if method(data):
                        report.checks_passed.append(method_name)
                    else:
                        report.checks_failed.append(method_name)
                        logger.warning(f"Validation failed: {method_name} for {symbol}")
                except Exception as e:
                    report.checks_failed.append(f"{method_name}_error: {str(e)}")
                    logger.error(f"Validation error in {method_name}: {e}")
            
            # Calculate integrity score
            total_checks = len(validation_methods)
            passed_checks = len(report.checks_passed)
            report.integrity_score = passed_checks / total_checks if total_checks > 0 else 0.0
            
            # Generate cryptographic signatures if required
            if self.validation_level in [ValidationLevel.STRICT, ValidationLevel.CRYPTOGRAPHIC]:
                report.data_hash = self.hash_manager.calculate_data_hash(data)
                report.hmac_signature = self.hash_manager.calculate_hmac_signature(data)
            
            logger.info(f"Validation completed for {symbol}: {report.integrity_score:.2%} passed")
            return report
    
    def _get_validation_methods(self) -> Dict[str, callable]:
        """Get validation methods based on validation level"""
        basic_methods = {
            'ohlc_structure': self._validate_ohlc_structure,
            'price_relationships': self._validate_price_relationships,
            'no_null_values': self._validate_no_nulls,
            'monotonic_index': self._validate_monotonic_index
        }
        
        standard_methods = {
            **basic_methods,
            'price_ranges': self._validate_price_ranges,
            'spread_validation': self._validate_spreads,
            'volume_validation': self._validate_volume,
            'temporal_consistency': self._validate_temporal_consistency
        }
        
        strict_methods = {
            **standard_methods,
            'gap_analysis': self._validate_price_gaps,
            'outlier_detection': self._validate_outliers,
            'statistical_consistency': self._validate_statistical_consistency,
            'duplicate_detection': self._validate_duplicates
        }
        
        cryptographic_methods = {
            **strict_methods,
            'hash_consistency': self._validate_hash_consistency,
            'data_completeness': self._validate_data_completeness
        }
        
        level_methods = {
            ValidationLevel.BASIC: basic_methods,
            ValidationLevel.STANDARD: standard_methods,
            ValidationLevel.STRICT: strict_methods,
            ValidationLevel.CRYPTOGRAPHIC: cryptographic_methods
        }
        
        return level_methods.get(self.validation_level, basic_methods)
    
    def _validate_ohlc_structure(self, data: pd.DataFrame) -> bool:
        """Validate OHLC data structure"""
        required_columns = ['open', 'high', 'low', 'close']
        return all(col in data.columns for col in required_columns)
    
    def _validate_price_relationships(self, data: pd.DataFrame) -> bool:
        """Validate OHLC price relationships"""
        return (
            (data['high'] >= data['low']).all() and
            (data['high'] >= data['open']).all() and
            (data['high'] >= data['close']).all() and
            (data['low'] <= data['open']).all() and
            (data['low'] <= data['close']).all()
        )
    
    def _validate_no_nulls(self, data: pd.DataFrame) -> bool:
        """Validate no null values in critical columns"""
        critical_columns = ['open', 'high', 'low', 'close']
        return not data[critical_columns].isnull().any().any()
    
    def _validate_monotonic_index(self, data: pd.DataFrame) -> bool:
        """Validate index is monotonically increasing"""
        return data.index.is_monotonic_increasing
    
    def _validate_price_ranges(self, data: pd.DataFrame) -> bool:
        """Validate prices are within acceptable ranges"""
        price_columns = ['open', 'high', 'low', 'close']
        for col in price_columns:
            if not data[col].apply(self.rules.validate_price_range).all():
                return False
        return True
    
    def _validate_spreads(self, data: pd.DataFrame) -> bool:
        """Validate spreads are reasonable"""
        return data.apply(
            lambda row: self.rules.validate_spread(row['high'], row['low']), 
            axis=1
        ).all()
    
    def _validate_volume(self, data: pd.DataFrame) -> bool:
        """Validate volume data if present"""
        if 'volume' not in data.columns:
            return True  # Skip if no volume data
        
        return (
            (data['volume'] >= self.rules.min_volume).all() and
            (data['volume'] <= self.rules.max_volume).all()
        )
    
    def _validate_temporal_consistency(self, data: pd.DataFrame) -> bool:
        """Validate temporal consistency of data"""
        if len(data) < 2:
            return True
        
        # Check for reasonable time gaps
        time_diffs = data.index.to_series().diff().dropna()
        
        # Allow for larger gaps to accommodate weekends and holidays
        max_gap = timedelta(days=7)  # Allow for weekends/holidays
        return (time_diffs <= max_gap).all()
    
    def _validate_price_gaps(self, data: pd.DataFrame) -> bool:
        """Validate price gaps between consecutive candles"""
        if len(data) < 2:
            return True
        
        # Calculate gaps between consecutive closes and opens
        close_to_open_gaps = abs(data['open'].iloc[1:].values - data['close'].iloc[:-1].values)
        avg_prices = (data['high'] + data['low']).iloc[:-1] / 2
        
        gap_ratios = close_to_open_gaps / avg_prices
        return (gap_ratios <= self.rules.max_gap_ratio).all()
    
    def _validate_outliers(self, data: pd.DataFrame) -> bool:
        """Detect and validate outliers using statistical methods"""
        price_columns = ['open', 'high', 'low', 'close']
        
        for col in price_columns:
            # Use IQR method for outlier detection
            Q1 = data[col].quantile(0.25)
            Q3 = data[col].quantile(0.75)
            IQR = Q3 - Q1
            
            lower_bound = Q1 - 3 * IQR  # More lenient than typical 1.5
            upper_bound = Q3 + 3 * IQR
            
            outliers = (data[col] < lower_bound) | (data[col] > upper_bound)
            if outliers.sum() > len(data) * 0.05:  # More than 5% outliers
                return False
        
        return True
    
    def _validate_statistical_consistency(self, data: pd.DataFrame) -> bool:
        """Validate statistical consistency of price movements"""
        if len(data) < 10:
            return True
        
        # Calculate returns
        returns = data['close'].pct_change().dropna()
        
        # Check for reasonable volatility
        volatility = returns.std()
        if volatility > 0.1:  # 10% daily volatility threshold
            return False
        
        # Check for reasonable skewness and kurtosis
        skewness = returns.skew()
        kurtosis = returns.kurtosis()
        
        return abs(skewness) < 5 and abs(kurtosis) < 25
    
    def _validate_duplicates(self, data: pd.DataFrame) -> bool:
        """Validate no duplicate timestamps"""
        return not data.index.duplicated().any()
    
    def _validate_hash_consistency(self, data: pd.DataFrame) -> bool:
        """Validate data hash consistency (placeholder for stored hash comparison)"""
        # In production, this would compare against stored hash
        current_hash = self.hash_manager.calculate_data_hash(data)
        return len(current_hash) == 64  # SHA-256 hash length
    
    def _validate_data_completeness(self, data: pd.DataFrame) -> bool:
        """Validate data completeness for expected time series"""
        if len(data) < 2:
            return True
        
        # Check for missing time periods (basic implementation)
        expected_frequency = pd.infer_freq(data.index)
        if expected_frequency is None:
            return True  # Cannot infer frequency
        
        # Generate expected index
        expected_index = pd.date_range(
            start=data.index[0],
            end=data.index[-1],
            freq=expected_frequency
        )
        
        # Allow for some missing data (weekends, holidays)
        missing_ratio = 1 - (len(data) / len(expected_index))
        return missing_ratio <= 0.3  # Allow up to 30% missing data


class ForexDataLoader:
    """Advanced forex data loader with comprehensive integrity validation"""
    
    def __init__(self, 
                 data_source: DataSource = DataSource.MOCK,
                 validation_level: ValidationLevel = ValidationLevel.STANDARD,
                 cache_enabled: bool = True):
        self.data_source = data_source
        self.validator = DataValidator(validation_level)
        self.cache_enabled = cache_enabled
        self.cache_dir = Path("data_cache")
        self.cache_dir.mkdir(exist_ok=True)
        self._setup_database()
        
        logger.info(f"ForexDataLoader initialized with {data_source.value} source, "
                   f"{validation_level.value} validation")
    
    def load_pair(self, pair: str, 
                  start_date: Optional[datetime] = None,
                  end_date: Optional[datetime] = None,
                  timeframe: str = "1H") -> Tuple[pd.DataFrame, DataIntegrityReport]:
        """
        Load forex pair data with comprehensive integrity validation
        
        Args:
            pair: Currency pair (e.g., 'EURUSD')
            start_date: Start date for data
            end_date: End date for data
            timeframe: Data timeframe (1M, 5M, 1H, 1D, etc.)
            
        Returns:
            Tuple of (validated_data, integrity_report)
        """
        logger.info(f"Loading {pair} data from {start_date} to {end_date}")
        
        try:
            # Check cache first
            if self.cache_enabled:
                cached_data = self._load_from_cache(pair, start_date, end_date, timeframe)
                if cached_data is not None:
                    logger.info(f"Loaded {pair} from cache")
                    report = self.validator.validate_data(cached_data, pair)
                    if report.is_valid():
                        return cached_data, report
                    else:
                        logger.warning(f"Cached data for {pair} failed validation, reloading")
            
            # Fetch raw data from source
            raw_data = self._fetch_from_source(pair, start_date, end_date, timeframe)
            
            # Validate OHLC structure
            self._validate_ohlc_structure(raw_data)
            
            # Apply comprehensive integrity checks
            verified_data, integrity_report = self._apply_integrity_checks(raw_data, pair)
            
            # Cache validated data
            if self.cache_enabled and integrity_report.is_valid():
                self._save_to_cache(verified_data, pair, start_date, end_date, timeframe)
            
            # Store in database
            self._store_in_database(verified_data, pair, integrity_report)
            
            logger.info(f"Successfully loaded {pair}: {len(verified_data)} records, "
                       f"integrity score: {integrity_report.integrity_score:.2%}")
            
            return verified_data, integrity_report
            
        except Exception as e:
            logger.error(f"Failed to load {pair}: {e}")
            raise DataIntegrityError(f"Data loading failed for {pair}: {str(e)}")
    
    def load_multiple_pairs(self, pairs: List[str], 
                          start_date: Optional[datetime] = None,
                          end_date: Optional[datetime] = None,
                          timeframe: str = "1H",
                          max_workers: int = 4) -> Dict[str, Tuple[pd.DataFrame, DataIntegrityReport]]:
        """Load multiple currency pairs concurrently"""
        logger.info(f"Loading {len(pairs)} pairs concurrently")
        
        results = {}
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_pair = {
                executor.submit(self.load_pair, pair, start_date, end_date, timeframe): pair
                for pair in pairs
            }
            
            # Collect results
            for future in as_completed(future_to_pair):
                pair = future_to_pair[future]
                try:
                    data, report = future.result()
                    results[pair] = (data, report)
                    logger.info(f"Completed loading {pair}")
                except Exception as e:
                    logger.error(f"Failed to load {pair}: {e}")
                    results[pair] = (None, None)
        
        return results
    
    def _fetch_from_source(self, pair: str, 
                          start_date: Optional[datetime] = None,
                          end_date: Optional[datetime] = None,
                          timeframe: str = "1H") -> pd.DataFrame:
        """Fetch data from configured source"""
        if self.data_source == DataSource.MOCK:
            return self._generate_mock_data(pair, start_date, end_date, timeframe)
        elif self.data_source == DataSource.CSV:
            return self._load_from_csv(pair, timeframe)
        elif self.data_source == DataSource.DATABASE:
            return self._load_from_database(pair, start_date, end_date, timeframe)
        elif self.data_source == DataSource.API:
            return self._load_from_api(pair, start_date, end_date, timeframe)
        else:
            raise ValueError(f"Unsupported data source: {self.data_source}")
    
    def _generate_mock_data(self, pair: str, 
                           start_date: Optional[datetime] = None,
                           end_date: Optional[datetime] = None,
                           timeframe: str = "1H") -> pd.DataFrame:
        """Generate realistic mock forex data for testing"""
        if start_date is None:
            start_date = datetime.now(timezone.utc) - timedelta(days=30)
        if end_date is None:
            end_date = datetime.now(timezone.utc)
        
        # Generate time index based on timeframe
        freq_map = {
            '1M': '1T',   # 1 minute
            '5M': '5T',   # 5 minutes
            '15M': '15T', # 15 minutes
            '1H': '1H',   # 1 hour
            '4H': '4H',   # 4 hours
            '1D': '1D'    # 1 day
        }
        
        freq = freq_map.get(timeframe, '1H')
        
        # Create time index (skip weekends for forex)
        dates = pd.date_range(start=start_date, end=end_date, freq=freq)
        # Filter out weekends (forex markets closed)
        dates = dates[dates.weekday < 5]
        
        # Base price for different pairs
        base_prices = {
            'EURUSD': 1.1000,
            'GBPUSD': 1.3000,
            'USDJPY': 110.00,
            'USDCHF': 0.9200,
            'AUDUSD': 0.7500,
            'USDCAD': 1.2500,
            'NZDUSD': 0.7000
        }
        
        base_price = base_prices.get(pair, 1.0000)
        
        # Generate realistic price movements
        np.random.seed(hash(pair) % 2**32)  # Deterministic but pair-specific
        
        # Generate returns with realistic properties
        returns = np.random.normal(0, 0.001, len(dates))  # 0.1% hourly volatility
        
        # Add some trend and mean reversion
        trend = np.sin(np.arange(len(dates)) * 2 * np.pi / (24 * 7)) * 0.0005  # Weekly cycle
        returns += trend
        
        # Generate prices
        prices = base_price * np.exp(np.cumsum(returns))
        
        # Generate OHLC data
        data = []
        for i, (date, price) in enumerate(zip(dates, prices)):
            # Generate realistic OHLC from close price
            volatility = abs(returns[i]) * 2
            
            open_price = price * (1 + np.random.normal(0, volatility * 0.5))
            close_price = price
            
            high_price = max(open_price, close_price) * (1 + abs(np.random.normal(0, volatility)))
            low_price = min(open_price, close_price) * (1 - abs(np.random.normal(0, volatility)))
            
            # Ensure OHLC relationships
            high_price = max(high_price, open_price, close_price)
            low_price = min(low_price, open_price, close_price)
            
            volume = np.random.lognormal(10, 1)  # Realistic volume distribution
            
            data.append({
                'open': round(open_price, 5),
                'high': round(high_price, 5),
                'low': round(low_price, 5),
                'close': round(close_price, 5),
                'volume': int(volume)
            })
        
        df = pd.DataFrame(data, index=dates)
        df.index.name = 'timestamp'
        
        # Add symbol metadata
        df.attrs['symbol'] = pair
        df.attrs['timeframe'] = timeframe
        df.attrs['source'] = 'mock'
        
        return df
    
    def _load_from_csv(self, pair: str, timeframe: str) -> pd.DataFrame:
        """Load data from CSV file"""
        csv_path = self.cache_dir / f"{pair}_{timeframe}.csv"
        
        if not csv_path.exists():
            raise FileNotFoundError(f"CSV file not found: {csv_path}")
        
        df = pd.read_csv(csv_path, index_col=0, parse_dates=True)
        df.attrs['symbol'] = pair
        df.attrs['timeframe'] = timeframe
        df.attrs['source'] = 'csv'
        
        return df
    
    def _load_from_database(self, pair: str, 
                           start_date: Optional[datetime] = None,
                           end_date: Optional[datetime] = None,
                           timeframe: str = "1H") -> pd.DataFrame:
        """Load data from database"""
        query = """
        SELECT timestamp, open, high, low, close, volume
        FROM forex_data 
        WHERE symbol = ? AND timeframe = ?
        """
        params = [pair, timeframe]
        
        if start_date:
            query += " AND timestamp >= ?"
            params.append(start_date.isoformat())
        
        if end_date:
            query += " AND timestamp <= ?"
            params.append(end_date.isoformat())
        
        query += " ORDER BY timestamp"
        
        with sqlite3.connect(self.db_path) as conn:
            df = pd.read_sql_query(query, conn, params=params, 
                                 index_col='timestamp', parse_dates=['timestamp'])
        
        if df.empty:
            raise DataIntegrityError(f"No data found for {pair} in database")
        
        df.attrs['symbol'] = pair
        df.attrs['timeframe'] = timeframe
        df.attrs['source'] = 'database'
        
        return df
    
    def _load_from_api(self, pair: str, 
                      start_date: Optional[datetime] = None,
                      end_date: Optional[datetime] = None,
                      timeframe: str = "1H") -> pd.DataFrame:
        """Load data from API (placeholder implementation)"""
        # This would integrate with actual forex data APIs
        # For now, return mock data
        logger.warning("API source not implemented, using mock data")
        return self._generate_mock_data(pair, start_date, end_date, timeframe)
    
    def _validate_ohlc_structure(self, data: pd.DataFrame) -> None:
        """Validate basic OHLC data structure"""
        required_columns = ['open', 'high', 'low', 'close']
        missing_columns = [col for col in required_columns if col not in data.columns]
        
        if missing_columns:
            raise DataIntegrityError(f"Missing required columns: {missing_columns}")
        
        if data.empty:
            raise DataIntegrityError("Data is empty")
        
        if not isinstance(data.index, pd.DatetimeIndex):
            raise DataIntegrityError("Index must be DatetimeIndex")
    
    def _apply_integrity_checks(self, data: pd.DataFrame, 
                               symbol: str) -> Tuple[pd.DataFrame, DataIntegrityReport]:
        """Apply comprehensive integrity checks with zero-hallucination validation"""
        logger.info(f"Applying integrity checks to {symbol} data")
        
        # Run validation
        integrity_report = self.validator.validate_data(data, symbol)
        
        # If validation fails, raise exception
        if not integrity_report.is_valid():
            failed_checks = ', '.join(integrity_report.checks_failed)
            raise DataIntegrityError(
                f"Validation failed for {symbol}. Failed checks: {failed_checks}"
            )
        
        # Apply any data corrections if needed (minimal, preserving integrity)
        verified_data = self._apply_data_corrections(data)
        
        return verified_data, integrity_report
    
    def _apply_data_corrections(self, data: pd.DataFrame) -> pd.DataFrame:
        """Apply minimal data corrections while preserving integrity"""
        corrected_data = data.copy()
        
        # Sort by timestamp to ensure chronological order
        corrected_data = corrected_data.sort_index()
        
        # Remove any duplicate timestamps
        corrected_data = corrected_data[~corrected_data.index.duplicated(keep='first')]
        
        # Round prices to appropriate precision
        price_columns = ['open', 'high', 'low', 'close']
        for col in price_columns:
            if col in corrected_data.columns:
                corrected_data[col] = corrected_data[col].round(5)
        
        return corrected_data
    
    def _setup_database(self) -> None:
        """Setup SQLite database for data storage"""
        self.db_path = self.cache_dir / "forex_data.db"
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS forex_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    timeframe TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    open REAL NOT NULL,
                    high REAL NOT NULL,
                    low REAL NOT NULL,
                    close REAL NOT NULL,
                    volume REAL,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(symbol, timeframe, timestamp)
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS integrity_reports (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    validation_level TEXT NOT NULL,
                    total_records INTEGER NOT NULL,
                    checks_passed INTEGER NOT NULL,
                    checks_failed INTEGER NOT NULL,
                    integrity_score REAL NOT NULL,
                    data_hash TEXT,
                    hmac_signature TEXT,
                    validation_timestamp TEXT NOT NULL,
                    report_data TEXT
                )
            """)
            
            # Create indexes for performance
            conn.execute("CREATE INDEX IF NOT EXISTS idx_forex_symbol_time ON forex_data(symbol, timeframe, timestamp)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_integrity_symbol ON integrity_reports(symbol)")
    
    def _store_in_database(self, data: pd.DataFrame, symbol: str, 
                          report: DataIntegrityReport) -> None:
        """Store validated data and integrity report in database"""
        with sqlite3.connect(self.db_path) as conn:
            # Store data
            data_to_store = data.copy()
            data_to_store['symbol'] = symbol
            data_to_store['timeframe'] = data.attrs.get('timeframe', '1H')
            data_to_store.reset_index(inplace=True)
            
            data_to_store.to_sql('forex_data', conn, if_exists='replace', index=False)
            
            # Store integrity report
            report_data = json.dumps(report.get_summary())
            
            conn.execute("""
                INSERT OR REPLACE INTO integrity_reports 
                (symbol, validation_level, total_records, checks_passed, checks_failed,
                 integrity_score, data_hash, hmac_signature, validation_timestamp, report_data)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                report.symbol,
                report.validation_level.value,
                report.total_records,
                len(report.checks_passed),
                len(report.checks_failed),
                report.integrity_score,
                report.data_hash,
                report.hmac_signature,
                report.validation_timestamp.isoformat(),
                report_data
            ))
    
    def _load_from_cache(self, pair: str, start_date: Optional[datetime],
                        end_date: Optional[datetime], timeframe: str) -> Optional[pd.DataFrame]:
        """Load data from cache if available and valid"""
        cache_key = f"{pair}_{timeframe}_{start_date}_{end_date}"
        cache_file = self.cache_dir / f"{cache_key}.pkl"
        
        if cache_file.exists():
            try:
                # Check if cache is recent (within 1 hour)
                cache_age = time.time() - cache_file.stat().st_mtime
                if cache_age < 3600:  # 1 hour
                    return pd.read_pickle(cache_file)
            except Exception as e:
                logger.warning(f"Failed to load cache for {pair}: {e}")
        
        return None
    
    def _save_to_cache(self, data: pd.DataFrame, pair: str, 
                      start_date: Optional[datetime], end_date: Optional[datetime],
                      timeframe: str) -> None:
        """Save validated data to cache"""
        cache_key = f"{pair}_{timeframe}_{start_date}_{end_date}"
        cache_file = self.cache_dir / f"{cache_key}.pkl"
        
        try:
            data.to_pickle(cache_file)
            logger.debug(f"Cached data for {pair}")
        except Exception as e:
            logger.warning(f"Failed to cache data for {pair}: {e}")
    
    def get_integrity_report(self, symbol: str) -> Optional[DataIntegrityReport]:
        """Get latest integrity report for symbol"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT * FROM integrity_reports 
                WHERE symbol = ? 
                ORDER BY validation_timestamp DESC 
                LIMIT 1
            """, (symbol,))
            
            row = cursor.fetchone()
            if row:
                return DataIntegrityReport(
                    symbol=row[1],
                    validation_level=ValidationLevel(row[2]),
                    total_records=row[3],
                    validation_timestamp=datetime.fromisoformat(row[9]),
                    checks_passed=[],  # Would need to parse from report_data
                    checks_failed=[],
                    integrity_score=row[6],
                    data_hash=row[7] or "",
                    hmac_signature=row[8] or ""
                )
        
        return None


# Factory function for easy instantiation
def create_forex_data_loader(validation_level: ValidationLevel = ValidationLevel.STANDARD,
                           data_source: DataSource = DataSource.MOCK,
                           cache_enabled: bool = True) -> ForexDataLoader:
    """Factory function to create ForexDataLoader with specified configuration"""
    return ForexDataLoader(
        data_source=data_source,
        validation_level=validation_level,
        cache_enabled=cache_enabled
    )


# Example usage and testing
if __name__ == "__main__":
    # Create loader with strict validation
    loader = create_forex_data_loader(
        validation_level=ValidationLevel.STRICT,
        data_source=DataSource.MOCK
    )
    
    # Load single pair
    try:
        data, report = loader.load_pair("EURUSD")
        print(f"Loaded {len(data)} records for EURUSD")
        print(f"Integrity score: {report.integrity_score:.2%}")
        print(f"Validation status: {'PASSED' if report.is_valid() else 'FAILED'}")
        
        if not report.is_valid():
            print(f"Failed checks: {report.checks_failed}")
        
    except DataIntegrityError as e:
        print(f"Data integrity error: {e}")
    
    # Load multiple pairs
    pairs = ["EURUSD", "GBPUSD", "USDJPY"]
    results = loader.load_multiple_pairs(pairs)
    
    print(f"\nLoaded {len(results)} pairs:")
    for pair, (data, report) in results.items():
        if data is not None:
            print(f"  {pair}: {len(data)} records, {report.integrity_score:.2%} integrity")
        else:
            print(f"  {pair}: FAILED to load")