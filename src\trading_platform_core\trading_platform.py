# src/platform/trading_platform.py
"""
AI Enhanced Trading Platform - Main Platform Class
Integrates all components into a unified trading system
"""

import asyncio
import logging
import signal
import sys
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from pathlib import Path

# Import all platform components
from validation.data_validator import DataValidator, DataSourceManager
from evolution.darwin_godel_machine import DarwinGodelMachine, StrategyGenome
from chatbot.knowledge_base import TradingChatbot
from trading.mt5_integration import MT5Integration, SafetyLimits, TradingMode, OrderType
from monitoring.performance_monitor import PerformanceMonitor, TradingPerformanceMetric
from data_feeds.multi_source_feed import DataFeedAggregator, DataSource

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('trading_platform.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

class TradingPlatformConfig:
    """Configuration class for the trading platform"""
    
    def __init__(self):
        # Data validation settings
        self.enable_data_validation = True
        self.validation_strict_mode = True
        
        # AI Evolution settings
        self.ai_population_size = 50
        self.ai_generations = 100
        self.ai_mutation_rate = 0.1
        self.ai_crossover_rate = 0.7
        
        # Trading safety settings
        self.max_daily_loss = 1000.0
        self.max_position_size = 1.0
        self.max_open_positions = 5
        self.max_risk_per_trade = 100.0
        self.allowed_symbols = ["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD"]
        self.trading_hours_start = "09:00"
        self.trading_hours_end = "17:00"
        self.emergency_stop_loss = 2000.0
        
        # Performance monitoring settings
        self.monitoring_interval = 1.0  # seconds
        self.enable_performance_alerts = True
        
        # Data feed settings
        self.primary_data_sources = []
        self.backup_data_sources = []
        
        # Platform settings
        self.trading_mode = TradingMode.DUMMY  # Start in dummy mode for safety
        self.auto_start_trading = False
        self.enable_chatbot = True
        self.data_path = "./platform-data"
        self.log_level = logging.INFO

class AIEnhancedTradingPlatform:
    """
    Main AI Enhanced Trading Platform Class
    
    This class integrates all components:
    - Data validation and integrity
    - AI-driven strategy evolution
    - Safe trading execution
    - Performance monitoring
    - Multi-source data feeds
    - Knowledge-based chatbot
    """
    
    def __init__(self, config: TradingPlatformConfig = None):
        self.config = config or TradingPlatformConfig()
        self.logger = logging.getLogger(__name__)
        
        # Platform state
        self.is_initialized = False
        self.is_running = False
        self.shutdown_requested = False
        
        # Component instances
        self.data_validator = None
        self.source_manager = None
        self.darwin_machine = None
        self.chatbot = None
        self.mt5_integration = None
        self.performance_monitor = None
        self.data_aggregator = None
        
        # Platform data
        self.active_strategies = {}
        self.trading_signals = []
        self.platform_metrics = {}
        
        # Event callbacks
        self.event_callbacks = {
            'on_strategy_evolved': [],
            'on_trade_executed': [],
            'on_performance_alert': [],
            'on_data_received': [],
            'on_error': []
        }
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        self.logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.shutdown_requested = True
        asyncio.create_task(self.shutdown())
    
    async def initialize(self) -> bool:
        """Initialize all platform components"""
        try:
            self.logger.info("🚀 Initializing AI Enhanced Trading Platform...")
            
            # Create data directory
            Path(self.config.data_path).mkdir(parents=True, exist_ok=True)
            
            # Initialize data validation
            self.logger.info("📊 Initializing data validation...")
            self.data_validator = DataValidator()
            self.source_manager = DataSourceManager()
            
            # Initialize AI evolution engine
            self.logger.info("🧠 Initializing Darwin-Gödel Machine...")
            self.darwin_machine = DarwinGodelMachine(seed=42)
            
            # Initialize chatbot knowledge system
            self.logger.info("💬 Initializing knowledge-based chatbot...")
            if self.config.enable_chatbot:
                from chatbot.knowledge_base import KnowledgeBase
                kb = KnowledgeBase()
                self.chatbot = TradingChatbot(kb)
                await self._setup_trading_knowledge()
            
            # Initialize trading integration with safety limits
            self.logger.info("💰 Initializing trading system...")
            safety_limits = SafetyLimits(
                max_daily_loss=self.config.max_daily_loss,
                max_position_size=self.config.max_position_size,
                max_open_positions=self.config.max_open_positions,
                max_risk_per_trade=self.config.max_risk_per_trade,
                allowed_symbols=self.config.allowed_symbols,
                trading_hours_start=self.config.trading_hours_start,
                trading_hours_end=self.config.trading_hours_end,
                emergency_stop_loss=self.config.emergency_stop_loss
            )
            self.mt5_integration = MT5Integration(safety_limits, self.config.trading_mode)
            
            # Initialize performance monitoring
            self.logger.info("📈 Initializing performance monitoring...")
            self.performance_monitor = PerformanceMonitor(
                monitoring_interval=self.config.monitoring_interval
            )
            
            # Initialize data feed aggregator
            self.logger.info("📡 Initializing data feed aggregator...")
            self.data_aggregator = DataFeedAggregator()
            await self._setup_data_sources()
            
            # Setup event callbacks
            self._setup_event_callbacks()
            
            self.is_initialized = True
            self.logger.info("✅ Platform initialization completed successfully!")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Platform initialization failed: {e}")
            return False
    
    async def _setup_trading_knowledge(self):
        """Setup initial trading knowledge for the chatbot"""
        try:
            # Add basic trading knowledge
            self.chatbot.add_trading_knowledge()
            self.logger.info("📚 Trading knowledge base initialized")
        except Exception as e:
            self.logger.warning(f"⚠️ Could not fully initialize knowledge base: {e}")
    
    async def _setup_data_sources(self):
        """Setup data sources for the aggregator"""
        try:
            # Add primary data sources
            for source_config in self.config.primary_data_sources:
                source = DataSource(**source_config)
                self.data_aggregator.add_data_source(source)
            
            # Add backup data sources
            for source_config in self.config.backup_data_sources:
                source = DataSource(**source_config)
                self.data_aggregator.add_data_source(source)
            
            self.logger.info(f"📡 Configured {len(self.data_aggregator.data_sources)} data sources")
        except Exception as e:
            self.logger.warning(f"⚠️ Could not setup all data sources: {e}")
    
    def _setup_event_callbacks(self):
        """Setup internal event callbacks"""
        # Performance monitoring alerts
        if self.config.enable_performance_alerts:
            self.performance_monitor.add_alert_callback(self._handle_performance_alert)
        
        # Data feed callbacks
        self.data_aggregator.add_data_callback(self._handle_new_data)
        self.data_aggregator.add_validation_callback(self._handle_data_validation)
    
    def _handle_performance_alert(self, alert):
        """Handle performance monitoring alerts"""
        self.logger.warning(f"⚠️ Performance Alert: {alert}")
        self._trigger_event('on_performance_alert', alert)
    
    def _handle_new_data(self, price_data):
        """Handle new price data from feeds"""
        self._trigger_event('on_data_received', price_data)
    
    def _handle_data_validation(self, validation_result):
        """Handle data validation results"""
        if not validation_result.is_valid:
            self.logger.warning(f"⚠️ Data validation failed: {validation_result.validation_errors}")
    
    def _trigger_event(self, event_name: str, data: Any):
        """Trigger event callbacks"""
        for callback in self.event_callbacks.get(event_name, []):
            try:
                callback(data)
            except Exception as e:
                self.logger.error(f"❌ Error in event callback {event_name}: {e}")
    
    async def start(self) -> bool:
        """Start the trading platform"""
        if not self.is_initialized:
            self.logger.error("❌ Platform not initialized. Call initialize() first.")
            return False
        
        try:
            self.logger.info("🚀 Starting AI Enhanced Trading Platform...")
            
            # Start performance monitoring
            self.performance_monitor.start_monitoring()
            self.logger.info("📈 Performance monitoring started")
            
            # Initialize AI population
            self.logger.info("🧠 Initializing AI strategy population...")
            population = self.darwin_machine.initialize_population(
                size=self.config.ai_population_size
            )
            self.logger.info(f"🧬 Created {len(population)} initial strategies")
            
            # Start main trading loop
            self.is_running = True
            self.logger.info("✅ Platform started successfully!")
            
            if self.config.auto_start_trading:
                await self._main_trading_loop()
            else:
                self.logger.info("🔄 Platform ready. Call start_trading() to begin automated trading.")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to start platform: {e}")
            return False
    
    async def start_trading(self):
        """Start automated trading"""
        if not self.is_running:
            self.logger.error("❌ Platform not running. Call start() first.")
            return
        
        self.logger.info("💰 Starting automated trading...")
        await self._main_trading_loop()
    
    async def _main_trading_loop(self):
        """Main trading loop"""
        self.logger.info("🔄 Entering main trading loop...")
        
        generation = 0
        current_population = self.darwin_machine.initialize_population(
            size=self.config.ai_population_size
        )
        
        while self.is_running and not self.shutdown_requested:
            try:
                # Evolution cycle
                if generation < self.config.ai_generations:
                    self.logger.info(f"🧬 Evolution Generation {generation + 1}")
                    
                    # Evaluate strategies
                    await self._evaluate_strategies(current_population)
                    
                    # Evolve to next generation
                    current_population = self.darwin_machine.evolve_generation(current_population)
                    generation += 1
                    
                    # Store in history
                    self.darwin_machine.generation_history.append(current_population.copy())
                    
                    self._trigger_event('on_strategy_evolved', {
                        'generation': generation,
                        'population_size': len(current_population),
                        'best_fitness': max(g.fitness for g in current_population if g.fitness)
                    })
                
                # Get market data
                symbols = self.config.allowed_symbols
                for symbol in symbols:
                    try:
                        price_data_list = await self.data_aggregator.fetch_price_data(symbol)
                        
                        if price_data_list:
                            # Get best quality data
                            best_price = self.data_aggregator.get_best_price(symbol)
                            
                            if best_price:
                                # Generate trading signals using best strategies
                                await self._generate_trading_signals(symbol, best_price, current_population)
                    
                    except Exception as e:
                        self.logger.error(f"❌ Error processing {symbol}: {e}")
                
                # Execute pending trades
                await self._execute_pending_trades()
                
                # Update performance metrics
                await self._update_platform_metrics()
                
                # Sleep before next iteration
                await asyncio.sleep(1.0)
                
            except Exception as e:
                self.logger.error(f"❌ Error in main trading loop: {e}")
                self._trigger_event('on_error', e)
                await asyncio.sleep(5.0)  # Wait before retrying
    
    async def _evaluate_strategies(self, population: List[StrategyGenome]):
        """Evaluate strategy fitness using historical data"""
        # This is a simplified evaluation - in practice, you'd use more sophisticated backtesting
        for genome in population:
            try:
                # Simple fitness calculation for demonstration
                # In practice, this would involve comprehensive backtesting
                genome.fitness = self._calculate_simple_fitness(genome)
            except Exception as e:
                self.logger.error(f"❌ Error evaluating strategy {genome.id}: {e}")
                genome.fitness = 0.0
    
    def _calculate_simple_fitness(self, genome: StrategyGenome) -> float:
        """Calculate a simple fitness score for demonstration"""
        # This is a placeholder - real implementation would use backtesting
        import random
        return random.uniform(0.1, 1.0)
    
    async def _generate_trading_signals(self, symbol: str, price_data, population: List[StrategyGenome]):
        """Generate trading signals using AI strategies"""
        try:
            # Use best performing strategies to generate signals
            best_strategies = sorted(population, key=lambda x: x.fitness or 0, reverse=True)[:5]
            
            for strategy in best_strategies:
                signal = self._evaluate_strategy_signal(strategy, symbol, price_data)
                if signal and signal['confidence'] > 0.7:
                    self.trading_signals.append(signal)
                    
        except Exception as e:
            self.logger.error(f"❌ Error generating signals for {symbol}: {e}")
    
    def _evaluate_strategy_signal(self, strategy: StrategyGenome, symbol: str, price_data) -> Optional[Dict]:
        """Evaluate a strategy to generate trading signal"""
        try:
            # Simplified signal generation based on strategy parameters
            params = strategy.parameters
            
            # Example signal logic (simplified)
            if 'rsi_period' in params and 'rsi_oversold' in params:
                # Simulate RSI-based signal
                confidence = min(0.9, strategy.fitness + 0.1) if strategy.fitness else 0.5
                
                return {
                    'symbol': symbol,
                    'action': 'BUY',  # Simplified
                    'confidence': confidence,
                    'entry_price': price_data.ask,
                    'stop_loss': price_data.ask * 0.995,  # 0.5% stop loss
                    'take_profit': price_data.ask * 1.01,  # 1% take profit
                    'strategy_id': strategy.id,
                    'timestamp': datetime.now()
                }
        except Exception as e:
            self.logger.error(f"❌ Error evaluating strategy signal: {e}")
        
        return None
    
    async def _execute_pending_trades(self):
        """Execute pending trading signals"""
        executed_signals = []
        
        for signal in self.trading_signals:
            try:
                if signal['confidence'] > 0.7:  # Only execute high-confidence signals
                    order = self.mt5_integration.create_order(
                        symbol=signal['symbol'],
                        order_type=OrderType.BUY if signal['action'] == 'BUY' else OrderType.SELL,
                        volume=0.01,  # Small volume for safety
                        price=signal['entry_price'],
                        stop_loss=signal['stop_loss'],
                        take_profit=signal['take_profit'],
                        comment=f"AI Strategy {signal['strategy_id'][:8]}"
                    )
                    
                    success, message, position_id = self.mt5_integration.submit_order(order)
                    
                    if success:
                        self.logger.info(f"✅ Trade executed: {signal['symbol']} {signal['action']} - {message}")
                        self._trigger_event('on_trade_executed', {
                            'signal': signal,
                            'position_id': position_id,
                            'message': message
                        })
                    else:
                        self.logger.warning(f"⚠️ Trade rejected: {message}")
                    
                    executed_signals.append(signal)
                    
            except Exception as e:
                self.logger.error(f"❌ Error executing trade: {e}")
                executed_signals.append(signal)  # Remove failed signal
        
        # Remove executed signals
        for signal in executed_signals:
            self.trading_signals.remove(signal)
    
    async def _update_platform_metrics(self):
        """Update platform performance metrics"""
        try:
            # Record trading performance
            positions = self.mt5_integration.get_positions()
            safety_status = self.mt5_integration.get_safety_status()
            
            metric = TradingPerformanceMetric(
                timestamp=datetime.now(),
                orders_per_second=len(self.trading_signals) / 60.0,  # Approximate
                avg_order_latency_ms=50.0,  # Placeholder
                backtest_execution_time_ms=100.0,  # Placeholder
                data_processing_time_ms=25.0,  # Placeholder
                ml_inference_time_ms=15.0,  # Placeholder
                memory_usage_mb=200.0,  # Placeholder
                active_positions=len(positions),
                pending_orders=len(self.trading_signals)
            )
            
            self.performance_monitor.record_trading_metric(metric)
            
            # Update platform metrics
            self.platform_metrics.update({
                'active_positions': len(positions),
                'pending_signals': len(self.trading_signals),
                'daily_pnl': safety_status.get('daily_pnl', 0.0),
                'total_trades': safety_status.get('total_trades', 0),
                'last_update': datetime.now()
            })
            
        except Exception as e:
            self.logger.error(f"❌ Error updating metrics: {e}")
    
    async def shutdown(self):
        """Gracefully shutdown the platform"""
        self.logger.info("🛑 Shutting down AI Enhanced Trading Platform...")
        
        self.is_running = False
        
        try:
            # Close all open positions in emergency
            if self.mt5_integration:
                positions = self.mt5_integration.get_positions()
                if positions:
                    self.logger.info(f"🚨 Closing {len(positions)} open positions...")
                    result = self.mt5_integration.emergency_stop()
                    self.logger.info(f"Emergency stop result: {result}")
            
            # Stop performance monitoring
            if self.performance_monitor and self.performance_monitor.is_monitoring:
                self.performance_monitor.stop_monitoring()
                self.logger.info("📈 Performance monitoring stopped")
            
            # Save final state
            await self._save_platform_state()
            
            self.logger.info("✅ Platform shutdown completed")
            
        except Exception as e:
            self.logger.error(f"❌ Error during shutdown: {e}")
    
    async def _save_platform_state(self):
        """Save platform state for recovery"""
        try:
            import json
            
            state = {
                'timestamp': datetime.now().isoformat(),
                'platform_metrics': self.platform_metrics,
                'active_strategies': len(self.active_strategies),
                'generation_history_count': len(self.darwin_machine.generation_history) if self.darwin_machine else 0
            }
            
            state_file = Path(self.config.data_path) / 'platform_state.json'
            with open(state_file, 'w') as f:
                json.dump(state, f, indent=2)
            
            self.logger.info(f"💾 Platform state saved to {state_file}")
            
        except Exception as e:
            self.logger.error(f"❌ Error saving platform state: {e}")
    
    # Public API methods
    
    def add_event_callback(self, event_name: str, callback: Callable):
        """Add event callback"""
        if event_name in self.event_callbacks:
            self.event_callbacks[event_name].append(callback)
    
    def get_platform_status(self) -> Dict[str, Any]:
        """Get current platform status"""
        return {
            'is_initialized': self.is_initialized,
            'is_running': self.is_running,
            'trading_mode': self.config.trading_mode.value,
            'platform_metrics': self.platform_metrics,
            'active_positions': len(self.mt5_integration.get_positions()) if self.mt5_integration else 0,
            'pending_signals': len(self.trading_signals),
            'data_sources': len(self.data_aggregator.data_sources) if self.data_aggregator else 0
        }
    
    def get_performance_summary(self, minutes: int = 60) -> Dict[str, Any]:
        """Get performance summary"""
        if self.performance_monitor:
            return self.performance_monitor.get_performance_summary(minutes)
        return {}
    
    async def query_chatbot(self, query: str) -> str:
        """Query the trading chatbot"""
        if self.chatbot:
            response = self.chatbot.get_response(query)
            return response.get('response', 'No response available') if response else 'Chatbot not available'
        return 'Chatbot not enabled'
    
    def get_trading_signals(self) -> List[Dict]:
        """Get current trading signals"""
        return self.trading_signals.copy()
    
    def get_ai_evolution_status(self) -> Dict[str, Any]:
        """Get AI evolution status"""
        if self.darwin_machine:
            generations_completed = len(self.darwin_machine.generation_history)
            current_population_size = len(self.darwin_machine.generation_history[-1]) if self.darwin_machine.generation_history else 0
            
            # Calculate best fitness safely
            best_fitness = 0
            if self.darwin_machine.generation_history:
                last_generation = self.darwin_machine.generation_history[-1]
                fitness_values = [g.fitness for g in last_generation if g.fitness is not None]
                best_fitness = max(fitness_values) if fitness_values else 0
            
            return {
                'generations_completed': generations_completed,
                'current_population_size': current_population_size,
                'best_fitness': best_fitness
            }
        return {}

# Convenience function for easy platform creation
def create_trading_platform(config: TradingPlatformConfig = None) -> AIEnhancedTradingPlatform:
    """Create and return a configured trading platform instance"""
    return AIEnhancedTradingPlatform(config)

# Example usage
if __name__ == "__main__":
    async def main():
        # Create platform with default configuration
        config = TradingPlatformConfig()
        config.trading_mode = TradingMode.DUMMY  # Safe mode for testing
        
        platform = create_trading_platform(config)
        
        # Initialize and start
        if await platform.initialize():
            await platform.start()
        else:
            print("Failed to initialize platform")
    
    # Run the platform
    asyncio.run(main())