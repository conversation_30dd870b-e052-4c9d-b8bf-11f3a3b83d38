/**
 * Integration test utilities for testing bridge services with mock Python engine
 * Provides helpers for setting up integration tests that mirror real system behavior
 */

import { EventEmitter } from 'events';
import { PythonEngineMock, PythonEngineMockConfig } from './python-engine-mock';
import { PythonEngineService } from '../../backend/src/services/bridge/python-engine.service';
import { TradingBridgeService } from '../../backend/src/services/bridge/trading-bridge.service';
import { BacktestBridgeService } from '../../backend/src/services/bridge/backtest-bridge.service';
import { ChatBridgeService } from '../../backend/src/services/bridge/chat-bridge.service';
import { BridgeServiceRegistry } from '../../backend/src/services/bridge/bridge-service-registry';

// Define Logger interface locally to avoid import issues
export interface Logger {
  debug: (message: string, ...args: any[]) => void;
  info: (message: string, ...args: any[]) => void;
  warn: (message: string, ...args: any[]) => void;
  error: (message: string, ...args: any[]) => void;
}

export interface IntegrationTestConfig {
  pythonEngine?: PythonEngineMockConfig;
  enableRealTimeEvents?: boolean;
  testTimeout?: number;
}

export interface IntegrationTestContext {
  pythonEngineMock: PythonEngineMock;
  tradingService: TradingBridgeService;
  backtestService: BacktestBridgeService;
  chatService: ChatBridgeService;
  bridgeRegistry: BridgeServiceRegistry;
  logger: Logger;
  cleanup: () => Promise<void>;
}

/**
 * Mock logger for testing
 */
export function createMockLogger(): Logger {
  return {
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  };
}

/**
 * Create HTTP server mock that uses PythonEngineMock
 */
export class MockHttpServer extends EventEmitter {
  private pythonEngineMock: PythonEngineMock;
  private isRunning: boolean = false;
  private port: number;

  constructor(pythonEngineMock: PythonEngineMock, port: number = 8000) {
    super();
    this.pythonEngineMock = pythonEngineMock;
    this.port = port;
  }

  start(): Promise<void> {
    return new Promise((resolve) => {
      this.isRunning = true;
      this.emit('listening');
      resolve();
    });
  }

  stop(): Promise<void> {
    return new Promise((resolve) => {
      this.isRunning = false;
      this.emit('close');
      resolve();
    });
  }

  isListening(): boolean {
    return this.isRunning;
  }

  getPort(): number {
    return this.port;
  }

  // Mock HTTP endpoints with better error handling
  async handleRequest(method: string, path: string, body: any): Promise<any> {
    if (!this.isRunning) {
      throw new Error('Server not running');
    }

    try {
      switch (path) {
        case '/health':
          return await this.pythonEngineMock.checkHealth();

        case '/api/trading/command':
          if (!body) {
            throw new Error('Request body is required for trading commands');
          }
          return await this.pythonEngineMock.processTradingCommand(body);

        case '/api/backtest/submit':
          if (!body) {
            throw new Error('Request body is required for backtest submission');
          }
          return await this.pythonEngineMock.processBacktest(body);

        case '/api/chat/query':
          if (!body) {
            throw new Error('Request body is required for chat queries');
          }
          return await this.pythonEngineMock.processChat(body);

        case '/api/data/process':
          if (!body) {
            throw new Error('Request body is required for data processing');
          }
          return await this.pythonEngineMock.processData(body);

        default:
          if (path.startsWith('/api/backtest/') && path.endsWith('/status')) {
            const pathParts = path.split('/');
            if (pathParts.length !== 4) {
              throw new Error('Invalid backtest status endpoint format');
            }
            const backtestId = pathParts[3];
            if (!backtestId) {
              throw new Error('Backtest ID is required');
            }
            return await this.pythonEngineMock.getBacktestStatus(backtestId);
          }
          throw new Error(`Unknown endpoint: ${method} ${path}`);
      }
    } catch (error) {
      // Log the error for debugging
      console.error(`MockHttpServer error for ${method} ${path}:`, error);
      throw error;
    }
  }
}

/**
 * Setup integration test environment
 */
export async function setupIntegrationTest(
  config: IntegrationTestConfig = {}
): Promise<IntegrationTestContext> {
  const logger = createMockLogger();
  
  // Create Python engine mock
  const pythonEngineMock = new PythonEngineMock(config.pythonEngine);
  
  // Create mock HTTP server
  const mockServer = new MockHttpServer(pythonEngineMock);
  await mockServer.start();

  // Create Python engine service with mock - Add proper typing
  const pythonEngineService = new PythonEngineService({
    logger,
    config: {
      baseUrl: `http://localhost:${mockServer.getPort()}`,
      timeout: config.testTimeout || 5000,
      retryAttempts: 1,
      retryDelay: 100,
    },
  });

  // Mock the HTTP client with proper typing
  const mockHttpClient = {
    post: jest.fn().mockImplementation(async (path: string, data: any, options?: any) => {
      const response = await mockServer.handleRequest('POST', path, data);
      return { data: response };
    }),
    get: jest.fn().mockImplementation(async (path: string, options?: any) => {
      const response = await mockServer.handleRequest('GET', path, null);
      return { data: response };
    }),
    interceptors: {
      request: { use: jest.fn() },
      response: { use: jest.fn() },
    },
  };

  // Type assertion with proper interface
  (pythonEngineService as any).httpClient = mockHttpClient;

  // Create bridge services
  const tradingService = new TradingBridgeService({
    pythonEngineService,
    logger,
  });

  const backtestService = new BacktestBridgeService({
    pythonEngineService,
    logger,
  });

  const chatService = new ChatBridgeService({
    pythonEngineService,
    logger,
  });

  // Create bridge registry
  const bridgeRegistry = new BridgeServiceRegistry({
    logger,
    config: {
      pythonEngine: {
        baseUrl: `http://localhost:${mockServer.getPort()}`,
        timeout: config.testTimeout || 5000,
        retryAttempts: 1,
        retryDelay: 100,
      },
      healthCheckInterval: 1000,
      cleanupInterval: 2000,
    },
  });

  // Mock the registry's services to use our created services
  (bridgeRegistry as any).pythonEngineService = pythonEngineService;
  (bridgeRegistry as any).tradingBridgeService = tradingService;
  (bridgeRegistry as any).backtestBridgeService = backtestService;
  (bridgeRegistry as any).chatBridgeService = chatService;
  (bridgeRegistry as any).isInitialized = true;

  // Setup event forwarding if enabled
  if (config.enableRealTimeEvents) {
    pythonEngineMock.on('order_submitted', (data) => {
      tradingService.emit('order_submitted', data);
    });

    pythonEngineMock.on('backtest_progress', (data) => {
      backtestService.emit('backtest_progress_updated', data);
    });
  }

  // Cleanup function
  const cleanup = async (): Promise<void> => {
    await mockServer.stop();
    await pythonEngineService.stop();
    await tradingService.stop();
    await backtestService.stop();
    await chatService.stop();
    pythonEngineMock.reset();
    pythonEngineMock.removeAllListeners();
  };

  return {
    pythonEngineMock,
    tradingService,
    backtestService,
    chatService,
    bridgeRegistry,
    logger,
    cleanup,
  };
}

/**
 * Wait for event with timeout
 */
export function waitForEvent(
  emitter: EventEmitter,
  eventName: string,
  timeout: number = 5000
): Promise<any> {
  return new Promise((resolve, reject) => {
    const timeoutId = setTimeout(() => {
      emitter.off(eventName, listener);
      reject(new Error(`Timeout waiting for event: ${eventName}`));
    }, timeout);

    const listener = (data: any) => {
      clearTimeout(timeoutId);
      emitter.off(eventName, listener);
      resolve(data);
    };

    emitter.on(eventName, listener);
  });
}

/**
 * Wait for multiple events
 */
export function waitForEvents(
  events: Array<{ emitter: EventEmitter; eventName: string }>,
  timeout: number = 5000
): Promise<any[]> {
  return Promise.all(
    events.map(({ emitter, eventName }) => waitForEvent(emitter, eventName, timeout))
  );
}

/**
 * Simulate time progression for testing time-based operations
 */
export class TimeSimulator {
  private currentTime: Date;
  private intervals: Map<NodeJS.Timeout, { callback: () => void; delay: number }> = new Map();
  private timeouts: Map<NodeJS.Timeout, { callback: () => void; delay: number }> = new Map();

  constructor(startTime: Date = new Date()) {
    this.currentTime = new Date(startTime);
  }

  getCurrentTime(): Date {
    return new Date(this.currentTime);
  }

  advance(milliseconds: number): void {
    this.currentTime = new Date(this.currentTime.getTime() + milliseconds);
    
    // Process timeouts
    for (const [id, { callback, delay }] of this.timeouts.entries()) {
      if (this.currentTime.getTime() >= delay) {
        callback();
        this.timeouts.delete(id);
      }
    }

    // Process intervals (simplified - in real implementation would track next execution)
    for (const [id, { callback }] of this.intervals.entries()) {
      callback();
    }
  }

  setTimeout(callback: () => void, delay: number): NodeJS.Timeout {
    const id = setTimeout(() => {}, 0) as NodeJS.Timeout;
    this.timeouts.set(id, { callback, delay: this.currentTime.getTime() + delay });
    return id;
  }

  setInterval(callback: () => void, delay: number): NodeJS.Timeout {
    const id = setInterval(() => {}, 0) as NodeJS.Timeout;
    this.intervals.set(id, { callback, delay });
    return id;
  }

  clearTimeout(id: NodeJS.Timeout): void {
    this.timeouts.delete(id);
  }

  clearInterval(id: NodeJS.Timeout): void {
    this.intervals.delete(id);
  }

  reset(): void {
    this.intervals.clear();
    this.timeouts.clear();
  }
}

/**
 * Test data builder for creating test scenarios
 */
export class TestDataBuilder {
  static createOrderScenario(type: 'success' | 'failure' | 'validation_error') {
    const scenarios = {
      success: {
        request: {
          symbol: 'EURUSD',
          volume: 0.01,
          order_type: 'buy',
          price: 1.1000,
          stop_loss: 1.0950,
          take_profit: 1.1050,
        },
        expectedResult: {
          success: true,
          order_id: expect.any(Number),
        },
      },
      failure: {
        request: {
          symbol: 'EURUSD',
          volume: 100, // Excessive volume
          order_type: 'buy',
          price: 1.1000,
        },
        expectedError: {
          code: 'ORDER_REJECTED',
          message: expect.stringContaining('Insufficient'),
        },
      },
      validation_error: {
        request: {
          symbol: 'EURUSD',
          volume: -0.01, // Invalid negative volume
          order_type: 'buy',
          price: 1.1000,
        },
        expectedError: {
          code: 'INVALID_ORDER',
          message: expect.stringContaining('validation'),
        },
      },
    };

    const scenario = scenarios[type];
    if (!scenario) {
      throw new Error(`Unknown order scenario type: ${type}. Available types: ${Object.keys(scenarios).join(', ')}`);
    }
    
    return scenario;
  }

  static createBacktestScenario(type: 'fast' | 'slow' | 'error') {
    const baseConfig = {
      name: 'Test Strategy',
      symbols: ['EURUSD'],
      start_date: new Date('2024-01-01'),
      end_date: new Date('2024-06-30'),
      initial_balance: 10000,
      strategy: {
        name: 'sma_crossover',
        parameters: { fast_period: 10, slow_period: 20 },
      },
      risk_management: {
        max_risk_per_trade: 0.02,
        max_concurrent_trades: 3,
      },
    };

    const scenarios = {
      fast: {
        config: baseConfig,
        expectedCompletion: 'immediate' as const,
      },
      slow: {
        config: {
          ...baseConfig,
          symbols: ['EURUSD', 'GBPUSD', 'USDJPY'], // Multi-symbol = slower
        },
        expectedCompletion: 'progressive' as const,
      },
      error: {
        config: {
          ...baseConfig,
          initial_balance: 500, // Below minimum
        },
        expectedError: {
          code: 'INVALID_BACKTEST_CONFIG',
          message: expect.stringContaining('validation'),
        },
      },
    };

    const scenario = scenarios[type];
    if (!scenario) {
      throw new Error(`Unknown backtest scenario type: ${type}. Available types: ${Object.keys(scenarios).join(', ')}`);
    }
    
    return scenario;
  }

  static createChatScenario(type: 'analysis' | 'recommendation' | 'error') {
    const scenarios = {
      analysis: {
        query: 'What is the current trend for EURUSD?',
        expectedResponse: {
          type: 'analysis',
          confidence: expect.any(Number),
          message: expect.stringContaining('trend'),
        },
      },
      recommendation: {
        query: 'Should I buy GBPUSD now?',
        expectedResponse: {
          type: 'recommendation',
          confidence: expect.any(Number),
          message: expect.stringContaining('recommend'),
        },
      },
      error: {
        query: '', // Empty query
        expectedError: {
          code: 'INVALID_MESSAGE',
          message: 'Message cannot be empty',
        },
      },
    };

    const scenario = scenarios[type];
    if (!scenario) {
      throw new Error(`Unknown chat scenario type: ${type}. Available types: ${Object.keys(scenarios).join(', ')}`);
    }
    
    return scenario;
  }
}

/**
 * Performance test utilities
 */
export class PerformanceTestUtils {
  static async measureExecutionTime<T>(
    operation: () => Promise<T>
  ): Promise<{ result: T; executionTime: number }> {
    const startTime = process.hrtime.bigint(); // More precise timing
    try {
      const result = await operation();
      const endTime = process.hrtime.bigint();
      const executionTime = Number(endTime - startTime) / 1_000_000; // Convert to milliseconds
      
      return { result, executionTime };
    } catch (error) {
      const endTime = process.hrtime.bigint();
      const executionTime = Number(endTime - startTime) / 1_000_000;
      throw new Error(`Operation failed after ${executionTime}ms: ${error}`);
    }
  }

  static async measureThroughput<T>(
    operation: () => Promise<T>,
    iterations: number,
    concurrency: number = 1
  ): Promise<{ results: T[]; totalTime: number; throughput: number; errors: Error[] }> {
    if (iterations <= 0) {
      throw new Error('Iterations must be greater than 0');
    }
    if (concurrency <= 0) {
      throw new Error('Concurrency must be greater than 0');
    }

    const startTime = process.hrtime.bigint();
    const results: T[] = [];
    const errors: Error[] = [];

    for (let i = 0; i < iterations; i += concurrency) {
      const batch: Promise<T | null>[] = [];
      const batchSize = Math.min(concurrency, iterations - i);
      
      for (let j = 0; j < batchSize; j++) {
        batch.push(
          operation().catch(error => {
            errors.push(error);
            return null; // Return null for failed operations
          })
        );
      }
      
      const batchResults = await Promise.all(batch);
      results.push(...batchResults.filter((result): result is Awaited<T> => result !== null));
    }

    const endTime = process.hrtime.bigint();
    const totalTime = Number(endTime - startTime) / 1_000_000; // Convert to milliseconds
    const throughput = (results.length / totalTime) * 1000; // operations per second

    return { results, totalTime, throughput, errors };
  }
}

// Export commonly used test utilities
export { PythonEngineMock } from './python-engine-mock';
// MockHttpServer is already exported in its class declaration above
// Only export TEST_FIXTURES if it's defined somewhere accessible