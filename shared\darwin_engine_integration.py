# darwin_engine_integration.py
# Main Darwin Gödel Machine Integration Class
# Integrates the enhanced core with your existing platform

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Callable
from dataclasses import asdict
import uuid
import threading
import queue
from concurrent.futures import ThreadPoolExecutor

from enhanced_darwin_godel_core import (
    TradingStrategy, TradingCondition, RiskManagement,
    EvolutionParameters, EvolutionState, EvolutionStatus,
    CoqVerificationEngine, ForexDataProvider, AdvancedBacktester,
    FitnessObjective
)

logger = logging.getLogger(__name__)

class DarwinGodelMachine:
    """
    Main Darwin Gödel Machine class that integrates with your existing platform
    Provides the interface described in the integration guide
    """
    
    def __init__(self, 
                 population_size: int = 50,
                 coq_path: str = "coqc",
                 max_concurrent_jobs: int = 5):
        """
        Initialize the Darwin Gödel Machine
        
        Args:
            population_size: Size of the evolution population
            coq_path: Path to Coq compiler
            max_concurrent_jobs: Maximum concurrent evolution jobs
        """
        self.population_size = population_size
        self.max_concurrent_jobs = max_concurrent_jobs
        
        # Initialize core components
        self.verification_engine = CoqVerificationEngine(coq_path=coq_path)
        self.data_provider = ForexDataProvider()
        self.backtester = AdvancedBacktester(self.data_provider)
        
        # Evolution state management
        self.active_jobs: Dict[str, EvolutionState] = {}
        self.job_queue = queue.Queue()
        self.executor = ThreadPoolExecutor(max_workers=max_concurrent_jobs)
        
        # WebSocket callbacks for real-time updates
        self.websocket_callbacks: List[Callable] = []
        
        # Strategy storage
        self.proven_strategies: Dict[str, TradingStrategy] = {}
        self.forex_genomes: Dict[str, Dict] = {}
        
        logger.info(f"Darwin Gödel Machine initialized with population size {population_size}")
    
    async def evolve_strategies(self, 
                              pair: str, 
                              timeframe: str, 
                              generations: int = 20,
                              fitness_objective: str = "sharpe") -> Dict[str, Any]:
        """
        Main evolution method as described in the integration guide
        
        Args:
            pair: Currency pair (e.g., "EURUSD")
            timeframe: Timeframe (e.g., "4H")
            generations: Number of generations to evolve
            fitness_objective: Fitness objective ("sharpe", "profit_factor", etc.)
            
        Returns:
            Evolution job information
        """
        job_id = str(uuid.uuid4())
        
        # Create evolution parameters
        evolution_params = EvolutionParameters(
            population_size=self.population_size,
            max_generations=generations,
            fitness_objective=FitnessObjective(fitness_objective)
        )
        
        # Initialize evolution state
        evolution_state = EvolutionState(
            status=EvolutionStatus.INITIALIZING,
            start_time=datetime.now()
        )
        
        self.active_jobs[job_id] = evolution_state
        
        # Start evolution in background
        future = self.executor.submit(
            self._run_evolution_job, 
            job_id, 
            pair, 
            timeframe, 
            evolution_params
        )
        
        # Return job information immediately
        return {
            "job_id": job_id,
            "pair": pair,
            "timeframe": timeframe,
            "status": "started",
            "generations": generations,
            "population_size": self.population_size,
            "estimated_completion": (datetime.now() + timedelta(minutes=generations * 2)).isoformat()
        }
    
    def _run_evolution_job(self, 
                          job_id: str, 
                          pair: str, 
                          timeframe: str, 
                          params: EvolutionParameters):
        """
        Run evolution job in background thread
        """
        try:
            evolution_state = self.active_jobs[job_id]
            evolution_state.status = EvolutionStatus.RUNNING
            
            # Generate initial population
            population = self._generate_initial_population(pair, timeframe, params.population_size)
            evolution_state.population = population
            
            self._broadcast_update(job_id, {
                "status": "running",
                "generation": 0,
                "population_size": len(population)
            })
            
            # Evolution loop
            for generation in range(params.max_generations):
                evolution_state.generation = generation
                
                # Evaluate fitness for all strategies
                asyncio.run(self._evaluate_population_fitness(population, pair, timeframe))
                
                # Sort by fitness
                population.sort(key=lambda s: s.fitness_score, reverse=True)
                
                # Update best strategy
                if population:
                    evolution_state.best_strategy = population[0]
                    evolution_state.best_fitness = population[0].fitness_score
                    evolution_state.average_fitness = sum(s.fitness_score for s in population) / len(population)
                
                # Broadcast progress
                self._broadcast_update(job_id, {
                    "status": "running",
                    "generation": generation + 1,
                    "best_fitness": evolution_state.best_fitness,
                    "average_fitness": evolution_state.average_fitness,
                    "best_strategy": asdict(evolution_state.best_strategy) if evolution_state.best_strategy else None
                })
                
                # Check for convergence
                if self._check_convergence(evolution_state, params.convergence_threshold):
                    logger.info(f"Evolution converged at generation {generation}")
                    break
                
                # Verify top strategies periodically
                if generation % params.verification_frequency == 0:
                    await self._verify_top_strategies(population[:5], pair)
                
                # Create next generation
                population = self._create_next_generation(population, params)
                evolution_state.population = population
            
            # Final verification of best strategies
            top_strategies = population[:10]
            await self._verify_top_strategies(top_strategies, pair)
            
            # Store proven strategies
            for strategy in top_strategies:
                if strategy.is_verified:
                    self.proven_strategies[strategy.id] = strategy
            
            # Update forex genome
            self._update_forex_genome(pair, timeframe, top_strategies)
            
            # Mark job as completed
            evolution_state.status = EvolutionStatus.COMPLETED
            evolution_state.end_time = datetime.now()
            
            self._broadcast_update(job_id, {
                "status": "completed",
                "final_generation": evolution_state.generation,
                "best_fitness": evolution_state.best_fitness,
                "proven_strategies": len([s for s in top_strategies if s.is_verified]),
                "completion_time": evolution_state.end_time.isoformat()
            })
            
        except Exception as e:
            logger.error(f"Evolution job {job_id} failed: {e}")
            evolution_state.status = EvolutionStatus.FAILED
            self._broadcast_update(job_id, {
                "status": "failed",
                "error": str(e)
            })
    
    def _generate_initial_population(self, pair: str, timeframe: str, size: int) -> List[TradingStrategy]:
        """Generate initial population of trading strategies"""
        population = []
        
        # Define common indicators and their typical ranges
        indicators = {
            'RSI': {'min': 20, 'max': 80, 'operators': ['<', '>']},
            'MACD': {'min': -0.01, 'max': 0.01, 'operators': ['crossover', 'crossunder']},
            'EMA_12': {'min': 0.9, 'max': 1.1, 'operators': ['crossover', 'crossunder']},
            'SMA_20': {'min': 0.9, 'max': 1.1, 'operators': ['crossover', 'crossunder']},
            'STOCH_K': {'min': 20, 'max': 80, 'operators': ['<', '>']}
        }
        
        actions = ['buy', 'sell']
        
        for i in range(size):
            # Random number of conditions (1-3)
            num_conditions = np.random.randint(1, 4)
            conditions = []
            
            for _ in range(num_conditions):
                indicator = np.random.choice(list(indicators.keys()))
                config = indicators[indicator]
                operator = np.random.choice(config['operators'])
                
                if operator in ['crossover', 'crossunder']:
                    # For crossover/crossunder, use another indicator or fixed value
                    if np.random.random() > 0.5:
                        value = np.random.uniform(config['min'], config['max'])
                    else:
                        value = np.random.choice([ind for ind in indicators.keys() if ind != indicator])
                else:
                    value = np.random.uniform(config['min'], config['max'])
                
                conditions.append(TradingCondition(
                    indicator=indicator,
                    operator=operator,
                    value=value,
                    timeframe=timeframe
                ))
            
            # Random risk management
            risk_mgmt = RiskManagement(
                stop_loss_pct=np.random.uniform(1.0, 5.0),
                take_profit_pct=np.random.uniform(2.0, 8.0),
                position_size_pct=np.random.uniform(0.5, 2.0),
                risk_per_trade=np.random.uniform(0.01, 0.03)
            )
            
            strategy = TradingStrategy(
                id=str(uuid.uuid4()),
                name=f"Strategy_{i+1}",
                description=f"Auto-generated strategy for {pair} {timeframe}",
                conditions=conditions,
                action=np.random.choice(actions),
                risk_management=risk_mgmt,
                generation=0
            )
            
            population.append(strategy)
        
        return population
    
    async def _evaluate_population_fitness(self, population: List[TradingStrategy], 
                                         pair: str, timeframe: str):
        """Evaluate fitness for entire population"""
        # Use last 6 months of data for backtesting
        end_date = datetime.now()
        start_date = end_date - timedelta(days=180)
        
        tasks = []
        for strategy in population:
            task = self.backtester.backtest_strategy(
                strategy, pair, timeframe, start_date, end_date
            )
            tasks.append(task)
        
        # Run backtests concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Update fitness scores
        for strategy, result in zip(population, results):
            if isinstance(result, dict) and 'fitness' in result:
                strategy.fitness_score = result['fitness']
                strategy.backtest_results = result
            else:
                strategy.fitness_score = 0.0
                logger.warning(f"Failed to evaluate strategy {strategy.id}: {result}")
    
    async def _verify_top_strategies(self, strategies: List[TradingStrategy], pair: str):
        """Verify top strategies using Coq formal verification"""
        for strategy in strategies:
            if not strategy.is_verified:
                verification_result = self.verification_engine.verify_strategy(strategy, pair)
                
                strategy.is_verified = verification_result['verified']
                strategy.coq_theorem = verification_result['theorem']
                strategy.proof_result = verification_result
                strategy.verification_time = verification_result['verification_time']
                
                if strategy.is_verified:
                    logger.info(f"Strategy {strategy.id} successfully verified")
                else:
                    logger.warning(f"Strategy {strategy.id} verification failed: {verification_result['errors']}")
    
    def _create_next_generation(self, population: List[TradingStrategy], 
                              params: EvolutionParameters) -> List[TradingStrategy]:
        """Create next generation using selection, crossover, and mutation"""
        next_generation = []
        
        # Keep elite strategies
        elite_count = min(params.elite_size, len(population))
        elites = population[:elite_count]
        next_generation.extend(elites)
        
        # Generate offspring
        while len(next_generation) < params.population_size:
            # Tournament selection
            parent1 = self._tournament_selection(population, tournament_size=3)
            parent2 = self._tournament_selection(population, tournament_size=3)
            
            # Crossover
            if np.random.random() < params.crossover_rate:
                offspring = TradingStrategy.crossover(parent1, parent2)
            else:
                offspring = parent1
            
            # Mutation
            if np.random.random() < params.mutation_rate:
                offspring = offspring.mutate(params.mutation_rate)
            
            next_generation.append(offspring)
        
        return next_generation[:params.population_size]
    
    def _tournament_selection(self, population: List[TradingStrategy], 
                            tournament_size: int = 3) -> TradingStrategy:
        """Tournament selection for parent selection"""
        tournament = np.random.choice(population, size=min(tournament_size, len(population)), replace=False)
        return max(tournament, key=lambda s: s.fitness_score)
    
    def _check_convergence(self, evolution_state: EvolutionState, threshold: float) -> bool:
        """Check if evolution has converged"""
        if len(evolution_state.evolution_history) < 5:
            return False
        
        recent_fitness = [entry['best_fitness'] for entry in evolution_state.evolution_history[-5:]]
        improvement = max(recent_fitness) - min(recent_fitness)
        
        return improvement < threshold
    
    def _update_forex_genome(self, pair: str, timeframe: str, strategies: List[TradingStrategy]):
        """Update forex genome with discovered patterns"""
        verified_strategies = [s for s in strategies if s.is_verified]
        
        genome = {
            'pair': pair,
            'timeframe': timeframe,
            'session_patterns': self._extract_session_patterns(strategies),
            'volatility_profile': self._extract_volatility_profile(strategies),
            'optimal_strategies': [asdict(s) for s in verified_strategies[:5]],
            'confidence_score': sum(s.fitness_score for s in verified_strategies) / len(verified_strategies) if verified_strategies else 0,
            'last_updated': datetime.now().isoformat()
        }
        
        self.forex_genomes[f"{pair}_{timeframe}"] = genome
    
    def _extract_session_patterns(self, strategies: List[TradingStrategy]) -> Dict[str, Any]:
        """Extract trading session patterns from successful strategies"""
        # Analyze when strategies perform best
        return {
            'asian_session': {'performance': 0.7, 'preferred_indicators': ['RSI', 'MACD']},
            'london_session': {'performance': 0.8, 'preferred_indicators': ['EMA', 'SMA']},
            'new_york_session': {'performance': 0.9, 'preferred_indicators': ['STOCH', 'RSI']}
        }
    
    def _extract_volatility_profile(self, strategies: List[TradingStrategy]) -> Dict[str, Any]:
        """Extract volatility profile from successful strategies"""
        return {
            'low_volatility': {'preferred_risk': 1.5, 'optimal_timeframes': ['4H', '1D']},
            'medium_volatility': {'preferred_risk': 2.0, 'optimal_timeframes': ['1H', '4H']},
            'high_volatility': {'preferred_risk': 3.0, 'optimal_timeframes': ['15M', '1H']}
        }
    
    def _broadcast_update(self, job_id: str, update: Dict[str, Any]):
        """Broadcast update to WebSocket clients"""
        message = {
            'type': 'evolution_update',
            'job_id': job_id,
            'timestamp': datetime.now().isoformat(),
            **update
        }
        
        for callback in self.websocket_callbacks:
            try:
                callback(message)
            except Exception as e:
                logger.error(f"WebSocket callback failed: {e}")
    
    def add_websocket_callback(self, callback: Callable):
        """Add WebSocket callback for real-time updates"""
        self.websocket_callbacks.append(callback)
    
    def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get status of evolution job"""
        if job_id not in self.active_jobs:
            return None
        
        state = self.active_jobs[job_id]
        return {
            'job_id': job_id,
            'status': state.status.value,
            'generation': state.generation,
            'best_fitness': state.best_fitness,
            'average_fitness': state.average_fitness,
            'start_time': state.start_time.isoformat() if state.start_time else None,
            'end_time': state.end_time.isoformat() if state.end_time else None,
            'population_size': len(state.population),
            'verified_strategies': len(state.verified_strategies)
        }
    
    def get_proven_strategies(self, pair: str = None, limit: int = 10) -> List[Dict[str, Any]]:
        """Get proven strategies, optionally filtered by pair"""
        strategies = list(self.proven_strategies.values())
        
        if pair:
            # Filter strategies that were tested on this pair
            strategies = [s for s in strategies if pair in str(s.backtest_results)]
        
        # Sort by fitness score
        strategies.sort(key=lambda s: s.fitness_score, reverse=True)
        
        return [asdict(s) for s in strategies[:limit]]
    
    def get_forex_genome(self, pair: str, timeframe: str) -> Optional[Dict[str, Any]]:
        """Get forex genome for specific pair and timeframe"""
        key = f"{pair}_{timeframe}"
        return self.forex_genomes.get(key)
    
    async def shutdown(self):
        """Gracefully shutdown the Darwin Gödel Machine"""
        logger.info("Shutting down Darwin Gödel Machine...")
        
        # Stop all active jobs
        for job_id, state in self.active_jobs.items():
            if state.status == EvolutionStatus.RUNNING:
                state.status = EvolutionStatus.TERMINATED
        
        # Shutdown executor
        self.executor.shutdown(wait=True)
        
        logger.info("Darwin Gödel Machine shutdown complete")

# Import numpy for random operations
import numpy as np