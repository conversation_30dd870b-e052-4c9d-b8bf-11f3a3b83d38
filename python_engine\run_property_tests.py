#!/usr/bin/env python
"""
Property-Based Test Runner

Runs comprehensive property-based tests using Hypothesis to verify
system invariants across a wide range of inputs.
"""

import subprocess
import sys
import time
import os

def run_property_tests():
    """Run all property-based tests with detailed reporting"""
    print("🔍 Starting Property-Based Test Suite")
    print("=" * 60)
    
    # Property test categories
    test_categories = [
        {
            'name': 'Portfolio Properties',
            'path': 'tests/property_based/test_portfolio_properties.py',
            'description': 'Tests portfolio allocation and risk management invariants'
        },
        {
            'name': 'Strategy Properties',
            'path': 'tests/property_based/test_strategy_properties.py',
            'description': 'Tests trading strategy mathematical properties'
        }
    ]
    
    results = {}
    
    for category in test_categories:
        print(f"\n🧪 Running {category['name']}")
        print(f"   {category['description']}")
        print("-" * 40)
        
        start_time = time.time()
        
        try:
            # Run tests with Hypothesis settings
            result = subprocess.run([
                sys.executable, '-m', 'pytest',
                category['path'],
                '-v',
                '--tb=short',
                '--hypothesis-show-statistics',
                '--disable-warnings'
            ], capture_output=True, text=True, cwd=os.getcwd())
            
            end_time = time.time()
            duration = end_time - start_time
            
            results[category['name']] = {
                'success': result.returncode == 0,
                'duration': duration,
                'output': result.stdout,
                'errors': result.stderr
            }
            
            if result.returncode == 0:
                print(f"✅ {category['name']} - PASSED ({duration:.2f}s)")
                
                # Extract Hypothesis statistics
                output_lines = result.stdout.split('\n')
                for line in output_lines:
                    if 'examples' in line.lower() or 'hypothesis' in line.lower():
                        print(f"   📊 {line.strip()}")
            else:
                print(f"❌ {category['name']} - FAILED ({duration:.2f}s)")
                print(f"Error: {result.stderr}")
                
        except Exception as e:
            print(f"❌ {category['name']} - ERROR: {e}")
            results[category['name']] = {
                'success': False,
                'duration': 0,
                'output': '',
                'errors': str(e)
            }
    
    # Summary report
    print("\n" + "=" * 60)
    print("🔬 PROPERTY-BASED TEST SUMMARY")
    print("=" * 60)
    
    total_tests = len(test_categories)
    passed_tests = sum(1 for r in results.values() if r['success'])
    total_time = sum(r['duration'] for r in results.values())
    
    print(f"Total Categories: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Total Time: {total_time:.2f}s")
    print(f"Success Rate: {passed_tests/total_tests*100:.1f}%")
    
    # Detailed results
    print(f"\n📋 DETAILED RESULTS:")
    for name, result in results.items():
        status = "✅ PASS" if result['success'] else "❌ FAIL"
        print(f"  {name:<25} {status:<8} ({result['duration']:.2f}s)")
    
    print(f"\n💡 PROPERTY-BASED TESTING BENEFITS:")
    print(f"  • Automatically generates thousands of test cases")
    print(f"  • Finds edge cases that manual tests might miss")
    print(f"  • Verifies mathematical invariants across input ranges")
    print(f"  • Provides confidence in system robustness")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = run_property_tests()
    sys.exit(0 if success else 1)