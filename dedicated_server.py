#!/usr/bin/env python3
"""
Dedicated Local Server for AI Enhanced Trading Platform
Combines frontend serving, backend API, and MT5 integration in one process
"""

import os
import sys
import asyncio
import logging
from pathlib import Path
from fastapi import FastAPI, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
import uvicorn

# Add paths for imports
project_root = Path(__file__).parent
sys.path.append(str(project_root))
sys.path.append(str(project_root / "backend"))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("dedicated_server")

# Create FastAPI app
app = FastAPI(
    title="AI Enhanced Trading Platform - Dedicated Server",
    description="Complete trading platform with Ollama and MT5 integration",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Import components with error handling
try:
    from backend.app.chatbot.ollama_client import OllamaClient
    from backend.app.chatbot.enhanced_strategy_chatbot import EnhancedStrategyChatbot
    from backend.app.mt5_bridge.mt5_strategy_api import router as mt5_router
    logger.info("✅ Successfully imported all components")
    components_available = True
except ImportError as e:
    logger.warning(f"⚠️ Could not import some components: {e}")
    OllamaClient = None
    EnhancedStrategyChatbot = None
    mt5_router = None
    components_available = False

# Include routers
if mt5_router and components_available:
    app.include_router(mt5_router, prefix="/api/mt5", tags=["MT5 Strategy"])
    logger.info("✅ MT5 strategy router included")

# Global instances
ollama_client = None
chatbot = None

@app.on_event("startup")
async def startup():
    """Initialize components on startup"""
    global ollama_client, chatbot
    
    if components_available:
        try:
            # Define Ollama URL
            ollama_base_url = "http://localhost:11435"
            
            # Initialize chatbot with the base URL
            chatbot = EnhancedStrategyChatbot(ollama_base_url=ollama_base_url)
            
            # Test connection
            health = await chatbot.get_health_status()
            logger.info(f"✅ Ollama health: {health['message']}")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize components: {e}")
            chatbot = None
    else:
        logger.warning("⚠️ Components not available - running in limited mode")

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "ok",
        "components_available": components_available,
        "ollama_available": chatbot is not None,
        "server": "dedicated_local_server"
    }

# Ollama endpoints
@app.get("/api/ollama/status")
async def ollama_status():
    """Get Ollama status"""
    if not chatbot:
        return {
            "available": False,
            "models": [],
            "message": "Ollama not available",
            "error": "Components not initialized"
        }
    
    try:
        status = await chatbot.get_health_status()
        return {
            "available": status.get("available", False),
            "models": status.get("models", []),
            "message": status.get("message", "Unknown status"),
            "error": None
        }
    except Exception as e:
        return {
            "available": False,
            "models": [],
            "message": "Ollama connection failed",
            "error": str(e)
        }

@app.post("/api/ollama/chat")
async def ollama_chat(request: dict):
    """Chat with Ollama"""
    if not chatbot:
        raise HTTPException(status_code=503, detail="Ollama not available")
    
    try:
        message = request.get("message", "")
        model = request.get("model", "llama3.2:1b")
        conversation_id = request.get("conversation_id", "default")
        
        response = await chatbot.process_message(
            user_message=message,
            model=model,
            conversation_id=conversation_id
        )
        
        return {
            "response": response.get("response", ""),
            "template_data": response.get("template_data"),
            "conversation_id": conversation_id,
            "model": model
        }
        
    except Exception as e:
        logger.error(f"Chat error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Strategy templates endpoint (fallback)
@app.get("/api/strategy-templates")
async def get_strategy_templates():
    """Get available strategy templates"""
    return {
        "templates": [
            {
                "name": "Mean Reversion RSI",
                "description": "RSI-based mean reversion strategy",
                "category": "Mean Reversion"
            },
            {
                "name": "Momentum MACD",
                "description": "MACD-based momentum strategy", 
                "category": "Momentum"
            },
            {
                "name": "Machine Learning",
                "description": "ML-based trading strategy",
                "category": "AI/ML"
            }
        ]
    }

# Serve frontend static files
frontend_dist = project_root / "frontend" / "dist"
if frontend_dist.exists():
    app.mount("/assets", StaticFiles(directory=frontend_dist / "assets"), name="assets")
    
    @app.get("/", response_class=HTMLResponse)
    async def serve_frontend():
        """Serve the frontend application"""
        index_file = frontend_dist / "index.html"
        if index_file.exists():
            return index_file.read_text()
        else:
            return """
            <html>
                <head><title>AI Trading Platform</title></head>
                <body>
                    <h1>AI Enhanced Trading Platform</h1>
                    <p>Frontend not built yet. Please run: <code>cd frontend && npm run build</code></p>
                    <p>Then restart this server.</p>
                    <h2>Available API Endpoints:</h2>
                    <ul>
                        <li><a href="/docs">API Documentation</a></li>
                        <li><a href="/health">Health Check</a></li>
                        <li><a href="/api/ollama/status">Ollama Status</a></li>
                    </ul>
                </body>
            </html>
            """
else:
    @app.get("/", response_class=HTMLResponse)
    async def serve_fallback():
        """Serve fallback page when frontend not built"""
        return """
        <html>
            <head><title>AI Trading Platform</title></head>
            <body>
                <h1>🚀 AI Enhanced Trading Platform</h1>
                <p><strong>Dedicated Local Server Running!</strong></p>
                <p>Frontend not built yet. To build and serve the frontend:</p>
                <ol>
                    <li><code>cd frontend</code></li>
                    <li><code>npm install</code></li>
                    <li><code>npm run build</code></li>
                    <li>Restart this server</li>
                </ol>
                <h2>🔗 Available API Endpoints:</h2>
                <ul>
                    <li><a href="/docs">📚 API Documentation</a></li>
                    <li><a href="/health">❤️ Health Check</a></li>
                    <li><a href="/api/ollama/status">🤖 Ollama Status</a></li>
                    <li><a href="/api/mt5/status">📈 MT5 Status</a></li>
                </ul>
                <h2>🔧 System Status:</h2>
                <p><strong>Components Available:</strong> {}</p>
                <p><strong>Ollama Connected:</strong> {}</p>
                <p><strong>Server Port:</strong> 9000</p>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 40px; }}
                    code {{ background: #f0f0f0; padding: 2px 4px; border-radius: 3px; }}
                    h1 {{ color: #2563eb; }}
                    h2 {{ color: #059669; }}
                </style>
            </body>
        </html>
        """.format(components_available, chatbot is not None)

def find_available_port(start_port=9000, max_attempts=10):
    """Find an available port starting from start_port"""
    import socket
    
    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('', port))
                return port
        except OSError:
            continue
    
    return start_port  # Fallback

def main():
    """Main server function"""
    logger.info("🚀 Starting AI Enhanced Trading Platform - Dedicated Server")
    
    # Find available port
    port = find_available_port(9000)
    logger.info(f"📡 Server will run on port {port}")
    
    # Server configuration
    config = uvicorn.Config(
        app=app,
        host="0.0.0.0",
        port=port,
        log_level="info",
        reload=False  # Disable reload for stability
    )
    
    server = uvicorn.Server(config)
    
    try:
        logger.info(f"🌟 Access your trading platform at: http://localhost:{port}")
        logger.info(f"📚 API Documentation: http://localhost:{port}/docs")
        logger.info(f"❤️ Health Check: http://localhost:{port}/health")
        
        server.run()
        
    except KeyboardInterrupt:
        logger.info("👋 Server stopped by user")
    except Exception as e:
        logger.error(f"💥 Server error: {e}")

if __name__ == "__main__":
    main()
