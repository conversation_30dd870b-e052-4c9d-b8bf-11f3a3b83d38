#!/usr/bin/env python3
"""
Doctest Integration Tests for Trading Strategies

This module runs all doctests from strategy modules and provides
comprehensive validation of inline documentation examples.
"""

import doctest
import sys
import os
import pytest
from typing import List, Tuple

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Import strategy modules directly
import importlib.util

def load_module(name, path):
    spec = importlib.util.spec_from_file_location(name, path)
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module

# Load modules
base_path = os.path.join(os.path.dirname(__file__), '..', 'src', 'platform', 'strategies')
rsi = load_module('rsi', os.path.join(base_path, 'rsi.py'))
macd = load_module('macd', os.path.join(base_path, 'macd.py'))
moving_averages = load_module('moving_averages', os.path.join(base_path, 'moving_averages.py'))


class TestDoctestIntegration:
    """Test class for running and validating doctests"""
    
    def test_rsi_doctests(self):
        """Run all doctests in RSI module"""
        print("\n🧪 Running RSI doctests...")
        result = doctest.testmod(rsi, verbose=False)
        
        assert result.failed == 0, (
            f"RSI doctests failed: {result.failed} out of {result.attempted} tests failed"
        )
        
        print(f"✅ RSI doctests passed: {result.attempted} tests")
    
    def test_macd_doctests(self):
        """Run all doctests in MACD module"""
        print("\n🧪 Running MACD doctests...")
        result = doctest.testmod(macd, verbose=False)
        
        assert result.failed == 0, (
            f"MACD doctests failed: {result.failed} out of {result.attempted} tests failed"
        )
        
        print(f"✅ MACD doctests passed: {result.attempted} tests")
    
    def test_moving_averages_doctests(self):
        """Run all doctests in Moving Averages module"""
        print("\n🧪 Running Moving Averages doctests...")
        result = doctest.testmod(moving_averages, verbose=False)
        
        assert result.failed == 0, (
            f"Moving Averages doctests failed: {result.failed} out of {result.attempted} tests failed"
        )
        
        print(f"✅ Moving Averages doctests passed: {result.attempted} tests")
    
    def test_all_doctests_comprehensive(self):
        """Run comprehensive doctest validation across all modules"""
        modules = [rsi, macd, moving_averages]
        total_attempted = 0
        total_failed = 0
        
        for module in modules:
            result = doctest.testmod(module, verbose=False)
            total_attempted += result.attempted
            total_failed += result.failed
        
        print(f"\n📊 Comprehensive Doctest Summary:")
        print(f"   Total Tests: {total_attempted}")
        print(f"   Failed Tests: {total_failed}")
        print(f"   Success Rate: {((total_attempted - total_failed) / total_attempted * 100):.1f}%")
        
        assert total_failed == 0, (
            f"Comprehensive doctest validation failed: {total_failed} out of {total_attempted} tests failed"
        )


class TestUserErrorHandling:
    """Test comprehensive user error handling scenarios"""
    
    def test_rsi_user_errors(self):
        """Test RSI function with various user error scenarios"""
        
        # Test insufficient data
        with pytest.raises(ValueError, match="Not enough data"):
            rsi.rsi([1, 2, 3], period=14)
        
        # Test empty list
        with pytest.raises(ValueError, match="Not enough data"):
            rsi.rsi([], period=14)
        
        # Test invalid period
        with pytest.raises(ValueError, match="Period must be positive"):
            rsi.rsi([1]*20, period=0)
        
        # Test negative period
        with pytest.raises(ValueError, match="Period must be positive"):
            rsi.rsi([1]*20, period=-5)
        
        # Test non-numeric prices
        with pytest.raises(TypeError, match="All prices must be numeric"):
            rsi.rsi([1, 2, "invalid", 4, 5]*5, period=14)
        
        # Test None in prices
        with pytest.raises(TypeError, match="All prices must be numeric"):
            rsi.rsi([1, 2, None, 4, 5]*5, period=14)
        
        # Test RSI signal errors
        with pytest.raises(ValueError, match="Overbought threshold.*must be greater than oversold"):
            rsi.rsi_signal([1]*20, overbought=30.0, oversold=70.0)
        
        print("✅ RSI user error handling validated")
    
    def test_macd_user_errors(self):
        """Test MACD function with various user error scenarios"""
        
        # Test insufficient data
        with pytest.raises(ValueError, match="Not enough data"):
            macd.macd([1, 2, 3, 4, 5])
        
        # Test invalid periods
        with pytest.raises(ValueError, match="Fast period must be positive"):
            macd.macd([1]*50, fast_period=0)
        
        # Test fast >= slow period
        with pytest.raises(ValueError, match="Fast period.*must be less than slow period"):
            macd.macd([1]*50, fast_period=26, slow_period=12)
        
        # Test non-numeric prices
        with pytest.raises(TypeError, match="All prices must be numeric"):
            macd.macd([1, 2, "invalid"] + [4]*50)
        
        print("✅ MACD user error handling validated")
    
    def test_moving_averages_user_errors(self):
        """Test Moving Averages functions with various user error scenarios"""
        
        # Test SMA errors
        with pytest.raises(ValueError, match="Not enough data"):
            moving_averages.sma([1, 2], period=5)
        
        with pytest.raises(ValueError, match="Period must be positive"):
            moving_averages.sma([1, 2, 3, 4, 5], period=0)
        
        with pytest.raises(TypeError, match="All prices must be numeric"):
            moving_averages.sma([1, 2, "invalid", 4, 5], period=3)
        
        # Test EMA errors
        with pytest.raises(ValueError, match="Smoothing factor must be positive"):
            moving_averages.ema([1, 2, 3, 4, 5], period=3, smoothing=0)
        
        # Test MA signal errors
        with pytest.raises(ValueError, match="Short period.*must be less than long period"):
            moving_averages.ma_signal([1]*30, short_period=20, long_period=10)
        
        with pytest.raises(ValueError, match="MA type must be"):
            moving_averages.ma_signal([1]*30, short_period=5, long_period=10, ma_type='invalid')
        
        # Test crossover errors
        with pytest.raises(ValueError, match="Short MA and Long MA must have the same length"):
            moving_averages.ma_crossover([1, 2, 3], [4, 5])
        
        print("✅ Moving Averages user error handling validated")
    
    def test_edge_cases_comprehensive(self):
        """Test comprehensive edge cases across all strategies"""
        
        # Test with minimum required data
        min_data_rsi = [1.0] * 15
        result = rsi.rsi(min_data_rsi, period=14)
        assert isinstance(result, float)
        
        # Test with exactly required data for MACD
        min_data_macd = [1.0] * 35
        macd_line, signal_line, histogram = macd.macd(min_data_macd)
        assert all(isinstance(x, float) for x in [macd_line, signal_line, histogram])
        
        # Test with large datasets
        large_data = list(range(1, 1001))  # 1000 data points
        sma_result = moving_averages.sma(large_data, period=100)
        ema_result = moving_averages.ema(large_data, period=100)
        assert isinstance(sma_result, float)
        assert isinstance(ema_result, float)
        
        # Test with extreme values
        extreme_data = [1e-10, 1e10, 1e-5, 1e5] * 10
        sma_extreme = moving_averages.sma(extreme_data, period=10)
        assert isinstance(sma_extreme, float)
        
        print("✅ Edge cases comprehensive validation passed")


class TestDoctestTemplates:
    """Test doctest template patterns and best practices"""
    
    def test_doctest_coverage(self):
        """Ensure all public functions have comprehensive doctests"""
        
        # Check RSI module
        rsi_functions = ['rsi', 'rsi_signal', 'rsi_divergence']
        for func_name in rsi_functions:
            func = getattr(rsi, func_name)
            assert func.__doc__ is not None, f"Function {func_name} missing docstring"
            assert ">>>" in func.__doc__, f"Function {func_name} missing doctest examples"
            assert "Examples:" in func.__doc__, f"Function {func_name} missing Examples section"
        
        # Check MACD module
        macd_functions = ['macd', 'macd_signal', 'macd_crossover_signal']
        for func_name in macd_functions:
            func = getattr(macd, func_name)
            assert func.__doc__ is not None, f"Function {func_name} missing docstring"
            assert ">>>" in func.__doc__, f"Function {func_name} missing doctest examples"
        
        # Check Moving Averages module
        ma_functions = ['sma', 'ema', 'ma_crossover', 'ma_signal']
        for func_name in ma_functions:
            func = getattr(moving_averages, func_name)
            assert func.__doc__ is not None, f"Function {func_name} missing docstring"
            assert ">>>" in func.__doc__, f"Function {func_name} missing doctest examples"
        
        print("✅ Doctest coverage validation passed")
    
    def test_error_case_documentation(self):
        """Ensure all functions document their error cases in doctests"""
        
        modules_to_check = [
            (rsi, ['rsi', 'rsi_signal']),
            (macd, ['macd', 'macd_signal']),
            (moving_averages, ['sma', 'ema', 'ma_signal'])
        ]
        
        for module, function_names in modules_to_check:
            for func_name in function_names:
                func = getattr(module, func_name)
                docstring = func.__doc__
                
                # Check for error case documentation
                assert "Error cases" in docstring, f"Function {func_name} missing error case documentation"
                assert "Traceback" in docstring, f"Function {func_name} missing error traceback examples"
                assert "ValueError" in docstring or "TypeError" in docstring, f"Function {func_name} missing exception examples"
        
        print("✅ Error case documentation validation passed")
    
    def test_doctest_best_practices(self):
        """Validate doctest best practices are followed"""
        
        # Check RSI function as example
        rsi_func = rsi.rsi
        docstring = rsi_func.__doc__
        
        # Should have comprehensive sections
        required_sections = [
            "Args:",
            "Returns:",
            "Raises:",
            "Examples:"
        ]
        
        for section in required_sections:
            assert section in docstring, f"RSI function missing {section} section"
        
        # Should have multiple example categories
        example_categories = [
            "Basic",
            "Error cases",
            "Edge case"
        ]
        
        for category in example_categories:
            assert category in docstring, f"RSI function missing {category} examples"
        
        print("✅ Doctest best practices validation passed")


def run_all_doctests():
    """Utility function to run all doctests manually"""
    print("🚀 RUNNING ALL STRATEGY DOCTESTS")
    print("=" * 50)
    
    modules = [
        ('RSI', rsi),
        ('MACD', macd), 
        ('Moving Averages', moving_averages)
    ]
    
    total_attempted = 0
    total_failed = 0
    
    for name, module in modules:
        print(f"\n📊 {name} Module:")
        print("-" * 20)
        
        result = doctest.testmod(module, verbose=True)
        total_attempted += result.attempted
        total_failed += result.failed
        
        if result.failed == 0:
            print(f"✅ {name}: All {result.attempted} tests passed!")
        else:
            print(f"❌ {name}: {result.failed} out of {result.attempted} tests failed!")
    
    print(f"\n🎯 OVERALL DOCTEST SUMMARY")
    print("=" * 30)
    print(f"📊 Total Tests: {total_attempted}")
    print(f"✅ Passed: {total_attempted - total_failed}")
    print(f"❌ Failed: {total_failed}")
    print(f"📈 Success Rate: {((total_attempted - total_failed) / total_attempted * 100):.1f}%")
    
    return total_failed == 0


if __name__ == "__main__":
    # Run all doctests when script is executed directly
    success = run_all_doctests()
    
    if success:
        print("\n🎉 ALL DOCTESTS PASSED!")
        sys.exit(0)
    else:
        print("\n❌ SOME DOCTESTS FAILED!")
        sys.exit(1)