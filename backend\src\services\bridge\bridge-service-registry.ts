import { EventEmitter } from 'events';
import { Logger } from '@/shared/types';
import { PythonEngineService, PythonEngineConfig } from './python-engine.service';
import { TradingBridgeService } from './trading-bridge.service';
import { BacktestBridgeService } from './backtest-bridge.service';
import { ChatBridgeService } from './chat-bridge.service';
import { DarwinGodelBridgeService } from './darwin-godel-bridge.service';

export interface BridgeServiceRegistryConfig {
  pythonEngine: PythonEngineConfig;
  healthCheckInterval?: number;
  cleanupInterval?: number;
}

export interface BridgeServiceRegistryDependencies {
  logger: Logger;
  config: BridgeServiceRegistryConfig;
}

/**
 * Central registry and coordinator for all bridge services
 * Manages service lifecycle, health monitoring, and cleanup tasks
 */
export class BridgeServiceRegistry extends EventEmitter {
  private pythonEngineService!: PythonEngineService;
  private tradingBridgeService!: TradingBridgeService;
  private backtestBridgeService!: BacktestBridgeService;
  private chatBridgeService!: ChatBridgeService;
  private darwinGodelBridgeService!: DarwinGodelBridgeService;

  private healthCheckInterval: NodeJS.Timeout | null = null;
  private cleanupInterval: NodeJS.Timeout | null = null;
  private isInitialized: boolean = false;

  constructor(private dependencies: BridgeServiceRegistryDependencies) {
    super();
  }

  /**
   * Initialize all bridge services
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      throw new Error('Bridge service registry is already initialized');
    }

    try {
      this.dependencies.logger.info('Initializing bridge service registry...');

      // Initialize Python Engine Service
      this.pythonEngineService = new PythonEngineService({
        logger: this.dependencies.logger,
        config: this.dependencies.config.pythonEngine,
      });

      // Initialize bridge services
      this.tradingBridgeService = new TradingBridgeService({
        pythonEngineService: this.pythonEngineService,
        logger: this.dependencies.logger,
      });

      this.backtestBridgeService = new BacktestBridgeService({
        pythonEngineService: this.pythonEngineService,
        logger: this.dependencies.logger,
      });

      this.chatBridgeService = new ChatBridgeService({
        pythonEngineService: this.pythonEngineService,
        logger: this.dependencies.logger,
      });

      this.darwinGodelBridgeService = new DarwinGodelBridgeService(this.pythonEngineService);
      await this.darwinGodelBridgeService.initialize();

      // Setup event forwarding
      this.setupEventForwarding();

      // Start monitoring tasks
      this.startMonitoringTasks();

      this.isInitialized = true;
      this.dependencies.logger.info('Bridge service registry initialized successfully');

      this.emit('initialized');
    } catch (error) {
      this.dependencies.logger.error('Failed to initialize bridge service registry', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      
      // Cleanup partially initialized services
      await this.cleanup();
      throw error;
    }
  }

  /**
   * Setup event forwarding from bridge services
   */
  private setupEventForwarding(): void {
    // Python Engine events
    this.pythonEngineService.on('health_check_completed', (data) => {
      this.emit('python_engine_health_changed', data);
    });

    this.pythonEngineService.on('health_check_failed', (data) => {
      this.emit('python_engine_health_failed', data);
    });

    // Trading events
    this.tradingBridgeService.on('order_submitted', (data) => {
      this.emit('order_submitted', data);
    });

    this.tradingBridgeService.on('order_closed', (data) => {
      this.emit('order_closed', data);
    });

    // Backtest events
    this.backtestBridgeService.on('backtest_submitted', (data) => {
      this.emit('backtest_submitted', data);
    });

    this.backtestBridgeService.on('backtest_status_changed', (data) => {
      this.emit('backtest_status_changed', data);
    });

    this.backtestBridgeService.on('backtest_progress_updated', (data) => {
      this.emit('backtest_progress_updated', data);
    });

    // Chat events
    this.chatBridgeService.on('message_processed', (data) => {
      this.emit('chat_message_processed', data);
    });

    this.chatBridgeService.on('session_ended', (data) => {
      this.emit('chat_session_ended', data);
    });

    // Darwin Gödel Machine events (if the service extends EventEmitter)
    // Note: DarwinGodelBridgeService doesn't extend EventEmitter in current implementation
    // but we can add event forwarding if needed
  }

  /**
   * Start monitoring and cleanup tasks
   */
  private startMonitoringTasks(): void {
    const healthCheckInterval = this.dependencies.config.healthCheckInterval || 60000; // 1 minute
    const cleanupInterval = this.dependencies.config.cleanupInterval || 300000; // 5 minutes

    // Health monitoring
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, healthCheckInterval);

    // Cleanup tasks
    this.cleanupInterval = setInterval(() => {
      this.performCleanupTasks();
    }, cleanupInterval);

    this.dependencies.logger.debug('Started monitoring tasks', {
      healthCheckInterval,
      cleanupInterval,
    });
  }

  /**
   * Perform health checks on all services
   */
  private async performHealthCheck(): Promise<void> {
    try {
      const healthStatus = {
        pythonEngine: this.pythonEngineService.getHealthStatus(),
        timestamp: new Date(),
      };

      this.emit('health_check_completed', healthStatus);

      if (!healthStatus.pythonEngine.healthy) {
        this.dependencies.logger.warn('Python engine is unhealthy', healthStatus);
      }
    } catch (error) {
      this.dependencies.logger.error('Health check failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * Perform cleanup tasks
   */
  private performCleanupTasks(): void {
    try {
      // Cleanup completed backtests
      this.backtestBridgeService.cleanupCompletedBacktests();

      // Cleanup inactive chat sessions
      this.chatBridgeService.cleanupInactiveSessions(24); // 24 hours

      this.dependencies.logger.debug('Cleanup tasks completed');
    } catch (error) {
      this.dependencies.logger.error('Cleanup tasks failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * Get Python Engine Service
   */
  getPythonEngineService(): PythonEngineService {
    this.ensureInitialized();
    return this.pythonEngineService;
  }

  /**
   * Get Trading Bridge Service
   */
  getTradingBridgeService(): TradingBridgeService {
    this.ensureInitialized();
    return this.tradingBridgeService;
  }

  /**
   * Get Backtest Bridge Service
   */
  getBacktestBridgeService(): BacktestBridgeService {
    this.ensureInitialized();
    return this.backtestBridgeService;
  }

  /**
   * Get Chat Bridge Service
   */
  getChatBridgeService(): ChatBridgeService {
    this.ensureInitialized();
    return this.chatBridgeService;
  }

  /**
   * Get Darwin Gödel Bridge Service
   */
  getDarwinGodelBridgeService(): DarwinGodelBridgeService {
    this.ensureInitialized();
    return this.darwinGodelBridgeService;
  }

  /**
   * Get overall system health status
   */
  async getSystemHealth(): Promise<{
    healthy: boolean;
    services: {
      pythonEngine: { healthy: boolean; lastCheck: Date | null };
      trading: boolean;
      backtest: boolean;
      chat: boolean;
    };
    timestamp: Date;
  }> {
    this.ensureInitialized();

    const pythonEngineHealth = this.pythonEngineService.getHealthStatus();
    
    const health = {
      healthy: pythonEngineHealth.healthy,
      services: {
        pythonEngine: pythonEngineHealth,
        trading: pythonEngineHealth.healthy, // Trading depends on Python engine
        backtest: pythonEngineHealth.healthy, // Backtest depends on Python engine
        chat: pythonEngineHealth.healthy, // Chat depends on Python engine
      },
      timestamp: new Date(),
    };

    return health;
  }

  /**
   * Get service statistics
   */
  getServiceStatistics(): {
    activeBacktests: number;
    activeChatSessions: number;
    pythonEngineHealth: { healthy: boolean; lastCheck: Date | null };
  } {
    this.ensureInitialized();

    return {
      activeBacktests: this.backtestBridgeService.getActiveBacktests().length,
      activeChatSessions: this.chatBridgeService.getUserSessions('').length, // This would need user filtering in real implementation
      pythonEngineHealth: this.pythonEngineService.getHealthStatus(),
    };
  }

  /**
   * Shutdown all services gracefully
   */
  async shutdown(): Promise<void> {
    this.dependencies.logger.info('Shutting down bridge service registry...');

    try {
      // Stop monitoring tasks
      if (this.healthCheckInterval) {
        clearInterval(this.healthCheckInterval);
        this.healthCheckInterval = null;
      }

      if (this.cleanupInterval) {
        clearInterval(this.cleanupInterval);
        this.cleanupInterval = null;
      }

      // Shutdown services in reverse order
      if (this.darwinGodelBridgeService) {
        await this.darwinGodelBridgeService.cleanup();
      }

      if (this.chatBridgeService) {
        await this.chatBridgeService.stop();
      }

      if (this.backtestBridgeService) {
        await this.backtestBridgeService.stop();
      }

      if (this.tradingBridgeService) {
        await this.tradingBridgeService.stop();
      }

      if (this.pythonEngineService) {
        await this.pythonEngineService.stop();
      }

      this.isInitialized = false;
      this.removeAllListeners();

      this.dependencies.logger.info('Bridge service registry shutdown completed');
      this.emit('shutdown_completed');
    } catch (error) {
      this.dependencies.logger.error('Error during shutdown', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Cleanup resources (used internally during initialization failures)
   */
  private async cleanup(): Promise<void> {
    try {
      if (this.healthCheckInterval) {
        clearInterval(this.healthCheckInterval);
      }
      if (this.cleanupInterval) {
        clearInterval(this.cleanupInterval);
      }

      // Stop services that might have been initialized
      const services = [
        this.chatBridgeService,
        this.backtestBridgeService,
        this.tradingBridgeService,
        this.pythonEngineService,
      ];

      await Promise.allSettled(
        services
          .filter(service => service !== undefined)
          .map(service => service.stop())
      );

      this.removeAllListeners();
    } catch (error) {
      this.dependencies.logger.error('Error during cleanup', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * Ensure the registry is initialized before accessing services
   */
  private ensureInitialized(): void {
    if (!this.isInitialized) {
      throw new Error('Bridge service registry is not initialized. Call initialize() first.');
    }
  }

  /**
   * Check if the registry is initialized
   */
  isReady(): boolean {
    return this.isInitialized;
  }
}