"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SimulationResultSchema = exports.SimulationConfigSchema = exports.ChatbotResponseSchema = exports.ChatbotQuerySchema = exports.MLPredictionSchema = exports.TradeAnalysisSchema = exports.OHLCDataSchema = exports.TickDataSchema = exports.PositionSchema = exports.TradingEngineResponseSchema = exports.TradingEngineRequestSchema = exports.AccountInfoSchema = exports.OrderResultSchema = exports.OrderRequestSchema = exports.TradingSymbolSchema = exports.OrderTypeSchema = void 0;
const zod_1 = require("zod");
// Core Trading Types
exports.OrderTypeSchema = zod_1.z.enum(['buy', 'sell']);
exports.TradingSymbolSchema = zod_1.z.enum([
    'EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD',
    'USDCAD', 'USDCHF', 'NZDUSD', 'EURGBP'
]);
// Order Schemas (matching Python Pydantic models)
exports.OrderRequestSchema = zod_1.z.object({
    symbol: exports.TradingSymbolSchema,
    volume: zod_1.z.number().positive(),
    order_type: exports.OrderTypeSchema,
    price: zod_1.z.number().positive(),
    stop_loss: zod_1.z.number().positive().optional(),
    take_profit: zod_1.z.number().positive().optional(),
});
exports.OrderResultSchema = zod_1.z.object({
    success: zod_1.z.boolean(),
    order_id: zod_1.z.number().int().optional(),
    error: zod_1.z.string().optional(),
});
// Account Schemas
exports.AccountInfoSchema = zod_1.z.object({
    balance: zod_1.z.number(),
    equity: zod_1.z.number(),
    margin: zod_1.z.number(),
    currency: zod_1.z.string().default('USD'),
});
// Trading Engine Communication Schemas
exports.TradingEngineRequestSchema = zod_1.z.object({
    action: zod_1.z.enum(['get_account', 'submit_order', 'close_order', 'get_positions']),
    payload: zod_1.z.record(zod_1.z.any()).optional(),
    timestamp: zod_1.z.date().default(() => new Date()),
    request_id: zod_1.z.string().uuid(),
});
exports.TradingEngineResponseSchema = zod_1.z.object({
    success: zod_1.z.boolean(),
    data: zod_1.z.any().optional(),
    error: zod_1.z.string().optional(),
    timestamp: zod_1.z.date().default(() => new Date()),
    request_id: zod_1.z.string().uuid(),
});
// Position Schemas
exports.PositionSchema = zod_1.z.object({
    position_id: zod_1.z.number().int(),
    symbol: exports.TradingSymbolSchema,
    volume: zod_1.z.number(),
    open_price: zod_1.z.number(),
    current_price: zod_1.z.number(),
    pnl: zod_1.z.number(),
    order_type: exports.OrderTypeSchema,
    open_time: zod_1.z.date(),
});
// Market Data Schemas
exports.TickDataSchema = zod_1.z.object({
    symbol: exports.TradingSymbolSchema,
    bid: zod_1.z.number().positive(),
    ask: zod_1.z.number().positive(),
    timestamp: zod_1.z.date(),
});
exports.OHLCDataSchema = zod_1.z.object({
    symbol: exports.TradingSymbolSchema,
    timestamp: zod_1.z.date(),
    open: zod_1.z.number().positive(),
    high: zod_1.z.number().positive(),
    low: zod_1.z.number().positive(),
    close: zod_1.z.number().positive(),
    volume: zod_1.z.number().nonnegative().optional(),
});
// Analysis Schemas (bridging with Python analysis module)
exports.TradeAnalysisSchema = zod_1.z.object({
    total_trades: zod_1.z.number().int().nonnegative(),
    winning_trades: zod_1.z.number().int().nonnegative(),
    losing_trades: zod_1.z.number().int().nonnegative(),
    win_rate: zod_1.z.number().min(0).max(1),
    total_pnl: zod_1.z.number(),
    average_win: zod_1.z.number(),
    average_loss: zod_1.z.number(),
    profit_factor: zod_1.z.number().optional(),
    max_drawdown: zod_1.z.number().optional(),
});
// ML Prediction Schemas
exports.MLPredictionSchema = zod_1.z.object({
    symbol: exports.TradingSymbolSchema,
    prediction_type: zod_1.z.enum(['price', 'direction', 'volatility']),
    value: zod_1.z.number(),
    confidence: zod_1.z.number().min(0).max(1),
    timestamp: zod_1.z.date(),
    model_version: zod_1.z.string(),
});
// Chatbot Integration Schemas
exports.ChatbotQuerySchema = zod_1.z.object({
    query: zod_1.z.string().min(1),
    context: zod_1.z.object({
        user_id: zod_1.z.string(),
        session_id: zod_1.z.string(),
        trading_data: zod_1.z.any().optional(),
    }),
    timestamp: zod_1.z.date().default(() => new Date()),
});
exports.ChatbotResponseSchema = zod_1.z.object({
    response: zod_1.z.string(),
    confidence: zod_1.z.number().min(0).max(1),
    sources: zod_1.z.array(zod_1.z.string()).optional(),
    suggested_actions: zod_1.z.array(zod_1.z.string()).optional(),
    timestamp: zod_1.z.date().default(() => new Date()),
});
// Simulation Schemas
exports.SimulationConfigSchema = zod_1.z.object({
    initial_balance: zod_1.z.number().positive().default(10000),
    start_date: zod_1.z.date(),
    end_date: zod_1.z.date(),
    symbols: zod_1.z.array(exports.TradingSymbolSchema),
    strategy_config: zod_1.z.record(zod_1.z.any()),
});
exports.SimulationResultSchema = zod_1.z.object({
    config: exports.SimulationConfigSchema,
    analysis: exports.TradeAnalysisSchema,
    trades: zod_1.z.array(zod_1.z.object({
        symbol: exports.TradingSymbolSchema,
        entry_time: zod_1.z.date(),
        exit_time: zod_1.z.date().optional(),
        entry_price: zod_1.z.number(),
        exit_price: zod_1.z.number().optional(),
        volume: zod_1.z.number(),
        pnl: zod_1.z.number().optional(),
        order_type: exports.OrderTypeSchema,
    })),
    balance_history: zod_1.z.array(zod_1.z.object({
        timestamp: zod_1.z.date(),
        balance: zod_1.z.number(),
    })),
    created_at: zod_1.z.date().default(() => new Date()),
});
//# sourceMappingURL=trading.schemas.js.map