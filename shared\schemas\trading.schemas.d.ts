import { z } from 'zod';
export declare const OrderTypeSchema: z.<PERSON><["buy", "sell"]>;
export type OrderType = z.infer<typeof OrderTypeSchema>;
export declare const TradingSymbolSchema: z.<PERSON>od<PERSON><["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>;
export type TradingSymbol = z.infer<typeof TradingSymbolSchema>;
export declare const OrderRequestSchema: z.ZodObject<{
    symbol: z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>;
    volume: z.ZodNumber;
    order_type: z.Zod<PERSON><["buy", "sell"]>;
    price: z.ZodNumber;
    stop_loss: z.ZodOptional<z.ZodNumber>;
    take_profit: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
    volume: number;
    order_type: "buy" | "sell";
    price: number;
    stop_loss?: number | undefined;
    take_profit?: number | undefined;
}, {
    symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
    volume: number;
    order_type: "buy" | "sell";
    price: number;
    stop_loss?: number | undefined;
    take_profit?: number | undefined;
}>;
export type OrderRequest = z.infer<typeof OrderRequestSchema>;
export declare const OrderResultSchema: z.ZodObject<{
    success: z.ZodBoolean;
    order_id: z.ZodOptional<z.ZodNumber>;
    error: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    error?: string | undefined;
    order_id?: number | undefined;
}, {
    success: boolean;
    error?: string | undefined;
    order_id?: number | undefined;
}>;
export type OrderResult = z.infer<typeof OrderResultSchema>;
export declare const AccountInfoSchema: z.ZodObject<{
    balance: z.ZodNumber;
    equity: z.ZodNumber;
    margin: z.ZodNumber;
    currency: z.ZodDefault<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    balance: number;
    equity: number;
    margin: number;
    currency: string;
}, {
    balance: number;
    equity: number;
    margin: number;
    currency?: string | undefined;
}>;
export type AccountInfo = z.infer<typeof AccountInfoSchema>;
export declare const TradingEngineRequestSchema: z.ZodObject<{
    action: z.ZodEnum<["get_account", "submit_order", "close_order", "get_positions"]>;
    payload: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
    timestamp: z.ZodDefault<z.ZodDate>;
    request_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    action: "get_account" | "submit_order" | "close_order" | "get_positions";
    timestamp: Date;
    request_id: string;
    payload?: Record<string, any> | undefined;
}, {
    action: "get_account" | "submit_order" | "close_order" | "get_positions";
    request_id: string;
    timestamp?: Date | undefined;
    payload?: Record<string, any> | undefined;
}>;
export type TradingEngineRequest = z.infer<typeof TradingEngineRequestSchema>;
export declare const TradingEngineResponseSchema: z.ZodObject<{
    success: z.ZodBoolean;
    data: z.ZodOptional<z.ZodAny>;
    error: z.ZodOptional<z.ZodString>;
    timestamp: z.ZodDefault<z.ZodDate>;
    request_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    timestamp: Date;
    request_id: string;
    data?: any;
    error?: string | undefined;
}, {
    success: boolean;
    request_id: string;
    data?: any;
    error?: string | undefined;
    timestamp?: Date | undefined;
}>;
export type TradingEngineResponse = z.infer<typeof TradingEngineResponseSchema>;
export declare const PositionSchema: z.ZodObject<{
    position_id: z.ZodNumber;
    symbol: z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>;
    volume: z.ZodNumber;
    open_price: z.ZodNumber;
    current_price: z.ZodNumber;
    pnl: z.ZodNumber;
    order_type: z.ZodEnum<["buy", "sell"]>;
    open_time: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
    volume: number;
    order_type: "buy" | "sell";
    position_id: number;
    open_price: number;
    current_price: number;
    pnl: number;
    open_time: Date;
}, {
    symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
    volume: number;
    order_type: "buy" | "sell";
    position_id: number;
    open_price: number;
    current_price: number;
    pnl: number;
    open_time: Date;
}>;
export type Position = z.infer<typeof PositionSchema>;
export declare const TickDataSchema: z.ZodObject<{
    symbol: z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>;
    bid: z.ZodNumber;
    ask: z.ZodNumber;
    timestamp: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
    timestamp: Date;
    bid: number;
    ask: number;
}, {
    symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
    timestamp: Date;
    bid: number;
    ask: number;
}>;
export type TickData = z.infer<typeof TickDataSchema>;
export declare const OHLCDataSchema: z.ZodObject<{
    symbol: z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>;
    timestamp: z.ZodDate;
    open: z.ZodNumber;
    high: z.ZodNumber;
    low: z.ZodNumber;
    close: z.ZodNumber;
    volume: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
    low: number;
    high: number;
    close: number;
    open: number;
    timestamp: Date;
    volume?: number | undefined;
}, {
    symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
    low: number;
    high: number;
    close: number;
    open: number;
    timestamp: Date;
    volume?: number | undefined;
}>;
export type OHLCData = z.infer<typeof OHLCDataSchema>;
export declare const TradeAnalysisSchema: z.ZodObject<{
    total_trades: z.ZodNumber;
    winning_trades: z.ZodNumber;
    losing_trades: z.ZodNumber;
    win_rate: z.ZodNumber;
    total_pnl: z.ZodNumber;
    average_win: z.ZodNumber;
    average_loss: z.ZodNumber;
    profit_factor: z.ZodOptional<z.ZodNumber>;
    max_drawdown: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    win_rate: number;
    total_trades: number;
    winning_trades: number;
    losing_trades: number;
    total_pnl: number;
    average_win: number;
    average_loss: number;
    profit_factor?: number | undefined;
    max_drawdown?: number | undefined;
}, {
    win_rate: number;
    total_trades: number;
    winning_trades: number;
    losing_trades: number;
    total_pnl: number;
    average_win: number;
    average_loss: number;
    profit_factor?: number | undefined;
    max_drawdown?: number | undefined;
}>;
export type TradeAnalysis = z.infer<typeof TradeAnalysisSchema>;
export declare const MLPredictionSchema: z.ZodObject<{
    symbol: z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>;
    prediction_type: z.ZodEnum<["price", "direction", "volatility"]>;
    value: z.ZodNumber;
    confidence: z.ZodNumber;
    timestamp: z.ZodDate;
    model_version: z.ZodString;
}, "strip", z.ZodTypeAny, {
    symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
    value: number;
    confidence: number;
    timestamp: Date;
    prediction_type: "price" | "direction" | "volatility";
    model_version: string;
}, {
    symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
    value: number;
    confidence: number;
    timestamp: Date;
    prediction_type: "price" | "direction" | "volatility";
    model_version: string;
}>;
export type MLPrediction = z.infer<typeof MLPredictionSchema>;
export declare const ChatbotQuerySchema: z.ZodObject<{
    query: z.ZodString;
    context: z.ZodObject<{
        user_id: z.ZodString;
        session_id: z.ZodString;
        trading_data: z.ZodOptional<z.ZodAny>;
    }, "strip", z.ZodTypeAny, {
        user_id: string;
        session_id: string;
        trading_data?: any;
    }, {
        user_id: string;
        session_id: string;
        trading_data?: any;
    }>;
    timestamp: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    context: {
        user_id: string;
        session_id: string;
        trading_data?: any;
    };
    timestamp: Date;
    query: string;
}, {
    context: {
        user_id: string;
        session_id: string;
        trading_data?: any;
    };
    query: string;
    timestamp?: Date | undefined;
}>;
export type ChatbotQuery = z.infer<typeof ChatbotQuerySchema>;
export declare const ChatbotResponseSchema: z.ZodObject<{
    response: z.ZodString;
    confidence: z.ZodNumber;
    sources: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    suggested_actions: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    timestamp: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    confidence: number;
    timestamp: Date;
    response: string;
    sources?: string[] | undefined;
    suggested_actions?: string[] | undefined;
}, {
    confidence: number;
    response: string;
    timestamp?: Date | undefined;
    sources?: string[] | undefined;
    suggested_actions?: string[] | undefined;
}>;
export type ChatbotResponse = z.infer<typeof ChatbotResponseSchema>;
export declare const SimulationConfigSchema: z.ZodObject<{
    initial_balance: z.ZodDefault<z.ZodNumber>;
    start_date: z.ZodDate;
    end_date: z.ZodDate;
    symbols: z.ZodArray<z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>, "many">;
    strategy_config: z.ZodRecord<z.ZodString, z.ZodAny>;
}, "strip", z.ZodTypeAny, {
    initial_balance: number;
    start_date: Date;
    end_date: Date;
    symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
    strategy_config: Record<string, any>;
}, {
    start_date: Date;
    end_date: Date;
    symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
    strategy_config: Record<string, any>;
    initial_balance?: number | undefined;
}>;
export type SimulationConfig = z.infer<typeof SimulationConfigSchema>;
export declare const SimulationResultSchema: z.ZodObject<{
    config: z.ZodObject<{
        initial_balance: z.ZodDefault<z.ZodNumber>;
        start_date: z.ZodDate;
        end_date: z.ZodDate;
        symbols: z.ZodArray<z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>, "many">;
        strategy_config: z.ZodRecord<z.ZodString, z.ZodAny>;
    }, "strip", z.ZodTypeAny, {
        initial_balance: number;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        strategy_config: Record<string, any>;
    }, {
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        strategy_config: Record<string, any>;
        initial_balance?: number | undefined;
    }>;
    analysis: z.ZodObject<{
        total_trades: z.ZodNumber;
        winning_trades: z.ZodNumber;
        losing_trades: z.ZodNumber;
        win_rate: z.ZodNumber;
        total_pnl: z.ZodNumber;
        average_win: z.ZodNumber;
        average_loss: z.ZodNumber;
        profit_factor: z.ZodOptional<z.ZodNumber>;
        max_drawdown: z.ZodOptional<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        win_rate: number;
        total_trades: number;
        winning_trades: number;
        losing_trades: number;
        total_pnl: number;
        average_win: number;
        average_loss: number;
        profit_factor?: number | undefined;
        max_drawdown?: number | undefined;
    }, {
        win_rate: number;
        total_trades: number;
        winning_trades: number;
        losing_trades: number;
        total_pnl: number;
        average_win: number;
        average_loss: number;
        profit_factor?: number | undefined;
        max_drawdown?: number | undefined;
    }>;
    trades: z.ZodArray<z.ZodObject<{
        symbol: z.ZodEnum<["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURGBP"]>;
        entry_time: z.ZodDate;
        exit_time: z.ZodOptional<z.ZodDate>;
        entry_price: z.ZodNumber;
        exit_price: z.ZodOptional<z.ZodNumber>;
        volume: z.ZodNumber;
        pnl: z.ZodOptional<z.ZodNumber>;
        order_type: z.ZodEnum<["buy", "sell"]>;
    }, "strip", z.ZodTypeAny, {
        symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
        volume: number;
        order_type: "buy" | "sell";
        entry_time: Date;
        entry_price: number;
        pnl?: number | undefined;
        exit_time?: Date | undefined;
        exit_price?: number | undefined;
    }, {
        symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
        volume: number;
        order_type: "buy" | "sell";
        entry_time: Date;
        entry_price: number;
        pnl?: number | undefined;
        exit_time?: Date | undefined;
        exit_price?: number | undefined;
    }>, "many">;
    balance_history: z.ZodArray<z.ZodObject<{
        timestamp: z.ZodDate;
        balance: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        timestamp: Date;
        balance: number;
    }, {
        timestamp: Date;
        balance: number;
    }>, "many">;
    created_at: z.ZodDefault<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    analysis: {
        win_rate: number;
        total_trades: number;
        winning_trades: number;
        losing_trades: number;
        total_pnl: number;
        average_win: number;
        average_loss: number;
        profit_factor?: number | undefined;
        max_drawdown?: number | undefined;
    };
    config: {
        initial_balance: number;
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        strategy_config: Record<string, any>;
    };
    trades: {
        symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
        volume: number;
        order_type: "buy" | "sell";
        entry_time: Date;
        entry_price: number;
        pnl?: number | undefined;
        exit_time?: Date | undefined;
        exit_price?: number | undefined;
    }[];
    balance_history: {
        timestamp: Date;
        balance: number;
    }[];
    created_at: Date;
}, {
    analysis: {
        win_rate: number;
        total_trades: number;
        winning_trades: number;
        losing_trades: number;
        total_pnl: number;
        average_win: number;
        average_loss: number;
        profit_factor?: number | undefined;
        max_drawdown?: number | undefined;
    };
    config: {
        start_date: Date;
        end_date: Date;
        symbols: ("EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP")[];
        strategy_config: Record<string, any>;
        initial_balance?: number | undefined;
    };
    trades: {
        symbol: "EURUSD" | "GBPUSD" | "USDJPY" | "AUDUSD" | "USDCAD" | "USDCHF" | "NZDUSD" | "EURGBP";
        volume: number;
        order_type: "buy" | "sell";
        entry_time: Date;
        entry_price: number;
        pnl?: number | undefined;
        exit_time?: Date | undefined;
        exit_price?: number | undefined;
    }[];
    balance_history: {
        timestamp: Date;
        balance: number;
    }[];
    created_at?: Date | undefined;
}>;
export type SimulationResult = z.infer<typeof SimulationResultSchema>;
//# sourceMappingURL=trading.schemas.d.ts.map