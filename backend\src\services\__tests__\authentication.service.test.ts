import { describe, it, expect, beforeEach, jest, afterEach } from '@jest/globals';
import { AuthenticationService } from '../authentication.service';

// Mock dependencies
const mockBcrypt = {
  hash: jest.fn(),
  compare: jest.fn(),
};

const mockJwt = {
  sign: jest.fn(),
  verify: jest.fn(),
  decode: jest.fn(),
};

jest.mock('bcrypt', () => mockBcrypt);
jest.mock('jsonwebtoken', () => mockJwt);

describe('AuthenticationService', () => {
  let authService: AuthenticationService;
  let mockUserRepository: any;
  let mockCacheService: any;

  const mockUser = {
    id: 'user_123',
    email: '<EMAIL>',
    password: '$2b$10$hashedpassword',
    role: 'user',
    isActive: true,
    emailVerified: true,
    lastLoginAt: new Date(),
    createdAt: new Date(),
  };

  beforeEach(() => {
    mockUserRepository = {
      findByEmail: jest.fn(),
      findById: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    };

    mockCacheService = {
      get: jest.fn(),
      set: jest.fn(),
      delete: jest.fn(),
      exists: jest.fn(),
    };

    authService = new AuthenticationService(mockUserRepository, mockCacheService, {
      jwtSecret: 'test-secret',
      jwtExpiresIn: '1h',
      refreshTokenExpiresIn: '7d',
      bcryptRounds: 10,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('register', () => {
    it('should register a new user successfully', async () => {
      // Arrange
      const userData = {
        email: '<EMAIL>',
        password: 'SecurePassword123!',
        firstName: 'John',
        lastName: 'Doe',
      };

      mockUserRepository.findByEmail.mockResolvedValue(null);
      mockBcrypt.hash.mockResolvedValue('$2b$10$hashedpassword');
      mockUserRepository.create.mockResolvedValue({
        ...mockUser,
        email: userData.email,
        id: 'new_user_123',
      });

      // Act
      const result = await authService.register(userData);

      // Assert
      expect(result.success).toBe(true);
      expect(result.user).toBeDefined();
      expect(result.user?.email).toBe(userData.email);
      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(userData.email);
      expect(mockBcrypt.hash).toHaveBeenCalledWith(userData.password, 10);
      expect(mockUserRepository.create).toHaveBeenCalled();
    });

    it('should reject registration with existing email', async () => {
      // Arrange
      const userData = {
        email: '<EMAIL>',
        password: 'SecurePassword123!',
        firstName: 'John',
        lastName: 'Doe',
      };

      mockUserRepository.findByEmail.mockResolvedValue(mockUser);

      // Act
      const result = await authService.register(userData);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('Email already exists');
      expect(mockUserRepository.create).not.toHaveBeenCalled();
    });

    it('should validate password strength', async () => {
      // Arrange
      const userData = {
        email: '<EMAIL>',
        password: 'weak',
        firstName: 'John',
        lastName: 'Doe',
      };

      // Act
      const result = await authService.register(userData);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toContain('Password must be at least 8 characters');
    });

    it('should validate email format', async () => {
      // Arrange
      const userData = {
        email: 'invalid-email',
        password: 'SecurePassword123!',
        firstName: 'John',
        lastName: 'Doe',
      };

      // Act
      const result = await authService.register(userData);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid email format');
    });
  });

  describe('login', () => {
    it('should login user with valid credentials', async () => {
      // Arrange
      const credentials = {
        email: '<EMAIL>',
        password: 'correctpassword',
      };

      mockUserRepository.findByEmail.mockResolvedValue(mockUser);
      mockBcrypt.compare.mockResolvedValue(true);
      mockJwt.sign.mockReturnValue('mock-jwt-token');

      // Act
      const result = await authService.login(credentials);

      // Assert
      expect(result.success).toBe(true);
      expect(result.accessToken).toBe('mock-jwt-token');
      expect(result.refreshToken).toBeDefined();
      expect(result.user).toBeDefined();
      expect(mockUserRepository.update).toHaveBeenCalledWith(mockUser.id, {
        lastLoginAt: expect.any(Date),
      });
    });

    it('should reject login with invalid email', async () => {
      // Arrange
      const credentials = {
        email: '<EMAIL>',
        password: 'password',
      };

      mockUserRepository.findByEmail.mockResolvedValue(null);

      // Act
      const result = await authService.login(credentials);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid credentials');
      expect(mockBcrypt.compare).not.toHaveBeenCalled();
    });

    it('should reject login with invalid password', async () => {
      // Arrange
      const credentials = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      };

      mockUserRepository.findByEmail.mockResolvedValue(mockUser);
      mockBcrypt.compare.mockResolvedValue(false);

      // Act
      const result = await authService.login(credentials);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid credentials');
      expect(mockJwt.sign).not.toHaveBeenCalled();
    });

    it('should reject login for inactive user', async () => {
      // Arrange
      const credentials = {
        email: '<EMAIL>',
        password: 'correctpassword',
      };

      const inactiveUser = { ...mockUser, isActive: false };
      mockUserRepository.findByEmail.mockResolvedValue(inactiveUser);

      // Act
      const result = await authService.login(credentials);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('Account is deactivated');
    });

    it('should reject login for unverified email', async () => {
      // Arrange
      const credentials = {
        email: '<EMAIL>',
        password: 'correctpassword',
      };

      const unverifiedUser = { ...mockUser, emailVerified: false };
      mockUserRepository.findByEmail.mockResolvedValue(unverifiedUser);

      // Act
      const result = await authService.login(credentials);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('Email not verified');
    });
  });

  describe('refreshToken', () => {
    it('should refresh valid token', async () => {
      // Arrange
      const refreshToken = 'valid-refresh-token';
      const tokenPayload = {
        userId: mockUser.id,
        email: mockUser.email,
        role: mockUser.role,
        type: 'refresh',
      };

      (jwt.verify as jest.Mock).mockReturnValue(tokenPayload);
      mockUserRepository.findById.mockResolvedValue(mockUser);
      mockCacheService.exists.mockResolvedValue(true);
      (jwt.sign as jest.Mock).mockReturnValue('new-access-token');

      // Act
      const result = await authService.refreshToken(refreshToken);

      // Assert
      expect(result.success).toBe(true);
      expect(result.accessToken).toBe('new-access-token');
      expect(jwt.verify).toHaveBeenCalledWith(refreshToken, 'test-secret');
    });

    it('should reject invalid refresh token', async () => {
      // Arrange
      const refreshToken = 'invalid-token';
      (jwt.verify as jest.Mock).mockImplementation(() => {
        throw new Error('Invalid token');
      });

      // Act
      const result = await authService.refreshToken(refreshToken);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid refresh token');
    });

    it('should reject blacklisted refresh token', async () => {
      // Arrange
      const refreshToken = 'blacklisted-token';
      const tokenPayload = {
        userId: mockUser.id,
        email: mockUser.email,
        role: mockUser.role,
        type: 'refresh',
      };

      (jwt.verify as jest.Mock).mockReturnValue(tokenPayload);
      mockCacheService.exists.mockResolvedValue(false); // Token not in whitelist

      // Act
      const result = await authService.refreshToken(refreshToken);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('Token has been revoked');
    });
  });

  describe('logout', () => {
    it('should logout user and blacklist tokens', async () => {
      // Arrange
      const userId = mockUser.id;
      const accessToken = 'access-token';
      const refreshToken = 'refresh-token';

      mockCacheService.set.mockResolvedValue(undefined);
      mockCacheService.delete.mockResolvedValue(true);

      // Act
      const result = await authService.logout(userId, accessToken, refreshToken);

      // Assert
      expect(result).toBe(true);
      expect(mockCacheService.set).toHaveBeenCalledTimes(2); // Blacklist both tokens
      expect(mockCacheService.delete).toHaveBeenCalledWith(`refresh_token:${userId}`);
    });
  });

  describe('verifyToken', () => {
    it('should verify valid access token', async () => {
      // Arrange
      const token = 'valid-access-token';
      const tokenPayload = {
        userId: mockUser.id,
        email: mockUser.email,
        role: mockUser.role,
        type: 'access',
      };

      (jwt.verify as jest.Mock).mockReturnValue(tokenPayload);
      mockCacheService.exists.mockResolvedValue(false); // Not blacklisted

      // Act
      const result = await authService.verifyToken(token);

      // Assert
      expect(result.valid).toBe(true);
      expect(result.payload).toEqual(tokenPayload);
    });

    it('should reject blacklisted token', async () => {
      // Arrange
      const token = 'blacklisted-token';
      const tokenPayload = {
        userId: mockUser.id,
        email: mockUser.email,
        role: mockUser.role,
        type: 'access',
      };

      (jwt.verify as jest.Mock).mockReturnValue(tokenPayload);
      mockCacheService.exists.mockResolvedValue(true); // Blacklisted

      // Act
      const result = await authService.verifyToken(token);

      // Assert
      expect(result.valid).toBe(false);
      expect(result.error).toBe('Token has been revoked');
    });

    it('should reject expired token', async () => {
      // Arrange
      const token = 'expired-token';
      (jwt.verify as jest.Mock).mockImplementation(() => {
        const error = new Error('Token expired');
        error.name = 'TokenExpiredError';
        throw error;
      });

      // Act
      const result = await authService.verifyToken(token);

      // Assert
      expect(result.valid).toBe(false);
      expect(result.error).toBe('Token expired');
    });
  });

  describe('changePassword', () => {
    it('should change password with valid current password', async () => {
      // Arrange
      const userId = mockUser.id;
      const currentPassword = 'currentpassword';
      const newPassword = 'NewSecurePassword123!';

      mockUserRepository.findById.mockResolvedValue(mockUser);
      (bcrypt.compare as jest.Mock).mockResolvedValue(true);
      (bcrypt.hash as jest.Mock).mockResolvedValue('$2b$10$newhashed');

      // Act
      const result = await authService.changePassword(userId, currentPassword, newPassword);

      // Assert
      expect(result.success).toBe(true);
      expect(mockUserRepository.update).toHaveBeenCalledWith(userId, {
        password: '$2b$10$newhashed',
      });
    });

    it('should reject password change with invalid current password', async () => {
      // Arrange
      const userId = mockUser.id;
      const currentPassword = 'wrongpassword';
      const newPassword = 'NewSecurePassword123!';

      mockUserRepository.findById.mockResolvedValue(mockUser);
      (bcrypt.compare as jest.Mock).mockResolvedValue(false);

      // Act
      const result = await authService.changePassword(userId, currentPassword, newPassword);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('Current password is incorrect');
      expect(mockUserRepository.update).not.toHaveBeenCalled();
    });

    it('should validate new password strength', async () => {
      // Arrange
      const userId = mockUser.id;
      const currentPassword = 'currentpassword';
      const newPassword = 'weak';

      // Act
      const result = await authService.changePassword(userId, currentPassword, newPassword);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toContain('Password must be at least 8 characters');
    });
  });

  describe('resetPassword', () => {
    it('should initiate password reset', async () => {
      // Arrange
      const email = '<EMAIL>';
      mockUserRepository.findByEmail.mockResolvedValue(mockUser);
      (jwt.sign as jest.Mock).mockReturnValue('reset-token');

      // Act
      const result = await authService.initiatePasswordReset(email);

      // Assert
      expect(result.success).toBe(true);
      expect(result.resetToken).toBe('reset-token');
      expect(mockCacheService.set).toHaveBeenCalledWith(
        `password_reset:${mockUser.id}`,
        'reset-token',
        3600 // 1 hour
      );
    });

    it('should complete password reset with valid token', async () => {
      // Arrange
      const resetToken = 'valid-reset-token';
      const newPassword = 'NewSecurePassword123!';
      const tokenPayload = {
        userId: mockUser.id,
        type: 'password_reset',
      };

      (jwt.verify as jest.Mock).mockReturnValue(tokenPayload);
      mockCacheService.get.mockResolvedValue(resetToken);
      (bcrypt.hash as jest.Mock).mockResolvedValue('$2b$10$newhashed');

      // Act
      const result = await authService.completePasswordReset(resetToken, newPassword);

      // Assert
      expect(result.success).toBe(true);
      expect(mockUserRepository.update).toHaveBeenCalledWith(mockUser.id, {
        password: '$2b$10$newhashed',
      });
      expect(mockCacheService.delete).toHaveBeenCalledWith(`password_reset:${mockUser.id}`);
    });
  });

  describe('user management', () => {
    it('should get user profile', async () => {
      // Arrange
      const userId = mockUser.id;
      mockUserRepository.findById.mockResolvedValue(mockUser);

      // Act
      const result = await authService.getUserProfile(userId);

      // Assert
      expect(result).toBeDefined();
      expect(result?.id).toBe(userId);
      expect((result as any)?.password).toBeUndefined(); // Password should be excluded
    });

    it('should update user profile', async () => {
      // Arrange
      const userId = mockUser.id;
      const updates = {
        firstName: 'Updated',
        lastName: 'Name',
      };

      mockUserRepository.update.mockResolvedValue({ ...mockUser, ...updates });

      // Act
      const result = await authService.updateUserProfile(userId, updates);

      // Assert
      expect(result.success).toBe(true);
      expect(mockUserRepository.update).toHaveBeenCalledWith(userId, updates);
    });

    it('should deactivate user account', async () => {
      // Arrange
      const userId = mockUser.id;

      // Act
      const result = await authService.deactivateUser(userId);

      // Assert
      expect(result).toBe(true);
      expect(mockUserRepository.update).toHaveBeenCalledWith(userId, {
        isActive: false,
      });
    });
  });

  describe('session management', () => {
    it('should get active sessions for user', async () => {
      // Arrange
      const userId = mockUser.id;
      const sessions = [
        { id: 'session1', deviceInfo: 'Chrome on Windows', lastActivity: new Date() },
        { id: 'session2', deviceInfo: 'Safari on iOS', lastActivity: new Date() },
      ];

      mockCacheService.get.mockResolvedValue(JSON.stringify(sessions));

      // Act
      const result = await authService.getActiveSessions(userId);

      // Assert
      expect(result).toEqual(sessions);
    });

    it('should revoke specific session', async () => {
      // Arrange
      const userId = mockUser.id;
      const sessionId = 'session1';

      // Act
      const result = await authService.revokeSession(userId, sessionId);

      // Assert
      expect(result).toBe(true);
      expect(mockCacheService.delete).toHaveBeenCalledWith(`session:${sessionId}`);
    });

    it('should revoke all sessions for user', async () => {
      // Arrange
      const userId = mockUser.id;

      // Act
      const result = await authService.revokeAllSessions(userId);

      // Assert
      expect(result).toBe(true);
      expect(mockCacheService.delete).toHaveBeenCalledWith(`user_sessions:${userId}`);
    });
  });

  describe('security features', () => {
    it('should track failed login attempts', async () => {
      // Arrange
      const email = '<EMAIL>';
      const ip = '***********';

      // Act
      await authService.trackFailedLogin(email, ip);

      // Assert
      expect(mockCacheService.set).toHaveBeenCalledWith(
        `failed_login:${email}:${ip}`,
        expect.any(String),
        900 // 15 minutes
      );
    });

    it('should check if account is locked', async () => {
      // Arrange
      const email = '<EMAIL>';
      const ip = '***********';
      mockCacheService.get.mockResolvedValue('5'); // 5 failed attempts

      // Act
      const result = await authService.isAccountLocked(email, ip);

      // Assert
      expect(result).toBe(true);
    });

    it('should generate secure API key', async () => {
      // Arrange
      const userId = mockUser.id;
      const keyName = 'Trading API Key';

      // Act
      const result = await authService.generateApiKey(userId, keyName);

      // Assert
      expect(result.success).toBe(true);
      expect(result.apiKey).toBeDefined();
      expect(result.apiKey).toMatch(/^ak_[a-zA-Z0-9]{32}$/);
    });

    it('should validate API key', async () => {
      // Arrange
      const apiKey = 'ak_validkey123456789012345678901234';
      const keyData = {
        userId: mockUser.id,
        name: 'Trading API Key',
        permissions: ['read', 'trade'],
        isActive: true,
      };

      mockCacheService.get.mockResolvedValue(JSON.stringify(keyData));

      // Act
      const result = await authService.validateApiKey(apiKey);

      // Assert
      expect(result.valid).toBe(true);
      expect(result.userId).toBe(mockUser.id);
      expect(result.permissions).toEqual(['read', 'trade']);
    });
  });
});