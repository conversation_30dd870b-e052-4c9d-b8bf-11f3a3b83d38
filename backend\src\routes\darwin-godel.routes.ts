import { Router, Request, Response } from 'express';
import { DarwinGodelBridgeService } from '../services/darwin-godel-bridge.service';

export const darwinGodelRoutes = Router();

// Initialize the bridge service
const bridgeService = new DarwinGodelBridgeService();

/**
 * POST /verify
 * Verify a trading strategy using the Darwin Godel model
 */
darwinGodelRoutes.post('/verify', async (req: Request, res: Response) => {
  try {
    const { strategyCode } = req.body;

    // Validate input
    if (!strategyCode) {
      return res.status(400).json({
        success: false,
        error: 'Strategy code is required'
      });
    }

    if (typeof strategyCode !== 'string' || strategyCode.trim() === '') {
      return res.status(400).json({
        success: false,
        error: 'Strategy code cannot be empty'
      });
    }

    // Verify the strategy
    const result = await bridgeService.verifyStrategy(strategyCode);

    return res.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Strategy verification error:', error);
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown verification error'
    });
  }
});

/**
 * POST /verify-with-backtest
 * Verify a strategy with historical backtesting
 */
darwinGodelRoutes.post('/verify-with-backtest', async (req: Request, res: Response) => {
  try {
    const { strategyCode, historicalData, initialCapital = 10000 } = req.body;

    // Validate input
    if (!strategyCode) {
      return res.status(400).json({
        success: false,
        error: 'Strategy code is required'
      });
    }

    if (typeof strategyCode !== 'string' || strategyCode.trim() === '') {
      return res.status(400).json({
        success: false,
        error: 'Strategy code cannot be empty'
      });
    }

    if (!historicalData) {
      return res.status(400).json({
        success: false,
        error: 'Historical data is required'
      });
    }

    // Validate historical data structure
    if (!historicalData.close || !Array.isArray(historicalData.close)) {
      return res.status(400).json({
        success: false,
        error: 'Historical data must contain a close price array'
      });
    }

    // Verify the strategy with backtest
    const result = await bridgeService.verifyWithBacktest(
      strategyCode,
      historicalData,
      initialCapital
    );

    return res.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Strategy backtest verification error:', error);
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown verification error'
    });
  }
});

/**
 * POST /monte-carlo
 * Run Monte Carlo validation on a strategy
 */
darwinGodelRoutes.post('/monte-carlo', async (req: Request, res: Response) => {
  try {
    const { 
      strategyCode, 
      simulations = 100, 
      dataVariations = 0.02 
    } = req.body;

    // Validate input
    if (!strategyCode) {
      return res.status(400).json({
        success: false,
        error: 'Strategy code is required'
      });
    }

    if (typeof strategyCode !== 'string' || strategyCode.trim() === '') {
      return res.status(400).json({
        success: false,
        error: 'Strategy code cannot be empty'
      });
    }

    // Validate parameters
    if (simulations && (typeof simulations !== 'number' || simulations < 1 || simulations > 1000)) {
      return res.status(400).json({
        success: false,
        error: 'Simulations must be a number between 1 and 1000'
      });
    }

    if (dataVariations && (typeof dataVariations !== 'number' || dataVariations < 0 || dataVariations > 1)) {
      return res.status(400).json({
        success: false,
        error: 'Data variations must be a number between 0 and 1'
      });
    }

    // Run Monte Carlo validation
    const result = await bridgeService.runMonteCarloValidation(
      strategyCode,
      simulations,
      dataVariations
    );

    return res.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Monte Carlo validation error:', error);
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown validation error'
    });
  }
});

/**
 * GET /health
 * Check the health of the Darwin Godel Python engine
 */
darwinGodelRoutes.get('/health', async (_req: Request, res: Response) => {
  try {
    const isHealthy = await bridgeService.checkHealth();

    if (isHealthy) {
      return res.json({
        success: true,
        data: {
          status: 'healthy',
          pythonEngine: 'available'
        }
      });
    } else {
      return res.status(503).json({
        success: false,
        data: {
          status: 'unhealthy',
          pythonEngine: 'unavailable'
        }
      });
    }

  } catch (error) {
    console.error('Health check error:', error);
    return res.status(503).json({
      success: false,
      data: {
        status: 'unhealthy',
        pythonEngine: 'error'
      }
    });
  }
});

/**
 * GET /
 * Get information about the Darwin Godel verification engine
 */
darwinGodelRoutes.get('/', (_req: Request, res: Response) => {
  res.json({
    success: true,
    data: {
      name: 'Darwin Godel Strategy Verification Engine',
      version: '1.0.0',
      description: 'Advanced strategy verification using formal methods and statistical analysis',
      capabilities: [
        'Security validation',
        'Strategy type detection',
        'Risk assessment',
        'Robustness analysis',
        'Historical backtesting',
        'Monte Carlo simulation'
      ],
      endpoints: {
        verify: 'POST /verify - Basic strategy verification',
        verifyWithBacktest: 'POST /verify-with-backtest - Verification with historical data',
        monteCarlo: 'POST /monte-carlo - Monte Carlo robustness testing',
        health: 'GET /health - Engine health check'
      }
    }
  });
});