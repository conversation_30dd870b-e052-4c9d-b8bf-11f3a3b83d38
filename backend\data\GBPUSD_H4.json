{"timestamp": ["2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00"], "open": [1.3, 1.3001, 1.3002, 1.3003, 1.3004, 1.3005, 1.3006, 1.3007, 1.3008, 1.3009, 1.301, 1.3011000000000001, 1.3012000000000001, 1.3013000000000001, 1.3014000000000001, 1.3015, 1.3016, 1.3017, 1.3018, 1.3019, 1.302, 1.3021, 1.3022, 1.3023, 1.3024, 1.3025, 1.3026, 1.3027, 1.3028, 1.3029, 1.303, 1.3031000000000001, 1.3032000000000001, 1.3033000000000001, 1.3034000000000001, 1.3035, 1.3036, 1.3037, 1.3038, 1.3039, 1.304, 1.3041, 1.3042, 1.3043, 1.3044, 1.3045, 1.3046, 1.3047, 1.3048, 1.3049, 1.305, 1.3051000000000001, 1.3052000000000001, 1.3053000000000001, 1.3054000000000001, 1.3055, 1.3056, 1.3057, 1.3058, 1.3059, 1.306, 1.3061, 1.3062, 1.3063, 1.3064, 1.3065, 1.3066, 1.3067, 1.3068, 1.3069, 1.307, 1.3071000000000002, 1.3072000000000001, 1.3073000000000001, 1.3074000000000001, 1.3075, 1.3076, 1.3077, 1.3078, 1.3079, 1.308, 1.3081, 1.3082, 1.3083, 1.3084, 1.3085, 1.3086, 1.3087, 1.3088, 1.3089, 1.309, 1.3091000000000002, 1.3092000000000001, 1.3093000000000001, 1.3094000000000001, 1.3095, 1.3096, 1.3097, 1.3098, 1.3099], "high": [1.305, 1.3051, 1.3052, 1.3053, 1.3054, 1.3054999999999999, 1.3055999999999999, 1.3056999999999999, 1.3057999999999998, 1.3058999999999998, 1.3059999999999998, 1.3061, 1.3062, 1.3063, 1.3064, 1.3065, 1.3066, 1.3067, 1.3068, 1.3069, 1.307, 1.3071, 1.3072, 1.3073, 1.3074, 1.3074999999999999, 1.3075999999999999, 1.3076999999999999, 1.3077999999999999, 1.3078999999999998, 1.3079999999999998, 1.3081, 1.3082, 1.3083, 1.3084, 1.3085, 1.3086, 1.3087, 1.3088, 1.3089, 1.309, 1.3091, 1.3092, 1.3093, 1.3094, 1.3094999999999999, 1.3095999999999999, 1.3096999999999999, 1.3097999999999999, 1.3098999999999998, 1.3099999999999998, 1.3101, 1.3102, 1.3103, 1.3104, 1.3105, 1.3106, 1.3107, 1.3108, 1.3109, 1.311, 1.3111, 1.3112, 1.3113, 1.3114, 1.3114999999999999, 1.3115999999999999, 1.3116999999999999, 1.3117999999999999, 1.3118999999999998, 1.3119999999999998, 1.3121, 1.3122, 1.3123, 1.3124, 1.3125, 1.3126, 1.3127, 1.3128, 1.3129, 1.313, 1.3131, 1.3132, 1.3133, 1.3134, 1.3135, 1.3135999999999999, 1.3136999999999999, 1.3137999999999999, 1.3138999999999998, 1.3139999999999998, 1.3141, 1.3142, 1.3143, 1.3144, 1.3145, 1.3146, 1.3147, 1.3148, 1.3149], "low": [1.295, 1.2951, 1.2952, 1.2953, 1.2953999999999999, 1.2954999999999999, 1.2955999999999999, 1.2956999999999999, 1.2957999999999998, 1.2958999999999998, 1.2959999999999998, 1.2961, 1.2962, 1.2963, 1.2964, 1.2965, 1.2966, 1.2967, 1.2968, 1.2969, 1.297, 1.2971, 1.2972, 1.2973, 1.2973999999999999, 1.2974999999999999, 1.2975999999999999, 1.2976999999999999, 1.2977999999999998, 1.2978999999999998, 1.2979999999999998, 1.2981, 1.2982, 1.2983, 1.2984, 1.2985, 1.2986, 1.2987, 1.2988, 1.2989, 1.299, 1.2991, 1.2992, 1.2993, 1.2993999999999999, 1.2994999999999999, 1.2995999999999999, 1.2996999999999999, 1.2997999999999998, 1.2998999999999998, 1.2999999999999998, 1.3001, 1.3002, 1.3003, 1.3004, 1.3005, 1.3006, 1.3007, 1.3008, 1.3009, 1.301, 1.3011, 1.3012, 1.3013, 1.3014, 1.3014999999999999, 1.3015999999999999, 1.3016999999999999, 1.3017999999999998, 1.3018999999999998, 1.3019999999999998, 1.3021, 1.3022, 1.3023, 1.3024, 1.3025, 1.3026, 1.3027, 1.3028, 1.3029, 1.303, 1.3031, 1.3032, 1.3033, 1.3034, 1.3034999999999999, 1.3035999999999999, 1.3036999999999999, 1.3037999999999998, 1.3038999999999998, 1.3039999999999998, 1.3041, 1.3042, 1.3043, 1.3044, 1.3045, 1.3046, 1.3047, 1.3048, 1.3049], "close": [1.302, 1.3021, 1.3022, 1.3023, 1.3024, 1.3025, 1.3026, 1.3027, 1.3028, 1.3029, 1.303, 1.3031000000000001, 1.3032000000000001, 1.3033000000000001, 1.3034000000000001, 1.3035, 1.3036, 1.3037, 1.3038, 1.3039, 1.304, 1.3041, 1.3042, 1.3043, 1.3044, 1.3045, 1.3046, 1.3047, 1.3048, 1.3049, 1.305, 1.3051000000000001, 1.3052000000000001, 1.3053000000000001, 1.3054000000000001, 1.3055, 1.3056, 1.3057, 1.3058, 1.3059, 1.306, 1.3061, 1.3062, 1.3063, 1.3064, 1.3065, 1.3066, 1.3067, 1.3068, 1.3069, 1.307, 1.3071000000000002, 1.3072000000000001, 1.3073000000000001, 1.3074000000000001, 1.3075, 1.3076, 1.3077, 1.3078, 1.3079, 1.308, 1.3081, 1.3082, 1.3083, 1.3084, 1.3085, 1.3086, 1.3087, 1.3088, 1.3089, 1.309, 1.3091000000000002, 1.3092000000000001, 1.3093000000000001, 1.3094000000000001, 1.3095, 1.3096, 1.3097, 1.3098, 1.3099, 1.31, 1.3101, 1.3102, 1.3103, 1.3104, 1.3105, 1.3106, 1.3107, 1.3108, 1.3109, 1.311, 1.3111000000000002, 1.3112000000000001, 1.3113000000000001, 1.3114000000000001, 1.3115, 1.3116, 1.3117, 1.3118, 1.3119], "volume": [800, 808, 816, 824, 832, 840, 848, 856, 864, 872, 880, 888, 896, 904, 912, 920, 928, 936, 944, 952, 960, 968, 976, 984, 992, 1000, 1008, 1016, 1024, 1032, 1040, 1048, 1056, 1064, 1072, 1080, 1088, 1096, 1104, 1112, 1120, 1128, 1136, 1144, 1152, 1160, 1168, 1176, 1184, 1192, 1200, 1208, 1216, 1224, 1232, 1240, 1248, 1256, 1264, 1272, 1280, 1288, 1296, 1304, 1312, 1320, 1328, 1336, 1344, 1352, 1360, 1368, 1376, 1384, 1392, 1400, 1408, 1416, 1424, 1432, 1440, 1448, 1456, 1464, 1472, 1480, 1488, 1496, 1504, 1512, 1520, 1528, 1536, 1544, 1552, 1560, 1568, 1576, 1584, 1592]}