# src/risk/risk_manager.py
"""
Risk Management System

This module provides risk management functionality for trading strategies.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from pydantic import BaseModel, Field

# Import if available, otherwise use placeholders
try:
    from ..events.event_bus import EventBus
    from .models import Position
    from .correlation_analyzer import CorrelationAnalyzer, CorrelationRisk
    LEGACY_IMPORTS = True
except ImportError:
    LEGACY_IMPORTS = False
    # Define placeholder classes if imports not available
    @dataclass
    class Position:
        symbol: str
        type: str
        lot: float
        price: float
        stop_loss: Optional[float] = None
        take_profit: Optional[float] = None
        
    class CorrelationAnalyzer:
        def analyze(self, *args, **kwargs):
            return []
    
    class CorrelationRisk:
        level: str = "LOW"
        score: float = 0.0

# Configure logging
logger = logging.getLogger(__name__)

@dataclass
class PortfolioState:
    balance: float
    equity: float
    positions: List[Position]
    total_exposure: float
    max_equity: Optional[float] = None
    margin_used: Optional[float] = None
    free_margin: Optional[float] = None

@dataclass
class RiskLimits:
    max_drawdown_percent: float = 20.0
    max_daily_loss_percent: float = 5.0
    max_position_size_percent: float = 10.0
    max_total_exposure: float = 10.0  # 10x leverage max
    max_correlation_risk: float = 0.8
    min_free_margin_percent: float = 30.0

@dataclass
class RiskEvaluation:
    approved: bool
    violations: List[str]
    warnings: List[str]
    risk_score: float  # 0-1, where 1 is maximum risk
    recommended_actions: List[str]
    critical: bool

class RiskManager:
    """Advanced risk management system with real-time monitoring"""
    
    def __init__(self, event_bus: Optional[EventBus] = None):
        self.logger = logger
        self.event_bus = event_bus
        self.limits = RiskLimits()
        self.correlation_analyzer = CorrelationAnalyzer()
        
        # Risk monitoring state
        self._daily_pnl_history: List[Dict[str, Any]] = []
        self._risk_alerts_sent: Dict[str, datetime] = {}
        self._alert_cooldown_minutes = 15
        
        self._setup_risk_monitoring()


class RiskManager2:
    """
    Enhanced Risk Management System for the MVP
    
    This class provides risk management functionality for trading strategies,
    including position sizing, risk limits, and drawdown protection.
    """
    
    def __init__(self, 
                mt5_bridge,
                risk_per_trade: float = 0.02,
                max_risk_per_symbol: float = 0.05,
                max_total_risk: float = 0.2,
                max_drawdown: float = 0.1,
                position_sizing_method: str = "fixed_risk"):
        """
        Initialize the Risk Manager
        
        Args:
            mt5_bridge: MT5 Bridge instance
            risk_per_trade: Risk per trade as a fraction of account balance (0.02 = 2%)
            max_risk_per_symbol: Maximum risk per symbol as a fraction of account balance
            max_total_risk: Maximum total risk as a fraction of account balance
            max_drawdown: Maximum allowed drawdown as a fraction of account balance
            position_sizing_method: Method for position sizing ("fixed_risk", "fixed_lot", "kelly")
        """
        self.mt5_bridge = mt5_bridge
        self.risk_per_trade = risk_per_trade
        self.max_risk_per_symbol = max_risk_per_symbol
        self.max_total_risk = max_total_risk
        self.max_drawdown = max_drawdown
        self.position_sizing_method = position_sizing_method
        
        # Risk state
        self.current_risk = 0.0
        self.risk_by_symbol = {}
        self.initial_balance = 10000.0  # Default value, will be updated
        self.peak_balance = 10000.0  # Default value, will be updated
        self.current_drawdown = 0.0
        
        # Trade history for performance tracking
        self.trade_history = []
        
        logger.info(f"Risk Manager initialized with risk_per_trade={risk_per_trade}, "
                   f"max_total_risk={max_total_risk}, max_drawdown={max_drawdown}")
    
    def update(self) -> None:
        """Update risk state based on current positions"""
        try:
            # Get account info
            account_info = self._get_account_info()
            
            # Update initial balance if not set
            if self.initial_balance == 10000.0:
                self.initial_balance = account_info["balance"]
                self.peak_balance = account_info["balance"]
            
            # Update peak balance
            if account_info["balance"] > self.peak_balance:
                self.peak_balance = account_info["balance"]
            
            # Calculate current drawdown
            self.current_drawdown = 1.0 - (account_info["balance"] / self.peak_balance)
            
            # Get current positions
            positions = self.mt5_bridge.get_positions()
            
            # Calculate current risk
            self.current_risk = 0.0
            self.risk_by_symbol = {}
            
            for position in positions:
                symbol = position["symbol"]
                position_risk = self._calculate_position_risk(position)
                
                # Update risk by symbol
                if symbol not in self.risk_by_symbol:
                    self.risk_by_symbol[symbol] = 0.0
                
                self.risk_by_symbol[symbol] += position_risk
                self.current_risk += position_risk
            
            logger.debug(f"Current risk: {self.current_risk:.2%}, Drawdown: {self.current_drawdown:.2%}")
            
        except Exception as e:
            logger.error(f"Error updating risk state: {str(e)}")
    
    def check_trade(self, 
                   symbol: str, 
                   order_type: str, 
                   lot: float, 
                   stop_loss: Optional[float] = None,
                   take_profit: Optional[float] = None) -> Dict[str, Any]:
        """
        Check if a trade is allowed based on risk parameters
        
        Args:
            symbol: Trading symbol
            order_type: Order type ("BUY" or "SELL")
            lot: Order volume
            stop_loss: Stop loss price
            take_profit: Take profit price
        
        Returns:
            Dict: Result with keys:
                - allowed: Whether the trade is allowed
                - reason: Reason if not allowed
                - adjusted_lot: Adjusted lot size if needed
        """
        try:
            # Calculate trade risk
            trade_risk = self._calculate_trade_risk(symbol, order_type, lot, stop_loss)
            
            # Check if we're in max drawdown
            if self.current_drawdown >= self.max_drawdown:
                return {
                    "allowed": False,
                    "reason": f"Maximum drawdown reached: {self.current_drawdown:.2%}",
                    "adjusted_lot": 0.0
                }
            
            # Calculate symbol risk after this trade
            symbol_risk = self.risk_by_symbol.get(symbol, 0.0) + trade_risk
            
            # Check if symbol risk is too high
            if symbol_risk > self.max_risk_per_symbol:
                # Calculate adjusted lot size
                max_additional_risk = self.max_risk_per_symbol - self.risk_by_symbol.get(symbol, 0.0)
                adjusted_lot = lot * (max_additional_risk / trade_risk) if trade_risk > 0 else 0.0
                
                return {
                    "allowed": False,
                    "reason": f"Symbol risk too high: {symbol_risk:.2%} > {self.max_risk_per_symbol:.2%}",
                    "adjusted_lot": adjusted_lot
                }
            
            # Calculate total risk after this trade
            total_risk = self.current_risk + trade_risk
            
            # Check if total risk is too high
            if total_risk > self.max_total_risk:
                # Calculate adjusted lot size
                max_additional_risk = self.max_total_risk - self.current_risk
                adjusted_lot = lot * (max_additional_risk / trade_risk) if trade_risk > 0 else 0.0
                
                return {
                    "allowed": False,
                    "reason": f"Total risk too high: {total_risk:.2%} > {self.max_total_risk:.2%}",
                    "adjusted_lot": adjusted_lot
                }
            
            # Trade is allowed
            return {
                "allowed": True,
                "reason": "Trade allowed",
                "adjusted_lot": lot
            }
            
        except Exception as e:
            logger.error(f"Error checking trade: {str(e)}")
            return {
                "allowed": False,
                "reason": f"Error checking trade: {str(e)}",
                "adjusted_lot": 0.0
            }
    
    def calculate_position_size(self, 
                              symbol: str, 
                              order_type: str, 
                              stop_loss: Optional[float] = None,
                              risk_amount: Optional[float] = None) -> float:
        """
        Calculate position size based on risk parameters
        
        Args:
            symbol: Trading symbol
            order_type: Order type ("BUY" or "SELL")
            stop_loss: Stop loss price
            risk_amount: Risk amount as a fraction of account balance (overrides risk_per_trade)
        
        Returns:
            float: Position size in lots
        """
        try:
            # Get account info
            account_info = self._get_account_info()
            balance = account_info["balance"]
            
            # Use provided risk amount or default
            risk_amount = risk_amount if risk_amount is not None else self.risk_per_trade
            
            # Calculate risk in currency
            risk_currency = balance * risk_amount
            
            # If no stop loss, use a default value
            if stop_loss is None:
                # Default to 1% of current price
                current_price = self._get_current_price(symbol)
                if order_type == "BUY":
                    stop_loss = current_price * 0.99
                else:  # SELL
                    stop_loss = current_price * 1.01
            
            # Calculate stop loss distance
            current_price = self._get_current_price(symbol)
            if order_type == "BUY":
                stop_distance = current_price - stop_loss
            else:  # SELL
                stop_distance = stop_loss - current_price
            
            # Ensure stop distance is positive
            stop_distance = abs(stop_distance)
            
            # Calculate position size based on method
            if self.position_sizing_method == "fixed_risk":
                # Position size = Risk amount / (Stop distance * Pip value)
                # For simplicity, we'll assume 1 pip = 0.0001 and pip value = lot * 10
                pip_value = 10.0
                position_size = risk_currency / (stop_distance * 10000 * pip_value)
                
            elif self.position_sizing_method == "fixed_lot":
                # Fixed lot size (ignores risk parameters)
                position_size = 0.1
                
            elif self.position_sizing_method == "kelly":
                # Kelly criterion (requires win rate and risk/reward ratio)
                # For simplicity, we'll use fixed values
                win_rate = 0.5
                risk_reward = 2.0
                kelly_fraction = win_rate - ((1 - win_rate) / risk_reward)
                position_size = (kelly_fraction * risk_currency) / (stop_distance * 10000 * 10.0)
                
            else:
                # Default to fixed risk
                pip_value = 10.0
                position_size = risk_currency / (stop_distance * 10000 * pip_value)
            
            # Round to 2 decimal places and ensure minimum lot size
            position_size = round(max(position_size, 0.01), 2)
            
            logger.debug(f"Calculated position size: {position_size} lots for {symbol} {order_type}")
            return position_size
            
        except Exception as e:
            logger.error(f"Error calculating position size: {str(e)}")
            return 0.01  # Minimum lot size as fallback
    
    def _calculate_trade_risk(self, 
                            symbol: str, 
                            order_type: str, 
                            lot: float, 
                            stop_loss: Optional[float] = None) -> float:
        """
        Calculate the risk of a trade as a fraction of account balance
        
        Args:
            symbol: Trading symbol
            order_type: Order type ("BUY" or "SELL")
            lot: Order volume
            stop_loss: Stop loss price
        
        Returns:
            float: Risk as a fraction of account balance
        """
        # Get account info
        account_info = self._get_account_info()
        balance = account_info["balance"]
        
        # If no stop loss, use a default value
        if stop_loss is None:
            # Default to 1% of current price
            current_price = self._get_current_price(symbol)
            if order_type == "BUY":
                stop_loss = current_price * 0.99
            else:  # SELL
                stop_loss = current_price * 1.01
        
        # Calculate stop loss distance
        current_price = self._get_current_price(symbol)
        if order_type == "BUY":
            stop_distance = current_price - stop_loss
        else:  # SELL
            stop_distance = stop_loss - current_price
        
        # Ensure stop distance is positive
        stop_distance = abs(stop_distance)
        
        # Calculate potential loss
        # For simplicity, we'll assume 1 pip = 0.0001 and pip value = lot * 10
        pip_value = 10.0
        potential_loss = stop_distance * 10000 * lot * pip_value
        
        # Calculate risk as fraction of balance
        risk = potential_loss / balance
        
        return risk
    
    def _calculate_position_risk(self, position: Dict[str, Any]) -> float:
        """
        Calculate the risk of an open position
        
        Args:
            position: Position information
        
        Returns:
            float: Risk as a fraction of account balance
        """
        symbol = position["symbol"]
        order_type = position["type"]
        lot = position["lot"]
        
        # Get stop loss from position or use a default value
        stop_loss = position.get("stop_loss")
        
        return self._calculate_trade_risk(symbol, order_type, lot, stop_loss)
    
    def _get_account_info(self) -> Dict[str, Any]:
        """
        Get account information
        
        Returns:
            Dict: Account information
        """
        # For offline mode, we'll use a simple account info structure
        if hasattr(self.mt5_bridge, "get_account_info"):
            return self.mt5_bridge.get_account_info()
        
        # Default account info
        return {
            "balance": 10000.0,
            "equity": 10000.0,
            "margin": 0.0,
            "free_margin": 10000.0,
            "margin_level": 100.0,
            "leverage": 100.0
        }
    
    def _get_current_price(self, symbol: str) -> float:
        """
        Get current price for a symbol
        
        Args:
            symbol: Trading symbol
        
        Returns:
            float: Current price
        """
        # For offline mode, we'll use a simple price lookup
        if hasattr(self.mt5_bridge, "current_prices") and symbol in self.mt5_bridge.current_prices:
            return self.mt5_bridge.current_prices[symbol]
        
        # Default prices for common symbols
        default_prices = {
            "EURUSD": 1.1,
            "USDJPY": 110.0,
            "GBPUSD": 1.3,
            "AUDUSD": 0.7,
            "USDCAD": 1.25
        }
        
        return default_prices.get(symbol, 1.0)
    
    def record_trade(self, trade: Dict[str, Any]) -> None:
        """
        Record a trade for performance tracking
        
        Args:
            trade: Trade information
        """
        self.trade_history.append(trade)
        
        # Update risk state
        self.update()
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Get performance metrics
        
        Returns:
            Dict: Performance metrics
        """
        if not self.trade_history:
            return {
                "total_trades": 0,
                "winning_trades": 0,
                "losing_trades": 0,
                "win_rate": 0.0,
                "average_win": 0.0,
                "average_loss": 0.0,
                "profit_factor": 0.0,
                "expected_payoff": 0.0,
                "max_drawdown": 0.0
            }
        
        # Calculate metrics
        total_trades = len(self.trade_history)
        winning_trades = sum(1 for trade in self.trade_history if trade.get("profit", 0) > 0)
        losing_trades = sum(1 for trade in self.trade_history if trade.get("profit", 0) <= 0)
        
        win_rate = winning_trades / total_trades if total_trades > 0 else 0.0
        
        # Calculate average win and loss
        wins = [trade.get("profit", 0) for trade in self.trade_history if trade.get("profit", 0) > 0]
        losses = [trade.get("profit", 0) for trade in self.trade_history if trade.get("profit", 0) <= 0]
        
        average_win = sum(wins) / len(wins) if wins else 0.0
        average_loss = sum(losses) / len(losses) if losses else 0.0
        
        # Calculate profit factor
        total_profit = sum(wins)
        total_loss = abs(sum(losses))
        profit_factor = total_profit / total_loss if total_loss > 0 else 0.0
        
        # Calculate expected payoff
        expected_payoff = (win_rate * average_win) - ((1 - win_rate) * abs(average_loss))
        
        return {
            "total_trades": total_trades,
            "winning_trades": winning_trades,
            "losing_trades": losing_trades,
            "win_rate": win_rate,
            "average_win": average_win,
            "average_loss": average_loss,
            "profit_factor": profit_factor,
            "expected_payoff": expected_payoff,
            "max_drawdown": self.current_drawdown
        }
    
    def _setup_risk_monitoring(self) -> None:
        """Setup continuous risk monitoring"""
        if self.event_bus:
            # Subscribe to relevant events for real-time risk monitoring
            self.event_bus.subscribe('POSITION_OPENED', self._on_position_opened)
            self.event_bus.subscribe('POSITION_CLOSED', self._on_position_closed)
            self.event_bus.subscribe('MARKET_DATA_RECEIVED', self._on_market_data_received)
        
        self.logger.info("Risk monitoring system initialized")
    
    async def evaluate_new_position(
        self, 
        portfolio: PortfolioState, 
        new_position: Dict[str, Any]
    ) -> RiskEvaluation:
        """Evaluate risk for a new position"""
        violations = []
        warnings = []
        risk_score = 0.0
        
        symbol = new_position['symbol']
        size = new_position['size']
        estimated_margin = new_position.get('estimated_margin', 0)
        
        # Check position size limit
        position_risk = abs(size) / portfolio.balance
        if position_risk > self.limits.max_position_size_percent / 100:
            violations.append('MAX_POSITION_SIZE_EXCEEDED')
            risk_score += 0.3
            self.logger.warning(f"Position size risk exceeded for {symbol}: {position_risk:.2%}")
        
        # Check total exposure
        new_total_exposure = portfolio.total_exposure + abs(size)
        if new_total_exposure > self.limits.max_total_exposure:
            violations.append('MAX_EXPOSURE_EXCEEDED')
            risk_score += 0.4
            self.logger.warning(f"Total exposure limit exceeded: {new_total_exposure}")
        
        # Check margin requirements
        current_margin_used = portfolio.margin_used or 0
        new_margin_used = current_margin_used + estimated_margin
        margin_utilization = new_margin_used / portfolio.balance
        required_free_margin = self.limits.min_free_margin_percent / 100
        
        if margin_utilization > (1 - required_free_margin):
            violations.append('INSUFFICIENT_MARGIN')
            risk_score += 0.3
            self.logger.warning(f"Insufficient margin: {margin_utilization:.2%}")
        
        # Check correlation risk
        test_position = Position(
            symbol=symbol,
            size=size,
            unrealized_pnl=0,
            timestamp=datetime.now()
        )
        test_positions = portfolio.positions + [test_position]
        correlation_risk = self.correlation_analyzer.calculate_portfolio_correlation_risk(test_positions)
        
        if correlation_risk.overall_score > self.limits.max_correlation_risk:
            warnings.append('HIGH_CORRELATION_RISK')
            risk_score += 0.2
            self.logger.warning(f"High correlation risk: {correlation_risk.overall_score:.2f}")
        
        # Generate recommendations
        recommended_actions = self._generate_recommendations(violations, warnings, correlation_risk)
        
        evaluation = RiskEvaluation(
            approved=len(violations) == 0,
            violations=violations,
            warnings=warnings,
            risk_score=min(1.0, risk_score),
            recommended_actions=recommended_actions,
            critical='MAX_EXPOSURE_EXCEEDED' in violations or 'INSUFFICIENT_MARGIN' in violations
        )
        
        # Log evaluation
        self.logger.info(
            f"Position risk evaluation for {symbol}: "
            f"approved={evaluation.approved}, risk_score={evaluation.risk_score:.2f}, "
            f"violations={len(violations)}, warnings={len(warnings)}"
        )
        
        return evaluation
    
    async def evaluate_portfolio_risk(self, portfolio: PortfolioState) -> RiskEvaluation:
        """Evaluate overall portfolio risk"""
        violations = []
        warnings = []
        risk_score = 0.0
        
        # Drawdown check
        max_equity = portfolio.max_equity or portfolio.balance
        current_drawdown = (max_equity - portfolio.equity) / max_equity
        
        if current_drawdown > self.limits.max_drawdown_percent / 100:
            violations.append('MAX_DRAWDOWN_EXCEEDED')
            risk_score += 0.5
            self.logger.critical(f"Maximum drawdown exceeded: {current_drawdown:.2%}")
            
            # Send critical risk alert
            await self._send_risk_alert('CRITICAL', 'MAX_DRAWDOWN', current_drawdown, portfolio)
        
        # Daily loss check
        daily_loss = self._calculate_daily_loss(portfolio)
        if daily_loss > self.limits.max_daily_loss_percent / 100:
            violations.append('MAX_DAILY_LOSS_EXCEEDED')
            risk_score += 0.4
            self.logger.error(f"Daily loss limit exceeded: {daily_loss:.2%}")
        
        # Margin check
        if portfolio.margin_used and portfolio.balance > 0:
            margin_utilization = portfolio.margin_used / portfolio.balance
            if margin_utilization > 0.9:  # 90% margin used
                warnings.append('HIGH_MARGIN_UTILIZATION')
                risk_score += 0.2
        
        # Correlation risk
        correlation_risk = self.correlation_analyzer.calculate_portfolio_correlation_risk(portfolio.positions)
        if correlation_risk.overall_score > self.limits.max_correlation_risk:
            warnings.append('HIGH_CORRELATION_RISK')
            risk_score += 0.2
        
        # Generate recommendations
        recommended_actions = self._generate_recommendations(violations, warnings, correlation_risk)
        
        # Add critical actions for severe violations
        if 'MAX_DRAWDOWN_EXCEEDED' in violations:
            recommended_actions.insert(0, 'CLOSE_ALL_POSITIONS')
            recommended_actions.insert(1, 'STOP_NEW_TRADING')
        
        evaluation = RiskEvaluation(
            approved=len(violations) == 0,
            violations=violations,
            warnings=warnings,
            risk_score=min(1.0, risk_score),
            recommended_actions=recommended_actions,
            critical=len(violations) > 0
        )
        
        return evaluation
    
    def calculate_correlation_risk(self, portfolio: PortfolioState) -> CorrelationRisk:
        """Calculate correlation risk for portfolio"""
        return self.correlation_analyzer.calculate_portfolio_correlation_risk(portfolio.positions)
    
    def _calculate_daily_loss(self, portfolio: PortfolioState) -> float:
        """Calculate daily loss percentage"""
        # This would typically track P&L from start of trading day
        # For now, use a simple approximation based on unrealized P&L
        total_unrealized_pnl = sum(pos.unrealized_pnl for pos in portfolio.positions)
        
        if total_unrealized_pnl >= 0:
            return 0.0
        
        return abs(total_unrealized_pnl) / portfolio.balance
    
    def _generate_recommendations(
        self, 
        violations: List[str], 
        warnings: List[str], 
        correlation_risk: CorrelationRisk
    ) -> List[str]:
        """Generate risk management recommendations"""
        recommendations = []
        
        if 'MAX_POSITION_SIZE_EXCEEDED' in violations:
            recommendations.append('REDUCE_POSITION_SIZE')
        
        if 'MAX_EXPOSURE_EXCEEDED' in violations:
            recommendations.append('CLOSE_SOME_POSITIONS')
        
        if 'INSUFFICIENT_MARGIN' in violations:
            recommendations.append('ADD_FUNDS_OR_CLOSE_POSITIONS')
        
        if 'HIGH_CORRELATION_RISK' in warnings:
            recommendations.append('DIVERSIFY_PORTFOLIO')
            # Add specific diversification suggestions
            diversification_suggestions = self.correlation_analyzer.get_diversification_suggestions([])
            recommendations.extend(diversification_suggestions[:2])  # Add top 2 suggestions
        
        if 'HIGH_MARGIN_UTILIZATION' in warnings:
            recommendations.append('REDUCE_LEVERAGE')
        
        return recommendations
    
    async def _send_risk_alert(
        self, 
        severity: str, 
        limit_type: str, 
        current_value: float, 
        portfolio: PortfolioState
    ) -> None:
        """Send risk alert through event bus"""
        if not self.event_bus:
            return
        
        # Check cooldown to prevent spam
        alert_key = f"{severity}_{limit_type}"
        last_alert = self._risk_alerts_sent.get(alert_key)
        
        if last_alert:
            time_since_last = datetime.now() - last_alert
            if time_since_last.total_seconds() < self._alert_cooldown_minutes * 60:
                return  # Still in cooldown
        
        # Create risk alert event
        alert_event = {
            'type': 'RISK_LIMIT_BREACHED',
            'timestamp': datetime.now().isoformat(),
            'payload': {
                'severity': severity,
                'limit_type': limit_type,
                'current_value': current_value,
                'limit_value': getattr(self.limits, f"max_{limit_type.lower()}_percent", 0) / 100,
                'portfolio_state': {
                    'balance': portfolio.balance,
                    'equity': portfolio.equity,
                    'positions_count': len(portfolio.positions),
                    'total_exposure': portfolio.total_exposure
                },
                'recommended_actions': self._get_emergency_actions(severity, limit_type)
            }
        }
        
        try:
            await self.event_bus.publish(alert_event)
            self._risk_alerts_sent[alert_key] = datetime.now()
            self.logger.info(f"Risk alert sent: {severity} {limit_type}")
        except Exception as e:
            self.logger.error(f"Failed to send risk alert: {str(e)}")
    
    def _get_emergency_actions(self, severity: str, limit_type: str) -> List[str]:
        """Get emergency actions for critical risk situations"""
        if severity == 'CRITICAL':
            return [
                'STOP_ALL_TRADING',
                'CLOSE_ALL_POSITIONS',
                'NOTIFY_RISK_MANAGER',
                'REVIEW_RISK_LIMITS'
            ]
        elif severity == 'HIGH':
            return [
                'REDUCE_POSITION_SIZES',
                'CLOSE_LOSING_POSITIONS',
                'INCREASE_MONITORING'
            ]
        else:
            return [
                'MONITOR_CLOSELY',
                'CONSIDER_POSITION_ADJUSTMENT'
            ]
    
    async def _on_position_opened(self, event: Any) -> None:
        """Handle position opened event"""
        self.logger.debug("Position opened event received")
        # Could trigger real-time risk re-evaluation
    
    async def _on_position_closed(self, event: Any) -> None:
        """Handle position closed event"""
        self.logger.debug("Position closed event received")
        # Could update risk metrics
    
    async def _on_market_data_received(self, event: Any) -> None:
        """Handle market data event"""
        # Could trigger mark-to-market updates and risk re-evaluation
        pass
    
    def update_risk_limits(self, new_limits: RiskLimits) -> None:
        """Update risk limits"""
        old_limits = self.limits
        self.limits = new_limits
        
        self.logger.info(
            f"Risk limits updated: "
            f"max_drawdown={new_limits.max_drawdown_percent}% "
            f"(was {old_limits.max_drawdown_percent}%), "
            f"max_exposure={new_limits.max_total_exposure} "
            f"(was {old_limits.max_total_exposure})"
        )
    
    def get_risk_limits(self) -> RiskLimits:
        """Get current risk limits"""
        return self.limits
    
    def get_risk_metrics(self, portfolio: PortfolioState) -> Dict[str, Any]:
        """Get comprehensive risk metrics"""
        correlation_risk = self.calculate_correlation_risk(portfolio)
        
        max_equity = portfolio.max_equity or portfolio.balance
        current_drawdown = (max_equity - portfolio.equity) / max_equity if max_equity > 0 else 0
        
        return {
            'current_drawdown': current_drawdown,
            'daily_loss': self._calculate_daily_loss(portfolio),
            'margin_utilization': (portfolio.margin_used / portfolio.balance) if portfolio.margin_used and portfolio.balance > 0 else 0,
            'total_exposure': portfolio.total_exposure,
            'correlation_risk': correlation_risk.overall_score,
            'diversification_score': correlation_risk.diversification_score,
            'positions_count': len(portfolio.positions),
            'risk_limits': {
                'max_drawdown': self.limits.max_drawdown_percent / 100,
                'max_daily_loss': self.limits.max_daily_loss_percent / 100,
                'max_exposure': self.limits.max_total_exposure,
                'max_correlation': self.limits.max_correlation_risk
            }
        }