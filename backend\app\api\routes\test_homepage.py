import pytest
from fastapi.testclient import TestClient
from main import app  # Your FastAPI app
from unittest.mock import patch

client = TestClient(app)

def test_homepage_returns_200():
    """Test homepage loads successfully"""
    response = client.get("/")
    assert response.status_code == 200

def test_homepage_contains_core_sections():
    """Test homepage includes all required sections"""
    response = client.get("/")
    content = response.text
    assert "Build Strategies in Python" in content
    assert "MT5 EA Generation" in content
    assert "Professional Prompts" in content
    assert "number of users" not in content.lower()
    assert "reviews" not in content.lower()

def test_professional_prompts_displayed():
    """Test professional prompts are visible and clickable"""
    response = client.get("/")
    content = response.text
    assert "RSI Strategy" in content
    assert "Moving Average Crossover" in content
    assert "click to try" in content.lower()

def test_chatbot_integration_available():
    """Test chatbot access is available from homepage"""
    response = client.get("/")
    content = response.text
    assert "chat" in content.lower()
    assert "build strategy" in content.lower()

def test_mt5_integration_status_displayed():
    """Test MT5 connection status is visible"""
    with patch('main.mt5_bridge.is_connected') as mock_connected:
        mock_connected.return_value = True
        response = client.get("/")
        content = response.text
        assert "MT5 Connected" in content
