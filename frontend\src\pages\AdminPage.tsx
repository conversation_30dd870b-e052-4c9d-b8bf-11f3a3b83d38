/**
 * Admin Page
 * Administrator dashboard and controls
 */

// import React from 'react'; // Not needed with new JSX transform
import { Shield } from 'lucide-react';

export function AdminPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
        <p className="text-gray-600 mt-2">
          System administration and user management
        </p>
      </div>

      {/* Coming Soon Placeholder */}
      <div className="card p-12 text-center">
        <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Shield className="w-8 h-8 text-purple-600" />
        </div>
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          Admin Panel Coming Soon
        </h2>
        <p className="text-gray-600 max-w-md mx-auto">
          Comprehensive admin tools for user management, 
          system monitoring, and platform administration 
          are being developed.
        </p>
      </div>
    </div>
  );
}