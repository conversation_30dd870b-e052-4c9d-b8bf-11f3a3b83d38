#!/usr/bin/env python3
"""
Test History Tracker

This script analyzes test reports over time to track test stability and identify flaky tests.
"""

import json
import os
import glob
from datetime import datetime
import matplotlib.pyplot as plt
from collections import defaultdict


class TestHistoryTracker:
    def __init__(self, reports_dir="reports"):
        self.reports_dir = reports_dir
        self.history = defaultdict(list)
        self.flaky_tests = []
        self.stable_tests = []
    
    def load_reports(self):
        """Load all test reports from the reports directory"""
        report_files = glob.glob(os.path.join(self.reports_dir, "test_report_*.json"))
        
        for report_file in sorted(report_files):
            try:
                with open(report_file, 'r') as f:
                    report_data = json.load(f)
                
                # Extract timestamp from filename
                filename = os.path.basename(report_file)
                timestamp_str = filename.replace("test_report_", "").replace(".json", "")
                timestamp = datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")
                
                # Process report data
                self._process_report(report_data, timestamp)
            except Exception as e:
                print(f"Error processing {report_file}: {e}")
    
    def _process_report(self, report_data, timestamp):
        """Process a single test report"""
        # This implementation depends on the structure of your test reports
        # Adjust as needed based on your actual report format
        
        # Example implementation assuming report contains test results by name
        if 'results' in report_data and 'tests' in report_data['results']:
            for test_name, test_result in report_data['results']['tests'].items():
                self.history[test_name].append({
                    'timestamp': timestamp,
                    'passed': test_result.get('passed', False),
                    'duration': test_result.get('duration', 0)
                })
    
    def identify_flaky_tests(self, threshold=0.9):
        """Identify flaky tests (tests that sometimes pass, sometimes fail)"""
        self.flaky_tests = []
        self.stable_tests = []
        
        for test_name, results in self.history.items():
            if len(results) < 3:
                # Not enough data to determine flakiness
                continue
            
            # Calculate pass rate
            pass_count = sum(1 for r in results if r['passed'])
            pass_rate = pass_count / len(results)
            
            # Tests with pass rate between 0.1 and threshold are considered flaky
            if 0.1 < pass_rate < threshold:
                self.flaky_tests.append({
                    'name': test_name,
                    'pass_rate': pass_rate,
                    'run_count': len(results)
                })
            elif pass_rate >= threshold:
                self.stable_tests.append({
                    'name': test_name,
                    'pass_rate': pass_rate,
                    'run_count': len(results)
                })
    
    def generate_report(self):
        """Generate a report of test history and flaky tests"""
        if not self.history:
            self.load_reports()
        
        self.identify_flaky_tests()
        
        print(f"📊 Test History Analysis")
        print(f"========================")
        print(f"Total tests tracked: {len(self.history)}")
        print(f"Stable tests: {len(self.stable_tests)}")
        print(f"Flaky tests: {len(self.flaky_tests)}")
        
        if self.flaky_tests:
            print("\n⚠️ Flaky Tests:")
            for test in sorted(self.flaky_tests, key=lambda x: x['pass_rate']):
                print(f"  - {test['name']}: {test['pass_rate']:.1%} pass rate ({test['run_count']} runs)")
        
        # Generate visualization if matplotlib is available
        try:
            self._generate_visualization()
        except Exception as e:
            print(f"Could not generate visualization: {e}")
    
    def _generate_visualization(self):
        """Generate visualizations of test history"""
        # Create a simple bar chart of flaky tests
        if not self.flaky_tests:
            return
        
        plt.figure(figsize=(10, 6))
        
        test_names = [t['name'].split('::')[-1] for t in self.flaky_tests[:10]]  # Show top 10
        pass_rates = [t['pass_rate'] for t in self.flaky_tests[:10]]
        
        plt.barh(test_names, pass_rates)
        plt.xlabel('Pass Rate')
        plt.title('Top Flaky Tests')
        plt.tight_layout()
        
        # Save the chart
        plt.savefig(os.path.join(self.reports_dir, 'flaky_tests.png'))
        print(f"\n📈 Visualization saved to {os.path.join(self.reports_dir, 'flaky_tests.png')}")


def main():
    tracker = TestHistoryTracker()
    tracker.generate_report()


if __name__ == "__main__":
    main()