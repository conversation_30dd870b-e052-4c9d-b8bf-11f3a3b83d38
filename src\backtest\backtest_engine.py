"""
Backtesting Engine

This module provides functionality for backtesting trading strategies.
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from pathlib import Path

from src.strategies.strategy_base import StrategyBase
from src.trading.mt5_bridge_tdd import MT5Bridge

# Configure logging
logger = logging.getLogger(__name__)


class BacktestBridge(MT5Bridge):
    """
    MT5 Bridge implementation for backtesting
    
    This class extends the MT5 Bridge to simulate trading during backtesting.
    It uses historical data to simulate order execution and market behavior.
    """
    
    def __init__(self, historical_data: Dict[str, pd.DataFrame]):
        """
        Initialize the Backtest Bridge
        
        Args:
            historical_data: Dictionary mapping symbols to DataFrames with historical data
                Each DataFrame should have columns: datetime, open, high, low, close, volume
        """
        super().__init__(offline_mode=True)
        
        self.historical_data = historical_data
        self.current_time = None
        self.current_prices = {}
        self.account_balance = 10000.0  # Initial balance
        self.equity = 10000.0  # Initial equity
        
        # Set the current time to the earliest time in the data
        for symbol, data in historical_data.items():
            if self.current_time is None or data.iloc[0]["datetime"] < self.current_time:
                self.current_time = data.iloc[0]["datetime"]
        
        logger.info(f"Backtest Bridge initialized with {len(historical_data)} symbols")
        logger.info(f"Backtest start time: {self.current_time}")
    
    def advance_time(self, time_delta: timedelta) -> None:
        """
        Advance the current time by a specified amount
        
        Args:
            time_delta: Time to advance
        """
        self.current_time += time_delta
        
        # Update current prices
        for symbol, data in self.historical_data.items():
            # Find the closest data point before or at the current time
            data_before = data[data["datetime"] <= self.current_time]
            if not data_before.empty:
                self.current_prices[symbol] = data_before.iloc[-1]["close"]
        
        # Update open positions
        self._update_positions()
        
        logger.debug(f"Advanced time to {self.current_time}")
    
    def _update_positions(self) -> None:
        """Update open positions based on current prices"""
        # Calculate profit/loss for each position
        total_profit = 0.0
        
        for position in self.get_positions():
            symbol = position["symbol"]
            if symbol in self.current_prices:
                current_price = self.current_prices[symbol]
                
                # Calculate profit/loss
                if position["type"] == "BUY":
                    profit = (current_price - position["price"]) * position["lot"] * 100000
                else:  # SELL
                    profit = (position["price"] - current_price) * position["lot"] * 100000
                
                # Update position profit
                position["profit"] = profit
                total_profit += profit
                
                # Check for stop loss or take profit
                if "stop_loss" in position and position["stop_loss"] is not None:
                    if (position["type"] == "BUY" and current_price <= position["stop_loss"]) or \
                       (position["type"] == "SELL" and current_price >= position["stop_loss"]):
                        # Stop loss hit
                        self.close_order(position["id"])
                        logger.info(f"Stop loss hit for {symbol} at {current_price}")
                
                if "take_profit" in position and position["take_profit"] is not None:
                    if (position["type"] == "BUY" and current_price >= position["take_profit"]) or \
                       (position["type"] == "SELL" and current_price <= position["take_profit"]):
                        # Take profit hit
                        self.close_order(position["id"])
                        logger.info(f"Take profit hit for {symbol} at {current_price}")
        
        # Update equity
        self.equity = self.account_balance + total_profit
    
    def place_order(self, 
                   symbol: str, 
                   order_type: str, 
                   lot: float, 
                   price: Optional[float] = None,
                   stop_loss: Optional[float] = None, 
                   take_profit: Optional[float] = None) -> int:
        """
        Place a trading order in the backtest
        
        Args:
            symbol: Trading symbol
            order_type: Order type ("BUY", "SELL", etc.)
            lot: Order volume
            price: Order price (for pending orders)
            stop_loss: Stop loss price
            take_profit: Take profit price
            
        Returns:
            int: Order ID
        """
        # Check if we have data for this symbol
        if symbol not in self.current_prices:
            raise ValueError(f"No data for symbol {symbol}")
        
        # Use current price if not specified
        if price is None:
            price = self.current_prices[symbol]
        
        # Call parent method to place the order
        order_id = super().place_order(
            symbol=symbol,
            order_type=order_type,
            lot=lot,
            price=price,
            stop_loss=stop_loss,
            take_profit=take_profit
        )
        
        # Add additional fields for backtesting
        for order in self.orders:
            if order["id"] == order_id:
                order["datetime"] = self.current_time
                order["stop_loss"] = stop_loss
                order["take_profit"] = take_profit
                order["profit"] = 0.0
        
        return order_id
    
    def close_order(self, order_id: int) -> bool:
        """
        Close an order in the backtest
        
        Args:
            order_id: Order ID
            
        Returns:
            bool: True if order was closed, False otherwise
        """
        # Find the order
        for order in self.orders:
            if order["id"] == order_id and order["status"] == "filled":
                # Calculate profit/loss
                symbol = order["symbol"]
                if symbol in self.current_prices:
                    current_price = self.current_prices[symbol]
                    
                    # Calculate profit/loss
                    if order["type"] == "BUY":
                        profit = (current_price - order["price"]) * order["lot"] * 100000
                    else:  # SELL
                        profit = (order["price"] - current_price) * order["lot"] * 100000
                    
                    # Update account balance
                    self.account_balance += profit
                    
                    # Update order
                    order["profit"] = profit
                    order["close_price"] = current_price
                    order["close_time"] = self.current_time
                    
                    logger.info(f"Closed order {order_id} with profit: {profit:.2f}")
        
        # Call parent method to close the order
        return super().close_order(order_id)
    
    def get_account_info(self) -> Dict[str, Any]:
        """
        Get account information
        
        Returns:
            Dict: Account information
        """
        return {
            "balance": self.account_balance,
            "equity": self.equity,
            "margin": 0.0,  # Not used in backtest
            "free_margin": self.equity,  # Same as equity in backtest
            "margin_level": 100.0,  # Not used in backtest
            "leverage": 100.0  # Default leverage
        }


class BacktestEngine:
    """
    Backtesting Engine
    
    This class provides functionality for backtesting trading strategies
    using historical data.
    """
    
    def __init__(self, 
                strategy: StrategyBase,
                historical_data: Dict[str, pd.DataFrame],
                start_date: Optional[datetime] = None,
                end_date: Optional[datetime] = None,
                initial_balance: float = 10000.0):
        """
        Initialize the Backtesting Engine
        
        Args:
            strategy: Trading strategy to backtest
            historical_data: Dictionary mapping symbols to DataFrames with historical data
            start_date: Start date for the backtest (if None, use earliest date in data)
            end_date: End date for the backtest (if None, use latest date in data)
            initial_balance: Initial account balance
        """
        self.strategy = strategy
        self.historical_data = historical_data
        
        # Determine start and end dates
        if start_date is None:
            self.start_date = min(data.iloc[0]["datetime"] for data in historical_data.values())
        else:
            self.start_date = start_date
        
        if end_date is None:
            self.end_date = max(data.iloc[-1]["datetime"] for data in historical_data.values())
        else:
            self.end_date = end_date
        
        # Create backtest bridge
        self.bridge = BacktestBridge(historical_data)
        self.bridge.account_balance = initial_balance
        self.bridge.equity = initial_balance
        
        # Replace strategy's bridge with our backtest bridge
        self.strategy.mt5_bridge = self.bridge
        
        # Results
        self.results = {
            "trades": [],
            "equity_curve": [],
            "performance_metrics": {}
        }
        
        logger.info(f"Backtest Engine initialized for {strategy.name}")
        logger.info(f"Backtest period: {self.start_date} to {self.end_date}")
    
    def run(self, update_interval: timedelta = timedelta(hours=1)) -> Dict[str, Any]:
        """
        Run the backtest
        
        Args:
            update_interval: Time interval between strategy updates
        
        Returns:
            Dict: Backtest results
        """
        logger.info(f"Starting backtest for {self.strategy.name}")
        
        # Set current time to start date
        self.bridge.current_time = self.start_date
        
        # Start the strategy
        self.strategy.start()
        
        # Initialize equity curve
        self.results["equity_curve"].append({
            "datetime": self.bridge.current_time,
            "equity": self.bridge.equity
        })
        
        # Run the backtest
        current_time = self.start_date
        while current_time <= self.end_date:
            # Update the strategy
            self.strategy.update()
            
            # Record equity
            self.results["equity_curve"].append({
                "datetime": current_time,
                "equity": self.bridge.equity
            })
            
            # Advance time
            current_time += update_interval
            self.bridge.advance_time(update_interval)
        
        # Stop the strategy
        self.strategy.stop()
        
        # Calculate performance metrics
        self._calculate_performance_metrics()
        
        logger.info(f"Backtest completed for {self.strategy.name}")
        logger.info(f"Final equity: {self.bridge.equity:.2f}")
        
        return self.results
    
    def _calculate_performance_metrics(self) -> None:
        """Calculate performance metrics from backtest results"""
        # Get closed trades
        closed_trades = [order for order in self.bridge.orders if order["status"] == "closed"]
        
        # Calculate metrics
        total_trades = len(closed_trades)
        winning_trades = sum(1 for trade in closed_trades if trade.get("profit", 0) > 0)
        losing_trades = sum(1 for trade in closed_trades if trade.get("profit", 0) <= 0)
        
        total_profit = sum(trade.get("profit", 0) for trade in closed_trades)
        
        # Calculate max drawdown
        equity_curve = pd.DataFrame(self.results["equity_curve"])
        if not equity_curve.empty:
            equity_curve["drawdown"] = equity_curve["equity"].cummax() - equity_curve["equity"]
            max_drawdown = equity_curve["drawdown"].max()
        else:
            max_drawdown = 0.0
        
        # Calculate Sharpe ratio (if we have enough data)
        if len(equity_curve) > 1:
            equity_curve["return"] = equity_curve["equity"].pct_change()
            sharpe_ratio = equity_curve["return"].mean() / equity_curve["return"].std() * np.sqrt(252)
        else:
            sharpe_ratio = 0.0
        
        # Store metrics
        self.results["performance_metrics"] = {
            "total_trades": total_trades,
            "winning_trades": winning_trades,
            "losing_trades": losing_trades,
            "win_rate": winning_trades / total_trades if total_trades > 0 else 0.0,
            "total_profit": total_profit,
            "max_drawdown": max_drawdown,
            "sharpe_ratio": sharpe_ratio,
            "profit_factor": sum(trade.get("profit", 0) for trade in closed_trades if trade.get("profit", 0) > 0) / 
                            abs(sum(trade.get("profit", 0) for trade in closed_trades if trade.get("profit", 0) < 0))
                            if abs(sum(trade.get("profit", 0) for trade in closed_trades if trade.get("profit", 0) < 0)) > 0 else 0.0
        }
        
        # Store trades
        self.results["trades"] = closed_trades
    
    def plot_results(self, save_path: Optional[str] = None) -> None:
        """
        Plot backtest results
        
        Args:
            save_path: Path to save the plot (if None, display the plot)
        """
        if not self.results["equity_curve"]:
            logger.warning("No equity curve data to plot")
            return
        
        # Convert to DataFrame
        equity_curve = pd.DataFrame(self.results["equity_curve"])
        
        # Create figure with subplots
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), gridspec_kw={'height_ratios': [3, 1]})
        
        # Plot equity curve
        ax1.plot(equity_curve["datetime"], equity_curve["equity"])
        ax1.set_title(f"Backtest Results: {self.strategy.name}")
        ax1.set_ylabel("Equity")
        ax1.grid(True)
        
        # Calculate drawdown
        equity_curve["drawdown"] = equity_curve["equity"].cummax() - equity_curve["equity"]
        
        # Plot drawdown
        ax2.fill_between(equity_curve["datetime"], 0, equity_curve["drawdown"], color='red', alpha=0.3)
        ax2.set_title("Drawdown")
        ax2.set_ylabel("Drawdown")
        ax2.set_xlabel("Date")
        ax2.grid(True)
        
        # Add performance metrics as text
        metrics = self.results["performance_metrics"]
        metrics_text = (
            f"Total Trades: {metrics['total_trades']}\n"
            f"Win Rate: {metrics['win_rate']:.2%}\n"
            f"Total Profit: ${metrics['total_profit']:.2f}\n"
            f"Max Drawdown: ${metrics['max_drawdown']:.2f}\n"
            f"Sharpe Ratio: {metrics['sharpe_ratio']:.2f}\n"
            f"Profit Factor: {metrics['profit_factor']:.2f}"
        )
        
        # Add text box with metrics
        props = dict(boxstyle='round', facecolor='wheat', alpha=0.5)
        ax1.text(0.05, 0.95, metrics_text, transform=ax1.transAxes, fontsize=10,
                verticalalignment='top', bbox=props)
        
        plt.tight_layout()
        
        # Save or show the plot
        if save_path:
            plt.savefig(save_path)
            logger.info(f"Plot saved to {save_path}")
        else:
            plt.show()


def load_csv_data(data_dir: str, symbols: List[str]) -> Dict[str, pd.DataFrame]:
    """
    Load historical data from CSV files
    
    Args:
        data_dir: Directory containing CSV files
        symbols: List of symbols to load
    
    Returns:
        Dict: Dictionary mapping symbols to DataFrames with historical data
    """
    data = {}
    
    for symbol in symbols:
        file_path = Path(data_dir) / f"{symbol}.csv"
        
        if file_path.exists():
            # Load data
            df = pd.read_csv(file_path)
            
            # Convert datetime column
            if "datetime" in df.columns:
                df["datetime"] = pd.to_datetime(df["datetime"])
            
            data[symbol] = df
            logger.info(f"Loaded data for {symbol}: {len(df)} rows")
        else:
            logger.warning(f"Data file not found for {symbol}: {file_path}")
    
    return data


def generate_sample_data(symbols: List[str], 
                        start_date: datetime, 
                        end_date: datetime,
                        interval: timedelta = timedelta(hours=1)) -> Dict[str, pd.DataFrame]:
    """
    Generate sample historical data for backtesting
    
    Args:
        symbols: List of symbols to generate data for
        start_date: Start date for the data
        end_date: End date for the data
        interval: Time interval between data points
    
    Returns:
        Dict: Dictionary mapping symbols to DataFrames with historical data
    """
    data = {}
    
    for symbol in symbols:
        # Generate datetime index
        current_date = start_date
        dates = []
        
        while current_date <= end_date:
            dates.append(current_date)
            current_date += interval
        
        # Generate price data
        base_price = 1.0 if symbol == "EURUSD" else 100.0
        prices = [base_price]
        
        # Generate random walk
        for _ in range(1, len(dates)):
            # Random price change between -0.5% and +0.5%
            change = prices[-1] * (1 + (np.random.random() - 0.5) * 0.01)
            prices.append(change)
        
        # Create DataFrame
        df = pd.DataFrame({
            "datetime": dates,
            "open": prices,
            "high": [p * (1 + np.random.random() * 0.005) for p in prices],
            "low": [p * (1 - np.random.random() * 0.005) for p in prices],
            "close": prices,
            "volume": [int(np.random.random() * 1000) for _ in prices]
        })
        
        data[symbol] = df
        logger.info(f"Generated sample data for {symbol}: {len(df)} rows")
    
    return data