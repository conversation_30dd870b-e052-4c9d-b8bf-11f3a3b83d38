"""
Strategy Builder Core - TDD Implementation
Validates and analyzes trading strategy code with comprehensive security and quality checks
"""

import ast
import re
import time
from typing import Dict, List, Optional, Set, Any
from dataclasses import dataclass, field

from .models import ValidationResult, StrategyType


class StrategyValidationError(Exception):
    """Raised when strategy validation fails"""
    pass


class SecurityError(Exception):
    """Raised when unsafe code is detected"""
    pass


@dataclass
class ValidationResult:
    """Extended validation result with detailed analysis"""
    is_valid: bool
    strategy_type: str = "basic"
    strategy_format: str = "function"  # "function" or "class"
    
    # Code analysis
    detected_libraries: List[str] = field(default_factory=list)
    detected_indicators: List[str] = field(default_factory=list)
    detected_parameters: List[str] = field(default_factory=list)
    
    # Quality metrics
    estimated_complexity: str = "medium"  # "low", "medium", "high"
    memory_usage_estimate: str = "low"
    
    # Validation details
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    
    # Security analysis
    has_security_issues: bool = False
    dangerous_imports: List[str] = field(default_factory=list)
    
    # Structure analysis
    has_required_methods: bool = True
    function_signature_valid: bool = True
    return_format_valid: bool = True


class StrategyBuilder:
    """
    Strategy Builder Core - Validates and analyzes trading strategy code
    Implements comprehensive validation with security, quality, and structure checks
    """
    
    # Dangerous imports that should be blocked
    DANGEROUS_IMPORTS = {
        'os', 'subprocess', 'sys', 'socket', 'urllib', 'requests', 
        'http', 'ftplib', 'smtplib', 'telnetlib', 'webbrowser',
        'pickle', 'marshal', 'shelve', 'dbm', 'sqlite3',
        'ctypes', 'multiprocessing', 'threading', 'asyncio'
    }
    
    # Dangerous function calls
    DANGEROUS_FUNCTIONS = {
        'exec', 'eval', 'compile', '__import__', 'open', 'file',
        'input', 'raw_input', 'reload', 'vars', 'globals', 'locals'
    }
    
    # ML-related imports
    ML_IMPORTS = {
        'sklearn', 'scikit-learn', 'tensorflow', 'keras', 'torch', 'pytorch',
        'xgboost', 'lightgbm', 'catboost', 'statsmodels'
    }
    
    # Common trading indicators (for detection)
    INDICATOR_PATTERNS = {
        'rsi': r'\brsi\b',
        'macd': r'\bmacd\b',
        'sma': r'\bsma\b|simple.*moving.*average',
        'ema': r'\bema\b|exponential.*moving.*average',
        'bollinger': r'\bbollinger\b|bb_',
        'stochastic': r'\bstoch\b',
        'atr': r'\batr\b|average.*true.*range',
        'adx': r'\badx\b',
        'cci': r'\bcci\b',
        'williams': r'\bwilliams\b|%r'
    }
    
    def __init__(self):
        """Initialize the Strategy Builder"""
        pass
    
    def validate_strategy_code(self, code: str) -> ValidationResult:
        """
        Comprehensive validation of strategy code
        
        Args:
            code: The strategy code to validate
            
        Returns:
            ValidationResult with detailed analysis
            
        Raises:
            StrategyValidationError: If validation fails
            SecurityError: If unsafe code is detected
        """
        start_time = time.time()
        
        # Initialize result
        result = ValidationResult(is_valid=False)
        
        try:
            # Basic validation
            self._validate_basic_structure(code, result)
            
            # Parse the code
            tree = self._parse_code(code, result)
            if tree is None:
                return result
            
            # Security validation
            self._validate_security(tree, code, result)
            
            # Structure validation
            self._validate_structure(tree, result)
            
            # Analyze code content
            self._analyze_imports(tree, result)
            self._analyze_indicators(code, result)
            self._analyze_parameters(tree, result)
            self._analyze_complexity(tree, code, result)
            
            # Determine strategy type
            self._determine_strategy_type(result)
            
            # Final validation
            if len(result.errors) == 0:
                result.is_valid = True
            
            return result
            
        except SecurityError:
            raise
        except StrategyValidationError:
            raise
        except Exception as e:
            raise StrategyValidationError(f"Validation failed: {str(e)}")
    
    def _validate_basic_structure(self, code: str, result: ValidationResult):
        """Validate basic code structure"""
        if not code or not code.strip():
            raise StrategyValidationError("Empty code")
        
        # Check if code is only whitespace
        if not code.strip():
            raise StrategyValidationError("Empty code")
        
        # Check if code is only comments
        lines = [line.strip() for line in code.split('\n')]
        executable_lines = [line for line in lines if line and not line.startswith('#')]
        if not executable_lines:
            raise StrategyValidationError("No executable code")
    
    def _parse_code(self, code: str, result: ValidationResult) -> Optional[ast.AST]:
        """Parse code into AST"""
        try:
            tree = ast.parse(code)
            return tree
        except SyntaxError as e:
            raise StrategyValidationError(f"Syntax error: {str(e)}")
    
    def _validate_security(self, tree: ast.AST, code: str, result: ValidationResult):
        """Validate code security"""
        # Check for dangerous imports
        dangerous_imports = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    if alias.name in self.DANGEROUS_IMPORTS:
                        dangerous_imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module and node.module in self.DANGEROUS_IMPORTS:
                    dangerous_imports.append(node.module)
        
        if dangerous_imports:
            result.dangerous_imports = dangerous_imports
            result.has_security_issues = True
            raise SecurityError(f"Dangerous imports detected: {', '.join(dangerous_imports)}")
        
        # Check for dangerous function calls
        for node in ast.walk(tree):
            if isinstance(node, ast.Call):
                if isinstance(node.func, ast.Name) and node.func.id in self.DANGEROUS_FUNCTIONS:
                    raise SecurityError(f"Unsafe code detected: {node.func.id}")
        
        # Check for system calls in string literals
        dangerous_patterns = [
            r'os\.system', r'subprocess\.', r'eval\(', r'exec\(',
            r'rm\s+-rf', r'del\s+/[a-z]', r'format\s+[a-z]:'
        ]
        
        for pattern in dangerous_patterns:
            if re.search(pattern, code, re.IGNORECASE):
                raise SecurityError("Unsafe code detected")
    
    def _validate_structure(self, tree: ast.AST, result: ValidationResult):
        """Validate code structure"""
        # Find trading_strategy functions
        trading_strategy_functions = []
        standalone_functions = []
        class_methods = []
        
        # First pass: find all top-level nodes
        for node in tree.body:
            if isinstance(node, ast.FunctionDef) and node.name == 'trading_strategy':
                standalone_functions.append(node)
            elif isinstance(node, ast.ClassDef):
                # Check for trading_strategy method in class
                for class_node in node.body:
                    if isinstance(class_node, ast.FunctionDef) and class_node.name == 'trading_strategy':
                        class_methods.append(class_node)
                        result.strategy_format = "class"
        
        # Combine all found functions
        trading_strategy_functions = standalone_functions + class_methods
        
        if len(trading_strategy_functions) == 0:
            raise StrategyValidationError("Missing trading_strategy function")
        
        if len(trading_strategy_functions) > 1:
            raise StrategyValidationError("Multiple trading_strategy functions")
        
        # Validate function signature
        func = trading_strategy_functions[0]
        if len(func.args.args) < 2:
            raise StrategyValidationError("Invalid function signature")
        
        # Check parameter names
        param_names = [arg.arg for arg in func.args.args]
        if result.strategy_format == "class":
            # For class methods, first parameter is 'self'
            if len(param_names) < 3 or param_names[1] != 'data' or param_names[2] != 'params':
                raise StrategyValidationError("Invalid function signature")
        else:
            # For standalone functions
            if param_names[0] != 'data' or param_names[1] != 'params':
                raise StrategyValidationError("Invalid function signature")
        
        result.function_signature_valid = True
        
        # Validate return format (basic check)
        has_return = False
        for node in ast.walk(func):
            if isinstance(node, ast.Return) and node.value:
                has_return = True
                # Check if return is a dictionary
                if isinstance(node.value, ast.Dict):
                    result.return_format_valid = True
                elif isinstance(node.value, (ast.Str, ast.Constant)) and isinstance(getattr(node.value, 'value', getattr(node.value, 's', None)), str):
                    raise StrategyValidationError("Invalid return format")
        
        if not has_return:
            result.warnings.append("No return statement found")
    
    def _analyze_imports(self, tree: ast.AST, result: ValidationResult):
        """Analyze imports to detect libraries and strategy type"""
        imports = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    imports.append(node.module)
        
        result.detected_libraries = imports
        
        # Normalize sklearn imports for detection
        normalized_imports = []
        for imp in imports:
            if imp.startswith('sklearn'):
                normalized_imports.append('sklearn')
            else:
                normalized_imports.append(imp)
        
        # Check for ML imports
        ml_imports = [imp for imp in normalized_imports if imp in self.ML_IMPORTS]
        if ml_imports:
            result.strategy_type = "machine_learning"
    
    def _analyze_indicators(self, code: str, result: ValidationResult):
        """Analyze code to detect trading indicators"""
        indicators = []
        
        for indicator, pattern in self.INDICATOR_PATTERNS.items():
            if re.search(pattern, code, re.IGNORECASE):
                indicators.append(indicator)
        
        result.detected_indicators = indicators
    
    def _analyze_parameters(self, tree: ast.AST, result: ValidationResult):
        """Analyze parameter usage"""
        parameters = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Call):
                if (isinstance(node.func, ast.Attribute) and 
                    isinstance(node.func.value, ast.Name) and 
                    node.func.value.id == 'params' and 
                    node.func.attr == 'get'):
                    # params.get('parameter_name')
                    if node.args:
                        arg = node.args[0]
                        if isinstance(arg, ast.Str):
                            parameters.append(arg.s)
                        elif isinstance(arg, ast.Constant) and isinstance(arg.value, str):
                            parameters.append(arg.value)
        
        result.detected_parameters = parameters
    
    def _analyze_complexity(self, tree: ast.AST, code: str, result: ValidationResult):
        """Analyze code complexity"""
        # Count various complexity indicators
        lines = len([line for line in code.split('\n') if line.strip()])
        functions = len([node for node in ast.walk(tree) if isinstance(node, ast.FunctionDef)])
        loops = len([node for node in ast.walk(tree) if isinstance(node, (ast.For, ast.While))])
        conditions = len([node for node in ast.walk(tree) if isinstance(node, ast.If)])
        imports = len(result.detected_libraries)
        
        # Calculate complexity score
        complexity_score = (
            lines * 0.2 +
            functions * 3 +
            loops * 4 +
            conditions * 2 +
            imports * 1
        )
        
        # Boost complexity for ML strategies
        if result.strategy_type == "machine_learning":
            complexity_score += 10
        
        # Adjust thresholds for better detection
        if complexity_score < 5:
            result.estimated_complexity = "low"
        elif complexity_score < 12:
            result.estimated_complexity = "medium"
        else:
            result.estimated_complexity = "high"
    
    def _determine_strategy_type(self, result: ValidationResult):
        """Determine strategy type based on analysis"""
        if result.strategy_type == "machine_learning":
            return  # Already determined
        
        # Check indicators for strategy type hints
        momentum_indicators = ['macd', 'ema', 'adx']
        mean_reversion_indicators = ['rsi', 'bollinger', 'stochastic']
        
        momentum_count = sum(1 for ind in momentum_indicators if ind in result.detected_indicators)
        mean_reversion_count = sum(1 for ind in mean_reversion_indicators if ind in result.detected_indicators)
        
        if momentum_count > mean_reversion_count:
            result.strategy_type = "momentum"
        elif mean_reversion_count > 0:
            result.strategy_type = "mean_reversion"
        else:
            result.strategy_type = "basic"