#!/usr/bin/env python3
"""
Test Execution Dashboard

This script generates an HTML dashboard for test results visualization.
"""

import json
import os
import glob
from datetime import datetime
import webbrowser
from pathlib import Path


class TestDashboard:
    def __init__(self, reports_dir="reports"):
        self.reports_dir = Path(reports_dir)
        self.reports_dir.mkdir(exist_ok=True)
        self.reports = []
        self.dashboard_file = self.reports_dir / "dashboard.html"
    
    def load_reports(self):
        """Load all test reports from the reports directory"""
        report_files = glob.glob(str(self.reports_dir / "test_report_*.json"))
        
        for report_file in sorted(report_files, reverse=True):
            try:
                with open(report_file, 'r') as f:
                    report_data = json.load(f)
                
                # Extract timestamp from filename
                filename = os.path.basename(report_file)
                timestamp_str = filename.replace("test_report_", "").replace(".json", "")
                
                self.reports.append({
                    'file': report_file,
                    'timestamp': timestamp_str,
                    'data': report_data
                })
            except Exception as e:
                print(f"Error processing {report_file}: {e}")
    
    def generate_dashboard(self):
        """Generate an HTML dashboard for test results"""
        if not self.reports:
            self.load_reports()
        
        html = self._generate_html()
        
        with open(self.dashboard_file, 'w') as f:
            f.write(html)
        
        print(f"📊 Dashboard generated at {self.dashboard_file}")
        
        # Open in browser
        webbrowser.open(f"file://{self.dashboard_file.absolute()}")
    
    def _generate_html(self):
        """Generate the HTML content for the dashboard"""
        html = """
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Test Execution Dashboard</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background-color: #f5f5f5;
                }
                .container {
                    max-width: 1200px;
                    margin: 0 auto;
                    background-color: white;
                    padding: 20px;
                    border-radius: 5px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                }
                h1, h2 {
                    color: #333;
                }
                .summary {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 20px;
                }
                .summary-card {
                    background-color: #f9f9f9;
                    padding: 15px;
                    border-radius: 5px;
                    width: 30%;
                    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                }
                .summary-card.success {
                    background-color: #e6ffe6;
                }
                .summary-card.warning {
                    background-color: #fff9e6;
                }
                .summary-card.danger {
                    background-color: #ffe6e6;
                }
                .report-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-top: 20px;
                }
                .report-table th, .report-table td {
                    padding: 10px;
                    text-align: left;
                    border-bottom: 1px solid #ddd;
                }
                .report-table th {
                    background-color: #f2f2f2;
                }
                .status-badge {
                    display: inline-block;
                    padding: 5px 10px;
                    border-radius: 3px;
                    font-size: 12px;
                    font-weight: bold;
                }
                .status-badge.success {
                    background-color: #dff0d8;
                    color: #3c763d;
                }
                .status-badge.failure {
                    background-color: #f2dede;
                    color: #a94442;
                }
                .chart-container {
                    margin-top: 30px;
                    height: 300px;
                }
            </style>
            <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        </head>
        <body>
            <div class="container">
                <h1>Test Execution Dashboard</h1>
                <p>Last updated: """ + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + """</p>
                
                <div class="summary">
        """
        
        # Calculate summary statistics
        total_reports = len(self.reports)
        passed_reports = sum(1 for r in self.reports if r['data'].get('results', {}).get('basic_tests', {}).get('passed', False))
        
        # Add summary cards
        html += f"""
                    <div class="summary-card {'success' if passed_reports == total_reports else 'warning'}">
                        <h3>Test Runs</h3>
                        <p style="font-size: 24px;">{total_reports}</p>
                    </div>
                    <div class="summary-card {'success' if passed_reports == total_reports else 'warning'}">
                        <h3>Success Rate</h3>
                        <p style="font-size: 24px;">{passed_reports/total_reports*100:.1f}%</p>
                    </div>
        """
        
        # Add coverage card if available
        latest_coverage = None
        for report in self.reports:
            if 'coverage' in report['data'].get('results', {}):
                latest_coverage = report['data']['results']['coverage'].get('total_coverage', 0)
                break
        
        if latest_coverage is not None:
            coverage_class = 'success' if latest_coverage >= 80 else 'warning' if latest_coverage >= 60 else 'danger'
            html += f"""
                    <div class="summary-card {coverage_class}">
                        <h3>Test Coverage</h3>
                        <p style="font-size: 24px;">{latest_coverage:.1f}%</p>
                    </div>
            """
        
        html += """
                </div>
                
                <h2>Recent Test Runs</h2>
                <table class="report-table">
                    <thead>
                        <tr>
                            <th>Timestamp</th>
                            <th>Status</th>
                            <th>Duration</th>
                            <th>Coverage</th>
                            <th>Details</th>
                        </tr>
                    </thead>
                    <tbody>
        """
        
        # Add rows for each report
        for report in self.reports[:10]:  # Show only the 10 most recent
            timestamp = report['timestamp']
            passed = report['data'].get('results', {}).get('basic_tests', {}).get('passed', False)
            duration = report['data'].get('duration', 0)
            coverage = report['data'].get('results', {}).get('coverage', {}).get('total_coverage', 'N/A')
            
            status_class = 'success' if passed else 'failure'
            status_text = 'PASSED' if passed else 'FAILED'
            
            html += f"""
                        <tr>
                            <td>{timestamp}</td>
                            <td><span class="status-badge {status_class}">{status_text}</span></td>
                            <td>{duration:.1f}s</td>
                            <td>{coverage if coverage == 'N/A' else f'{coverage:.1f}%'}</td>
                            <td><a href="file://{report['file']}" target="_blank">View Report</a></td>
                        </tr>
            """
        
        html += """
                    </tbody>
                </table>
                
                <div class="chart-container">
                    <canvas id="coverageChart"></canvas>
                </div>
                
                <script>
                    // Prepare data for the chart
                    const timestamps = [];
                    const coverageData = [];
                    const passRateData = [];
                """
        
        # Add data for the chart
        chart_data = []
        for report in reversed(self.reports[-10:]):  # Last 10 reports in chronological order
            timestamp = report['timestamp']
            coverage = report['data'].get('results', {}).get('coverage', {}).get('total_coverage')
            passed = report['data'].get('results', {}).get('basic_tests', {}).get('passed', False)
            
            if coverage is not None:
                chart_data.append({
                    'timestamp': timestamp,
                    'coverage': coverage,
                    'passed': 100 if passed else 0
                })
        
        # Generate JavaScript arrays for the chart
        timestamps_js = "[" + ", ".join([f"'{d['timestamp']}'" for d in chart_data]) + "]"
        coverage_js = "[" + ", ".join([f"{d['coverage']}" for d in chart_data]) + "]"
        passed_js = "[" + ", ".join([f"{d['passed']}" for d in chart_data]) + "]"
        
        html += f"""
                    const timestamps = {timestamps_js};
                    const coverageData = {coverage_js};
                    const passRateData = {passed_js};
                    
                    // Create the chart
                    const ctx = document.getElementById('coverageChart').getContext('2d');
                    const chart = new Chart(ctx, {{
                        type: 'line',
                        data: {{
                            labels: timestamps,
                            datasets: [
                                {{
                                    label: 'Coverage (%)',
                                    data: coverageData,
                                    borderColor: 'rgba(75, 192, 192, 1)',
                                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                                    tension: 0.1
                                }},
                                {{
                                    label: 'Pass Rate (%)',
                                    data: passRateData,
                                    borderColor: 'rgba(153, 102, 255, 1)',
                                    backgroundColor: 'rgba(153, 102, 255, 0.2)',
                                    tension: 0.1
                                }}
                            ]
                        }},
                        options: {{
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {{
                                y: {{
                                    beginAtZero: true,
                                    max: 100
                                }}
                            }}
                        }}
                    }});
                </script>
            </div>
        </body>
        </html>
        """
        
        return html


def main():
    dashboard = TestDashboard()
    dashboard.generate_dashboard()


if __name__ == "__main__":
    main()