"""
Service Interfaces for Dependency Injection

Defines abstract interfaces for all major services to enable
dependency injection and improve testability.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Protocol
from datetime import datetime
from dataclasses import dataclass

# Data Transfer Objects
@dataclass
class MarketData:
    """Market data structure"""
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format"""
        return {
            'symbol': self.symbol,
            'timestamp': self.timestamp.isoformat(),
            'open': self.open,
            'high': self.high,
            'low': self.low,
            'close': self.close,
            'volume': self.volume
        }

@dataclass
class TradingSignal:
    """Trading signal structure"""
    symbol: str
    signal: str  # 'buy', 'sell', 'hold'
    confidence: float
    timestamp: datetime
    strategy_name: str
    metadata: Dict[str, Any]

@dataclass
class Position:
    """Trading position structure"""
    symbol: str
    quantity: int
    entry_price: float
    current_price: float
    unrealized_pnl: float
    timestamp: datetime

@dataclass
class Order:
    """Trading order structure"""
    id: str
    symbol: str
    side: str  # 'buy', 'sell'
    quantity: int
    price: float
    order_type: str  # 'market', 'limit'
    status: str  # 'pending', 'filled', 'cancelled'
    timestamp: datetime

# Service Interfaces
class IMarketDataService(ABC):
    """Interface for market data services"""
    
    @abstractmethod
    async def get_current_price(self, symbol: str) -> float:
        """Get current price for a symbol"""
        pass
    
    @abstractmethod
    async def get_historical_data(self, symbol: str, period: str, interval: str) -> List[MarketData]:
        """Get historical market data"""
        pass
    
    @abstractmethod
    async def subscribe_to_updates(self, symbol: str, callback) -> None:
        """Subscribe to real-time market data updates"""
        pass
    
    @abstractmethod
    async def unsubscribe_from_updates(self, symbol: str) -> None:
        """Unsubscribe from market data updates"""
        pass

class IStrategyService(ABC):
    """Interface for strategy services"""
    
    @abstractmethod
    async def execute_strategy(self, strategy_code: str, market_data: Dict[str, Any], params: Dict[str, Any]) -> TradingSignal:
        """Execute a trading strategy"""
        pass
    
    @abstractmethod
    async def validate_strategy(self, strategy_code: str) -> Dict[str, Any]:
        """Validate strategy code"""
        pass
    
    @abstractmethod
    async def get_strategy_performance(self, strategy_name: str) -> Dict[str, Any]:
        """Get strategy performance metrics"""
        pass

class ITradingService(ABC):
    """Interface for trading services"""
    
    @abstractmethod
    async def place_order(self, order: Order) -> str:
        """Place a trading order"""
        pass
    
    @abstractmethod
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel a trading order"""
        pass
    
    @abstractmethod
    async def get_positions(self) -> List[Position]:
        """Get current positions"""
        pass
    
    @abstractmethod
    async def get_account_balance(self) -> Dict[str, float]:
        """Get account balance information"""
        pass

class IRiskManagementService(ABC):
    """Interface for risk management services"""
    
    @abstractmethod
    async def calculate_position_size(self, symbol: str, signal: TradingSignal, account_balance: float) -> int:
        """Calculate appropriate position size"""
        pass
    
    @abstractmethod
    async def check_risk_limits(self, order: Order, current_positions: List[Position]) -> bool:
        """Check if order violates risk limits"""
        pass
    
    @abstractmethod
    async def calculate_portfolio_risk(self, positions: List[Position]) -> Dict[str, float]:
        """Calculate portfolio risk metrics"""
        pass

class IPortfolioService(ABC):
    """Interface for portfolio management services"""
    
    @abstractmethod
    async def get_portfolio_value(self) -> float:
        """Get total portfolio value"""
        pass
    
    @abstractmethod
    async def get_portfolio_allocation(self) -> Dict[str, float]:
        """Get portfolio allocation by asset"""
        pass
    
    @abstractmethod
    async def rebalance_portfolio(self, target_allocation: Dict[str, float]) -> List[Order]:
        """Rebalance portfolio to target allocation"""
        pass

class INotificationService(ABC):
    """Interface for notification services"""
    
    @abstractmethod
    async def send_alert(self, message: str, level: str = "info") -> None:
        """Send an alert notification"""
        pass
    
    @abstractmethod
    async def send_trade_notification(self, order: Order) -> None:
        """Send trade execution notification"""
        pass

class IDataStorageService(ABC):
    """Interface for data storage services"""
    
    @abstractmethod
    async def store_market_data(self, data: MarketData) -> None:
        """Store market data"""
        pass
    
    @abstractmethod
    async def store_trading_signal(self, signal: TradingSignal) -> None:
        """Store trading signal"""
        pass
    
    @abstractmethod
    async def store_order(self, order: Order) -> None:
        """Store order information"""
        pass
    
    @abstractmethod
    async def get_historical_signals(self, symbol: str, start_date: datetime, end_date: datetime) -> List[TradingSignal]:
        """Get historical trading signals"""
        pass

class IConfigurationService(ABC):
    """Interface for configuration services"""
    
    @abstractmethod
    def get_config(self, key: str, default: Any = None) -> Any:
        """Get configuration value"""
        pass
    
    @abstractmethod
    def set_config(self, key: str, value: Any) -> None:
        """Set configuration value"""
        pass
    
    @abstractmethod
    def get_trading_parameters(self) -> Dict[str, Any]:
        """Get trading parameters"""
        pass

class ILoggingService(ABC):
    """Interface for logging services"""
    
    @abstractmethod
    def log_info(self, message: str, **kwargs) -> None:
        """Log info message"""
        pass
    
    @abstractmethod
    def log_warning(self, message: str, **kwargs) -> None:
        """Log warning message"""
        pass
    
    @abstractmethod
    def log_error(self, message: str, **kwargs) -> None:
        """Log error message"""
        pass
    
    @abstractmethod
    def log_trade(self, order: Order) -> None:
        """Log trade execution"""
        pass

# Protocol for dependency injection container
class IDependencyContainer(Protocol):
    """Protocol for dependency injection container"""
    
    def register(self, interface: type, implementation: type, singleton: bool = True) -> None:
        """Register a service implementation"""
        ...
    
    def register_instance(self, interface: type, instance: Any) -> None:
        """Register a service instance"""
        ...
    
    def resolve(self, interface: type) -> Any:
        """Resolve a service instance"""
        ...
    
    def is_registered(self, interface: type) -> bool:
        """Check if a service is registered"""
        ...