# src/monitoring/performance_monitor.py
import time
import psutil
import threading
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from collections import deque
import json
import statistics

@dataclass
class PerformanceMetric:
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    disk_io_read_mb: float
    disk_io_write_mb: float
    network_sent_mb: float
    network_recv_mb: float
    active_threads: int
    open_files: int

@dataclass
class TradingPerformanceMetric:
    timestamp: datetime
    orders_per_second: float
    avg_order_latency_ms: float
    backtest_execution_time_ms: float
    data_processing_time_ms: float
    ml_inference_time_ms: float
    memory_usage_mb: float
    active_positions: int
    pending_orders: int

@dataclass
class AlertThreshold:
    metric_name: str
    threshold_value: float
    comparison: str  # 'greater_than', 'less_than', 'equals'
    alert_message: str
    severity: str  # 'low', 'medium', 'high', 'critical'

class PerformanceAlert:
    def __init__(self, threshold: AlertThreshold, current_value: float, timestamp: datetime):
        self.threshold = threshold
        self.current_value = current_value
        self.timestamp = timestamp
        self.acknowledged = False
        self.alert_id = f"alert_{int(timestamp.timestamp())}_{threshold.metric_name}"

class PerformanceMonitor:
    def __init__(self, monitoring_interval: float = 1.0, history_size: int = 1000):
        self.monitoring_interval = monitoring_interval
        self.history_size = history_size
        
        # Performance data storage
        self.system_metrics: deque = deque(maxlen=history_size)
        self.trading_metrics: deque = deque(maxlen=history_size)
        
        # Monitoring state
        self.is_monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        
        # Alert system
        self.alert_thresholds: List[AlertThreshold] = []
        self.active_alerts: List[PerformanceAlert] = []
        self.alert_callbacks: List[Callable[[PerformanceAlert], None]] = []
        
        # Performance optimization
        self.optimization_suggestions: List[Dict[str, Any]] = []
        
        # Baseline metrics for comparison
        self.baseline_metrics: Optional[Dict[str, float]] = None
        
        # Initialize default thresholds
        self._setup_default_thresholds()
    
    def _setup_default_thresholds(self):
        """Setup default performance alert thresholds"""
        default_thresholds = [
            AlertThreshold("cpu_percent", 80.0, "greater_than", "High CPU usage detected", "high"),
            AlertThreshold("memory_percent", 85.0, "greater_than", "High memory usage detected", "high"),
            AlertThreshold("avg_order_latency_ms", 1000.0, "greater_than", "High order latency detected", "medium"),
            AlertThreshold("orders_per_second", 1.0, "less_than", "Low order throughput detected", "medium"),
            AlertThreshold("open_files", 1000, "greater_than", "Too many open files", "high"),
            AlertThreshold("active_threads", 100, "greater_than", "Too many active threads", "medium")
        ]
        
        self.alert_thresholds.extend(default_thresholds)
    
    def start_monitoring(self):
        """Start performance monitoring in background thread"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        
        print(f"🔍 Performance monitoring started (interval: {self.monitoring_interval}s)")
    
    def stop_monitoring(self):
        """Stop performance monitoring"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5.0)
        
        print("🛑 Performance monitoring stopped")
    
    def _monitoring_loop(self):
        """Main monitoring loop"""
        last_disk_io = psutil.disk_io_counters()
        last_network_io = psutil.net_io_counters()
        
        while self.is_monitoring:
            try:
                # Collect system metrics
                system_metric = self._collect_system_metrics(last_disk_io, last_network_io)
                self.system_metrics.append(system_metric)
                
                # Update last IO counters
                last_disk_io = psutil.disk_io_counters()
                last_network_io = psutil.net_io_counters()
                
                # Check for alerts
                self._check_alerts(system_metric)
                
                # Generate optimization suggestions
                self._analyze_performance()
                
                time.sleep(self.monitoring_interval)
                
            except Exception as e:
                print(f"Error in monitoring loop: {e}")
                time.sleep(self.monitoring_interval)
    
    def _collect_system_metrics(self, last_disk_io, last_network_io) -> PerformanceMetric:
        """Collect current system performance metrics"""
        # CPU and Memory
        cpu_percent = psutil.cpu_percent(interval=None)
        memory = psutil.virtual_memory()
        
        # Disk I/O
        current_disk_io = psutil.disk_io_counters()
        disk_read_mb = (current_disk_io.read_bytes - last_disk_io.read_bytes) / (1024 * 1024) if last_disk_io else 0
        disk_write_mb = (current_disk_io.write_bytes - last_disk_io.write_bytes) / (1024 * 1024) if last_disk_io else 0
        
        # Network I/O
        current_network_io = psutil.net_io_counters()
        network_sent_mb = (current_network_io.bytes_sent - last_network_io.bytes_sent) / (1024 * 1024) if last_network_io else 0
        network_recv_mb = (current_network_io.bytes_recv - last_network_io.bytes_recv) / (1024 * 1024) if last_network_io else 0
        
        # Process info
        process = psutil.Process()
        
        return PerformanceMetric(
            timestamp=datetime.now(),
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_used_mb=memory.used / (1024 * 1024),
            disk_io_read_mb=disk_read_mb,
            disk_io_write_mb=disk_write_mb,
            network_sent_mb=network_sent_mb,
            network_recv_mb=network_recv_mb,
            active_threads=threading.active_count(),
            open_files=len(process.open_files())
        )
    
    def record_trading_metric(self, metric: TradingPerformanceMetric):
        """Record trading-specific performance metric"""
        self.trading_metrics.append(metric)
        
        # Check trading-specific alerts
        self._check_trading_alerts(metric)
    
    def _check_alerts(self, metric: PerformanceMetric):
        """Check system metrics against alert thresholds"""
        metric_dict = asdict(metric)
        
        for threshold in self.alert_thresholds:
            if threshold.metric_name in metric_dict:
                current_value = metric_dict[threshold.metric_name]
                
                if self._should_alert(current_value, threshold):
                    alert = PerformanceAlert(threshold, current_value, metric.timestamp)
                    self.active_alerts.append(alert)
                    
                    # Trigger alert callbacks
                    for callback in self.alert_callbacks:
                        try:
                            callback(alert)
                        except Exception as e:
                            print(f"Error in alert callback: {e}")
    
    def _check_trading_alerts(self, metric: TradingPerformanceMetric):
        """Check trading metrics against alert thresholds"""
        metric_dict = asdict(metric)
        
        for threshold in self.alert_thresholds:
            if threshold.metric_name in metric_dict:
                current_value = metric_dict[threshold.metric_name]
                
                if self._should_alert(current_value, threshold):
                    alert = PerformanceAlert(threshold, current_value, metric.timestamp)
                    self.active_alerts.append(alert)
                    
                    # Trigger alert callbacks
                    for callback in self.alert_callbacks:
                        try:
                            callback(alert)
                        except Exception as e:
                            print(f"Error in alert callback: {e}")
    
    def _should_alert(self, current_value: float, threshold: AlertThreshold) -> bool:
        """Check if current value should trigger an alert"""
        if threshold.comparison == "greater_than":
            return current_value > threshold.threshold_value
        elif threshold.comparison == "less_than":
            return current_value < threshold.threshold_value
        elif threshold.comparison == "equals":
            return abs(current_value - threshold.threshold_value) < 0.001
        
        return False
    
    def _analyze_performance(self):
        """Analyze performance and generate optimization suggestions"""
        if len(self.system_metrics) < 10:  # Need some history
            return
        
        recent_metrics = list(self.system_metrics)[-10:]  # Last 10 metrics
        
        # Calculate averages
        avg_cpu = statistics.mean([m.cpu_percent for m in recent_metrics])
        avg_memory = statistics.mean([m.memory_percent for m in recent_metrics])
        avg_threads = statistics.mean([m.active_threads for m in recent_metrics])
        
        suggestions = []
        
        # CPU optimization suggestions
        if avg_cpu > 70:
            suggestions.append({
                "type": "cpu_optimization",
                "severity": "medium",
                "message": f"High CPU usage ({avg_cpu:.1f}%). Consider optimizing algorithms or reducing concurrent operations.",
                "timestamp": datetime.now()
            })
        
        # Memory optimization suggestions
        if avg_memory > 75:
            suggestions.append({
                "type": "memory_optimization",
                "severity": "medium",
                "message": f"High memory usage ({avg_memory:.1f}%). Consider implementing data cleanup or reducing cache sizes.",
                "timestamp": datetime.now()
            })
        
        # Thread optimization suggestions
        if avg_threads > 50:
            suggestions.append({
                "type": "thread_optimization",
                "severity": "low",
                "message": f"High thread count ({avg_threads:.0f}). Consider using thread pools or async operations.",
                "timestamp": datetime.now()
            })
        
        # Add new suggestions (avoid duplicates)
        for suggestion in suggestions:
            if not any(s["type"] == suggestion["type"] for s in self.optimization_suggestions[-5:]):
                self.optimization_suggestions.append(suggestion)
    
    def get_performance_summary(self, minutes: int = 5) -> Dict[str, Any]:
        """Get performance summary for the last N minutes"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        
        # Filter recent metrics
        recent_system = [m for m in self.system_metrics if m.timestamp >= cutoff_time]
        recent_trading = [m for m in self.trading_metrics if m.timestamp >= cutoff_time]
        
        if not recent_system:
            return {"error": "No recent performance data available"}
        
        # Calculate system statistics
        system_stats = {
            "cpu_percent": {
                "avg": statistics.mean([m.cpu_percent for m in recent_system]),
                "max": max([m.cpu_percent for m in recent_system]),
                "min": min([m.cpu_percent for m in recent_system])
            },
            "memory_percent": {
                "avg": statistics.mean([m.memory_percent for m in recent_system]),
                "max": max([m.memory_percent for m in recent_system]),
                "min": min([m.memory_percent for m in recent_system])
            },
            "active_threads": {
                "avg": statistics.mean([m.active_threads for m in recent_system]),
                "max": max([m.active_threads for m in recent_system]),
                "min": min([m.active_threads for m in recent_system])
            }
        }
        
        # Calculate trading statistics if available
        trading_stats = {}
        if recent_trading:
            trading_stats = {
                "orders_per_second": {
                    "avg": statistics.mean([m.orders_per_second for m in recent_trading]),
                    "max": max([m.orders_per_second for m in recent_trading]),
                    "min": min([m.orders_per_second for m in recent_trading])
                },
                "avg_order_latency_ms": {
                    "avg": statistics.mean([m.avg_order_latency_ms for m in recent_trading]),
                    "max": max([m.avg_order_latency_ms for m in recent_trading]),
                    "min": min([m.avg_order_latency_ms for m in recent_trading])
                }
            }
        
        return {
            "time_period_minutes": minutes,
            "data_points": len(recent_system),
            "system_performance": system_stats,
            "trading_performance": trading_stats,
            "active_alerts": len([a for a in self.active_alerts if not a.acknowledged]),
            "optimization_suggestions": len(self.optimization_suggestions),
            "timestamp": datetime.now().isoformat()
        }
    
    def get_active_alerts(self) -> List[Dict[str, Any]]:
        """Get all active (unacknowledged) alerts"""
        return [
            {
                "alert_id": alert.alert_id,
                "metric_name": alert.threshold.metric_name,
                "current_value": alert.current_value,
                "threshold_value": alert.threshold.threshold_value,
                "severity": alert.threshold.severity,
                "message": alert.threshold.alert_message,
                "timestamp": alert.timestamp.isoformat(),
                "acknowledged": alert.acknowledged
            }
            for alert in self.active_alerts
            if not alert.acknowledged
        ]
    
    def acknowledge_alert(self, alert_id: str) -> bool:
        """Acknowledge an alert"""
        for alert in self.active_alerts:
            if alert.alert_id == alert_id:
                alert.acknowledged = True
                return True
        return False
    
    def get_optimization_suggestions(self) -> List[Dict[str, Any]]:
        """Get recent optimization suggestions"""
        return self.optimization_suggestions[-10:]  # Last 10 suggestions
    
    def add_alert_threshold(self, threshold: AlertThreshold):
        """Add custom alert threshold"""
        self.alert_thresholds.append(threshold)
    
    def add_alert_callback(self, callback: Callable[[PerformanceAlert], None]):
        """Add callback function for alerts"""
        self.alert_callbacks.append(callback)
    
    def set_baseline(self):
        """Set current performance as baseline for comparison"""
        if not self.system_metrics:
            return False
        
        recent_metrics = list(self.system_metrics)[-5:]  # Last 5 metrics
        
        self.baseline_metrics = {
            "cpu_percent": statistics.mean([m.cpu_percent for m in recent_metrics]),
            "memory_percent": statistics.mean([m.memory_percent for m in recent_metrics]),
            "active_threads": statistics.mean([m.active_threads for m in recent_metrics])
        }
        
        return True
    
    def compare_to_baseline(self) -> Optional[Dict[str, Any]]:
        """Compare current performance to baseline"""
        if not self.baseline_metrics or not self.system_metrics:
            return None
        
        recent_metrics = list(self.system_metrics)[-5:]  # Last 5 metrics
        current_avg = {
            "cpu_percent": statistics.mean([m.cpu_percent for m in recent_metrics]),
            "memory_percent": statistics.mean([m.memory_percent for m in recent_metrics]),
            "active_threads": statistics.mean([m.active_threads for m in recent_metrics])
        }
        
        comparison = {}
        for metric, baseline_value in self.baseline_metrics.items():
            current_value = current_avg[metric]
            change_percent = ((current_value - baseline_value) / baseline_value) * 100
            
            comparison[metric] = {
                "baseline": baseline_value,
                "current": current_value,
                "change_percent": change_percent,
                "status": "improved" if change_percent < -5 else "degraded" if change_percent > 5 else "stable"
            }
        
        return comparison
    
    def export_metrics(self, filename: str, format: str = "json"):
        """Export performance metrics to file"""
        data = {
            "export_timestamp": datetime.now().isoformat(),
            "system_metrics": [asdict(m) for m in self.system_metrics],
            "trading_metrics": [asdict(m) for m in self.trading_metrics],
            "alert_thresholds": [asdict(t) for t in self.alert_thresholds],
            "optimization_suggestions": self.optimization_suggestions
        }
        
        if format.lower() == "json":
            with open(filename, 'w') as f:
                json.dump(data, f, indent=2, default=str)
        
        return True