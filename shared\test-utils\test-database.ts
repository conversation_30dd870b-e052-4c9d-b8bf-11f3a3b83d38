/**
 * Test database utilities for integration testing
 */

export interface TestDatabaseConfig {
  host?: string;
  port?: number;
  database?: string;
  username?: string;
  password?: string;
}

export class TestDatabase {
  private config: TestDatabaseConfig;
  private isConnected: boolean = false;

  constructor(config: TestDatabaseConfig = {}) {
    this.config = {
      host: 'localhost',
      port: 5432,
      database: 'test_db',
      username: 'test_user',
      password: 'test_password',
      ...config,
    };
  }

  async connect(): Promise<void> {
    // Mock database connection for testing
    this.isConnected = true;
  }

  async disconnect(): Promise<void> {
    this.isConnected = false;
  }

  async cleanup(): Promise<void> {
    // Mock cleanup operations
    if (this.isConnected) {
      await this.disconnect();
    }
  }

  isReady(): boolean {
    return this.isConnected;
  }

  async seed(data: any): Promise<void> {
    // Mock data seeding
    if (!this.isConnected) {
      throw new Error('Database not connected');
    }
  }

  async truncate(tables: string[]): Promise<void> {
    // Mock table truncation
    if (!this.isConnected) {
      throw new Error('Database not connected');
    }
  }
}

export const createTestDatabase = (config?: TestDatabaseConfig): TestDatabase => {
  return new TestDatabase(config);
};