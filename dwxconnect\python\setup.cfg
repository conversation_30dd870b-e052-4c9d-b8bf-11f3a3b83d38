[metadata]
name = DWX_Connect
version = 0.0.1
author = Darwinex
author_email = <EMAIL>
description = A simple multi-language MT4 connector
long_description = file: README.md
long_description_content_type = text/markdown
url = https://github.com/darwinex/dwx_connect
project_urls =
    Bug Tracker = https://github.com/darwinex/dwx_connect/issues
classifiers =
    Programming Language :: Python :: 3
    License :: OSI Approved :: BSD 3-Clause License
    Operating System :: OS Independent

[options]
package_dir =
    = api
packages = find:
python_requires = >=3.6

[options.packages.find]
where = api