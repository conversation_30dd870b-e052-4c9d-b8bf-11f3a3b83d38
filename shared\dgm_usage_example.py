# dgm_usage_example.py
# Complete usage example of the Darwin Gödel Machine

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any
import websockets
import threading
import time

from darwin_godel_orchestrator import DarwinGodelMachine
from enhanced_darwin_godel_core import (
    EvolutionParameters, EvolutionState, FitnessObjective, 
    TradingStrategy, EvolutionStatus
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DGMMonitor:
    """Real-time monitoring and control for Darwin Gödel Machine"""
    
    def __init__(self, dgm: DarwinGodelMachine):
        self.dgm = dgm
        self.websocket_clients = set()
        self.monitoring_data = {
            'current_state': None,
            'performance_history': [],
            'verification_events': [],
            'system_metrics': {}
        }
        
        # Setup callbacks
        self.dgm.add_generation_callback(self.on_generation_complete)
        self.dgm.add_verification_callback(self.on_strategy_verified)
        self.dgm.add_completion_callback(self.on_evolution_complete)
        
    def on_generation_complete(self, generation: int, state: EvolutionState):
        """Callback for generation completion"""
        logger.info(f"Generation {generation} complete - Best fitness: {state.best_fitness:.4f}")
        
        # Update monitoring data
        self.monitoring_data['current_state'] = {
            'generation': generation,
            'best_fitness': state.best_fitness,
            'average_fitness': state.average_fitness,
            'verified_count': len(state.verified_strategies),
            'status': state.status.value,
            'timestamp': datetime.now().isoformat()
        }
        
        self.monitoring_data['performance_history'].append({
            'generation': generation,
            'best_fitness': state.best_fitness,
            'average_fitness': state.average_fitness,
            'timestamp': datetime.now().isoformat()
        })
        
        # Broadcast to websocket clients
        self.broadcast_update('generation_complete', self.monitoring_data['current_state'])
    
    def on_strategy_verified(self, strategy: TradingStrategy, verification_result: Dict):
        """Callback for strategy verification"""
        logger.info(f"Strategy {strategy.name} verified: {verification_result['verified']}")
        
        verification_event = {
            'strategy_id': strategy.id,
            'strategy_name': strategy.name,
            'verified': verification_result['verified'],
            'fitness_score': strategy.fitness_score,
            'verification_time': verification_result['verification_time'],
            'generation': strategy.generation,
            'timestamp': datetime.now().isoformat()
        }
        
        self.monitoring_data['verification_events'].append(verification_event)
        
        # Keep only last 100 verification events
        if len(self.monitoring_data['verification_events']) > 100:
            self.monitoring_data['verification_events'] = self.monitoring_data['verification_events'][-100:]
        
        # Broadcast verification event
        self.broadcast_update('strategy_verified', verification_event)
    
    def on_evolution_complete(self, state: EvolutionState):
        """Callback for evolution completion"""
        logger.info(f"Evolution complete - Final best fitness: {state.best_fitness:.4f}")
        
        completion_data = {
            'final_generation': state.generation,
            'best_fitness': state.best_fitness,
            'verified_strategies': len(state.verified_strategies),
            'total_time': (state.end_time - state.start_time).total_seconds() if state.end_time and state.start_time else 0,
            'status': state.status.value,
            'timestamp': datetime.now().isoformat()
        }
        
        # Broadcast completion
        self.broadcast_update('evolution_complete', completion_data)
    
    def broadcast_update(self, event_type: str, data: Dict):
        """Broadcast update to all connected websocket clients"""
        if not self.websocket_clients:
            return
        
        message = json.dumps({
            'type': event_type,
            'data': data,
            'timestamp': datetime.now().isoformat()
        })
        
        # Send to all connected clients
        disconnected_clients = set()
        for client in self.websocket_clients:
            try:
                asyncio.create_task(client.send(message))
            except Exception as e:
                logger.error(f"Failed to send message to client: {e}")
                disconnected_clients.add(client)
        
        # Remove disconnected clients
        self.websocket_clients -= disconnected_clients
    
    async def websocket_handler(self, websocket, path):
        """Handle websocket connections for real-time monitoring"""
        logger.info(f"New websocket client connected from {websocket.remote_address}")
        self.websocket_clients.add(websocket)
        
        try:
            # Send current state to new client
            if self.monitoring_data['current_state']:
                await websocket.send(json.dumps({
                    'type': 'current_state',
                    'data': self.monitoring_data['current_state']
                }))
            
            # Keep connection alive
            async for message in websocket:
                # Handle client messages (control commands, etc.)
                try:
                    data = json.loads(message)
                    await self.handle_client_message(websocket, data)
                except json.JSONDecodeError:
                    await websocket.send(json.dumps({
                        'type': 'error',
                        'message': 'Invalid JSON'
                    }))
        
        except websockets.exceptions.ConnectionClosed:
            logger.info("Websocket client disconnected")
        finally:
            self.websocket_clients.discard(websocket)
    
    async def handle_client_message(self, websocket, data: Dict):
        """Handle messages from websocket clients"""
        message_type = data.get('type')
        
        if message_type == 'stop_evolution':
            logger.info("Received stop evolution command from client")
            self.dgm.stop_evolution()
            await websocket.send(json.dumps({
                'type': 'command_acknowledged',
                'message': 'Evolution stop requested'
            }))
        
        elif message_type == 'get_status':
            await websocket.send(json.dumps({
                'type': 'status_response',
                'data': {
                    'is_running': self.dgm.is_running,
                    'current_state': self.monitoring_data['current_state'],
                    'performance_history': self.monitoring_data['performance_history'][-20:]  # Last 20 generations
                }
            }))
        
        elif message_type == 'get_best_strategies':
            count = data.get('count', 10)
            verified_only = data.get('verified_only', False)
            best_strategies = self.dgm.get_best_strategies(count, verified_only)
            
            strategies_data = [
                {
                    'id': s.id,
                    'name': s.name,
                    'fitness_score': s.fitness_score,
                    'is_verified': s.is_verified,
                    'generation': s.generation,
                    'conditions_count': len(s.conditions),
                    'win_rate': s.backtest_results.get('win_rate', 0),
                    'sharpe_ratio': s.backtest_results.get('sharpe_ratio', 0)
                }
                for s in best_strategies
            ]
            
            await websocket.send(json.dumps({
                'type': 'best_strategies_response',
                'data': strategies_data
            }))
    
    def start_websocket_server(self, host='localhost', port=8765):
        """Start websocket server for real-time monitoring"""
        logger.info(f"Starting websocket server on {host}:{port}")
        
        server = websockets.serve(self.websocket_handler, host, port)
        return server

class DGMController:
    """High-level controller for managing multiple DGM instances"""
    
    def __init__(self):
        self.active_dgms: Dict[str, DarwinGodelMachine] = {}
        self.monitors: Dict[str, DGMMonitor] = {}
        self.evolution_tasks: Dict[str, asyncio.Task] = {}
    
    async def start_evolution(self, 
                            pair: str, 
                            timeframe: str = "1H",
                            evolution_params: EvolutionParameters = None,
                            coq_path: str = "coqc") -> str:
        """Start evolution for a currency pair"""
        
        job_id = f"{pair}_{timeframe}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Create DGM instance
        if not evolution_params:
            evolution_params = EvolutionParameters(
                population_size=50,
                max_generations=30,
                mutation_rate=0.15,
                fitness_objective=FitnessObjective.SHARPE_RATIO
            )
        
        dgm = DarwinGodelMachine(
            evolution_params=evolution_params,
            coq_path=coq_path,
            max_workers=4
        )
        
        # Create monitor
        monitor = DGMMonitor(dgm)
        
        # Store instances
        self.active_dgms[job_id] = dgm
        self.monitors[job_id] = monitor
        
        # Start evolution task
        evolution_task = asyncio.create_task(
            self._run_evolution(dgm, pair, timeframe, job_id)
        )
        self.evolution_tasks[job_id] = evolution_task
        
        logger.info(f"Started evolution job {job_id} for {pair} {timeframe}")
        return job_id
    
    async def _run_evolution(self, dgm: DarwinGodelMachine, pair: str, timeframe: str, job_id: str):
        """Run evolution and handle completion"""
        try:
            logger.info(f"Running evolution for {pair} {timeframe}")
            
            # Set date range (last year for backtesting)
            end_date = datetime.now() - timedelta(days=30)
            start_date = end_date - timedelta(days=365)
            
            # Run evolution
            async for state in dgm.evolve_strategies(pair, timeframe, start_date, end_date):
                # Evolution state is automatically handled by callbacks
                pass
            
            # Export results
            results_file = f"dgm_results_{job_id}.json"
            dgm.export_results(results_file)
            
            logger.info(f"Evolution {job_id} completed successfully")
            
        except Exception as e:
            logger.error(f"Evolution {job_id} failed: {e}")
        finally:
            # Cleanup
            if job_id in self.evolution_tasks:
                del self.evolution_tasks[job_id]
    
    def stop_evolution(self, job_id: str) -> bool:
        """Stop a running evolution"""
        if job_id in self.active_dgms:
            self.active_dgms[job_id].stop_evolution()
            logger.info(f"Stopped evolution {job_id}")
            return True
        return False
    
    def get_evolution_status(self, job_id: str) -> Dict[str, Any]:
        """Get status of an evolution job"""
        if job_id not in self.monitors:
            return {'error': 'Job not found'}
        
        monitor = self.monitors[job_id]
        return {
            'job_id': job_id,
            'current_state': monitor.monitoring_data['current_state'],
            'performance_history': monitor.monitoring_data['performance_history'],
            'verification_events': monitor.monitoring_data['verification_events'][-10:],  # Last 10 events
            'is_running': job_id in self.evolution_tasks and not self.evolution_tasks[job_id].done()
        }
    
    def list_active_jobs(self) -> List[Dict[str, Any]]:
        """List all active evolution jobs"""
        jobs = []
        for job_id in self.active_dgms:
            status = self.get_evolution_status(job_id)
            jobs.append(status)
        return jobs
    
    async def start_monitoring_server(self, host='localhost', port=8765):
        """Start websocket monitoring server for all jobs"""
        # For simplicity, we'll use the first monitor's websocket handler
        # In production, you'd want a unified monitoring server
        if self.monitors:
            first_monitor = next(iter(self.monitors.values()))
            server = await first_monitor.start_websocket_server(host, port)
            logger.info(f"Monitoring server started on {host}:{port}")
            return server
        else:
            logger.warning("No monitors available to start server")

# Example usage and testing
async def example_usage():
    """Complete example of using the Darwin Gödel Machine"""
    
    print("🧬 Darwin Gödel Machine - Forex Strategy Evolution Example")
    print("=" * 60)
    
    # Create controller
    controller = DGMController()
    
    # Configure evolution parameters
    evolution_params = EvolutionParameters(
        population_size=30,  # Smaller for demo
        max_generations=15,   # Fewer generations for demo
        mutation_rate=0.2,
        crossover_rate=0.8,
        elite_size=5,
        fitness_objective=FitnessObjective.SHARPE_RATIO,
        verification_frequency=3  # Verify every 3 generations
    )
    
    try:
        # Start evolution for EUR/USD
        print("\n🚀 Starting evolution for EURUSD 1H...")
        job_id = await controller.start_evolution(
            pair="EURUSD",
            timeframe="1H", 
            evolution_params=evolution_params
        )
        
        print(f"📋 Evolution job started: {job_id}")
        
        # Monitor progress
        print("\n📊 Monitoring evolution progress...")
        
        while True:
            status = controller.get_evolution_status(job_id)
            
            if 'error' in status:
                print(f"❌ Error: {status['error']}")
                break
            
            current_state = status.get('current_state')
            if current_state:
                print(f"Generation {current_state['generation']}: "
                      f"Best fitness: {current_state['best_fitness']:.4f}, "
                      f"Verified: {current_state['verified_count']}")
            
            # Check if evolution is complete
            if not status['is_running']:
                print("\n✅ Evolution completed!")
                break
            
            # Wait before next check
            await asyncio.sleep(5)
        
        # Get final results
        print("\n📈 Final Results:")
        print("-" * 40)
        
        final_status = controller.get_evolution_status(job_id)
        if final_status['current_state']:
            state = final_status['current_state']
            print(f"Best fitness achieved: {state['best_fitness']:.4f}")
            print(f"Total verified strategies: {state['verified_count']}")
            print(f"Final generation: {state['generation']}")
        
        # Get best strategies
        dgm = controller.active_dgms[job_id]
        best_strategies = dgm.get_best_strategies(5, verified_only=True)
        
        if best_strategies:
            print(f"\n🏆 Top {len(best_strategies)} Verified Strategies:")
            for i, strategy in enumerate(best_strategies, 1):
                print(f"{i}. {strategy.name}")
                print(f"   Fitness: {strategy.fitness_score:.4f}")
                print(f"   Win Rate: {strategy.backtest_results.get('win_rate', 0):.2%}")
                print(f"   Sharpe Ratio: {strategy.backtest_results.get('sharpe_ratio', 0):.2f}")
                print(f"   Conditions: {len(strategy.conditions)}")
                if strategy.is_verified:
                    print(f"   ✓ Formally verified")
                print()
        
        # Get forex genome
        genome = dgm.get_forex_genome("EURUSD", "1H")
        if genome:
            print("🧬 Forex Genome Generated:")
            print(f"   Most effective indicators: {genome['most_effective_indicators']}")
            print(f"   Strategies tested: {genome['total_strategies_tested']}")
            print(f"   Verification success rate: {genome['market_insights']['verification_success_rate']:.2%}")
        
        print(f"\n💾 Results exported to: dgm_results_{job_id}.json")
        
    except KeyboardInterrupt:
        print("\n⏹️  Evolution stopped by user")
        controller.stop_evolution(job_id)
    
    except Exception as e:
        print(f"\n❌ Error during evolution: {e}")
        logger.exception("Evolution failed")
    
    print("\n🎯 Darwin Gödel Machine demonstration completed!")

# WebSocket monitoring client example
async def websocket_monitoring_client():
    """Example websocket client for real-time monitoring"""
    
    uri = "ws://localhost:8765"
    
    try:
        async with websockets.connect(uri) as websocket:
            print("📡 Connected to DGM monitoring server")
            
            # Send status request
            await websocket.send(json.dumps({'type': 'get_status'}))
            
            # Listen for updates
            async for message in websocket:
                data = json.loads(message)
                
                if data['type'] == 'generation_complete':
                    state = data['data']
                    print(f"🔄 Generation {state['generation']}: "
                          f"Fitness: {state['best_fitness']:.4f}")
                
                elif data['type'] == 'strategy_verified':
                    event = data['data']
                    status = "✅ VERIFIED" if event['verified'] else "❌ FAILED"
                    print(f"🔍 {status}: {event['strategy_name']} "
                          f"(Fitness: {event['fitness_score']:.4f})")
                
                elif data['type'] == 'evolution_complete':
                    completion = data['data']
                    print(f"🏁 Evolution completed! "
                          f"Best fitness: {completion['best_fitness']:.4f}")
                    break
    
    except Exception as e:
        print(f"❌ Monitoring client error: {e}")

if __name__ == "__main__":
    # Run the example
    print("🧬 Starting Darwin Gödel Machine Example...")
    
    # You can run either the full example or just the monitoring client
    asyncio.run(example_usage())
    
    # Or run the monitoring client if you have a server running:
    # asyncio.run(websocket_monitoring_client())