# tests/test_mt5_integration.py
import pytest
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from trading.mt5_integration import (
    MT5Integration, MT5SafetyController, SafetyLimits, TradingOrder, Position,
    TradingMode, OrderType, OrderStatus
)

class TestMT5Integration:
    def setup_method(self):
        """Setup test environment"""
        self.safety_limits = SafetyLimits(
            max_daily_loss=1000.0,
            max_position_size=1.0,
            max_open_positions=5,
            max_risk_per_trade=100.0,
            allowed_symbols=["EURUSD", "GBPUSD", "USDJPY"],
            trading_hours_start="00:00",  # 24-hour trading for tests
            trading_hours_end="23:59",
            emergency_stop_loss=2000.0
        )
        self.mt5 = MT5Integration(self.safety_limits, TradingMode.DUMMY)
    
    def test_order_creation(self):
        """Test order creation"""
        order = self.mt5.create_order(
            symbol="EURUSD",
            order_type=OrderType.BUY,
            volume=0.1,
            price=1.1000,
            stop_loss=1.0950,
            take_profit=1.1100,
            comment="Test order"
        )
        
        assert order.symbol == "EURUSD"
        assert order.order_type == OrderType.BUY
        assert order.volume == 0.1
        assert order.price == 1.1000
        assert order.stop_loss == 1.0950
        assert order.take_profit == 1.1100
        assert order.status == OrderStatus.PENDING
        assert order.order_id is not None
    
    def test_dummy_mode_execution(self):
        """Test order execution in dummy mode"""
        order = self.mt5.create_order(
            symbol="EURUSD",
            order_type=OrderType.BUY,
            volume=0.1,
            price=1.1000
        )
        
        success, message, position_id = self.mt5.submit_order(order)
        
        assert success == True
        assert "dummy mode" in message
        assert position_id is not None
        
        # Check position was created
        positions = self.mt5.get_positions()
        assert len(positions) == 1
        assert position_id in positions
    
    def test_safety_limits_position_size(self):
        """Test position size safety limits"""
        order = self.mt5.create_order(
            symbol="EURUSD",
            order_type=OrderType.BUY,
            volume=2.0,  # Exceeds max_position_size of 1.0
            price=1.1000
        )
        
        success, message, position_id = self.mt5.submit_order(order)
        
        assert success == False
        assert "exceeds limit" in message
        assert position_id is None
    
    def test_safety_limits_unauthorized_symbol(self):
        """Test unauthorized symbol rejection"""
        order = self.mt5.create_order(
            symbol="BTCUSD",  # Not in allowed_symbols
            order_type=OrderType.BUY,
            volume=0.1,
            price=50000.0
        )
        
        success, message, position_id = self.mt5.submit_order(order)
        
        assert success == False
        assert "not in allowed list" in message
        assert position_id is None
    
    def test_safety_limits_max_open_positions(self):
        """Test maximum open positions limit"""
        # Create maximum allowed positions
        for i in range(self.safety_limits.max_open_positions):
            order = self.mt5.create_order(
                symbol="EURUSD",
                order_type=OrderType.BUY,
                volume=0.1,
                price=1.1000 + i * 0.0001
            )
            success, _, _ = self.mt5.submit_order(order)
            assert success == True
        
        # Try to create one more (should fail)
        order = self.mt5.create_order(
            symbol="EURUSD",
            order_type=OrderType.BUY,
            volume=0.1,
            price=1.1100
        )
        
        success, message, position_id = self.mt5.submit_order(order)
        
        assert success == False
        assert "Maximum open positions" in message
        assert position_id is None
    
    def test_position_closing(self):
        """Test position closing functionality"""
        # Create and execute order
        order = self.mt5.create_order(
            symbol="EURUSD",
            order_type=OrderType.BUY,
            volume=0.1,
            price=1.1000
        )
        
        success, message, position_id = self.mt5.submit_order(order)
        assert success == True
        
        # Close position
        success, close_message = self.mt5.close_position(position_id, 1.1050)
        
        assert success == True
        assert "profit" in close_message
        
        # Check position was removed
        positions = self.mt5.get_positions()
        assert len(positions) == 0
    
    def test_daily_pnl_tracking(self):
        """Test daily P&L tracking"""
        # Create profitable position
        order = self.mt5.create_order(
            symbol="EURUSD",
            order_type=OrderType.BUY,
            volume=1.0,
            price=1.1000
        )
        
        success, message, position_id = self.mt5.submit_order(order)
        assert success == True
        
        # Close with profit
        success, message = self.mt5.close_position(position_id, 1.1050)
        assert success == True
        
        # Check daily P&L
        safety_status = self.mt5.get_safety_status()
        assert safety_status["daily_pnl"] > 0
    
    def test_emergency_stop_functionality(self):
        """Test emergency stop functionality"""
        # Create some positions
        for i in range(3):
            order = self.mt5.create_order(
                symbol="EURUSD",
                order_type=OrderType.BUY,
                volume=0.1,
                price=1.1000 + i * 0.0001
            )
            self.mt5.submit_order(order)
        
        # Trigger emergency stop
        result = self.mt5.emergency_stop()
        
        assert result["emergency_stop_activated"] == True
        assert len(result["closed_positions"]) == 3
        
        # Check all positions are closed
        positions = self.mt5.get_positions()
        assert len(positions) == 0
        
        # Check emergency stop status
        safety_status = self.mt5.get_safety_status()
        assert safety_status["emergency_stop_active"] == True
    
    def test_order_validation_integrity(self):
        """Test order integrity validation"""
        # Test invalid order (negative volume)
        order = self.mt5.create_order(
            symbol="EURUSD",
            order_type=OrderType.BUY,
            volume=-0.1,  # Invalid
            price=1.1000
        )
        
        success, message, position_id = self.mt5.submit_order(order)
        
        assert success == False
        assert "integrity validation" in message
    
    def test_stop_loss_take_profit_logic(self):
        """Test stop loss and take profit validation"""
        # Invalid stop loss for BUY order (stop loss above price)
        order = self.mt5.create_order(
            symbol="EURUSD",
            order_type=OrderType.BUY,
            volume=0.1,
            price=1.1000,
            stop_loss=1.1050,  # Invalid: stop loss above buy price
            take_profit=1.1100
        )
        
        success, message, position_id = self.mt5.submit_order(order)
        
        assert success == False
        assert "integrity validation" in message

class TestMT5SafetyController:
    def setup_method(self):
        """Setup test environment"""
        self.safety_limits = SafetyLimits(
            max_daily_loss=1000.0,
            max_position_size=1.0,
            max_open_positions=5,
            max_risk_per_trade=100.0,
            allowed_symbols=["EURUSD", "GBPUSD", "USDJPY"],
            trading_hours_start="00:00",  # Always allow for testing
            trading_hours_end="23:59",
            emergency_stop_loss=2000.0
        )
        self.controller = MT5SafetyController(self.safety_limits, TradingMode.DUMMY)
    
    def test_trading_hours_validation(self):
        """Test trading hours validation"""
        # Create controller with restricted hours
        restricted_limits = SafetyLimits(
            max_daily_loss=1000.0,
            max_position_size=1.0,
            max_open_positions=5,
            max_risk_per_trade=100.0,
            allowed_symbols=["EURUSD"],
            trading_hours_start="09:00",
            trading_hours_end="17:00",
            emergency_stop_loss=2000.0
        )
        
        restricted_controller = MT5SafetyController(restricted_limits, TradingMode.DUMMY)
        
        # Create order
        order = TradingOrder(
            symbol="EURUSD",
            order_type=OrderType.BUY,
            volume=0.1,
            price=1.1000,
            stop_loss=None,
            take_profit=None,
            comment="Test",
            magic_number=12345,
            timestamp=datetime.now(),
            order_id="test_order_1",
            status=OrderStatus.PENDING
        )
        
        # Check if current time affects validation
        is_valid, message = restricted_controller.validate_order(order)
        
        # The result depends on current time, but we can check the message format
        if not is_valid and "trading hours" in message:
            assert "09:00-17:00" in message
    
    def test_daily_loss_limit(self):
        """Test daily loss limit enforcement"""
        # Simulate reaching daily loss limit
        self.controller.daily_pnl = -1000.0  # At the limit
        
        order = TradingOrder(
            symbol="EURUSD",
            order_type=OrderType.BUY,
            volume=0.1,
            price=1.1000,
            stop_loss=None,
            take_profit=None,
            comment="Test",
            magic_number=12345,
            timestamp=datetime.now(),
            order_id="test_order_1",
            status=OrderStatus.PENDING
        )
        
        is_valid, message = self.controller.validate_order(order)
        
        assert is_valid == False
        assert "Daily loss limit reached" in message
    
    def test_risk_per_trade_validation(self):
        """Test risk per trade validation"""
        # Temporarily increase position size limit to test risk validation
        original_limit = self.safety_limits.max_position_size
        self.safety_limits.max_position_size = 5000.0
        
        order = TradingOrder(
            symbol="EURUSD",
            order_type=OrderType.BUY,
            volume=1000.0,  # Large volume
            price=1.1000,
            stop_loss=1.0900,  # Risk = abs(1.1000 - 1.0900) * 1000 = 0.01 * 1000 = 10 (under limit)
            take_profit=None,
            comment="Test",
            magic_number=12345,
            timestamp=datetime.now(),
            order_id="test_order_1",
            status=OrderStatus.PENDING
        )
        
        is_valid, message = self.controller.validate_order(order)
        
        # Should be valid as it's under the limit
        assert is_valid == True
        
        # Now test exceeding the limit
        order.stop_loss = 1.0800  # Risk = abs(1.1000 - 1.0800) * 1000 = 0.02 * 1000 = 20 (still under limit)
        order.stop_loss = 1.0000  # Risk = abs(1.1000 - 1.0000) * 1000 = 0.1 * 1000 = 100 (at limit)
        order.stop_loss = 0.9000  # Risk = abs(1.1000 - 0.9000) * 1000 = 0.2 * 1000 = 200 (exceeds limit of 100)
        
        is_valid, message = self.controller.validate_order(order)
        
        # Restore original limit
        self.safety_limits.max_position_size = original_limit
        
        assert is_valid == False
        assert "Risk per trade" in message
    
    def test_safety_violations_logging(self):
        """Test safety violations are properly logged"""
        # Create order with unauthorized symbol
        order = TradingOrder(
            symbol="BTCUSD",  # Not in allowed symbols
            order_type=OrderType.BUY,
            volume=0.1,
            price=50000.0,
            stop_loss=None,
            take_profit=None,
            comment="Test",
            magic_number=12345,
            timestamp=datetime.now(),
            order_id="test_order_1",
            status=OrderStatus.PENDING
        )
        
        is_valid, message = self.controller.validate_order(order)
        
        assert is_valid == False
        assert len(self.controller.safety_violations) > 0
        
        violation = self.controller.safety_violations[-1]
        assert violation["type"] == "unauthorized_symbol"
        assert violation["symbol"] == "BTCUSD"
        assert violation["order_id"] == "test_order_1"
    
    def test_emergency_stop_trigger(self):
        """Test emergency stop trigger conditions"""
        # Temporarily increase position size limit for this test
        original_limit = self.safety_limits.max_position_size
        self.safety_limits.max_position_size = 10000.0  # Allow large position
        
        # Create and execute large order to generate big loss
        order = TradingOrder(
            symbol="EURUSD",
            order_type=OrderType.BUY,
            volume=10000.0,  # Large volume
            price=1.1000,
            stop_loss=None,
            take_profit=None,
            comment="Test",
            magic_number=12345,
            timestamp=datetime.now(),
            order_id="test_order_1",
            status=OrderStatus.PENDING
        )
        
        success, message, position_id = self.controller.execute_order(order)
        assert success == True
        
        # Close position with loss to trigger emergency stop
        # Loss = (1.1000 - 0.9000) * 10000.0 = 2000.0 (exactly at emergency limit)
        success, message = self.controller.close_position(position_id, 0.9000)
        
        # Restore original limit
        self.safety_limits.max_position_size = original_limit
        
        assert success == True
        assert self.controller.emergency_stop_triggered == True
    
    def test_audit_trail_generation(self):
        """Test audit trail generation"""
        # Execute some orders
        order = TradingOrder(
            symbol="EURUSD",
            order_type=OrderType.BUY,
            volume=0.1,
            price=1.1000,
            stop_loss=None,
            take_profit=None,
            comment="Test",
            magic_number=12345,
            timestamp=datetime.now(),
            order_id="test_order_1",
            status=OrderStatus.PENDING
        )
        
        self.controller.execute_order(order)
        
        # Get audit trail
        audit_trail = self.controller.get_audit_trail()
        
        assert "trading_log" in audit_trail
        assert "safety_violations" in audit_trail
        assert "order_history" in audit_trail
        assert "current_positions" in audit_trail
        assert "safety_limits" in audit_trail
        
        assert len(audit_trail["trading_log"]) > 0
        assert len(audit_trail["order_history"]) > 0
    
    def test_daily_reset_functionality(self):
        """Test daily reset functionality"""
        # Set some daily P&L and emergency stop
        self.controller.daily_pnl = -500.0
        self.controller.emergency_stop_triggered = True
        
        # Simulate next day by changing the last reset date
        self.controller.last_reset_date = (datetime.now() - timedelta(days=1)).date()
        
        # Trigger daily reset check
        self.controller._check_daily_reset()
        
        assert self.controller.daily_pnl == 0.0
        assert self.controller.emergency_stop_triggered == False
    
    def test_safety_status_reporting(self):
        """Test safety status reporting"""
        status = self.controller.get_safety_status()
        
        required_fields = [
            "trading_mode", "emergency_stop_active", "daily_pnl",
            "open_positions_count", "daily_loss_limit", "remaining_daily_loss",
            "safety_violations_today", "trading_hours_active"
        ]
        
        for field in required_fields:
            assert field in status
        
        assert status["trading_mode"] == "dummy"
        assert isinstance(status["emergency_stop_active"], bool)
        assert isinstance(status["daily_pnl"], (int, float))
        assert isinstance(status["open_positions_count"], int)