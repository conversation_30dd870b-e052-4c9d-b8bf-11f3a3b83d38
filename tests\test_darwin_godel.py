#!/usr/bin/env python3
"""
Darwin-Gödel Machine (DGM) Test Suite: Determinism & Audit Trails

This test suite verifies:
- Reproducibility with deterministic behavior
- Complete audit trail integrity
- Tamper detection capabilities
- Cryptographic hash verification
- Evolution process auditability
"""

import pytest
import json
import tempfile
import os
from typing import List, Dict, Any

# Import the DGM implementation
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from evolution.darwin_godel_machine import (
    DarwinGodelMachine, 
    StrategyGenome, 
    AuditLogEntry, 
    OperationType
)


class TestDGMReproducibility:
    """Test Darwin-Gödel Machine reproducibility and determinism"""
    
    def test_dgm_reproducibility(self):
        """Test that DGM produces identical results with same seed"""
        # Create two DGM instances with same seed
        dgm1 = DarwinGodelMachine(seed=123)
        dgm2 = DarwinGodelMachine(seed=123)
        
        # Run evolution with same parameters
        result1, _ = dgm1.run_evolution(generations=10, population_size=20)
        result2, _ = dgm2.run_evolution(generations=10, population_size=20)
        
        # Results should be identical
        assert result1.parameters == result2.parameters
        assert result1.fitness == result2.fitness
        assert result1.generation == result2.generation
        
        # Verify audit logs are also identical
        audit1 = dgm1.get_evolution_audit_trail()
        audit2 = dgm2.get_evolution_audit_trail()
        
        assert audit1['seed'] == audit2['seed']
        assert audit1['generations_count'] == audit2['generations_count']
        assert len(dgm1.audit_log) == len(dgm2.audit_log)
    
    def test_dgm_different_seeds_produce_different_results(self):
        """Test that different seeds produce different results"""
        dgm1 = DarwinGodelMachine(seed=123)
        dgm2 = DarwinGodelMachine(seed=456)
        
        result1, _ = dgm1.run_evolution(generations=5, population_size=10)
        result2, _ = dgm2.run_evolution(generations=5, population_size=10)
        
        # Results should be different (with very high probability)
        assert result1.parameters != result2.parameters or result1.fitness != result2.fitness
        
        # Seeds should be different
        assert dgm1.seed != dgm2.seed
    
    def test_population_initialization_reproducibility(self):
        """Test that population initialization is reproducible"""
        dgm1 = DarwinGodelMachine(seed=789)
        dgm2 = DarwinGodelMachine(seed=789)
        
        pop1 = dgm1.initialize_population(size=15)
        pop2 = dgm2.initialize_population(size=15)
        
        assert len(pop1) == len(pop2)
        
        for i in range(len(pop1)):
            assert pop1[i].parameters == pop2[i].parameters
            assert pop1[i].id == pop2[i].id
            assert pop1[i].generation == pop2[i].generation
    
    def test_mutation_reproducibility(self):
        """Test that mutations are reproducible with same seed"""
        dgm1 = DarwinGodelMachine(seed=101)
        dgm2 = DarwinGodelMachine(seed=101)
        
        # Create identical parent genomes
        pop1 = dgm1.initialize_population(size=5)
        pop2 = dgm2.initialize_population(size=5)
        
        # Mutate first genome from each population
        child1 = dgm1.mutate(pop1[0])
        child2 = dgm2.mutate(pop2[0])
        
        # Mutations should be identical
        assert child1.parameters == child2.parameters
        assert child1.parent_ids == child2.parent_ids
    
    def test_crossover_reproducibility(self):
        """Test that crossover is reproducible with same seed"""
        dgm1 = DarwinGodelMachine(seed=202)
        dgm2 = DarwinGodelMachine(seed=202)
        
        # Create identical populations
        pop1 = dgm1.initialize_population(size=5)
        pop2 = dgm2.initialize_population(size=5)
        
        # Perform crossover on same parents
        child1 = dgm1.crossover(pop1[0], pop1[1])
        child2 = dgm2.crossover(pop2[0], pop2[1])
        
        # Crossover results should be identical
        assert child1.parameters == child2.parameters
        assert child1.parent_ids == child2.parent_ids


class TestDGMAuditTrail:
    """Test Darwin-Gödel Machine audit trail functionality"""
    
    def test_dgm_audit_log(self):
        """Test that audit log is created and contains required fields"""
        dgm = DarwinGodelMachine(seed=42, enable_audit=True)
        result, log = dgm.run_evolution(generations=3, return_audit=True)
        
        # Verify audit log exists and has entries
        assert isinstance(log, list)
        assert len(log) > 0
        
        # Verify all entries have required fields
        for entry in log:
            assert hasattr(entry, 'hash')
            assert hasattr(entry, 'parameters')
            assert hasattr(entry, 'timestamp')
            assert hasattr(entry, 'operation_type')
            assert entry.hash is not None
            assert entry.parameters is not None
    
    def test_audit_log_integrity_verification(self):
        """Test that audit log integrity can be verified"""
        dgm = DarwinGodelMachine(seed=42, enable_audit=True)
        dgm.run_evolution(generations=2, population_size=5)
        
        # Verify audit log integrity
        is_valid, errors = dgm.verify_audit_integrity()
        assert is_valid is True
        assert len(errors) == 0
        
        # Verify each entry individually
        for entry in dgm.audit_log:
            assert entry.verify_integrity() is True
    
    def test_audit_log_tamper_detection(self):
        """Test that tampering with audit log is detected"""
        dgm = DarwinGodelMachine(seed=42, enable_audit=True)
        dgm.run_evolution(generations=2, population_size=5)
        
        # Get original audit log
        original_log = dgm.audit_log.copy()
        
        # Tamper with an entry
        if len(dgm.audit_log) > 0:
            tampered_entry = dgm.audit_log[0]
            tampered_entry.hash = "FAKE_HASH_12345"
            
            # Verify tampering is detected
            is_valid, errors = dgm.verify_audit_integrity()
            assert is_valid is False
            assert len(errors) > 0
            assert "failed hash verification" in errors[0]
    
    def test_audit_log_chain_integrity(self):
        """Test that audit log chain integrity is maintained"""
        dgm = DarwinGodelMachine(seed=42, enable_audit=True)
        dgm.run_evolution(generations=2, population_size=5)
        
        # Verify chain integrity
        for i in range(1, len(dgm.audit_log)):
            current_entry = dgm.audit_log[i]
            previous_entry = dgm.audit_log[i-1]
            
            # Current entry's parent_hash should match previous entry's hash
            assert current_entry.parent_hash == previous_entry.hash
    
    def test_audit_log_operation_types(self):
        """Test that all expected operation types are logged"""
        dgm = DarwinGodelMachine(seed=42, enable_audit=True)
        dgm.run_evolution(generations=2, population_size=5)
        
        # Collect all operation types from audit log
        operation_types = set()
        for entry in dgm.audit_log:
            operation_types.add(entry.operation_type)
        
        # Verify expected operations are present
        expected_operations = {
            OperationType.INITIALIZATION,
            OperationType.FITNESS_CALCULATION,
            OperationType.GENERATION_EVOLUTION
        }
        
        # Should have at least the basic operations
        assert expected_operations.issubset(operation_types)
    
    def test_genome_integrity_verification(self):
        """Test that genome integrity can be verified"""
        dgm = DarwinGodelMachine(seed=42)
        population = dgm.initialize_population(size=5)
        
        # All genomes should have valid integrity
        for genome in population:
            assert genome.verify_integrity() is True
        
        # Tamper with a genome
        if len(population) > 0:
            genome = population[0]
            original_hash = genome.integrity_hash
            
            # Modify parameters
            genome.parameters['rsi_period'] = 999
            
            # Integrity should fail with old hash
            assert genome.verify_integrity() is False
            
            # Update hash and verify again
            genome.update_integrity_hash()
            assert genome.verify_integrity() is True
            assert genome.integrity_hash != original_hash
    
    def test_audit_log_export_import(self):
        """Test audit log export and import functionality"""
        dgm = DarwinGodelMachine(seed=42, enable_audit=True)
        dgm.run_evolution(generations=2, population_size=5)
        
        # Export audit log
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_file = f.name
        
        try:
            # Export
            export_success = dgm.export_complete_audit_log(temp_file)
            assert export_success is True
            
            # Verify file exists and has content
            assert os.path.exists(temp_file)
            with open(temp_file, 'r') as f:
                audit_data = json.load(f)
            
            assert 'metadata' in audit_data
            assert 'audit_log' in audit_data
            assert 'evolution_summary' in audit_data
            
            # Import and verify
            is_valid, errors = dgm.import_and_verify_audit_log(temp_file)
            if not is_valid:
                print(f"Import validation failed with errors: {errors}")
            assert is_valid is True
            assert len(errors) == 0
            
        finally:
            # Cleanup
            if os.path.exists(temp_file):
                os.unlink(temp_file)


class TestDGMEvolutionProcess:
    """Test Darwin-Gödel Machine evolution process"""
    
    def test_evolution_with_audit_disabled(self):
        """Test that evolution works with audit disabled"""
        dgm = DarwinGodelMachine(seed=42, enable_audit=False)
        result, log = dgm.run_evolution(generations=3, return_audit=True)
        
        # Should still return a result
        assert result is not None
        assert hasattr(result, 'fitness')
        assert hasattr(result, 'parameters')
        
        # Audit log should be empty or None
        assert log is None or len(log) == 0
    
    def test_fitness_calculation_audit(self):
        """Test that fitness calculations are properly audited"""
        dgm = DarwinGodelMachine(seed=42, enable_audit=True)
        population = dgm.initialize_population(size=3)
        
        # Calculate fitness for first genome
        mock_results = {
            'total_return': 0.15,
            'sharpe_ratio': 1.2,
            'max_drawdown': 0.08,
            'win_rate': 0.65,
            'trades_count': 100
        }
        
        original_log_length = len(dgm.audit_log)
        fitness = dgm.calculate_fitness(population[0], mock_results)
        
        # Should have added fitness calculation entry to audit log
        assert len(dgm.audit_log) > original_log_length
        
        # Find the fitness calculation entry
        fitness_entries = [
            entry for entry in dgm.audit_log 
            if entry.operation_type == OperationType.FITNESS_CALCULATION
        ]
        assert len(fitness_entries) > 0
        
        # Verify fitness calculation entry has expected data
        fitness_entry = fitness_entries[-1]  # Get the latest one
        assert fitness_entry.genome_id == population[0].id
        assert 'backtest_metrics' in fitness_entry.parameters
        assert 'new_fitness' in fitness_entry.result
    
    def test_generation_evolution_audit(self):
        """Test that generation evolution is properly audited"""
        dgm = DarwinGodelMachine(seed=42, enable_audit=True)
        population = dgm.initialize_population(size=5)
        
        # Set some fitness values
        for i, genome in enumerate(population):
            mock_results = {
                'total_return': 0.1 + i * 0.02,
                'sharpe_ratio': 1.0 + i * 0.1,
                'max_drawdown': 0.1,
                'win_rate': 0.6,
                'trades_count': 100
            }
            dgm.calculate_fitness(genome, mock_results)
        
        # Evolve generation
        original_log_length = len(dgm.audit_log)
        dgm.current_generation = 1
        next_gen = dgm.evolve_generation(population)
        
        # Should have added generation evolution entries
        assert len(dgm.audit_log) > original_log_length
        
        # Find generation evolution entries
        evolution_entries = [
            entry for entry in dgm.audit_log 
            if entry.operation_type == OperationType.GENERATION_EVOLUTION
        ]
        assert len(evolution_entries) >= 2  # Start and completion entries
    
    def test_complete_evolution_audit_trail(self):
        """Test complete evolution process audit trail"""
        dgm = DarwinGodelMachine(seed=42, enable_audit=True)
        result, audit_log = dgm.run_evolution(generations=3, population_size=10, return_audit=True)
        
        # Verify comprehensive audit trail
        audit_summary = dgm.get_audit_log_summary()
        
        assert audit_summary['total_entries'] > 0
        assert 'initialization' in audit_summary['operations']
        assert 'fitness_calculation' in audit_summary['operations']
        assert 'generation_evolution' in audit_summary['operations']
        
        # Verify evolution audit trail
        evolution_trail = dgm.get_evolution_audit_trail()
        
        assert evolution_trail['seed'] == 42
        assert evolution_trail['generations_count'] == 4  # Initial + 3 evolved
        assert evolution_trail['integrity_status']['valid'] is True
        assert len(evolution_trail['integrity_status']['errors']) == 0


class TestDGMEdgeCases:
    """Test Darwin-Gödel Machine edge cases and error handling"""
    
    def test_invalid_backtest_results(self):
        """Test handling of invalid backtest results"""
        dgm = DarwinGodelMachine(seed=42)
        population = dgm.initialize_population(size=1)
        
        # Test with missing required fields
        invalid_results = {'total_return': 0.1}  # Missing other required fields
        
        with pytest.raises(ValueError, match="Backtest results failed integrity check"):
            dgm.calculate_fitness(population[0], invalid_results)
    
    def test_genome_integrity_failure(self):
        """Test handling of genome integrity failures"""
        dgm = DarwinGodelMachine(seed=42)
        population = dgm.initialize_population(size=2)
        
        # Corrupt a genome's integrity hash
        population[0].integrity_hash = "CORRUPTED_HASH"
        
        # Should raise error when trying to mutate corrupted genome
        with pytest.raises(ValueError, match="failed integrity check"):
            dgm.mutate(population[0])
        
        # Should raise error when trying to use in crossover
        with pytest.raises(ValueError, match="failed integrity check"):
            dgm.crossover(population[0], population[1])
    
    def test_empty_population_handling(self):
        """Test handling of empty populations"""
        dgm = DarwinGodelMachine(seed=42)
        
        # Test with empty population
        empty_pop = []
        
        # Should handle gracefully
        audit_trail = dgm.get_evolution_audit_trail()
        assert audit_trail['generations_count'] == 0
    
    def test_audit_log_with_no_operations(self):
        """Test audit log behavior with no operations"""
        dgm = DarwinGodelMachine(seed=42, enable_audit=True)
        
        # Should have at least initialization entry
        assert len(dgm.audit_log) >= 1
        
        # Verify integrity of minimal log
        is_valid, errors = dgm.verify_audit_integrity()
        assert is_valid is True
        assert len(errors) == 0


if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v", "--tb=short"])