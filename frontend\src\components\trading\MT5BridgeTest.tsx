/**
 * MT5 Bridge Test Component
 * Simple component to test MT5 Bridge integration
 */

import { useState, useEffect } from 'react';
import { apiService } from '../../services/api';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Spinner } from '@/components/ui/spinner';
import { Badge } from '@/components/ui/badge';

export function MT5BridgeTest() {
  const [status, setStatus] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [orderResult, setOrderResult] = useState<any>(null);

  const fetchStatus = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await apiService.getMT5Status();
      setStatus(result);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch MT5 status');
    } finally {
      setLoading(false);
    }
  };

  const handleConnect = async () => {
    setLoading(true);
    setError(null);
    
    try {
      await apiService.connectMT5();
      fetchStatus();
    } catch (err: any) {
      setError(err.message || 'Failed to connect to MT5');
      setLoading(false);
    }
  };

  const handleDisconnect = async () => {
    setLoading(true);
    setError(null);
    
    try {
      await apiService.disconnectMT5();
      fetchStatus();
    } catch (err: any) {
      setError(err.message || 'Failed to disconnect from MT5');
      setLoading(false);
    }
  };

  const handlePlaceOrder = async () => {
    setLoading(true);
    setError(null);
    setOrderResult(null);
    
    try {
      const result = await apiService.placeMT5Order({
        symbol: 'EURUSD',
        orderType: 'BUY',
        volume: 0.1,
        price: undefined,
        stopLoss: undefined,
        takeProfit: undefined
      });
      
      setOrderResult(result);
      fetchStatus();
    } catch (err: any) {
      setError(err.message || 'Failed to place order');
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStatus();
  }, []);

  return (
    <div className="space-y-6">
      {/* Status */}
      <div className="rounded-md border p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium">Connection Status</h3>
          {status && (
            <Badge variant={status.connected ? "success" : "destructive"}>
              {status.connected ? "Connected" : "Disconnected"}
            </Badge>
          )}
        </div>
        
        {loading && (
          <div className="flex justify-center py-4">
            <Spinner size="md" />
          </div>
        )}
        
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        {status && (
          <div className="space-y-4">
            {status.accountInfo && (
              <div className="grid grid-cols-2 gap-4">
                <div className="rounded-md bg-muted p-3">
                  <div className="text-sm text-muted-foreground">Balance</div>
                  <div className="text-lg font-semibold">${status.accountInfo.balance.toFixed(2)}</div>
                </div>
                <div className="rounded-md bg-muted p-3">
                  <div className="text-sm text-muted-foreground">Equity</div>
                  <div className="text-lg font-semibold">${status.accountInfo.equity.toFixed(2)}</div>
                </div>
                <div className="rounded-md bg-muted p-3">
                  <div className="text-sm text-muted-foreground">Margin</div>
                  <div className="text-lg font-semibold">${status.accountInfo.margin.toFixed(2)}</div>
                </div>
                <div className="rounded-md bg-muted p-3">
                  <div className="text-sm text-muted-foreground">Free Margin</div>
                  <div className="text-lg font-semibold">${status.accountInfo.freeMargin.toFixed(2)}</div>
                </div>
              </div>
            )}
            
            {status.positions && status.positions.length > 0 && (
              <div>
                <h4 className="text-md font-medium mb-2">Open Positions ({status.positions.length})</h4>
                <div className="rounded-md border">
                  <div className="grid grid-cols-5 gap-4 p-3 border-b font-medium text-sm">
                    <div>Symbol</div>
                    <div>Type</div>
                    <div>Volume</div>
                    <div>Open Price</div>
                    <div>Profit</div>
                  </div>
                  {status.positions.map((pos: any) => (
                    <div key={pos.id} className="grid grid-cols-5 gap-4 p-3 border-b last:border-0 text-sm">
                      <div>{pos.symbol}</div>
                      <div>
                        <Badge variant={pos.type === 'BUY' ? 'success' : 'destructive'}>
                          {pos.type}
                        </Badge>
                      </div>
                      <div>{pos.volume} lots</div>
                      <div>{pos.openPrice}</div>
                      <div className={pos.profit >= 0 ? 'text-green-600' : 'text-red-600'}>
                        ${pos.profit?.toFixed(2) || '0.00'}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
      
      {/* Actions */}
      <div className="flex flex-wrap gap-4">
        <Button
          onClick={fetchStatus}
          disabled={loading}
          variant="outline"
        >
          {loading ? <Spinner size="sm" className="mr-2" /> : null}
          Refresh Status
        </Button>
        
        <Button
          onClick={handleConnect}
          disabled={loading || status?.connected}
          variant="default"
        >
          {loading ? <Spinner size="sm" className="mr-2" /> : null}
          Connect
        </Button>
        
        <Button
          onClick={handleDisconnect}
          disabled={loading || !status?.connected}
          variant="destructive"
        >
          {loading ? <Spinner size="sm" className="mr-2" /> : null}
          Disconnect
        </Button>
        
        <Button
          onClick={handlePlaceOrder}
          disabled={loading || !status?.connected}
          variant="secondary"
        >
          {loading ? <Spinner size="sm" className="mr-2" /> : null}
          Place Test Order
        </Button>
      </div>
      
      {/* Order Result */}
      {orderResult && (
        <Alert variant="success" className="border-green-500 bg-green-50">
          <AlertTitle>Order Placed Successfully</AlertTitle>
          <AlertDescription>
            <div className="mt-2">
              <p><strong>Order ID:</strong> {orderResult.orderId}</p>
              <p><strong>Status:</strong> {orderResult.success ? 'Success' : 'Failed'}</p>
            </div>
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}