"""
Test Suite for Strategy Chatbot Integration
Tests the chatbot's ability to generate Python trading strategies from natural language
"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime
from typing import Dict, List, Any

from src.chatbot.strategy_generator import StrategyGenerator, StrategyRequest, GeneratedStrategy
from src.chatbot.code_validator import CodeValidator
from src.chatbot.requirements_parser import RequirementsParser


class TestRequirementsParser:
    """Test natural language requirements parsing"""
    
    def setup_method(self):
        self.parser = RequirementsParser()
    
    def test_parse_simple_mean_reversion_request(self):
        """Test parsing simple mean reversion strategy request"""
        user_input = "I want a mean reversion strategy for EUR/USD using RSI"
        
        result = self.parser.parse(user_input)
        
        assert result.strategy_type == "mean_reversion"
        assert "EURUSD" in result.symbols
        assert "rsi" in result.indicators
        assert result.timeframe is None  # Should default later
    
    def test_parse_complex_momentum_request(self):
        """Test parsing complex momentum strategy with multiple parameters"""
        user_input = """
        Create a momentum strategy for GBPUSD and USDJPY on 1-hour timeframe.
        Use MACD and Bollinger Bands. Risk 2% per trade with maximum 3 positions.
        Stop loss at 50 pips, take profit at 100 pips.
        """
        
        result = self.parser.parse(user_input)
        
        assert result.strategy_type == "momentum"
        assert "GBPUSD" in result.symbols
        assert "USDJPY" in result.symbols
        assert result.timeframe == "1H"
        assert "macd" in result.indicators
        assert "bollinger_bands" in result.indicators
        assert result.risk_per_trade == 0.02
        assert result.max_positions == 3
        assert result.stop_loss_pips == 50
        assert result.take_profit_pips == 100
    
    def test_parse_machine_learning_request(self):
        """Test parsing ML-enhanced strategy request"""
        user_input = """
        Build a machine learning strategy using Random Forest classifier.
        Features should include RSI, MACD, and price momentum.
        Train on last 1000 bars, retrain every 100 bars.
        """
        
        result = self.parser.parse(user_input)
        
        assert result.strategy_type == "machine_learning"
        assert result.ml_model == "random_forest"
        assert "rsi" in result.features
        assert "macd" in result.features
        assert "price_momentum" in result.features
        assert result.training_bars == 1000
        assert result.retrain_frequency == 100
    
    def test_parse_invalid_request_raises_error(self):
        """Test that invalid requests raise appropriate errors"""
        user_input = "Make me rich quickly with no risk"
        
        with pytest.raises(ValueError, match="Unable to parse strategy requirements"):
            self.parser.parse(user_input)


class TestStrategyGenerator:
    """Test Python code generation from parsed requirements"""
    
    def setup_method(self):
        self.generator = StrategyGenerator()
        self.validator = Mock(spec=CodeValidator)
    
    def test_generate_mean_reversion_strategy(self):
        """Test generating a mean reversion strategy"""
        request = StrategyRequest(
            strategy_type="mean_reversion",
            symbols=["EURUSD"],
            indicators=["rsi"],
            timeframe="1H",
            risk_per_trade=0.02
        )
        
        result = self.generator.generate(request)
        
        assert isinstance(result, GeneratedStrategy)
        assert "class MeanReversionStrategy" in result.code
        assert "def calculate_rsi" in result.code
        assert "def generate_signal" in result.code
        assert "EURUSD" in result.code
        assert result.strategy_name.startswith("MeanReversion")
        assert len(result.test_cases) > 0
    
    def test_generate_momentum_strategy_with_multiple_symbols(self):
        """Test generating momentum strategy for multiple symbols"""
        request = StrategyRequest(
            strategy_type="momentum",
            symbols=["GBPUSD", "USDJPY"],
            indicators=["macd", "bollinger_bands"],
            timeframe="4H",
            risk_per_trade=0.015,
            max_positions=2
        )
        
        result = self.generator.generate(request)
        
        assert "class MomentumStrategy" in result.code
        assert "def calculate_macd" in result.code
        assert "def calculate_bollinger_bands" in result.code
        assert "GBPUSD" in result.code
        assert "USDJPY" in result.code
        assert "max_positions = 2" in result.code
        assert len(result.test_cases) >= 3  # Multiple symbols require more tests
    
    def test_generate_ml_strategy(self):
        """Test generating machine learning strategy"""
        request = StrategyRequest(
            strategy_type="machine_learning",
            ml_model="random_forest",
            features=["rsi", "macd", "price_momentum"],
            training_bars=1000,
            retrain_frequency=100
        )
        
        result = self.generator.generate(request)
        
        assert "from sklearn.ensemble import RandomForestClassifier" in result.code
        assert "class MLStrategy" in result.code
        assert "def prepare_features" in result.code
        assert "def train_model" in result.code
        assert "def predict_signal" in result.code
        assert "training_bars = 1000" in result.code
        assert "retrain_frequency = 100" in result.code
    
    def test_generate_strategy_includes_proper_imports(self):
        """Test that generated code includes all necessary imports"""
        request = StrategyRequest(
            strategy_type="mean_reversion",
            symbols=["EURUSD"],
            indicators=["rsi", "sma"]
        )
        
        result = self.generator.generate(request)
        
        assert "import numpy as np" in result.code
        assert "import pandas as pd" in result.code
        assert "from src.strategies.strategy_base import StrategyBase" in result.code
        assert "from typing import Dict, List, Optional" in result.code
    
    def test_generate_strategy_includes_test_cases(self):
        """Test that generated strategies include comprehensive test cases"""
        request = StrategyRequest(
            strategy_type="momentum",
            symbols=["EURUSD"],
            indicators=["macd"]
        )
        
        result = self.generator.generate(request)
        
        # Should generate test cases for different market conditions
        test_case_names = [tc.name for tc in result.test_cases]
        assert "test_bullish_momentum_signal" in test_case_names
        assert "test_bearish_momentum_signal" in test_case_names
        assert "test_no_signal_condition" in test_case_names
        assert "test_risk_management" in test_case_names


class TestCodeValidator:
    """Test validation of generated Python code"""
    
    def setup_method(self):
        self.validator = CodeValidator()
    
    def test_validate_syntactically_correct_code(self):
        """Test validation of syntactically correct code"""
        code = """
import numpy as np
from src.strategies.strategy_base import StrategyBase

class TestStrategy(StrategyBase):
    def __init__(self):
        super().__init__("Test", ["EURUSD"])
    
    def generate_signal(self, data):
        return {"signal": "hold", "confidence": 0.5}
"""
        
        result = self.validator.validate_syntax(code)
        
        assert result.is_valid is True
        assert len(result.errors) == 0
    
    def test_validate_code_with_syntax_error(self):
        """Test validation catches syntax errors"""
        code = """
import numpy as np

class TestStrategy:
    def __init__(self)  # Missing colon
        pass
"""
        
        result = self.validator.validate_syntax(code)
        
        assert result.is_valid is False
        assert len(result.errors) > 0
        assert "syntax error" in result.errors[0].lower()
    
    def test_validate_security_compliance(self):
        """Test that security validation catches dangerous operations"""
        dangerous_code = """
import os
import subprocess

class MaliciousStrategy:
    def generate_signal(self, data):
        os.system("rm -rf /")  # Dangerous!
        return {"signal": "buy"}
"""
        
        result = self.validator.validate_security(dangerous_code)
        
        assert result.is_valid is False
        assert any("dangerous operation" in error.lower() for error in result.errors)
    
    def test_validate_strategy_interface_compliance(self):
        """Test that generated strategies comply with required interface"""
        code = """
from src.strategies.strategy_base import StrategyBase

class TestStrategy(StrategyBase):
    def __init__(self):
        super().__init__("Test", ["EURUSD"])
    
    def generate_signal(self, data):
        return {"signal": "hold", "confidence": 0.5}
"""
        
        result = self.validator.validate_interface(code)
        
        assert result.is_valid is True
        assert result.has_required_methods is True
        assert result.inherits_from_base is True


class TestChatbotIntegration:
    """Test end-to-end chatbot integration"""
    
    def setup_method(self):
        self.parser = Mock(spec=RequirementsParser)
        self.generator = Mock(spec=StrategyGenerator)
        self.validator = Mock(spec=CodeValidator)
    
    def test_complete_strategy_generation_flow(self):
        """Test complete flow from natural language to validated Python code"""
        # Mock the parsing result
        parsed_request = StrategyRequest(
            strategy_type="mean_reversion",
            symbols=["EURUSD"],
            indicators=["rsi"]
        )
        self.parser.parse.return_value = parsed_request
        
        # Mock the generation result
        generated_strategy = GeneratedStrategy(
            code="# Generated strategy code",
            strategy_name="MeanReversionStrategy",
            test_cases=[],
            documentation="Strategy documentation"
        )
        self.generator.generate.return_value = generated_strategy
        
        # Mock validation success
        from src.chatbot.code_validator import ValidationResult
        self.validator.validate_all.return_value = ValidationResult(
            is_valid=True,
            errors=[],
            warnings=[]
        )
        
        # Test the integration
        from src.chatbot.strategy_chatbot import StrategyChatbot
        chatbot = StrategyChatbot(self.parser, self.generator, self.validator)
        
        result = chatbot.generate_strategy_from_text(
            "Create a mean reversion strategy for EUR/USD using RSI"
        )
        
        assert result.success is True
        assert result.generated_code is not None
        assert result.strategy_name == "MeanReversionStrategy"
        assert len(result.validation_errors) == 0
    
    def test_chatbot_handles_generation_errors_gracefully(self):
        """Test that chatbot handles errors gracefully and provides helpful feedback"""
        self.parser.parse.side_effect = ValueError("Unable to parse requirements")
        
        from src.chatbot.strategy_chatbot import StrategyChatbot
        chatbot = StrategyChatbot(self.parser, self.generator, self.validator)
        
        result = chatbot.generate_strategy_from_text("Invalid request")
        
        assert result.success is False
        assert "Unable to parse requirements" in result.error_message
        assert result.suggestions is not None
        assert len(result.suggestions) > 0
    
    def test_chatbot_provides_code_explanations(self):
        """Test that chatbot can explain generated code in natural language"""
        strategy_code = """
class MeanReversionStrategy(StrategyBase):
    def calculate_rsi(self, prices, period=14):
        # RSI calculation logic
        pass
    
    def generate_signal(self, data):
        rsi = self.calculate_rsi(data['close'])
        if rsi < 30:
            return {"signal": "buy", "confidence": 0.8}
        elif rsi > 70:
            return {"signal": "sell", "confidence": 0.8}
        return {"signal": "hold", "confidence": 0.5}
"""
        
        from src.chatbot.strategy_chatbot import StrategyChatbot
        chatbot = StrategyChatbot(self.parser, self.generator, self.validator)
        
        explanation = chatbot.explain_code(strategy_code)
        
        assert "mean reversion" in explanation.lower()
        assert "rsi" in explanation.lower()
        assert "buy signal when rsi below 30" in explanation.lower()
        assert "sell signal when rsi above 70" in explanation.lower()


class TestStrategyTemplates:
    """Test strategy template system for common patterns"""
    
    def test_get_available_templates(self):
        """Test retrieving available strategy templates"""
        from src.chatbot.strategy_templates import StrategyTemplateManager
        
        manager = StrategyTemplateManager()
        templates = manager.get_available_templates()
        
        assert len(templates) > 0
        assert any(t.name == "Mean Reversion RSI" for t in templates)
        assert any(t.name == "Momentum MACD" for t in templates)
        assert any(t.name == "Breakout Strategy" for t in templates)
    
    def test_customize_template_with_user_parameters(self):
        """Test customizing a template with user-specific parameters"""
        from src.chatbot.strategy_templates import StrategyTemplateManager
        
        manager = StrategyTemplateManager()
        template = manager.get_template("Mean Reversion RSI")
        
        customized = manager.customize_template(template, {
            "symbols": ["GBPUSD", "USDJPY"],
            "rsi_period": 21,
            "oversold_level": 25,
            "overbought_level": 75
        })
        
        assert "GBPUSD" in customized.code
        assert "USDJPY" in customized.code
        assert "rsi_period = 21" in customized.code
        assert "oversold_level = 25" in customized.code