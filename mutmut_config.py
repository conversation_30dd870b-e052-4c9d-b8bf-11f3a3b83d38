#!/usr/bin/env python3
"""
Mutation testing configuration for AI Enhanced Trading Platform
Advanced mutation testing to ensure test suite quality
"""

import os
from pathlib import Path

# Base configuration for mutmut
MUTMUT_CONFIG = {
    # Paths to mutate - focus on core business logic
    'paths_to_mutate': [
        'src/strategies/',
        'src/backtesting/',
        'src/data/',
        'src/risk_management/',
        'src/portfolio/',
        'src/indicators/'
    ],
    
    # Paths to exclude from mutation
    'paths_to_exclude': [
        'tests/',
        'demo_',
        '__pycache__',
        '.git',
        '.pytest_cache',
        'build/',
        'dist/',
        '*.egg-info',
        'venv/',
        'env/',
        '.venv/',
        '.env/'
    ],
    
    # Test command to run after each mutation
    'test_command': 'python -m pytest tests/ -x --tb=short --disable-warnings',
    
    # Mutation types to apply
    'mutation_types': [
        'number',      # Mutate numeric literals
        'string',      # Mutate string literals  
        'boolean',     # Mutate boolean values
        'operator',    # Mutate operators (+, -, *, /, ==, !=, etc.)
        'keyword',     # Mutate keywords (and, or, not, etc.)
        'name'         # Mutate variable names
    ],
    
    # Runner configuration
    'runner': 'python',
    'timeout_factor': 2.0,  # Allow 2x normal test time
    'backup': True,         # Create backups before mutation
    'swallow_output': False # Show test output for debugging
}

# Strategy-specific mutation focus areas
STRATEGY_MUTATION_TARGETS = {
    'RSI': {
        'file_patterns': ['*rsi*', '*RSI*'],
        'critical_parameters': [
            'overbought',      # RSI overbought threshold (typically 70)
            'oversold',        # RSI oversold threshold (typically 30)
            'period',          # RSI calculation period (typically 14)
            'signal_threshold' # Signal strength threshold
        ],
        'critical_operators': [
            '>=', '<=', '>', '<',  # Threshold comparisons
            '+', '-',              # Price calculations
            '==', '!='             # Signal comparisons
        ],
        'edge_cases': [
            'boundary_values',     # Test 69/31 vs 70/30 thresholds
            'period_variations',   # Test period 13/15 vs 14
            'signal_strength'      # Test signal threshold variations
        ]
    },
    
    'MACD': {
        'file_patterns': ['*macd*', '*MACD*'],
        'critical_parameters': [
            'fast_period',     # Fast EMA period (typically 12)
            'slow_period',     # Slow EMA period (typically 26)
            'signal_period',   # Signal line period (typically 9)
            'signal_threshold' # Crossover threshold
        ],
        'critical_operators': [
            '-',               # MACD line calculation (fast - slow)
            '>', '<',          # Crossover detection
            '>=', '<='         # Signal thresholds
        ],
        'edge_cases': [
            'crossover_detection', # Test crossover logic
            'period_relationships', # Ensure fast < slow
            'signal_timing'        # Test signal generation timing
        ]
    },
    
    'MovingAverage': {
        'file_patterns': ['*moving_average*', '*ma_*', '*sma_*', '*ema_*'],
        'critical_parameters': [
            'short_period',    # Short MA period
            'long_period',     # Long MA period
            'signal_threshold' # Crossover threshold
        ],
        'critical_operators': [
            '>', '<',          # Crossover comparisons
            '>=', '<='         # Threshold comparisons
        ],
        'edge_cases': [
            'crossover_logic',     # Test MA crossover detection
            'period_validation',   # Ensure short < long
            'signal_persistence'   # Test signal confirmation
        ]
    }
}

# Data validation mutation targets
DATA_VALIDATION_MUTATION_TARGETS = {
    'OHLC_Validation': {
        'file_patterns': ['*data_loader*', '*validator*'],
        'critical_checks': [
            'high >= low',         # OHLC relationship
            'high >= open',        # High vs open
            'high >= close',       # High vs close
            'low <= open',         # Low vs open
            'low <= close',        # Low vs close
            'price > 0'            # Positive prices
        ],
        'critical_operators': [
            '>=', '<=', '>', '<',  # Comparison operators
            '==', '!='             # Equality checks
        ],
        'edge_cases': [
            'boundary_conditions', # Test exact equality cases
            'null_handling',       # Test null/NaN handling
            'extreme_values'       # Test very large/small values
        ]
    },
    
    'Hash_Validation': {
        'file_patterns': ['*hash*', '*crypto*', '*integrity*'],
        'critical_operations': [
            'hash_calculation',    # SHA-256 hashing
            'hmac_signature',      # HMAC generation
            'integrity_check'      # Verification logic
        ],
        'critical_operators': [
            '==', '!=',            # Hash comparisons
            'and', 'or'            # Logical operations
        ],
        'edge_cases': [
            'hash_consistency',    # Same data = same hash
            'tamper_detection',    # Different data = different hash
            'signature_validation' # HMAC verification
        ]
    }
}

# Risk management mutation targets
RISK_MANAGEMENT_MUTATION_TARGETS = {
    'Position_Sizing': {
        'file_patterns': ['*position*', '*risk*', '*size*'],
        'critical_parameters': [
            'max_position_size',   # Maximum position size
            'risk_per_trade',      # Risk per trade
            'stop_loss_pct',       # Stop loss percentage
            'take_profit_pct'      # Take profit percentage
        ],
        'critical_operators': [
            '*', '/',              # Size calculations
            '<=', '>=',            # Limit checks
            '+', '-'               # PnL calculations
        ],
        'edge_cases': [
            'size_limits',         # Test position size limits
            'risk_calculations',   # Test risk amount calculations
            'percentage_bounds'    # Test percentage validations
        ]
    },
    
    'Stop_Loss': {
        'file_patterns': ['*stop*', '*loss*'],
        'critical_logic': [
            'stop_trigger',        # Stop loss trigger logic
            'price_comparison',    # Price vs stop level
            'order_execution'      # Stop order execution
        ],
        'critical_operators': [
            '<=', '>=',            # Stop level comparisons
            '-', '*'               # Stop level calculations
        ],
        'edge_cases': [
            'trigger_conditions',  # Test exact trigger conditions
            'slippage_handling',   # Test slippage scenarios
            'gap_scenarios'        # Test price gap handling
        ]
    }
}

# Backtesting mutation targets
BACKTESTING_MUTATION_TARGETS = {
    'Performance_Calculation': {
        'file_patterns': ['*backtest*', '*performance*', '*metrics*'],
        'critical_calculations': [
            'total_return',        # Total return calculation
            'sharpe_ratio',        # Sharpe ratio calculation
            'max_drawdown',        # Maximum drawdown
            'win_rate'             # Win rate calculation
        ],
        'critical_operators': [
            '/', '*',              # Return calculations
            '+', '-',              # PnL aggregation
            '>', '<'               # Performance comparisons
        ],
        'edge_cases': [
            'zero_division',       # Handle division by zero
            'negative_returns',    # Handle negative returns
            'empty_trades'         # Handle no trades scenario
        ]
    },
    
    'Trade_Execution': {
        'file_patterns': ['*trade*', '*execution*', '*order*'],
        'critical_logic': [
            'order_matching',      # Order matching logic
            'price_execution',     # Execution price logic
            'commission_calc',     # Commission calculation
            'slippage_calc'        # Slippage calculation
        ],
        'critical_operators': [
            '+', '-',              # Price adjustments
            '*',                   # Commission calculations
            '==', '!='             # Order matching
        ],
        'edge_cases': [
            'partial_fills',       # Test partial order fills
            'execution_timing',    # Test execution timing
            'cost_calculations'    # Test transaction costs
        ]
    }
}

# Mutation testing execution plan
MUTATION_EXECUTION_PLAN = {
    'phases': [
        {
            'name': 'Critical Path Mutations',
            'description': 'Focus on core trading logic and data validation',
            'targets': ['strategies', 'data_validation', 'risk_management'],
            'mutation_types': ['operator', 'number'],
            'timeout_factor': 1.5,
            'expected_mutations': 200
        },
        {
            'name': 'Parameter Boundary Testing',
            'description': 'Test strategy parameter edge cases',
            'targets': ['strategy_parameters'],
            'mutation_types': ['number', 'boolean'],
            'timeout_factor': 2.0,
            'expected_mutations': 150
        },
        {
            'name': 'Logic Flow Mutations',
            'description': 'Test conditional logic and control flow',
            'targets': ['backtesting', 'execution'],
            'mutation_types': ['keyword', 'operator'],
            'timeout_factor': 2.5,
            'expected_mutations': 100
        },
        {
            'name': 'Comprehensive Coverage',
            'description': 'Full mutation testing across all components',
            'targets': ['all'],
            'mutation_types': ['all'],
            'timeout_factor': 3.0,
            'expected_mutations': 500
        }
    ],
    
    'success_criteria': {
        'minimum_mutation_score': 0.85,  # 85% of mutations should be caught
        'critical_path_score': 0.95,    # 95% for critical trading logic
        'max_false_positives': 0.05,    # <5% false positive rate
        'performance_threshold': 300    # Max 300 seconds per phase
    },
    
    'reporting': {
        'generate_html': True,
        'generate_json': True,
        'include_coverage': True,
        'highlight_survivors': True,  # Highlight mutations that survived
        'categorize_by_risk': True    # Categorize by business risk level
    }
}

# Helper functions for mutation testing
def get_mutation_command(phase='all', target_path=None):
    """Generate mutmut command for specific phase or target"""
    
    base_cmd = "mutmut run"
    
    if target_path:
        base_cmd += f" --paths-to-mutate={target_path}"
    else:
        paths = ":".join(MUTMUT_CONFIG['paths_to_mutate'])
        base_cmd += f" --paths-to-mutate={paths}"
    
    # Add test command
    base_cmd += f" --runner='{MUTMUT_CONFIG['test_command']}'"
    
    # Add timeout
    base_cmd += f" --timeout-factor={MUTMUT_CONFIG['timeout_factor']}"
    
    return base_cmd

def get_critical_mutations():
    """Get list of critical mutations to prioritize"""
    
    critical_files = []
    
    # Add strategy files
    for strategy, config in STRATEGY_MUTATION_TARGETS.items():
        critical_files.extend(config['file_patterns'])
    
    # Add data validation files
    for component, config in DATA_VALIDATION_MUTATION_TARGETS.items():
        critical_files.extend(config['file_patterns'])
    
    # Add risk management files
    for component, config in RISK_MANAGEMENT_MUTATION_TARGETS.items():
        critical_files.extend(config['file_patterns'])
    
    return critical_files

def generate_mutation_report():
    """Generate comprehensive mutation testing report"""
    
    report_template = """
    # Mutation Testing Report
    
    ## Executive Summary
    - Total Mutations: {total_mutations}
    - Mutations Killed: {killed_mutations}
    - Mutation Score: {mutation_score:.2%}
    - Critical Path Score: {critical_score:.2%}
    
    ## Phase Results
    {phase_results}
    
    ## Surviving Mutations (High Priority)
    {surviving_mutations}
    
    ## Recommendations
    {recommendations}
    """
    
    return report_template

# Export configuration for mutmut
def setup_mutmut_config():
    """Setup mutmut configuration file"""
    
    config_content = f"""
[mutmut]
paths_to_mutate = {':'.join(MUTMUT_CONFIG['paths_to_mutate'])}
backup = {MUTMUT_CONFIG['backup']}
runner = {MUTMUT_CONFIG['runner']}
tests_dir = tests/
timeout_factor = {MUTMUT_CONFIG['timeout_factor']}
"""
    
    with open('.mutmut_config', 'w') as f:
        f.write(config_content)
    
    print("✅ Mutmut configuration created: .mutmut_config")

if __name__ == "__main__":
    # Setup configuration
    setup_mutmut_config()
    
    # Print mutation targets
    print("\n🎯 Mutation Testing Targets:")
    print(f"   Strategy Components: {len(STRATEGY_MUTATION_TARGETS)}")
    print(f"   Data Validation: {len(DATA_VALIDATION_MUTATION_TARGETS)}")
    print(f"   Risk Management: {len(RISK_MANAGEMENT_MUTATION_TARGETS)}")
    print(f"   Backtesting: {len(BACKTESTING_MUTATION_TARGETS)}")
    
    # Print execution plan
    print(f"\n📋 Execution Plan: {len(MUTATION_EXECUTION_PLAN['phases'])} phases")
    for phase in MUTATION_EXECUTION_PLAN['phases']:
        print(f"   {phase['name']}: {phase['expected_mutations']} mutations")
    
    print(f"\n🎉 Mutation testing configuration ready!")
    print("   Run: mutmut run --paths-to-mutate=src/")
    print("   View results: mutmut html")