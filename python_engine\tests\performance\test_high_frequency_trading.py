"""
High-Frequency Trading Performance Tests

Tests the system's ability to handle high-frequency trading scenarios
with strict latency and throughput requirements.
"""

import pytest
import time
import threading
import statistics
import sys
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

from services.darwin_godel.strategy_verifier import DarwinGodelVerifier
from services.darwin_godel.secure_executor import SecureStrategyExecutor

class TestHighFrequencyTrading:
    """Performance tests for high-frequency trading scenarios"""
    
    def setup_method(self):
        """Setup before each test"""
        self.verifier = DarwinGodelVerifier()
        self.executor = SecureStrategyExecutor()
        
        # High-frequency trading strategy
        self.hft_strategy = """
def trading_strategy(data, params):
    if len(data['close']) < 2:
        return {'signal': 'hold', 'confidence': 0.5}
    
    # Ultra-fast momentum detection
    current_price = data['close'][-1]
    prev_price = data['close'][-2]
    
    price_change = (current_price - prev_price) / prev_price
    
    if price_change > 0.001:  # 0.1% threshold
        return {'signal': 'buy', 'confidence': 0.9}
    elif price_change < -0.001:
        return {'signal': 'sell', 'confidence': 0.9}
    else:
        return {'signal': 'hold', 'confidence': 0.3}
"""
        
        # Sample market data for testing
        self.market_data = {
            'close': [100.0 + i * 0.01 for i in range(1000)],
            'high': [100.5 + i * 0.01 for i in range(1000)],
            'low': [99.5 + i * 0.01 for i in range(1000)],
            'volume': [10000] * 1000
        }
    
    @pytest.mark.performance
    def test_single_execution_latency(self):
        """Test single strategy execution latency (should be < 10ms)"""
        # Warm up
        for _ in range(5):
            self.executor.execute_strategy(self.hft_strategy, self.market_data, {})
        
        # Measure execution times
        execution_times = []
        for _ in range(100):
            start_time = time.perf_counter()
            result = self.executor.execute_strategy(self.hft_strategy, self.market_data, {})
            end_time = time.perf_counter()
            
            execution_time = (end_time - start_time) * 1000  # Convert to milliseconds
            execution_times.append(execution_time)
            
            # Verify result is valid
            assert 'signal' in result
            assert result['signal'] in ['buy', 'sell', 'hold']
        
        # Performance assertions
        avg_latency = statistics.mean(execution_times)
        p95_latency = statistics.quantiles(execution_times, n=20)[18]  # 95th percentile
        p99_latency = statistics.quantiles(execution_times, n=100)[98]  # 99th percentile
        
        print(f"\n=== Single Execution Latency ===")
        print(f"Average: {avg_latency:.2f}ms")
        print(f"P95: {p95_latency:.2f}ms")
        print(f"P99: {p99_latency:.2f}ms")
        print(f"Min: {min(execution_times):.2f}ms")
        print(f"Max: {max(execution_times):.2f}ms")
        
        # Performance requirements for HFT
        assert avg_latency < 10.0, f"Average latency {avg_latency:.2f}ms exceeds 10ms requirement"
        assert p95_latency < 20.0, f"P95 latency {p95_latency:.2f}ms exceeds 20ms requirement"
        assert p99_latency < 50.0, f"P99 latency {p99_latency:.2f}ms exceeds 50ms requirement"
    
    @pytest.mark.performance
    def test_throughput_capacity(self):
        """Test maximum throughput (executions per second)"""
        duration_seconds = 5
        execution_count = 0
        
        start_time = time.time()
        end_time = start_time + duration_seconds
        
        while time.time() < end_time:
            result = self.executor.execute_strategy(self.hft_strategy, self.market_data, {})
            execution_count += 1
            
            # Verify result validity
            assert 'signal' in result
        
        actual_duration = time.time() - start_time
        throughput = execution_count / actual_duration
        
        print(f"\n=== Throughput Test ===")
        print(f"Executions: {execution_count}")
        print(f"Duration: {actual_duration:.2f}s")
        print(f"Throughput: {throughput:.2f} executions/second")
        
        # Minimum throughput requirement
        assert throughput >= 100, f"Throughput {throughput:.2f} exec/s is below 100 exec/s requirement"
    
    @pytest.mark.performance
    def test_burst_handling(self):
        """Test handling of burst traffic (1000 executions in quick succession)"""
        burst_size = 1000
        execution_times = []
        
        start_time = time.perf_counter()
        
        for i in range(burst_size):
            exec_start = time.perf_counter()
            result = self.executor.execute_strategy(self.hft_strategy, self.market_data, {})
            exec_end = time.perf_counter()
            
            execution_times.append((exec_end - exec_start) * 1000)
            
            # Verify result
            assert 'signal' in result
            assert result['confidence'] >= 0.0
        
        total_time = time.perf_counter() - start_time
        avg_latency = statistics.mean(execution_times)
        
        print(f"\n=== Burst Handling Test ===")
        print(f"Burst size: {burst_size}")
        print(f"Total time: {total_time:.2f}s")
        print(f"Average latency: {avg_latency:.2f}ms")
        print(f"Throughput: {burst_size / total_time:.2f} exec/s")
        
        # Burst handling requirements
        assert total_time < 30.0, f"Burst processing took {total_time:.2f}s, should be < 30s"
        assert avg_latency < 25.0, f"Average burst latency {avg_latency:.2f}ms exceeds 25ms"
    
    @pytest.mark.performance
    def test_strategy_verification_performance(self):
        """Test strategy verification performance for HFT scenarios"""
        verification_times = []
        
        # Test multiple strategy variations
        strategies = [
            self.hft_strategy,
            self.hft_strategy.replace("0.001", "0.002"),
            self.hft_strategy.replace("0.9", "0.8"),
            self.hft_strategy.replace("momentum", "trend"),
        ]
        
        for strategy in strategies:
            for _ in range(10):  # 10 runs per strategy
                start_time = time.perf_counter()
                result = self.verifier.verify_strategy(strategy)
                end_time = time.perf_counter()
                
                verification_time = (end_time - start_time) * 1000
                verification_times.append(verification_time)
                
                # Verify result
                assert 'is_valid' in result
        
        avg_verification_time = statistics.mean(verification_times)
        p95_verification_time = statistics.quantiles(verification_times, n=20)[18]
        
        print(f"\n=== Strategy Verification Performance ===")
        print(f"Average: {avg_verification_time:.2f}ms")
        print(f"P95: {p95_verification_time:.2f}ms")
        print(f"Total verifications: {len(verification_times)}")
        
        # Verification performance requirements
        assert avg_verification_time < 100.0, f"Average verification {avg_verification_time:.2f}ms exceeds 100ms"
        assert p95_verification_time < 200.0, f"P95 verification {p95_verification_time:.2f}ms exceeds 200ms"
    
    @pytest.mark.performance
    def test_market_data_processing_speed(self):
        """Test processing speed with different market data sizes"""
        data_sizes = [100, 500, 1000, 5000, 10000]
        processing_times = {}
        
        for size in data_sizes:
            # Generate market data of specified size
            test_data = {
                'close': [100.0 + i * 0.01 for i in range(size)],
                'high': [100.5 + i * 0.01 for i in range(size)],
                'low': [99.5 + i * 0.01 for i in range(size)],
                'volume': [10000] * size
            }
            
            # Measure processing time
            times = []
            for _ in range(20):  # 20 runs per size
                start_time = time.perf_counter()
                result = self.executor.execute_strategy(self.hft_strategy, test_data, {})
                end_time = time.perf_counter()
                
                times.append((end_time - start_time) * 1000)
                assert 'signal' in result
            
            processing_times[size] = {
                'avg': statistics.mean(times),
                'p95': statistics.quantiles(times, n=20)[18] if len(times) >= 20 else max(times)
            }
        
        print(f"\n=== Market Data Processing Speed ===")
        for size, metrics in processing_times.items():
            print(f"Size {size:5d}: Avg {metrics['avg']:6.2f}ms, P95 {metrics['p95']:6.2f}ms")
        
        # Performance should scale reasonably with data size
        small_avg = processing_times[100]['avg']
        large_avg = processing_times[10000]['avg']
        
        # Large dataset shouldn't be more than 10x slower than small dataset
        scaling_factor = large_avg / small_avg
        assert scaling_factor < 10.0, f"Processing scales poorly: {scaling_factor:.2f}x slower for 100x data"
    
    @pytest.mark.performance
    def test_memory_efficiency_hft(self):
        """Test memory efficiency during high-frequency operations"""
        import psutil
        import gc
        
        process = psutil.Process()
        
        # Baseline memory
        gc.collect()
        baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Perform many HFT operations
        for i in range(1000):
            result = self.executor.execute_strategy(self.hft_strategy, self.market_data, {})
            assert 'signal' in result
            
            # Check memory every 100 operations
            if i % 100 == 0:
                current_memory = process.memory_info().rss / 1024 / 1024
                memory_growth = current_memory - baseline_memory
                
                # Memory growth should be reasonable
                assert memory_growth < 50.0, f"Memory grew by {memory_growth:.2f}MB after {i} operations"
        
        # Final memory check
        gc.collect()
        final_memory = process.memory_info().rss / 1024 / 1024
        total_growth = final_memory - baseline_memory
        
        print(f"\n=== Memory Efficiency ===")
        print(f"Baseline: {baseline_memory:.2f}MB")
        print(f"Final: {final_memory:.2f}MB")
        print(f"Growth: {total_growth:.2f}MB")
        
        # Total memory growth should be minimal
        assert total_growth < 20.0, f"Total memory growth {total_growth:.2f}MB exceeds 20MB limit"
    
    @pytest.mark.performance
    def test_error_handling_performance(self):
        """Test performance when handling errors in HFT scenarios"""
        # Strategy with intentional error
        error_strategy = """
def trading_strategy(data, params):
    if len(data['close']) < 2:
        return {'signal': 'hold', 'confidence': 0.5}
    
    # This will cause division by zero sometimes
    divisor = data['close'][-1] - 100.0  # Will be 0 when price is exactly 100
    result_value = 1.0 / divisor
    
    return {'signal': 'buy', 'confidence': 0.8}
"""
        
        error_handling_times = []
        successful_executions = 0
        error_count = 0
        
        # Test data that will cause errors
        error_prone_data = {
            'close': [100.0, 99.5, 100.0, 100.5, 100.0],  # Contains exact 100.0 values
            'high': [100.5, 100.0, 100.5, 101.0, 100.5],
            'low': [99.5, 99.0, 99.5, 100.0, 99.5],
            'volume': [10000] * 5
        }
        
        for _ in range(100):
            start_time = time.perf_counter()
            try:
                result = self.executor.execute_strategy(error_strategy, error_prone_data, {})
                successful_executions += 1
                assert 'signal' in result
            except Exception:
                error_count += 1
            
            end_time = time.perf_counter()
            error_handling_times.append((end_time - start_time) * 1000)
        
        avg_error_handling_time = statistics.mean(error_handling_times)
        
        print(f"\n=== Error Handling Performance ===")
        print(f"Successful: {successful_executions}")
        print(f"Errors: {error_count}")
        print(f"Avg handling time: {avg_error_handling_time:.2f}ms")
        
        # Error handling should still be fast
        assert avg_error_handling_time < 50.0, f"Error handling too slow: {avg_error_handling_time:.2f}ms"
        
        # Should handle some errors gracefully
        assert error_count > 0, "Expected some errors to test error handling"