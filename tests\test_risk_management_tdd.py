"""
TDD tests for Risk Management component
Focusing on position sizing and stop-loss/take-profit enforcement
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime
from unittest.mock import patch, MagicMock

# Import the backtesting components
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src', 'trading'))

from backtest import (
    BacktestEngine, TradingStrategy, BacktestConfig, BacktestResult,
    BacktestStatus, Trade
)


class TestRiskManagementTDD:
    """
    TDD tests for the Risk Management component
    """
    
    @pytest.fixture
    def sample_data(self):
        """Sample price data for testing"""
        return pd.DataFrame({
            'close': [100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110,
                     109, 108, 107, 106, 105, 104, 103, 102, 101, 100]
        })
    
    @pytest.fixture
    def strategy_with_stop_loss(self):
        """Strategy that implements stop-loss"""
        class StopLossStrategy(TradingStrategy):
            def __init__(self, stop_loss_pct=0.05):
                super().__init__(params={'stop_loss_pct': stop_loss_pct})
                self.entry_price = None
                self.stop_loss_price = None
            
            def generate_signals(self, data):
                signals = pd.Series(0, index=data.index)
                
                # Buy signal at the beginning
                signals.iloc[1] = 1
                
                # Check for stop-loss
                for i in range(2, len(data)):
                    if self.entry_price is not None and self.stop_loss_price is not None:
                        if data['close'].iloc[i] <= self.stop_loss_price:
                            signals.iloc[i] = -1  # Sell signal (stop-loss triggered)
                            self.entry_price = None
                            self.stop_loss_price = None
                
                return signals
            
            def on_trade_executed(self, trade):
                """Called when a trade is executed"""
                if trade.action == 'BUY':
                    self.entry_price = trade.price
                    self.stop_loss_price = trade.price * (1 - self.params['stop_loss_pct'])
                elif trade.action == 'SELL':
                    self.entry_price = None
                    self.stop_loss_price = None
        
        return StopLossStrategy()
    
    @pytest.fixture
    def strategy_with_take_profit(self):
        """Strategy that implements take-profit"""
        class TakeProfitStrategy(TradingStrategy):
            def __init__(self, take_profit_pct=0.05):
                super().__init__(params={'take_profit_pct': take_profit_pct})
                self.entry_price = None
                self.take_profit_price = None
            
            def generate_signals(self, data):
                signals = pd.Series(0, index=data.index)
                
                # Buy signal at the beginning
                signals.iloc[1] = 1
                
                # Check for take-profit
                for i in range(2, len(data)):
                    if self.entry_price is not None and self.take_profit_price is not None:
                        if data['close'].iloc[i] >= self.take_profit_price:
                            signals.iloc[i] = -1  # Sell signal (take-profit triggered)
                            self.entry_price = None
                            self.take_profit_price = None
                
                return signals
            
            def on_trade_executed(self, trade):
                """Called when a trade is executed"""
                if trade.action == 'BUY':
                    self.entry_price = trade.price
                    self.take_profit_price = trade.price * (1 + self.params['take_profit_pct'])
                elif trade.action == 'SELL':
                    self.entry_price = None
                    self.take_profit_price = None
        
        return TakeProfitStrategy()
    
    def test_position_sizing_with_max_position_size(self, sample_data):
        """Test position sizing with max position size limit"""
        class SimpleStrategy(TradingStrategy):
            def generate_signals(self, data):
                signals = pd.Series(0, index=data.index)
                signals.iloc[1] = 1  # Buy signal
                return signals
        
        engine = BacktestEngine()
        
        # Test with different max position sizes
        config1 = BacktestConfig(initial_capital=10000, max_position_size=0.2)  # 20% max
        config2 = BacktestConfig(initial_capital=10000, max_position_size=0.5)  # 50% max
        config3 = BacktestConfig(initial_capital=10000, max_position_size=1.0)  # 100% max
        
        result1 = engine.run(sample_data, SimpleStrategy(), config1)
        result2 = engine.run(sample_data, SimpleStrategy(), config2)
        result3 = engine.run(sample_data, SimpleStrategy(), config3)
        
        # Verify position sizes are proportional to max_position_size
        assert result1.trades[0].quantity < result2.trades[0].quantity
        assert result2.trades[0].quantity < result3.trades[0].quantity
        
        # Verify position sizes respect limits
        for result, max_size in [(result1, 0.2), (result2, 0.5), (result3, 1.0)]:
            for point in result.equity_curve:
                if 'price' in point and point['price'] > 0 and point['position'] > 0:
                    position_value = point['position'] * point['price']
                    portfolio_value = point['equity']
                    position_ratio = position_value / portfolio_value if portfolio_value > 0 else 0
                    assert position_ratio <= max_size + 0.01  # Allow small tolerance for rounding
    
    def test_custom_position_sizing_strategy(self, sample_data):
        """Test custom position sizing strategy"""
        class CustomPositionSizingStrategy(TradingStrategy):
            def generate_signals(self, data):
                signals = pd.Series(0, index=data.index)
                signals.iloc[1] = 1  # Buy signal
                return signals
            
            def get_position_size(self, signal, current_price, available_capital):
                # Custom position sizing: fixed 5% of available capital
                if signal == 0:
                    return 0
                
                position_value = available_capital * 0.05
                return position_value / current_price if current_price > 0 else 0
        
        engine = BacktestEngine()
        config = BacktestConfig(initial_capital=10000)
        result = engine.run(sample_data, CustomPositionSizingStrategy(), config)
        
        # Verify position size is approximately 5% of capital
        trade = result.trades[0]
        position_value = trade.quantity * trade.price
        assert 450 <= position_value <= 550  # ~5% of 10000, with some tolerance for rounding
    
    def test_stop_loss_execution(self, sample_data, strategy_with_stop_loss):
        """Test stop-loss execution"""
        # Create data with a price drop to trigger stop-loss
        data = sample_data.copy()
        data.loc[5, 'close'] = 95  # Price drop to trigger 5% stop-loss
        
        engine = BacktestEngine()
        config = BacktestConfig(initial_capital=10000)
        result = engine.run(data, strategy_with_stop_loss, config)
        
        # Verify stop-loss was triggered
        assert len(result.trades) >= 2  # Should have buy and sell trades
        assert result.trades[0].action == 'BUY'
        assert result.trades[1].action == 'SELL'
        
        # Verify sell happened at the stop-loss point
        buy_price = result.trades[0].price
        sell_price = result.trades[1].price
        stop_loss_pct = strategy_with_stop_loss.params['stop_loss_pct']
        expected_stop_price = buy_price * (1 - stop_loss_pct)
        
        # Allow some tolerance for slippage and execution price
        assert abs(sell_price - expected_stop_price) < 1.0
    
    def test_take_profit_execution(self, sample_data, strategy_with_take_profit):
        """Test take-profit execution"""
        # Create data with a price increase to trigger take-profit
        data = sample_data.copy()
        data.loc[5, 'close'] = 107  # Price increase to trigger 5% take-profit
        
        engine = BacktestEngine()
        config = BacktestConfig(initial_capital=10000)
        result = engine.run(data, strategy_with_take_profit, config)
        
        # Verify take-profit was triggered
        assert len(result.trades) >= 2  # Should have buy and sell trades
        assert result.trades[0].action == 'BUY'
        assert result.trades[1].action == 'SELL'
        
        # Verify sell happened at the take-profit point
        buy_price = result.trades[0].price
        sell_price = result.trades[1].price
        take_profit_pct = strategy_with_take_profit.params['take_profit_pct']
        expected_tp_price = buy_price * (1 + take_profit_pct)
        
        # Allow some tolerance for slippage and execution price
        assert abs(sell_price - expected_tp_price) < 1.0
    
    def test_risk_reward_ratio(self, sample_data):
        """Test risk-reward ratio calculation"""
        class RiskRewardStrategy(TradingStrategy):
            def __init__(self, risk_reward_ratio=2.0):
                super().__init__(params={'risk_reward_ratio': risk_reward_ratio, 'stop_loss_pct': 0.05})
                self.entry_price = None
                self.stop_loss_price = None
                self.take_profit_price = None
            
            def generate_signals(self, data):
                signals = pd.Series(0, index=data.index)
                
                # Buy signal at the beginning
                signals.iloc[1] = 1
                
                # Check for stop-loss or take-profit
                for i in range(2, len(data)):
                    if self.entry_price is not None:
                        if (self.stop_loss_price is not None and 
                            data['close'].iloc[i] <= self.stop_loss_price):
                            signals.iloc[i] = -1  # Stop-loss
                            self.reset_prices()
                        elif (self.take_profit_price is not None and 
                              data['close'].iloc[i] >= self.take_profit_price):
                            signals.iloc[i] = -1  # Take-profit
                            self.reset_prices()
                
                return signals
            
            def on_trade_executed(self, trade):
                """Called when a trade is executed"""
                if trade.action == 'BUY':
                    self.entry_price = trade.price
                    self.stop_loss_price = trade.price * (1 - self.params['stop_loss_pct'])
                    
                    # Calculate take-profit based on risk-reward ratio
                    risk = trade.price - self.stop_loss_price
                    reward = risk * self.params['risk_reward_ratio']
                    self.take_profit_price = trade.price + reward
                elif trade.action == 'SELL':
                    self.reset_prices()
            
            def reset_prices(self):
                """Reset price levels"""
                self.entry_price = None
                self.stop_loss_price = None
                self.take_profit_price = None
        
        # Create data with price movements to test both scenarios
        data = pd.DataFrame({
            'close': [100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110,
                     109, 108, 107, 106, 105, 104, 103, 102, 101, 95]
        })
        
        engine = BacktestEngine()
        config = BacktestConfig(initial_capital=10000)
        
        # Test with risk-reward ratio of 2.0
        strategy = RiskRewardStrategy(risk_reward_ratio=2.0)
        result = engine.run(data, strategy, config)
        
        # Verify trade execution
        assert len(result.trades) >= 2
        
        # Calculate expected take-profit level
        buy_price = result.trades[0].price
        stop_loss_price = buy_price * (1 - strategy.params['stop_loss_pct'])
        risk = buy_price - stop_loss_price
        expected_tp_price = buy_price + (risk * strategy.params['risk_reward_ratio'])
        
        # If take-profit was hit, verify it was at the expected level
        if result.trades[1].price > buy_price:
            assert abs(result.trades[1].price - expected_tp_price) < 1.0
    
    def test_max_drawdown_control(self, sample_data):
        """Test maximum drawdown control"""
        class DrawdownControlStrategy(TradingStrategy):
            def __init__(self, max_drawdown=0.1):
                super().__init__(params={'max_drawdown': max_drawdown})
                self.peak_equity = None
            
            def generate_signals(self, data):
                signals = pd.Series(0, index=data.index)
                
                # Buy signal at the beginning
                signals.iloc[1] = 1
                
                return signals
            
            def on_equity_update(self, equity, timestamp):
                """Called when equity is updated"""
                if self.peak_equity is None:
                    self.peak_equity = equity
                elif equity > self.peak_equity:
                    self.peak_equity = equity
                
                # Calculate current drawdown
                if self.peak_equity > 0:
                    drawdown = (self.peak_equity - equity) / self.peak_equity
                    
                    # If drawdown exceeds max, exit all positions
                    if drawdown > self.params['max_drawdown']:
                        return -1  # Signal to exit all positions
                
                return 0  # No signal
        
        # Create data with a significant drawdown
        data = pd.DataFrame({
            'close': [100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110,
                     105, 100, 95, 90, 85, 80, 75, 70, 65, 60]
        })
        
        engine = BacktestEngine()
        config = BacktestConfig(initial_capital=10000)
        
        # Test with max drawdown of 10%
        strategy = DrawdownControlStrategy(max_drawdown=0.1)
        
        # This test is conceptual - the actual implementation would require
        # modifying the BacktestEngine to call on_equity_update after each step
        # For now, we're just verifying the strategy logic
        
        # Simulate equity updates
        peak_equity = 11000  # Assuming equity peaked at 11000
        current_equity = 9000  # Current equity after drawdown
        
        strategy.peak_equity = peak_equity
        signal = strategy.on_equity_update(current_equity, datetime.now())
        
        # Drawdown is (11000-9000)/11000 = 0.1818, which exceeds 0.1
        assert signal == -1  # Should signal to exit positions


if __name__ == "__main__":
    pytest.main(["-v", __file__])