#!/usr/bin/env python3
"""
Darwin Gödel Machine Optimizer Demo
Comprehensive demonstration of advanced evolutionary optimization with cryptographic audit trails.
"""

import sys
import os
import time
import numpy as np
import pandas as pd
from datetime import datetime, timezone
import json

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src', 'optimization'))

from dgm_optimizer import (
    DarwinGodelMachineOptimizer, OptimizationConfig, OptimizationStatus,
    MovingAverageCrossoverStrategy, SelectionMethod, MutationStrategy,
    BaseStrategy, ParameterSpace, create_dgm_optimizer
)


def print_header(title: str):
    """Print formatted header"""
    print(f"\n{'='*80}")
    print(f"🧬 {title}")
    print(f"{'='*80}")


def print_section(title: str):
    """Print formatted section"""
    print(f"\n{'-'*60}")
    print(f"📊 {title}")
    print(f"{'-'*60}")


def create_sample_data(start_date='2020-01-01', end_date='2023-12-31', seed=42):
    """Create realistic sample market data"""
    np.random.seed(seed)
    dates = pd.date_range(start_date, end_date, freq='D')
    
    # Generate realistic price series with trends and volatility
    returns = np.random.normal(0.0005, 0.02, len(dates))
    
    # Add some trend periods
    trend_periods = [
        (100, 200, 0.001),   # Bull market
        (300, 400, -0.0008), # Bear market
        (600, 700, 0.0012),  # Strong bull
    ]
    
    for start, end, trend in trend_periods:
        if start < len(returns) and end < len(returns):
            returns[start:end] += trend
    
    # Add volatility clustering
    volatility = np.abs(returns)
    for i in range(1, len(volatility)):
        volatility[i] = 0.1 * volatility[i-1] + 0.9 * volatility[i]
    
    returns = returns * volatility * 10
    
    # Generate prices
    prices = 100 * np.exp(np.cumsum(returns))
    
    # Add volume data
    volume = np.random.lognormal(10, 0.5, len(dates)).astype(int)
    
    return pd.DataFrame({
        'close': prices,
        'volume': volume,
        'returns': returns
    }, index=dates)


class RSIStrategy(BaseStrategy):
    """RSI-based trading strategy for demonstration"""
    
    @classmethod
    def get_params_space(cls):
        return [
            ParameterSpace("rsi_period", 5, 30, "int"),
            ParameterSpace("oversold_threshold", 20, 40, "float"),
            ParameterSpace("overbought_threshold", 60, 80, "float"),
            ParameterSpace("position_size", 0.1, 1.0, "float")
        ]
    
    def generate_signals(self, data: pd.DataFrame) -> pd.Series:
        """Generate RSI-based trading signals"""
        # Calculate RSI
        delta = data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=self.parameters['rsi_period']).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=self.parameters['rsi_period']).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        # Generate signals
        signals = pd.Series(0, index=data.index)
        
        # Buy when RSI is oversold
        buy_condition = rsi < self.parameters['oversold_threshold']
        signals[buy_condition] = self.parameters['position_size']
        
        # Sell when RSI is overbought
        sell_condition = rsi > self.parameters['overbought_threshold']
        signals[sell_condition] = -self.parameters['position_size']
        
        return signals


class MeanReversionStrategy(BaseStrategy):
    """Mean reversion strategy for demonstration"""
    
    @classmethod
    def get_params_space(cls):
        return [
            ParameterSpace("lookback_period", 10, 100, "int"),
            ParameterSpace("entry_threshold", 1.0, 3.0, "float"),
            ParameterSpace("exit_threshold", 0.5, 1.5, "float"),
            ParameterSpace("max_position", 0.5, 2.0, "float")
        ]
    
    def generate_signals(self, data: pd.DataFrame) -> pd.Series:
        """Generate mean reversion signals"""
        # Calculate rolling mean and standard deviation
        rolling_mean = data['close'].rolling(window=self.parameters['lookback_period']).mean()
        rolling_std = data['close'].rolling(window=self.parameters['lookback_period']).std()
        
        # Calculate z-score
        z_score = (data['close'] - rolling_mean) / rolling_std
        
        # Generate signals
        signals = pd.Series(0, index=data.index)
        
        # Mean reversion signals
        # Buy when price is below mean (negative z-score)
        buy_condition = z_score < -self.parameters['entry_threshold']
        signals[buy_condition] = self.parameters['max_position']
        
        # Sell when price is above mean (positive z-score)
        sell_condition = z_score > self.parameters['entry_threshold']
        signals[sell_condition] = -self.parameters['max_position']
        
        # Exit positions when z-score approaches zero
        exit_condition = np.abs(z_score) < self.parameters['exit_threshold']
        signals[exit_condition] = 0
        
        return signals


def demo_basic_optimization():
    """Demonstrate basic optimization functionality"""
    print_header("Basic Darwin Gödel Machine Optimization")
    
    # Create sample data
    print("📈 Creating sample market data...")
    data = create_sample_data('2020-01-01', '2022-12-31')
    print(f"   Data shape: {data.shape}")
    print(f"   Date range: {data.index[0].date()} to {data.index[-1].date()}")
    print(f"   Price range: ${data['close'].min():.2f} - ${data['close'].max():.2f}")
    
    # Configure optimizer
    config = OptimizationConfig(
        population_size=20,
        generations=10,
        mutation_rate=0.1,
        crossover_rate=0.8,
        selection_method=SelectionMethod.TOURNAMENT,
        random_seed=42
    )
    
    print(f"\n🔧 Optimizer Configuration:")
    print(f"   Population Size: {config.population_size}")
    print(f"   Generations: {config.generations}")
    print(f"   Mutation Rate: {config.mutation_rate}")
    print(f"   Crossover Rate: {config.crossover_rate}")
    print(f"   Selection Method: {config.selection_method.value}")
    
    # Create optimizer
    optimizer = create_dgm_optimizer(config)
    
    # Run optimization
    print(f"\n🚀 Starting optimization...")
    start_time = time.time()
    
    result = optimizer.optimize(MovingAverageCrossoverStrategy, data)
    
    execution_time = time.time() - start_time
    
    # Display results
    print(f"\n✅ Optimization completed!")
    print(f"   Status: {result.optimization_status.value}")
    print(f"   Execution Time: {execution_time:.2f}s")
    print(f"   Generations Completed: {result.generations_completed}")
    print(f"   Total Evaluations: {result.total_evaluations}")
    print(f"   Best Fitness: {result.best_fitness:.6f}")
    
    print(f"\n🏆 Best Parameters:")
    for param, value in result.best_parameters.items():
        print(f"   {param}: {value}")
    
    print(f"\n📊 Convergence History:")
    for i, conv in enumerate(result.convergence_history[-5:], 1):  # Show last 5
        print(f"   Generation {len(result.convergence_history)-5+i}: {conv:.6f}")
    
    print(f"\n🔒 Security & Audit:")
    print(f"   Audit Trail Entries: {len(result.audit_trail)}")
    print(f"   Verification Hash: {result.verification_hash[:32]}...")
    print(f"   HMAC Signature: {result.hmac_signature[:32]}...")
    print(f"   Integrity Verified: {result.verify_integrity()}")
    
    return result


def demo_strategy_comparison():
    """Demonstrate optimization of different strategies"""
    print_header("Multi-Strategy Optimization Comparison")
    
    # Create sample data
    data = create_sample_data('2021-01-01', '2023-06-30', seed=123)
    
    strategies = [
        ("Moving Average Crossover", MovingAverageCrossoverStrategy),
        ("RSI Strategy", RSIStrategy),
        ("Mean Reversion", MeanReversionStrategy)
    ]
    
    # Small config for demo
    config = OptimizationConfig(
        population_size=15,
        generations=8,
        mutation_rate=0.15,
        parallel_evaluation=True,
        max_workers=2,
        random_seed=456
    )
    
    results = {}
    
    for strategy_name, strategy_class in strategies:
        print(f"\n🧬 Optimizing {strategy_name}...")
        
        # Show parameter space
        params_space = strategy_class.get_params_space()
        print(f"   Parameter Space ({len(params_space)} parameters):")
        for param in params_space:
            print(f"     {param.name}: {param.param_type} [{param.min_value}, {param.max_value}]")
        
        # Run optimization
        optimizer = create_dgm_optimizer(config)
        start_time = time.time()
        
        result = optimizer.optimize(strategy_class, data)
        
        execution_time = time.time() - start_time
        results[strategy_name] = (result, execution_time)
        
        print(f"   ✅ Completed in {execution_time:.2f}s")
        print(f"   Best Fitness: {result.best_fitness:.6f}")
        print(f"   Status: {result.optimization_status.value}")
    
    # Compare results
    print_section("Strategy Comparison Results")
    
    print(f"{'Strategy':<25} {'Fitness':<12} {'Status':<12} {'Time(s)':<8} {'Evaluations':<12}")
    print("-" * 80)
    
    for strategy_name, (result, exec_time) in results.items():
        print(f"{strategy_name:<25} {result.best_fitness:<12.6f} {result.optimization_status.value:<12} "
              f"{exec_time:<8.2f} {result.total_evaluations:<12}")
    
    # Find best strategy
    best_strategy = max(results.items(), key=lambda x: x[1][0].best_fitness)
    print(f"\n🏆 Best Performing Strategy: {best_strategy[0]}")
    print(f"   Fitness: {best_strategy[1][0].best_fitness:.6f}")
    print(f"   Parameters: {best_strategy[1][0].best_parameters}")


def demo_selection_methods():
    """Demonstrate different selection methods"""
    print_header("Selection Methods Comparison")
    
    data = create_sample_data('2020-06-01', '2022-06-01', seed=789)
    
    selection_methods = [
        SelectionMethod.TOURNAMENT,
        SelectionMethod.ROULETTE,
        SelectionMethod.RANK,
        SelectionMethod.ELITIST
    ]
    
    results = {}
    
    for method in selection_methods:
        print(f"\n🎯 Testing {method.value.upper()} selection...")
        
        config = OptimizationConfig(
            population_size=12,
            generations=6,
            selection_method=method,
            mutation_rate=0.12,
            random_seed=101
        )
        
        optimizer = create_dgm_optimizer(config)
        start_time = time.time()
        
        result = optimizer.optimize(MovingAverageCrossoverStrategy, data)
        
        execution_time = time.time() - start_time
        results[method.value] = (result, execution_time)
        
        print(f"   Best Fitness: {result.best_fitness:.6f}")
        print(f"   Generations: {result.generations_completed}")
        print(f"   Time: {execution_time:.2f}s")
    
    # Compare selection methods
    print_section("Selection Method Performance")
    
    print(f"{'Method':<12} {'Best Fitness':<15} {'Generations':<12} {'Time(s)':<8} {'Status':<12}")
    print("-" * 70)
    
    for method_name, (result, exec_time) in results.items():
        print(f"{method_name:<12} {result.best_fitness:<15.6f} {result.generations_completed:<12} "
              f"{exec_time:<8.2f} {result.optimization_status.value:<12}")


def demo_convergence_analysis():
    """Demonstrate convergence analysis and early stopping"""
    print_header("Convergence Analysis & Early Stopping")
    
    data = create_sample_data('2019-01-01', '2023-01-01', seed=202)
    
    # Test different convergence thresholds
    convergence_configs = [
        ("Strict Convergence", 1e-8, 100),
        ("Moderate Convergence", 1e-4, 100),
        ("Loose Convergence", 1e-2, 100),
        ("No Early Stopping", 0.0, 100)
    ]
    
    results = {}
    
    for config_name, threshold, max_gen in convergence_configs:
        print(f"\n📈 Testing {config_name} (threshold: {threshold})...")
        
        config = OptimizationConfig(
            population_size=15,
            generations=max_gen,
            convergence_threshold=threshold,
            max_stagnation_generations=15,
            mutation_rate=0.1,
            random_seed=303
        )
        
        optimizer = create_dgm_optimizer(config)
        start_time = time.time()
        
        result = optimizer.optimize(RSIStrategy, data)
        
        execution_time = time.time() - start_time
        results[config_name] = (result, execution_time)
        
        print(f"   Status: {result.optimization_status.value}")
        print(f"   Generations: {result.generations_completed}/{max_gen}")
        print(f"   Final Fitness: {result.best_fitness:.6f}")
        print(f"   Time: {execution_time:.2f}s")
        
        # Show convergence progression
        if len(result.convergence_history) > 0:
            print(f"   Convergence: {result.convergence_history[0]:.6f} → {result.convergence_history[-1]:.6f}")
    
    # Analyze convergence patterns
    print_section("Convergence Analysis Summary")
    
    for config_name, (result, exec_time) in results.items():
        print(f"\n{config_name}:")
        print(f"  Final Status: {result.optimization_status.value}")
        print(f"  Efficiency: {result.best_fitness/exec_time:.4f} fitness/second")
        print(f"  Evaluations: {result.total_evaluations}")
        
        if len(result.convergence_history) >= 2:
            improvement = result.convergence_history[0] - result.convergence_history[-1]
            print(f"  Convergence Improvement: {improvement:.6f}")


def demo_audit_trail_analysis():
    """Demonstrate comprehensive audit trail analysis"""
    print_header("Cryptographic Audit Trail Analysis")
    
    data = create_sample_data('2021-01-01', '2022-12-31', seed=404)
    
    config = OptimizationConfig(
        population_size=10,
        generations=5,
        mutation_rate=0.2,
        crossover_rate=0.9,
        random_seed=505
    )
    
    print("🔍 Running optimization with detailed audit trail...")
    
    optimizer = create_dgm_optimizer(config)
    result = optimizer.optimize(MeanReversionStrategy, data)
    
    print(f"\n📋 Audit Trail Analysis:")
    print(f"   Total Entries: {len(result.audit_trail)}")
    print(f"   Session ID: {result.audit_trail[0]['metadata']['session_id'] if result.audit_trail else 'N/A'}")
    
    # Analyze operations
    operations = {}
    for entry in result.audit_trail:
        op = entry['operation']
        operations[op] = operations.get(op, 0) + 1
    
    print(f"\n📊 Operations Breakdown:")
    for operation, count in sorted(operations.items()):
        print(f"   {operation}: {count}")
    
    # Verify audit trail integrity
    print(f"\n🔒 Security Verification:")
    print(f"   Verification Hash: {result.verification_hash}")
    print(f"   HMAC Signature: {result.hmac_signature}")
    print(f"   Integrity Status: {'✅ VERIFIED' if result.verify_integrity() else '❌ COMPROMISED'}")
    
    # Analyze generation progression
    generation_entries = [e for e in result.audit_trail if e['operation'] == 'generation_complete']
    
    if generation_entries:
        print(f"\n📈 Generation Progression:")
        print(f"{'Gen':<4} {'Best Fitness':<15} {'Convergence':<15} {'Population':<10}")
        print("-" * 50)
        
        for entry in generation_entries:
            gen = entry['generation']
            fitness = entry['fitness']
            conv = entry['metadata'].get('convergence_metric', 0)
            pop_size = entry['metadata'].get('population_size', 0)
            print(f"{gen:<4} {fitness:<15.6f} {conv:<15.6f} {pop_size:<10}")
    
    # Show individual evaluations
    eval_entries = [e for e in result.audit_trail if e['operation'] == 'individual_evaluation']
    
    if eval_entries:
        print(f"\n🧬 Individual Evaluations Sample (first 5):")
        for i, entry in enumerate(eval_entries[:5]):
            print(f"   {i+1}. {entry['individual_id']}: fitness={entry['fitness']:.6f}")
    
    # Analyze selection and genetic operations
    selection_entries = [e for e in result.audit_trail if e['operation'] == 'selection']
    crossover_entries = [e for e in result.audit_trail if e['operation'] == 'crossover']
    mutation_entries = [e for e in result.audit_trail if e['operation'] == 'mutation']
    
    print(f"\n🧬 Genetic Operations:")
    print(f"   Selection Operations: {len(selection_entries)}")
    print(f"   Crossover Operations: {len(crossover_entries)}")
    print(f"   Mutation Operations: {len(mutation_entries)}")


def demo_performance_benchmarking():
    """Demonstrate performance benchmarking"""
    print_header("Performance Benchmarking")
    
    data = create_sample_data('2020-01-01', '2023-12-31', seed=606)
    
    # Test different population sizes
    population_sizes = [5, 10, 20, 30]
    
    print("⚡ Testing Population Size Impact:")
    print(f"{'Pop Size':<10} {'Time(s)':<10} {'Fitness':<15} {'Evals/s':<10} {'Status':<12}")
    print("-" * 65)
    
    for pop_size in population_sizes:
        config = OptimizationConfig(
            population_size=pop_size,
            generations=5,
            parallel_evaluation=True,
            max_workers=2,
            random_seed=707
        )
        
        optimizer = create_dgm_optimizer(config)
        start_time = time.time()
        
        result = optimizer.optimize(MovingAverageCrossoverStrategy, data)
        
        execution_time = time.time() - start_time
        evals_per_sec = result.total_evaluations / execution_time if execution_time > 0 else 0
        
        print(f"{pop_size:<10} {execution_time:<10.2f} {result.best_fitness:<15.6f} "
              f"{evals_per_sec:<10.1f} {result.optimization_status.value:<12}")
    
    # Test parallel vs sequential evaluation
    print(f"\n🔄 Parallel vs Sequential Evaluation:")
    
    eval_methods = [
        ("Sequential", False),
        ("Parallel", True)
    ]
    
    for method_name, parallel in eval_methods:
        config = OptimizationConfig(
            population_size=15,
            generations=4,
            parallel_evaluation=parallel,
            max_workers=3,
            random_seed=808
        )
        
        optimizer = create_dgm_optimizer(config)
        start_time = time.time()
        
        result = optimizer.optimize(RSIStrategy, data)
        
        execution_time = time.time() - start_time
        throughput = result.total_evaluations / execution_time
        
        print(f"   {method_name}: {execution_time:.2f}s ({throughput:.1f} evals/s)")


def demo_reproducibility():
    """Demonstrate optimization reproducibility"""
    print_header("Reproducibility & Deterministic Results")
    
    data = create_sample_data('2021-01-01', '2023-01-01', seed=909)
    
    # Test reproducibility with same seed
    seed = 12345
    config = OptimizationConfig(
        population_size=8,
        generations=4,
        mutation_rate=0.15,
        random_seed=seed
    )
    
    print(f"🔄 Running optimization twice with seed {seed}...")
    
    results = []
    for run in range(2):
        print(f"\n   Run {run + 1}:")
        
        optimizer = create_dgm_optimizer(config)
        result = optimizer.optimize(MovingAverageCrossoverStrategy, data)
        
        results.append(result)
        print(f"     Best Fitness: {result.best_fitness:.8f}")
        print(f"     Best Parameters: {result.best_parameters}")
        print(f"     Generations: {result.generations_completed}")
    
    # Compare results
    print(f"\n📊 Reproducibility Analysis:")
    
    fitness_match = results[0].best_fitness == results[1].best_fitness
    params_match = results[0].best_parameters == results[1].best_parameters
    generations_match = results[0].generations_completed == results[1].generations_completed
    
    print(f"   Fitness Match: {'✅' if fitness_match else '❌'}")
    print(f"   Parameters Match: {'✅' if params_match else '❌'}")
    print(f"   Generations Match: {'✅' if generations_match else '❌'}")
    
    if fitness_match and params_match:
        print(f"   🎯 Perfect Reproducibility Achieved!")
    else:
        print(f"   ⚠️ Results differ - check random seed implementation")
    
    # Test different seeds produce different results
    print(f"\n🎲 Testing Different Seeds:")
    
    seeds = [111, 222, 333]
    seed_results = []
    
    for seed in seeds:
        config.random_seed = seed
        optimizer = create_dgm_optimizer(config)
        result = optimizer.optimize(MovingAverageCrossoverStrategy, data)
        seed_results.append(result.best_fitness)
        print(f"   Seed {seed}: {result.best_fitness:.6f}")
    
    # Check diversity
    unique_results = len(set(f"{f:.6f}" for f in seed_results))
    print(f"   Unique Results: {unique_results}/{len(seeds)} {'✅' if unique_results > 1 else '⚠️'}")


def main():
    """Main demo function"""
    print("🧬 Darwin Gödel Machine Optimizer - Comprehensive Demo")
    print("Advanced Evolutionary Optimization with Cryptographic Audit Trails")
    print(f"Demo started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Run all demonstrations
        demo_basic_optimization()
        demo_strategy_comparison()
        demo_selection_methods()
        demo_convergence_analysis()
        demo_audit_trail_analysis()
        demo_performance_benchmarking()
        demo_reproducibility()
        
        print_header("Demo Complete")
        print("✅ All demonstrations completed successfully!")
        
        print("\n🎉 Key Features Demonstrated:")
        print("   • Advanced evolutionary optimization algorithms")
        print("   • Multiple strategy optimization and comparison")
        print("   • Comprehensive cryptographic audit trails")
        print("   • Different selection methods (Tournament, Roulette, Rank, Elitist)")
        print("   • Convergence analysis and early stopping")
        print("   • Performance benchmarking and scalability")
        print("   • Reproducible results with random seeds")
        print("   • Thread-safe parallel evaluation")
        print("   • Enterprise-grade security and verification")
        
        print(f"\n🚀 Darwin Gödel Machine Optimizer is ready for production deployment!")
        print("   Perfect for optimizing trading strategies with mathematical rigor")
        print("   and complete auditability for regulatory compliance.")
        
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()