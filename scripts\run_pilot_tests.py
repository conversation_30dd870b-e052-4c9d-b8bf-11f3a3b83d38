#!/usr/bin/env python3
"""
Pilot Test Runner with Enhanced Reporting

This script demonstrates the enhanced test reporting capabilities
as a pilot implementation of the TDD improvements.
"""

import subprocess
import sys
import os
import json
import time
from datetime import datetime
from pathlib import Path
import webbrowser


class PilotTestRunner:
    def __init__(self):
        self.project_root = Path.cwd()
        self.reports_dir = self.project_root / "reports"
        self.reports_dir.mkdir(exist_ok=True)
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.test_results = {}
        self.start_time = None
    
    def print_banner(self, text, char="="):
        """Print a formatted banner"""
        print(f"\n{char * 60}")
        print(f" {text}")
        print(f"{char * 60}\n")
    
    def run_command(self, command, description):
        """Run a command and capture output"""
        print(f"🔄 {description}...")
        try:
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            return result
        except subprocess.TimeoutExpired:
            print(f"❌ {description} timed out after 5 minutes")
            return None
        except Exception as e:
            print(f"❌ Error running {description}: {e}")
            return None
    
    def run_tests(self, test_path="tests/test_mt5_bridge_tdd.py"):
        """Run tests with enhanced reporting"""
        self.print_banner("RUNNING PILOT TESTS", "=")
        
        # Define report file paths
        junit_report = self.reports_dir / f"junit_report_{self.timestamp}.xml"
        json_report = self.reports_dir / f"json_report_{self.timestamp}.json"
        html_report = self.reports_dir / f"html_report_{self.timestamp}.html"
        
        # Run tests with multiple report formats
        command = (
            f"python -m pytest {test_path} -v "
            f"--tb=short "
            f"--junitxml={junit_report} "
            f"--json-report --json-report-file={json_report} "
            f"--html={html_report} "
            f"--cov=src --cov-report=term-missing:skip-covered "
            f"--cov-report=html:reports/coverage_{self.timestamp}"
        )
        
        result = self.run_command(command, "Running tests with enhanced reporting")
        
        if result:
            print("\n📊 Test Results:")
            print(result.stdout)
            if result.stderr:
                print("\n⚠️ Warnings/Errors:")
                print(result.stderr)
            
            self.test_results['test_run'] = {
                'returncode': result.returncode,
                'passed': result.returncode == 0,
                'junit_report': str(junit_report),
                'json_report': str(json_report),
                'html_report': str(html_report)
            }
            
            print(f"\n📄 Reports saved to:")
            print(f"  - JUnit XML: {junit_report}")
            print(f"  - JSON: {json_report}")
            print(f"  - HTML: {html_report}")
            print(f"  - Coverage: reports/coverage_{self.timestamp}")
            
            # Open HTML report in browser
            try:
                if html_report.exists():
                    webbrowser.open(f"file://{html_report.absolute()}")
                    print(f"\n🌐 Opened HTML report in browser")
            except Exception as e:
                print(f"Could not open HTML report: {e}")
            
            return result.returncode == 0
        
        return False
    
    def generate_summary(self):
        """Generate a summary of the test run"""
        self.print_banner("TEST SUMMARY", "=")
        
        # Save results to file
        summary_file = self.reports_dir / f"summary_{self.timestamp}.json"
        try:
            with open(summary_file, 'w') as f:
                json.dump({
                    'timestamp': datetime.now().isoformat(),
                    'results': self.test_results
                }, f, indent=2)
            print(f"\n💾 Summary saved to: {summary_file}")
        except Exception as e:
            print(f"⚠️ Could not save summary: {e}")
    
    def run(self, test_path=None):
        """Run the pilot test suite"""
        self.start_time = time.time()
        
        print("🚀 AI Trading Platform - Pilot Test Runner")
        print(f"📁 Project Root: {self.project_root}")
        print(f"📅 Timestamp: {self.timestamp}")
        
        # Run tests
        success = self.run_tests(test_path)
        
        # Generate summary
        self.generate_summary()
        
        # Calculate duration
        end_time = time.time()
        duration = end_time - self.start_time
        print(f"\n⏱️ Total duration: {duration:.2f} seconds")
        
        return success


def main():
    """Main function"""
    test_path = sys.argv[1] if len(sys.argv) > 1 else "tests/test_mt5_bridge_tdd.py"
    runner = PilotTestRunner()
    success = runner.run(test_path)
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()