
from bridge.schemas import OrderRequest, AccountInfo

def mock_order_request(**overrides):
    data = dict(symbol="EURUSD", volume=0.01, order_type="buy", price=1.1)
    data.update(overrides)
    return OrderRequest(**data)

def mock_account_info(**overrides):
    data = dict(balance=10000, equity=10000, margin=0, currency="USD")
    data.update(overrides)
    return AccountInfo(**data)

def mock_ml_data():
    X = [{"feature": f} for f in range(1, 4)]
    y = [f*1.0 for f in range(1, 4)]
    return X, y
