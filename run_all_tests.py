#!/usr/bin/env python3
"""
Comprehensive TDD Test Runner
Complete test suite for AI Enhanced Trading Platform
"""

import sys
import os
import subprocess
import argparse
import time
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def run_command(cmd, description=""):
    """Run a command and return the result"""
    print(f"\n{'='*80}")
    print(f"🧪 {description or cmd}")
    print(f"{'='*80}")
    
    start_time = time.time()
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    end_time = time.time()
    
    print(f"⏱️  Duration: {end_time - start_time:.2f} seconds")
    print(f"📊 Return code: {result.returncode}")
    
    if result.stdout:
        print(f"\n📝 STDOUT:\n{result.stdout}")
    
    if result.stderr and result.returncode != 0:
        print(f"\n❌ STDERR:\n{result.stderr}")
    
    return result

def run_mt5_bridge_tests():
    """Run MT5 Bridge tests"""
    cmd = "python -m pytest tests/test_mt5_bridge.py -v --tb=short"
    return run_command(cmd, "MT5 Bridge Tests")

def run_risk_manager_tests():
    """Run Risk Manager tests"""
    cmd = "python -m pytest tests/test_risk_manager.py -v --tb=short"
    return run_command(cmd, "Risk Manager Tests")

def run_portfolio_manager_tests():
    """Run Portfolio Manager tests"""
    cmd = "python -m pytest tests/test_portfolio_manager.py -v --tb=short"
    return run_command(cmd, "Portfolio Manager Tests")

def run_model_pipeline_tests():
    """Run ML Model Pipeline tests"""
    cmd = "python -m pytest tests/test_model_pipeline.py -v --tb=short"
    return run_command(cmd, "ML Model Pipeline Tests")

def run_integration_tests():
    """Run integration tests across all components"""
    cmd = "python -m pytest tests/ -k 'integration' -v --tb=short"
    return run_command(cmd, "Integration Tests")

def run_performance_tests():
    """Run performance tests"""
    cmd = "python -m pytest tests/ -k 'performance' -v --tb=short"
    return run_command(cmd, "Performance Tests")

def run_all_tests():
    """Run all tests"""
    cmd = "python -m pytest tests/ -v --tb=short"
    return run_command(cmd, "All Tests")

def run_coverage_tests():
    """Run tests with coverage report"""
    cmd = "python -m pytest tests/ --cov=python_engine --cov=src --cov-report=html --cov-report=term-missing -v"
    return run_command(cmd, "Coverage Tests")

def run_smoke_tests():
    """Run critical smoke tests"""
    smoke_tests = [
        "tests/test_mt5_bridge.py::TestMT5BridgeConnection::test_connect_offline_mode",
        "tests/test_risk_manager.py::TestRiskManagerInitialization::test_risk_manager_initialization_default",
        "tests/test_portfolio_manager.py::TestPortfolioManagerInitialization::test_portfolio_manager_initialization_default",
        "tests/test_model_pipeline.py::TestModelPipelineInitialization::test_model_pipeline_initialization_default"
    ]
    
    print(f"\n🚀 Running {len(smoke_tests)} Critical Smoke Tests")
    
    passed = 0
    failed = 0
    
    for test in smoke_tests:
        cmd = f"python -m pytest {test} -v"
        result = run_command(cmd, f"Smoke Test: {test.split('::')[-1]}")
        
        if result.returncode == 0:
            print(f"✅ PASSED: {test.split('::')[-1]}")
            passed += 1
        else:
            print(f"❌ FAILED: {test.split('::')[-1]}")
            failed += 1
    
    print(f"\n📊 Smoke Test Results: {passed} passed, {failed} failed")
    return subprocess.CompletedProcess(args=[], returncode=failed, stdout="", stderr="")

def validate_test_environment():
    """Validate test environment setup"""
    print("🔍 Validating test environment...")
    
    # Check if required files exist
    required_files = [
        "python_engine/mt5_bridge.py",
        "python_engine/risk_manager.py", 
        "python_engine/portfolio_manager.py",
        "src/ml/model_pipeline.py",
        "tests/test_mt5_bridge.py",
        "tests/test_risk_manager.py",
        "tests/test_portfolio_manager.py",
        "tests/test_model_pipeline.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing required files: {missing_files}")
        return False
    
    # Check if pytest is available
    try:
        result = subprocess.run(["python", "-m", "pytest", "--version"], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ pytest is not available")
            return False
        print(f"✅ pytest version: {result.stdout.strip()}")
    except Exception as e:
        print(f"❌ Error checking pytest: {e}")
        return False
    
    print("✅ Test environment validation passed!")
    return True

def run_component_test_suite():
    """Run comprehensive component test suite"""
    print("🚀 Running Comprehensive TDD Test Suite")
    print(f"📅 Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Validate environment first
    if not validate_test_environment():
        print("❌ Environment validation failed. Aborting tests.")
        return 1
    
    # Define test suites
    test_suites = [
        ("Smoke Tests", run_smoke_tests),
        ("MT5 Bridge", run_mt5_bridge_tests),
        ("Risk Manager", run_risk_manager_tests),
        ("Portfolio Manager", run_portfolio_manager_tests),
        ("ML Model Pipeline", run_model_pipeline_tests),
        ("Integration Tests", run_integration_tests),
        ("Performance Tests", run_performance_tests),
    ]
    
    results = {}
    total_start_time = time.time()
    
    for suite_name, test_function in test_suites:
        print(f"\n🧪 Running {suite_name}...")
        start_time = time.time()
        result = test_function()
        end_time = time.time()
        
        results[suite_name] = {
            "returncode": result.returncode,
            "duration": end_time - start_time,
            "success": result.returncode == 0
        }
        
        if result.returncode == 0:
            print(f"✅ {suite_name} PASSED ({end_time - start_time:.2f}s)")
        else:
            print(f"❌ {suite_name} FAILED ({end_time - start_time:.2f}s)")
    
    total_end_time = time.time()
    
    # Print comprehensive summary
    print(f"\n{'='*80}")
    print("🎯 COMPREHENSIVE TDD TEST SUITE SUMMARY")
    print(f"{'='*80}")
    print(f"⏱️  Total Duration: {total_end_time - total_start_time:.2f} seconds")
    print(f"📅 Completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    passed_suites = 0
    for suite_name, result in results.items():
        status = "✅ PASSED" if result["success"] else "❌ FAILED"
        print(f"{suite_name:20} {status:12} ({result['duration']:.2f}s)")
        if result["success"]:
            passed_suites += 1
    
    print(f"\n📊 Test Suite Results: {passed_suites}/{len(test_suites)} passed")
    
    if passed_suites == len(test_suites):
        print("🎉 ALL TEST SUITES PASSED!")
        print("✅ TDD Implementation is working perfectly!")
        return 0
    else:
        print("⚠️  Some test suites failed!")
        print("🔧 Check the failed tests and fix issues.")
        return 1

def generate_test_report():
    """Generate comprehensive test report"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"tdd_test_report_{timestamp}.html"
    
    cmd = f"python -m pytest tests/ --html={report_file} --self-contained-html -v"
    result = run_command(cmd, f"Generate Comprehensive Test Report: {report_file}")
    
    if result.returncode == 0:
        print(f"✅ Test report generated: {report_file}")
    else:
        print(f"❌ Failed to generate test report")
    
    return result

def main():
    """Main test runner function"""
    parser = argparse.ArgumentParser(description="Comprehensive TDD Test Runner")
    parser.add_argument("--test-type", choices=[
        "mt5", "risk", "portfolio", "model", "integration", "performance", 
        "all", "coverage", "smoke", "component", "report"
    ], default="component", help="Type of tests to run")
    
    parser.add_argument("--validate", action="store_true", 
                       help="Validate test environment only")
    
    args = parser.parse_args()
    
    print("🧪 AI Enhanced Trading Platform - TDD Test Runner")
    print(f"📅 Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 Working Directory: {os.getcwd()}")
    print(f"🐍 Python Version: {sys.version}")
    
    if args.validate:
        return 0 if validate_test_environment() else 1
    
    # Run based on test type
    test_functions = {
        "mt5": run_mt5_bridge_tests,
        "risk": run_risk_manager_tests,
        "portfolio": run_portfolio_manager_tests,
        "model": run_model_pipeline_tests,
        "integration": run_integration_tests,
        "performance": run_performance_tests,
        "all": run_all_tests,
        "coverage": run_coverage_tests,
        "smoke": run_smoke_tests,
        "component": run_component_test_suite,
        "report": generate_test_report
    }
    
    test_function = test_functions.get(args.test_type)
    if test_function:
        if args.test_type == "component":
            return test_function()
        else:
            result = test_function()
            return result.returncode if hasattr(result, 'returncode') else 0
    else:
        print(f"❌ Unknown test type: {args.test_type}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)