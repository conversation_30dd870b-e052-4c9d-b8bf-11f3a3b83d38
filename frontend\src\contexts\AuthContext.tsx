/**
 * Authentication Context
 * Manages user authentication state
 */

import { createContext, useState, useEffect, ReactNode } from 'react';
import { apiService } from '@/services/api';

// Define the User type
export interface User {
  email: string;
  full_name: string;
  role: string;
  created_at: string;
  last_login?: string;
}

// Define the context type
interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (user: User) => void;
  logout: () => void;
}

// Create the context with default values
export const AuthContext = createContext<AuthContextType>({
  user: null,
  isAuthenticated: false,
  isLoading: true,
  login: () => {},
  logout: () => {},
});

// Create the provider component
export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Check if the user is authenticated on mount
  useEffect(() => {
    const checkAuth = async () => {
      if (apiService.isAuthenticated()) {
        try {
          // Fetch the current user
          const response = await fetch(`${apiService.getBaseURL()}/api/v1/auth/me`, {
            headers: {
              'Authorization': `Bearer ${apiService.getAuthToken()}`,
            },
          });
          
          if (response.ok) {
            const userData = await response.json();
            setUser(userData);
          } else {
            // If the token is invalid, clear it
            apiService.clearAuthToken();
          }
        } catch (error) {
          console.error('Error checking authentication:', error);
          apiService.clearAuthToken();
        }
      }
      
      setIsLoading(false);
    };
    
    checkAuth();
  }, []);

  // Login function
  const login = (userData: User) => {
    setUser(userData);
  };

  // Logout function
  const logout = () => {
    apiService.clearAuthToken();
    setUser(null);
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated: !!user,
        isLoading,
        login,
        logout,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}