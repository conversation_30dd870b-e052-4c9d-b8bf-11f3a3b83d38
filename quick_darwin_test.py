#!/usr/bin/env python3
"""
Quick Darwin Step 2 test with minimal data
"""

import sys
sys.path.append('shared')

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from darwin_engine_step2 import AdvancedBacktester, ForexDataProvider
from darwin_engine_step1 import TradingStrategy, TradingCondition, RiskManagement, IndicatorType, OperatorType, ActionType

def quick_test():
    print("🚀 Quick Darwin Step 2 Test")
    print("=" * 40)
    
    # Create minimal test data
    dates = pd.date_range(start='2024-01-01', end='2024-01-31', freq='1H')
    n_points = len(dates)
    
    # Generate simple price data
    base_price = 1.0850
    prices = base_price + np.cumsum(np.random.normal(0, 0.001, n_points))
    
    data = pd.DataFrame({
        'Open': prices * (1 + np.random.normal(0, 0.0001, n_points)),
        'High': prices * (1 + np.abs(np.random.normal(0, 0.002, n_points))),
        'Low': prices * (1 - np.abs(np.random.normal(0, 0.002, n_points))),
        'Close': prices,
        'Volume': np.random.lognormal(10, 1, n_points)
    }, index=dates)
    
    # Add basic RSI
    delta = data['Close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    data['RSI'] = 100 - (100 / (1 + rs))
    
    print(f"📊 Generated {len(data)} data points")
    
    # Create a simple strategy
    condition = TradingCondition(
        indicator=IndicatorType.RSI,
        period=14,
        operator=OperatorType.LESS_THAN,
        value=30.0
    )
    
    risk_mgmt = RiskManagement(
        stop_loss_pct=2.0,
        take_profit_pct=3.0,
        position_size_pct=1.0
    )
    
    strategy = TradingStrategy(
        id="test_001",
        name="Test_RSI_Strategy",
        description="Simple RSI oversold strategy",
        conditions=[condition],
        action=ActionType.BUY,
        risk_management=risk_mgmt
    )
    
    # Test backtesting
    backtester = AdvancedBacktester(initial_balance=10000)
    
    print("🧪 Running backtest...")
    results = backtester.backtest_strategy(strategy, data, 'EURUSD')
    
    print(f"\n📈 Backtest Results:")
    print(f"  Fitness: {results.get('fitness', 0):.4f}")
    print(f"  Total Trades: {results.get('total_trades', 0)}")
    print(f"  Win Rate: {results.get('win_rate', 0):.2%}")
    print(f"  Total Return: {results.get('total_return_pct', 0):.2f}%")
    print(f"  Sharpe Ratio: {results.get('sharpe_ratio', 0):.2f}")
    print(f"  Max Drawdown: {results.get('max_drawdown_pct', 0):.2f}%")
    
    print(f"\n✅ Quick test complete!")

if __name__ == "__main__":
    quick_test()