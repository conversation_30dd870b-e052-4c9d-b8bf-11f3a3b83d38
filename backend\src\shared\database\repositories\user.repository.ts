import { Knex } from 'knex';
import { v4 as uuidv4 } from 'uuid';
import { User, UserId, SubscriptionTier } from '../../types';
import { DatabaseConnection } from '../connection';

export interface CreateUserData {
  email: string;
  fullName: string;
  passwordHash: string;
  subscriptionTier?: SubscriptionTier;
  apiQuotaUsed?: number;
  apiQuotaLimit?: number;
}

export interface UpdateUserData {
  email?: string;
  fullName?: string;
  subscriptionTier?: SubscriptionTier;
  apiQuotaUsed?: number;
  apiQuotaLimit?: number;
}

export interface UserWithPasswordHash extends User {
  passwordHash: string;
}

interface DbUser {
  id: string;
  email: string;
  full_name?: string;
  password_hash: string;
  subscription_tier: SubscriptionTier;
  api_quota_used: number;
  api_quota_limit: number;
  created_at: Date;
  updated_at: Date;
}

export class UserRepository {
  private db: Knex;

  constructor() {
    this.db = DatabaseConnection.getInstance().connection;
  }

  async findByEmail(email: string): Promise<UserWithPasswordHash | null> {
    try {
      const user = await this.db
        .select('*')
        .from('users')
        .where('email', email)
        .first<DbUser>();

      return user ? this.mapToUserWithPassword(user) : null;
    } catch (error) {
      throw error;
    }
  }

  async findById(id: UserId): Promise<UserWithPasswordHash | null> {
    try {
      const user = await this.db
        .select('*')
        .from('users')
        .where('id', id)
        .first<DbUser>();

      return user ? this.mapToUserWithPassword(user) : null;
    } catch (error) {
      throw error;
    }
  }

  async create(data: CreateUserData): Promise<UserWithPasswordHash> {
    try {
      const now = new Date();
      const userData = {
        id: uuidv4(),
        email: data.email,
        full_name: data.fullName,
        password_hash: data.passwordHash,
        subscription_tier: data.subscriptionTier || 'free',
        api_quota_used: data.apiQuotaUsed || 0,
        api_quota_limit: data.apiQuotaLimit || 100,
        created_at: now,
        updated_at: now,
      };

      const [createdUser] = await this.db
        .insert(userData)
        .into('users')
        .returning('*');

      return this.mapToUserWithPassword(createdUser);
    } catch (error) {
      throw error;
    }
  }

  async update(id: UserId, data: UpdateUserData): Promise<UserWithPasswordHash | null> {
    try {
      const updateData: Partial<DbUser> = {
        updated_at: new Date(),
      };

      if (data.email) updateData.email = data.email;
      if (data.fullName !== undefined) updateData.full_name = data.fullName;
      if (data.subscriptionTier) updateData.subscription_tier = data.subscriptionTier;
      if (data.apiQuotaUsed !== undefined) updateData.api_quota_used = data.apiQuotaUsed;
      if (data.apiQuotaLimit !== undefined) updateData.api_quota_limit = data.apiQuotaLimit;

      const [updatedUser] = await this.db('users')
        .where('id', id)
        .update(updateData)
        .returning('*');

      return updatedUser ? this.mapToUserWithPassword(updatedUser) : null;
    } catch (error) {
      throw error;
    }
  }

  async delete(id: UserId): Promise<boolean> {
    try {
      const deletedCount = await this.db('users')
        .where('id', id)
        .delete();

      return deletedCount > 0;
    } catch (error) {
      throw error;
    }
  }

  async findMany(filters: {
    subscriptionTier?: SubscriptionTier;
    email?: string;
    limit?: number;
    offset?: number;
  } = {}): Promise<User[]> {
    try {
      let query = this.db
        .select('id', 'email', 'full_name', 'subscription_tier', 'api_quota_used', 'api_quota_limit', 'created_at', 'updated_at')
        .from('users');

      // Apply filters
      if (filters.subscriptionTier) {
        query = query.where('subscription_tier', filters.subscriptionTier);
      }
      if (filters.email) {
        query = query.where('email', 'like', `%${filters.email}%`);
      }

      // Apply pagination if provided
      if (filters.limit) {
        query = query.limit(filters.limit);
      }
      if (filters.offset) {
        query = query.offset(filters.offset);
      }

      // Default ordering
      query = query.orderBy('created_at', 'desc');

      const users = await query;
      return users.map(user => this.mapToUser(user));
    } catch (error) {
      throw error;
    }
  }

  async countUsers(filters: {
    subscriptionTier?: SubscriptionTier;
  } = {}): Promise<number> {
    try {
      let query = this.db('users').count('* as count');

      // Apply filters
      if (filters.subscriptionTier) {
        query = query.where('subscription_tier', filters.subscriptionTier);
      }

      const result = await query.first();
      return parseInt(result?.count as string) || 0;
    } catch (error) {
      throw error;
    }
  }

  private mapToUser(dbUser: Omit<DbUser, 'password_hash'>): User {
    return {
      id: dbUser.id as UserId,
      email: dbUser.email,
      fullName: dbUser.full_name,
      subscriptionTier: dbUser.subscription_tier as SubscriptionTier,
      apiQuotaUsed: dbUser.api_quota_used,
      apiQuotaLimit: dbUser.api_quota_limit,
      createdAt: new Date(dbUser.created_at),
      updatedAt: new Date(dbUser.updated_at),
    };
  }

  private mapToUserWithPassword(dbUser: DbUser): UserWithPasswordHash {
    const user = this.mapToUser(dbUser);
    return {
      ...user,
      passwordHash: dbUser.password_hash,
    };
  }
}