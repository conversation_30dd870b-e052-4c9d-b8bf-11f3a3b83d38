#!/usr/bin/env python3
"""
RSI (Relative Strength Index) Strategy Implementation

This module implements RSI calculation and trading signals with comprehensive
doctest examples and robust error handling for user inputs.
"""

import math
from typing import List, Union, Tuple, Optional


def rsi(prices: List[float], period: int = 14) -> float:
    """
    Compute RSI (Relative Strength Index) indicator.
    
    The RSI is a momentum oscillator that measures the speed and magnitude
    of recent price changes to evaluate overbought or oversold conditions.
    
    Args:
        prices: List of price values (floats)
        period: Period for RSI calculation (default: 14)
    
    Returns:
        float: RSI value between 0 and 100
    
    Raises:
        ValueError: If not enough data or invalid parameters
        TypeError: If prices contain non-numeric values
    
    Examples:
        Basic RSI calculation with upward trend:
        >>> rsi([1,2,3,4,5,6,7,8,9,10,11,12,13,14,15], period=14)
        100.0
        
        RSI with flat prices (no movement):
        >>> rsi([1,1,1,1,1,1,1,1,1,1,1,1,1,1,1], period=14)
        100.0
        
        RSI with downward trend:
        >>> rsi([15,14,13,12,11,10,9,8,7,6,5,4,3,2,1], period=14)
        0.0
        
        RSI with mixed price movements:
        >>> prices = [44, 44.34, 44.09, 44.15, 43.61, 44.33, 44.83, 45.85, 46.08, 45.89, 46.03, 46.83, 46.69, 46.45, 46.59]
        >>> round(rsi(prices, period=14), 2)
        74.39
        
        Short period RSI:
        >>> rsi([10, 12, 11, 13, 12, 14], period=5)
        75.0
        
        Error cases - insufficient data:
        >>> rsi([1,2,3], period=14)
        Traceback (most recent call last):
            ...
        ValueError: Not enough data: need at least 15 prices for period 14, got 3
        
        Error cases - empty list:
        >>> rsi([], period=14)
        Traceback (most recent call last):
            ...
        ValueError: Not enough data: need at least 15 prices for period 14, got 0
        
        Error cases - invalid period:
        >>> rsi([1,2,3,4,5], period=0)
        Traceback (most recent call last):
            ...
        ValueError: Period must be positive, got 0
        
        Error cases - negative period:
        >>> rsi([1,2,3,4,5], period=-5)
        Traceback (most recent call last):
            ...
        ValueError: Period must be positive, got -5
        
        Error cases - non-numeric prices:
        >>> rsi([1, 2, "invalid", 4, 5], period=3)
        Traceback (most recent call last):
            ...
        TypeError: All prices must be numeric, found <class 'str'> at index 2
        
        Error cases - None in prices:
        >>> rsi([1, 2, None, 4, 5], period=3)
        Traceback (most recent call last):
            ...
        TypeError: All prices must be numeric, found <class 'NoneType'> at index 2
        
        Edge case - exactly minimum data:
        >>> rsi([1,2,3,4,5,6,7,8,9,10,11,12,13,14,15], period=14)
        100.0
        
        Edge case - large period:
        >>> prices = list(range(1, 102))  # 101 prices
        >>> result = rsi(prices, period=100)
        >>> isinstance(result, float) and 0 <= result <= 100
        True
    """
    # Input validation
    if not isinstance(period, int) or period <= 0:
        raise ValueError(f"Period must be positive, got {period}")
    
    if not isinstance(prices, (list, tuple)):
        raise TypeError(f"Prices must be a list or tuple, got {type(prices)}")
    
    if len(prices) < period + 1:
        raise ValueError(f"Not enough data: need at least {period + 1} prices for period {period}, got {len(prices)}")
    
    # Validate all prices are numeric
    for i, price in enumerate(prices):
        if not isinstance(price, (int, float)) or price is None:
            raise TypeError(f"All prices must be numeric, found {type(price)} at index {i}")
        if math.isnan(price) or math.isinf(price):
            raise ValueError(f"Price at index {i} is not a valid number: {price}")
    
    # Calculate price changes
    changes = []
    for i in range(1, len(prices)):
        changes.append(prices[i] - prices[i-1])
    
    if len(changes) < period:
        raise ValueError(f"Not enough price changes: need at least {period}, got {len(changes)}")
    
    # Separate gains and losses
    gains = [max(change, 0) for change in changes]
    losses = [abs(min(change, 0)) for change in changes]
    
    # Calculate initial averages
    avg_gain = sum(gains[:period]) / period
    avg_loss = sum(losses[:period]) / period
    
    # Handle edge case where there are no losses (all gains)
    if avg_loss == 0:
        return 100.0
    
    # Handle edge case where there are no gains (all losses)
    if avg_gain == 0:
        return 0.0
    
    # Calculate RSI using smoothed averages for remaining periods
    for i in range(period, len(gains)):
        avg_gain = (avg_gain * (period - 1) + gains[i]) / period
        avg_loss = (avg_loss * (period - 1) + losses[i]) / period
    
    # Final RSI calculation
    if avg_loss == 0:
        return 100.0
    
    rs = avg_gain / avg_loss
    rsi_value = 100 - (100 / (1 + rs))
    
    return round(rsi_value, 2)


def rsi_signal(prices: List[float], period: int = 14, 
               overbought: float = 70.0, oversold: float = 30.0) -> str:
    """
    Generate RSI trading signal based on overbought/oversold levels.
    
    Args:
        prices: List of price values
        period: RSI calculation period (default: 14)
        overbought: Overbought threshold (default: 70.0)
        oversold: Oversold threshold (default: 30.0)
    
    Returns:
        str: Trading signal ('BUY', 'SELL', 'HOLD')
    
    Raises:
        ValueError: If thresholds are invalid or RSI calculation fails
        TypeError: If inputs are not of correct type
    
    Examples:
        Overbought signal (SELL):
        >>> prices = [44, 44.34, 44.09, 44.15, 43.61, 44.33, 44.83, 45.85, 46.08, 45.89, 46.03, 46.83, 46.69, 46.45, 46.59]
        >>> rsi_signal(prices, period=14, overbought=70.0, oversold=30.0)
        'SELL'
        
        Oversold signal (BUY):
        >>> prices = [50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36]
        >>> rsi_signal(prices, period=14, overbought=70.0, oversold=30.0)
        'BUY'
        
        Neutral signal (HOLD):
        >>> prices = [50, 50.5, 49.5, 50.2, 49.8, 50.1, 49.9, 50.3, 49.7, 50.0, 50.2, 49.8, 50.1, 49.9, 50.0]
        >>> rsi_signal(prices, period=14, overbought=70.0, oversold=30.0)
        'HOLD'
        
        Custom thresholds:
        >>> prices = [44, 44.34, 44.09, 44.15, 43.61, 44.33, 44.83, 45.85, 46.08, 45.89, 46.03, 46.83, 46.69, 46.45, 46.59]
        >>> rsi_signal(prices, period=14, overbought=60.0, oversold=40.0)
        'SELL'
        
        Error cases - invalid thresholds:
        >>> rsi_signal([1,2,3,4,5,6,7,8,9,10,11,12,13,14,15], overbought=30.0, oversold=70.0)
        Traceback (most recent call last):
            ...
        ValueError: Overbought threshold (30.0) must be greater than oversold threshold (70.0)
        
        Error cases - thresholds out of range:
        >>> rsi_signal([1,2,3,4,5,6,7,8,9,10,11,12,13,14,15], overbought=150.0)
        Traceback (most recent call last):
            ...
        ValueError: Overbought threshold must be between 0 and 100, got 150.0
        
        Error cases - negative thresholds:
        >>> rsi_signal([1,2,3,4,5,6,7,8,9,10,11,12,13,14,15], oversold=-10.0)
        Traceback (most recent call last):
            ...
        ValueError: Oversold threshold must be between 0 and 100, got -10.0
        
        Error cases - insufficient data (propagated from rsi function):
        >>> rsi_signal([1,2,3], period=14)
        Traceback (most recent call last):
            ...
        ValueError: Not enough data: need at least 15 prices for period 14, got 3
    """
    # Validate thresholds
    if not isinstance(overbought, (int, float)):
        raise TypeError(f"Overbought threshold must be numeric, got {type(overbought)}")
    
    if not isinstance(oversold, (int, float)):
        raise TypeError(f"Oversold threshold must be numeric, got {type(oversold)}")
    
    if not (0 <= overbought <= 100):
        raise ValueError(f"Overbought threshold must be between 0 and 100, got {overbought}")
    
    if not (0 <= oversold <= 100):
        raise ValueError(f"Oversold threshold must be between 0 and 100, got {oversold}")
    
    if overbought <= oversold:
        raise ValueError(f"Overbought threshold ({overbought}) must be greater than oversold threshold ({oversold})")
    
    # Calculate RSI (this will handle all price validation)
    rsi_value = rsi(prices, period)
    
    # Generate signal
    if rsi_value >= overbought:
        return 'SELL'
    elif rsi_value <= oversold:
        return 'BUY'
    else:
        return 'HOLD'


def rsi_divergence(prices: List[float], rsi_values: List[float], 
                   lookback: int = 5) -> Optional[str]:
    """
    Detect RSI divergence patterns for advanced trading signals.
    
    Args:
        prices: List of price values
        rsi_values: List of corresponding RSI values
        lookback: Number of periods to look back for divergence (default: 5)
    
    Returns:
        Optional[str]: Divergence type ('BULLISH', 'BEARISH', None)
    
    Examples:
        Bullish divergence (price down, RSI up):
        >>> prices = [100, 95, 90, 85, 80]
        >>> rsi_vals = [50, 45, 40, 45, 55]
        >>> rsi_divergence(prices, rsi_vals, lookback=4)
        'BULLISH'
        
        Bearish divergence (price up, RSI down):
        >>> prices = [100, 105, 110, 115, 120]
        >>> rsi_vals = [70, 65, 60, 55, 50]
        >>> rsi_divergence(prices, rsi_vals, lookback=4)
        'BEARISH'
        
        No divergence:
        >>> prices = [100, 105, 110, 115, 120]
        >>> rsi_vals = [50, 55, 60, 65, 70]
        >>> rsi_divergence(prices, rsi_vals, lookback=4) is None
        True
        
        Error cases - mismatched lengths:
        >>> rsi_divergence([1,2,3], [50,60], lookback=2)
        Traceback (most recent call last):
            ...
        ValueError: Prices and RSI values must have the same length: got 3 and 2
        
        Error cases - insufficient data:
        >>> rsi_divergence([1,2], [50,60], lookback=5)
        Traceback (most recent call last):
            ...
        ValueError: Not enough data: need at least 5 values for lookback 5, got 2
    """
    # Input validation
    if len(prices) != len(rsi_values):
        raise ValueError(f"Prices and RSI values must have the same length: got {len(prices)} and {len(rsi_values)}")
    
    if len(prices) < lookback:
        raise ValueError(f"Not enough data: need at least {lookback} values for lookback {lookback}, got {len(prices)}")
    
    if lookback <= 1:
        raise ValueError(f"Lookback must be greater than 1, got {lookback}")
    
    # Get recent data
    recent_prices = prices[-lookback:]
    recent_rsi = rsi_values[-lookback:]
    
    # Calculate trends
    price_trend = recent_prices[-1] - recent_prices[0]
    rsi_trend = recent_rsi[-1] - recent_rsi[0]
    
    # Detect divergence (need significant trends to avoid noise)
    price_threshold = abs(recent_prices[0]) * 0.01  # 1% threshold
    rsi_threshold = 1.0  # 1 point threshold
    
    if abs(price_trend) > price_threshold and abs(rsi_trend) > rsi_threshold:
        if price_trend < 0 and rsi_trend > 0:  # Price down, RSI up
            return 'BULLISH'
        elif price_trend > 0 and rsi_trend < 0:  # Price up, RSI down
            return 'BEARISH'
    
    return None


if __name__ == "__main__":
    import doctest
    
    print("Running RSI strategy doctests...")
    
    # Run doctests with verbose output
    result = doctest.testmod(verbose=True)
    
    if result.failed == 0:
        print(f"\n✅ All {result.attempted} doctests passed!")
    else:
        print(f"\n❌ {result.failed} out of {result.attempted} doctests failed!")
    
    # Additional manual tests
    print("\n🧪 Running additional manual tests...")
    
    # Test with real-world data
    sample_prices = [
        44.0, 44.34, 44.09, 44.15, 43.61, 44.33, 44.83, 45.85, 46.08, 45.89,
        46.03, 46.83, 46.69, 46.45, 46.59, 46.3, 46.28, 46.28, 46.0, 46.03
    ]
    
    try:
        rsi_value = rsi(sample_prices, period=14)
        signal = rsi_signal(sample_prices, period=14)
        print(f"✅ Sample RSI calculation: {rsi_value}")
        print(f"✅ Sample RSI signal: {signal}")
    except Exception as e:
        print(f"❌ Manual test failed: {e}")
    
    print("\n🎯 RSI strategy testing complete!")