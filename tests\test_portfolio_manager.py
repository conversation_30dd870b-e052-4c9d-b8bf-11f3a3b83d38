# tests/test_portfolio_manager.py
import pytest
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta
from python_engine.portfolio_manager import (
    PortfolioManager, PortfolioManagerException, TradeStatus,
    Trade, PortfolioMetrics, PortfolioPosition
)

class TestPortfolioManagerInitialization:
    """Test Portfolio Manager initialization"""
    
    def test_portfolio_manager_initialization_default(self):
        """Test default initialization"""
        # Act
        portfolio = PortfolioManager()
        
        # Assert
        assert portfolio.initial_balance == 10000.0
        assert portfolio.current_balance == 10000.0
        assert portfolio.offline_mode is True
        assert len(portfolio.positions) == 0
        assert len(portfolio.trade_history) == 0
        assert len(portfolio.daily_balances) == 0
        assert "EURUSD" in portfolio.mock_prices
    
    def test_portfolio_manager_initialization_custom(self):
        """Test custom initialization"""
        # Act
        portfolio = PortfolioManager(initial_balance=50000.0, offline_mode=False)
        
        # Assert
        assert portfolio.initial_balance == 50000.0
        assert portfolio.current_balance == 50000.0
        assert portfolio.offline_mode is False

class TestTradeManagement:
    """Test trade addition and management"""
    
    def test_add_trade_buy_success(self):
        """Test successful BUY trade addition"""
        # Arrange
        portfolio = PortfolioManager()
        
        # Act
        trade = portfolio.add_trade("EURUSD", 1.0, "BUY", 1.0850, "Test trade")
        
        # Assert
        assert isinstance(trade, Trade)
        assert trade.symbol == "EURUSD"
        assert trade.volume == 1.0
        assert trade.order_type == "BUY"
        assert trade.entry_price == 1.0850
        assert trade.status == TradeStatus.EXECUTED
        assert trade.commission == 7.0  # $7 per lot
        assert len(portfolio.trade_history) == 1
        assert portfolio.current_balance == 10000.0 - 7.0  # Initial balance minus commission
    
    def test_add_trade_sell_success(self):
        """Test successful SELL trade addition"""
        # Arrange
        portfolio = PortfolioManager()
        
        # Act
        trade = portfolio.add_trade("EURUSD", 2.0, "SELL", 1.0850)
        
        # Assert
        assert trade.order_type == "SELL"
        assert trade.volume == 2.0
        assert trade.commission == 14.0  # $7 * 2 lots
        assert portfolio.current_balance == 10000.0 - 14.0
    
    def test_add_trade_auto_price_offline(self):
        """Test trade addition with automatic price in offline mode"""
        # Arrange
        portfolio = PortfolioManager()
        
        # Act
        trade = portfolio.add_trade("EURUSD", 1.0, "BUY")  # No price specified
        
        # Assert
        assert trade.entry_price == portfolio.mock_prices["EURUSD"]
    
    def test_add_trade_position_creation(self):
        """Test position creation when adding trade"""
        # Arrange
        portfolio = PortfolioManager()
        
        # Act
        portfolio.add_trade("EURUSD", 1.0, "BUY", 1.0850)
        
        # Assert
        assert "EURUSD" in portfolio.positions
        position = portfolio.positions["EURUSD"]
        assert position.symbol == "EURUSD"
        assert position.volume == 1.0
        assert position.average_price == 1.0850
        assert len(position.trades) == 1
    
    def test_add_multiple_trades_same_symbol(self):
        """Test adding multiple trades for same symbol"""
        # Arrange
        portfolio = PortfolioManager()
        
        # Act
        portfolio.add_trade("EURUSD", 1.0, "BUY", 1.0850)
        portfolio.add_trade("EURUSD", 2.0, "BUY", 1.0900)
        
        # Assert
        position = portfolio.positions["EURUSD"]
        assert position.volume == 3.0  # 1.0 + 2.0
        # Average price should be weighted: (1.0*1.0850 + 2.0*1.0900) / 3.0 = 1.0883
        expected_avg = (1.0 * 1.0850 + 2.0 * 1.0900) / 3.0
        assert abs(position.average_price - expected_avg) < 0.0001
    
    def test_add_opposing_trades(self):
        """Test adding opposing trades (BUY then SELL)"""
        # Arrange
        portfolio = PortfolioManager()
        
        # Act
        portfolio.add_trade("EURUSD", 2.0, "BUY", 1.0850)
        portfolio.add_trade("EURUSD", 1.0, "SELL", 1.0900)
        
        # Assert
        position = portfolio.positions["EURUSD"]
        assert position.volume == 1.0  # 2.0 - 1.0
        assert position.average_price == 1.0900  # Last trade price for reduced position

class TestTradeClosing:
    """Test trade closing functionality"""
    
    def test_close_trade_success(self):
        """Test successful trade closing"""
        # Arrange
        portfolio = PortfolioManager()
        trade = portfolio.add_trade("EURUSD", 1.0, "BUY", 1.0850)
        initial_balance = portfolio.current_balance
        
        # Act
        result = portfolio.close_trade(trade.id, 1.0950)
        
        # Assert
        assert result is True
        assert trade.exit_price == 1.0950
        assert trade.status == TradeStatus.CANCELLED  # Marked as closed
        expected_pnl = (1.0950 - 1.0850) * 1.0 * 100000  # 1000
        assert trade.profit_loss == expected_pnl
        # Balance should increase by P&L minus closing commission
        expected_balance = initial_balance + expected_pnl - 7.0
        assert portfolio.current_balance == expected_balance
    
    def test_close_trade_loss(self):
        """Test closing trade with loss"""
        # Arrange
        portfolio = PortfolioManager()
        trade = portfolio.add_trade("EURUSD", 1.0, "BUY", 1.0850)
        initial_balance = portfolio.current_balance
        
        # Act
        portfolio.close_trade(trade.id, 1.0750)  # Lower exit price
        
        # Assert
        expected_pnl = (1.0750 - 1.0850) * 1.0 * 100000  # -1000
        assert trade.profit_loss == expected_pnl
        expected_balance = initial_balance + expected_pnl - 7.0
        assert portfolio.current_balance == expected_balance
    
    def test_close_trade_sell_position(self):
        """Test closing SELL trade"""
        # Arrange
        portfolio = PortfolioManager()
        trade = portfolio.add_trade("EURUSD", 1.0, "SELL", 1.0850)
        
        # Act
        portfolio.close_trade(trade.id, 1.0750)  # Lower exit price = profit for SELL
        
        # Assert
        expected_pnl = (1.0850 - 1.0750) * 1.0 * 100000  # 1000
        assert trade.profit_loss == expected_pnl
    
    def test_close_trade_not_found(self):
        """Test closing non-existent trade"""
        # Arrange
        portfolio = PortfolioManager()
        
        # Act
        result = portfolio.close_trade("INVALID_ID")
        
        # Assert
        assert result is False
    
    def test_close_trade_auto_price(self):
        """Test closing trade with automatic price"""
        # Arrange
        portfolio = PortfolioManager()
        trade = portfolio.add_trade("EURUSD", 1.0, "BUY", 1.0850)
        
        # Act
        portfolio.close_trade(trade.id)  # No exit price specified
        
        # Assert
        assert trade.exit_price == portfolio.mock_prices["EURUSD"]

class TestPositionManagement:
    """Test position management"""
    
    def test_close_position_success(self):
        """Test successful position closing"""
        # Arrange
        portfolio = PortfolioManager()
        portfolio.add_trade("EURUSD", 1.0, "BUY", 1.0850)
        portfolio.add_trade("EURUSD", 1.0, "BUY", 1.0900)
        
        # Act
        result = portfolio.close_position("EURUSD")
        
        # Assert
        assert result is True
        assert "EURUSD" not in portfolio.positions
        # All trades should be closed
        eur_trades = [t for t in portfolio.trade_history if t.symbol == "EURUSD"]
        assert all(t.status == TradeStatus.CANCELLED for t in eur_trades)
    
    def test_close_position_not_found(self):
        """Test closing non-existent position"""
        # Arrange
        portfolio = PortfolioManager()
        
        # Act
        result = portfolio.close_position("NONEXISTENT")
        
        # Assert
        assert result is False
    
    def test_position_removal_on_zero_volume(self):
        """Test position removal when volume becomes zero"""
        # Arrange
        portfolio = PortfolioManager()
        portfolio.add_trade("EURUSD", 1.0, "BUY", 1.0850)
        portfolio.add_trade("EURUSD", 1.0, "SELL", 1.0900)  # Opposite trade
        
        # Assert
        assert "EURUSD" not in portfolio.positions  # Should be removed

class TestPriceUpdates:
    """Test price update functionality"""
    
    def test_update_prices_single_symbol(self):
        """Test updating prices for single symbol"""
        # Arrange
        portfolio = PortfolioManager()
        portfolio.add_trade("EURUSD", 1.0, "BUY", 1.0850)
        
        # Act
        portfolio.update_prices({"EURUSD": 1.0950})
        
        # Assert
        position = portfolio.positions["EURUSD"]
        assert position.current_price == 1.0950
        expected_pnl = (1.0950 - 1.0850) * 1.0 * 100000  # 1000
        assert position.unrealized_pnl == expected_pnl
    
    def test_update_prices_multiple_symbols(self):
        """Test updating prices for multiple symbols"""
        # Arrange
        portfolio = PortfolioManager()
        portfolio.add_trade("EURUSD", 1.0, "BUY", 1.0850)
        portfolio.add_trade("GBPUSD", 1.0, "SELL", 1.2650)
        
        # Act
        portfolio.update_prices({
            "EURUSD": 1.0950,
            "GBPUSD": 1.2550
        })
        
        # Assert
        eur_position = portfolio.positions["EURUSD"]
        gbp_position = portfolio.positions["GBPUSD"]
        
        assert eur_position.current_price == 1.0950
        assert gbp_position.current_price == 1.2550
        
        # EUR: BUY position, price up = profit
        assert eur_position.unrealized_pnl > 0
        # GBP: SELL position, price down = profit
        assert gbp_position.unrealized_pnl > 0
    
    def test_update_prices_short_position(self):
        """Test price update for short position"""
        # Arrange
        portfolio = PortfolioManager()
        portfolio.add_trade("EURUSD", 2.0, "BUY", 1.0850)
        portfolio.add_trade("EURUSD", 3.0, "SELL", 1.0900)  # Net short 1.0
        
        # Act
        portfolio.update_prices({"EURUSD": 1.0800})  # Price down
        
        # Assert
        position = portfolio.positions["EURUSD"]
        assert position.volume == -1.0  # Short position
        # Short position profits when price goes down
        assert position.unrealized_pnl > 0

class TestPortfolioMetrics:
    """Test portfolio metrics calculation"""
    
    def test_get_portfolio_metrics_empty(self):
        """Test portfolio metrics with empty portfolio"""
        # Arrange
        portfolio = PortfolioManager()
        
        # Act
        metrics = portfolio.get_portfolio_metrics()
        
        # Assert
        assert isinstance(metrics, PortfolioMetrics)
        assert metrics.total_value == 10000.0  # Initial balance
        assert metrics.total_pnl == 0.0
        assert metrics.realized_pnl == 0.0
        assert metrics.unrealized_pnl == 0.0
        assert metrics.total_trades == 0
        assert metrics.win_rate == 0.0
    
    def test_get_portfolio_metrics_with_trades(self):
        """Test portfolio metrics with trades"""
        # Arrange
        portfolio = PortfolioManager()
        
        # Add and close some trades
        trade1 = portfolio.add_trade("EURUSD", 1.0, "BUY", 1.0850)
        portfolio.close_trade(trade1.id, 1.0950)  # Profit
        
        trade2 = portfolio.add_trade("GBPUSD", 1.0, "BUY", 1.2650)
        portfolio.close_trade(trade2.id, 1.2550)  # Loss
        
        # Add open position
        portfolio.add_trade("USDJPY", 1.0, "BUY", 149.50)
        portfolio.update_prices({"USDJPY": 150.00})  # Unrealized profit
        
        # Act
        metrics = portfolio.get_portfolio_metrics()
        
        # Assert
        assert metrics.total_trades == 2  # Only closed trades
        assert metrics.winning_trades == 1
        assert metrics.losing_trades == 1
        assert metrics.win_rate == 0.5
        assert metrics.realized_pnl != 0.0
        assert metrics.unrealized_pnl > 0.0
        assert metrics.total_pnl == metrics.realized_pnl + metrics.unrealized_pnl
    
    def test_get_portfolio_metrics_profit_factor(self):
        """Test profit factor calculation"""
        # Arrange
        portfolio = PortfolioManager()
        
        # Add winning trades
        trade1 = portfolio.add_trade("EURUSD", 1.0, "BUY", 1.0850)
        portfolio.close_trade(trade1.id, 1.0950)  # +1000
        
        trade2 = portfolio.add_trade("GBPUSD", 1.0, "BUY", 1.2650)
        portfolio.close_trade(trade2.id, 1.2750)  # +1000
        
        # Add losing trade
        trade3 = portfolio.add_trade("USDJPY", 1.0, "BUY", 149.50)
        portfolio.close_trade(trade3.id, 149.00)  # -500
        
        # Act
        metrics = portfolio.get_portfolio_metrics()
        
        # Assert
        assert metrics.profit_factor == 4.0  # 2000 / 500
        assert metrics.average_win > 0
        assert metrics.average_loss < 0

class TestPositionSummary:
    """Test position summary functionality"""
    
    def test_get_position_summary_empty(self):
        """Test position summary with empty portfolio"""
        # Arrange
        portfolio = PortfolioManager()
        
        # Act
        summary = portfolio.get_position_summary()
        
        # Assert
        assert summary["total_positions"] == 0
        assert summary["total_unrealized_pnl"] == 0.0
        assert summary["total_volume"] == 0.0
        assert len(summary["positions"]) == 0
    
    def test_get_position_summary_with_positions(self):
        """Test position summary with active positions"""
        # Arrange
        portfolio = PortfolioManager()
        portfolio.add_trade("EURUSD", 1.0, "BUY", 1.0850)
        portfolio.add_trade("GBPUSD", 2.0, "SELL", 1.2650)
        portfolio.update_prices({"EURUSD": 1.0950, "GBPUSD": 1.2550})
        
        # Act
        summary = portfolio.get_position_summary()
        
        # Assert
        assert summary["total_positions"] == 2
        assert summary["total_volume"] == 3.0  # 1.0 + 2.0
        assert summary["total_unrealized_pnl"] > 0  # Both should be profitable
        
        # Check individual position details
        assert "EURUSD" in summary["positions"]
        assert "GBPUSD" in summary["positions"]
        
        eur_pos = summary["positions"]["EURUSD"]
        assert eur_pos["volume"] == 1.0
        assert eur_pos["average_price"] == 1.0850
        assert eur_pos["current_price"] == 1.0950

class TestTradeHistory:
    """Test trade history functionality"""
    
    def test_get_trade_history_all(self):
        """Test getting all trade history"""
        # Arrange
        portfolio = PortfolioManager()
        portfolio.add_trade("EURUSD", 1.0, "BUY", 1.0850)
        portfolio.add_trade("GBPUSD", 1.0, "SELL", 1.2650)
        
        # Act
        history = portfolio.get_trade_history()
        
        # Assert
        assert len(history) == 2
        assert all(isinstance(trade, Trade) for trade in history)
        # Should be sorted by timestamp (most recent first)
        assert history[0].timestamp >= history[1].timestamp
    
    def test_get_trade_history_filtered_by_symbol(self):
        """Test getting trade history filtered by symbol"""
        # Arrange
        portfolio = PortfolioManager()
        portfolio.add_trade("EURUSD", 1.0, "BUY", 1.0850)
        portfolio.add_trade("GBPUSD", 1.0, "SELL", 1.2650)
        portfolio.add_trade("EURUSD", 1.0, "SELL", 1.0900)
        
        # Act
        eur_history = portfolio.get_trade_history(symbol="EURUSD")
        
        # Assert
        assert len(eur_history) == 2
        assert all(trade.symbol == "EURUSD" for trade in eur_history)
    
    def test_get_trade_history_with_limit(self):
        """Test getting trade history with limit"""
        # Arrange
        portfolio = PortfolioManager()
        for i in range(5):
            portfolio.add_trade("EURUSD", 1.0, "BUY", 1.0850 + i * 0.001)
        
        # Act
        limited_history = portfolio.get_trade_history(limit=3)
        
        # Assert
        assert len(limited_history) == 3

class TestDailyBalanceTracking:
    """Test daily balance tracking"""
    
    def test_record_daily_balance_new_day(self):
        """Test recording daily balance for new day"""
        # Arrange
        portfolio = PortfolioManager()
        
        # Act
        portfolio.record_daily_balance()
        
        # Assert
        assert len(portfolio.daily_balances) == 1
        assert portfolio.daily_balances[0][1] == portfolio.current_balance
    
    def test_record_daily_balance_same_day_update(self):
        """Test updating daily balance for same day"""
        # Arrange
        portfolio = PortfolioManager()
        portfolio.record_daily_balance()
        
        # Change balance
        portfolio.current_balance = 11000.0
        
        # Act
        portfolio.record_daily_balance()
        
        # Assert
        assert len(portfolio.daily_balances) == 1  # Should update, not add
        assert portfolio.daily_balances[0][1] == 11000.0
    
    def test_daily_balance_limit(self):
        """Test daily balance record limit"""
        # Arrange
        portfolio = PortfolioManager()
        
        # Add many daily records
        for i in range(400):
            date = datetime.now() - timedelta(days=i)
            portfolio.daily_balances.append((date, 10000.0 + i))
        
        # Act
        portfolio.record_daily_balance()
        
        # Assert
        assert len(portfolio.daily_balances) <= 365

class TestPortfolioDataExport:
    """Test portfolio data export"""
    
    def test_export_portfolio_data(self):
        """Test exporting portfolio data"""
        # Arrange
        portfolio = PortfolioManager()
        portfolio.add_trade("EURUSD", 1.0, "BUY", 1.0850)
        portfolio.record_daily_balance()
        
        # Act
        data = portfolio.export_portfolio_data()
        
        # Assert
        assert "initial_balance" in data
        assert "current_balance" in data
        assert "positions" in data
        assert "trade_history" in data
        assert "daily_balances" in data
        
        assert data["initial_balance"] == 10000.0
        assert len(data["trade_history"]) == 1
        assert "EURUSD" in data["positions"]

class TestPortfolioReset:
    """Test portfolio reset functionality"""
    
    def test_reset_portfolio(self):
        """Test portfolio reset"""
        # Arrange
        portfolio = PortfolioManager()
        portfolio.add_trade("EURUSD", 1.0, "BUY", 1.0850)
        portfolio.record_daily_balance()
        
        # Act
        portfolio.reset_portfolio()
        
        # Assert
        assert portfolio.current_balance == portfolio.initial_balance
        assert len(portfolio.positions) == 0
        assert len(portfolio.trade_history) == 0
        assert len(portfolio.daily_balances) == 0

class TestPortfolioManagerEdgeCases:
    """Test edge cases and error handling"""
    
    def test_add_trade_exception_handling(self):
        """Test exception handling in add_trade"""
        # Arrange
        portfolio = PortfolioManager()
        
        # Act & Assert
        with patch.object(portfolio, '_update_position', side_effect=Exception("Test error")):
            with pytest.raises(PortfolioManagerException):
                portfolio.add_trade("EURUSD", 1.0, "BUY", 1.0850)
    
    def test_close_trade_exception_handling(self):
        """Test exception handling in close_trade"""
        # Arrange
        portfolio = PortfolioManager()
        trade = portfolio.add_trade("EURUSD", 1.0, "BUY", 1.0850)
        
        # Act & Assert
        with patch.object(portfolio.trade_history, '__iter__', side_effect=Exception("Test error")):
            with pytest.raises(PortfolioManagerException):
                portfolio.close_trade(trade.id)
    
    def test_update_prices_exception_handling(self):
        """Test exception handling in update_prices"""
        # Arrange
        portfolio = PortfolioManager()
        
        # Act & Assert
        with patch.object(portfolio.mock_prices, 'update', side_effect=Exception("Test error")):
            with pytest.raises(PortfolioManagerException):
                portfolio.update_prices({"EURUSD": 1.0950})
    
    def test_get_portfolio_metrics_exception_handling(self):
        """Test exception handling in get_portfolio_metrics"""
        # Arrange
        portfolio = PortfolioManager()
        
        # Act & Assert
        with patch.object(portfolio, 'trade_history', side_effect=Exception("Test error")):
            with pytest.raises(PortfolioManagerException):
                portfolio.get_portfolio_metrics()

class TestPortfolioManagerIntegration:
    """Test integration scenarios"""
    
    def test_complete_trading_workflow(self):
        """Test complete trading workflow"""
        # Arrange
        portfolio = PortfolioManager(initial_balance=50000.0)
        
        # Act - Complete workflow
        # 1. Add multiple trades
        trade1 = portfolio.add_trade("EURUSD", 2.0, "BUY", 1.0850, "Long EUR")
        trade2 = portfolio.add_trade("GBPUSD", 1.5, "SELL", 1.2650, "Short GBP")
        trade3 = portfolio.add_trade("EURUSD", 1.0, "BUY", 1.0900, "Add to EUR")
        
        # 2. Update prices
        portfolio.update_prices({
            "EURUSD": 1.0950,  # Profitable for EUR longs
            "GBPUSD": 1.2550   # Profitable for GBP short
        })
        
        # 3. Close some trades
        portfolio.close_trade(trade1.id, 1.0950)
        
        # 4. Record daily balance
        portfolio.record_daily_balance()
        
        # 5. Get comprehensive metrics
        metrics = portfolio.get_portfolio_metrics()
        summary = portfolio.get_position_summary()
        history = portfolio.get_trade_history()
        
        # Assert workflow results
        assert len(portfolio.trade_history) == 3
        assert len(portfolio.positions) == 2  # EUR and GBP positions
        assert metrics.total_trades == 1  # One closed trade
        assert metrics.realized_pnl > 0  # Should be profitable
        assert summary["total_positions"] == 2
        assert len(history) == 3
        assert len(portfolio.daily_balances) == 1
        
        # 6. Export data
        export_data = portfolio.export_portfolio_data()
        assert len(export_data["trade_history"]) == 3
        assert len(export_data["positions"]) == 2
    
    def test_portfolio_performance_tracking(self):
        """Test portfolio performance tracking over time"""
        # Arrange
        portfolio = PortfolioManager()
        
        # Act - Simulate trading over multiple days
        # Day 1: Profitable trades
        trade1 = portfolio.add_trade("EURUSD", 1.0, "BUY", 1.0850)
        portfolio.close_trade(trade1.id, 1.0950)  # +1000
        portfolio.record_daily_balance()
        
        # Day 2: Losing trade
        trade2 = portfolio.add_trade("GBPUSD", 1.0, "BUY", 1.2650)
        portfolio.close_trade(trade2.id, 1.2550)  # -1000
        portfolio.record_daily_balance()
        
        # Day 3: Mixed results
        trade3 = portfolio.add_trade("USDJPY", 1.0, "BUY", 149.50)
        portfolio.close_trade(trade3.id, 150.00)  # +500
        
        trade4 = portfolio.add_trade("USDCHF", 1.0, "SELL", 0.8950)
        portfolio.close_trade(trade4.id, 0.8900)  # +500
        portfolio.record_daily_balance()
        
        # Assert performance tracking
        metrics = portfolio.get_portfolio_metrics()
        assert metrics.total_trades == 4
        assert metrics.winning_trades == 3
        assert metrics.losing_trades == 1
        assert metrics.win_rate == 0.75
        assert metrics.profit_factor > 1.0  # More wins than losses
        assert len(portfolio.daily_balances) == 3
        
        # Check that Sharpe ratio can be calculated
        assert isinstance(metrics.sharpe_ratio, float)
    
    def test_complex_position_management(self):
        """Test complex position management scenarios"""
        # Arrange
        portfolio = PortfolioManager()
        
        # Act - Complex position building and reduction
        # Build long EUR position
        portfolio.add_trade("EURUSD", 1.0, "BUY", 1.0850)
        portfolio.add_trade("EURUSD", 2.0, "BUY", 1.0900)
        portfolio.add_trade("EURUSD", 1.0, "BUY", 1.0950)
        
        # Partially reduce position
        portfolio.add_trade("EURUSD", 2.0, "SELL", 1.0975)
        
        # Build short GBP position
        portfolio.add_trade("GBPUSD", 1.5, "SELL", 1.2650)
        portfolio.add_trade("GBPUSD", 1.0, "SELL", 1.2600)
        
        # Reverse GBP position
        portfolio.add_trade("GBPUSD", 3.0, "BUY", 1.2550)
        
        # Update prices
        portfolio.update_prices({
            "EURUSD": 1.1000,
            "GBPUSD": 1.2500
        })
        
        # Assert complex position states
        eur_position = portfolio.positions["EURUSD"]
        gbp_position = portfolio.positions["GBPUSD"]
        
        assert eur_position.volume == 2.0  # Net long 2.0 lots
        assert gbp_position.volume == 0.5  # Net long 0.5 lots (reversed from short)
        
        # Both positions should be profitable
        assert eur_position.unrealized_pnl > 0
        assert gbp_position.unrealized_pnl > 0
        
        # Close all positions
        portfolio.close_position("EURUSD")
        portfolio.close_position("GBPUSD")
        
        assert len(portfolio.positions) == 0
        
        # All trades should be closed
        open_trades = [t for t in portfolio.trade_history if t.status == TradeStatus.EXECUTED]
        assert len(open_trades) == 0