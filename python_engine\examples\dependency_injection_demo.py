"""
Dependency Injection Architecture Demo

This demo showcases the before/after comparison of dependency injection
and demonstrates the benefits for testability and maintainability.
"""

import asyncio
from datetime import datetime
from typing import Dict, Any

from core.dependency_injection import DependencyContainer, inject
from core.service_configuration import ServiceConfigurator, ServiceMode
from core.trading_engine import TradingEngine
from core.interfaces import TradingSignal, Order
from services.mock_services import MockServiceCollection

class BeforeAndAfterDemo:
    """Demonstrate the benefits of dependency injection"""
    
    def __init__(self):
        self.results = {}
    
    def demonstrate_before_di(self):
        """Show the problems with hard-coded dependencies"""
        print("🔴 BEFORE: Hard-coded Dependencies")
        print("=" * 50)
        
        # This is how code looked BEFORE dependency injection
        class OldTradingEngine:
            def __init__(self):
                # Hard-coded dependencies - difficult to test!
                from services.market_data_service import YFinanceMarketDataService
                from services.logging_service import FileLoggingService
                from services.configuration_service import FileConfigurationService
                
                self.config = FileConfigurationService()  # Always uses file config
                self.logger = FileLoggingService(self.config)  # Always uses file logging
                self.market_data = YFinanceMarketDataService(self.logger, self.config)  # Always uses Yahoo Finance
                
                print("❌ Hard-coded dependencies:")
                print(f"   - Config: {self.config.__class__.__name__}")
                print(f"   - Logger: {self.logger.__class__.__name__}")
                print(f"   - Market Data: {self.market_data.__class__.__name__}")
                print("❌ Problems:")
                print("   - Hard to unit test (always hits real services)")
                print("   - Hard to mock for testing")
                print("   - Tight coupling between components")
                print("   - Difficult to swap implementations")
                print("   - No flexibility for different environments")
        
        try:
            old_engine = OldTradingEngine()
            self.results['before'] = "Created with hard-coded dependencies"
        except Exception as e:
            print(f"❌ Failed to create old engine: {e}")
            self.results['before'] = f"Failed: {e}"
    
    def demonstrate_after_di(self):
        """Show the benefits of dependency injection"""
        print("\n✅ AFTER: Dependency Injection")
        print("=" * 50)
        
        # Configure services for testing
        configurator = ServiceConfigurator()
        container = configurator.configure_for_testing()
        
        # Create trading engine with injected dependencies
        engine = container.resolve(TradingEngine)
        
        print("✅ Injected dependencies:")
        print(f"   - Config: {engine.config.__class__.__name__}")
        print(f"   - Logger: {engine.logger.__class__.__name__}")
        print(f"   - Market Data: {engine.market_data.__class__.__name__}")
        print(f"   - Strategy: {engine.strategy.__class__.__name__}")
        print(f"   - Trading: {engine.trading.__class__.__name__}")
        print(f"   - Risk Management: {engine.risk_management.__class__.__name__}")
        print(f"   - Portfolio: {engine.portfolio.__class__.__name__}")
        print(f"   - Notifications: {engine.notifications.__class__.__name__}")
        print(f"   - Data Storage: {engine.data_storage.__class__.__name__}")
        
        print("✅ Benefits:")
        print("   - Easy to unit test (can inject mocks)")
        print("   - Loose coupling between components")
        print("   - Easy to swap implementations")
        print("   - Flexible for different environments")
        print("   - Single Responsibility Principle")
        print("   - Open/Closed Principle")
        
        self.results['after'] = "Created with dependency injection"
        return engine
    
    def demonstrate_testing_benefits(self):
        """Show how DI makes testing easier"""
        print("\n🧪 TESTING BENEFITS")
        print("=" * 50)
        
        # Create different configurations for different test scenarios
        scenarios = {
            "Unit Testing": ServiceMode.TESTING,
            "Integration Testing": ServiceMode.DEVELOPMENT,
            "Backtesting": None  # Special configuration
        }
        
        for scenario_name, mode in scenarios.items():
            print(f"\n📋 {scenario_name}:")
            
            if mode:
                configurator = ServiceConfigurator()
                if mode == ServiceMode.TESTING:
                    container = configurator.configure_for_testing()
                elif mode == ServiceMode.DEVELOPMENT:
                    container = configurator.configure_for_development()
                
                engine = container.resolve(TradingEngine)
                print(f"   ✅ Market Data: {engine.market_data.__class__.__name__}")
                print(f"   ✅ Trading: {engine.trading.__class__.__name__}")
                
            elif scenario_name == "Backtesting":
                configurator = ServiceConfigurator()
                container = configurator.configure_for_backtesting()
                engine = container.resolve(TradingEngine)
                print(f"   ✅ Strategy: {engine.strategy.__class__.__name__}")
                print(f"   ✅ Auto-trading: {engine.config.get_config('engine.auto_trading_enabled')}")
    
    async def demonstrate_runtime_flexibility(self):
        """Show runtime flexibility with DI"""
        print("\n🔄 RUNTIME FLEXIBILITY")
        print("=" * 50)
        
        # Create base configuration
        configurator = ServiceConfigurator()
        container = configurator.configure_for_testing()
        
        # Create mock service collection for easy manipulation
        mocks = MockServiceCollection()
        
        # Scenario 1: Conservative trading
        print("\n📊 Scenario 1: Conservative Trading")
        mocks.risk_management.set_max_position_size(50)
        mocks.risk_management.set_position_size_multiplier(0.5)
        container.register_instance(type(mocks.risk_management), mocks.risk_management)
        
        engine1 = container.resolve(TradingEngine)
        signal = TradingSignal(
            symbol="AAPL",
            signal="buy",
            confidence=0.8,
            timestamp=datetime.now(),
            strategy_name="test",
            metadata={}
        )
        
        position_size = await engine1.risk_management.calculate_position_size("AAPL", signal, 10000)
        print(f"   Conservative position size: {position_size}")
        
        # Scenario 2: Aggressive trading
        print("\n📊 Scenario 2: Aggressive Trading")
        mocks.risk_management.set_max_position_size(200)
        mocks.risk_management.set_position_size_multiplier(1.5)
        
        position_size = await engine1.risk_management.calculate_position_size("AAPL", signal, 10000)
        print(f"   Aggressive position size: {position_size}")
        
        print("   ✅ Same interface, different behavior!")
        print("   ✅ Easy A/B testing of strategies!")
    
    def demonstrate_inject_decorator(self):
        """Show the @inject decorator in action"""
        print("\n🎯 @INJECT DECORATOR")
        print("=" * 50)
        
        # Configure services
        configurator = ServiceConfigurator()
        container = configurator.configure_for_testing()
        
        # Function with automatic dependency injection
        @inject
        def analyze_market(market_data_service, config_service, logging_service):
            """Function with automatically injected dependencies"""
            logging_service.log_info("Analyzing market with injected dependencies")
            
            # Get configuration
            risk_tolerance = config_service.get_config('trading.risk_tolerance', 0.02)
            
            # Mock analysis
            analysis = {
                'market_data_service': market_data_service.__class__.__name__,
                'risk_tolerance': risk_tolerance,
                'timestamp': datetime.now().isoformat()
            }
            
            logging_service.log_info(f"Market analysis complete: {analysis}")
            return analysis
        
        # Call function - dependencies are automatically injected!
        result = analyze_market()
        
        print("✅ Function called without parameters!")
        print(f"   Result: {result['market_data_service']}")
        print(f"   Risk tolerance: {result['risk_tolerance']}")
        print("✅ Dependencies were automatically injected!")
    
    async def run_complete_demo(self):
        """Run the complete demonstration"""
        print("🚀 DEPENDENCY INJECTION ARCHITECTURE DEMO")
        print("=" * 60)
        
        # Before DI
        self.demonstrate_before_di()
        
        # After DI
        engine = self.demonstrate_after_di()
        
        # Testing benefits
        self.demonstrate_testing_benefits()
        
        # Runtime flexibility
        await self.demonstrate_runtime_flexibility()
        
        # Inject decorator
        self.demonstrate_inject_decorator()
        
        # Summary
        print("\n🎉 SUMMARY")
        print("=" * 50)
        print("✅ Dependency Injection provides:")
        print("   1. Better testability (easy mocking)")
        print("   2. Loose coupling (flexible architecture)")
        print("   3. Runtime flexibility (swap implementations)")
        print("   4. Environment-specific configurations")
        print("   5. Single Responsibility Principle")
        print("   6. Open/Closed Principle")
        print("   7. Easier maintenance and debugging")
        print("   8. Better code organization")
        
        return self.results

class RealWorldScenarios:
    """Demonstrate real-world scenarios where DI shines"""
    
    async def scenario_unit_testing(self):
        """Unit testing scenario"""
        print("\n🧪 SCENARIO: Unit Testing")
        print("-" * 30)
        
        # Create completely mocked environment
        configurator = ServiceConfigurator()
        container = configurator.configure_for_testing()
        
        # All services are mocks - no external dependencies
        engine = container.resolve(TradingEngine)
        
        # Configure mock behavior
        engine.config.set_config('engine.auto_trading_enabled', True)
        engine._engine_config = engine._load_engine_config()
        
        # Test signal processing
        signal = TradingSignal(
            symbol="TEST",
            signal="buy",
            confidence=0.9,
            timestamp=datetime.now(),
            strategy_name="unit_test",
            metadata={}
        )
        
        await engine._process_signal("unit_test", signal)
        
        # Verify behavior
        stored_signals = engine.data_storage.get_stored_signals()
        orders = engine.trading.get_orders()
        
        print(f"   ✅ Signals stored: {len(stored_signals)}")
        print(f"   ✅ Orders placed: {len(orders)}")
        print("   ✅ No external dependencies hit!")
        print("   ✅ Fast, reliable, isolated test!")
    
    async def scenario_integration_testing(self):
        """Integration testing scenario"""
        print("\n🔗 SCENARIO: Integration Testing")
        print("-" * 30)
        
        # Mix of real and mock services
        configurator = ServiceConfigurator()
        container = configurator.configure_for_development()
        
        engine = container.resolve(TradingEngine)
        
        print(f"   📊 Market Data: {engine.market_data.__class__.__name__}")
        print(f"   🧠 Strategy: {engine.strategy.__class__.__name__}")
        print(f"   💰 Trading: {engine.trading.__class__.__name__}")
        print("   ✅ Real strategy execution with safe mock trading!")
    
    async def scenario_production_monitoring(self):
        """Production monitoring scenario"""
        print("\n🏭 SCENARIO: Production Monitoring")
        print("-" * 30)
        
        # Production-like configuration
        configurator = ServiceConfigurator()
        container = configurator.configure_for_development()  # Using dev as proxy for prod
        
        engine = container.resolve(TradingEngine)
        
        # Get engine status
        status = engine.get_engine_status()
        
        print(f"   📊 Engine running: {status['running']}")
        print(f"   📈 Active strategies: {status['active_strategies']}")
        print(f"   ⚙️ Auto-trading: {status['config']['auto_trading_enabled']}")
        print("   ✅ Easy monitoring through DI container!")
    
    async def run_scenarios(self):
        """Run all real-world scenarios"""
        print("\n🌍 REAL-WORLD SCENARIOS")
        print("=" * 50)
        
        await self.scenario_unit_testing()
        await self.scenario_integration_testing()
        await self.scenario_production_monitoring()

async def main():
    """Main demo function"""
    print("🎭 DEPENDENCY INJECTION ARCHITECTURE DEMONSTRATION")
    print("=" * 70)
    
    # Run main demo
    demo = BeforeAndAfterDemo()
    results = await demo.run_complete_demo()
    
    # Run real-world scenarios
    scenarios = RealWorldScenarios()
    await scenarios.run_scenarios()
    
    print("\n🎯 CONCLUSION")
    print("=" * 50)
    print("Dependency Injection transforms our trading platform from:")
    print("❌ Tightly coupled, hard-to-test monolith")
    print("✅ Loosely coupled, easily testable, flexible architecture")
    print("\nThis enables:")
    print("🧪 Comprehensive testing strategies")
    print("🔄 Easy A/B testing of components")
    print("🏭 Environment-specific configurations")
    print("🚀 Rapid development and deployment")
    print("🛡️ Better error handling and debugging")
    print("📈 Improved maintainability and scalability")

if __name__ == "__main__":
    asyncio.run(main())