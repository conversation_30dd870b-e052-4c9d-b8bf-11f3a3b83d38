#!/usr/bin/env python3
"""
MACD (Moving Average Convergence Divergence) Strategy Implementation

This module implements MACD calculation and trading signals with comprehensive
doctest examples and robust error handling for user inputs.
"""

import math
from typing import List, Tuple, Union, Optional


def ema(prices: List[float], period: int) -> float:
    """
    Calculate Exponential Moving Average (EMA).
    
    Args:
        prices: List of price values
        period: EMA period
    
    Returns:
        float: EMA value
    
    Examples:
        Basic EMA calculation:
        >>> round(ema([1, 2, 3, 4, 5], period=3), 2)
        4.0
        
        Single value:
        >>> ema([10], period=1)
        10.0
        
        Error cases - insufficient data:
        >>> ema([1, 2], period=5)
        Traceback (most recent call last):
            ...
        ValueError: Not enough data: need at least 5 prices for period 5, got 2
    """
    if len(prices) < period:
        raise ValueError(f"Not enough data: need at least {period} prices for period {period}, got {len(prices)}")
    
    if period <= 0:
        raise ValueError(f"Period must be positive, got {period}")
    
    # Validate prices
    for i, price in enumerate(prices):
        if not isinstance(price, (int, float)) or price is None:
            raise TypeError(f"All prices must be numeric, found {type(price)} at index {i}")
        if math.isnan(price) or math.isinf(price):
            raise ValueError(f"Price at index {i} is not a valid number: {price}")
    
    # Calculate EMA
    multiplier = 2 / (period + 1)
    ema_value = sum(prices[:period]) / period  # Start with SMA
    
    for price in prices[period:]:
        ema_value = (price * multiplier) + (ema_value * (1 - multiplier))
    
    return round(ema_value, 6)


def macd(prices: List[float], fast_period: int = 12, slow_period: int = 26, 
         signal_period: int = 9) -> Tuple[float, float, float]:
    """
    Calculate MACD (Moving Average Convergence Divergence) indicator.
    
    MACD is a trend-following momentum indicator that shows the relationship
    between two moving averages of a security's price.
    
    Args:
        prices: List of price values (floats)
        fast_period: Fast EMA period (default: 12)
        slow_period: Slow EMA period (default: 26)
        signal_period: Signal line EMA period (default: 9)
    
    Returns:
        Tuple[float, float, float]: (MACD line, Signal line, Histogram)
    
    Raises:
        ValueError: If not enough data or invalid parameters
        TypeError: If prices contain non-numeric values
    
    Examples:
        Basic MACD calculation with upward trend:
        >>> prices = list(range(1, 51))  # 1 to 50
        >>> macd_line, signal_line, histogram = macd(prices)
        >>> isinstance(macd_line, float) and isinstance(signal_line, float) and isinstance(histogram, float)
        True
        
        MACD with sufficient data:
        >>> prices = [22.27, 22.19, 22.08, 22.17, 22.18, 22.13, 22.23, 22.43, 22.24, 22.29,
        ...          22.15, 22.39, 22.38, 22.61, 23.36, 24.05, 23.75, 23.83, 23.95, 23.63,
        ...          23.82, 23.87, 23.65, 23.19, 23.10, 23.33, 22.68, 23.10, 22.40, 22.17,
        ...          22.50, 22.80, 23.10, 23.40, 23.70]
        >>> macd_line, signal_line, histogram = macd(prices)
        >>> isinstance(macd_line, float)
        True
        
        Custom periods:
        >>> prices = list(range(1, 101))  # 1 to 100
        >>> macd_line, signal_line, histogram = macd(prices, fast_period=5, slow_period=10, signal_period=3)
        >>> isinstance(macd_line, float)
        True
        
        Error cases - insufficient data:
        >>> macd([1, 2, 3, 4, 5], fast_period=12, slow_period=26)
        Traceback (most recent call last):
            ...
        ValueError: Not enough data: need at least 35 prices for MACD(12,26,9), got 5
        
        Error cases - empty list:
        >>> macd([])
        Traceback (most recent call last):
            ...
        ValueError: Not enough data: need at least 35 prices for MACD(12,26,9), got 0
        
        Error cases - invalid periods:
        >>> macd([1]*50, fast_period=0)
        Traceback (most recent call last):
            ...
        ValueError: Fast period must be positive, got 0
        
        Error cases - fast >= slow:
        >>> macd([1]*50, fast_period=26, slow_period=12)
        Traceback (most recent call last):
            ...
        ValueError: Fast period (26) must be less than slow period (12)
        
        Error cases - non-numeric prices:
        >>> macd([1, 2, "invalid"] + [4]*50, fast_period=12, slow_period=26)
        Traceback (most recent call last):
            ...
        TypeError: All prices must be numeric, found <class 'str'> at index 2
        
        Edge case - exactly minimum data:
        >>> prices = [1.0] * 35
        >>> macd_line, signal_line, histogram = macd(prices)
        >>> macd_line == 0.0 and signal_line == 0.0 and histogram == 0.0
        True
        
        Edge case - alternating prices:
        >>> prices = [10, 20] * 25  # 50 prices alternating between 10 and 20
        >>> macd_line, signal_line, histogram = macd(prices)
        >>> isinstance(macd_line, float)
        True
    """
    # Input validation
    if not isinstance(fast_period, int) or fast_period <= 0:
        raise ValueError(f"Fast period must be positive, got {fast_period}")
    
    if not isinstance(slow_period, int) or slow_period <= 0:
        raise ValueError(f"Slow period must be positive, got {slow_period}")
    
    if not isinstance(signal_period, int) or signal_period <= 0:
        raise ValueError(f"Signal period must be positive, got {signal_period}")
    
    if fast_period >= slow_period:
        raise ValueError(f"Fast period ({fast_period}) must be less than slow period ({slow_period})")
    
    min_required = slow_period + signal_period
    if len(prices) < min_required:
        raise ValueError(f"Not enough data: need at least {min_required} prices for MACD({fast_period},{slow_period},{signal_period}), got {len(prices)}")
    
    if not isinstance(prices, (list, tuple)):
        raise TypeError(f"Prices must be a list or tuple, got {type(prices)}")
    
    # Calculate EMAs
    fast_ema = ema(prices, fast_period)
    slow_ema = ema(prices, slow_period)
    
    # MACD line = Fast EMA - Slow EMA
    macd_line = fast_ema - slow_ema
    
    # Calculate MACD values for signal line calculation
    macd_values = []
    for i in range(slow_period, len(prices) + 1):
        subset = prices[:i]
        if len(subset) >= slow_period:
            fast_ema_i = ema(subset, fast_period)
            slow_ema_i = ema(subset, slow_period)
            macd_values.append(fast_ema_i - slow_ema_i)
    
    # Signal line = EMA of MACD line
    if len(macd_values) >= signal_period:
        signal_line = ema(macd_values, signal_period)
    else:
        signal_line = macd_line  # Fallback if not enough MACD values
    
    # Histogram = MACD line - Signal line
    histogram = macd_line - signal_line
    
    return (round(macd_line, 4), round(signal_line, 4), round(histogram, 4))


def macd_signal(prices: List[float], fast_period: int = 12, slow_period: int = 26, 
                signal_period: int = 9) -> str:
    """
    Generate MACD trading signal based on line crossovers.
    
    Args:
        prices: List of price values
        fast_period: Fast EMA period (default: 12)
        slow_period: Slow EMA period (default: 26)
        signal_period: Signal line EMA period (default: 9)
    
    Returns:
        str: Trading signal ('BUY', 'SELL', 'HOLD')
    
    Raises:
        ValueError: If MACD calculation fails
        TypeError: If inputs are not of correct type
    
    Examples:
        Signal generation with trend data:
        >>> prices = list(range(1, 51))  # Strong upward trend
        >>> macd_signal(prices) in ['BUY', 'SELL', 'HOLD']
        True
        
        Signal generation with downward trend:
        >>> prices = list(range(50, 0, -1))  # Strong downward trend
        >>> macd_signal(prices) in ['BUY', 'SELL', 'HOLD']
        True
        
        Neutral signal (MACD near signal):
        >>> prices = [50] * 50  # Flat prices
        >>> macd_signal(prices)
        'HOLD'
        
        Custom periods:
        >>> prices = list(range(1, 101))
        >>> macd_signal(prices, fast_period=5, slow_period=10, signal_period=3) in ['BUY', 'SELL', 'HOLD']
        True
        
        Error cases - insufficient data (propagated from macd function):
        >>> macd_signal([1, 2, 3])
        Traceback (most recent call last):
            ...
        ValueError: Not enough data: need at least 35 prices for MACD(12,26,9), got 3
        
        Error cases - invalid periods:
        >>> macd_signal([1]*50, fast_period=-5)
        Traceback (most recent call last):
            ...
        ValueError: Fast period must be positive, got -5
    """
    # Calculate MACD (this will handle all validation)
    macd_line, signal_line, histogram = macd(prices, fast_period, slow_period, signal_period)
    
    # Generate signal based on histogram
    if histogram > 0.001:  # MACD above signal line (with small threshold)
        return 'BUY'
    elif histogram < -0.001:  # MACD below signal line (with small threshold)
        return 'SELL'
    else:
        return 'HOLD'


def macd_crossover_signal(prices: List[float], lookback: int = 2,
                         fast_period: int = 12, slow_period: int = 26, 
                         signal_period: int = 9) -> Optional[str]:
    """
    Detect MACD crossover signals by comparing recent periods.
    
    Args:
        prices: List of price values
        lookback: Number of periods to look back for crossover detection
        fast_period: Fast EMA period (default: 12)
        slow_period: Slow EMA period (default: 26)
        signal_period: Signal line EMA period (default: 9)
    
    Returns:
        Optional[str]: Crossover signal ('BULLISH_CROSS', 'BEARISH_CROSS', None)
    
    Examples:
        Bullish crossover detection:
        >>> # Create prices that would generate a bullish crossover
        >>> prices = [20] * 30 + list(range(20, 40))  # Flat then rising
        >>> result = macd_crossover_signal(prices, lookback=2)
        >>> result in ['BULLISH_CROSS', 'BEARISH_CROSS', None]
        True
        
        No crossover:
        >>> prices = list(range(1, 51))  # Consistent trend
        >>> macd_crossover_signal(prices, lookback=2) in [None, 'BULLISH_CROSS']
        True
        
        Error cases - insufficient data:
        >>> macd_crossover_signal([1, 2, 3], lookback=2)
        Traceback (most recent call last):
            ...
        ValueError: Not enough data: need at least 37 prices for crossover detection, got 3
        
        Error cases - invalid lookback:
        >>> macd_crossover_signal([1]*50, lookback=0)
        Traceback (most recent call last):
            ...
        ValueError: Lookback must be positive, got 0
    """
    if lookback <= 0:
        raise ValueError(f"Lookback must be positive, got {lookback}")
    
    min_required = slow_period + signal_period + lookback
    if len(prices) < min_required:
        raise ValueError(f"Not enough data: need at least {min_required} prices for crossover detection, got {len(prices)}")
    
    # Calculate MACD for current and previous periods
    current_macd, current_signal, current_hist = macd(prices, fast_period, slow_period, signal_period)
    previous_macd, previous_signal, previous_hist = macd(prices[:-lookback], fast_period, slow_period, signal_period)
    
    # Detect crossovers
    if previous_hist <= 0 and current_hist > 0:
        return 'BULLISH_CROSS'
    elif previous_hist >= 0 and current_hist < 0:
        return 'BEARISH_CROSS'
    else:
        return None


if __name__ == "__main__":
    import doctest
    
    print("Running MACD strategy doctests...")
    
    # Run doctests with verbose output
    result = doctest.testmod(verbose=True)
    
    if result.failed == 0:
        print(f"\n✅ All {result.attempted} doctests passed!")
    else:
        print(f"\n❌ {result.failed} out of {result.attempted} doctests failed!")
    
    # Additional manual tests
    print("\n🧪 Running additional manual tests...")
    
    # Test with real-world data
    sample_prices = [
        22.27, 22.19, 22.08, 22.17, 22.18, 22.13, 22.23, 22.43, 22.24, 22.29,
        22.15, 22.39, 22.38, 22.61, 23.36, 24.05, 23.75, 23.83, 23.95, 23.63,
        23.82, 23.87, 23.65, 23.19, 23.10, 23.33, 22.68, 23.10, 22.40, 22.17,
        22.27, 22.19, 22.08, 22.17, 22.18, 22.13, 22.23, 22.43, 22.24, 22.29
    ]
    
    try:
        macd_line, signal_line, histogram = macd(sample_prices)
        signal = macd_signal(sample_prices)
        crossover = macd_crossover_signal(sample_prices)
        
        print(f"✅ Sample MACD calculation: Line={macd_line}, Signal={signal_line}, Histogram={histogram}")
        print(f"✅ Sample MACD signal: {signal}")
        print(f"✅ Sample crossover signal: {crossover}")
    except Exception as e:
        print(f"❌ Manual test failed: {e}")
    
    print("\n🎯 MACD strategy testing complete!")