# MT5 Bridge User Guide

The MT5 Bridge allows you to execute Python trading strategies on your MetaTrader 5 account. This guide explains how to set up and use the bridge.

## Overview

The MT5 Bridge provides:

1. Direct connection to your MT5 account from Python
2. Ability to place and manage trades
3. Access to historical price data
4. Strategy execution framework
5. Backtesting capabilities (via separate module)

## Setup Instructions

### Prerequisites

- MetaTrader 5 terminal installed
- Python 3.7 or higher
- Required Python packages: `MetaTrader5`, `numpy`, `pandas`

### Installation

1. Install the required Python packages:
   ```
   pip install MetaTrader5 numpy pandas
   ```

2. Ensure your MetaTrader 5 terminal is running before using the bridge

3. Make sure your MT5 account has API access enabled

## Using the MT5 Bridge

### Connecting to MT5

```python
import requests

# API endpoint
API_URL = "http://localhost:8000/api/v1"

# Connect to MT5
response = requests.post(f"{API_URL}/mt5/connect", json={
    "server": "YourBrokerServer",
    "login": "YourLoginID",
    "password": "YourPassword"
})

print(response.json())
```

### Creating Python Strategies

You can create Python strategies in the standard format:

```python
"""
Strategy Name and Description
"""

import pandas as pd
from app.mt5_bridge.mt5_client import MT5Client

class MyStrategy:
    def __init__(self, client, symbol, timeframe, parameters...):
        self.client = client
        self.symbol = symbol
        # Initialize other parameters
        
    def calculate_indicators(self, data):
        # Calculate your indicators
        return data
    
    def check_for_signals(self, data):
        # Check for trading signals
        return signal  # 'buy', 'sell', or None
    
    def execute_trade(self, signal):
        # Execute trade based on signal
        pass
        
    def run(self, once=False):
        # Main execution loop
        pass

# Entry point for direct execution
def run_strategy(login, password, server, parameters...):
    client = MT5Client()
    client.connect(login, password, server)
    strategy = MyStrategy(client, parameters...)
    strategy.run()
```

### Uploading Strategies

```python
# Upload a strategy
with open("my_strategy.py", "r") as f:
    strategy_code = f.read()

response = requests.post(f"{API_URL}/mt5/strategy", json={
    "name": "my_strategy.py",
    "code": strategy_code,
    "description": "My trading strategy"
})

print(response.json())
```

### Executing Strategies

```python
# Execute a strategy
response = requests.post(f"{API_URL}/mt5/execute_strategy", params={
    "login": "YourLoginID",
    "server": "YourBrokerServer",
    "strategy_name": "my_strategy.py",
    "symbol": "EURUSD",
    "timeframe": "H1"
})

print(response.json())
```

### Placing Orders Manually

```python
# Place a buy order
response = requests.post(f"{API_URL}/mt5/order", params={
    "login": "YourLoginID",
    "server": "YourBrokerServer"
}, json={
    "symbol": "EURUSD",
    "order_type": "buy",
    "volume": 0.1,
    "stop_loss": 1.1200,
    "take_profit": 1.1300
})

print(response.json())
```

## Sample Strategies

The platform includes sample strategies to get you started:

1. **Moving Average Crossover** - Trades when fast MA crosses slow MA
2. **RSI Divergence** - Identifies price-RSI divergences for potential reversals

## Troubleshooting

Common issues:

1. **Connection errors**: Make sure MT5 terminal is running
2. **Authentication failures**: Verify login credentials and server details
3. **Strategy errors**: Check the strategy code for Python errors
4. **Order placement failures**: Verify you have sufficient balance and the symbol is available

## Further Reading

For more information, refer to:

1. The MT5 Bridge API Documentation
2. Python MT5 Client Documentation
3. MetaTrader 5 Python Documentation: https://www.mql5.com/en/docs/python_metatrader5
