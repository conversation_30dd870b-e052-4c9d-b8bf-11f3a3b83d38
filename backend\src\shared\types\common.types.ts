import { Request } from 'express';
import { User } from '@ai-trading/shared';

// Extended Express Request with User
export interface AuthenticatedRequest extends Request {
  user?: User;
}

// Database Transaction Type
export interface DatabaseTransaction {
  commit(): Promise<void>;
  rollback(): Promise<void>;
}

// Service Response Type
export interface ServiceResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

// Async Service Handler
export type AsyncServiceHandler<T = any, U = any> = (
  input: T
) => Promise<ServiceResponse<U>>;

// Controller Response Type
export interface ControllerResponse<T = any> {
  statusCode: number;
  success: boolean;
  message?: string;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

// Environment Variables Type
export interface EnvironmentConfig {
  NODE_ENV: 'development' | 'production' | 'test';
  PORT: number;
  APP_NAME: string;
  LOG_LEVEL: 'error' | 'warn' | 'info' | 'debug' | 'trace';
  
  // Database
  DB_HOST: string;
  DB_PORT: number;
  DB_NAME: string;
  DB_USERNAME: string;
  DB_PASSWORD: string;
  DB_SSL: boolean;
  DB_POOL_SIZE: number;
  
  // Redis
  REDIS_HOST?: string;
  REDIS_PORT?: number;
  REDIS_PASSWORD?: string;
  REDIS_DB?: number;
  
  // Security
  JWT_SECRET: string;
  JWT_EXPIRES_IN: string;
  REFRESH_TOKEN_EXPIRES_IN: string;
  BCRYPT_ROUNDS: number;
  
  // CORS
  CORS_ORIGINS: string;
  
  // Rate Limiting
  RATE_LIMIT_WINDOW_MS: number;
  RATE_LIMIT_MAX_REQUESTS: number;
  
  // File Upload
  UPLOAD_MAX_SIZE: number;
  UPLOAD_ALLOWED_TYPES: string;
  
  // API Keys
  OPENAI_API_KEY?: string;
  ANTHROPIC_API_KEY?: string;
  GOOGLE_API_KEY?: string;
  COHERE_API_KEY?: string;
  
  // Trading Data
  ALPHA_VANTAGE_API_KEY?: string;
  POLYGON_API_KEY?: string;
  IEX_CLOUD_API_KEY?: string;
  
  // Python Scripts
  PYTHON_EXECUTABLE: string;
  XLLM_SCRIPT_PATH: string;
  DGM_SCRIPT_PATH: string;
}

// Logger Interface
export interface Logger {
  error(message: string, meta?: any): void;
  warn(message: string, meta?: any): void;
  info(message: string, meta?: any): void;
  debug(message: string, meta?: any): void;
  trace(message: string, meta?: any): void;
}

// Database Repository Interface
export interface Repository<T> {
  create(data: Partial<T>): Promise<T>;
  findById(id: string): Promise<T | null>;
  findMany(filters?: any): Promise<T[]>;
  update(id: string, data: Partial<T>): Promise<T | null>;
  delete(id: string): Promise<boolean>;
}

// Cache Interface
export interface Cache {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T, ttl?: number): Promise<void>;
  del(key: string): Promise<void>;
  exists(key: string): Promise<boolean>;
}

// File Storage Interface
export interface FileStorage {
  upload(file: Express.Multer.File, path: string): Promise<string>;
  download(path: string): Promise<Buffer>;
  delete(path: string): Promise<void>;
  exists(path: string): Promise<boolean>;
}

// Event Emitter Interface
export interface EventEmitter {
  emit(event: string, data: any): void;
  on(event: string, listener: (data: any) => void): void;
  off(event: string, listener: (data: any) => void): void;
}

// Pagination Options
export interface PaginationOptions {
  page: number;
  limit: number;
  orderBy?: string;
  order?: 'asc' | 'desc';
}

// Paginated Result
export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}