#!/usr/bin/env python
"""Quick test script for pattern detection as specified in setup guide"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.darwin_godel.pattern_detector import StrategyPatternDetector
from services.darwin_godel.pattern_report import PatternReport

def main():
    print("🧪 Testing Pattern Detection System (Setup Guide Verification)")
    print("=" * 60)
    
    # Test strategy from setup guide
    strategy = """
def trading_strategy(data, params):
    sma = calculate_sma(data['close'], 20)
    if data['close'][-1] < sma[-1] * 0.98:
        return {'signal': 'buy'}
    elif data['close'][-1] > sma[-1] * 1.02:
        return {'signal': 'sell'}
    return {'signal': 'hold'}
"""

    print("📝 Analyzing Strategy:")
    print(strategy)
    print("\n" + "=" * 60)
    
    # Analyze the strategy
    detector = StrategyPatternDetector()
    result = detector.analyze_strategy(strategy)
    
    # Generate and display report
    report = PatternReport.generate_report(result)
    print(report)
    
    # Display simple summary
    summary = PatternReport.generate_simple_summary(result)
    print(f"📊 Summary: {summary}")
    
    print("\n✅ Pattern detection system is working correctly!")
    print("🎉 Setup guide requirements fulfilled!")

if __name__ == "__main__":
    main()