import { BacktestMetrics, BacktestTrade } from '../../../shared/schemas/backtest.schemas';

export class MetricsCalculator {
  calculateMetrics(
    trades: BacktestTrade[],
    balanceCurve: Array<{ timestamp: Date; balance: number; equity: number; drawdown: number }>
  ): BacktestMetrics {
    if (trades.length === 0) {
      return this.getEmptyMetrics();
    }

    const winningTrades = trades.filter(t => t.pnl > 0);
    const losingTrades = trades.filter(t => t.pnl < 0);
    
    const totalPnL = trades.reduce((sum, t) => sum + t.pnl, 0);
    const grossProfit = winningTrades.reduce((sum, t) => sum + t.pnl, 0);
    const grossLoss = losingTrades.reduce((sum, t) => sum + t.pnl, 0);
    
    const winRate = trades.length > 0 ? winningTrades.length / trades.length : 0;
    const profitFactor = grossLoss !== 0 ? grossProfit / Math.abs(grossLoss) : 0;
    
    const averageWin = winningTrades.length > 0 ? grossProfit / winningTrades.length : 0;
    const averageLoss = losingTrades.length > 0 ? grossLoss / losingTrades.length : 0;
    
    const largestWin = winningTrades.length > 0 ? Math.max(...winningTrades.map(t => t.pnl)) : 0;
    const largestLoss = losingTrades.length > 0 ? Math.min(...losingTrades.map(t => t.pnl)) : 0;
    
    const { maxDrawdown, maxDrawdownPercent } = this.calculateDrawdown(balanceCurve);
    
    const expectancy = trades.length > 0 ? totalPnL / trades.length : 0;
    
    const averageTradeDuration = this.calculateAverageTradeDuration(trades);
    const totalTimeInMarket = this.calculateTotalTimeInMarket(trades);
    
    const sharpeRatio = this.calculateSharpeRatio(balanceCurve);
    const sortinoRatio = this.calculateSortinoRatio(balanceCurve);
    const calmarRatio = this.calculateCalmarRatio(balanceCurve, maxDrawdown);
    const kellyPercentage = this.calculateKellyCriterion(winRate, averageWin, Math.abs(averageLoss));

    return {
      total_trades: trades.length,
      winning_trades: winningTrades.length,
      losing_trades: losingTrades.length,
      win_rate: winRate,
      total_pnl: totalPnL,
      gross_profit: grossProfit,
      gross_loss: grossLoss,
      profit_factor: profitFactor,
      max_drawdown: maxDrawdown,
      max_drawdown_percent: maxDrawdownPercent,
      average_win: averageWin,
      average_loss: averageLoss,
      largest_win: largestWin,
      largest_loss: largestLoss,
      expectancy,
      average_trade_duration_minutes: averageTradeDuration,
      total_time_in_market_minutes: totalTimeInMarket,
      sharpe_ratio: sharpeRatio,
      sortino_ratio: sortinoRatio,
      calmar_ratio: calmarRatio,
      kelly_criterion: kellyPercentage,
    };
  }

  calculateDrawdown(
    balanceCurve: Array<{ timestamp: Date; balance: number; equity: number; drawdown: number }>
  ): { maxDrawdown: number; maxDrawdownPercent: number } {
    if (balanceCurve.length === 0) {
      return { maxDrawdown: 0, maxDrawdownPercent: 0 };
    }

    let maxEquity = balanceCurve[0].equity;
    let maxDrawdown = 0;
    let maxDrawdownPercent = 0;

    for (const point of balanceCurve) {
      if (point.equity > maxEquity) {
        maxEquity = point.equity;
      }

      const drawdown = point.equity - maxEquity;
      const drawdownPercent = maxEquity > 0 ? drawdown / maxEquity : 0;

      if (drawdown < maxDrawdown) {
        maxDrawdown = drawdown;
      }

      if (drawdownPercent < maxDrawdownPercent) {
        maxDrawdownPercent = drawdownPercent;
      }
    }

    return { maxDrawdown, maxDrawdownPercent };
  }

  calculateSharpeRatio(
    balanceCurve: Array<{ timestamp: Date; balance: number; equity: number; drawdown: number }>,
    riskFreeRate: number = 0.02 // 2% annual risk-free rate
  ): number {
    if (balanceCurve.length < 2) return 0;

    const returns = this.calculateReturns(balanceCurve);
    if (returns.length === 0) return 0;

    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const returnStdDev = this.calculateStandardDeviation(returns);

    if (returnStdDev === 0) return 0;

    // Annualize the metrics (assuming daily data)
    const annualizedReturn = avgReturn * 252; // 252 trading days per year
    const annualizedStdDev = returnStdDev * Math.sqrt(252);

    return (annualizedReturn - riskFreeRate) / annualizedStdDev;
  }

  private getEmptyMetrics(): BacktestMetrics {
    return {
      total_trades: 0,
      winning_trades: 0,
      losing_trades: 0,
      win_rate: 0,
      total_pnl: 0,
      gross_profit: 0,
      gross_loss: 0,
      profit_factor: 0,
      max_drawdown: 0,
      max_drawdown_percent: 0,
      average_win: 0,
      average_loss: 0,
      largest_win: 0,
      largest_loss: 0,
      expectancy: 0,
    };
  }

  private calculateAverageTradeDuration(trades: BacktestTrade[]): number {
    const tradesWithDuration = trades.filter(t => t.duration_minutes !== undefined);
    if (tradesWithDuration.length === 0) return 0;

    const totalDuration = tradesWithDuration.reduce((sum, t) => sum + (t.duration_minutes || 0), 0);
    return totalDuration / tradesWithDuration.length;
  }

  private calculateTotalTimeInMarket(trades: BacktestTrade[]): number {
    return trades.reduce((sum, t) => sum + (t.duration_minutes || 0), 0);
  }

  private calculateReturns(
    balanceCurve: Array<{ timestamp: Date; balance: number; equity: number; drawdown: number }>
  ): number[] {
    const returns: number[] = [];
    
    for (let i = 1; i < balanceCurve.length; i++) {
      const prevEquity = balanceCurve[i - 1].equity;
      const currentEquity = balanceCurve[i].equity;
      
      if (prevEquity > 0) {
        const returnRate = (currentEquity - prevEquity) / prevEquity;
        returns.push(returnRate);
      }
    }
    
    return returns;
  }

  private calculateStandardDeviation(values: number[]): number {
    if (values.length === 0) return 0;
    
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squaredDifferences = values.map(val => Math.pow(val - mean, 2));
    const variance = squaredDifferences.reduce((sum, val) => sum + val, 0) / values.length;
    
    return Math.sqrt(variance);
  }

  private calculateSortinoRatio(
    balanceCurve: Array<{ timestamp: Date; balance: number; equity: number; drawdown: number }>,
    targetReturn: number = 0
  ): number {
    if (balanceCurve.length < 2) return 0;

    const returns = this.calculateReturns(balanceCurve);
    if (returns.length === 0) return 0;

    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    
    // Calculate downside deviation (only negative returns)
    const negativeReturns = returns.filter(r => r < targetReturn);
    if (negativeReturns.length === 0) return Infinity;

    const downsideVariance = negativeReturns.reduce((sum, r) => sum + Math.pow(r - targetReturn, 2), 0) / returns.length;
    const downsideDeviation = Math.sqrt(downsideVariance);

    if (downsideDeviation === 0) return 0;

    // Annualize the metrics
    const annualizedReturn = avgReturn * 252;
    const annualizedDownsideDeviation = downsideDeviation * Math.sqrt(252);

    return (annualizedReturn - targetReturn) / annualizedDownsideDeviation;
  }

  private calculateCalmarRatio(
    balanceCurve: Array<{ timestamp: Date; balance: number; equity: number; drawdown: number }>,
    maxDrawdown: number
  ): number {
    if (balanceCurve.length < 2 || maxDrawdown === 0) return 0;

    const returns = this.calculateReturns(balanceCurve);
    if (returns.length === 0) return 0;

    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const annualizedReturn = avgReturn * 252;

    return annualizedReturn / Math.abs(maxDrawdown);
  }

  private calculateKellyCriterion(winRate: number, avgWin: number, avgLoss: number): number {
    if (avgLoss === 0) return 0;
    
    const winLossRatio = avgWin / avgLoss;
    const kellyPercentage = winRate - ((1 - winRate) / winLossRatio);
    
    // Cap Kelly percentage at reasonable levels
    return Math.max(0, Math.min(kellyPercentage, 0.25)); // Max 25%
  }
}