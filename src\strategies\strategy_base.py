"""
Base Strategy Module

This module provides the base class for all trading strategies.
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union
from datetime import datetime

from src.trading.mt5_bridge_tdd import MT5Bridge

# Configure logging
logger = logging.getLogger(__name__)


class StrategyBase(ABC):
    """
    Base class for all trading strategies
    
    This abstract class defines the interface that all strategies must implement.
    It provides common functionality for strategy execution, risk management,
    and performance tracking.
    """
    
    def __init__(self, 
                name: str, 
                symbols: List[str], 
                mt5_bridge: Optional[MT5Bridge] = None,
                risk_per_trade: float = 0.02,
                max_open_positions: int = 5,
                offline_mode: bool = False):
        """
        Initialize the strategy
        
        Args:
            name: Strategy name
            symbols: List of symbols to trade
            mt5_bridge: MT5 Bridge instance (if None, a new one will be created)
            risk_per_trade: Risk per trade as a fraction of account balance (0.02 = 2%)
            max_open_positions: Maximum number of open positions
            offline_mode: Whether to run in offline mode
        """
        self.name = name
        self.symbols = symbols
        self.risk_per_trade = risk_per_trade
        self.max_open_positions = max_open_positions
        self.offline_mode = offline_mode
        
        # Create or use provided MT5 Bridge
        self.mt5_bridge = mt5_bridge if mt5_bridge else MT5Bridge(offline_mode=offline_mode)
        
        # Ensure connection
        if not self.mt5_bridge.is_connected():
            self.mt5_bridge.connect()
        
        # Strategy state
        self.active = False
        self.last_update = None
        self.positions = []
        self.orders = []
        self.performance_metrics = {
            "total_trades": 0,
            "winning_trades": 0,
            "losing_trades": 0,
            "total_profit": 0.0,
            "max_drawdown": 0.0,
            "win_rate": 0.0
        }
        
        logger.info(f"Strategy '{name}' initialized with symbols: {symbols}")
    
    def start(self) -> bool:
        """
        Start the strategy
        
        Returns:
            bool: True if started successfully, False otherwise
        """
        if self.active:
            logger.warning(f"Strategy '{self.name}' is already active")
            return False
        
        try:
            # Ensure connection to MT5
            if not self.mt5_bridge.is_connected():
                if not self.mt5_bridge.connect():
                    logger.error(f"Failed to connect to MT5, cannot start strategy '{self.name}'")
                    return False
            
            # Initialize strategy
            self.active = True
            self.last_update = datetime.now()
            logger.info(f"Strategy '{self.name}' started")
            
            # Run initial update
            self.update()
            
            return True
            
        except Exception as e:
            logger.error(f"Error starting strategy '{self.name}': {str(e)}")
            self.active = False
            return False
    
    def stop(self) -> bool:
        """
        Stop the strategy
        
        Returns:
            bool: True if stopped successfully, False otherwise
        """
        if not self.active:
            logger.warning(f"Strategy '{self.name}' is not active")
            return False
        
        try:
            # Close all positions if requested
            self.active = False
            logger.info(f"Strategy '{self.name}' stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping strategy '{self.name}': {str(e)}")
            return False
    
    def update(self) -> bool:
        """
        Update the strategy (process market data, execute signals)
        
        Returns:
            bool: True if updated successfully, False otherwise
        """
        if not self.active:
            logger.warning(f"Cannot update inactive strategy '{self.name}'")
            return False
        
        try:
            # Update positions
            self.positions = self.mt5_bridge.get_positions()
            
            # Generate signals
            signals = self.generate_signals()
            
            # Execute signals
            for signal in signals:
                self.execute_signal(signal)
            
            # Update last update time
            self.last_update = datetime.now()
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating strategy '{self.name}': {str(e)}")
            return False
    
    def execute_signal(self, signal: Dict[str, Any]) -> Optional[int]:
        """
        Execute a trading signal
        
        Args:
            signal: Trading signal with the following keys:
                - symbol: Trading symbol
                - action: "BUY", "SELL", or "CLOSE"
                - lot: Optional lot size (if not provided, calculated based on risk)
                - price: Optional price for limit orders
                - stop_loss: Optional stop loss price
                - take_profit: Optional take profit price
        
        Returns:
            int: Order ID if successful, None otherwise
        """
        if not self.active:
            logger.warning(f"Cannot execute signal in inactive strategy '{self.name}'")
            return None
        
        try:
            # Extract signal parameters
            symbol = signal.get("symbol")
            action = signal.get("action")
            lot = signal.get("lot")
            price = signal.get("price")
            stop_loss = signal.get("stop_loss")
            take_profit = signal.get("take_profit")
            
            # Validate signal
            if not symbol or not action:
                logger.error(f"Invalid signal: {signal}")
                return None
            
            # Check if we can open more positions
            if action in ["BUY", "SELL"] and len(self.positions) >= self.max_open_positions:
                logger.warning(f"Maximum positions reached ({self.max_open_positions}), cannot open new position")
                return None
            
            # Calculate lot size if not provided
            if not lot and action in ["BUY", "SELL"]:
                lot = self.calculate_position_size(symbol, action, stop_loss)
            
            # Execute the signal
            if action == "CLOSE":
                # Close position for symbol
                for position in self.positions:
                    if position["symbol"] == symbol:
                        self.mt5_bridge.close_order(position["id"])
                        logger.info(f"Closed position for {symbol}")
                        return position["id"]
                
                logger.warning(f"No open position found for {symbol}")
                return None
                
            elif action in ["BUY", "SELL"]:
                # Place new order
                order_id = self.mt5_bridge.place_order(
                    symbol=symbol,
                    order_type=action,
                    lot=lot,
                    price=price,
                    stop_loss=stop_loss,
                    take_profit=take_profit
                )
                
                logger.info(f"Placed {action} order for {symbol}, lot={lot}, order_id={order_id}")
                self.performance_metrics["total_trades"] += 1
                return order_id
            
            else:
                logger.error(f"Unknown action: {action}")
                return None
                
        except Exception as e:
            logger.error(f"Error executing signal: {str(e)}")
            return None
    
    def calculate_position_size(self, 
                              symbol: str, 
                              order_type: str, 
                              stop_loss: Optional[float] = None) -> float:
        """
        Calculate position size based on risk parameters
        
        Args:
            symbol: Trading symbol
            order_type: Order type ("BUY" or "SELL")
            stop_loss: Stop loss price
        
        Returns:
            float: Position size in lots
        """
        # In a real implementation, this would calculate the position size
        # based on account balance, risk per trade, and stop loss distance
        # For the MVP, we'll use a simple fixed lot size
        return 0.1
    
    @abstractmethod
    def generate_signals(self) -> List[Dict[str, Any]]:
        """
        Generate trading signals based on market data and strategy logic
        
        This method must be implemented by all strategy subclasses.
        
        Returns:
            List[Dict]: List of trading signals
        """
        pass
    
    def get_performance(self) -> Dict[str, Any]:
        """
        Get strategy performance metrics
        
        Returns:
            Dict: Performance metrics
        """
        # Calculate win rate
        if self.performance_metrics["total_trades"] > 0:
            self.performance_metrics["win_rate"] = (
                self.performance_metrics["winning_trades"] / 
                self.performance_metrics["total_trades"]
            )
        
        return self.performance_metrics