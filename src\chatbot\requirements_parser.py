"""
Natural Language Requirements Parser
Converts user natural language input into structured StrategyRequest objects
"""

import re
from typing import Dict, List, Optional, Set
from dataclasses import dataclass

from .models import StrategyRequest, StrategyType, Timeframe, MLModel


class RequirementsParser:
    """Parses natural language trading strategy requirements"""
    
    def __init__(self):
        self.strategy_keywords = {
            "mean_reversion": [
                "mean reversion", "mean reverting", "revert to mean", "oversold", "overbought",
                "rsi", "bollinger bands", "stochastic", "contrarian"
            ],
            "momentum": [
                "momentum", "trend following", "breakout", "macd", "moving average crossover",
                "trend", "directional", "follow the trend"
            ],
            "breakout": [
                "breakout", "break out", "resistance", "support", "channel", "range",
                "volatility breakout", "price breakout"
            ],
            "machine_learning": [
                "machine learning", "ml", "ai", "neural network", "random forest",
                "svm", "classification", "prediction", "model", "algorithm"
            ],
            "pairs_trading": [
                "pairs trading", "pair trade", "statistical arbitrage", "cointegration",
                "spread", "correlation", "hedge"
            ],
            "grid_trading": [
                "grid", "grid trading", "martingale", "averaging down", "dollar cost averaging"
            ]
        }
        
        self.indicator_keywords = {
            "rsi": ["rsi", "relative strength index"],
            "macd": ["macd", "moving average convergence divergence"],
            "sma": ["sma", "simple moving average", "moving average"],
            "ema": ["ema", "exponential moving average"],
            "bollinger_bands": ["bollinger bands", "bollinger", "bb"],
            "stochastic": ["stochastic", "stoch"],
            "atr": ["atr", "average true range"],
            "adx": ["adx", "average directional index"],
            "cci": ["cci", "commodity channel index"],
            "williams_r": ["williams %r", "williams r", "williams percent r"]
        }
        
        self.symbol_patterns = [
            r'\b([A-Z]{6})\b',  # EURUSD, GBPUSD, etc.
            r'\b([A-Z]{3}/[A-Z]{3})\b',  # EUR/USD, GBP/USD, etc.
            r'\b([A-Z]{3}-[A-Z]{3})\b'   # EUR-USD, GBP-USD, etc.
        ]
        
        self.timeframe_keywords = {
            "1M": ["1 minute", "1min", "1m", "one minute"],
            "5M": ["5 minute", "5min", "5m", "five minute"],
            "15M": ["15 minute", "15min", "15m", "fifteen minute"],
            "30M": ["30 minute", "30min", "30m", "thirty minute"],
            "1H": ["1 hour", "1hr", "1h", "hourly", "one hour"],
            "4H": ["4 hour", "4hr", "4h", "four hour"],
            "1D": ["daily", "1 day", "1d", "day", "one day"],
            "1W": ["weekly", "1 week", "1w", "week", "one week"]
        }
        
        self.ml_model_keywords = {
            "random_forest": ["random forest", "rf", "random forest classifier"],
            "svm": ["svm", "support vector machine", "support vector"],
            "neural_network": ["neural network", "nn", "neural net", "deep learning"],
            "xgboost": ["xgboost", "xgb", "gradient boosting"],
            "lstm": ["lstm", "long short term memory", "recurrent neural network", "rnn"]
        }
    
    def parse(self, user_input: str) -> StrategyRequest:
        """Parse natural language input into structured requirements"""
        if not user_input or not user_input.strip():
            raise ValueError("Empty input provided")
        
        text = user_input.lower().strip()
        
        try:
            # Parse strategy type
            strategy_type = self._extract_strategy_type(text)
            
            # Parse symbols
            symbols = self._extract_symbols(user_input)  # Use original case for symbols
            
            # Parse timeframe
            timeframe = self._extract_timeframe(text)
            
            # Parse indicators
            indicators = self._extract_indicators(text)
            
            # Parse risk management parameters
            risk_params = self._extract_risk_parameters(text)
            
            # Parse ML-specific parameters
            ml_params = self._extract_ml_parameters(text)
            
            # Create the request
            request = StrategyRequest(
                strategy_type=strategy_type,
                symbols=symbols,
                timeframe=timeframe,
                indicators=indicators,
                **risk_params,
                **ml_params
            )
            
            # Validate the request
            self._validate_request(request)
            
            return request
            
        except Exception as e:
            raise ValueError(f"Unable to parse strategy requirements: {str(e)}")
    
    def _extract_strategy_type(self, text: str) -> str:
        """Extract strategy type from text"""
        for strategy_type, keywords in self.strategy_keywords.items():
            for keyword in keywords:
                if keyword in text:
                    return strategy_type
        
        # Default to momentum if no specific type found but has trend indicators
        trend_indicators = ["macd", "moving average", "ema", "sma"]
        if any(indicator in text for indicator in trend_indicators):
            return "momentum"
        
        # Default to mean reversion if has mean reversion indicators
        mr_indicators = ["rsi", "bollinger", "stochastic", "oversold", "overbought"]
        if any(indicator in text for indicator in mr_indicators):
            return "mean_reversion"
        
        # If we can't determine, default to momentum
        return "momentum"
    
    def _extract_symbols(self, text: str) -> List[str]:
        """Extract trading symbols from text"""
        symbols = []
        
        for pattern in self.symbol_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                # Normalize symbol format to XXXYYY
                symbol = match.replace('/', '').replace('-', '')
                if len(symbol) == 6:
                    symbols.append(symbol)
        
        # If no symbols found, default to EURUSD
        if not symbols:
            symbols = ["EURUSD"]
        
        return list(set(symbols))  # Remove duplicates
    
    def _extract_timeframe(self, text: str) -> Optional[str]:
        """Extract timeframe from text"""
        for timeframe, keywords in self.timeframe_keywords.items():
            for keyword in keywords:
                if keyword in text:
                    return timeframe
        return None
    
    def _extract_indicators(self, text: str) -> List[str]:
        """Extract technical indicators from text"""
        indicators = []
        
        for indicator, keywords in self.indicator_keywords.items():
            for keyword in keywords:
                if keyword in text:
                    indicators.append(indicator)
                    break
        
        return list(set(indicators))  # Remove duplicates
    
    def _extract_risk_parameters(self, text: str) -> Dict:
        """Extract risk management parameters from text"""
        params = {}
        
        # Risk per trade
        risk_match = re.search(r'risk\s+(\d+(?:\.\d+)?)\s*%', text)
        if risk_match:
            params['risk_per_trade'] = float(risk_match.group(1)) / 100
        
        # Maximum positions
        pos_match = re.search(r'(?:max|maximum)\s+(\d+)\s+position', text)
        if pos_match:
            params['max_positions'] = int(pos_match.group(1))
        
        # Stop loss
        sl_match = re.search(r'stop\s+loss\s+(?:at\s+)?(\d+)\s+pip', text)
        if sl_match:
            params['stop_loss_pips'] = int(sl_match.group(1))
        
        # Take profit
        tp_match = re.search(r'take\s+profit\s+(?:at\s+)?(\d+)\s+pip', text)
        if tp_match:
            params['take_profit_pips'] = int(tp_match.group(1))
        
        return params
    
    def _extract_ml_parameters(self, text: str) -> Dict:
        """Extract machine learning specific parameters"""
        params = {}
        
        # ML model type
        for model, keywords in self.ml_model_keywords.items():
            for keyword in keywords:
                if keyword in text:
                    params['ml_model'] = model
                    break
            if 'ml_model' in params:
                break
        
        # Training parameters
        train_match = re.search(r'train\s+on\s+(?:last\s+)?(\d+)\s+bar', text)
        if train_match:
            params['training_bars'] = int(train_match.group(1))
        
        retrain_match = re.search(r'retrain\s+every\s+(\d+)\s+bar', text)
        if retrain_match:
            params['retrain_frequency'] = int(retrain_match.group(1))
        
        # Features (for ML strategies)
        if 'ml_model' in params:
            features = []
            feature_keywords = {
                "rsi": ["rsi"],
                "macd": ["macd"],
                "price_momentum": ["price momentum", "momentum"],
                "volatility": ["volatility", "atr"],
                "volume": ["volume"],
                "price_change": ["price change", "returns"]
            }
            
            for feature, keywords in feature_keywords.items():
                for keyword in keywords:
                    if keyword in text:
                        features.append(feature)
                        break
            
            if features:
                params['features'] = features
        
        return params
    
    def _validate_request(self, request: StrategyRequest) -> None:
        """Validate the parsed request"""
        if not request.strategy_type:
            raise ValueError("Could not determine strategy type")
        
        if not request.symbols:
            raise ValueError("No trading symbols specified")
        
        # Validate ML-specific requirements
        if request.strategy_type == "machine_learning":
            if not request.ml_model:
                raise ValueError("Machine learning strategy requires model specification")
            if not request.features:
                raise ValueError("Machine learning strategy requires feature specification")
        
        # Validate risk parameters
        if request.risk_per_trade <= 0 or request.risk_per_trade > 0.1:
            raise ValueError("Risk per trade must be between 0 and 10%")
        
        if request.max_positions <= 0 or request.max_positions > 20:
            raise ValueError("Maximum positions must be between 1 and 20")
    
    def get_parsing_suggestions(self, error_message: str) -> List[str]:
        """Provide suggestions when parsing fails"""
        suggestions = [
            "Try specifying a clear strategy type (e.g., 'mean reversion', 'momentum', 'breakout')",
            "Include specific trading symbols (e.g., 'EURUSD', 'GBPUSD')",
            "Mention technical indicators (e.g., 'RSI', 'MACD', 'Bollinger Bands')",
            "Specify timeframe (e.g., '1 hour', 'daily', '15 minutes')",
            "Include risk parameters (e.g., 'risk 2% per trade', 'maximum 3 positions')"
        ]
        
        if "machine learning" in error_message.lower():
            suggestions.extend([
                "For ML strategies, specify the model type (e.g., 'Random Forest', 'Neural Network')",
                "Include features for the model (e.g., 'using RSI, MACD, and price momentum')",
                "Mention training parameters (e.g., 'train on last 1000 bars')"
            ])
        
        return suggestions