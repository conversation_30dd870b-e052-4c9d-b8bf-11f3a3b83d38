
from pydantic import BaseModel
from typing import Optional

class OrderRequest(BaseModel):
    symbol: str
    volume: float
    order_type: str  # "buy" | "sell"
    price: float
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None

class OrderResult(BaseModel):
    success: bool
    order_id: Optional[int] = None
    error: Optional[str] = None

class AccountInfo(BaseModel):
    balance: float
    equity: float
    margin: float
    currency: str
