#!/usr/bin/env python
# run_mt5_tdd_tests.py
"""
Script to run MT5 Bridge TDD tests
"""

import os
import sys
import logging

# Try to import pytest, install if not available
try:
    import pytest
except ImportError:
    print("pytest not found. Please install it using: pip install pytest hypothesis")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("mt5_tdd_tests")

def run_tests():
    """Run MT5 Bridge TDD tests"""
    logger.info("Running MT5 Bridge TDD tests...")
    
    # Define test files
    test_files = [
        "tests/test_mt5_bridge_tdd.py",
        "tests/test_mt5_bridge_property.py"
    ]
    
    # Run tests with pytest
    args = [
        "-v",                  # Verbose output
        "--no-header",         # No header
        "--no-summary",        # No summary
        "--tb=short",          # Short traceback
    ] + test_files
    
    result = pytest.main(args)
    
    if result == 0:
        logger.info("All tests passed!")
    else:
        logger.error(f"Tests failed with exit code: {result}")
    
    return result

if __name__ == "__main__":
    sys.exit(run_tests())