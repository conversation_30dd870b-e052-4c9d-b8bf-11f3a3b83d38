import { v4 as uuidv4 } from 'uuid';
import { EventEmitter } from 'events';
import { Logger, ServiceResponse } from '@/shared/types';
import {
  OrderRequest,
  OrderResult,
  AccountInfo,
  Position,
  TradingEngineRequest,
  // TradingEngineResponse, // Not currently used
} from '../../../../shared/schemas';
import { PythonEngineService } from './python-engine.service';

export interface TradingBridgeServiceDependencies {
  pythonEngineService: PythonEngineService;
  logger: Logger;
}

/**
 * High-level trading service that bridges between our API and the Python trading engine
 * Provides simplified, type-safe trading operations
 */
export class TradingBridgeService extends EventEmitter {
  constructor(private dependencies: TradingBridgeServiceDependencies) {
    super();
    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    // Forward Python engine events
    this.dependencies.pythonEngineService.on('trading_command_sent', (data) => {
      this.emit('trading_command_completed', data);
    });

    this.dependencies.pythonEngineService.on('health_check_completed', (data) => {
      this.emit('trading_engine_health_changed', data);
    });
  }

  /**
   * Get current account information
   */
  async getAccountInfo(): Promise<ServiceResponse<AccountInfo>> {
    try {
      const request: TradingEngineRequest = {
        action: 'get_account',
        timestamp: new Date(),
        request_id: uuidv4(),
      };

      const response = await this.dependencies.pythonEngineService.sendTradingCommand(request);

      if (!response.success || !response.data) {
        return {
          success: false,
          error: response.error || {
            code: 'ACCOUNT_INFO_ERROR',
            message: 'Failed to retrieve account information',
          },
        };
      }

      // Validate and transform the response data to AccountInfo
      if (!response.data.success || !response.data.data) {
        return {
          success: false,
          error: {
            code: 'ACCOUNT_INFO_ERROR',
            message: response.data.error || 'Invalid account data received',
          },
        };
      }

      this.dependencies.logger.info('Account info retrieved successfully', {
        requestId: request.request_id,
        balance: response.data.data.balance,
      });

      return {
        success: true,
        data: response.data.data as AccountInfo,
      };
    } catch (error) {
      this.dependencies.logger.error('Account info retrieval failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return {
        success: false,
        error: {
          code: 'ACCOUNT_INFO_ERROR',
          message: 'Failed to retrieve account information',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * Submit a trading order
   */
  async submitOrder(orderRequest: OrderRequest): Promise<ServiceResponse<OrderResult>> {
    try {
      // Validate order request
      const validationResult = this.validateOrderRequest(orderRequest);
      if (!validationResult.isValid) {
        return {
          success: false,
          error: {
            code: 'INVALID_ORDER',
            message: 'Order validation failed',
            details: validationResult.errors.join(', '),
          },
        };
      }

      const request: TradingEngineRequest = {
        action: 'submit_order',
        payload: orderRequest,
        timestamp: new Date(),
        request_id: uuidv4(),
      };

      const response = await this.dependencies.pythonEngineService.sendTradingCommand(request);

      if (!response.success || !response.data) {
        return {
          success: false,
          error: response.error || {
            code: 'ORDER_SUBMISSION_ERROR',
            message: 'Failed to submit order',
          },
        };
      }

      if (!response.data.success) {
        return {
          success: false,
          error: {
            code: 'ORDER_REJECTED',
            message: response.data.error || 'Order was rejected by trading engine',
          },
        };
      }

      this.dependencies.logger.info('Order submitted successfully', {
        requestId: request.request_id,
        symbol: orderRequest.symbol,
        orderType: orderRequest.order_type,
        volume: orderRequest.volume,
        orderId: response.data.data?.order_id,
      });

      this.emit('order_submitted', {
        orderRequest,
        orderResult: response.data.data,
        requestId: request.request_id,
      });

      return {
        success: true,
        data: response.data.data as OrderResult,
      };
    } catch (error) {
      this.dependencies.logger.error('Order submission failed', {
        symbol: orderRequest.symbol,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return {
        success: false,
        error: {
          code: 'ORDER_SUBMISSION_ERROR',
          message: 'Failed to submit order',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * Close an existing order/position
   */
  async closeOrder(orderId: number): Promise<ServiceResponse<OrderResult>> {
    try {
      const request: TradingEngineRequest = {
        action: 'close_order',
        payload: { order_id: orderId },
        timestamp: new Date(),
        request_id: uuidv4(),
      };

      const response = await this.dependencies.pythonEngineService.sendTradingCommand(request);

      if (!response.success || !response.data) {
        return {
          success: false,
          error: response.error || {
            code: 'ORDER_CLOSE_ERROR',
            message: 'Failed to close order',
          },
        };
      }

      if (!response.data.success) {
        return {
          success: false,
          error: {
            code: 'ORDER_CLOSE_REJECTED',
            message: response.data.error || 'Order close was rejected by trading engine',
          },
        };
      }

      this.dependencies.logger.info('Order closed successfully', {
        requestId: request.request_id,
        orderId,
      });

      this.emit('order_closed', {
        orderId,
        result: response.data.data,
        requestId: request.request_id,
      });

      return {
        success: true,
        data: response.data.data as OrderResult,
      };
    } catch (error) {
      this.dependencies.logger.error('Order close failed', {
        orderId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return {
        success: false,
        error: {
          code: 'ORDER_CLOSE_ERROR',
          message: 'Failed to close order',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * Get current open positions
   */
  async getPositions(): Promise<ServiceResponse<Position[]>> {
    try {
      const request: TradingEngineRequest = {
        action: 'get_positions',
        timestamp: new Date(),
        request_id: uuidv4(),
      };

      const response = await this.dependencies.pythonEngineService.sendTradingCommand(request);

      if (!response.success || !response.data) {
        return {
          success: false,
          error: response.error || {
            code: 'POSITIONS_ERROR',
            message: 'Failed to retrieve positions',
          },
        };
      }

      if (!response.data.success) {
        return {
          success: false,
          error: {
            code: 'POSITIONS_ERROR',
            message: response.data.error || 'Failed to get positions from trading engine',
          },
        };
      }

      this.dependencies.logger.debug('Positions retrieved successfully', {
        requestId: request.request_id,
        positionCount: Array.isArray(response.data.data) ? response.data.data.length : 0,
      });

      return {
        success: true,
        data: (response.data.data as Position[]) || [],
      };
    } catch (error) {
      this.dependencies.logger.error('Positions retrieval failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return {
        success: false,
        error: {
          code: 'POSITIONS_ERROR',
          message: 'Failed to retrieve positions',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * Check if trading engine is available
   */
  async isEngineHealthy(): Promise<boolean> {
    const healthStatus = this.dependencies.pythonEngineService.getHealthStatus();
    return healthStatus.healthy;
  }

  /**
   * Get detailed health information
   */
  async getEngineHealth(): Promise<ServiceResponse<{ healthy: boolean; lastCheck: Date | null }>> {
    try {
      const healthCheck = await this.dependencies.pythonEngineService.checkHealth();
      const status = this.dependencies.pythonEngineService.getHealthStatus();

      return {
        success: true,
        data: {
          healthy: healthCheck.success && status.healthy,
          lastCheck: status.lastCheck,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'HEALTH_CHECK_ERROR',
          message: 'Failed to check trading engine health',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * Validate order request before submission
   */
  private validateOrderRequest(order: OrderRequest): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Volume validation
    if (order.volume <= 0) {
      errors.push('Volume must be positive');
    }

    if (order.volume > 100) {
      errors.push('Volume exceeds maximum allowed (100)');
    }

    // Price validation
    if (order.price <= 0) {
      errors.push('Price must be positive');
    }

    // Stop loss validation
    if (order.stop_loss !== undefined) {
      if (order.stop_loss <= 0) {
        errors.push('Stop loss must be positive');
      }

      if (order.order_type === 'buy' && order.stop_loss >= order.price) {
        errors.push('Stop loss must be below entry price for buy orders');
      }

      if (order.order_type === 'sell' && order.stop_loss <= order.price) {
        errors.push('Stop loss must be above entry price for sell orders');
      }
    }

    // Take profit validation
    if (order.take_profit !== undefined) {
      if (order.take_profit <= 0) {
        errors.push('Take profit must be positive');
      }

      if (order.order_type === 'buy' && order.take_profit <= order.price) {
        errors.push('Take profit must be above entry price for buy orders');
      }

      if (order.order_type === 'sell' && order.take_profit >= order.price) {
        errors.push('Take profit must be below entry price for sell orders');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Calculate position size based on risk management
   */
  calculatePositionSize(
    accountBalance: number,
    riskPercent: number,
    entryPrice: number,
    stopLoss: number
  ): number {
    const riskAmount = accountBalance * (riskPercent / 100);
    const pipsRisk = Math.abs(entryPrice - stopLoss);
    
    if (pipsRisk <= 0) return 0;
    
    // Simplified calculation - in real implementation, consider pip value, currency, etc.
    const positionSize = riskAmount / pipsRisk;
    
    // Ensure minimum and maximum position sizes
    return Math.max(0.01, Math.min(positionSize, 100));
  }

  /**
   * Stop the service and cleanup
   */
  async stop(): Promise<void> {
    this.removeAllListeners();
    this.dependencies.logger.info('Trading bridge service stopped');
  }
}