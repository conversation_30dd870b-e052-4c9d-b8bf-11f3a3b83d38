
from chatbot.bot import TradingBot
from knowledge.graph import KnowledgeGraph
from ml.pipeline import <PERSON><PERSON><PERSON><PERSON><PERSON>

def test_chatbot_can_learn_and_answer():
    kg = KnowledgeGraph()
    kg.add_concept("Sharpe Ratio")
    kg.add_relation("Sharpe Ratio", "Risk", "quantifies")
    bot = TradingBot(kg)
    answer = bot.chat("What is Sharpe Ratio?")
    assert "Sharpe Ratio" in answer and "Risk" in answer

    # ML pipeline example
    pipe = MLPipeline()
    X, y = [{"feature": 1}, {"feature": 2}], [0.5, 1.5]
    pipe.fit(X, y)
    preds = pipe.predict(X)
    assert preds[0] == preds[1]  # mean prediction
