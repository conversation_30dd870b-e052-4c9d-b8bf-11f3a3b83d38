import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';

export interface User {
  id: string;
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  role: 'user' | 'admin' | 'premium';
  isActive: boolean;
  emailVerified: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt?: Date;
}

export interface UserRepository {
  findByEmail(email: string): Promise<User | null>;
  findById(id: string): Promise<User | null>;
  create(userData: Partial<User>): Promise<User>;
  update(id: string, updates: Partial<User>): Promise<User>;
  delete(id: string): Promise<boolean>;
}

export interface CacheService {
  get(key: string): Promise<string | null>;
  set(key: string, value: string, ttl?: number): Promise<void>;
  delete(key: string): Promise<boolean>;
  exists(key: string): Promise<boolean>;
}

export interface AuthConfig {
  jwtSecret: string;
  jwtExpiresIn: string;
  refreshTokenExpiresIn: string;
  bcryptRounds: number;
  maxFailedAttempts?: number;
  lockoutDuration?: number;
}

export interface TokenPayload {
  userId: string;
  email: string;
  role: string;
  type: 'access' | 'refresh' | 'password_reset' | 'email_verification';
  iat?: number;
  exp?: number;
}

export interface AuthResult {
  success: boolean;
  user?: Omit<User, 'password'>;
  accessToken?: string;
  refreshToken?: string;
  error?: string;
}

export interface TokenVerificationResult {
  valid: boolean;
  payload?: TokenPayload;
  error?: string;
}

export interface ApiKeyResult {
  success: boolean;
  apiKey?: string;
  error?: string;
}

export interface ApiKeyValidationResult {
  valid: boolean;
  userId?: string;
  permissions?: string[];
  error?: string;
}

export interface SessionInfo {
  id: string;
  deviceInfo: string;
  lastActivity: Date;
  ipAddress?: string;
}

export class AuthenticationService {
  private readonly maxFailedAttempts: number;
  private readonly lockoutDuration: number;

  constructor(
    private userRepository: UserRepository,
    private cacheService: CacheService,
    private config: AuthConfig
  ) {
    this.maxFailedAttempts = config.maxFailedAttempts || 5;
    this.lockoutDuration = config.lockoutDuration || 900; // 15 minutes
  }

  async register(userData: {
    email: string;
    password: string;
    firstName?: string;
    lastName?: string;
  }): Promise<AuthResult> {
    try {
      // Validate input
      const validation = this.validateRegistrationData(userData);
      if (!validation.valid) {
        return { success: false, error: validation.error };
      }

      // Check if user already exists
      const existingUser = await this.userRepository.findByEmail(userData.email);
      if (existingUser) {
        return { success: false, error: 'Email already exists' };
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(userData.password, this.config.bcryptRounds);

      // Create user
      const newUser = await this.userRepository.create({
        email: userData.email,
        password: hashedPassword,
        firstName: userData.firstName,
        lastName: userData.lastName,
        role: 'user',
        isActive: true,
        emailVerified: false,
        createdAt: new Date(),
      });

      // Remove password from response
      const { password, ...userWithoutPassword } = newUser;

      return {
        success: true,
        user: userWithoutPassword,
      };
    } catch (error) {
      console.error('Registration error:', error);
      return { success: false, error: 'Registration failed' };
    }
  }

  async login(credentials: {
    email: string;
    password: string;
    deviceInfo?: string;
    ipAddress?: string;
  }): Promise<AuthResult> {
    try {
      // Check if account is locked
      if (await this.isAccountLocked(credentials.email, credentials.ipAddress || '')) {
        return { success: false, error: 'Account temporarily locked due to failed login attempts' };
      }

      // Find user
      const user = await this.userRepository.findByEmail(credentials.email);
      if (!user) {
        await this.trackFailedLogin(credentials.email, credentials.ipAddress || '');
        return { success: false, error: 'Invalid credentials' };
      }

      // Check if user is active
      if (!user.isActive) {
        return { success: false, error: 'Account is deactivated' };
      }

      // Check if email is verified
      if (!user.emailVerified) {
        return { success: false, error: 'Email not verified' };
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(credentials.password, user.password);
      if (!isPasswordValid) {
        await this.trackFailedLogin(credentials.email, credentials.ipAddress || '');
        return { success: false, error: 'Invalid credentials' };
      }

      // Clear failed login attempts
      await this.clearFailedLoginAttempts(credentials.email, credentials.ipAddress || '');

      // Generate tokens
      const accessToken = this.generateAccessToken(user);
      const refreshToken = this.generateRefreshToken(user);

      // Store refresh token
      await this.storeRefreshToken(user.id, refreshToken);

      // Update last login
      await this.userRepository.update(user.id, {
        lastLoginAt: new Date(),
      });

      // Track session
      if (credentials.deviceInfo) {
        await this.trackSession(user.id, {
          deviceInfo: credentials.deviceInfo,
          ipAddress: credentials.ipAddress,
        });
      }

      // Remove password from response
      const { password, ...userWithoutPassword } = user;

      return {
        success: true,
        user: userWithoutPassword,
        accessToken,
        refreshToken,
      };
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: 'Login failed' };
    }
  }

  async refreshToken(refreshToken: string): Promise<AuthResult> {
    try {
      // Verify refresh token
      const payload = jwt.verify(refreshToken, this.config.jwtSecret) as TokenPayload;

      if (payload.type !== 'refresh') {
        return { success: false, error: 'Invalid token type' };
      }

      // Check if token is still valid (not revoked)
      const isTokenValid = await this.cacheService.exists(`refresh_token:${payload.userId}`);
      if (!isTokenValid) {
        return { success: false, error: 'Token has been revoked' };
      }

      // Get user
      const user = await this.userRepository.findById(payload.userId);
      if (!user || !user.isActive) {
        return { success: false, error: 'User not found or inactive' };
      }

      // Generate new access token
      const newAccessToken = this.generateAccessToken(user);

      // Remove password from response
      const { password, ...userWithoutPassword } = user;

      return {
        success: true,
        user: userWithoutPassword,
        accessToken: newAccessToken,
      };
    } catch (error) {
      console.error('Token refresh error:', error);
      return { success: false, error: 'Invalid refresh token' };
    }
  }

  async logout(userId: string, accessToken?: string, refreshToken?: string): Promise<boolean> {
    try {
      // Blacklist access token if provided
      if (accessToken) {
        const payload = jwt.decode(accessToken) as TokenPayload;
        if (payload && payload.exp) {
          const ttl = payload.exp - Math.floor(Date.now() / 1000);
          if (ttl > 0) {
            await this.cacheService.set(`blacklist:${accessToken}`, 'true', ttl);
          }
        }
      }

      // Blacklist refresh token if provided
      if (refreshToken) {
        const payload = jwt.decode(refreshToken) as TokenPayload;
        if (payload && payload.exp) {
          const ttl = payload.exp - Math.floor(Date.now() / 1000);
          if (ttl > 0) {
            await this.cacheService.set(`blacklist:${refreshToken}`, 'true', ttl);
          }
        }
      }

      // Remove stored refresh token
      await this.cacheService.delete(`refresh_token:${userId}`);

      return true;
    } catch (error) {
      console.error('Logout error:', error);
      return false;
    }
  }

  async verifyToken(token: string): Promise<TokenVerificationResult> {
    try {
      // Check if token is blacklisted
      const isBlacklisted = await this.cacheService.exists(`blacklist:${token}`);
      if (isBlacklisted) {
        return { valid: false, error: 'Token has been revoked' };
      }

      // Verify token
      const payload = jwt.verify(token, this.config.jwtSecret) as TokenPayload;

      return { valid: true, payload };
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        return { valid: false, error: 'Token expired' };
      }
      if (error instanceof jwt.JsonWebTokenError) {
        return { valid: false, error: 'Invalid token' };
      }
      console.error('Token verification error:', error);
      return { valid: false, error: 'Token verification failed' };
    }
  }

  async changePassword(
    userId: string,
    currentPassword: string,
    newPassword: string
  ): Promise<AuthResult> {
    try {
      // Validate new password
      const validation = this.validatePassword(newPassword);
      if (!validation.valid) {
        return { success: false, error: validation.error };
      }

      // Get user
      const user = await this.userRepository.findById(userId);
      if (!user) {
        return { success: false, error: 'User not found' };
      }

      // Verify current password
      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
      if (!isCurrentPasswordValid) {
        return { success: false, error: 'Current password is incorrect' };
      }

      // Hash new password
      const hashedNewPassword = await bcrypt.hash(newPassword, this.config.bcryptRounds);

      // Update password
      await this.userRepository.update(userId, {
        password: hashedNewPassword,
      });

      return { success: true };
    } catch (error) {
      console.error('Password change error:', error);
      return { success: false, error: 'Password change failed' };
    }
  }

  async initiatePasswordReset(email: string): Promise<{ success: boolean; resetToken?: string; error?: string }> {
    try {
      // Find user
      const user = await this.userRepository.findByEmail(email);
      if (!user) {
        // Don't reveal if email exists
        return { success: true };
      }

      // Generate reset token
      const resetToken = jwt.sign(
        { userId: user.id, type: 'password_reset' },
        this.config.jwtSecret,
        { expiresIn: '1h' }
      );

      // Store reset token
      await this.cacheService.set(`password_reset:${user.id}`, resetToken, 3600); // 1 hour

      return { success: true, resetToken };
    } catch (error) {
      console.error('Password reset initiation error:', error);
      return { success: false, error: 'Password reset failed' };
    }
  }

  async completePasswordReset(resetToken: string, newPassword: string): Promise<AuthResult> {
    try {
      // Validate new password
      const validation = this.validatePassword(newPassword);
      if (!validation.valid) {
        return { success: false, error: validation.error };
      }

      // Verify reset token
      const payload = jwt.verify(resetToken, this.config.jwtSecret) as TokenPayload;

      if (payload.type !== 'password_reset') {
        return { success: false, error: 'Invalid token type' };
      }

      // Check if token is still valid
      const storedToken = await this.cacheService.get(`password_reset:${payload.userId}`);
      if (storedToken !== resetToken) {
        return { success: false, error: 'Invalid or expired reset token' };
      }

      // Hash new password
      const hashedPassword = await bcrypt.hash(newPassword, this.config.bcryptRounds);

      // Update password
      await this.userRepository.update(payload.userId, {
        password: hashedPassword,
      });

      // Remove reset token
      await this.cacheService.delete(`password_reset:${payload.userId}`);

      return { success: true };
    } catch (error) {
      console.error('Password reset completion error:', error);
      return { success: false, error: 'Password reset failed' };
    }
  }

  async getUserProfile(userId: string): Promise<Omit<User, 'password'> | null> {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) {
        return null;
      }

      const { password, ...userWithoutPassword } = user;
      return userWithoutPassword;
    } catch (error) {
      console.error('Get user profile error:', error);
      return null;
    }
  }

  async updateUserProfile(userId: string, updates: Partial<User>): Promise<AuthResult> {
    try {
      // Remove sensitive fields from updates
      const { password, role, isActive, emailVerified, ...safeUpdates } = updates;

      const updatedUser = await this.userRepository.update(userId, safeUpdates);
      const { password: _, ...userWithoutPassword } = updatedUser;

      return { success: true, user: userWithoutPassword };
    } catch (error) {
      console.error('Update user profile error:', error);
      return { success: false, error: 'Profile update failed' };
    }
  }

  async deactivateUser(userId: string): Promise<boolean> {
    try {
      await this.userRepository.update(userId, { isActive: false });
      // Revoke all sessions
      await this.revokeAllSessions(userId);
      return true;
    } catch (error) {
      console.error('Deactivate user error:', error);
      return false;
    }
  }

  // Session Management
  async getActiveSessions(userId: string): Promise<SessionInfo[]> {
    try {
      const sessionsData = await this.cacheService.get(`user_sessions:${userId}`);
      return sessionsData ? JSON.parse(sessionsData) : [];
    } catch (error) {
      console.error('Get active sessions error:', error);
      return [];
    }
  }

  async revokeSession(userId: string, sessionId: string): Promise<boolean> {
    try {
      await this.cacheService.delete(`session:${sessionId}`);
      return true;
    } catch (error) {
      console.error('Revoke session error:', error);
      return false;
    }
  }

  async revokeAllSessions(userId: string): Promise<boolean> {
    try {
      await this.cacheService.delete(`user_sessions:${userId}`);
      await this.cacheService.delete(`refresh_token:${userId}`);
      return true;
    } catch (error) {
      console.error('Revoke all sessions error:', error);
      return false;
    }
  }

  // Security Features
  async trackFailedLogin(email: string, ipAddress: string): Promise<void> {
    try {
      const key = `failed_login:${email}:${ipAddress}`;
      const current = await this.cacheService.get(key);
      const count = current ? parseInt(current) + 1 : 1;
      await this.cacheService.set(key, count.toString(), this.lockoutDuration);
    } catch (error) {
      console.error('Track failed login error:', error);
    }
  }

  async isAccountLocked(email: string, ipAddress: string): Promise<boolean> {
    try {
      const key = `failed_login:${email}:${ipAddress}`;
      const attempts = await this.cacheService.get(key);
      return attempts ? parseInt(attempts) >= this.maxFailedAttempts : false;
    } catch (error) {
      console.error('Check account lock error:', error);
      return false;
    }
  }

  async clearFailedLoginAttempts(email: string, ipAddress: string): Promise<void> {
    try {
      const key = `failed_login:${email}:${ipAddress}`;
      await this.cacheService.delete(key);
    } catch (error) {
      console.error('Clear failed login attempts error:', error);
    }
  }

  // API Key Management
  async generateApiKey(userId: string, name: string, permissions: string[] = ['read']): Promise<ApiKeyResult> {
    try {
      const apiKey = `ak_${crypto.randomBytes(32).toString('hex')}`;
      
      const keyData = {
        userId,
        name,
        permissions,
        isActive: true,
        createdAt: new Date().toISOString(),
      };

      await this.cacheService.set(`api_key:${apiKey}`, JSON.stringify(keyData));

      return { success: true, apiKey };
    } catch (error) {
      console.error('Generate API key error:', error);
      return { success: false, error: 'API key generation failed' };
    }
  }

  async validateApiKey(apiKey: string): Promise<ApiKeyValidationResult> {
    try {
      const keyData = await this.cacheService.get(`api_key:${apiKey}`);
      if (!keyData) {
        return { valid: false, error: 'Invalid API key' };
      }

      const parsedData = JSON.parse(keyData);
      if (!parsedData.isActive) {
        return { valid: false, error: 'API key is inactive' };
      }

      return {
        valid: true,
        userId: parsedData.userId,
        permissions: parsedData.permissions,
      };
    } catch (error) {
      console.error('Validate API key error:', error);
      return { valid: false, error: 'API key validation failed' };
    }
  }

  // Private helper methods
  private generateAccessToken(user: User): string {
    return jwt.sign(
      {
        userId: user.id,
        email: user.email,
        role: user.role,
        type: 'access',
      },
      this.config.jwtSecret,
      { expiresIn: this.config.jwtExpiresIn }
    );
  }

  private generateRefreshToken(user: User): string {
    return jwt.sign(
      {
        userId: user.id,
        email: user.email,
        role: user.role,
        type: 'refresh',
      },
      this.config.jwtSecret,
      { expiresIn: this.config.refreshTokenExpiresIn }
    );
  }

  private async storeRefreshToken(userId: string, refreshToken: string): Promise<void> {
    const payload = jwt.decode(refreshToken) as TokenPayload;
    const ttl = payload.exp ? payload.exp - Math.floor(Date.now() / 1000) : 604800; // 7 days default
    await this.cacheService.set(`refresh_token:${userId}`, refreshToken, ttl);
  }

  private async trackSession(userId: string, sessionInfo: { deviceInfo: string; ipAddress?: string }): Promise<void> {
    try {
      const sessionId = crypto.randomBytes(16).toString('hex');
      const session: SessionInfo = {
        id: sessionId,
        deviceInfo: sessionInfo.deviceInfo,
        lastActivity: new Date(),
        ipAddress: sessionInfo.ipAddress,
      };

      const existingSessions = await this.getActiveSessions(userId);
      const updatedSessions = [...existingSessions, session];

      await this.cacheService.set(`user_sessions:${userId}`, JSON.stringify(updatedSessions), 86400); // 24 hours
    } catch (error) {
      console.error('Track session error:', error);
    }
  }

  private validateRegistrationData(userData: { email: string; password: string }): { valid: boolean; error?: string } {
    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(userData.email)) {
      return { valid: false, error: 'Invalid email format' };
    }

    // Password validation
    const passwordValidation = this.validatePassword(userData.password);
    if (!passwordValidation.valid) {
      return passwordValidation;
    }

    return { valid: true };
  }

  private validatePassword(password: string): { valid: boolean; error?: string } {
    if (password.length < 8) {
      return { valid: false, error: 'Password must be at least 8 characters long' };
    }

    if (!/(?=.*[a-z])/.test(password)) {
      return { valid: false, error: 'Password must contain at least one lowercase letter' };
    }

    if (!/(?=.*[A-Z])/.test(password)) {
      return { valid: false, error: 'Password must contain at least one uppercase letter' };
    }

    if (!/(?=.*\d)/.test(password)) {
      return { valid: false, error: 'Password must contain at least one number' };
    }

    if (!/(?=.*[!@#$%^&*])/.test(password)) {
      return { valid: false, error: 'Password must contain at least one special character (!@#$%^&*)' };
    }

    return { valid: true };
  }
}