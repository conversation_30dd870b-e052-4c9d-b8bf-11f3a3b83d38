import React, { useState } from 'react';
import { 
  Bot, 
  Code, 
  TrendingUp, 
  Settings, 
  Database, 
  Activity,
  DollarSign,
  Target,
  Zap,
  Shield,
  Brain,
  Link,
  Play,
  Pause,
  Square,
  BarChart3,
  FileText,
  Users,
  MessageCircle,
  Send,
  User,
  CheckCircle,
  AlertCircle,
  TestTube,
  Save
} from 'lucide-react';
import { motion } from 'framer-motion';
import SimpleStrategyHelper from './SimpleStrategyHelper';

interface Strategy {
  id: string;
  name: string;
  status: 'active' | 'paused' | 'stopped';
  pnl: number;
  trades: number;
  winRate: number;
  type: string;
}

interface MetricCardProps {
  title: string;
  value: string;
  change: string;
  icon: React.ReactNode;
  color: string;
}

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  code?: string;
  timestamp: Date;
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, change, icon, color }) => (
  <motion.div
    whileHover={{ scale: 1.02 }}
    className="bg-white p-6 rounded-xl shadow-sm border border-gray-200"
  >
    <div className="flex items-center justify-between">
      <div>
        <p className="text-sm font-medium text-gray-600">{title}</p>
        <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
        <p className="text-sm text-gray-500 mt-1">{change}</p>
      </div>
      <div className={`p-3 rounded-lg ${color}`}>
        {icon}
      </div>
    </div>
  </motion.div>
);

const PlatformDashboard: React.FC = () => {
  const [activeHelperTab, setActiveHelperTab] = useState('ai-prompts');
  const [chatMessages, setChatMessages] = useState<Message[]>([
    {
      id: '1',
      role: 'assistant',
      content: "Hello! I'm your AI trading strategy assistant. Describe the strategy you'd like to create and I'll generate complete Python code for you.\n\nFor example: 'Create a mean reversion strategy for EUR/USD using RSI with 2% risk per trade'",
      timestamp: new Date()
    }
  ]);
  const [chatInput, setChatInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedPrompt, setSelectedPrompt] = useState<any>(null);
  const [promptVariables, setPromptVariables] = useState<Record<string, string>>({});
  const [generatedPrompt, setGeneratedPrompt] = useState<string>('');

  // AI Prompts from promptadvance.club
  const aiPrompts = [
    {
      id: "market_scanner",
      title: "Market Analysis & Asset Scanner",
      description: "Identify trading assets that meet specific criteria",
      category: "Market Analysis",
      prompt_template: "Act as a day trading assistant. Your task is to identify trading assets that meet the specified {criteria}. Utilize your expertise and available market analysis tools to scan, filter, and evaluate potential assets for trading opportunities.\n\nPlease provide:\n1. A list of 5-10 assets that match the criteria\n2. Brief analysis of why each asset meets the requirements\n3. Current market conditions affecting these assets\n4. Risk factors to consider\n5. Recommended timeframes for analysis\n\nCriteria: {criteria}",
      variables: ["criteria"],
      example_usage: "criteria: 'tech stocks with strong momentum and AI exposure'",
      detailed_info: "This prompt helps you systematically scan markets for opportunities. It provides structured analysis including risk assessment and timing recommendations."
    },
    {
      id: "technical_analyzer",
      title: "Comprehensive Technical Analysis",
      description: "Detailed technical analysis with entry/exit points",
      category: "Technical Analysis",
      prompt_template: "Act as an experienced day trader. Your objective is to analyze the price and volume patterns of {trading_asset} to identify potential buying or selling opportunities.\n\nProvide a comprehensive analysis including:\n1. Current trend analysis (short, medium, long-term)\n2. Key support and resistance levels\n3. Technical indicators analysis (RSI, MACD, Moving Averages)\n4. Volume analysis and patterns\n5. Entry points with specific price levels\n6. Stop-loss recommendations\n7. Take-profit targets\n8. Risk-reward ratio assessment\n9. Market sentiment indicators\n10. Timeframe recommendations\n\nAsset: {trading_asset}",
      variables: ["trading_asset"],
      example_usage: "trading_asset: 'NVDA (NVIDIA Corporation)'",
      detailed_info: "Provides comprehensive technical analysis with specific entry/exit points, risk management, and multi-timeframe perspective."
    },
    {
      id: "trade_execution",
      title: "Optimal Trade Execution Strategy",
      description: "Determine optimal entry, stop-loss, and target points",
      category: "Trade Execution",
      prompt_template: "Act as an experienced day trader. Based on your comprehensive analysis of current market conditions for {asset}, provide an optimal trade execution strategy.\n\nInclude:\n1. Precise entry price and entry conditions\n2. Stop-loss placement with reasoning\n3. Multiple take-profit targets\n4. Position sizing recommendations\n5. Risk management rules\n6. Market timing considerations\n7. Alternative scenarios (if trade goes against you)\n8. Exit strategy for different market conditions\n\nAsset: {asset}\nAccount Size: {account_size}\nRisk Tolerance: {risk_tolerance}%",
      variables: ["asset", "account_size", "risk_tolerance"],
      example_usage: "asset: 'TSLA', account_size: '$50000', risk_tolerance: '2'",
      detailed_info: "Creates detailed execution plans with precise entry/exit criteria, position sizing, and contingency planning."
    },
    {
      id: "trade_journal",
      title: "Comprehensive Trade Journal System",
      description: "Create systematic trade documentation system",
      category: "Trade Management",
      prompt_template: "Act as an experienced day trader and create a comprehensive trade journal entry template for {trade_type} trades.\n\nCreate a structured format including:\n1. Pre-trade analysis section\n2. Trade setup identification\n3. Entry criteria and reasoning\n4. Risk management parameters\n5. Emotional state assessment\n6. Market conditions documentation\n7. Post-trade analysis framework\n8. Lessons learned section\n9. Performance metrics tracking\n10. Improvement action items\n\nTrade Type: {trade_type}\nTimeframe: {timeframe}",
      variables: ["trade_type", "timeframe"],
      example_usage: "trade_type: 'swing trading', timeframe: 'daily'",
      detailed_info: "Develops systematic documentation habits for continuous improvement and performance tracking."
    },
    {
      id: "performance_review",
      title: "Trading Performance Analysis",
      description: "Analyze and review trading performance",
      category: "Performance Review",
      prompt_template: "Act as a professional trading performance analyst. Review my trading performance data and provide comprehensive analysis.\n\nAnalyze:\n1. Overall performance metrics\n2. Win/loss ratio analysis\n3. Risk-adjusted returns\n4. Drawdown analysis\n5. Trading frequency and timing\n6. Asset class performance breakdown\n7. Strategy effectiveness\n8. Behavioral patterns identification\n9. Areas for improvement\n10. Specific recommendations for optimization\n\nPerformance Period: {period}\nTotal Trades: {total_trades}\nWin Rate: {win_rate}%\nTotal P&L: {total_pnl}",
      variables: ["period", "total_trades", "win_rate", "total_pnl"],
      example_usage: "period: 'last 3 months', total_trades: '45', win_rate: '62', total_pnl: '$3,250'",
      detailed_info: "Provides detailed performance analysis with actionable insights for improving trading results."
    },
    {
      id: "market_research",
      title: "Market Research & Analysis",
      description: "Conduct comprehensive market research",
      category: "Research",
      prompt_template: "Act as a financial research analyst specializing in {market_sector}. Conduct comprehensive market research and analysis.\n\nProvide:\n1. Sector overview and current trends\n2. Key market drivers and catalysts\n3. Economic factors impact analysis\n4. Top performing assets in the sector\n5. Emerging opportunities\n6. Risk factors and challenges\n7. Seasonal patterns and cycles\n8. Correlation analysis with other markets\n9. Future outlook and projections\n10. Trading strategy recommendations\n\nSector: {market_sector}\nTimeframe: {analysis_timeframe}",
      variables: ["market_sector", "analysis_timeframe"],
      example_usage: "market_sector: 'Technology', analysis_timeframe: '6 months'",
      detailed_info: "Delivers comprehensive sector analysis with trading opportunities and risk assessment."
    },
    {
      id: "trading_psychology",
      title: "Trading Psychology Coach",
      description: "Improve trading psychology and discipline",
      category: "Psychology",
      prompt_template: "Act as a trading psychology coach with expertise in behavioral finance. Help me address {psychological_challenge} in my trading.\n\nProvide:\n1. Analysis of the psychological challenge\n2. Common causes and triggers\n3. Impact on trading performance\n4. Practical coping strategies\n5. Mental exercises and techniques\n6. Behavioral modification approaches\n7. Risk management psychology\n8. Confidence building methods\n9. Stress management techniques\n10. Long-term psychological development plan\n\nChallenge: {psychological_challenge}\nTrading Experience: {experience_level}",
      variables: ["psychological_challenge", "experience_level"],
      example_usage: "psychological_challenge: 'FOMO and overtrading', experience_level: 'intermediate'",
      detailed_info: "Addresses psychological barriers with practical techniques for mental discipline and emotional control."
    },
    {
      id: "learning_plan",
      title: "Trading Education & Skill Development",
      description: "Structured learning and skill development",
      category: "Education",
      prompt_template: "Act as an expert trading educator. Create a comprehensive learning plan for {learning_goal}.\n\nDevelop:\n1. Structured curriculum outline\n2. Learning objectives and milestones\n3. Recommended resources and materials\n4. Practical exercises and assignments\n5. Skill assessment methods\n6. Timeline and progression schedule\n7. Prerequisites and foundations\n8. Advanced topics roadmap\n9. Real-world application projects\n10. Continuous improvement framework\n\nLearning Goal: {learning_goal}\nCurrent Level: {current_level}\nTime Commitment: {time_commitment}",
      variables: ["learning_goal", "current_level", "time_commitment"],
      example_usage: "learning_goal: 'options trading strategies', current_level: 'beginner', time_commitment: '10 hours/week'",
      detailed_info: "Creates personalized learning paths with structured progression and practical application."
    },
    {
      id: "strategy_backtest",
      title: "Strategy Backtesting & Validation",
      description: "Backtest and validate trading strategies",
      category: "Strategy Development",
      prompt_template: "Act as a quantitative analyst specializing in strategy backtesting. Help me design a comprehensive backtesting framework for {strategy_type}.\n\nInclude:\n1. Backtesting methodology and approach\n2. Data requirements and sources\n3. Performance metrics to track\n4. Risk metrics and drawdown analysis\n5. Statistical significance testing\n6. Out-of-sample validation methods\n7. Parameter optimization guidelines\n8. Overfitting prevention techniques\n9. Walk-forward analysis framework\n10. Strategy robustness assessment\n\nStrategy: {strategy_type}\nAsset Class: {asset_class}\nTimeframe: {timeframe}",
      variables: ["strategy_type", "asset_class", "timeframe"],
      example_usage: "strategy_type: 'RSI mean reversion', asset_class: 'forex', timeframe: '1 hour'",
      detailed_info: "Provides comprehensive backtesting framework with statistical validation and robustness testing."
    }
  ];

  const strategies: Strategy[] = [
    { id: '1', name: 'RSI Mean Reversion', status: 'active', pnl: 1247, trades: 23, winRate: 65, type: 'Mean Reversion' },
    { id: '2', name: 'MACD Momentum', status: 'active', pnl: 892, trades: 18, winRate: 72, type: 'Momentum' },
    { id: '3', name: 'Bollinger Bands', status: 'paused', pnl: 456, trades: 12, winRate: 58, type: 'Volatility' },
    { id: '4', name: 'Bollinger Breakout', status: 'stopped', pnl: -23, trades: 5, winRate: 40, type: 'Breakout' },
    { id: '5', name: 'Multi-Timeframe', status: 'active', pnl: 178, trades: 10, winRate: 70, type: 'Advanced' }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-100';
      case 'paused': return 'text-yellow-600 bg-yellow-100';
      case 'stopped': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <Play className="w-3 h-3" />;
      case 'paused': return <Pause className="w-3 h-3" />;
      case 'stopped': return <Square className="w-3 h-3" />;
      default: return <Square className="w-3 h-3" />;
    }
  };

  const generateStrategyResponse = (prompt: string): { message: string; code?: string } => {
    const promptLower = prompt.toLowerCase();
    
    if (promptLower.includes('mean reversion') || promptLower.includes('rsi')) {
      return {
        message: "✅ **Mean Reversion RSI Strategy Generated!**\n\n**Strategy Details:**\n- Type: Mean Reversion\n- Indicator: RSI (14 period)\n- Buy: RSI < 30 (oversold)\n- Sell: RSI > 70 (overbought)\n- Risk: 2% per trade\n- Max Positions: 3\n\n**Features:**\n- Complete Python class with MT5 integration\n- Comprehensive test cases\n- Risk management included\n- Ready for deployment",
        code: `class MeanReversionRSIStrategy(StrategyBase):
    """Mean Reversion Strategy using RSI indicator"""
    
    def __init__(self, symbol="EURUSD", timeframe="H1", risk_percent=2.0):
        super().__init__(symbol, timeframe)
        self.risk_percent = risk_percent
        self.rsi_period = 14
        self.oversold_level = 30
        self.overbought_level = 70
        self.max_positions = 3
        
    def calculate_indicators(self, data):
        """Calculate RSI indicator"""
        delta = data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=self.rsi_period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=self.rsi_period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return {'rsi': rsi}
    
    def generate_signals(self, data, indicators):
        """Generate buy/sell signals based on RSI"""
        signals = []
        rsi = indicators['rsi']
        
        for i in range(len(data)):
            if rsi.iloc[i] < self.oversold_level:
                signals.append('BUY')
            elif rsi.iloc[i] > self.overbought_level:
                signals.append('SELL')
            else:
                signals.append('HOLD')
                
        return signals
    
    def calculate_position_size(self, account_balance, stop_loss_pips):
        """Calculate position size based on risk management"""
        risk_amount = account_balance * (self.risk_percent / 100)
        pip_value = 10  # For EURUSD
        position_size = risk_amount / (stop_loss_pips * pip_value)
        return round(position_size, 2)
    
    def backtest(self, historical_data):
        """Run comprehensive backtest"""
        results = {
            'total_trades': 0,
            'winning_trades': 0,
            'total_pnl': 0,
            'max_drawdown': 0,
            'sharpe_ratio': 0
        }
        # Backtest implementation here
        return results`
      };
    } else if (promptLower.includes('momentum') || promptLower.includes('macd')) {
      return {
        message: "✅ **MACD Momentum Strategy Generated!**\n\n**Strategy Details:**\n- Type: Momentum Following\n- Indicators: MACD (12,26,9)\n- Buy: MACD line crosses above signal\n- Sell: MACD line crosses below signal\n- Risk: 2% per trade\n\n**Features:**\n- Trend following logic\n- Multiple timeframe analysis\n- Advanced risk management",
        code: `class MACDMomentumStrategy(StrategyBase):
    """MACD Momentum Strategy"""
    
    def __init__(self, symbol="EURUSD", timeframe="H4"):
        super().__init__(symbol, timeframe)
        self.fast_period = 12
        self.slow_period = 26
        self.signal_period = 9
        
    def calculate_indicators(self, data):
        """Calculate MACD indicators"""
        exp1 = data['close'].ewm(span=self.fast_period).mean()
        exp2 = data['close'].ewm(span=self.slow_period).mean()
        macd_line = exp1 - exp2
        signal_line = macd_line.ewm(span=self.signal_period).mean()
        histogram = macd_line - signal_line
        
        return {
            'macd_line': macd_line,
            'signal_line': signal_line,
            'histogram': histogram
        }
    
    def generate_signals(self, data, indicators):
        """Generate momentum-based signals"""
        signals = []
        macd = indicators['macd_line']
        signal = indicators['signal_line']
        
        for i in range(1, len(data)):
            if macd.iloc[i] > signal.iloc[i] and macd.iloc[i-1] <= signal.iloc[i-1]:
                signals.append('BUY')
            elif macd.iloc[i] < signal.iloc[i] and macd.iloc[i-1] >= signal.iloc[i-1]:
                signals.append('SELL')
            else:
                signals.append('HOLD')
                
        return ['HOLD'] + signals  # Add first signal
        
    def validate_trend(self, data):
        """Additional trend validation"""
        sma_50 = data['close'].rolling(50).mean()
        sma_200 = data['close'].rolling(200).mean()
        return sma_50.iloc[-1] > sma_200.iloc[-1]  # Bullish trend`
      };
    } else {
      return {
        message: "I can help you create various trading strategies! Here are some popular options:\n\n🔹 **Mean Reversion**: RSI, Bollinger Bands\n🔹 **Momentum**: MACD, Moving Average Crossover\n🔹 **Breakout**: Channel breakouts, Support/Resistance\n🔹 **Scalping**: Quick profit strategies\n🔹 **Grid Trading**: Multiple position strategies\n\nPlease describe what type of strategy you'd like to create, including:\n- Strategy type (mean reversion, momentum, etc.)\n- Indicators you prefer\n- Risk tolerance\n- Trading timeframe"
      };
    }
  };

  const handleChatSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!chatInput.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: chatInput,
      timestamp: new Date()
    };

    setChatMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    // Simulate AI response
    setTimeout(() => {
      const response = generateStrategyResponse(chatInput);
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: response.message,
        code: response.code,
        timestamp: new Date()
      };

      setChatMessages(prev => [...prev, assistantMessage]);
      setIsLoading(false);
    }, 1500);

    setChatInput('');
  };

  const generateAIPrompt = () => {
    if (!selectedPrompt) return;

    let prompt = selectedPrompt.prompt_template;
    
    // Replace variables in the prompt
    selectedPrompt.variables.forEach((variable: string) => {
      const value = promptVariables[variable] || `[${variable}]`;
      prompt = prompt.replace(new RegExp(`{${variable}}`, 'g'), value);
    });

    setGeneratedPrompt(prompt);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      'Market Analysis': 'bg-blue-100 text-blue-800',
      'Technical Analysis': 'bg-green-100 text-green-800',
      'Trade Execution': 'bg-purple-100 text-purple-800',
      'Trade Management': 'bg-orange-100 text-orange-800',
      'Performance Review': 'bg-red-100 text-red-800',
      'Research': 'bg-indigo-100 text-indigo-800',
      'Psychology': 'bg-pink-100 text-pink-800',
      'Education': 'bg-yellow-100 text-yellow-800',
      'Strategy Development': 'bg-teal-100 text-teal-800'
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation Header */}
      <nav className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Bot className="w-6 h-6 text-blue-600" />
              </div>
              <span className="text-xl font-bold text-gray-900">AI Trading Platform</span>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>MT5 Connected</span>
              </div>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 space-y-8">
        {/* Hero Section */}
        <section className="bg-gradient-to-r from-blue-600 to-indigo-700 rounded-xl p-8 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2">🚀 AI-Enhanced Trading Platform</h1>
              <p className="text-blue-100 text-lg">Transform natural language into production-ready Python trading strategies</p>
              <div className="flex items-center space-x-6 mt-4">
                <div className="flex items-center space-x-2">
                  <Bot className="w-5 h-5" />
                  <span>AI Chatbot</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Code className="w-5 h-5" />
                  <span>Python IDE</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Link className="w-5 h-5" />
                  <span>MT5 Integration</span>
                </div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold">12</div>
              <div className="text-blue-100">Active Strategies</div>
            </div>
          </div>
        </section>

        {/* Key Metrics */}
        <section>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">📊 Key Metrics</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <MetricCard
              title="Total P&L"
              value="$2,847"
              change="+$127 today"
              icon={<DollarSign className="w-6 h-6 text-white" />}
              color="bg-green-500"
            />
            <MetricCard
              title="Win Rate"
              value="68%"
              change="+2% this week"
              icon={<Target className="w-6 h-6 text-white" />}
              color="bg-blue-500"
            />
            <MetricCard
              title="Active Strategies"
              value="12"
              change="+3 this month"
              icon={<Activity className="w-6 h-6 text-white" />}
              color="bg-purple-500"
            />
            <MetricCard
              title="MT5 Status"
              value="Connected"
              change="Stable connection"
              icon={<Zap className="w-6 h-6 text-white" />}
              color="bg-green-500"
            />
          </div>
        </section>

        {/* Enhanced Strategy Helper */}
        <section>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">🧠 AI Strategy Helper</h2>
          <div className="bg-white rounded-xl shadow-sm border border-gray-200">
            <div className="p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <MessageCircle className="w-5 h-5 text-blue-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900">Strategy Generator</h3>
                <div className="flex-1"></div>
                <div className="text-sm text-gray-500">Powered by AI</div>
              </div>
              
              {/* Chat Messages */}
              <div className="h-96 overflow-y-auto mb-4 p-4 bg-gray-50 rounded-lg">
                <div className="space-y-4">
                  {chatMessages.map((message) => (
                    <div key={message.id} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                      <div className={`max-w-3xl ${message.role === 'user' ? 'bg-blue-600 text-white' : 'bg-white border border-gray-200'} rounded-lg p-4`}>
                        <div className="flex items-start space-x-3">
                          <div className={`p-1 rounded-full ${message.role === 'user' ? 'bg-blue-500' : 'bg-gray-100'}`}>
                            {message.role === 'user' ? 
                              <User className="w-4 h-4 text-white" /> : 
                              <Bot className="w-4 h-4 text-gray-600" />
                            }
                          </div>
                          <div className="flex-1">
                            <div className="whitespace-pre-wrap text-sm">{message.content}</div>
                            {message.code && (
                              <div className="mt-3">
                                <div className="bg-gray-900 text-green-400 p-4 rounded-lg text-sm font-mono overflow-x-auto">
                                  <pre>{message.code}</pre>
                                </div>
                                <div className="flex space-x-2 mt-3">
                                  <button className="flex items-center space-x-1 px-3 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700">
                                    <Save className="w-3 h-3" />
                                    <span>Save</span>
                                  </button>
                                  <button className="flex items-center space-x-1 px-3 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700">
                                    <TestTube className="w-3 h-3" />
                                    <span>Test</span>
                                  </button>
                                  <button className="flex items-center space-x-1 px-3 py-1 bg-purple-600 text-white rounded text-xs hover:bg-purple-700">
                                    <TrendingUp className="w-3 h-3" />
                                    <span>Backtest</span>
                                  </button>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                  {isLoading && (
                    <div className="flex justify-start">
                      <div className="bg-white border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center space-x-2">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                          <span className="text-sm text-gray-600">AI is generating your strategy...</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Chat Input */}
              <form onSubmit={handleChatSubmit} className="flex space-x-3">
                <input
                  type="text"
                  value={chatInput}
                  onChange={(e) => setChatInput(e.target.value)}
                  placeholder="Describe your trading strategy idea..."
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={isLoading}
                />
                <button
                  type="submit"
                  disabled={isLoading || !chatInput.trim()}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  <Send className="w-4 h-4" />
                  <span>Send</span>
                </button>
              </form>
            </div>
          </div>
        </section>

        {/* Active Strategies */}
        <section>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">📈 Active Strategies</h2>
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Strategy</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">P&L</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trades</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Win Rate</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {strategies.map((strategy) => (
                    <tr key={strategy.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{strategy.name}</div>
                          <div className="text-sm text-gray-500">{strategy.type}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(strategy.status)}`}>
                          {getStatusIcon(strategy.status)}
                          <span className="ml-1 capitalize">{strategy.status}</span>
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`text-sm font-medium ${strategy.pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          ${strategy.pnl >= 0 ? '+' : ''}{strategy.pnl}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{strategy.trades}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{strategy.winRate}%</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        <button className="text-blue-600 hover:text-blue-900">Edit</button>
                        <button className="text-green-600 hover:text-green-900">Start</button>
                        <button className="text-red-600 hover:text-red-900">Stop</button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </section>

        {/* MT5 Integration Status */}
        <section>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">🔗 MT5 Integration</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Connection Status</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Server</span>
                  <span className="text-sm font-medium text-green-600">Connected</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Account</span>
                  <span className="text-sm font-medium text-gray-900">********</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Balance</span>
                  <span className="text-sm font-medium text-gray-900">$10,000.00</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Equity</span>
                  <span className="text-sm font-medium text-gray-900">$10,247.50</span>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Open Positions</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <div>
                    <div className="text-sm font-medium text-gray-900">EURUSD</div>
                    <div className="text-xs text-gray-500">Buy 0.1 lots</div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-green-600">+$45.20</div>
                    <div className="text-xs text-gray-500">1.0850</div>
                  </div>
                </div>
                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <div>
                    <div className="text-sm font-medium text-gray-900">GBPUSD</div>
                    <div className="text-xs text-gray-500">Sell 0.05 lots</div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-red-600">-$12.30</div>
                    <div className="text-xs text-gray-500">1.2650</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Coming Soon Sections */}
        <section className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 text-center">
            <TrendingUp className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Advanced Backtesting</h3>
            <p className="text-gray-600">Comprehensive backtesting with historical data analysis coming soon...</p>
          </div>
          
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 text-center">
            <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Strategy Templates</h3>
            <p className="text-gray-600">Pre-built strategy templates and community sharing coming soon...</p>
          </div>
        </section>
      </div>
    </div>
  );
};

export default PlatformDashboard;