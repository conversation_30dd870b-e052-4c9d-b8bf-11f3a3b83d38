import { v4 as uuidv4 } from 'uuid';
import { EventEmitter } from 'events';
import { Logger, ServiceResponse } from '@/shared/types';
import {
  BacktestConfig,
  BacktestResults,
  PythonBacktestRequest,
  // PythonBacktestResponse, // Not currently used
  BacktestProgress,
  BacktestStatus,
} from '../../../../shared/schemas';
import { PythonEngineService } from './python-engine.service';

export interface BacktestBridgeServiceDependencies {
  pythonEngineService: PythonEngineService;
  logger: Logger;
}

/**
 * Service for managing backtests through the Python engine
 * Handles backtest submission, monitoring, and result retrieval
 */
export class BacktestBridgeService extends EventEmitter {
  private activeBacktests: Map<string, BacktestStatus> = new Map();
  private backtestResults: Map<string, BacktestResults> = new Map();

  constructor(private dependencies: BacktestBridgeServiceDependencies) {
    super();
    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    // Forward Python engine events
    this.dependencies.pythonEngineService.on('backtest_submitted', (data) => {
      this.emit('backtest_submitted', data);
    });
  }

  /**
   * Submit a backtest for execution
   */
  async submitBacktest(
    config: BacktestConfig,
    marketData: any[]
  ): Promise<ServiceResponse<{ backtestId: string; status: BacktestStatus }>> {
    try {
      const backtestId = uuidv4();
      
      // Validate backtest configuration
      const validationResult = this.validateBacktestConfig(config);
      if (!validationResult.isValid) {
        return {
          success: false,
          error: {
            code: 'INVALID_BACKTEST_CONFIG',
            message: 'Backtest configuration validation failed',
            details: validationResult.errors.join(', '),
          },
        };
      }

      // Prepare request for Python engine
      const request: PythonBacktestRequest = {
        request_id: uuidv4(),
        config,
        data: {
          market_data: marketData,
        },
      };

      // Mark backtest as pending
      this.activeBacktests.set(backtestId, 'pending');

      // Submit to Python engine
      const response = await this.dependencies.pythonEngineService.submitBacktest(request);

      if (!response.success || !response.data) {
        this.activeBacktests.delete(backtestId);
        return {
          success: false,
          error: response.error || {
            code: 'BACKTEST_SUBMISSION_ERROR',
            message: 'Failed to submit backtest',
          },
        };
      }

      if (!response.data.success) {
        this.activeBacktests.delete(backtestId);
        return {
          success: false,
          error: {
            code: 'BACKTEST_REJECTED',
            message: response.data.error || 'Backtest was rejected by Python engine',
          },
        };
      }

      // Update status to running
      this.activeBacktests.set(backtestId, 'running');

      // Store results if available immediately (for fast backtests)
      if (response.data.results) {
        this.backtestResults.set(backtestId, response.data.results);
        this.activeBacktests.set(backtestId, 'completed');
      }

      this.dependencies.logger.info('Backtest submitted successfully', {
        backtestId,
        requestId: request.request_id,
        strategy: config.strategy.name,
        symbols: config.symbols,
        executionTime: response.data.execution_time_seconds,
      });

      this.emit('backtest_status_changed', {
        backtestId,
        status: this.activeBacktests.get(backtestId),
        progress: response.data.results ? 100 : 0,
      });

      return {
        success: true,
        data: {
          backtestId,
          status: this.activeBacktests.get(backtestId)!,
        },
      };
    } catch (error) {
      this.dependencies.logger.error('Backtest submission failed', {
        strategy: config.strategy.name,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return {
        success: false,
        error: {
          code: 'BACKTEST_SUBMISSION_ERROR',
          message: 'Failed to submit backtest',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * Get backtest status and progress
   */
  async getBacktestStatus(backtestId: string): Promise<ServiceResponse<BacktestProgress>> {
    try {
      const localStatus = this.activeBacktests.get(backtestId);
      
      if (!localStatus) {
        return {
          success: false,
          error: {
            code: 'BACKTEST_NOT_FOUND',
            message: 'Backtest not found',
          },
        };
      }

      // If backtest is completed, return final status
      if (localStatus === 'completed' || localStatus === 'error') {
        return {
          success: true,
          data: {
            backtest_id: backtestId,
            status: localStatus,
            progress: localStatus === 'completed' ? 100 : 0,
            trades_executed: 0, // Default value
          },
        };
      }

      // Query Python engine for current status
      const response = await this.dependencies.pythonEngineService.getBacktestStatus(backtestId);

      if (!response.success || !response.data) {
        return {
          success: true,
          data: {
            backtest_id: backtestId,
            status: localStatus,
            progress: 0,
            trades_executed: 0, // Default value
          },
        };
      }

      // Update local status
      const engineStatus = response.data.status as BacktestStatus;
      this.activeBacktests.set(backtestId, engineStatus);

      const progress: BacktestProgress = {
        backtest_id: backtestId,
        status: engineStatus,
        progress: response.data.progress || 0,
        trades_executed: (response.data as any).trades_executed || 0,
      };

      this.emit('backtest_progress_updated', progress);

      return {
        success: true,
        data: progress,
      };
    } catch (error) {
      this.dependencies.logger.error('Failed to get backtest status', {
        backtestId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return {
        success: false,
        error: {
          code: 'BACKTEST_STATUS_ERROR',
          message: 'Failed to get backtest status',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * Get backtest results
   */
  async getBacktestResults(backtestId: string): Promise<ServiceResponse<BacktestResults>> {
    try {
      // Check if results are cached locally
      const cachedResults = this.backtestResults.get(backtestId);
      if (cachedResults) {
        return {
          success: true,
          data: cachedResults,
        };
      }

      // Check if backtest exists and is completed
      const status = this.activeBacktests.get(backtestId);
      if (!status) {
        return {
          success: false,
          error: {
            code: 'BACKTEST_NOT_FOUND',
            message: 'Backtest not found',
          },
        };
      }

      if (status !== 'completed') {
        return {
          success: false,
          error: {
            code: 'BACKTEST_NOT_COMPLETED',
            message: 'Backtest has not completed yet',
          },
        };
      }

      // In a real implementation, you would fetch results from Python engine
      // For now, return error indicating results not available
      return {
        success: false,
        error: {
          code: 'RESULTS_NOT_AVAILABLE',
          message: 'Backtest results are not available',
        },
      };
    } catch (error) {
      this.dependencies.logger.error('Failed to get backtest results', {
        backtestId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return {
        success: false,
        error: {
          code: 'BACKTEST_RESULTS_ERROR',
          message: 'Failed to get backtest results',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * Cancel a running backtest
   */
  async cancelBacktest(backtestId: string): Promise<ServiceResponse<{ cancelled: boolean }>> {
    try {
      const status = this.activeBacktests.get(backtestId);
      
      if (!status) {
        return {
          success: false,
          error: {
            code: 'BACKTEST_NOT_FOUND',
            message: 'Backtest not found',
          },
        };
      }

      if (status === 'completed' || status === 'error') {
        return {
          success: false,
          error: {
            code: 'BACKTEST_ALREADY_FINISHED',
            message: 'Cannot cancel a finished backtest',
          },
        };
      }

      // Update status to error (cancelled)
      this.activeBacktests.set(backtestId, 'error');

      this.dependencies.logger.info('Backtest cancelled', { backtestId });

      this.emit('backtest_cancelled', { backtestId });

      return {
        success: true,
        data: { cancelled: true },
      };
    } catch (error) {
      this.dependencies.logger.error('Failed to cancel backtest', {
        backtestId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return {
        success: false,
        error: {
          code: 'BACKTEST_CANCEL_ERROR',
          message: 'Failed to cancel backtest',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * Get all active backtests
   */
  getActiveBacktests(): { backtestId: string; status: BacktestStatus }[] {
    return Array.from(this.activeBacktests.entries()).map(([backtestId, status]) => ({
      backtestId,
      status,
    }));
  }

  /**
   * Clean up completed backtests from memory
   */
  cleanupCompletedBacktests(): void {
    const completedBacktests: string[] = [];
    
    for (const [backtestId, status] of this.activeBacktests.entries()) {
      if (status === 'completed' || status === 'error') {
        completedBacktests.push(backtestId);
      }
    }

    // Keep results but remove from active tracking after some time
    for (const backtestId of completedBacktests) {
      // In a real implementation, you might want to persist results to database
      // before removing from memory
      this.activeBacktests.delete(backtestId);
    }

    if (completedBacktests.length > 0) {
      this.dependencies.logger.debug('Cleaned up completed backtests', {
        count: completedBacktests.length,
      });
    }
  }

  /**
   * Validate backtest configuration
   */
  private validateBacktestConfig(config: BacktestConfig): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Date validation
    if (config.start_date >= config.end_date) {
      errors.push('Start date must be before end date');
    }

    // Balance validation
    if (config.initial_balance <= 0) {
      errors.push('Initial balance must be positive');
    }

    if (config.initial_balance < 1000) {
      errors.push('Initial balance too low (minimum 1000)');
    }

    // Strategy validation
    if (!config.strategy.name || config.strategy.name.trim() === '') {
      errors.push('Strategy name is required');
    }

    // Symbols validation
    if (config.symbols.length === 0) {
      errors.push('At least one symbol is required');
    }

    if (config.symbols.length > 10) {
      errors.push('Maximum 10 symbols allowed');
    }

    // Risk management validation
    if (config.risk_management.max_risk_per_trade <= 0 || config.risk_management.max_risk_per_trade > 0.1) {
      errors.push('Max risk per trade must be between 0 and 10%');
    }

    if (config.risk_management.max_concurrent_trades <= 0 || config.risk_management.max_concurrent_trades > 50) {
      errors.push('Max concurrent trades must be between 1 and 50');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Stop the service and cleanup
   */
  async stop(): Promise<void> {
    this.activeBacktests.clear();
    this.backtestResults.clear();
    this.removeAllListeners();
    this.dependencies.logger.info('Backtest bridge service stopped');
  }
}