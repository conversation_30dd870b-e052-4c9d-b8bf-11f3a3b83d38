import { describe, it, expect } from '@jest/globals';

// Import all schema modules to test integration
import * as AuthSchemas from './auth.schemas';
import * as TradingSchemas from './trading.schemas';
import * as BacktestSchemas from './backtest.schemas';
import * as ChatSchemas from './chat.schemas';
import * as UploadSchemas from './upload.schemas';
import * as ApiSchemas from './api.schemas';
import * as CommonSchemas from './common.schemas';

describe('Schema Integration Tests', () => {
  describe('Trading Integration', () => {
    it('should validate OrderRequest compatible with Python Pydantic model', () => {
      // This matches the Python OrderRequest from bridge/schemas.py
      const orderRequest = {
        symbol: 'EURUSD',
        volume: 0.01,
        order_type: 'buy',
        price: 1.1000,
        stop_loss: 1.0950,
        take_profit: 1.1050,
      };

      const result = TradingSchemas.OrderRequestSchema.safeParse(orderRequest);
      expect(result.success).toBe(true);
      
      if (result.success) {
        expect(result.data.symbol).toBe('EURUSD');
        expect(result.data.order_type).toBe('buy');
      }
    });

    it('should validate OrderResult compatible with Python response', () => {
      const orderResult = {
        success: true,
        order_id: 12345,
      };

      const result = TradingSchemas.OrderResultSchema.safeParse(orderResult);
      expect(result.success).toBe(true);
    });

    it('should validate AccountInfo matching Python model', () => {
      const accountInfo = {
        balance: 10000.50,
        equity: 10050.25,
        margin: 500.00,
        currency: 'USD',
      };

      const result = TradingSchemas.AccountInfoSchema.safeParse(accountInfo);
      expect(result.success).toBe(true);
    });
  });

  describe('Python Engine Communication', () => {
    it('should validate TradingEngineRequest for Python bridge', () => {
      const request = {
        action: 'submit_order',
        payload: {
          symbol: 'EURUSD',
          volume: 0.01,
          order_type: 'buy',
          price: 1.1000,
        },
        timestamp: new Date(),
        request_id: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
      };

      const result = TradingSchemas.TradingEngineRequestSchema.safeParse(request);
      expect(result.success).toBe(true);
    });

    it('should validate PythonBacktestRequest', () => {
      const request = {
        request_id: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
        config: {
          name: 'Test Strategy',
          symbols: ['EURUSD'],
          start_date: new Date('2024-01-01'),
          end_date: new Date('2024-12-31'),
          initial_balance: 10000,
          strategy: {
            name: 'simple_ma',
            parameters: { period: 20 },
          },
          risk_management: {
            max_risk_per_trade: 0.02,
            max_concurrent_trades: 5,
          },
        },
        data: {
          market_data: [{
            symbol: 'EURUSD',
            timestamp: new Date('2024-01-01T00:00:00Z'),
            open: 1.1000,
            high: 1.1010,
            low: 1.0990,
            close: 1.1005,
            volume: 1000,
          }],
        },
      };

      const result = BacktestSchemas.PythonBacktestRequestSchema.safeParse(request);
      expect(result.success).toBe(true);
    });

    it('should validate PythonChatRequest', () => {
      const request = {
        request_id: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
        query: 'What is the current trend for EURUSD?',
        session_id: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
        user_context: {
          user_id: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
          trading_data: {
            balance: 10000,
            open_positions: [],
          },
        },
        rag_config: {
          use_knowledge_graph: true,
          use_market_data: true,
          max_context_length: 4000,
        },
      };

      const result = ChatSchemas.PythonChatRequestSchema.safeParse(request);
      expect(result.success).toBe(true);
    });
  });

  describe('API Contract Validation', () => {
    it('should validate complete login flow', () => {
      // Login request
      const loginRequest = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
      };

      const loginRequestResult = ApiSchemas.LoginApiRequestSchema.safeParse(loginRequest);
      expect(loginRequestResult.success).toBe(true);

      // Login response
      const loginResponse = {
        success: true,
        data: {
          user: {
            id: 'user-123',
            email: '<EMAIL>',
            fullName: 'Test User',
            subscriptionTier: 'free',
          },
          tokens: {
            accessToken: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...',
            refreshToken: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...',
            expiresIn: 3600,
          },
        },
        timestamp: new Date(),
      };

      const loginResponseResult = ApiSchemas.LoginApiResponseSchema.safeParse(loginResponse);
      expect(loginResponseResult.success).toBe(true);
    });

    it('should validate backtest creation flow', () => {
      const createBacktestRequest = {
        config: {
          name: 'My Test Strategy',
          description: 'Testing simple moving average strategy',
          symbols: ['EURUSD', 'GBPUSD'],
          start_date: new Date('2024-01-01'),
          end_date: new Date('2024-06-30'),
          initial_balance: 10000,
          strategy: {
            name: 'sma_crossover',
            parameters: {
              fast_period: 10,
              slow_period: 20,
            },
          },
          risk_management: {
            max_risk_per_trade: 0.02,
            max_concurrent_trades: 3,
          },
        },
        data_source: 'historical',
      };

      const result = ApiSchemas.CreateBacktestApiRequestSchema.safeParse(createBacktestRequest);
      expect(result.success).toBe(true);
    });
  });

  describe('Type Compatibility', () => {
    it('should ensure User types match between auth and user schemas', () => {
      // Test that auth user and user schema user are compatible
      const user = {
        id: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
        email: '<EMAIL>',
        fullName: 'Test User',
        subscriptionTier: 'pro' as const,
        apiQuotaUsed: 50,
        apiQuotaLimit: 1000,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // This should compile without errors, proving type compatibility
      const userValidation = AuthSchemas.UserSchema.safeParse(user);
      expect(userValidation.success).toBe(true);
    });

    it('should ensure TradingSymbol consistency across schemas', () => {
      const symbols: TradingSchemas.TradingSymbol[] = ['EURUSD', 'GBPUSD', 'USDJPY'];
      
      // Test that these symbols work in different contexts
      const orderRequest = {
        symbol: symbols[0],
        volume: 0.01,
        order_type: 'buy' as const,
        price: 1.1000,
      };

      const backtest = {
        name: 'Test',
        symbols: symbols,
        start_date: new Date('2024-01-01'),
        end_date: new Date('2024-12-31'),
        strategy: { name: 'test', parameters: {} },
        risk_management: {
          max_risk_per_trade: 0.02,
          max_concurrent_trades: 5,
        },
      };

      expect(TradingSchemas.OrderRequestSchema.safeParse(orderRequest).success).toBe(true);
      expect(BacktestSchemas.BacktestConfigSchema.safeParse(backtest).success).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should provide meaningful validation errors', () => {
      const invalidOrderRequest = {
        symbol: 'INVALID_SYMBOL',
        volume: -1, // Invalid negative volume
        order_type: 'invalid_type',
        price: 0, // Invalid zero price
      };

      const result = TradingSchemas.OrderRequestSchema.safeParse(invalidOrderRequest);
      expect(result.success).toBe(false);
      
      if (!result.success) {
        expect(result.error.errors.length).toBeGreaterThan(0);
        expect(result.error.errors.some(e => e.path.includes('symbol'))).toBe(true);
        expect(result.error.errors.some(e => e.path.includes('volume'))).toBe(true);
        expect(result.error.errors.some(e => e.path.includes('order_type'))).toBe(true);
      }
    });

    it('should validate date ranges properly', () => {
      const invalidDateRange = {
        name: 'Test',
        symbols: ['EURUSD'],
        start_date: new Date('2024-12-31'),
        end_date: new Date('2024-01-01'), // End before start
        strategy: { name: 'test', parameters: {} },
        risk_management: {
          max_risk_per_trade: 0.02,
          max_concurrent_trades: 5,
        },
      };

      const result = BacktestSchemas.BacktestConfigSchema.safeParse(invalidDateRange);
      expect(result.success).toBe(false);
    });
  });

  describe('Schema Completeness', () => {
    it('should have all required exports', () => {
      // Verify all main schema modules export their key types
      expect(AuthSchemas.UserSchema).toBeDefined();
      expect(TradingSchemas.OrderRequestSchema).toBeDefined();
      expect(BacktestSchemas.BacktestConfigSchema).toBeDefined();
      expect(ChatSchemas.ChatMessageSchema).toBeDefined();
      expect(UploadSchemas.DataFileUploadSchema).toBeDefined();
      expect(ApiSchemas.LoginApiRequestSchema).toBeDefined();
      expect(CommonSchemas.ApiResponseSchema).toBeDefined();
    });

    it('should support all Python engine integration patterns', () => {
      // Test that all Python integration schemas are available
      expect(TradingSchemas.TradingEngineRequestSchema).toBeDefined();
      expect(BacktestSchemas.PythonBacktestRequestSchema).toBeDefined();
      expect(ChatSchemas.PythonChatRequestSchema).toBeDefined();
      expect(UploadSchemas.PythonDataProcessingRequestSchema).toBeDefined();
    });
  });
});