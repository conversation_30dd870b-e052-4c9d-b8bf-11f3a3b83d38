/**
 * Worker Management Schemas
 * Defines types for Python background worker integration
 */
import { z } from 'zod';
export declare const WorkerStatusSchema: z.ZodObject<{
    name: z.ZodString;
    status: z.<PERSON>od<PERSON>num<["running", "stopped", "error", "restarting"]>;
    pid: z.<PERSON>od<PERSON>ptional<z.ZodNumber>;
    start_time: z.ZodOptional<z.ZodDate>;
    last_activity: z.ZodOptional<z.ZodDate>;
    error_message: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    status: "error" | "running" | "stopped" | "restarting";
    name: string;
    error_message?: string | undefined;
    last_activity?: Date | undefined;
    pid?: number | undefined;
    start_time?: Date | undefined;
}, {
    status: "error" | "running" | "stopped" | "restarting";
    name: string;
    error_message?: string | undefined;
    last_activity?: Date | undefined;
    pid?: number | undefined;
    start_time?: Date | undefined;
}>;
type WorkerStatus = z.infer<typeof WorkerStatusSchema>;
export declare const WorkerStatsSchema: z.ZodObject<{
    manager_status: z.Z<PERSON><["running", "stopped", "starting", "stopping"]>;
    uptime_seconds: z.ZodNumber;
    start_time: z.ZodString;
    active_workers: z.ZodNumber;
    active_tasks: z.ZodNumber;
    worker_stats: z.ZodRecord<z.ZodString, z.ZodObject<{
        status: z.ZodString;
        running_jobs: z.ZodOptional<z.ZodNumber>;
        max_concurrent: z.ZodOptional<z.ZodNumber>;
        poll_interval: z.ZodOptional<z.ZodNumber>;
        job_ids: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, "strip", z.ZodTypeAny, {
        status: string;
        running_jobs?: number | undefined;
        max_concurrent?: number | undefined;
        poll_interval?: number | undefined;
        job_ids?: string[] | undefined;
    }, {
        status: string;
        running_jobs?: number | undefined;
        max_concurrent?: number | undefined;
        poll_interval?: number | undefined;
        job_ids?: string[] | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    start_time: string;
    manager_status: "running" | "stopped" | "starting" | "stopping";
    uptime_seconds: number;
    active_workers: number;
    active_tasks: number;
    worker_stats: Record<string, {
        status: string;
        running_jobs?: number | undefined;
        max_concurrent?: number | undefined;
        poll_interval?: number | undefined;
        job_ids?: string[] | undefined;
    }>;
}, {
    start_time: string;
    manager_status: "running" | "stopped" | "starting" | "stopping";
    uptime_seconds: number;
    active_workers: number;
    active_tasks: number;
    worker_stats: Record<string, {
        status: string;
        running_jobs?: number | undefined;
        max_concurrent?: number | undefined;
        poll_interval?: number | undefined;
        job_ids?: string[] | undefined;
    }>;
}>;
type WorkerStats = z.infer<typeof WorkerStatsSchema>;
export declare const WorkerHealthCheckSchema: z.ZodObject<{
    healthy: z.ZodBoolean;
    timestamp: z.ZodDate;
    workers: z.ZodRecord<z.ZodString, z.ZodObject<{
        healthy: z.ZodBoolean;
        status: z.ZodOptional<z.ZodString>;
        reason: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        healthy: boolean;
        status?: string | undefined;
        reason?: string | undefined;
    }, {
        healthy: boolean;
        status?: string | undefined;
        reason?: string | undefined;
    }>>;
    uptime: z.ZodNumber;
    error: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    healthy: boolean;
    timestamp: Date;
    uptime: number;
    workers: Record<string, {
        healthy: boolean;
        status?: string | undefined;
        reason?: string | undefined;
    }>;
    error?: string | undefined;
}, {
    healthy: boolean;
    timestamp: Date;
    uptime: number;
    workers: Record<string, {
        healthy: boolean;
        status?: string | undefined;
        reason?: string | undefined;
    }>;
    error?: string | undefined;
}>;
type WorkerHealthCheck = z.infer<typeof WorkerHealthCheckSchema>;
export declare const FileUploadSessionSchema: z.ZodObject<{
    id: z.ZodString;
    user_id: z.ZodString;
    original_filename: z.ZodString;
    file_size: z.ZodNumber;
    status: z.ZodEnum<["pending", "mapping_confirmed", "parsing_in_progress", "ready", "error"]>;
    final_mapping: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodString>>;
    timezone: z.ZodOptional<z.ZodString>;
    rows_processed: z.ZodOptional<z.ZodNumber>;
    error_message: z.ZodOptional<z.ZodString>;
    created_at: z.ZodDate;
    updated_at: z.ZodDate;
    temporary_file_path: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    status: "error" | "pending" | "ready" | "mapping_confirmed" | "parsing_in_progress";
    id: string;
    user_id: string;
    created_at: Date;
    updated_at: Date;
    original_filename: string;
    file_size: number;
    timezone?: string | undefined;
    error_message?: string | undefined;
    rows_processed?: number | undefined;
    final_mapping?: Record<string, string> | undefined;
    temporary_file_path?: string | undefined;
}, {
    status: "error" | "pending" | "ready" | "mapping_confirmed" | "parsing_in_progress";
    id: string;
    user_id: string;
    created_at: Date;
    updated_at: Date;
    original_filename: string;
    file_size: number;
    timezone?: string | undefined;
    error_message?: string | undefined;
    rows_processed?: number | undefined;
    final_mapping?: Record<string, string> | undefined;
    temporary_file_path?: string | undefined;
}>;
type FileUploadSession = z.infer<typeof FileUploadSessionSchema>;
export declare const BacktestJobSchema: z.ZodObject<{
    id: z.ZodString;
    user_id: z.ZodString;
    name: z.ZodString;
    status: z.ZodEnum<["pending", "running", "completed", "error", "cancelled"]>;
    symbol: z.ZodString;
    start_date: z.ZodDate;
    end_date: z.ZodDate;
    strategy_config: z.ZodRecord<z.ZodString, z.ZodAny>;
    progress: z.ZodOptional<z.ZodNumber>;
    started_at: z.ZodOptional<z.ZodDate>;
    completed_at: z.ZodOptional<z.ZodDate>;
    error_message: z.ZodOptional<z.ZodString>;
    results: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
    created_at: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    symbol: string;
    status: "error" | "running" | "pending" | "completed" | "cancelled";
    name: string;
    id: string;
    user_id: string;
    start_date: Date;
    end_date: Date;
    strategy_config: Record<string, any>;
    created_at: Date;
    progress?: number | undefined;
    started_at?: Date | undefined;
    completed_at?: Date | undefined;
    error_message?: string | undefined;
    results?: Record<string, any> | undefined;
}, {
    symbol: string;
    status: "error" | "running" | "pending" | "completed" | "cancelled";
    name: string;
    id: string;
    user_id: string;
    start_date: Date;
    end_date: Date;
    strategy_config: Record<string, any>;
    created_at: Date;
    progress?: number | undefined;
    started_at?: Date | undefined;
    completed_at?: Date | undefined;
    error_message?: string | undefined;
    results?: Record<string, any> | undefined;
}>;
type BacktestJob = z.infer<typeof BacktestJobSchema>;
export declare const DGMExperimentSchema: z.ZodObject<{
    id: z.ZodString;
    user_id: z.ZodString;
    experiment_name: z.ZodString;
    status: z.ZodEnum<["pending", "running", "completed", "error", "deployed"]>;
    base_strategy: z.ZodRecord<z.ZodString, z.ZodAny>;
    generated_strategy: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
    fitness_improvement: z.ZodOptional<z.ZodNumber>;
    deployed_backtest_id: z.ZodOptional<z.ZodString>;
    started_at: z.ZodOptional<z.ZodDate>;
    completed_at: z.ZodOptional<z.ZodDate>;
    error_message: z.ZodOptional<z.ZodString>;
    created_at: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    status: "error" | "running" | "pending" | "completed" | "deployed";
    id: string;
    user_id: string;
    created_at: Date;
    experiment_name: string;
    base_strategy: Record<string, any>;
    started_at?: Date | undefined;
    completed_at?: Date | undefined;
    error_message?: string | undefined;
    generated_strategy?: Record<string, any> | undefined;
    fitness_improvement?: number | undefined;
    deployed_backtest_id?: string | undefined;
}, {
    status: "error" | "running" | "pending" | "completed" | "deployed";
    id: string;
    user_id: string;
    created_at: Date;
    experiment_name: string;
    base_strategy: Record<string, any>;
    started_at?: Date | undefined;
    completed_at?: Date | undefined;
    error_message?: string | undefined;
    generated_strategy?: Record<string, any> | undefined;
    fitness_improvement?: number | undefined;
    deployed_backtest_id?: string | undefined;
}>;
type DGMExperiment = z.infer<typeof DGMExperimentSchema>;
export declare const WorkerManagementRequestSchema: z.ZodObject<{
    action: z.ZodEnum<["status", "health", "restart", "stats"]>;
    worker_name: z.ZodOptional<z.ZodString>;
    timestamp: z.ZodDate;
    request_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    action: "status" | "health" | "restart" | "stats";
    timestamp: Date;
    request_id: string;
    worker_name?: string | undefined;
}, {
    action: "status" | "health" | "restart" | "stats";
    timestamp: Date;
    request_id: string;
    worker_name?: string | undefined;
}>;
type WorkerManagementRequest = z.infer<typeof WorkerManagementRequestSchema>;
export declare const WorkerManagementResponseSchema: z.ZodObject<{
    success: z.ZodBoolean;
    data: z.ZodOptional<z.ZodAny>;
    error: z.ZodOptional<z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        details?: string | undefined;
    }, {
        code: string;
        message: string;
        details?: string | undefined;
    }>>;
    timestamp: z.ZodDate;
    request_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    timestamp: Date;
    request_id: string;
    data?: any;
    error?: {
        code: string;
        message: string;
        details?: string | undefined;
    } | undefined;
}, {
    success: boolean;
    timestamp: Date;
    request_id: string;
    data?: any;
    error?: {
        code: string;
        message: string;
        details?: string | undefined;
    } | undefined;
}>;
type WorkerManagementResponse = z.infer<typeof WorkerManagementResponseSchema>;
export declare const WorkerBridgeStatusSchema: z.ZodObject<{
    bridge_healthy: z.ZodBoolean;
    python_workers_healthy: z.ZodBoolean;
    last_health_check: z.ZodOptional<z.ZodDate>;
    last_known_status: z.ZodOptional<z.ZodObject<{
        manager_status: z.ZodEnum<["running", "stopped", "starting", "stopping"]>;
        uptime_seconds: z.ZodNumber;
        start_time: z.ZodString;
        active_workers: z.ZodNumber;
        active_tasks: z.ZodNumber;
        worker_stats: z.ZodRecord<z.ZodString, z.ZodObject<{
            status: z.ZodString;
            running_jobs: z.ZodOptional<z.ZodNumber>;
            max_concurrent: z.ZodOptional<z.ZodNumber>;
            poll_interval: z.ZodOptional<z.ZodNumber>;
            job_ids: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        }, "strip", z.ZodTypeAny, {
            status: string;
            running_jobs?: number | undefined;
            max_concurrent?: number | undefined;
            poll_interval?: number | undefined;
            job_ids?: string[] | undefined;
        }, {
            status: string;
            running_jobs?: number | undefined;
            max_concurrent?: number | undefined;
            poll_interval?: number | undefined;
            job_ids?: string[] | undefined;
        }>>;
    }, "strip", z.ZodTypeAny, {
        start_time: string;
        manager_status: "running" | "stopped" | "starting" | "stopping";
        uptime_seconds: number;
        active_workers: number;
        active_tasks: number;
        worker_stats: Record<string, {
            status: string;
            running_jobs?: number | undefined;
            max_concurrent?: number | undefined;
            poll_interval?: number | undefined;
            job_ids?: string[] | undefined;
        }>;
    }, {
        start_time: string;
        manager_status: "running" | "stopped" | "starting" | "stopping";
        uptime_seconds: number;
        active_workers: number;
        active_tasks: number;
        worker_stats: Record<string, {
            status: string;
            running_jobs?: number | undefined;
            max_concurrent?: number | undefined;
            poll_interval?: number | undefined;
            job_ids?: string[] | undefined;
        }>;
    }>>;
    monitoring_config: z.ZodObject<{
        health_check_interval: z.ZodNumber;
        status_poll_interval: z.ZodNumber;
        auto_restart: z.ZodBoolean;
    }, "strip", z.ZodTypeAny, {
        health_check_interval: number;
        status_poll_interval: number;
        auto_restart: boolean;
    }, {
        health_check_interval: number;
        status_poll_interval: number;
        auto_restart: boolean;
    }>;
    active_jobs: z.ZodObject<{
        file_parsing: z.ZodNumber;
        backtests: z.ZodNumber;
        dgm_experiments: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        backtests: number;
        file_parsing: number;
        dgm_experiments: number;
    }, {
        backtests: number;
        file_parsing: number;
        dgm_experiments: number;
    }>;
}, "strip", z.ZodTypeAny, {
    bridge_healthy: boolean;
    python_workers_healthy: boolean;
    monitoring_config: {
        health_check_interval: number;
        status_poll_interval: number;
        auto_restart: boolean;
    };
    active_jobs: {
        backtests: number;
        file_parsing: number;
        dgm_experiments: number;
    };
    last_health_check?: Date | undefined;
    last_known_status?: {
        start_time: string;
        manager_status: "running" | "stopped" | "starting" | "stopping";
        uptime_seconds: number;
        active_workers: number;
        active_tasks: number;
        worker_stats: Record<string, {
            status: string;
            running_jobs?: number | undefined;
            max_concurrent?: number | undefined;
            poll_interval?: number | undefined;
            job_ids?: string[] | undefined;
        }>;
    } | undefined;
}, {
    bridge_healthy: boolean;
    python_workers_healthy: boolean;
    monitoring_config: {
        health_check_interval: number;
        status_poll_interval: number;
        auto_restart: boolean;
    };
    active_jobs: {
        backtests: number;
        file_parsing: number;
        dgm_experiments: number;
    };
    last_health_check?: Date | undefined;
    last_known_status?: {
        start_time: string;
        manager_status: "running" | "stopped" | "starting" | "stopping";
        uptime_seconds: number;
        active_workers: number;
        active_tasks: number;
        worker_stats: Record<string, {
            status: string;
            running_jobs?: number | undefined;
            max_concurrent?: number | undefined;
            poll_interval?: number | undefined;
            job_ids?: string[] | undefined;
        }>;
    } | undefined;
}>;
type WorkerBridgeStatus = z.infer<typeof WorkerBridgeStatusSchema>;
export declare const FileProcessingProgressSchema: z.ZodObject<{
    session_id: z.ZodString;
    status: z.ZodEnum<["pending", "parsing", "validating", "inserting", "completed", "error"]>;
    progress_percent: z.ZodNumber;
    rows_processed: z.ZodNumber;
    total_rows: z.ZodOptional<z.ZodNumber>;
    current_step: z.ZodString;
    error_message: z.ZodOptional<z.ZodString>;
    timestamp: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    status: "error" | "pending" | "completed" | "parsing" | "validating" | "inserting";
    timestamp: Date;
    session_id: string;
    rows_processed: number;
    current_step: string;
    progress_percent: number;
    error_message?: string | undefined;
    total_rows?: number | undefined;
}, {
    status: "error" | "pending" | "completed" | "parsing" | "validating" | "inserting";
    timestamp: Date;
    session_id: string;
    rows_processed: number;
    current_step: string;
    progress_percent: number;
    error_message?: string | undefined;
    total_rows?: number | undefined;
}>;
type FileProcessingProgress = z.infer<typeof FileProcessingProgressSchema>;
export declare const DGMExperimentProgressSchema: z.ZodObject<{
    experiment_id: z.ZodString;
    status: z.ZodEnum<["pending", "initializing", "evolving", "testing", "completed", "error"]>;
    progress_percent: z.ZodNumber;
    current_generation: z.ZodOptional<z.ZodNumber>;
    total_generations: z.ZodOptional<z.ZodNumber>;
    best_fitness: z.ZodOptional<z.ZodNumber>;
    fitness_improvement: z.ZodOptional<z.ZodNumber>;
    current_step: z.ZodString;
    elapsed_time: z.ZodOptional<z.ZodNumber>;
    estimated_completion: z.ZodOptional<z.ZodDate>;
    error_message: z.ZodOptional<z.ZodString>;
    timestamp: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    status: "error" | "pending" | "completed" | "initializing" | "evolving" | "testing";
    timestamp: Date;
    current_step: string;
    progress_percent: number;
    experiment_id: string;
    error_message?: string | undefined;
    estimated_completion?: Date | undefined;
    fitness_improvement?: number | undefined;
    current_generation?: number | undefined;
    total_generations?: number | undefined;
    best_fitness?: number | undefined;
    elapsed_time?: number | undefined;
}, {
    status: "error" | "pending" | "completed" | "initializing" | "evolving" | "testing";
    timestamp: Date;
    current_step: string;
    progress_percent: number;
    experiment_id: string;
    error_message?: string | undefined;
    estimated_completion?: Date | undefined;
    fitness_improvement?: number | undefined;
    current_generation?: number | undefined;
    total_generations?: number | undefined;
    best_fitness?: number | undefined;
    elapsed_time?: number | undefined;
}>;
type DGMExperimentProgress = z.infer<typeof DGMExperimentProgressSchema>;
export declare const WorkerEventSchema: z.ZodObject<{
    event_type: z.ZodEnum<["worker_started", "worker_stopped", "worker_error", "worker_restarted", "job_started", "job_completed", "job_failed", "job_progress", "health_check", "status_update"]>;
    worker_name: z.ZodOptional<z.ZodString>;
    job_id: z.ZodOptional<z.ZodString>;
    job_type: z.ZodOptional<z.ZodEnum<["file_parsing", "backtest", "dgm_experiment"]>>;
    data: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
    timestamp: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    timestamp: Date;
    event_type: "worker_started" | "worker_stopped" | "worker_error" | "worker_restarted" | "job_started" | "job_completed" | "job_failed" | "job_progress" | "health_check" | "status_update";
    data?: Record<string, any> | undefined;
    worker_name?: string | undefined;
    job_id?: string | undefined;
    job_type?: "backtest" | "file_parsing" | "dgm_experiment" | undefined;
}, {
    timestamp: Date;
    event_type: "worker_started" | "worker_stopped" | "worker_error" | "worker_restarted" | "job_started" | "job_completed" | "job_failed" | "job_progress" | "health_check" | "status_update";
    data?: Record<string, any> | undefined;
    worker_name?: string | undefined;
    job_id?: string | undefined;
    job_type?: "backtest" | "file_parsing" | "dgm_experiment" | undefined;
}>;
type WorkerEvent = z.infer<typeof WorkerEventSchema>;
export declare const WorkerConfigSchema: z.ZodObject<{
    file_parser: z.ZodObject<{
        poll_interval: z.ZodDefault<z.ZodNumber>;
        max_file_size: z.ZodDefault<z.ZodNumber>;
        chunk_size: z.ZodDefault<z.ZodNumber>;
        supported_formats: z.ZodDefault<z.ZodArray<z.ZodString, "many">>;
    }, "strip", z.ZodTypeAny, {
        poll_interval: number;
        max_file_size: number;
        chunk_size: number;
        supported_formats: string[];
    }, {
        poll_interval?: number | undefined;
        max_file_size?: number | undefined;
        chunk_size?: number | undefined;
        supported_formats?: string[] | undefined;
    }>;
    backtest_runner: z.ZodObject<{
        poll_interval: z.ZodDefault<z.ZodNumber>;
        max_concurrent: z.ZodDefault<z.ZodNumber>;
        timeout_hours: z.ZodDefault<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        max_concurrent: number;
        poll_interval: number;
        timeout_hours: number;
    }, {
        max_concurrent?: number | undefined;
        poll_interval?: number | undefined;
        timeout_hours?: number | undefined;
    }>;
    dgm_monitor: z.ZodObject<{
        poll_interval: z.ZodDefault<z.ZodNumber>;
        max_concurrent: z.ZodDefault<z.ZodNumber>;
        enabled: z.ZodDefault<z.ZodBoolean>;
        fitness_threshold: z.ZodDefault<z.ZodNumber>;
        timeout_hours: z.ZodDefault<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        enabled: boolean;
        max_concurrent: number;
        poll_interval: number;
        timeout_hours: number;
        fitness_threshold: number;
    }, {
        enabled?: boolean | undefined;
        max_concurrent?: number | undefined;
        poll_interval?: number | undefined;
        timeout_hours?: number | undefined;
        fitness_threshold?: number | undefined;
    }>;
    general: z.ZodObject<{
        log_level: z.ZodDefault<z.ZodEnum<["DEBUG", "INFO", "WARNING", "ERROR"]>>;
        health_check_interval: z.ZodDefault<z.ZodNumber>;
        status_poll_interval: z.ZodDefault<z.ZodNumber>;
        auto_restart: z.ZodDefault<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        health_check_interval: number;
        status_poll_interval: number;
        auto_restart: boolean;
        log_level: "DEBUG" | "INFO" | "WARNING" | "ERROR";
    }, {
        health_check_interval?: number | undefined;
        status_poll_interval?: number | undefined;
        auto_restart?: boolean | undefined;
        log_level?: "DEBUG" | "INFO" | "WARNING" | "ERROR" | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    general: {
        health_check_interval: number;
        status_poll_interval: number;
        auto_restart: boolean;
        log_level: "DEBUG" | "INFO" | "WARNING" | "ERROR";
    };
    file_parser: {
        poll_interval: number;
        max_file_size: number;
        chunk_size: number;
        supported_formats: string[];
    };
    backtest_runner: {
        max_concurrent: number;
        poll_interval: number;
        timeout_hours: number;
    };
    dgm_monitor: {
        enabled: boolean;
        max_concurrent: number;
        poll_interval: number;
        timeout_hours: number;
        fitness_threshold: number;
    };
}, {
    general: {
        health_check_interval?: number | undefined;
        status_poll_interval?: number | undefined;
        auto_restart?: boolean | undefined;
        log_level?: "DEBUG" | "INFO" | "WARNING" | "ERROR" | undefined;
    };
    file_parser: {
        poll_interval?: number | undefined;
        max_file_size?: number | undefined;
        chunk_size?: number | undefined;
        supported_formats?: string[] | undefined;
    };
    backtest_runner: {
        max_concurrent?: number | undefined;
        poll_interval?: number | undefined;
        timeout_hours?: number | undefined;
    };
    dgm_monitor: {
        enabled?: boolean | undefined;
        max_concurrent?: number | undefined;
        poll_interval?: number | undefined;
        timeout_hours?: number | undefined;
        fitness_threshold?: number | undefined;
    };
}>;
type WorkerConfig = z.infer<typeof WorkerConfigSchema>;
export declare const SystemResourceUsageSchema: z.ZodObject<{
    cpu_percent: z.ZodNumber;
    memory_percent: z.ZodNumber;
    memory_used_mb: z.ZodNumber;
    disk_usage_percent: z.ZodNumber;
    active_connections: z.ZodNumber;
    timestamp: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    timestamp: Date;
    cpu_percent: number;
    memory_percent: number;
    memory_used_mb: number;
    disk_usage_percent: number;
    active_connections: number;
}, {
    timestamp: Date;
    cpu_percent: number;
    memory_percent: number;
    memory_used_mb: number;
    disk_usage_percent: number;
    active_connections: number;
}>;
type SystemResourceUsage = z.infer<typeof SystemResourceUsageSchema>;
export declare const WorkerSchemas: {
    WorkerStatus: z.ZodObject<{
        name: z.ZodString;
        status: z.ZodEnum<["running", "stopped", "error", "restarting"]>;
        pid: z.ZodOptional<z.ZodNumber>;
        start_time: z.ZodOptional<z.ZodDate>;
        last_activity: z.ZodOptional<z.ZodDate>;
        error_message: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        status: "error" | "running" | "stopped" | "restarting";
        name: string;
        error_message?: string | undefined;
        last_activity?: Date | undefined;
        pid?: number | undefined;
        start_time?: Date | undefined;
    }, {
        status: "error" | "running" | "stopped" | "restarting";
        name: string;
        error_message?: string | undefined;
        last_activity?: Date | undefined;
        pid?: number | undefined;
        start_time?: Date | undefined;
    }>;
    WorkerStats: z.ZodObject<{
        manager_status: z.ZodEnum<["running", "stopped", "starting", "stopping"]>;
        uptime_seconds: z.ZodNumber;
        start_time: z.ZodString;
        active_workers: z.ZodNumber;
        active_tasks: z.ZodNumber;
        worker_stats: z.ZodRecord<z.ZodString, z.ZodObject<{
            status: z.ZodString;
            running_jobs: z.ZodOptional<z.ZodNumber>;
            max_concurrent: z.ZodOptional<z.ZodNumber>;
            poll_interval: z.ZodOptional<z.ZodNumber>;
            job_ids: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        }, "strip", z.ZodTypeAny, {
            status: string;
            running_jobs?: number | undefined;
            max_concurrent?: number | undefined;
            poll_interval?: number | undefined;
            job_ids?: string[] | undefined;
        }, {
            status: string;
            running_jobs?: number | undefined;
            max_concurrent?: number | undefined;
            poll_interval?: number | undefined;
            job_ids?: string[] | undefined;
        }>>;
    }, "strip", z.ZodTypeAny, {
        start_time: string;
        manager_status: "running" | "stopped" | "starting" | "stopping";
        uptime_seconds: number;
        active_workers: number;
        active_tasks: number;
        worker_stats: Record<string, {
            status: string;
            running_jobs?: number | undefined;
            max_concurrent?: number | undefined;
            poll_interval?: number | undefined;
            job_ids?: string[] | undefined;
        }>;
    }, {
        start_time: string;
        manager_status: "running" | "stopped" | "starting" | "stopping";
        uptime_seconds: number;
        active_workers: number;
        active_tasks: number;
        worker_stats: Record<string, {
            status: string;
            running_jobs?: number | undefined;
            max_concurrent?: number | undefined;
            poll_interval?: number | undefined;
            job_ids?: string[] | undefined;
        }>;
    }>;
    WorkerHealthCheck: z.ZodObject<{
        healthy: z.ZodBoolean;
        timestamp: z.ZodDate;
        workers: z.ZodRecord<z.ZodString, z.ZodObject<{
            healthy: z.ZodBoolean;
            status: z.ZodOptional<z.ZodString>;
            reason: z.ZodOptional<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            healthy: boolean;
            status?: string | undefined;
            reason?: string | undefined;
        }, {
            healthy: boolean;
            status?: string | undefined;
            reason?: string | undefined;
        }>>;
        uptime: z.ZodNumber;
        error: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        healthy: boolean;
        timestamp: Date;
        uptime: number;
        workers: Record<string, {
            healthy: boolean;
            status?: string | undefined;
            reason?: string | undefined;
        }>;
        error?: string | undefined;
    }, {
        healthy: boolean;
        timestamp: Date;
        uptime: number;
        workers: Record<string, {
            healthy: boolean;
            status?: string | undefined;
            reason?: string | undefined;
        }>;
        error?: string | undefined;
    }>;
    FileUploadSession: z.ZodObject<{
        id: z.ZodString;
        user_id: z.ZodString;
        original_filename: z.ZodString;
        file_size: z.ZodNumber;
        status: z.ZodEnum<["pending", "mapping_confirmed", "parsing_in_progress", "ready", "error"]>;
        final_mapping: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodString>>;
        timezone: z.ZodOptional<z.ZodString>;
        rows_processed: z.ZodOptional<z.ZodNumber>;
        error_message: z.ZodOptional<z.ZodString>;
        created_at: z.ZodDate;
        updated_at: z.ZodDate;
        temporary_file_path: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        status: "error" | "pending" | "ready" | "mapping_confirmed" | "parsing_in_progress";
        id: string;
        user_id: string;
        created_at: Date;
        updated_at: Date;
        original_filename: string;
        file_size: number;
        timezone?: string | undefined;
        error_message?: string | undefined;
        rows_processed?: number | undefined;
        final_mapping?: Record<string, string> | undefined;
        temporary_file_path?: string | undefined;
    }, {
        status: "error" | "pending" | "ready" | "mapping_confirmed" | "parsing_in_progress";
        id: string;
        user_id: string;
        created_at: Date;
        updated_at: Date;
        original_filename: string;
        file_size: number;
        timezone?: string | undefined;
        error_message?: string | undefined;
        rows_processed?: number | undefined;
        final_mapping?: Record<string, string> | undefined;
        temporary_file_path?: string | undefined;
    }>;
    BacktestJob: z.ZodObject<{
        id: z.ZodString;
        user_id: z.ZodString;
        name: z.ZodString;
        status: z.ZodEnum<["pending", "running", "completed", "error", "cancelled"]>;
        symbol: z.ZodString;
        start_date: z.ZodDate;
        end_date: z.ZodDate;
        strategy_config: z.ZodRecord<z.ZodString, z.ZodAny>;
        progress: z.ZodOptional<z.ZodNumber>;
        started_at: z.ZodOptional<z.ZodDate>;
        completed_at: z.ZodOptional<z.ZodDate>;
        error_message: z.ZodOptional<z.ZodString>;
        results: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
        created_at: z.ZodDate;
    }, "strip", z.ZodTypeAny, {
        symbol: string;
        status: "error" | "running" | "pending" | "completed" | "cancelled";
        name: string;
        id: string;
        user_id: string;
        start_date: Date;
        end_date: Date;
        strategy_config: Record<string, any>;
        created_at: Date;
        progress?: number | undefined;
        started_at?: Date | undefined;
        completed_at?: Date | undefined;
        error_message?: string | undefined;
        results?: Record<string, any> | undefined;
    }, {
        symbol: string;
        status: "error" | "running" | "pending" | "completed" | "cancelled";
        name: string;
        id: string;
        user_id: string;
        start_date: Date;
        end_date: Date;
        strategy_config: Record<string, any>;
        created_at: Date;
        progress?: number | undefined;
        started_at?: Date | undefined;
        completed_at?: Date | undefined;
        error_message?: string | undefined;
        results?: Record<string, any> | undefined;
    }>;
    DGMExperiment: z.ZodObject<{
        id: z.ZodString;
        user_id: z.ZodString;
        experiment_name: z.ZodString;
        status: z.ZodEnum<["pending", "running", "completed", "error", "deployed"]>;
        base_strategy: z.ZodRecord<z.ZodString, z.ZodAny>;
        generated_strategy: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
        fitness_improvement: z.ZodOptional<z.ZodNumber>;
        deployed_backtest_id: z.ZodOptional<z.ZodString>;
        started_at: z.ZodOptional<z.ZodDate>;
        completed_at: z.ZodOptional<z.ZodDate>;
        error_message: z.ZodOptional<z.ZodString>;
        created_at: z.ZodDate;
    }, "strip", z.ZodTypeAny, {
        status: "error" | "running" | "pending" | "completed" | "deployed";
        id: string;
        user_id: string;
        created_at: Date;
        experiment_name: string;
        base_strategy: Record<string, any>;
        started_at?: Date | undefined;
        completed_at?: Date | undefined;
        error_message?: string | undefined;
        generated_strategy?: Record<string, any> | undefined;
        fitness_improvement?: number | undefined;
        deployed_backtest_id?: string | undefined;
    }, {
        status: "error" | "running" | "pending" | "completed" | "deployed";
        id: string;
        user_id: string;
        created_at: Date;
        experiment_name: string;
        base_strategy: Record<string, any>;
        started_at?: Date | undefined;
        completed_at?: Date | undefined;
        error_message?: string | undefined;
        generated_strategy?: Record<string, any> | undefined;
        fitness_improvement?: number | undefined;
        deployed_backtest_id?: string | undefined;
    }>;
    WorkerManagementRequest: z.ZodObject<{
        action: z.ZodEnum<["status", "health", "restart", "stats"]>;
        worker_name: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDate;
        request_id: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        action: "status" | "health" | "restart" | "stats";
        timestamp: Date;
        request_id: string;
        worker_name?: string | undefined;
    }, {
        action: "status" | "health" | "restart" | "stats";
        timestamp: Date;
        request_id: string;
        worker_name?: string | undefined;
    }>;
    WorkerManagementResponse: z.ZodObject<{
        success: z.ZodBoolean;
        data: z.ZodOptional<z.ZodAny>;
        error: z.ZodOptional<z.ZodObject<{
            code: z.ZodString;
            message: z.ZodString;
            details: z.ZodOptional<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            code: string;
            message: string;
            details?: string | undefined;
        }, {
            code: string;
            message: string;
            details?: string | undefined;
        }>>;
        timestamp: z.ZodDate;
        request_id: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        success: boolean;
        timestamp: Date;
        request_id: string;
        data?: any;
        error?: {
            code: string;
            message: string;
            details?: string | undefined;
        } | undefined;
    }, {
        success: boolean;
        timestamp: Date;
        request_id: string;
        data?: any;
        error?: {
            code: string;
            message: string;
            details?: string | undefined;
        } | undefined;
    }>;
    WorkerBridgeStatus: z.ZodObject<{
        bridge_healthy: z.ZodBoolean;
        python_workers_healthy: z.ZodBoolean;
        last_health_check: z.ZodOptional<z.ZodDate>;
        last_known_status: z.ZodOptional<z.ZodObject<{
            manager_status: z.ZodEnum<["running", "stopped", "starting", "stopping"]>;
            uptime_seconds: z.ZodNumber;
            start_time: z.ZodString;
            active_workers: z.ZodNumber;
            active_tasks: z.ZodNumber;
            worker_stats: z.ZodRecord<z.ZodString, z.ZodObject<{
                status: z.ZodString;
                running_jobs: z.ZodOptional<z.ZodNumber>;
                max_concurrent: z.ZodOptional<z.ZodNumber>;
                poll_interval: z.ZodOptional<z.ZodNumber>;
                job_ids: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
            }, "strip", z.ZodTypeAny, {
                status: string;
                running_jobs?: number | undefined;
                max_concurrent?: number | undefined;
                poll_interval?: number | undefined;
                job_ids?: string[] | undefined;
            }, {
                status: string;
                running_jobs?: number | undefined;
                max_concurrent?: number | undefined;
                poll_interval?: number | undefined;
                job_ids?: string[] | undefined;
            }>>;
        }, "strip", z.ZodTypeAny, {
            start_time: string;
            manager_status: "running" | "stopped" | "starting" | "stopping";
            uptime_seconds: number;
            active_workers: number;
            active_tasks: number;
            worker_stats: Record<string, {
                status: string;
                running_jobs?: number | undefined;
                max_concurrent?: number | undefined;
                poll_interval?: number | undefined;
                job_ids?: string[] | undefined;
            }>;
        }, {
            start_time: string;
            manager_status: "running" | "stopped" | "starting" | "stopping";
            uptime_seconds: number;
            active_workers: number;
            active_tasks: number;
            worker_stats: Record<string, {
                status: string;
                running_jobs?: number | undefined;
                max_concurrent?: number | undefined;
                poll_interval?: number | undefined;
                job_ids?: string[] | undefined;
            }>;
        }>>;
        monitoring_config: z.ZodObject<{
            health_check_interval: z.ZodNumber;
            status_poll_interval: z.ZodNumber;
            auto_restart: z.ZodBoolean;
        }, "strip", z.ZodTypeAny, {
            health_check_interval: number;
            status_poll_interval: number;
            auto_restart: boolean;
        }, {
            health_check_interval: number;
            status_poll_interval: number;
            auto_restart: boolean;
        }>;
        active_jobs: z.ZodObject<{
            file_parsing: z.ZodNumber;
            backtests: z.ZodNumber;
            dgm_experiments: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            backtests: number;
            file_parsing: number;
            dgm_experiments: number;
        }, {
            backtests: number;
            file_parsing: number;
            dgm_experiments: number;
        }>;
    }, "strip", z.ZodTypeAny, {
        bridge_healthy: boolean;
        python_workers_healthy: boolean;
        monitoring_config: {
            health_check_interval: number;
            status_poll_interval: number;
            auto_restart: boolean;
        };
        active_jobs: {
            backtests: number;
            file_parsing: number;
            dgm_experiments: number;
        };
        last_health_check?: Date | undefined;
        last_known_status?: {
            start_time: string;
            manager_status: "running" | "stopped" | "starting" | "stopping";
            uptime_seconds: number;
            active_workers: number;
            active_tasks: number;
            worker_stats: Record<string, {
                status: string;
                running_jobs?: number | undefined;
                max_concurrent?: number | undefined;
                poll_interval?: number | undefined;
                job_ids?: string[] | undefined;
            }>;
        } | undefined;
    }, {
        bridge_healthy: boolean;
        python_workers_healthy: boolean;
        monitoring_config: {
            health_check_interval: number;
            status_poll_interval: number;
            auto_restart: boolean;
        };
        active_jobs: {
            backtests: number;
            file_parsing: number;
            dgm_experiments: number;
        };
        last_health_check?: Date | undefined;
        last_known_status?: {
            start_time: string;
            manager_status: "running" | "stopped" | "starting" | "stopping";
            uptime_seconds: number;
            active_workers: number;
            active_tasks: number;
            worker_stats: Record<string, {
                status: string;
                running_jobs?: number | undefined;
                max_concurrent?: number | undefined;
                poll_interval?: number | undefined;
                job_ids?: string[] | undefined;
            }>;
        } | undefined;
    }>;
    FileProcessingProgress: z.ZodObject<{
        session_id: z.ZodString;
        status: z.ZodEnum<["pending", "parsing", "validating", "inserting", "completed", "error"]>;
        progress_percent: z.ZodNumber;
        rows_processed: z.ZodNumber;
        total_rows: z.ZodOptional<z.ZodNumber>;
        current_step: z.ZodString;
        error_message: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDate;
    }, "strip", z.ZodTypeAny, {
        status: "error" | "pending" | "completed" | "parsing" | "validating" | "inserting";
        timestamp: Date;
        session_id: string;
        rows_processed: number;
        current_step: string;
        progress_percent: number;
        error_message?: string | undefined;
        total_rows?: number | undefined;
    }, {
        status: "error" | "pending" | "completed" | "parsing" | "validating" | "inserting";
        timestamp: Date;
        session_id: string;
        rows_processed: number;
        current_step: string;
        progress_percent: number;
        error_message?: string | undefined;
        total_rows?: number | undefined;
    }>;
    BacktestProgress: z.ZodObject<{
        backtest_id: z.ZodString;
        progress: z.ZodNumber;
        current_date: z.ZodOptional<z.ZodDate>;
        trades_executed: z.ZodNumber;
        current_balance: z.ZodOptional<z.ZodNumber>;
        status: z.ZodEnum<["pending", "running", "completed", "error"]>;
        message: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        status: "error" | "running" | "pending" | "completed";
        progress: number;
        backtest_id: string;
        trades_executed: number;
        message?: string | undefined;
        current_date?: Date | undefined;
        current_balance?: number | undefined;
    }, {
        status: "error" | "running" | "pending" | "completed";
        progress: number;
        backtest_id: string;
        trades_executed: number;
        message?: string | undefined;
        current_date?: Date | undefined;
        current_balance?: number | undefined;
    }>;
    DGMExperimentProgress: z.ZodObject<{
        experiment_id: z.ZodString;
        status: z.ZodEnum<["pending", "initializing", "evolving", "testing", "completed", "error"]>;
        progress_percent: z.ZodNumber;
        current_generation: z.ZodOptional<z.ZodNumber>;
        total_generations: z.ZodOptional<z.ZodNumber>;
        best_fitness: z.ZodOptional<z.ZodNumber>;
        fitness_improvement: z.ZodOptional<z.ZodNumber>;
        current_step: z.ZodString;
        elapsed_time: z.ZodOptional<z.ZodNumber>;
        estimated_completion: z.ZodOptional<z.ZodDate>;
        error_message: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodDate;
    }, "strip", z.ZodTypeAny, {
        status: "error" | "pending" | "completed" | "initializing" | "evolving" | "testing";
        timestamp: Date;
        current_step: string;
        progress_percent: number;
        experiment_id: string;
        error_message?: string | undefined;
        estimated_completion?: Date | undefined;
        fitness_improvement?: number | undefined;
        current_generation?: number | undefined;
        total_generations?: number | undefined;
        best_fitness?: number | undefined;
        elapsed_time?: number | undefined;
    }, {
        status: "error" | "pending" | "completed" | "initializing" | "evolving" | "testing";
        timestamp: Date;
        current_step: string;
        progress_percent: number;
        experiment_id: string;
        error_message?: string | undefined;
        estimated_completion?: Date | undefined;
        fitness_improvement?: number | undefined;
        current_generation?: number | undefined;
        total_generations?: number | undefined;
        best_fitness?: number | undefined;
        elapsed_time?: number | undefined;
    }>;
    WorkerEvent: z.ZodObject<{
        event_type: z.ZodEnum<["worker_started", "worker_stopped", "worker_error", "worker_restarted", "job_started", "job_completed", "job_failed", "job_progress", "health_check", "status_update"]>;
        worker_name: z.ZodOptional<z.ZodString>;
        job_id: z.ZodOptional<z.ZodString>;
        job_type: z.ZodOptional<z.ZodEnum<["file_parsing", "backtest", "dgm_experiment"]>>;
        data: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
        timestamp: z.ZodDate;
    }, "strip", z.ZodTypeAny, {
        timestamp: Date;
        event_type: "worker_started" | "worker_stopped" | "worker_error" | "worker_restarted" | "job_started" | "job_completed" | "job_failed" | "job_progress" | "health_check" | "status_update";
        data?: Record<string, any> | undefined;
        worker_name?: string | undefined;
        job_id?: string | undefined;
        job_type?: "backtest" | "file_parsing" | "dgm_experiment" | undefined;
    }, {
        timestamp: Date;
        event_type: "worker_started" | "worker_stopped" | "worker_error" | "worker_restarted" | "job_started" | "job_completed" | "job_failed" | "job_progress" | "health_check" | "status_update";
        data?: Record<string, any> | undefined;
        worker_name?: string | undefined;
        job_id?: string | undefined;
        job_type?: "backtest" | "file_parsing" | "dgm_experiment" | undefined;
    }>;
    WorkerConfig: z.ZodObject<{
        file_parser: z.ZodObject<{
            poll_interval: z.ZodDefault<z.ZodNumber>;
            max_file_size: z.ZodDefault<z.ZodNumber>;
            chunk_size: z.ZodDefault<z.ZodNumber>;
            supported_formats: z.ZodDefault<z.ZodArray<z.ZodString, "many">>;
        }, "strip", z.ZodTypeAny, {
            poll_interval: number;
            max_file_size: number;
            chunk_size: number;
            supported_formats: string[];
        }, {
            poll_interval?: number | undefined;
            max_file_size?: number | undefined;
            chunk_size?: number | undefined;
            supported_formats?: string[] | undefined;
        }>;
        backtest_runner: z.ZodObject<{
            poll_interval: z.ZodDefault<z.ZodNumber>;
            max_concurrent: z.ZodDefault<z.ZodNumber>;
            timeout_hours: z.ZodDefault<z.ZodNumber>;
        }, "strip", z.ZodTypeAny, {
            max_concurrent: number;
            poll_interval: number;
            timeout_hours: number;
        }, {
            max_concurrent?: number | undefined;
            poll_interval?: number | undefined;
            timeout_hours?: number | undefined;
        }>;
        dgm_monitor: z.ZodObject<{
            poll_interval: z.ZodDefault<z.ZodNumber>;
            max_concurrent: z.ZodDefault<z.ZodNumber>;
            enabled: z.ZodDefault<z.ZodBoolean>;
            fitness_threshold: z.ZodDefault<z.ZodNumber>;
            timeout_hours: z.ZodDefault<z.ZodNumber>;
        }, "strip", z.ZodTypeAny, {
            enabled: boolean;
            max_concurrent: number;
            poll_interval: number;
            timeout_hours: number;
            fitness_threshold: number;
        }, {
            enabled?: boolean | undefined;
            max_concurrent?: number | undefined;
            poll_interval?: number | undefined;
            timeout_hours?: number | undefined;
            fitness_threshold?: number | undefined;
        }>;
        general: z.ZodObject<{
            log_level: z.ZodDefault<z.ZodEnum<["DEBUG", "INFO", "WARNING", "ERROR"]>>;
            health_check_interval: z.ZodDefault<z.ZodNumber>;
            status_poll_interval: z.ZodDefault<z.ZodNumber>;
            auto_restart: z.ZodDefault<z.ZodBoolean>;
        }, "strip", z.ZodTypeAny, {
            health_check_interval: number;
            status_poll_interval: number;
            auto_restart: boolean;
            log_level: "DEBUG" | "INFO" | "WARNING" | "ERROR";
        }, {
            health_check_interval?: number | undefined;
            status_poll_interval?: number | undefined;
            auto_restart?: boolean | undefined;
            log_level?: "DEBUG" | "INFO" | "WARNING" | "ERROR" | undefined;
        }>;
    }, "strip", z.ZodTypeAny, {
        general: {
            health_check_interval: number;
            status_poll_interval: number;
            auto_restart: boolean;
            log_level: "DEBUG" | "INFO" | "WARNING" | "ERROR";
        };
        file_parser: {
            poll_interval: number;
            max_file_size: number;
            chunk_size: number;
            supported_formats: string[];
        };
        backtest_runner: {
            max_concurrent: number;
            poll_interval: number;
            timeout_hours: number;
        };
        dgm_monitor: {
            enabled: boolean;
            max_concurrent: number;
            poll_interval: number;
            timeout_hours: number;
            fitness_threshold: number;
        };
    }, {
        general: {
            health_check_interval?: number | undefined;
            status_poll_interval?: number | undefined;
            auto_restart?: boolean | undefined;
            log_level?: "DEBUG" | "INFO" | "WARNING" | "ERROR" | undefined;
        };
        file_parser: {
            poll_interval?: number | undefined;
            max_file_size?: number | undefined;
            chunk_size?: number | undefined;
            supported_formats?: string[] | undefined;
        };
        backtest_runner: {
            max_concurrent?: number | undefined;
            poll_interval?: number | undefined;
            timeout_hours?: number | undefined;
        };
        dgm_monitor: {
            enabled?: boolean | undefined;
            max_concurrent?: number | undefined;
            poll_interval?: number | undefined;
            timeout_hours?: number | undefined;
            fitness_threshold?: number | undefined;
        };
    }>;
    SystemResourceUsage: z.ZodObject<{
        cpu_percent: z.ZodNumber;
        memory_percent: z.ZodNumber;
        memory_used_mb: z.ZodNumber;
        disk_usage_percent: z.ZodNumber;
        active_connections: z.ZodNumber;
        timestamp: z.ZodDate;
    }, "strip", z.ZodTypeAny, {
        timestamp: Date;
        cpu_percent: number;
        memory_percent: number;
        memory_used_mb: number;
        disk_usage_percent: number;
        active_connections: number;
    }, {
        timestamp: Date;
        cpu_percent: number;
        memory_percent: number;
        memory_used_mb: number;
        disk_usage_percent: number;
        active_connections: number;
    }>;
};
export type { WorkerStatus, WorkerStats, WorkerHealthCheck, FileUploadSession, BacktestJob, DGMExperiment, WorkerManagementRequest, WorkerManagementResponse, WorkerBridgeStatus, FileProcessingProgress, DGMExperimentProgress, WorkerEvent, WorkerConfig, SystemResourceUsage, };
//# sourceMappingURL=workers.d.ts.map