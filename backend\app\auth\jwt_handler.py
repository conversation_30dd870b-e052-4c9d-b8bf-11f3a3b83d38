"""
JWT Handler for authentication
"""

import os
import time
import jwt
from typing import Dict
from datetime import datetime, timedelta
from fastapi import Request, HTTPException, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from ..db.config import get_db
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Secret key for JWT
SECRET_KEY = os.getenv("JWT_SECRET", "your_secret_key_here")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))

def token_response(token: str):
    """Return token response"""
    return {
        "access_token": token,
        "token_type": "bearer"
    }

def create_access_token(data: Dict, expires_delta: timedelta = None):
    """Create access token"""
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    
    return encoded_jwt

def decode_token(token: str):
    """Decode token"""
    try:
        decoded_token = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        if decoded_token["exp"] >= time.time():
            return decoded_token
        return None
    except Exception:
        return None

class JWTBearer(HTTPBearer):
    """JWT Bearer authentication"""
    def __init__(self, auto_error: bool = True, required_roles: list = None):
        super(JWTBearer, self).__init__(auto_error=auto_error)
        self.required_roles = required_roles
    
    async def __call__(self, request: Request, db: AsyncSession = Depends(get_db)):
        """Validate token"""
        credentials: HTTPAuthorizationCredentials = await super(JWTBearer, self).__call__(request)
        
        if credentials:
            if not credentials.scheme == "Bearer":
                raise HTTPException(status_code=403, detail="Invalid authentication scheme.")
            
            payload = self.verify_jwt(credentials.credentials)
            if not payload:
                raise HTTPException(status_code=403, detail="Invalid token or expired token.")
            
            # Check role if required
            if self.required_roles and "role" in payload:
                if payload["role"] not in self.required_roles:
                    raise HTTPException(status_code=403, detail="Insufficient permissions.")
            
            # Get user from database
            from ..db.crud.user import get_user_by_email
            user = await get_user_by_email(db, payload["sub"])
            if not user:
                raise HTTPException(status_code=403, detail="User not found.")
            
            # Add user to request state
            request.state.user = user
            request.state.token_data = payload
            
            return credentials.credentials
        else:
            raise HTTPException(status_code=403, detail="Invalid authorization code.")
    
    def verify_jwt(self, jwtoken: str):
        """Verify JWT token"""
        try:
            payload = decode_token(jwtoken)
            return payload
        except:
            return None