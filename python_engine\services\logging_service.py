"""
Logging Service Implementation

Concrete implementation of ILoggingService with dependency injection support.
"""

import logging
import logging.handlers
import json
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path

from core.interfaces import ILoggingService, IConfigurationService, Order

class FileLoggingService(ILoggingService):
    """File-based logging service"""
    
    def __init__(self, config_service: IConfigurationService):
        self.config = config_service
        self._setup_logging()
    
    def _setup_logging(self):
        """Setup logging configuration"""
        log_level = self.config.get_config('logging.level', 'INFO')
        log_file = self.config.get_config('logging.file', 'trading.log')
        max_size = self.config.get_config('logging.max_size', 10) * 1024 * 1024  # MB to bytes
        backup_count = self.config.get_config('logging.backup_count', 5)
        
        # Create logger
        self.logger = logging.getLogger('trading_platform')
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # File handler with rotation
        file_handler = logging.handlers.RotatingFileHandler(
            log_file, maxBytes=max_size, backupCount=backup_count
        )
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # Trade-specific logger
        self.trade_logger = logging.getLogger('trading_platform.trades')
        self.trade_logger.setLevel(logging.INFO)
        
        # Trade file handler
        trade_handler = logging.handlers.RotatingFileHandler(
            'trades.log', maxBytes=max_size, backupCount=backup_count
        )
        trade_formatter = logging.Formatter('%(asctime)s - %(message)s')
        trade_handler.setFormatter(trade_formatter)
        self.trade_logger.addHandler(trade_handler)
    
    def log_info(self, message: str, **kwargs) -> None:
        """Log info message"""
        extra_info = self._format_extra_info(kwargs)
        self.logger.info(f"{message}{extra_info}")
    
    def log_warning(self, message: str, **kwargs) -> None:
        """Log warning message"""
        extra_info = self._format_extra_info(kwargs)
        self.logger.warning(f"{message}{extra_info}")
    
    def log_error(self, message: str, **kwargs) -> None:
        """Log error message"""
        extra_info = self._format_extra_info(kwargs)
        self.logger.error(f"{message}{extra_info}")
    
    def log_trade(self, order: Order) -> None:
        """Log trade execution"""
        trade_info = {
            'order_id': order.id,
            'symbol': order.symbol,
            'side': order.side,
            'quantity': order.quantity,
            'price': order.price,
            'order_type': order.order_type,
            'status': order.status,
            'timestamp': order.timestamp.isoformat()
        }
        
        self.trade_logger.info(json.dumps(trade_info))
    
    def _format_extra_info(self, kwargs: Dict[str, Any]) -> str:
        """Format extra information for logging"""
        if not kwargs:
            return ""
        
        try:
            return f" | {json.dumps(kwargs)}"
        except (TypeError, ValueError):
            return f" | {str(kwargs)}"

class ConsoleLoggingService(ILoggingService):
    """Console-only logging service"""
    
    def __init__(self, config_service: IConfigurationService):
        self.config = config_service
        self.log_level = self.config.get_config('logging.level', 'INFO').upper()
    
    def _should_log(self, level: str) -> bool:
        """Check if message should be logged based on level"""
        levels = {'DEBUG': 10, 'INFO': 20, 'WARNING': 30, 'ERROR': 40}
        return levels.get(level, 20) >= levels.get(self.log_level, 20)
    
    def log_info(self, message: str, **kwargs) -> None:
        """Log info message to console"""
        if self._should_log('INFO'):
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            extra = f" | {kwargs}" if kwargs else ""
            print(f"[{timestamp}] INFO: {message}{extra}")
    
    def log_warning(self, message: str, **kwargs) -> None:
        """Log warning message to console"""
        if self._should_log('WARNING'):
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            extra = f" | {kwargs}" if kwargs else ""
            print(f"[{timestamp}] WARNING: {message}{extra}")
    
    def log_error(self, message: str, **kwargs) -> None:
        """Log error message to console"""
        if self._should_log('ERROR'):
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            extra = f" | {kwargs}" if kwargs else ""
            print(f"[{timestamp}] ERROR: {message}{extra}")
    
    def log_trade(self, order: Order) -> None:
        """Log trade execution to console"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] TRADE: {order.side.upper()} {order.quantity} {order.symbol} @ ${order.price:.2f} [{order.status}]")

class MockLoggingService(ILoggingService):
    """Mock logging service for testing"""
    
    def __init__(self, config_service: IConfigurationService = None):
        self.config = config_service
        self.logs = []
        self.trades = []
    
    def log_info(self, message: str, **kwargs) -> None:
        """Log info message to memory"""
        self.logs.append({
            'level': 'INFO',
            'message': message,
            'kwargs': kwargs,
            'timestamp': datetime.now()
        })
    
    def log_warning(self, message: str, **kwargs) -> None:
        """Log warning message to memory"""
        self.logs.append({
            'level': 'WARNING',
            'message': message,
            'kwargs': kwargs,
            'timestamp': datetime.now()
        })
    
    def log_error(self, message: str, **kwargs) -> None:
        """Log error message to memory"""
        self.logs.append({
            'level': 'ERROR',
            'message': message,
            'kwargs': kwargs,
            'timestamp': datetime.now()
        })
    
    def log_trade(self, order: Order) -> None:
        """Log trade execution to memory"""
        self.trades.append(order)
    
    def get_logs(self, level: Optional[str] = None) -> list:
        """Get logged messages"""
        if level:
            return [log for log in self.logs if log['level'] == level]
        return self.logs.copy()
    
    def get_trades(self) -> list:
        """Get logged trades"""
        return self.trades.copy()
    
    def clear_logs(self):
        """Clear all logs"""
        self.logs.clear()
        self.trades.clear()