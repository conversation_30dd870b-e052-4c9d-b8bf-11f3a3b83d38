# src/trading/mt5_bridge_tdd.py
"""
MT5 Bridge implementation developed using TDD approach
"""

import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from enum import Enum

# Mock MetaTrader5 for offline development
try:
    import MetaTrader5 as mt5
    MT5_AVAILABLE = True
except ImportError:
    MT5_AVAILABLE = False
    # Create a mock MT5 module for testing
    class MockMT5:
        @staticmethod
        def initialize():
            return True
        
        @staticmethod
        def shutdown():
            pass
        
        @staticmethod
        def order_send(request):
            return None
        
        @staticmethod
        def positions_get():
            return []
        
        @staticmethod
        def orders_get():
            return []
        
        @staticmethod
        def symbol_info(symbol):
            return None
        
        @staticmethod
        def last_error():
            return (0, "No error")
    
    mt5 = MockMT5()

logger = logging.getLogger(__name__)


class MT5BridgeException(Exception):
    """Custom exception for MT5 Bridge operations"""
    pass


class OrderType(Enum):
    """Order types supported by MT5"""
    BUY = "BUY"
    SELL = "SELL"
    BUY_LIMIT = "BUY_LIMIT"
    SELL_LIMIT = "SELL_LIMIT"
    BUY_STOP = "BUY_STOP"
    SELL_STOP = "SELL_STOP"


class MT5Bridge:
    """
    MT5 Bridge implementation with TDD approach
    Provides a clean interface to interact with MetaTrader 5
    """
    
    def __init__(self, offline_mode: bool = False):
        """
        Initialize MT5 Bridge
        
        Args:
            offline_mode: Force offline mode for testing
        """
        self.offline_mode = offline_mode or not MT5_AVAILABLE
        self.connected = False
        self.orders = []
        self.next_order_id = 1
        self._simulate_error = False
        
        # Initialize connection (both in offline and online mode)
        self.connect()
    
    def connect(self) -> bool:
        """
        Connect to MT5 terminal
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        if self.offline_mode:
            self.connected = True
            logger.info("Connected in offline mode")
            return True
        
        try:
            # Initialize MT5 connection
            if not mt5.initialize():
                error = mt5.last_error()
                logger.error(f"Failed to connect to MT5: {error}")
                return False
            
            self.connected = True
            logger.info("Successfully connected to MT5 terminal")
            return True
            
        except Exception as e:
            logger.error(f"Connection error: {str(e)}")
            self.connected = False
            return False
    
    def disconnect(self) -> None:
        """Disconnect from MT5 terminal"""
        if not self.offline_mode and self.connected:
            mt5.shutdown()
        
        self.connected = False
        logger.info("Disconnected from MT5")
    
    def is_connected(self) -> bool:
        """Check if connected to MT5"""
        return self.connected
    
    def place_order(self, 
                   symbol: str, 
                   order_type: str, 
                   lot: float, 
                   price: Optional[float] = None,
                   stop_loss: Optional[float] = None, 
                   take_profit: Optional[float] = None) -> int:
        """
        Place a trading order
        
        Args:
            symbol: Trading symbol (e.g., "EURUSD")
            order_type: Order type ("BUY", "SELL", etc.) - case insensitive
            lot: Order volume
            price: Order price (for pending orders)
            stop_loss: Stop loss price
            take_profit: Take profit price
            
        Returns:
            int: Order ID
            
        Raises:
            ValueError: If inputs are invalid
            Exception: If API error occurs
        """
        # Check for simulated error
        if hasattr(self, '_simulate_error') and self._simulate_error:
            raise Exception("API error")
            
        # Ensure we're connected
        if not self.is_connected():
            self.connect()
        
        # Validate inputs
        if not symbol or symbol == "INVALID":
            raise ValueError("Invalid symbol")
        
        if lot <= 0:
            raise ValueError("Invalid lot size")
        
        # Normalize order type to uppercase for consistency
        order_type = order_type.upper() if isinstance(order_type, str) else order_type
        
        try:
            # In offline mode, simulate order placement
            if self.offline_mode:
                return self._place_order_offline(symbol, order_type, lot, price, stop_loss, take_profit)
            
            # In live mode, place order through MT5 API
            return self._place_order_live(symbol, order_type, lot, price, stop_loss, take_profit)
            
        except Exception as e:
            logger.error(f"Order placement error: {str(e)}")
            raise
    
    def _place_order_offline(self, 
                           symbol: str, 
                           order_type: str, 
                           lot: float, 
                           price: Optional[float] = None,
                           stop_loss: Optional[float] = None, 
                           take_profit: Optional[float] = None) -> int:
        """Place order in offline mode (simulation)"""
        order_id = self.next_order_id
        self.next_order_id += 1
        
        # Create order record
        order = {
            "id": order_id,
            "symbol": symbol,
            "type": order_type,
            "lot": lot,
            "price": price,
            "stop_loss": stop_loss,
            "take_profit": take_profit,
            "status": "filled",
            "time": datetime.now()
        }
        
        self.orders.append(order)
        logger.info(f"Order placed (offline): {order_id}")
        
        return order_id
    
    def _place_order_live(self, 
                        symbol: str, 
                        order_type: str, 
                        lot: float, 
                        price: Optional[float] = None,
                        stop_loss: Optional[float] = None, 
                        take_profit: Optional[float] = None) -> int:
        """Place order in live MT5"""
        # Convert order type to MT5 format
        mt5_order_type = self._convert_order_type(order_type)
        
        # Create order request
        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": symbol,
            "volume": lot,
            "type": mt5_order_type,
            "price": price,
            "sl": stop_loss,
            "tp": take_profit,
            "deviation": 20,
            "magic": 123456,
            "comment": f"Python MT5 Bridge {order_type}",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_IOC,
        }
        
        # Send order
        result = mt5.order_send(request)
        
        if result is None:
            error = mt5.last_error()
            logger.error(f"Order send failed: {error}")
            raise Exception(f"Order send failed: {error}")
        
        if result.retcode != 10009:  # TRADE_RETCODE_DONE
            logger.error(f"Order rejected with retcode: {result.retcode}")
            raise Exception(f"Order rejected with retcode: {result.retcode}")
        
        # Store order for tracking
        order_id = result.order
        order = {
            "id": order_id,
            "symbol": symbol,
            "type": order_type,
            "lot": lot,
            "price": result.price,
            "stop_loss": stop_loss,
            "take_profit": take_profit,
            "status": "filled",
            "time": datetime.now()
        }
        
        self.orders.append(order)
        logger.info(f"Order placed (live): {order_id}")
        
        return order_id
    
    def _convert_order_type(self, order_type: str) -> int:
        """Convert string order type to MT5 order type constant"""
        # Order type should already be uppercase from place_order method
        # but we'll ensure it here as well for robustness
        order_type = order_type.upper()
        
        if order_type == "BUY":
            return mt5.ORDER_TYPE_BUY
        elif order_type == "SELL":
            return mt5.ORDER_TYPE_SELL
        elif order_type == "BUY_LIMIT":
            return mt5.ORDER_TYPE_BUY_LIMIT
        elif order_type == "SELL_LIMIT":
            return mt5.ORDER_TYPE_SELL_LIMIT
        elif order_type == "BUY_STOP":
            return mt5.ORDER_TYPE_BUY_STOP
        elif order_type == "SELL_STOP":
            return mt5.ORDER_TYPE_SELL_STOP
        else:
            raise ValueError(f"Invalid order type: {order_type}")
    
    def get_order_status(self, order_id: int) -> str:
        """
        Get the status of an order
        
        Args:
            order_id: Order ID
            
        Returns:
            str: Order status ("filled", "closed", "not_found")
        """
        for order in self.orders:
            if order["id"] == order_id:
                return order["status"]
        
        return "not_found"
    
    def close_order(self, order_id: int) -> bool:
        """
        Close an open order
        
        Args:
            order_id: Order ID
            
        Returns:
            bool: True if order was closed, False otherwise
        """
        for order in self.orders:
            if order["id"] == order_id:
                # In offline mode, just update the status
                if self.offline_mode:
                    order["status"] = "closed"
                    logger.info(f"Order closed (offline): {order_id}")
                    return True
                
                # In live mode, send close request to MT5
                try:
                    # Create close request
                    request = {
                        "action": mt5.TRADE_ACTION_DEAL,
                        "symbol": order["symbol"],
                        "volume": order["lot"],
                        "type": mt5.ORDER_TYPE_SELL if order["type"] == "BUY" else mt5.ORDER_TYPE_BUY,
                        "position": order_id,
                        "price": 0.0,  # Market price
                        "deviation": 20,
                        "magic": 123456,
                        "comment": f"Close {order['type']} position",
                        "type_time": mt5.ORDER_TIME_GTC,
                        "type_filling": mt5.ORDER_FILLING_IOC,
                    }
                    
                    # Send close request
                    result = mt5.order_send(request)
                    
                    if result is None or result.retcode != 10009:
                        logger.error(f"Failed to close order {order_id}")
                        return False
                    
                    order["status"] = "closed"
                    logger.info(f"Order closed (live): {order_id}")
                    return True
                    
                except Exception as e:
                    logger.error(f"Error closing order: {str(e)}")
                    return False
        
        logger.warning(f"Order not found: {order_id}")
        return False
    
    def get_positions(self) -> List[Dict[str, Any]]:
        """
        Get all open positions
        
        Returns:
            List[Dict]: List of open positions
        """
        if self.offline_mode:
            # Return orders with status "filled" (not "closed")
            return [
                order for order in self.orders 
                if order["status"] == "filled"
            ]
        
        try:
            positions = mt5.positions_get()
            
            if positions is None:
                logger.warning("Failed to get positions")
                return []
            
            # Convert to our format
            result = []
            for pos in positions:
                result.append({
                    "id": pos.ticket,
                    "symbol": pos.symbol,
                    "type": "BUY" if pos.type == 0 else "SELL",
                    "lot": pos.volume,
                    "price": pos.price_open,
                    "status": "filled",
                    "profit": pos.profit,
                    "time": pos.time
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting positions: {str(e)}")
            return []
    
    def get_orders(self) -> List[Dict[str, Any]]:
        """
        Get all pending orders
        
        Returns:
            List[Dict]: List of pending orders
        """
        if self.offline_mode:
            # In our simplified model, we don't distinguish between
            # positions and pending orders, so return empty list
            return []
        
        try:
            orders = mt5.orders_get()
            
            if orders is None:
                logger.warning("Failed to get orders")
                return []
            
            # Convert to our format
            result = []
            for order in orders:
                order_type = "BUY"
                if order.type == 1:
                    order_type = "SELL"
                elif order.type == 2:
                    order_type = "BUY_LIMIT"
                elif order.type == 3:
                    order_type = "SELL_LIMIT"
                elif order.type == 4:
                    order_type = "BUY_STOP"
                elif order.type == 5:
                    order_type = "SELL_STOP"
                
                result.append({
                    "id": order.ticket,
                    "symbol": order.symbol,
                    "type": order_type,
                    "lot": order.volume_current,
                    "price": order.price_open,
                    "status": "pending",
                    "time": order.time_setup
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting orders: {str(e)}")
            return []
    
    def simulate_connection_loss(self):
        """Simulate connection loss for testing"""
        self.connected = False
    
    def simulate_api_error(self):
        """Simulate API error for testing"""
        # This is just a flag for the mock implementation
        # In the real implementation, we'll raise an exception
        # when this flag is set and place_order is called
        self._simulate_error = True