import { PasswordService } from './password.service';
import bcrypt from 'bcryptjs';

// Mock bcrypt
jest.mock('bcryptjs');

describe('PasswordService', () => {
  let passwordService: PasswordService;
  const mockBcrypt = jest.mocked(bcrypt);

  beforeEach(() => {
    jest.clearAllMocks();
    passwordService = new PasswordService();
  });

  describe('hashPassword', () => {
    it('should hash password successfully', async () => {
      // Arrange
      const password = 'plainTextPassword';
      const hashedPassword = 'hashedPassword123';
      mockBcrypt.hash.mockResolvedValue(hashedPassword);

      // Act
      const result = await passwordService.hashPassword(password);

      // Assert
      expect(result).toBe(hashedPassword);
      expect(mockBcrypt.hash).toHaveBeenCalledWith(password, 12);
    });

    it('should handle hashing errors', async () => {
      // Arrange
      const password = 'plainTextPassword';
      const error = new Error('Hashing failed');
      mockBcrypt.hash.mockRejectedValue(error);

      // Act & Assert
      await expect(passwordService.hashPassword(password)).rejects.toThrow('Hashing failed');
    });

    it('should use custom salt rounds when provided', async () => {
      // Arrange
      const password = 'plainTextPassword';
      const hashedPassword = 'hashedPassword123';
      const customSaltRounds = 8;
      mockBcrypt.hash.mockResolvedValue(hashedPassword);

      const customPasswordService = new PasswordService(customSaltRounds);

      // Act
      const result = await customPasswordService.hashPassword(password);

      // Assert
      expect(result).toBe(hashedPassword);
      expect(mockBcrypt.hash).toHaveBeenCalledWith(password, customSaltRounds);
    });
  });

  describe('verifyPassword', () => {
    it('should verify password successfully when password matches', async () => {
      // Arrange
      const password = 'plainTextPassword';
      const hashedPassword = 'hashedPassword123';
      mockBcrypt.compare.mockResolvedValue(true);

      // Act
      const result = await passwordService.verifyPassword(password, hashedPassword);

      // Assert
      expect(result).toBe(true);
      expect(mockBcrypt.compare).toHaveBeenCalledWith(password, hashedPassword);
    });

    it('should verify password successfully when password does not match', async () => {
      // Arrange
      const password = 'wrongPassword';
      const hashedPassword = 'hashedPassword123';
      mockBcrypt.compare.mockResolvedValue(false);

      // Act
      const result = await passwordService.verifyPassword(password, hashedPassword);

      // Assert
      expect(result).toBe(false);
      expect(mockBcrypt.compare).toHaveBeenCalledWith(password, hashedPassword);
    });

    it('should handle verification errors', async () => {
      // Arrange
      const password = 'plainTextPassword';
      const hashedPassword = 'hashedPassword123';
      const error = new Error('Verification failed');
      mockBcrypt.compare.mockRejectedValue(error);

      // Act & Assert
      await expect(passwordService.verifyPassword(password, hashedPassword)).rejects.toThrow('Verification failed');
    });
  });

  describe('validatePasswordStrength', () => {
    it('should return true for strong password', () => {
      // Arrange
      const strongPassword = 'StrongP@ssw0rd123';

      // Act
      const result = passwordService.validatePasswordStrength(strongPassword);

      // Assert
      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual([]);
    });

    it('should return false for short password', () => {
      // Arrange
      const shortPassword = 'Short1!';

      // Act
      const result = passwordService.validatePasswordStrength(shortPassword);

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must be at least 8 characters long');
    });

    it('should return false for password without uppercase', () => {
      // Arrange
      const password = 'lowercase123!';

      // Act
      const result = passwordService.validatePasswordStrength(password);

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one uppercase letter');
    });

    it('should return false for password without lowercase', () => {
      // Arrange
      const password = 'UPPERCASE123!';

      // Act
      const result = passwordService.validatePasswordStrength(password);

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one lowercase letter');
    });

    it('should return false for password without number', () => {
      // Arrange
      const password = 'NoNumbers!';

      // Act
      const result = passwordService.validatePasswordStrength(password);

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one number');
    });

    it('should return false for password without special character', () => {
      // Arrange
      const password = 'NoSpecialChar123';

      // Act
      const result = passwordService.validatePasswordStrength(password);

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one special character');
    });

    it('should return multiple errors for weak password', () => {
      // Arrange
      const weakPassword = 'weak';

      // Act
      const result = passwordService.validatePasswordStrength(weakPassword);

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(4);
      expect(result.errors).toContain('Password must be at least 8 characters long');
      expect(result.errors).toContain('Password must contain at least one uppercase letter');
      expect(result.errors).toContain('Password must contain at least one number');
      expect(result.errors).toContain('Password must contain at least one special character');
    });
  });
});