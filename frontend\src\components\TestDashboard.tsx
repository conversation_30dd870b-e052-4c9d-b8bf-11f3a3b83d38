import React, { useState } from 'react';
import { Bot, Eye, MessageCircle, Send, Upload, Plus } from 'lucide-react';

const TestDashboard: React.FC = () => {
  const [currentTheme, setCurrentTheme] = useState('professional');
  const [chatInput, setChatInput] = useState('');

  const themes = [
    { id: 'professional', name: 'Professional', colors: { primary: 'blue', secondary: 'indigo' } },
    { id: 'enhanced', name: 'Enhanced', colors: { primary: 'purple', secondary: 'pink' } },
    { id: 'minimal', name: 'Minimal', colors: { primary: 'gray', secondary: 'slate' } }
  ];

  const currentThemeData = themes.find(t => t.id === currentTheme) || themes[0];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header with Theme Switcher */}
      <nav className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-lg ${
                currentTheme === 'professional' ? 'bg-blue-100' :
                currentTheme === 'enhanced' ? 'bg-purple-100' :
                'bg-gray-100'
              }`}>
                <Bot className={`w-6 h-6 ${
                  currentTheme === 'professional' ? 'text-blue-600' :
                  currentTheme === 'enhanced' ? 'text-purple-600' :
                  'text-gray-600'
                }`} />
              </div>
              <div>
                <span className="text-xl font-bold text-gray-900">AI Trading Platform</span>
                <div className={`text-xs font-medium ${
                  currentTheme === 'professional' ? 'text-blue-600' :
                  currentTheme === 'enhanced' ? 'text-purple-600' :
                  'text-gray-600'
                }`}>
                  ✅ {currentThemeData.name} Theme Active
                </div>
              </div>
            </div>
            
            {/* THEME SWITCHER - VISIBLE HERE */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Eye className="w-4 h-4 text-gray-500" />
                <span className="text-sm text-gray-600">Theme:</span>
                <div className="flex bg-gray-100 rounded-lg p-1">
                  {themes.map((theme) => (
                    <button
                      key={theme.id}
                      onClick={() => setCurrentTheme(theme.id)}
                      className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                        currentTheme === theme.id
                          ? (theme.id === 'professional' ? 'bg-blue-500 text-white shadow-sm' :
                             theme.id === 'enhanced' ? 'bg-purple-500 text-white shadow-sm' :
                             'bg-gray-500 text-white shadow-sm')
                          : 'text-gray-600 hover:text-gray-900'
                      }`}
                    >
                      {theme.name}
                    </button>
                  ))}
                </div>
              </div>
              
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>MT5 Connected</span>
              </div>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 space-y-8">
        {/* Hero Section */}
        <section className={`rounded-xl p-8 text-white ${
          currentTheme === 'professional' ? 'bg-gradient-to-r from-blue-600 to-indigo-700' :
          currentTheme === 'enhanced' ? 'bg-gradient-to-r from-purple-600 to-pink-700' :
          'bg-gradient-to-r from-gray-600 to-slate-700'
        }`}>
          <h1 className="text-3xl font-bold mb-2">🚀 Complete Trading Platform</h1>
          <p className={`text-lg ${
            currentTheme === 'professional' ? 'text-blue-100' :
            currentTheme === 'enhanced' ? 'text-purple-100' :
            'text-gray-100'
          }`}>
            Current Theme: <strong>{currentThemeData.name}</strong> | All features on one page
          </p>
        </section>

        {/* AI Chatbot Section */}
        <section>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">💬 AI Chatbot (VISIBLE!)</h2>
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className={`p-2 rounded-lg ${
                currentTheme === 'professional' ? 'bg-blue-100' :
                currentTheme === 'enhanced' ? 'bg-purple-100' :
                'bg-gray-100'
              }`}>
                <MessageCircle className={`w-5 h-5 ${
                  currentTheme === 'professional' ? 'text-blue-600' :
                  currentTheme === 'enhanced' ? 'text-purple-600' :
                  'text-gray-600'
                }`} />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">AI Strategy Assistant</h3>
              <div className={`ml-auto px-2 py-1 rounded-full text-xs font-medium ${
                currentTheme === 'professional' ? 'bg-blue-100 text-blue-800' :
                currentTheme === 'enhanced' ? 'bg-purple-100 text-purple-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {currentThemeData.name} Theme
              </div>
            </div>
            
            {/* Chat Area */}
            <div className="h-64 bg-gray-50 rounded-lg p-4 mb-4">
              <div className="bg-white border border-gray-200 rounded-lg p-4 mb-4">
                <div className="flex items-start space-x-3">
                  <div className="p-1 rounded-full bg-gray-100">
                    <Bot className="w-4 h-4 text-gray-600" />
                  </div>
                  <div className="text-sm">
                    Hello! I'm your AI trading assistant. Ask me to create a trading strategy and I'll generate Python code for you!
                  </div>
                </div>
              </div>
            </div>

            {/* Chat Input */}
            <div className="flex space-x-3">
              <input
                type="text"
                value={chatInput}
                onChange={(e) => setChatInput(e.target.value)}
                placeholder="Ask me to create a trading strategy..."
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button className={`px-6 py-2 text-white rounded-lg flex items-center space-x-2 ${
                currentTheme === 'professional' ? 'bg-blue-600 hover:bg-blue-700' :
                currentTheme === 'enhanced' ? 'bg-purple-600 hover:bg-purple-700' :
                'bg-gray-600 hover:bg-gray-700'
              }`}>
                <Send className="w-4 h-4" />
                <span>Send</span>
              </button>
            </div>
          </div>
        </section>

        {/* Strategy Builder */}
        <section>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">🔧 Strategy Builder</h2>
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-2 bg-green-100 rounded-lg">
                <Plus className="w-5 h-5 text-green-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Create New Strategy</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Strategy Name</label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="e.g., RSI Scalping Strategy"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Symbol</label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                  <option value="EURUSD">EUR/USD</option>
                  <option value="GBPUSD">GBP/USD</option>
                  <option value="USDJPY">USD/JPY</option>
                </select>
              </div>
            </div>
            
            <button className="mt-4 flex items-center space-x-2 px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
              <Plus className="w-4 h-4" />
              <span>Create Strategy</span>
            </button>
          </div>
        </section>

        {/* Data Upload */}
        <section>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">📁 Data Upload</h2>
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Upload className="w-5 h-5 text-purple-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Upload Trading Data</h3>
            </div>
            
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
              <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-gray-900 mb-2">Upload your files</h4>
              <p className="text-gray-600 mb-4">Support for CSV, Excel, JSON, and MT5 history files</p>
              
              <input type="file" multiple className="hidden" id="file-upload" />
              <label
                htmlFor="file-upload"
                className="inline-flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 cursor-pointer"
              >
                <Upload className="w-4 h-4" />
                <span>Choose Files</span>
              </label>
            </div>
          </div>
        </section>

        {/* Professional AI Prompts */}
        <section>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">🧠 Professional AI Prompts</h2>
          <div className="grid gap-6 md:grid-cols-2">
            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Market Scanner</h3>
              <p className="text-sm text-gray-600 mb-4">Identify trading assets that meet specific criteria</p>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-700 font-mono">
                  Act as a day trading assistant. Your task is to identify trading assets...
                </p>
              </div>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Technical Analysis</h3>
              <p className="text-sm text-gray-600 mb-4">Detailed technical analysis with entry/exit points</p>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-700 font-mono">
                  Act as an experienced day trader. Analyze price patterns...
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Active Strategies */}
        <section>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">📈 Active Strategies</h2>
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Strategy</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">P&L</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">RSI Mean Reversion</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Active</span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">+$1,247</td>
                </tr>
              </tbody>
            </table>
          </div>
        </section>
      </div>
    </div>
  );
};

export default TestDashboard;