// src/demo/app.ts - Main Forex Demo Application
import Fastify from 'fastify';
import cors from '@fastify/cors';
import { EventBus } from '../infrastructure/event-bus';
import { MetricsCollector } from '../monitoring/metrics-collector';
import { TradeService, MarketDataService, TradeRepository, Trade } from '../services/trade-service';
import { WebSocketManager } from '../infrastructure/websocket-manager';
import { mockForexData, mockCryptoData } from './mock-data';

// Mock services for demo
class MockMarketDataService implements MarketDataService {
  async isMarketOpen(symbol: string): Promise<boolean> {
    return isForexMarketOpen();
  }

  async getCurrentPrice(symbol: string): Promise<number> {
    const data = mockForexData[symbol];
    return data ? (data.bid + data.ask) / 2 : 1.0;
  }
}

class MockTradeRepository implements TradeRepository {
  private trades: Trade[] = [];

  async save(trade: Trade): Promise<Trade> {
    const savedTrade = { ...trade, id: `trade-${Date.now()}` };
    this.trades.push(savedTrade);
    return savedTrade;
  }

  async findById(id: string): Promise<Trade | null> {
    return this.trades.find(t => t.id === id) || null;
  }

  async findBySymbol(symbol: string): Promise<Trade[]> {
    return this.trades.filter(t => t.symbol === symbol);
  }
}

// Forex helper functions
function calculatePipValue(pair: string, lotSize: number): number {
  const standardLot = 100000;
  if (pair.endsWith('JPY')) {
    return (0.01 / (mockForexData[pair]?.ask || 1)) * standardLot * lotSize;
  }
  return 10 * lotSize; // Standard $10 per pip for 1 lot
}

function calculateMargin(pair: string, lotSize: number, leverage: number): number {
  const price = mockForexData[pair]?.ask || 1;
  const contractSize = 100000;
  return (contractSize * lotSize * price) / leverage;
}

function isForexMarketOpen(): boolean {
  const now = new Date();
  const day = now.getUTCDay();
  const hour = now.getUTCHours();
  
  // Forex market is open from Sunday 21:00 UTC to Friday 21:00 UTC
  if (day === 0 && hour < 21) return false; // Sunday before 21:00
  if (day === 6) return false; // Saturday
  if (day === 5 && hour >= 21) return false; // Friday after 21:00
  
  return true;
}

export async function createForexDemoApp() {
  const app = Fastify({ logger: true });
  
  // Register plugins
  await app.register(cors, { origin: true });
  
  // Initialize services
  const eventBus = new EventBus();
  const metricsCollector = new MetricsCollector();
  const marketDataService = new MockMarketDataService();
  const tradeRepository = new MockTradeRepository();
  const tradeService = new TradeService(marketDataService, tradeRepository);

  // Initialize WebSocket server
  const wsManager = new WebSocketManager(8080);

  // Health check endpoint
  app.get('/health', async () => {
    return { 
      status: 'healthy', 
      timestamp: new Date().toISOString(),
      services: {
        eventBus: 'active',
        metrics: 'active',
        trading: 'active',
        websocket: 'active'
      }
    };
  });

  // Metrics endpoint
  app.get('/metrics', async () => {
    return metricsCollector.exportPrometheusMetrics();
  });

  // Demo API endpoints
  app.get('/api/forex/:pair', async (request) => {
    const { pair } = request.params as { pair: string };
    metricsCollector.incrementCounter('api_requests', { endpoint: '/forex' });
    
    const forexData = mockForexData[pair];
    if (!forexData) {
      return { error: 'Pair not found' };
    }
    
    // Simulate real-time price movement
    const movement = (Math.random() - 0.5) * 0.0005;
    const bid = forexData.bid + movement;
    const ask = forexData.ask + movement;
    
    return {
      pair,
      bid: parseFloat(bid.toFixed(5)),
      ask: parseFloat(ask.toFixed(5)),
      spread: forexData.spread,
      high24h: parseFloat((Math.max(bid, ask) * 1.002).toFixed(5)),
      low24h: parseFloat((Math.min(bid, ask) * 0.998).toFixed(5)),
      change24h: parseFloat(((Math.random() - 0.5) * 2).toFixed(2)),
      volume24h: Math.floor(Math.random() * 1000000000),
      timestamp: new Date().toISOString(),
      marketOpen: isForexMarketOpen()
    };
  });

  app.post('/api/forex/trade', async (request) => {
    metricsCollector.incrementCounter('forex_trades_total', { status: 'pending' });
    const trade = request.body as any;
    
    try {
      // Calculate pip value and margin requirement
      const pipValue = calculatePipValue(trade.pair, trade.lotSize);
      const marginRequired = calculateMargin(trade.pair, trade.lotSize, trade.leverage);
      
      const result = await tradeService.executeTrade({
        symbol: trade.pair,
        quantity: trade.lotSize,
        side: trade.side,
        type: trade.orderType || 'market',
        price: trade.price
      });
      
      metricsCollector.incrementCounter('forex_trades_total', { status: 'success' });
      metricsCollector.recordHistogram('forex_trade_size', trade.lotSize);
      
      return { 
        success: true, 
        trade: result,
        executionPrice: trade.side === 'buy' ? mockForexData[trade.pair].ask : mockForexData[trade.pair].bid,
        pipValue,
        marginRequired
      };
    } catch (error) {
      metricsCollector.incrementCounter('forex_trades_total', { status: 'failed' });
      return { success: false, error: (error as Error).message };
    }
  });

  app.get('/api/forex/portfolio', async () => {
    return {
      balance: 50000,
      equity: 52500,
      usedMargin: 5250,
      freeMargin: 47250,
      marginLevel: 1000, // percentage
      openPositions: [
        { 
          pair: 'EUR/USD', 
          side: 'buy',
          lotSize: 1.0,
          openPrice: 1.0842,
          currentPrice: 1.0856,
          pips: 14,
          profit: 140,
          margin: 1084,
          openTime: new Date(Date.now() - 3600000).toISOString()
        },
        { 
          pair: 'GBP/USD', 
          side: 'sell',
          lotSize: 0.5,
          openPrice: 1.2645,
          currentPrice: 1.2634,
          pips: 11,
          profit: 55,
          margin: 632,
          openTime: new Date(Date.now() - 7200000).toISOString()
        }
      ],
      pendingOrders: [
        {
          pair: 'USD/JPY',
          type: 'limit',
          side: 'buy',
          lotSize: 0.3,
          targetPrice: 149.50,
          currentPrice: 149.85
        }
      ]
    };
  });

  // Subscribe to events for demo
  eventBus.subscribe('trade.executed', async (event) => {
    console.log('Trade executed:', event.payload);
    metricsCollector.recordHistogram('trade_latency', Math.random() * 100 + 50);
  });

  // Cleanup on shutdown
  process.on('SIGTERM', () => {
    wsManager.close();
  });

  return app;
}