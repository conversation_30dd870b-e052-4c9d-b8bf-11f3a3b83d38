import React, { useState, useEffect } from 'react';
import StrategyChatbot from './StrategyChatbot';
import { aiPromptsService } from '../services/aiPrompts';
import './MainHomepage.css';

interface AIPrompt {
  id: string;
  title: string;
  description: string;
  category: string;
  prompt: string;
}

const MainHomepage: React.FC = () => {
  const [prompts, setPrompts] = useState<AIPrompt[]>([]);
  const [loading, setLoading] = useState(true);
  const [activePrompt, setActivePrompt] = useState<string | null>(null);
  const [selectedPrompt, setSelectedPrompt] = useState<string>('');
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    const fetchPrompts = async () => {
      try {
        const fetchedPrompts = await aiPromptsService.getPrompts();
        setPrompts(fetchedPrompts);
      } catch (error) {
        console.error('Failed to fetch prompts:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPrompts();
  }, []);

  const handlePromptSelect = (prompt: AIPrompt) => {
    setActivePrompt(prompt.id);
    setSelectedPrompt(prompt.prompt);
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    element?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <div className="homepage">
      {/* Navigation */}
      <nav className="navbar">
        <div className="nav-container">
          <div className="nav-brand">
            <span className="brand-name">TradeBuilder</span>
            <span className="brand-subtitle">AI</span>
          </div>
          <div className="nav-links">
            <button type="button" onClick={() => scrollToSection('chat-demo')}>AI Chatbot</button>
            <button type="button" onClick={() => scrollToSection('features')}>Features</button>
            <button type="button" onClick={() => scrollToSection('how-it-works')}>How It Works</button>
            <button type="button" onClick={() => scrollToSection('mql5-tools')}>MQL5 Tools</button>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="hero">
        <div className="hero-container">
          <div className="hero-content">
            <h1 className="hero-title">
              AI-Enhanced Trading Platform
            </h1>
            <p className="hero-description">
              Build Python trading strategies with natural language. Create custom MT5 indicators and 
              Expert Advisors without coding knowledge. Test with historical data and deploy to live markets.
            </p>
            <div className="hero-buttons">
              <button type="button" className="btn-primary" onClick={() => scrollToSection('chat-demo')}>
                Try the AI Chatbot
              </button>
              <button type="button" className="btn-secondary" onClick={() => scrollToSection('features')}>
                Explore Features
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Chat Demo Section */}
      <section id="chat-demo" className="chat-demo">
        <div className="container">
          <div className="section-header">
            <h2>Python Strategy Builder</h2>
            <p>Select a pre-made template or describe your own trading strategy in plain English</p>
          </div>
          
          <div className={`chat-layout ${isExpanded ? 'expanded' : ''}`}>
            {/* AI Prompts Sidebar */}
            {!isExpanded && (
              <div className="prompts-sidebar">
                <h3>Strategy Templates</h3>
                {loading ? (
                  <div className="loading">Loading templates...</div>
                ) : (
                  <>
                    <div className="category-filters">
                      <span className="filter active">All</span>
                      <span className="filter">Technical</span>
                      <span className="filter">MQL5</span>
                      <span className="filter">Analysis</span>
                    </div>
                    <div className="prompts-grid">
                      {prompts.map((prompt) => (
                        <div 
                          key={prompt.id}
                          className={`prompt-card ${activePrompt === prompt.id ? 'active' : ''}`}
                          onClick={() => handlePromptSelect(prompt)}
                        >
                          <div className="prompt-category">{prompt.category.replace('_', ' ')}</div>
                          <h4>{prompt.title}</h4>
                          <p>{prompt.description}</p>
                        </div>
                      ))}
                    </div>
                  </>
                )}
              </div>
            )}
            
            {/* Chatbot Interface */}
            <div className="chatbot-section">
              <div className="chatbot-header">
                <div className="chatbot-title">
                  <h3>🐍 Python Strategy Builder</h3>
                  <span className="status">● Ready</span>
                </div>
                {activePrompt && (
                  <div className="active-prompt-indicator">
                    <span className="template-label">Selected Template:</span> {prompts.find(p => p.id === activePrompt)?.title}
                  </div>
                )}
                <button 
                  type="button"
                  className="expand-btn"
                  onClick={() => setIsExpanded(!isExpanded)}
                  title={isExpanded ? "Show templates" : "Hide templates"}
                >
                  {isExpanded ? '◀' : '▶'}
                </button>
              </div>
              <div className="chatbot-wrapper">
                <StrategyChatbot initialPrompt={selectedPrompt} />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="features">
        <div className="container">
          <div className="section-header">
            <h2>Platform Features</h2>
            <p>Tools and capabilities to enhance your trading</p>
          </div>
          
          <div className="features-grid">
            <div className="feature-card">
              <div className="feature-icon">�</div>
              <h3>Python Strategy Building</h3>
              <p>Build complete trading strategies in Python with natural language instructions. No coding experience required.</p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">📊</div>
              <h3>Backtesting Engine</h3>
              <p>Test your strategies with historical market data to evaluate performance before live trading.</p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">🚀</div>
              <h3>MT5 Integration</h3>
              <p>Deploy strategies directly to MetaTrader 5 with our secure bridge connection.</p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">�</div>
              <h3>Custom MT5 Indicators</h3>
              <p>Create custom MQL5 indicators without paying marketplace fees (premium feature).</p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">�</div>
              <h3>Ready-to-Use Templates</h3>
              <p>Access professional trading strategy templates for quick deployment.</p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">�</div>
              <h3>Performance Analytics</h3>
              <p>Evaluate your strategies with detailed metrics and performance indicators.</p>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section id="how-it-works" className="how-it-works">
        <div className="container">
          <div className="section-header">
            <h2>How It Works</h2>
            <p>Strategy development process</p>
          </div>
          
          <div className="steps-grid">
            <div className="step">
              <div className="step-number">1</div>
              <h3>Describe Your Strategy</h3>
              <p>Enter your trading idea in the AI chatbot using plain English or select from our templates.</p>
            </div>
            
            <div className="step">
              <div className="step-number">2</div>
              <h3>Generate Code</h3>
              <p>The AI creates Python code implementing your strategy with proper risk management parameters.</p>
            </div>
            
            <div className="step">
              <div className="step-number">3</div>
              <h3>Backtest</h3>
              <p>Test your strategy against historical market data to evaluate performance and optimize.</p>
            </div>
            
            <div className="step">
              <div className="step-number">4</div>
              <h3>Deploy</h3>
              <p>Deploy your strategy to MetaTrader 5 through our secure bridge and monitor performance.</p>
            </div>
          </div>
        </div>
      </section>

      {/* MQL5 Tools Section (Premium Feature) */}
      <section id="mql5-tools" className="mql5-tools">
        <div className="container">
          <div className="section-header">
            <h2>Premium MQL5 Tools</h2>
            <p>Create custom indicators and EAs without marketplace fees</p>
          </div>
          
          <div className="mql5-content">
            <div className="mql5-description">
              <h3>Custom MQL5 Indicator Development</h3>
              <p>
                Build professional custom indicators for MetaTrader 5 without writing code. 
                Our AI can generate complete MQL5 indicators based on your specifications.
              </p>
              <h3>Expert Advisor Creation</h3>
              <p>
                Generate complete Expert Advisors with advanced features like:
              </p>
              <ul>
                <li>Custom entry/exit logic</li>
                <li>Risk management parameters</li>
                <li>Automated position sizing</li>
                <li>Multi-timeframe analysis</li>
                <li>Full source code access</li>
              </ul>
              <p className="premium-note">
                <strong>Note:</strong> This is a premium feature that enables you to create unlimited 
                indicators and EAs without paying marketplace fees.
              </p>
              <button 
                type="button" 
                className="btn-primary"
                onClick={() => {
                  const chatPrompt = "Create a custom MT5 indicator that identifies divergence between price and RSI";
                  // Find the MQL5 indicator prompt
                  const mql5Prompt = prompts.find(p => p.id === 'mql5_indicator' || p.category === 'trade_execution');
                  if (mql5Prompt) {
                    setSelectedPrompt(mql5Prompt.prompt);
                    setActivePrompt(mql5Prompt.id);
                  } else {
                    setSelectedPrompt(chatPrompt);
                  }
                  scrollToSection('chat-demo');
                }}
              >
                Try MQL5 Generator
              </button>
            </div>
            <div className="mql5-image">
              {/* You could add an image of an MQL5 indicator or code here */}
              <div className="code-preview">
                <pre>
                  <code>
{`//+------------------------------------------------------------------+
//|                                         RSI Divergence.mq5 |
//|                      Copyright 2025, AI Trading Platform |
//+------------------------------------------------------------------+
#property copyright "AI Trading Platform"
#property link      "https://www.example.com"
#property version   "1.00"
#property indicator_chart_window
#property indicator_buffers 2
#property indicator_plots   2

// Input parameters
input int    RSI_Period = 14;      // RSI period
input int    LookBack   = 10;      // Bars to look back for divergence
input color  Bull_Color = clrGreen; // Bullish divergence color
input color  Bear_Color = clrRed;  // Bearish divergence color`}
                  </code>
                </pre>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-brand">
              <span className="brand-name">AI Trading Platform</span>
              <p>Trading strategy development platform</p>
            </div>
            <div className="footer-links">
              <a href="#chat-demo" onClick={() => scrollToSection('chat-demo')} aria-label="Chatbot">AI Chatbot</a>
              <a href="#features" onClick={() => scrollToSection('features')} aria-label="Features">Features</a>
              <a href="#how-it-works" onClick={() => scrollToSection('how-it-works')} aria-label="How It Works">How It Works</a>
              <a href="#mql5-tools" onClick={() => scrollToSection('mql5-tools')} aria-label="MQL5 Tools">MQL5 Tools</a>
            </div>
          </div>
          <div className="footer-bottom">
            <p>&copy; 2025 AI Enhanced Trading Platform. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default MainHomepage;