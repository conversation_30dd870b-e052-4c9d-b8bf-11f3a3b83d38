#!/usr/bin/env python
# tests/mvp_test_suite.py
"""
MVP Test Suite for AI Enhanced Trading Platform

This script provides a streamlined test runner for the MVP version of the trading platform.
It focuses on the core functionality needed for the MVP:
1. MT5 Bridge (connection, order placement, position management)
2. Basic trading functionality
3. Risk management

Usage:
    python -m tests.mvp_test_suite [--offline] [--verbose]
"""

import argparse
import sys
import unittest
import pytest
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("mvp_test_suite")

# Define the core MVP test modules
MVP_TEST_MODULES = [
    "tests/test_mvp_core.py",              # Core MVP tests
    "tests/test_mt5_mvp.py",               # MT5 MVP tests
]

# Additional test modules (run these if they exist)
ADDITIONAL_TEST_MODULES = [
    "tests/test_mt5_bridge_tdd.py",        # Core MT5 Bridge tests
    "tests/test_mt5_bridge_integration.py", # MT5 Bridge integration tests
    "tests/test_risk_management_tdd.py",    # Risk management tests
    "tests/test_strategy_execution_tdd.py", # Strategy execution tests
]

def run_pytest_tests(offline_mode=True, verbose=False, include_additional=False):
    """Run the MVP tests using pytest"""
    logger.info("Running MVP test suite with pytest...")
    
    # Build pytest arguments
    pytest_args = []
    
    # Add core MVP test files that exist
    for module_path in MVP_TEST_MODULES:
        if Path(module_path).exists():
            pytest_args.append(module_path)
        else:
            logger.warning(f"Core test module not found: {module_path}")
    
    # Add additional test modules if requested
    if include_additional:
        logger.info("Including additional test modules...")
        for module_path in ADDITIONAL_TEST_MODULES:
            if Path(module_path).exists():
                pytest_args.append(module_path)
            else:
                logger.warning(f"Additional test module not found: {module_path}")
    
    # If no test files found, create and run the MVP tests
    if not pytest_args:
        logger.warning("No test modules found, creating MVP test files...")
        create_test_mvp_core()
        create_test_mt5_mvp()
        pytest_args = ["tests/test_mvp_core.py", "tests/test_mt5_mvp.py"]
    
    # Add verbosity flag if requested
    if verbose:
        pytest_args.append("-v")
    
    # Add offline mode marker if requested
    if offline_mode:
        pytest_args.append("-m")
        pytest_args.append("not live")
    
    # Run the tests
    logger.info(f"Running pytest with args: {pytest_args}")
    return pytest.main(pytest_args)

def run_unittest_tests(offline_mode=True, verbose=False, include_additional=False):
    """Run the MVP tests using unittest (fallback if pytest is not available)"""
    logger.info("Running MVP test suite with unittest...")
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Discover tests in each module
    modules_to_test = MVP_TEST_MODULES.copy()
    
    # Add additional modules if requested
    if include_additional:
        modules_to_test.extend(ADDITIONAL_TEST_MODULES)
    
    for module_path in modules_to_test:
        try:
            if Path(module_path).exists():
                module_tests = unittest.defaultTestLoader.discover(
                    str(Path(module_path).parent),
                    pattern=Path(module_path).name
                )
                test_suite.addTest(module_tests)
            else:
                logger.warning(f"Test module not found: {module_path}")
        except Exception as e:
            logger.error(f"Error loading test module {module_path}: {str(e)}")
    
    # If no test files found, create and run the MVP tests
    if test_suite.countTestCases() == 0:
        logger.warning("No test modules found, creating MVP test files...")
        create_test_mvp_core()
        create_test_mt5_mvp()
        
        for module_path in ["tests/test_mvp_core.py", "tests/test_mt5_mvp.py"]:
            try:
                module_tests = unittest.defaultTestLoader.discover(
                    str(Path(module_path).parent),
                    pattern=Path(module_path).name
                )
                test_suite.addTest(module_tests)
            except Exception as e:
                logger.error(f"Error loading test module {module_path}: {str(e)}")
    
    # Run the tests
    test_runner = unittest.TextTestRunner(verbosity=2 if verbose else 1)
    result = test_runner.run(test_suite)
    
    # Return exit code based on test results
    return 0 if result.wasSuccessful() else 1

def create_test_mvp_core():
    """Create the core MVP test file if it doesn't exist"""
    core_test_path = Path("tests/test_mvp_core.py")
    
    if not core_test_path.exists():
        logger.info("Creating MVP core test file...")
        
        core_test_content = """# tests/test_mvp_core.py
\"\"\"
Core MVP tests for the AI Enhanced Trading Platform
\"\"\"

import pytest
from src.trading.mt5_bridge_tdd import MT5Bridge

@pytest.mark.unit
def test_basic_order_placement():
    \"\"\"Test basic buy/sell order functionality\"\"\"
    # Create MT5 Bridge in offline mode
    bridge = MT5Bridge(offline_mode=True)
    
    # Place a market buy order
    order_id = bridge.place_order(
        symbol="EURUSD",
        order_type="BUY",
        lot=0.1
    )
    
    # Verify order was placed
    assert order_id > 0
    assert bridge.get_order_status(order_id) == "filled"

@pytest.mark.unit
def test_portfolio_balance():
    \"\"\"Test portfolio balance calculation\"\"\"
    # This is a placeholder for portfolio balance testing
    # Implement actual portfolio balance calculation test
    pass

@pytest.mark.unit
def test_risk_management():
    \"\"\"Test basic risk limits\"\"\"
    # This is a placeholder for risk management testing
    # Implement actual risk management test
    pass
"""
        
        with open(core_test_path, "w") as f:
            f.write(core_test_content)
        
        logger.info(f"Created {core_test_path}")

def create_test_mt5_mvp():
    """Create the MT5 MVP test file if it doesn't exist"""
    mt5_test_path = Path("tests/test_mt5_mvp.py")
    
    if not mt5_test_path.exists():
        logger.info("Creating MT5 MVP test file...")
        
        mt5_test_content = """# tests/test_mt5_mvp.py
\"\"\"
MT5 integration tests for the MVP version
\"\"\"

import pytest
from src.trading.mt5_bridge_tdd import MT5Bridge

@pytest.mark.integration
def test_mt5_connection():
    \"\"\"Test MT5 connection in offline mode\"\"\"
    # Create MT5 Bridge in offline mode
    bridge = MT5Bridge(offline_mode=True)
    
    # Test connection
    assert bridge.is_connected() is True
    
    # Test disconnection
    bridge.disconnect()
    assert bridge.is_connected() is False
    
    # Test reconnection
    assert bridge.connect() is True
    assert bridge.is_connected() is True

@pytest.mark.integration
def test_mt5_order_simulation():
    \"\"\"Test simulated order placement\"\"\"
    # Create MT5 Bridge in offline mode
    bridge = MT5Bridge(offline_mode=True)
    
    # Place a market buy order
    order_id = bridge.place_order(
        symbol="EURUSD",
        order_type="BUY",
        lot=0.1
    )
    
    # Verify order was placed
    assert order_id > 0
    
    # Get positions
    positions = bridge.get_positions()
    assert len(positions) > 0
    
    # Close the order
    assert bridge.close_order(order_id) is True
    
    # Verify order was closed
    assert bridge.get_order_status(order_id) == "closed"
"""
        
        with open(mt5_test_path, "w") as f:
            f.write(mt5_test_content)
        
        logger.info(f"Created {mt5_test_path}")

def main():
    """Main entry point for the MVP test suite"""
    parser = argparse.ArgumentParser(description="MVP Test Suite for AI Enhanced Trading Platform")
    parser.add_argument("--offline", action="store_true", default=True, 
                        help="Run tests in offline mode (default: True)")
    parser.add_argument("--verbose", "-v", action="store_true", 
                        help="Enable verbose output")
    parser.add_argument("--create-tests", action="store_true",
                        help="Create MVP test files if they don't exist")
    parser.add_argument("--all-tests", action="store_true",
                        help="Run all available tests, not just MVP tests")
    
    args = parser.parse_args()
    
    # Create test files if requested
    if args.create_tests:
        create_test_mvp_core()
        create_test_mt5_mvp()
    
    # Try to run tests with pytest first
    try:
        import pytest
        exit_code = run_pytest_tests(
            offline_mode=args.offline, 
            verbose=args.verbose,
            include_additional=args.all_tests
        )
    except ImportError:
        # Fall back to unittest if pytest is not available
        logger.warning("pytest not found, falling back to unittest")
        exit_code = run_unittest_tests(
            offline_mode=args.offline, 
            verbose=args.verbose,
            include_additional=args.all_tests
        )
    
    # Print summary
    if exit_code == 0:
        logger.info("✅ All MVP tests passed!")
    else:
        logger.error("❌ Some MVP tests failed!")
    
    return exit_code

if __name__ == "__main__":
    sys.exit(main())