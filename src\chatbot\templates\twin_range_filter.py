class TwinRangeFilterStrategy(StrategyBase):
    """Twin Range Filter Strategy implementation
    
    This strategy uses high and low price ranges to define a trading channel
    and generates signals based on price movement within the channel.
    """
    
    def __init__(self, symbol="GBPUSD", timeframe="H4", mt5_bridge=None, risk_per_trade=0.015):
        super().__init__(
            name=f"Twin Range Filter {symbol} {timeframe}",
            symbols=[symbol],
            mt5_bridge=mt5_bridge,
            risk_per_trade=risk_per_trade
        )
        self.period = 20
        self.timeframe = timeframe
        self.stop_loss_multiplier = 2.0
        self.take_profit_multiplier = 3.0
    
    def calculate_ranges(self, data):
        """Calculate the high range, low range and middle line"""
        high_prices = data['high']
        low_prices = data['low']
        
        # Calculate the highest high and lowest low over the period
        high_range = high_prices.rolling(window=self.period).max()
        low_range = low_prices.rolling(window=self.period).min()
        
        # Calculate middle line
        middle_line = (high_range + low_range) / 2
        
        return {
            'high_range': high_range,
            'low_range': low_range,
            'middle_line': middle_line
        }
    
    def generate_signal(self, symbol, data):
        """Generate trading signal based on Twin Range Filter"""
        if len(data['close']) < self.period + 2:
            return {
                "signal": "hold",
                "confidence": 0,
                "reason": "Insufficient data for calculation"
            }
            
        # Calculate the ranges
        ranges = self.calculate_ranges(data)
        
        # Current and previous values
        curr_close = data['close'].iloc[-1]
        prev_close = data['close'].iloc[-2]
        curr_middle = ranges['middle_line'].iloc[-1]
        prev_middle = ranges['middle_line'].iloc[-2]
        curr_high = ranges['high_range'].iloc[-1]
        curr_low = ranges['low_range'].iloc[-1]
        
        # Determine trend strength
        range_size = curr_high - curr_low
        
        # Generate signals based on price crossing the middle line
        if prev_close < prev_middle and curr_close > curr_middle:
            return {
                "signal": "buy",
                "confidence": 0.8,
                "reason": f"Price crossed above middle line: {curr_middle:.5f}",
                "stop_loss": curr_low - (range_size * 0.1),
                "take_profit": curr_close + (range_size * self.take_profit_multiplier)
            }
        elif prev_close > prev_middle and curr_close < curr_middle:
            return {
                "signal": "sell",
                "confidence": 0.8,
                "reason": f"Price crossed below middle line: {curr_middle:.5f}",
                "stop_loss": curr_high + (range_size * 0.1),
                "take_profit": curr_close - (range_size * self.take_profit_multiplier)
            }
        else:
            return {
                "signal": "hold",
                "confidence": 0.5,
                "reason": f"Price remained on same side of middle line: {curr_middle:.5f}"
            }
