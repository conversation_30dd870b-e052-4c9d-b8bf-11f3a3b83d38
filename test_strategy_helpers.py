#!/usr/bin/env python3
"""
Test Script for Strategy Helpers Integration
Verifies that all components are working correctly
"""

import sys
import os
import json
from datetime import datetime

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_ai_prompts():
    """Test AI Trading Prompts Library"""
    print("🧠 Testing AI Trading Prompts Library...")
    
    try:
        from strategy_helpers.ai_trading_prompts import AITradingPromptsLibrary, PromptCategory
        
        # Initialize library
        prompts = AITradingPromptsLibrary()
        
        # Test basic functionality
        all_prompts = prompts.list_all_prompts()
        print(f"   ✅ Found {len(all_prompts)} AI prompts")
        
        # Test categories
        categories = list(PromptCategory)
        print(f"   ✅ Found {len(categories)} prompt categories")
        
        # Test specific prompt
        market_prompt = prompts.get_prompt("market_scanner")
        if market_prompt:
            print(f"   ✅ Market scanner prompt loaded: {market_prompt.title}")
        
        # Test prompt formatting
        formatted = prompts.format_prompt(
            "market_scanner",
            criteria="high-growth tech stocks"
        )
        if formatted and "high-growth tech stocks" in formatted:
            print("   ✅ Prompt formatting works correctly")
        
        # Test search
        tech_prompts = prompts.search_prompts("technical")
        print(f"   ✅ Search found {len(tech_prompts)} technical prompts")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing AI prompts: {e}")
        return False

def test_quant_strategies():
    """Test Quantitative Strategies Library"""
    print("\n📊 Testing Quantitative Strategies Library...")
    
    try:
        from strategy_helpers.quant_strategies import QuantStrategiesLibrary, StrategyType, TimeFrame
        
        # Initialize library
        quant_lib = QuantStrategiesLibrary()
        
        # Test basic functionality
        strategies = quant_lib.list_strategies()
        print(f"   ✅ Found {len(strategies)} quantitative strategies")
        
        # Test strategy types
        strategy_types = list(StrategyType)
        print(f"   ✅ Found {len(strategy_types)} strategy types")
        
        # Test specific strategy
        rsi_strategy = quant_lib.get_strategy("rsi_mean_reversion")
        if rsi_strategy:
            print(f"   ✅ RSI strategy loaded: {rsi_strategy.name}")
            print(f"      Parameters: {rsi_strategy.parameters}")
        
        # Test metadata
        rsi_metadata = quant_lib.get_metadata("rsi_mean_reversion")
        if rsi_metadata:
            print(f"   ✅ RSI metadata loaded: {rsi_metadata.description}")
        
        # Test recommendations
        recommendations = quant_lib.get_strategy_recommendations(
            asset_class="forex",
            timeframe=TimeFrame.HOUR_1,
            complexity="beginner"
        )
        print(f"   ✅ Found {len(recommendations)} strategy recommendations")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing quant strategies: {e}")
        return False

def test_strategy_assistant():
    """Test Strategy Assistant"""
    print("\n🎯 Testing Strategy Assistant...")
    
    try:
        from strategy_helpers.strategy_assistant import StrategyAssistant
        
        # Initialize assistant
        assistant = StrategyAssistant()
        
        # Test user profile
        user_profile = {
            'experience_level': 'intermediate',
            'asset_classes': ['forex', 'stocks'],
            'timeframes': ['1h', '4h'],
            'risk_tolerance': 'medium',
            'capital': 25000,
            'goals': ['income', 'growth']
        }
        
        # Test recommendations
        recommendations = assistant.get_comprehensive_recommendations(user_profile)
        print(f"   ✅ Generated {len(recommendations)} personalized recommendations")
        
        if recommendations:
            first_rec = recommendations[0]
            print(f"      Top recommendation: {first_rec.name} ({first_rec.type})")
        
        # Test learning path
        learning_path = assistant.get_learning_path(user_profile)
        print(f"   ✅ Generated learning path with {len(learning_path)} modules")
        
        # Test trading plan (if we have strategies)
        if recommendations and recommendations[0].type == "quant_strategy":
            strategy_name = recommendations[0].name.lower().replace(' ', '_')
            try:
                trading_plan = assistant.create_trading_plan(strategy_name, user_profile)
                if "error" not in trading_plan:
                    print(f"   ✅ Created trading plan for {trading_plan.get('strategy_name', 'strategy')}")
            except:
                print("   ⚠️  Trading plan creation skipped (strategy not found)")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing strategy assistant: {e}")
        return False

def test_api_imports():
    """Test API imports"""
    print("\n🌐 Testing API Components...")
    
    try:
        # Test if we can import the API components
        sys.path.append('backend')
        
        # This will test if all imports work
        import strategy_helper_api
        print("   ✅ Strategy Helper API imports successfully")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing API imports: {e}")
        print(f"      This is expected if FastAPI dependencies are not installed")
        return False

def generate_test_report():
    """Generate a comprehensive test report"""
    print("\n" + "="*60)
    print("🎯 STRATEGY HELPERS INTEGRATION TEST REPORT")
    print("="*60)
    print(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run all tests
    tests = [
        ("AI Trading Prompts", test_ai_prompts),
        ("Quantitative Strategies", test_quant_strategies),
        ("Strategy Assistant", test_strategy_assistant),
        ("API Components", test_api_imports)
    ]
    
    results = {}
    for test_name, test_func in tests:
        results[test_name] = test_func()
    
    # Summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<25} {status}")
    
    print(f"\nOverall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! Strategy Helpers are ready to use!")
        print("\nNext Steps:")
        print("1. Start the Strategy Helper API: python backend/strategy_helper_api.py")
        print("2. Integrate the StrategyHelper component into your frontend")
        print("3. Add navigation links to access the strategy helper")
        print("4. Test the full integration with your MVP")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please check the errors above.")
        print("\nTroubleshooting:")
        print("1. Ensure all dependencies are installed")
        print("2. Check that the src directory structure is correct")
        print("3. Verify Python path configuration")
    
    return passed == total

def demo_functionality():
    """Demonstrate key functionality"""
    print("\n" + "="*60)
    print("🚀 STRATEGY HELPERS DEMO")
    print("="*60)
    
    try:
        from strategy_helpers.ai_trading_prompts import AITradingPromptsLibrary
        from strategy_helpers.quant_strategies import QuantStrategiesLibrary
        from strategy_helpers.strategy_assistant import StrategyAssistant
        
        # Demo AI Prompts
        print("\n1. 🧠 AI Trading Prompts Demo:")
        prompts = AITradingPromptsLibrary()
        
        # Show available prompts
        all_prompts = prompts.list_all_prompts()
        print(f"   Available prompts: {len(all_prompts)}")
        for prompt in all_prompts[:3]:  # Show first 3
            print(f"   - {prompt.title} ({prompt.category.value})")
        
        # Demo prompt generation
        print("\n   Generated Market Scanner Prompt:")
        formatted = prompts.format_prompt(
            "market_scanner",
            criteria="AI and technology stocks with strong momentum"
        )
        if formatted:
            print(f"   {formatted[:200]}...")
        
        # Demo Quantitative Strategies
        print("\n2. 📊 Quantitative Strategies Demo:")
        quant_lib = QuantStrategiesLibrary()
        
        strategies = quant_lib.list_strategies()
        print(f"   Available strategies: {len(strategies)}")
        for strategy in strategies[:3]:  # Show first 3
            metadata = quant_lib.get_metadata(strategy)
            if metadata:
                print(f"   - {metadata.name} ({metadata.complexity}): Sharpe {metadata.expected_sharpe}")
        
        # Demo Strategy Assistant
        print("\n3. 🎯 Strategy Assistant Demo:")
        assistant = StrategyAssistant()
        
        user_profile = {
            'experience_level': 'beginner',
            'asset_classes': ['forex'],
            'timeframes': ['1h'],
            'risk_tolerance': 'medium',
            'capital': 10000,
            'goals': ['learning']
        }
        
        recommendations = assistant.get_comprehensive_recommendations(user_profile)
        print(f"   Generated {len(recommendations)} recommendations for beginner trader:")
        for i, rec in enumerate(recommendations[:3], 1):
            print(f"   {i}. {rec.name} ({rec.type}) - {rec.complexity}")
        
        learning_path = assistant.get_learning_path(user_profile)
        print(f"\n   Learning path has {len(learning_path)} modules:")
        for module in learning_path:
            print(f"   - {module['title']} ({module['duration']})")
        
        print("\n✨ Demo completed successfully!")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")

if __name__ == "__main__":
    print("🎯 AI Enhanced Trading Platform - Strategy Helpers Test")
    print("=" * 60)
    
    # Run tests
    success = generate_test_report()
    
    # Run demo if tests pass
    if success:
        demo_functionality()
    
    print("\n" + "="*60)
    print("Test completed. Check the results above.")
    print("="*60)