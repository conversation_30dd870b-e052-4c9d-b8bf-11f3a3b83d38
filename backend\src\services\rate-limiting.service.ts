export interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  keyGenerator: (identifier: string) => string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  current: number;
  resetTime: Date;
  retryAfter?: number; // seconds until next allowed request
}

export interface RateLimitStatus {
  current: number;
  remaining: number;
  resetTime: Date;
  windowMs: number;
}

export type SubscriptionTier = 'free' | 'solo' | 'pro' | 'enterprise';
export type RateLimitType = 'login' | 'api' | 'backtest' | 'burst';

interface CacheService {
  get(key: string): Promise<string | null>;
  set(key: string, value: string, ttl?: number): Promise<void>;
  delete(key: string): Promise<boolean>;
  exists(key: string): Promise<boolean>;
}

interface RateLimitData {
  count: number;
  resetTime: number;
}

export class RateLimitingService {
  private whitelist = new Set<string>();
  private blacklist = new Set<string>();

  // Rate limit configurations
  private readonly configs = {
    login: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 5,
      keyGenerator: (id: string) => `rate_limit:login:${id}`,
    },
    api: {
      free: {
        windowMs: 60 * 60 * 1000, // 1 hour
        maxRequests: 100,
        keyGenerator: (id: string) => `rate_limit:api:free:${id}`,
      },
      solo: {
        windowMs: 60 * 60 * 1000, // 1 hour
        maxRequests: 500,
        keyGenerator: (id: string) => `rate_limit:api:solo:${id}`,
      },
      pro: {
        windowMs: 60 * 60 * 1000, // 1 hour
        maxRequests: 2000,
        keyGenerator: (id: string) => `rate_limit:api:pro:${id}`,
      },
      enterprise: {
        windowMs: 60 * 60 * 1000, // 1 hour
        maxRequests: 10000,
        keyGenerator: (id: string) => `rate_limit:api:enterprise:${id}`,
      },
    },
    backtest: {
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 10,
      keyGenerator: (id: string) => `rate_limit:backtest:${id}`,
    },
    burst: {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 10,
      keyGenerator: (id: string) => `rate_limit:burst:${id}`,
    },
    ip: {
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 1000,
      keyGenerator: (ip: string) => `rate_limit:ip:${ip}`,
    },
  };

  constructor(private cacheService: CacheService) {}

  async checkRateLimit(identifier: string, config: RateLimitConfig): Promise<RateLimitResult> {
    // Check whitelist/blacklist first
    if (this.whitelist.has(identifier)) {
      return {
        allowed: true,
        remaining: Infinity,
        current: 0,
        resetTime: new Date(Date.now() + config.windowMs),
      };
    }

    if (this.blacklist.has(identifier)) {
      return {
        allowed: false,
        remaining: 0,
        current: config.maxRequests,
        resetTime: new Date(Date.now() + config.windowMs),
        retryAfter: Math.ceil(config.windowMs / 1000),
      };
    }

    const key = config.keyGenerator(identifier);
    const now = Date.now();
    const resetTime = now + config.windowMs;

    try {
      const cached = await this.cacheService.get(key);
      let data: RateLimitData;

      if (cached) {
        data = JSON.parse(cached);
        
        // Check if window has expired
        if (now >= data.resetTime) {
          // Reset the window
          data = { count: 1, resetTime };
        } else {
          // Increment count for this request
          data.count++;
        }
      } else {
        // First request in window
        data = { count: 1, resetTime };
      }

      // Check if limit exceeded
      const allowed = data.count <= config.maxRequests;
      const remaining = Math.max(0, config.maxRequests - data.count);

      // Update cache
      const ttlSeconds = Math.ceil((data.resetTime - now) / 1000);
      await this.cacheService.set(key, JSON.stringify(data), ttlSeconds);

      const result: RateLimitResult = {
        allowed,
        remaining,
        current: data.count,
        resetTime: new Date(data.resetTime),
      };

      if (!allowed) {
        result.retryAfter = Math.ceil((data.resetTime - now) / 1000);
      }

      return result;
    } catch (error) {
      console.error('Rate limiting error:', error);
      // Fail open for availability
      return {
        allowed: true,
        remaining: config.maxRequests - 1,
        current: 1,
        resetTime: new Date(resetTime),
      };
    }
  }

  async checkLoginRateLimit(identifier: string): Promise<RateLimitResult> {
    return this.checkRateLimit(identifier, this.configs.login);
  }

  async checkApiRateLimit(identifier: string): Promise<RateLimitResult> {
    // Default to free tier if not specified
    return this.checkRateLimit(identifier, this.configs.api.free);
  }

  async checkApiRateLimitByTier(identifier: string, tier: SubscriptionTier): Promise<RateLimitResult> {
    const config = this.configs.api[tier];
    return this.checkRateLimit(identifier, config);
  }

  async checkBacktestRateLimit(identifier: string): Promise<RateLimitResult> {
    return this.checkRateLimit(identifier, this.configs.backtest);
  }

  async checkBurstProtection(identifier: string): Promise<RateLimitResult> {
    return this.checkRateLimit(identifier, this.configs.burst);
  }

  async checkIpRateLimit(ipAddress: string): Promise<RateLimitResult> {
    return this.checkRateLimit(ipAddress, this.configs.ip);
  }

  async getRateLimitStatus(identifier: string, type: RateLimitType): Promise<RateLimitStatus> {
    let config: RateLimitConfig;
    
    switch (type) {
      case 'login':
        config = this.configs.login;
        break;
      case 'api':
        config = this.configs.api.free; // Default to free
        break;
      case 'backtest':
        config = this.configs.backtest;
        break;
      case 'burst':
        config = this.configs.burst;
        break;
      default:
        throw new Error(`Unknown rate limit type: ${type}`);
    }

    const key = config.keyGenerator(identifier);
    
    try {
      const cached = await this.cacheService.get(key);
      
      if (!cached) {
        return {
          current: 0,
          remaining: config.maxRequests,
          resetTime: new Date(Date.now() + config.windowMs),
          windowMs: config.windowMs,
        };
      }

      const data: RateLimitData = JSON.parse(cached);
      const now = Date.now();

      // Check if window has expired
      if (now >= data.resetTime) {
        return {
          current: 0,
          remaining: config.maxRequests,
          resetTime: new Date(now + config.windowMs),
          windowMs: config.windowMs,
        };
      }

      return {
        current: data.count,
        remaining: Math.max(0, config.maxRequests - data.count),
        resetTime: new Date(data.resetTime),
        windowMs: config.windowMs,
      };
    } catch (error) {
      console.error('Error getting rate limit status:', error);
      return {
        current: 0,
        remaining: config.maxRequests,
        resetTime: new Date(Date.now() + config.windowMs),
        windowMs: config.windowMs,
      };
    }
  }

  async resetRateLimit(identifier: string, type: RateLimitType): Promise<boolean> {
    let config: RateLimitConfig;
    
    switch (type) {
      case 'login':
        config = this.configs.login;
        break;
      case 'api':
        config = this.configs.api.free;
        break;
      case 'backtest':
        config = this.configs.backtest;
        break;
      case 'burst':
        config = this.configs.burst;
        break;
      default:
        throw new Error(`Unknown rate limit type: ${type}`);
    }

    const key = config.keyGenerator(identifier);
    
    try {
      return await this.cacheService.delete(key);
    } catch (error) {
      console.error('Error resetting rate limit:', error);
      return false;
    }
  }

  async resetAllRateLimits(identifier: string): Promise<boolean> {
    try {
      const promises = [
        this.resetRateLimit(identifier, 'login'),
        this.resetRateLimit(identifier, 'api'),
        this.resetRateLimit(identifier, 'backtest'),
        this.resetRateLimit(identifier, 'burst'),
      ];

      const results = await Promise.all(promises);
      return results.every(result => result);
    } catch (error) {
      console.error('Error resetting all rate limits:', error);
      return false;
    }
  }

  // Whitelist management
  async addToWhitelist(identifier: string): Promise<void> {
    this.whitelist.add(identifier);
    // Optionally persist to cache/database
    await this.cacheService.set(`whitelist:${identifier}`, 'true');
  }

  async removeFromWhitelist(identifier: string): Promise<void> {
    this.whitelist.delete(identifier);
    await this.cacheService.delete(`whitelist:${identifier}`);
  }

  async isWhitelisted(identifier: string): Promise<boolean> {
    if (this.whitelist.has(identifier)) {
      return true;
    }

    // Check persistent storage
    try {
      const exists = await this.cacheService.exists(`whitelist:${identifier}`);
      if (exists) {
        this.whitelist.add(identifier);
        return true;
      }
    } catch (error) {
      console.error('Error checking whitelist:', error);
    }

    return false;
  }

  // Blacklist management
  async addToBlacklist(identifier: string): Promise<void> {
    this.blacklist.add(identifier);
    await this.cacheService.set(`blacklist:${identifier}`, 'true');
  }

  async removeFromBlacklist(identifier: string): Promise<void> {
    this.blacklist.delete(identifier);
    await this.cacheService.delete(`blacklist:${identifier}`);
  }

  async isBlacklisted(identifier: string): Promise<boolean> {
    if (this.blacklist.has(identifier)) {
      return true;
    }

    // Check persistent storage
    try {
      const exists = await this.cacheService.exists(`blacklist:${identifier}`);
      if (exists) {
        this.blacklist.add(identifier);
        return true;
      }
    } catch (error) {
      console.error('Error checking blacklist:', error);
    }

    return false;
  }

  // Load whitelist/blacklist from persistent storage
  async loadListsFromStorage(): Promise<void> {
    try {
      // This would typically load from database
      // For now, we'll skip implementation
      console.log('Loading whitelist/blacklist from storage...');
    } catch (error) {
      console.error('Error loading lists from storage:', error);
    }
  }

  // Get rate limiting statistics
  async getStatistics(): Promise<{
    whitelistSize: number;
    blacklistSize: number;
    activeRateLimits: number;
  }> {
    return {
      whitelistSize: this.whitelist.size,
      blacklistSize: this.blacklist.size,
      activeRateLimits: 0, // Would need to query cache for active keys
    };
  }

  // Middleware helper for Express.js
  createMiddleware(type: RateLimitType, tier?: SubscriptionTier) {
    return async (req: any, res: any, next: any) => {
      try {
        const identifier = req.user?.id || req.ip;
        let result: RateLimitResult;

        switch (type) {
          case 'login':
            result = await this.checkLoginRateLimit(identifier);
            break;
          case 'api':
            if (tier) {
              result = await this.checkApiRateLimitByTier(identifier, tier);
            } else {
              result = await this.checkApiRateLimit(identifier);
            }
            break;
          case 'backtest':
            result = await this.checkBacktestRateLimit(identifier);
            break;
          case 'burst':
            result = await this.checkBurstProtection(identifier);
            break;
          default:
            throw new Error(`Unknown rate limit type: ${type}`);
        }

        // Set rate limit headers
        res.set({
          'X-RateLimit-Limit': result.current + result.remaining,
          'X-RateLimit-Remaining': result.remaining,
          'X-RateLimit-Reset': result.resetTime.toISOString(),
        });

        if (!result.allowed) {
          if (result.retryAfter) {
            res.set('Retry-After', result.retryAfter.toString());
          }
          return res.status(429).json({
            error: 'Too Many Requests',
            message: 'Rate limit exceeded',
            retryAfter: result.retryAfter,
          });
        }

        next();
      } catch (error) {
        console.error('Rate limiting middleware error:', error);
        // Fail open
        next();
      }
    };
  }

  // Dynamic rate limit adjustment
  async adjustRateLimit(
    identifier: string, 
    type: RateLimitType, 
    newLimit: number, 
    windowMs?: number
  ): Promise<void> {
    // This would typically update configuration in database
    // For now, we'll just log the adjustment
    console.log(`Adjusting rate limit for ${identifier}: ${type} = ${newLimit} requests per ${windowMs || 'default window'}`);
  }

  // Cleanup expired rate limit data
  async cleanup(): Promise<void> {
    try {
      // This would typically clean up expired keys from cache
      console.log('Cleaning up expired rate limit data...');
    } catch (error) {
      console.error('Error during cleanup:', error);
    }
  }
}