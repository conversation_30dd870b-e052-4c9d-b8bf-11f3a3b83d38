# Getting Started with AI Enhanced Trading Platform

This guide will help you get started with the AI Enhanced Trading Platform MVP.

## Installation

### Prerequisites

Before you begin, ensure you have the following installed:
- Python 3.10 or higher
- Node.js 16 or higher
- Git (optional, for cloning the repository)

### Step 1: Clone the Repository

```bash
git clone <repository-url>
cd AI-Enhanced-Trading-Platform
```

### Step 2: Set Up the Backend

1. Create and activate a virtual environment:
   ```bash
   # Windows
   python -m venv venv
   venv\Scripts\activate

   # macOS/Linux
   python -m venv venv
   source venv/bin/activate
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Create logs directory:
   ```bash
   mkdir logs
   ```

4. Start the backend server:
   ```bash
   python backend/minimal_server.py
   ```

   The server will start on http://localhost:8000

### Step 3: Set Up the Frontend

1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev:mvp
   ```

   The frontend will be available at http://localhost:5173

## Your First Trading Strategy

### Creating a Strategy

1. Open your browser and navigate to http://localhost:5173
2. Log in with the default credentials:
   - Username: admin
   - Password: trading123
3. Click on "Strategies" in the navigation menu
4. Click "Create New Strategy"
5. Fill in the strategy details:
   - Name: My First Strategy
   - Description: A simple moving average crossover strategy
   - Parameters:
     ```json
     {
       "fast_period": 10,
       "slow_period": 30
     }
     ```
6. Click "Save Strategy"

### Backtesting Your Strategy

1. Navigate to the "Backtesting" section
2. Select your strategy from the dropdown
3. Set the backtest parameters:
   - Symbol: EURUSD
   - Timeframe: H1
   - Start Date: 2023-01-01
   - End Date: 2023-12-31
   - Initial Capital: 10000
4. Click "Run Backtest"
5. View the results in the performance chart

### Executing a Trade

1. Navigate to the "Trading" section
2. Select a symbol (e.g., EURUSD)
3. Enter the lot size (e.g., 0.1)
4. Choose the order type (BUY or SELL)
5. Click "Place Order"
6. View your open positions in the "Portfolio" section

## API Usage

### Authentication

The API uses HTTP Basic Authentication. Include your username and password with each request:

```bash
curl -X GET http://localhost:8000/api/strategies -u admin:trading123
```

### Example API Requests

#### Get All Strategies

```bash
curl -X GET http://localhost:8000/api/strategies -u admin:trading123
```

#### Create a Strategy

```bash
curl -X POST http://localhost:8000/api/strategies \
  -H "Content-Type: application/json" \
  -u admin:trading123 \
  -d '{
    "name": "RSI Strategy",
    "description": "Strategy based on RSI indicator",
    "parameters": {
      "rsi_period": 14,
      "overbought": 70,
      "oversold": 30
    }
  }'
```

#### Place a Trade

```bash
curl -X POST http://localhost:8000/api/mvp/trade \
  -H "Content-Type: application/json" \
  -u admin:trading123 \
  -d '{
    "symbol": "EURUSD",
    "order_type": "BUY",
    "lot": 0.1
  }'
```

## Next Steps

- Explore the [API Reference](docs/API_REFERENCE.md) for more API endpoints
- Read the [MVP User Guide](MVP_USER_GUIDE.md) for detailed usage instructions
- Check the [Deployment Guide](DEPLOYMENT_GUIDE.md) for production deployment options

## Troubleshooting

### Common Issues

1. **Connection Refused**: Ensure the backend server is running on port 8000
2. **Authentication Failed**: Verify you're using the correct username and password
3. **Missing Dependencies**: Make sure all dependencies are installed correctly
4. **Port Already in Use**: Check if another application is using port 8000 or 5173

### Getting Help

If you encounter issues not covered in this guide, please:
1. Check the logs in the `logs` directory
2. Consult the documentation
3. Contact the development team for support