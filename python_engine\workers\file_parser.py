"""
Advanced file parsing worker for trading data ingestion
Handles CSV, Excel, JSON files with intelligent format detection
"""

import asyncio
import os
import time
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
import json
import logging
import re

from app.database import SessionLocal
from app.models.database import UploadSessions, UserData
from app.config import settings
from app.utils.validators import validate_trading_data
from app.utils.formatters import normalize_timestamp

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FileParserWorker:
    """Enhanced background file processing worker"""
    
    def __init__(self):
        self.poll_interval = getattr(settings, 'WORKER_POLL_INTERVAL', 10)
        self.max_file_size = getattr(settings, 'MAX_UPLOAD_SIZE', 100 * 1024 * 1024)  # 100MB
        self.chunk_size = 10000  # Process files in chunks
        
    async def run(self):
        """Main worker loop"""
        logger.info("File Parser Worker started")
        
        while True:
            try:
                await self._process_pending_uploads()
                await asyncio.sleep(self.poll_interval)
            except Exception as e:
                logger.error(f"Worker error: {e}", exc_info=True)
                await asyncio.sleep(self.poll_interval)
    
    async def _process_pending_uploads(self):
        """Process all pending upload sessions"""
        db = SessionLocal()
        
        try:
            # Get pending sessions
            pending_sessions = db.query(UploadSessions).filter(
                UploadSessions.status.in_(['mapping_confirmed', 'parsing_in_progress'])
            ).order_by(UploadSessions.created_at).limit(10).all()
            
            for session in pending_sessions:
                try:
                    await self._process_single_upload(session, db)
                except Exception as e:
                    logger.error(f"Failed to process upload {session.id}: {e}")
                    session.status = 'error'
                    session.error_message = str(e)
                    db.commit()
        
        finally:
            db.close()
    
    async def _process_single_upload(self, session: UploadSessions, db: Session):
        """Process a single upload session"""
        logger.info(f"Processing upload session {session.id}")
        
        # Update status
        session.status = 'parsing_in_progress'
        session.updated_at = datetime.utcnow()
        db.commit()
        
        # Load and validate file
        file_path = session.temporary_file_path
        if not os.path.exists(file_path):
            raise ValueError(f"File not found: {file_path}")
        
        # Check file size
        file_size = os.path.getsize(file_path)
        if file_size > self.max_file_size:
            raise ValueError(f"File too large: {file_size} bytes")
        
        # Determine file type and parse
        file_ext = os.path.splitext(file_path)[1].lower()
        
        if file_ext in ['.csv', '.txt']:
            df = await self._parse_csv_file(file_path, session)
        elif file_ext in ['.xlsx', '.xls']:
            df = await self._parse_excel_file(file_path, session)
        elif file_ext == '.json':
            df = await self._parse_json_file(file_path, session)
        else:
            raise ValueError(f"Unsupported file type: {file_ext}")
        
        # Validate and clean data
        df = await self._validate_and_clean_data(df, session)
        
        # Insert into database in chunks
        await self._insert_data_chunked(df, session, db)
        
        # Update session status
        session.status = 'ready'
        session.rows_processed = len(df)
        session.updated_at = datetime.utcnow()
        db.commit()
        
        # Clean up temporary file
        try:
            os.unlink(file_path)
        except:
            pass
        
        logger.info(f"Successfully processed {len(df)} rows for session {session.id}")
    
    async def _parse_csv_file(self, file_path: str, session: UploadSessions) -> pd.DataFrame:
        """Parse CSV file with intelligent delimiter detection"""
        
        # Try different delimiters
        delimiters = [',', ';', '\t', '|']
        best_df = None
        best_columns = 0
        best_delimiter = ','
        
        for delimiter in delimiters:
            try:
                # Read sample to detect structure
                sample_df = pd.read_csv(file_path, delimiter=delimiter, nrows=100)
                if len(sample_df.columns) > best_columns:
                    best_columns = len(sample_df.columns)
                    best_delimiter = delimiter
            except:
                continue
        
        if best_columns == 0:
            raise ValueError("Could not detect CSV structure")
        
        # Read full file with best delimiter
        df = pd.read_csv(
            file_path,
            delimiter=best_delimiter,
            low_memory=False,
            encoding='utf-8',
            parse_dates=False  # We'll handle dates manually
        )
        
        return df
    
    async def _parse_excel_file(self, file_path: str, session: UploadSessions) -> pd.DataFrame:
        """Parse Excel file"""
        
        # Try to read all sheets and pick the largest one
        excel_file = pd.ExcelFile(file_path)
        
        largest_df = None
        largest_rows = 0
        
        for sheet_name in excel_file.sheet_names:
            try:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                if len(df) > largest_rows:
                    largest_rows = len(df)
                    largest_df = df
            except:
                continue
        
        if largest_df is None:
            raise ValueError("Could not read any Excel sheets")
        
        return largest_df
    
    async def _parse_json_file(self, file_path: str, session: UploadSessions) -> pd.DataFrame:
        """Parse JSON file"""
        
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        # Handle different JSON structures
        if isinstance(data, list):
            df = pd.DataFrame(data)
        elif isinstance(data, dict):
            if 'data' in data:
                df = pd.DataFrame(data['data'])
            else:
                df = pd.DataFrame([data])
        else:
            raise ValueError("Unsupported JSON structure")
        
        return df
    
    async def _validate_and_clean_data(
        self, 
        df: pd.DataFrame, 
        session: UploadSessions
    ) -> pd.DataFrame:
        """Validate and clean trading data"""
        
        mapping = session.final_mapping or {}
        timezone = getattr(session, 'timezone', 'UTC') or 'UTC'
        
        # Apply column mapping
        column_map = {}
        required_columns = {}
        
        for original_col, mapped_role in mapping.items():
            if mapped_role != 'Ignore' and original_col in df.columns:
                if mapped_role == 'Time':
                    column_map[original_col] = 'timestamp_utc'
                    required_columns['timestamp_utc'] = original_col
                elif mapped_role in ['Open', 'High', 'Low', 'Close', 'Volume', 'Bid', 'Ask']:
                    new_col = mapped_role.lower()
                    column_map[original_col] = new_col
                    required_columns[new_col] = original_col
        
        # Rename columns
        df = df.rename(columns=column_map)
        
        # Validate required columns
        if 'timestamp_utc' not in df.columns:
            raise ValueError("No timestamp column found")
        
        # Process timestamp column
        df['timestamp_utc'] = await self._normalize_timestamps(
            df['timestamp_utc'], timezone
        )
        
        # Process numeric columns
        numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'bid', 'ask']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Add symbol column if not present
        if 'symbol' not in df.columns:
            # Try to infer from filename or use default
            symbol = self._infer_symbol_from_filename(session.original_filename)
            df['symbol'] = symbol
        
        # Validate OHLC relationships
        if all(col in df.columns for col in ['open', 'high', 'low', 'close']):
            df = self._validate_ohlc_data(df)
        
        # Remove rows with critical missing data
        df = df.dropna(subset=['timestamp_utc'])
        
        # Sort by timestamp
        df = df.sort_values('timestamp_utc')
        
        # Remove duplicates
        df = df.drop_duplicates(subset=['timestamp_utc', 'symbol'])
        
        # Validate data quality
        await self._validate_data_quality(df)
        
        return df
    
    async def _normalize_timestamps(
        self, 
        timestamp_series: pd.Series, 
        timezone: str
    ) -> pd.Series:
        """Normalize timestamps to UTC"""
        
        # Try different timestamp formats
        timestamp_formats = [
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%d %H:%M:%S.%f',
            '%Y.%m.%d %H:%M:%S',
            '%m/%d/%Y %H:%M:%S',
            '%d/%m/%Y %H:%M:%S',
            '%Y-%m-%d',
            None  # Let pandas infer
        ]
        
        for fmt in timestamp_formats:
            try:
                if fmt is None:
                    # Let pandas infer the format
                    timestamps = pd.to_datetime(timestamp_series, infer_datetime_format=True)
                else:
                    timestamps = pd.to_datetime(timestamp_series, format=fmt)
                
                # Convert to UTC if timezone specified
                if timezone != 'UTC':
                    if timestamps.dt.tz is None:
                        # Localize to specified timezone first
                        timestamps = timestamps.dt.tz_localize(timezone)
                    timestamps = timestamps.dt.tz_convert('UTC')
                
                # Remove timezone info for storage
                return timestamps.dt.tz_localize(None)
                
            except:
                continue
        
        raise ValueError("Could not parse timestamp format")
    
    def _infer_symbol_from_filename(self, filename: str) -> str:
        """Infer trading symbol from filename"""
        
        # Common symbol patterns
        symbol_patterns = [
            r'([A-Z]{6})',  # EURUSD, GBPUSD, etc.
            r'([A-Z]{3}[A-Z]{3})',  # EUR USD, GBP USD, etc.
        ]
        
        filename_upper = filename.upper()
        
        for pattern in symbol_patterns:
            match = re.search(pattern, filename_upper)
            if match:
                return match.group(1)
        
        # Default symbol
        return 'UNKNOWN'
    
    def _validate_ohlc_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Validate OHLC data relationships"""
        
        # Check High >= Open, Close and High >= Low
        invalid_high = (df['high'] < df[['open', 'close', 'low']].max(axis=1))
        
        # Check Low <= Open, Close and Low <= High  
        invalid_low = (df['low'] > df[['open', 'close', 'high']].min(axis=1))
        
        # Remove invalid rows
        invalid_rows = invalid_high | invalid_low
        
        if invalid_rows.sum() > 0:
            logger.warning(f"Removing {invalid_rows.sum()} rows with invalid OHLC relationships")
            df = df[~invalid_rows]
        
        return df
    
    async def _validate_data_quality(self, df: pd.DataFrame):
        """Validate overall data quality"""
        
        if len(df) == 0:
            raise ValueError("No valid data rows after cleaning")
        
        # Check for reasonable price ranges
        price_columns = ['open', 'high', 'low', 'close', 'bid', 'ask']
        for col in price_columns:
            if col in df.columns:
                prices = df[col].dropna()
                if len(prices) > 0:
                    if prices.min() <= 0:
                        raise ValueError(f"Invalid {col} prices: found zero or negative values")
                    
                    # Check for extreme outliers (more than 10x the median)
                    median_price = prices.median()
                    outliers = (prices > median_price * 10) | (prices < median_price / 10)
                    if outliers.sum() > len(prices) * 0.01:  # More than 1% outliers
                        logger.warning(f"Found {outliers.sum()} potential outliers in {col}")
        
        # Check timestamp continuity
        timestamps = df['timestamp_utc'].sort_values()
        if len(timestamps) > 1:
            time_diffs = timestamps.diff().dropna()
            median_diff = time_diffs.median()
            
            # Check for large gaps (more than 100x the median interval)
            large_gaps = time_diffs > median_diff * 100
            if large_gaps.sum() > 0:
                logger.warning(f"Found {large_gaps.sum()} large time gaps in data")
    
    async def _insert_data_chunked(
        self, 
        df: pd.DataFrame, 
        session: UploadSessions, 
        db: Session
    ):
        """Insert data into database in chunks"""
        
        total_rows = len(df)
        processed_rows = 0
        
        # Add metadata columns
        df['user_id'] = session.user_id
        df['upload_id'] = session.id
        df['created_at'] = datetime.utcnow()
        
        # Process in chunks
        for chunk_start in range(0, total_rows, self.chunk_size):
            chunk_end = min(chunk_start + self.chunk_size, total_rows)
            chunk_df = df.iloc[chunk_start:chunk_end].copy()
            
            # Convert to database records
            records = []
            for _, row in chunk_df.iterrows():
                record = UserData(
                    user_id=row['user_id'],
                    upload_id=row['upload_id'],
                    symbol=row.get('symbol', 'UNKNOWN'),
                    timestamp_utc=row['timestamp_utc'],
                    open=row.get('open'),
                    high=row.get('high'),
                    low=row.get('low'),
                    close=row.get('close'),
                    volume=row.get('volume'),
                    bid=row.get('bid'),
                    ask=row.get('ask'),
                    extra_data={}
                )
                records.append(record)
            
            # Bulk insert
            db.bulk_save_objects(records)
            db.commit()
            
            processed_rows += len(records)
            
            # Update progress
            session.rows_processed = processed_rows
            db.commit()
            
            logger.info(f"Processed {processed_rows}/{total_rows} rows for session {session.id}")