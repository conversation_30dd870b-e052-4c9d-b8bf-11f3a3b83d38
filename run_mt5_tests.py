#!/usr/bin/env python3
"""
MT5 Bridge Test Runner
Comprehensive test runner for MT5 Bridge with various test scenarios
"""

import sys
import os
import subprocess
import argparse
import time
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def run_command(cmd, description=""):
    """Run a command and return the result"""
    print(f"\n{'='*60}")
    print(f"Running: {description or cmd}")
    print(f"{'='*60}")
    
    start_time = time.time()
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    end_time = time.time()
    
    print(f"Duration: {end_time - start_time:.2f} seconds")
    print(f"Return code: {result.returncode}")
    
    if result.stdout:
        print(f"\nSTDOUT:\n{result.stdout}")
    
    if result.stderr:
        print(f"\nSTDERR:\n{result.stderr}")
    
    return result

def run_basic_tests():
    """Run basic MT5 Bridge tests"""
    cmd = "python -m pytest tests/test_mt5_bridge.py -v --tb=short"
    return run_command(cmd, "Basic MT5 Bridge Tests")

def run_advanced_tests():
    """Run advanced MT5 Bridge tests"""
    cmd = "python -m pytest tests/test_mt5_bridge_advanced.py -v --tb=short"
    return run_command(cmd, "Advanced MT5 Bridge Tests")

def run_unit_tests():
    """Run unit tests only"""
    cmd = "python -m pytest tests/test_mt5_bridge.py tests/test_mt5_bridge_advanced.py -m unit -v"
    return run_command(cmd, "Unit Tests")

def run_integration_tests():
    """Run integration tests only"""
    cmd = "python -m pytest tests/test_mt5_bridge.py tests/test_mt5_bridge_advanced.py -m integration -v"
    return run_command(cmd, "Integration Tests")

def run_performance_tests():
    """Run performance tests"""
    cmd = "python -m pytest tests/test_mt5_bridge.py tests/test_mt5_bridge_advanced.py -m performance -v --tb=short"
    return run_command(cmd, "Performance Tests")

def run_security_tests():
    """Run security tests"""
    cmd = "python -m pytest tests/test_mt5_bridge.py tests/test_mt5_bridge_advanced.py -m security -v --tb=short"
    return run_command(cmd, "Security Tests")

def run_fast_tests():
    """Run fast tests (exclude slow tests)"""
    cmd = 'python -m pytest tests/test_mt5_bridge*.py -m "not slow" -v --tb=short'
    return run_command(cmd, "Fast Tests (excluding slow tests)")

def run_all_tests():
    """Run all MT5 Bridge tests"""
    cmd = "python -m pytest tests/test_mt5_bridge*.py -v --tb=short"
    return run_command(cmd, "All MT5 Bridge Tests")

def run_coverage_tests():
    """Run tests with coverage report"""
    cmd = "python -m pytest tests/test_mt5_bridge*.py --cov=python_engine.mt5_bridge --cov-report=html --cov-report=term-missing -v"
    return run_command(cmd, "Tests with Coverage Report")

def run_parallel_tests():
    """Run tests in parallel"""
    cmd = "python -m pytest tests/test_mt5_bridge*.py -n auto -v"
    return run_command(cmd, "Parallel Test Execution")

def run_specific_test_class(class_name):
    """Run a specific test class"""
    cmd = f"python -m pytest tests/test_mt5_bridge*.py::{class_name} -v --tb=short"
    return run_command(cmd, f"Specific Test Class: {class_name}")

def run_specific_test_method(class_name, method_name):
    """Run a specific test method"""
    cmd = f"python -m pytest tests/test_mt5_bridge*.py::{class_name}::{method_name} -v --tb=short"
    return run_command(cmd, f"Specific Test Method: {class_name}::{method_name}")

def run_smoke_tests():
    """Run smoke tests for quick validation"""
    smoke_tests = [
        "TestMT5BridgeConnection::test_connect_offline_mode",
        "TestMT5BridgeOrderPlacement::test_place_order_buy_market_success",
        "TestMT5BridgePositionsAndOrders::test_get_positions_empty",
        "TestMT5BridgeSymbolInfo::test_get_symbol_info_valid_symbol"
    ]
    
    for test in smoke_tests:
        cmd = f"python -m pytest tests/test_mt5_bridge.py::{test} -v"
        result = run_command(cmd, f"Smoke Test: {test}")
        if result.returncode != 0:
            print(f"❌ Smoke test failed: {test}")
            return result
        else:
            print(f"✅ Smoke test passed: {test}")
    
    print("\n🎉 All smoke tests passed!")
    return subprocess.CompletedProcess(args=[], returncode=0, stdout="", stderr="")

def run_regression_tests():
    """Run regression tests"""
    cmd = "python -m pytest tests/test_mt5_bridge*.py --tb=short --strict-markers"
    return run_command(cmd, "Regression Tests")

def run_stress_tests():
    """Run stress tests"""
    cmd = "python -m pytest tests/test_mt5_bridge_advanced.py::TestMT5BridgePerformance -v --tb=short"
    return run_command(cmd, "Stress Tests")

def validate_test_environment():
    """Validate test environment setup"""
    print("🔍 Validating test environment...")
    
    # Check if required files exist
    required_files = [
        "python_engine/mt5_bridge.py",
        "tests/test_mt5_bridge.py",
        "tests/test_mt5_bridge_advanced.py",
        "tests/conftest.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing required files: {missing_files}")
        return False
    
    # Check if pytest is available
    try:
        result = subprocess.run(["python", "-m", "pytest", "--version"], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ pytest is not available")
            return False
        print(f"✅ pytest version: {result.stdout.strip()}")
    except Exception as e:
        print(f"❌ Error checking pytest: {e}")
        return False
    
    # Check if required packages are available
    required_packages = ["unittest.mock", "datetime", "threading", "concurrent.futures"]
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} is available")
        except ImportError:
            print(f"❌ {package} is not available")
            return False
    
    print("✅ Test environment validation passed!")
    return True

def generate_test_report():
    """Generate comprehensive test report"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"mt5_test_report_{timestamp}.html"
    
    cmd = f"python -m pytest tests/test_mt5_bridge*.py --html={report_file} --self-contained-html -v"
    result = run_command(cmd, f"Generate Test Report: {report_file}")
    
    if result.returncode == 0:
        print(f"✅ Test report generated: {report_file}")
    else:
        print(f"❌ Failed to generate test report")
    
    return result

def run_custom_test_suite():
    """Run custom test suite with specific configuration"""
    print("🚀 Running Custom MT5 Bridge Test Suite")
    
    # Validate environment first
    if not validate_test_environment():
        print("❌ Environment validation failed. Aborting tests.")
        return 1
    
    # Run tests in order of importance
    test_suites = [
        ("Smoke Tests", run_smoke_tests),
        ("Unit Tests", run_unit_tests),
        ("Integration Tests", run_integration_tests),
        ("Security Tests", run_security_tests),
        ("Performance Tests", run_performance_tests),
    ]
    
    results = {}
    total_start_time = time.time()
    
    for suite_name, test_function in test_suites:
        print(f"\n🧪 Running {suite_name}...")
        start_time = time.time()
        result = test_function()
        end_time = time.time()
        
        results[suite_name] = {
            "returncode": result.returncode,
            "duration": end_time - start_time,
            "success": result.returncode == 0
        }
        
        if result.returncode == 0:
            print(f"✅ {suite_name} passed ({end_time - start_time:.2f}s)")
        else:
            print(f"❌ {suite_name} failed ({end_time - start_time:.2f}s)")
    
    total_end_time = time.time()
    
    # Print summary
    print(f"\n{'='*60}")
    print("TEST SUITE SUMMARY")
    print(f"{'='*60}")
    print(f"Total Duration: {total_end_time - total_start_time:.2f} seconds")
    print()
    
    passed_suites = 0
    for suite_name, result in results.items():
        status = "✅ PASSED" if result["success"] else "❌ FAILED"
        print(f"{suite_name:20} {status:10} ({result['duration']:.2f}s)")
        if result["success"]:
            passed_suites += 1
    
    print(f"\nSuites Passed: {passed_suites}/{len(test_suites)}")
    
    if passed_suites == len(test_suites):
        print("🎉 All test suites passed!")
        return 0
    else:
        print("⚠️  Some test suites failed!")
        return 1

def main():
    """Main test runner function"""
    parser = argparse.ArgumentParser(description="MT5 Bridge Test Runner")
    parser.add_argument("--test-type", choices=[
        "basic", "advanced", "unit", "integration", "performance", 
        "security", "fast", "all", "coverage", "parallel", "smoke", 
        "regression", "stress", "custom", "report"
    ], default="custom", help="Type of tests to run")
    
    parser.add_argument("--class", dest="test_class", 
                       help="Run specific test class")
    parser.add_argument("--method", dest="test_method", 
                       help="Run specific test method (requires --class)")
    parser.add_argument("--validate", action="store_true", 
                       help="Validate test environment only")
    
    args = parser.parse_args()
    
    print("🧪 MT5 Bridge Test Runner")
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Working Directory: {os.getcwd()}")
    print(f"Python Version: {sys.version}")
    
    if args.validate:
        return 0 if validate_test_environment() else 1
    
    if args.test_class and args.test_method:
        result = run_specific_test_method(args.test_class, args.test_method)
    elif args.test_class:
        result = run_specific_test_class(args.test_class)
    else:
        # Run based on test type
        test_functions = {
            "basic": run_basic_tests,
            "advanced": run_advanced_tests,
            "unit": run_unit_tests,
            "integration": run_integration_tests,
            "performance": run_performance_tests,
            "security": run_security_tests,
            "fast": run_fast_tests,
            "all": run_all_tests,
            "coverage": run_coverage_tests,
            "parallel": run_parallel_tests,
            "smoke": run_smoke_tests,
            "regression": run_regression_tests,
            "stress": run_stress_tests,
            "custom": run_custom_test_suite,
            "report": generate_test_report
        }
        
        test_function = test_functions.get(args.test_type)
        if test_function:
            if args.test_type == "custom":
                return test_function()
            else:
                result = test_function()
        else:
            print(f"❌ Unknown test type: {args.test_type}")
            return 1
    
    return result.returncode if hasattr(result, 'returncode') else 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)