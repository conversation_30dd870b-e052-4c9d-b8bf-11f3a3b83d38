<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI Forex Trading Platform - Demo</title>
  <script src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
  <script src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    .pip-positive { color: #10b981; }
    .pip-negative { color: #ef4444; }
    .market-open { color: #10b981; }
    .market-closed { color: #ef4444; }
  </style>
</head>
<body>
  <div id="root"></div>
  
  <script type="text/babel">
    const { useState, useEffect } = React;
    
    function ForexTradingApp() {
      const [portfolio, setPortfolio] = useState(null);
      const [forexData, setForexData] = useState({});
      const [selectedPair, setSelectedPair] = useState('EUR/USD');
      const [tradeResult, setTradeResult] = useState(null);
      const [lotSize, setLotSize] = useState(0.1);
      const [leverage, setLeverage] = useState(100);
      const [orderType, setOrderType] = useState('market');
      
      const majorPairs = ['EUR/USD', 'GBP/USD', 'USD/JPY', 'USD/CHF', 'AUD/USD', 'USD/CAD'];
      const crossPairs = ['EUR/GBP', 'EUR/JPY', 'GBP/JPY'];
      
      useEffect(() => {
        // Fetch portfolio
        fetch('http://localhost:3001/api/forex/portfolio')
          .then(res => res.json())
          .then(data => setPortfolio(data))
          .catch(err => console.error('Portfolio fetch error:', err));
        
        // Fetch forex data
        const fetchForexData = async () => {
          for (const pair of [...majorPairs, ...crossPairs]) {
            try {
              const res = await fetch(`http://localhost:3001/api/forex/${pair}`);
              const data = await res.json();
              setForexData(prev => ({ ...prev, [pair]: data }));
            } catch (err) {
              console.error(`Error fetching ${pair}:`, err);
            }
          }
        };
        
        fetchForexData();
        const interval = setInterval(fetchForexData, 2000); // Update every 2 seconds
        
        return () => clearInterval(interval);
      }, []);
      
      const executeTrade = async (side) => {
        try {
          const res = await fetch('http://localhost:3001/api/forex/trade', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              pair: selectedPair,
              side: side,
              lotSize: parseFloat(lotSize),
              leverage: leverage,
              orderType: orderType
            })
          });
          const result = await res.json();
          setTradeResult(result);
          setTimeout(() => setTradeResult(null), 5000);
          
          // Refresh portfolio
          fetch('http://localhost:3001/api/forex/portfolio')
            .then(res => res.json())
            .then(data => setPortfolio(data));
        } catch (err) {
          console.error('Trade execution error:', err);
          setTradeResult({ success: false, error: 'Network error' });
        }
      };
      
      const calculatePipValue = (pair, lots) => {
        if (pair.endsWith('JPY')) {
          return (lots * 100000 * 0.01 / (forexData[pair]?.ask || 100)).toFixed(2);
        }
        return (lots * 10).toFixed(2);
      };
      
      return (
        <div className="min-h-screen bg-gray-900 text-white p-4">
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-6">
              <h1 className="text-3xl font-bold">AI Forex Trading Platform</h1>
              <div className="flex items-center gap-4">
                <span className={forexData['EUR/USD']?.marketOpen ? 'market-open' : 'market-closed'}>
                  ● {forexData['EUR/USD']?.marketOpen ? 'Market Open' : 'Market Closed'}
                </span>
                <span className="text-sm text-gray-400">
                  {new Date().toLocaleString()}
                </span>
              </div>
            </div>
            
            {/* Trade Result Alert */}
            {tradeResult && (
              <div className={`mb-4 p-4 rounded ${tradeResult.success ? 'bg-green-900 border border-green-500' : 'bg-red-900 border border-red-500'}`}>
                <div className="font-semibold">
                  {tradeResult.success ? '✅ Trade Executed' : '❌ Trade Failed'}
                </div>
                <div className="text-sm mt-1">
                  {tradeResult.success ? 
                    `Executed at ${tradeResult.executionPrice} | Pip Value: $${tradeResult.pipValue} | Margin: $${tradeResult.marginRequired}` :
                    tradeResult.error
                  }
                </div>
              </div>
            )}
            
            {/* Forex Pairs Grid */}
            <div className="mb-6">
              <h2 className="text-xl font-semibold mb-3">Major Pairs</h2>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3 mb-4">
                {majorPairs.map(pair => (
                  <div 
                    key={pair} 
                    className={`bg-gray-800 p-3 rounded cursor-pointer border-2 transition-all ${
                      selectedPair === pair ? 'border-blue-500 bg-gray-700' : 'border-transparent hover:border-gray-600'
                    }`}
                    onClick={() => setSelectedPair(pair)}
                  >
                    <h3 className="text-sm font-bold mb-1">{pair}</h3>
                    <div className="grid grid-cols-2 gap-1 text-xs">
                      <div>
                        <span className="text-gray-400">Bid:</span>
                        <div className="text-sm">{forexData[pair]?.bid?.toFixed(5) || '...'}</div>
                      </div>
                      <div>
                        <span className="text-gray-400">Ask:</span>
                        <div className="text-sm">{forexData[pair]?.ask?.toFixed(5) || '...'}</div>
                      </div>
                    </div>
                    <div className="mt-1 text-xs">
                      <span className="text-gray-400">Spread: </span>
                      <span className="text-yellow-400">{forexData[pair]?.spread || '...'}</span>
                    </div>
                    <div className={`text-xs mt-1 ${forexData[pair]?.change24h > 0 ? 'pip-positive' : 'pip-negative'}`}>
                      {forexData[pair]?.change24h > 0 ? '+' : ''}{forexData[pair]?.change24h}%
                    </div>
                  </div>
                ))}
              </div>
              
              <h2 className="text-xl font-semibold mb-3">Cross Pairs</h2>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {crossPairs.map(pair => (
                  <div 
                    key={pair} 
                    className={`bg-gray-800 p-3 rounded cursor-pointer border-2 transition-all ${
                      selectedPair === pair ? 'border-blue-500 bg-gray-700' : 'border-transparent hover:border-gray-600'
                    }`}
                    onClick={() => setSelectedPair(pair)}
                  >
                    <h3 className="text-sm font-bold mb-1">{pair}</h3>
                    <div className="flex justify-between text-xs">
                      <span>{forexData[pair]?.bid?.toFixed(5) || '...'} / {forexData[pair]?.ask?.toFixed(5) || '...'}</span>
                      <span className="text-yellow-400">{forexData[pair]?.spread}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            {/* Trading Panel */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
              <div className="lg:col-span-2 bg-gray-800 p-6 rounded">
                <h2 className="text-xl font-semibold mb-4">Trade {selectedPair}</h2>
                
                {/* Price Display */}
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="bg-gray-700 p-4 rounded">
                    <div className="text-sm text-gray-400">Bid (Sell)</div>
                    <div className="text-2xl font-bold text-red-400">
                      {forexData[selectedPair]?.bid?.toFixed(5) || '...'}
                    </div>
                  </div>
                  <div className="bg-gray-700 p-4 rounded">
                    <div className="text-sm text-gray-400">Ask (Buy)</div>
                    <div className="text-2xl font-bold text-green-400">
                      {forexData[selectedPair]?.ask?.toFixed(5) || '...'}
                    </div>
                  </div>
                </div>
                
                {/* Trade Controls */}
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="block text-sm text-gray-400 mb-1">Lot Size</label>
                    <select 
                      value={lotSize} 
                      onChange={(e) => setLotSize(e.target.value)}
                      className="w-full bg-gray-700 p-2 rounded"
                    >
                      <option value="0.01">0.01 (Micro)</option>
                      <option value="0.1">0.1 (Mini)</option>
                      <option value="1">1.0 (Standard)</option>
                      <option value="5">5.0 (Large)</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm text-gray-400 mb-1">Leverage</label>
                    <select 
                      value={leverage} 
                      onChange={(e) => setLeverage(e.target.value)}
                      className="w-full bg-gray-700 p-2 rounded"
                    >
                      <option value="50">1:50</option>
                      <option value="100">1:100</option>
                      <option value="200">1:200</option>
                      <option value="500">1:500</option>
                    </select>
                  </div>
                </div>
                
                {/* Trade Info */}
                <div className="bg-gray-700 p-3 rounded mb-4">
                  <div className="text-sm text-gray-400">Trade Information</div>
                  <div className="grid grid-cols-2 gap-4 mt-2 text-sm">
                    <div>Pip Value: ${calculatePipValue(selectedPair, lotSize)}</div>
                    <div>Spread: {forexData[selectedPair]?.spread || 0} pips</div>
                  </div>
                </div>
                
                {/* Trade Buttons */}
                <div className="grid grid-cols-2 gap-4">
                  <button 
                    onClick={() => executeTrade('sell')}
                    className="bg-red-600 hover:bg-red-700 p-4 rounded font-semibold transition-colors"
                  >
                    SELL {forexData[selectedPair]?.bid?.toFixed(5) || '...'}
                  </button>
                  <button 
                    onClick={() => executeTrade('buy')}
                    className="bg-green-600 hover:bg-green-700 p-4 rounded font-semibold transition-colors"
                  >
                    BUY {forexData[selectedPair]?.ask?.toFixed(5) || '...'}
                  </button>
                </div>
              </div>
              
              {/* Portfolio Panel */}
              <div className="bg-gray-800 p-6 rounded">
                <h2 className="text-xl font-semibold mb-4">Portfolio</h2>
                {portfolio ? (
                  <div className="space-y-4">
                    <div className="bg-gray-700 p-3 rounded">
                      <div className="text-sm text-gray-400">Balance</div>
                      <div className="text-xl font-bold">${portfolio.balance?.toLocaleString()}</div>
                    </div>
                    <div className="bg-gray-700 p-3 rounded">
                      <div className="text-sm text-gray-400">Equity</div>
                      <div className="text-xl font-bold">${portfolio.equity?.toLocaleString()}</div>
                    </div>
                    <div className="grid grid-cols-2 gap-2">
                      <div className="bg-gray-700 p-2 rounded text-center">
                        <div className="text-xs text-gray-400">Used Margin</div>
                        <div className="font-semibold">${portfolio.usedMargin?.toLocaleString()}</div>
                      </div>
                      <div className="bg-gray-700 p-2 rounded text-center">
                        <div className="text-xs text-gray-400">Free Margin</div>
                        <div className="font-semibold">${portfolio.freeMargin?.toLocaleString()}</div>
                      </div>
                    </div>
                    
                    {/* Open Positions */}
                    <div>
                      <h3 className="font-semibold mb-2">Open Positions</h3>
                      {portfolio.openPositions?.map((pos, idx) => (
                        <div key={idx} className="bg-gray-700 p-2 rounded mb-2 text-sm">
                          <div className="flex justify-between">
                            <span>{pos.pair} {pos.side.toUpperCase()}</span>
                            <span className={pos.profit > 0 ? 'pip-positive' : 'pip-negative'}>
                              ${pos.profit}
                            </span>
                          </div>
                          <div className="text-xs text-gray-400">
                            {pos.lotSize} lots @ {pos.openPrice}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="text-gray-400">Loading portfolio...</div>
                )}
              </div>
            </div>
            
            {/* Footer */}
            <div className="text-center text-gray-400 text-sm">
              <p>Demo Mode - Virtual Trading Only</p>
              <p>WebSocket: <span id="ws-status">Connecting...</span></p>
            </div>
          </div>
        </div>
      );
    }
    
    ReactDOM.render(<ForexTradingApp />, document.getElementById('root'));
  </script>
</body>
</html>