/**
 * MVP Page - Simplified page for testing the MVP backend
 */

import { useState, useEffect } from 'react';
import { mvpApi } from '../services/mvp-api';

interface Strategy {
  id: number;
  name: string;
  description: string;
  parameters: any;
  created_at: string;
}

interface Backtest {
  id: number;
  strategy_id: number;
  start_date: string;
  end_date: string;
  symbol: string;
  timeframe: string;
  initial_capital: number;
  results: any;
  created_at: string;
}

export function MvpPage() {
  const [strategies, setStrategies] = useState<Strategy[]>([]);
  const [backtests, setBacktests] = useState<Backtest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [healthStatus, setHealthStatus] = useState<any>(null);

  // New strategy form state
  const [newStrategy, setNewStrategy] = useState({
    name: '',
    description: '',
    parameters: {
      fast_period: 10,
      slow_period: 30
    }
  });

  // New backtest form state
  const [newBacktest, setNewBacktest] = useState({
    strategy_id: 0,
    start_date: '2023-01-01T00:00:00',
    end_date: '2023-12-31T23:59:59',
    symbol: 'EURUSD',
    timeframe: 'H1',
    initial_capital: 10000
  });

  // Load data on component mount
  useEffect(() => {
    async function loadData() {
      try {
        setLoading(true);
        setError(null);

        // Check health
        const health = await mvpApi.getHealth();
        setHealthStatus(health);

        // Load strategies
        const strategiesData = await mvpApi.getStrategies();
        setStrategies(strategiesData);

        // Load backtests
        const backtestsData = await mvpApi.getBacktests();
        setBacktests(backtestsData);

        setLoading(false);
      } catch (err) {
        setError('Failed to load data. Please check if the server is running.');
        setLoading(false);
        console.error('Error loading data:', err);
      }
    }

    loadData();
  }, []);

  // Handle creating a new strategy
  const handleCreateStrategy = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const result = await mvpApi.createStrategy(newStrategy);
      setStrategies([...strategies, result]);
      setNewStrategy({
        name: '',
        description: '',
        parameters: {
          fast_period: 10,
          slow_period: 30
        }
      });
      alert('Strategy created successfully!');
    } catch (err) {
      console.error('Error creating strategy:', err);
    }
  };

  // Handle creating a new backtest
  const handleCreateBacktest = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const result = await mvpApi.createBacktest(newBacktest);
      setBacktests([...backtests, result]);
      alert('Backtest created successfully!');
    } catch (err) {
      console.error('Error creating backtest:', err);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">AI Trading Platform MVP</h1>
      
      {/* Health Status */}
      <div className="mb-6 p-4 border rounded bg-gray-50">
        <h2 className="text-lg font-semibold mb-2">Server Status</h2>
        {healthStatus ? (
          <div className="text-green-600">
            Server is running. Status: {healthStatus.status}
            <p className="text-sm text-gray-500">Last checked: {healthStatus.timestamp}</p>
          </div>
        ) : loading ? (
          <p>Checking server status...</p>
        ) : (
          <p className="text-red-500">Unable to connect to server</p>
        )}
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <p>Loading data...</p>
        </div>
      ) : error ? (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{error}</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Strategies Section */}
          <div className="border rounded p-4">
            <h2 className="text-xl font-semibold mb-4">Trading Strategies</h2>
            
            {/* Strategies List */}
            <div className="mb-6">
              {strategies.length > 0 ? (
                <ul className="divide-y">
                  {strategies.map((strategy) => (
                    <li key={strategy.id} className="py-3">
                      <h3 className="font-medium">{strategy.name}</h3>
                      <p className="text-sm text-gray-600">{strategy.description}</p>
                      <div className="mt-1 text-xs text-gray-500">
                        Parameters: {JSON.stringify(strategy.parameters)}
                      </div>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-gray-500">No strategies found</p>
              )}
            </div>
            
            {/* Create Strategy Form */}
            <div className="bg-gray-50 p-4 rounded">
              <h3 className="font-medium mb-3">Create New Strategy</h3>
              <form onSubmit={handleCreateStrategy}>
                <div className="mb-3">
                  <label className="block text-sm font-medium mb-1">Name</label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border rounded"
                    value={newStrategy.name}
                    onChange={(e) => setNewStrategy({...newStrategy, name: e.target.value})}
                    required
                  />
                </div>
                <div className="mb-3">
                  <label className="block text-sm font-medium mb-1">Description</label>
                  <textarea
                    className="w-full px-3 py-2 border rounded"
                    value={newStrategy.description}
                    onChange={(e) => setNewStrategy({...newStrategy, description: e.target.value})}
                    rows={2}
                  />
                </div>
                <div className="mb-3">
                  <label className="block text-sm font-medium mb-1">Fast Period</label>
                  <input
                    type="number"
                    className="w-full px-3 py-2 border rounded"
                    value={newStrategy.parameters.fast_period}
                    onChange={(e) => setNewStrategy({
                      ...newStrategy, 
                      parameters: {
                        ...newStrategy.parameters,
                        fast_period: parseInt(e.target.value)
                      }
                    })}
                    required
                  />
                </div>
                <div className="mb-3">
                  <label className="block text-sm font-medium mb-1">Slow Period</label>
                  <input
                    type="number"
                    className="w-full px-3 py-2 border rounded"
                    value={newStrategy.parameters.slow_period}
                    onChange={(e) => setNewStrategy({
                      ...newStrategy, 
                      parameters: {
                        ...newStrategy.parameters,
                        slow_period: parseInt(e.target.value)
                      }
                    })}
                    required
                  />
                </div>
                <button
                  type="submit"
                  className="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded"
                >
                  Create Strategy
                </button>
              </form>
            </div>
          </div>

          {/* Backtests Section */}
          <div className="border rounded p-4">
            <h2 className="text-xl font-semibold mb-4">Backtests</h2>
            
            {/* Backtests List */}
            <div className="mb-6">
              {backtests.length > 0 ? (
                <ul className="divide-y">
                  {backtests.map((backtest) => (
                    <li key={backtest.id} className="py-3">
                      <h3 className="font-medium">
                        Backtest #{backtest.id} - {backtest.symbol} {backtest.timeframe}
                      </h3>
                      <p className="text-sm">
                        Strategy ID: {backtest.strategy_id}
                      </p>
                      <p className="text-sm text-gray-600">
                        {new Date(backtest.start_date).toLocaleDateString()} to {new Date(backtest.end_date).toLocaleDateString()}
                      </p>
                      {backtest.results && (
                        <div className="mt-2 p-2 bg-gray-50 rounded text-sm">
                          <p>Final Capital: ${backtest.results.final_capital.toFixed(2)}</p>
                          <p>Profit Factor: {backtest.results.profit_factor.toFixed(2)}</p>
                          <p>Win Rate: {backtest.results.win_rate.toFixed(1)}%</p>
                        </div>
                      )}
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-gray-500">No backtests found</p>
              )}
            </div>
            
            {/* Create Backtest Form */}
            <div className="bg-gray-50 p-4 rounded">
              <h3 className="font-medium mb-3">Create New Backtest</h3>
              <form onSubmit={handleCreateBacktest}>
                <div className="mb-3">
                  <label className="block text-sm font-medium mb-1">Strategy</label>
                  <select
                    className="w-full px-3 py-2 border rounded"
                    value={newBacktest.strategy_id}
                    onChange={(e) => setNewBacktest({...newBacktest, strategy_id: parseInt(e.target.value)})}
                    required
                  >
                    <option value={0} disabled>Select a strategy</option>
                    {strategies.map(strategy => (
                      <option key={strategy.id} value={strategy.id}>{strategy.name}</option>
                    ))}
                  </select>
                </div>
                <div className="mb-3">
                  <label className="block text-sm font-medium mb-1">Symbol</label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border rounded"
                    value={newBacktest.symbol}
                    onChange={(e) => setNewBacktest({...newBacktest, symbol: e.target.value})}
                    required
                  />
                </div>
                <div className="mb-3">
                  <label className="block text-sm font-medium mb-1">Timeframe</label>
                  <select
                    className="w-full px-3 py-2 border rounded"
                    value={newBacktest.timeframe}
                    onChange={(e) => setNewBacktest({...newBacktest, timeframe: e.target.value})}
                    required
                  >
                    <option value="M1">M1</option>
                    <option value="M5">M5</option>
                    <option value="M15">M15</option>
                    <option value="M30">M30</option>
                    <option value="H1">H1</option>
                    <option value="H4">H4</option>
                    <option value="D1">D1</option>
                  </select>
                </div>
                <div className="mb-3">
                  <label className="block text-sm font-medium mb-1">Initial Capital</label>
                  <input
                    type="number"
                    className="w-full px-3 py-2 border rounded"
                    value={newBacktest.initial_capital}
                    onChange={(e) => setNewBacktest({...newBacktest, initial_capital: parseFloat(e.target.value)})}
                    required
                  />
                </div>
                <div className="grid grid-cols-2 gap-3 mb-3">
                  <div>
                    <label className="block text-sm font-medium mb-1">Start Date</label>
                    <input
                      type="date"
                      className="w-full px-3 py-2 border rounded"
                      value={newBacktest.start_date.split('T')[0]}
                      onChange={(e) => setNewBacktest({...newBacktest, start_date: `${e.target.value}T00:00:00`})}
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">End Date</label>
                    <input
                      type="date"
                      className="w-full px-3 py-2 border rounded"
                      value={newBacktest.end_date.split('T')[0]}
                      onChange={(e) => setNewBacktest({...newBacktest, end_date: `${e.target.value}T23:59:59`})}
                      required
                    />
                  </div>
                </div>
                <button
                  type="submit"
                  className="w-full bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded"
                  disabled={newBacktest.strategy_id === 0}
                >
                  Run Backtest
                </button>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}