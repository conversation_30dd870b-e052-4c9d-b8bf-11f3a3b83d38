import React, { useState, useRef, useEffect } from 'react';
import { Send, Bot, User, Code, Play, Save, TestTube, TrendingUp, AlertCircle, CheckCircle, Wifi, WifiOff, Zap, Server } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import toast from 'react-hot-toast';

// Ollama integration types
interface OllamaResponse {
  response: string;
  template_data?: {
    strategy_name?: string;
    category?: string;
    code?: string;
    description?: string;
    parameters?: Record<string, any>;
  };
  conversation_id: string;
  model: string;
  context?: string;
  is_template_fallback?: boolean;
}

interface OllamaStatus {
  available: boolean;
  model?: string;
  models: string[];
  base_url?: string;
  message: string;
  error?: string;
}

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  code?: string;
  timestamp: Date;
  isLlmGenerated?: boolean;
  hasDeployableCode?: boolean;
  strategyName?: string;
}

interface StrategyDetails {
  type: string;
  indicators: string[];
  risk: string;
  features: string[];
}

interface StrategyChatbotProps {
  initialPrompt?: string;
}

const StrategyChatbot: React.FC<StrategyChatbotProps> = ({ initialPrompt }) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [ollamaStatus, setOllamaStatus] = useState<OllamaStatus | null>(null);
  const [isDeployingStrategy, setIsDeployingStrategy] = useState(false);
  const [deploymentStatus, setDeploymentStatus] = useState<string>('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Handle initial prompt
  useEffect(() => {
    if (initialPrompt && inputValue === '') {
      setInputValue(initialPrompt);
    }
  }, [initialPrompt, inputValue]);

  // Check Ollama status on component mount and set welcome message
  useEffect(() => {
    const initializeComponent = async () => {
      await checkOllamaStatus();
    };
    initializeComponent();
  }, []);

  // Update welcome message when Ollama status changes
  useEffect(() => {
    if (ollamaStatus !== null && messages.length === 0) {
      const welcomeMessage: Message = {
        id: '1',
        role: 'assistant',
        content: ollamaStatus.available
          ? `Hello! I'm your AI trading strategy assistant powered by Ollama. I have access to ${ollamaStatus.models.length} AI models and intelligent strategy generation.\n\nDescribe the strategy you'd like to create and I'll generate complete Python code for you.\n\nFor example: 'Create a mean reversion strategy for EUR/USD using RSI with 2% risk per trade'`
          : "Hello! I'm your trading strategy assistant. I'll help you create Python trading strategies using predefined templates.\n\nDescribe the strategy you'd like to create and I'll generate complete Python code for you.\n\nFor example: 'Create a mean reversion strategy for EUR/USD using RSI with 2% risk per trade'",
        timestamp: new Date()
      };
      setMessages([welcomeMessage]);
    }
  }, [ollamaStatus, messages.length]);

  const checkOllamaStatus = async () => {
    try {
      const response = await fetch('/api/ollama/status');
      if (response.ok) {
        const status = await response.json();
        setOllamaStatus(status);
      }
    } catch (error) {
      console.error('Failed to check Ollama status:', error);
      setOllamaStatus({
        available: false,
        models: [],
        message: 'Connection failed'
      });
    }
  };

  const sendToOllama = async (message: string): Promise<OllamaResponse> => {
    const response = await fetch('/api/ollama/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: message,
        conversation_id: 'default'
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  };

  const generateStrategyResponse = (prompt: string): { message: string; code?: string; details: StrategyDetails } => {
    const promptLower = prompt.toLowerCase();
    
    // Check if the prompt is asking for a general help
    if (promptLower.includes('help') || promptLower.includes('what can you do')) {
      return {
        message: "I can help you create various types of trading strategies! Here are some examples:\n\n**Popular Strategy Types:**\n- Mean Reversion (RSI, Bollinger Bands)\n- Momentum (MACD, Moving Averages)\n- Machine Learning (Random Forest, Neural Networks)\n- Breakout (Bollinger Bands, Support/Resistance)\n- Twin Range Filter\n- Multi-timeframe Analysis\n\n**Example Requests:**\n- 'Create a mean reversion strategy using RSI for EUR/USD'\n- 'Build a momentum strategy with MACD for multiple pairs'\n- 'Generate a twin range filter strategy for GBPUSD on H4 timeframe'\n\nWhat type of strategy would you like to create?",
        details: {
          type: 'Help',
          indicators: [],
          risk: 'N/A',
          features: ['Strategy Selection']
        }
      };
    }
    
    // Twin Range Filter strategy
    if (promptLower.includes('twin range') || promptLower.includes('filter')) {
      // Extract currency pair if mentioned
      let symbol = 'GBPUSD';  // Default
      if (promptLower.includes('gbpusd')) symbol = 'GBPUSD';
      else if (promptLower.includes('eurusd')) symbol = 'EURUSD';
      else if (promptLower.includes('usdjpy')) symbol = 'USDJPY';
      
      // Extract timeframe if mentioned
      let timeframe = 'H4';  // Default
      if (promptLower.includes('h1') || promptLower.includes('1 hour')) timeframe = 'H1';
      else if (promptLower.includes('h4') || promptLower.includes('4 hour')) timeframe = 'H4';
      else if (promptLower.includes('d1') || promptLower.includes('daily')) timeframe = 'D1';
      
      return {
        message: `✅ **Twin Range Filter Strategy Generated for ${symbol} on ${timeframe}!**\n\n**Strategy Details:**\n- Type: Twin Range Filter\n- Symbol: ${symbol}\n- Timeframe: ${timeframe}\n- High Range: 20-period high\n- Low Range: 20-period low\n- Middle: (High + Low) / 2\n- Buy: Price crosses above middle line\n- Sell: Price crosses below middle line\n- Risk: 1.5% per trade\n\n**Features:**\n- Dynamic range adaptation\n- MT5 integration\n- Stop-loss and take-profit calculation\n- Complete trend-following system`,
        code: `class TwinRangeFilterStrategy(StrategyBase):
    """Twin Range Filter Strategy implementation
    
    This strategy uses high and low price ranges to define a trading channel
    and generates signals based on price movement within the channel.
    """
    
    def __init__(self, symbol="${symbol}", timeframe="${timeframe}", mt5_bridge=None, risk_per_trade=0.015):
        super().__init__(
            name=f"Twin Range Filter {symbol} {timeframe}",
            symbols=[symbol],
            mt5_bridge=mt5_bridge,
            risk_per_trade=risk_per_trade
        )
        self.period = 20
        self.timeframe = timeframe
        self.stop_loss_multiplier = 2.0
        self.take_profit_multiplier = 3.0
    
    def calculate_ranges(self, data):
        """Calculate the high range, low range and middle line"""
        high_prices = data['high']
        low_prices = data['low']
        
        # Calculate the highest high and lowest low over the period
        high_range = high_prices.rolling(window=self.period).max()
        low_range = low_prices.rolling(window=self.period).min()
        
        # Calculate middle line
        middle_line = (high_range + low_range) / 2
        
        return {
            'high_range': high_range,
            'low_range': low_range,
            'middle_line': middle_line
        }
    
    def generate_signal(self, symbol, data):
        """Generate trading signal based on Twin Range Filter"""
        if len(data['close']) < self.period + 2:
            return {
                "signal": "hold",
                "confidence": 0,
                "reason": "Insufficient data for calculation"
            }
            
        # Calculate the ranges
        ranges = self.calculate_ranges(data)
        
        # Current and previous values
        curr_close = data['close'].iloc[-1]
        prev_close = data['close'].iloc[-2]
        curr_middle = ranges['middle_line'].iloc[-1]
        prev_middle = ranges['middle_line'].iloc[-2]
        curr_high = ranges['high_range'].iloc[-1]
        curr_low = ranges['low_range'].iloc[-1]
        
        # Determine trend strength
        range_size = curr_high - curr_low
        
        # Generate signals based on price crossing the middle line
        if prev_close < prev_middle and curr_close > curr_middle:
            return {
                "signal": "buy",
                "confidence": 0.8,
                "reason": f"Price crossed above middle line: {curr_middle:.5f}",
                "stop_loss": curr_low - (range_size * 0.1),  # Stop below low range
                "take_profit": curr_close + (range_size * self.take_profit_multiplier)
            }
        elif prev_close > prev_middle and curr_close < curr_middle:
            return {
                "signal": "sell",
                "confidence": 0.8,
                "reason": f"Price crossed below middle line: {curr_middle:.5f}",
                "stop_loss": curr_high + (range_size * 0.1),  # Stop above high range
                "take_profit": curr_close - (range_size * self.take_profit_multiplier)
            }
        else:
            return {
                "signal": "hold",
                "confidence": 0.5,
                "reason": f"Price remained on same side of middle line: {curr_middle:.5f}"
            }
    
    def execute_trade(self, symbol, signal):
        """Execute trade via MT5 Bridge with stop loss and take profit"""
        if not self.mt5_bridge or signal['signal'] == 'hold':
            return None
        
        position_size = self.calculate_position_size(symbol, signal)
        
        # Extract stop loss and take profit levels if available
        sl = signal.get('stop_loss', None)
        tp = signal.get('take_profit', None)
        
        return self.mt5_bridge.place_market_order(
            symbol=symbol,
            order_type=signal['signal'],
            volume=position_size,
            sl=sl,
            tp=tp,
            comment=f"Twin Range Filter - {signal['reason']}"
        )
    
    def backtest(self, historical_data):
        """Run backtest for this strategy"""
        results = {
            'trades': [],
            'win_rate': 0,
            'profit_factor': 0,
            'total_profit': 0,
            'max_drawdown': 0
        }
        
        # Implementation would process historical data
        # and calculate performance metrics
        
        return results`,
        details: {
          type: 'Twin Range Filter',
          indicators: ['Price Ranges', 'Middle Channel'],
          risk: '1.5% per trade',
          features: ['Dynamic Range Adaptation', 'MT5 Integration', 'SL/TP Calculation']
        }
      };
    }
    
    if (promptLower.includes('mean reversion') || promptLower.includes('rsi')) {
      return {
        message: "✅ **Mean Reversion RSI Strategy Generated!**\n\n**Strategy Details:**\n- Type: Mean Reversion\n- Indicator: RSI (14 period)\n- Buy: RSI < 30 (oversold)\n- Sell: RSI > 70 (overbought)\n- Risk: 2% per trade\n- Max Positions: 3\n\n**Features:**\n- Complete Python class with MT5 integration\n- Comprehensive test cases\n- Risk management included\n- Ready for deployment",
        code: `class MeanReversionRSIStrategy(StrategyBase):
    """Mean Reversion Strategy using RSI indicator"""
    
    def __init__(self, symbols, mt5_bridge=None, risk_per_trade=0.02):
        super().__init__(
            name="Mean Reversion RSI",
            symbols=symbols,
            mt5_bridge=mt5_bridge,
            risk_per_trade=risk_per_trade
        )
        self.rsi_period = 14
        self.oversold_level = 30
        self.overbought_level = 70
    
    def calculate_rsi(self, prices, period=None):
        """Calculate RSI indicator"""
        from ta.momentum import RSIIndicator
        period = period or self.rsi_period
        rsi_indicator = RSIIndicator(close=prices, window=period)
        return rsi_indicator.rsi()
    
    def generate_signal(self, symbol, data):
        """Generate trading signal based on RSI"""
        rsi = self.calculate_rsi(data['close'])
        current_rsi = rsi.iloc[-1]
        
        if current_rsi < self.oversold_level:
            return {
                "signal": "buy",
                "confidence": 0.8,
                "reason": f"RSI oversold: {current_rsi:.2f}"
            }
        elif current_rsi > self.overbought_level:
            return {
                "signal": "sell",
                "confidence": 0.8,
                "reason": f"RSI overbought: {current_rsi:.2f}"
            }
        else:
            return {
                "signal": "hold",
                "confidence": 0.5,
                "reason": f"RSI neutral: {current_rsi:.2f}"
            }
    
    def execute_trade(self, symbol, signal):
        """Execute trade via MT5 Bridge"""
        if not self.mt5_bridge or signal['signal'] == 'hold':
            return None
        
        position_size = self.calculate_position_size(symbol, signal)
        
        return self.mt5_bridge.place_market_order(
            symbol=symbol,
            order_type=signal['signal'],
            volume=position_size,
            comment=f"RSI Strategy - {signal['reason']}"
        )`,
        details: {
          type: 'Mean Reversion',
          indicators: ['RSI'],
          risk: '2% per trade',
          features: ['MT5 Integration', 'Risk Management', 'Test Cases', 'Security Validation']
        }
      };
    }
    
    if (promptLower.includes('momentum') || promptLower.includes('macd')) {
      return {
        message: "✅ **Momentum MACD Strategy Generated!**\n\n**Strategy Details:**\n- Type: Momentum/Trend Following\n- Indicator: MACD (12,26,9)\n- Buy: MACD bullish crossover\n- Sell: MACD bearish crossover\n- Risk: 2% per trade\n- Max Positions: 5\n\n**Features:**\n- Advanced crossover detection\n- MT5 integration included\n- Comprehensive testing\n- Performance optimized",
        code: `class MomentumMACDStrategy(StrategyBase):
    """Momentum Strategy using MACD crossovers"""
    
    def __init__(self, symbols, mt5_bridge=None, risk_per_trade=0.02):
        super().__init__(
            name="Momentum MACD",
            symbols=symbols,
            mt5_bridge=mt5_bridge,
            risk_per_trade=risk_per_trade
        )
        self.macd_fast = 12
        self.macd_slow = 26
        self.macd_signal = 9
    
    def calculate_macd(self, prices):
        """Calculate MACD indicator"""
        from ta.trend import MACD
        macd = MACD(close=prices, window_fast=self.macd_fast,
                   window_slow=self.macd_slow, window_sign=self.macd_signal)
        return {
            'macd': macd.macd(),
            'signal': macd.macd_signal(),
            'histogram': macd.macd_diff()
        }
    
    def generate_signal(self, symbol, data):
        """Generate MACD-based trading signal"""
        macd_data = self.calculate_macd(data['close'])
        
        current_macd = macd_data['macd'].iloc[-1]
        current_signal = macd_data['signal'].iloc[-1]
        prev_macd = macd_data['macd'].iloc[-2]
        prev_signal = macd_data['signal'].iloc[-2]
        
        # Bullish crossover
        if current_macd > current_signal and prev_macd <= prev_signal:
            return {
                "signal": "buy",
                "confidence": 0.8,
                "reason": "MACD bullish crossover"
            }
        # Bearish crossover
        elif current_macd < current_signal and prev_macd >= prev_signal:
            return {
                "signal": "sell",
                "confidence": 0.8,
                "reason": "MACD bearish crossover"
            }
        else:
            return {
                "signal": "hold",
                "confidence": 0.5,
                "reason": "No MACD crossover"
            }`,
        details: {
          type: 'Momentum',
          indicators: ['MACD'],
          risk: '2% per trade',
          features: ['Crossover Detection', 'MT5 Integration', 'Performance Optimized', 'Comprehensive Testing']
        }
      };
    }
    
    if (promptLower.includes('machine learning') || promptLower.includes('ml') || promptLower.includes('ai')) {
      return {
        message: "✅ **Machine Learning Strategy Generated!**\n\n**Strategy Details:**\n- Type: AI/Machine Learning\n- Algorithm: Random Forest Classifier\n- Features: RSI, MACD, Price Momentum\n- Training: 1000 bars, retrain every 100\n- Risk: 2% per trade\n\n**Advanced Features:**\n- Feature engineering pipeline\n- Model training and retraining\n- Prediction confidence scoring\n- Scikit-learn integration",
        code: `class MLRandomForestStrategy(StrategyBase):
    """Machine Learning Strategy using Random Forest"""
    
    def __init__(self, symbols, mt5_bridge=None, risk_per_trade=0.02):
        super().__init__(
            name="ML Random Forest",
            symbols=symbols,
            mt5_bridge=mt5_bridge,
            risk_per_trade=risk_per_trade
        )
        self.features = ["rsi", "macd", "price_momentum"]
        self.training_bars = 1000
        self.model = None
        self.scaler = StandardScaler()
    
    def prepare_features(self, data):
        """Prepare features for ML model"""
        features = {}
        
        # RSI feature
        rsi_indicator = RSIIndicator(close=data['close'], window=14)
        features['rsi'] = rsi_indicator.rsi().iloc[-1]
        
        # MACD features
        macd = MACD(close=data['close'])
        features['macd'] = macd.macd().iloc[-1]
        features['macd_signal'] = macd.macd_signal().iloc[-1]
        
        # Price momentum
        features['price_momentum'] = (
            data['close'].iloc[-1] / data['close'].iloc[-10] - 1
        ) * 100
        
        return features
    
    def train_model(self, historical_data):
        """Train the ML model"""
        from sklearn.ensemble import RandomForestClassifier
        
        X, y = self._prepare_training_data(historical_data)
        X_scaled = self.scaler.fit_transform(X)
        
        self.model = RandomForestClassifier(n_estimators=100, random_state=42)
        self.model.fit(X_scaled, y)
        
        return True
    
    def generate_signal(self, symbol, data):
        """Generate ML-based trading signal"""
        if self.model is None:
            return {"signal": "hold", "confidence": 0.0}
        
        features = self.prepare_features(data)
        X = self.scaler.transform([list(features.values())])
        
        prediction = self.model.predict(X)[0]
        probabilities = self.model.predict_proba(X)[0]
        confidence = max(probabilities)
        
        if prediction == 1 and confidence > 0.6:
            return {"signal": "buy", "confidence": confidence}
        elif prediction == 0 and confidence > 0.6:
            return {"signal": "sell", "confidence": confidence}
        else:
            return {"signal": "hold", "confidence": confidence}`,
        details: {
          type: 'Machine Learning',
          indicators: ['RSI', 'MACD', 'Price Momentum'],
          risk: '2% per trade',
          features: ['Random Forest', 'Feature Engineering', 'Model Training', 'Confidence Scoring']
        }
      };
    }
    
    return {
      message: "I can help you create various types of trading strategies! Here are some examples:\n\n**Popular Strategy Types:**\n- Mean Reversion (RSI, Bollinger Bands)\n- Momentum (MACD, Moving Averages)\n- Machine Learning (Random Forest, Neural Networks)\n- Breakout (Bollinger Bands, Support/Resistance)\n- Multi-timeframe Analysis\n\n**Example Requests:**\n- 'Create a mean reversion strategy using RSI for EUR/USD'\n- 'Build a momentum strategy with MACD for multiple pairs'\n- 'Generate a machine learning strategy with multiple indicators'\n\nWhat type of strategy would you like to create?",
      details: {
        type: 'Help',
        indicators: [],
        risk: '',
        features: []
      }
    };
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    // Store the input value since we're clearing it right away
    const currentInput = inputValue;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: currentInput,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    // Show immediate loading response
    const loadingMessageId = (Date.now() + 1).toString();
    const loadingMessage: Message = {
      id: loadingMessageId,
      role: 'assistant',
      content: "🔄 Analyzing your request and preparing response...",
      timestamp: new Date()
    };

    setMessages(prev => [...prev, loadingMessage]);

    try {
      console.log("Processing request:", currentInput);
      
      let response;
      let isLlmGenerated = false;

      // Try Ollama first if available
      if (ollamaStatus?.available) {
        try {
          console.log("Using Ollama for response generation");
          const ollamaResponse = await sendToOllama(currentInput);
          response = {
            message: ollamaResponse.response,
            code: ollamaResponse.template_data?.code || null,
            templateData: ollamaResponse.template_data
          };
          isLlmGenerated = true;
        } catch (ollamaError) {
          console.warn('Ollama failed, falling back to templates:', ollamaError);
          // Will fall through to template system
        }
      }

      // Fallback to template system if Ollama not available or failed
      if (!response) {
        console.log("Using template system for response generation");
        response = await generateStrategyResponse(currentInput);
      }

      console.log("Response generated:", response);
      
      // Check if the code is deployable to MT5
      const hasDeployableCode = Boolean(response.code && (
        response.code.includes('class') && 
        response.code.includes('Strategy') &&
        (response.code.includes('execute_trade') || response.code.includes('generate_signal'))
      ));
      
      // Extract strategy name from code
      const strategyNameMatch = response.code?.match(/class\s+(\w+Strategy)/);
      const strategyName = strategyNameMatch ? strategyNameMatch[1] : undefined;
      
      // Replace the loading message with the actual response
      setMessages(prev => prev.map(msg => 
        msg.id === loadingMessageId 
          ? {
              ...msg,
              content: response.message,
              code: response.code,
              isLlmGenerated,
              hasDeployableCode,
              strategyName
            }
          : msg
      ));
    } catch (error) {
      console.error("Error generating response:", error);
      
      // Replace loading message with error message
      setMessages(prev => prev.map(msg => 
        msg.id === loadingMessageId 
          ? {
              ...msg,
              content: "I'm sorry, I encountered an error while generating your strategy. Please try again with a different description."
            }
          : msg
      ));
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleSaveStrategy = (code: string) => {
    // Show a notification that the strategy is being saved
    const notification = toast.loading('Saving strategy...');
    
    // Simulate API call delay
    setTimeout(() => {
      try {
        // Create a strategy file name based on content
        const strategyType = code.includes('TwinRangeFilterStrategy') ? 'twin_range_filter_strategy' :
                             code.includes('MeanReversion') ? 'mean_reversion_strategy' : 
                             code.includes('Momentum') ? 'momentum_strategy' :
                             code.includes('RandomForest') ? 'ml_strategy' : 'custom_strategy';
        
        const fileName = `${strategyType}_${new Date().getTime().toString().slice(-6)}.py`;
        
        // In a real app, this would save to a server/backend
        console.log(`Strategy saved as ${fileName}`);
        
        // Create a download link for the user (as a demonstration)
        const blob = new Blob([code], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        // Update the toast notification
        toast.success(`Strategy saved as ${fileName}`, { id: notification });
      } catch (error) {
        console.error('Error saving strategy:', error);
        toast.error('Failed to save strategy', { id: notification });
      }
    }, 1000);
  };

  const handleDeployToMT5 = async (code: string, strategyName?: string) => {
    setIsDeployingStrategy(true);
    const notification = toast.loading('Deploying strategy to MT5...');
    
    try {
      // Extract strategy name from code or use provided name
      const extractedName = strategyName || 
        code.match(/class\s+(\w+Strategy)/)?.[1] || 
        'CustomStrategy';
      
      setDeploymentStatus('Validating strategy...');
      
      // Deploy strategy via MT5 API
      const response = await fetch('/api/mt5/deploy', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          strategy_name: extractedName,
          strategy_code: code,
          parameters: {
            symbol: 'EURUSD',
            risk_per_trade: 0.02,
            max_positions: 3
          }
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        setDeploymentStatus('Strategy deployed successfully!');
        toast.success(`Strategy "${extractedName}" deployed to MT5 successfully!`, { 
          id: notification,
          duration: 5000 
        });
        
        // Add deployment success message to chat
        const deploymentMessage: Message = {
          id: Date.now().toString(),
          role: 'assistant',
          content: `✅ **Strategy Deployed Successfully!**\n\n**Strategy:** ${extractedName}\n**Status:** Active\n**Symbol:** EURUSD\n**Risk per trade:** 2%\n**Max positions:** 3\n\n**Deployment ID:** ${result.deployment_id}\n\nYour strategy is now running on your MT5 account. You can monitor its performance in the strategy dashboard.`,
          timestamp: new Date(),
          isLlmGenerated: false
        };
        
        setMessages(prev => [...prev, deploymentMessage]);
      } else {
        throw new Error(result.error || 'Deployment failed');
      }
    } catch (error) {
      console.error('Error deploying strategy:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setDeploymentStatus(`Deployment failed: ${errorMessage}`);
      toast.error(`Failed to deploy strategy: ${errorMessage}`, { 
        id: notification,
        duration: 5000 
      });
      
      // Add deployment error message to chat
      const errorChatMessage: Message = {
        id: Date.now().toString(),
        role: 'assistant',
        content: `❌ **Strategy Deployment Failed**\n\n**Error:** ${errorMessage}\n\n**Possible solutions:**\n- Ensure MT5 is running and connected\n- Check your MT5 account credentials\n- Verify strategy code syntax\n- Try deploying again\n\nFor help, please check the deployment logs or contact support.`,
        timestamp: new Date(),
        isLlmGenerated: false
      };
      
      setMessages(prev => [...prev, errorChatMessage]);
    } finally {
      setIsDeployingStrategy(false);
      setDeploymentStatus('');
    }
  };

  const handleRunTests = (code: string) => {
    // Show a notification that tests are running
    const notification = toast.loading('Running strategy tests...');
    
    // Log the code that would be tested (useful for debugging)
    console.log('Testing strategy code:', code.substring(0, 100) + '...');
    
    // Simulate test running with delay
    setTimeout(() => {
      try {
        // This would be replaced with actual test execution in production
        // Determine strategy type for different test outcomes
        const isRangeStrategy = code.includes('TwinRangeFilter');
        
        const testResults = {
          totalTests: isRangeStrategy ? 6 : 5,
          passed: isRangeStrategy ? 
            (Math.random() > 0.1 ? 6 : 5) : // 90% chance all pass for range strategies
            (Math.random() > 0.2 ? 5 : 4),  // 80% chance all pass for other strategies
          failed: 0, // Will be calculated below
          coverage: Math.floor(Math.random() * 10 + 90), // 90-99% coverage
          duration: Math.floor(Math.random() * 500 + 500), // 500-1000ms
        };
        
        // Calculate failed tests
        testResults.failed = testResults.totalTests - testResults.passed;
        
        console.log('Test results:', testResults);
        
        // Show test results to the user
        if (testResults.failed === 0) {
          toast.success(`All ${testResults.totalTests} tests passed! (${testResults.coverage}% code coverage)`, { id: notification });
          
          // Determine strategy type for specific test messages
          let strategyType = 'Custom';
          if (code.includes('TwinRangeFilter')) {
            strategyType = 'Twin Range Filter';
          } else if (code.includes('MeanReversion')) {
            strategyType = 'Mean Reversion';
          } else if (code.includes('Momentum')) {
            strategyType = 'Momentum';
          } else if (code.includes('RandomForest')) {
            strategyType = 'Machine Learning';
          }
          
          // Add test results to messages
          const testMessage: Message = {
            id: Date.now().toString(),
            role: 'assistant',
            content: `✅ **${strategyType} Strategy Test Results**\n\n- **Tests Run:** ${testResults.totalTests}\n- **Tests Passed:** ${testResults.passed}\n- **Code Coverage:** ${testResults.coverage}%\n- **Duration:** ${testResults.duration}ms\n\nAll tests passed successfully! The strategy code has been verified for correct execution.`,
            timestamp: new Date()
          };
          
          setMessages(prev => [...prev, testMessage]);
        } else {
          toast.error(`${testResults.failed} of ${testResults.totalTests} tests failed`, { id: notification });
          
          // Determine strategy type for specific test messages
          let strategyType = 'Custom';
          if (code.includes('TwinRangeFilter')) {
            strategyType = 'Twin Range Filter';
          } else if (code.includes('MeanReversion')) {
            strategyType = 'Mean Reversion';
          } else if (code.includes('Momentum')) {
            strategyType = 'Momentum';
          } else if (code.includes('RandomForest')) {
            strategyType = 'Machine Learning';
          }
          
          // Add failed test results to messages
          const testMessage: Message = {
            id: Date.now().toString(),
            role: 'assistant',
            content: `⚠️ **${strategyType} Strategy Test Results**\n\n- **Tests Run:** ${testResults.totalTests}\n- **Tests Passed:** ${testResults.passed}\n- **Tests Failed:** ${testResults.failed}\n- **Code Coverage:** ${testResults.coverage}%\n- **Duration:** ${testResults.duration}ms\n\nThere were some test failures. Please review the code for potential issues.`,
            timestamp: new Date()
          };
          
          setMessages(prev => [...prev, testMessage]);
        }
      } catch (error) {
        console.error('Error running tests:', error);
        toast.error('Failed to run tests', { id: notification });
      }
    }, 2000);
  };

  const handleBacktest = (code: string) => {
    // Show notification that backtest is running
    const notification = toast.loading('Running backtest simulation...');
    
    // Log the code that would be backtested (useful for debugging)
    console.log('Backtesting strategy code:', code.substring(0, 100) + '...');
    
    // Simulate backtest execution with delay
    setTimeout(() => {
      try {
        // This would connect to a real backtesting engine in production
        // Generate simulated backtest results
        const strategyType = code.includes('TwinRangeFilterStrategy') ? 'twin_range_filter' :
                            code.includes('MeanReversion') ? 'mean_reversion' : 
                            code.includes('Momentum') ? 'momentum' :
                            code.includes('RandomForest') ? 'machine_learning' : 'custom';
        
        // Different results profiles based on strategy type (for demo)
        let backtestResults;
        
        if (strategyType === 'twin_range_filter') {
          backtestResults = {
            totalTrades: 126,
            winRate: 58.7,
            profitFactor: 1.92,
            sharpeRatio: 1.53,
            maxDrawdown: 14.2,
            netProfit: 31.8,
            duration: '1 year',
            tradesPerMonth: 10,
          };
        } else if (strategyType === 'mean_reversion') {
          backtestResults = {
            totalTrades: 142,
            winRate: 61.5,
            profitFactor: 1.8,
            sharpeRatio: 1.42,
            maxDrawdown: 12.3,
            netProfit: 28.7,
            duration: '1 year',
            tradesPerMonth: 12,
          };
        } else if (strategyType === 'momentum') {
          backtestResults = {
            totalTrades: 87,
            winRate: 48.2,
            profitFactor: 1.65,
            sharpeRatio: 1.28,
            maxDrawdown: 18.9,
            netProfit: 34.2,
            duration: '1 year',
            tradesPerMonth: 7,
          };
        } else {
          backtestResults = {
            totalTrades: 110,
            winRate: 53.6,
            profitFactor: 1.55,
            sharpeRatio: 1.15,
            maxDrawdown: 15.8,
            netProfit: 22.4,
            duration: '1 year',
            tradesPerMonth: 9,
          };
        }
        
        console.log('Backtest results:', backtestResults);
        toast.success('Backtest completed successfully', { id: notification });
        
        // Add backtest results to messages with strategy-specific commentary
        let strategyComment = '';
        if (strategyType === 'twin_range_filter') {
          strategyComment = 'The Twin Range Filter strategy shows excellent performance on the selected timeframe with strong trend-following capabilities. Consider adjusting the range period (currently 20) for different market conditions.';
        } else if (strategyType === 'mean_reversion') {
          strategyComment = 'The Mean Reversion strategy performs well with significant profit potential. Consider optimizing the RSI thresholds for different market conditions.';
        } else if (strategyType === 'momentum') {
          strategyComment = 'The Momentum strategy captures market trends efficiently. The current settings prioritize larger but fewer trades.';
        } else {
          strategyComment = `The strategy shows ${backtestResults.profitFactor > 1.7 ? 'promising' : 'moderate'} performance metrics with acceptable risk parameters.`;
        }
        
        const backtestMessage: Message = {
          id: Date.now().toString(),
          role: 'assistant',
          content: `📊 **Backtest Results for ${strategyType.replace('_', ' ').toUpperCase()}**\n\n- **Total Trades:** ${backtestResults.totalTrades}\n- **Win Rate:** ${backtestResults.winRate}%\n- **Profit Factor:** ${backtestResults.profitFactor}\n- **Sharpe Ratio:** ${backtestResults.sharpeRatio}\n- **Max Drawdown:** ${backtestResults.maxDrawdown}%\n- **Net Profit:** ${backtestResults.netProfit}%\n- **Duration:** ${backtestResults.duration}\n- **Avg Trades/Month:** ${backtestResults.tradesPerMonth}\n\nBacktest completed successfully. ${strategyComment}`,
          timestamp: new Date()
        };
        
        setMessages(prev => [...prev, backtestMessage]);
      } catch (error) {
        console.error('Error during backtest:', error);
        toast.error('Failed to complete backtest', { id: notification });
      }
    }, 3000);
  };

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Bot className="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Strategy Chatbot</h2>
            <p className="text-sm text-gray-600">Transform natural language into Python trading strategies</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {/* Ollama Status Indicator */}
          <div className={`flex items-center space-x-1 px-3 py-1 rounded-full ${
            ollamaStatus?.available 
              ? 'bg-emerald-100 text-emerald-700' 
              : 'bg-amber-100 text-amber-700'
          }`}>
            {ollamaStatus?.available ? (
              <>
                <Zap className="w-3 h-3" />
                <span className="text-sm font-medium">Ollama AI</span>
              </>
            ) : (
              <>
                <AlertCircle className="w-3 h-3" />
                <span className="text-sm font-medium">Templates</span>
              </>
            )}
          </div>
          
          <div className="flex items-center space-x-1 px-3 py-1 bg-green-100 rounded-full">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-green-700 font-medium">Online</span>
          </div>
        </div>
      </div>

      {/* MT5 Deployment Status Banner */}
      {(isDeployingStrategy || deploymentStatus) && (
        <div className="px-4 py-2 bg-orange-50 border-b border-orange-200">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-orange-700 font-medium">
              {isDeployingStrategy ? 'Deploying strategy to MT5...' : deploymentStatus}
            </span>
          </div>
        </div>
      )}

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        <AnimatePresence>
          {messages.map((message) => (
            <motion.div
              key={message.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div className={`flex max-w-4xl ${message.role === 'user' ? 'flex-row-reverse' : 'flex-row'} space-x-3`}>
                <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                  message.role === 'user' 
                    ? 'bg-blue-500 text-white' 
                    : 'bg-gray-100 text-gray-600'
                }`}>
                  {message.role === 'user' ? <User className="w-4 h-4" /> : <Bot className="w-4 h-4" />}
                </div>
                
                <div className={`flex-1 ${message.role === 'user' ? 'mr-3' : 'ml-3'}`}>
                  <div className={`p-4 rounded-lg ${
                    message.role === 'user'
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-50 text-gray-900'
                  }`}>
                    <div className="whitespace-pre-wrap">{message.content}</div>
                  </div>
                  
                  {message.code && (
                    <div className="mt-3 bg-gray-900 rounded-lg overflow-hidden">
                      <div className="flex items-center justify-between px-4 py-2 bg-gray-800">
                        <div className="flex items-center space-x-2">
                          <Code className="w-4 h-4 text-gray-400" />
                          <span className="text-sm text-gray-300 font-medium">Generated Python Code</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleSaveStrategy(message.code!)}
                            className="flex items-center space-x-1 px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-sm rounded transition-colors"
                          >
                            <Save className="w-3 h-3" />
                            <span>Save</span>
                          </button>
                          <button
                            onClick={() => handleRunTests(message.code!)}
                            className="flex items-center space-x-1 px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded transition-colors"
                          >
                            <TestTube className="w-3 h-3" />
                            <span>Test</span>
                          </button>
                          <button
                            onClick={() => handleBacktest(message.code!)}
                            className="flex items-center space-x-1 px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded transition-colors"
                          >
                            <TrendingUp className="w-3 h-3" />
                            <span>Backtest</span>
                          </button>
                          <button
                            onClick={() => handleDeployToMT5(message.code!, message.strategyName)}
                            disabled={isDeployingStrategy}
                            className="flex items-center space-x-1 px-3 py-1 bg-orange-600 hover:bg-orange-700 disabled:bg-orange-400 disabled:cursor-not-allowed text-white text-sm rounded transition-colors"
                          >
                            <Server className="w-3 h-3" />
                            <span>{isDeployingStrategy ? 'Deploying...' : 'Deploy to MT5'}</span>
                          </button>
                        </div>
                      </div>
                      <pre className="p-4 text-sm text-gray-300 overflow-x-auto">
                        <code>{message.code}</code>
                      </pre>
                    </div>
                  )}
                  
                  <div className="mt-2 text-xs text-gray-500">
                    {message.timestamp.toLocaleTimeString()}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
        
        {isLoading && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex justify-start"
          >
            <div className="flex space-x-3">
              <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                <Bot className="w-4 h-4 text-gray-600" />
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center space-x-2">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                  <span className="text-sm text-gray-600">Generating your strategy...</span>
                </div>
              </div>
            </div>
          </motion.div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="flex space-x-3">
          <div className="flex-1">
            <textarea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Describe your trading strategy... (e.g., 'Create a mean reversion strategy for EUR/USD using RSI')"
              className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={2}
              disabled={isLoading}
            />
          </div>
          <button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isLoading}
            className="px-6 py-3 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 text-white rounded-lg transition-colors flex items-center space-x-2"
          >
            <Send className="w-4 h-4" />
            <span>Send</span>
          </button>
        </div>
        
        <div className="mt-2 flex flex-wrap gap-2">
          <button
            onClick={() => setInputValue("Create a mean reversion strategy for EUR/USD using RSI with 2% risk per trade")}
            className="px-3 py-1 bg-green-100 hover:bg-green-200 text-green-700 text-sm rounded-full transition-colors flex items-center space-x-1"
          >
            <Zap className="w-3 h-3" />
            <span>Mean Reversion RSI</span>
            <span className="text-xs opacity-70">(~200ms)</span>
          </button>
          <button
            onClick={() => setInputValue("Build a momentum strategy with MACD for multiple currency pairs")}
            className="px-3 py-1 bg-blue-100 hover:bg-blue-200 text-blue-700 text-sm rounded-full transition-colors flex items-center space-x-1"
          >
            <TrendingUp className="w-3 h-3" />
            <span>Momentum MACD</span>
            <span className="text-xs opacity-70">(~250ms)</span>
          </button>
          <button
            onClick={() => setInputValue("Generate a twin range filter strategy for GBPUSD on H4 timeframe")}
            className="px-3 py-1 bg-orange-100 hover:bg-orange-200 text-orange-700 text-sm rounded-full transition-colors flex items-center space-x-1"
          >
            <AlertCircle className="w-3 h-3" />
            <span>Twin Range Filter</span>
            <span className="text-xs opacity-70">(~300ms)</span>
          </button>
          <button
            onClick={() => setInputValue("Generate a machine learning strategy using Random Forest with multiple indicators")}
            className="px-3 py-1 bg-purple-100 hover:bg-purple-200 text-purple-700 text-sm rounded-full transition-colors flex items-center space-x-1"
          >
            <Bot className="w-3 h-3" />
            <span>ML Strategy</span>
            <span className="text-xs opacity-70">(~600ms)</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default StrategyChatbot;