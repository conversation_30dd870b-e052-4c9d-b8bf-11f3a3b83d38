/**
 * MarketOverview Component
 * Displays current market data and price movements
 */

import { useQuery } from '@tanstack/react-query';
import { Card } from '@/components/ui/card';
import { Spinner } from '@/components/ui/spinner';
import { api } from '@/services/api';

interface MarketData {
  symbol: string;
  bid: number;
  ask: number;
  change: number;
  changePercent: number;
  high: number;
  low: number;
}

export function MarketOverview() {
  // Fetch market data
  const { data: marketData, isLoading } = useQuery({
    queryKey: ['market-data'],
    queryFn: async () => {
      try {
        // This would be replaced with a real API call
        // For now, we'll return mock data
        // const marketData = await api.getMarketData();
        
        // Mock data
        return [
          {
            symbol: 'EURUSD',
            bid: 1.0876,
            ask: 1.0878,
            change: 0.0023,
            changePercent: 0.21,
            high: 1.0892,
            low: 1.0845,
          },
          {
            symbol: 'GBPUSD',
            bid: 1.2543,
            ask: 1.2546,
            change: -0.0015,
            changePercent: -0.12,
            high: 1.2567,
            low: 1.2532,
          },
          {
            symbol: 'USDJPY',
            bid: 149.32,
            ask: 149.35,
            change: 0.45,
            changePercent: 0.30,
            high: 149.67,
            low: 148.92,
          },
          {
            symbol: 'AUDUSD',
            bid: 0.6543,
            ask: 0.6545,
            change: -0.0032,
            changePercent: -0.49,
            high: 0.6582,
            low: 0.6538,
          },
          {
            symbol: 'USDCAD',
            bid: 1.3654,
            ask: 1.3657,
            change: 0.0043,
            changePercent: 0.32,
            high: 1.3672,
            low: 1.3612,
          },
        ] as MarketData[];
      } catch (error) {
        console.error('Error fetching market data:', error);
        throw error;
      }
    },
    staleTime: 60 * 1000, // 1 minute
    refetchInterval: 60 * 1000, // Refetch every minute
  });

  return (
    <Card className="p-6">
      <h3 className="text-lg font-semibold mb-4">Market Overview</h3>
      
      {isLoading ? (
        <div className="flex justify-center py-8">
          <Spinner size="lg" />
        </div>
      ) : marketData && marketData.length > 0 ? (
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-2 font-medium text-gray-500 text-sm">Symbol</th>
                <th className="text-right py-2 font-medium text-gray-500 text-sm">Bid</th>
                <th className="text-right py-2 font-medium text-gray-500 text-sm">Ask</th>
                <th className="text-right py-2 font-medium text-gray-500 text-sm">Change</th>
                <th className="text-right py-2 font-medium text-gray-500 text-sm">High</th>
                <th className="text-right py-2 font-medium text-gray-500 text-sm">Low</th>
              </tr>
            </thead>
            <tbody>
              {marketData.map((item) => (
                <tr key={item.symbol} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-3 text-sm font-medium">{item.symbol}</td>
                  <td className="py-3 text-right text-sm">{item.bid.toFixed(4)}</td>
                  <td className="py-3 text-right text-sm">{item.ask.toFixed(4)}</td>
                  <td className={`py-3 text-right text-sm ${
                    item.change > 0 ? 'text-green-600' : item.change < 0 ? 'text-red-600' : ''
                  }`}>
                    {item.change > 0 ? '+' : ''}{item.change.toFixed(4)} ({item.change > 0 ? '+' : ''}{item.changePercent.toFixed(2)}%)
                  </td>
                  <td className="py-3 text-right text-sm">{item.high.toFixed(4)}</td>
                  <td className="py-3 text-right text-sm">{item.low.toFixed(4)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">
          No market data available
        </div>
      )}
    </Card>
  );
}