/**
 * Homepage Integration Tests
 * Tests complete user flows and interactions
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, test, expect, beforeEach } from 'vitest';
import userEvent from '@testing-library/user-event';
import Homepage from './Homepage';

describe('Homepage Integration Tests', () => {
  beforeEach(() => {
    // Mock scrollIntoView for navigation tests
    Object.defineProperty(Element.prototype, 'scrollIntoView', {
      value: () => {},
      writable: true,
    });
  });

  test('complete user navigation flow', async () => {
    const user = userEvent.setup();
    render(<Homepage />);

    // User sees the hero section
    expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent(
      'Build Trading Strategies Without Code'
    );

    // User clicks on "See How It Works" button
    const heroButton = screen.getByRole('button', { name: /see how it works/i });
    await user.click(heroButton);

    // User navigates through sections using navigation
    const featuresNav = screen.getByRole('button', { name: /features/i });
    await user.click(featuresNav);

    const pricingNav = screen.getByRole('button', { name: /pricing/i });
    await user.click(pricingNav);

    // User sees pricing plans
    expect(screen.getByText('$49')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /start free trial/i })).toBeInTheDocument();
  });

  test('responsive navigation behavior', () => {
    render(<Homepage />);

    // Check that navigation elements are accessible
    const navButtons = screen.getAllByRole('button');
    const navigationButtons = navButtons.filter(button => 
      ['Features', 'How It Works', 'Pricing', 'Contact'].includes(button.textContent || '')
    );

    expect(navigationButtons).toHaveLength(4);

    // Each navigation button should be clickable
    navigationButtons.forEach(button => {
      expect(button).not.toBeDisabled();
    });
  });

  test('feature cards display correctly', () => {
    render(<Homepage />);

    // Check all feature titles are present
    const featureTitles = [
      'Describe Your Strategy',
      'Test With Real Data', 
      'Trade on MT5',
      'AI Assistant',
      'Save Money',
      'Track Performance'
    ];

    featureTitles.forEach(title => {
      expect(screen.getByRole('heading', { name: title })).toBeInTheDocument();
    });

    // Check icons are present (using aria-hidden attribute)
    const icons = screen.getAllByRole('generic', { hidden: true });
    const featureIcons = icons.filter(icon => icon.classList.contains('feature-icon'));
    expect(featureIcons).toHaveLength(6);
  });

  test('pricing section user interaction flow', async () => {
    const user = userEvent.setup();
    render(<Homepage />);

    // Find all pricing buttons
    const getStartedButton = screen.getByRole('button', { name: /get started/i });
    const freeTrialButton = screen.getByRole('button', { name: /start free trial/i });
    const contactSalesButton = screen.getByRole('button', { name: /contact sales/i });

    // All pricing buttons should be clickable
    expect(getStartedButton).not.toBeDisabled();
    expect(freeTrialButton).not.toBeDisabled();
    expect(contactSalesButton).not.toBeDisabled();

    // Pro plan should be featured
    const proCard = freeTrialButton.closest('.pricing-card');
    expect(proCard).toHaveClass('featured');
  });

  test('how it works section step flow', () => {
    render(<Homepage />);

    // Check all steps are present in order
    const steps = ['1', '2', '3', '4'];
    const stepTitles = [
      'Describe Your Idea',
      'We Build It', 
      'Test It',
      'Start Trading'
    ];

    steps.forEach(stepNumber => {
      expect(screen.getByLabelText(`Step ${stepNumber}`)).toBeInTheDocument();
    });

    stepTitles.forEach(title => {
      expect(screen.getByRole('heading', { name: title })).toBeInTheDocument();
    });
  });

  test('footer links and content', () => {
    render(<Homepage />);

    // Check footer links
    const footerLinks = [
      'Privacy Policy',
      'Terms of Service', 
      'Documentation',
      'Support'
    ];

    footerLinks.forEach(linkText => {
      expect(screen.getByLabelText(linkText)).toBeInTheDocument();
    });

    // Check copyright
    expect(screen.getByText(/© 2025 TradeBuilder/)).toBeInTheDocument();
  });

  test('accessibility compliance', () => {
    render(<Homepage />);

    // Check proper heading hierarchy
    const h1Elements = screen.getAllByRole('heading', { level: 1 });
    expect(h1Elements).toHaveLength(1);

    const h2Elements = screen.getAllByRole('heading', { level: 2 });
    expect(h2Elements.length).toBeGreaterThan(0);

    const h3Elements = screen.getAllByRole('heading', { level: 3 });
    expect(h3Elements.length).toBeGreaterThan(0);

    // Check all buttons have proper attributes
    const allButtons = screen.getAllByRole('button');
    allButtons.forEach(button => {
      expect(button).toHaveAttribute('type', 'button');
      expect(button.textContent).toBeTruthy();
    });

    // Check links have proper labels
    const allLinks = screen.getAllByRole('link');
    allLinks.forEach(link => {
      expect(link).toHaveAttribute('aria-label');
    });
  });

  test('performance considerations', () => {
    render(<Homepage />);

    // Check that large content doesn't cause layout issues
    const homepage = document.querySelector('.homepage');
    expect(homepage).toBeInTheDocument();

    // Verify sections are properly structured
    const sections = ['hero', 'features', 'how-it-works', 'pricing', 'contact'];
    sections.forEach(sectionId => {
      const section = document.getElementById(sectionId);
      expect(section).toBeInTheDocument();
    });
  });
});
