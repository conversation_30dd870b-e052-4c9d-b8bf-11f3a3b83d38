version: '3.8'

services:
  ollama-trading:
    image: ollama/ollama:latest
    container_name: ollama-trading-platform
    ports:
      - "11435:11434"  # Map to different port to avoid conflicts
    volumes:
      - ollama_trading_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0:11434
    restart: unless-stopped
    networks:
      - trading-network

  # Optional: Ollama Web UI for this instance
  ollama-webui-trading:
    image: ghcr.io/open-webui/open-webui:main
    container_name: ollama-webui-trading
    ports:
      - "3001:8080"  # Different port from main instance
    environment:
      - OLLAMA_BASE_URL=http://ollama-trading:11434
      - WEBUI_SECRET_KEY=your-secret-key-here
    volumes:
      - ollama_webui_trading_data:/app/backend/data
    depends_on:
      - ollama-trading
    restart: unless-stopped
    networks:
      - trading-network

volumes:
  ollama_trading_data:
    driver: local
  ollama_webui_trading_data:
    driver: local

networks:
  trading-network:
    driver: bridge
