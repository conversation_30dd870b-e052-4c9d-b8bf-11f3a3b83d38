"""
🧬 Darwin Strategy Verification Platform - Python Backend
Flask API server with real market data and professional backtesting
"""

from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import pandas as pd
import numpy as np
import yfinance as yf
from datetime import datetime, timedelta
import json
import os
import logging
from concurrent.futures import ThreadPoolExecutor
import warnings
warnings.filterwarnings('ignore')

# Initialize Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for web frontend

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DarwinVerificationEngine:
    """Professional strategy verification engine with real market data"""
    
    def __init__(self):
        self.market_data = {}
        self.verification_results = {}
        
    def download_market_data(self, symbol, timeframe='1h', period='2y'):
        """Download real market data from Yahoo Finance"""
        try:
            logger.info(f"Downloading {symbol} data for {period}")
            
            # Convert timeframe to yfinance format
            interval_map = {
                '1m': '1m', '5m': '5m', '15m': '15m', '30m': '30m',
                '1h': '1h', '2h': '2h', '4h': '4h', '1d': '1d'
            }
            
            ticker = yf.Ticker(symbol)
            data = ticker.history(period=period, interval=interval_map.get(timeframe, '1h'))
            
            if data.empty:
                raise ValueError(f"No data found for symbol {symbol}")
            
            # Convert to standard format
            market_data = []
            for idx, row in data.iterrows():
                market_data.append({
                    'timestamp': idx.isoformat(),
                    'open': float(row['Open']),
                    'high': float(row['High']),
                    'low': float(row['Low']),
                    'close': float(row['Close']),
                    'volume': int(row['Volume'])
                })
            
            self.market_data[symbol] = market_data
            logger.info(f"Downloaded {len(market_data)} data points for {symbol}")
            
            return {
                'success': True,
                'symbol': symbol,
                'dataPoints': len(market_data),
                'timeframe': timeframe,
                'period': period,
                'data': market_data[-100:]  # Return last 100 points for preview
            }
            
        except Exception as e:
            logger.error(f"Error downloading data: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def calculate_technical_indicators(self, data):
        """Calculate technical indicators for strategy use"""
        df = pd.DataFrame(data)
        
        # Simple Moving Averages
        df['sma_20'] = df['close'].rolling(window=20).mean()
        df['sma_50'] = df['close'].rolling(window=50).mean()
        
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # Bollinger Bands
        df['bb_middle'] = df['close'].rolling(window=20).mean()
        bb_std = df['close'].rolling(window=20).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        
        # MACD
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['macd'] = exp1 - exp2
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        
        return df.fillna(0).to_dict('records')
    
    def backtest_strategy(self, symbol, strategy_code, config):
        """Professional backtesting with realistic execution"""
        try:
            logger.info(f"Starting backtest for {symbol}")
            
            if symbol not in self.market_data:
                return {'success': False, 'error': 'Market data not loaded'}
            
            data = self.market_data[symbol]
            enhanced_data = self.calculate_technical_indicators(data)
            
            # Execute strategy
            signals = self.execute_strategy_code(strategy_code, enhanced_data, config)
            
            # Simulate trading
            backtest_results = self.simulate_trading(signals, enhanced_data, config)
            
            return {
                'success': True,
                'totalTrades': backtest_results['total_trades'],
                'winRate': backtest_results['win_rate'],
                'totalReturn': backtest_results['total_return'],
                'sharpeRatio': backtest_results['sharpe_ratio'],
                'maxDrawdown': backtest_results['max_drawdown'],
                'profitFactor': backtest_results['profit_factor'],
                'trades': backtest_results['trades'][-10:]  # Last 10 trades
            }
            
        except Exception as e:
            logger.error(f"Backtest error: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def execute_strategy_code(self, strategy_code, data, config):
        """Safely execute user strategy code"""
        # Create safe execution environment
        safe_globals = {
            '__builtins__': {},
            'len': len, 'range': range, 'enumerate': enumerate,
            'min': min, 'max': max, 'sum': sum, 'abs': abs,
            'round': round, 'pow': pow,
            'calculateSMA': self.calculate_sma,
            'calculateRSI': self.calculate_rsi,
            'calculateMACD': self.calculate_macd,
            'calculateBollingerBands': self.calculate_bollinger_bands
        }
        
        try:
            # Execute strategy code
            exec(strategy_code, safe_globals)
            strategy_func = safe_globals.get('tradingStrategy')
            
            if not strategy_func:
                raise ValueError("Strategy must define 'tradingStrategy' function")
            
            # Run strategy
            signals = strategy_func(data, config)
            return signals if signals else []
            
        except Exception as e:
            raise ValueError(f"Strategy execution error: {str(e)}")
    
    def simulate_trading(self, signals, data, config):
        """Realistic trading simulation with costs"""
        initial_capital = float(config.get('initialCapital', 100000))
        commission = float(config.get('commission', 7))
        spread_pips = float(config.get('spread', 2))
        
        portfolio = initial_capital
        trades = []
        positions = []
        equity_curve = [initial_capital]
        
        for signal in signals:
            if signal['action'] in ['buy', 'sell']:
                # Find corresponding price data
                signal_time = signal['timestamp']
                price_data = next((d for d in data if d['timestamp'] == signal_time), None)
                
                if price_data:
                    # Apply spread and commission
                    if signal['action'] == 'buy':
                        execution_price = price_data['close'] * (1 + spread_pips/10000)
                    else:
                        execution_price = price_data['close'] * (1 - spread_pips/10000)
                    
                    position_size = min(signal.get('size', 0.1), 1.0)
                    trade_amount = portfolio * position_size
                    
                    if trade_amount > commission:
                        trade = {
                            'timestamp': signal_time,
                            'action': signal['action'],
                            'price': execution_price,
                            'size': position_size,
                            'amount': trade_amount - commission,
                            'commission': commission
                        }
                        trades.append(trade)
                        
                        # Update portfolio (simplified)
                        if signal['action'] == 'buy':
                            positions.append(trade)
                        elif positions:  # Close position
                            position = positions.pop(0)
                            pnl = (execution_price - position['price']) / position['price']
                            portfolio += trade_amount * pnl - commission
                            
                        equity_curve.append(portfolio)
        
        # Calculate performance metrics
        returns = np.diff(equity_curve) / equity_curve[:-1]
        total_return = (portfolio - initial_capital) / initial_capital * 100
        
        win_trades = [t for i, t in enumerate(trades[1::2]) if i < len(trades)//2 and 
                     trades[i*2+1]['price'] > trades[i*2]['price']]
        win_rate = len(win_trades) / max(len(trades)//2, 1) * 100
        
        sharpe_ratio = np.mean(returns) / (np.std(returns) + 1e-8) * np.sqrt(252)
        max_drawdown = np.max(np.maximum.accumulate(equity_curve) - equity_curve) / initial_capital * 100
        
        profit_trades = sum(1 for r in returns if r > 0)
        loss_trades = sum(1 for r in returns if r < 0)
        profit_factor = profit_trades / max(loss_trades, 1)
        
        return {
            'total_trades': len(trades),
            'win_rate': round(win_rate, 2),
            'total_return': round(total_return, 2),
            'sharpe_ratio': round(sharpe_ratio, 3),
            'max_drawdown': round(max_drawdown, 2),
            'profit_factor': round(profit_factor, 2),
            'trades': trades,
            'equity_curve': equity_curve
        }
    
    def run_monte_carlo_simulation(self, symbol, strategy_code, config, simulations=1000):
        """Monte Carlo analysis for strategy robustness"""
        try:
            logger.info(f"Running {simulations} Monte Carlo simulations")
            
            if symbol not in self.market_data:
                return {'success': False, 'error': 'Market data not loaded'}
            
            original_data = self.market_data[symbol]
            results = []
            
            # Run simulations with data variations
            with ThreadPoolExecutor(max_workers=4) as executor:
                futures = []
                for i in range(simulations):
                    # Create slightly randomized data
                    varied_data = self.create_data_variation(original_data)
                    future = executor.submit(self.single_simulation, varied_data, strategy_code, config)
                    futures.append(future)
                
                # Collect results
                for future in futures:
                    try:
                        result = future.result(timeout=30)
                        if result['success']:
                            results.append(result['return'])
                    except Exception as e:
                        logger.warning(f"Simulation failed: {e}")
            
            if not results:
                return {'success': False, 'error': 'All simulations failed'}
            
            # Analyze results
            results = np.array(results)
            positive_returns = np.sum(results > 0)
            success_rate = positive_returns / len(results) * 100
            
            return {
                'success': True,
                'simulations': len(results),
                'successRate': round(success_rate, 1),
                'meanReturn': round(np.mean(results), 2),
                'stdReturn': round(np.std(results), 2),
                'worstCase': round(np.percentile(results, 5), 2),
                'bestCase': round(np.percentile(results, 95), 2),
                'medianReturn': round(np.median(results), 2)
            }
            
        except Exception as e:
            logger.error(f"Monte Carlo error: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def create_data_variation(self, original_data):
        """Create slight variations in market data for Monte Carlo"""
        varied_data = []
        for point in original_data:
            # Add small random variations (±0.1%)
            variation = 1 + (np.random.random() - 0.5) * 0.002
            varied_point = point.copy()
            for key in ['open', 'high', 'low', 'close']:
                varied_point[key] = point[key] * variation
            varied_data.append(varied_point)
        return varied_data
    
    def single_simulation(self, data, strategy_code, config):
        """Run single Monte Carlo simulation"""
        try:
            enhanced_data = self.calculate_technical_indicators(data)
            signals = self.execute_strategy_code(strategy_code, enhanced_data, config)
            results = self.simulate_trading(signals, enhanced_data, config)
            return {'success': True, 'return': results['total_return']}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    # Helper functions for strategy use
    def calculate_sma(self, data, period):
        """Calculate Simple Moving Average"""
        if len(data) < period:
            return [None] * len(data)
        
        sma = []
        for i in range(len(data)):
            if i < period - 1:
                sma.append(None)
            else:
                avg = sum(data[j]['close'] for j in range(i - period + 1, i + 1)) / period
                sma.append(avg)
        return sma
    
    def calculate_rsi(self, data, period=14):
        """Calculate RSI"""
        if len(data) < period + 1:
            return [None] * len(data)
        
        deltas = [data[i]['close'] - data[i-1]['close'] for i in range(1, len(data))]
        gains = [d if d > 0 else 0 for d in deltas]
        losses = [-d if d < 0 else 0 for d in deltas]
        
        rsi = [None]  # First value is None
        for i in range(period - 1, len(gains)):
            avg_gain = sum(gains[max(0, i-period+1):i+1]) / period
            avg_loss = sum(losses[max(0, i-period+1):i+1]) / period
            
            if avg_loss == 0:
                rsi.append(100)
            else:
                rs = avg_gain / avg_loss
                rsi.append(100 - (100 / (1 + rs)))
        
        return rsi
    
    def calculate_macd(self, data, fast=12, slow=26, signal=9):
        """Calculate MACD"""
        if len(data) < slow:
            return {'macd': [None] * len(data), 'signal': [None] * len(data)}
        
        prices = [d['close'] for d in data]
        
        # Simple approximation of EMA using SMA
        fast_sma = self.calculate_sma([{'close': p} for p in prices], fast)
        slow_sma = self.calculate_sma([{'close': p} for p in prices], slow)
        
        macd = []
        for i in range(len(prices)):
            if fast_sma[i] is not None and slow_sma[i] is not None:
                macd.append(fast_sma[i] - slow_sma[i])
            else:
                macd.append(None)
        
        signal_line = self.calculate_sma([{'close': m} for m in macd if m is not None], signal)
        
        return {'macd': macd, 'signal': signal_line}
    
    def calculate_bollinger_bands(self, data, period=20, std_dev=2):
        """Calculate Bollinger Bands"""
        sma = self.calculate_sma(data, period)
        
        upper = []
        lower = []
        
        for i in range(len(data)):
            if sma[i] is not None and i >= period - 1:
                # Calculate standard deviation
                prices = [data[j]['close'] for j in range(i - period + 1, i + 1)]
                std = np.std(prices)
                upper.append(sma[i] + (std * std_dev))
                lower.append(sma[i] - (std * std_dev))
            else:
                upper.append(None)
                lower.append(None)
        
        return {'upper': upper, 'middle': sma, 'lower': lower}

# Initialize verification engine
verification_engine = DarwinVerificationEngine()

# API Routes
@app.route('/')
def serve_frontend():
    """Serve the web frontend"""
    return send_from_directory('.', 'darwin-platform.html')

@app.route('/api/market-data', methods=['POST'])
def download_market_data():
    """Download real market data"""
    data = request.json
    symbol = data.get('symbol', 'EURUSD=X')
    timeframe = data.get('timeframe', '1h')
    period = data.get('period', '2y')
    
    result = verification_engine.download_market_data(symbol, timeframe, period)
    return jsonify(result)

@app.route('/api/backtest', methods=['POST'])
def run_backtest():
    """Run strategy backtest"""
    data = request.json
    symbol = data.get('symbol')
    strategy_code = data.get('strategyCode')
    config = data.get('config', {})
    
    if not symbol or not strategy_code:
        return jsonify({'success': False, 'error': 'Symbol and strategy code required'})
    
    result = verification_engine.backtest_strategy(symbol, strategy_code, config)
    return jsonify(result)

@app.route('/api/monte-carlo', methods=['POST'])
def run_monte_carlo():
    """Run Monte Carlo simulation"""
    data = request.json
    symbol = data.get('symbol')
    strategy_code = data.get('strategyCode')
    config = data.get('config', {})
    simulations = data.get('simulations', 1000)
    
    result = verification_engine.run_monte_carlo_simulation(
        symbol, strategy_code, config, simulations
    )
    return jsonify(result)

@app.route('/api/technical-indicators', methods=['POST'])
def calculate_indicators():
    """Calculate technical indicators"""
    data = request.json
    symbol = data.get('symbol')
    
    if symbol not in verification_engine.market_data:
        return jsonify({'success': False, 'error': 'Market data not loaded'})
    
    try:
        market_data = verification_engine.market_data[symbol]
        indicators = verification_engine.calculate_technical_indicators(market_data)
        return jsonify({'success': True, 'indicators': indicators})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'loaded_symbols': list(verification_engine.market_data.keys())
    })

if __name__ == '__main__':
    logger.info("🧬 Starting Darwin Verification Backend...")
    logger.info("📊 Real market data integration enabled")
    logger.info("🔬 Professional backtesting engine ready")
    
    # Create required directories
    os.makedirs('data', exist_ok=True)
    os.makedirs('reports', exist_ok=True)
    
    # Start Flask server
    app.run(debug=True, host='0.0.0.0', port=5000)
