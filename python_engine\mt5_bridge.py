# python_engine/mt5_bridge.py
import logging
import time
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
import hashlib
import json
import os

# Mock MetaTrader5 for offline development
try:
    import MetaTrader5 as mt5
    MT5_AVAILABLE = True
except ImportError:
    MT5_AVAILABLE = False
    # Create a mock MT5 module for testing
    class MockMT5:
        @staticmethod
        def initialize():
            return False
        
        @staticmethod
        def shutdown():
            pass
        
        @staticmethod
        def order_send(request):
            return None
        
        @staticmethod
        def positions_get():
            return []
        
        @staticmethod
        def orders_get():
            return []
        
        @staticmethod
        def symbol_info(symbol):
            return None
        
        @staticmethod
        def last_error():
            return (0, "No error")
    
    mt5 = MockMT5()

logger = logging.getLogger(__name__)

class MT5BridgeException(Exception):
    """Custom exception for MT5 Bridge operations"""
    pass

class ConnectionState(Enum):
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    ERROR = "error"

class OrderType(Enum):
    BUY = "BUY"
    SELL = "SELL"
    BUY_LIMIT = "BUY_LIMIT"
    SELL_LIMIT = "SELL_LIMIT"
    BUY_STOP = "BUY_STOP"
    SELL_STOP = "SELL_STOP"

@dataclass
class OrderRequest:
    symbol: str
    volume: float
    order_type: OrderType
    price: Optional[float] = None
    sl: Optional[float] = None  # Stop Loss
    tp: Optional[float] = None  # Take Profit
    comment: str = ""
    magic: int = 0
    deviation: int = 20

@dataclass
class OrderResult:
    ticket: int
    retcode: int
    deal: Optional[int] = None
    order: Optional[int] = None
    volume: float = 0.0
    price: float = 0.0
    bid: float = 0.0
    ask: float = 0.0
    comment: str = ""
    request_id: int = 0
    retcode_external: int = 0

@dataclass
class Position:
    ticket: int
    symbol: str
    volume: float
    type: int
    price_open: float
    price_current: float
    profit: float
    swap: float
    comment: str
    time: datetime

@dataclass
class SymbolInfo:
    name: str
    bid: float
    ask: float
    spread: int
    digits: int
    point: float
    trade_mode: int
    volume_min: float
    volume_max: float
    volume_step: float

@dataclass
class ErrorResult:
    success: bool
    error_code: int
    error_message: str

class MT5Bridge:
    """
    Robust MT5 Bridge with comprehensive error handling and offline mocking capability.
    Supports both live MT5 connection and offline testing/development.
    """
    
    def __init__(self, 
                 timeout: int = 60000,
                 login: Optional[int] = None,
                 password: Optional[str] = None,
                 server: Optional[str] = None,
                 offline_mode: bool = False):
        """
        Initialize MT5 Bridge
        
        Args:
            timeout: Connection timeout in milliseconds
            login: MT5 account login
            password: MT5 account password
            server: MT5 server name
            offline_mode: Force offline mode for testing
        """
        self.timeout = timeout
        self.login = login
        self.password = password
        self.server = server
        self.offline_mode = offline_mode or not MT5_AVAILABLE
        
        self.connection_state = ConnectionState.DISCONNECTED
        self.last_error = (0, "No error")
        self.connection_attempts = 0
        self.max_connection_attempts = 3
        
        # Offline simulation data
        self._mock_positions: List[Position] = []
        self._mock_orders: List[OrderResult] = []
        self._mock_symbols: Dict[str, SymbolInfo] = self._initialize_mock_symbols()
        self._next_ticket = 1000
        
        # Audit trail
        self.operation_log: List[Dict[str, Any]] = []
        
        # Magic number for order identification
        self.magic_number = int(os.getenv("MT5_MAGIC_NUMBER", "12345"))
        
        logger.info(f"MT5Bridge initialized - Offline mode: {self.offline_mode}")
    
    def connect(self) -> bool:
        """
        Connect to MetaTrader 5 terminal.
        Returns True if successful, False otherwise.
        Handles ConnectionError and logs last_error.
        """
        try:
            if not mt5.initialize():
                error = mt5.last_error()
                self.last_error = error[1]
                return False
            self.connected = True
            return True
        except ConnectionError as ce:
            self.last_error = str(ce)
            self.connected = False
            return False
        except Exception as e:
            self.last_error = str(e)
            self.connected = False
            return False
    
    def _connect_live(self) -> bool:
        """Connect to live MT5 terminal"""
        self.connection_state = ConnectionState.CONNECTING
        self.connection_attempts += 1
        
        try:
            # Initialize MT5 connection
            if not mt5.initialize():
                self.last_error = mt5.last_error()
                self.connection_state = ConnectionState.ERROR
                
                if self.connection_attempts >= self.max_connection_attempts:
                    error_msg = f"Failed to connect to MT5 after {self.max_connection_attempts} attempts. Error: {self.last_error}"
                    self._log_operation("connection_failed", {"error": error_msg, "attempts": self.connection_attempts})
                    raise MT5BridgeException(error_msg)
                
                return False
            
            # Login if credentials provided
            if self.login and self.password and self.server:
                if not mt5.login(self.login, self.password, self.server):
                    self.last_error = mt5.last_error()
                    self.connection_state = ConnectionState.ERROR
                    error_msg = f"Failed to login to MT5. Error: {self.last_error}"
                    self._log_operation("login_failed", {"error": error_msg})
                    raise MT5BridgeException(error_msg)
            
            self.connection_state = ConnectionState.CONNECTED
            self._log_operation("connected", {"mode": "live", "attempts": self.connection_attempts})
            logger.info("Successfully connected to MT5 terminal")
            return True
            
        except Exception as e:
            self.connection_state = ConnectionState.ERROR
            error_msg = f"Connection error: {str(e)}"
            self._log_operation("connection_error", {"error": error_msg})
            raise MT5BridgeException(error_msg)
    
    def _connect_offline(self) -> bool:
        """Connect in offline mode for testing"""
        self.connection_state = ConnectionState.CONNECTED
        self._log_operation("connected", {"mode": "offline"})
        logger.info("Connected in offline mode")
        return True
    
    def disconnect(self) -> None:
        """Disconnect from MT5 terminal"""
        if not self.offline_mode and self.connection_state == ConnectionState.CONNECTED:
            mt5.shutdown()
        
        self.connection_state = ConnectionState.DISCONNECTED
        self._log_operation("disconnected", {})
        logger.info("Disconnected from MT5")
    
    def is_connected(self) -> bool:
        """Check if connected to MT5"""
        return self.connection_state == ConnectionState.CONNECTED
    
    def place_order(self, 
                   symbol: str, 
                   lot: float, 
                   order_type: str, 
                   price: Optional[float] = None,
                   sl: Optional[float] = None, 
                   tp: Optional[float] = None,
                   comment: str = "",
                   magic: int = 0) -> Dict[str, Any]:
        """
        Place a trading order
        
        Args:
            symbol: Trading symbol (e.g., "EURUSD")
            lot: Order volume
            order_type: Order type ("BUY", "SELL", etc.)
            price: Order price (for pending orders)
            sl: Stop loss price
            tp: Take profit price
            comment: Order comment
            magic: Magic number
            
        Returns:
            Dict containing order result
            
        Raises:
            MT5BridgeException: If order placement fails
        """
        if not self.is_connected():
            raise MT5BridgeException("Not connected to MT5")
        
        # Validate inputs
        self._validate_order_inputs(symbol, lot, order_type, price, sl, tp)
        
        # Create order request
        order_request = OrderRequest(
            symbol=symbol,
            volume=lot,
            order_type=OrderType(order_type.upper()),
            price=price,
            sl=sl,
            tp=tp,
            comment=comment,
            magic=magic
        )
        
        if self.offline_mode:
            return self._place_order_offline(order_request)
        else:
            return self._place_order_live(order_request)
    
    def _validate_order_inputs(self, symbol: str, lot: float, order_type: str, 
                              price: Optional[float], sl: Optional[float], tp: Optional[float]) -> None:
        """Validate order inputs"""
        if not symbol or not isinstance(symbol, str):
            raise MT5BridgeException("Invalid symbol")
        
        if lot <= 0:
            raise MT5BridgeException("Invalid lot size")
        
        if order_type.upper() not in [ot.value for ot in OrderType]:
            raise MT5BridgeException(f"Invalid order type: {order_type}")
        
        # Validate price for pending orders
        if order_type.upper() in ["BUY_LIMIT", "SELL_LIMIT", "BUY_STOP", "SELL_STOP"]:
            if price is None or price <= 0:
                raise MT5BridgeException("Price required for pending orders")
        
        # Validate stop loss and take profit
        if sl is not None and sl <= 0:
            raise MT5BridgeException("Invalid stop loss")
        
        if tp is not None and tp <= 0:
            raise MT5BridgeException("Invalid take profit")
    
    def _place_order_live(self, order_request: OrderRequest) -> Dict[str, Any]:
        """Place order in live MT5"""
        try:
            # Convert to MT5 format
            mt5_request = self._convert_to_mt5_request(order_request)
            
            # Send order
            result = mt5.order_send(mt5_request)
            
            if result is None:
                self.last_error = mt5.last_error()
                error_msg = f"Order send failed: {self.last_error}"
                self._log_operation("order_failed", {"request": asdict(order_request), "error": error_msg})
                raise MT5BridgeException(error_msg)
            
            # Convert result
            order_result = self._convert_mt5_result(result)
            
            if order_result.retcode != 10009:  # TRADE_RETCODE_DONE
                error_msg = f"Order rejected with retcode: {order_result.retcode}"
                self._log_operation("order_rejected", {"request": asdict(order_request), "result": asdict(order_result)})
                raise MT5BridgeException(error_msg)
            
            self._log_operation("order_placed", {"request": asdict(order_request), "result": asdict(order_result)})
            return asdict(order_result)
            
        except Exception as e:
            error_msg = f"Order placement error: {str(e)}"
            self._log_operation("order_error", {"request": asdict(order_request), "error": error_msg})
            raise MT5BridgeException(error_msg)
    
    def _place_order_offline(self, order_request: OrderRequest) -> Dict[str, Any]:
        """Place order in offline mode (simulation)"""
        # Simulate order processing
        ticket = self._next_ticket
        self._next_ticket += 1
        
        # Get symbol info
        symbol_info = self._mock_symbols.get(order_request.symbol)
        if not symbol_info:
            raise MT5BridgeException(f"Invalid symbol: {order_request.symbol}")
        
        # Simulate successful order
        order_result = OrderResult(
            ticket=ticket,
            retcode=10009,  # TRADE_RETCODE_DONE
            deal=ticket,
            order=ticket,
            volume=order_request.volume,
            price=symbol_info.ask if order_request.order_type == OrderType.BUY else symbol_info.bid,
            bid=symbol_info.bid,
            ask=symbol_info.ask,
            comment=order_request.comment,
            request_id=0,
            retcode_external=0
        )
        
        # Store for tracking
        self._mock_orders.append(order_result)
        
        # Create position for market orders
        if order_request.order_type in [OrderType.BUY, OrderType.SELL]:
            position = Position(
                ticket=ticket,
                symbol=order_request.symbol,
                volume=order_request.volume,
                type=0 if order_request.order_type == OrderType.BUY else 1,
                price_open=order_result.price,
                price_current=order_result.price,
                profit=0.0,
                swap=0.0,
                comment=order_request.comment,
                time=datetime.now()
            )
            self._mock_positions.append(position)
        
        self._log_operation("order_placed_offline", {"request": asdict(order_request), "result": asdict(order_result)})
        return asdict(order_result)
    
    def submit_order(self, order: OrderRequest) -> ErrorResult:
        if not order.symbol or order.volume <= 0:
            return ErrorResult(success=False, error_code=400, error_message="Invalid parameters")
        
        if not self.is_connected():
            return ErrorResult(success=False, error_code=500, error_message="Not connected to MT5")
        
        # Validate inputs
        self._validate_order_inputs(order.symbol, order.volume, order.order_type, order.price, order.sl, order.tp)
        
        # Create order request
        order_request = OrderRequest(
            symbol=order.symbol,
            volume=order.volume,
            order_type=OrderType(order.order_type.upper()),
            price=order.price,
            sl=order.sl,
            tp=order.tp,
            comment=order.comment,
            magic=order.magic
        )
        
        try:
            if self.offline_mode:
                result = self._place_order_offline(order_request)
            else:
                result = self._place_order_live(order_request)
            
            return ErrorResult(success=True, error_code=0, error_message="",
                               ticket=result.ticket, retcode=result.retcode)
        
        except MT5BridgeException as e:
            return ErrorResult(success=False, error_code=500, error_message=str(e))
        except Exception as e:
            return ErrorResult(success=False, error_code=500, error_message="Unexpected error: " + str(e))
    
    def get_positions(self) -> List[Dict[str, Any]]:
        """Get all open positions"""
        if not self.is_connected():
            raise MT5BridgeException("Not connected to MT5")
        
        if self.offline_mode:
            return [asdict(pos) for pos in self._mock_positions]
        
        try:
            positions = mt5.positions_get()
            if positions is None:
                self.last_error = mt5.last_error()
                return []
            
            return [self._convert_position(pos) for pos in positions]
            
        except Exception as e:
            error_msg = f"Failed to get positions: {str(e)}"
            self._log_operation("get_positions_error", {"error": error_msg})
            raise MT5BridgeException(error_msg)
    
    def get_orders(self) -> List[Dict[str, Any]]:
        """Get all pending orders"""
        if not self.is_connected():
            raise MT5BridgeException("Not connected to MT5")
        
        if self.offline_mode:
            return [asdict(order) for order in self._mock_orders]
        
        try:
            orders = mt5.orders_get()
            if orders is None:
                self.last_error = mt5.last_error()
                return []
            
            return [self._convert_order(order) for order in orders]
            
        except Exception as e:
            error_msg = f"Failed to get orders: {str(e)}"
            self._log_operation("get_orders_error", {"error": error_msg})
            raise MT5BridgeException(error_msg)
    
    def get_symbol_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get symbol information"""
        if not self.is_connected():
            raise MT5BridgeException("Not connected to MT5")
        
        if self.offline_mode:
            symbol_info = self._mock_symbols.get(symbol)
            return asdict(symbol_info) if symbol_info else None
        
        try:
            info = mt5.symbol_info(symbol)
            if info is None:
                return None
            
            return self._convert_symbol_info(info)
            
        except Exception as e:
            error_msg = f"Failed to get symbol info: {str(e)}"
            self._log_operation("get_symbol_info_error", {"symbol": symbol, "error": error_msg})
            raise MT5BridgeException(error_msg)
    
    def close_position(self, ticket: int) -> Dict[str, Any]:
        """Close a position by ticket"""
        if not self.is_connected():
            raise MT5BridgeException("Not connected to MT5")
        
        if self.offline_mode:
            return self._close_position_offline(ticket)
        
        # Implementation for live MT5 would go here
        raise NotImplementedError("Live position closing not implemented yet")
    
    def _close_position_offline(self, ticket: int) -> Dict[str, Any]:
        """Close position in offline mode"""
        # Find position
        position = None
        for i, pos in enumerate(self._mock_positions):
            if pos.ticket == ticket:
                position = pos
                del self._mock_positions[i]
                break
        
        if not position:
            raise MT5BridgeException(f"Position {ticket} not found")
        
        # Simulate close
        close_result = OrderResult(
            ticket=self._next_ticket,
            retcode=10009,
            deal=self._next_ticket,
            order=self._next_ticket,
            volume=position.volume,
            price=position.price_current,
            comment=f"Close #{ticket}"
        )
        
        self._next_ticket += 1
        self._log_operation("position_closed_offline", {"ticket": ticket, "result": asdict(close_result)})
        return asdict(close_result)
    
    def _initialize_mock_symbols(self) -> Dict[str, SymbolInfo]:
        """Initialize mock symbol data for offline mode"""
        symbols = {}
        
        # Major forex pairs
        forex_pairs = [
            ("EURUSD", 1.1000, 1.1002, 2),
            ("GBPUSD", 1.2500, 1.2503, 3),
            ("USDJPY", 150.00, 150.03, 3),
            ("USDCHF", 0.8800, 0.8802, 2),
            ("AUDUSD", 0.6500, 0.6502, 2),
        ]
        
        for symbol, bid, ask, spread in forex_pairs:
            symbols[symbol] = SymbolInfo(
                name=symbol,
                bid=bid,
                ask=ask,
                spread=spread,
                digits=5 if symbol != "USDJPY" else 3,
                point=0.00001 if symbol != "USDJPY" else 0.001,
                trade_mode=4,  # Full trading
                volume_min=0.01,
                volume_max=100.0,
                volume_step=0.01
            )
        
        return symbols
    
    def _convert_to_mt5_request(self, order_request: OrderRequest) -> Dict[str, Any]:
        """Convert OrderRequest to MT5 format"""
        # This would contain the actual MT5 request format conversion
        # Simplified for demonstration
        return asdict(order_request)
    
    def _convert_mt5_result(self, mt5_result) -> OrderResult:
        """Convert MT5 result to OrderResult"""
        # This would contain the actual MT5 result conversion
        # Simplified for demonstration
        return OrderResult(
            ticket=getattr(mt5_result, 'order', 0),
            retcode=getattr(mt5_result, 'retcode', 0)
        )
    
    def _convert_position(self, mt5_position) -> Dict[str, Any]:
        """Convert MT5 position to dict"""
        # Simplified conversion
        return {}
    
    def _convert_order(self, mt5_order) -> Dict[str, Any]:
        """Convert MT5 order to dict"""
        # Simplified conversion
        return {}
    
    def _convert_symbol_info(self, mt5_symbol_info) -> Dict[str, Any]:
        """Convert MT5 symbol info to dict"""
        # Simplified conversion
        return {}
    
    def _log_operation(self, operation: str, details: Dict[str, Any]) -> None:
        """Log operation for audit trail"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "operation": operation,
            "connection_state": self.connection_state.value,
            "offline_mode": self.offline_mode,
            "details": details
        }
        self.operation_log.append(log_entry)
        
        # Keep log size manageable
        if len(self.operation_log) > 1000:
            self.operation_log = self.operation_log[-500:]
    
    def get_operation_log(self) -> List[Dict[str, Any]]:
        """Get operation log for debugging"""
        return self.operation_log.copy()
    
    def get_connection_info(self) -> Dict[str, Any]:
        """Get connection information"""
        return {
            "state": self.connection_state.value,
            "offline_mode": self.offline_mode,
            "connection_attempts": self.connection_attempts,
            "last_error": self.last_error,
            "mt5_available": MT5_AVAILABLE
        }