<?xml version="1.0" encoding="utf-8"?><testsuites name="pytest tests"><testsuite name="pytest" errors="0" failures="8" skipped="0" tests="56" time="0.617" timestamp="2025-07-08T18:56:23.330405+01:00" hostname="DESKTOP-JOMI8K7"><testcase classname="tests.test_secure_mt5_bridge.TestTradeOrder" name="test_trade_order_creation" time="0.121" /><testcase classname="tests.test_secure_mt5_bridge.TestTradeOrder" name="test_trade_order_decimal_conversion" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestTradeOrder" name="test_trade_order_hash_generation" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestTradeOrder" name="test_trade_order_to_dict" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestTradeReceipt" name="test_trade_receipt_creation" time="0.004" /><testcase classname="tests.test_secure_mt5_bridge.TestTradeReceipt" name="test_trade_receipt_integrity_verification" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestTradeReceipt" name="test_trade_receipt_audit_trail" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestMarketData" name="test_market_data_creation" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestMarketData" name="test_market_data_integrity_verification" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestDataIntegrityValidator" name="test_store_and_verify_price_data" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestDataIntegrityValidator" name="test_verify_price_data_within_tolerance" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestDataIntegrityValidator" name="test_check_hash_recent_data" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestDataIntegrityValidator" name="test_integrity_report_generation" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestDataIntegrityValidator" name="test_thread_safety" time="0.003" /><testcase classname="tests.test_secure_mt5_bridge.TestSecureMarketDataProvider" name="test_get_market_data" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestSecureMarketDataProvider" name="test_verify_spread_within_limits" time="0.002"><failure message="AssertionError: assert False is True&#10; +  where False = verify_spread('EURUSD')&#10; +    where verify_spread = &lt;secure_mt5_bridge.SecureMarketDataProvider object at 0x00000162C9A9E710&gt;.verify_spread">tests\test_secure_mt5_bridge.py:308: in test_verify_spread_within_limits
    assert provider.verify_spread("EURUSD") is True
E   AssertionError: assert False is True
E    +  where False = verify_spread('EURUSD')
E    +    where verify_spread = &lt;secure_mt5_bridge.SecureMarketDataProvider object at 0x00000162C9A9E710&gt;.verify_spread</failure></testcase><testcase classname="tests.test_secure_mt5_bridge.TestSecureMarketDataProvider" name="test_spread_violation_logging" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestSecureMarketDataProvider" name="test_market_data_caching" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestZeroHallucinationValidator" name="test_validate_valid_order" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestZeroHallucinationValidator" name="test_validate_volume_positive[volume0-True]" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestZeroHallucinationValidator" name="test_validate_volume_positive[volume1-True]" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestZeroHallucinationValidator" name="test_validate_volume_positive[volume2-False]" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestZeroHallucinationValidator" name="test_validate_volume_positive[volume3-False]" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestZeroHallucinationValidator" name="test_validate_symbol_format[EURUSD-True]" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestZeroHallucinationValidator" name="test_validate_symbol_format[GBPUSD-True]" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestZeroHallucinationValidator" name="test_validate_symbol_format[USDJPY-True]" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestZeroHallucinationValidator" name="test_validate_symbol_format[INVALID-False]" time="0.002" /><testcase classname="tests.test_secure_mt5_bridge.TestZeroHallucinationValidator" name="test_validate_symbol_format[-False]" time="0.002" /><testcase classname="tests.test_secure_mt5_bridge.TestZeroHallucinationValidator" name="test_validate_symbol_format[EUR-False]" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestZeroHallucinationValidator" name="test_validate_price_logic_for_limit_orders" time="0.027" /><testcase classname="tests.test_secure_mt5_bridge.TestZeroHallucinationValidator" name="test_validate_stop_loss_logic" time="0.002" /><testcase classname="tests.test_secure_mt5_bridge.TestZeroHallucinationValidator" name="test_validate_take_profit_logic" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestZeroHallucinationValidator" name="test_validate_timestamp_recent" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestZeroHallucinationValidator" name="test_validate_order_id_format" time="0.005" /><testcase classname="tests.test_secure_mt5_bridge.TestZeroHallucinationValidator" name="test_validation_statistics" time="0.002" /><testcase classname="tests.test_secure_mt5_bridge.TestSecureMT5Bridge" name="test_bridge_initialization" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestSecureMT5Bridge" name="test_security_config_by_level" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestSecureMT5Bridge" name="test_execute_valid_order" time="0.002"><failure message="secure_mt5_bridge.MarketConditionError: Spread exceeds safety limits">tests\test_secure_mt5_bridge.py:557: in test_execute_valid_order
    receipt = bridge.execute_order(valid_order)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src\trading\secure_mt5_bridge.py:634: in execute_order
    self._validate_order_safety(order)
src\trading\secure_mt5_bridge.py:601: in _validate_order_safety
    raise MarketConditionError("Spread exceeds safety limits")
E   secure_mt5_bridge.MarketConditionError: Spread exceeds safety limits</failure></testcase><testcase classname="tests.test_secure_mt5_bridge.TestSecureMT5Bridge" name="test_execute_invalid_order_volume" time="0.002" /><testcase classname="tests.test_secure_mt5_bridge.TestSecureMT5Bridge" name="test_execute_invalid_order_symbol" time="0.002" /><testcase classname="tests.test_secure_mt5_bridge.TestSecureMT5Bridge" name="test_execute_order_with_spread_violation" time="0.002" /><testcase classname="tests.test_secure_mt5_bridge.TestSecureMT5Bridge" name="test_execute_order_with_old_timestamp" time="0.002"><failure message="AssertionError: assert 'Order too old' in 'Order validation failed: Order timestamp too old: 300.000144 seconds'&#10; +  where 'Order validation failed: Order timestamp too old: 300.000144 seconds' = str(InvalidOrderError('Order validation failed: Order timestamp too old: 300.000144 seconds'))&#10; +    where InvalidOrderError('Order validation failed: Order timestamp too old: 300.000144 seconds') = &lt;ExceptionInfo InvalidOrderError('Order validation failed: Order timestamp too old: 300.000144 seconds') tblen=3&gt;.value">tests\test_secure_mt5_bridge.py:624: in test_execute_order_with_old_timestamp
    assert "Order too old" in str(exc_info.value)
E   AssertionError: assert 'Order too old' in 'Order validation failed: Order timestamp too old: 300.000144 seconds'
E    +  where 'Order validation failed: Order timestamp too old: 300.000144 seconds' = str(InvalidOrderError('Order validation failed: Order timestamp too old: 300.000144 seconds'))
E    +    where InvalidOrderError('Order validation failed: Order timestamp too old: 300.000144 seconds') = &lt;ExceptionInfo InvalidOrderError('Order validation failed: Order timestamp too old: 300.000144 seconds') tblen=3&gt;.value</failure></testcase><testcase classname="tests.test_secure_mt5_bridge.TestSecureMT5Bridge" name="test_data_integrity_verification" time="0.002"><failure message="secure_mt5_bridge.MarketConditionError: Spread exceeds safety limits">tests\test_secure_mt5_bridge.py:632: in test_data_integrity_verification
    bridge.execute_order(valid_order)
src\trading\secure_mt5_bridge.py:634: in execute_order
    self._validate_order_safety(order)
src\trading\secure_mt5_bridge.py:601: in _validate_order_safety
    raise MarketConditionError("Spread exceeds safety limits")
E   secure_mt5_bridge.MarketConditionError: Spread exceeds safety limits</failure></testcase><testcase classname="tests.test_secure_mt5_bridge.TestSecureMT5Bridge" name="test_security_context_manager" time="0.002" /><testcase classname="tests.test_secure_mt5_bridge.TestSecureMT5Bridge" name="test_security_report_generation" time="0.002"><failure message="secure_mt5_bridge.MarketConditionError: Spread exceeds safety limits">tests\test_secure_mt5_bridge.py:657: in test_security_report_generation
    bridge.execute_order(valid_order)
src\trading\secure_mt5_bridge.py:634: in execute_order
    self._validate_order_safety(order)
src\trading\secure_mt5_bridge.py:601: in _validate_order_safety
    raise MarketConditionError("Spread exceeds safety limits")
E   secure_mt5_bridge.MarketConditionError: Spread exceeds safety limits</failure></testcase><testcase classname="tests.test_secure_mt5_bridge.TestSecureMT5Bridge" name="test_thread_safety" time="0.005" /><testcase classname="tests.test_secure_mt5_bridge.TestSecurityLevels" name="test_bridge_creation_with_different_levels[SecurityLevel.DEVELOPMENT]" time="0.002" /><testcase classname="tests.test_secure_mt5_bridge.TestSecurityLevels" name="test_bridge_creation_with_different_levels[SecurityLevel.TESTING]" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestSecurityLevels" name="test_bridge_creation_with_different_levels[SecurityLevel.STAGING]" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestSecurityLevels" name="test_bridge_creation_with_different_levels[SecurityLevel.PRODUCTION]" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestSecurityLevels" name="test_development_vs_production_security" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestFactoryFunction" name="test_create_secure_bridge_default" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestFactoryFunction" name="test_create_secure_bridge_with_level" time="0.001" /><testcase classname="tests.test_secure_mt5_bridge.TestIntegrationScenarios" name="test_complete_order_execution_workflow" time="0.002"><failure message="secure_mt5_bridge.MarketConditionError: Spread exceeds safety limits">tests\test_secure_mt5_bridge.py:769: in test_complete_order_execution_workflow
    receipt = production_bridge.execute_order(order)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src\trading\secure_mt5_bridge.py:634: in execute_order
    self._validate_order_safety(order)
src\trading\secure_mt5_bridge.py:601: in _validate_order_safety
    raise MarketConditionError("Spread exceeds safety limits")
E   secure_mt5_bridge.MarketConditionError: Spread exceeds safety limits</failure></testcase><testcase classname="tests.test_secure_mt5_bridge.TestIntegrationScenarios" name="test_multiple_orders_execution" time="0.002"><failure message="secure_mt5_bridge.MarketConditionError: Spread exceeds safety limits">tests\test_secure_mt5_bridge.py:796: in test_multiple_orders_execution
    receipt = production_bridge.execute_order(order)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src\trading\secure_mt5_bridge.py:634: in execute_order
    self._validate_order_safety(order)
src\trading\secure_mt5_bridge.py:601: in _validate_order_safety
    raise MarketConditionError("Spread exceeds safety limits")
E   secure_mt5_bridge.MarketConditionError: Spread exceeds safety limits</failure></testcase><testcase classname="tests.test_secure_mt5_bridge.TestIntegrationScenarios" name="test_error_handling_and_recovery" time="0.002"><failure message="secure_mt5_bridge.MarketConditionError: Spread exceeds safety limits">tests\test_secure_mt5_bridge.py:817: in test_error_handling_and_recovery
    receipt1 = production_bridge.execute_order(valid_order)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src\trading\secure_mt5_bridge.py:634: in execute_order
    self._validate_order_safety(order)
src\trading\secure_mt5_bridge.py:601: in _validate_order_safety
    raise MarketConditionError("Spread exceeds safety limits")
E   secure_mt5_bridge.MarketConditionError: Spread exceeds safety limits</failure></testcase></testsuite></testsuites>