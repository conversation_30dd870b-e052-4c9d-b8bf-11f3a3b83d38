#!/usr/bin/env python3
"""
MVP Runner Script - Launches both backend and frontend

This script provides a simple way to start both the backend and frontend
components of the MVP in a single command.

Usage:
    python run_mvp_all.py [--no-browser] [--port PORT]
"""

import os
import sys
import time
import argparse
import subprocess
import webbrowser
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("mvp_runner")

def check_dependencies():
    """Check if all required dependencies are installed"""
    logger.info("Checking dependencies...")
    
    # Check Python version
    python_version = sys.version_info
    if python_version.major != 3 or python_version.minor < 8:
        logger.error(f"Python 3.8+ required, found {python_version.major}.{python_version.minor}")
        return False
    
    # Check if Node.js is installed
    try:
        subprocess.run(["node", "--version"], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        logger.error("Node.js not found. Please install Node.js")
        return False
    
    # Check if npm is installed
    try:
        subprocess.run(["npm", "--version"], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        logger.error("npm not found. Please install npm")
        return False
    
    logger.info("✅ All dependencies found")
    return True

def start_backend(port=8000):
    """Start the backend server"""
    logger.info(f"Starting backend server on port {port}...")
    
    backend_script = Path("backend") / "minimal_server.py"
    if not backend_script.exists():
        logger.error(f"Backend script not found: {backend_script}")
        return None
    
    # Start the backend process
    backend_process = subprocess.Popen(
        [sys.executable, str(backend_script)],
        env={**os.environ, "PORT": str(port)}
    )
    
    # Wait for the server to start
    time.sleep(2)
    
    # Check if the process is still running
    if backend_process.poll() is not None:
        logger.error("Backend server failed to start")
        return None
    
    logger.info(f"✅ Backend server running on http://localhost:{port}")
    return backend_process

def start_frontend(backend_port=8000):
    """Start the frontend development server"""
    logger.info("Starting frontend development server...")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists() or not frontend_dir.is_dir():
        logger.error(f"Frontend directory not found: {frontend_dir}")
        return None
    
    # Set environment variables for the frontend
    env = {
        **os.environ,
        "VITE_API_URL": f"http://localhost:{backend_port}"
    }
    
    # Start the frontend process
    frontend_process = subprocess.Popen(
        ["npm", "run", "dev:mvp"],
        cwd=str(frontend_dir),
        env=env
    )
    
    # Wait for the server to start
    time.sleep(5)
    
    # Check if the process is still running
    if frontend_process.poll() is not None:
        logger.error("Frontend server failed to start")
        return None
    
    logger.info("✅ Frontend server running on http://localhost:5173")
    return frontend_process

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="MVP Runner for AI Enhanced Trading Platform")
    parser.add_argument("--no-browser", action="store_true", help="Don't open browser automatically")
    parser.add_argument("--port", type=int, default=8000, help="Port for the backend server")
    
    args = parser.parse_args()
    
    print("🚀 AI Enhanced Trading Platform - MVP Runner")
    print("Starting both backend and frontend servers...")
    print("-" * 60)
    
    # Check dependencies
    if not check_dependencies():
        return 1
    
    # Start the backend
    backend_process = start_backend(port=args.port)
    if backend_process is None:
        return 1
    
    # Start the frontend
    frontend_process = start_frontend(backend_port=args.port)
    if frontend_process is None:
        backend_process.terminate()
        return 1
    
    # Open browser
    if not args.no_browser:
        logger.info("Opening browser...")
        webbrowser.open("http://localhost:5173")
    
    print("\n" + "="*60)
    print("MVP is now running!")
    print("="*60)
    print(f"Backend: http://localhost:{args.port}")
    print("Frontend: http://localhost:5173")
    print("\nPress Ctrl+C to stop both servers")
    print("-"*60)
    
    try:
        # Wait for user to press Ctrl+C
        backend_process.wait()
    except KeyboardInterrupt:
        logger.info("Shutting down servers...")
        backend_process.terminate()
        frontend_process.terminate()
        logger.info("Servers stopped")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())