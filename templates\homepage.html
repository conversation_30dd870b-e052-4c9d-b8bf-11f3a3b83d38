<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI-Enhanced Trading Platform</title>
    <style>
        :root {
            --primary: #2563eb;
            --secondary: #64748b;
            --success: #10b981;
            --premium: #f59e0b;
        }
        body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; margin: 0; padding: 0; color: #1f2937; background: #f8fafc; }
        .container { max-width: 1200px; margin: 0 auto; padding: 2rem; }
        .header { text-align: center; margin-bottom: 3rem; }
        .features-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-bottom: 3rem; }
        .feature-card { background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); text-align: center; }
        .feature-icon { font-size: 3rem; margin-bottom: 1rem; }
        .premium-badge { background: var(--premium); color: white; padding: 0.25rem 0.5rem; border-radius: 20px; font-size: 0.8rem; margin-left: 0.5rem; }
        .prompts-section { background: white; padding: 2rem; border-radius: 12px; margin-bottom: 3rem; }
        .prompts-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; margin-top: 1rem; }
        .prompt-card { border: 1px solid #e2e8f0; padding: 1rem; border-radius: 8px; cursor: pointer; transition: all 0.2s; }
        .prompt-card:hover { border-color: var(--primary); transform: translateY(-2px); }
        .status-bar { background: #f1f5f9; padding: 1rem; border-radius: 8px; margin-bottom: 2rem; display: flex; justify-content: space-between; align-items: center; }
        .status-connected { color: var(--success); }
        .btn { background: var(--primary); color: white; padding: 0.75rem 1.5rem; border: none; border-radius: 6px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn:hover { opacity: 0.9; }
    </style>
</head>
<body>
    <div class="container">
        <!-- Status Bar -->
        <div class="status-bar">
            <div>
                <strong>MT5 Status:</strong>
                <span class="status-connected">
                    {{ "Connected" if mt5_connected else "Disconnected" }}
                </span>
            </div>
            <div>
                <!-- Authentication status would go here -->
            </div>
        </div>
        <!-- Main Header -->
        <div class="header">
            <h1>AI-Enhanced Trading Platform</h1>
            <p>Build, test, and deploy trading strategies without coding experience</p>
        </div>
        <!-- Core Features Grid -->
        <div class="features-grid">
            {% for feature in core_features %}
            <div class="feature-card">
                <div class="feature-icon">{{ feature.icon }}</div>
                <h3>{{ feature.title }}</h3>
                <p>{{ feature.description }}</p>
                {% if feature.premium %}
                <span class="premium-badge">Premium</span>
                {% endif %}
                <a href="#chatbot" class="btn">{{ feature.cta }}</a>
            </div>
            {% endfor %}
        </div>
        <!-- Professional Prompts Section -->
        <div class="prompts-section">
            <h2>Professional Trading Strategies</h2>
            <p>Click any strategy to start building with our AI assistant</p>
            <div class="prompts-grid">
                {% for prompt in professional_prompts %}
                <div class="prompt-card" onclick="applyPrompt({{ prompt.id }})">
                    <h4>{{ prompt.title }}</h4>
                    <p>{{ prompt.description }}</p>
                    <small>Category: {{ prompt.category }} • Difficulty: {{ prompt.difficulty }}</small>
                </div>
                {% endfor %}
            </div>
        </div>
        <!-- Chatbot Integration Section -->
        <div id="chatbot">
            <h2>Build Your Strategy</h2>
            <p>Describe your trading idea in plain English, and we'll help you build the Python code</p>
            <!-- Chatbot interface would be integrated here -->
        </div>
    </div>
    <script>
        async function applyPrompt(promptId) {
            try {
                const response = await fetch(`/api/prompts/${promptId}/apply`, {
                    method: 'POST'
                });
                const result = await response.json();
                if (result.success) {
                    // Load the chatbot with this context
                    console.log('Prompt applied:', result.chatbot_context);
                    // You would integrate with your chatbot here
                    alert(`Ready to build: ${result.prompt.title}`);
                }
            } catch (error) {
                console.error('Error applying prompt:', error);
            }
        }
    </script>
</body>
</html>
