# darwin_engine_step1.py
# Step 1: Core Foundation - Building the Darwin Engine from scratch

import random
import uuid
import json
import logging
from dataclasses import dataclass, field, asdict
from typing import List, Dict, Any, Optional, Union
from enum import Enum
import time
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ============================================================================
# STEP 1: BASIC STRATEGY REPRESENTATION
# ============================================================================

class IndicatorType(Enum):
    """Available technical indicators"""
    RSI = "RSI"
    MACD = "MACD" 
    EMA = "EMA"
    SMA = "SMA"
    BOLLINGER_BANDS = "BOLLINGER_BANDS"
    STOCHASTIC = "STOCHASTIC"
    ADX = "ADX"

class OperatorType(Enum):
    """Available condition operators"""
    GREATER_THAN = ">"
    LESS_THAN = "<"
    GREATER_EQUAL = ">="
    LESS_EQUAL = "<="
    EQUALS = "=="
    CROSSOVER = "crossover"
    CROSSUNDER = "crossunder"

class ActionType(Enum):
    """Available trading actions"""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"
    CLOSE = "close"

@dataclass
class TradingCondition:
    """A single trading condition (e.g., RSI > 70)"""
    indicator: IndicatorType
    operator: OperatorType
    value: Union[float, str]
    period: Optional[int] = 14  # Default period for indicators
    
    def __str__(self):
        return f"{self.indicator.value}({self.period}) {self.operator.value} {self.value}"
    
    def to_dict(self):
        return {
            'indicator': self.indicator.value,
            'operator': self.operator.value,
            'value': self.value,
            'period': self.period
        }

@dataclass
class RiskManagement:
    """Risk management parameters for a strategy"""
    stop_loss_pct: float = 2.0      # Stop loss percentage
    take_profit_pct: float = 4.0    # Take profit percentage  
    position_size_pct: float = 1.0  # Position size as % of account
    max_risk_per_trade: float = 0.02 # Max 2% risk per trade
    
    def to_dict(self):
        return asdict(self)

@dataclass
class TradingStrategy:
    """Core trading strategy representation"""
    id: str
    name: str
    description: str
    conditions: List[TradingCondition]
    action: ActionType
    risk_management: RiskManagement
    
    # Evolution tracking
    generation: int = 0
    fitness_score: float = 0.0
    parent_ids: List[str] = field(default_factory=list)
    
    # Performance tracking
    backtest_results: Dict[str, float] = field(default_factory=dict)
    
    def __str__(self):
        conditions_str = " AND ".join([str(c) for c in self.conditions])
        return f"{self.name}: IF {conditions_str} THEN {self.action.value}"
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'conditions': [c.to_dict() for c in self.conditions],
            'action': self.action.value,
            'risk_management': self.risk_management.to_dict(),
            'generation': self.generation,
            'fitness_score': self.fitness_score,
            'parent_ids': self.parent_ids,
            'backtest_results': self.backtest_results
        }

# ============================================================================
# STEP 2: STRATEGY FACTORY - CREATING INITIAL POPULATION
# ============================================================================

class StrategyFactory:
    """Factory for creating diverse trading strategies"""
    
    def __init__(self):
        self.strategy_templates = self._load_strategy_templates()
    
    def create_random_strategy(self, strategy_id: str = None) -> TradingStrategy:
        """Create a random trading strategy"""
        if not strategy_id:
            strategy_id = str(uuid.uuid4())
        
        # Random strategy template
        template = random.choice(list(self.strategy_templates.keys()))
        
        # Create conditions based on template
        conditions = self._create_conditions_from_template(template)
        
        # Random action
        action = random.choice(list(ActionType))
        
        # Random risk management
        risk_mgmt = RiskManagement(
            stop_loss_pct=random.uniform(1.0, 5.0),
            take_profit_pct=random.uniform(2.0, 8.0),
            position_size_pct=random.uniform(0.5, 2.0),
            max_risk_per_trade=random.uniform(0.01, 0.05)
        )
        
        return TradingStrategy(
            id=strategy_id,
            name=f"{template}_{strategy_id[:8]}",
            description=f"Auto-generated {template} strategy",
            conditions=conditions,
            action=action,
            risk_management=risk_mgmt
        )
    
    def create_initial_population(self, population_size: int) -> List[TradingStrategy]:
        """Create initial population of diverse strategies"""
        population = []
        
        for i in range(population_size):
            strategy = self.create_random_strategy()
            population.append(strategy)
        
        logger.info(f"Created initial population of {len(population)} strategies")
        return population
    
    def _load_strategy_templates(self) -> Dict[str, Dict]:
        """Load predefined strategy templates"""
        return {
            "RSI_Oversold": {
                "indicators": [IndicatorType.RSI],
                "typical_conditions": [
                    {"indicator": IndicatorType.RSI, "operator": OperatorType.LESS_THAN, "value_range": (20, 35)}
                ]
            },
            "RSI_Overbought": {
                "indicators": [IndicatorType.RSI],
                "typical_conditions": [
                    {"indicator": IndicatorType.RSI, "operator": OperatorType.GREATER_THAN, "value_range": (65, 80)}
                ]
            },
            "MACD_Momentum": {
                "indicators": [IndicatorType.MACD],
                "typical_conditions": [
                    {"indicator": IndicatorType.MACD, "operator": OperatorType.CROSSOVER, "value": 0}
                ]
            },
            "EMA_Trend": {
                "indicators": [IndicatorType.EMA],
                "typical_conditions": [
                    {"indicator": IndicatorType.EMA, "operator": OperatorType.GREATER_THAN, "value": "EMA_200", "period": 50}
                ]
            },
            "Multi_Indicator": {
                "indicators": [IndicatorType.RSI, IndicatorType.MACD, IndicatorType.EMA],
                "typical_conditions": [
                    {"indicator": IndicatorType.RSI, "operator": OperatorType.GREATER_THAN, "value_range": (40, 60)},
                    {"indicator": IndicatorType.MACD, "operator": OperatorType.GREATER_THAN, "value": 0}
                ]
            }
        }
    
    def _create_conditions_from_template(self, template_name: str) -> List[TradingCondition]:
        """Create conditions based on a template"""
        template = self.strategy_templates[template_name]
        conditions = []
        
        # Number of conditions (1-3)
        num_conditions = random.randint(1, min(3, len(template["typical_conditions"])))
        
        for i in range(num_conditions):
            if i < len(template["typical_conditions"]):
                cond_template = template["typical_conditions"][i]
            else:
                cond_template = random.choice(template["typical_conditions"])
            
            # Create condition with some randomization
            indicator = cond_template["indicator"]
            operator = cond_template["operator"]
            
            # Handle value assignment
            if "value_range" in cond_template:
                value = random.uniform(*cond_template["value_range"])
            else:
                value = cond_template["value"]
            
            period = cond_template.get("period", random.choice([14, 21, 50, 200]))
            
            condition = TradingCondition(
                indicator=indicator,
                operator=operator,
                value=value,
                period=period
            )
            conditions.append(condition)
        
        return conditions

# ============================================================================
# STEP 3: BASIC MUTATION OPERATIONS
# ============================================================================

class StrategyMutator:
    """Handles mutation operations on trading strategies"""
    
    def __init__(self, mutation_rate: float = 0.1):
        self.mutation_rate = mutation_rate
    
    def mutate(self, strategy: TradingStrategy) -> TradingStrategy:
        """Create a mutated version of a strategy"""
        # Create a copy
        new_strategy = TradingStrategy(
            id=str(uuid.uuid4()),
            name=f"{strategy.name}_mut",
            description=f"Mutation of {strategy.description}",
            conditions=strategy.conditions.copy(),
            action=strategy.action,
            risk_management=RiskManagement(**asdict(strategy.risk_management)),
            generation=strategy.generation + 1,
            parent_ids=[strategy.id]
        )
        
        # Apply mutations
        if random.random() < self.mutation_rate:
            new_strategy = self._mutate_conditions(new_strategy)
        
        if random.random() < self.mutation_rate:
            new_strategy = self._mutate_action(new_strategy)
        
        if random.random() < self.mutation_rate:
            new_strategy = self._mutate_risk_management(new_strategy)
        
        return new_strategy
    
    def _mutate_conditions(self, strategy: TradingStrategy) -> TradingStrategy:
        """Mutate the conditions of a strategy"""
        if not strategy.conditions:
            return strategy
        
        mutation_type = random.choice(['modify_value', 'change_operator', 'add_condition', 'remove_condition'])
        
        if mutation_type == 'modify_value' and strategy.conditions:
            # Modify a random condition's value
            condition = random.choice(strategy.conditions)
            if isinstance(condition.value, (int, float)):
                if condition.indicator == IndicatorType.RSI:
                    # RSI values should stay between 0-100
                    variation = random.uniform(-10, 10)
                    condition.value = max(0, min(100, condition.value + variation))
                else:
                    # Other indicators - vary by ±20%
                    variation = condition.value * random.uniform(-0.2, 0.2)
                    condition.value += variation
        
        elif mutation_type == 'change_operator' and strategy.conditions:
            # Change operator of a random condition
            condition = random.choice(strategy.conditions)
            available_operators = [op for op in OperatorType if op != condition.operator]
            if available_operators:
                condition.operator = random.choice(available_operators)
        
        elif mutation_type == 'add_condition' and len(strategy.conditions) < 4:
            # Add a new condition
            factory = StrategyFactory()
            new_condition = factory._create_conditions_from_template("Multi_Indicator")[0]
            strategy.conditions.append(new_condition)
        
        elif mutation_type == 'remove_condition' and len(strategy.conditions) > 1:
            # Remove a random condition
            strategy.conditions.pop(random.randint(0, len(strategy.conditions) - 1))
        
        return strategy
    
    def _mutate_action(self, strategy: TradingStrategy) -> TradingStrategy:
        """Mutate the action of a strategy"""
        available_actions = [action for action in ActionType if action != strategy.action]
        if available_actions:
            strategy.action = random.choice(available_actions)
        return strategy
    
    def _mutate_risk_management(self, strategy: TradingStrategy) -> TradingStrategy:
        """Mutate risk management parameters"""
        risk = strategy.risk_management
        
        # Randomly adjust one parameter
        mutation_choice = random.choice(['stop_loss', 'take_profit', 'position_size', 'max_risk'])
        
        if mutation_choice == 'stop_loss':
            risk.stop_loss_pct *= random.uniform(0.5, 2.0)
            risk.stop_loss_pct = max(0.5, min(10.0, risk.stop_loss_pct))
        
        elif mutation_choice == 'take_profit':
            risk.take_profit_pct *= random.uniform(0.5, 2.0)
            risk.take_profit_pct = max(1.0, min(20.0, risk.take_profit_pct))
        
        elif mutation_choice == 'position_size':
            risk.position_size_pct *= random.uniform(0.5, 2.0)
            risk.position_size_pct = max(0.1, min(5.0, risk.position_size_pct))
        
        elif mutation_choice == 'max_risk':
            risk.max_risk_per_trade *= random.uniform(0.5, 2.0)
            risk.max_risk_per_trade = max(0.005, min(0.1, risk.max_risk_per_trade))
        
        return strategy

# ============================================================================
# STEP 4: BASIC FITNESS EVALUATION (MOCK)
# ============================================================================

class MockFitnessEvaluator:
    """Mock fitness evaluator for testing purposes"""
    
    def __init__(self):
        self.evaluation_count = 0
    
    def evaluate_strategy(self, strategy: TradingStrategy) -> float:
        """Evaluate strategy fitness (mock implementation)"""
        self.evaluation_count += 1
        
        # Mock fitness calculation based on strategy characteristics
        fitness = 0.0
        
        # Reward strategies with reasonable number of conditions
        if 1 <= len(strategy.conditions) <= 3:
            fitness += 0.2
        
        # Reward RSI-based strategies (common pattern)
        if any(c.indicator == IndicatorType.RSI for c in strategy.conditions):
            fitness += 0.3
        
        # Reward reasonable risk management
        risk = strategy.risk_management
        if 1.0 <= risk.stop_loss_pct <= 5.0:
            fitness += 0.2
        if 2.0 <= risk.take_profit_pct <= 8.0:
            fitness += 0.2
        
        # Add some randomness to simulate market variability
        fitness += random.uniform(0.0, 0.3)
        
        # Store mock backtest results
        strategy.backtest_results = {
            'fitness': fitness,
            'win_rate': random.uniform(0.4, 0.8),
            'total_trades': random.randint(50, 200),
            'sharpe_ratio': random.uniform(0.5, 2.5),
            'max_drawdown': random.uniform(5, 25)
        }
        
        strategy.fitness_score = fitness
        return fitness

# ============================================================================
# STEP 5: BASIC DARWIN ENGINE
# ============================================================================

@dataclass
class EvolutionConfig:
    """Configuration for evolution process"""
    population_size: int = 50
    max_generations: int = 20
    mutation_rate: float = 0.15
    elite_size: int = 10  # Number of top strategies to keep
    tournament_size: int = 3  # For tournament selection

class BasicDarwinEngine:
    """Basic Darwin Engine implementation"""
    
    def __init__(self, config: EvolutionConfig = None):
        self.config = config or EvolutionConfig()
        self.factory = StrategyFactory()
        self.mutator = StrategyMutator(self.config.mutation_rate)
        self.evaluator = MockFitnessEvaluator()
        
        # Evolution state
        self.population: List[TradingStrategy] = []
        self.generation = 0
        self.best_strategy: Optional[TradingStrategy] = None
        self.evolution_history: List[Dict] = []
    
    def run_evolution(self) -> Dict[str, Any]:
        """Run the complete evolution process"""
        logger.info("🧬 Starting Darwin Engine Evolution")
        start_time = time.time()
        
        # Step 1: Initialize population
        self.population = self.factory.create_initial_population(self.config.population_size)
        
        # Step 2: Evolution loop
        for generation in range(self.config.max_generations):
            self.generation = generation + 1
            logger.info(f"Generation {self.generation}/{self.config.max_generations}")
            
            # Evaluate fitness
            self._evaluate_population()
            
            # Sort by fitness
            self.population.sort(key=lambda s: s.fitness_score, reverse=True)
            
            # Track best strategy
            current_best = self.population[0]
            if not self.best_strategy or current_best.fitness_score > self.best_strategy.fitness_score:
                self.best_strategy = current_best
            
            # Record generation stats
            avg_fitness = sum(s.fitness_score for s in self.population) / len(self.population)
            self.evolution_history.append({
                'generation': self.generation,
                'best_fitness': current_best.fitness_score,
                'average_fitness': avg_fitness,
                'best_strategy_id': current_best.id
            })
            
            logger.info(f"  Best fitness: {current_best.fitness_score:.4f}")
            logger.info(f"  Average fitness: {avg_fitness:.4f}")
            logger.info(f"  Best strategy: {current_best}")
            
            # Create next generation (except for last iteration)
            if generation < self.config.max_generations - 1:
                self.population = self._create_next_generation()
        
        total_time = time.time() - start_time
        
        # Return results
        results = {
            'best_strategy': self.best_strategy.to_dict() if self.best_strategy else None,
            'final_population': [s.to_dict() for s in self.population[:10]],  # Top 10
            'evolution_history': self.evolution_history,
            'total_evaluations': self.evaluator.evaluation_count,
            'total_time_seconds': total_time,
            'generations_completed': self.generation
        }
        
        logger.info(f"🎯 Evolution completed in {total_time:.2f} seconds")
        logger.info(f"   Best fitness achieved: {self.best_strategy.fitness_score:.4f}")
        logger.info(f"   Total evaluations: {self.evaluator.evaluation_count}")
        
        return results
    
    def _evaluate_population(self):
        """Evaluate fitness for all strategies in population"""
        for strategy in self.population:
            self.evaluator.evaluate_strategy(strategy)
    
    def _create_next_generation(self) -> List[TradingStrategy]:
        """Create next generation using selection, crossover, and mutation"""
        new_population = []
        
        # Keep elite strategies
        elites = self.population[:self.config.elite_size]
        new_population.extend(elites)
        
        # Fill rest with mutations and crossovers
        while len(new_population) < self.config.population_size:
            # Tournament selection
            parent = self._tournament_selection()
            
            # Mutate parent
            child = self.mutator.mutate(parent)
            new_population.append(child)
        
        return new_population[:self.config.population_size]
    
    def _tournament_selection(self) -> TradingStrategy:
        """Select parent using tournament selection"""
        tournament = random.sample(self.population, self.config.tournament_size)
        return max(tournament, key=lambda s: s.fitness_score)

# ============================================================================
# STEP 6: DEMONSTRATION AND TESTING
# ============================================================================

def demonstrate_darwin_engine():
    """Demonstrate the basic Darwin Engine"""
    print("🧬 Darwin Engine - Step 1 Demonstration")
    print("=" * 50)
    
    # Configure evolution
    config = EvolutionConfig(
        population_size=20,  # Small for demo
        max_generations=10,   # Quick demo
        mutation_rate=0.2,
        elite_size=4
    )
    
    # Create and run engine
    engine = BasicDarwinEngine(config)
    results = engine.run_evolution()
    
    # Display results
    print("\n📊 Evolution Results:")
    print(f"Generations: {results['generations_completed']}")
    print(f"Total evaluations: {results['total_evaluations']}")
    print(f"Time: {results['total_time_seconds']:.2f} seconds")
    
    print("\n🏆 Best Strategy Found:")
    best = results['best_strategy']
    if best:
        print(f"  Name: {best['name']}")
        print(f"  Fitness: {best['fitness_score']:.4f}")
        print(f"  Action: {best['action']}")
        print(f"  Conditions: {len(best['conditions'])}")
        for i, condition in enumerate(best['conditions']):
            print(f"    {i+1}. {condition['indicator']} {condition['operator']} {condition['value']}")
        print(f"  Risk Management:")
        risk = best['risk_management']
        print(f"    Stop Loss: {risk['stop_loss_pct']:.1f}%")
        print(f"    Take Profit: {risk['take_profit_pct']:.1f}%")
    
    print("\n📈 Evolution Progress:")
    for entry in results['evolution_history'][-5:]:  # Last 5 generations
        print(f"  Gen {entry['generation']}: Best={entry['best_fitness']:.4f}, "
              f"Avg={entry['average_fitness']:.4f}")
    
    print("\n🔬 Strategy Analysis:")
    population = results['final_population'][:5]  # Top 5
    for i, strategy in enumerate(population, 1):
        print(f"  {i}. {strategy['name']} (Fitness: {strategy['fitness_score']:.4f})")
    
    return results

if __name__ == "__main__":
    # Run demonstration
    results = demonstrate_darwin_engine()
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"darwin_engine_step1_results_{timestamp}.json"
    
    with open(filename, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 Results saved to: {filename}")
    print("\n✅ Step 1 Complete! Ready for Step 2: Real Backtesting")