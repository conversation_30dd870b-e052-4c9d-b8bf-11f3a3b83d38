{"name": "@ai-trading/backend", "version": "1.0.0", "description": "Backend API for AI Trading Platform", "main": "dist/server.js", "scripts": {"dev": "nodemon --exec ts-node src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --testPathPattern=integration", "test:unit": "jest --testPathPattern=unit", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "db:migrate": "knex migrate:latest", "db:rollback": "knex migrate:rollback", "db:seed": "knex seed:run"}, "dependencies": {"@ai-trading/shared": "file:../shared", "@types/bcrypt": "^5.0.2", "axios": "^1.6.2", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "csv-parser": "^3.0.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "knex": "^3.0.1", "multer": "^1.4.5-lts.1", "openai": "^4.104.0", "pg": "^8.11.3", "redis": "^4.6.10", "uuid": "^9.0.1", "winston": "^3.11.0", "ws": "^8.14.2", "zod": "^3.25.67"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^1.4.11", "@types/node": "^20.9.0", "@types/pg": "^8.10.9", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.7", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "eslint": "^8.53.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "rimraf": "^5.0.5", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}}