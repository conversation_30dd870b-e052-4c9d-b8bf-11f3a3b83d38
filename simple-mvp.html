<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Trading Platform - Simple MVP</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        h1, h2, h3 {
            color: #333;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .status.online {
            background-color: #d4edda;
            color: #155724;
        }
        .status.offline {
            background-color: #f8d7da;
            color: #721c24;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
        .form-section {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AI Trading Platform - Simple MVP</h1>
        
        <div id="serverStatus" class="status">
            Checking server status...
        </div>
        
        <div class="grid">
            <!-- Strategies Section -->
            <div>
                <h2>Trading Strategies</h2>
                <div id="strategiesList"></div>
                
                <div class="form-section">
                    <h3>Create New Strategy</h3>
                    <form id="strategyForm">
                        <div>
                            <label for="strategyName">Name</label>
                            <input type="text" id="strategyName" required>
                        </div>
                        <div>
                            <label for="strategyDescription">Description</label>
                            <textarea id="strategyDescription" rows="2"></textarea>
                        </div>
                        <div>
                            <label for="fastPeriod">Fast Period</label>
                            <input type="number" id="fastPeriod" value="10" required>
                        </div>
                        <div>
                            <label for="slowPeriod">Slow Period</label>
                            <input type="number" id="slowPeriod" value="30" required>
                        </div>
                        <button type="submit">Create Strategy</button>
                    </form>
                </div>
                
                <!-- Trading Section (New) -->
                <div class="form-section" style="margin-top: 20px; background-color: #f0f8ff;">
                    <h3>Execute Trade</h3>
                    <form id="tradeForm">
                        <div>
                            <label for="tradeSymbol">Symbol</label>
                            <input type="text" id="tradeSymbol" value="EURUSD" required>
                        </div>
                        <div>
                            <label for="tradeLot">Lot Size</label>
                            <input type="number" id="tradeLot" value="0.1" min="0.01" step="0.01" required>
                        </div>
                        <div>
                            <label for="tradeType">Order Type</label>
                            <select id="tradeType" required>
                                <option value="BUY">BUY</option>
                                <option value="SELL">SELL</option>
                            </select>
                        </div>
                        <button type="submit" style="background-color: #4169E1;">Execute Trade</button>
                    </form>
                    <div id="tradeResult" style="margin-top: 10px; padding: 10px; display: none;"></div>
                </div>
            </div>
            
            <!-- Backtests Section -->
            <div>
                <h2>Backtests</h2>
                <div id="backtestsList"></div>
                
                <div class="form-section">
                    <h3>Create New Backtest</h3>
                    <form id="backtestForm">
                        <div>
                            <label for="strategyId">Strategy</label>
                            <select id="strategyId" required>
                                <option value="">Select a strategy</option>
                            </select>
                        </div>
                        <div>
                            <label for="symbol">Symbol</label>
                            <input type="text" id="symbol" value="EURUSD" required>
                        </div>
                        <div>
                            <label for="timeframe">Timeframe</label>
                            <select id="timeframe" required>
                                <option value="M1">M1</option>
                                <option value="M5">M5</option>
                                <option value="M15">M15</option>
                                <option value="M30">M30</option>
                                <option value="H1" selected>H1</option>
                                <option value="H4">H4</option>
                                <option value="D1">D1</option>
                            </select>
                        </div>
                        <div>
                            <label for="initialCapital">Initial Capital</label>
                            <input type="number" id="initialCapital" value="10000" required>
                        </div>
                        <div>
                            <label for="startDate">Start Date</label>
                            <input type="date" id="startDate" value="2023-01-01" required>
                        </div>
                        <div>
                            <label for="endDate">End Date</label>
                            <input type="date" id="endDate" value="2023-12-31" required>
                        </div>
                        <button type="submit">Run Backtest</button>
                    </form>
                </div>
                
                <!-- Trade History Section (New) -->
                <div id="tradeHistory" class="form-section" style="margin-top: 20px; background-color: #f0f8ff;">
                    <h3>Recent Trades</h3>
                    <div id="tradesList" style="max-height: 300px; overflow-y: auto;">
                        <p>No trades executed yet</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Configuration
        const API_URL = 'http://localhost:8000';
        
        // DOM Elements
        const serverStatus = document.getElementById('serverStatus');
        const strategiesList = document.getElementById('strategiesList');
        const backtestsList = document.getElementById('backtestsList');
        const strategyForm = document.getElementById('strategyForm');
        const backtestForm = document.getElementById('backtestForm');
        const tradeForm = document.getElementById('tradeForm');
        const tradeResult = document.getElementById('tradeResult');
        const tradesList = document.getElementById('tradesList');
        const strategyIdSelect = document.getElementById('strategyId');
        
        // Helper Functions
        function showMessage(message, isError = false) {
            alert(isError ? `Error: ${message}` : message);
        }
        
        async function fetchData(endpoint) {
            try {
                const response = await fetch(`${API_URL}${endpoint}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return await response.json();
            } catch (error) {
                console.error(`Error fetching ${endpoint}:`, error);
                throw error;
            }
        }
        
        async function postData(endpoint, data) {
            try {
                const response = await fetch(`${API_URL}${endpoint}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data),
                });
                
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP error! Status: ${response.status}, ${errorText}`);
                }
                
                return await response.json();
            } catch (error) {
                console.error(`Error posting to ${endpoint}:`, error);
                throw error;
            }
        }
        
        // Check Server Status
        async function checkServerStatus() {
            try {
                const health = await fetchData('/health');
                serverStatus.textContent = `Server is running. Status: ${health.status}`;
                serverStatus.className = 'status online';
                return true;
            } catch (error) {
                serverStatus.textContent = 'Server is offline or not responding';
                serverStatus.className = 'status offline';
                return false;
            }
        }
        
        // Load Strategies
        async function loadStrategies() {
            try {
                const strategies = await fetchData('/api/strategies');
                
                // Update strategies list
                if (strategies.length === 0) {
                    strategiesList.innerHTML = '<p>No strategies found</p>';
                } else {
                    strategiesList.innerHTML = strategies.map(strategy => `
                        <div class="card">
                            <h3>${strategy.name}</h3>
                            <p>${strategy.description || 'No description'}</p>
                            <p><small>Parameters: ${JSON.stringify(strategy.parameters)}</small></p>
                        </div>
                    `).join('');
                }
                
                // Update strategy select in backtest form
                strategyIdSelect.innerHTML = '<option value="">Select a strategy</option>';
                strategies.forEach(strategy => {
                    const option = document.createElement('option');
                    option.value = strategy.id;
                    option.textContent = strategy.name;
                    strategyIdSelect.appendChild(option);
                });
                
                return strategies;
            } catch (error) {
                strategiesList.innerHTML = '<p>Error loading strategies</p>';
                return [];
            }
        }
        
        // Load Backtests
        async function loadBacktests() {
            try {
                const backtests = await fetchData('/api/backtests');
                
                if (backtests.length === 0) {
                    backtestsList.innerHTML = '<p>No backtests found</p>';
                } else {
                    backtestsList.innerHTML = backtests.map(backtest => `
                        <div class="card">
                            <h3>Backtest #${backtest.id} - ${backtest.symbol} ${backtest.timeframe}</h3>
                            <p>Strategy ID: ${backtest.strategy_id}</p>
                            <p>${new Date(backtest.start_date).toLocaleDateString()} to ${new Date(backtest.end_date).toLocaleDateString()}</p>
                            ${backtest.results ? `
                                <div style="background-color: #f5f5f5; padding: 10px; margin-top: 10px;">
                                    <p>Final Capital: $${backtest.results.final_capital.toFixed(2)}</p>
                                    <p>Profit Factor: ${backtest.results.profit_factor.toFixed(2)}</p>
                                    <p>Win Rate: ${backtest.results.win_rate.toFixed(1)}%</p>
                                </div>
                            ` : ''}
                        </div>
                    `).join('');
                }
                
                return backtests;
            } catch (error) {
                backtestsList.innerHTML = '<p>Error loading backtests</p>';
                return [];
            }
        }
        
        // Load Trade History
        async function loadTradeHistory() {
            try {
                const trades = await fetchData('/api/mvp/trades');
                
                if (trades.length === 0) {
                    tradesList.innerHTML = '<p>No trades executed yet</p>';
                } else {
                    tradesList.innerHTML = trades.map(trade => `
                        <div class="card">
                            <h4>${trade.order_type} ${trade.symbol}</h4>
                            <p>Lot: ${trade.lot}, Ticket: ${trade.ticket}</p>
                            <p>Price: ${trade.open_price}</p>
                            <p><small>${new Date(trade.time).toLocaleString()}</small></p>
                        </div>
                    `).join('');
                }
                
                return trades;
            } catch (error) {
                tradesList.innerHTML = '<p>Error loading trade history</p>';
                return [];
            }
        }
        
        // Create Strategy
        strategyForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const strategyData = {
                name: document.getElementById('strategyName').value,
                description: document.getElementById('strategyDescription').value,
                parameters: {
                    fast_period: parseInt(document.getElementById('fastPeriod').value),
                    slow_period: parseInt(document.getElementById('slowPeriod').value)
                }
            };
            
            try {
                const result = await postData('/api/strategies', strategyData);
                showMessage('Strategy created successfully!');
                strategyForm.reset();
                await loadStrategies();
            } catch (error) {
                showMessage(`Failed to create strategy: ${error.message}`, true);
            }
        });
        
        // Create Backtest
        backtestForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const backtestData = {
                strategy_id: parseInt(document.getElementById('strategyId').value),
                symbol: document.getElementById('symbol').value,
                timeframe: document.getElementById('timeframe').value,
                initial_capital: parseFloat(document.getElementById('initialCapital').value),
                start_date: `${document.getElementById('startDate').value}T00:00:00`,
                end_date: `${document.getElementById('endDate').value}T23:59:59`
            };
            
            try {
                const result = await postData('/api/backtests', backtestData);
                showMessage('Backtest created successfully!');
                await loadBacktests();
            } catch (error) {
                showMessage(`Failed to create backtest: ${error.message}`, true);
            }
        });
        
        // Execute Trade
        tradeForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const tradeData = {
                symbol: document.getElementById('tradeSymbol').value,
                lot: parseFloat(document.getElementById('tradeLot').value),
                order_type: document.getElementById('tradeType').value
            };
            
            try {
                tradeResult.innerHTML = '<p>Executing trade...</p>';
                tradeResult.style.display = 'block';
                tradeResult.style.backgroundColor = '#f0f0f0';
                
                const result = await postData('/api/mvp/trade', tradeData);
                
                // Display the result
                tradeResult.innerHTML = `
                    <h4>Trade Executed Successfully</h4>
                    <p><strong>Ticket:</strong> ${result.ticket}</p>
                    <p><strong>Symbol:</strong> ${result.symbol}</p>
                    <p><strong>Type:</strong> ${result.order_type}</p>
                    <p><strong>Lot:</strong> ${result.lot}</p>
                    <p><strong>Price:</strong> ${result.open_price}</p>
                    <p><strong>Time:</strong> ${new Date(result.time).toLocaleString()}</p>
                `;
                tradeResult.style.backgroundColor = '#d4edda';
                
                // Refresh the trade history from the server
                await loadTradeHistory();
                
            } catch (error) {
                tradeResult.innerHTML = `<p>Error: ${error.message}</p>`;
                tradeResult.style.backgroundColor = '#f8d7da';
            }
        });
        
        // Initialize
        async function initialize() {
            const isServerOnline = await checkServerStatus();
            if (isServerOnline) {
                await Promise.all([
                    loadStrategies(),
                    loadBacktests(),
                    loadTradeHistory()
                ]);
            }
        }
        
        // Start the application
        initialize();
    </script>
</body>
</html>