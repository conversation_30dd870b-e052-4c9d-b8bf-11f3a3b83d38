/**
 * AI Prompts Service
 * Connects to the backend Strategy Helper API to fetch AI prompts
 */

export interface AIPrompt {
  id: string;
  title: string;
  description: string;
  category: string;
  prompt: string; // Added this field that MainHomepage expects
  variables?: string[];
  example_usage?: string;
  expected_output?: string;
  prompt_template?: string;
}

export interface PromptCategory {
  value: string;
  name: string;
  count: number;
}

export class AIPromptsService {
  private baseUrl = 'http://localhost:8000';

  async getPrompts(): Promise<AIPrompt[]> {
    try {
      // Try multiple endpoints (the API routes might have changed during refactoring)
      const endpoints = [
        '/ai-prompts',
        '/api/v1/strategies/prompts',
        '/api/v1/ai-prompts'
      ];
      
      // Default prompts if API fails
      const defaultPrompts = [
        {
          id: 'trend_following',
          title: 'Trend Following Strategy',
          description: 'Simple yet effective trend following strategy using moving averages',
          category: 'technical_analysis',
          prompt: 'Create a trend following strategy using 20 and 50 period moving averages for EUR/USD on the H1 timeframe.'
        },
        {
          id: 'rsi_strategy',
          title: 'RSI Reversal Strategy',
          description: 'Mean reversion strategy using RSI indicator to identify overbought/oversold conditions',
          category: 'technical_analysis',
          prompt: 'Create a mean reversion strategy using the RSI indicator for EUR/USD. Buy when RSI goes below 30 and sell when it goes above 70.'
        },
        {
          id: 'breakout_strategy',
          title: 'Breakout Strategy',
          description: 'Strategy to capture price breakouts from consolidation patterns',
          category: 'trade_execution', 
          prompt: 'Create a breakout strategy that identifies consolidation patterns and enters trades when price breaks out with increased volume.'
        },
        {
          id: 'mql5_indicator',
          title: 'Custom MQL5 Indicator',
          description: 'Create a custom indicator in MQL5 without paying marketplace fees',
          category: 'trade_execution',
          prompt: 'Generate a custom MQL5 indicator that identifies divergences between price and RSI.'
        }
      ];
      
      // Try each endpoint
      for (const endpoint of endpoints) {
        try {
          const response = await fetch(`${this.baseUrl}${endpoint}`);
          if (response.ok) {
            const data = await response.json();
            console.log(`Prompts loaded from ${endpoint}`, data);
            
            // Transform the data to match MainHomepage expectations
            if (Array.isArray(data)) {
              return data.map((item: any) => ({
                id: item.id || item.prompt_id || Math.random().toString(),
                title: item.title || item.name || 'Trading Strategy',
                description: item.description || item.prompt_template?.substring(0, 100) || 'AI Trading Strategy',
                category: item.category || 'Trading',
                prompt: item.prompt_template || item.prompt || item.example_usage || 'Create a trading strategy'
              }));
            } else if (data.prompts && Array.isArray(data.prompts)) {
              return data.prompts.map((item: any) => ({
                id: item.id || item.prompt_id || Math.random().toString(),
                title: item.title || item.name || 'Trading Strategy',
                description: item.description || 'AI Trading Strategy',
                category: item.category || 'Trading',
                prompt: item.prompt_template || item.prompt || item.example_usage || 'Create a trading strategy'
              }));
            }
            
            return data;
          }
        } catch (endpointError) {
          console.warn(`Error fetching from ${endpoint}:`, endpointError);
        }
      }
      
      // Return default prompts if all endpoints fail
      console.log('Using default prompts as fallback');
      return defaultPrompts;
    } catch (error) {
      console.error('Error fetching AI prompts:', error);
      // Return default prompts instead of throwing
      return [
        {
          id: 'trend_following',
          title: 'Trend Following Strategy',
          description: 'Simple yet effective trend following strategy using moving averages',
          category: 'technical_analysis',
          prompt: 'Create a trend following strategy using 20 and 50 period moving averages for EUR/USD on the H1 timeframe.'
        },
        {
          id: 'rsi_strategy',
          title: 'RSI Reversal Strategy',
          description: 'Mean reversion strategy using RSI indicator to identify overbought/oversold conditions',
          category: 'technical_analysis',
          prompt: 'Create a mean reversion strategy using the RSI indicator for EUR/USD. Buy when RSI goes below 30 and sell when it goes above 70.'
        }
      ];
    }
  }
}

export const aiPromptsService = new AIPromptsService();
