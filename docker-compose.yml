version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: trading-postgres
    environment:
      POSTGRES_DB: trading_dev
      POSTGRES_USER: trading_user
      POSTGRES_PASSWORD: trading_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U trading_user -d trading_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: trading-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Python ML Engine
  ml-engine:
    build:
      context: ./python_engine
      dockerfile: Dockerfile
    container_name: trading-ml-engine
    ports:
      - "5001:5001"
    environment:
      - FLASK_ENV=development
      - FLASK_DEBUG=1
    volumes:
      - ./python_engine:/app
    depends_on:
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: trading-backend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************************/trading_dev
      - REDIS_URL=redis://redis:6379
      - ML_ENGINE_URL=http://ml-engine:5001
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      ml-engine:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend (if exists)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: trading-frontend
    ports:
      - "3001:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:3000
      - REACT_APP_ML_ENGINE_URL=http://localhost:5001
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    profiles:
      - frontend

  # Forex Demo API
  forex-demo:
    build: .
    container_name: forex-demo-api
    ports:
      - "3002:3001"
      - "8080:8080"
    environment:
      - NODE_ENV=development
      - PORT=3001
    volumes:
      - ./src:/app/src
      - ./public:/app/public
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Forex Demo UI
  forex-ui:
    image: nginx:alpine
    container_name: forex-demo-ui
    ports:
      - "3003:80"
    volumes:
      - ./public:/usr/share/nginx/html:ro
    depends_on:
      - forex-demo

  # Optional: Add monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: trading-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
    profiles:
      - monitoring

volumes:
  postgres_data:
  redis_data:

networks:
  default:
    name: trading-network