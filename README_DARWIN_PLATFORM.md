# 🧬 Darwin Strategy Verification Platform

**Professional hybrid trading strategy verification system with Python backend + Web frontend**

## 🚀 Quick Start

### **Option 1: One-Click Launch (Windows)**
```bash
# Double-click or run in terminal
darwin_launcher.bat
```

### **Option 2: Manual Setup**
```bash
# 1. Install Python dependencies
pip install -r requirements.txt

# 2. Start the backend
python darwin_backend.py

# 3. Open the frontend
# Double-click darwin_platform.html or open in browser
```

## 🏗️ Architecture Overview

```
🧬 Darwin Platform
├── 🐍 Python Backend (Port 5000)
│   ├── Real market data (Yahoo Finance)
│   ├── Professional backtesting engine
│   ├── Monte Carlo simulations
│   ├── Technical indicators (RSI, MACD, Bollinger)
│   └── Safe strategy execution environment
│
└── 🌐 Web Frontend (HTML/JS)
    ├── Modern responsive UI
    ├── 5-step verification workflow
    ├── Real-time backend communication
    ├── Interactive results dashboard
    └── Professional report exports
```

## ✨ Key Features

### **🔴 Real Market Data Integration**
- ✅ Live data from Yahoo Finance API
- ✅ Multiple timeframes (1m to 1d)
- ✅ Support for Forex, Stocks, Crypto
- ✅ Technical indicators calculated server-side

### **🔬 Professional Verification Suite**
- ✅ Realistic backtesting with spreads & commissions
- ✅ Monte Carlo robustness analysis (up to 2000 simulations)
- ✅ Statistical significance testing
- ✅ Comprehensive performance metrics
- ✅ Walk-forward validation (coming soon)

### **⚡ Advanced Strategy Development**
- ✅ Built-in strategy templates (Mean Reversion, Momentum, Bollinger)
- ✅ Access to real technical indicators in strategy code
- ✅ Safe code execution environment
- ✅ Strategy validation and syntax checking

### **📊 Professional Reporting**
- ✅ PDF report generation
- ✅ Excel/CSV data export
- ✅ JSON results export
- ✅ Shareable verification summaries

## 🎯 5-Step Verification Workflow

### **1. 📊 Market Data**
- Load real market data from Yahoo Finance
- Choose from Forex (EURUSD=X), Stocks (AAPL), Crypto (BTC-USD)
- Select timeframe (1m to 1d) and period (1mo to 5y)

### **2. ⚡ Strategy Development**
- Choose from professional templates or write custom strategies
- Access real technical indicators: RSI, MACD, Bollinger Bands, SMAs
- Validate strategy syntax and logic

### **3. ⚙️ Configuration**
- Set trading parameters (capital, commission, spread)
- Configure Monte Carlo simulations (100-2000 runs)
- Adjust strategy-specific parameters

### **4. 🔬 Verification**
- Professional backtesting with realistic execution
- Monte Carlo robustness analysis
- Real-time progress tracking

### **5. 📈 Results & Reports**
- Comprehensive performance metrics
- Professional PDF reports
- Export capabilities (PDF, Excel, JSON)
- Shareable verification summaries

## 💻 System Requirements

### **Python Backend**
- Python 3.8+
- Required packages (auto-installed):
  - Flask 2.3.3
  - pandas 2.1.0
  - numpy 1.24.3
  - yfinance 0.2.21
  - Flask-CORS 4.0.0

### **Web Frontend**
- Modern web browser (Chrome, Firefox, Safari, Edge)
- JavaScript enabled
- Internet connection for market data

## 🔧 API Documentation

### **Backend Endpoints** (`http://localhost:5000/api/`)

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/health` | GET | Backend health check |
| `/market-data` | POST | Download real market data |
| `/backtest` | POST | Run professional backtest |
| `/monte-carlo` | POST | Monte Carlo analysis |
| `/technical-indicators` | POST | Calculate indicators |

### **Example API Usage**
```python
import requests

# Download market data
response = requests.post('http://localhost:5000/api/market-data', json={
    'symbol': 'AAPL',
    'timeframe': '1h',
    'period': '1y'
})

# Run backtest
response = requests.post('http://localhost:5000/api/backtest', json={
    'symbol': 'AAPL',
    'strategyCode': 'function tradingStrategy(data, params) { ... }',
    'config': {
        'initialCapital': 100000,
        'commission': 7,
        'spread': 2
    }
})
```

## 📈 Strategy Development Guide

### **Available Technical Indicators**
Your strategy code has access to real technical indicators calculated by the Python backend:

```javascript
function tradingStrategy(data, params) {
    const signals = [];
    
    for (let i = 50; i < data.length; i++) {
        const current = data[i];
        
        // Real technical indicators available:
        const rsi = current.rsi;           // Relative Strength Index
        const sma20 = current.sma_20;      // 20-period Simple Moving Average
        const sma50 = current.sma_50;      // 50-period Simple Moving Average
        const bbUpper = current.bb_upper;  // Bollinger Band Upper
        const bbLower = current.bb_lower;  // Bollinger Band Lower
        const bbMiddle = current.bb_middle; // Bollinger Band Middle
        const macd = current.macd;         // MACD Line
        const macdSignal = current.macd_signal; // MACD Signal Line
        
        // Your strategy logic here
        if (rsi < 30 && current.close < bbLower) {
            signals.push({
                timestamp: current.timestamp,
                action: 'buy',
                size: 0.1,  // 10% of portfolio
                price: current.close
            });
        }
    }
    
    return signals;
}
```

### **Strategy Templates**

#### **Mean Reversion Strategy**
- Buys when RSI < 30 and price below lower Bollinger Band
- Sells when RSI > 70 and price above upper Bollinger Band
- Uses real technical indicators for precise signals

#### **Momentum Strategy**
- MACD crossover signals with RSI confirmation
- SMA trend filters for directional bias
- Professional entry/exit timing

#### **Bollinger Band Strategy**
- Price touches band boundaries with RSI confirmation
- Volatility-based position sizing
- Mean reversion to middle band

## 🔒 Security Features

- ✅ **Safe code execution** environment for user strategies
- ✅ **Input validation** on all API endpoints
- ✅ **Sandboxed strategy execution** with limited globals
- ✅ **CORS protection** for cross-origin requests
- ✅ **Error handling** and graceful degradation

## 🚀 Performance Optimization

### **Backend Performance**
- Multi-threaded Monte Carlo simulations
- Efficient pandas operations for data processing
- Caching of market data to avoid re-downloads
- Vectorized technical indicator calculations

### **Frontend Performance**
- Asynchronous API calls with progress tracking
- Efficient DOM updates during verification
- Responsive design for all screen sizes
- Optimized JavaScript execution

## 🛠️ Development & Customization

### **Adding New Technical Indicators**
1. Add calculation method to `DarwinVerificationEngine` class
2. Include in `calculate_technical_indicators()` method
3. Update strategy templates to demonstrate usage

### **Adding New Strategy Templates**
1. Add template to `loadStrategyTemplate()` function in frontend
2. Include comprehensive comments and examples
3. Test with various market conditions

### **Extending API Endpoints**
1. Add new route to `darwin_backend.py`
2. Implement corresponding frontend function
3. Update API documentation

## 📊 Sample Results

### **Professional Backtest Results**
```
📈 AAPL Strategy Verification Results
=====================================
📊 Total Trades: 156
🎯 Win Rate: 64.1%
💰 Total Return: 12.8%
📊 Sharpe Ratio: 1.34
📉 Max Drawdown: 5.2%
💼 Profit Factor: 1.91
```

### **Monte Carlo Analysis**
```
🎲 Monte Carlo Simulation (1000 runs)
====================================
✅ Success Rate: 71.2%
📊 Mean Return: 13.1%
📈 Best Case: 34.7%
📉 Worst Case: -8.3%
🎯 Confidence: 95%
```

## 🆘 Troubleshooting

### **Backend Connection Issues**
```bash
# Check if backend is running
curl http://localhost:5000/api/health

# Verify Python dependencies
pip list | grep Flask

# Check port availability
netstat -an | findstr :5000
```

### **Market Data Download Issues**
- ✅ Verify internet connection
- ✅ Check symbol format (EURUSD=X for forex, AAPL for stocks)
- ✅ Try different time periods
- ✅ Ensure Yahoo Finance API is accessible

### **Strategy Execution Errors**
- ✅ Ensure strategy defines `tradingStrategy` function
- ✅ Check for JavaScript syntax errors
- ✅ Verify all required parameters are provided
- ✅ Test with smaller datasets first

## 🎯 Best Practices

### **Strategy Development**
- Start with simple strategies and gradually add complexity
- Use real technical indicators for accurate signals
- Test with multiple market conditions and timeframes
- Implement proper risk management (position sizing, stop losses)

### **Verification Process**
- Use sufficient historical data (1+ years recommended)
- Run adequate Monte Carlo simulations (1000+ for robustness)
- Test across different market regimes (bull, bear, sideways)
- Validate results with out-of-sample data

### **Performance Optimization**
- Use vectorized operations in strategy code
- Limit Monte Carlo simulations during development
- Cache market data to avoid re-downloading
- Use appropriate timeframes for strategy frequency

## 🔮 Future Enhancements

### **Planned Features**
- 🔄 Walk-forward analysis implementation
- 📊 Advanced portfolio optimization
- 🤖 Machine learning strategy templates
- 📱 Mobile-responsive improvements
- 🔗 Integration with more data providers
- 📈 Real-time strategy monitoring
- 🎯 Advanced risk metrics (VaR, CVaR)

### **Integration Possibilities**
- 🔌 MetaTrader integration
- 📊 TradingView connectivity
- 🏦 Broker API connections
- 📱 Mobile app development
- ☁️ Cloud deployment options

## 📞 Support & Community

### **Getting Help**
- 📖 Check this README for common issues
- 🔍 Review the troubleshooting section
- 💻 Examine the example strategies
- 🧪 Test with demo results first

### **Contributing**
- 🐛 Report bugs and issues
- 💡 Suggest new features
- 🔧 Submit improvements
- 📚 Improve documentation

---

## 🎉 Ready to Verify Your Trading Strategies!

**Start the platform and begin professional strategy verification with real market data!**

```bash
# Launch the platform
darwin_launcher.bat

# Open darwin_platform.html in your browser
# Start verifying your strategies! 🚀
```

---

*Built with ❤️ for professional traders and quantitative analysts*