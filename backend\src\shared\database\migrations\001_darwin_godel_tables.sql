-- Darwin <PERSON>del Machine Database Schema
-- Migration: 001_dar<PERSON>_godel_tables.sql
-- Creates tables for storing proven strategies, forex genomes, and evolution jobs

-- Table for storing mathematically proven trading strategies
CREATE TABLE IF NOT EXISTS proven_strategies (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    conditions JSON NOT NULL,
    action VARCHAR(50) NOT NULL CHECK (action IN ('buy', 'sell', 'close')),
    risk_management JSON NOT NULL,
    
    -- Evolution tracking
    generation INTEGER DEFAULT 0,
    fitness_score DECIMAL(5,4) DEFAULT 0.0000,
    parent_ids JSON,
    mutation_history JSON,
    
    -- Verification status
    proven BOOLEAN DEFAULT FALSE,
    coq_theorem TEXT,
    proof_result JSON,
    verification_time DECIMAL(8,3) DEFAULT 0.000,
    
    -- Performance metrics
    backtest_results JSON,
    live_performance JSON,
    
    -- <PERSON>ada<PERSON>
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by <PERSON><PERSON>HA<PERSON>(255),
    
    -- Indexes
    INDEX idx_fitness_score (fitness_score DESC),
    INDEX idx_proven (proven),
    INDEX idx_generation (generation),
    INDEX idx_created_at (created_at DESC)
);

-- Table for storing forex genome data (discovered patterns per pair/timeframe)
CREATE TABLE IF NOT EXISTS forex_genomes (
    id VARCHAR(255) PRIMARY KEY,
    pair VARCHAR(10) NOT NULL,
    timeframe VARCHAR(10) NOT NULL,
    
    -- Discovered patterns
    session_patterns JSON,
    volatility_profile JSON,
    optimal_strategies JSON,
    
    -- Confidence and metadata
    confidence_score DECIMAL(5,4) DEFAULT 0.0000,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    evolution_cycles INTEGER DEFAULT 0,
    
    -- Performance tracking
    historical_performance JSON,
    validation_results JSON,
    
    -- Unique constraint on pair/timeframe combination
    UNIQUE KEY unique_pair_timeframe (pair, timeframe),
    
    -- Indexes
    INDEX idx_pair (pair),
    INDEX idx_timeframe (timeframe),
    INDEX idx_confidence_score (confidence_score DESC),
    INDEX idx_last_updated (last_updated DESC)
);

-- Table for tracking evolution jobs and their progress
CREATE TABLE IF NOT EXISTS evolution_jobs (
    job_id VARCHAR(255) PRIMARY KEY,
    pair VARCHAR(10) NOT NULL,
    timeframe VARCHAR(10) NOT NULL,
    
    -- Job parameters
    population_size INTEGER NOT NULL,
    max_generations INTEGER NOT NULL,
    fitness_objective VARCHAR(50) NOT NULL,
    
    -- Job status
    status VARCHAR(20) NOT NULL DEFAULT 'initializing' 
        CHECK (status IN ('initializing', 'running', 'paused', 'completed', 'failed', 'terminated')),
    current_generation INTEGER DEFAULT 0,
    
    -- Performance metrics
    best_fitness DECIMAL(5,4) DEFAULT 0.0000,
    average_fitness DECIMAL(5,4) DEFAULT 0.0000,
    convergence_rate DECIMAL(5,4) DEFAULT 0.0000,
    
    -- Results
    best_strategy_id VARCHAR(255),
    verified_strategies_count INTEGER DEFAULT 0,
    total_strategies_generated INTEGER DEFAULT 0,
    
    -- Timing
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    estimated_completion TIMESTAMP NULL,
    
    -- Progress tracking
    evolution_history JSON,
    error_log JSON,
    
    -- Foreign key to best strategy
    FOREIGN KEY (best_strategy_id) REFERENCES proven_strategies(id) ON DELETE SET NULL,
    
    -- Indexes
    INDEX idx_status (status),
    INDEX idx_pair_timeframe (pair, timeframe),
    INDEX idx_created_at (created_at DESC),
    INDEX idx_best_fitness (best_fitness DESC),
    INDEX idx_completed_at (completed_at DESC)
);

-- Table for storing strategy verification results
CREATE TABLE IF NOT EXISTS strategy_verifications (
    id VARCHAR(255) PRIMARY KEY,
    strategy_id VARCHAR(255) NOT NULL,
    pair VARCHAR(10) NOT NULL,
    
    -- Verification results
    verified BOOLEAN NOT NULL DEFAULT FALSE,
    theorem TEXT,
    proof_steps JSON,
    errors JSON,
    
    -- Performance metrics
    verification_time DECIMAL(8,3) NOT NULL,
    confidence DECIMAL(5,4) NOT NULL DEFAULT 0.0000,
    
    -- Metadata
    coq_version VARCHAR(50),
    verification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    verifier_instance VARCHAR(255),
    
    -- Foreign key
    FOREIGN KEY (strategy_id) REFERENCES proven_strategies(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_strategy_id (strategy_id),
    INDEX idx_verified (verified),
    INDEX idx_pair (pair),
    INDEX idx_verification_date (verification_date DESC),
    INDEX idx_confidence (confidence DESC)
);

-- Table for storing real-time market analysis results
CREATE TABLE IF NOT EXISTS market_analysis (
    id VARCHAR(255) PRIMARY KEY,
    pair VARCHAR(10) NOT NULL,
    timeframe VARCHAR(10) NOT NULL,
    
    -- Analysis results
    trend VARCHAR(20) NOT NULL CHECK (trend IN ('bullish', 'bearish', 'sideways')),
    trend_strength DECIMAL(5,4) NOT NULL DEFAULT 0.0000,
    
    -- Technical indicators
    indicators JSON NOT NULL,
    key_levels JSON,
    signals JSON,
    
    -- Recommendation
    recommendation JSON NOT NULL,
    
    -- S3 Core analysis
    query_type VARCHAR(50),
    confidence DECIMAL(5,4) NOT NULL DEFAULT 0.0000,
    reasoning TEXT,
    
    -- Timing
    analysis_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    market_timestamp TIMESTAMP NOT NULL,
    
    -- Indexes
    INDEX idx_pair_timeframe (pair, timeframe),
    INDEX idx_trend (trend),
    INDEX idx_analysis_timestamp (analysis_timestamp DESC),
    INDEX idx_market_timestamp (market_timestamp DESC),
    INDEX idx_confidence (confidence DESC)
);

-- Table for storing chat interactions and queries
CREATE TABLE IF NOT EXISTS chat_interactions (
    id VARCHAR(255) PRIMARY KEY,
    session_id VARCHAR(255),
    user_id VARCHAR(255),
    
    -- Query details
    user_query TEXT NOT NULL,
    query_type VARCHAR(50),
    extracted_pair VARCHAR(10),
    extracted_timeframe VARCHAR(10),
    
    -- S3 Core response
    oracle_response TEXT,
    query_confidence DECIMAL(5,4),
    processing_time DECIMAL(8,3),
    
    -- Analysis results (if applicable)
    analysis_id VARCHAR(255),
    verification_id VARCHAR(255),
    evolution_job_id VARCHAR(255),
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    
    -- Foreign keys
    FOREIGN KEY (analysis_id) REFERENCES market_analysis(id) ON DELETE SET NULL,
    FOREIGN KEY (verification_id) REFERENCES strategy_verifications(id) ON DELETE SET NULL,
    FOREIGN KEY (evolution_job_id) REFERENCES evolution_jobs(job_id) ON DELETE SET NULL,
    
    -- Indexes
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_query_type (query_type),
    INDEX idx_created_at (created_at DESC),
    INDEX idx_pair_timeframe (extracted_pair, extracted_timeframe)
);

-- Table for storing system performance metrics
CREATE TABLE IF NOT EXISTS system_metrics (
    id VARCHAR(255) PRIMARY KEY,
    metric_type VARCHAR(50) NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    
    -- Metric values
    value_numeric DECIMAL(15,6),
    value_text TEXT,
    value_json JSON,
    
    -- Context
    component VARCHAR(50), -- 's3_core', 'verification_engine', 'darwin_engine'
    instance_id VARCHAR(255),
    
    -- Timing
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_metric_type (metric_type),
    INDEX idx_component (component),
    INDEX idx_timestamp (timestamp DESC),
    INDEX idx_metric_name (metric_name)
);

-- Create views for common queries

-- View for active evolution jobs with progress
CREATE VIEW active_evolution_jobs AS
SELECT 
    ej.*,
    ps.name as best_strategy_name,
    ps.fitness_score as best_strategy_fitness,
    TIMESTAMPDIFF(MINUTE, ej.started_at, NOW()) as runtime_minutes,
    CASE 
        WHEN ej.max_generations > 0 THEN (ej.current_generation / ej.max_generations) * 100
        ELSE 0 
    END as progress_percentage
FROM evolution_jobs ej
LEFT JOIN proven_strategies ps ON ej.best_strategy_id = ps.id
WHERE ej.status IN ('running', 'paused');

-- View for top performing strategies by pair
CREATE VIEW top_strategies_by_pair AS
SELECT 
    ps.*,
    fg.confidence_score as genome_confidence,
    COUNT(sv.id) as verification_count,
    AVG(sv.confidence) as avg_verification_confidence
FROM proven_strategies ps
LEFT JOIN forex_genomes fg ON JSON_EXTRACT(ps.backtest_results, '$.pair') = fg.pair
LEFT JOIN strategy_verifications sv ON ps.id = sv.strategy_id
WHERE ps.proven = TRUE
GROUP BY ps.id
ORDER BY ps.fitness_score DESC;

-- View for recent market analysis with recommendations
CREATE VIEW recent_market_analysis AS
SELECT 
    ma.*,
    ci.user_query,
    ci.session_id,
    TIMESTAMPDIFF(MINUTE, ma.analysis_timestamp, NOW()) as age_minutes
FROM market_analysis ma
LEFT JOIN chat_interactions ci ON ma.id = ci.analysis_id
WHERE ma.analysis_timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
ORDER BY ma.analysis_timestamp DESC;

-- Insert initial system configuration
INSERT INTO system_metrics (id, metric_type, metric_name, value_text, component, timestamp) VALUES
('init_s3_core', 'status', 'initialization', 'completed', 's3_core', NOW()),
('init_verification', 'status', 'initialization', 'completed', 'verification_engine', NOW()),
('init_darwin', 'status', 'initialization', 'completed', 'darwin_engine', NOW());

-- Create triggers for updating timestamps
DELIMITER //

CREATE TRIGGER update_proven_strategies_timestamp 
    BEFORE UPDATE ON proven_strategies
    FOR EACH ROW 
BEGIN
    SET NEW.updated_at = CURRENT_TIMESTAMP;
END//

CREATE TRIGGER update_forex_genomes_timestamp 
    BEFORE UPDATE ON forex_genomes
    FOR EACH ROW 
BEGIN
    SET NEW.last_updated = CURRENT_TIMESTAMP;
END//

DELIMITER ;

-- Create stored procedures for common operations

DELIMITER //

-- Procedure to get strategy performance summary
CREATE PROCEDURE GetStrategyPerformanceSummary(IN strategy_id VARCHAR(255))
BEGIN
    SELECT 
        ps.*,
        sv.verified,
        sv.confidence as verification_confidence,
        sv.verification_time,
        COUNT(ci.id) as usage_count,
        AVG(ci.query_confidence) as avg_query_confidence
    FROM proven_strategies ps
    LEFT JOIN strategy_verifications sv ON ps.id = sv.strategy_id
    LEFT JOIN chat_interactions ci ON ps.id = ci.verification_id
    WHERE ps.id = strategy_id
    GROUP BY ps.id;
END//

-- Procedure to get evolution job progress
CREATE PROCEDURE GetEvolutionJobProgress(IN job_id VARCHAR(255))
BEGIN
    SELECT 
        ej.*,
        ps.name as best_strategy_name,
        ps.fitness_score as current_best_fitness,
        CASE 
            WHEN ej.status = 'completed' THEN 100
            WHEN ej.max_generations > 0 THEN (ej.current_generation / ej.max_generations) * 100
            ELSE 0 
        END as progress_percentage,
        CASE 
            WHEN ej.status = 'running' AND ej.started_at IS NOT NULL THEN
                TIMESTAMPDIFF(SECOND, ej.started_at, NOW())
            ELSE 0
        END as runtime_seconds
    FROM evolution_jobs ej
    LEFT JOIN proven_strategies ps ON ej.best_strategy_id = ps.id
    WHERE ej.job_id = job_id;
END//

DELIMITER ;

-- Add comments to tables
ALTER TABLE proven_strategies COMMENT = 'Stores mathematically proven trading strategies with formal verification';
ALTER TABLE forex_genomes COMMENT = 'Stores discovered patterns and optimal strategies for each currency pair and timeframe';
ALTER TABLE evolution_jobs COMMENT = 'Tracks Darwin engine evolution jobs and their progress';
ALTER TABLE strategy_verifications COMMENT = 'Stores Coq formal verification results for trading strategies';
ALTER TABLE market_analysis COMMENT = 'Stores real-time market analysis results from S3 Core engine';
ALTER TABLE chat_interactions COMMENT = 'Stores chat interactions between users and the Trading Oracle';
ALTER TABLE system_metrics COMMENT = 'Stores system performance and health metrics';

-- Grant permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON proven_strategies TO 'trading_app'@'%';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON forex_genomes TO 'trading_app'@'%';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON evolution_jobs TO 'trading_app'@'%';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON strategy_verifications TO 'trading_app'@'%';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON market_analysis TO 'trading_app'@'%';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON chat_interactions TO 'trading_app'@'%';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON system_metrics TO 'trading_app'@'%';

-- Migration completed successfully
SELECT 'Darwin Gödel Machine database schema created successfully!' as status;