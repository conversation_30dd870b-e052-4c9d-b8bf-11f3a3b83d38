"""
Simple Moving Average Crossover Strategy

This strategy trades based on the crossover of fast and slow moving averages.
When the fast MA crosses above the slow MA, it generates a buy signal.
When the fast MA crosses below the slow MA, it generates a sell signal.
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import logging
import time
import sys
import os

# Add the parent directory to the path to import the MT5 client
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from app.mt5_bridge.mt5_client import MT5Client

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MovingAverageCrossover:
    """
    Moving Average Crossover Strategy
    """
    
    def __init__(self, 
                 client: MT5Client,
                 symbol: str = "EURUSD",
                 timeframe: str = "H1",
                 fast_period: int = 10,
                 slow_period: int = 30,
                 lot_size: float = 0.1,
                 stop_loss_pips: int = 50,
                 take_profit_pips: int = 100,
                 magic: int = 12345):
        """
        Initialize the strategy
        """
        self.client = client
        self.symbol = symbol
        self.timeframe = timeframe
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.lot_size = lot_size
        self.stop_loss_pips = stop_loss_pips
        self.take_profit_pips = take_profit_pips
        self.magic = magic
        
        # Get symbol info to calculate pips
        symbol_info = self.client.get_symbol_info(symbol)
        self.point = symbol_info.get("point", 0.00001)
        self.digits = symbol_info.get("digits", 5)
        self.pip_value = 10 ** (-self.digits + 1) if self.digits >= 4 else 0.01
        
        # State variables
        self.last_signal = None
        self.positions = []
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate indicators for the strategy
        """
        # Calculate moving averages
        data['fast_ma'] = data['close'].rolling(window=self.fast_period).mean()
        data['slow_ma'] = data['close'].rolling(window=self.slow_period).mean()
        
        # Calculate crossover signals
        data['prev_fast_ma'] = data['fast_ma'].shift(1)
        data['prev_slow_ma'] = data['slow_ma'].shift(1)
        data['cross_up'] = (data['prev_fast_ma'] < data['prev_slow_ma']) & (data['fast_ma'] > data['slow_ma'])
        data['cross_down'] = (data['prev_fast_ma'] > data['prev_slow_ma']) & (data['fast_ma'] < data['slow_ma'])
        
        return data
    
    def get_historical_data(self) -> pd.DataFrame:
        """
        Get historical data for analysis
        """
        # Get data from MT5
        bars = self.client.get_historical_data(
            symbol=self.symbol,
            timeframe=self.timeframe,
            count=self.slow_period + 50  # Get enough bars for indicators
        )
        
        if not bars:
            logger.error(f"No historical data available for {self.symbol}")
            return pd.DataFrame()
        
        # Convert to DataFrame
        df = pd.DataFrame(bars)
        
        # Calculate indicators
        df = self.calculate_indicators(df)
        
        return df
    
    def check_for_signals(self, data: pd.DataFrame) -> str:
        """
        Check for trading signals
        """
        if data.empty or len(data) < self.slow_period + 2:
            return None
        
        # Get the last row
        last_row = data.iloc[-1]
        
        # Check for signals
        if last_row['cross_up']:
            return "buy"
        elif last_row['cross_down']:
            return "sell"
        
        return None
    
    def calculate_stop_loss(self, order_type: str, price: float) -> float:
        """
        Calculate stop loss level
        """
        if order_type == "buy":
            return price - self.stop_loss_pips * self.pip_value
        else:
            return price + self.stop_loss_pips * self.pip_value
    
    def calculate_take_profit(self, order_type: str, price: float) -> float:
        """
        Calculate take profit level
        """
        if order_type == "buy":
            return price + self.take_profit_pips * self.pip_value
        else:
            return price - self.take_profit_pips * self.pip_value
    
    def execute_trade(self, signal: str) -> bool:
        """
        Execute a trade based on the signal
        """
        if not signal:
            return False
        
        # Check if we already have a position in the same direction
        positions = self.client.get_positions()
        for pos in positions:
            if pos["symbol"] == self.symbol and pos["type"] == signal:
                logger.info(f"Already have a {signal} position open for {self.symbol}")
                return False
        
        # Get current price
        prices = self.client.get_symbol_price(self.symbol)
        if not prices:
            logger.error(f"Failed to get price for {self.symbol}")
            return False
        
        # Set price based on order type
        price = prices["ask"] if signal == "buy" else prices["bid"]
        
        # Calculate stop loss and take profit
        stop_loss = self.calculate_stop_loss(signal, price)
        take_profit = self.calculate_take_profit(signal, price)
        
        # Place order
        result = self.client.place_order(
            symbol=self.symbol,
            order_type=signal,
            volume=self.lot_size,
            price=price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            comment="MA Crossover Strategy",
            magic=self.magic
        )
        
        if result["success"]:
            logger.info(f"Successfully placed {signal} order for {self.symbol} at {price}")
            logger.info(f"Stop Loss: {stop_loss}, Take Profit: {take_profit}")
            return True
        else:
            logger.error(f"Failed to place order: {result['message']}")
            return False
    
    def close_positions(self, signal: str) -> bool:
        """
        Close positions in the opposite direction of the signal
        """
        opposite_type = "sell" if signal == "buy" else "buy"
        
        positions = self.client.get_positions()
        closed = False
        
        for pos in positions:
            if pos["symbol"] == self.symbol and pos["type"] == opposite_type:
                result = self.client.close_position(pos["ticket"])
                
                if result["success"]:
                    logger.info(f"Closed {opposite_type} position {pos['ticket']} with profit: {result['profit']}")
                    closed = True
                else:
                    logger.error(f"Failed to close position {pos['ticket']}: {result['message']}")
        
        return closed
    
    def run(self, once: bool = False) -> None:
        """
        Run the strategy
        """
        logger.info(f"Starting MA Crossover strategy for {self.symbol}")
        logger.info(f"Fast period: {self.fast_period}, Slow period: {self.slow_period}")
        
        while True:
            try:
                # Get historical data
                data = self.get_historical_data()
                
                # Check for signals
                signal = self.check_for_signals(data)
                
                # If we have a signal different from the last one
                if signal and signal != self.last_signal:
                    logger.info(f"New signal: {signal}")
                    
                    # Close opposite positions
                    self.close_positions(signal)
                    
                    # Execute trade
                    if self.execute_trade(signal):
                        self.last_signal = signal
                
                # If running just once, exit after checking
                if once:
                    break
                
                # Sleep for a while
                sleep_time = 60  # 1 minute
                if self.timeframe == "M1":
                    sleep_time = 30
                elif self.timeframe == "M5":
                    sleep_time = 60
                elif self.timeframe == "M15":
                    sleep_time = 180
                elif self.timeframe == "M30":
                    sleep_time = 300
                elif self.timeframe == "H1":
                    sleep_time = 600
                
                logger.info(f"Sleeping for {sleep_time} seconds")
                time.sleep(sleep_time)
                
            except Exception as e:
                logger.error(f"Error in strategy execution: {str(e)}")
                if once:
                    break
                time.sleep(60)  # Sleep for a minute before retrying
        
        logger.info("Strategy execution completed")

# Main function to run the strategy
def run_strategy(login: str, password: str, server: str, symbol: str = "EURUSD", 
                 timeframe: str = "H1", fast_period: int = 10, slow_period: int = 30):
    """
    Run the MA Crossover strategy
    """
    # Create MT5 client
    client = MT5Client()
    
    # Connect to MT5
    if client.connect(login, password, server):
        # Create and run strategy
        strategy = MovingAverageCrossover(
            client=client,
            symbol=symbol,
            timeframe=timeframe,
            fast_period=fast_period,
            slow_period=slow_period
        )
        
        # Run the strategy
        strategy.run()
        
        # Disconnect from MT5
        client.disconnect()
    else:
        logger.error("Failed to connect to MT5")

# Entry point when run directly
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Moving Average Crossover Strategy")
    parser.add_argument("--login", type=str, required=True, help="MT5 login")
    parser.add_argument("--password", type=str, required=True, help="MT5 password")
    parser.add_argument("--server", type=str, required=True, help="MT5 server")
    parser.add_argument("--symbol", type=str, default="EURUSD", help="Symbol to trade")
    parser.add_argument("--timeframe", type=str, default="H1", help="Timeframe to use")
    parser.add_argument("--fast", type=int, default=10, help="Fast MA period")
    parser.add_argument("--slow", type=int, default=30, help="Slow MA period")
    
    args = parser.parse_args()
    
    run_strategy(
        login=args.login,
        password=args.password,
        server=args.server,
        symbol=args.symbol,
        timeframe=args.timeframe,
        fast_period=args.fast,
        slow_period=args.slow
    )
