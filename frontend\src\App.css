/* App.css - Base application styles */

.App {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Reset for consistent styling */
html, body, #root {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
}

.App {
  min-height: 100vh;
  width: 100%;
}

/* Ensure smooth scrolling for navigation */
html {
  scroll-behavior: smooth;
}

/* Button reset */
button {
  border: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
}

/* Link reset */
a {
  text-decoration: none;
  color: inherit;
}

/* Input reset */
input, textarea {
  font-family: inherit;
  border: none;
  outline: none;
}

/* Focus styles for accessibility */
button:focus-visible,
input:focus-visible,
textarea:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}