# tests/conftest.py
"""
Pytest configuration and shared fixtures for MT5 Bridge testing
"""

import pytest
import logging
import tempfile
import os
from unittest.mock import patch, MagicMock
from datetime import datetime


class MT5BridgeMock:
    def __init__(self):
        self.connected = True
        self.orders = []
        self.simulate_error = False
    
    def connect(self):
        self.connected = True
        return True
    
    def is_connected(self):
        return self.connected
    
    def place_order(self, symbol, order_type, lot, price=None, stop_loss=None, take_profit=None):
        if self.simulate_error:
            raise Exception("API error")
        
        if not self.connected:
            self.connect()
        
        if symbol == "INVALID":
            raise ValueError("Invalid symbol")
        
        # Normalize order type to uppercase for consistency
        order_type = order_type.upper() if isinstance(order_type, str) else order_type
        
        order_id = len(self.orders) + 1
        self.orders.append({
            "id": order_id,
            "symbol": symbol,
            "type": order_type,
            "lot": lot,
            "price": price,
            "status": "filled"
        })
        return order_id
    
    def get_order_status(self, order_id):
        for order in self.orders:
            if order["id"] == order_id:
                return order["status"]
        return "not_found"
    
    def close_order(self, order_id):
        for order in self.orders:
            if order["id"] == order_id:
                order["status"] = "closed"
                return True
        return False
    
    def simulate_connection_loss(self):
        self.connected = False
    
    def simulate_api_error(self):
        self.simulate_error = True


@pytest.fixture
def mt5_bridge_mock():
    return MT5BridgeMock()

# Configure logging for tests
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Test markers
def pytest_configure(config):
    """Configure pytest markers"""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "unit: marks tests as unit tests"
    )
    config.addinivalue_line(
        "markers", "security: marks tests as security tests"
    )
    config.addinivalue_line(
        "markers", "performance: marks tests as performance tests"
    )


# Global fixtures
@pytest.fixture(scope="session")
def test_config():
    """Test configuration fixture"""
    return {
        "mt5_timeout": 5000,
        "max_test_orders": 100,
        "test_symbols": ["EURUSD", "GBPUSD", "USDJPY"],
        "test_lot_sizes": [0.01, 0.1, 1.0],
        "offline_mode": True
    }


@pytest.fixture(scope="session")
def mock_mt5_session():
    """Session-scoped MT5 mock for consistent behavior across tests"""
    with patch("python_engine.mt5_bridge.mt5") as mock_mt5:
        # Configure default mock behavior
        mock_mt5.initialize.return_value = True
        mock_mt5.login.return_value = True
        mock_mt5.shutdown.return_value = None
        mock_mt5.last_error.return_value = (0, "No error")
        
        # Mock successful order result
        mock_order_result = MagicMock()
        mock_order_result.retcode = 10009
        mock_order_result.order = 1000
        mock_order_result.deal = 1000
        mock_order_result.volume = 0.1
        mock_order_result.price = 1.1000
        mock_order_result.bid = 1.0998
        mock_order_result.ask = 1.1002
        mock_order_result.comment = "Test order"
        mock_mt5.order_send.return_value = mock_order_result
        
        # Mock positions and orders
        mock_mt5.positions_get.return_value = []
        mock_mt5.orders_get.return_value = []
        
        # Mock symbol info
        mock_symbol_info = MagicMock()
        mock_symbol_info.name = "EURUSD"
        mock_symbol_info.bid = 1.0998
        mock_symbol_info.ask = 1.1002
        mock_symbol_info.spread = 4
        mock_symbol_info.digits = 5
        mock_mt5.symbol_info.return_value = mock_symbol_info
        
        yield mock_mt5


@pytest.fixture
def temp_directory():
    """Temporary directory for test files"""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield temp_dir


@pytest.fixture
def test_logger():
    """Test logger fixture"""
    logger = logging.getLogger("test_mt5_bridge")
    logger.setLevel(logging.DEBUG)
    return logger


@pytest.fixture
def performance_monitor():
    """Performance monitoring fixture"""
    import time
    
    class PerformanceMonitor:
        def __init__(self):
            self.start_time = None
            self.end_time = None
            self.operations = []
        
        def start(self):
            self.start_time = time.time()
        
        def end(self):
            self.end_time = time.time()
        
        def record_operation(self, operation_name, duration):
            self.operations.append({
                "name": operation_name,
                "duration": duration,
                "timestamp": time.time()
            })
        
        def get_total_duration(self):
            if self.start_time and self.end_time:
                return self.end_time - self.start_time
            return None
        
        def get_average_operation_time(self):
            if not self.operations:
                return 0
            return sum(op["duration"] for op in self.operations) / len(self.operations)
    
    return PerformanceMonitor()


# Test data fixtures
@pytest.fixture
def sample_order_data():
    """Sample order data for testing"""
    return {
        "valid_orders": [
            {"symbol": "EURUSD", "lot": 0.1, "order_type": "BUY"},
            {"symbol": "GBPUSD", "lot": 0.2, "order_type": "SELL"},
            {"symbol": "USDJPY", "lot": 0.05, "order_type": "BUY_LIMIT", "price": 150.00},
        ],
        "invalid_orders": [
            {"symbol": "", "lot": 0.1, "order_type": "BUY"},
            {"symbol": "EURUSD", "lot": 0, "order_type": "BUY"},
            {"symbol": "EURUSD", "lot": 0.1, "order_type": "INVALID"},
        ]
    }


@pytest.fixture
def sample_symbol_data():
    """Sample symbol data for testing"""
    return {
        "EURUSD": {
            "name": "EURUSD",
            "bid": 1.0998,
            "ask": 1.1002,
            "spread": 4,
            "digits": 5,
            "point": 0.00001,
            "trade_mode": 4,
            "volume_min": 0.01,
            "volume_max": 100.0,
            "volume_step": 0.01
        },
        "GBPUSD": {
            "name": "GBPUSD",
            "bid": 1.2498,
            "ask": 1.2503,
            "spread": 5,
            "digits": 5,
            "point": 0.00001,
            "trade_mode": 4,
            "volume_min": 0.01,
            "volume_max": 100.0,
            "volume_step": 0.01
        }
    }


# Error simulation fixtures
@pytest.fixture
def mt5_error_simulator():
    """MT5 error simulation fixture"""
    class MT5ErrorSimulator:
        def __init__(self):
            self.error_scenarios = {
                "connection_failed": (1, "Connection failed"),
                "login_failed": (2, "Login failed"),
                "invalid_request": (10013, "Invalid request"),
                "insufficient_funds": (10019, "Insufficient funds"),
                "market_closed": (10018, "Market is closed"),
                "invalid_price": (10016, "Invalid price"),
                "no_connection": (10020, "No connection"),
            }
        
        def get_error(self, scenario):
            return self.error_scenarios.get(scenario, (0, "No error"))
        
        def simulate_connection_error(self, mock_mt5):
            mock_mt5.initialize.return_value = False
            mock_mt5.last_error.return_value = self.get_error("connection_failed")
        
        def simulate_login_error(self, mock_mt5):
            mock_mt5.initialize.return_value = True
            mock_mt5.login.return_value = False
            mock_mt5.last_error.return_value = self.get_error("login_failed")
        
        def simulate_order_error(self, mock_mt5, scenario="invalid_request"):
            mock_mt5.order_send.return_value = None
            mock_mt5.last_error.return_value = self.get_error(scenario)
        
        def simulate_order_rejection(self, mock_mt5, retcode=10013):
            mock_result = MagicMock()
            mock_result.retcode = retcode
            mock_mt5.order_send.return_value = mock_result
    
    return MT5ErrorSimulator()


# Cleanup fixtures
@pytest.fixture(autouse=True)
def cleanup_test_environment():
    """Auto-cleanup fixture that runs after each test"""
    yield
    # Cleanup code here if needed
    pass


# Parametrized fixtures for comprehensive testing
@pytest.fixture(params=["EURUSD", "GBPUSD", "USDJPY", "USDCHF", "AUDUSD"])
def test_symbol(request):
    """Parametrized fixture for testing different symbols"""
    return request.param


@pytest.fixture(params=[0.01, 0.1, 0.5, 1.0])
def test_lot_size(request):
    """Parametrized fixture for testing different lot sizes"""
    return request.param


@pytest.fixture(params=["BUY", "SELL"])
def test_order_type(request):
    """Parametrized fixture for testing different order types"""
    return request.param


# Custom assertions
class MT5Assertions:
    """Custom assertions for MT5 testing"""
    
    @staticmethod
    def assert_valid_order_result(result):
        """Assert that order result is valid"""
        assert isinstance(result, dict)
        assert "ticket" in result
        assert "retcode" in result
        assert result["ticket"] > 0
        assert result["retcode"] == 10009  # TRADE_RETCODE_DONE
    
    @staticmethod
    def assert_valid_position(position):
        """Assert that position data is valid"""
        assert isinstance(position, dict)
        required_fields = ["ticket", "symbol", "volume", "type"]
        for field in required_fields:
            assert field in position
    
    @staticmethod
    def assert_valid_symbol_info(symbol_info):
        """Assert that symbol info is valid"""
        assert isinstance(symbol_info, dict)
        required_fields = ["name", "bid", "ask", "spread", "digits"]
        for field in required_fields:
            assert field in symbol_info
        
        assert symbol_info["bid"] > 0
        assert symbol_info["ask"] > 0
        assert symbol_info["ask"] >= symbol_info["bid"]
    
    @staticmethod
    def assert_connection_state(bridge, expected_state):
        """Assert bridge connection state"""
        from python_engine.mt5_bridge import ConnectionState
        assert bridge.connection_state == expected_state
        
        if expected_state == ConnectionState.CONNECTED:
            assert bridge.is_connected() is True
        else:
            assert bridge.is_connected() is False


@pytest.fixture
def mt5_assertions():
    """Custom assertions fixture"""
    return MT5Assertions()


# Test data generators
@pytest.fixture
def order_data_generator():
    """Order data generator for property-based testing"""
    import random
    
    def generate_valid_order():
        symbols = ["EURUSD", "GBPUSD", "USDJPY", "USDCHF", "AUDUSD"]
        order_types = ["BUY", "SELL"]
        
        return {
            "symbol": random.choice(symbols),
            "lot": round(random.uniform(0.01, 2.0), 2),
            "order_type": random.choice(order_types),
            "comment": f"Generated order {random.randint(1000, 9999)}"
        }
    
    def generate_invalid_order():
        invalid_cases = [
            {"symbol": "", "lot": 0.1, "order_type": "BUY"},
            {"symbol": "INVALID", "lot": 0.1, "order_type": "BUY"},
            {"symbol": "EURUSD", "lot": 0, "order_type": "BUY"},
            {"symbol": "EURUSD", "lot": -0.1, "order_type": "BUY"},
            {"symbol": "EURUSD", "lot": 0.1, "order_type": "INVALID"},
        ]
        return random.choice(invalid_cases)
    
    return {
        "valid": generate_valid_order,
        "invalid": generate_invalid_order
    }


# Database fixtures (if needed for integration tests)
@pytest.fixture(scope="session")
def test_database():
    """Test database fixture for integration tests"""
    # This would set up a test database if needed
    # For now, return None as we're using offline mode
    return None


# Environment fixtures
@pytest.fixture
def test_environment():
    """Test environment configuration"""
    return {
        "MT5_TERMINAL_PATH": os.getenv("MT5_TERMINAL_PATH", ""),
        "MT5_LOGIN": os.getenv("MT5_LOGIN", ""),
        "MT5_PASSWORD": os.getenv("MT5_PASSWORD", ""),
        "MT5_SERVER": os.getenv("MT5_SERVER", ""),
        "TEST_MODE": "offline",
        "LOG_LEVEL": "DEBUG"
    }


# Hooks for test reporting
def pytest_runtest_setup(item):
    """Setup hook for each test"""
    # Log test start
    logging.getLogger("test_runner").info(f"Starting test: {item.name}")


def pytest_runtest_teardown(item, nextitem):
    """Teardown hook for each test"""
    # Log test completion
    logging.getLogger("test_runner").info(f"Completed test: {item.name}")


def pytest_collection_modifyitems(config, items):
    """Modify test collection"""
    # Add markers based on test names
    for item in items:
        if "performance" in item.name.lower():
            item.add_marker(pytest.mark.performance)
        if "security" in item.name.lower():
            item.add_marker(pytest.mark.security)
        if "integration" in item.name.lower():
            item.add_marker(pytest.mark.integration)
        if "slow" in item.name.lower() or "concurrent" in item.name.lower():
            item.add_marker(pytest.mark.slow)
