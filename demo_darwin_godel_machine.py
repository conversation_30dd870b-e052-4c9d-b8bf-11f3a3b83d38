#!/usr/bin/env python3
"""
Darwin-Gödel Machine (DGM) Demonstration

This script demonstrates the key features of the Darwin-Gödel Machine:
- Deterministic reproducibility with seed-based randomness
- Complete audit trails with cryptographic hashing
- Tamper detection capabilities
- Evolution process with full auditability
"""

import json
import tempfile
import os
from datetime import datetime

# Import the DGM implementation
from src.evolution.darwin_godel_machine import (
    DarwinGodelMachine, 
    StrategyGenome, 
    AuditLogEntry, 
    OperationType
)


def demonstrate_reproducibility():
    """Demonstrate that DGM produces identical results with same seed"""
    print("🔄 REPRODUCIBILITY DEMONSTRATION")
    print("=" * 50)
    
    # Create two DGM instances with same seed
    print("Creating two DGM instances with seed=12345...")
    dgm1 = DarwinGodelMachine(seed=12345)
    dgm2 = DarwinGodelMachine(seed=12345)
    
    # Run evolution with same parameters
    print("Running evolution (5 generations, 10 population size)...")
    result1, _ = dgm1.run_evolution(generations=5, population_size=10)
    result2, _ = dgm2.run_evolution(generations=5, population_size=10)
    
    # Compare results
    print(f"\nResults comparison:")
    print(f"DGM1 Best Fitness: {result1.fitness:.6f}")
    print(f"DGM2 Best Fitness: {result2.fitness:.6f}")
    print(f"Fitness Match: {result1.fitness == result2.fitness}")
    
    print(f"\nParameter comparison:")
    params_match = result1.parameters == result2.parameters
    print(f"Parameters Match: {params_match}")
    
    if params_match:
        print("✅ REPRODUCIBILITY VERIFIED: Identical results with same seed!")
    else:
        print("❌ REPRODUCIBILITY FAILED: Results differ!")
    
    # Show some parameters
    print(f"\nSample parameters from both runs:")
    for param, value in list(result1.parameters.items())[:3]:
        print(f"  {param}: {value:.4f} (both instances)")
    
    print()


def demonstrate_audit_trail():
    """Demonstrate comprehensive audit trail functionality"""
    print("📋 AUDIT TRAIL DEMONSTRATION")
    print("=" * 50)
    
    # Create DGM with audit enabled
    print("Creating DGM with audit trail enabled...")
    dgm = DarwinGodelMachine(seed=42, enable_audit=True)
    
    # Run evolution and get audit log
    print("Running evolution with audit logging...")
    best_genome, audit_log = dgm.run_evolution(generations=3, population_size=8, return_audit=True)
    
    print(f"\nEvolution completed!")
    print(f"Best fitness achieved: {best_genome.fitness:.6f}")
    print(f"Total audit log entries: {len(audit_log)}")
    
    # Show audit log summary
    audit_summary = dgm.get_audit_log_summary()
    print(f"\nAudit log summary:")
    print(f"  Total entries: {audit_summary['total_entries']}")
    print(f"  Operations breakdown:")
    for op_type, count in audit_summary['operations'].items():
        print(f"    {op_type}: {count} entries")
    
    # Verify audit integrity
    is_valid, errors = dgm.verify_audit_integrity()
    print(f"\nAudit integrity check:")
    print(f"  Valid: {is_valid}")
    print(f"  Errors: {len(errors)}")
    
    if is_valid:
        print("✅ AUDIT INTEGRITY VERIFIED: All entries are valid!")
    else:
        print("❌ AUDIT INTEGRITY FAILED!")
        for error in errors[:3]:  # Show first 3 errors
            print(f"    - {error}")
    
    # Show sample audit entries
    print(f"\nSample audit entries:")
    for i, entry in enumerate(audit_log[:3]):
        print(f"  Entry {i+1}:")
        print(f"    Operation: {entry.operation_type.value}")
        print(f"    Timestamp: {entry.timestamp}")
        print(f"    Hash: {entry.hash[:16]}...")
        print(f"    Generation: {entry.generation}")
    
    print()
    return dgm, audit_log


def demonstrate_tamper_detection(dgm, audit_log):
    """Demonstrate tamper detection capabilities"""
    print("🛡️ TAMPER DETECTION DEMONSTRATION")
    print("=" * 50)
    
    # First verify original integrity
    print("Verifying original audit log integrity...")
    is_valid, errors = dgm.verify_audit_integrity()
    print(f"Original integrity: {'✅ Valid' if is_valid else '❌ Invalid'}")
    
    # Tamper with an audit entry
    print("\nSimulating tampering with audit log...")
    if len(dgm.audit_log) > 0:
        original_hash = dgm.audit_log[0].hash
        dgm.audit_log[0].hash = "TAMPERED_HASH_12345"
        print(f"Changed hash from {original_hash[:16]}... to TAMPERED_HASH_12345")
        
        # Verify integrity after tampering
        print("Verifying integrity after tampering...")
        is_valid, errors = dgm.verify_audit_integrity()
        print(f"Post-tamper integrity: {'✅ Valid' if is_valid else '❌ Invalid'}")
        
        if not is_valid:
            print("✅ TAMPER DETECTION SUCCESSFUL: Tampering detected!")
            print(f"Detected {len(errors)} integrity violations:")
            for error in errors[:3]:
                print(f"  - {error}")
        else:
            print("❌ TAMPER DETECTION FAILED: Tampering not detected!")
        
        # Restore original hash
        dgm.audit_log[0].hash = original_hash
        print(f"\nRestored original hash: {original_hash[:16]}...")
    
    print()


def demonstrate_export_import():
    """Demonstrate audit log export and import functionality"""
    print("💾 EXPORT/IMPORT DEMONSTRATION")
    print("=" * 50)
    
    # Create DGM and run evolution
    print("Creating DGM and running evolution...")
    dgm = DarwinGodelMachine(seed=999, enable_audit=True)
    dgm.run_evolution(generations=2, population_size=5)
    
    # Export audit log
    print("Exporting audit log to file...")
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        temp_file = f.name
    
    try:
        export_success = dgm.export_complete_audit_log(temp_file)
        print(f"Export successful: {export_success}")
        
        if export_success:
            # Check file size
            file_size = os.path.getsize(temp_file)
            print(f"Exported file size: {file_size} bytes")
            
            # Import and verify
            print("Importing and verifying audit log...")
            is_valid, errors = dgm.import_and_verify_audit_log(temp_file)
            print(f"Import verification: {'✅ Valid' if is_valid else '❌ Invalid'}")
            
            if is_valid:
                print("✅ EXPORT/IMPORT SUCCESSFUL: Audit log integrity maintained!")
            else:
                print("❌ EXPORT/IMPORT FAILED!")
                for error in errors[:3]:
                    print(f"  - {error}")
            
            # Show sample of exported data
            print("\nSample of exported data structure:")
            with open(temp_file, 'r') as f:
                data = json.load(f)
            
            print(f"  Metadata keys: {list(data['metadata'].keys())}")
            print(f"  Audit log entries: {len(data['audit_log'])}")
            print(f"  Evolution summary keys: {list(data['evolution_summary'].keys())}")
    
    finally:
        # Cleanup
        if os.path.exists(temp_file):
            os.unlink(temp_file)
    
    print()


def demonstrate_genome_integrity():
    """Demonstrate genome integrity verification"""
    print("🧬 GENOME INTEGRITY DEMONSTRATION")
    print("=" * 50)
    
    # Create DGM and population
    print("Creating DGM and initializing population...")
    dgm = DarwinGodelMachine(seed=777)
    population = dgm.initialize_population(size=5)
    
    # Verify original integrity
    print("Verifying original genome integrity...")
    all_valid = True
    for i, genome in enumerate(population):
        is_valid = genome.verify_integrity()
        print(f"  Genome {i+1}: {'✅ Valid' if is_valid else '❌ Invalid'}")
        if not is_valid:
            all_valid = False
    
    if all_valid:
        print("✅ All genomes have valid integrity!")
    
    # Tamper with a genome
    print("\nSimulating genome tampering...")
    if len(population) > 0:
        genome = population[0]
        original_hash = genome.integrity_hash
        original_param = genome.parameters['rsi_period']
        
        # Modify parameter without updating hash
        genome.parameters['rsi_period'] = 999
        print(f"Changed rsi_period from {original_param} to 999")
        
        # Verify integrity
        is_valid = genome.verify_integrity()
        print(f"Integrity after tampering: {'✅ Valid' if is_valid else '❌ Invalid'}")
        
        if not is_valid:
            print("✅ GENOME TAMPERING DETECTED!")
        
        # Update integrity hash
        print("Updating integrity hash...")
        genome.update_integrity_hash()
        new_hash = genome.integrity_hash
        
        print(f"Hash changed: {original_hash[:16]}... -> {new_hash[:16]}...")
        
        # Verify again
        is_valid = genome.verify_integrity()
        print(f"Integrity after hash update: {'✅ Valid' if is_valid else '❌ Invalid'}")
    
    print()


def demonstrate_evolution_statistics():
    """Demonstrate evolution statistics and analysis"""
    print("📊 EVOLUTION STATISTICS DEMONSTRATION")
    print("=" * 50)
    
    # Create DGM and run longer evolution
    print("Running extended evolution (10 generations, 20 population)...")
    dgm = DarwinGodelMachine(seed=555, enable_audit=True)
    best_genome, _ = dgm.run_evolution(generations=10, population_size=20)
    
    # Get evolution audit trail
    evolution_trail = dgm.get_evolution_audit_trail()
    
    print(f"\nEvolution completed!")
    print(f"Seed used: {evolution_trail['seed']}")
    print(f"Total generations: {evolution_trail['generations_count']}")
    print(f"Best final fitness: {best_genome.fitness:.6f}")
    
    # Show generation statistics
    print(f"\nGeneration-by-generation statistics:")
    print(f"{'Gen':<4} {'Pop Size':<8} {'Best Fit':<10} {'Avg Fit':<10}")
    print("-" * 35)
    
    for gen_stat in evolution_trail['generation_stats']:
        print(f"{gen_stat['generation']:<4} "
              f"{gen_stat['population_size']:<8} "
              f"{gen_stat['best_fitness']:<10.6f} "
              f"{gen_stat['avg_fitness']:<10.6f}")
    
    # Show best genome details
    print(f"\nBest genome details:")
    print(f"  ID: {best_genome.id}")
    print(f"  Generation: {best_genome.generation}")
    print(f"  Fitness: {best_genome.fitness:.6f}")
    print(f"  Parameters:")
    for param, value in best_genome.parameters.items():
        if isinstance(value, float):
            print(f"    {param}: {value:.4f}")
        else:
            print(f"    {param}: {value}")
    
    # Show audit summary
    audit_summary = dgm.get_audit_log_summary()
    print(f"\nAudit summary:")
    print(f"  Total operations logged: {audit_summary['total_entries']}")
    print(f"  Integrity status: {'✅ Valid' if audit_summary['integrity_verified'] else '❌ Invalid'}")
    
    print()


def main():
    """Run all demonstrations"""
    print("🧬 DARWIN-GÖDEL MACHINE (DGM) COMPREHENSIVE DEMONSTRATION")
    print("=" * 60)
    print(f"Demonstration started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # Run all demonstrations
        demonstrate_reproducibility()
        dgm, audit_log = demonstrate_audit_trail()
        demonstrate_tamper_detection(dgm, audit_log)
        demonstrate_export_import()
        demonstrate_genome_integrity()
        demonstrate_evolution_statistics()
        
        print("🎉 ALL DEMONSTRATIONS COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print("\nKey features demonstrated:")
        print("✅ Deterministic reproducibility with seed-based randomness")
        print("✅ Complete audit trails with cryptographic hashing")
        print("✅ Tamper detection for both audit logs and genomes")
        print("✅ Export/import functionality with integrity verification")
        print("✅ Comprehensive evolution statistics and analysis")
        print("✅ Full genome integrity verification")
        
        print(f"\nDemonstration completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        print(f"❌ DEMONSTRATION FAILED: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()