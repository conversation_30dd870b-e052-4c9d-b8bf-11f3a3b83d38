#!/usr/bin/env python3
"""
MT5 Integration Test for AI Trading Platform
Tests the complete workflow: Strategy Generation → MT5 Deployment → Monitoring
"""

import sys
import os
from pathlib import Path
import json
import time
import asyncio
import httpx  # Use httpx for async requests

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import config
try:
    from config import OLLAMA_URL, API_URL, API_USERNAME, API_PASSWORD
except ImportError:
    print("❌ Could not import from config.py. Please ensure the file exists.")
    print("Falling back to default values, but this is not recommended.")
    OLLAMA_URL = "http://localhost:11435"
    API_URL = "http://localhost:9999"
    API_USERNAME = "admin"
    API_PASSWORD = "trading123"

def test_mt5_imports():
    """Test if MT5 components can be imported"""
    print("🔍 Testing MT5 component imports...")
    
    imports_successful = True
    
    # Test MT5 strategy executor
    try:
        sys.path.append(str(project_root))
        from backend.app.mt5_bridge.mt5_strategy_executor import MT5StrategyExecutor
        print("✅ MT5StrategyExecutor imported successfully")
    except ImportError as e:
        print(f"❌ MT5StrategyExecutor import failed: {e}")
        imports_successful = False
    
    # Test MT5 API components
    try:
        from backend.app.api.routes.mt5 import router as mt5_router
        print("✅ MT5 API router imported successfully")
    except ImportError as e:
        print(f"⚠️ MT5 API router import failed: {e}")
        # Not critical, don't fail the test for this
    
    # Test chatbot (check if it exists)
    try:
        from backend.app.chatbot.enhanced_strategy_chatbot import EnhancedStrategyChatbot
        print("✅ EnhancedStrategyChatbot imported successfully from app.chatbot")
    except ImportError:
        try:
            # Try different location
            from src.chatbot.enhanced_strategy_chatbot import EnhancedStrategyChatbot
            print("✅ EnhancedStrategyChatbot imported successfully from src.chatbot")
        except ImportError:
            print("⚠️ EnhancedStrategyChatbot not found - will use alternative")
            # Not critical, don't fail the test for this
    
    return imports_successful

async def test_docker_ollama_async():
    """Test Docker Ollama connection asynchronously"""
    print("\n🐳 Testing Docker Ollama connection...")
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(f"{OLLAMA_URL}/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json()
                print(f"✅ Docker Ollama connected on {OLLAMA_URL}")
                print(f"📋 Available models: {[m['name'] for m in models.get('models', [])]}")
                return True
            else:
                print(f"❌ Docker Ollama responded with status {response.status_code}")
                return False
                
        except httpx.ConnectError:
            print(f"❌ Could not connect to Docker Ollama on {OLLAMA_URL}")
            return False
        except Exception as e:
            print(f"❌ Docker Ollama test error: {e}")
            return False

async def test_mt5_strategy_executor_async():
    """Test MT5 Strategy Executor functionality asynchronously"""
    print("\n⚙️ Testing MT5 Strategy Executor...")
    
    try:
        from backend.app.mt5_bridge.mt5_strategy_executor import MT5StrategyExecutor
        
        executor = MT5StrategyExecutor()
        print("✅ MT5StrategyExecutor initialized")
        
        # Test strategy validation with both valid and invalid code
        valid_code = '''
class TestStrategy:
    def __init__(self):
        self.name = "Valid Test Strategy"
    def generate_signals(self, data):
        return {"action": "buy"}
'''
        invalid_code = "this is not valid python code"
        
        if hasattr(executor, 'validate_strategy_code'):
            print("✅ Strategy validation method available")
            # Test with valid code
            validation_result_valid = await executor.validate_strategy_code(valid_code)
            if validation_result_valid:
                print("✅ Valid strategy code passed validation")
            else:
                print("❌ Valid strategy code failed validation")
            
            # Test with invalid code
            validation_result_invalid = await executor.validate_strategy_code(invalid_code)
            if not validation_result_invalid:
                print("✅ Invalid strategy code correctly failed validation")
            else:
                print("❌ Invalid strategy code incorrectly passed validation")
        else:
            print("⚠️ Strategy validation method not found")
        
        if hasattr(executor, 'deploy_strategy'):
            print("✅ Strategy deployment method available")
        else:
            print("⚠️ Strategy deployment method not found")
            
        return True
        
    except Exception as e:
        print(f"❌ MT5 Strategy Executor test error: {e}")
        return False

def test_strategy_generation():
    """Test AI strategy generation"""
    print("\n🤖 Testing AI Strategy Generation...")
    
    try:
        # Try to import enhanced chatbot, fallback to mock implementation
        try:
            from backend.app.chatbot.enhanced_strategy_chatbot import EnhancedStrategyChatbot
            # Backend version takes ollama_base_url parameter
            chatbot = EnhancedStrategyChatbot(ollama_base_url=OLLAMA_URL)
        except ImportError:
            # Create a mock chatbot for testing if the primary one fails
            print("⚠️ Could not import EnhancedStrategyChatbot from backend. Using Mock.")
            class MockStrategyChatbot:
                def __init__(self, ollama_base_url=None):
                    self.ollama_base_url = ollama_base_url
                
                async def generate_strategy_async(self, prompt):
                    print("🤖 Mock chatbot generating strategy...")
                    # Simulate returning a basic strategy structure
                    return {
                        "name": "Mock RSI Strategy",
                        "code": "class MockStrategy:\\n    pass"
                    }
            
            chatbot = MockStrategyChatbot(ollama_base_url=OLLAMA_URL)
        print("✅ Strategy Chatbot initialized")
        
        test_prompt = "Generate a simple RSI-based trading strategy for EURUSD"
        print(f"📤 Testing with prompt: {test_prompt}")
        
        # Test if the generation method exists (sync or async)
        if hasattr(chatbot, 'generate_strategy_async'):
            print("✅ Async strategy generation method available")
        elif hasattr(chatbot, 'generate_strategy'):
            print("✅ Sync strategy generation method available")
        else:
            print("⚠️ No strategy generation method found")

        print("💡 Use the web interface for full testing")
        
        return True
        
    except Exception as e:
        print(f"❌ Strategy generation test error: {e}")
        return False

async def test_api_endpoints_async():
    """Test API endpoint availability asynchronously"""
    print("\n🌐 Testing API Endpoints...")
    
    async with httpx.AsyncClient() as client:
        try:
            # Test simple server health (no auth needed)
            response = await client.get(f'{API_URL}/health', timeout=3)
            response.raise_for_status()
            data = response.json()
            print(f"✅ Simple server health: {data['status']}")
            
            # Test API status with Basic Authentication
            auth = (API_USERNAME, API_PASSWORD)
            response = await client.get(f'{API_URL}/api/status', auth=auth, timeout=3)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ API status with auth: {data['status']}")
            elif response.status_code == 401:
                print("❌ API status check failed: Unauthorized (401). Check credentials in config.py.")
            else:
                response.raise_for_status()

            return True
            
        except httpx.HTTPStatusError as e:
            print(f"❌ API endpoint test failed with status {e.response.status_code}")
            return False
        except httpx.ConnectError as e:
            print(f"❌ API endpoint test failed: Could not connect to {e.request.url}")
            return False
        except Exception as e:
            print(f"❌ API endpoint test error: {e}")
            return False

def create_mt5_test_strategy():
    """Create a test strategy for MT5 deployment"""
    print("\n📋 Creating MT5 Test Strategy...")
    
    test_strategy = {
        "name": "RSI Scalping Strategy",
        "description": "Simple RSI-based scalping strategy for testing",
        "symbol": "EURUSD",
        "timeframe": "M15",
        "indicators": {
            "RSI": {"period": 14, "overbought": 70, "oversold": 30}
        },
        "entry_conditions": [
            "RSI crosses below 30 (oversold)",
            "Price above 20-period MA"
        ],
        "exit_conditions": [
            "RSI crosses above 70 (overbought)",
            "Stop loss at 50 pips",
            "Take profit at 100 pips"
        ],
        "risk_management": {
            "position_size": 0.1,
            "stop_loss_pips": 50,
            "take_profit_pips": 100,
            "max_spread": 3
        },
        "time_filters": {
            "start_hour": 8,
            "end_hour": 18,
            "trading_days": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]
        }
    }
    
    # Save test strategy
    strategy_file = project_root / "test_strategy.json"
    with open(strategy_file, 'w') as f:
        json.dump(test_strategy, f, indent=2)
    
    print(f"✅ Test strategy saved to {strategy_file}")
    print(f"📊 Strategy: {test_strategy['name']}")
    print(f"💱 Symbol: {test_strategy['symbol']}")
    print(f"⏱️ Timeframe: {test_strategy['timeframe']}")
    
    return test_strategy

async def run_comprehensive_test_async():
    """Run all MT5 integration tests asynchronously"""
    print("🚀 Starting Comprehensive MT5 Integration Test")
    print("=" * 60)
    
    results = {}
    
    # Test 1: Component imports (sync)
    results['imports'] = test_mt5_imports()
    
    # Test 2: Docker Ollama connection (async)
    results['docker_ollama'] = await test_docker_ollama_async()
    
    # Test 3: MT5 Strategy Executor (async)
    results['mt5_executor'] = await test_mt5_strategy_executor_async()
    
    # Test 4: Strategy generation (sync, but checks for async method)
    results['strategy_generation'] = test_strategy_generation()
    
    # Test 5: API endpoints (async)
    results['api_endpoints'] = await test_api_endpoints_async()
    
    # Test 6: Create test strategy (sync)
    test_strategy = create_mt5_test_strategy()
    results['test_strategy'] = test_strategy is not None
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name.replace('_', ' ').title():<25} {status}")
    
    print(f"\n🎯 Overall Score: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! MT5 integration is ready for deployment.")
        print("\n🚀 Next Steps:")
        print(f"1. Open {API_URL} in your browser")
        print("2. Use the strategy chatbot to generate new strategies")
        print("3. Deploy strategies to MT5 for live testing")
        print("4. Monitor performance and adjust as needed")
    else:
        print("⚠️ Some tests failed. Please check the errors above.")
        print("\n🔧 Troubleshooting:")
        if not results.get('docker_ollama'):
            print(f"- Start Docker Ollama or check URL in config.py (current: {OLLAMA_URL})")
        if not results.get('imports'):
            print("- Check Python dependencies and imports. You may need to run: pip install httpx")
        if not results.get('api_endpoints'):
            print(f"- Restart the simple server and check API URL/credentials in config.py (current URL: {API_URL})")
    
    return results

if __name__ == "__main__":
    try:
        # Run the async test suite
        results = asyncio.run(run_comprehensive_test_async())
        
        # Generate test report
        report_file = project_root / "mt5_integration_test_report.json"
        with open(report_file, 'w') as f:
            json.dump({
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "results": results,
                "server_url": API_URL,
                "ollama_url": OLLAMA_URL
            }, f, indent=2)
        
        print(f"\n📄 Test report saved to {report_file}")
        
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test suite error: {e}")
        import traceback
        traceback.print_exc()
