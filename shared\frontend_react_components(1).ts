# Frontend React Components

## Project Structure
```
frontend/
├── public/
│   ├── index.html
│   └── favicon.ico
├── src/
│   ├── components/
│   │   ├── common/
│   │   │   ├── Button.tsx
│   │   │   ├── Input.tsx
│   │   │   ├── Modal.tsx
│   │   │   ├── LoadingSpinner.tsx
│   │   │   └── ErrorBoundary.tsx
│   │   ├── layout/
│   │   │   ├── Header.tsx
│   │   │   ├── Sidebar.tsx
│   │   │   └── Layout.tsx
│   │   ├── upload/
│   │   │   ├── FileUpload.tsx
│   │   │   ├── ColumnMapping.tsx
│   │   │   └── UploadProgress.tsx
│   │   ├── chat/
│   │   │   ├── ChatWidget.tsx
│   │   │   ├── ChatMessage.tsx
│   │   │   ├── ModelSelector.tsx
│   │   │   └── ChatHistory.tsx
│   │   ├── backtest/
│   │   │   ├── BacktestForm.tsx
│   │   │   ├── BacktestResults.tsx
│   │   │   ├── EquityCurve.tsx
│   │   │   └── TradesList.tsx
│   │   ├── portfolio/
│   │   │   ├── PortfolioOverview.tsx
│   │   │   ├── PerformanceMetrics.tsx
│   │   │   └── AssetAllocation.tsx
│   │   └── dgm/
│   │       ├── DGMExperiments.tsx
│   │       ├── StrategyEvolution.tsx
│   │       └── ExperimentResults.tsx
│   ├── hooks/
│   │   ├── useAuth.ts
│   │   ├── useApi.ts
│   │   ├── useWebSocket.ts
│   │   └── useLocalStorage.ts
│   ├── services/
│   │   ├── api.ts
│   │   ├── auth.ts
│   │   └── websocket.ts
│   ├── types/
│   │   ├── api.ts
│   │   ├── trading.ts
│   │   └── index.ts
│   ├── utils/
│   │   ├── formatters.ts
│   │   ├── validators.ts
│   │   └── constants.ts
│   ├── pages/
│   │   ├── Dashboard.tsx
│   │   ├── Upload.tsx
│   │   ├── Backtesting.tsx
│   │   ├── Portfolio.tsx
│   │   ├── DGMExperiments.tsx
│   │   └── Settings.tsx
│   ├── App.tsx
│   ├── index.tsx
│   └── index.css
├── package.json
├── tsconfig.json
├── tailwind.config.js
└── vite.config.ts
```

## Core Types

### src/types/api.ts
```typescript
export interface User {
  id: string;
  email: string;
  fullName?: string;
  subscriptionTier: 'free' | 'solo' | 'pro' | 'enterprise';
  apiQuotaUsed: number;
  apiQuotaLimit: number;
  createdAt: string;
}

export interface UploadSession {
  id: string;
  originalFilename: string;
  fileSize?: number;
  inferredColumns: Record<string, string>;
  finalMapping?: Record<string, string>;
  timezone: string;
  status: 'pending' | 'mapping' | 'parsing' | 'ready' | 'error';
  errorMessage?: string;
  rowsProcessed: number;
  createdAt: string;
}

export interface BacktestConfig {
  name: string;
  symbol: string;
  strategyConfig: Record<string, any>;
  startDate: string;
  endDate: string;
  initialBalance?: number;
  modelName: string;
}

export interface Backtest {
  id: string;
  name: string;
  symbol: string;
  totalReturn?: number;
  maxDrawdown?: number;
  sharpeRatio?: number;
  winRate?: number;
  totalTrades?: number;
  status: 'pending' | 'running' | 'completed' | 'error';
  createdAt: string;
  completedAt?: string;
}

export interface ChatSession {
  id: string;
  title: string;
  createdAt: string;
  updatedAt: string;
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  modelUsed?: string;
  confidenceScore?: number;
  createdAt: string;
}

export interface DGMExperiment {
  id: string;
  experimentName: string;
  baseStrategy: Record<string, any>;
  generatedStrategy?: Record<string, any>;
  fitnessScore?: number;
  improvementOverBase?: number;
  status: 'pending' | 'running' | 'completed' | 'deployed' | 'error';
  createdAt: string;
}
```

### src/types/trading.ts
```typescript
export interface EquityPoint {
  timestamp: string;
  equity: number;
  drawdownPct?: number;
}

export interface Trade {
  id: string;
  symbol: string;
  side: 'BUY' | 'SELL';
  entryTime: string;
  exitTime?: string;
  entryPrice: number;
  exitPrice?: number;
  quantity: number;
  pnl?: number;
  pnlPct?: number;
  reasonEntry?: string;
  reasonExit?: string;
}

export interface PerformanceMetrics {
  totalReturn: number;
  annualizedReturn: number;
  maxDrawdown: number;
  sharpeRatio: number;
  winRate: number;
  avgWin: number;
  avgLoss: number;
  profitFactor: number;
}
```

## API Service

### src/services/api.ts
```typescript
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { User, UploadSession, Backtest, ChatSession, ChatMessage, DGMExperiment } from '../types/api';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1',
      timeout: 30000,
    });

    // Request interceptor for auth
    this.api.interceptors.request.use((config) => {
      const token = localStorage.getItem('auth_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    });

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('auth_token');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Auth endpoints
  async login(email: string, password: string): Promise<{ token: string; user: User }> {
    const response = await this.api.post('/auth/login', { email, password });
    return response.data;
  }

  async register(email: string, password: string, fullName?: string): Promise<{ token: string; user: User }> {
    const response = await this.api.post('/auth/register', { email, password, fullName });
    return response.data;
  }

  async getCurrentUser(): Promise<User> {
    const response = await this.api.get('/auth/me');
    return response.data;
  }

  // Upload endpoints
  async uploadFile(file: File, userId: string): Promise<UploadSession> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('user_id', userId);

    const response = await this.api.post('/upload', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
    return response.data;
  }

  async confirmMapping(
    uploadSessionId: string,
    finalMapping: Record<string, string>,
    timezone: string,
    temporaryFilePath: string
  ): Promise<{ message: string }> {
    const response = await this.api.post('/upload/confirm-mapping', {
      uploadSessionId,
      finalMapping,
      timezone,
      temporaryFilePath,
    });
    return response.data;
  }

  async getUploadSessions(): Promise<UploadSession[]> {
    const response = await this.api.get('/upload/sessions');
    return response.data;
  }

  // Chat endpoints
  async createChatSession(title?: string): Promise<ChatSession> {
    const response = await this.api.post('/chat/sessions', { title });
    return response.data;
  }

  async getChatSessions(): Promise<ChatSession[]> {
    const response = await this.api.get('/chat/sessions');
    return response.data;
  }

  async sendMessage(
    sessionId: string,
    message: string,
    models?: string[],
    requireConsensus?: boolean
  ): Promise<{
    message: string;
    consensus: any;
    tradingContext?: any;
    messageId: string;
  }> {
    const response = await this.api.post(`/chat/sessions/${sessionId}/messages`, {
      message,
      models,
      requireConsensus,
    });
    return response.data;
  }

  async getSessionMessages(sessionId: string, limit = 50): Promise<ChatMessage[]> {
    const response = await this.api.get(`/chat/sessions/${sessionId}/messages?limit=${limit}`);
    return response.data;
  }

  // Backtest endpoints
  async createBacktest(config: BacktestConfig): Promise<{ backtestId: string }> {
    const response = await this.api.post('/backtest', config);
    return response.data;
  }

  async getBacktests(): Promise<Backtest[]> {
    const response = await this.api.get('/backtest');
    return response.data;
  }

  async getBacktestResults(backtestId: string): Promise<{
    backtest: Backtest;
    equityCurve: any[];
    trades: any[];
    metrics: any;
  }> {
    const response = await this.api.get(`/backtest/${backtestId}/results`);
    return response.data;
  }

  // DGM endpoints
  async createDGMExperiment(
    name: string,
    baseStrategy: Record<string, any>
  ): Promise<{ experimentId: string }> {
    const response = await this.api.post('/dgm/experiments', {
      experimentName: name,
      baseStrategy,
    });
    return response.data;
  }

  async getDGMExperiments(): Promise<DGMExperiment[]> {
    const response = await this.api.get('/dgm/experiments');
    return response.data;
  }

  async getBestStrategies(): Promise<any[]> {
    const response = await this.api.get('/dgm/best-strategies');
    return response.data;
  }

  // Portfolio endpoints
  async getPortfolioOverview(): Promise<any> {
    const response = await this.api.get('/portfolio/overview');
    return response.data;
  }

  async getPerformanceMetrics(period = '1M'): Promise<any> {
    const response = await this.api.get(`/portfolio/metrics?period=${period}`);
    return response.data;
  }
}

export const apiService = new ApiService();
```

## Custom Hooks

### src/hooks/useAuth.ts
```typescript
import { useState, useEffect, createContext, useContext, ReactNode } from 'react';
import { User } from '../types/api';
import { apiService } from '../services/api';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, fullName?: string) => Promise<void>;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      apiService.getCurrentUser()
        .then(setUser)
        .catch(() => localStorage.removeItem('auth_token'))
        .finally(() => setIsLoading(false));
    } else {
      setIsLoading(false);
    }
  }, []);

  const login = async (email: string, password: string) => {
    const { token, user: userData } = await apiService.login(email, password);
    localStorage.setItem('auth_token', token);
    setUser(userData);
  };

  const register = async (email: string, password: string, fullName?: string) => {
    const { token, user: userData } = await apiService.register(email, password, fullName);
    localStorage.setItem('auth_token', token);
    setUser(userData);
  };

  const logout = () => {
    localStorage.removeItem('auth_token');
    setUser(null);
  };

  return (
    <AuthContext.Provider value={{ user, isLoading, login, register, logout }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
```

### src/hooks/useApi.ts
```typescript
import { useState, useCallback } from 'react';

interface UseApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

export function useApi<T>() {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: false,
    error: null,
  });

  const execute = useCallback(async (apiCall: () => Promise<T>) => {
    setState({ data: null, loading: true, error: null });
    
    try {
      const result = await apiCall();
      setState({ data: result, loading: false, error: null });
      return result;
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'An error occurred';
      setState({ data: null, loading: false, error: errorMessage });
      throw error;
    }
  }, []);

  return { ...state, execute };
}
```

## Core Components

### src/components/upload/FileUpload.tsx
```typescript
import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, FileText, AlertCircle } from 'lucide-react';
import { apiService } from '../../services/api';
import { useAuth } from '../../hooks/useAuth';
import { useApi } from '../../hooks/useApi';
import { UploadSession } from '../../types/api';
import { ColumnMapping } from './ColumnMapping';
import { UploadProgress } from './UploadProgress';

export function FileUpload() {
  const { user } = useAuth();
  const [uploadSession, setUploadSession] = useState<UploadSession | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const { data, loading, error, execute } = useApi<UploadSession>();

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (!user || acceptedFiles.length === 0) return;

    const file = acceptedFiles[0];
    
    try {
      const session = await execute(() => apiService.uploadFile(file, user.id));
      setUploadSession(session);
    } catch (error) {
      console.error('Upload failed:', error);
    }
  }, [user, execute]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.ms-excel': ['.xls', '.xlsx'],
      'application/json': ['.json'],
    },
    maxSize: 500 * 1024 * 1024, // 500MB
    multiple: false,
  });

  const handleMappingConfirm = async (
    finalMapping: Record<string, string>,
    timezone: string
  ) => {
    if (!uploadSession) return;

    try {
      await apiService.confirmMapping(
        uploadSession.id,
        finalMapping,
        timezone,
        uploadSession.temporaryFilePath || ''
      );
      
      // Start polling for progress
      setUploadProgress(10);
      pollProgress(uploadSession.id);
    } catch (error) {
      console.error('Mapping confirmation failed:', error);
    }
  };

  const pollProgress = async (sessionId: string) => {
    // This would poll the upload session status
    // For now, simulate progress
    let progress = 10;
    const interval = setInterval(() => {
      progress += 10;
      setUploadProgress(progress);
      
      if (progress >= 100) {
        clearInterval(interval);
        setUploadSession(null);
        setUploadProgress(0);
      }
    }, 1000);
  };

  if (uploadProgress > 0) {
    return <UploadProgress progress={uploadProgress} />;
  }

  if (uploadSession) {
    return (
      <ColumnMapping
        uploadSession={uploadSession}
        onConfirm={handleMappingConfirm}
        onCancel={() => setUploadSession(null)}
      />
    );
  }

  return (
    <div className="w-full max-w-2xl mx-auto">
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
          isDragActive
            ? 'border-blue-400 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
        }`}
      >
        <input {...getInputProps()} />
        
        <div className="flex flex-col items-center space-y-4">
          <Upload className={`w-12 h-12 ${isDragActive ? 'text-blue-500' : 'text-gray-400'}`} />
          
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              {isDragActive ? 'Drop your file here' : 'Upload trading data'}
            </h3>
            <p className="text-sm text-gray-500 mt-1">
              Drag and drop your CSV, Excel, or JSON file, or click to browse
            </p>
          </div>
          
          <div className="text-xs text-gray-400 space-y-1">
            <p>Supported formats: CSV, XLSX, XLS, JSON</p>
            <p>Maximum file size: 500MB</p>
          </div>
        </div>
      </div>

      {loading && (
        <div className="mt-4 text-center">
          <div className="inline-flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span className="text-sm text-gray-600">Uploading and analyzing file...</span>
          </div>
        </div>
      )}

      {error && (
        <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <AlertCircle className="w-5 h-5 text-red-500" />
            <span className="text-sm text-red-700">{error}</span>
          </div>
        </div>
      )}
    </div>
  );
}
```

### src/components/chat/ChatWidget.tsx
```typescript
import React, { useState, useEffect, useRef } from 'react';
import { Send, Bot, User, Zap } from 'lucide-react';
import { apiService } from '../../services/api';
import { useAuth } from '../../hooks/useAuth';
import { ChatMessage as ChatMessageType, ChatSession } from '../../types/api';
import { ChatMessage } from './ChatMessage';
import { ModelSelector } from './ModelSelector';

interface ChatWidgetProps {
  className?: string;
}

export function ChatWidget({ className = '' }: ChatWidgetProps) {
  const { user } = useAuth();
  const [session, setSession] = useState<ChatSession | null>(null);
  const [messages, setMessages] = useState<ChatMessageType[]>([]);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [selectedModels, setSelectedModels] = useState(['hf_primary']);
  const [requireConsensus, setRequireConsensus] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (user) {
      initializeSession();
    }
  }, [user]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const initializeSession = async () => {
    try {
      const newSession = await apiService.createChatSession('Trading Analysis');
      setSession(newSession);
      
      // Add welcome message
      setMessages([
        {
          id: 'welcome',
          role: 'assistant',
          content: 'Hi! I\'m your AI trading assistant. I can help you analyze your trading data, understand backtest results, and provide insights on trading strategies. What would you like to explore?',
          createdAt: new Date().toISOString(),
        },
      ]);
    } catch (error) {
      console.error('Failed to create chat session:', error);
    }
  };

  const sendMessage = async () => {
    if (!input.trim() || !session || loading) return;

    const userMessage: ChatMessageType = {
      id: Date.now().toString(),
      role: 'user',
      content: input,
      createdAt: new Date().toISOString(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setLoading(true);

    try {
      const response = await apiService.sendMessage(
        session.id,
        input,
        selectedModels,
        requireConsensus
      );

      const assistantMessage: ChatMessageType = {
        id: response.messageId,
        role: 'assistant',
        content: response.message,
        modelUsed: selectedModels.join(', '),
        confidenceScore: response.consensus?.confidence,
        createdAt: new Date().toISOString(),
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Failed to send message:', error);
      
      const errorMessage: ChatMessageType = {
        id: Date.now().toString(),
        role: 'assistant',
        content: 'I apologize, but I encountered an error processing your message. Please try again.',
        createdAt: new Date().toISOString(),
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  return (
    <div className={`flex flex-col h-full bg-white rounded-lg shadow-lg ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <Bot className="w-6 h-6 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">AI Trading Assistant</h3>
        </div>
        
        <ModelSelector
          selectedModels={selectedModels}
          onSelectionChange={setSelectedModels}
          requireConsensus={requireConsensus}
          onConsensusChange={setRequireConsensus}
        />
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <ChatMessage key={message.id} message={message} />
        ))}
        
        {loading && (
          <div className="flex items-center space-x-2">
            <Bot className="w-6 h-6 text-blue-600" />
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex space-x-2">
          <textarea
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask me about your trading data, backtests, or strategies..."
            className="flex-1 p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            rows={1}
            style={{ maxHeight: '120px' }}
          />
          
          <button
            onClick={sendMessage}
            disabled={!input.trim() || loading}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Send className="w-5 h-5" />
          </button>
        </div>
        
        {requireConsensus && selectedModels.length > 1 && (
          <div className="mt-2 flex items-center space-x-1 text-sm text-amber-600">
            <Zap className="w-4 h-4" />
            <span>Consensus mode enabled - all models must agree</span>
          </div>
        )}
      </div>
    </div>
  );
}
```

### src/components/backtest/BacktestForm.tsx
```typescript
import React, { useState } from 'react';
import { Calendar, TrendingUp, Settings } from 'lucide-react';
import { apiService } from '../../services/api';
import { useApi } from '../../hooks/useApi';
import { BacktestConfig } from '../../types/api';

interface BacktestFormProps {
  onBacktestCreated: (backtestId: string) => void;
}

export function BacktestForm({ onBacktestCreated }: BacktestFormProps) {
  const [config, setConfig] = useState<Partial<BacktestConfig>>({
    name: '',
    symbol: 'EURUSD',
    startDate: '',
    endDate: '',
    initialBalance: 100000,
    modelName: 'xllm-mini',
    strategyConfig: {
      indicators: {
        rsi: { period: 14, oversold: 30, overbought: 70 },
        macd: { fast: 12, slow: 26, signal: 9 },
        sma: { period: 20 }
      },
      riskManagement: {
        stopLoss: 0.02,
        takeProfit: 0.04,
        positionSize: 0.1
      },
      rules: [
        'BUY when RSI < oversold AND MACD > 0',
        'SELL when RSI > overbought OR stop_loss OR take_profit'
      ]
    }
  });

  const { loading, error, execute } = useApi<{ backtestId: string }>();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!config.name || !config.startDate || !config.endDate) {
      return;
    }

    try {
      const result = await execute(() => 
        apiService.createBacktest(config as BacktestConfig)
      );
      
      if (result) {
        onBacktestCreated(result.backtestId);
        // Reset form
        setConfig(prev => ({ ...prev, name: '' }));
      }
    } catch (error) {
      console.error('Failed to create backtest:', error);
    }
  };

  const updateStrategyConfig = (key: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      strategyConfig: {
        ...prev.strategyConfig,
        [key]: value
      }
    }));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 bg-white p-6 rounded-lg shadow">
      {/* Basic Configuration */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Configuration</h3>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Backtest Name
            </label>
            <input
              type="text"
              value={config.name || ''}
              onChange={(e) => setConfig(prev => ({ ...prev, name: e.target.value }))}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              placeholder="My Trading Strategy"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Symbol
            </label>
            <select
              value={config.symbol || 'EURUSD'}
              onChange={(e) => setConfig(prev => ({ ...prev, symbol: e.target.value }))}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            >
              <option value="EURUSD">EUR/USD</option>
              <option value="GBPUSD">GBP/USD</option>
              <option value="USDJPY">USD/JPY</option>
              <option value="AUDUSD">AUD/USD</option>
              <option value="USDCAD">USD/CAD</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Start Date
            </label>
            <input
              type="date"
              value={config.startDate || ''}
              onChange={(e) => setConfig(prev => ({ ...prev, startDate: e.target.value }))}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              End Date
            </label>
            <input
              type="date"
              value={config.endDate || ''}
              onChange={(e) => setConfig(prev => ({ ...prev, endDate: e.target.value }))}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Initial Balance ($)
            </label>
            <input
              type="number"
              value={config.initialBalance || 100000}
              onChange={(e) => setConfig(prev => ({ ...prev, initialBalance: Number(e.target.value) }))}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              min="1000"
              step="1000"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Model
            </label>
            <select
              value={config.modelName || 'xllm-mini'}
              onChange={(e) => setConfig(prev => ({ ...prev, modelName: e.target.value }))}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            >
              <option value="xllm-mini">xLLM Mini</option>
              <option value="xllm-pro">xLLM Pro</option>
              <option value="traditional">Traditional</option>
            </select>
          </div>
        </div>
      </div>

      {/* Strategy Configuration */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <Settings className="w-5 h-5 mr-2" />
          Strategy Configuration
        </h3>
        
        {/* Indicators */}
        <div className="mb-4">
          <h4 className="text-md font-medium text-gray-800 mb-2">Technical Indicators</h4>
          
          <div className="grid grid-cols-3 gap-4">
            <div className="p-3 border border-gray-200 rounded-lg">
              <h5 className="font-medium text-sm text-gray-700 mb-2">RSI</h5>
              <div className="space-y-2">
                <input
                  type="number"
                  placeholder="Period (14)"
                  value={config.strategyConfig?.indicators?.rsi?.period || 14}
                  onChange={(e) => updateStrategyConfig('indicators', {
                    ...config.strategyConfig?.indicators,
                    rsi: {
                      ...config.strategyConfig?.indicators?.rsi,
                      period: Number(e.target.value)
                    }
                  })}
                  className="w-full p-1 text-xs border border-gray-300 rounded"
                />
                <input
                  type="number"
                  placeholder="Oversold (30)"
                  value={config.strategyConfig?.indicators?.rsi?.oversold || 30}
                  onChange={(e) => updateStrategyConfig('indicators', {
                    ...config.strategyConfig?.indicators,
                    rsi: {
                      ...config.strategyConfig?.indicators?.rsi,
                      oversold: Number(e.target.value)
                    }
                  })}
                  className="w-full p-1 text-xs border border-gray-300 rounded"
                />
                <input
                  type="number"
                  placeholder="Overbought (70)"
                  value={config.strategyConfig?.indicators?.rsi?.overbought || 70}
                  onChange={(e) => updateStrategyConfig('indicators', {
                    ...config.strategyConfig?.indicators,
                    rsi: {
                      ...config.strategyConfig?.indicators?.rsi,
                      overbought: Number(e.target.value)
                    }
                  })}
                  className="w-full p-1 text-xs border border-gray-300 rounded"
                />
              </div>
            </div>
            
            <div className="p-3 border border-gray-200 rounded-lg">
              <h5 className="font-medium text-sm text-gray-700 mb-2">MACD</h5>
              <div className="space-y-2">
                <input
                  type="number"
                  placeholder="Fast (12)"
                  value={config.strategyConfig?.indicators?.macd?.fast || 12}
                  className="w-full p-1 text-xs border border-gray-300 rounded"
                />
                <input
                  type="number"
                  placeholder="Slow (26)"
                  value={config.strategyConfig?.indicators?.macd?.slow || 26}
                  className="w-full p-1 text-xs border border-gray-300 rounded"
                />
                <input
                  type="number"
                  placeholder="Signal (9)"
                  value={config.strategyConfig?.indicators?.macd?.signal || 9}
                  className="w-full p-1 text-xs border border-gray-300 rounded"
                />
              </div>
            </div>
            
            <div className="p-3 border border-gray-200 rounded-lg">
              <h5 className="font-medium text-sm text-gray-700 mb-2">SMA</h5>
              <input
                type="number"
                placeholder="Period (20)"
                value={config.strategyConfig?.indicators?.sma?.period || 20}
                className="w-full p-1 text-xs border border-gray-300 rounded"
              />
            </div>
          </div>
        </div>

        {/* Risk Management */}
        <div className="mb-4">
          <h4 className="text-md font-medium text-gray-800 mb-2">Risk Management</h4>
          
          <div className="grid grid-cols-3 gap-4">
            <div>
              <label className="block text-xs font-medium text-gray-600 mb-1">
                Stop Loss (%)
              </label>
              <input
                type="number"
                value={config.strategyConfig?.riskManagement?.stopLoss || 0.02}
                onChange={(e) => updateStrategyConfig('riskManagement', {
                  ...config.strategyConfig?.riskManagement,
                  stopLoss: Number(e.target.value)
                })}
                className="w-full p-2 border border-gray-300 rounded"
                step="0.001"
                min="0"
                max="1"
              />
            </div>
            
            <div>
              <label className="block text-xs font-medium text-gray-600 mb-1">
                Take Profit (%)
              </label>
              <input
                type="number"
                value={config.strategyConfig?.riskManagement?.takeProfit || 0.04}
                className="w-full p-2 border border-gray-300 rounded"
                step="0.001"
                min="0"
                max="1"
              />
            </div>
            
            <div>
              <label className="block text-xs font-medium text-gray-600 mb-1">
                Position Size (%)
              </label>
              <input
                type="number"
                value={config.strategyConfig?.riskManagement?.positionSize || 0.1}
                className="w-full p-2 border border-gray-300 rounded"
                step="0.01"
                min="0.01"
                max="1"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Submit */}
      <div className="flex justify-end space-x-3">
        <button
          type="button"
          className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
        >
          Save as Template
        </button>
        
        <button
          type="submit"
          disabled={loading}
          className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
        >
          {loading ? (
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
          ) : (
            <TrendingUp className="w-4 h-4" />
          )}
          <span>{loading ? 'Creating...' : 'Start Backtest'}</span>
        </button>
      </div>

      {error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-sm text-red-700">
          {error}
        </div>
      )}
    </form>
  );
}
```

### package.json
```json
{
  "name": "ai-trading-frontend",
  "version": "1.0.0",
  "private": true,
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.8.0",
    "axios": "^1.3.0",
    "react-dropzone": "^14.2.0",
    "lucide-react": "^0.263.1",
    "recharts": "^2.5.0",
    "date-fns": "^2.29.0",
    "classnames": "^2.3.0"
  },
  "devDependencies": {
    "@types/react": "^18.0.0",
    "@types/react-dom": "^18.0.0",
    "@vitejs/plugin-react": "^3.1.0",
    "typescript": "^4.9.0",
    "vite": "^4.1.0",
    "tailwindcss": "^3.2.0",
    "autoprefixer": "^10.4.0",
    "postcss": "^8.4.0"
  },
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0"
  }
}
```
