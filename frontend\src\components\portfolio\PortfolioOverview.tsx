// import React from 'react'; // Not needed with new JSX transform
import { TrendingUp, TrendingDown, DollarSign, PieChart, BarChart3, Target } from 'lucide-react';

interface PortfolioData {
  totalValue: number;
  totalReturn: number;
  totalReturnPercent: number;
  dayChange: number;
  dayChangePercent: number;
  totalTrades: number;
  winRate: number;
  activeStrategies: number;
  bestPerformer: {
    name: string;
    return: number;
  } | null;
  worstPerformer: {
    name: string;
    return: number;
  } | null;
}

interface PortfolioOverviewProps {
  data: PortfolioData;
  loading?: boolean;
}

export function PortfolioOverview({ data, loading = false }: PortfolioOverviewProps) {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(8)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gray-200 rounded"></div>
              <div className="ml-3 flex-1">
                <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                <div className="h-6 bg-gray-200 rounded w-16 mb-1"></div>
                <div className="h-3 bg-gray-200 rounded w-20"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  const StatCard = ({ 
    title, 
    value, 
    change, 
    changePercent,
    icon: Icon, 
    color = 'text-gray-600',
    trend
  }: any) => (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <Icon className={`w-8 h-8 ${color}`} />
        </div>
        <div className="ml-3 flex-1">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-semibold text-gray-900">{value}</p>
          {(change !== undefined || changePercent !== undefined) && (
            <div className="flex items-center mt-1">
              {trend === 'up' ? (
                <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
              ) : trend === 'down' ? (
                <TrendingDown className="w-4 h-4 text-red-500 mr-1" />
              ) : null}
              <span className={`text-sm ${
                trend === 'up' ? 'text-green-600' : 
                trend === 'down' ? 'text-red-600' : 
                'text-gray-600'
              }`}>
                {change && `$${Math.abs(change).toLocaleString()}`}
                {change && changePercent && ' '}
                {changePercent && `(${changePercent >= 0 ? '+' : ''}${changePercent.toFixed(2)}%)`}
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Main Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Portfolio Value"
          value={`$${data.totalValue.toLocaleString()}`}
          change={data.dayChange}
          changePercent={data.dayChangePercent}
          icon={DollarSign}
          color="text-green-600"
          trend={data.dayChange >= 0 ? 'up' : 'down'}
        />

        <StatCard
          title="Total Return"
          value={`$${data.totalReturn.toLocaleString()}`}
          changePercent={data.totalReturnPercent}
          icon={TrendingUp}
          color="text-blue-600"
          trend={data.totalReturn >= 0 ? 'up' : 'down'}
        />

        <StatCard
          title="Win Rate"
          value={`${data.winRate.toFixed(1)}%`}
          icon={Target}
          color="text-purple-600"
        />

        <StatCard
          title="Active Strategies"
          value={data.activeStrategies}
          icon={BarChart3}
          color="text-orange-600"
        />
      </div>

      {/* Additional Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <PieChart className="w-8 h-8 text-indigo-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Total Trades</p>
              <p className="text-2xl font-semibold text-gray-900">{data.totalTrades.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <TrendingUp className="w-8 h-8 text-green-600" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600">Best Performer</p>
              {data.bestPerformer ? (
                <>
                  <p className="text-lg font-semibold text-gray-900 truncate">
                    {data.bestPerformer.name}
                  </p>
                  <p className="text-sm text-green-600">
                    +{data.bestPerformer.return.toFixed(2)}%
                  </p>
                </>
              ) : (
                <p className="text-lg text-gray-500">No data</p>
              )}
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <TrendingDown className="w-8 h-8 text-red-600" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600">Worst Performer</p>
              {data.worstPerformer ? (
                <>
                  <p className="text-lg font-semibold text-gray-900 truncate">
                    {data.worstPerformer.name}
                  </p>
                  <p className="text-sm text-red-600">
                    {data.worstPerformer.return.toFixed(2)}%
                  </p>
                </>
              ) : (
                <p className="text-lg text-gray-500">No data</p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}