# src/validation/data_validator.py
import hashlib
import json
import logging
from decimal import Decimal
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import asdict, dataclass
from enum import Enum

class DataValidationError(Exception):
    """Custom exception for data validation errors"""
    pass


class ValidationSeverity(Enum):
    """Severity levels for validation issues"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class ValidationResult:
    """Result of data validation with detailed information"""
    is_valid: bool
    severity: ValidationSeverity
    message: str
    field: Optional[str] = None
    expected_value: Optional[Any] = None
    actual_value: Optional[Any] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class AuditLogEntry:
    """Audit log entry for data operations"""
    timestamp: datetime
    operation: str
    data_hash: str
    source: str
    user_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class OHLCData:
    """OHLC data structure with enhanced validation support"""
    timestamp: datetime
    open: Decimal
    high: Decimal
    low: Decimal
    close: Decimal
    volume: int
    source: str
    hash: str
    symbol: Optional[str] = None
    timeframe: Optional[str] = None
    validated: bool = False
    validation_timestamp: Optional[datetime] = None

class DataValidator:
    """Enhanced data validator with comprehensive audit trail capabilities"""
    
    def __init__(self, enable_audit_trail: bool = True):
        self.validation_rules = {
            'ohlc_consistency': self._validate_ohlc_consistency,
            'timestamp_sequence': self._validate_timestamp_sequence,
            'volume_positive': self._validate_volume_positive,
            'price_precision': self._validate_price_precision,
            'symbol_format': self._validate_symbol_format,
            'timeframe_validity': self._validate_timeframe_validity
        }
        
        self.enable_audit_trail = enable_audit_trail
        self.audit_log: List[AuditLogEntry] = []
        self.validation_history: List[ValidationResult] = []
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def validate_ohlc(self, data: OHLCData) -> bool:
        """Validate OHLC data consistency with detailed audit trail"""
        validation_results = []
        
        try:
            for rule_name, rule_func in self.validation_rules.items():
                try:
                    is_valid = rule_func(data)
                    if not is_valid:
                        result = ValidationResult(
                            is_valid=False,
                            severity=ValidationSeverity.ERROR,
                            message=f"Validation failed: {rule_name}",
                            field=rule_name
                        )
                        validation_results.append(result)
                        self.validation_history.append(result)
                        
                        if self.enable_audit_trail:
                            self._log_validation_failure(data, rule_name, result)
                        
                        raise DataValidationError(f"Validation failed: {rule_name}")
                    else:
                        result = ValidationResult(
                            is_valid=True,
                            severity=ValidationSeverity.INFO,
                            message=f"Validation passed: {rule_name}",
                            field=rule_name
                        )
                        validation_results.append(result)
                        
                except Exception as rule_error:
                    result = ValidationResult(
                        is_valid=False,
                        severity=ValidationSeverity.CRITICAL,
                        message=f"Rule execution error in {rule_name}: {str(rule_error)}",
                        field=rule_name
                    )
                    validation_results.append(result)
                    self.validation_history.append(result)
                    raise DataValidationError(f"Rule execution failed: {rule_name} - {str(rule_error)}")
            
            # Mark data as validated
            data.validated = True
            data.validation_timestamp = datetime.now()
            
            # Log successful validation
            if self.enable_audit_trail:
                self._log_successful_validation(data)
            
            return True
            
        except Exception as e:
            if self.enable_audit_trail:
                self._log_validation_error(data, str(e))
            raise DataValidationError(f"OHLC validation failed: {str(e)}")
    
    def validate_ohlc_detailed(self, data: OHLCData) -> Tuple[bool, List[ValidationResult]]:
        """Validate OHLC data and return detailed results"""
        validation_results = []
        overall_valid = True
        
        for rule_name, rule_func in self.validation_rules.items():
            try:
                is_valid = rule_func(data)
                result = ValidationResult(
                    is_valid=is_valid,
                    severity=ValidationSeverity.INFO if is_valid else ValidationSeverity.ERROR,
                    message=f"Validation {'passed' if is_valid else 'failed'}: {rule_name}",
                    field=rule_name
                )
                validation_results.append(result)
                self.validation_history.append(result)
                
                if not is_valid:
                    overall_valid = False
                    
            except Exception as rule_error:
                result = ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.CRITICAL,
                    message=f"Rule execution error in {rule_name}: {str(rule_error)}",
                    field=rule_name
                )
                validation_results.append(result)
                self.validation_history.append(result)
                overall_valid = False
        
        return overall_valid, validation_results
    
    def _validate_ohlc_consistency(self, data: OHLCData) -> bool:
        """Ensure high >= low and open/close within range"""
        if data.high < data.low:
            return False
        if not (data.low <= data.open <= data.high):
            return False
        if not (data.low <= data.close <= data.high):
            return False
        return True
    
    def _validate_timestamp_sequence(self, data: OHLCData) -> bool:
        """Validate timestamp is reasonable"""
        now = datetime.now()
        if data.timestamp > now:
            return False
        # Don't accept data older than 10 years
        if (now - data.timestamp).days > 3650:
            return False
        return True
    
    def _validate_volume_positive(self, data: OHLCData) -> bool:
        """Ensure volume is positive"""
        return data.volume >= 0
    
    def _validate_price_precision(self, data: OHLCData) -> bool:
        """Validate price precision (max 5 decimal places for forex)"""
        for price in [data.open, data.high, data.low, data.close]:
            if price.as_tuple().exponent < -5:
                return False
        return True
    
    def _validate_symbol_format(self, data: OHLCData) -> bool:
        """Validate symbol format if provided"""
        if data.symbol is None:
            return True  # Optional field
        
        # Basic symbol format validation (e.g., EURUSD, GBPJPY)
        if not isinstance(data.symbol, str):
            return False
        
        # Should be 6-8 characters for forex pairs
        if len(data.symbol) < 6 or len(data.symbol) > 8:
            return False
        
        # Should contain only uppercase letters
        if not data.symbol.isupper() or not data.symbol.isalpha():
            return False
        
        return True
    
    def _validate_timeframe_validity(self, data: OHLCData) -> bool:
        """Validate timeframe if provided"""
        if data.timeframe is None:
            return True  # Optional field
        
        valid_timeframes = [
            "M1", "M5", "M15", "M30", 
            "H1", "H4", "H8", "H12",
            "D1", "W1", "MN1"
        ]
        
        return data.timeframe in valid_timeframes
    
    def generate_data_hash(self, data: OHLCData) -> str:
        """Generate SHA-256 hash for data integrity"""
        # Exclude hash field from hashing
        data_dict = asdict(data)
        data_dict.pop('hash', None)
        
        # Create deterministic string representation
        data_string = json.dumps(data_dict, sort_keys=True, default=str)
        return hashlib.sha256(data_string.encode()).hexdigest()
    
    def verify_integrity(self, data: OHLCData) -> bool:
        """Verify data integrity using stored hash"""
        expected_hash = self.generate_data_hash(data)
        is_valid = data.hash == expected_hash
        
        if self.enable_audit_trail:
            self._log_integrity_check(data, is_valid)
        
        return is_valid
    
    # ============================================================================
    # AUDIT TRAIL METHODS
    # ============================================================================
    
    def _log_validation_failure(self, data: OHLCData, rule_name: str, result: ValidationResult):
        """Log validation failure to audit trail"""
        audit_entry = AuditLogEntry(
            timestamp=datetime.now(),
            operation=f"validation_failure_{rule_name}",
            data_hash=self.generate_data_hash(data),
            source=data.source,
            metadata={
                "rule": rule_name,
                "severity": result.severity.value,
                "message": result.message,
                "symbol": data.symbol,
                "timeframe": data.timeframe
            }
        )
        self.audit_log.append(audit_entry)
        self.logger.warning(f"Validation failure: {rule_name} for {data.source} data")
    
    def _log_successful_validation(self, data: OHLCData):
        """Log successful validation to audit trail"""
        audit_entry = AuditLogEntry(
            timestamp=datetime.now(),
            operation="validation_success",
            data_hash=self.generate_data_hash(data),
            source=data.source,
            metadata={
                "symbol": data.symbol,
                "timeframe": data.timeframe,
                "timestamp": data.timestamp.isoformat(),
                "rules_passed": len(self.validation_rules)
            }
        )
        self.audit_log.append(audit_entry)
        self.logger.info(f"Validation successful for {data.source} data")
    
    def _log_validation_error(self, data: OHLCData, error_message: str):
        """Log validation error to audit trail"""
        audit_entry = AuditLogEntry(
            timestamp=datetime.now(),
            operation="validation_error",
            data_hash=self.generate_data_hash(data),
            source=data.source,
            metadata={
                "error": error_message,
                "symbol": data.symbol,
                "timeframe": data.timeframe
            }
        )
        self.audit_log.append(audit_entry)
        self.logger.error(f"Validation error: {error_message}")
    
    def _log_integrity_check(self, data: OHLCData, is_valid: bool):
        """Log integrity check to audit trail"""
        audit_entry = AuditLogEntry(
            timestamp=datetime.now(),
            operation="integrity_check",
            data_hash=self.generate_data_hash(data),
            source=data.source,
            metadata={
                "integrity_valid": is_valid,
                "stored_hash": data.hash,
                "symbol": data.symbol,
                "timeframe": data.timeframe
            }
        )
        self.audit_log.append(audit_entry)
        
        if is_valid:
            self.logger.info(f"Integrity check passed for {data.source} data")
        else:
            self.logger.warning(f"Integrity check failed for {data.source} data - possible tampering detected")
    
    def get_audit_log(self, 
                     operation_filter: Optional[str] = None,
                     source_filter: Optional[str] = None,
                     start_time: Optional[datetime] = None,
                     end_time: Optional[datetime] = None) -> List[AuditLogEntry]:
        """Get filtered audit log entries"""
        filtered_log = self.audit_log
        
        if operation_filter:
            filtered_log = [entry for entry in filtered_log if operation_filter in entry.operation]
        
        if source_filter:
            filtered_log = [entry for entry in filtered_log if entry.source == source_filter]
        
        if start_time:
            filtered_log = [entry for entry in filtered_log if entry.timestamp >= start_time]
        
        if end_time:
            filtered_log = [entry for entry in filtered_log if entry.timestamp <= end_time]
        
        return filtered_log
    
    def get_validation_statistics(self) -> Dict[str, Any]:
        """Get validation statistics from history"""
        if not self.validation_history:
            return {"total_validations": 0}
        
        total = len(self.validation_history)
        passed = sum(1 for result in self.validation_history if result.is_valid)
        failed = total - passed
        
        # Count by severity
        severity_counts = {}
        for severity in ValidationSeverity:
            severity_counts[severity.value] = sum(
                1 for result in self.validation_history 
                if result.severity == severity
            )
        
        # Count by field/rule
        field_counts = {}
        for result in self.validation_history:
            if result.field:
                field_counts[result.field] = field_counts.get(result.field, 0) + 1
        
        return {
            "total_validations": total,
            "passed": passed,
            "failed": failed,
            "success_rate": (passed / total) * 100 if total > 0 else 0,
            "severity_breakdown": severity_counts,
            "field_breakdown": field_counts,
            "most_common_failures": sorted(
                [(field, count) for field, count in field_counts.items()],
                key=lambda x: x[1],
                reverse=True
            )[:5]
        }
    
    def export_audit_log(self, filename: str, format: str = "json"):
        """Export audit log to file"""
        if format.lower() == "json":
            audit_data = []
            for entry in self.audit_log:
                audit_data.append({
                    "timestamp": entry.timestamp.isoformat(),
                    "operation": entry.operation,
                    "data_hash": entry.data_hash,
                    "source": entry.source,
                    "user_id": entry.user_id,
                    "metadata": entry.metadata
                })
            
            with open(filename, 'w') as f:
                json.dump(audit_data, f, indent=2, default=str)
        
        elif format.lower() == "csv":
            import csv
            with open(filename, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(["timestamp", "operation", "data_hash", "source", "user_id", "metadata"])
                
                for entry in self.audit_log:
                    writer.writerow([
                        entry.timestamp.isoformat(),
                        entry.operation,
                        entry.data_hash,
                        entry.source,
                        entry.user_id,
                        json.dumps(entry.metadata) if entry.metadata else ""
                    ])
        
        self.logger.info(f"Audit log exported to {filename} in {format.upper()} format")
    
    def clear_audit_log(self, older_than: Optional[datetime] = None):
        """Clear audit log entries"""
        if older_than:
            self.audit_log = [entry for entry in self.audit_log if entry.timestamp > older_than]
            self.logger.info(f"Cleared audit log entries older than {older_than}")
        else:
            self.audit_log.clear()
            self.logger.info("Cleared all audit log entries")
    
    def clear_validation_history(self, older_than: Optional[datetime] = None):
        """Clear validation history"""
        if older_than:
            self.validation_history = [
                result for result in self.validation_history 
                if result.timestamp > older_than
            ]
            self.logger.info(f"Cleared validation history older than {older_than}")
        else:
            self.validation_history.clear()
            self.logger.info("Cleared all validation history")

# ============================================================================
# DATA SOURCE VERIFICATION & MANAGEMENT
# ============================================================================

class DataSourceManager:
    """Enhanced data source manager with comprehensive verification and audit trail"""
    
    def __init__(self, enable_audit_trail: bool = True):
        self.verified_sources = {
            'dukascopy': {
                'base_url': 'https://datafeed.dukascopy.com',
                'checksum_url': 'https://datafeed.dukascopy.com/checksums/',
                'trusted': True,
                'verification_method': 'checksum',
                'supported_formats': ['csv', 'bi5'],
                'rate_limit': 100,  # requests per minute
                'last_verified': None
            },
            'forexsb': {
                'base_url': 'https://forexsb.com/historical-forex-data',
                'trusted': True,
                'verification_method': 'signature',
                'supported_formats': ['csv', 'txt'],
                'rate_limit': 50,
                'last_verified': None
            },
            'test_source': {
                'base_url': 'http://localhost:8080/test-data',
                'trusted': True,
                'verification_method': 'none',
                'supported_formats': ['csv', 'json'],
                'rate_limit': 1000,
                'last_verified': None
            }
        }
        
        self.enable_audit_trail = enable_audit_trail
        self.source_audit_log: List[AuditLogEntry] = []
        self.verification_cache: Dict[str, Tuple[bool, datetime]] = {}
        
        # Setup logging
        self.logger = logging.getLogger(f"{__name__}.DataSourceManager")
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def verify_source_authenticity(self, source: str, data_file: str) -> bool:
        """Verify data comes from trusted source with enhanced verification"""
        verification_key = f"{source}:{data_file}"
        
        # Check cache first (valid for 1 hour)
        if verification_key in self.verification_cache:
            cached_result, cached_time = self.verification_cache[verification_key]
            if (datetime.now() - cached_time).seconds < 3600:  # 1 hour cache
                self._log_source_verification(source, data_file, cached_result, "cached")
                return cached_result
        
        # Perform verification
        is_verified = self._perform_source_verification(source, data_file)
        
        # Cache result
        self.verification_cache[verification_key] = (is_verified, datetime.now())
        
        # Log verification
        if self.enable_audit_trail:
            self._log_source_verification(source, data_file, is_verified, "verified")
        
        return is_verified
    
    def _perform_source_verification(self, source: str, data_file: str) -> bool:
        """Perform actual source verification"""
        if source not in self.verified_sources:
            self.logger.warning(f"Unknown source: {source}")
            return False
        
        source_config = self.verified_sources[source]
        if not source_config.get('trusted', False):
            self.logger.warning(f"Untrusted source: {source}")
            return False
        
        # Check file format
        file_extension = data_file.split('.')[-1].lower() if '.' in data_file else ''
        supported_formats = source_config.get('supported_formats', [])
        if supported_formats and file_extension not in supported_formats:
            self.logger.warning(f"Unsupported file format {file_extension} for source {source}")
            return False
        
        # Update last verified timestamp
        source_config['last_verified'] = datetime.now()
        
        # Additional verification based on method
        verification_method = source_config.get('verification_method', 'none')
        if verification_method == 'checksum':
            return self._verify_checksum(source, data_file)
        elif verification_method == 'signature':
            return self._verify_signature(source, data_file)
        else:
            return True  # Basic verification passed
    
    def _verify_checksum(self, source: str, data_file: str) -> bool:
        """Verify file checksum (placeholder implementation)"""
        # In a real implementation, this would fetch and verify checksums
        self.logger.info(f"Checksum verification for {source}:{data_file} (placeholder)")
        return True
    
    def _verify_signature(self, source: str, data_file: str) -> bool:
        """Verify file signature (placeholder implementation)"""
        # In a real implementation, this would verify digital signatures
        self.logger.info(f"Signature verification for {source}:{data_file} (placeholder)")
        return True
    
    def _log_source_verification(self, source: str, data_file: str, is_verified: bool, method: str):
        """Log source verification to audit trail"""
        audit_entry = AuditLogEntry(
            timestamp=datetime.now(),
            operation=f"source_verification_{method}",
            data_hash=hashlib.sha256(f"{source}:{data_file}".encode()).hexdigest(),
            source=source,
            metadata={
                "data_file": data_file,
                "verified": is_verified,
                "verification_method": method,
                "source_config": self.verified_sources.get(source, {})
            }
        )
        self.source_audit_log.append(audit_entry)
        
        if is_verified:
            self.logger.info(f"Source verification successful: {source}:{data_file}")
        else:
            self.logger.warning(f"Source verification failed: {source}:{data_file}")
    
    def add_trusted_source(self, source_name: str, config: Dict[str, Any]):
        """Add a new trusted source"""
        required_fields = ['base_url', 'trusted']
        for field in required_fields:
            if field not in config:
                raise ValueError(f"Missing required field: {field}")
        
        self.verified_sources[source_name] = config
        
        if self.enable_audit_trail:
            audit_entry = AuditLogEntry(
                timestamp=datetime.now(),
                operation="add_trusted_source",
                data_hash=hashlib.sha256(source_name.encode()).hexdigest(),
                source=source_name,
                metadata={"config": config}
            )
            self.source_audit_log.append(audit_entry)
        
        self.logger.info(f"Added trusted source: {source_name}")
    
    def remove_trusted_source(self, source_name: str):
        """Remove a trusted source"""
        if source_name in self.verified_sources:
            del self.verified_sources[source_name]
            
            if self.enable_audit_trail:
                audit_entry = AuditLogEntry(
                    timestamp=datetime.now(),
                    operation="remove_trusted_source",
                    data_hash=hashlib.sha256(source_name.encode()).hexdigest(),
                    source=source_name,
                    metadata={"removed": True}
                )
                self.source_audit_log.append(audit_entry)
            
            self.logger.info(f"Removed trusted source: {source_name}")
        else:
            self.logger.warning(f"Attempted to remove non-existent source: {source_name}")
    
    def get_source_statistics(self) -> Dict[str, Any]:
        """Get statistics about source verifications"""
        total_verifications = len(self.source_audit_log)
        successful_verifications = sum(
            1 for entry in self.source_audit_log 
            if entry.metadata and entry.metadata.get('verified', False)
        )
        
        # Count by source
        source_counts = {}
        for entry in self.source_audit_log:
            source = entry.source
            source_counts[source] = source_counts.get(source, 0) + 1
        
        return {
            "total_sources": len(self.verified_sources),
            "total_verifications": total_verifications,
            "successful_verifications": successful_verifications,
            "success_rate": (successful_verifications / total_verifications * 100) if total_verifications > 0 else 0,
            "source_usage": source_counts,
            "cache_size": len(self.verification_cache)
        }
    
    def clear_verification_cache(self):
        """Clear the verification cache"""
        cache_size = len(self.verification_cache)
        self.verification_cache.clear()
        self.logger.info(f"Cleared verification cache ({cache_size} entries)")
    
    def get_source_audit_log(self, source_filter: Optional[str] = None) -> List[AuditLogEntry]:
        """Get source audit log with optional filtering"""
        if source_filter:
            return [entry for entry in self.source_audit_log if entry.source == source_filter]
        return self.source_audit_log.copy()