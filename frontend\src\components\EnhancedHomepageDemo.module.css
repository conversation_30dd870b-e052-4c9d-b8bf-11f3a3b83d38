/* Modern dark theme with orange neon accents */
.homepage-demo-container {
  display: flex;
  flex-direction: row;
  gap: 2rem;
  justify-content: center;
  align-items: flex-start;
  background: #111;
  padding: 2rem 0;
}

.demo-card {
  background: #181818;
  border-radius: 1.2rem;
  box-shadow: 0 0 24px 4px rgba(255, 140, 0, 0.25), 0 2px 8px rgba(0,0,0,0.5);
  border: 2px solid #ff8000;
  color: #fff;
  flex: 1;
  min-width: 340px;
  max-width: 480px;
  padding: 2rem;
  margin: 0;
  position: relative;
  transition: box-shadow 0.2s;
}
.demo-card:hover {
  box-shadow: 0 0 32px 8px #ff8000, 0 2px 8px rgba(0,0,0,0.5);
}

.demo-card-title {
  font-size: 2rem;
  font-weight: 700;
  color: #ff8000;
  text-shadow: 0 0 8px #ff8000;
  margin-bottom: 1rem;
}

.demo-card-content {
  font-size: 1.1rem;
  color: #fff;
}

.demo-card-neon {
  border: 2px solid #ff8000;
  box-shadow: 0 0 16px 2px #ff8000;
}

.demo-chatbot {
  background: #181818;
  border-radius: 1.2rem;
  box-shadow: 0 0 24px 4px #ff8000, 0 2px 8px rgba(0,0,0,0.5);
  border: 2px solid #ff8000;
  color: #fff;
  flex: 1;
  min-width: 340px;
  max-width: 480px;
  padding: 2rem;
  margin: 0;
  position: relative;
}

.demo-chatbot-title {
  font-size: 2rem;
  font-weight: 700;
  color: #ff8000;
  text-shadow: 0 0 8px #ff8000;
  margin-bottom: 1rem;
}

.demo-chatbot-content {
  font-size: 1.1rem;
  color: #fff;
}

/* Neon button */
.demo-btn-neon {
  background: #ff8000;
  color: #fff;
  border: none;
  border-radius: 0.7rem;
  padding: 0.8rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  box-shadow: 0 0 12px 2px #ff8000;
  cursor: pointer;
  transition: box-shadow 0.2s, background 0.2s;
}
.demo-btn-neon:hover {
  background: #ff9900;
  box-shadow: 0 0 24px 4px #ff8000;
}

/* Responsive for mobile */
@media (max-width: 900px) {
  .homepage-demo-container {
    flex-direction: column;
    gap: 2rem;
    align-items: center;
  }
  .demo-card, .demo-chatbot {
    max-width: 98vw;
    min-width: 0;
  }
}

/* Section for demo */
#demo {
  padding: 4rem 0;
  background: #111;
}

/* Featured strategy prompts */
.featured-strategy-prompts {
  margin-bottom: 2rem;
}

/* Individual prompt item */
.prompt-item {
  cursor: pointer;
  margin-bottom: 1.5rem;
  background: #222;
  border-radius: 1rem;
  padding: 1rem;
  box-shadow: 0 0 8px #ff8000;
  transition: transform 0.2s;
}
.prompt-item:hover {
  transform: translateY(-2px);
}

/* Prompt category */
.prompt-category {
  color: #ff8000;
  font-weight: 600;
}

/* Prompt title */
.prompt-title {
  margin: 0.5rem 0;
  color: #fff;
}

/* Prompt description */
.prompt-description {
  color: #ccc;
}

/* Prompt variables */
.prompt-variables {
  font-size: 0.95rem;
  color: #ff8000;
}

/* Strategy Chatbot */
.strategy-chatbot {
  /* Add any specific styles for the chatbot here */
}
