"""
Mock Services for Testing

Complete set of mock service implementations for dependency injection testing.
These mocks provide controllable behavior for comprehensive unit testing.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field

from core.interfaces import (
    ITradingService, IRiskManagementService, IPortfolioService, 
    INotificationService, IDataStorageService,
    TradingSignal, Order, Position, MarketData
)

class MockTradingService(ITradingService):
    """Mock trading service for testing"""
    
    def __init__(self):
        self.orders: Dict[str, Order] = {}
        self.positions: List[Position] = []
        self.account_balance = {'cash': 10000.0, 'equity': 10000.0}
        self.order_counter = 0
        self.should_fail_orders = False
        self.order_fill_delay = 0.0  # seconds
    
    def set_account_balance(self, cash: float, equity: float):
        """Set mock account balance"""
        self.account_balance = {'cash': cash, 'equity': equity}
    
    def add_position(self, position: Position):
        """Add mock position"""
        self.positions.append(position)
    
    def set_order_failure(self, should_fail: bool):
        """Control order failure for testing"""
        self.should_fail_orders = should_fail
    
    def set_order_fill_delay(self, delay: float):
        """Set order fill delay for testing"""
        self.order_fill_delay = delay
    
    async def place_order(self, order: Order) -> str:
        """Place mock order"""
        if self.should_fail_orders:
            raise Exception("Mock order failure")
        
        self.order_counter += 1
        order_id = f"MOCK_{self.order_counter:06d}"
        
        # Update order with ID and status
        order.id = order_id
        order.status = 'submitted'
        
        # Store order
        self.orders[order_id] = order
        
        # Simulate order fill delay
        if self.order_fill_delay > 0:
            await asyncio.sleep(self.order_fill_delay)
            order.status = 'filled'
        
        return order_id
    
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel mock order"""
        if order_id in self.orders:
            self.orders[order_id].status = 'cancelled'
            return True
        return False
    
    async def get_positions(self) -> List[Position]:
        """Get mock positions"""
        return self.positions.copy()
    
    async def get_account_balance(self) -> Dict[str, float]:
        """Get mock account balance"""
        return self.account_balance.copy()
    
    def get_orders(self) -> Dict[str, Order]:
        """Get all orders (for testing)"""
        return self.orders.copy()
    
    def clear_orders(self):
        """Clear all orders (for testing)"""
        self.orders.clear()
        self.order_counter = 0

class MockRiskManagementService(IRiskManagementService):
    """Mock risk management service for testing"""
    
    def __init__(self):
        self.max_position_size = 100
        self.risk_limits_enabled = True
        self.position_size_multiplier = 1.0
        self.should_reject_orders = False
    
    def set_max_position_size(self, size: int):
        """Set maximum position size"""
        self.max_position_size = size
    
    def set_risk_limits_enabled(self, enabled: bool):
        """Enable/disable risk limits"""
        self.risk_limits_enabled = enabled
    
    def set_position_size_multiplier(self, multiplier: float):
        """Set position size multiplier"""
        self.position_size_multiplier = multiplier
    
    def set_order_rejection(self, should_reject: bool):
        """Control order rejection for testing"""
        self.should_reject_orders = should_reject
    
    def set_stop_loss_threshold(self, threshold: float):
        """Set stop loss threshold"""
        self.stop_loss_threshold = threshold
    
    def set_max_portfolio_risk(self, risk: float):
        """Set maximum portfolio risk"""
        self.max_portfolio_risk = risk
    
    async def calculate_position_size(self, symbol: str, signal: TradingSignal, account_balance: float) -> int:
        """Calculate mock position size"""
        base_size = min(self.max_position_size, int(account_balance * 0.1 / 100))  # Assume $100 per share
        return int(base_size * self.position_size_multiplier * signal.confidence)
    
    async def check_risk_limits(self, order: Order, current_positions: List[Position]) -> bool:
        """Check mock risk limits"""
        if self.should_reject_orders:
            return False
        
        if not self.risk_limits_enabled:
            return True
        
        # Simple risk check: don't exceed max position size
        return order.quantity <= self.max_position_size
    
    async def calculate_portfolio_risk(self, positions: List[Position]) -> Dict[str, float]:
        """Calculate mock portfolio risk"""
        if not positions:
            return {'total_risk': 0.0, 'position_count': 0}
        
        total_value = sum(abs(pos.quantity * pos.current_price) for pos in positions)
        total_pnl = sum(pos.unrealized_pnl for pos in positions)
        
        return {
            'total_risk': min(0.5, total_value / 100000),  # Mock risk calculation
            'position_count': len(positions),
            'total_value': total_value,
            'total_pnl': total_pnl,
            'risk_score': 0.1 + len(positions) * 0.05
        }

class MockPortfolioService(IPortfolioService):
    """Mock portfolio service for testing"""
    
    def __init__(self):
        self.portfolio_value = 10000.0
        self.allocation = {'AAPL': 0.3, 'GOOGL': 0.3, 'MSFT': 0.2, 'CASH': 0.2}
        self.rebalance_orders = []
    
    def set_portfolio_value(self, value: float):
        """Set mock portfolio value"""
        self.portfolio_value = value
    
    def set_allocation(self, allocation: Dict[str, float]):
        """Set mock allocation"""
        self.allocation = allocation
    
    async def get_portfolio_value(self) -> float:
        """Get mock portfolio value"""
        return self.portfolio_value
    
    async def get_portfolio_allocation(self) -> Dict[str, float]:
        """Get mock portfolio allocation"""
        return self.allocation.copy()
    
    async def rebalance_portfolio(self, target_allocation: Dict[str, float]) -> List[Order]:
        """Mock portfolio rebalancing"""
        orders = []
        
        for symbol, target_weight in target_allocation.items():
            if symbol == 'CASH':
                continue
            
            current_weight = self.allocation.get(symbol, 0.0)
            weight_diff = target_weight - current_weight
            
            if abs(weight_diff) > 0.01:  # 1% threshold
                order = Order(
                    id=f"REBALANCE_{symbol}_{datetime.now().timestamp()}",
                    symbol=symbol,
                    side='buy' if weight_diff > 0 else 'sell',
                    quantity=int(abs(weight_diff) * self.portfolio_value / 100),  # Assume $100 per share
                    price=100.0,  # Mock price
                    order_type='market',
                    status='pending',
                    timestamp=datetime.now()
                )
                orders.append(order)
        
        self.rebalance_orders = orders
        return orders
    
    def set_initial_capital(self, capital: float):
        """Set initial capital for testing"""
        self.portfolio_value = capital
    
    async def get_total_value(self) -> float:
        """Get total portfolio value"""
        return self.portfolio_value
    
    def reset(self):
        """Reset portfolio for testing"""
        self.portfolio_value = 10000.0
        self.allocation = {'AAPL': 0.3, 'GOOGL': 0.3, 'MSFT': 0.2, 'CASH': 0.2}
        self.rebalance_orders = []

class MockNotificationService(INotificationService):
    """Mock notification service for testing"""
    
    def __init__(self):
        self.alerts: List[Dict[str, Any]] = []
        self.trade_notifications: List[Order] = []
        self.notification_enabled = True
    
    def set_notification_enabled(self, enabled: bool):
        """Enable/disable notifications"""
        self.notification_enabled = enabled
    
    async def send_alert(self, message: str, level: str = "info") -> None:
        """Send mock alert"""
        if self.notification_enabled:
            self.alerts.append({
                'message': message,
                'level': level,
                'timestamp': datetime.now()
            })
    
    async def send_trade_notification(self, order: Order) -> None:
        """Send mock trade notification"""
        if self.notification_enabled:
            self.trade_notifications.append(order)
    
    def get_alerts(self) -> List[Dict[str, Any]]:
        """Get all alerts (for testing)"""
        return self.alerts.copy()
    
    def get_trade_notifications(self) -> List[Order]:
        """Get all trade notifications (for testing)"""
        return self.trade_notifications.copy()
    
    def clear_notifications(self):
        """Clear all notifications"""
        self.alerts.clear()
        self.trade_notifications.clear()

class MockDataStorageService(IDataStorageService):
    """Mock data storage service for testing"""
    
    def __init__(self):
        self.market_data: List[MarketData] = []
        self.trading_signals: List[TradingSignal] = []
        self.orders: List[Order] = []
        self.storage_enabled = True
    
    def set_storage_enabled(self, enabled: bool):
        """Enable/disable storage"""
        self.storage_enabled = enabled
    
    async def store_market_data(self, data: MarketData) -> None:
        """Store mock market data"""
        if self.storage_enabled:
            self.market_data.append(data)
    
    async def store_trading_signal(self, signal: TradingSignal) -> None:
        """Store mock trading signal"""
        if self.storage_enabled:
            self.trading_signals.append(signal)
    
    async def store_order(self, order: Order) -> None:
        """Store mock order"""
        if self.storage_enabled:
            self.orders.append(order)
    
    async def get_historical_signals(self, symbol: str, start_date: datetime, end_date: datetime) -> List[TradingSignal]:
        """Get mock historical signals"""
        return [
            signal for signal in self.trading_signals
            if signal.symbol == symbol and start_date <= signal.timestamp <= end_date
        ]
    
    def get_stored_market_data(self) -> List[MarketData]:
        """Get all stored market data (for testing)"""
        return self.market_data.copy()
    
    def get_stored_signals(self) -> List[TradingSignal]:
        """Get all stored signals (for testing)"""
        return self.trading_signals.copy()
    
    def get_stored_orders(self) -> List[Order]:
        """Get all stored orders (for testing)"""
        return self.orders.copy()
    
    def clear_storage(self):
        """Clear all stored data"""
        self.market_data.clear()
        self.trading_signals.clear()
        self.orders.clear()

@dataclass
class MockServiceCollection:
    """Collection of all mock services for easy testing setup"""
    
    market_data: 'MockMarketDataService' = field(default_factory=lambda: None)
    strategy: 'MockStrategyService' = field(default_factory=lambda: None)
    trading: MockTradingService = field(default_factory=MockTradingService)
    risk_management: MockRiskManagementService = field(default_factory=MockRiskManagementService)
    portfolio: MockPortfolioService = field(default_factory=MockPortfolioService)
    notifications: MockNotificationService = field(default_factory=MockNotificationService)
    data_storage: MockDataStorageService = field(default_factory=MockDataStorageService)
    logging: 'MockLoggingService' = field(default_factory=lambda: None)
    config: 'MockConfigurationService' = field(default_factory=lambda: None)
    
    def __post_init__(self):
        """Initialize services that require dependencies"""
        from services.logging_service import MockLoggingService
        from services.configuration_service import MockConfigurationService
        from services.market_data_service import MockMarketDataService
        from services.strategy_service import MockStrategyService
        
        if self.config is None:
            self.config = MockConfigurationService()
        
        if self.logging is None:
            self.logging = MockLoggingService(self.config)
        
        if self.market_data is None:
            self.market_data = MockMarketDataService(self.logging, self.config)
        
        if self.strategy is None:
            self.strategy = MockStrategyService(self.logging, self.config)
    
    def reset_all(self):
        """Reset all mock services to initial state"""
        self.trading = MockTradingService()
        self.risk_management = MockRiskManagementService()
        self.portfolio = MockPortfolioService()
        self.notifications = MockNotificationService()
        self.data_storage = MockDataStorageService()
        
        if hasattr(self.logging, 'clear_logs'):
            self.logging.clear_logs()
    
    def configure_for_testing(self, 
                            auto_trading: bool = False,
                            risk_limits: bool = True,
                            notifications: bool = True):
        """Configure services for specific test scenarios"""
        # Configure mock behavior
        self.risk_management.set_risk_limits_enabled(risk_limits)
        self.notifications.set_notification_enabled(notifications)
        
        # Set test configuration
        self.config.set_config('engine.auto_trading_enabled', auto_trading)
        self.config.set_config('engine.risk_check_enabled', risk_limits)
        self.config.set_config('engine.notification_enabled', notifications)