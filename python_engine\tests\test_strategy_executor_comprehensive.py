"""
Comprehensive Strategy Executor Tests

This module demonstrates the power of dependency injection for testing complex
trading strategies with realistic market scenarios.
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any
import numpy as np

from core.dependency_injection import DependencyContainer
from core.service_configuration import ServiceConfigurator
from core.trading_engine import TradingEngine
from core.interfaces import TradingSignal, MarketData, Order
from services.mock_services import MockServiceCollection

class TradingDataFactory:
    """Factory for creating realistic market data scenarios"""
    
    @staticmethod
    def create_market_data(symbol: str, base_price: float, days: int = 30) -> List[MarketData]:
        """Create realistic market data with price movements"""
        data = []
        current_price = base_price
        
        for i in range(days):
            # Simulate realistic price movements
            daily_return = np.random.normal(0.001, 0.02)  # 0.1% mean, 2% volatility
            current_price *= (1 + daily_return)
            
            # Create market data point
            timestamp = datetime.now() - timedelta(days=days-i)
            data.append(MarketData(
                symbol=symbol,
                timestamp=timestamp,
                open=current_price * 0.995,
                high=current_price * 1.01,
                low=current_price * 0.99,
                close=current_price,
                volume=int(np.random.normal(1000000, 200000))
            ))
        
        return data
    
    @staticmethod
    def create_trending_market(symbol: str, base_price: float, trend: float = 0.02) -> List[MarketData]:
        """Create trending market data (bullish or bearish)"""
        data = []
        current_price = base_price
        
        for i in range(30):
            # Add trend component
            daily_return = np.random.normal(trend/30, 0.015)  # Trending with volatility
            current_price *= (1 + daily_return)
            
            timestamp = datetime.now() - timedelta(days=30-i)
            data.append(MarketData(
                symbol=symbol,
                timestamp=timestamp,
                open=current_price * 0.998,
                high=current_price * 1.005,
                low=current_price * 0.995,
                close=current_price,
                volume=int(np.random.normal(1200000, 300000))
            ))
        
        return data
    
    @staticmethod
    def create_crash_scenario(symbol: str = "AAPL", base_price: float = 150.0) -> List[MarketData]:
        """Create market crash scenario for stress testing"""
        data = []
        current_price = base_price
        
        # Normal market for first 20 days
        for i in range(20):
            daily_return = np.random.normal(0.001, 0.015)
            current_price *= (1 + daily_return)
            
            timestamp = datetime.now() - timedelta(days=30-i)
            data.append(MarketData(
                symbol=symbol,
                timestamp=timestamp,
                open=current_price * 0.998,
                high=current_price * 1.005,
                low=current_price * 0.995,
                close=current_price,
                volume=int(np.random.normal(1000000, 200000))
            ))
        
        # Market crash for next 10 days
        for i in range(20, 30):
            # Severe negative returns during crash
            daily_return = np.random.normal(-0.05, 0.03)  # -5% mean with high volatility
            current_price *= (1 + daily_return)
            
            timestamp = datetime.now() - timedelta(days=30-i)
            data.append(MarketData(
                symbol=symbol,
                timestamp=timestamp,
                open=current_price * 1.02,  # Gap down opening
                high=current_price * 1.01,
                low=current_price * 0.95,   # Large intraday swings
                close=current_price,
                volume=int(np.random.normal(3000000, 500000))  # High volume during crash
            ))
        
        return data
    
    @staticmethod
    def create_sideways_market(symbol: str, base_price: float, range_pct: float = 0.05) -> List[MarketData]:
        """Create sideways/ranging market for mean reversion testing"""
        data = []
        center_price = base_price
        
        for i in range(30):
            # Oscillate around center price
            phase = (i / 30) * 4 * np.pi  # Multiple cycles
            price_offset = np.sin(phase) * range_pct
            noise = np.random.normal(0, 0.01)
            
            current_price = center_price * (1 + price_offset + noise)
            
            timestamp = datetime.now() - timedelta(days=30-i)
            data.append(MarketData(
                symbol=symbol,
                timestamp=timestamp,
                open=current_price * 0.999,
                high=current_price * 1.003,
                low=current_price * 0.997,
                close=current_price,
                volume=int(np.random.normal(800000, 150000))
            ))
        
        return data

class StrategyExecutor:
    """Strategy executor for backtesting and validation"""
    
    def __init__(self, trading_engine: TradingEngine):
        self.engine = trading_engine
        self.results = []
    
    async def execute(self, strategy_code: str, market_data: List[MarketData], 
                     initial_capital: float = 100000) -> Dict[str, Any]:
        """Execute strategy against market data"""
        
        # Initialize portfolio
        self.engine.portfolio.set_initial_capital(initial_capital)
        
        # Add strategy to engine
        await self.engine.add_strategy(
            "test_strategy",
            strategy_code,
            [market_data[0].symbol],
            {"lookback_period": 20, "threshold": 0.02}
        )
        
        # Process each market data point
        trades = []
        signals = []
        
        for data_point in market_data:
            # Update market data
            self.engine.market_data.set_mock_price(data_point.symbol, data_point.close)
            self.engine.market_data.add_historical_data(data_point.symbol, data_point)
            
            # Execute strategy
            try:
                signal = await self.engine.strategy.execute_strategy(
                    strategy_code,
                    {data_point.symbol: data_point.close},
                    {"lookback_period": 20, "threshold": 0.02}
                )
                
                if signal and signal.signal != 'hold':
                    signals.append(signal)
                    
                    # Process signal through engine
                    await self.engine._process_signal("test_strategy", signal)
                    
                    # Get resulting trades
                    new_orders = self.engine.trading.get_orders()
                    trades.extend(new_orders.values())
                    
            except Exception as e:
                print(f"Strategy execution error: {e}")
        
        # Calculate performance metrics
        portfolio_value = await self.engine.portfolio.get_total_value()
        total_return = (portfolio_value - initial_capital) / initial_capital
        
        # Calculate max drawdown
        max_drawdown = self._calculate_max_drawdown(trades, initial_capital)
        
        return {
            'success': True,
            'trades': trades,
            'signals': signals,
            'total_return': total_return,
            'final_portfolio_value': portfolio_value,
            'max_drawdown': max_drawdown,
            'num_trades': len(trades),
            'win_rate': self._calculate_win_rate(trades),
            'sharpe_ratio': self._calculate_sharpe_ratio(trades, total_return)
        }
    
    def _calculate_max_drawdown(self, trades: List[Order], initial_capital: float) -> float:
        """Calculate maximum drawdown"""
        if not trades:
            return 0.0
        
        # Simplified drawdown calculation
        running_pnl = 0
        peak = 0
        max_dd = 0
        
        for trade in trades:
            if hasattr(trade, 'pnl'):
                running_pnl += trade.pnl
                if running_pnl > peak:
                    peak = running_pnl
                drawdown = (peak - running_pnl) / initial_capital
                max_dd = max(max_dd, drawdown)
        
        return max_dd
    
    def _calculate_win_rate(self, trades: List[Order]) -> float:
        """Calculate win rate of trades"""
        if not trades:
            return 0.0
        
        winning_trades = sum(1 for trade in trades if hasattr(trade, 'pnl') and trade.pnl > 0)
        return winning_trades / len(trades)
    
    def _calculate_sharpe_ratio(self, trades: List[Order], total_return: float) -> float:
        """Calculate Sharpe ratio (simplified)"""
        if not trades or total_return == 0:
            return 0.0
        
        # Simplified Sharpe calculation
        returns = [trade.pnl / 100000 for trade in trades if hasattr(trade, 'pnl')]
        if not returns:
            return 0.0
        
        mean_return = np.mean(returns)
        std_return = np.std(returns)
        
        return mean_return / std_return if std_return > 0 else 0.0

class TestStrategyExecutorComprehensive:
    """Comprehensive strategy executor tests"""
    
    def setup_method(self):
        """Setup before each test"""
        # Configure services for testing with DI
        configurator = ServiceConfigurator()
        self.container = configurator.configure_for_testing()
        
        # Create trading engine with injected dependencies
        self.engine = self.container.resolve(TradingEngine)
        
        # Create strategy executor
        self.executor = StrategyExecutor(self.engine)
        
        # Configure engine for auto-trading
        self.engine.config.set_config('engine.auto_trading_enabled', True)
        self.engine._engine_config = self.engine._load_engine_config()
    
    def create_mean_reversion_strategy(self) -> str:
        """Create a mean reversion strategy"""
        return """
def trading_strategy(data, params):
    import numpy as np
    
    symbol = list(data.keys())[0]
    current_price = data[symbol]
    
    # Get historical data (simulated)
    lookback = params.get('lookback_period', 20)
    threshold = params.get('threshold', 0.02)
    
    # Simple mean reversion logic
    # In real implementation, would use actual historical data
    mean_price = current_price * (1 + np.random.normal(0, 0.01))
    
    if current_price < mean_price * (1 - threshold):
        return {'signal': 'buy', 'confidence': 0.7}
    elif current_price > mean_price * (1 + threshold):
        return {'signal': 'sell', 'confidence': 0.7}
    else:
        return {'signal': 'hold', 'confidence': 0.5}
"""
    
    def create_momentum_strategy(self) -> str:
        """Create a momentum strategy"""
        return """
def trading_strategy(data, params):
    import numpy as np
    
    symbol = list(data.keys())[0]
    current_price = data[symbol]
    
    # Simple momentum logic
    momentum_threshold = params.get('momentum_threshold', 0.03)
    
    # Simulate momentum calculation
    momentum = np.random.normal(0, 0.02)
    
    if momentum > momentum_threshold:
        return {'signal': 'buy', 'confidence': 0.8}
    elif momentum < -momentum_threshold:
        return {'signal': 'sell', 'confidence': 0.8}
    else:
        return {'signal': 'hold', 'confidence': 0.3}
"""
    
    @pytest.mark.asyncio
    async def test_strategy_execution_with_realistic_data(self):
        """Test strategy execution with realistic market data"""
        print("\n📊 TESTING: Strategy Execution with Realistic Data")
        print("-" * 60)
        
        # Arrange
        strategy = self.create_mean_reversion_strategy()
        market_data = TradingDataFactory.create_market_data("AAPL", 150.0, 30)
        
        # Act
        result = await self.executor.execute(strategy, market_data, 100000)
        
        # Assert
        assert result['success'] == True
        assert 'trades' in result
        assert 'total_return' in result
        assert result['max_drawdown'] >= 0  # Drawdown should be non-negative
        assert result['final_portfolio_value'] > 0
        
        print(f"✅ Strategy executed successfully")
        print(f"   📈 Total Return: {result['total_return']:.2%}")
        print(f"   📉 Max Drawdown: {result['max_drawdown']:.2%}")
        print(f"   💰 Final Portfolio: ${result['final_portfolio_value']:,.2f}")
        print(f"   📊 Number of Trades: {result['num_trades']}")
        print(f"   🎯 Win Rate: {result['win_rate']:.1%}")
    
    @pytest.mark.asyncio
    async def test_strategy_handles_market_crash_scenario(self):
        """Test strategy resilience during extreme market conditions"""
        print("\n💥 TESTING: Market Crash Scenario")
        print("-" * 60)
        
        # Arrange
        strategy = self.create_mean_reversion_strategy()
        crash_data = TradingDataFactory.create_crash_scenario("AAPL", 150.0)
        
        # Configure risk management for crash scenario
        self.engine.risk_management.set_max_position_size(100)  # Smaller positions
        self.engine.risk_management.set_stop_loss_threshold(0.05)  # 5% stop loss
        
        # Act
        result = await self.executor.execute(strategy, crash_data, 100000)
        
        # Assert - Strategy should survive crash
        assert result['success'] == True
        assert result['max_drawdown'] < 0.5  # Should not lose more than 50%
        assert result['final_portfolio_value'] > 50000  # Should retain at least 50%
        
        print(f"✅ Strategy survived market crash")
        print(f"   💥 Crash Impact - Total Return: {result['total_return']:.2%}")
        print(f"   🛡️ Max Drawdown: {result['max_drawdown']:.2%}")
        print(f"   💰 Portfolio Survived: ${result['final_portfolio_value']:,.2f}")
        print(f"   📊 Trades During Crisis: {result['num_trades']}")
        
        # Risk management should have limited losses
        assert result['max_drawdown'] < 0.3, "Risk management should limit drawdown to <30%"
    
    @pytest.mark.asyncio
    async def test_momentum_strategy_in_trending_market(self):
        """Test momentum strategy in trending market"""
        print("\n📈 TESTING: Momentum Strategy in Trending Market")
        print("-" * 60)
        
        # Arrange
        strategy = self.create_momentum_strategy()
        trending_data = TradingDataFactory.create_trending_market("TSLA", 200.0, 0.03)  # 3% uptrend
        
        # Act
        result = await self.executor.execute(strategy, trending_data, 100000)
        
        # Assert
        assert result['success'] == True
        assert result['total_return'] > -0.1  # Should not lose more than 10%
        
        print(f"✅ Momentum strategy in trending market")
        print(f"   📈 Trend Performance: {result['total_return']:.2%}")
        print(f"   📊 Signals Generated: {len(result['signals'])}")
        print(f"   💼 Trades Executed: {result['num_trades']}")
        print(f"   🎯 Win Rate: {result['win_rate']:.1%}")
    
    @pytest.mark.asyncio
    async def test_mean_reversion_in_sideways_market(self):
        """Test mean reversion strategy in sideways market"""
        print("\n↔️ TESTING: Mean Reversion in Sideways Market")
        print("-" * 60)
        
        # Arrange
        strategy = self.create_mean_reversion_strategy()
        sideways_data = TradingDataFactory.create_sideways_market("MSFT", 300.0, 0.04)
        
        # Act
        result = await self.executor.execute(strategy, sideways_data, 100000)
        
        # Assert
        assert result['success'] == True
        # Mean reversion should work well in sideways markets
        assert result['total_return'] > -0.05  # Should not lose more than 5%
        
        print(f"✅ Mean reversion in sideways market")
        print(f"   ↔️ Sideways Performance: {result['total_return']:.2%}")
        print(f"   🔄 Mean Reversion Trades: {result['num_trades']}")
        print(f"   📊 Signal Quality: {len(result['signals'])} signals")
        print(f"   🎯 Win Rate: {result['win_rate']:.1%}")
    
    @pytest.mark.asyncio
    async def test_strategy_risk_management_integration(self):
        """Test strategy integration with risk management"""
        print("\n🛡️ TESTING: Risk Management Integration")
        print("-" * 60)
        
        # Arrange
        strategy = self.create_momentum_strategy()
        volatile_data = TradingDataFactory.create_market_data("NVDA", 400.0, 20)
        
        # Configure strict risk management
        self.engine.risk_management.set_max_position_size(50)
        self.engine.risk_management.set_position_size_multiplier(0.5)
        self.engine.risk_management.set_max_portfolio_risk(0.02)  # 2% max risk
        
        # Act
        result = await self.executor.execute(strategy, volatile_data, 100000)
        
        # Assert
        assert result['success'] == True
        assert result['max_drawdown'] < 0.1  # Risk management should limit drawdown
        
        print(f"✅ Risk management integration working")
        print(f"   🛡️ Risk-Adjusted Return: {result['total_return']:.2%}")
        print(f"   📉 Controlled Drawdown: {result['max_drawdown']:.2%}")
        print(f"   ⚖️ Risk-Managed Trades: {result['num_trades']}")
        
        # Verify risk management limited position sizes
        trades = result['trades']
        if trades:
            max_position = max(trade.quantity for trade in trades if hasattr(trade, 'quantity'))
            assert max_position <= 50, "Risk management should limit position sizes"
    
    @pytest.mark.asyncio
    async def test_multi_strategy_comparison(self):
        """Compare multiple strategies on same data"""
        print("\n🔬 TESTING: Multi-Strategy Comparison")
        print("-" * 60)
        
        # Arrange
        market_data = TradingDataFactory.create_market_data("AMZN", 120.0, 25)
        strategies = {
            "Mean Reversion": self.create_mean_reversion_strategy(),
            "Momentum": self.create_momentum_strategy()
        }
        
        results = {}
        
        # Act - Test each strategy
        for name, strategy in strategies.items():
            # Reset engine state
            self.engine.trading.clear_orders()
            self.engine.portfolio.reset()
            
            result = await self.executor.execute(strategy, market_data, 100000)
            results[name] = result
        
        # Assert and Compare
        print("📊 Strategy Comparison Results:")
        for name, result in results.items():
            print(f"   {name}:")
            print(f"     📈 Return: {result['total_return']:.2%}")
            print(f"     📉 Max DD: {result['max_drawdown']:.2%}")
            print(f"     💼 Trades: {result['num_trades']}")
            print(f"     🎯 Win Rate: {result['win_rate']:.1%}")
        
        # All strategies should execute successfully
        for result in results.values():
            assert result['success'] == True
        
        print("✅ Multi-strategy comparison completed")
    
    @pytest.mark.asyncio
    async def test_strategy_performance_metrics(self):
        """Test comprehensive performance metrics calculation"""
        print("\n📊 TESTING: Performance Metrics Calculation")
        print("-" * 60)
        
        # Arrange
        strategy = self.create_mean_reversion_strategy()
        market_data = TradingDataFactory.create_market_data("GOOGL", 2500.0, 30)
        
        # Act
        result = await self.executor.execute(strategy, market_data, 100000)
        
        # Assert - Check all metrics are calculated
        required_metrics = [
            'success', 'trades', 'signals', 'total_return',
            'final_portfolio_value', 'max_drawdown', 'num_trades',
            'win_rate', 'sharpe_ratio'
        ]
        
        for metric in required_metrics:
            assert metric in result, f"Missing metric: {metric}"
        
        # Validate metric ranges
        assert -1.0 <= result['total_return'] <= 10.0  # Reasonable return range
        assert 0.0 <= result['max_drawdown'] <= 1.0    # Drawdown percentage
        assert 0.0 <= result['win_rate'] <= 1.0        # Win rate percentage
        assert result['num_trades'] >= 0               # Non-negative trades
        
        print("✅ All performance metrics calculated correctly")
        print(f"   📊 Metrics Summary:")
        print(f"     💰 Total Return: {result['total_return']:.2%}")
        print(f"     📉 Max Drawdown: {result['max_drawdown']:.2%}")
        print(f"     🎯 Win Rate: {result['win_rate']:.1%}")
        print(f"     📈 Sharpe Ratio: {result['sharpe_ratio']:.2f}")
        print(f"     💼 Total Trades: {result['num_trades']}")

class TestStrategyExecutorEdgeCases:
    """Test edge cases and error scenarios"""
    
    def setup_method(self):
        """Setup before each test"""
        configurator = ServiceConfigurator()
        self.container = configurator.configure_for_testing()
        self.engine = self.container.resolve(TradingEngine)
        self.executor = StrategyExecutor(self.engine)
        
        self.engine.config.set_config('engine.auto_trading_enabled', True)
        self.engine._engine_config = self.engine._load_engine_config()
    
    @pytest.mark.asyncio
    async def test_strategy_with_no_signals(self):
        """Test strategy that generates no trading signals"""
        print("\n🔇 TESTING: Strategy with No Signals")
        print("-" * 60)
        
        # Strategy that always holds
        hold_strategy = """
def trading_strategy(data, params):
    return {'signal': 'hold', 'confidence': 0.5}
"""
        
        market_data = TradingDataFactory.create_market_data("AAPL", 150.0, 10)
        result = await self.executor.execute(hold_strategy, market_data, 100000)
        
        assert result['success'] == True
        assert result['num_trades'] == 0
        assert result['total_return'] == 0.0  # No trades, no return
        
        print("✅ Hold-only strategy handled correctly")
        print(f"   📊 Signals: {len(result['signals'])}")
        print(f"   💼 Trades: {result['num_trades']}")
    
    @pytest.mark.asyncio
    async def test_strategy_with_extreme_volatility(self):
        """Test strategy with extremely volatile market data"""
        print("\n🌪️ TESTING: Extreme Volatility Scenario")
        print("-" * 60)
        
        # Create extremely volatile data
        volatile_data = []
        base_price = 100.0
        
        for i in range(20):
            # Extreme daily moves (-20% to +20%)
            daily_return = np.random.uniform(-0.2, 0.2)
            base_price *= (1 + daily_return)
            
            timestamp = datetime.now() - timedelta(days=20-i)
            volatile_data.append(MarketData(
                symbol="VOLATILE",
                timestamp=timestamp,
                open=base_price * 0.95,
                high=base_price * 1.1,
                low=base_price * 0.9,
                close=base_price,
                volume=5000000
            ))
        
        strategy = """
def trading_strategy(data, params):
    import numpy as np
    symbol = list(data.keys())[0]
    current_price = data[symbol]
    
    # Conservative strategy for volatile markets
    if np.random.random() > 0.8:  # Only trade 20% of the time
        return {'signal': 'buy', 'confidence': 0.3}
    else:
        return {'signal': 'hold', 'confidence': 0.7}
"""
        
        result = await self.executor.execute(strategy, volatile_data, 100000)
        
        assert result['success'] == True
        # In extreme volatility, risk management should protect capital
        assert result['final_portfolio_value'] > 50000  # Should not lose more than 50%
        
        print("✅ Extreme volatility handled")
        print(f"   🌪️ Volatility Impact: {result['total_return']:.2%}")
        print(f"   🛡️ Capital Protection: ${result['final_portfolio_value']:,.2f}")

if __name__ == "__main__":
    # Run comprehensive tests
    print("🚀 COMPREHENSIVE STRATEGY EXECUTOR TESTS")
    print("=" * 80)
    
    # This would normally be run with pytest, but showing structure
    print("Tests implemented:")
    print("✅ Realistic market data testing")
    print("✅ Market crash scenario testing")
    print("✅ Momentum strategy in trending markets")
    print("✅ Mean reversion in sideways markets")
    print("✅ Risk management integration")
    print("✅ Multi-strategy comparison")
    print("✅ Performance metrics validation")
    print("✅ Edge case handling")
    print("✅ Extreme volatility scenarios")
    
    print("\n🎯 Key Benefits Demonstrated:")
    print("✅ Easy testing with dependency injection")
    print("✅ Realistic market scenario simulation")
    print("✅ Comprehensive performance analysis")
    print("✅ Risk management validation")
    print("✅ Strategy comparison capabilities")
    print("✅ Edge case and stress testing")