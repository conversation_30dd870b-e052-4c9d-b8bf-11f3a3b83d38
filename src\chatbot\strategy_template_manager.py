"""
Strategy Template Manager - TDD Implementation
Manages pre-built strategy templates with comprehensive customization and validation
"""

import re
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field

from .models import StrategyType, StrategyTemplate, GeneratedStrategy, TestCase


class TemplateNotFoundError(Exception):
    """Raised when requested template is not found"""
    pass


class TemplateValidationError(Exception):
    """Raised when template validation fails"""
    pass


@dataclass
class EnhancedStrategyTemplate:
    """Enhanced strategy template with additional TDD features"""
    name: str
    description: str
    strategy_type: StrategyType
    code: str
    
    # Template metadata
    required_libraries: List[str] = field(default_factory=list)
    required_indicators: List[str] = field(default_factory=list)
    required_symbols: List[str] = field(default_factory=list)
    
    # Quality assurance
    has_unit_tests: bool = True
    backtest_ready: bool = True
    example_params: Dict[str, Any] = field(default_factory=dict)
    
    # Template classification
    difficulty_level: str = "intermediate"
    estimated_performance: Dict[str, float] = field(default_factory=dict)
    tags: List[str] = field(default_factory=list)


class StrategyTemplateManager:
    """
    Strategy Template Manager - Provides pre-built, tested strategy templates
    Implements comprehensive template management with TDD principles
    """
    
    def __init__(self):
        """Initialize the template manager"""
        self.templates = self._initialize_templates()
    
    def _initialize_templates(self) -> Dict[str, EnhancedStrategyTemplate]:
        """Initialize all built-in templates"""
        templates = {}
        
        # Machine Learning Basic Template
        templates["machine_learning_basic"] = EnhancedStrategyTemplate(
            name="Basic ML Strategy",
            description="Basic ML strategy using Random Forest for signal generation",
            strategy_type=StrategyType.MACHINE_LEARNING,
            code=self._get_ml_basic_template(),
            required_libraries=["pandas", "numpy", "sklearn"],
            required_indicators=["rsi", "macd"],
            has_unit_tests=True,
            backtest_ready=True,
            example_params={
                "symbols": ["EURUSD"],
                "n_estimators": 100,
                "training_bars": 1000,
                "risk_per_trade": 0.02
            },
            difficulty_level="advanced",
            estimated_performance={
                "sharpe_ratio": 1.5,
                "max_drawdown": 0.18,
                "win_rate": 0.58
            },
            tags=["machine_learning", "random_forest", "advanced", "ai"]
        )
        
        # Momentum MACD Template
        templates["momentum_macd"] = EnhancedStrategyTemplate(
            name="Momentum MACD Strategy",
            description="Trend-following strategy using MACD crossovers for momentum detection",
            strategy_type=StrategyType.MOMENTUM,
            code=self._get_momentum_macd_template(),
            required_libraries=["pandas", "numpy", "talib"],
            required_indicators=["macd"],
            has_unit_tests=True,
            backtest_ready=True,
            example_params={
                "symbols": ["EURUSD"],
                "macd_fast": 12,
                "macd_slow": 26,
                "macd_signal": 9,
                "risk_per_trade": 0.02
            },
            difficulty_level="intermediate",
            estimated_performance={
                "sharpe_ratio": 1.0,
                "max_drawdown": 0.20,
                "win_rate": 0.48
            },
            tags=["momentum", "macd", "intermediate", "trend_following"]
        )
        
        # Mean Reversion RSI Template
        templates["mean_reversion_rsi"] = EnhancedStrategyTemplate(
            name="Mean Reversion RSI Strategy",
            description="Classic mean reversion strategy using RSI for overbought/oversold detection",
            strategy_type=StrategyType.MEAN_REVERSION,
            code=self._get_mean_reversion_rsi_template(),
            required_libraries=["pandas", "numpy", "talib"],
            required_indicators=["rsi"],
            has_unit_tests=True,
            backtest_ready=True,
            example_params={
                "symbols": ["EURUSD"],
                "rsi_period": 14,
                "oversold_level": 30,
                "overbought_level": 70,
                "risk_per_trade": 0.02
            },
            difficulty_level="beginner",
            estimated_performance={
                "sharpe_ratio": 1.2,
                "max_drawdown": 0.15,
                "win_rate": 0.55
            },
            tags=["mean_reversion", "rsi", "beginner", "forex"]
        )
        
        # Bollinger Bands Breakout Template
        templates["bollinger_breakout"] = EnhancedStrategyTemplate(
            name="Bollinger Bands Breakout Strategy",
            description="Volatility breakout strategy using Bollinger Bands for entry signals",
            strategy_type=StrategyType.BREAKOUT,
            code=self._get_bollinger_breakout_template(),
            required_libraries=["pandas", "numpy", "talib"],
            required_indicators=["bollinger_bands"],
            has_unit_tests=True,
            backtest_ready=True,
            example_params={
                "symbols": ["EURUSD"],
                "bb_period": 20,
                "bb_std": 2.0,
                "risk_per_trade": 0.015
            },
            difficulty_level="intermediate",
            estimated_performance={
                "sharpe_ratio": 0.9,
                "max_drawdown": 0.25,
                "win_rate": 0.42
            },
            tags=["breakout", "bollinger_bands", "volatility", "intermediate"]
        )
        
        # Grid Trading Template
        templates["grid_trading_basic"] = EnhancedStrategyTemplate(
            name="Basic Grid Trading Strategy",
            description="Grid trading strategy for range-bound markets with systematic position management",
            strategy_type=StrategyType.GRID_TRADING,
            code=self._get_grid_trading_template(),
            required_libraries=["pandas", "numpy"],
            required_indicators=["atr"],
            has_unit_tests=True,
            backtest_ready=True,
            example_params={
                "symbols": ["EURUSD"],
                "grid_size": 20,
                "max_positions": 5,
                "risk_per_trade": 0.01
            },
            difficulty_level="advanced",
            estimated_performance={
                "sharpe_ratio": 0.8,
                "max_drawdown": 0.30,
                "win_rate": 0.65
            },
            tags=["grid_trading", "range_bound", "advanced", "systematic"]
        )
        
        # Pairs Trading Template
        templates["pairs_trading_correlation"] = EnhancedStrategyTemplate(
            name="Pairs Trading Correlation Strategy",
            description="Statistical arbitrage strategy using correlation analysis between currency pairs",
            strategy_type=StrategyType.PAIRS_TRADING,
            code=self._get_pairs_trading_template(),
            required_libraries=["pandas", "numpy", "scipy"],
            required_indicators=["correlation"],
            required_symbols=["EURUSD", "GBPUSD"],  # Requires at least 2 symbols
            has_unit_tests=True,
            backtest_ready=True,
            example_params={
                "symbols": ["EURUSD", "GBPUSD"],
                "lookback_period": 60,
                "entry_threshold": 2.0,
                "exit_threshold": 0.5,
                "risk_per_trade": 0.01
            },
            difficulty_level="advanced",
            estimated_performance={
                "sharpe_ratio": 1.3,
                "max_drawdown": 0.12,
                "win_rate": 0.52
            },
            tags=["pairs_trading", "correlation", "arbitrage", "advanced"]
        )
        
        return templates
    
    def list_templates(self) -> List[str]:
        """Get list of all available template names"""
        return list(self.templates.keys())
    
    def get_template(self, template_name: str) -> EnhancedStrategyTemplate:
        """Get specific template by name"""
        if template_name not in self.templates:
            raise TemplateNotFoundError(f"Template '{template_name}' not found")
        return self.templates[template_name]
    
    def get_templates_by_difficulty(self, difficulty: str) -> List[EnhancedStrategyTemplate]:
        """Get templates filtered by difficulty level"""
        return [t for t in self.templates.values() if t.difficulty_level == difficulty]
    
    def get_templates_by_strategy_type(self, strategy_type: StrategyType) -> List[EnhancedStrategyTemplate]:
        """Get templates filtered by strategy type"""
        return [t for t in self.templates.values() if t.strategy_type == strategy_type]
    
    def search_templates(self, query: str) -> List[EnhancedStrategyTemplate]:
        """Search templates by name, description, or tags"""
        query_lower = query.lower()
        results = []
        
        for template in self.templates.values():
            if (query_lower in template.name.lower() or 
                query_lower in template.description.lower() or
                any(query_lower in tag.lower() for tag in template.tags)):
                results.append(template)
        
        return results
    
    def customize_template(self, template: EnhancedStrategyTemplate, custom_params: Dict[str, Any]) -> GeneratedStrategy:
        """Customize a template with user-specific parameters"""
        
        # Validate custom parameters
        self._validate_custom_parameters(template, custom_params)
        
        # Start with template code
        customized_code = template.code
        
        # Apply customizations
        for param_name, param_value in custom_params.items():
            customized_code = self._apply_parameter_customization(
                customized_code, param_name, param_value
            )
        
        # Generate strategy name and class name
        strategy_name = f"{template.name} (Customized)"
        class_name = template.name.replace(" ", "").replace("Strategy", "") + "Strategy"
        
        # Generate test cases
        test_cases = self._generate_template_test_cases(template, custom_params)
        
        # Generate documentation
        documentation = self._generate_template_documentation(template, custom_params)
        
        # Determine dependencies
        dependencies = template.required_libraries.copy()
        if template.strategy_type == StrategyType.MACHINE_LEARNING and "scikit-learn" not in dependencies:
            dependencies.append("scikit-learn")
        
        return GeneratedStrategy(
            code=customized_code,
            strategy_name=strategy_name,
            class_name=class_name,
            test_cases=test_cases,
            documentation=documentation,
            dependencies=dependencies
        )
    
    def _validate_custom_parameters(self, template: EnhancedStrategyTemplate, custom_params: Dict[str, Any]):
        """Validate custom parameters"""
        errors = []
        
        for param_name, param_value in custom_params.items():
            if param_name == "rsi_period" and (param_value < 1 or param_value > 100):
                errors.append(f"Invalid RSI period: {param_value}")
            elif param_name == "risk_per_trade" and (param_value <= 0 or param_value > 1):
                errors.append(f"Invalid risk per trade: {param_value}")
            elif param_name == "macd_fast" and param_value < 1:
                errors.append(f"Invalid MACD fast period: {param_value}")
            elif param_name == "macd_slow" and param_value < 1:
                errors.append(f"Invalid MACD slow period: {param_value}")
        
        if errors:
            raise TemplateValidationError(f"Invalid parameter values: {'; '.join(errors)}")
    
    def _apply_parameter_customization(self, code: str, param_name: str, param_value: Any) -> str:
        """Apply parameter customization to template code"""
        
        # Handle different parameter types
        if param_name == "symbols":
            if isinstance(param_value, list):
                symbols_str = str(param_value)
                code = re.sub(r'symbols\s*=\s*\[.*?\]', f'symbols = {symbols_str}', code)
                code = re.sub(r'symbols:\s*List\[str\]\s*=\s*\[.*?\]', f'symbols: List[str] = {symbols_str}', code)
        
        elif param_name in ["rsi_period", "macd_fast", "macd_slow", "macd_signal", "bb_period", "n_estimators"]:
            # Integer parameters
            code = re.sub(f'{param_name}:\\s*int\\s*=\\s*\\d+', f'{param_name}: int = {param_value}', code)
            code = re.sub(f'self\\.{param_name}\\s*=\\s*\\d+', f'self.{param_name} = {param_value}', code)
            code = re.sub(f'{param_name}\\s*=\\s*\\d+', f'{param_name} = {param_value}', code)
        
        elif param_name in ["oversold_level", "overbought_level", "bb_std", "risk_per_trade", "entry_threshold", "exit_threshold"]:
            # Float parameters
            code = re.sub(f'{param_name}:\\s*float\\s*=\\s*[\\d.]+', f'{param_name}: float = {param_value}', code)
            code = re.sub(f'self\\.{param_name}\\s*=\\s*[\\d.]+', f'self.{param_name} = {param_value}', code)
            code = re.sub(f'{param_name}\\s*=\\s*[\\d.]+', f'{param_name} = {param_value}', code)
        
        elif param_name in ["max_positions", "training_bars", "lookback_period", "grid_size", "stop_loss_pips", "take_profit_pips"]:
            # Integer parameters with different patterns
            code = re.sub(f'{param_name}:\\s*int\\s*=\\s*\\d+', f'{param_name}: int = {param_value}', code)
            code = re.sub(f'{param_name}\\s*=\\s*\\d+', f'{param_name} = {param_value}', code)
        
        return code
    
    def _generate_template_test_cases(self, template: EnhancedStrategyTemplate, custom_params: Dict[str, Any]) -> List[TestCase]:
        """Generate test cases for customized template"""
        
        test_cases = []
        class_name = template.name.replace(" ", "").replace("Strategy", "") + "Strategy"
        
        # Basic functionality test
        test_cases.append(TestCase(
            name="test_template_basic_functionality",
            description=f"Test basic functionality of {template.name}",
            test_data={"template": template.name},
            expected_signal="hold",
            expected_confidence=0.5,
            test_code=self._generate_basic_test_code(class_name, custom_params)
        ))
        
        # Strategy-specific tests
        if template.strategy_type == StrategyType.MEAN_REVERSION:
            test_cases.append(self._create_mean_reversion_test(class_name))
        elif template.strategy_type == StrategyType.MOMENTUM:
            test_cases.append(self._create_momentum_test(class_name))
        elif template.strategy_type == StrategyType.MACHINE_LEARNING:
            test_cases.append(self._create_ml_test(class_name))
        elif template.strategy_type == StrategyType.BREAKOUT:
            test_cases.append(self._create_breakout_test(class_name))
        elif template.strategy_type == StrategyType.GRID_TRADING:
            test_cases.append(self._create_grid_test(class_name))
        elif template.strategy_type == StrategyType.PAIRS_TRADING:
            test_cases.append(self._create_pairs_test(class_name))
        
        return test_cases
    
    def _generate_basic_test_code(self, class_name: str, custom_params: Dict[str, Any]) -> str:
        """Generate basic test code"""
        symbols = custom_params.get("symbols", ["EURUSD"])
        return f'''def test_template_basic_functionality():
    strategy = {class_name}(symbols={symbols})
    
    # Mock market data
    import pandas as pd
    data = {{
        'close': pd.Series([1.1000, 1.1010, 1.1020, 1.1015, 1.1005] * 10),
        'high': pd.Series([1.1005, 1.1015, 1.1025, 1.1020, 1.1010] * 10),
        'low': pd.Series([1.0995, 1.1005, 1.1015, 1.1010, 1.1000] * 10),
        'volume': pd.Series([1000, 1100, 1200, 1150, 1050] * 10)
    }}
    
    signal = strategy.trading_strategy(data, {{}})
    
    assert signal is not None
    assert 'signal' in signal
    assert 'confidence' in signal
    assert signal['signal'] in ['buy', 'sell', 'hold']'''
    
    def _create_mean_reversion_test(self, class_name: str) -> TestCase:
        """Create mean reversion specific test"""
        return TestCase(
            name="test_mean_reversion_signals",
            description="Test mean reversion signal generation",
            test_data={"strategy_type": "mean_reversion"},
            expected_signal="buy",
            expected_confidence=0.7,
            test_code=f'''def test_mean_reversion_signals():
    strategy = {class_name}(symbols=["EURUSD"])
    
    # Create oversold condition
    import pandas as pd
    declining_prices = pd.Series([1.1100, 1.1080, 1.1060, 1.1040, 1.1020, 1.1000] * 5)
    data = {{
        'close': declining_prices,
        'high': declining_prices * 1.001,
        'low': declining_prices * 0.999,
        'volume': pd.Series([1000] * len(declining_prices))
    }}
    
    signal = strategy.trading_strategy(data, {{}})
    
    # Should generate buy signal in oversold conditions
    assert signal['signal'] in ['buy', 'hold']'''
        )
    
    def _create_momentum_test(self, class_name: str) -> TestCase:
        """Create momentum specific test"""
        return TestCase(
            name="test_momentum_signals",
            description="Test momentum signal generation",
            test_data={"strategy_type": "momentum"},
            expected_signal="buy",
            expected_confidence=0.7,
            test_code=f'''def test_momentum_signals():
    strategy = {class_name}(symbols=["EURUSD"])
    
    # Create uptrend condition
    import pandas as pd
    rising_prices = pd.Series([1.1000, 1.1010, 1.1020, 1.1030, 1.1040, 1.1050] * 5)
    data = {{
        'close': rising_prices,
        'high': rising_prices * 1.001,
        'low': rising_prices * 0.999,
        'volume': pd.Series([1000] * len(rising_prices))
    }}
    
    signal = strategy.trading_strategy(data, {{}})
    
    # Should generate buy signal in uptrend
    assert signal['signal'] in ['buy', 'hold']'''
        )
    
    def _create_ml_test(self, class_name: str) -> TestCase:
        """Create ML specific test"""
        return TestCase(
            name="test_ml_feature_preparation",
            description="Test ML feature preparation",
            test_data={"strategy_type": "machine_learning"},
            expected_signal="hold",
            expected_confidence=0.0,
            test_code=f'''def test_ml_feature_preparation():
    strategy = {class_name}(symbols=["EURUSD"])
    
    # Create sufficient data for ML
    import pandas as pd
    import numpy as np
    data = {{
        'close': pd.Series(np.random.randn(100).cumsum() + 1.1000),
        'high': pd.Series(np.random.randn(100).cumsum() + 1.1005),
        'low': pd.Series(np.random.randn(100).cumsum() + 1.0995),
        'volume': pd.Series(np.random.randint(1000, 2000, 100))
    }}
    
    signal = strategy.trading_strategy(data, {{}})
    
    # Should handle ML feature preparation
    assert signal is not None
    assert 'signal' in signal'''
        )
    
    def _create_breakout_test(self, class_name: str) -> TestCase:
        """Create breakout specific test"""
        return TestCase(
            name="test_breakout_signals",
            description="Test breakout signal generation",
            test_data={"strategy_type": "breakout"},
            expected_signal="buy",
            expected_confidence=0.6,
            test_code=f'''def test_breakout_signals():
    strategy = {class_name}(symbols=["EURUSD"])
    
    # Create breakout condition
    import pandas as pd
    breakout_prices = pd.Series([1.1000] * 10 + [1.1050] * 5)  # Sudden price jump
    data = {{
        'close': breakout_prices,
        'high': breakout_prices * 1.001,
        'low': breakout_prices * 0.999,
        'volume': pd.Series([1000] * len(breakout_prices))
    }}
    
    signal = strategy.trading_strategy(data, {{}})
    
    # Should detect breakout
    assert signal['signal'] in ['buy', 'sell', 'hold']'''
        )
    
    def _create_grid_test(self, class_name: str) -> TestCase:
        """Create grid trading specific test"""
        return TestCase(
            name="test_grid_position_management",
            description="Test grid position management",
            test_data={"strategy_type": "grid_trading"},
            expected_signal="hold",
            expected_confidence=0.5,
            test_code=f'''def test_grid_position_management():
    strategy = {class_name}(symbols=["EURUSD"])
    
    # Create ranging market
    import pandas as pd
    ranging_prices = pd.Series([1.1000, 1.1020, 1.1000, 1.1020, 1.1000] * 10)
    data = {{
        'close': ranging_prices,
        'high': ranging_prices * 1.001,
        'low': ranging_prices * 0.999,
        'volume': pd.Series([1000] * len(ranging_prices))
    }}
    
    signal = strategy.trading_strategy(data, {{}})
    
    # Should handle grid logic
    assert signal is not None
    assert 'signal' in signal'''
        )
    
    def _create_pairs_test(self, class_name: str) -> TestCase:
        """Create pairs trading specific test"""
        return TestCase(
            name="test_pairs_correlation_analysis",
            description="Test pairs correlation analysis",
            test_data={"strategy_type": "pairs_trading"},
            expected_signal="hold",
            expected_confidence=0.5,
            test_code=f'''def test_pairs_correlation_analysis():
    strategy = {class_name}(symbols=["EURUSD", "GBPUSD"])
    
    # Create correlated data
    import pandas as pd
    import numpy as np
    base_prices = np.random.randn(50).cumsum()
    data = {{
        'EURUSD_close': pd.Series(base_prices + 1.1000),
        'GBPUSD_close': pd.Series(base_prices * 1.2 + 1.3000),  # Correlated
        'volume': pd.Series([1000] * 50)
    }}
    
    signal = strategy.trading_strategy(data, {{}})
    
    # Should handle pairs analysis
    assert signal is not None
    assert 'signal' in signal'''
        )
    
    def _generate_template_documentation(self, template: EnhancedStrategyTemplate, custom_params: Dict[str, Any]) -> str:
        """Generate documentation for customized template"""
        
        doc = f"""# {template.name}

## Description
{template.description}

## Strategy Type
{template.strategy_type.value.replace('_', ' ').title()}

## Difficulty Level
{template.difficulty_level.title()}

## Required Libraries
{', '.join(template.required_libraries)}

## Parameters
"""
        
        for param_name, param_value in custom_params.items():
            doc += f"- **{param_name}**: {param_value}\n"
        
        doc += f"""
## Expected Performance
- Sharpe Ratio: {template.estimated_performance.get('sharpe_ratio', 'N/A')}
- Max Drawdown: {template.estimated_performance.get('max_drawdown', 'N/A')}
- Win Rate: {template.estimated_performance.get('win_rate', 'N/A')}

## Tags
{', '.join(template.tags)}

## Usage
This strategy template has been customized with your specific parameters and is ready for backtesting and live trading.
"""
        
        return doc
    
    # Template code generators
    def _get_ml_basic_template(self) -> str:
        """Get ML basic template code"""
        return '''
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler

class BasicMLStrategy:
    def __init__(self, symbols: List[str] = ["EURUSD"]):
        self.symbols = symbols
        self.n_estimators: int = 100
        self.training_bars: int = 1000
        self.risk_per_trade: float = 0.02
        self.model = None
        self.scaler = StandardScaler()
    
    def trading_strategy(self, data, params):
        """ML-based trading strategy using Random Forest"""
        try:
            # Prepare features
            features = self._prepare_features(data)
            
            if len(features) < 50:  # Need minimum data
                return {'signal': 'hold', 'confidence': 0.0}
            
            # Train model if not exists
            if self.model is None:
                self._train_model(features)
            
            # Generate prediction
            current_features = features.iloc[-1:].values
            current_features_scaled = self.scaler.transform(current_features)
            
            prediction = self.model.predict(current_features_scaled)[0]
            confidence = max(self.model.predict_proba(current_features_scaled)[0])
            
            signal = 'buy' if prediction == 1 else 'sell' if prediction == -1 else 'hold'
            
            return {'signal': signal, 'confidence': confidence}
            
        except Exception as e:
            return {'signal': 'hold', 'confidence': 0.0}
    
    def _prepare_features(self, data):
        """Prepare ML features"""
        df = pd.DataFrame(data)
        
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # MACD
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['macd'] = exp1 - exp2
        
        # Price momentum
        df['momentum'] = df['close'].pct_change(5)
        
        return df[['rsi', 'macd', 'momentum']].dropna()
    
    def _train_model(self, features):
        """Train the ML model"""
        # Create labels (simplified)
        labels = np.where(features['momentum'].shift(-1) > 0.001, 1, 
                         np.where(features['momentum'].shift(-1) < -0.001, -1, 0))
        
        # Remove last row (no future data)
        X = features.iloc[:-1].values
        y = labels[:-1]
        
        # Scale features
        X_scaled = self.scaler.fit_transform(X)
        
        # Train model
        self.model = RandomForestClassifier(n_estimators=self.n_estimators, random_state=42)
        self.model.fit(X_scaled, y)
'''
    
    def _get_momentum_macd_template(self) -> str:
        """Get momentum MACD template code"""
        return '''
import pandas as pd
import numpy as np
import talib

class MomentumMACDStrategy:
    def __init__(self, symbols: List[str] = ["EURUSD"]):
        self.symbols = symbols
        self.macd_fast: int = 12
        self.macd_slow: int = 26
        self.macd_signal: int = 9
        self.risk_per_trade: float = 0.02
        self.max_positions: int = 5
        self.stop_loss_pips: int = 50
        self.take_profit_pips: int = 100
    
    def trading_strategy(self, data, params):
        """MACD-based momentum strategy"""
        try:
            df = pd.DataFrame(data)
            
            if len(df) < self.macd_slow + self.macd_signal:
                return {'signal': 'hold', 'confidence': 0.0}
            
            # Calculate MACD
            macd, macd_signal, macd_hist = talib.MACD(
                df['close'].values, 
                fastperiod=self.macd_fast,
                slowperiod=self.macd_slow, 
                signalperiod=self.macd_signal
            )
            
            # Current values
            current_macd = macd[-1]
            current_signal = macd_signal[-1]
            prev_macd = macd[-2]
            prev_signal = macd_signal[-2]
            
            # Generate signals
            if current_macd > current_signal and prev_macd <= prev_signal:
                # Bullish crossover
                return {'signal': 'buy', 'confidence': 0.7}
            elif current_macd < current_signal and prev_macd >= prev_signal:
                # Bearish crossover
                return {'signal': 'sell', 'confidence': 0.7}
            else:
                return {'signal': 'hold', 'confidence': 0.5}
                
        except Exception as e:
            return {'signal': 'hold', 'confidence': 0.0}
'''
    
    def _get_mean_reversion_rsi_template(self) -> str:
        """Get mean reversion RSI template code"""
        return '''
import pandas as pd
import numpy as np
import talib

class MeanReversionRSIStrategy:
    def __init__(self, symbols: List[str] = ["EURUSD"]):
        self.symbols = symbols
        self.rsi_period: int = 14
        self.oversold_level: float = 30
        self.overbought_level: float = 70
        self.risk_per_trade: float = 0.02
    
    def trading_strategy(self, data, params):
        """RSI-based mean reversion strategy"""
        try:
            df = pd.DataFrame(data)
            
            if len(df) < self.rsi_period + 1:
                return {'signal': 'hold', 'confidence': 0.0}
            
            # Calculate RSI
            rsi = talib.RSI(df['close'].values, timeperiod=self.rsi_period)
            current_rsi = rsi[-1]
            
            # Generate signals
            if current_rsi < self.oversold_level:
                # Oversold - buy signal
                confidence = (self.oversold_level - current_rsi) / self.oversold_level
                return {'signal': 'buy', 'confidence': min(confidence, 0.9)}
            elif current_rsi > self.overbought_level:
                # Overbought - sell signal
                confidence = (current_rsi - self.overbought_level) / (100 - self.overbought_level)
                return {'signal': 'sell', 'confidence': min(confidence, 0.9)}
            else:
                return {'signal': 'hold', 'confidence': 0.5}
                
        except Exception as e:
            return {'signal': 'hold', 'confidence': 0.0}
'''
    
    def _get_bollinger_breakout_template(self) -> str:
        """Get Bollinger Bands breakout template code"""
        return '''
import pandas as pd
import numpy as np
import talib

class BollingerBreakoutStrategy:
    def __init__(self, symbols: List[str] = ["EURUSD"]):
        self.symbols = symbols
        self.bb_period: int = 20
        self.bb_std: float = 2.0
        self.risk_per_trade: float = 0.015
    
    def trading_strategy(self, data, params):
        """Bollinger Bands breakout strategy"""
        try:
            df = pd.DataFrame(data)
            
            if len(df) < self.bb_period:
                return {'signal': 'hold', 'confidence': 0.0}
            
            # Calculate Bollinger Bands
            upper, middle, lower = talib.BBANDS(
                df['close'].values,
                timeperiod=self.bb_period,
                nbdevup=self.bb_std,
                nbdevdn=self.bb_std
            )
            
            current_price = df['close'].iloc[-1]
            current_upper = upper[-1]
            current_lower = lower[-1]
            
            # Generate signals
            if current_price > current_upper:
                # Breakout above upper band
                return {'signal': 'buy', 'confidence': 0.6}
            elif current_price < current_lower:
                # Breakout below lower band
                return {'signal': 'sell', 'confidence': 0.6}
            else:
                return {'signal': 'hold', 'confidence': 0.3}
                
        except Exception as e:
            return {'signal': 'hold', 'confidence': 0.0}
'''
    
    def _get_grid_trading_template(self) -> str:
        """Get grid trading template code"""
        return '''
import pandas as pd
import numpy as np

class GridTradingStrategy:
    def __init__(self, symbols: List[str] = ["EURUSD"]):
        self.symbols = symbols
        self.grid_size: int = 20  # pips
        self.max_positions: int = 5
        self.risk_per_trade: float = 0.01
        self.grid_levels = []
        self.positions = []
    
    def trading_strategy(self, data, params):
        """Grid trading strategy for range-bound markets"""
        try:
            df = pd.DataFrame(data)
            current_price = df['close'].iloc[-1]
            
            # Initialize grid if empty
            if not self.grid_levels:
                self._initialize_grid(current_price)
            
            # Check for grid signals
            signal = self._check_grid_signals(current_price)
            
            return signal
                
        except Exception as e:
            return {'signal': 'hold', 'confidence': 0.0}
    
    def _initialize_grid(self, current_price):
        """Initialize grid levels"""
        pip_value = 0.0001  # For most forex pairs
        grid_distance = self.grid_size * pip_value
        
        # Create grid levels above and below current price
        for i in range(1, self.max_positions + 1):
            self.grid_levels.append(current_price + (i * grid_distance))  # Upper levels
            self.grid_levels.append(current_price - (i * grid_distance))  # Lower levels
    
    def _check_grid_signals(self, current_price):
        """Check for grid trading signals"""
        # Simplified grid logic
        if len(self.positions) < self.max_positions:
            # Look for entry opportunities
            for level in self.grid_levels:
                if abs(current_price - level) < 0.0001:  # Close to grid level
                    return {'signal': 'buy' if current_price < level else 'sell', 'confidence': 0.5}
        
        return {'signal': 'hold', 'confidence': 0.3}
'''
    
    def _get_pairs_trading_template(self) -> str:
        """Get pairs trading template code"""
        return '''
import pandas as pd
import numpy as np
from scipy import stats

class PairsTradingStrategy:
    def __init__(self, symbols: List[str] = ["EURUSD", "GBPUSD"]):
        self.symbols = symbols
        self.lookback_period: int = 60
        self.entry_threshold: float = 2.0
        self.exit_threshold: float = 0.5
        self.risk_per_trade: float = 0.01
    
    def trading_strategy(self, data, params):
        """Pairs trading strategy using correlation analysis"""
        try:
            if len(self.symbols) < 2:
                return {'signal': 'hold', 'confidence': 0.0}
            
            df = pd.DataFrame(data)
            
            if len(df) < self.lookback_period:
                return {'signal': 'hold', 'confidence': 0.0}
            
            # Calculate spread between pairs
            spread = self._calculate_spread(df)
            
            if spread is None:
                return {'signal': 'hold', 'confidence': 0.0}
            
            # Calculate z-score
            z_score = self._calculate_z_score(spread)
            
            # Generate signals
            if z_score > self.entry_threshold:
                return {'signal': 'sell', 'confidence': 0.6}  # Spread too high
            elif z_score < -self.entry_threshold:
                return {'signal': 'buy', 'confidence': 0.6}   # Spread too low
            elif abs(z_score) < self.exit_threshold:
                return {'signal': 'hold', 'confidence': 0.8}  # Near mean
            else:
                return {'signal': 'hold', 'confidence': 0.3}
                
        except Exception as e:
            return {'signal': 'hold', 'confidence': 0.0}
    
    def _calculate_spread(self, df):
        """Calculate spread between currency pairs"""
        try:
            # Simplified spread calculation
            pair1_col = f"{self.symbols[0]}_close" if f"{self.symbols[0]}_close" in df.columns else 'close'
            pair2_col = f"{self.symbols[1]}_close" if f"{self.symbols[1]}_close" in df.columns else 'close'
            
            if pair1_col in df.columns and pair2_col in df.columns:
                return df[pair1_col] - df[pair2_col]
            else:
                # Fallback to single close column
                return df['close'].diff()
        except:
            return None
    
    def _calculate_z_score(self, spread):
        """Calculate z-score of spread"""
        recent_spread = spread.tail(self.lookback_period)
        mean_spread = recent_spread.mean()
        std_spread = recent_spread.std()
        
        if std_spread == 0:
            return 0
        
        current_spread = spread.iloc[-1]
        return (current_spread - mean_spread) / std_spread
'''
    
    def filter_by_difficulty(self, difficulty: str) -> List[EnhancedStrategyTemplate]:
        """Filter templates by difficulty level"""
        return [template for template in self.templates.values() 
                if template.difficulty_level == difficulty]
    
    def filter_by_strategy_type(self, strategy_type: StrategyType) -> List[EnhancedStrategyTemplate]:
        """Filter templates by strategy type"""
        return [template for template in self.templates.values() 
                if template.strategy_type == strategy_type]