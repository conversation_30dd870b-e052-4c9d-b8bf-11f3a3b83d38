"""
MetaTrader 5 Bridge API
Handles all MT5 trading operations and provides endpoints for executing Python strategies on MT5
"""

import logging
import json
import os
import time
from typing import Dict, List, Any, Optional, Union
from enum import Enum
from pydantic import BaseModel
from fastapi import APIRouter, HTTPException, Depends, UploadFile, File, Form, BackgroundTasks, Body
from fastapi.responses import FileResponse, JSONResponse

# Import MT5 account handling
from ..db.crud import mt5_account

# Setup router
router = APIRouter()

# Configure logging
logger = logging.getLogger(__name__)

# Constants
STRATEGIES_DIR = os.path.join(os.path.dirname(__file__), "../../storage/strategies")
os.makedirs(STRATEGIES_DIR, exist_ok=True)

# Enum for order types
class OrderType(str, Enum):
    BUY = "buy"
    SELL = "sell"
    BUY_LIMIT = "buy_limit"
    SELL_LIMIT = "sell_limit"
    BUY_STOP = "buy_stop"
    SELL_STOP = "sell_stop"

# Enum for time in force
class TimeInForce(str, Enum):
    GTC = "gtc"  # Good till cancelled
    IOC = "ioc"  # Immediate or cancel
    FOK = "fok"  # Fill or kill
    DAY = "day"  # Day order

# Models
class MT5Credentials(BaseModel):
    server: str
    login: str
    password: str

class MT5ConnectionStatus(BaseModel):
    connected: bool
    message: str
    account_info: Optional[Dict[str, Any]] = None

class MT5OrderRequest(BaseModel):
    symbol: str
    order_type: OrderType
    volume: float
    price: Optional[float] = None
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    comment: Optional[str] = None
    magic: Optional[int] = None
    expiration: Optional[int] = None
    time_in_force: Optional[TimeInForce] = TimeInForce.GTC

class MT5OrderResponse(BaseModel):
    success: bool
    message: str
    order_id: Optional[int] = None
    details: Optional[Dict[str, Any]] = None

class MT5StrategyRequest(BaseModel):
    name: str
    code: str
    description: str = ""
    parameters: Dict[str, Any] = {}
    symbols: List[str] = []
    timeframes: List[str] = []
    is_active: bool = False

class MT5StrategyResponse(BaseModel):
    success: bool
    message: str
    strategy_id: Optional[str] = None
    path: Optional[str] = None

# Helper function to save Python strategy
def save_strategy(name: str, code: str) -> str:
    """Save a Python strategy to file and return the file path"""
    # Ensure filename has correct extension
    if not name.endswith(".py"):
        name = name + ".py"
    
    file_path = os.path.join(STRATEGIES_DIR, name)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(code)
    
    return file_path

# Routes for MT5 connection and management
@router.post("/connect", response_model=MT5ConnectionStatus)
async def connect_to_mt5(credentials: MT5Credentials):
    """
    Connect to MT5 platform with provided credentials
    """
    try:
        # Here we would use MetaTrader5 Python package to connect
        # For now we'll simulate a successful connection
        logger.info(f"Connecting to MT5 server: {credentials.server} with login: {credentials.login}")
        
        # In production, you would use:
        # import MetaTrader5 as mt5
        # if not mt5.initialize():
        #     return {"connected": False, "message": f"MT5 initialization failed: {mt5.last_error()}"}
        # if not mt5.login(credentials.login, credentials.password, credentials.server):
        #     return {"connected": False, "message": f"MT5 login failed: {mt5.last_error()}"}
        
        # Simulated response
        account_info = {
            "login": credentials.login,
            "server": credentials.server,
            "balance": 10000.0,
            "equity": 10050.0,
            "margin": 200.0,
            "free_margin": 9850.0,
            "margin_level": 5025.0,
            "currency": "USD",
            "leverage": 100,
            "name": f"Demo Account {credentials.login}",
        }
        
        # Store account info in database
        mt5_account.create_or_update_mt5_account(
            login=credentials.login,
            server=credentials.server,
            account_info=account_info
        )
        
        return {
            "connected": True,
            "message": "Successfully connected to MT5",
            "account_info": account_info
        }
    except Exception as e:
        logger.error(f"Failed to connect to MT5: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to connect to MT5: {str(e)}"
        )

@router.get("/status", response_model=MT5ConnectionStatus)
async def get_mt5_status(login: str, server: str):
    """
    Get MT5 connection status for a specific account
    """
    try:
        # Retrieve account from database
        account_data = mt5_account.get_mt5_account(login=login, server=server)
        if not account_data:
            return {
                "connected": False,
                "message": f"No MT5 account found for login {login} on server {server}"
            }
            
        # In production, you would verify the connection is still active:
        # import MetaTrader5 as mt5
        # if not mt5.terminal_info():
        #     return {"connected": False, "message": "MT5 terminal not connected"}
        
        return {
            "connected": True,
            "message": "MT5 connection active",
            "account_info": account_data.get("account_info", {})
        }
    except Exception as e:
        logger.error(f"Failed to check MT5 status: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to check MT5 status: {str(e)}"
        )

# Routes for Python strategy management
@router.post("/strategy", response_model=MT5StrategyResponse)
async def create_strategy(strategy: MT5StrategyRequest):
    """
    Create and save a Python trading strategy
    """
    try:
        logger.info(f"Creating strategy '{strategy.name}'")
        
        # Save the strategy code to file
        file_path = save_strategy(strategy.name, strategy.code)
        
        # Generate unique ID for the strategy
        import uuid
        strategy_id = str(uuid.uuid4())
        
        # In a production environment, you'd store strategy metadata in a database
        
        return {
            "success": True,
            "message": f"Strategy '{strategy.name}' created successfully",
            "strategy_id": strategy_id,
            "path": file_path
        }
    except Exception as e:
        logger.error(f"Failed to create strategy: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create strategy: {str(e)}"
        )

@router.get("/strategies", response_model=List[Dict[str, Any]])
async def list_strategies():
    """
    List all available Python trading strategies
    """
    try:
        strategies = []
        
        for filename in os.listdir(STRATEGIES_DIR):
            if filename.endswith(".py"):
                file_path = os.path.join(STRATEGIES_DIR, filename)
                stat_info = os.stat(file_path)
                
                # Extract description from file
                description = ""
                try:
                    with open(file_path, 'r') as f:
                        first_lines = ''.join([next(f) for _ in range(10)])
                        if '"""' in first_lines:
                            description = first_lines.split('"""')[1].strip()
                except:
                    pass
                
                strategies.append({
                    "name": filename,
                    "path": file_path,
                    "size": stat_info.st_size,
                    "modified": stat_info.st_mtime,
                    "description": description
                })
        
        return strategies
    except Exception as e:
        logger.error(f"Failed to list strategies: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list strategies: {str(e)}"
        )

@router.get("/strategy/{name}")
async def get_strategy(name: str):
    """
    Get a Python trading strategy by name
    """
    try:
        # Ensure filename has correct extension
        if not name.endswith(".py"):
            name = name + ".py"
        
        file_path = os.path.join(STRATEGIES_DIR, name)
        
        if not os.path.exists(file_path):
            raise HTTPException(
                status_code=404,
                detail=f"Strategy '{name}' not found"
            )
        
        return FileResponse(
            path=file_path,
            filename=name,
            media_type="text/x-python"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get strategy '{name}': {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get strategy '{name}': {str(e)}"
        )

# Routes for executing trades through MT5
@router.post("/order", response_model=MT5OrderResponse)
async def place_order(login: str, server: str, order: MT5OrderRequest):
    """
    Place an order on MT5 platform
    """
    try:
        logger.info(f"Placing {order.order_type} order for {order.symbol}")
        
        # Check if account exists
        account_data = mt5_account.get_mt5_account(login=login, server=server)
        if not account_data:
            raise HTTPException(
                status_code=404,
                detail=f"No MT5 account found for login {login} on server {server}"
            )
        
        # In a real implementation, we would use:
        # import MetaTrader5 as mt5
        # request = {
        #    "action": mt5.TRADE_ACTION_DEAL,
        #    "symbol": order.symbol,
        #    "volume": order.volume,
        #    "type": mt5.ORDER_TYPE_BUY if order.order_type == OrderType.BUY else mt5.ORDER_TYPE_SELL,
        #    "price": order.price or mt5.symbol_info_tick(order.symbol).ask,
        #    "sl": order.stop_loss,
        #    "tp": order.take_profit,
        #    "comment": order.comment,
        #    "magic": order.magic,
        #    "type_time": mt5.ORDER_TIME_GTC,
        # }
        # result = mt5.order_send(request)
        # if result.retcode != mt5.TRADE_RETCODE_DONE:
        #    return {"success": False, "message": f"Order failed with error code: {result.retcode}"}
        
        # Simulate successful order placement
        import uuid
        import random
        
        # Generate a random order ID
        order_id = random.randint(1000000, 9999999)
        
        # Simulate market price if not provided
        price = order.price
        if price is None:
            if order.order_type in [OrderType.BUY, OrderType.BUY_STOP, OrderType.BUY_LIMIT]:
                price = round(random.uniform(1.0, 1.5), 5)  # Simulated ask price
            else:
                price = round(random.uniform(0.9, 1.4), 5)  # Simulated bid price
        
        # Simulate order details
        order_details = {
            "order_id": order_id,
            "symbol": order.symbol,
            "type": order.order_type,
            "volume": order.volume,
            "price": price,
            "stop_loss": order.stop_loss,
            "take_profit": order.take_profit,
            "time": time.time(),
            "state": "filled" if order.order_type in [OrderType.BUY, OrderType.SELL] else "placed",
        }
        
        return {
            "success": True,
            "message": f"{order.order_type.capitalize()} order placed successfully",
            "order_id": order_id,
            "details": order_details
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to place order: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to place order: {str(e)}"
        )

@router.post("/execute_strategy")
async def execute_strategy(login: str, server: str, strategy_name: str, symbol: str, timeframe: str = "M15"):
    """
    Execute a Python strategy on an MT5 account
    """
    try:
        logger.info(f"Executing strategy '{strategy_name}' on {symbol} {timeframe}")
        
        # Check if account exists
        account_data = mt5_account.get_mt5_account(login=login, server=server)
        if not account_data:
            raise HTTPException(
                status_code=404,
                detail=f"No MT5 account found for login {login} on server {server}"
            )
        
        # Check if strategy exists
        if not strategy_name.endswith(".py"):
            strategy_name = strategy_name + ".py"
        
        strategy_path = os.path.join(STRATEGIES_DIR, strategy_name)
        if not os.path.exists(strategy_path):
            raise HTTPException(
                status_code=404,
                detail=f"Strategy '{strategy_name}' not found"
            )
        
        # In a production environment, you would:
        # 1. Load the strategy module dynamically
        # 2. Initialize the strategy with the MT5 connection
        # 3. Execute the strategy with the symbol and timeframe
        # 4. Return the execution results
        
        # Simulate strategy execution results
        import random
        
        # Simulate some trades
        trades = []
        for _ in range(random.randint(1, 3)):
            order_type = random.choice([OrderType.BUY, OrderType.SELL])
            price = round(random.uniform(1.0, 1.5), 5)
            
            trades.append({
                "symbol": symbol,
                "type": order_type,
                "volume": round(random.uniform(0.1, 1.0), 2),
                "price": price,
                "time": time.time(),
                "profit": round(random.uniform(-100, 100), 2),
            })
        
        return {
            "success": True,
            "message": f"Strategy '{strategy_name}' executed successfully on {symbol} {timeframe}",
            "trades": trades,
            "account": login,
            "execution_time": time.time()
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to execute strategy: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to execute strategy: {str(e)}"
        )
