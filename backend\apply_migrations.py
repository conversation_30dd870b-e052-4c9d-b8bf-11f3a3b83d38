"""
<PERSON>ript to apply database migrations
"""

import os
import sys
import subprocess
import logging
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("apply_migrations")

# Load environment variables
load_dotenv()

def main():
    """Main function"""
    try:
        # Check if DATABASE_URL is set
        if not os.getenv("DATABASE_URL"):
            logger.error("DATABASE_URL environment variable is not set")
            sys.exit(1)
        
        # Apply migrations
        logger.info("Applying migrations...")
        subprocess.run(
            ["alembic", "upgrade", "head"],
            check=True
        )
        
        logger.info("Migrations applied successfully")
    except subprocess.CalledProcessError as e:
        logger.error(f"Error applying migrations: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()