import pytest
import pandas as pd
from .backtester import Backtester, BacktestValidationError

def test_backtest_with_valid_strategy():
    """Test backtesting with a valid strategy"""
    backtester = Backtester()
    test_data = pd.DataFrame({
        'open': [99, 100, 101, 102, 103],
        'high': [100, 101, 102, 103, 104],
        'low': [98, 99, 100, 101, 102],
        'close': [100, 101, 102, 103, 104],
        'volume': [1000, 1100, 1200, 1300, 1400]
    })
    
    strategy_code = """
def strategy(data):
    return data['close'] > 100
"""
    
    result = backtester.run_backtest(strategy_code, test_data)
    
    assert 'performance_metrics' in result
    assert 'equity_curve' in result
    assert result['performance_metrics']['total_return'] > 0

def test_backtest_with_invalid_data():
    """Test backtesting validation with insufficient data"""
    backtester = Backtester()
    insufficient_data = pd.DataFrame({'close': [100]})  # Only one data point
    
    with pytest.raises(BacktestValidationError):
        backtester.run_backtest("def strategy(data): return True", insufficient_data)
