# src/events/event_schemas.py
from pydantic import BaseModel, Field, field_validator
from typing import Dict, List, Optional, Union, Any, Literal
from datetime import datetime
from decimal import Decimal
import re
from .event_types import EventType

class StrategyExecutedPayload(BaseModel):
    symbol: str = Field(..., pattern=r'^[A-Z]{6}$', description="Currency pair in EURUSD format")
    action: str = Field(..., pattern=r'^(BUY|SELL|HOLD)$')
    confidence: float = Field(..., ge=0.0, le=1.0)
    parameters: Dict[str, Union[str, float, int]]
    reasoning: Optional[str] = None

class StrategyExecutedMetadata(BaseModel):
    backtest_id: Optional[str] = None
    dummy_mode: bool = True
    user_id: str = Field(..., pattern=r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$')

class StrategyExecutedEvent(BaseModel):
    type: Literal["STRATEGY_EXECUTED"] = "STRATEGY_EXECUTED"
    timestamp: datetime
    strategy_id: str = Field(..., min_length=1, max_length=50)
    payload: StrategyExecutedPayload
    metadata: Optional[StrategyExecutedMetadata] = None
    id: Optional[str] = None

class MarketDataPayload(BaseModel):
    symbol: str = Field(..., pattern=r'^[A-Z]{6}$')
    data: Dict[str, Any] = Field(..., description="OHLCV data")
    source: str = Field(..., pattern=r'^(dukascopy|forexsb|yahoo|mt5)$')
    integrity_hash: str = Field(..., min_length=64, max_length=64, description="SHA-256 hash")

    @field_validator('data')
    @classmethod
    def validate_ohlcv_data(cls, v):
        required_fields = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
        for field in required_fields:
            if field not in v:
                raise ValueError(f"Missing required field: {field}")
        
        # Validate OHLC consistency
        if v['high'] < v['low']:
            raise ValueError("High price cannot be less than low price")
        if not (v['low'] <= v['open'] <= v['high']):
            raise ValueError("Open price must be between low and high")
        if not (v['low'] <= v['close'] <= v['high']):
            raise ValueError("Close price must be between low and high")
        if v['volume'] < 0:
            raise ValueError("Volume cannot be negative")
            
        return v

class MarketDataReceivedEvent(BaseModel):
    type: Literal["MARKET_DATA_RECEIVED"] = "MARKET_DATA_RECEIVED"
    timestamp: datetime
    payload: MarketDataPayload
    id: Optional[str] = None

class ConvergenceMetrics(BaseModel):
    fitness_variance: float = Field(..., ge=0.0)
    generation_improvement: float
    diversity_score: float = Field(..., ge=0.0, le=1.0)

class EliteGenome(BaseModel):
    id: str = Field(..., pattern=r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$')
    fitness: float = Field(..., ge=0.0, le=1.0)
    parameters: Dict[str, Union[str, float, int]]
    backtest_hash: str = Field(..., min_length=64, max_length=64)

class DGMEvolutionPayload(BaseModel):
    generation: int = Field(..., ge=0)
    population_size: int = Field(..., gt=0)
    best_fitness: float = Field(..., ge=0.0, le=1.0)
    convergence_metrics: ConvergenceMetrics
    elite_genomes: List[EliteGenome] = Field(..., max_length=10)

class DGMEvolutionCompleteEvent(BaseModel):
    type: Literal["DGM_EVOLUTION_COMPLETE"] = "DGM_EVOLUTION_COMPLETE"
    timestamp: datetime
    payload: DGMEvolutionPayload
    id: Optional[str] = None

class RiskLimitPayload(BaseModel):
    severity: str = Field(..., pattern=r'^(LOW|MEDIUM|HIGH|CRITICAL)$')
    limit_type: str = Field(..., pattern=r'^(MAX_DRAWDOWN|MAX_EXPOSURE|MARGIN_CALL|CORRELATION_RISK)$')
    current_value: float
    limit_value: float
    portfolio_state: Dict[str, Any]
    recommended_actions: List[str]

class RiskLimitBreachedEvent(BaseModel):
    type: Literal["RISK_LIMIT_BREACHED"] = "RISK_LIMIT_BREACHED"
    timestamp: datetime
    payload: RiskLimitPayload
    id: Optional[str] = None

# Union type for all trading events
TradingEvent = Union[
    StrategyExecutedEvent,
    MarketDataReceivedEvent, 
    DGMEvolutionCompleteEvent,
    RiskLimitBreachedEvent
]

class TradingEventSchemas:
    """Container for all trading event schemas"""
    STRATEGY_EXECUTED = StrategyExecutedEvent
    MARKET_DATA_RECEIVED = MarketDataReceivedEvent
    DGM_EVOLUTION_COMPLETE = DGMEvolutionCompleteEvent
    RISK_LIMIT_BREACHED = RiskLimitBreachedEvent
    
    @classmethod
    def get_schema(cls, event_type: str):
        """Get schema class for event type"""
        schema_map = {
            EventType.STRATEGY_EXECUTED: cls.STRATEGY_EXECUTED,
            EventType.MARKET_DATA_RECEIVED: cls.MARKET_DATA_RECEIVED,
            EventType.DGM_EVOLUTION_COMPLETE: cls.DGM_EVOLUTION_COMPLETE,
            EventType.RISK_LIMIT_BREACHED: cls.RISK_LIMIT_BREACHED,
        }
        return schema_map.get(event_type)