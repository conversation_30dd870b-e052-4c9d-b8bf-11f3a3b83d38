from fastapi import APIRouter, Request
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from typing import List, Dict

router = APIRouter()
templates = Jinja2Templates(directory="templates")

class HomepageService:
    def __init__(self):
        self.professional_prompts = [
            {
                "id": 1,
                "title": "RSI Oversold Strategy",
                "description": "Buy when RSI < 30, sell when RSI > 70",
                "category": "Mean Reversion",
                "difficulty": "Beginner"
            },
            {
                "id": 2,
                "title": "Moving Average Crossover",
                "description": "Golden cross strategy with SMA 50/200",
                "category": "Trend Following", 
                "difficulty": "Intermediate"
            },
            {
                "id": 3,
                "title": "Bollinger Bands Strategy",
                "description": "Trade breakouts from Bollinger Bands",
                "category": "Volatility",
                "difficulty": "Intermediate"
            }
        ]
    
    def get_prompts(self) -> List[Dict]:
        return self.professional_prompts

homepage_service = HomepageService()

@router.get("/", response_class=HTMLResponse)
async def homepage(request: Request, mt5_connected: bool = False):
    """Single-page homepage with all core features"""
    context = {
        "request": request,
        "core_features": [
            {
                "title": "Python Strategy Builder",
                "description": "Build trading strategies using natural language with our AI chatbot",
                "icon": "🤖",
                "cta": "Start Building"
            },
            {
                "title": "Free MT5 EA Generation",
                "description": "Convert Python strategies to MetaTrader 5 Expert Advisors",
                "icon": "⚡", 
                "cta": "Generate EA",
                "premium": True
            },
            {
                "title": "Professional Templates",
                "description": "Use ready-made strategies from experienced traders",
                "icon": "🎯",
                "cta": "Explore Templates"
            }
        ],
        "professional_prompts": homepage_service.get_prompts(),
        "mt5_connected": mt5_connected,
        "show_marketing": False
    }
    return templates.TemplateResponse("homepage.html", context)

@router.get("/api/prompts")
async def get_professional_prompts():
    return {"prompts": homepage_service.get_prompts()}

@router.post("/api/prompts/{prompt_id}/apply")
async def apply_prompt(prompt_id: int):
    prompt = next((p for p in homepage_service.get_prompts() if p["id"] == prompt_id), None)
    if not prompt:
        return {"error": "Prompt not found"}
    return {
        "success": True,
        "prompt": prompt,
        "chatbot_context": f"User selected professional prompt: {prompt['title']}. Please help them build this strategy."
    }
