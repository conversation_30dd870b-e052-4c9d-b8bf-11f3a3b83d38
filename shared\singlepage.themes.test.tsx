
// tests/integration/singlepage.themes.test.tsx
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'jest-axe';
import userEvent from '@testing-library/user-event';

import { DystopianSinglePage } from '../src/components/themes/DystopianSinglePage';
import { CyberpunkSinglePage } from '../src/components/themes/CyberpunkSinglePage';
import { SteampunkSinglePage } from '../src/components/themes/SteampunkSinglePage';
import { ProfessionalSinglePage } from '../src/components/themes/ProfessionalSinglePage';

expect.extend(toHaveNoViolations);

// Mock framer-motion for testing
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    h1: ({ children, ...props }: any) => <h1 {...props}>{children}</h1>,
    p: ({ children, ...props }: any) => <p {...props}>{children}</p>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
    header: ({ children, ...props }: any) => <header {...props}>{children}</header>,
    section: ({ children, ...props }: any) => <section {...props}>{children}</section>,
  },
  useInView: () => true,
  AnimatePresence: ({ children }: any) => children,
}));

describe('Single Page Theme Integration', () => {
  const themes = [
    { 
      name: 'dystopian', 
      component: DystopianSinglePage,
      brandText: /DARWIN.*GÖDEL.*SYSTEM/i,
      primaryColor: 'red',
      sections: ['HOME', 'STRATEGIES', 'AI_INTERFACE', 'METRICS']
    },
    { 
      name: 'cyberpunk', 
      component: CyberpunkSinglePage,
      brandText: /DARWIN.*GÖDEL.*NEXUS/i,
      primaryColor: 'cyan',
      sections: ['HOME', 'NEURAL STRATEGIES', 'AI CONSCIOUSNESS', 'QUANTUM METRICS']
    },
    { 
      name: 'steampunk', 
      component: SteampunkSinglePage,
      brandText: /Darwin.*Gödel.*Analytical.*Engine/i,
      primaryColor: 'amber',
      sections: ['Home', 'Mechanical Strategies', 'Analytical Assistant', 'Engine Metrics']
    },
    { 
      name: 'professional', 
      component: ProfessionalSinglePage,
      brandText: /Darwin.*Gödel.*Platform/i,
      primaryColor: 'blue',
      sections: ['Home', 'Strategies', 'AI Assistant', 'Analytics']
    }
  ];

  themes.forEach(({ name, component: Component, brandText, sections }) => {
    describe(`${name} single-page theme`, () => {
      beforeEach(() => {
        // Mock scrollIntoView
        Element.prototype.scrollIntoView = jest.fn();
      });

      it('should render without errors', () => {
        render(<Component />);
        expect(document.body).toBeInTheDocument();
      });

      it('should display correct branding', () => {
        render(<Component />);
        expect(screen.getByText(brandText)).toBeInTheDocument();
      });

      it('should have fixed navigation with all sections', () => {
        render(<Component />);

        sections.forEach(section => {
          expect(screen.getByText(section)).toBeInTheDocument();
        });
      });

      it('should have hero section with call-to-action buttons', () => {
        render(<Component />);

        // Should have at least 2 CTA buttons
        const buttons = screen.getAllByRole('button');
        expect(buttons.length).toBeGreaterThanOrEqual(2);
      });

      it('should have strategy management section', () => {
        render(<Component />);

        // Should display multiple strategies
        expect(screen.getByText(/strateg/i)).toBeInTheDocument();
      });

      it('should have interactive AI chat interface', async () => {
        const user = userEvent.setup();
        render(<Component />);

        // Find chat input
        const chatInput = screen.getByPlaceholderText(/communicate|interface|ask/i);
        expect(chatInput).toBeInTheDocument();

        // Find send button
        const sendButton = screen.getByText(/send|transmit|exec/i);
        expect(sendButton).toBeInTheDocument();

        // Test chat functionality
        await user.type(chatInput, 'Test message');
        await user.click(sendButton);

        // Should show user message
        await waitFor(() => {
          expect(screen.getByText('Test message')).toBeInTheDocument();
        });
      });

      it('should have metrics/analytics dashboard', () => {
        render(<Component />);

        // Should display performance metrics
        expect(screen.getByText(/metric|analytic|performance/i)).toBeInTheDocument();
      });

      it('should support smooth scrolling navigation', async () => {
        const user = userEvent.setup();
        render(<Component />);

        // Click on a navigation item
        const navButton = screen.getByText(sections[1]); // Second section
        await user.click(navButton);

        // scrollIntoView should have been called
        expect(Element.prototype.scrollIntoView).toHaveBeenCalled();
      });

      it('should be responsive on mobile devices', () => {
        // Mock mobile viewport
        Object.defineProperty(window, 'innerWidth', {
          writable: true,
          configurable: true,
          value: 375,
        });

        render(<Component />);

        // Content should still be accessible
        expect(screen.getByText(brandText)).toBeInTheDocument();
      });

      it('should handle keyboard navigation', async () => {
        const user = userEvent.setup();
        render(<Component />);

        // Tab through interactive elements
        await user.tab();
        expect(document.activeElement).toBeInstanceOf(HTMLElement);
      });

      // Theme-specific tests
      if (name === 'professional') {
        it('should maintain accessibility standards', async () => {
          const { container } = render(<Component />);
          const results = await axe(container);
          expect(results).toHaveNoViolations();
        });

        it('should display compliance indicators', () => {
          render(<Component />);
          expect(screen.getByText(/SOC 2|Compliant/i)).toBeInTheDocument();
          expect(screen.getByText(/99.9%.*Uptime/i)).toBeInTheDocument();
        });

        it('should show professional metrics', () => {
          render(<Component />);
          expect(screen.getByText(/Sharpe Ratio/i)).toBeInTheDocument();
          expect(screen.getByText(/Max Drawdown/i)).toBeInTheDocument();
        });
      }

      if (name === 'dystopian') {
        it('should have terminal-style interface elements', () => {
          render(<Component />);
          expect(screen.getByText(/\[SYSTEM\]|\[RUNNING\]|\[ACTIVE\]/)).toBeInTheDocument();
        });

        it('should display glitch effects on title', () => {
          render(<Component />);
          const title = screen.getByText(/SYSTEM.*ONLINE/i);
          expect(title).toHaveClass(/glitch/);
        });
      }

      if (name === 'cyberpunk') {
        it('should have neon/holographic styling', () => {
          render(<Component />);
          expect(screen.getByText(/NEXUS.*ONLINE/i)).toBeInTheDocument();
          expect(screen.getByText(/Neural|Quantum|Consciousness/i)).toBeInTheDocument();
        });
      }

      if (name === 'steampunk') {
        it('should display Victorian/mechanical elements', () => {
          render(<Component />);
          expect(screen.getByText(/ENGINE.*OPERATIONAL/i)).toBeInTheDocument();
          expect(screen.getByText(/Mechanical|Steam|Brass|Clockwork/i)).toBeInTheDocument();
        });
      }
    });
  });

  describe('Cross-theme functionality', () => {
    it('should maintain consistent chat functionality across themes', async () => {
      const user = userEvent.setup();

      for (const { component: Component } of themes) {
        const { unmount } = render(<Component />);

        const chatInput = screen.getByPlaceholderText(/communicate|interface|ask/i);
        const sendButton = screen.getByText(/send|transmit|exec/i);

        await user.type(chatInput, 'Test consistency');
        await user.click(sendButton);

        await waitFor(() => {
          expect(screen.getByText('Test consistency')).toBeInTheDocument();
        });

        unmount();
      }
    });

    it('should have consistent navigation structure', () => {
      themes.forEach(({ component: Component, sections }) => {
        const { unmount } = render(<Component />);

        // Each theme should have 4 main sections
        expect(sections).toHaveLength(4);

        sections.forEach(section => {
          expect(screen.getByText(section)).toBeInTheDocument();
        });

        unmount();
      });
    });

    it('should handle theme switching without errors', () => {
      const { rerender } = render(<DystopianSinglePage />);
      expect(screen.getByText(/DARWIN.*GÖDEL.*SYSTEM/i)).toBeInTheDocument();

      rerender(<CyberpunkSinglePage />);
      expect(screen.getByText(/DARWIN.*GÖDEL.*NEXUS/i)).toBeInTheDocument();

      rerender(<SteampunkSinglePage />);
      expect(screen.getByText(/Darwin.*Gödel.*Analytical.*Engine/i)).toBeInTheDocument();

      rerender(<ProfessionalSinglePage />);
      expect(screen.getByText(/Darwin.*Gödel.*Platform/i)).toBeInTheDocument();
    });
  });

  describe('Performance and optimization', () => {
    it('should not have memory leaks when switching themes', () => {
      const { rerender } = render(<DystopianSinglePage />);

      themes.forEach(({ component: Component }) => {
        rerender(<Component />);
      });

      // No specific assertion needed - test passes if no errors thrown
    });

    it('should handle rapid navigation clicks', async () => {
      const user = userEvent.setup();
      render(<ProfessionalSinglePage />);

      const navButtons = screen.getAllByRole('button').slice(0, 4); // First 4 nav buttons

      // Rapidly click navigation buttons
      for (const button of navButtons) {
        await user.click(button);
      }

      // Should not throw errors
      expect(Element.prototype.scrollIntoView).toHaveBeenCalled();
    });
  });

  describe('Error handling', () => {
    it('should handle missing message gracefully', async () => {
      const user = userEvent.setup();
      render(<ProfessionalSinglePage />);

      const sendButton = screen.getByText(/send/i);
      await user.click(sendButton);

      // Should not add empty message
      expect(screen.queryByText('')).not.toBeInTheDocument();
    });

    it('should handle long messages appropriately', async () => {
      const user = userEvent.setup();
      render(<ProfessionalSinglePage />);

      const chatInput = screen.getByPlaceholderText(/ask about/i);
      const longMessage = 'A'.repeat(1000);

      await user.type(chatInput, longMessage);
      await user.click(screen.getByText(/send/i));

      await waitFor(() => {
        expect(screen.getByText(longMessage)).toBeInTheDocument();
      });
    });
  });
});
