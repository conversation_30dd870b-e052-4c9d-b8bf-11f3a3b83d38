# tests/test_data_validation.py
import pytest
import hashlib
import json
from datetime import datetime, timedelta
from decimal import Decimal
from dataclasses import dataclass
from typing import Optional, List, Dict, Any

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

# Property-based testing imports
from hypothesis import given, strategies as st, assume, settings, Verbosity
from hypothesis.strategies import composite

from validation.data_validator import DataValidator, DataValidationError, OHLCData, DataSourceManager

class TestDataValidation:
    def test_ohlc_consistency_validation(self):
        """Test OHLC data consistency rules"""
        validator = DataValidator()
        
        # Valid data
        valid_data = OHLCData(
            timestamp=datetime.now(),
            open=Decimal('1.2000'),
            high=Decimal('1.2050'),
            low=Decimal('1.1950'),
            close=Decimal('1.2020'),
            volume=1000,
            source="dukascopy",
            hash=""
        )
        
        assert validator.validate_ohlc(valid_data) == True
        
        # Invalid data - high < low
        invalid_data = OHLCData(
            timestamp=datetime.now(),
            open=Decimal('1.2000'),
            high=Decimal('1.1900'),  # Invalid: high < low
            low=Decimal('1.1950'),
            close=Decimal('1.2020'),
            volume=1000,
            source="dukascopy",
            hash=""
        )
        
        with pytest.raises(DataValidationError):
            validator.validate_ohlc(invalid_data)
    
    def test_data_integrity_hashing(self):
        """Test data integrity verification"""
        validator = DataValidator()
        
        data = OHLCData(
            timestamp=datetime(2025, 1, 1, 12, 0),
            open=Decimal('1.2000'),
            high=Decimal('1.2050'),
            low=Decimal('1.1950'),
            close=Decimal('1.2020'),
            volume=1000,
            source="dukascopy",
            hash=""
        )
        
        # Generate hash
        data_hash = validator.generate_data_hash(data)
        data.hash = data_hash
        
        # Verify integrity
        assert validator.verify_integrity(data) == True
        
        # Test tampering detection
        data.close = Decimal('1.3000')  # Tamper with data
        assert validator.verify_integrity(data) == False

    def test_ohlc_open_close_within_range(self):
        """Test that open and close prices are within high-low range"""
        validator = DataValidator()
        
        # Invalid data - open outside range
        invalid_open_data = OHLCData(
            timestamp=datetime.now(),
            open=Decimal('1.2100'),  # Invalid: open > high
            high=Decimal('1.2050'),
            low=Decimal('1.1950'),
            close=Decimal('1.2020'),
            volume=1000,
            source="dukascopy",
            hash=""
        )
        
        with pytest.raises(DataValidationError):
            validator.validate_ohlc(invalid_open_data)
        
        # Invalid data - close outside range
        invalid_close_data = OHLCData(
            timestamp=datetime.now(),
            open=Decimal('1.2000'),
            high=Decimal('1.2050'),
            low=Decimal('1.1950'),
            close=Decimal('1.1900'),  # Invalid: close < low
            volume=1000,
            source="dukascopy",
            hash=""
        )
        
        with pytest.raises(DataValidationError):
            validator.validate_ohlc(invalid_close_data)

    def test_timestamp_validation(self):
        """Test timestamp validation rules"""
        validator = DataValidator()
        
        # Future timestamp (invalid)
        future_data = OHLCData(
            timestamp=datetime(2030, 1, 1),  # Future date
            open=Decimal('1.2000'),
            high=Decimal('1.2050'),
            low=Decimal('1.1950'),
            close=Decimal('1.2020'),
            volume=1000,
            source="dukascopy",
            hash=""
        )
        
        with pytest.raises(DataValidationError):
            validator.validate_ohlc(future_data)
        
        # Very old timestamp (invalid)
        old_data = OHLCData(
            timestamp=datetime(2010, 1, 1),  # Too old
            open=Decimal('1.2000'),
            high=Decimal('1.2050'),
            low=Decimal('1.1950'),
            close=Decimal('1.2020'),
            volume=1000,
            source="dukascopy",
            hash=""
        )
        
        with pytest.raises(DataValidationError):
            validator.validate_ohlc(old_data)

    def test_volume_validation(self):
        """Test volume validation"""
        validator = DataValidator()
        
        # Negative volume (invalid)
        negative_volume_data = OHLCData(
            timestamp=datetime.now(),
            open=Decimal('1.2000'),
            high=Decimal('1.2050'),
            low=Decimal('1.1950'),
            close=Decimal('1.2020'),
            volume=-100,  # Invalid: negative volume
            source="dukascopy",
            hash=""
        )
        
        with pytest.raises(DataValidationError):
            validator.validate_ohlc(negative_volume_data)
        
        # Zero volume (valid)
        zero_volume_data = OHLCData(
            timestamp=datetime.now(),
            open=Decimal('1.2000'),
            high=Decimal('1.2050'),
            low=Decimal('1.1950'),
            close=Decimal('1.2020'),
            volume=0,  # Valid: zero volume
            source="dukascopy",
            hash=""
        )
        
        assert validator.validate_ohlc(zero_volume_data) == True

    def test_price_precision_validation(self):
        """Test price precision validation"""
        validator = DataValidator()
        
        # Too many decimal places (invalid)
        high_precision_data = OHLCData(
            timestamp=datetime.now(),
            open=Decimal('1.200000'),  # Invalid: 6 decimal places
            high=Decimal('1.205000'),
            low=Decimal('1.195000'),
            close=Decimal('1.202000'),
            volume=1000,
            source="dukascopy",
            hash=""
        )
        
        with pytest.raises(DataValidationError):
            validator.validate_ohlc(high_precision_data)

class TestDataSourceManager:
    def test_trusted_source_verification(self):
        """Test verification of trusted data sources"""
        source_manager = DataSourceManager()
        
        # Test trusted source
        assert source_manager.verify_source_authenticity("dukascopy", "test_file.csv") == True
        assert source_manager.verify_source_authenticity("forexsb", "test_file.csv") == True
        
        # Test untrusted source
        assert source_manager.verify_source_authenticity("unknown_source", "test_file.csv") == False

    def test_source_configuration(self):
        """Test source configuration structure"""
        source_manager = DataSourceManager()
        
        # Check dukascopy configuration
        dukascopy_config = source_manager.verified_sources.get("dukascopy")
        assert dukascopy_config is not None
        assert dukascopy_config["trusted"] == True
        assert "base_url" in dukascopy_config
        
        # Check forexsb configuration
        forexsb_config = source_manager.verified_sources.get("forexsb")
        assert forexsb_config is not None
        assert forexsb_config["trusted"] == True
        assert "base_url" in forexsb_config


# ============================================================================
# PROPERTY-BASED TESTING FOR OHLC DATA
# ============================================================================

@composite
def valid_ohlc_data(draw):
    """Generate valid OHLC data using Hypothesis composite strategy"""
    # Generate base price around 1.0 to 2.0 for forex-like data
    base_price = draw(st.floats(min_value=0.5, max_value=2.0, allow_nan=False, allow_infinity=False))
    
    # Generate spread (high - low) as percentage of base price
    spread_pct = draw(st.floats(min_value=0.0001, max_value=0.05))  # 0.01% to 5%
    spread = base_price * spread_pct
    
    # Generate low and high prices
    low = draw(st.floats(min_value=max(0.0001, base_price - spread), 
                        max_value=base_price + spread, 
                        allow_nan=False, allow_infinity=False))
    high = draw(st.floats(min_value=low, 
                         max_value=low + spread * 2, 
                         allow_nan=False, allow_infinity=False))
    
    # Ensure high >= low (additional safety check)
    assume(high >= low)
    
    # Generate open and close within the range
    open_price = draw(st.floats(min_value=low, max_value=high, allow_nan=False, allow_infinity=False))
    close_price = draw(st.floats(min_value=low, max_value=high, allow_nan=False, allow_infinity=False))
    
    # Generate timestamp within reasonable range (last 5 years)
    now = datetime.now()
    start_date = now - timedelta(days=5*365)
    timestamp = draw(st.datetimes(min_value=start_date, max_value=now))
    
    # Generate volume
    volume = draw(st.integers(min_value=0, max_value=1000000))
    
    # Generate source
    source = draw(st.sampled_from(["dukascopy", "forexsb", "test_source"]))
    
    return OHLCData(
        timestamp=timestamp,
        open=Decimal(str(round(open_price, 5))),
        high=Decimal(str(round(high, 5))),
        low=Decimal(str(round(low, 5))),
        close=Decimal(str(round(close_price, 5))),
        volume=volume,
        source=source,
        hash=""
    )


@composite 
def invalid_ohlc_data(draw):
    """Generate invalid OHLC data for testing error conditions"""
    # Start with valid data
    data = draw(valid_ohlc_data())
    
    # Introduce specific violations
    violation_type = draw(st.sampled_from([
        "high_less_than_low",
        "open_outside_range", 
        "close_outside_range",
        "future_timestamp",
        "old_timestamp",
        "negative_volume",
        "excessive_precision"
    ]))
    
    if violation_type == "high_less_than_low":
        # Make high < low
        data.high = data.low - Decimal('0.0001')
    elif violation_type == "open_outside_range":
        # Make open outside high-low range
        if draw(st.booleans()):
            data.open = data.high + Decimal('0.0001')  # Above high
        else:
            data.open = data.low - Decimal('0.0001')   # Below low
    elif violation_type == "close_outside_range":
        # Make close outside high-low range
        if draw(st.booleans()):
            data.close = data.high + Decimal('0.0001')  # Above high
        else:
            data.close = data.low - Decimal('0.0001')   # Below low
    elif violation_type == "future_timestamp":
        # Future timestamp
        data.timestamp = datetime.now() + timedelta(days=1)
    elif violation_type == "old_timestamp":
        # Very old timestamp
        data.timestamp = datetime.now() - timedelta(days=11*365)  # 11 years ago
    elif violation_type == "negative_volume":
        # Negative volume
        data.volume = -1
    elif violation_type == "excessive_precision":
        # Too many decimal places
        data.open = Decimal('1.123456')  # 6 decimal places
        
    return data, violation_type


class TestPropertyBasedOHLCValidation:
    """Property-based tests for OHLC data validation using Hypothesis"""
    
    @given(valid_ohlc_data())
    @settings(max_examples=100, verbosity=Verbosity.verbose)
    def test_valid_ohlc_always_passes_validation(self, ohlc_data):
        """Property: All valid OHLC data should pass validation"""
        validator = DataValidator()
        
        # Valid OHLC data should always pass validation
        assert validator.validate_ohlc(ohlc_data) == True
        
        # Additional property checks
        assert ohlc_data.high >= ohlc_data.low, "High must be >= Low"
        assert ohlc_data.low <= ohlc_data.open <= ohlc_data.high, "Open must be within High-Low range"
        assert ohlc_data.low <= ohlc_data.close <= ohlc_data.high, "Close must be within High-Low range"
        assert ohlc_data.volume >= 0, "Volume must be non-negative"
    
    @given(st.data())
    @settings(max_examples=50)
    def test_ohlc_consistency_property(self, data):
        """Property: OHLC consistency rules must always hold for properly constructed data"""
        # Generate base parameters
        base_price = data.draw(st.floats(min_value=0.1, max_value=1000.0, allow_nan=False, allow_infinity=False))
        spread_pct = data.draw(st.floats(min_value=0.0001, max_value=0.1, allow_nan=False, allow_infinity=False))
        
        # Generate consistent OHLC data
        spread = base_price * spread_pct
        
        # Generate low and high with proper relationship
        low = base_price - spread/2
        high = base_price + spread/2
        
        # Ensure positive prices
        assume(low > 0)
        
        # Generate open and close within the range using data.draw()
        open_offset = data.draw(st.floats(min_value=-spread/2, max_value=spread/2))
        close_offset = data.draw(st.floats(min_value=-spread/2, max_value=spread/2))
        
        open_price = base_price + open_offset
        close_price = base_price + close_offset
        
        # Ensure they're within bounds
        open_price = max(low, min(high, open_price))
        close_price = max(low, min(high, close_price))
        
        # Property 1: High should be >= Low
        assert high >= low, f"High {high} should be >= Low {low}"
        
        # Property 2: Open should be within High-Low range
        assert low <= open_price <= high, f"Open {open_price} not in range [{low}, {high}]"
        
        # Property 3: Close should be within High-Low range
        assert low <= close_price <= high, f"Close {close_price} not in range [{low}, {high}]"
        
        # Property 4: High should be the maximum of all values
        all_prices = [open_price, high, low, close_price]
        assert high == max(all_prices), f"High {high} should be maximum of {all_prices}"
        
        # Property 5: Low should be the minimum of all values
        assert low == min(all_prices), f"Low {low} should be minimum of {all_prices}"
    
    @given(invalid_ohlc_data())
    @settings(max_examples=50)
    def test_invalid_ohlc_always_fails_validation(self, invalid_data_tuple):
        """Property: Invalid OHLC data should always fail validation"""
        invalid_data, violation_type = invalid_data_tuple
        validator = DataValidator()
        
        # Invalid data should always raise DataValidationError
        with pytest.raises(DataValidationError):
            validator.validate_ohlc(invalid_data)
    
    @given(valid_ohlc_data())
    @settings(max_examples=50)
    def test_data_hash_integrity_property(self, ohlc_data):
        """Property: Data hash should always detect tampering"""
        validator = DataValidator()
        
        # Generate hash for original data
        original_hash = validator.generate_data_hash(ohlc_data)
        ohlc_data.hash = original_hash
        
        # Original data should verify successfully
        assert validator.verify_integrity(ohlc_data) == True
        
        # Tamper with each field and verify detection
        tampered_fields = []
        
        # Test tampering with each price field
        for field_name in ['open', 'high', 'low', 'close']:
            original_value = getattr(ohlc_data, field_name)
            setattr(ohlc_data, field_name, original_value + Decimal('0.0001'))
            
            # Should detect tampering
            assert validator.verify_integrity(ohlc_data) == False, f"Failed to detect tampering in {field_name}"
            tampered_fields.append(field_name)
            
            # Restore original value
            setattr(ohlc_data, field_name, original_value)
            
        # Test tampering with volume
        original_volume = ohlc_data.volume
        ohlc_data.volume = original_volume + 1
        assert validator.verify_integrity(ohlc_data) == False, "Failed to detect volume tampering"
        ohlc_data.volume = original_volume
        
        # Test tampering with timestamp
        original_timestamp = ohlc_data.timestamp
        ohlc_data.timestamp = original_timestamp + timedelta(seconds=1)
        assert validator.verify_integrity(ohlc_data) == False, "Failed to detect timestamp tampering"
        ohlc_data.timestamp = original_timestamp
        
        # After restoring all values, integrity should pass again
        assert validator.verify_integrity(ohlc_data) == True
    
    @given(st.lists(valid_ohlc_data(), min_size=2, max_size=10))
    @settings(max_examples=20)
    def test_batch_validation_property(self, ohlc_list):
        """Property: Batch validation should be consistent with individual validation"""
        validator = DataValidator()
        
        # Individual validation results
        individual_results = []
        for ohlc_data in ohlc_list:
            try:
                result = validator.validate_ohlc(ohlc_data)
                individual_results.append(True)
            except DataValidationError:
                individual_results.append(False)
        
        # All should pass since we're using valid_ohlc_data()
        assert all(individual_results), "All valid OHLC data should pass individual validation"
    
    @given(
        st.floats(min_value=0.0001, max_value=10.0, allow_nan=False, allow_infinity=False),
        st.floats(min_value=0.0, max_value=0.1, allow_nan=False, allow_infinity=False)
    )
    @settings(max_examples=50)
    def test_price_spread_property(self, base_price, spread_ratio):
        """Property: Price spread relationships should be maintained"""
        # Calculate spread
        spread = base_price * spread_ratio
        
        # Generate prices with known relationships
        low = base_price - spread/2
        high = base_price + spread/2
        open_price = base_price - spread/4  # Slightly below base
        close_price = base_price + spread/4  # Slightly above base
        
        # Ensure all prices are positive
        assume(low > 0)
        
        ohlc_data = OHLCData(
            timestamp=datetime.now() - timedelta(hours=1),
            open=Decimal(str(round(open_price, 5))),
            high=Decimal(str(round(high, 5))),
            low=Decimal(str(round(low, 5))),
            close=Decimal(str(round(close_price, 5))),
            volume=1000,
            source="test_source",
            hash=""
        )
        
        validator = DataValidator()
        
        # Should pass validation
        assert validator.validate_ohlc(ohlc_data) == True
        
        # Verify spread properties
        actual_spread = float(ohlc_data.high - ohlc_data.low)
        expected_spread = spread
        assert abs(actual_spread - expected_spread) < 0.0001, "Spread calculation mismatch"
    
    @given(st.integers(min_value=0, max_value=1000000))
    @settings(max_examples=30)
    def test_volume_property(self, volume):
        """Property: Volume validation should be consistent"""
        # Create OHLC data with specific volume
        ohlc_data = OHLCData(
            timestamp=datetime.now() - timedelta(hours=1),
            open=Decimal('1.2000'),
            high=Decimal('1.2050'),
            low=Decimal('1.1950'),
            close=Decimal('1.2020'),
            volume=volume,
            source="test_source",
            hash=""
        )
        
        validator = DataValidator()
        
        # Non-negative volumes should always pass
        if volume >= 0:
            assert validator.validate_ohlc(ohlc_data) == True
        else:
            with pytest.raises(DataValidationError):
                validator.validate_ohlc(ohlc_data)


class TestDataIntegrityAuditTrail:
    """Advanced data integrity and audit trail testing"""
    
    def test_audit_trail_creation(self):
        """Test creation of audit trail for data operations"""
        validator = DataValidator()
        
        # Create sample data
        ohlc_data = OHLCData(
            timestamp=datetime.now(),
            open=Decimal('1.2000'),
            high=Decimal('1.2050'),
            low=Decimal('1.1950'),
            close=Decimal('1.2020'),
            volume=1000,
            source="dukascopy",
            hash=""
        )
        
        # Generate hash and verify audit trail
        data_hash = validator.generate_data_hash(ohlc_data)
        ohlc_data.hash = data_hash
        
        # Verify the hash is deterministic
        second_hash = validator.generate_data_hash(ohlc_data)
        assert data_hash == second_hash, "Hash generation should be deterministic"
        
        # Verify hash format
        assert len(data_hash) == 64, "SHA-256 hash should be 64 characters"
        assert all(c in '0123456789abcdef' for c in data_hash), "Hash should be hexadecimal"
    
    @given(valid_ohlc_data())
    @settings(max_examples=20)
    def test_hash_uniqueness_property(self, ohlc_data):
        """Property: Different data should produce different hashes"""
        validator = DataValidator()
        
        # Generate hash for original data
        original_hash = validator.generate_data_hash(ohlc_data)
        
        # Create slightly modified data
        modified_data = OHLCData(
            timestamp=ohlc_data.timestamp,
            open=ohlc_data.open + Decimal('0.0001'),  # Tiny change
            high=ohlc_data.high,
            low=ohlc_data.low,
            close=ohlc_data.close,
            volume=ohlc_data.volume,
            source=ohlc_data.source,
            hash=""
        )
        
        modified_hash = validator.generate_data_hash(modified_data)
        
        # Hashes should be different
        assert original_hash != modified_hash, "Different data should produce different hashes"
    
    def test_data_source_verification_comprehensive(self):
        """Comprehensive test of data source verification"""
        source_manager = DataSourceManager()
        
        # Test all trusted sources
        trusted_sources = ["dukascopy", "forexsb"]
        for source in trusted_sources:
            assert source_manager.verify_source_authenticity(source, "test.csv") == True
            
        # Test untrusted sources
        untrusted_sources = ["random_source", "malicious_feed", ""]
        for source in untrusted_sources:
            assert source_manager.verify_source_authenticity(source, "test.csv") == False
    
    @given(st.text(min_size=1, max_size=50))
    @settings(max_examples=20)
    def test_source_verification_property(self, source_name):
        """Property: Source verification should be consistent"""
        source_manager = DataSourceManager()
        
        # Verification result should be consistent
        result1 = source_manager.verify_source_authenticity(source_name, "test1.csv")
        result2 = source_manager.verify_source_authenticity(source_name, "test2.csv")
        
        # Result should be the same regardless of filename
        assert result1 == result2, "Source verification should be consistent across files"
        
        # Known trusted sources should always return True
        if source_name in ["dukascopy", "forexsb"]:
            assert result1 == True, f"Trusted source {source_name} should always verify"


# ============================================================================
# PLATFORM-SPECIFIC DATA VALIDATION FUNCTIONS
# ============================================================================

def hash_data(data: Dict[str, Any]) -> str:
    """Generate hash for arbitrary data dictionary"""
    # Create deterministic string representation
    data_string = json.dumps(data, sort_keys=True, default=str)
    return hashlib.sha256(data_string.encode()).hexdigest()


def verify_hash(data: Dict[str, Any], expected_hash: str) -> bool:
    """Verify data integrity using hash"""
    actual_hash = hash_data(data)
    return actual_hash == expected_hash


class TestPlatformDataIntegrity:
    """Test platform-specific data integrity functions"""
    
    def test_data_hash_integrity(self):
        """Test hash-based data integrity verification"""
        # Original test case from the request
        data = {
            'timestamp': '2024-07-01 00:00', 
            'open': 1.1, 
            'high': 1.2, 
            'low': 1.0, 
            'close': 1.15
        }
        
        h = hash_data(data)
        assert verify_hash(data, h)
        
        # Simulate tampering
        data_tampered = dict(data)
        data_tampered['close'] = 1.25
        assert not verify_hash(data_tampered, h)
    
    @given(st.dictionaries(
        keys=st.sampled_from(['timestamp', 'open', 'high', 'low', 'close', 'volume']),
        values=st.one_of(
            st.text(min_size=1, max_size=50),
            st.floats(min_value=0.0001, max_value=1000.0, allow_nan=False, allow_infinity=False),
            st.integers(min_value=0, max_value=1000000)
        ),
        min_size=1,
        max_size=6
    ))
    @settings(max_examples=30)
    def test_hash_data_property(self, data_dict):
        """Property: Hash function should be deterministic and detect changes"""
        # Generate hash
        original_hash = hash_data(data_dict)
        
        # Hash should be deterministic
        second_hash = hash_data(data_dict)
        assert original_hash == second_hash, "Hash should be deterministic"
        
        # Hash should be valid SHA-256
        assert len(original_hash) == 64, "Hash should be 64 characters (SHA-256)"
        assert all(c in '0123456789abcdef' for c in original_hash), "Hash should be hexadecimal"
        
        # Verification should work
        assert verify_hash(data_dict, original_hash) == True
        
        # If we can modify the data, hash should change
        if len(data_dict) > 0:
            modified_dict = dict(data_dict)
            key = list(modified_dict.keys())[0]
            original_value = modified_dict[key]
            
            # Try to modify the value
            if isinstance(original_value, (int, float)):
                modified_dict[key] = original_value + 1
            elif isinstance(original_value, str):
                modified_dict[key] = original_value + "_modified"
            
            # If we actually changed something, hash should be different
            if modified_dict[key] != original_value:
                modified_hash = hash_data(modified_dict)
                assert modified_hash != original_hash, "Modified data should have different hash"
                assert verify_hash(modified_dict, original_hash) == False, "Original hash should not verify modified data"