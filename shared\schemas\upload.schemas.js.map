{"version": 3, "file": "upload.schemas.js", "sourceRoot": "", "sources": ["upload.schemas.ts"], "names": [], "mappings": ";;;AAAA,6BAAwB;AACxB,qDAA4C;AAC5C,uDAAwD;AAExD,gBAAgB;AACH,QAAA,kBAAkB,GAAG,OAAC,CAAC,IAAI,CAAC;IACvC,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,OAAO;CAC7E,CAAC,CAAC;AAGH,uBAAuB;AACV,QAAA,uBAAuB,GAAG,OAAC,CAAC,IAAI,CAAC;IAC5C,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ;CACzE,CAAC,CAAC;AAGH,qBAAqB;AACR,QAAA,oBAAoB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC3C,EAAE,EAAE,yBAAQ;IACZ,OAAO,EAAE,yBAAQ;IACjB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAC3B,iBAAiB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IACpC,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE;IACzC,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE;IACrB,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE;IAEvB,gBAAgB;IAChB,MAAM,EAAE,qCAAmB,CAAC,QAAQ,EAAE;IACtC,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,EAAE,yBAAyB;IAE3D,oBAAoB;IACpB,MAAM,EAAE,0BAAkB;IAC1B,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;IAE/C,iBAAiB;IACjB,cAAc,EAAE,OAAC,CAAC,MAAM,CAAC,+BAAuB,CAAC,CAAC,QAAQ,EAAE;IAE5D,qBAAqB;IACrB,iBAAiB,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IACjD,YAAY,EAAE,OAAC,CAAC,MAAM,CAAC;QACrB,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE;QAC1C,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE;QAC1C,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE;QAC5C,UAAU,EAAE,OAAC,CAAC,MAAM,CAAC;YACnB,KAAK,EAAE,OAAC,CAAC,IAAI,EAAE;YACf,GAAG,EAAE,OAAC,CAAC,IAAI,EAAE;SACd,CAAC,CAAC,QAAQ,EAAE;QACb,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,8BAA8B;KACvE,CAAC,CAAC,QAAQ,EAAE;IAEb,oBAAoB;IACpB,aAAa,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACpC,aAAa,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IAE7C,aAAa;IACb,UAAU,EAAE,OAAC,CAAC,IAAI,EAAE;IACpB,UAAU,EAAE,OAAC,CAAC,IAAI,EAAE;IACpB,YAAY,EAAE,OAAC,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;CAClC,CAAC,CAAC;AAGH,iBAAiB;AACJ,QAAA,yBAAyB,GAAG,OAAC,CAAC,MAAM,CAAC;IAChD,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAC3B,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;IACtC,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE;IACrB,MAAM,EAAE,qCAAmB,CAAC,QAAQ,EAAE;IACtC,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CACjC,CAAC,CAAC;AAGH,yBAAyB;AACZ,QAAA,0BAA0B,GAAG,OAAC,CAAC,MAAM,CAAC;IACjD,SAAS,EAAE,yBAAQ;IACnB,OAAO,EAAE,OAAC,CAAC,MAAM,CAAC,+BAAuB,CAAC;IAC1C,MAAM,EAAE,qCAAmB;IAC3B,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE;CACtB,CAAC,CAAC;AAGH,4CAA4C;AAC/B,QAAA,iBAAiB,GAAG,OAAC,CAAC,MAAM,CAAC;IACxC,OAAO,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC;IAC5B,WAAW,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC;IACzC,kBAAkB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACzC,iBAAiB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACxC,cAAc,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE;CAC/C,CAAC,CAAC;AAGH,qBAAqB;AACR,QAAA,sBAAsB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC7C,MAAM,EAAE,qCAAmB;IAC3B,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE;IACrB,IAAI,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,CAAC;QACrB,SAAS,EAAE,OAAC,CAAC,IAAI,EAAE;QACnB,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QAC3B,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QAC3B,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QAC1B,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QAC5B,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,WAAW,EAAE,CAAC,QAAQ,EAAE;QAC3C,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;QACrC,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;KACtC,CAAC,CAAC;IACH,QAAQ,EAAE,OAAC,CAAC,MAAM,CAAC;QACjB,aAAa,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE;QAC7C,UAAU,EAAE,OAAC,CAAC,MAAM,CAAC;YACnB,KAAK,EAAE,OAAC,CAAC,IAAI,EAAE;YACf,GAAG,EAAE,OAAC,CAAC,IAAI,EAAE;SACd,CAAC;QACF,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACtC,IAAI,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,CAAC;YACrB,KAAK,EAAE,OAAC,CAAC,IAAI,EAAE;YACf,GAAG,EAAE,OAAC,CAAC,IAAI,EAAE;YACb,gBAAgB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;SAC9C,CAAC,CAAC,CAAC,QAAQ,EAAE;KACf,CAAC;CACH,CAAC,CAAC;AAGH,gCAAgC;AACnB,QAAA,iCAAiC,GAAG,OAAC,CAAC,MAAM,CAAC;IACxD,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE;IAC7B,SAAS,EAAE,yBAAQ;IACnB,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE;IACrB,cAAc,EAAE,OAAC,CAAC,MAAM,CAAC,+BAAuB,CAAC;IACjD,MAAM,EAAE,qCAAmB;IAC3B,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE;IACrB,iBAAiB,EAAE,OAAC,CAAC,MAAM,CAAC;QAC1B,YAAY,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;QACvC,eAAe,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC;QACzD,eAAe,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;QACxD,gBAAgB,EAAE,OAAC,CAAC,MAAM,CAAC;YACzB,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC;YAChD,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC;YACjD,gBAAgB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,MAAM;SAChE,CAAC;KACH,CAAC;CACH,CAAC,CAAC;AAGU,QAAA,kCAAkC,GAAG,OAAC,CAAC,MAAM,CAAC;IACzD,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE;IAC7B,OAAO,EAAE,OAAC,CAAC,OAAO,EAAE;IACpB,IAAI,EAAE,8BAAsB,CAAC,QAAQ,EAAE;IACvC,iBAAiB,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IACjD,QAAQ,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IACxC,gBAAgB,EAAE,OAAC,CAAC,MAAM,CAAC;QACzB,cAAc,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE;QAC9C,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE;QAC1C,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE;QAC5C,kBAAkB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,WAAW,EAAE;KAC7C,CAAC;IACF,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CAC7B,CAAC,CAAC;AAGH,0BAA0B;AACb,QAAA,oBAAoB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC3C,SAAS,EAAE,yBAAQ;IACnB,MAAM,EAAE,0BAAkB;IAC1B,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;IACpC,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC9B,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACnC,oBAAoB,EAAE,OAAC,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;CAC1C,CAAC,CAAC;AAGH,wBAAwB;AACX,QAAA,wBAAwB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC/C,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE;IAChB,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE;IACvB,SAAS,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;IAClE,UAAU,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,GAAG,EAAE,CAAC;IAC7B,QAAQ,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;CAC/C,CAAC,CAAC;AAGU,QAAA,0BAA0B,GAAG,OAAC,CAAC,MAAM,CAAC;IACjD,IAAI,EAAE,gCAAwB;IAC9B,MAAM,EAAE,OAAC,CAAC,OAAO,EAAE;IACnB,aAAa,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE;IAC7C,OAAO,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,CAAC;QACxB,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;QACvC,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QAC7B,KAAK,EAAE,OAAC,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;QACzB,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE;KACpB,CAAC,CAAC,CAAC,QAAQ,EAAE;CACf,CAAC,CAAC;AAGH,iCAAiC;AACpB,QAAA,uBAAuB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC9C,KAAK,EAAE,OAAC,CAAC,KAAK,CAAC,iCAAyB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;IACxD,cAAc,EAAE,qCAAmB,CAAC,QAAQ,EAAE;IAC9C,iBAAiB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACxC,iBAAiB,EAAE,OAAC,CAAC,MAAM,CAAC;QAC1B,mBAAmB,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;QAC9C,gBAAgB,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC;QAC3E,YAAY,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;KACxC,CAAC;CACH,CAAC,CAAC;AAGU,QAAA,wBAAwB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC/C,QAAQ,EAAE,yBAAQ;IAClB,OAAO,EAAE,OAAC,CAAC,KAAK,CAAC,4BAAoB,CAAC;IACtC,OAAO,EAAE,OAAC,CAAC,MAAM,CAAC;QAChB,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE;QAC3C,kBAAkB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE;QAClD,cAAc,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE;QAC9C,iBAAiB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE;KAClD,CAAC;CACH,CAAC,CAAC"}