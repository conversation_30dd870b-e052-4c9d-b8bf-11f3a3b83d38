{"name": "RSI Scalping Strategy", "description": "Simple RSI-based scalping strategy for testing", "symbol": "EURUSD", "timeframe": "M15", "indicators": {"RSI": {"period": 14, "overbought": 70, "oversold": 30}}, "entry_conditions": ["RSI crosses below 30 (oversold)", "Price above 20-period MA"], "exit_conditions": ["RSI crosses above 70 (overbought)", "Stop loss at 50 pips", "Take profit at 100 pips"], "risk_management": {"position_size": 0.1, "stop_loss_pips": 50, "take_profit_pips": 100, "max_spread": 3}, "time_filters": {"start_hour": 8, "end_hour": 18, "trading_days": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]}}