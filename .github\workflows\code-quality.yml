name: Code Quality

on:
  push:
    branches: [ "main", "develop" ]
  pull_request:
    branches: [ "main", "develop" ]

jobs:
  code-quality:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install black isort flake8 mypy bandit
        if [ -f requirements-dev.txt ]; then pip install -r requirements-dev.txt; fi
    
    - name: Check code formatting with Black
      run: |
        black --check --diff src/ tests/
    
    - name: Check import sorting with isort
      run: |
        isort --check-only --diff src/ tests/
    
    - name: Lint with flake8
      run: |
        flake8 src/ tests/ --max-line-length=88 --extend-ignore=E203,W503
    
    - name: Type checking with mypy
      run: |
        mypy src/ --ignore-missing-imports
    
    - name: Security analysis with bandit
      run: |
        bandit -r src/ -ll
    
    - name: Check for common security issues
      run: |
        python -m pip check
        safety check --json || true

  documentation:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install sphinx sphinx-rtd-theme
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
    
    - name: Build documentation
      run: |
        if [ -d "docs" ]; then
          cd docs
          make html
        else
          echo "No docs directory found, skipping documentation build"
        fi
    
    - name: Check for broken links in documentation
      run: |
        if [ -d "docs" ]; then
          cd docs
          make linkcheck || echo "Link check completed with warnings"
        fi