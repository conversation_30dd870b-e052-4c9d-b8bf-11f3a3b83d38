/**
 * API testing helpers for making HTTP requests in tests
 */

export interface ApiTestConfig {
  baseUrl?: string;
  timeout?: number;
  headers?: Record<string, string>;
}

export interface ApiResponse<T = any> {
  status: number;
  data: T;
  headers: Record<string, string>;
}

export class ApiTestClient {
  private config: ApiTestConfig;

  constructor(config: ApiTestConfig = {}) {
    this.config = {
      baseUrl: 'http://localhost:3000',
      timeout: 5000,
      headers: {
        'Content-Type': 'application/json',
      },
      ...config,
    };
  }

  async get<T = any>(path: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    // Mock GET request
    return {
      status: 200,
      data: {} as T,
      headers: { ...this.config.headers, ...headers },
    };
  }

  async post<T = any>(
    path: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    // Mock POST request
    return {
      status: 201,
      data: {} as T,
      headers: { ...this.config.headers, ...headers },
    };
  }

  async put<T = any>(
    path: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    // Mock PUT request
    return {
      status: 200,
      data: {} as T,
      headers: { ...this.config.headers, ...headers },
    };
  }

  async delete<T = any>(path: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    // Mock DELETE request
    return {
      status: 204,
      data: {} as T,
      headers: { ...this.config.headers, ...headers },
    };
  }
}

export const createApiTestClient = (config?: ApiTestConfig): ApiTestClient => {
  return new ApiTestClient(config);
};

export const expectApiResponse = {
  success: <T>(response: ApiResponse<T>): void => {
    expect(response.status).toBeGreaterThanOrEqual(200);
    expect(response.status).toBeLessThan(300);
  },

  error: (response: ApiResponse, expectedStatus?: number): void => {
    expect(response.status).toBeGreaterThanOrEqual(400);
    if (expectedStatus) {
      expect(response.status).toBe(expectedStatus);
    }
  },

  created: <T>(response: ApiResponse<T>): void => {
    expect(response.status).toBe(201);
    expect(response.data).toBeDefined();
  },

  noContent: (response: ApiResponse): void => {
    expect(response.status).toBe(204);
  },
};