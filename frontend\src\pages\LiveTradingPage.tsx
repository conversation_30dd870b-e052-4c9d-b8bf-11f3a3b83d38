/**
 * Live Trading Page
 * Interface for live trading with MT5 Bridge
 */

import { useState } from 'react';
import { Activity, Play, ArrowLeft, Settings, TrendingUp } from 'lucide-react';
import { MT5BridgePanel } from '../components/trading/MT5BridgePanel';

export function LiveTradingPage() {
  const [activeStrategy, setActiveStrategy] = useState<string | null>(null);
  
  // Sample strategies for the MVP
  const strategies = [
    {
      id: 'rsi-strategy',
      name: 'RSI Strategy',
      description: 'Buy when RSI is oversold, sell when overbought',
      parameters: {
        rsiPeriod: 14,
        oversold: 30,
        overbought: 70,
        stopLoss: 0.02,
        takeProfit: 0.04
      }
    },
    {
      id: 'macd-strategy',
      name: 'MACD Strategy',
      description: 'Buy on MACD crossover, sell on crossunder',
      parameters: {
        fastPeriod: 12,
        slowPeriod: 26,
        signalPeriod: 9,
        stopLoss: 0.02,
        takeProfit: 0.03
      }
    },
    {
      id: 'ma-strategy',
      name: 'Moving Average Strategy',
      description: 'Buy when price crosses above MA, sell when below',
      parameters: {
        fastMAPeriod: 10,
        slowMAPeriod: 30,
        stopLoss: 0.015,
        takeProfit: 0.025
      }
    }
  ];

  const handleOrderPlaced = (orderId: number) => {
    console.log(`Order placed: ${orderId}`);
    // You could show a notification or update a list of orders here
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Live Trading</h1>
          <p className="text-gray-600 mt-2">
            Execute trades in real-time using the MT5 Bridge
          </p>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Strategies Panel */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow p-6 space-y-4">
            <div className="flex items-center space-x-2">
              <Settings className="w-5 h-5 text-blue-600" />
              <h3 className="text-lg font-medium text-gray-900">Trading Strategies</h3>
            </div>
            
            <div className="space-y-3">
              {strategies.map(strategy => (
                <div 
                  key={strategy.id}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    activeStrategy === strategy.id 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:border-blue-300'
                  }`}
                  onClick={() => setActiveStrategy(strategy.id)}
                >
                  <h4 className="font-medium text-gray-900">{strategy.name}</h4>
                  <p className="text-sm text-gray-600 mt-1">{strategy.description}</p>
                  
                  {activeStrategy === strategy.id && (
                    <div className="mt-3 pt-3 border-t border-gray-200">
                      <h5 className="text-xs font-medium text-gray-700 mb-2">Parameters</h5>
                      <div className="grid grid-cols-2 gap-2 text-xs">
                        {Object.entries(strategy.parameters).map(([key, value]) => (
                          <div key={key} className="flex justify-between">
                            <span className="text-gray-600">{key}:</span>
                            <span className="font-medium">{value}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
            
            <div className="pt-4 border-t border-gray-200">
              <button
                disabled={!activeStrategy}
                className={`w-full py-2 px-4 rounded-lg font-medium ${
                  !activeStrategy
                    ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                    : 'bg-green-600 text-white hover:bg-green-700'
                }`}
              >
                <Play className="w-4 h-4 inline-block mr-2" />
                Start Trading
              </button>
            </div>
          </div>
        </div>
        
        {/* MT5 Bridge Panel */}
        <div className="lg:col-span-2">
          <MT5BridgePanel onOrderPlaced={handleOrderPlaced} />
        </div>
      </div>
    </div>
  );
}