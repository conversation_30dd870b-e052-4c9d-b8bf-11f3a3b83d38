#!/usr/bin/env python3
"""
Comprehensive tests for Historical Data Integrity Pipeline
Test-driven development for zero-hallucination data validation.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timezone, timedelta
import tempfile
import shutil
from pathlib import Path
import sqlite3
import json

# Import the modules to test
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src', 'data'))

from data_loader import (
    ForexDataLoader, DataValidator, DataHashManager, OHLCValidationRules,
    DataIntegrityError, ValidationLevel, DataSource, DataIntegrityReport,
    create_forex_data_loader
)


class TestOHLCValidationRules:
    """Test OHLC validation rules"""
    
    def test_validation_rules_creation(self):
        """Test creation of validation rules"""
        rules = OHLCValidationRules()
        
        assert rules.min_price == 0.0001
        assert rules.max_price == 100000.0
        assert rules.max_spread_ratio == 0.1
        assert rules.max_gap_ratio == 0.2
    
    def test_price_range_validation(self):
        """Test price range validation"""
        rules = OHLCValidationRules()
        
        assert rules.validate_price_range(1.1234) is True
        assert rules.validate_price_range(0.0001) is True
        assert rules.validate_price_range(100000.0) is True
        assert rules.validate_price_range(0.0) is False
        assert rules.validate_price_range(100001.0) is False
        assert rules.validate_price_range(-1.0) is False
    
    def test_ohlc_relationship_validation(self):
        """Test OHLC price relationship validation"""
        rules = OHLCValidationRules()
        
        # Valid OHLC
        assert rules.validate_ohlc_relationship(1.1000, 1.1050, 1.0950, 1.1020) is True
        
        # Invalid: high < low
        assert rules.validate_ohlc_relationship(1.1000, 1.0950, 1.1050, 1.1020) is False
        
        # Invalid: open > high
        assert rules.validate_ohlc_relationship(1.1100, 1.1050, 1.0950, 1.1020) is False
        
        # Invalid: close < low
        assert rules.validate_ohlc_relationship(1.1000, 1.1050, 1.0950, 1.0900) is False
    
    def test_spread_validation(self):
        """Test spread validation"""
        rules = OHLCValidationRules()
        
        # Normal spread (1% of high price)
        assert rules.validate_spread(1.1000, 1.0890) is True
        
        # Large spread (15% of high price)
        assert rules.validate_spread(1.1000, 0.9350) is False
        
        # Zero high price
        assert rules.validate_spread(0.0, 1.0000) is False


class TestDataHashManager:
    """Test cryptographic hash management"""
    
    def test_hash_manager_creation(self):
        """Test hash manager creation"""
        manager = DataHashManager()
        assert manager.secret_key is not None
        
        custom_manager = DataHashManager("custom_key")
        assert custom_manager.secret_key == b"custom_key"
    
    def test_data_hash_calculation(self):
        """Test data hash calculation"""
        manager = DataHashManager()
        
        # Create test data
        data = pd.DataFrame({
            'open': [1.1000, 1.1010],
            'high': [1.1020, 1.1030],
            'low': [1.0980, 1.0990],
            'close': [1.1010, 1.1020]
        }, index=pd.date_range('2023-01-01', periods=2, freq='1H'))
        
        hash1 = manager.calculate_data_hash(data)
        hash2 = manager.calculate_data_hash(data)
        
        # Hash should be consistent
        assert hash1 == hash2
        assert len(hash1) == 64  # SHA-256 hex length
    
    def test_hmac_signature_calculation(self):
        """Test HMAC signature calculation"""
        manager = DataHashManager()
        
        data = pd.DataFrame({
            'open': [1.1000],
            'high': [1.1020],
            'low': [1.0980],
            'close': [1.1010]
        }, index=pd.date_range('2023-01-01', periods=1, freq='1H'))
        
        signature1 = manager.calculate_hmac_signature(data)
        signature2 = manager.calculate_hmac_signature(data)
        
        assert signature1 == signature2
        assert len(signature1) == 64  # SHA-256 hex length
    
    def test_data_integrity_verification(self):
        """Test data integrity verification"""
        manager = DataHashManager()
        
        data = pd.DataFrame({
            'open': [1.1000],
            'high': [1.1020],
            'low': [1.0980],
            'close': [1.1010]
        }, index=pd.date_range('2023-01-01', periods=1, freq='1H'))
        
        hash_val = manager.calculate_data_hash(data)
        hmac_val = manager.calculate_hmac_signature(data)
        
        # Should verify successfully
        assert manager.verify_data_integrity(data, hash_val, hmac_val) is True
        
        # Should fail with wrong hash
        assert manager.verify_data_integrity(data, "wrong_hash", hmac_val) is False
        
        # Should fail with wrong HMAC
        assert manager.verify_data_integrity(data, hash_val, "wrong_hmac") is False
    
    def test_dataframe_to_string_consistency(self):
        """Test DataFrame to string conversion consistency"""
        manager = DataHashManager()
        
        # Create data with different row orders
        data1 = pd.DataFrame({
            'open': [1.1000, 1.1010],
            'high': [1.1020, 1.1030],
            'low': [1.0980, 1.0990],
            'close': [1.1010, 1.1020]
        }, index=pd.date_range('2023-01-01', periods=2, freq='1H'))
        
        data2 = data1.iloc[::-1].copy()  # Reverse order
        
        # Should produce same string after sorting
        str1 = manager._dataframe_to_string(data1)
        str2 = manager._dataframe_to_string(data2)
        
        assert str1 == str2


class TestDataValidator:
    """Test data validation functionality"""
    
    @pytest.fixture
    def valid_data(self):
        """Create valid OHLC data for testing"""
        dates = pd.date_range('2023-01-01', periods=100, freq='1H')
        
        # Generate realistic price data
        np.random.seed(42)
        base_price = 1.1000
        returns = np.random.normal(0, 0.001, len(dates))
        prices = base_price * np.exp(np.cumsum(returns))
        
        data = []
        for i, (date, price) in enumerate(zip(dates, prices)):
            volatility = 0.0005
            open_price = price * (1 + np.random.normal(0, volatility))
            close_price = price
            high_price = max(open_price, close_price) * (1 + abs(np.random.normal(0, volatility)))
            low_price = min(open_price, close_price) * (1 - abs(np.random.normal(0, volatility)))
            
            data.append({
                'open': round(open_price, 5),
                'high': round(high_price, 5),
                'low': round(low_price, 5),
                'close': round(close_price, 5),
                'volume': int(np.random.lognormal(10, 1))
            })
        
        return pd.DataFrame(data, index=dates)
    
    @pytest.fixture
    def invalid_data(self):
        """Create invalid OHLC data for testing"""
        dates = pd.date_range('2023-01-01', periods=10, freq='1H')
        
        data = []
        for date in dates:
            data.append({
                'open': 1.1000,
                'high': 1.0900,  # Invalid: high < low
                'low': 1.1100,
                'close': 1.1050,
                'volume': 1000
            })
        
        return pd.DataFrame(data, index=dates)
    
    def test_validator_creation(self):
        """Test validator creation with different levels"""
        validator = DataValidator(ValidationLevel.BASIC)
        assert validator.validation_level == ValidationLevel.BASIC
        
        validator = DataValidator(ValidationLevel.CRYPTOGRAPHIC)
        assert validator.validation_level == ValidationLevel.CRYPTOGRAPHIC
    
    def test_basic_validation_valid_data(self, valid_data):
        """Test basic validation with valid data"""
        validator = DataValidator(ValidationLevel.BASIC)
        report = validator.validate_data(valid_data, "EURUSD")
        
        assert report.symbol == "EURUSD"
        assert report.validation_level == ValidationLevel.BASIC
        assert report.total_records == len(valid_data)
        assert report.is_valid() is True
        assert report.integrity_score > 0.9
    
    def test_basic_validation_invalid_data(self, invalid_data):
        """Test basic validation with invalid data"""
        validator = DataValidator(ValidationLevel.BASIC)
        report = validator.validate_data(invalid_data, "EURUSD")
        
        assert report.is_valid() is False
        assert "price_relationships" in report.checks_failed
        assert report.integrity_score < 1.0
    
    def test_standard_validation(self, valid_data):
        """Test standard validation level"""
        validator = DataValidator(ValidationLevel.STANDARD)
        report = validator.validate_data(valid_data, "EURUSD")
        
        assert report.validation_level == ValidationLevel.STANDARD
        assert len(report.checks_passed) > 4  # More checks than basic
        assert report.is_valid() is True
    
    def test_strict_validation(self, valid_data):
        """Test strict validation level"""
        validator = DataValidator(ValidationLevel.STRICT)
        report = validator.validate_data(valid_data, "EURUSD")
        
        assert report.validation_level == ValidationLevel.STRICT
        assert len(report.checks_passed) > 8  # More checks than standard
        assert report.is_valid() is True
    
    def test_cryptographic_validation(self, valid_data):
        """Test cryptographic validation level"""
        validator = DataValidator(ValidationLevel.CRYPTOGRAPHIC)
        report = validator.validate_data(valid_data, "EURUSD")
        
        assert report.validation_level == ValidationLevel.CRYPTOGRAPHIC
        assert report.data_hash != ""
        assert report.hmac_signature != ""
        assert len(report.data_hash) == 64
        assert len(report.hmac_signature) == 64
    
    def test_ohlc_structure_validation(self, valid_data):
        """Test OHLC structure validation"""
        validator = DataValidator()
        
        # Valid structure
        assert validator._validate_ohlc_structure(valid_data) is True
        
        # Missing column
        invalid_data = valid_data.drop('high', axis=1)
        assert validator._validate_ohlc_structure(invalid_data) is False
    
    def test_price_relationships_validation(self, valid_data, invalid_data):
        """Test price relationships validation"""
        validator = DataValidator()
        
        assert validator._validate_price_relationships(valid_data) == True
        assert validator._validate_price_relationships(invalid_data) == False
    
    def test_null_values_validation(self, valid_data):
        """Test null values validation"""
        validator = DataValidator()
        
        # Valid data
        assert validator._validate_no_nulls(valid_data) is True
        
        # Data with nulls
        data_with_nulls = valid_data.copy()
        data_with_nulls.loc[data_with_nulls.index[0], 'close'] = np.nan
        assert validator._validate_no_nulls(data_with_nulls) is False
    
    def test_monotonic_index_validation(self, valid_data):
        """Test monotonic index validation"""
        validator = DataValidator()
        
        # Valid monotonic index
        assert validator._validate_monotonic_index(valid_data) is True
        
        # Non-monotonic index
        shuffled_data = valid_data.sample(frac=1)
        assert validator._validate_monotonic_index(shuffled_data) is False
    
    def test_price_ranges_validation(self, valid_data):
        """Test price ranges validation"""
        validator = DataValidator()
        
        # Valid ranges
        assert validator._validate_price_ranges(valid_data) is True
        
        # Invalid range (negative price)
        invalid_data = valid_data.copy()
        invalid_data.loc[invalid_data.index[0], 'low'] = -1.0
        assert validator._validate_price_ranges(invalid_data) is False
    
    def test_spreads_validation(self, valid_data):
        """Test spreads validation"""
        validator = DataValidator()
        
        # Valid spreads
        assert validator._validate_spreads(valid_data) == True
        
        # Invalid spread (too large)
        invalid_data = valid_data.copy()
        invalid_data.loc[invalid_data.index[0], 'low'] = 0.5  # 50% spread
        assert validator._validate_spreads(invalid_data) is False
    
    def test_volume_validation(self, valid_data):
        """Test volume validation"""
        validator = DataValidator()
        
        # Valid volume
        assert validator._validate_volume(valid_data) == True
        
        # Invalid volume (negative)
        invalid_data = valid_data.copy()
        invalid_data.loc[invalid_data.index[0], 'volume'] = -1000
        assert validator._validate_volume(invalid_data) is False
        
        # No volume column (should pass)
        no_volume_data = valid_data.drop('volume', axis=1)
        assert validator._validate_volume(no_volume_data) is True
    
    def test_duplicates_validation(self, valid_data):
        """Test duplicates validation"""
        validator = DataValidator()
        
        # No duplicates
        assert validator._validate_duplicates(valid_data) is True
        
        # With duplicates
        duplicate_data = pd.concat([valid_data, valid_data.iloc[:1]])
        assert validator._validate_duplicates(duplicate_data) is False
    
    def test_thread_safety(self, valid_data):
        """Test validator thread safety"""
        import threading
        import time
        
        validator = DataValidator()
        results = []
        
        def validate_data():
            report = validator.validate_data(valid_data, "EURUSD")
            results.append(report.is_valid())
        
        # Run multiple validations concurrently
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=validate_data)
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        # All should succeed
        assert all(results)
        assert len(results) == 5


class TestDataIntegrityReport:
    """Test data integrity report functionality"""
    
    def test_report_creation(self):
        """Test integrity report creation"""
        report = DataIntegrityReport(
            symbol="EURUSD",
            validation_level=ValidationLevel.STANDARD,
            total_records=100,
            validation_timestamp=datetime.now(timezone.utc)
        )
        
        assert report.symbol == "EURUSD"
        assert report.validation_level == ValidationLevel.STANDARD
        assert report.total_records == 100
        assert report.is_valid() is True  # No failed checks
    
    def test_report_validation_status(self):
        """Test report validation status"""
        report = DataIntegrityReport(
            symbol="EURUSD",
            validation_level=ValidationLevel.BASIC,
            total_records=100,
            validation_timestamp=datetime.now(timezone.utc)
        )
        
        # Initially valid
        assert report.is_valid() is True
        
        # Add failed check
        report.checks_failed.append("test_failure")
        assert report.is_valid() is False
    
    def test_report_summary(self):
        """Test report summary generation"""
        report = DataIntegrityReport(
            symbol="EURUSD",
            validation_level=ValidationLevel.STANDARD,
            total_records=100,
            validation_timestamp=datetime.now(timezone.utc),
            checks_passed=["check1", "check2"],
            checks_failed=["check3"],
            integrity_score=0.67
        )
        
        summary = report.get_summary()
        
        assert summary['symbol'] == "EURUSD"
        assert summary['validation_level'] == "standard"
        assert summary['total_records'] == 100
        assert summary['checks_passed'] == 2
        assert summary['checks_failed'] == 1
        assert summary['integrity_score'] == 0.67
        assert summary['is_valid'] is False


class TestForexDataLoader:
    """Test forex data loader functionality"""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for testing"""
        temp_dir = tempfile.mkdtemp()
        yield Path(temp_dir)
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def loader(self, temp_dir):
        """Create data loader for testing"""
        loader = ForexDataLoader(
            data_source=DataSource.MOCK,
            validation_level=ValidationLevel.STANDARD,
            cache_enabled=True
        )
        loader.cache_dir = temp_dir
        loader._setup_database()
        return loader
    
    def test_loader_creation(self):
        """Test data loader creation"""
        loader = ForexDataLoader()
        
        assert loader.data_source == DataSource.MOCK
        assert loader.validator.validation_level == ValidationLevel.STANDARD
        assert loader.cache_enabled is True
    
    def test_load_single_pair(self, loader):
        """Test loading single currency pair"""
        data, report = loader.load_pair("EURUSD")
        
        assert isinstance(data, pd.DataFrame)
        assert isinstance(report, DataIntegrityReport)
        assert len(data) > 0
        assert report.symbol == "EURUSD"
        assert report.is_valid() is True
        
        # Check OHLC columns
        required_columns = ['open', 'high', 'low', 'close']
        assert all(col in data.columns for col in required_columns)
    
    def test_load_pair_with_date_range(self, loader):
        """Test loading pair with specific date range"""
        start_date = datetime(2023, 1, 1, tzinfo=timezone.utc)
        end_date = datetime(2023, 1, 7, tzinfo=timezone.utc)
        
        data, report = loader.load_pair("EURUSD", start_date, end_date)
        
        assert len(data) > 0
        assert data.index[0] >= start_date
        assert data.index[-1] <= end_date
        assert report.is_valid() is True
    
    def test_load_multiple_pairs(self, loader):
        """Test loading multiple currency pairs"""
        pairs = ["EURUSD", "GBPUSD", "USDJPY"]
        results = loader.load_multiple_pairs(pairs)
        
        assert len(results) == 3
        
        for pair in pairs:
            assert pair in results
            data, report = results[pair]
            assert data is not None
            assert report is not None
            assert report.symbol == pair
            assert len(data) > 0
    
    def test_mock_data_generation(self, loader):
        """Test mock data generation"""
        start_date = datetime(2023, 1, 1, tzinfo=timezone.utc)
        end_date = datetime(2023, 1, 2, tzinfo=timezone.utc)
        
        data = loader._generate_mock_data("EURUSD", start_date, end_date, "1H")
        
        assert isinstance(data, pd.DataFrame)
        assert len(data) > 0
        assert data.attrs['symbol'] == "EURUSD"
        assert data.attrs['timeframe'] == "1H"
        
        # Check OHLC relationships
        assert (data['high'] >= data['low']).all()
        assert (data['high'] >= data['open']).all()
        assert (data['high'] >= data['close']).all()
        assert (data['low'] <= data['open']).all()
        assert (data['low'] <= data['close']).all()
    
    def test_data_validation_failure(self, loader):
        """Test handling of data validation failure"""
        # Mock the validator to always fail
        original_validate = loader.validator.validate_data
        
        def mock_validate(data, symbol):
            report = DataIntegrityReport(
                symbol=symbol,
                validation_level=ValidationLevel.STANDARD,
                total_records=len(data),
                validation_timestamp=datetime.now(timezone.utc),
                checks_failed=["mock_failure"]
            )
            return report
        
        loader.validator.validate_data = mock_validate
        
        with pytest.raises(DataIntegrityError):
            loader.load_pair("EURUSD")
        
        # Restore original method
        loader.validator.validate_data = original_validate
    
    def test_cache_functionality(self, loader):
        """Test data caching functionality"""
        # First load should generate data
        data1, report1 = loader.load_pair("EURUSD")
        
        # Second load should use cache (if implemented)
        data2, report2 = loader.load_pair("EURUSD")
        
        assert len(data1) == len(data2)
        assert report1.symbol == report2.symbol
    
    def test_database_storage(self, loader):
        """Test database storage functionality"""
        data, report = loader.load_pair("EURUSD")
        
        # Check if data was stored in database
        with sqlite3.connect(loader.db_path) as conn:
            cursor = conn.execute("SELECT COUNT(*) FROM forex_data WHERE symbol = ?", ("EURUSD",))
            count = cursor.fetchone()[0]
            assert count > 0
            
            cursor = conn.execute("SELECT COUNT(*) FROM integrity_reports WHERE symbol = ?", ("EURUSD",))
            report_count = cursor.fetchone()[0]
            assert report_count > 0
    
    def test_integrity_report_retrieval(self, loader):
        """Test integrity report retrieval"""
        # Load data first
        data, original_report = loader.load_pair("EURUSD")
        
        # Retrieve report
        retrieved_report = loader.get_integrity_report("EURUSD")
        
        assert retrieved_report is not None
        assert retrieved_report.symbol == "EURUSD"
        assert retrieved_report.validation_level == ValidationLevel.STANDARD
    
    def test_different_timeframes(self, loader):
        """Test loading different timeframes"""
        timeframes = ["1M", "5M", "1H", "1D"]
        
        for timeframe in timeframes:
            data, report = loader.load_pair("EURUSD", timeframe=timeframe)
            
            assert len(data) > 0
            assert data.attrs['timeframe'] == timeframe
            assert report.is_valid() is True
    
    def test_data_corrections(self, loader):
        """Test data corrections functionality"""
        # Create data with issues that can be corrected
        dates = pd.date_range('2023-01-01', periods=5, freq='1H')
        data = pd.DataFrame({
            'open': [1.1000, 1.1010, 1.1020, 1.1030, 1.1040],
            'high': [1.1020, 1.1030, 1.1040, 1.1050, 1.1060],
            'low': [1.0980, 1.0990, 1.1000, 1.1010, 1.1020],
            'close': [1.1010, 1.1020, 1.1030, 1.1040, 1.1050]
        }, index=dates[::-1])  # Reverse order
        
        corrected_data = loader._apply_data_corrections(data)
        
        # Should be sorted
        assert corrected_data.index.is_monotonic_increasing
        
        # Should have same length
        assert len(corrected_data) == len(data)


class TestFactoryFunction:
    """Test factory function"""
    
    def test_create_forex_data_loader(self):
        """Test factory function"""
        loader = create_forex_data_loader()
        
        assert isinstance(loader, ForexDataLoader)
        assert loader.data_source == DataSource.MOCK
        assert loader.validator.validation_level == ValidationLevel.STANDARD
        assert loader.cache_enabled is True
    
    def test_create_forex_data_loader_custom(self):
        """Test factory function with custom parameters"""
        loader = create_forex_data_loader(
            validation_level=ValidationLevel.STRICT,
            data_source=DataSource.CSV,
            cache_enabled=False
        )
        
        assert loader.data_source == DataSource.CSV
        assert loader.validator.validation_level == ValidationLevel.STRICT
        assert loader.cache_enabled is False


class TestIntegrationScenarios:
    """Integration tests for complete workflows"""
    
    def test_complete_data_pipeline(self):
        """Test complete data loading and validation pipeline"""
        # Create loader with strict validation
        loader = create_forex_data_loader(
            validation_level=ValidationLevel.STRICT,
            data_source=DataSource.MOCK
        )
        
        # Load data
        data, report = loader.load_pair("EURUSD")
        
        # Verify complete pipeline
        assert isinstance(data, pd.DataFrame)
        assert len(data) > 0
        assert report.is_valid() is True
        assert report.integrity_score > 0.8
        
        # Check data quality
        assert (data['high'] >= data['low']).all()
        assert not data.isnull().any().any()
        assert data.index.is_monotonic_increasing
    
    def test_multi_pair_validation_consistency(self):
        """Test validation consistency across multiple pairs"""
        loader = create_forex_data_loader(ValidationLevel.STANDARD)
        
        pairs = ["EURUSD", "GBPUSD", "USDJPY", "USDCHF"]
        results = loader.load_multiple_pairs(pairs)
        
        # All pairs should validate successfully
        for pair, (data, report) in results.items():
            assert data is not None
            assert report.is_valid() is True
            assert report.integrity_score > 0.7
            assert len(data) > 0
    
    def test_validation_level_progression(self):
        """Test different validation levels on same data"""
        levels = [
            ValidationLevel.BASIC,
            ValidationLevel.STANDARD,
            ValidationLevel.STRICT,
            ValidationLevel.CRYPTOGRAPHIC
        ]
        
        results = {}
        
        for level in levels:
            loader = create_forex_data_loader(validation_level=level)
            data, report = loader.load_pair("EURUSD")
            results[level] = (data, report)
        
        # All should pass validation
        for level, (data, report) in results.items():
            assert report.is_valid() is True
            assert len(data) > 0
        
        # Cryptographic level should have hash and signature
        crypto_report = results[ValidationLevel.CRYPTOGRAPHIC][1]
        assert crypto_report.data_hash != ""
        assert crypto_report.hmac_signature != ""
    
    def test_error_handling_and_recovery(self):
        """Test error handling and recovery mechanisms"""
        loader = create_forex_data_loader()
        
        # Test with invalid pair (should still work with mock data)
        try:
            data, report = loader.load_pair("INVALID")
            assert len(data) > 0  # Mock data should still be generated
        except DataIntegrityError:
            pass  # Expected for some validation failures
        
        # Test with extreme date ranges
        start_date = datetime(1990, 1, 1, tzinfo=timezone.utc)
        end_date = datetime(1990, 1, 2, tzinfo=timezone.utc)
        
        data, report = loader.load_pair("EURUSD", start_date, end_date)
        assert len(data) > 0
        assert report.is_valid() is True


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v", "--tb=short"])