#!/usr/bin/env python3
"""
Property-based tests for trading platform
Advanced testing with hypothesis for comprehensive edge case coverage
"""

import pytest
from hypothesis import given, strategies as st, settings, assume, example
from hypothesis.extra.pandas import data_frames
import pandas as pd
import numpy as np
from datetime import datetime, timezone, timedelta
import sys
import os

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

# Import generators
from tests.generators.financial_data import (
    generate_financial_data,
    generate_strategy_parameters,
    generate_market_conditions,
    generate_risk_parameters,
    generate_portfolio_data,
    generate_trade_sequence,
    generate_corrupted_data,
    currency_pairs,
    timeframes,
    validation_levels
)

# Import system components
try:
    from data.data_loader import (
        ForexDataLoader, DataValidator, DataHashManager, OHLCValidationRules,
        ValidationLevel, DataSource, DataIntegrityReport, create_forex_data_loader
    )
except ImportError:
    # Mock imports for testing
    class MockValidator:
        def __init__(self, level):
            self.validation_level = level
        
        def validate_data(self, data, symbol):
            return type('Report', (), {
                'is_valid': lambda: True,
                'integrity_score': 1.0,
                'checks_passed': ['mock_check'],
                'checks_failed': [],
                'total_records': len(data)
            })()


class TestPropertyBasedDataValidation:
    """Property-based tests for data validation components"""
    
    @given(data=generate_financial_data(min_records=10, max_records=100))
    @settings(max_examples=100, deadline=10000)
    def test_ohlc_relationships_always_valid(self, data):
        """Property: Generated OHLC data should always have valid relationships"""
        
        # Property 1: High >= Low for all records
        assert (data['high'] >= data['low']).all(), "High should always be >= Low"
        
        # Property 2: High >= Open and High >= Close
        assert (data['high'] >= data['open']).all(), "High should always be >= Open"
        assert (data['high'] >= data['close']).all(), "High should always be >= Close"
        
        # Property 3: Low <= Open and Low <= Close
        assert (data['low'] <= data['open']).all(), "Low should always be <= Open"
        assert (data['low'] <= data['close']).all(), "Low should always be <= Close"
        
        # Property 4: All prices should be positive
        assert (data['open'] > 0).all(), "Open prices should be positive"
        assert (data['high'] > 0).all(), "High prices should be positive"
        assert (data['low'] > 0).all(), "Low prices should be positive"
        assert (data['close'] > 0).all(), "Close prices should be positive"
        
        # Property 5: Volume should be non-negative
        if 'volume' in data.columns:
            assert (data['volume'] >= 0).all(), "Volume should be non-negative"
    
    @given(
        data=generate_financial_data(min_records=20, max_records=100),
        validation_level=st.sampled_from([
            ValidationLevel.BASIC,
            ValidationLevel.STANDARD,
            ValidationLevel.STRICT
        ])
    )
    @settings(max_examples=50, deadline=15000)
    def test_validation_consistency_property(self, data, validation_level):
        """Property: Validation should be consistent and deterministic"""
        
        try:
            validator = DataValidator(validation_level)
            
            # Run validation multiple times
            report1 = validator.validate_data(data, "TEST")
            report2 = validator.validate_data(data, "TEST")
            
            # Property: Results should be identical
            assert report1.is_valid() == report2.is_valid()
            assert report1.integrity_score == report2.integrity_score
            assert len(report1.checks_passed) == len(report2.checks_passed)
            assert len(report1.checks_failed) == len(report2.checks_failed)
            
            # Property: Integrity score should be between 0 and 1
            assert 0.0 <= report1.integrity_score <= 1.0
            
            # Property: If valid, integrity score should be 1.0
            if report1.is_valid():
                assert report1.integrity_score == 1.0
            
        except ImportError:
            # Skip test if components not available
            pytest.skip("Data validation components not available")
    
    @given(data=generate_financial_data(min_records=50, max_records=200))
    @settings(max_examples=30, deadline=20000)
    def test_hash_consistency_property(self, data):
        """Property: Hash calculation should be consistent and deterministic"""
        
        try:
            manager = DataHashManager()
            
            # Property 1: Same data produces same hash
            hash1 = manager.calculate_data_hash(data)
            hash2 = manager.calculate_data_hash(data)
            assert hash1 == hash2, "Same data should produce same hash"
            
            # Property 2: Hash should be 64 characters (SHA-256 hex)
            assert len(hash1) == 64, "SHA-256 hash should be 64 characters"
            assert all(c in '0123456789abcdef' for c in hash1), "Hash should be valid hex"
            
            # Property 3: Different data produces different hash
            if len(data) > 0:
                modified_data = data.copy()
                modified_data.iloc[0, 0] = modified_data.iloc[0, 0] + 0.0001
                hash3 = manager.calculate_data_hash(modified_data)
                assert hash1 != hash3, "Different data should produce different hash"
            
            # Property 4: HMAC signature should be consistent
            hmac1 = manager.calculate_hmac_signature(data)
            hmac2 = manager.calculate_hmac_signature(data)
            assert hmac1 == hmac2, "HMAC should be consistent"
            assert len(hmac1) == 64, "HMAC should be 64 characters"
            
            # Property 5: Integrity verification should work
            assert manager.verify_data_integrity(data, hash1, hmac1), "Integrity verification should pass"
            
        except ImportError:
            pytest.skip("Hash manager not available")
    
    @given(
        base_data=generate_financial_data(min_records=20, max_records=50),
        corruption_seed=st.integers(min_value=0, max_value=1000)
    )
    @settings(max_examples=30, deadline=15000)
    def test_corruption_detection_property(self, base_data, corruption_seed):
        """Property: Validation should detect data corruption"""
        
        np.random.seed(corruption_seed)
        
        try:
            # Generate corrupted data
            corrupted_data = generate_corrupted_data(base_data).example()
            
            validator = DataValidator(ValidationLevel.STRICT)
            
            # Validate original data
            original_report = validator.validate_data(base_data, "ORIGINAL")
            
            # Validate corrupted data
            corrupted_report = validator.validate_data(corrupted_data, "CORRUPTED")
            
            # Property: Corrupted data should have lower integrity score
            assert corrupted_report.integrity_score <= original_report.integrity_score
            
            # Property: If corruption is severe, validation should fail
            corruption_type = corrupted_data.attrs.get('corruption_type', 'unknown')
            
            if corruption_type in ['negative_prices', 'invalid_ohlc', 'extreme_values']:
                assert not corrupted_report.is_valid(), f"Should detect {corruption_type}"
            
        except (ImportError, AttributeError):
            pytest.skip("Validation components not available")


class TestPropertyBasedStrategyTesting:
    """Property-based tests for trading strategies"""
    
    @given(
        data=generate_financial_data(min_records=100, max_records=300),
        strategy_params=generate_strategy_parameters('RSI')
    )
    @settings(max_examples=30, deadline=30000)
    def test_rsi_strategy_properties(self, data, strategy_params):
        """Property: RSI strategy should have consistent behavior"""
        
        # Ensure we have enough data for the RSI period
        assume(len(data) >= strategy_params['period'] + 20)
        assume(strategy_params['overbought'] > strategy_params['oversold'])
        assume(strategy_params['overbought'] <= 100)
        assume(strategy_params['oversold'] >= 0)
        
        try:
            # Mock RSI strategy for testing
            class MockRSIStrategy:
                def __init__(self, **params):
                    self.params = params
                
                def generate_signals(self, data):
                    # Simple mock implementation
                    signals = []
                    for i in range(len(data)):
                        if i % 10 == 0:  # Generate some signals
                            signals.append({
                                'timestamp': data.index[i],
                                'signal': 'BUY' if i % 20 == 0 else 'SELL',
                                'strength': 0.5,
                                'price': data.iloc[i]['close']
                            })
                    return signals
            
            strategy = MockRSIStrategy(**strategy_params)
            signals = strategy.generate_signals(data)
            
            # Property 1: Signals should be within data timeframe
            if signals:
                signal_times = [s['timestamp'] for s in signals]
                assert min(signal_times) >= data.index[0]
                assert max(signal_times) <= data.index[-1]
            
            # Property 2: Signal strength should be reasonable
            for signal in signals:
                assert 0.0 <= signal['strength'] <= 1.0
                assert signal['signal'] in ['BUY', 'SELL']
                assert signal['price'] > 0
            
            # Property 3: Number of signals should be reasonable
            assert len(signals) <= len(data)  # Can't have more signals than data points
            
        except ImportError:
            pytest.skip("Strategy components not available")
    
    @given(
        data=generate_financial_data(min_records=100, max_records=300),
        strategy_params=generate_strategy_parameters('MACD')
    )
    @settings(max_examples=30, deadline=30000)
    def test_macd_strategy_properties(self, data, strategy_params):
        """Property: MACD strategy should have consistent behavior"""
        
        # Ensure we have enough data
        min_required = max(
            strategy_params['fast_period'],
            strategy_params['slow_period'],
            strategy_params['signal_period']
        ) + 20
        
        assume(len(data) >= min_required)
        assume(strategy_params['fast_period'] < strategy_params['slow_period'])
        
        try:
            # Mock MACD strategy
            class MockMACDStrategy:
                def __init__(self, **params):
                    self.params = params
                
                def calculate_macd(self, data):
                    # Simple mock MACD calculation
                    fast_ema = data['close'].ewm(span=self.params['fast_period']).mean()
                    slow_ema = data['close'].ewm(span=self.params['slow_period']).mean()
                    macd_line = fast_ema - slow_ema
                    signal_line = macd_line.ewm(span=self.params['signal_period']).mean()
                    histogram = macd_line - signal_line
                    
                    return macd_line, signal_line, histogram
            
            strategy = MockMACDStrategy(**strategy_params)
            macd_line, signal_line, histogram = strategy.calculate_macd(data)
            
            # Property 1: MACD components should be finite
            assert np.isfinite(macd_line.dropna()).all()
            assert np.isfinite(signal_line.dropna()).all()
            assert np.isfinite(histogram.dropna()).all()
            
            # Property 2: Histogram should equal MACD - Signal
            valid_indices = ~(macd_line.isna() | signal_line.isna() | histogram.isna())
            if valid_indices.any():
                diff = macd_line[valid_indices] - signal_line[valid_indices]
                np.testing.assert_array_almost_equal(
                    histogram[valid_indices].values,
                    diff.values,
                    decimal=10
                )
            
        except ImportError:
            pytest.skip("Strategy components not available")
    
    @given(
        portfolio=generate_portfolio_data(num_assets=3),
        risk_params=generate_risk_parameters()
    )
    @settings(max_examples=50, deadline=10000)
    def test_risk_management_properties(self, portfolio, risk_params):
        """Property: Risk management should enforce limits"""
        
        # Property 1: Total allocation should not exceed 100%
        total_allocation = portfolio['total_allocation']
        assert 0.0 <= total_allocation <= 1.0, "Total allocation should be between 0% and 100%"
        
        # Property 2: Individual position sizes should be reasonable
        for symbol, position in portfolio['positions'].items():
            assert position['size'] >= 0, "Position size should be non-negative"
            assert 0.0 <= position['weight'] <= 1.0, "Position weight should be between 0% and 100%"
        
        # Property 3: Cash should be non-negative
        assert portfolio['cash'] >= 0, "Cash should be non-negative"
        
        # Property 4: Portfolio value should equal positions + cash
        total_position_value = sum(pos['size'] for pos in portfolio['positions'].values())
        expected_total = total_position_value + portfolio['cash']
        assert abs(expected_total - portfolio['total_value']) < 0.01, "Portfolio value should balance"
        
        # Property 5: Risk parameters should be within reasonable bounds
        assert 0.0 < risk_params['max_position_size'] <= 1.0
        assert 0.0 < risk_params['stop_loss_pct'] <= 1.0
        assert 0.0 < risk_params['take_profit_pct'] <= 1.0
        assert 0.0 < risk_params['max_daily_loss'] <= 1.0
        assert 0.0 < risk_params['max_drawdown'] <= 1.0
    
    @given(trades=generate_trade_sequence(min_trades=5, max_trades=50))
    @settings(max_examples=30, deadline=10000)
    def test_trade_sequence_properties(self, trades):
        """Property: Trade sequences should have consistent properties"""
        
        # Property 1: Trades should be chronologically ordered
        timestamps = [trade['timestamp'] for trade in trades]
        assert timestamps == sorted(timestamps), "Trades should be chronologically ordered"
        
        # Property 2: All required fields should be present
        required_fields = ['timestamp', 'symbol', 'side', 'quantity', 'price']
        for trade in trades:
            for field in required_fields:
                assert field in trade, f"Trade should have {field} field"
        
        # Property 3: Trade values should be reasonable
        for trade in trades:
            assert trade['quantity'] > 0, "Trade quantity should be positive"
            assert trade['price'] > 0, "Trade price should be positive"
            assert trade['side'] in ['BUY', 'SELL'], "Trade side should be BUY or SELL"
            assert trade['commission'] >= 0, "Commission should be non-negative"
            assert 0 <= trade['slippage'] <= 1, "Slippage should be reasonable"
        
        # Property 4: PnL should be finite
        for trade in trades:
            assert np.isfinite(trade['pnl']), "PnL should be finite"
        
        # Property 5: Calculate basic statistics
        total_pnl = sum(trade['pnl'] for trade in trades)
        total_commission = sum(trade['commission'] for trade in trades)
        
        assert np.isfinite(total_pnl), "Total PnL should be finite"
        assert total_commission >= 0, "Total commission should be non-negative"


class TestPropertyBasedPerformanceMetrics:
    """Property-based tests for performance calculations"""
    
    @given(
        returns=st.lists(
            st.floats(min_value=-0.5, max_value=2.0, allow_nan=False, allow_infinity=False),
            min_size=10,
            max_size=100
        )
    )
    @settings(max_examples=100, deadline=5000)
    def test_return_calculations_properties(self, returns):
        """Property: Return calculations should be mathematically consistent"""
        
        # Filter out extreme values
        returns = [r for r in returns if -0.9 < r < 5.0]
        assume(len(returns) >= 5)
        
        # Property 1: Cumulative return calculation
        cumulative_return = 1.0
        for ret in returns:
            cumulative_return *= (1 + ret)
        
        total_return = cumulative_return - 1.0
        
        # Property 2: Total return should be finite
        assert np.isfinite(total_return), "Total return should be finite"
        
        # Property 3: If all returns are positive, total return should be positive
        if all(r > 0 for r in returns):
            assert total_return > 0, "Positive returns should yield positive total return"
        
        # Property 4: Calculate basic statistics
        if returns:
            mean_return = np.mean(returns)
            std_return = np.std(returns)
            
            assert np.isfinite(mean_return), "Mean return should be finite"
            assert std_return >= 0, "Standard deviation should be non-negative"
            
            # Property 5: Sharpe ratio calculation (if std > 0)
            if std_return > 0:
                sharpe_ratio = mean_return / std_return
                assert np.isfinite(sharpe_ratio), "Sharpe ratio should be finite"
    
    @given(
        prices=st.lists(
            st.floats(min_value=0.1, max_value=1000.0, allow_nan=False),
            min_size=20,
            max_size=200
        )
    )
    @settings(max_examples=50, deadline=10000)
    def test_drawdown_calculations_properties(self, prices):
        """Property: Drawdown calculations should be mathematically correct"""
        
        assume(all(p > 0 for p in prices))
        assume(len(set(prices)) > 1)  # Ensure some price variation
        
        # Calculate running maximum (peak)
        running_max = []
        current_max = prices[0]
        
        for price in prices:
            current_max = max(current_max, price)
            running_max.append(current_max)
        
        # Calculate drawdowns
        drawdowns = []
        for i, price in enumerate(prices):
            if running_max[i] > 0:
                drawdown = (price - running_max[i]) / running_max[i]
                drawdowns.append(drawdown)
        
        # Property 1: Drawdowns should be non-positive
        assert all(dd <= 0 for dd in drawdowns), "Drawdowns should be non-positive"
        
        # Property 2: Maximum drawdown should be the minimum drawdown
        if drawdowns:
            max_drawdown = min(drawdowns)
            assert max_drawdown <= 0, "Maximum drawdown should be non-positive"
            
            # Property 3: Maximum drawdown should be >= all other drawdowns
            assert all(dd >= max_drawdown for dd in drawdowns), "Max drawdown should be minimum value"
        
        # Property 4: Running maximum should be non-decreasing
        for i in range(1, len(running_max)):
            assert running_max[i] >= running_max[i-1], "Running maximum should be non-decreasing"


class TestPropertyBasedEdgeCases:
    """Property-based tests for edge cases and boundary conditions"""
    
    @given(
        data_size=st.integers(min_value=1, max_value=10),
        price_range=st.tuples(
            st.floats(min_value=0.001, max_value=1.0),
            st.floats(min_value=1.0, max_value=1000.0)
        )
    )
    @settings(max_examples=50, deadline=10000)
    def test_minimal_data_handling(self, data_size, price_range):
        """Property: System should handle minimal data gracefully"""
        
        assume(price_range[0] < price_range[1])
        
        # Generate minimal dataset
        data = generate_financial_data(
            min_records=data_size,
            max_records=data_size,
            price_range=price_range
        ).example()
        
        # Property 1: Data should still be valid
        assert len(data) == data_size
        assert not data.empty
        
        # Property 2: OHLC relationships should hold even for minimal data
        assert (data['high'] >= data['low']).all()
        assert (data['high'] >= data['open']).all()
        assert (data['high'] >= data['close']).all()
        
        # Property 3: Prices should be within specified range
        all_prices = pd.concat([data['open'], data['high'], data['low'], data['close']])
        assert all_prices.min() >= price_range[0] * 0.9  # Allow small tolerance
        assert all_prices.max() <= price_range[1] * 1.1  # Allow small tolerance
    
    @given(
        symbol=currency_pairs,
        timeframe=timeframes,
        validation_level=st.sampled_from(['BASIC', 'STANDARD', 'STRICT'])
    )
    @settings(max_examples=30, deadline=5000)
    def test_configuration_combinations(self, symbol, timeframe, validation_level):
        """Property: All valid configuration combinations should work"""
        
        # Property 1: Symbol should be valid currency pair format
        assert len(symbol) == 6, "Currency pair should be 6 characters"
        assert symbol.isupper(), "Currency pair should be uppercase"
        
        # Property 2: Timeframe should be valid
        valid_timeframes = ['1M', '5M', '15M', '30M', '1H', '4H', '1D', '1W']
        assert timeframe in valid_timeframes, "Timeframe should be valid"
        
        # Property 3: Validation level should be valid
        valid_levels = ['BASIC', 'STANDARD', 'STRICT', 'CRYPTOGRAPHIC']
        assert validation_level in valid_levels, "Validation level should be valid"
        
        # Property 4: Configuration should be internally consistent
        config = {
            'symbol': symbol,
            'timeframe': timeframe,
            'validation_level': validation_level
        }
        
        # All values should be strings
        assert all(isinstance(v, str) for v in config.values())
    
    @given(
        extreme_params=st.one_of(
            generate_strategy_parameters('RSI'),
            generate_strategy_parameters('MACD'),
            generate_strategy_parameters('MovingAverage')
        )
    )
    @settings(max_examples=50, deadline=5000)
    def test_extreme_parameter_handling(self, extreme_params):
        """Property: System should handle extreme parameters gracefully"""
        
        # Property 1: All parameters should be finite
        for key, value in extreme_params.items():
            if isinstance(value, (int, float)):
                assert np.isfinite(value), f"Parameter {key} should be finite"
        
        # Property 2: Parameters should be positive where applicable
        positive_params = ['period', 'fast_period', 'slow_period', 'signal_period']
        for param in positive_params:
            if param in extreme_params:
                assert extreme_params[param] > 0, f"{param} should be positive"
        
        # Property 3: Percentage parameters should be reasonable
        percentage_params = ['overbought', 'oversold', 'signal_threshold']
        for param in percentage_params:
            if param in extreme_params:
                value = extreme_params[param]
                if param in ['overbought', 'oversold']:
                    assert 0 <= value <= 100, f"{param} should be between 0 and 100"
                else:
                    assert 0 <= value <= 1, f"{param} should be between 0 and 1"


# Example-based tests for specific scenarios
class TestExampleBasedScenarios:
    """Example-based tests for specific known scenarios"""
    
    @given(data=generate_financial_data(min_records=100, max_records=100))
    @example(data=pd.DataFrame({
        'open': [1.0] * 100,
        'high': [1.0] * 100,
        'low': [1.0] * 100,
        'close': [1.0] * 100,
        'volume': [1000] * 100
    }, index=pd.date_range('2023-01-01', periods=100, freq='h')))
    @settings(max_examples=20, deadline=10000)
    def test_flat_market_scenario(self, data):
        """Test behavior with flat market (no price movement)"""
        
        # Property: System should handle flat markets gracefully
        price_range = data['high'].max() - data['low'].min()
        
        if price_range < 0.001:  # Essentially flat market
            # Should still validate successfully
            try:
                validator = DataValidator(ValidationLevel.BASIC)
                report = validator.validate_data(data, "FLAT_TEST")
                
                # Should be valid despite no movement
                assert report.is_valid(), "Flat market should still be valid"
                assert report.integrity_score > 0.8, "Flat market should have high integrity"
                
            except ImportError:
                pytest.skip("Validator not available")
    
    @given(data=generate_financial_data(min_records=50, max_records=50))
    @example(data=pd.DataFrame({
        'open': [1.0, 2.0, 0.5, 4.0, 0.1] * 10,
        'high': [1.1, 2.1, 0.6, 4.1, 0.2] * 10,
        'low': [0.9, 1.9, 0.4, 3.9, 0.05] * 10,
        'close': [1.05, 1.95, 0.55, 3.95, 0.15] * 10,
        'volume': [1000] * 50
    }, index=pd.date_range('2023-01-01', periods=50, freq='h')))
    @settings(max_examples=20, deadline=10000)
    def test_high_volatility_scenario(self, data):
        """Test behavior with high volatility market"""
        
        # Calculate volatility
        returns = data['close'].pct_change().dropna()
        volatility = returns.std()
        
        if volatility > 0.1:  # High volatility
            # System should still function but may have lower integrity scores
            try:
                validator = DataValidator(ValidationLevel.STRICT)
                report = validator.validate_data(data, "VOLATILE_TEST")
                
                # Should complete validation
                assert report is not None
                assert 0.0 <= report.integrity_score <= 1.0
                
                # High volatility might trigger some checks
                if not report.is_valid():
                    assert len(report.checks_failed) > 0
                
            except ImportError:
                pytest.skip("Validator not available")


if __name__ == "__main__":
    # Run property-based tests
    pytest.main([__file__, "-v", "--tb=short", "--hypothesis-show-statistics"])