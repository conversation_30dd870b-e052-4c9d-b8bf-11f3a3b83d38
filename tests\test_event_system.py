# tests/test_event_system.py
import pytest
import asyncio
from datetime import datetime
from src.events import EventBus, EventValidator, TradingEventSchemas

class TestEventSystem:
    """Test suite for the type-safe event system"""
    
    @pytest.fixture
    def event_bus(self):
        return EventBus()
    
    @pytest.fixture
    def event_validator(self):
        return EventValidator()
    
    def test_validate_strategy_execution_event(self, event_validator):
        """Test validation of strategy execution events"""
        valid_event = {
            'type': 'STRATEGY_EXECUTED',
            'timestamp': datetime.now().isoformat(),
            'strategy_id': 'RSI_MACD_001',
            'payload': {
                'symbol': 'EURUSD',
                'action': 'BUY',
                'confidence': 0.85,
                'parameters': {
                    'rsi_period': 14,
                    'macd_fast': 12,
                    'macd_slow': 26
                }
            },
            'metadata': {
                'dummy_mode': True,
                'user_id': '12345678-1234-1234-1234-123456789012'
            }
        }
        
        result = event_validator.validate('STRATEGY_EXECUTED', valid_event)
        assert result.success is True
        assert result.data is not None
    
    def test_reject_invalid_strategy_events(self, event_validator):
        """Test rejection of invalid strategy events"""
        invalid_event = {
            'type': 'STRATEGY_EXECUTED',
            'timestamp': 'invalid-date',
            'strategy_id': '',  # Empty string should fail
            'payload': {
                'symbol': 'INVALID_SYMBOL',
                'action': 'INVALID_ACTION',
                'confidence': 1.5  # Out of range
            }
        }
        
        result = event_validator.validate('STRATEGY_EXECUTED', invalid_event)
        assert result.success is False
        assert len(result.errors) > 0
    
    @pytest.mark.asyncio
    async def test_event_ordering_and_idempotency(self, event_bus):
        """Test that events maintain ordering and idempotency"""
        events_received = []
        
        def event_handler(event):
            events_received.append(event)
        
        event_bus.subscribe('STRATEGY_EXECUTED', event_handler)
        
        event1 = {
            'id': '1',
            'type': 'STRATEGY_EXECUTED',
            'timestamp': '2025-01-01T10:00:00Z',
            'strategy_id': 'test_strategy',
            'payload': {
                'symbol': 'EURUSD',
                'action': 'BUY',
                'confidence': 0.8,
                'parameters': {}
            },
            'metadata': {
                'user_id': '12345678-1234-1234-1234-123456789012'
            }
        }
        
        event2 = {
            'id': '2',
            'type': 'STRATEGY_EXECUTED',
            'timestamp': '2025-01-01T10:00:01Z',
            'strategy_id': 'test_strategy',
            'payload': {
                'symbol': 'GBPUSD',
                'action': 'SELL',
                'confidence': 0.7,
                'parameters': {}
            },
            'metadata': {
                'user_id': '12345678-1234-1234-1234-123456789012'
            }
        }
        
        # Publish events
        await event_bus.publish(event1)
        await event_bus.publish(event2)
        await event_bus.publish(event1)  # Duplicate should be ignored
        
        # Allow time for async processing
        await asyncio.sleep(0.1)
        
        assert len(events_received) == 2  # Duplicate should be ignored
        assert events_received[0].strategy_id == 'test_strategy'
        assert events_received[1].strategy_id == 'test_strategy'
    
    def test_market_data_validation(self, event_validator):
        """Test market data event validation"""
        valid_market_data = {
            'type': 'MARKET_DATA_RECEIVED',
            'timestamp': datetime.now().isoformat(),
            'payload': {
                'symbol': 'EURUSD',
                'data': {
                    'open': 1.2000,
                    'high': 1.2050,
                    'low': 1.1950,
                    'close': 1.2020,
                    'volume': 1000,
                    'timestamp': datetime.now().isoformat()
                },
                'source': 'dukascopy',
                'integrity_hash': 'a' * 64  # 64-character hash
            }
        }
        
        result = event_validator.validate('MARKET_DATA_RECEIVED', valid_market_data)
        assert result.success is True
    
    def test_invalid_ohlc_data_rejection(self, event_validator):
        """Test rejection of invalid OHLC data"""
        invalid_market_data = {
            'type': 'MARKET_DATA_RECEIVED',
            'timestamp': datetime.now().isoformat(),
            'payload': {
                'symbol': 'EURUSD',
                'data': {
                    'open': 1.2000,
                    'high': 1.1900,  # Invalid: high < low
                    'low': 1.1950,
                    'close': 1.2020,
                    'volume': 1000,
                    'timestamp': datetime.now().isoformat()
                },
                'source': 'dukascopy',
                'integrity_hash': 'a' * 64
            }
        }
        
        result = event_validator.validate('MARKET_DATA_RECEIVED', invalid_market_data)
        assert result.success is False
        assert any('high price cannot be less than low price' in error.lower() for error in result.errors)
    
    @pytest.mark.asyncio
    async def test_event_history_tracking(self, event_bus):
        """Test event history tracking"""
        event = {
            'type': 'STRATEGY_EXECUTED',
            'timestamp': datetime.now().isoformat(),
            'strategy_id': 'test_strategy',
            'payload': {
                'symbol': 'EURUSD',
                'action': 'BUY',
                'confidence': 0.8,
                'parameters': {}
            },
            'metadata': {
                'user_id': '12345678-1234-1234-1234-123456789012'
            }
        }
        
        await event_bus.publish(event)
        
        history = event_bus.get_event_history()
        assert len(history) == 1
        assert history[0]['event']['type'] == 'STRATEGY_EXECUTED'
    
    def test_cleanup_processed_events(self, event_bus):
        """Test cleanup of old processed events"""
        # Add some mock processed events
        event_bus._processed_events.add('STRATEGY_EXECUTED_2024-01-01T00:00:00Z_abc123')
        event_bus._processed_events.add('STRATEGY_EXECUTED_2025-01-01T00:00:00Z_def456')
        
        initial_count = len(event_bus._processed_events)
        cleaned_count = event_bus.cleanup_processed_events(older_than_hours=1)
        
        # Should have cleaned up at least some events
        assert event_bus.get_processed_event_count() <= initial_count
    
    @pytest.mark.asyncio
    async def test_subscriber_management(self, event_bus):
        """Test subscriber management"""
        events_received = []
        
        def handler1(event):
            events_received.append(f"handler1: {event.strategy_id}")
        
        def handler2(event):
            events_received.append(f"handler2: {event.strategy_id}")
        
        # Subscribe handlers
        event_bus.subscribe('STRATEGY_EXECUTED', handler1)
        event_bus.subscribe('STRATEGY_EXECUTED', handler2)
        
        assert event_bus.get_subscriber_count('STRATEGY_EXECUTED') == 2
        
        # Publish event
        event = {
            'type': 'STRATEGY_EXECUTED',
            'timestamp': datetime.now().isoformat(),
            'strategy_id': 'test_strategy',
            'payload': {
                'symbol': 'EURUSD',
                'action': 'BUY',
                'confidence': 0.8,
                'parameters': {}
            },
            'metadata': {
                'user_id': '12345678-1234-1234-1234-123456789012'
            }
        }
        
        await event_bus.publish(event)
        await asyncio.sleep(0.1)  # Allow async processing
        
        assert len(events_received) == 2
        
        # Unsubscribe one handler
        event_bus.unsubscribe('STRATEGY_EXECUTED', handler1)
        assert event_bus.get_subscriber_count('STRATEGY_EXECUTED') == 1