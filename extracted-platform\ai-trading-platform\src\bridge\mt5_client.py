
from .schemas import OrderRequest, OrderResult, AccountInfo

class MT5Client:
    def __init__(self, dummy_mode=False):
        self.dummy_mode = dummy_mode

    def get_account_info(self) -> AccountInfo:
        if self.dummy_mode:
            return AccountInfo(balance=10000, equity=10000, margin=0, currency="USD")
        raise NotImplementedError

    def submit_order(self, order: OrderRequest) -> OrderResult:
        if self.dummy_mode:
            if order.symbol not in ["EURUSD", "GBPUSD"]:
                return OrderResult(success=False, error="Invalid symbol")
            return OrderResult(success=True, order_id=12345)
        raise NotImplementedError
