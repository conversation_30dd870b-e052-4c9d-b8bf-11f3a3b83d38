"""
MQL5 Code Generator Module

This module provides functions for generating MQL5 code for indicators, Expert Advisors, and scripts.
"""

import os
import re
from typing import Dict, List, Any, Optional, Union

# Templates directory
TEMPLATES_DIR = os.path.join(os.path.dirname(__file__), "../templates/mql5")

# Ensure templates directory exists
os.makedirs(TEMPLATES_DIR, exist_ok=True)

# Default templates
DEFAULT_INDICATOR_TEMPLATE = """//+------------------------------------------------------------------+
//|                                                  {name}.mq5 |
//|                        AI Enhanced Trading Platform          |
//|                                              {datetime} |
//+------------------------------------------------------------------+
#property copyright "AI Enhanced Trading Platform"
#property link      "https://www.example.com"
#property version   "1.00"
#property indicator_chart_window
#property indicator_buffers 1
#property indicator_plots   1
//--- plot {name}
#property indicator_label1  "{name}"
#property indicator_type1   DRAW_LINE
#property indicator_color1  clrRed
#property indicator_style1  STYLE_SOLID
#property indicator_width1  1

//--- input parameters
input int      period=14;          // Period
input int      shift=0;            // Shift

//--- indicator buffers
double         Buffer[];

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
//--- indicator buffers mapping
   SetIndexBuffer(0,Buffer,INDICATOR_DATA);
   
//--- set short name and digits
   string short_name=StringFormat("{name}(%d,%d)",period,shift);
   IndicatorSetString(INDICATOR_SHORTNAME,short_name);
   IndicatorSetInteger(INDICATOR_DIGITS,_Digits);
   
//--- set index shift
   PlotIndexSetInteger(0,PLOT_SHIFT,shift);
   
//--- initialization done
   return(INIT_SUCCEEDED);
  }

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
//---
   int start;
   
   if(prev_calculated==0)
      start=period;
   else
      start=prev_calculated-1;
      
//--- main loop
   for(int i=start; i<rates_total; i++)
     {
      // TODO: Replace with your indicator calculation
      Buffer[i]=close[i];
     }
     
//--- return value of prev_calculated for next call
   return(rates_total);
  }
"""

DEFAULT_EA_TEMPLATE = """//+------------------------------------------------------------------+
//|                                                     {name}.mq5 |
//|                        AI Enhanced Trading Platform          |
//|                                              {datetime} |
//+------------------------------------------------------------------+
#property copyright "AI Enhanced Trading Platform"
#property link      "https://www.example.com"
#property version   "1.00"

// Input parameters
input double   Lot=0.1;            // Lot size
input int      TakeProfit=100;     // Take profit in points
input int      StopLoss=50;        // Stop loss in points
input int      MagicNumber=12345;  // Magic number

// Global variables
int handle;
int barsTotal;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   barsTotal=0;
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Count the number of bars
   int bars=Bars(_Symbol,_Period);
   
   // Exit if no new bar
   if(barsTotal==bars) return;
   barsTotal=bars;
   
   // Get the latest price data
   MqlRates rates[];
   int copied=CopyRates(_Symbol,_Period,0,3,rates);
   if(copied<3) return;
   
   // TODO: Replace with your trading logic
   bool buy_signal = rates[1].close > rates[2].close;
   bool sell_signal = rates[1].close < rates[2].close;
   
   // Check for buying conditions
   if(buy_signal)
   {
      // Calculate entry and stop levels
      double entry_price = SymbolInfoDouble(_Symbol,SYMBOL_ASK);
      double sl = entry_price - StopLoss*SymbolInfoDouble(_Symbol,SYMBOL_POINT);
      double tp = entry_price + TakeProfit*SymbolInfoDouble(_Symbol,SYMBOL_POINT);
      
      // Open a buy position
      MqlTradeRequest request={0};
      request.action=TRADE_ACTION_DEAL;
      request.symbol=_Symbol;
      request.volume=Lot;
      request.price=entry_price;
      request.sl=sl;
      request.tp=tp;
      request.type=ORDER_TYPE_BUY;
      request.magic=MagicNumber;
      request.comment="Buy order";
      
      MqlTradeResult result={0};
      OrderSend(request,result);
   }
   
   // Check for selling conditions
   if(sell_signal)
   {
      // Calculate entry and stop levels
      double entry_price = SymbolInfoDouble(_Symbol,SYMBOL_BID);
      double sl = entry_price + StopLoss*SymbolInfoDouble(_Symbol,SYMBOL_POINT);
      double tp = entry_price - TakeProfit*SymbolInfoDouble(_Symbol,SYMBOL_POINT);
      
      // Open a sell position
      MqlTradeRequest request={0};
      request.action=TRADE_ACTION_DEAL;
      request.symbol=_Symbol;
      request.volume=Lot;
      request.price=entry_price;
      request.sl=sl;
      request.tp=tp;
      request.type=ORDER_TYPE_SELL;
      request.magic=MagicNumber;
      request.comment="Sell order";
      
      MqlTradeResult result={0};
      OrderSend(request,result);
   }
}
"""

def load_template(template_type: str) -> str:
    """
    Load a template from file or return default template
    """
    template_path = os.path.join(TEMPLATES_DIR, f"{template_type}.mq5.template")
    
    if os.path.exists(template_path):
        with open(template_path, 'r', encoding='utf-8') as f:
            return f.read()
    
    if template_type == 'indicator':
        return DEFAULT_INDICATOR_TEMPLATE
    elif template_type == 'expert_advisor':
        return DEFAULT_EA_TEMPLATE
    else:
        raise ValueError(f"Unknown template type: {template_type}")

def generate_indicator_code(name: str, params: Dict[str, Any] = None) -> str:
    """
    Generate MQL5 code for an indicator
    """
    from datetime import datetime
    
    template = load_template('indicator')
    
    # Basic replacements
    code = template.replace('{name}', name)
    code = code.replace('{datetime}', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # Handle custom parameters
    if params:
        # Replace period if provided
        if 'period' in params:
            code = re.sub(
                r'input int\s+period=\d+;\s+// Period', 
                f'input int      period={params["period"]};          // Period', 
                code
            )
        
        # Add custom inputs
        input_section = ""
        for key, value in params.items():
            if key not in ['period', 'shift']:  # Skip already handled params
                if isinstance(value, int):
                    input_section += f"input int      {key}={value};\n"
                elif isinstance(value, float):
                    input_section += f"input double   {key}={value};\n"
                elif isinstance(value, str):
                    input_section += f'input string   {key}="{value}";\n'
                elif isinstance(value, bool):
                    input_section += f"input bool     {key}={str(value).lower()};\n"
        
        if input_section:
            # Add custom inputs after existing inputs
            code = re.sub(
                r'(input int\s+shift=\d+;\s+// Shift\n)',
                f'\\1\n{input_section}',
                code
            )
    
    return code

def generate_ea_code(name: str, params: Dict[str, Any] = None) -> str:
    """
    Generate MQL5 code for an Expert Advisor
    """
    from datetime import datetime
    
    template = load_template('expert_advisor')
    
    # Basic replacements
    code = template.replace('{name}', name)
    code = code.replace('{datetime}', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # Handle custom parameters
    if params:
        # Replace standard params if provided
        if 'Lot' in params:
            code = re.sub(
                r'input double\s+Lot=[\d\.]+;\s+// Lot size', 
                f'input double   Lot={params["Lot"]};            // Lot size', 
                code
            )
        
        if 'TakeProfit' in params:
            code = re.sub(
                r'input int\s+TakeProfit=\d+;\s+// Take profit in points', 
                f'input int      TakeProfit={params["TakeProfit"]};     // Take profit in points', 
                code
            )
        
        if 'StopLoss' in params:
            code = re.sub(
                r'input int\s+StopLoss=\d+;\s+// Stop loss in points', 
                f'input int      StopLoss={params["StopLoss"]};        // Stop loss in points', 
                code
            )
        
        # Add custom inputs
        input_section = ""
        for key, value in params.items():
            if key not in ['Lot', 'TakeProfit', 'StopLoss', 'MagicNumber']:  # Skip already handled params
                if isinstance(value, int):
                    input_section += f"input int      {key}={value};\n"
                elif isinstance(value, float):
                    input_section += f"input double   {key}={value};\n"
                elif isinstance(value, str):
                    input_section += f'input string   {key}="{value}";\n'
                elif isinstance(value, bool):
                    input_section += f"input bool     {key}={str(value).lower()};\n"
        
        if input_section:
            # Add custom inputs after existing inputs
            code = re.sub(
                r'(input int\s+MagicNumber=\d+;\s+// Magic number\n)',
                f'\\1\n{input_section}',
                code
            )
    
    return code

def generate_mql5_code(name: str, type_str: str, params: Dict[str, Any] = None) -> str:
    """
    Generate MQL5 code based on type
    """
    if type_str == 'indicator':
        return generate_indicator_code(name, params)
    elif type_str in ['expert_advisor', 'ea']:
        return generate_ea_code(name, params)
    else:
        raise ValueError(f"Unsupported MQL5 type: {type_str}")
