import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>, 
  T<PERSON>dingUp, 
  Zap, 
  Shield, 
  Brain, 
  DollarSign,
  BarChart3,
  MessageSquare,
  Sparkles,
  ChevronRight,
  Target,
  Maximize2,
  Minimize2
} from 'lucide-react';
import StrategyChatbot from './StrategyChatbot';
import { aiPromptsService } from '../services/aiPrompts';
import type { AIPrompt } from '../services/aiPrompts';
import './EnhancedHomepage.css';

const IntegratedHomepage: React.FC = () => {
  const [prompts, setPrompts] = useState<AIPrompt[]>([]);
  const [selectedPrompt, setSelectedPrompt] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [isChatExpanded, setIsChatExpanded] = useState(false);
  const [activePromptId, setActivePromptId] = useState<string>('');

  useEffect(() => {
    const loadPrompts = async () => {
      try {
        const data = await aiPromptsService.getAllPrompts();
        setPrompts(data.slice(0, 8)); // Show first 8 prompts
      } catch (error) {
        console.error('Error loading prompts:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadPrompts();
  }, []);

  const handlePromptSelect = (prompt: AIPrompt) => {
    setSelectedPrompt(prompt.prompt_template || prompt.description);
    setActivePromptId(prompt.id);
  };

  const getCategoryIcon = (category: string) => {
    const iconMap: Record<string, React.ReactNode> = {
      'trend_following': <TrendingUp className="w-5 h-5" />,
      'mean_reversion': <Target className="w-5 h-5" />,
      'momentum': <Zap className="w-5 h-5" />,
      'risk_management': <Shield className="w-5 h-5" />,
      'market_analysis': <BarChart3 className="w-5 h-5" />,
      'portfolio_optimization': <Brain className="w-5 h-5" />,
      'fundamental_analysis': <DollarSign className="w-5 h-5" />,
      'technical_analysis': <BarChart3 className="w-5 h-5" />
    };
    return iconMap[category] || <Sparkles className="w-5 h-5" />;
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    element?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <div className="enhanced-homepage">
      {/* Navigation */}
      <nav className="navbar">
        <div className="nav-container">
          <div className="logo">
            <Bot className="logo-icon" />
            TradeBuilder AI
          </div>
          <div className="nav-links">
            <button onClick={() => scrollToSection('chat-demo')}>
              Try Demo
            </button>
            <button onClick={() => scrollToSection('features')}>
              Features
            </button>
            <button onClick={() => scrollToSection('pricing')}>
              Pricing
            </button>
            <button onClick={() => scrollToSection('contact')}>
              Contact
            </button>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="hero" id="hero">
        <div className="hero-content">
          <h1>Build Trading Strategies in Plain English</h1>
          <p>
            Describe your trading idea below and watch our AI create professional strategies instantly. 
            No coding required - just chat with our AI assistant.
          </p>
          <div className="hero-buttons">
            <button 
              className="btn btn-primary btn-large"
              onClick={() => scrollToSection('chat-demo')}
            >
              <MessageSquare />
              Start Chatting
            </button>
            <button 
              className="btn btn-secondary btn-large"
              onClick={() => scrollToSection('features')}
            >
              See Features
            </button>
          </div>
        </div>
      </section>

      {/* Interactive Chat Demo Section */}
      <section className="chat-demo-section" id="chat-demo">
        <div className="section-container">
          <h2>Try Our AI Assistant Right Now</h2>
          <p>Click any prompt below to start a conversation, or type your own trading idea</p>
          
          <div className={`chat-layout ${isChatExpanded ? 'expanded' : ''}`}>
            {/* Prompts Gallery */}
            <div className={`prompts-sidebar ${isChatExpanded ? 'hidden' : ''}`}>
              <h3>
                <Sparkles className="w-5 h-5" />
                Popular Trading Prompts
              </h3>
              
              {isLoading ? (
                <div className="loading-state">
                  <div className="loading-spinner"></div>
                  <p>Loading AI prompts...</p>
                </div>
              ) : (
                <div className="prompt-list">
                  {prompts.map((prompt) => (
                    <div
                      key={prompt.id}
                      className={`prompt-card-compact ${activePromptId === prompt.id ? 'active' : ''}`}
                      onClick={() => handlePromptSelect(prompt)}
                    >
                      <div className="prompt-icon">
                        {getCategoryIcon(prompt.category)}
                      </div>
                      <div className="prompt-content">
                        <h4>{prompt.title}</h4>
                        <p>{prompt.description}</p>
                        <span className="prompt-category">
                          {prompt.category.replace('_', ' ')}
                        </span>
                      </div>
                      <ChevronRight className="prompt-arrow" />
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Chatbot Interface */}
            <div className="chatbot-main">
              <div className="chatbot-header-integrated">
                <div className="chatbot-title">
                  <Bot className="w-6 h-6" />
                  <h3>AI Trading Assistant</h3>
                  {activePromptId && (
                    <span className="active-prompt-indicator">
                      {prompts.find(p => p.id === activePromptId)?.title || 'Custom Prompt'}
                    </span>
                  )}
                </div>
                <div className="chatbot-controls">
                  <div className="chatbot-status">
                    <div className="status-dot"></div>
                    Online & Ready
                  </div>
                  <button
                    className="expand-btn"
                    onClick={() => setIsChatExpanded(!isChatExpanded)}
                    title={isChatExpanded ? 'Show prompts' : 'Expand chat'}
                  >
                    {isChatExpanded ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
                  </button>
                </div>
              </div>
              <div className="chatbot-content-wrapper">
                <StrategyChatbot initialPrompt={selectedPrompt} />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="features" id="features">
        <div className="section-container">
          <h2>Why Choose TradeBuilder AI?</h2>
          <div className="feature-grid">
            <div className="feature">
              <MessageSquare className="feature-icon" />
              <h3>Natural Language Processing</h3>
              <p>Describe strategies in plain English. Our AI understands trading terminology and converts your ideas into executable code.</p>
            </div>
            <div className="feature">
              <BarChart3 className="feature-icon" />
              <h3>Professional Backtesting</h3>
              <p>Test strategies with real historical data. Get detailed performance metrics and risk analysis before going live.</p>
            </div>
            <div className="feature">
              <Zap className="feature-icon" />
              <h3>Instant Code Generation</h3>
              <p>Watch your strategies come to life in real-time. Complete Python trading code generated in seconds.</p>
            </div>
            <div className="feature">
              <Shield className="feature-icon" />
              <h3>Risk Management</h3>
              <p>Built-in risk controls and position sizing. Our AI ensures your strategies include proper risk management.</p>
            </div>
            <div className="feature">
              <Brain className="feature-icon" />
              <h3>AI Optimization</h3>
              <p>Continuously improve your strategies with AI-powered optimization and machine learning insights.</p>
            </div>
            <div className="feature">
              <DollarSign className="feature-icon" />
              <h3>MT5 Integration</h3>
              <p>Deploy directly to MetaTrader 5. No manual coding or complex setup required.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="pricing" id="pricing">
        <div className="section-container">
          <h2>Simple, Transparent Pricing</h2>
          <div className="pricing-cards">
            <div className="pricing-card">
              <h3>Starter</h3>
              <div className="price">$0<span>/month</span></div>
              <ul>
                <li>5 strategy conversations</li>
                <li>Basic backtesting</li>
                <li>Community support</li>
                <li>Paper trading only</li>
              </ul>
              <button className="btn btn-secondary">Start Free</button>
            </div>
            <div className="pricing-card featured">
              <div className="popular-badge">Most Popular</div>
              <h3>Professional</h3>
              <div className="price">$49<span>/month</span></div>
              <ul>
                <li>Unlimited conversations</li>
                <li>Advanced backtesting</li>
                <li>Live MT5 trading</li>
                <li>AI optimization</li>
                <li>Priority support</li>
              </ul>
              <button className="btn btn-primary">Start 7-Day Free Trial</button>
            </div>
            <div className="pricing-card">
              <h3>Enterprise</h3>
              <div className="price">$149<span>/month</span></div>
              <ul>
                <li>Everything in Professional</li>
                <li>Team collaboration</li>
                <li>Custom integrations</li>
                <li>Dedicated support</li>
                <li>White-label options</li>
              </ul>
              <button className="btn btn-secondary">Contact Sales</button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer" id="contact">
        <div className="footer-content">
          <div className="footer-links">
            <a href="#privacy">Privacy Policy</a>
            <a href="#terms">Terms of Service</a>
            <a href="#docs">Documentation</a>
            <a href="#support">Support</a>
          </div>
          <p>© 2025 TradeBuilder AI. Empowering traders with artificial intelligence.</p>
        </div>
      </footer>
    </div>
  );
};

export default IntegratedHomepage;
