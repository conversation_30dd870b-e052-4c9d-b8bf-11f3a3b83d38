// test/helpers/test-database.ts
import { Pool } from 'pg';

/**
 * Test database management for integration tests
 */
export class TestDatabase {
  private pool: Pool;
  private dbName: string;

  constructor() {
    this.dbName = `test_${process.env.JEST_WORKER_ID || '1'}`;
    this.pool = new Pool({
      database: this.dbName,
      host: process.env.TEST_DB_HOST || 'localhost',
      port: parseInt(process.env.TEST_DB_PORT || '5432'),
      user: process.env.TEST_DB_USER || 'test',
      password: process.env.TEST_DB_PASSWORD || 'test',
    });
  }

  async setup() {
    // Create test database
    const adminPool = new Pool({
      database: 'postgres',
      host: process.env.TEST_DB_HOST || 'localhost',
      port: parseInt(process.env.TEST_DB_PORT || '5432'),
      user: process.env.TEST_DB_USER || 'test',
      password: process.env.TEST_DB_PASSWORD || 'test',
    });

    try {
      await adminPool.query(`DROP DATABASE IF EXISTS ${this.dbName}`);
      await adminPool.query(`CREATE DATABASE ${this.dbName}`);
    } catch (error) {
      console.warn('Database setup warning:', error);
    } finally {
      await adminPool.end();
    }

    // Run migrations (mock implementation)
    await this.runMigrations();
  }

  async teardown() {
    await this.pool.end();
  }

  async clean() {
    // Clean all tables for test isolation
    const tables = ['trades', 'users', 'market_data', 'signals'];
    for (const table of tables) {
      try {
        await this.pool.query(`TRUNCATE TABLE ${table} CASCADE`);
      } catch (error) {
        // Table might not exist, continue
        console.warn(`Could not truncate table ${table}:`, error);
      }
    }
  }

  getPool() {
    return this.pool;
  }

  private async runMigrations() {
    // Mock migration implementation
    const migrations = [
      `CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        created_at TIMESTAMP DEFAULT NOW()
      )`,
      `CREATE TABLE IF NOT EXISTS trades (
        id UUID PRIMARY KEY,
        user_id UUID REFERENCES users(id),
        symbol VARCHAR(10) NOT NULL,
        quantity INTEGER NOT NULL,
        price DECIMAL(10,2) NOT NULL,
        side VARCHAR(4) NOT NULL,
        status VARCHAR(20) NOT NULL,
        created_at TIMESTAMP DEFAULT NOW()
      )`,
      `CREATE TABLE IF NOT EXISTS market_data (
        id SERIAL PRIMARY KEY,
        symbol VARCHAR(10) NOT NULL,
        price DECIMAL(10,2) NOT NULL,
        volume BIGINT NOT NULL,
        timestamp TIMESTAMP DEFAULT NOW()
      )`,
      `CREATE TABLE IF NOT EXISTS signals (
        id SERIAL PRIMARY KEY,
        symbol VARCHAR(10) NOT NULL,
        action VARCHAR(10) NOT NULL,
        confidence DECIMAL(3,2) NOT NULL,
        created_at TIMESTAMP DEFAULT NOW()
      )`
    ];

    for (const migration of migrations) {
      try {
        await this.pool.query(migration);
      } catch (error) {
        console.warn('Migration warning:', error);
      }
    }
  }
}