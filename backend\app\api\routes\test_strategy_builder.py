import pytest
from .strategy_builder import <PERSON><PERSON><PERSON><PERSON>, InvalidStrategyError

def test_strategy_generation_from_natural_language():
    """Test converting natural language to Python strategy"""
    builder = StrategyBuilder()
    
    natural_language = "Buy when RSI is below 30, sell when RSI is above 70"
    
    result = builder.generate_strategy(natural_language)
    
    assert "def strategy" in result
    assert "RSI" in result
    assert "buy_signal" in result
    assert "sell_signal" in result

def test_invalid_strategy_raises_error():
    """Test invalid strategy descriptions raise appropriate errors"""
    builder = StrategyBuilder()
    
    with pytest.raises(InvalidStrategyError):
        builder.generate_strategy("This makes no sense for trading")

def test_strategy_validation():
    """Test generated strategy validation"""
    builder = StrategyBuilder()
    
    strategy_code = """
def strategy(data):
    return data['close'] > data['sma_20']
"""
    
    is_valid = builder.validate_strategy(strategy_code)
    assert is_valid is True
