﻿﻿/**
 * Backtesting Page
 * Strategy backtesting interface
 */

import { useState } from 'react';
import { Target, Play, ArrowLeft } from 'lucide-react';
import { BacktestForm } from '../components/backtest/BacktestForm';
import { BacktestResults } from '../components/backtest/BacktestResults';
import { useApi } from '../hooks/useApi';
import { apiService } from '../services/api';

export function BacktestingPage() {
  const [view, setView] = useState<'list' | 'form' | 'results'>('list');
  const [currentBacktestId, setCurrentBacktestId] = useState<string | null>(null);
  const [backtestResults, setBacktestResults] = useState<any | null>(null);
  
  const { loading, error, execute } = useApi();

  const handleCreateBacktest = (backtestId: string) => {
    setCurrentBacktestId(backtestId);
    fetchBacktestResults(backtestId);
  };

  const fetchBacktestResults = async (backtestId: string) => {
    try {
      const results = await execute(() => 
        apiService.getBacktestResults(backtestId)
      );
      
      if (results) {
        setBacktestResults(results);
        setView('results');
      }
    } catch (error) {
      console.error('Failed to fetch backtest results:', error);
    }
  };

  const handleNewBacktest = () => {
    setView('form');
    setCurrentBacktestId(null);
    setBacktestResults(null);
  };

  const handleBackToList = () => {
    setView('list');
    setCurrentBacktestId(null);
    setBacktestResults(null);
  };

  const handleRunAgain = () => {
    setView('form');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {view === 'list' && 'Backtesting'}
            {view === 'form' && 'New Backtest'}
            {view === 'results' && 'Backtest Results'}
          </h1>
          <p className="text-gray-600 mt-2">
            {view === 'list' && 'Test your trading strategies against historical data'}
            {view === 'form' && 'Configure your backtest parameters'}
            {view === 'results' && 'Analyze your backtest performance'}
          </p>
        </div>
        
        <div className="flex space-x-3">
          {view !== 'list' && (
            <button 
              onClick={handleBackToList}
              className="btn-secondary"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </button>
          )}
          
          {view === 'list' && (
            <button 
              onClick={handleNewBacktest}
              className="btn-primary"
            >
              <Play className="w-4 h-4 mr-2" />
              New Backtest
            </button>
          )}
        </div>
      </div>

      {/* Main Content */}
      {view === 'list' && (
        <div className="card p-12 text-center">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Target className="w-8 h-8 text-blue-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Start a New Backtest
          </h2>
          <p className="text-gray-600 max-w-md mx-auto mb-6">
            Test your trading strategies against historical data to analyze performance
            before deploying to live markets.
          </p>
          <button 
            onClick={handleNewBacktest}
            className="btn-primary mx-auto"
          >
            <Play className="w-4 h-4 mr-2" />
            Create Backtest
          </button>
        </div>
      )}

      {view === 'form' && (
        <BacktestForm onBacktestCreated={handleCreateBacktest} />
      )}

      {view === 'results' && backtestResults && (
        <BacktestResults 
          results={backtestResults} 
          onRestart={handleRunAgain} 
        />
      )}
    </div>
  );
}
