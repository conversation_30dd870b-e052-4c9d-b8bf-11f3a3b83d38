"""
Data Models for Strategy Chatbot System
Defines the core data structures for natural language to Python code generation
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from enum import Enum


class StrategyType(Enum):
    """Supported strategy types"""
    MEAN_REVERSION = "mean_reversion"
    MOMENTUM = "momentum"
    BREAKOUT = "breakout"
    ARBITRAGE = "arbitrage"
    MACHINE_LEARNING = "machine_learning"
    PAIRS_TRADING = "pairs_trading"
    GRID_TRADING = "grid_trading"


class MLModel(Enum):
    """Supported machine learning models"""
    RANDOM_FOREST = "random_forest"
    SVM = "svm"
    NEURAL_NETWORK = "neural_network"
    XGBOOST = "xgboost"
    LSTM = "lstm"


class Timeframe(Enum):
    """Supported timeframes"""
    M1 = "1M"
    M5 = "5M"
    M15 = "15M"
    M30 = "30M"
    H1 = "1H"
    H4 = "4H"
    D1 = "1D"
    W1 = "1W"


@dataclass
class StrategyRequest:
    """Parsed requirements from natural language input"""
    strategy_type: str
    symbols: List[str] = field(default_factory=list)
    timeframe: Optional[str] = None
    indicators: List[str] = field(default_factory=list)
    
    # Risk management parameters
    risk_per_trade: float = 0.02
    max_positions: int = 5
    stop_loss_pips: Optional[int] = None
    take_profit_pips: Optional[int] = None
    
    # Machine learning specific
    ml_model: Optional[str] = None
    features: List[str] = field(default_factory=list)
    training_bars: Optional[int] = None
    retrain_frequency: Optional[int] = None
    
    # Advanced parameters
    lookback_period: int = 100
    min_confidence: float = 0.6
    max_drawdown: float = 0.1
    
    # Custom parameters
    custom_params: Dict[str, Any] = field(default_factory=dict)


@dataclass
class TestCase:
    """Test case for generated strategy"""
    name: str
    description: str
    test_data: Dict[str, Any]
    expected_signal: str
    expected_confidence: float
    test_code: str


@dataclass
class GeneratedStrategy:
    """Complete generated strategy with code and metadata"""
    code: str
    strategy_name: str
    class_name: str
    test_cases: List[TestCase]
    documentation: str
    
    # Metadata
    created_at: datetime = field(default_factory=datetime.now)
    version: str = "1.0.0"
    dependencies: List[str] = field(default_factory=list)
    
    # Performance estimates (if available)
    estimated_sharpe: Optional[float] = None
    estimated_max_drawdown: Optional[float] = None
    estimated_win_rate: Optional[float] = None


@dataclass
class ValidationResult:
    """Result of code validation"""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    
    # Specific validation checks
    has_syntax_errors: bool = False
    has_security_issues: bool = False
    has_required_methods: bool = True
    inherits_from_base: bool = True
    
    # Performance analysis
    estimated_complexity: Optional[str] = None
    memory_usage_estimate: Optional[str] = None


@dataclass
class ChatbotResponse:
    """Response from the strategy chatbot"""
    success: bool
    message: str
    
    # Generated content
    generated_code: Optional[str] = None
    strategy_name: Optional[str] = None
    test_cases: List[TestCase] = field(default_factory=list)
    
    # Validation results
    validation_errors: List[str] = field(default_factory=list)
    validation_warnings: List[str] = field(default_factory=list)
    
    # Error handling
    error_message: Optional[str] = None
    suggestions: List[str] = field(default_factory=list)
    
    # Metadata
    processing_time: Optional[float] = None
    confidence_score: float = 0.0


@dataclass
class StrategyTemplate:
    """Template for common strategy patterns"""
    name: str
    description: str
    strategy_type: StrategyType
    template_code: str
    
    # Customizable parameters
    parameters: Dict[str, Any] = field(default_factory=dict)
    required_indicators: List[str] = field(default_factory=list)
    
    # Template metadata
    difficulty_level: str = "intermediate"  # beginner, intermediate, advanced
    estimated_performance: Dict[str, float] = field(default_factory=dict)
    tags: List[str] = field(default_factory=list)


@dataclass
class CodeExplanation:
    """Natural language explanation of strategy code"""
    summary: str
    detailed_explanation: str
    key_concepts: List[str]
    
    # Section-by-section breakdown
    method_explanations: Dict[str, str] = field(default_factory=dict)
    parameter_explanations: Dict[str, str] = field(default_factory=dict)
    
    # Educational content
    learning_resources: List[str] = field(default_factory=list)
    related_concepts: List[str] = field(default_factory=list)


@dataclass
class ConversationContext:
    """Context for ongoing chatbot conversation"""
    user_id: str
    session_id: str
    conversation_history: List[Dict[str, str]] = field(default_factory=list)
    
    # Current strategy being worked on
    current_strategy: Optional[GeneratedStrategy] = None
    current_request: Optional[StrategyRequest] = None
    
    # User preferences
    preferred_complexity: str = "intermediate"
    preferred_indicators: List[str] = field(default_factory=list)
    risk_tolerance: str = "moderate"
    
    # Session metadata
    created_at: datetime = field(default_factory=datetime.now)
    last_activity: datetime = field(default_factory=datetime.now)


@dataclass
class StrategyModification:
    """Request to modify an existing strategy"""
    original_code: str
    modification_request: str
    modification_type: str  # "add_indicator", "change_risk", "optimize", etc.
    
    # Specific changes
    new_parameters: Dict[str, Any] = field(default_factory=dict)
    additional_indicators: List[str] = field(default_factory=list)
    
    # Constraints
    preserve_core_logic: bool = True
    maintain_compatibility: bool = True


# Type aliases for better readability
StrategyCode = str
UserInput = str
ErrorMessage = str
SuccessMessage = str