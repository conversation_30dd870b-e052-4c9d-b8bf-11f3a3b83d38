/**
 * Enhanced Homepage Demo - Shows actual chatbot integration
 * This is a perfect blend of marketing homepage + functional chatbot
 */

import React, { useState, useEffect } from 'react';
import { Bot, MessageCircle, Zap, TrendingUp, Shield, Brain, Code } from 'lucide-react';
import StrategyChatbot from './StrategyChatbot';
import { aiPromptsService, AIPrompt } from '../services/aiPrompts';
import styles from './EnhancedHomepageDemo.module.css';

const EnhancedHomepageDemo: React.FC = () => {
  // Features array
  const features = [
    {
      icon: <MessageCircle size={48} />,
      title: 'AI-Powered Prompts',
      description: 'Access our library of proven trading prompts used by professional traders worldwide.'
    },
    {
      icon: <Brain size={48} />,
      title: 'Instant Code Generation',
      description: 'Advanced AI generates complete, tested Python trading strategies from natural language.'
    },
    {
      icon: <TrendingUp size={48} />,
      title: 'Real-Time Backtesting',
      description: 'Automatically backtest strategies with historical data and performance analytics.'
    },
    {
      icon: <Zap size={48} />,
      title: 'MT5 Integration',
      description: 'Deploy strategies directly to MetaTrader 5 with one-click automation.'
    },
    {
      icon: <Shield size={48} />,
      title: 'Built-in Risk Management',
      description: 'Every strategy includes position sizing, stop-loss, and risk control mechanisms.'
    },
    {
      icon: <Bot size={48} />,
      title: 'Continuous Learning',
      description: 'AI learns from market data and your trading performance to improve suggestions.'
    }
  ];

  // Demo prompts (replace with real data if available)
  const demoPrompts = [
    {
      id: '1',
      category: 'trend',
      title: 'Momentum Strategy for EUR/USD',
      description: 'Create a momentum strategy using MACD for EUR/USD with 2% risk.',
      variables: ['MACD', 'EUR/USD', 'Risk: 2%']
    },
    {
      id: '2',
      category: 'mean reversion',
      title: 'Mean Reversion GBP/USD',
      description: 'Mean reversion strategy for GBP/USD using RSI and Bollinger Bands.',
      variables: ['RSI', 'Bollinger Bands', 'GBP/USD']
    },
    {
      id: '3',
      category: 'breakout',
      title: 'Breakout Strategy for Gold',
      description: 'Breakout strategy for Gold using ATR and volume filter.',
      variables: ['ATR', 'Volume', 'Gold']
    },
    {
      id: '4',
      category: 'risk management',
      title: 'Risk Management Template',
      description: 'Template for position sizing and stop-loss calculation.',
      variables: ['Position Sizing', 'Stop-Loss']
    }
  ];

  const [selectedPrompt, setSelectedPrompt] = useState<any>(null);

  // Main layout
  return (
    <div>
      {/* Hero Section */}
      <section style={{ textAlign: 'center', padding: '3rem 1rem 2rem 1rem', background: '#111' }}>
        <h1 style={{ color: '#fff', fontSize: '2.8rem', fontWeight: 800, marginBottom: '1rem', textShadow: '0 0 12px #ff8000' }}>AI Enhanced Trading Platform</h1>
        <p style={{ color: '#ff8000', fontSize: '1.3rem', fontWeight: 500, marginBottom: '2rem', textShadow: '0 0 8px #ff8000' }}>
          Build, test, and deploy trading strategies with AI-powered code generation and backtesting.
        </p>
      </section>

      {/* Demo Section */}
      <section className={styles.homepageDemoContainer}>
        <div className={styles.demoCard}>
          <div className={styles.demoCardTitle}>Featured Strategy Prompts</div>
          <div className={styles.demoCardContent}>
            {demoPrompts.map((prompt, idx) => (
              <div key={prompt.id || idx} className={styles.demoCardNeon} onClick={() => setSelectedPrompt(prompt)} style={{cursor:'pointer', marginBottom:'1.5rem', background:'#222', borderRadius:'1rem', padding:'1rem', boxShadow:'0 0 8px #ff8000'}}>
                <div style={{marginBottom:'0.5rem'}}>{prompt.category}</div>
                <span style={{color:'#ff8000', fontWeight:600}}>{prompt.category.replace('_', ' ')}</span>
                <h4 style={{margin:'0.5rem 0', color:'#fff'}}>{prompt.title}</h4>
                <p style={{color:'#ccc'}}>{prompt.description}</p>
                <div style={{fontSize:'0.95rem', color:'#ff8000'}}>Variables: {prompt.variables?.join(', ') || 'Standard trading parameters'}</div>
              </div>
            ))}
          </div>
        </div>
        <div className={styles.demoChatbot}>
          <div className={styles.demoChatbotTitle}>Strategy Chatbot</div>
          <div className={styles.demoChatbotContent}>
            <StrategyChatbot initialPrompt={selectedPrompt?.title || ''} />
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section style={{ background: '#181818', padding: '2rem 0' }}>
        <h2 style={{ color: '#ff8000', textShadow: '0 0 8px #ff8000', textAlign: 'center' }}>Everything You Need to Trade Better</h2>
        <div style={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'center', gap: '2rem', marginTop: '2rem' }}>
          {features.map((feature, index) => (
            <div key={index} style={{ background: '#222', borderRadius: '1rem', boxShadow: '0 0 8px #ff8000', padding: '1.5rem', minWidth: '260px', maxWidth: '340px', color: '#fff', textAlign: 'center' }}>
              <div>{feature.icon}</div>
              <h3 style={{ color: '#ff8000', margin: '0.5rem 0' }}>{feature.title}</h3>
              <p style={{ color: '#fff' }}>{feature.description}</p>
            </div>
          ))}
        </div>
      </section>
    </div>
  );
};

export default EnhancedHomepageDemo;
