"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealthCheckResponseSchema = exports.RateLimitErrorResponseSchema = exports.AuthErrorResponseSchema = exports.ValidationErrorResponseSchema = exports.WebSocketSubscriptionSchema = exports.WebSocketMessageSchema = exports.GetDashboardDataApiResponseSchema = exports.GetMLPredictionsApiResponseSchema = exports.GetMLPredictionsApiRequestSchema = exports.GetMLPredictionApiResponseSchema = exports.GetMLPredictionApiRequestSchema = exports.DeleteUploadApiResponseSchema = exports.GetFilePreviewApiResponseSchema = exports.UpdateColumnMappingApiResponseSchema = exports.UpdateColumnMappingApiRequestSchema = exports.GetUploadApiResponseSchema = exports.GetUploadsApiResponseSchema = exports.GetUploadsApiRequestSchema = exports.CreateUploadApiResponseSchema = exports.CreateUploadApiRequestSchema = exports.SendChatMessageApiResponseSchema = exports.SendChatMessageApiRequestSchema = exports.GetChatMessagesApiResponseSchema = exports.GetChatMessagesApiRequestSchema = exports.GetChatSessionsApiResponseSchema = exports.GetChatSessionsApiRequestSchema = exports.CreateChatSessionApiResponseSchema = exports.CreateChatSessionApiRequestSchema = exports.DeleteBacktestApiResponseSchema = exports.GetBacktestResultsApiResponseSchema = exports.GetBacktestApiResponseSchema = exports.GetBacktestsApiResponseSchema = exports.GetBacktestsApiRequestSchema = exports.CreateBacktestApiResponseSchema = exports.CreateBacktestApiRequestSchema = exports.GetPositionsApiResponseSchema = exports.GetAccountInfoApiResponseSchema = exports.SubmitOrderApiResponseSchema = exports.SubmitOrderApiRequestSchema = exports.RegisterApiResponseSchema = exports.RegisterApiRequestSchema = exports.LoginApiResponseSchema = exports.LoginApiRequestSchema = void 0;
const zod_1 = require("zod");
const common_schemas_1 = require("./common.schemas");
const backtest_schemas_1 = require("./backtest.schemas");
const chat_schemas_1 = require("./chat.schemas");
const upload_schemas_1 = require("./upload.schemas");
const trading_schemas_1 = require("./trading.schemas");
// Auth API Schemas
exports.LoginApiRequestSchema = zod_1.z.object({
    email: zod_1.z.string().email(),
    password: zod_1.z.string().min(6),
});
exports.LoginApiResponseSchema = (0, common_schemas_1.ApiResponseSchema)(zod_1.z.object({
    user: zod_1.z.object({
        id: zod_1.z.string(),
        email: zod_1.z.string(),
        fullName: zod_1.z.string().optional(),
        subscriptionTier: zod_1.z.enum(['free', 'solo', 'pro', 'enterprise']),
    }),
    tokens: zod_1.z.object({
        accessToken: zod_1.z.string(),
        refreshToken: zod_1.z.string(),
        expiresIn: zod_1.z.number(),
    }),
}));
exports.RegisterApiRequestSchema = zod_1.z.object({
    email: zod_1.z.string().email(),
    password: zod_1.z.string().min(8),
    fullName: zod_1.z.string().optional(),
    acceptTerms: zod_1.z.boolean().refine(val => val === true, {
        message: "Must accept terms and conditions"
    }),
});
exports.RegisterApiResponseSchema = exports.LoginApiResponseSchema;
// Trading API Schemas
exports.SubmitOrderApiRequestSchema = trading_schemas_1.OrderRequestSchema;
exports.SubmitOrderApiResponseSchema = (0, common_schemas_1.ApiResponseSchema)(trading_schemas_1.OrderResultSchema);
exports.GetAccountInfoApiResponseSchema = (0, common_schemas_1.ApiResponseSchema)(trading_schemas_1.AccountInfoSchema);
exports.GetPositionsApiResponseSchema = (0, common_schemas_1.ApiResponseSchema)(zod_1.z.array(zod_1.z.object({
    position_id: zod_1.z.number(),
    symbol: zod_1.z.string(),
    volume: zod_1.z.number(),
    open_price: zod_1.z.number(),
    current_price: zod_1.z.number(),
    pnl: zod_1.z.number(),
    order_type: zod_1.z.enum(['buy', 'sell']),
    open_time: zod_1.z.date(),
})));
// Backtest API Schemas  
exports.CreateBacktestApiRequestSchema = backtest_schemas_1.CreateBacktestRequestSchema;
exports.CreateBacktestApiResponseSchema = (0, common_schemas_1.ApiResponseSchema)(backtest_schemas_1.BacktestSchema);
exports.GetBacktestsApiRequestSchema = common_schemas_1.PaginationRequestSchema.extend({
    status: zod_1.z.enum(['pending', 'running', 'completed', 'error']).optional(),
    symbol: zod_1.z.string().optional(),
});
exports.GetBacktestsApiResponseSchema = (0, common_schemas_1.ApiResponseSchema)(zod_1.z.object({
    backtests: zod_1.z.array(backtest_schemas_1.BacktestSchema),
    pagination: common_schemas_1.PaginationResponseSchema,
}));
exports.GetBacktestApiResponseSchema = (0, common_schemas_1.ApiResponseSchema)(backtest_schemas_1.BacktestSchema);
exports.GetBacktestResultsApiResponseSchema = (0, common_schemas_1.ApiResponseSchema)(backtest_schemas_1.BacktestResultsSchema);
exports.DeleteBacktestApiResponseSchema = (0, common_schemas_1.ApiResponseSchema)(zod_1.z.object({
    deleted: zod_1.z.boolean(),
}));
// Chat API Schemas
exports.CreateChatSessionApiRequestSchema = zod_1.z.object({
    title: zod_1.z.string().min(1).max(255).optional(),
    context: zod_1.z.object({
        trading_symbols: zod_1.z.array(zod_1.z.string()).optional(),
        timeframe: zod_1.z.string().optional(),
        strategy_focus: zod_1.z.string().optional(),
        risk_tolerance: zod_1.z.enum(['low', 'medium', 'high']).optional(),
    }).optional(),
});
exports.CreateChatSessionApiResponseSchema = (0, common_schemas_1.ApiResponseSchema)(chat_schemas_1.ChatSessionSchema);
exports.GetChatSessionsApiRequestSchema = common_schemas_1.PaginationRequestSchema;
exports.GetChatSessionsApiResponseSchema = (0, common_schemas_1.ApiResponseSchema)(zod_1.z.object({
    sessions: zod_1.z.array(chat_schemas_1.ChatSessionSchema),
    pagination: common_schemas_1.PaginationResponseSchema,
}));
exports.GetChatMessagesApiRequestSchema = common_schemas_1.PaginationRequestSchema;
exports.GetChatMessagesApiResponseSchema = (0, common_schemas_1.ApiResponseSchema)(zod_1.z.object({
    messages: zod_1.z.array(chat_schemas_1.ChatMessageSchema),
    pagination: common_schemas_1.PaginationResponseSchema,
}));
exports.SendChatMessageApiRequestSchema = zod_1.z.object({
    message: zod_1.z.string().min(1),
    session_id: zod_1.z.string().uuid(),
});
exports.SendChatMessageApiResponseSchema = (0, common_schemas_1.ApiResponseSchema)(chat_schemas_1.ChatResponseSchema);
// Upload API Schemas
exports.CreateUploadApiRequestSchema = upload_schemas_1.CreateUploadRequestSchema;
exports.CreateUploadApiResponseSchema = (0, common_schemas_1.ApiResponseSchema)(upload_schemas_1.DataFileUploadSchema);
exports.GetUploadsApiRequestSchema = common_schemas_1.PaginationRequestSchema.extend({
    status: zod_1.z.enum(['pending', 'uploading', 'mapping', 'parsing', 'validating', 'ready', 'error']).optional(),
    symbol: zod_1.z.string().optional(),
});
exports.GetUploadsApiResponseSchema = (0, common_schemas_1.ApiResponseSchema)(zod_1.z.object({
    uploads: zod_1.z.array(upload_schemas_1.DataFileUploadSchema),
    pagination: common_schemas_1.PaginationResponseSchema,
}));
exports.GetUploadApiResponseSchema = (0, common_schemas_1.ApiResponseSchema)(upload_schemas_1.DataFileUploadSchema);
exports.UpdateColumnMappingApiRequestSchema = upload_schemas_1.ColumnMappingRequestSchema;
exports.UpdateColumnMappingApiResponseSchema = (0, common_schemas_1.ApiResponseSchema)(upload_schemas_1.DataFileUploadSchema);
exports.GetFilePreviewApiResponseSchema = (0, common_schemas_1.ApiResponseSchema)(zod_1.z.object({
    headers: zod_1.z.array(zod_1.z.string()),
    sample_rows: zod_1.z.array(zod_1.z.array(zod_1.z.string())),
    detected_delimiter: zod_1.z.string().optional(),
    detected_encoding: zod_1.z.string().optional(),
    estimated_rows: zod_1.z.number(),
}));
exports.DeleteUploadApiResponseSchema = (0, common_schemas_1.ApiResponseSchema)(zod_1.z.object({
    deleted: zod_1.z.boolean(),
}));
// ML Prediction API Schemas
exports.GetMLPredictionApiRequestSchema = zod_1.z.object({
    symbol: zod_1.z.string(),
    prediction_type: zod_1.z.enum(['price', 'direction', 'volatility']),
    timeframe: zod_1.z.string(),
});
exports.GetMLPredictionApiResponseSchema = (0, common_schemas_1.ApiResponseSchema)(trading_schemas_1.MLPredictionSchema);
exports.GetMLPredictionsApiRequestSchema = common_schemas_1.PaginationRequestSchema.extend({
    symbol: zod_1.z.string().optional(),
    prediction_type: zod_1.z.enum(['price', 'direction', 'volatility']).optional(),
    min_confidence: zod_1.z.number().min(0).max(1).optional(),
});
exports.GetMLPredictionsApiResponseSchema = (0, common_schemas_1.ApiResponseSchema)(zod_1.z.object({
    predictions: zod_1.z.array(trading_schemas_1.MLPredictionSchema),
    pagination: common_schemas_1.PaginationResponseSchema,
}));
// Dashboard API Schemas
exports.GetDashboardDataApiResponseSchema = (0, common_schemas_1.ApiResponseSchema)(zod_1.z.object({
    account_summary: zod_1.z.object({
        balance: zod_1.z.number(),
        equity: zod_1.z.number(),
        margin_used: zod_1.z.number(),
        free_margin: zod_1.z.number(),
        profit_loss: zod_1.z.number(),
    }),
    active_positions: zod_1.z.array(zod_1.z.object({
        symbol: zod_1.z.string(),
        volume: zod_1.z.number(),
        pnl: zod_1.z.number(),
        open_price: zod_1.z.number(),
        current_price: zod_1.z.number(),
    })),
    recent_trades: zod_1.z.array(zod_1.z.object({
        symbol: zod_1.z.string(),
        order_type: zod_1.z.enum(['buy', 'sell']),
        volume: zod_1.z.number(),
        pnl: zod_1.z.number(),
        close_time: zod_1.z.date(),
    })),
    performance_metrics: zod_1.z.object({
        today_pnl: zod_1.z.number(),
        week_pnl: zod_1.z.number(),
        month_pnl: zod_1.z.number(),
        win_rate: zod_1.z.number(),
        total_trades: zod_1.z.number(),
    }),
    market_overview: zod_1.z.array(zod_1.z.object({
        symbol: zod_1.z.string(),
        current_price: zod_1.z.number(),
        change: zod_1.z.number(),
        change_percent: zod_1.z.number(),
    })),
}));
// WebSocket API Schemas
exports.WebSocketMessageSchema = zod_1.z.object({
    type: zod_1.z.enum([
        'price_update', 'position_update', 'order_update',
        'backtest_progress', 'chat_response', 'error'
    ]),
    payload: zod_1.z.any(),
    timestamp: zod_1.z.date(),
    id: zod_1.z.string().uuid().optional(),
});
exports.WebSocketSubscriptionSchema = zod_1.z.object({
    type: zod_1.z.enum(['subscribe', 'unsubscribe']),
    channel: zod_1.z.enum([
        'prices', 'positions', 'orders', 'backtest_progress', 'chat_updates'
    ]),
    symbol: zod_1.z.string().optional(),
    session_id: zod_1.z.string().uuid().optional(),
});
// Error Response Schemas
exports.ValidationErrorResponseSchema = (0, common_schemas_1.ApiResponseSchema)(zod_1.z.never()).extend({
    error: zod_1.z.object({
        code: zod_1.z.literal('VALIDATION_ERROR'),
        message: zod_1.z.string(),
        details: zod_1.z.array(zod_1.z.object({
            field: zod_1.z.string(),
            message: zod_1.z.string(),
            value: zod_1.z.any().optional(),
        })),
    }),
});
exports.AuthErrorResponseSchema = (0, common_schemas_1.ApiResponseSchema)(zod_1.z.never()).extend({
    error: zod_1.z.object({
        code: zod_1.z.enum(['UNAUTHORIZED', 'FORBIDDEN', 'TOKEN_EXPIRED']),
        message: zod_1.z.string(),
        details: zod_1.z.string().optional(),
    }),
});
exports.RateLimitErrorResponseSchema = (0, common_schemas_1.ApiResponseSchema)(zod_1.z.never()).extend({
    error: zod_1.z.object({
        code: zod_1.z.literal('RATE_LIMIT_EXCEEDED'),
        message: zod_1.z.string(),
        retry_after: zod_1.z.number().optional(),
    }),
});
// Health Check Schema
exports.HealthCheckResponseSchema = zod_1.z.object({
    status: zod_1.z.enum(['healthy', 'degraded', 'unhealthy']),
    timestamp: zod_1.z.date(),
    services: zod_1.z.object({
        database: zod_1.z.enum(['healthy', 'unhealthy']),
        python_engine: zod_1.z.enum(['healthy', 'unhealthy']),
        redis: zod_1.z.enum(['healthy', 'unhealthy']).optional(),
    }),
    version: zod_1.z.string(),
    uptime: zod_1.z.number(),
});
//# sourceMappingURL=api.schemas.js.map