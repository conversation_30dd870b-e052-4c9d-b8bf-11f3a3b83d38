# tests/test_darwin_godel_machine.py
import pytest
import numpy as np
from typing import Dict, List, Any
from dataclasses import dataclass
from datetime import datetime

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from evolution.darwin_godel_machine import DarwinGodelMachine, StrategyGenome

class TestDarwinGodelMachine:
    def test_deterministic_evolution(self):
        """Test that evolution is deterministic with same seed"""
        # Test that same seed produces same results
        dgm1 = DarwinGodelMachine(seed=12345)
        pop1_gen1 = dgm1.initialize_population(size=10)
        
        # Reset and create new instance with same seed
        dgm2 = DarwinGodelMachine(seed=12345)
        pop2_gen1 = dgm2.initialize_population(size=10)
        
        assert len(pop1_gen1) == len(pop2_gen1)
        
        # Check that parameters are the same for corresponding genomes
        for i in range(len(pop1_gen1)):
            # Compare each parameter individually for better debugging
            for param_name in pop1_gen1[i].parameters:
                assert abs(pop1_gen1[i].parameters[param_name] - pop2_gen1[i].parameters[param_name]) < 1e-10, \
                    f"Parameter {param_name} differs: {pop1_gen1[i].parameters[param_name]} vs {pop2_gen1[i].parameters[param_name]}"
    
    def test_fitness_calculation_integrity(self):
        """Test fitness calculation is based only on real backtest results"""
        dgm = DarwinGodelMachine(seed=12345)
        
        genome = StrategyGenome(
            parameters={'rsi_period': 14, 'ma_period': 20},
            fitness=0.0,
            generation=1,
            parent_ids=[],
            mutation_log=[],
            backtest_results={},
            id="test_genome_1"
        )
        
        # Mock backtest results
        backtest_results = {
            'total_return': 0.15,
            'sharpe_ratio': 1.2,
            'max_drawdown': 0.08,
            'trades_count': 150,
            'win_rate': 0.65,
            'data_integrity_hash': 'abc123...'
        }
        
        fitness = dgm.calculate_fitness(genome, backtest_results)
        assert isinstance(fitness, float)
        assert 0.0 <= fitness <= 1.0
    
    def test_mutation_auditability(self):
        """Test that all mutations are logged and auditable"""
        dgm = DarwinGodelMachine(seed=12345)
        
        parent = StrategyGenome(
            parameters={'rsi_period': 14, 'ma_period': 20},
            fitness=0.75,
            generation=1,
            parent_ids=[],
            mutation_log=[],
            backtest_results={},
            id="parent_1"
        )
        
        child = dgm.mutate(parent)
        
        assert child.generation == parent.generation + 1
        assert parent.id in child.parent_ids
        assert len(child.mutation_log) > 0
        assert child.id != parent.id

    def test_crossover_functionality(self):
        """Test crossover between two parents"""
        dgm = DarwinGodelMachine(seed=12345)
        
        parent1 = StrategyGenome(
            parameters={'rsi_period': 14, 'ma_period': 20, 'stop_loss': 0.02},
            fitness=0.75,
            generation=1,
            parent_ids=[],
            mutation_log=[],
            backtest_results={},
            id="parent_1"
        )
        
        parent2 = StrategyGenome(
            parameters={'rsi_period': 21, 'ma_period': 50, 'stop_loss': 0.03},
            fitness=0.80,
            generation=1,
            parent_ids=[],
            mutation_log=[],
            backtest_results={},
            id="parent_2"
        )
        
        child = dgm.crossover(parent1, parent2)
        
        assert child.generation == 2
        assert parent1.id in child.parent_ids
        assert parent2.id in child.parent_ids
        assert len(child.mutation_log) > 0
        assert child.id != parent1.id and child.id != parent2.id
        
        # Child should have parameters from both parents
        for param in child.parameters:
            assert (child.parameters[param] == parent1.parameters[param] or 
                   child.parameters[param] == parent2.parameters[param])

    def test_population_initialization(self):
        """Test population initialization with proper parameters"""
        dgm = DarwinGodelMachine(seed=12345)
        
        population = dgm.initialize_population(size=20)
        
        assert len(population) == 20
        
        for genome in population:
            assert genome.generation == 0
            assert len(genome.parent_ids) == 0
            assert len(genome.mutation_log) > 0
            assert genome.fitness == 0.0
            assert genome.id is not None
            
            # Check parameter ranges
            assert 10 <= genome.parameters['rsi_period'] <= 30
            assert 20 <= genome.parameters['rsi_oversold'] <= 40
            assert 60 <= genome.parameters['rsi_overbought'] <= 80
            assert 10 <= genome.parameters['ma_period'] <= 50
            assert 0.01 <= genome.parameters['stop_loss'] <= 0.05
            assert 0.02 <= genome.parameters['take_profit'] <= 0.10

    def test_evolution_generation(self):
        """Test evolution to next generation"""
        dgm = DarwinGodelMachine(seed=12345)
        
        # Initialize population
        population = dgm.initialize_population(size=10)
        
        # Set some fitness values
        for i, genome in enumerate(population):
            backtest_results = {
                'total_return': 0.1 + i * 0.01,
                'sharpe_ratio': 1.0 + i * 0.1,
                'max_drawdown': 0.05,
                'trades_count': 100,
                'win_rate': 0.6
            }
            dgm.calculate_fitness(genome, backtest_results)
        
        # Evolve to next generation
        next_generation = dgm.evolve_generation(population)
        
        assert len(next_generation) == len(population)
        
        # Check that some genomes are from next generation
        next_gen_genomes = [g for g in next_generation if g.generation > 0]
        assert len(next_gen_genomes) > 0

    def test_backtest_integrity_verification(self):
        """Test backtest results integrity verification"""
        dgm = DarwinGodelMachine(seed=12345)
        
        genome = StrategyGenome(
            parameters={'rsi_period': 14},
            fitness=0.0,
            generation=1,
            parent_ids=[],
            mutation_log=[],
            backtest_results={},
            id="test_genome"
        )
        
        # Invalid backtest results (missing required fields)
        invalid_results = {
            'total_return': 0.15,
            # Missing other required fields
        }
        
        with pytest.raises(ValueError):
            dgm.calculate_fitness(genome, invalid_results)
        
        # Valid backtest results
        valid_results = {
            'total_return': 0.15,
            'sharpe_ratio': 1.2,
            'max_drawdown': 0.08,
            'trades_count': 150,
            'win_rate': 0.65
        }
        
        fitness = dgm.calculate_fitness(genome, valid_results)
        assert fitness > 0

    def test_audit_trail_generation(self):
        """Test generation of complete audit trail"""
        dgm = DarwinGodelMachine(seed=12345)
        
        # Initialize and evolve a few generations
        population = dgm.initialize_population(size=5)
        
        # Set fitness and evolve
        for genome in population:
            backtest_results = {
                'total_return': 0.1,
                'sharpe_ratio': 1.0,
                'max_drawdown': 0.05,
                'trades_count': 100,
                'win_rate': 0.6
            }
            dgm.calculate_fitness(genome, backtest_results)
        
        next_gen = dgm.evolve_generation(population)
        
        # Get audit trail
        audit_trail = dgm.get_evolution_audit_trail()
        
        assert audit_trail['seed'] == 12345
        assert audit_trail['generations_count'] == 2  # Initial + 1 evolved
        assert 'fitness_function_params' in audit_trail
        assert 'mutation_rates' in audit_trail
        assert len(audit_trail['generation_stats']) == 2

    def test_best_genome_retrieval(self):
        """Test retrieval of best genome across all generations"""
        dgm = DarwinGodelMachine(seed=12345)
        
        population = dgm.initialize_population(size=5)
        
        # Set different fitness values
        for i, genome in enumerate(population):
            backtest_results = {
                'total_return': 0.1 + i * 0.05,  # Increasing returns
                'sharpe_ratio': 1.0,
                'max_drawdown': 0.05,
                'trades_count': 100,
                'win_rate': 0.6
            }
            dgm.calculate_fitness(genome, backtest_results)
        
        best_genome = dgm.get_best_genome()
        
        assert best_genome is not None
        assert best_genome.fitness > 0
        
        # Should be the genome with highest fitness
        all_fitness = [g.fitness for g in population]
        assert best_genome.fitness == max(all_fitness)

    def test_genome_lineage_export(self):
        """Test export of genome lineage"""
        dgm = DarwinGodelMachine(seed=12345)
        
        population = dgm.initialize_population(size=3)
        
        # Set fitness
        for genome in population:
            backtest_results = {
                'total_return': 0.1,
                'sharpe_ratio': 1.0,
                'max_drawdown': 0.05,
                'trades_count': 100,
                'win_rate': 0.6
            }
            dgm.calculate_fitness(genome, backtest_results)
        
        # Evolve one generation
        next_gen = dgm.evolve_generation(population)
        
        # Get lineage of a genome from second generation
        child_genome = next_gen[0]
        lineage = dgm.export_genome_lineage(child_genome.id)
        
        assert 'genome_id' in lineage
        assert 'lineage' in lineage
        assert 'total_generations' in lineage
        assert lineage['genome_id'] == child_genome.id
        assert len(lineage['lineage']) > 0

    def test_tournament_selection(self):
        """Test tournament selection mechanism"""
        dgm = DarwinGodelMachine(seed=12345)
        
        population = dgm.initialize_population(size=10)
        
        # Set different fitness values
        for i, genome in enumerate(population):
            genome.fitness = i * 0.1  # 0.0, 0.1, 0.2, ..., 0.9
        
        # Run tournament selection multiple times
        selected_genomes = []
        for _ in range(20):
            selected = dgm._tournament_selection(population, tournament_size=3)
            selected_genomes.append(selected)
        
        # Higher fitness genomes should be selected more often
        fitness_counts = {}
        for genome in selected_genomes:
            fitness = genome.fitness
            fitness_counts[fitness] = fitness_counts.get(fitness, 0) + 1
        
        # Check that higher fitness values appear more frequently
        high_fitness_selections = sum(count for fitness, count in fitness_counts.items() if fitness >= 0.5)
        low_fitness_selections = sum(count for fitness, count in fitness_counts.items() if fitness < 0.5)
        
        assert high_fitness_selections >= low_fitness_selections