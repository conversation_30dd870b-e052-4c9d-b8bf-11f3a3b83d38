"""
MT5 Bridge API routes
"""

import logging
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, Request
from pydantic import BaseModel, Field
from sqlalchemy.ext.asyncio import AsyncSession

# Import MT5 Bridge
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../../src'))
from trading.mt5_bridge_tdd import MT5Bridge

# Import JWT Bearer for authentication
from ...auth.jwt_handler import JWTBearer
from ...db.config import get_db
from ...db.crud import mt5_account as mt5_account_crud
from ...db.crud import position as position_crud
from ...db.crud import order as order_crud

# Configure logging
logger = logging.getLogger("api.mt5")

# Create router
router = APIRouter(dependencies=[Depends(JWTBearer())])

# MT5 Bridge instance (singleton)
_mt5_bridge: Optional[MT5Bridge] = None

def get_mt5_bridge() -> MT5Bridge:
    """Get or create MT5 Bridge instance"""
    global _mt5_bridge
    if _mt5_bridge is None:
        logger.info("Creating new MT5 Bridge instance")
        _mt5_bridge = MT5Bridge(offline_mode=False)
    return _mt5_bridge

# Request/Response models
class MT5AccountCreate(BaseModel):
    """MT5 Account create model"""
    name: str = Field(..., description="Account name")
    server: str = Field(..., description="MT5 server")
    login: str = Field(..., description="MT5 login")
    password: str = Field(..., description="MT5 password")
    is_demo: bool = Field(True, description="Is demo account")

class MT5AccountResponse(BaseModel):
    """MT5 Account response model"""
    id: uuid.UUID = Field(..., description="Account ID")
    name: str = Field(..., description="Account name")
    server: str = Field(..., description="MT5 server")
    login: str = Field(..., description="MT5 login")
    is_demo: bool = Field(..., description="Is demo account")
    is_active: bool = Field(..., description="Is active")
    created_at: datetime = Field(..., description="Created at")
    last_connected_at: Optional[datetime] = Field(None, description="Last connected at")

class OrderRequest(BaseModel):
    """Order request model"""
    symbol: str = Field(..., description="Trading symbol (e.g., 'EURUSD')")
    orderType: str = Field(..., description="Order type (BUY, SELL, BUY_LIMIT, etc.)")
    volume: float = Field(..., gt=0, description="Order volume in lots")
    price: Optional[float] = Field(None, description="Order price (for pending orders)")
    stopLoss: Optional[float] = Field(None, description="Stop loss price")
    takeProfit: Optional[float] = Field(None, description="Take profit price")
    mt5_account_id: Optional[uuid.UUID] = Field(None, description="MT5 account ID")

class OrderResponse(BaseModel):
    """Order response model"""
    orderId: int = Field(..., description="Order ID")
    success: bool = Field(..., description="Success status")

class StatusResponse(BaseModel):
    """Status response model"""
    connected: bool = Field(..., description="Connection status")
    accountInfo: Optional[Dict[str, Any]] = Field(None, description="Account information")
    positions: List[Dict[str, Any]] = Field([], description="Open positions")
    lastError: Optional[str] = Field(None, description="Last error message")

class SuccessResponse(BaseModel):
    """Success response model"""
    success: bool = Field(..., description="Success status")

# MT5 Account endpoints
@router.post("/accounts", response_model=MT5AccountResponse)
async def create_mt5_account(
    account_data: MT5AccountCreate,
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """Create a new MT5 account"""
    try:
        # Get user from request state
        user = request.state.user
        
        # Create MT5 account
        account = await mt5_account_crud.create_mt5_account(
            db,
            user_id=user.id,
            name=account_data.name,
            server=account_data.server,
            login=account_data.login,
            password=account_data.password,
            is_demo=account_data.is_demo
        )
        
        return account
    except Exception as e:
        logger.error(f"Error creating MT5 account: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/accounts", response_model=List[MT5AccountResponse])
async def get_mt5_accounts(
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """Get all MT5 accounts for the current user"""
    try:
        # Get user from request state
        user = request.state.user
        
        # Get MT5 accounts
        accounts = await mt5_account_crud.get_mt5_accounts_by_user(db, user.id)
        
        return accounts
    except Exception as e:
        logger.error(f"Error getting MT5 accounts: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/accounts/{account_id}", response_model=MT5AccountResponse)
async def get_mt5_account(
    account_id: uuid.UUID,
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """Get an MT5 account by ID"""
    try:
        # Get user from request state
        user = request.state.user
        
        # Get MT5 account
        account = await mt5_account_crud.get_mt5_account_by_id(db, account_id)
        
        # Check if account exists and belongs to the user
        if not account or account.user_id != user.id:
            raise HTTPException(status_code=404, detail="MT5 account not found")
        
        return account
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting MT5 account: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/accounts/{account_id}", response_model=SuccessResponse)
async def delete_mt5_account(
    account_id: uuid.UUID,
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """Delete an MT5 account"""
    try:
        # Get user from request state
        user = request.state.user
        
        # Get MT5 account
        account = await mt5_account_crud.get_mt5_account_by_id(db, account_id)
        
        # Check if account exists and belongs to the user
        if not account or account.user_id != user.id:
            raise HTTPException(status_code=404, detail="MT5 account not found")
        
        # Delete MT5 account
        result = await mt5_account_crud.delete_mt5_account(db, account_id)
        
        return {"success": result}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting MT5 account: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# MT5 Bridge endpoints
@router.get("/status", response_model=StatusResponse)
async def get_status(
    bridge: MT5Bridge = Depends(get_mt5_bridge),
    account_id: Optional[uuid.UUID] = None,
    db: AsyncSession = Depends(get_db),
    request: Request = None
):
    """Get MT5 Bridge status"""
    try:
        # Get account info if connected
        account_info = None
        if bridge.is_connected():
            # In a real implementation, you would get this from MT5
            # For now, we'll use dummy data
            account_info = {
                "balance": 10000.0,
                "equity": 10050.0,
                "margin": 100.0,
                "freeMargin": 9950.0,
                "leverage": 100,
                "name": "Demo Account"
            }
        
        # Get positions
        positions = bridge.get_positions()
        
        # Format positions for response
        formatted_positions = []
        for pos in positions:
            formatted_positions.append({
                "id": pos["id"],
                "symbol": pos["symbol"],
                "type": pos["type"],
                "volume": pos["lot"],
                "openPrice": pos["price"],
                "currentPrice": pos.get("current_price", pos["price"]),  # Might not be available in offline mode
                "profit": pos.get("profit", 0.0),
                "openTime": pos.get("time", "").isoformat() if hasattr(pos.get("time", ""), "isoformat") else pos.get("time", "")
            })
        
        return {
            "connected": bridge.is_connected(),
            "accountInfo": account_info,
            "positions": formatted_positions,
            "lastError": None
        }
    except Exception as e:
        logger.error(f"Error getting status: {str(e)}")
        return {
            "connected": bridge.is_connected(),
            "accountInfo": None,
            "positions": [],
            "lastError": str(e)
        }

@router.post("/connect", response_model=SuccessResponse)
async def connect(bridge: MT5Bridge = Depends(get_mt5_bridge)):
    """Connect to MT5"""
    try:
        result = bridge.connect()
        if not result:
            raise HTTPException(status_code=500, detail="Failed to connect to MT5")
        return {"success": True}
    except Exception as e:
        logger.error(f"Error connecting to MT5: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/disconnect", response_model=SuccessResponse)
async def disconnect(bridge: MT5Bridge = Depends(get_mt5_bridge)):
    """Disconnect from MT5"""
    try:
        bridge.disconnect()
        return {"success": True}
    except Exception as e:
        logger.error(f"Error disconnecting from MT5: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/order", response_model=OrderResponse)
async def place_order(order: OrderRequest, bridge: MT5Bridge = Depends(get_mt5_bridge)):
    """Place an order"""
    try:
        if not bridge.is_connected():
            bridge.connect()
            if not bridge.is_connected():
                raise HTTPException(status_code=500, detail="Failed to connect to MT5")
        
        order_id = bridge.place_order(
            symbol=order.symbol,
            order_type=order.orderType,
            lot=order.volume,
            price=order.price,
            stop_loss=order.stopLoss,
            take_profit=order.takeProfit
        )
        
        return {"orderId": order_id, "success": True}
    except ValueError as e:
        logger.error(f"Invalid order parameters: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error placing order: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/positions", response_model=List[Dict[str, Any]])
async def get_positions(bridge: MT5Bridge = Depends(get_mt5_bridge)):
    """Get open positions"""
    try:
        if not bridge.is_connected():
            bridge.connect()
            if not bridge.is_connected():
                raise HTTPException(status_code=500, detail="Failed to connect to MT5")
        
        positions = bridge.get_positions()
        
        # Format positions for response
        formatted_positions = []
        for pos in positions:
            formatted_positions.append({
                "id": pos["id"],
                "symbol": pos["symbol"],
                "type": pos["type"],
                "volume": pos["lot"],
                "openPrice": pos["price"],
                "currentPrice": pos.get("current_price", pos["price"]),  # Might not be available in offline mode
                "profit": pos.get("profit", 0.0),
                "openTime": pos.get("time", "").isoformat() if hasattr(pos.get("time", ""), "isoformat") else pos.get("time", "")
            })
        
        return formatted_positions
    except Exception as e:
        logger.error(f"Error getting positions: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/positions/{order_id}/close", response_model=SuccessResponse)
async def close_position(order_id: int, bridge: MT5Bridge = Depends(get_mt5_bridge)):
    """Close a position"""
    try:
        if not bridge.is_connected():
            bridge.connect()
            if not bridge.is_connected():
                raise HTTPException(status_code=500, detail="Failed to connect to MT5")
        
        result = bridge.close_order(order_id)
        if not result:
            raise HTTPException(status_code=404, detail=f"Order {order_id} not found or could not be closed")
        
        return {"success": True}
    except Exception as e:
        logger.error(f"Error closing position: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))