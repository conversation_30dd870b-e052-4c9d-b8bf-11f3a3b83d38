from RestrictedPython import compile_restricted, safe_globals, safe_builtins
from RestrictedPython.Guards import guarded_iter_unpack_sequence, safer_getattr
from typing import Dict, Any, List, Optional
import ast
import math
import statistics
import time
import threading
import platform
import sys
import tracemalloc

# Import resource module only on Unix-like systems
try:
    import resource
    HAS_RESOURCE = True
except ImportError:
    HAS_RESOURCE = False


class SecurityError(Exception):
    """Raised when strategy code violates security constraints"""
    pass


class TimeoutError(SecurityError):
    """Raised when strategy execution times out"""
    pass


class SecureStrategyExecutor:
    """
    Secure execution environment for user-provided trading strategies.
    
    Uses RestrictedPython to safely execute strategy code with:
    - Limited built-in functions
    - No file system access
    - No network access
    - Memory and execution time limits
    - Pre-defined technical indicators
    """
    
    def __init__(self, max_memory_mb: int = 100, default_timeout: int = 30):
        self.max_memory_mb = max_memory_mb
        self.default_timeout = default_timeout
        
        # Use safe builtins from RestrictedPython
        self.safe_builtins = safe_builtins.copy()
        self.safe_builtins.update({
            'len': len,
            'range': range,
            'enumerate': enumerate,
            'zip': zip,
            'min': min,
            'max': max,
            'sum': sum,
            'abs': abs,
            'round': round,
            'list': list,
            'dict': dict,
            'tuple': tuple,
            'set': set,
            'bool': bool,
            'int': int,
            'float': float,
            'str': str,
            'sorted': sorted,
            'reversed': reversed,
            'map': map,
            'filter': filter,
            'all': all,
            'any': any,
            'isinstance': isinstance,
            'type': type,
            # Essential guards for RestrictedPython
            '_getitem_': self._safe_getitem,
            '_getattr_': safer_getattr,
            '_getiter_': self._safe_getiter,
            '_iter_unpack_sequence_': guarded_iter_unpack_sequence,
            '__import__': self._restricted_import,
        })
        
        # Define safe modules and functions
        self.safe_globals = safe_globals.copy()
        self.safe_globals.update({
            '__builtins__': self.safe_builtins,
            '__name__': '__main__',
            '__metaclass__': type,
            # Essential guards
            '_getitem_': self._safe_getitem,
            '_getattr_': safer_getattr,
            '_getiter_': self._safe_getiter,
            '_iter_unpack_sequence_': guarded_iter_unpack_sequence,
            '__import__': self._restricted_import,
            # Math functions
            'math': self._create_safe_math_module(),
            'statistics': self._create_safe_statistics_module(),
            # Technical indicators
            'calculate_sma': self._calculate_sma,
            'calculate_rsi': self._calculate_rsi,
            'calculate_macd': self._calculate_macd,
            'calculate_bollinger': self._calculate_bollinger,
            'calculate_ema': self._calculate_ema,
            'calculate_stochastic': self._calculate_stochastic,
            'calculate_returns': self._calculate_returns,
        })

    def execute(self, strategy_code: str, data: Dict[str, Any], 
                params: Dict[str, Any], timeout: Optional[int] = None) -> Dict[str, Any]:
        """
        Safely execute user-provided strategy code
        
        Args:
            strategy_code: Python code defining trading strategy
            data: Market data dictionary
            params: Strategy parameters
            timeout: Execution timeout in seconds (optional)
            
        Returns:
            Strategy execution results
            
        Raises:
            SecurityError: If code contains restricted operations
            ValueError: If strategy function not found or invalid inputs
            TimeoutError: If execution exceeds timeout
        """
        # Validate inputs
        self._validate_inputs(data, params)
        
        # Set timeout
        execution_timeout = timeout or self.default_timeout
        
        try:
            # Set memory limit
            self._set_memory_limit()
            
            # Compile with restrictions
            try:
                byte_code = compile_restricted(
                    strategy_code, 
                    filename='<strategy>', 
                    mode='exec'
                )
                
                # In newer versions of RestrictedPython, compile_restricted returns a code object directly
                # or raises an exception if there are compilation errors
                if byte_code is None:
                    raise SecurityError("Failed to compile restricted code")
                    
            except (SyntaxError, ValueError) as e:
                raise SecurityError(f"Restricted code compilation error: {str(e)}")
            
            # Create isolated execution environment
            exec_globals = self.safe_globals.copy()
            exec_locals = {}
            
            # Execute with timeout
            result = self._execute_with_timeout(
                byte_code, 
                exec_globals, 
                exec_locals, 
                data, 
                params, 
                execution_timeout
            )
            
            return result
            
        except Exception as e:
            if isinstance(e, (SecurityError, ValueError, TimeoutError)):
                raise
            raise SecurityError(f"Strategy execution failed: {str(e)}")

    def _validate_inputs(self, data: Any, params: Any) -> None:
        """Validate input data types and structure"""
        if not isinstance(data, dict):
            raise ValueError("data must be a dictionary")
        
        if not isinstance(params, dict):
            raise ValueError("params must be a dictionary")
        
        # Validate required data fields
        if 'close' not in data:
            raise ValueError("data must contain 'close' prices")
        
        if not isinstance(data['close'], (list, tuple)):
            raise ValueError("data['close'] must be a list or tuple")
        
        if len(data['close']) == 0:
            raise ValueError("data['close'] cannot be empty")

    def _set_memory_limit(self) -> None:
        """Set memory usage limit for the process"""
        # Start memory tracing for all platforms
        try:
            tracemalloc.start()
        except RuntimeError:
            # Already started
            pass
            
        if HAS_RESOURCE:
            try:
                # Set virtual memory limit (in bytes) on Unix-like systems
                memory_limit = self.max_memory_mb * 1024 * 1024
                resource.setrlimit(resource.RLIMIT_AS, (memory_limit, memory_limit))
            except (OSError, ValueError, AttributeError):
                # Memory limiting not supported on this platform
                pass

    def _execute_with_timeout(self, code, exec_globals, exec_locals, 
                             data, params, timeout) -> Dict[str, Any]:
        """Execute code with timeout protection"""
        result = {}
        exception = None
        
        def target():
            nonlocal result, exception
            try:
                # Check memory before execution
                self._check_memory_usage()
                
                # Execute strategy code
                exec(code, exec_globals, exec_locals)
                
                # Check memory after code compilation
                self._check_memory_usage()
                
                # Extract strategy function
                strategy_func = exec_locals.get('trading_strategy')
                if not strategy_func:
                    raise ValueError(
                        "Strategy must define 'trading_strategy' function"
                    )
                
                # Execute strategy with data
                result = strategy_func(data, params)
                
                # Check memory after execution
                self._check_memory_usage()
                
                # Validate result
                if not isinstance(result, dict):
                    raise ValueError("Strategy must return a dictionary")
                
                if 'signal' not in result:
                    raise ValueError("Strategy result must contain 'signal' field")
                
                if result['signal'] not in ['buy', 'sell', 'hold']:
                    raise ValueError("Signal must be 'buy', 'sell', or 'hold'")
                    
            except Exception as e:
                exception = e
        
        # Run in separate thread with timeout
        thread = threading.Thread(target=target)
        thread.daemon = True
        thread.start()
        thread.join(timeout)
        
        if thread.is_alive():
            # Force thread termination (not ideal but necessary for security)
            raise TimeoutError(f"Strategy execution timeout after {timeout} seconds")
        
        if exception:
            if isinstance(exception, (SecurityError, ValueError)):
                raise exception
            raise SecurityError(f"Strategy execution error: {str(exception)}")
        
        return result

    def _create_safe_math_module(self):
        """Create a safe math module with limited functions"""
        return type('math', (), {
            'sqrt': math.sqrt,
            'pow': math.pow,
            'log': math.log,
            'log10': math.log10,
            'exp': math.exp,
            'sin': math.sin,
            'cos': math.cos,
            'tan': math.tan,
            'pi': math.pi,
            'e': math.e,
            'ceil': math.ceil,
            'floor': math.floor,
            'fabs': math.fabs,
            'isnan': math.isnan,
            'isinf': math.isinf,
        })

    def _create_safe_statistics_module(self):
        """Create a safe statistics module"""
        return type('statistics', (), {
            'mean': statistics.mean,
            'median': statistics.median,
            'mode': statistics.mode,
            'stdev': statistics.stdev,
            'variance': statistics.variance,
        })

    # Technical Indicators Implementation
    
    def _calculate_sma(self, values: List[float], period: int) -> List[float]:
        """Simple Moving Average calculation"""
        if not isinstance(values, (list, tuple)) or not values:
            return []
        
        if not isinstance(period, int) or period <= 0:
            raise ValueError("Period must be a positive integer")
        
        if len(values) < period:
            return []
        
        sma = []
        for i in range(period - 1, len(values)):
            avg = sum(values[i - period + 1:i + 1]) / period
            sma.append(avg)
        
        return sma

    def _calculate_ema(self, values: List[float], period: int) -> List[float]:
        """Exponential Moving Average calculation"""
        if not isinstance(values, (list, tuple)) or not values:
            return []
        
        if not isinstance(period, int) or period <= 0:
            raise ValueError("Period must be a positive integer")
        
        if len(values) < period:
            return []
        
        multiplier = 2 / (period + 1)
        ema = [sum(values[:period]) / period]  # Start with SMA
        
        for i in range(period, len(values)):
            ema_value = (values[i] * multiplier) + (ema[-1] * (1 - multiplier))
            ema.append(ema_value)
        
        return ema

    def _calculate_rsi(self, values: List[float], period: int = 14) -> List[float]:
        """Relative Strength Index calculation"""
        if not isinstance(values, (list, tuple)) or len(values) < period + 1:
            return []
        
        if not isinstance(period, int) or period <= 0:
            raise ValueError("Period must be a positive integer")
        
        deltas = [values[i] - values[i-1] for i in range(1, len(values))]
        gains = [delta if delta > 0 else 0 for delta in deltas]
        losses = [-delta if delta < 0 else 0 for delta in deltas]
        
        # Calculate initial averages
        avg_gain = sum(gains[:period]) / period
        avg_loss = sum(losses[:period]) / period
        
        rsi = []
        
        for i in range(period, len(gains)):
            if avg_loss == 0:
                rsi.append(100)
            else:
                rs = avg_gain / avg_loss
                rsi_value = 100 - (100 / (1 + rs))
                rsi.append(rsi_value)
            
            # Update averages
            avg_gain = ((avg_gain * (period - 1)) + gains[i]) / period
            avg_loss = ((avg_loss * (period - 1)) + losses[i]) / period
        
        return rsi

    def _calculate_macd(self, values: List[float], fast: int = 12, 
                       slow: int = 26, signal: int = 9) -> Dict[str, List[float]]:
        """MACD calculation"""
        if not isinstance(values, (list, tuple)) or len(values) < slow:
            return {}
        
        ema_fast = self._calculate_ema(values, fast)
        ema_slow = self._calculate_ema(values, slow)
        
        if not ema_fast or not ema_slow:
            return {}
        
        # Align the EMAs (slow EMA starts later)
        start_idx = slow - fast
        macd_line = [ema_fast[i + start_idx] - ema_slow[i] 
                     for i in range(len(ema_slow))]
        
        signal_line = self._calculate_ema(macd_line, signal)
        
        # Calculate histogram
        if len(signal_line) > 0:
            histogram = [macd_line[i + len(macd_line) - len(signal_line)] - signal_line[i] 
                        for i in range(len(signal_line))]
        else:
            histogram = []
        
        return {
            'macd': macd_line,
            'signal': signal_line,
            'histogram': histogram
        }

    def _calculate_bollinger(self, values: List[float], period: int = 20, 
                           std_dev: float = 2) -> Dict[str, List[float]]:
        """Bollinger Bands calculation"""
        if not isinstance(values, (list, tuple)) or len(values) < period:
            return {}
        
        sma = self._calculate_sma(values, period)
        if not sma:
            return {}
        
        upper_band = []
        lower_band = []
        
        for i in range(len(sma)):
            data_slice = values[i:i + period]
            std = statistics.stdev(data_slice)
            
            upper_band.append(sma[i] + (std_dev * std))
            lower_band.append(sma[i] - (std_dev * std))
        
        return {
            'upper': upper_band,
            'middle': sma,
            'lower': lower_band
        }

    def _calculate_stochastic(self, high: List[float], low: List[float], 
                             close: List[float], k_period: int = 14, 
                             d_period: int = 3) -> Dict[str, List[float]]:
        """Stochastic Oscillator calculation"""
        if (not all(isinstance(x, (list, tuple)) for x in [high, low, close]) or
            len(high) != len(low) or len(low) != len(close) or
            len(close) < k_period):
            return {}
        
        k_values = []
        
        for i in range(k_period - 1, len(close)):
            period_high = max(high[i - k_period + 1:i + 1])
            period_low = min(low[i - k_period + 1:i + 1])
            
            if period_high == period_low:
                k_values.append(50)  # Avoid division by zero
            else:
                k_value = ((close[i] - period_low) / (period_high - period_low)) * 100
                k_values.append(k_value)
        
        # Calculate %D (moving average of %K)
        d_values = self._calculate_sma(k_values, d_period)
        
        return {
            'k': k_values,
            'd': d_values
        }

    def _calculate_returns(self, values: List[float]) -> List[float]:
        """Calculate returns"""
        if not isinstance(values, (list, tuple)) or len(values) < 2:
            return []
        
        returns = []
        for i in range(1, len(values)):
            ret = (values[i] - values[i-1]) / values[i-1]
            returns.append(ret)
        
        return returns

    # Security guard methods for RestrictedPython
    
    def _safe_getitem(self, obj, key):
        """Safe getitem implementation for RestrictedPython"""
        if isinstance(obj, dict):
            return obj[key]
        elif isinstance(obj, (list, tuple)):
            return obj[key]
        elif hasattr(obj, '__getitem__'):
            return obj[key]
        else:
            raise TypeError(f"'{type(obj).__name__}' object is not subscriptable")
    
    def _safe_getiter(self, obj):
        """Safe getiter implementation for RestrictedPython"""
        if hasattr(obj, '__iter__'):
            return iter(obj)
        else:
            raise TypeError(f"'{type(obj).__name__}' object is not iterable")
    

    
    def _restricted_import(self, name, globals=None, locals=None, fromlist=(), level=0):
        """Restricted import function - only allow safe modules"""
        allowed_modules = {
            'math': self._create_safe_math_module(),
            'statistics': self._create_safe_statistics_module(),
        }
        
        if name in allowed_modules:
            return allowed_modules[name]
        else:
            raise ImportError(f"Import of module '{name}' is not allowed")
    
    def _check_memory_usage(self):
        """Check current memory usage and raise error if exceeded"""
        try:
            current, peak = tracemalloc.get_traced_memory()
            current_mb = current / 1024 / 1024
            
            if current_mb > self.max_memory_mb:
                raise SecurityError(f"Memory usage exceeded limit: {current_mb:.1f}MB > {self.max_memory_mb}MB")
        except Exception:
            # If memory tracking fails, continue execution
            pass