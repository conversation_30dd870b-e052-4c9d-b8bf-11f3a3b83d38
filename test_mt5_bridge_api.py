#!/usr/bin/env python
"""
Test script for MT5 Bridge API
This script tests the MT5 Bridge implementation directly
"""

import sys
import os
import logging
import json
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("mt5_bridge_api_test")

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Import MT5 Bridge
try:
    from trading.mt5_bridge_tdd import MT5Bridge
    logger.info("Successfully imported MT5Bridge")
except ImportError as e:
    logger.error(f"Failed to import MT5Bridge: {e}")
    sys.exit(1)

class MockRequest:
    """Mock request object for testing"""
    def __init__(self, **kwargs):
        self.__dict__.update(kwargs)

def test_get_status():
    """Test get_status endpoint"""
    logger.info("Testing get_status endpoint...")
    
    bridge = MT5Bridge(offline_mode=True)
    
    # Get account info
    account_info = {
        "balance": 10000.0,
        "equity": 10050.0,
        "margin": 100.0,
        "freeMargin": 9950.0,
        "leverage": 100,
        "name": "Demo Account"
    }
    
    # Get positions
    positions = bridge.get_positions()
    
    # Format positions for response
    formatted_positions = []
    for pos in positions:
        formatted_positions.append({
            "id": pos["id"],
            "symbol": pos["symbol"],
            "type": pos["type"],
            "volume": pos["lot"],
            "openPrice": pos["price"],
            "currentPrice": pos.get("current_price", pos["price"]),
            "profit": pos.get("profit", 0.0),
            "openTime": pos.get("time", "").isoformat() if hasattr(pos.get("time", ""), "isoformat") else pos.get("time", "")
        })
    
    response = {
        "connected": bridge.is_connected(),
        "accountInfo": account_info,
        "positions": formatted_positions,
        "lastError": None
    }
    
    logger.info(f"Status response: {json.dumps(response, indent=2)}")
    return response

def test_place_order():
    """Test place_order endpoint"""
    logger.info("Testing place_order endpoint...")
    
    bridge = MT5Bridge(offline_mode=True)
    
    # Create mock request
    request = MockRequest(
        symbol="EURUSD",
        orderType="BUY",
        volume=0.1,
        price=None,
        stopLoss=None,
        takeProfit=None
    )
    
    try:
        order_id = bridge.place_order(
            symbol=request.symbol,
            order_type=request.orderType,
            lot=request.volume,
            price=request.price,
            stop_loss=request.stopLoss,
            take_profit=request.takeProfit
        )
        
        response = {"orderId": order_id, "success": True}
        logger.info(f"Order response: {json.dumps(response, indent=2)}")
        return response
    except ValueError as e:
        logger.error(f"Invalid order parameters: {str(e)}")
        return {"error": str(e), "success": False}
    except Exception as e:
        logger.error(f"Error placing order: {str(e)}")
        return {"error": str(e), "success": False}

def test_get_positions():
    """Test get_positions endpoint"""
    logger.info("Testing get_positions endpoint...")
    
    bridge = MT5Bridge(offline_mode=True)
    
    # Place a test order to have a position
    bridge.place_order(
        symbol="EURUSD",
        order_type="BUY",
        lot=0.1
    )
    
    positions = bridge.get_positions()
    
    # Format positions for response
    formatted_positions = []
    for pos in positions:
        formatted_positions.append({
            "id": pos["id"],
            "symbol": pos["symbol"],
            "type": pos["type"],
            "volume": pos["lot"],
            "openPrice": pos["price"],
            "currentPrice": pos.get("current_price", pos["price"]),
            "profit": pos.get("profit", 0.0),
            "openTime": pos.get("time", "").isoformat() if hasattr(pos.get("time", ""), "isoformat") else pos.get("time", "")
        })
    
    logger.info(f"Positions response: {json.dumps(formatted_positions, indent=2)}")
    return formatted_positions

def test_close_position():
    """Test close_position endpoint"""
    logger.info("Testing close_position endpoint...")
    
    bridge = MT5Bridge(offline_mode=True)
    
    # Place a test order to have a position
    order_id = bridge.place_order(
        symbol="EURUSD",
        order_type="BUY",
        lot=0.1
    )
    
    try:
        result = bridge.close_order(order_id)
        response = {"success": result}
        logger.info(f"Close position response: {json.dumps(response, indent=2)}")
        return response
    except Exception as e:
        logger.error(f"Error closing position: {str(e)}")
        return {"error": str(e), "success": False}

def run_tests():
    """Run all tests"""
    logger.info("Starting MT5 Bridge API tests...")
    
    # Test get_status
    test_get_status()
    
    # Test place_order
    test_place_order()
    
    # Test get_positions
    test_get_positions()
    
    # Test close_position
    test_close_position()
    
    logger.info("All tests completed")

if __name__ == "__main__":
    run_tests()