import { Router, Request, Response } from 'express';
import { body, validationResult } from 'express-validator';
import { 
  UserRegistrationSchema, 
  UserLoginSchema 
} from '@ai-trading/shared';
import { AuthService } from './auth.service';
import { AuthenticatedRequest } from '../../shared/types/common.types';
import { authMiddleware } from '../../shared/middleware/auth.middleware';

export class AuthRouter {
  private router: Router;

  constructor(private authService: AuthService) {
    this.router = Router();
    this.setupRoutes();
  }

  private setupRoutes(): void {
    // Registration route
    this.router.post(
      '/register',
      this.validateRegistration(),
      this.handleValidationErrors,
      this.register.bind(this)
    );

    // Login route
    this.router.post(
      '/login',
      this.validateLogin(),
      this.handleValidationErrors,
      this.login.bind(this)
    );

    // Refresh token route
    this.router.post(
      '/refresh',
      this.validateRefreshToken(),
      this.handleValidationErrors,
      this.refreshToken.bind(this)
    );

    // Logout route (protected)
    this.router.post(
      '/logout',
      authMiddleware(this.authService),
      this.logout.bind(this)
    );

    // Get current user (protected)
    this.router.get(
      '/me',
      authMiddleware(this.authService),
      this.getCurrentUser.bind(this)
    );
  }

  private validateRegistration() {
    return [
      body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
      body('username')
        .isLength({ min: 3, max: 50 })
        .matches(/^[a-zA-Z0-9_]+$/)
        .withMessage('Username must be 3-50 characters and contain only letters, numbers, and underscores'),
      body('password')
        .isLength({ min: 8, max: 128 })
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
        .withMessage('Password must be 8-128 characters with at least one uppercase, lowercase, number, and special character'),
      body('firstName')
        .isLength({ min: 1, max: 100 })
        .trim()
        .withMessage('First name is required and must be 1-100 characters'),
      body('lastName')
        .isLength({ min: 1, max: 100 })
        .trim()
        .withMessage('Last name is required and must be 1-100 characters'),
    ];
  }

  private validateLogin() {
    return [
      body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
      body('password')
        .notEmpty()
        .withMessage('Password is required'),
    ];
  }

  private validateRefreshToken() {
    return [
      body('refreshToken')
        .notEmpty()
        .withMessage('Refresh token is required'),
    ];
  }

  private handleValidationErrors(req: Request, res: Response, next: Function): void {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Validation failed',
          details: errors.array(),
        },
      });
      return;
    }
    next();
  }

  private async register(req: Request, res: Response): Promise<void> {
    try {
      // Validate request body against schema
      const validationResult = UserRegistrationSchema.safeParse(req.body);
      if (!validationResult.success) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid registration data',
            details: validationResult.error.errors,
          },
        });
        return;
      }

      const result = await this.authService.register(validationResult.data);

      if (result.success) {
        res.status(201).json({
          success: true,
          message: 'User registered successfully',
          data: result.data,
        });
      } else {
        const statusCode = this.getStatusCodeFromError(result.error!.code);
        res.status(statusCode).json({
          success: false,
          error: result.error,
        });
      }
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'An unexpected error occurred',
        },
      });
    }
  }

  private async login(req: Request, res: Response): Promise<void> {
    try {
      // Validate request body against schema
      const validationResult = UserLoginSchema.safeParse(req.body);
      if (!validationResult.success) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid login data',
            details: validationResult.error.errors,
          },
        });
        return;
      }

      const result = await this.authService.login(validationResult.data);

      if (result.success) {
        res.status(200).json({
          success: true,
          message: 'Login successful',
          data: result.data,
        });
      } else {
        const statusCode = this.getStatusCodeFromError(result.error!.code);
        res.status(statusCode).json({
          success: false,
          error: result.error,
        });
      }
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'An unexpected error occurred',
        },
      });
    }
  }

  private async refreshToken(req: Request, res: Response): Promise<void> {
    try {
      const { refreshToken } = req.body;

      const result = await this.authService.refreshToken(refreshToken);

      if (result.success) {
        res.status(200).json({
          success: true,
          message: 'Tokens refreshed successfully',
          data: result.data,
        });
      } else {
        const statusCode = this.getStatusCodeFromError(result.error!.code);
        res.status(statusCode).json({
          success: false,
          error: result.error,
        });
      }
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'An unexpected error occurred',
        },
      });
    }
  }

  private async logout(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;

      const result = await this.authService.logout(userId);

      if (result.success) {
        res.status(200).json({
          success: true,
          message: 'Logout successful',
        });
      } else {
        res.status(500).json({
          success: false,
          error: result.error,
        });
      }
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'An unexpected error occurred',
        },
      });
    }
  }

  private async getCurrentUser(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      res.status(200).json({
        success: true,
        data: req.user,
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'An unexpected error occurred',
        },
      });
    }
  }

  private getStatusCodeFromError(errorCode: string): number {
    switch (errorCode) {
      case 'USER_ALREADY_EXISTS':
        return 409;
      case 'INVALID_CREDENTIALS':
      case 'INVALID_TOKEN':
      case 'INVALID_REFRESH_TOKEN':
        return 401;
      case 'ACCOUNT_INACTIVE':
        return 403;
      case 'USER_NOT_FOUND':
        return 404;
      case 'VALIDATION_ERROR':
        return 400;
      default:
        return 500;
    }
  }

  public getRouter(): Router {
    return this.router;
  }
}

// Create a default instance for easy import
// Note: In production, you'd want to properly inject dependencies
const mockAuthService = new AuthService({
  userRepository: {} as any,
  tokenService: {} as any,
  passwordService: {} as any
});

const authRouterInstance = new AuthRouter(mockAuthService);
export const authRouter = authRouterInstance.getRouter();