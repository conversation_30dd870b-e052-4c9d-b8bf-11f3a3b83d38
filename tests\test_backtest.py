"""
Comprehensive TDD tests for Enhanced Backtesting Engine
Following enterprise-grade testing patterns with full coverage.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timezone, timedelta
from unittest.mock import patch, MagicMock

# Import the backtesting components
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src', 'trading'))

from backtest import (
    BacktestEngine, RSITradingStrategy, TradingStrategy,
    BacktestConfig, BacktestResult, BacktestMetrics, Trade,
    BacktestStatus, DataIntegrityError, StrategyError, BacktestConfigError
)


class TestBacktestEngine:
    """Comprehensive test suite for BacktestEngine"""
    
    @pytest.fixture
    def sample_data(self):
        """Generate sample market data for testing"""
        dates = pd.date_range(start='2020-01-01', end='2020-12-31', freq='D')
        np.random.seed(42)  # For reproducible tests
        
        # Generate realistic price data with trend and volatility
        returns = np.random.normal(0.001, 0.02, len(dates))  # Daily returns
        prices = [100.0]  # Starting price
        
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        return pd.DataFrame({
            'close': prices,
            'volume': np.random.randint(1000, 10000, len(dates))
        }, index=dates)
    
    @pytest.fixture
    def simple_uptrend_data(self):
        """Simple uptrend data for predictable testing"""
        dates = pd.date_range(start='2020-01-01', periods=50, freq='D')
        prices = [100 + i * 0.5 for i in range(50)]  # Steady uptrend
        
        return pd.DataFrame({
            'close': prices
        }, index=dates)
    
    @pytest.fixture
    def rsi_strategy(self):
        """RSI strategy with default parameters"""
        return RSITradingStrategy(params={'rsi_period': 14, 'overbought': 70, 'oversold': 30})
    
    def test_backtest_returns_comprehensive_metrics(self, sample_data, rsi_strategy):
        """Test that backtest returns all required metrics"""
        engine = BacktestEngine()
        config = BacktestConfig(initial_capital=10000)
        
        result = engine.run(data=sample_data, strategy=rsi_strategy, config=config)
        
        # Verify result structure
        assert isinstance(result, BacktestResult)
        assert result.status == BacktestStatus.COMPLETED
        assert result.metrics is not None
        assert isinstance(result.metrics, BacktestMetrics)
        
        # Verify all required metrics are present
        metrics = result.metrics
        assert hasattr(metrics, 'sharpe_ratio')
        assert hasattr(metrics, 'max_drawdown')
        assert hasattr(metrics, 'total_return')
        assert hasattr(metrics, 'annualized_return')
        assert hasattr(metrics, 'win_rate')
        assert hasattr(metrics, 'profit_factor')
        assert hasattr(metrics, 'volatility')
        assert hasattr(metrics, 'calmar_ratio')
        assert hasattr(metrics, 'sortino_ratio')
        
        # Verify metrics are reasonable
        assert isinstance(metrics.total_return, (int, float))
        assert isinstance(metrics.sharpe_ratio, (int, float))
        assert metrics.max_drawdown <= 0  # Drawdown should be negative or zero
        assert metrics.total_return > -100  # No total loss (as per requirement)
        assert metrics.win_rate >= 0 and metrics.win_rate <= 100
        assert metrics.total_trades >= 0
    
    @pytest.mark.parametrize("invalid_data", [
        None,
        [],
        pd.DataFrame(),
        pd.DataFrame({'wrong_column': [1, 2, 3]}),
        pd.DataFrame({'close': []}),
        pd.DataFrame({'close': [1, 2, np.nan, 4]}),
        pd.DataFrame({'close': [1, 2, -1, 4]}),
        pd.DataFrame({'close': ['a', 'b', 'c']})
    ])
    def test_invalid_data_validation(self, invalid_data, rsi_strategy):
        """Test comprehensive data validation"""
        engine = BacktestEngine()
        
        with pytest.raises(DataIntegrityError):
            engine.run(data=invalid_data, strategy=rsi_strategy, config=10000)
    
    def test_backtest_with_simple_capital_parameter(self, sample_data, rsi_strategy):
        """Test backtest with simple capital parameter (backward compatibility)"""
        engine = BacktestEngine()
        
        result = engine.run(data=sample_data, strategy=rsi_strategy, config=10000)
        
        assert result.status == BacktestStatus.COMPLETED
        assert result.config.initial_capital == 10000
        assert result.metrics is not None
    
    def test_backtest_with_dict_config(self, sample_data, rsi_strategy):
        """Test backtest with dictionary configuration"""
        engine = BacktestEngine()
        config_dict = {
            'initial_capital': 50000,
            'commission': 0.002,
            'slippage': 0.0005
        }
        
        result = engine.run(data=sample_data, strategy=rsi_strategy, config=config_dict)
        
        assert result.status == BacktestStatus.COMPLETED
        assert result.config.initial_capital == 50000
        assert result.config.commission == 0.002
        assert result.config.slippage == 0.0005
    
    def test_backtest_config_validation(self):
        """Test BacktestConfig validation"""
        # Valid configuration
        config = BacktestConfig(initial_capital=10000)
        assert config.initial_capital == 10000
        
        # Invalid configurations
        with pytest.raises(BacktestConfigError):
            BacktestConfig(initial_capital=-1000)  # Negative capital
        
        with pytest.raises(BacktestConfigError):
            BacktestConfig(initial_capital=10000, commission=-0.1)  # Negative commission
        
        with pytest.raises(BacktestConfigError):
            BacktestConfig(initial_capital=10000, commission=1.5)  # Commission > 100%
        
        with pytest.raises(BacktestConfigError):
            BacktestConfig(initial_capital=10000, max_position_size=1.5)  # Position size > 100%
        
        with pytest.raises(BacktestConfigError):
            BacktestConfig(
                initial_capital=10000,
                start_date=datetime(2020, 12, 31),
                end_date=datetime(2020, 1, 1)  # End before start
            )
    
    def test_trade_execution_with_commission_and_slippage(self, simple_uptrend_data):
        """Test that trades include commission and slippage costs"""
        engine = BacktestEngine()
        strategy = RSITradingStrategy(params={'rsi_period': 5, 'oversold': 80})  # Force buy signals
        config = BacktestConfig(initial_capital=10000, commission=0.01, slippage=0.005)
        
        result = engine.run(data=simple_uptrend_data, strategy=strategy, config=config)
        
        assert result.status == BacktestStatus.COMPLETED
        assert len(result.trades) > 0
        
        # Verify trades have commission and slippage
        for trade in result.trades:
            assert trade.commission > 0
            assert trade.slippage >= 0
            assert isinstance(trade.timestamp, datetime)
            assert trade.action in ['BUY', 'SELL']
            assert trade.quantity > 0
            assert trade.price > 0
    
    def test_equity_curve_generation(self, sample_data, rsi_strategy):
        """Test that equity curve is properly generated"""
        engine = BacktestEngine()
        config = BacktestConfig(initial_capital=10000)
        
        result = engine.run(data=sample_data, strategy=rsi_strategy, config=config)
        
        assert result.status == BacktestStatus.COMPLETED
        assert len(result.equity_curve) > 0
        
        # Verify equity curve structure
        for point in result.equity_curve:
            assert 'timestamp' in point
            assert 'equity' in point
            assert 'position' in point
            assert 'cash' in point
            assert isinstance(point['equity'], (int, float))
            assert isinstance(point['position'], (int, float))
            assert isinstance(point['cash'], (int, float))
        
        # First point should be initial capital
        assert result.equity_curve[0]['equity'] == 10000
        assert result.equity_curve[0]['position'] == 0
        assert result.equity_curve[0]['cash'] == 10000
    
    def test_strategy_error_handling(self, sample_data):
        """Test handling of strategy errors"""
        class BrokenStrategy(TradingStrategy):
            def generate_signals(self, data):
                raise StrategyError("Strategy calculation failed")
        
        engine = BacktestEngine()
        broken_strategy = BrokenStrategy()
        
        result = engine.run(data=sample_data, strategy=broken_strategy, config=10000)
        
        assert result.status == BacktestStatus.FAILED
        assert result.error_message is not None
        assert "Strategy calculation failed" in result.error_message
        assert result.metrics is None
    
    def test_insufficient_data_for_strategy(self, rsi_strategy):
        """Test handling when data is insufficient for strategy"""
        # Create data with only 5 rows (RSI needs 14+)
        short_data = pd.DataFrame({
            'close': [100, 101, 102, 103, 104]
        })
        
        engine = BacktestEngine()
        result = engine.run(data=short_data, strategy=rsi_strategy, config=10000)
        
        assert result.status == BacktestStatus.FAILED
        assert result.error_message is not None
        assert "Insufficient data" in result.error_message
    
    def test_performance_metrics_calculation(self, simple_uptrend_data):
        """Test detailed performance metrics calculation"""
        # Create strategy that will generate predictable trades
        class SimpleStrategy(TradingStrategy):
            def generate_signals(self, data):
                signals = pd.Series(0, index=data.index)
                signals.iloc[5] = 1   # Buy signal
                signals.iloc[25] = -1  # Sell signal
                return signals
        
        engine = BacktestEngine()
        strategy = SimpleStrategy()
        config = BacktestConfig(initial_capital=10000, commission=0.001, slippage=0.0001)
        
        result = engine.run(data=simple_uptrend_data, strategy=strategy, config=config)
        
        assert result.status == BacktestStatus.COMPLETED
        metrics = result.metrics
        
        # With uptrend data and buy-then-sell, we should have positive return
        assert metrics.total_return > 0
        assert metrics.total_trades > 0
        assert metrics.winning_trades >= 0
        assert metrics.losing_trades >= 0
        assert metrics.winning_trades + metrics.losing_trades <= metrics.total_trades
        
        # Verify metric ranges
        assert -100 <= metrics.max_drawdown <= 0
        assert 0 <= metrics.win_rate <= 100
        assert metrics.volatility >= 0
    
    def test_audit_trail_generation(self, sample_data, rsi_strategy):
        """Test that audit trail is properly generated"""
        engine = BacktestEngine()
        config = BacktestConfig(initial_capital=10000)
        
        result = engine.run(data=sample_data, strategy=rsi_strategy, config=config)
        
        assert result.status == BacktestStatus.COMPLETED
        assert result.data_hash is not None
        assert result.strategy_hash is not None
        assert result.execution_time is not None
        assert result.timestamp is not None
        
        # Verify hashes are consistent
        result2 = engine.run(data=sample_data, strategy=rsi_strategy, config=config)
        assert result.data_hash == result2.data_hash
        assert result.strategy_hash == result2.strategy_hash
    
    def test_position_sizing_limits(self, simple_uptrend_data):
        """Test that position sizing respects limits"""
        class AggressiveStrategy(TradingStrategy):
            def generate_signals(self, data):
                # Generate buy signal for every period
                return pd.Series(1, index=data.index)
            
            def get_position_size(self, signal, current_price, available_capital):
                # Try to use all available capital
                return available_capital / current_price if current_price > 0 else 0
        
        engine = BacktestEngine()
        strategy = AggressiveStrategy()
        config = BacktestConfig(initial_capital=10000, max_position_size=0.5)  # 50% max
        
        result = engine.run(data=simple_uptrend_data, strategy=strategy, config=config)
        
        assert result.status == BacktestStatus.COMPLETED
        
        # Verify position never exceeds limits
        for point in result.equity_curve:
            if 'price' in point and point['price'] > 0:
                position_value = point['position'] * point['price']
                portfolio_value = point['equity']
                position_ratio = position_value / portfolio_value if portfolio_value > 0 else 0
                assert position_ratio <= 0.51  # Allow small tolerance for rounding
    
    def test_concurrent_backtest_safety(self, sample_data, rsi_strategy):
        """Test that multiple backtests can run concurrently without interference"""
        import threading
        import time
        
        results = []
        errors = []
        
        def run_backtest(engine_id):
            try:
                engine = BacktestEngine()
                config = BacktestConfig(initial_capital=10000 + engine_id * 1000)
                result = engine.run(data=sample_data, strategy=rsi_strategy, config=config)
                results.append((engine_id, result))
            except Exception as e:
                errors.append((engine_id, str(e)))
        
        # Run multiple backtests concurrently
        threads = []
        for i in range(5):
            thread = threading.Thread(target=run_backtest, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Verify all backtests completed successfully
        assert len(errors) == 0, f"Errors occurred: {errors}"
        assert len(results) == 5
        
        # Verify each backtest has correct initial capital
        for engine_id, result in results:
            expected_capital = 10000 + engine_id * 1000
            assert result.config.initial_capital == expected_capital
            assert result.status == BacktestStatus.COMPLETED
    
    def test_result_serialization(self, sample_data, rsi_strategy):
        """Test that backtest results can be serialized to dict"""
        engine = BacktestEngine()
        config = BacktestConfig(initial_capital=10000)
        
        result = engine.run(data=sample_data, strategy=rsi_strategy, config=config)
        
        # Test serialization
        result_dict = result.to_dict()
        
        assert isinstance(result_dict, dict)
        assert 'status' in result_dict
        assert 'config' in result_dict
        assert 'metrics' in result_dict
        assert 'trades' in result_dict
        assert 'equity_curve' in result_dict
        assert 'timestamp' in result_dict
        
        # Verify nested serialization
        assert isinstance(result_dict['trades'], list)
        assert isinstance(result_dict['equity_curve'], list)
        if result_dict['metrics']:
            assert isinstance(result_dict['metrics'], dict)


class TestRSITradingStrategy:
    """Test suite for RSI Trading Strategy"""
    
    @pytest.fixture
    def sample_data(self):
        """Sample data for RSI testing"""
        # Create data that will generate clear RSI signals
        dates = pd.date_range(start='2020-01-01', periods=30, freq='D')
        
        # Create price pattern: decline then rise (should generate oversold then overbought)
        prices = []
        for i in range(30):
            if i < 15:
                prices.append(100 - i * 2)  # Declining prices
            else:
                prices.append(70 + (i - 15) * 3)  # Rising prices
        
        return pd.DataFrame({'close': prices}, index=dates)
    
    def test_rsi_strategy_initialization(self):
        """Test RSI strategy initialization with parameters"""
        # Default parameters
        strategy = RSITradingStrategy()
        assert strategy.params['rsi_period'] == 14
        assert strategy.params['overbought'] == 70
        assert strategy.params['oversold'] == 30
        
        # Custom parameters
        custom_params = {'rsi_period': 21, 'overbought': 80, 'oversold': 20}
        strategy = RSITradingStrategy(params=custom_params)
        assert strategy.params['rsi_period'] == 21
        assert strategy.params['overbought'] == 80
        assert strategy.params['oversold'] == 20
    
    def test_rsi_signal_generation(self, sample_data):
        """Test RSI signal generation"""
        strategy = RSITradingStrategy(params={'rsi_period': 10, 'overbought': 70, 'oversold': 30})
        
        signals = strategy.generate_signals(sample_data)
        
        assert isinstance(signals, pd.Series)
        assert len(signals) == len(sample_data)
        assert all(signal in [-1, 0, 1] for signal in signals)
    
    def test_rsi_strategy_error_handling(self):
        """Test RSI strategy error handling"""
        strategy = RSITradingStrategy()
        
        # Missing close column
        bad_data = pd.DataFrame({'open': [1, 2, 3, 4, 5]})
        with pytest.raises(StrategyError):
            strategy.generate_signals(bad_data)
        
        # Insufficient data
        short_data = pd.DataFrame({'close': [1, 2, 3]})  # Need 15+ for RSI period 14
        with pytest.raises(StrategyError):
            strategy.generate_signals(short_data)
    
    def test_rsi_calculation_accuracy(self):
        """Test RSI calculation accuracy with known values"""
        # Use known price sequence for RSI calculation
        prices = [44, 44.34, 44.09, 44.15, 43.61, 44.33, 44.83, 45.85, 46.08, 45.89,
                 46.03, 46.83, 46.69, 46.45, 46.59, 46.3, 46.28, 46.28, 46.0, 46.03]
        
        data = pd.DataFrame({'close': prices})
        strategy = RSITradingStrategy(params={'rsi_period': 14})
        
        # This should not raise an error and should produce reasonable RSI values
        signals = strategy.generate_signals(data)
        assert len(signals) == len(data)
    
    def test_strategy_hash_consistency(self):
        """Test that strategy hash is consistent for same parameters"""
        strategy1 = RSITradingStrategy(params={'rsi_period': 14, 'overbought': 70})
        strategy2 = RSITradingStrategy(params={'rsi_period': 14, 'overbought': 70})
        strategy3 = RSITradingStrategy(params={'rsi_period': 21, 'overbought': 70})
        
        assert strategy1.get_hash() == strategy2.get_hash()
        assert strategy1.get_hash() != strategy3.get_hash()


class TestBacktestIntegration:
    """Integration tests for complete backtesting workflow"""
    
    def test_end_to_end_backtest_workflow(self):
        """Test complete end-to-end backtesting workflow"""
        # Generate realistic market data
        np.random.seed(42)
        dates = pd.date_range(start='2020-01-01', end='2020-06-30', freq='D')
        returns = np.random.normal(0.0005, 0.015, len(dates))
        prices = [100.0]
        
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        data = pd.DataFrame({'close': prices}, index=dates)
        
        # Create strategy
        strategy = RSITradingStrategy(params={
            'rsi_period': 14,
            'overbought': 75,
            'oversold': 25
        })
        
        # Configure backtest
        config = BacktestConfig(
            initial_capital=100000,
            commission=0.001,
            slippage=0.0005,
            max_position_size=0.8
        )
        
        # Run backtest
        engine = BacktestEngine()
        result = engine.run(data=data, strategy=strategy, config=config)
        
        # Comprehensive validation
        assert result.status == BacktestStatus.COMPLETED
        assert result.metrics is not None
        assert result.execution_time > 0
        assert len(result.equity_curve) > 0
        assert result.data_hash is not None
        assert result.strategy_hash is not None
        
        # Validate metrics are reasonable
        metrics = result.metrics
        assert -50 <= metrics.total_return <= 200  # Reasonable return range
        assert -5 <= metrics.sharpe_ratio <= 5     # Reasonable Sharpe ratio
        assert -50 <= metrics.max_drawdown <= 0    # Reasonable drawdown
        assert 0 <= metrics.win_rate <= 100        # Valid win rate
        assert metrics.total_trades >= 0           # Non-negative trades
        
        # Validate audit trail
        result_dict = result.to_dict()
        assert 'timestamp' in result_dict
        assert 'data_hash' in result_dict
        assert 'strategy_hash' in result_dict
    
    def test_multiple_strategies_comparison(self):
        """Test comparing multiple strategies on same data"""
        # Generate test data
        dates = pd.date_range(start='2020-01-01', periods=100, freq='D')
        np.random.seed(42)
        prices = [100 + np.sin(i/10) * 10 + np.random.normal(0, 1) for i in range(100)]
        data = pd.DataFrame({'close': prices}, index=dates)
        
        # Create different strategies
        strategies = [
            RSITradingStrategy(params={'rsi_period': 14, 'overbought': 70, 'oversold': 30}),
            RSITradingStrategy(params={'rsi_period': 21, 'overbought': 75, 'oversold': 25}),
            RSITradingStrategy(params={'rsi_period': 7, 'overbought': 80, 'oversold': 20})
        ]
        
        results = []
        engine = BacktestEngine()
        config = BacktestConfig(initial_capital=10000)
        
        for strategy in strategies:
            result = engine.run(data=data, strategy=strategy, config=config)
            results.append(result)
        
        # All backtests should complete successfully
        for result in results:
            assert result.status == BacktestStatus.COMPLETED
            assert result.metrics is not None
        
        # Results should have different strategy hashes
        hashes = [result.strategy_hash for result in results]
        assert len(set(hashes)) == len(hashes)  # All unique
        
        # Data hashes should be the same (same data)
        data_hashes = [result.data_hash for result in results]
        assert len(set(data_hashes)) == 1  # All same
    
    def test_backtest_performance_benchmarking(self):
        """Test backtest performance with large dataset"""
        import time
        
        # Generate large dataset
        dates = pd.date_range(start='2015-01-01', end='2020-12-31', freq='D')
        np.random.seed(42)
        returns = np.random.normal(0.0005, 0.02, len(dates))
        prices = [100.0]
        
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        data = pd.DataFrame({'close': prices}, index=dates)
        
        # Run backtest and measure performance
        engine = BacktestEngine()
        strategy = RSITradingStrategy()
        config = BacktestConfig(initial_capital=10000)
        
        start_time = time.time()
        result = engine.run(data=data, strategy=strategy, config=config)
        execution_time = time.time() - start_time
        
        # Verify performance
        assert result.status == BacktestStatus.COMPLETED
        assert execution_time < 10.0  # Should complete within 10 seconds
        assert len(data) > 1000  # Ensure we tested with substantial data
        assert result.execution_time is not None
        assert result.execution_time > 0


# Property-based testing examples
class TestBacktestProperties:
    """Property-based tests for backtest invariants"""
    
    def test_equity_curve_monotonic_properties(self):
        """Test that equity curve maintains certain properties"""
        # Generate various data patterns
        test_patterns = [
            # Uptrend
            [100 + i * 0.1 for i in range(50)],
            # Downtrend  
            [100 - i * 0.1 for i in range(50)],
            # Sideways
            [100 + np.sin(i/5) for i in range(50)],
            # Volatile
            [100 + np.random.normal(0, 5) for i in range(50)]
        ]
        
        for prices in test_patterns:
            data = pd.DataFrame({'close': prices})
            engine = BacktestEngine()
            strategy = RSITradingStrategy(params={'rsi_period': 10})
            
            result = engine.run(data=data, strategy=strategy, capital=10000)
            
            if result.status == BacktestStatus.COMPLETED:
                # Equity curve should have same length as data + 1 (initial point)
                assert len(result.equity_curve) == len(data) + 1
                
                # All equity values should be positive (no negative portfolio value)
                for point in result.equity_curve:
                    assert point['equity'] >= 0
                
                # Cash + position value should equal equity (approximately)
                for point in result.equity_curve:
                    if 'price' in point:
                        calculated_equity = point['cash'] + (point['position'] * point['price'])
                        assert abs(calculated_equity - point['equity']) < 0.01
    
    def test_backtest_invariants_under_various_configs(self):
        """Test that backtest maintains invariants under various configurations"""
        data = pd.DataFrame({'close': [100 + i * 0.1 for i in range(30)]})
        strategy = RSITradingStrategy(params={'rsi_period': 10})
        
        # Test various configurations
        configs = [
            {'initial_capital': 1000, 'commission': 0.0, 'slippage': 0.0},
            {'initial_capital': 10000, 'commission': 0.01, 'slippage': 0.01},
            {'initial_capital': 100000, 'commission': 0.001, 'slippage': 0.0001},
        ]
        
        for config_dict in configs:
            engine = BacktestEngine()
            config = BacktestConfig(**config_dict)
            result = engine.run(data=data, strategy=strategy, config=config)
            
            if result.status == BacktestStatus.COMPLETED:
                # Total return should be reasonable
                assert -100 < result.metrics.total_return < 1000
                
                # Win rate should be valid percentage
                assert 0 <= result.metrics.win_rate <= 100
                
                # Trade count should be non-negative
                assert result.metrics.total_trades >= 0
                
                # Winning + losing trades should not exceed total
                assert (result.metrics.winning_trades + 
                       result.metrics.losing_trades) <= result.metrics.total_trades


if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v", "--tb=short"])