// Template utility functions for optimized loading
export interface TemplateMetadata {
  id: string;
  name: string;
  description: string;
  strategyType: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedLoadTime: number; // in milliseconds
  fileSizeKB: number;
  tags: string[];
  indicators: string[];
  requiredLibraries: string[];
}

export interface QuickTemplate {
  id: string;
  name: string;
  description: string;
  previewCode: string; // Short preview instead of full code
}

// Lightweight template metadata - loads instantly
export const TEMPLATE_METADATA: Record<string, TemplateMetadata> = {
  twin_range_filter: {
    id: 'twin_range_filter',
    name: 'Twin Range Filter Strategy',
    description: 'Dynamic range-based strategy using high/low price channels',
    strategyType: 'Breakout',
    difficulty: 'intermediate',
    estimatedLoadTime: 300,
    fileSizeKB: 3.2,
    tags: ['range_filter', 'breakout', 'forex'],
    indicators: ['high_range', 'low_range', 'middle_line'],
    requiredLibraries: ['pandas', 'numpy']
  },
  mean_reversion_rsi: {
    id: 'mean_reversion_rsi',
    name: 'Mean Reversion RSI Strategy',
    description: 'Classic mean reversion using RSI overbought/oversold levels',
    strategyType: 'Mean Reversion',
    difficulty: 'beginner',
    estimatedLoadTime: 200,
    fileSizeKB: 2.8,
    tags: ['mean_reversion', 'rsi', 'beginner'],
    indicators: ['rsi'],
    requiredLibraries: ['pandas', 'numpy']
  },
  momentum_macd: {
    id: 'momentum_macd',
    name: 'Momentum MACD Strategy',
    description: 'Trend-following strategy using MACD crossovers',
    strategyType: 'Momentum',
    difficulty: 'intermediate',
    estimatedLoadTime: 250,
    fileSizeKB: 3.1,
    tags: ['momentum', 'macd', 'trend_following'],
    indicators: ['macd'],
    requiredLibraries: ['pandas', 'numpy']
  },
  machine_learning: {
    id: 'machine_learning',
    name: 'ML Random Forest Strategy',
    description: 'Advanced ML strategy using Random Forest for signals',
    strategyType: 'Machine Learning',
    difficulty: 'advanced',
    estimatedLoadTime: 600,
    fileSizeKB: 5.7,
    tags: ['machine_learning', 'random_forest', 'ai'],
    indicators: ['rsi', 'macd'],
    requiredLibraries: ['pandas', 'numpy', 'sklearn']
  }
};

// Quick template previews for instant display
export const QUICK_TEMPLATES: Record<string, QuickTemplate> = {
  twin_range_filter: {
    id: 'twin_range_filter',
    name: 'Twin Range Filter',
    description: 'Range-based breakout strategy',
    previewCode: `class TwinRangeFilterStrategy(StrategyBase):
    def __init__(self, symbol="GBPUSD", timeframe="H4"):
        self.period = 20
        # Calculate high/low ranges and middle line
        # Generate signals on price breakouts`
  },
  mean_reversion_rsi: {
    id: 'mean_reversion_rsi',
    name: 'Mean Reversion RSI',
    description: 'RSI overbought/oversold strategy',
    previewCode: `class MeanReversionRSIStrategy(StrategyBase):
    def __init__(self, symbol="EURUSD"):
        self.rsi_period = 14
        self.oversold_level = 30
        self.overbought_level = 70`
  },
  momentum_macd: {
    id: 'momentum_macd',
    name: 'Momentum MACD',
    description: 'MACD crossover momentum strategy',
    previewCode: `class MomentumMACDStrategy(StrategyBase):
    def __init__(self, symbol="EURUSD"):
        self.macd_fast = 12
        self.macd_slow = 26
        self.macd_signal = 9`
  },
  machine_learning: {
    id: 'machine_learning',
    name: 'ML Random Forest',
    description: 'AI-powered trading strategy',
    previewCode: `class MachineLearningStrategy(StrategyBase):
    def __init__(self, symbol="EURUSD"):
        self.model = RandomForestClassifier()
        self.features = ['rsi', 'macd', 'momentum']`
  }
};

// Template loading utility class
export class TemplateLoader {
  private static cache = new Map<string, string>();
  private static loadingPromises = new Map<string, Promise<string>>();

  static async getTemplateCode(templateId: string): Promise<string> {
    // Check cache first
    if (this.cache.has(templateId)) {
      return this.cache.get(templateId)!;
    }

    // Check if already loading
    if (this.loadingPromises.has(templateId)) {
      return this.loadingPromises.get(templateId)!;
    }

    // Create loading promise
    const loadingPromise = this.loadTemplate(templateId);
    this.loadingPromises.set(templateId, loadingPromise);

    try {
      const code = await loadingPromise;
      this.cache.set(templateId, code);
      this.loadingPromises.delete(templateId);
      return code;
    } catch (error) {
      this.loadingPromises.delete(templateId);
      throw error;
    }
  }

  private static async loadTemplate(templateId: string): Promise<string> {
    // In a real implementation, this would fetch from an API
    // For now, simulate loading with appropriate delay based on file size
    const metadata = TEMPLATE_METADATA[templateId];
    if (!metadata) {
      throw new Error(`Template ${templateId} not found`);
    }

    // Simulate loading delay based on file size
    await new Promise(resolve => setTimeout(resolve, metadata.estimatedLoadTime));

    // For demo purposes, return a placeholder
    // In production, this would fetch the actual template code
    return `// ${metadata.name} - Full implementation would be loaded here
${QUICK_TEMPLATES[templateId]?.previewCode || '// Template code here'}
// ... rest of implementation`;
  }

  static getQuickPreview(templateId: string): QuickTemplate | null {
    return QUICK_TEMPLATES[templateId] || null;
  }

  static getMetadata(templateId: string): TemplateMetadata | null {
    return TEMPLATE_METADATA[templateId] || null;
  }

  static getTemplatesByDifficulty(difficulty: 'beginner' | 'intermediate' | 'advanced'): TemplateMetadata[] {
    return Object.values(TEMPLATE_METADATA).filter(template => template.difficulty === difficulty);
  }

  static getQuickLoadingTemplates(): TemplateMetadata[] {
    return Object.values(TEMPLATE_METADATA)
      .filter(template => template.estimatedLoadTime < 400)
      .sort((a, b) => a.estimatedLoadTime - b.estimatedLoadTime);
  }

  static clearCache(): void {
    this.cache.clear();
    this.loadingPromises.clear();
  }

  static getCacheInfo(): { cachedCount: number; totalTemplates: number } {
    return {
      cachedCount: this.cache.size,
      totalTemplates: Object.keys(TEMPLATE_METADATA).length
    };
  }
}
