# Development and Testing Dependencies
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-xdist>=3.3.1
pytest-mock>=3.11.1
pytest-asyncio>=0.21.1
pytest-benchmark>=4.0.0
hypothesis>=6.82.0
faker>=19.3.0

# Code Quality
flake8>=6.0.0
black>=23.7.0
isort>=5.12.0
mypy>=1.5.0
bandit>=1.7.5
safety>=2.3.5

# Type Stubs
types-requests>=2.31.0
types-setuptools>=68.0.0

# Memory Profiling
memory-profiler>=0.61.0
psutil>=5.9.5

# Documentation
sphinx>=7.1.0
sphinx-rtd-theme>=1.3.0

# Jupyter for analysis
jupyter>=1.0.0
notebook>=7.0.0
ipykernel>=6.25.0

# Additional testing utilities
coverage>=7.2.0
tox>=4.6.0
pre-commit>=3.3.0