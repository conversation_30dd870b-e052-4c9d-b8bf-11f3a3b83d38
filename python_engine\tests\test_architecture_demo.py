"""
Architecture Demo Test

Run the dependency injection demo as a test to showcase the improvements.
"""

import pytest
import asyncio
from datetime import datetime

from core.dependency_injection import DependencyContainer, inject
from core.service_configuration import ServiceConfigurator, ServiceMode
from core.trading_engine import TradingEngine
from core.interfaces import TradingSignal
from services.mock_services import MockServiceCollection

class TestArchitectureDemo:
    """Demonstrate architecture improvements through tests"""
    
    def test_before_vs_after_comparison(self):
        """Compare before and after dependency injection"""
        print("\n🎭 DEPENDENCY INJECTION COMPARISON")
        print("=" * 60)
        
        print("🔴 BEFORE: Hard-coded Dependencies")
        print("-" * 40)
        print("❌ Problems with old approach:")
        print("   - Hard to unit test (always hits real services)")
        print("   - Hard to mock for testing")
        print("   - Tight coupling between components")
        print("   - Difficult to swap implementations")
        print("   - No flexibility for different environments")
        
        print("\n✅ AFTER: Dependency Injection")
        print("-" * 40)
        
        # Configure services for testing
        configurator = ServiceConfigurator()
        container = configurator.configure_for_testing()
        
        # Create trading engine with injected dependencies
        engine = container.resolve(TradingEngine)
        
        print("✅ Injected dependencies:")
        print(f"   - Config: {engine.config.__class__.__name__}")
        print(f"   - Logger: {engine.logger.__class__.__name__}")
        print(f"   - Market Data: {engine.market_data.__class__.__name__}")
        print(f"   - Strategy: {engine.strategy.__class__.__name__}")
        print(f"   - Trading: {engine.trading.__class__.__name__}")
        print(f"   - Risk Management: {engine.risk_management.__class__.__name__}")
        print(f"   - Portfolio: {engine.portfolio.__class__.__name__}")
        print(f"   - Notifications: {engine.notifications.__class__.__name__}")
        print(f"   - Data Storage: {engine.data_storage.__class__.__name__}")
        
        print("✅ Benefits achieved:")
        print("   - Easy to unit test (can inject mocks)")
        print("   - Loose coupling between components")
        print("   - Easy to swap implementations")
        print("   - Flexible for different environments")
        print("   - Single Responsibility Principle")
        print("   - Open/Closed Principle")
        
        assert engine is not None
        assert all([
            engine.config, engine.logger, engine.market_data,
            engine.strategy, engine.trading, engine.risk_management,
            engine.portfolio, engine.notifications, engine.data_storage
        ])
    
    def test_environment_flexibility(self):
        """Test different environment configurations"""
        print("\n🌍 ENVIRONMENT FLEXIBILITY")
        print("=" * 50)
        
        environments = {
            "Testing": ServiceMode.TESTING,
            "Development": ServiceMode.DEVELOPMENT,
        }
        
        for env_name, mode in environments.items():
            print(f"\n📋 {env_name} Environment:")
            
            configurator = ServiceConfigurator()
            if mode == ServiceMode.TESTING:
                container = configurator.configure_for_testing()
            elif mode == ServiceMode.DEVELOPMENT:
                container = configurator.configure_for_development()
            
            engine = container.resolve(TradingEngine)
            
            print(f"   ✅ Market Data: {engine.market_data.__class__.__name__}")
            print(f"   ✅ Trading: {engine.trading.__class__.__name__}")
            print(f"   ✅ Strategy: {engine.strategy.__class__.__name__}")
            
            # Verify configuration is valid
            validation = configurator.validate_configuration()
            assert validation['is_valid']
            print(f"   ✅ Configuration valid: {validation['is_valid']}")
    
    @pytest.mark.asyncio
    async def test_easy_mocking_for_testing(self):
        """Demonstrate how easy it is to mock services for testing"""
        print("\n🧪 EASY MOCKING FOR TESTING")
        print("=" * 50)
        
        # Create test configuration
        configurator = ServiceConfigurator()
        container = configurator.configure_for_testing()
        
        # Get mock services for manipulation
        mocks = MockServiceCollection()
        
        # Configure mock behavior
        mocks.market_data.set_mock_price("AAPL", 150.0)
        mocks.config.set_config('engine.auto_trading_enabled', True)
        
        # Override container services
        container.register_instance(type(mocks.market_data), mocks.market_data)
        container.register_instance(type(mocks.config), mocks.config)
        
        # Create engine with mocked services
        engine = container.resolve(TradingEngine)
        engine._engine_config = engine._load_engine_config()
        
        # Test signal processing
        signal = TradingSignal(
            symbol="AAPL",
            signal="buy",
            confidence=0.8,
            timestamp=datetime.now(),
            strategy_name="test_strategy",
            metadata={}
        )
        
        await engine._process_signal("test_strategy", signal)
        
        # Verify mock behavior
        stored_signals = engine.data_storage.get_stored_signals()
        orders = engine.trading.get_orders()
        
        print(f"   ✅ Signals stored: {len(stored_signals)}")
        print(f"   ✅ Orders placed: {len(orders)}")
        print("   ✅ All behavior controlled through mocks!")
        print("   ✅ No external dependencies hit!")
        print("   ✅ Fast, reliable, isolated test!")
        
        assert len(stored_signals) > 0
        assert len(orders) > 0
    
    @pytest.mark.asyncio
    async def test_runtime_configuration_changes(self):
        """Test changing behavior at runtime through DI"""
        print("\n🔄 RUNTIME CONFIGURATION CHANGES")
        print("=" * 50)
        
        # Create base configuration
        configurator = ServiceConfigurator()
        container = configurator.configure_for_testing()
        
        # Create mock service collection
        mocks = MockServiceCollection()
        container.register_instance(type(mocks.risk_management), mocks.risk_management)
        
        engine = container.resolve(TradingEngine)
        
        signal = TradingSignal(
            symbol="AAPL",
            signal="buy",
            confidence=0.8,
            timestamp=datetime.now(),
            strategy_name="test",
            metadata={}
        )
        
        # Scenario 1: Conservative trading
        print("\n📊 Scenario 1: Conservative Trading")
        mocks.risk_management.set_max_position_size(50)
        mocks.risk_management.set_position_size_multiplier(0.5)
        
        conservative_size = await engine.risk_management.calculate_position_size("AAPL", signal, 10000)
        print(f"   Conservative position size: {conservative_size}")
        
        # Scenario 2: Aggressive trading
        print("\n📊 Scenario 2: Aggressive Trading")
        mocks.risk_management.set_max_position_size(200)
        mocks.risk_management.set_position_size_multiplier(1.5)
        
        aggressive_size = await engine.risk_management.calculate_position_size("AAPL", signal, 10000)
        print(f"   Aggressive position size: {aggressive_size}")
        
        print("   ✅ Same interface, different behavior!")
        print("   ✅ Easy A/B testing of strategies!")
        print("   ✅ Runtime configuration changes!")
        
        assert conservative_size < aggressive_size
    
    def test_inject_decorator_functionality(self):
        """Test the @inject decorator"""
        print("\n🎯 @INJECT DECORATOR FUNCTIONALITY")
        print("=" * 50)
        
        # Configure services
        configurator = ServiceConfigurator()
        container = configurator.configure_for_testing()
        
        # Function with automatic dependency injection
        @inject
        def analyze_market(market_data_service, config_service, logging_service):
            """Function with automatically injected dependencies"""
            logging_service.log_info("Analyzing market with injected dependencies")
            
            # Get configuration
            risk_tolerance = config_service.get_config('trading.risk_tolerance', 0.02)
            
            # Mock analysis
            analysis = {
                'market_data_service': market_data_service.__class__.__name__,
                'risk_tolerance': risk_tolerance,
                'timestamp': datetime.now().isoformat()
            }
            
            logging_service.log_info(f"Market analysis complete")
            return analysis
        
        # Call function - dependencies are automatically injected!
        result = analyze_market()
        
        print("✅ Function called without parameters!")
        print(f"   Market Data Service: {result['market_data_service']}")
        print(f"   Risk Tolerance: {result['risk_tolerance']}")
        print("✅ Dependencies were automatically injected!")
        
        assert result['market_data_service'] == 'MockMarketDataService'
        assert result['risk_tolerance'] == 0.01  # From mock config
    
    @pytest.mark.asyncio
    async def test_complete_workflow_integration(self):
        """Test complete workflow with dependency injection"""
        print("\n🔄 COMPLETE WORKFLOW INTEGRATION")
        print("=" * 50)
        
        # Configure for testing
        configurator = ServiceConfigurator()
        container = configurator.configure_for_testing()
        
        # Create engine
        engine = container.resolve(TradingEngine)
        
        # Configure for auto-trading
        engine.config.set_config('engine.auto_trading_enabled', True)
        engine._engine_config = engine._load_engine_config()
        
        # Add a strategy
        await engine.add_strategy(
            "demo_strategy",
            "def trading_strategy(data, params): return {'signal': 'buy', 'confidence': 0.7}",
            ["DEMO"],
            {"test_param": "value"}
        )
        
        # Process a signal
        signal = TradingSignal(
            symbol="DEMO",
            signal="buy",
            confidence=0.7,
            timestamp=datetime.now(),
            strategy_name="demo_strategy",
            metadata={}
        )
        
        await engine._process_signal("demo_strategy", signal)
        
        # Get performance metrics
        metrics = await engine.get_performance_metrics()
        
        print(f"   ✅ Active strategies: {metrics['active_strategies']}")
        print(f"   ✅ Total signals: {metrics['total_signals_generated']}")
        print(f"   ✅ Total trades: {metrics['total_trades_executed']}")
        print(f"   ✅ Portfolio value: ${metrics['portfolio_value']:.2f}")
        
        # Verify workflow completed
        status = engine.get_engine_status()
        assert "demo_strategy" in status['strategies']
        assert metrics['active_strategies'] == 1
        
        print("✅ Complete workflow executed successfully!")
        print("✅ All components integrated through DI!")

if __name__ == "__main__":
    # Run the demo
    demo = TestArchitectureDemo()
    
    print("🚀 RUNNING ARCHITECTURE DEMONSTRATION")
    print("=" * 70)
    
    demo.test_before_vs_after_comparison()
    demo.test_environment_flexibility()
    asyncio.run(demo.test_easy_mocking_for_testing())
    asyncio.run(demo.test_runtime_configuration_changes())
    demo.test_inject_decorator_functionality()
    asyncio.run(demo.test_complete_workflow_integration())
    
    print("\n🎉 ARCHITECTURE DEMO COMPLETE!")
    print("=" * 70)
    print("✅ Dependency Injection successfully implemented!")
    print("✅ All benefits demonstrated!")
    print("✅ Ready for production use!")