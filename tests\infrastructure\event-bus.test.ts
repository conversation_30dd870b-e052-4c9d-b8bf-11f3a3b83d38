// tests/infrastructure/event-bus.test.ts
import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { z } from 'zod';

// Mock implementations
export const EventSchema = z.object({
  type: z.string(),
  payload: z.any(),
  timestamp: z.date(),
  correlationId: z.string(),
  causationId: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

export type Event = z.infer<typeof EventSchema>;
export type EventHandler = (event: Event) => Promise<void> | void;

export interface IEventStore {
  save(event: Event): Promise<void>;
  getEvents(type: string, from: Date, to: Date): Promise<Event[]>;
}

class Logger {
  constructor(private name: string) {}
  
  info(message: string, ...args: any[]) {
    console.log(`[${this.name}] INFO:`, message, ...args);
  }
  
  error(message: string, ...args: any[]) {
    console.error(`[${this.name}] ERROR:`, message, ...args);
  }
  
  warn(message: string, ...args: any[]) {
    console.warn(`[${this.name}] WARN:`, message, ...args);
  }
}

export class EventBus {
  private eventStore?: IEventStore;
  private logger: Logger;
  private handlers: Map<string, Set<EventHandler>>;

  constructor(eventStore?: IEventStore) {
    this.eventStore = eventStore;
    this.logger = new Logger('EventBus');
    this.handlers = new Map();
  }

  async publish(event: Event): Promise<void> {
    // Validate event schema
    const validatedEvent = EventSchema.parse(event);

    // Persist event if store is configured
    if (this.eventStore) {
      await this.eventStore.save(validatedEvent);
    }

    // Emit event to all handlers
    const handlers = this.handlers.get(event.type) || new Set();
    
    await Promise.allSettled(
      Array.from(handlers).map(handler => 
        this.executeHandler(handler, validatedEvent)
      )
    );
  }

  subscribe(eventType: string, handler: EventHandler): void {
    if (!this.handlers.has(eventType)) {
      this.handlers.set(eventType, new Set());
    }
    this.handlers.get(eventType)!.add(handler);
  }

  unsubscribe(eventType: string, handler: EventHandler): void {
    this.handlers.get(eventType)?.delete(handler);
  }

  async replay(eventType: string, events: Event[]): Promise<void> {
    const handlers = this.handlers.get(eventType) || new Set();
    
    for (const event of events) {
      await Promise.allSettled(
        Array.from(handlers).map(handler => 
          this.executeHandler(handler, event)
        )
      );
    }
  }

  private async executeHandler(handler: EventHandler, event: Event): Promise<void> {
    try {
      await handler(event);
    } catch (error) {
      this.logger.error(`Handler error for event ${event.type}:`, error);
      // Could publish error event here for monitoring
    }
  }
}

describe('EventBus', () => {
  let eventBus: EventBus;

  beforeEach(() => {
    eventBus = new EventBus();
  });

  describe('publish/subscribe pattern', () => {
    it('should deliver events to subscribed handlers', async () => {
      // Arrange
      const handler = jest.fn() as jest.MockedFunction<EventHandler>;
      const event: Event = {
        type: 'trade.executed',
        payload: { tradeId: '123', amount: 1000 },
        timestamp: new Date(),
        correlationId: 'corr-123',
      };

      eventBus.subscribe('trade.executed', handler);

      // Act
      await eventBus.publish(event);

      // Assert
      expect(handler).toHaveBeenCalledWith(event);
      expect(handler).toHaveBeenCalledTimes(1);
    });

    it('should support multiple handlers for same event', async () => {
      // Arrange
      const handler1 = jest.fn() as jest.MockedFunction<EventHandler>;
      const handler2 = jest.fn() as jest.MockedFunction<EventHandler>;
      const event: Event = {
        type: 'market.update',
        payload: { symbol: 'AAPL', price: 150 },
        timestamp: new Date(),
        correlationId: 'corr-456',
      };

      eventBus.subscribe('market.update', handler1);
      eventBus.subscribe('market.update', handler2);

      // Act
      await eventBus.publish(event);

      // Assert
      expect(handler1).toHaveBeenCalledWith(event);
      expect(handler2).toHaveBeenCalledWith(event);
    });

    it('should handle handler errors gracefully', async () => {
      // Arrange
      const errorHandler = jest.fn().mockImplementation(() => Promise.reject(new Error('Handler error'))) as jest.MockedFunction<EventHandler>;
      const successHandler = jest.fn() as jest.MockedFunction<EventHandler>;
      const event: Event = {
        type: 'trade.failed',
        payload: { reason: 'Insufficient funds' },
        timestamp: new Date(),
        correlationId: 'corr-789',
      };

      eventBus.subscribe('trade.failed', errorHandler);
      eventBus.subscribe('trade.failed', successHandler);

      // Act & Assert - should not throw
      await expect(eventBus.publish(event)).resolves.not.toThrow();
      expect(successHandler).toHaveBeenCalled();
    });
  });

  describe('event replay', () => {
    it('should replay events from event store', async () => {
      // Arrange
      const handler = jest.fn() as jest.MockedFunction<EventHandler>;
      const events = [
        { type: 'trade.executed', payload: { id: 1 }, timestamp: new Date(), correlationId: 'c1' },
        { type: 'trade.executed', payload: { id: 2 }, timestamp: new Date(), correlationId: 'c2' },
        { type: 'trade.executed', payload: { id: 3 }, timestamp: new Date(), correlationId: 'c3' },
      ];

      eventBus.subscribe('trade.executed', handler);

      // Act
      await eventBus.replay('trade.executed', events);

      // Assert
      expect(handler).toHaveBeenCalledTimes(3);
    });
  });
});