# AI Enhanced Trading Platform Backend

This is the backend API for the AI Enhanced Trading Platform. It provides endpoints for MT5 Bridge integration and backtesting.

## Setup

1. Install dependencies:

```bash
pip install -r requirements.txt
```

2. Run the server:

```bash
python run.py
```

The server will start on http://localhost:8000.

## API Documentation

Once the server is running, you can access the API documentation at:

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## Endpoints

### MT5 Bridge

- `GET /api/v1/mt5/status` - Get MT5 Bridge status
- `POST /api/v1/mt5/connect` - Connect to MT5
- `POST /api/v1/mt5/disconnect` - Disconnect from MT5
- `POST /api/v1/mt5/order` - Place an order
- `GET /api/v1/mt5/positions` - Get open positions
- `POST /api/v1/mt5/positions/{order_id}/close` - Close a position

### Backtesting

- `POST /api/v1/backtest` - Create a new backtest
- `GET /api/v1/backtest` - Get all backtests
- `GET /api/v1/backtest/{backtest_id}` - Get a specific backtest
- `GET /api/v1/backtest/{backtest_id}/results` - Get backtest results
- `DELETE /api/v1/backtest/{backtest_id}` - Cancel a backtest