"""
Test runner for historical data upload functionality tests.
This script runs the TDD-compliant tests for the historical data endpoints.
"""

import os
import sys
import pytest

if __name__ == "__main__":
    # Add the current directory to the path
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    # Run the tests
    print("Running historical data upload functionality tests...")
    result = pytest.main(["-xvs", "tests/test_historical_data.py"])
    
    # Exit with the test result code
    sys.exit(result)