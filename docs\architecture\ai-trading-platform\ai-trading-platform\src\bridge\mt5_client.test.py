
import pytest
from bridge.mt5_client import MT5<PERSON>lient
from bridge.schemas import OrderRequest, AccountInfo

@pytest.fixture
def mt5_client():
    return MT5Client(dummy_mode=True)

def test_account_info_returns_expected_fields(mt5_client):
    acc = mt5_client.get_account_info()
    assert isinstance(acc, AccountInfo)
    assert acc.balance > 0
    assert acc.currency == "USD"

def test_submit_order_returns_success(mt5_client):
    req = OrderRequest(symbol="EURUSD", volume=0.01, order_type="buy", price=1.1000)
    result = mt5_client.submit_order(req)
    assert result.success is True
    assert result.order_id is not None

def test_submit_order_handles_invalid_symbol(mt5_client):
    req = OrderRequest(symbol="FAKE", volume=0.01, order_type="buy", price=1.0000)
    result = mt5_client.submit_order(req)
    assert result.success is False
    assert "Invalid symbol" in result.error
