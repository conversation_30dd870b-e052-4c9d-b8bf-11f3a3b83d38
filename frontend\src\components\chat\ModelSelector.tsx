import React from 'react';
import { Ch<PERSON>ronDown, Zap, Brain, Sparkles } from 'lucide-react';

interface ModelSelectorProps {
  selectedModel: string;
  onSelect: (model: string) => void;
  disabled?: boolean;
}

interface ModelOption {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  tier: 'free' | 'pro' | 'premium';
}

const models: ModelOption[] = [
  {
    id: 'xllm-mini',
    name: 'XLLM Mini',
    description: 'Fast and efficient for basic trading questions',
    icon: <Zap className="w-4 h-4 text-green-500" />,
    tier: 'free',
  },
  {
    id: 'xllm-pro',
    name: 'XLLM Pro',
    description: 'Advanced analysis and strategy development',
    icon: <Brain className="w-4 h-4 text-blue-500" />,
    tier: 'pro',
  },
  {
    id: 'xllm-max',
    name: 'XLLM Max',
    description: 'Most powerful model for complex financial analysis',
    icon: <Sparkles className="w-4 h-4 text-purple-500" />,
    tier: 'premium',
  },
];

export function ModelSelector({ selectedModel, onSelect, disabled = false }: ModelSelectorProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const selectedModelData = models.find(m => m.id === selectedModel) || models[0];

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={disabled}
        className={`
          w-full flex items-center justify-between px-3 py-2 bg-white border border-gray-300 
          rounded-lg text-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
      >
        <div className="flex items-center space-x-2">
          {selectedModelData.icon}
          <span className="font-medium">{selectedModelData.name}</span>
        </div>
        <ChevronDown className={`w-4 h-4 text-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-20 max-h-64 overflow-y-auto">
            {models.map((model) => (
              <button
                key={model.id}
                onClick={() => {
                  onSelect(model.id);
                  setIsOpen(false);
                }}
                className={`
                  w-full px-3 py-3 text-left hover:bg-gray-50 flex items-start space-x-3
                  ${selectedModel === model.id ? 'bg-blue-50 border-l-2 border-blue-500' : ''}
                  ${model.tier !== 'free' ? 'opacity-75' : ''}
                `}
              >
                <div className="flex-shrink-0 mt-0.5">
                  {model.icon}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-sm text-gray-900">{model.name}</span>
                    {model.tier !== 'free' && (
                      <span className={`
                        px-1.5 py-0.5 text-xs font-medium rounded
                        ${model.tier === 'pro' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'}
                      `}>
                        {model.tier.toUpperCase()}
                      </span>
                    )}
                  </div>
                  <p className="text-xs text-gray-600 mt-0.5">{model.description}</p>
                </div>

                {selectedModel === model.id && (
                  <div className="flex-shrink-0">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  </div>
                )}
              </button>
            ))}
            
            {/* Upgrade notice */}
            <div className="border-t border-gray-100 p-3 bg-gray-50">
              <p className="text-xs text-gray-600">
                Upgrade your plan to access more powerful models
              </p>
            </div>
          </div>
        </>
      )}
    </div>
  );
}