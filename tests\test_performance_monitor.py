# tests/test_performance_monitor.py
import pytest
import time
import tempfile
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from monitoring.performance_monitor import (
    PerformanceMonitor, PerformanceMetric, TradingPerformanceMetric,
    AlertThreshold, PerformanceAlert
)

class TestPerformanceMonitor:
    def setup_method(self):
        """Setup test environment"""
        self.monitor = PerformanceMonitor(monitoring_interval=0.1, history_size=100)
    
    def teardown_method(self):
        """Cleanup test environment"""
        if self.monitor.is_monitoring:
            self.monitor.stop_monitoring()
    
    def test_monitor_initialization(self):
        """Test performance monitor initialization"""
        assert self.monitor.monitoring_interval == 0.1
        assert self.monitor.history_size == 100
        assert not self.monitor.is_monitoring
        assert len(self.monitor.alert_thresholds) > 0  # Default thresholds
        assert len(self.monitor.system_metrics) == 0
        assert len(self.monitor.trading_metrics) == 0
    
    def test_start_stop_monitoring(self):
        """Test starting and stopping monitoring"""
        # Start monitoring
        self.monitor.start_monitoring()
        assert self.monitor.is_monitoring == True
        assert self.monitor.monitor_thread is not None
        
        # Wait a bit for some metrics to be collected
        time.sleep(0.3)
        
        # Stop monitoring
        self.monitor.stop_monitoring()
        assert self.monitor.is_monitoring == False
        
        # Check that metrics were collected
        assert len(self.monitor.system_metrics) > 0
    
    def test_system_metrics_collection(self):
        """Test system metrics collection"""
        # Start monitoring briefly
        self.monitor.start_monitoring()
        time.sleep(0.3)
        self.monitor.stop_monitoring()
        
        # Check that metrics were collected
        assert len(self.monitor.system_metrics) > 0
        
        # Check metric structure
        metric = self.monitor.system_metrics[0]
        assert isinstance(metric, PerformanceMetric)
        assert isinstance(metric.cpu_percent, float)
        assert isinstance(metric.memory_percent, float)
        assert isinstance(metric.memory_used_mb, float)
        assert isinstance(metric.active_threads, int)
        assert isinstance(metric.timestamp, datetime)
    
    def test_trading_metrics_recording(self):
        """Test trading metrics recording"""
        trading_metric = TradingPerformanceMetric(
            timestamp=datetime.now(),
            orders_per_second=5.0,
            avg_order_latency_ms=50.0,
            backtest_execution_time_ms=1000.0,
            data_processing_time_ms=200.0,
            ml_inference_time_ms=100.0,
            memory_usage_mb=512.0,
            active_positions=3,
            pending_orders=2
        )
        
        self.monitor.record_trading_metric(trading_metric)
        
        assert len(self.monitor.trading_metrics) == 1
        assert self.monitor.trading_metrics[0] == trading_metric
    
    def test_alert_threshold_management(self):
        """Test alert threshold management"""
        initial_count = len(self.monitor.alert_thresholds)
        
        # Add custom threshold
        custom_threshold = AlertThreshold(
            metric_name="custom_metric",
            threshold_value=100.0,
            comparison="greater_than",
            alert_message="Custom alert triggered",
            severity="medium"
        )
        
        self.monitor.add_alert_threshold(custom_threshold)
        
        assert len(self.monitor.alert_thresholds) == initial_count + 1
        assert custom_threshold in self.monitor.alert_thresholds
    
    def test_alert_triggering(self):
        """Test alert triggering mechanism"""
        # Add a threshold that will definitely trigger
        low_threshold = AlertThreshold(
            metric_name="cpu_percent",
            threshold_value=0.1,  # Very low threshold
            comparison="greater_than",
            alert_message="Test alert",
            severity="low"
        )
        
        self.monitor.add_alert_threshold(low_threshold)
        
        # Start monitoring to trigger alerts
        self.monitor.start_monitoring()
        time.sleep(0.3)
        self.monitor.stop_monitoring()
        
        # Check if alert was triggered
        active_alerts = self.monitor.get_active_alerts()
        assert len(active_alerts) > 0
        
        # Check alert structure
        alert = active_alerts[0]
        assert "alert_id" in alert
        assert "metric_name" in alert
        assert "current_value" in alert
        assert "severity" in alert
        assert "message" in alert
    
    def test_alert_acknowledgment(self):
        """Test alert acknowledgment"""
        # Trigger an alert first
        low_threshold = AlertThreshold(
            metric_name="cpu_percent",
            threshold_value=0.1,
            comparison="greater_than",
            alert_message="Test alert",
            severity="low"
        )
        
        self.monitor.add_alert_threshold(low_threshold)
        self.monitor.start_monitoring()
        time.sleep(0.3)
        self.monitor.stop_monitoring()
        
        # Get active alerts
        active_alerts = self.monitor.get_active_alerts()
        assert len(active_alerts) > 0
        
        # Acknowledge first alert
        alert_id = active_alerts[0]["alert_id"]
        success = self.monitor.acknowledge_alert(alert_id)
        assert success == True
        
        # Check that alert is no longer active
        new_active_alerts = self.monitor.get_active_alerts()
        assert len(new_active_alerts) < len(active_alerts)
    
    def test_performance_summary(self):
        """Test performance summary generation"""
        # Start monitoring to collect some data
        self.monitor.start_monitoring()
        time.sleep(0.3)
        self.monitor.stop_monitoring()
        
        # Get performance summary
        summary = self.monitor.get_performance_summary(minutes=1)
        
        assert "time_period_minutes" in summary
        assert "data_points" in summary
        assert "system_performance" in summary
        assert "active_alerts" in summary
        assert "optimization_suggestions" in summary
        assert "timestamp" in summary
        
        # Check system performance structure
        sys_perf = summary["system_performance"]
        assert "cpu_percent" in sys_perf
        assert "memory_percent" in sys_perf
        assert "active_threads" in sys_perf
        
        # Check that each metric has avg, max, min
        for metric_data in sys_perf.values():
            assert "avg" in metric_data
            assert "max" in metric_data
            assert "min" in metric_data
    
    def test_optimization_suggestions(self):
        """Test optimization suggestions generation"""
        # Manually trigger performance analysis with high values
        # Create fake high-usage metrics
        for i in range(15):  # Need enough history
            fake_metric = PerformanceMetric(
                timestamp=datetime.now(),
                cpu_percent=85.0,  # High CPU
                memory_percent=80.0,  # High memory
                memory_used_mb=8000.0,
                disk_io_read_mb=0.0,
                disk_io_write_mb=0.0,
                network_sent_mb=0.0,
                network_recv_mb=0.0,
                active_threads=60,  # High thread count
                open_files=10
            )
            self.monitor.system_metrics.append(fake_metric)
        
        # Trigger analysis
        self.monitor._analyze_performance()
        
        # Check suggestions were generated
        suggestions = self.monitor.get_optimization_suggestions()
        assert len(suggestions) > 0
        
        # Check suggestion structure
        suggestion = suggestions[0]
        assert "type" in suggestion
        assert "severity" in suggestion
        assert "message" in suggestion
        assert "timestamp" in suggestion
    
    def test_baseline_comparison(self):
        """Test baseline setting and comparison"""
        # Start monitoring to collect baseline data
        self.monitor.start_monitoring()
        time.sleep(0.3)
        
        # Set baseline
        success = self.monitor.set_baseline()
        assert success == True
        assert self.monitor.baseline_metrics is not None
        
        # Continue monitoring for comparison data
        time.sleep(0.3)
        self.monitor.stop_monitoring()
        
        # Compare to baseline
        comparison = self.monitor.compare_to_baseline()
        assert comparison is not None
        
        # Check comparison structure
        assert "cpu_percent" in comparison
        assert "memory_percent" in comparison
        assert "active_threads" in comparison
        
        for metric_comparison in comparison.values():
            assert "baseline" in metric_comparison
            assert "current" in metric_comparison
            assert "change_percent" in metric_comparison
            assert "status" in metric_comparison
    
    def test_alert_callbacks(self):
        """Test alert callback functionality"""
        callback_triggered = []
        
        def test_callback(alert: PerformanceAlert):
            callback_triggered.append(alert)
        
        # Add callback
        self.monitor.add_alert_callback(test_callback)
        
        # Add threshold that will trigger
        low_threshold = AlertThreshold(
            metric_name="cpu_percent",
            threshold_value=0.1,
            comparison="greater_than",
            alert_message="Test callback alert",
            severity="low"
        )
        
        self.monitor.add_alert_threshold(low_threshold)
        
        # Start monitoring to trigger alert
        self.monitor.start_monitoring()
        time.sleep(0.3)
        self.monitor.stop_monitoring()
        
        # Check callback was triggered
        assert len(callback_triggered) > 0
        assert isinstance(callback_triggered[0], PerformanceAlert)
    
    def test_metrics_export(self):
        """Test metrics export functionality"""
        # Add some test data
        test_metric = PerformanceMetric(
            timestamp=datetime.now(),
            cpu_percent=50.0,
            memory_percent=60.0,
            memory_used_mb=4000.0,
            disk_io_read_mb=10.0,
            disk_io_write_mb=5.0,
            network_sent_mb=2.0,
            network_recv_mb=3.0,
            active_threads=20,
            open_files=50
        )
        
        self.monitor.system_metrics.append(test_metric)
        
        # Export to temporary file
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json') as f:
            temp_filename = f.name
        
        try:
            success = self.monitor.export_metrics(temp_filename, format="json")
            assert success == True
            
            # Check file was created and has content
            assert os.path.exists(temp_filename)
            assert os.path.getsize(temp_filename) > 0
            
            # Check file content structure
            import json
            with open(temp_filename, 'r') as f:
                data = json.load(f)
            
            assert "export_timestamp" in data
            assert "system_metrics" in data
            assert "trading_metrics" in data
            assert "alert_thresholds" in data
            assert "optimization_suggestions" in data
            
        finally:
            # Cleanup
            if os.path.exists(temp_filename):
                os.unlink(temp_filename)
    
    def test_trading_alert_triggering(self):
        """Test trading-specific alert triggering"""
        # Add trading-specific threshold
        latency_threshold = AlertThreshold(
            metric_name="avg_order_latency_ms",
            threshold_value=100.0,
            comparison="greater_than",
            alert_message="High order latency detected",
            severity="medium"
        )
        
        self.monitor.add_alert_threshold(latency_threshold)
        
        # Record trading metric that should trigger alert
        high_latency_metric = TradingPerformanceMetric(
            timestamp=datetime.now(),
            orders_per_second=2.0,
            avg_order_latency_ms=150.0,  # Above threshold
            backtest_execution_time_ms=1000.0,
            data_processing_time_ms=200.0,
            ml_inference_time_ms=100.0,
            memory_usage_mb=512.0,
            active_positions=3,
            pending_orders=2
        )
        
        self.monitor.record_trading_metric(high_latency_metric)
        
        # Check if alert was triggered
        active_alerts = self.monitor.get_active_alerts()
        latency_alerts = [a for a in active_alerts if a["metric_name"] == "avg_order_latency_ms"]
        assert len(latency_alerts) > 0
    
    def test_alert_comparison_logic(self):
        """Test different alert comparison logic"""
        # Test greater_than
        gt_threshold = AlertThreshold("test_metric", 50.0, "greater_than", "GT test", "low")
        assert self.monitor._should_alert(60.0, gt_threshold) == True
        assert self.monitor._should_alert(40.0, gt_threshold) == False
        
        # Test less_than
        lt_threshold = AlertThreshold("test_metric", 50.0, "less_than", "LT test", "low")
        assert self.monitor._should_alert(40.0, lt_threshold) == True
        assert self.monitor._should_alert(60.0, lt_threshold) == False
        
        # Test equals
        eq_threshold = AlertThreshold("test_metric", 50.0, "equals", "EQ test", "low")
        assert self.monitor._should_alert(50.0, eq_threshold) == True
        assert self.monitor._should_alert(50.0001, eq_threshold) == True  # Within tolerance
        assert self.monitor._should_alert(51.0, eq_threshold) == False
    
    def test_performance_summary_with_no_data(self):
        """Test performance summary when no data is available"""
        # Get summary without any monitoring data
        summary = self.monitor.get_performance_summary(minutes=1)
        
        assert "error" in summary
        assert "No recent performance data available" in summary["error"]
    
    def test_baseline_without_data(self):
        """Test baseline setting without sufficient data"""
        # Try to set baseline without any metrics
        success = self.monitor.set_baseline()
        assert success == False
        assert self.monitor.baseline_metrics is None