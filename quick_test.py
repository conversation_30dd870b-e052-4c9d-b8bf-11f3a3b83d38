#!/usr/bin/env python3
"""
Quick test of individual platform components
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_data_validator():
    """Test data validation component"""
    print("Testing Data Validator...")
    
    from src.validation.data_validator import DataValidator, OHLCData
    from decimal import Decimal
    from datetime import datetime
    
    validator = DataValidator()
    
    # Create valid OHLC data
    ohlc_data = OHLCData(
        timestamp=datetime.now(),
        open=Decimal('1.1000'),
        high=Decimal('1.1050'),
        low=Decimal('1.0950'),
        close=Decimal('1.1025'),
        volume=1000,
        source="test_source",
        hash=""
    )
    
    # Generate hash
    hash_value = validator.generate_data_hash(ohlc_data)
    ohlc_data.hash = hash_value
    
    # Validate
    is_valid = validator.validate_ohlc(ohlc_data)
    print(f"  - OHLC validation: {'PASS' if is_valid else 'FAIL'}")
    print(f"  - Data hash: {hash_value[:16]}...")
    
    return is_valid

def test_darwin_machine():
    """Test AI evolution component"""
    print("Testing Darwin-Godel Machine...")
    
    from src.evolution.darwin_godel_machine import DarwinGodelMachine
    
    machine = DarwinGodelMachine(seed=42)
    
    # Initialize population
    population = machine.initialize_population(size=5)
    print(f"  - Population size: {len(population)}")
    
    # Test evolution
    evolved_population = machine.evolve_generation(population)
    print(f"  - Evolution: {'PASS' if len(evolved_population) == 5 else 'FAIL'}")
    
    return len(evolved_population) == 5

def test_mt5_integration():
    """Test trading integration component"""
    print("Testing MT5 Integration...")
    
    from src.trading.mt5_integration import MT5Integration, SafetyLimits, TradingMode, OrderType
    
    # Create safety limits
    safety_limits = SafetyLimits(
        max_daily_loss=100.0,
        max_position_size=0.01,
        max_open_positions=2,
        max_risk_per_trade=50.0,
        allowed_symbols=["EURUSD"],
        trading_hours_start="09:00",
        trading_hours_end="17:00",
        emergency_stop_loss=200.0
    )
    
    # Create MT5 integration in dummy mode
    mt5 = MT5Integration(safety_limits, TradingMode.DUMMY)
    
    # Test order creation
    order = mt5.create_order(
        symbol="EURUSD",
        order_type=OrderType.BUY,
        volume=0.01,
        price=1.1000,
        stop_loss=1.0950,
        take_profit=1.1050,
        comment="Test order"
    )
    
    print(f"  - Order creation: {'PASS' if order else 'FAIL'}")
    
    # Test order submission
    success, message, position_id = mt5.submit_order(order)
    print(f"  - Order submission: {'PASS' if success else 'FAIL'}")
    print(f"  - Message: {message}")
    
    return success

def test_performance_monitor():
    """Test performance monitoring component"""
    print("Testing Performance Monitor...")
    
    from src.monitoring.performance_monitor import PerformanceMonitor
    import time
    
    monitor = PerformanceMonitor(monitoring_interval=0.1)
    
    # Start monitoring
    monitor.start_monitoring()
    print(f"  - Start monitoring: {'PASS' if monitor.is_monitoring else 'FAIL'}")
    
    # Wait a bit
    time.sleep(0.2)
    
    # Stop monitoring
    monitor.stop_monitoring()
    print(f"  - Stop monitoring: {'PASS' if not monitor.is_monitoring else 'FAIL'}")
    
    return True

def test_chatbot():
    """Test chatbot component"""
    print("Testing Chatbot Knowledge System...")
    
    from src.chatbot.knowledge_base import KnowledgeBase, TradingChatbot
    
    # Create knowledge base and chatbot
    kb = KnowledgeBase()
    chatbot = TradingChatbot(kb)
    
    # Add some knowledge
    chatbot.add_trading_knowledge()
    print("  - Knowledge base: PASS")
    
    # Test query
    response = chatbot.get_response("What is trading?")
    print(f"  - Query response: {'PASS' if response else 'FAIL'}")
    
    return True

def main():
    """Run all component tests"""
    print("AI Enhanced Trading Platform - Component Tests")
    print("=" * 50)
    
    tests = [
        ("Data Validator", test_data_validator),
        ("Darwin-Godel Machine", test_darwin_machine),
        ("MT5 Integration", test_mt5_integration),
        ("Performance Monitor", test_performance_monitor),
        ("Chatbot System", test_chatbot)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            result = test_func()
            results.append((test_name, "PASS" if result else "FAIL"))
        except Exception as e:
            print(f"  - ERROR: {e}")
            results.append((test_name, "ERROR"))
    
    print("\n" + "=" * 50)
    print("TEST RESULTS SUMMARY")
    print("=" * 50)
    
    for test_name, result in results:
        status_symbol = "✓" if result == "PASS" else "✗"
        print(f"{status_symbol} {test_name}: {result}")
    
    passed = sum(1 for _, result in results if result == "PASS")
    total = len(results)
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\nAll components working correctly!")
        print("The platform is ready for use.")
    else:
        print("\nSome components need attention.")

if __name__ == "__main__":
    main()