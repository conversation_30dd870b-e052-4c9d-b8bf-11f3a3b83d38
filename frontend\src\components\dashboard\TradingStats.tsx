/**
 * TradingStats Component
 * Displays key trading statistics and metrics on the dashboard
 */

import { useQuery } from '@tanstack/react-query';
import { ArrowUpIcon, ArrowDownIcon } from '@heroicons/react/24/solid';
import { Card } from '@/components/ui/card';
import { Spinner } from '@/components/ui/spinner';
import { api } from '@/services/api';

interface StatCardProps {
  title: string;
  value: string | number;
  change?: number;
  changeText?: string;
  isLoading?: boolean;
  icon?: React.ReactNode;
}

function StatCard({ title, value, change, changeText, isLoading, icon }: StatCardProps) {
  const isPositive = change && change > 0;
  const isNegative = change && change < 0;

  return (
    <Card className="p-6">
      <div className="flex justify-between items-start">
        <div>
          <p className="text-sm font-medium text-gray-500">{title}</p>
          {isLoading ? (
            <div className="h-8 flex items-center">
              <Spinner size="sm" />
            </div>
          ) : (
            <h3 className="text-2xl font-bold mt-1">{value}</h3>
          )}
          
          {(isPositive || isNegative) && (
            <div className="flex items-center mt-1">
              {isPositive ? (
                <ArrowUpIcon className="h-3 w-3 text-green-500 mr-1" />
              ) : (
                <ArrowDownIcon className="h-3 w-3 text-red-500 mr-1" />
              )}
              <span 
                className={`text-xs font-medium ${
                  isPositive ? 'text-green-600' : 'text-red-600'
                }`}
              >
                {Math.abs(change).toFixed(1)}% {changeText || ''}
              </span>
            </div>
          )}
        </div>
        {icon && (
          <div className="p-2 bg-primary-50 rounded-lg">
            {icon}
          </div>
        )}
      </div>
    </Card>
  );
}

export function TradingStats() {
  // Fetch trading statistics
  const { data: stats, isLoading } = useQuery({
    queryKey: ['trading-stats'],
    queryFn: async () => {
      try {
        // This would be replaced with a real API call
        // For now, we'll return mock data
        // const stats = await api.getTradingStats();
        
        // Mock data
        return {
          accountBalance: 10250.75,
          accountBalanceChange: 2.5,
          openPositions: 3,
          openPositionsChange: -1,
          dailyPnL: 125.50,
          dailyPnLChange: 3.2,
          weeklyPnL: 450.25,
          weeklyPnLChange: 5.7,
          winRate: 68,
          winRateChange: 2.1,
          drawdown: 3.2,
          drawdownChange: -0.5,
        };
      } catch (error) {
        console.error('Error fetching trading stats:', error);
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      <StatCard
        title="Account Balance"
        value={stats ? `$${stats.accountBalance.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}` : '$0.00'}
        change={stats?.accountBalanceChange}
        isLoading={isLoading}
      />
      
      <StatCard
        title="Open Positions"
        value={stats?.openPositions || 0}
        change={stats?.openPositionsChange}
        isLoading={isLoading}
      />
      
      <StatCard
        title="Daily P&L"
        value={stats ? `$${stats.dailyPnL.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}` : '$0.00'}
        change={stats?.dailyPnLChange}
        isLoading={isLoading}
      />
      
      <StatCard
        title="Weekly P&L"
        value={stats ? `$${stats.weeklyPnL.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}` : '$0.00'}
        change={stats?.weeklyPnLChange}
        isLoading={isLoading}
      />
      
      <StatCard
        title="Win Rate"
        value={stats ? `${stats.winRate}%` : '0%'}
        change={stats?.winRateChange}
        isLoading={isLoading}
      />
      
      <StatCard
        title="Max Drawdown"
        value={stats ? `${stats.drawdown}%` : '0%'}
        change={stats?.drawdownChange}
        changeText="lower is better"
        isLoading={isLoading}
      />
    </div>
  );
}