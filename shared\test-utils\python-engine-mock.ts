/**
 * Mock implementation of the Python Engine for testing
 * Mirrors the behavior of the actual Python AI Trading Engine
 */

import { EventEmitter } from 'events';
import {
  TradingEngineRequest,
  TradingEngineResponse,
  PythonBacktestRequest,
  PythonBacktestResponse,
  PythonChatRequest,
  PythonChatResponse,
  PythonDataProcessingRequest,
  PythonDataProcessingResponse,
  OrderRequest,
  OrderResult,
  AccountInfo,
  BacktestResults,
  ChatResponse,
} from '@/shared/schemas';
import { TEST_FIXTURES } from './fixtures';

export interface PythonEngineMockConfig {
  simulateNetworkDelay?: boolean;
  networkDelayMs?: number;
  simulateErrors?: boolean;
  errorRate?: number;
  healthyStatus?: boolean;
}

/**
 * Mock Python Engine that simulates the behavior of the actual Python AI Trading Engine
 * Used for testing bridge services without requiring the actual Python service
 */
export class PythonEngineMock extends EventEmitter {
  private config: Required<PythonEngineMockConfig>;
  private isHealthy: boolean;
  private activeBacktests: Map<string, { status: string; progress: number }> = new Map();
  private accountBalance: number = 10000;
  private nextOrderId: number = 1000;

  constructor(config: PythonEngineMockConfig = {}) {
    super();
    this.config = {
      simulateNetworkDelay: config.simulateNetworkDelay ?? true,
      networkDelayMs: config.networkDelayMs ?? 100,
      simulateErrors: config.simulateErrors ?? false,
      errorRate: config.errorRate ?? 0.1,
      healthyStatus: config.healthyStatus ?? true,
    };
    this.isHealthy = this.config.healthyStatus;
  }

  /**
   * Simulate network delay
   */
  private async simulateDelay(): Promise<void> {
    if (this.config.simulateNetworkDelay) {
      const delay = this.config.networkDelayMs + Math.random() * 50; // Add some jitter
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  /**
   * Simulate random errors
   */
  private shouldSimulateError(): boolean {
    return this.config.simulateErrors && Math.random() < this.config.errorRate;
  }

  /**
   * Mock trading command processing
   */
  async processTradingCommand(request: TradingEngineRequest): Promise<TradingEngineResponse> {
    await this.simulateDelay();

    if (this.shouldSimulateError()) {
      return {
        success: false,
        error: 'Simulated trading engine error',
        timestamp: new Date(),
        request_id: request.request_id,
      };
    }

    switch (request.action) {
      case 'get_account':
        return this.mockGetAccount(request);
      
      case 'submit_order':
        return this.mockSubmitOrder(request);
      
      case 'close_order':
        return this.mockCloseOrder(request);
      
      case 'get_positions':
        return this.mockGetPositions(request);
      
      default:
        return {
          success: false,
          error: `Unknown action: ${request.action}`,
          timestamp: new Date(),
          request_id: request.request_id,
        };
    }
  }

  /**
   * Mock get account info
   */
  private mockGetAccount(request: TradingEngineRequest): TradingEngineResponse {
    const accountInfo: AccountInfo = {
      balance: this.accountBalance,
      equity: this.accountBalance + Math.random() * 100 - 50, // Some floating P&L
      margin: Math.random() * 500,
      currency: 'USD',
    };

    return {
      success: true,
      data: accountInfo,
      timestamp: new Date(),
      request_id: request.request_id,
    };
  }

  /**
   * Mock submit order
   */
  private mockSubmitOrder(request: TradingEngineRequest): TradingEngineResponse {
    const orderRequest = request.payload as OrderRequest;
    
    // Simulate order validation
    if (orderRequest.volume <= 0) {
      return {
        success: false,
        error: 'Invalid volume',
        timestamp: new Date(),
        request_id: request.request_id,
      };
    }

    if (orderRequest.volume * orderRequest.price > this.accountBalance) {
      return {
        success: false,
        error: 'Insufficient balance',
        timestamp: new Date(),
        request_id: request.request_id,
      };
    }

    const orderResult: OrderResult = {
      success: true,
      order_id: this.nextOrderId++,
    };

    // Update mock account balance
    this.accountBalance -= orderRequest.volume * orderRequest.price * 0.1; // Simulate margin usage

    this.emit('order_submitted', { orderRequest, orderResult });

    return {
      success: true,
      data: orderResult,
      timestamp: new Date(),
      request_id: request.request_id,
    };
  }

  /**
   * Mock close order
   */
  private mockCloseOrder(request: TradingEngineRequest): TradingEngineResponse {
    const { order_id } = request.payload as { order_id: number };

    // Simulate order close with some P&L
    const pnl = (Math.random() - 0.5) * 100; // Random P&L between -50 and +50
    this.accountBalance += pnl;

    const orderResult: OrderResult = {
      success: true,
      order_id,
      pnl,
    };

    this.emit('order_closed', { orderId: order_id, pnl });

    return {
      success: true,
      data: orderResult,
      timestamp: new Date(),
      request_id: request.request_id,
    };
  }

  /**
   * Mock get positions
   */
  private mockGetPositions(request: TradingEngineRequest): TradingEngineResponse {
    // Return empty positions for simplicity
    return {
      success: true,
      data: [],
      timestamp: new Date(),
      request_id: request.request_id,
    };
  }

  /**
   * Mock backtest processing
   */
  async processBacktest(request: PythonBacktestRequest): Promise<PythonBacktestResponse> {
    await this.simulateDelay();

    if (this.shouldSimulateError()) {
      return {
        request_id: request.request_id,
        success: false,
        error: 'Simulated backtest error',
        execution_time_seconds: 0.1,
      };
    }

    const backtestId = `bt_${Date.now()}`;
    
    // Simulate backtest execution
    this.activeBacktests.set(backtestId, { status: 'running', progress: 0 });
    
    // Simulate progress updates
    this.simulateBacktestProgress(backtestId);

    // For fast backtests, return results immediately
    if (request.config.symbols.length === 1) {
      const results: BacktestResults = {
        ...TEST_FIXTURES.BACKTEST.SUCCESSFUL_BACKTEST_RESULTS,
        backtest_id: backtestId,
        config: request.config,
      };

      this.activeBacktests.set(backtestId, { status: 'completed', progress: 100 });

      return {
        request_id: request.request_id,
        success: true,
        results,
        execution_time_seconds: 0.5,
      };
    }

    // For complex backtests, return without results (will be retrieved later)
    return {
      request_id: request.request_id,
      success: true,
      execution_time_seconds: 0.1,
    };
  }

  /**
   * Mock backtest progress simulation
   */
  private simulateBacktestProgress(backtestId: string): void {
    const progressInterval = setInterval(() => {
      const currentStatus = this.activeBacktests.get(backtestId);
      if (!currentStatus || currentStatus.status === 'completed') {
        clearInterval(progressInterval);
        return;
      }

      const newProgress = Math.min(currentStatus.progress + Math.random() * 20, 100);
      const newStatus = newProgress >= 100 ? 'completed' : 'running';

      this.activeBacktests.set(backtestId, {
        status: newStatus,
        progress: newProgress,
      });

      this.emit('backtest_progress', { backtestId, progress: newProgress, status: newStatus });

      if (newStatus === 'completed') {
        clearInterval(progressInterval);
      }
    }, 500);
  }

  /**
   * Mock get backtest status
   */
  async getBacktestStatus(backtestId: string): Promise<{ status: string; progress: number }> {
    await this.simulateDelay();

    const backtestStatus = this.activeBacktests.get(backtestId);
    if (!backtestStatus) {
      throw new Error('Backtest not found');
    }

    return backtestStatus;
  }

  /**
   * Mock chat processing
   */
  async processChat(request: PythonChatRequest): Promise<PythonChatResponse> {
    await this.simulateDelay();

    if (this.shouldSimulateError()) {
      return {
        request_id: request.request_id,
        success: false,
        error: 'Simulated chat processing error',
        processing_time_ms: 100,
      };
    }

    // Generate mock response based on query
    const response = this.generateMockChatResponse(request.query);

    this.emit('chat_processed', { request, response });

    return {
      request_id: request.request_id,
      success: true,
      response,
      processing_time_ms: 800 + Math.random() * 1000, // 0.8-1.8 seconds
    };
  }

  /**
   * Generate mock chat response based on query
   */
  private generateMockChatResponse(query: string): ChatResponse {
    const lowerQuery = query.toLowerCase();
    
    if (lowerQuery.includes('trend') || lowerQuery.includes('analysis')) {
      return {
        message: 'Based on current market analysis, the trend shows bullish momentum with key support levels holding strong.',
        type: 'analysis',
        confidence: 0.85,
        timestamp: new Date(),
        sources: [
          { type: 'technical_analysis', content: 'Moving averages indicate upward trend' },
          { type: 'market_data', content: 'Volume confirms the trend direction' },
        ],
      };
    }

    if (lowerQuery.includes('buy') || lowerQuery.includes('sell') || lowerQuery.includes('trade')) {
      return {
        message: 'I recommend waiting for a better entry point. Consider risk management and position sizing before entering any trades.',
        type: 'recommendation',
        confidence: 0.72,
        timestamp: new Date(),
        sources: [
          { type: 'risk_management', content: 'Current volatility suggests cautious approach' },
        ],
      };
    }

    if (lowerQuery.includes('performance') || lowerQuery.includes('results')) {
      return {
        message: 'Your recent trading performance shows a win rate of 68% with an average risk-reward ratio of 1:1.5. Consider focusing on trade quality over quantity.',
        type: 'performance_analysis',
        confidence: 0.90,
        timestamp: new Date(),
        sources: [
          { type: 'trade_history', content: 'Analysis of last 30 trades' },
          { type: 'performance_metrics', content: 'Risk-adjusted returns calculation' },
        ],
      };
    }

    // Default response
    return {
      message: 'I understand your question about trading. Could you provide more specific details about what you\'d like to know?',
      type: 'clarification',
      confidence: 0.60,
      timestamp: new Date(),
      sources: [],
    };
  }

  /**
   * Mock data processing
   */
  async processData(request: PythonDataProcessingRequest): Promise<PythonDataProcessingResponse> {
    await this.simulateDelay();

    if (this.shouldSimulateError()) {
      return {
        request_id: request.request_id,
        success: false,
        error: 'Simulated data processing error',
        processing_stats: {
          rows_processed: 0,
          rows_valid: 0,
          rows_invalid: 0,
          processing_time_ms: 100,
        },
      };
    }

    // Simulate data processing
    const totalRows = 1000 + Math.floor(Math.random() * 5000);
    const validRows = Math.floor(totalRows * (0.85 + Math.random() * 0.14)); // 85-99% valid
    const invalidRows = totalRows - validRows;

    return {
      request_id: request.request_id,
      success: true,
      data: {
        symbol: request.symbol,
        timeframe: request.timeframe,
        data: TEST_FIXTURES.TRADING.MARKET_DATA,
        metadata: {
          total_records: totalRows,
          date_range: {
            start: new Date('2024-01-01'),
            end: new Date('2024-12-31'),
          },
          completeness: validRows / totalRows,
        },
      },
      processing_stats: {
        rows_processed: totalRows,
        rows_valid: validRows,
        rows_invalid: invalidRows,
        processing_time_ms: 2000 + Math.random() * 3000,
      },
    };
  }

  /**
   * Mock health check
   */
  async checkHealth(): Promise<{ status: string; version: string; uptime: number }> {
    await this.simulateDelay();

    if (!this.isHealthy) {
      throw new Error('Service unavailable');
    }

    return {
      status: 'healthy',
      version: '1.0.0-mock',
      uptime: Math.floor(Math.random() * 86400), // Random uptime in seconds
    };
  }

  /**
   * Set health status for testing
   */
  setHealthStatus(healthy: boolean): void {
    this.isHealthy = healthy;
    this.emit('health_changed', { healthy });
  }

  /**
   * Get current account balance (for testing)
   */
  getAccountBalance(): number {
    return this.accountBalance;
  }

  /**
   * Set account balance (for testing)
   */
  setAccountBalance(balance: number): void {
    this.accountBalance = balance;
  }

  /**
   * Reset mock state
   */
  reset(): void {
    this.activeBacktests.clear();
    this.accountBalance = 10000;
    this.nextOrderId = 1000;
    this.isHealthy = true;
    this.removeAllListeners();
  }

  /**
   * Get active backtests (for testing)
   */
  getActiveBacktests(): Array<{ id: string; status: string; progress: number }> {
    return Array.from(this.activeBacktests.entries()).map(([id, data]) => ({
      id,
      ...data,
    }));
  }
}