// src/services/trade-service.ts

export interface TradeRequest {
  symbol: string;
  quantity: number;
  side: 'buy' | 'sell';
  type?: 'market' | 'limit';
  price?: number;
}

export interface Trade {
  id: string;
  symbol: string;
  quantity: number;
  side: 'buy' | 'sell';
  type: 'market' | 'limit';
  price?: number;
  status: 'pending' | 'filled' | 'cancelled' | 'rejected';
  timestamp: Date;
  executedPrice?: number;
  executedQuantity?: number;
}

export interface MarketDataService {
  isMarketOpen(symbol: string): Promise<boolean>;
  getCurrentPrice(symbol: string): Promise<number>;
}

export interface TradeRepository {
  save(trade: Trade): Promise<Trade>;
  findById(id: string): Promise<Trade | null>;
  findBySymbol(symbol: string): Promise<Trade[]>;
}

/**
 * Service for handling trade execution
 */
export class TradeService {
  constructor(
    private marketDataService: MarketDataService,
    private tradeRepository: TradeRepository
  ) {}

  /**
   * Execute a trade
   */
  async executeTrade(request: TradeRequest): Promise<Trade> {
    // Validate request
    this.validateTradeRequest(request);

    // Check if market is open
    const isMarketOpen = await this.marketDataService.isMarketOpen(request.symbol);
    if (!isMarketOpen) {
      throw new Error(`Market is closed for ${request.symbol}`);
    }

    // Create trade
    const trade: Trade = {
      id: this.generateTradeId(),
      symbol: request.symbol,
      quantity: request.quantity,
      side: request.side,
      type: request.type || 'market',
      price: request.price,
      status: 'pending',
      timestamp: new Date()
    };

    // Execute trade logic
    if (trade.type === 'market') {
      const currentPrice = await this.marketDataService.getCurrentPrice(request.symbol);
      trade.executedPrice = currentPrice;
      trade.executedQuantity = trade.quantity;
      trade.status = 'filled';
    } else {
      // For limit orders, just set as pending
      trade.status = 'pending';
    }

    // Save trade
    const savedTrade = await this.tradeRepository.save(trade);

    return savedTrade;
  }

  /**
   * Get trade by ID
   */
  async getTrade(id: string): Promise<Trade | null> {
    return this.tradeRepository.findById(id);
  }

  /**
   * Get trades by symbol
   */
  async getTradesBySymbol(symbol: string): Promise<Trade[]> {
    return this.tradeRepository.findBySymbol(symbol);
  }

  private validateTradeRequest(request: TradeRequest): void {
    if (!request.symbol) {
      throw new Error('Symbol is required');
    }

    if (!request.quantity || request.quantity <= 0) {
      throw new Error('Quantity must be positive');
    }

    if (!['buy', 'sell'].includes(request.side)) {
      throw new Error('Side must be buy or sell');
    }

    if (request.type === 'limit' && (!request.price || request.price <= 0)) {
      throw new Error('Price is required for limit orders');
    }
  }

  private generateTradeId(): string {
    return `trade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * Mock implementations for testing
 */
export class MockMarketDataService implements MarketDataService {
  private marketOpen = true;
  private prices: Map<string, number> = new Map([
    ['AAPL', 150.00],
    ['GOOGL', 2500.00],
    ['MSFT', 300.00]
  ]);

  async isMarketOpen(symbol: string): Promise<boolean> {
    return this.marketOpen;
  }

  async getCurrentPrice(symbol: string): Promise<number> {
    const price = this.prices.get(symbol);
    if (!price) {
      throw new Error(`Price not available for ${symbol}`);
    }
    return price;
  }

  setMarketOpen(open: boolean): void {
    this.marketOpen = open;
  }

  setPrice(symbol: string, price: number): void {
    this.prices.set(symbol, price);
  }
}

export class MockTradeRepository implements TradeRepository {
  private trades: Map<string, Trade> = new Map();

  async save(trade: Trade): Promise<Trade> {
    this.trades.set(trade.id, { ...trade });
    return { ...trade };
  }

  async findById(id: string): Promise<Trade | null> {
    const trade = this.trades.get(id);
    return trade ? { ...trade } : null;
  }

  async findBySymbol(symbol: string): Promise<Trade[]> {
    return Array.from(this.trades.values())
      .filter(trade => trade.symbol === symbol)
      .map(trade => ({ ...trade }));
  }

  clear(): void {
    this.trades.clear();
  }
}