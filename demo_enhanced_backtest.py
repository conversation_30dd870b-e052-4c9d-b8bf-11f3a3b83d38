#!/usr/bin/env python3
"""
Enhanced Backtesting Engine Demo
Demonstrates comprehensive backtesting capabilities with TDD focus.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src', 'trading'))

from backtest import (
    BacktestEngine, RSITradingStrategy, TradingStrategy,
    BacktestConfig, BacktestStatus
)


def generate_sample_data(days=365, start_price=100.0, volatility=0.02, trend=0.0005):
    """Generate realistic sample market data"""
    dates = pd.date_range(start='2020-01-01', periods=days, freq='D')
    
    # Generate price series with trend and volatility
    np.random.seed(42)  # For reproducible results
    returns = np.random.normal(trend, volatility, days)
    
    prices = [start_price]
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    # Add some volume data
    volumes = np.random.randint(10000, 100000, days)
    
    return pd.DataFrame({
        'close': prices,
        'volume': volumes
    }, index=dates)


class MACDStrategy(TradingStrategy):
    """MACD-based trading strategy for demonstration"""
    
    def __init__(self, params=None):
        default_params = {
            'fast_period': 12,
            'slow_period': 26,
            'signal_period': 9
        }
        if params:
            default_params.update(params)
        super().__init__(default_params)
    
    def generate_signals(self, data):
        """Generate MACD-based trading signals"""
        if 'close' not in data.columns:
            raise ValueError("Data must contain 'close' column")
        
        if len(data) < self.params['slow_period'] + self.params['signal_period']:
            raise ValueError(f"Insufficient data for MACD calculation")
        
        # Calculate MACD
        close = data['close']
        ema_fast = close.ewm(span=self.params['fast_period']).mean()
        ema_slow = close.ewm(span=self.params['slow_period']).mean()
        macd_line = ema_fast - ema_slow
        signal_line = macd_line.ewm(span=self.params['signal_period']).mean()
        
        # Generate signals
        signals = pd.Series(0, index=data.index)
        signals[macd_line > signal_line] = 1   # Buy when MACD above signal
        signals[macd_line < signal_line] = -1  # Sell when MACD below signal
        
        return signals


def demo_basic_backtest():
    """Demonstrate basic backtesting functionality"""
    print("🚀 Enhanced Backtesting Engine Demo")
    print("=" * 50)
    
    # Generate sample data
    print("\n📊 Generating sample market data...")
    data = generate_sample_data(days=252, volatility=0.015, trend=0.0008)
    print(f"Generated {len(data)} days of market data")
    print(f"Price range: ${data['close'].min():.2f} - ${data['close'].max():.2f}")
    
    # Create RSI strategy
    print("\n🎯 Creating RSI trading strategy...")
    rsi_strategy = RSITradingStrategy(params={
        'rsi_period': 14,
        'overbought': 70,
        'oversold': 30
    })
    print(f"Strategy: {rsi_strategy.name}")
    print(f"Parameters: {rsi_strategy.params}")
    
    # Configure backtest
    print("\n⚙️ Configuring backtest...")
    config = BacktestConfig(
        initial_capital=100000,
        commission=0.001,      # 0.1% commission
        slippage=0.0005,       # 0.05% slippage
        max_position_size=0.8  # 80% max position
    )
    print(f"Initial capital: ${config.initial_capital:,.2f}")
    print(f"Commission: {config.commission:.3%}")
    print(f"Slippage: {config.slippage:.3%}")
    
    # Run backtest
    print("\n🔄 Running backtest...")
    engine = BacktestEngine()
    result = engine.run(data=data, strategy=rsi_strategy, config=config)
    
    # Display results
    print(f"\n📈 Backtest Results:")
    print(f"Status: {result.status.value}")
    print(f"Execution time: {result.execution_time:.3f} seconds")
    
    if result.status == BacktestStatus.COMPLETED:
        metrics = result.metrics
        print(f"\n💰 Performance Metrics:")
        print(f"Total Return: {metrics.total_return:.2f}%")
        print(f"Annualized Return: {metrics.annualized_return:.2f}%")
        print(f"Sharpe Ratio: {metrics.sharpe_ratio:.3f}")
        print(f"Max Drawdown: {metrics.max_drawdown:.2f}%")
        print(f"Volatility: {metrics.volatility:.2f}%")
        print(f"Calmar Ratio: {metrics.calmar_ratio:.3f}")
        print(f"Sortino Ratio: {metrics.sortino_ratio:.3f}")
        
        print(f"\n📊 Trading Statistics:")
        print(f"Total Trades: {metrics.total_trades}")
        print(f"Winning Trades: {metrics.winning_trades}")
        print(f"Losing Trades: {metrics.losing_trades}")
        print(f"Win Rate: {metrics.win_rate:.1f}%")
        print(f"Profit Factor: {metrics.profit_factor:.2f}")
        print(f"Average Win: ${metrics.avg_win:.2f}")
        print(f"Average Loss: ${metrics.avg_loss:.2f}")
        print(f"Largest Win: ${metrics.largest_win:.2f}")
        print(f"Largest Loss: ${metrics.largest_loss:.2f}")
        
        print(f"\n🔍 Audit Trail:")
        print(f"Data Hash: {result.data_hash}")
        print(f"Strategy Hash: {result.strategy_hash}")
        print(f"Timestamp: {result.timestamp}")
        
        # Show sample trades
        if result.trades:
            print(f"\n📋 Sample Trades (first 5):")
            for i, trade in enumerate(result.trades[:5]):
                print(f"  {i+1}. {trade.timestamp.strftime('%Y-%m-%d')} - "
                      f"{trade.action} {trade.quantity:.2f} @ ${trade.price:.2f} "
                      f"(Commission: ${trade.commission:.2f})")
    else:
        print(f"❌ Backtest failed: {result.error_message}")


def demo_strategy_comparison():
    """Demonstrate comparing multiple strategies"""
    print("\n\n🔬 Strategy Comparison Demo")
    print("=" * 50)
    
    # Generate data
    data = generate_sample_data(days=180, volatility=0.02, trend=0.001)
    
    # Create different strategies
    strategies = [
        ("RSI Conservative", RSITradingStrategy(params={
            'rsi_period': 14, 'overbought': 80, 'oversold': 20
        })),
        ("RSI Aggressive", RSITradingStrategy(params={
            'rsi_period': 7, 'overbought': 70, 'oversold': 30
        })),
        ("MACD Standard", MACDStrategy(params={
            'fast_period': 12, 'slow_period': 26, 'signal_period': 9
        }))
    ]
    
    config = BacktestConfig(initial_capital=50000, commission=0.001)
    engine = BacktestEngine()
    
    results = []
    
    print(f"\n🏁 Running {len(strategies)} strategy comparisons...")
    
    for name, strategy in strategies:
        print(f"\nTesting {name}...")
        result = engine.run(data=data, strategy=strategy, config=config)
        results.append((name, result))
        
        if result.status == BacktestStatus.COMPLETED:
            print(f"  ✅ Return: {result.metrics.total_return:.2f}%, "
                  f"Sharpe: {result.metrics.sharpe_ratio:.3f}, "
                  f"Trades: {result.metrics.total_trades}")
        else:
            print(f"  ❌ Failed: {result.error_message}")
    
    # Compare results
    print(f"\n📊 Strategy Comparison Summary:")
    print(f"{'Strategy':<20} {'Return':<10} {'Sharpe':<8} {'Drawdown':<10} {'Trades':<8}")
    print("-" * 60)
    
    for name, result in results:
        if result.status == BacktestStatus.COMPLETED:
            m = result.metrics
            print(f"{name:<20} {m.total_return:>7.2f}% {m.sharpe_ratio:>7.3f} "
                  f"{m.max_drawdown:>8.2f}% {m.total_trades:>7}")
        else:
            print(f"{name:<20} {'FAILED':<10}")


def demo_data_validation():
    """Demonstrate comprehensive data validation"""
    print("\n\n🛡️ Data Validation Demo")
    print("=" * 50)
    
    engine = BacktestEngine()
    strategy = RSITradingStrategy()
    
    # Test various invalid data scenarios
    test_cases = [
        ("None data", None),
        ("Empty list", []),
        ("Empty DataFrame", pd.DataFrame()),
        ("Missing close column", pd.DataFrame({'open': [1, 2, 3]})),
        ("NaN values", pd.DataFrame({'close': [100, np.nan, 102]})),
        ("Negative prices", pd.DataFrame({'close': [100, -50, 102]})),
        ("Non-numeric prices", pd.DataFrame({'close': ['100', '101', '102']}))
    ]
    
    print("\n🧪 Testing data validation...")
    
    for test_name, test_data in test_cases:
        try:
            result = engine.run(data=test_data, strategy=strategy, config=10000)
            if result.status == BacktestStatus.FAILED:
                print(f"  ✅ {test_name}: Properly rejected - {result.error_message}")
            else:
                print(f"  ❌ {test_name}: Should have been rejected but passed")
        except Exception as e:
            print(f"  ✅ {test_name}: Properly rejected - {str(e)}")


def demo_configuration_validation():
    """Demonstrate configuration validation"""
    print("\n\n⚙️ Configuration Validation Demo")
    print("=" * 50)
    
    # Valid data for testing
    data = generate_sample_data(days=50)
    strategy = RSITradingStrategy()
    engine = BacktestEngine()
    
    # Test invalid configurations
    invalid_configs = [
        ("Negative capital", {'initial_capital': -1000}),
        ("Invalid commission", {'initial_capital': 10000, 'commission': -0.1}),
        ("Commission > 100%", {'initial_capital': 10000, 'commission': 1.5}),
        ("Invalid position size", {'initial_capital': 10000, 'max_position_size': 1.5}),
        ("Invalid date range", {
            'initial_capital': 10000,
            'start_date': datetime(2020, 12, 31),
            'end_date': datetime(2020, 1, 1)
        })
    ]
    
    print("\n🧪 Testing configuration validation...")
    
    for test_name, config_dict in invalid_configs:
        try:
            result = engine.run(data=data, strategy=strategy, config=config_dict)
            if result.status == BacktestStatus.FAILED:
                print(f"  ✅ {test_name}: Properly rejected - {result.error_message}")
            else:
                print(f"  ❌ {test_name}: Should have been rejected but passed")
        except Exception as e:
            print(f"  ✅ {test_name}: Properly rejected - {str(e)}")


def demo_performance_benchmarking():
    """Demonstrate performance benchmarking"""
    print("\n\n⚡ Performance Benchmarking Demo")
    print("=" * 50)
    
    import time
    
    # Test with different data sizes
    data_sizes = [100, 500, 1000, 2000]
    strategy = RSITradingStrategy()
    config = BacktestConfig(initial_capital=10000)
    
    print("\n📊 Performance benchmarks:")
    print(f"{'Data Size':<12} {'Time (s)':<10} {'Trades':<8} {'Status'}")
    print("-" * 40)
    
    for size in data_sizes:
        data = generate_sample_data(days=size)
        engine = BacktestEngine()
        
        start_time = time.time()
        result = engine.run(data=data, strategy=strategy, config=config)
        execution_time = time.time() - start_time
        
        if result.status == BacktestStatus.COMPLETED:
            trades = result.metrics.total_trades
            status = "✅ OK"
        else:
            trades = 0
            status = "❌ FAIL"
        
        print(f"{size:<12} {execution_time:<10.3f} {trades:<8} {status}")


def main():
    """Run all demos"""
    try:
        demo_basic_backtest()
        demo_strategy_comparison()
        demo_data_validation()
        demo_configuration_validation()
        demo_performance_benchmarking()
        
        print("\n\n🎉 Enhanced Backtesting Engine Demo Complete!")
        print("\n✨ Key Features Demonstrated:")
        print("  • Comprehensive performance metrics calculation")
        print("  • Robust data validation and error handling")
        print("  • Multiple strategy support and comparison")
        print("  • Audit trail generation for compliance")
        print("  • Performance benchmarking capabilities")
        print("  • Enterprise-grade configuration validation")
        
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()