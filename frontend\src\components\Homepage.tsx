/**
 * Homepage Component
 * Modern landing page for AI Trading Platform
 * Built with TDD principles - implements all test requirements
 */

import React, { useState, useEffect } from 'react';
import './Homepage.css';

const Homepage: React.FC = () => {
  const [activeSection, setActiveSection] = useState('hero');

  useEffect(() => {
    const handleScroll = () => {
      const sections = ['hero', 'features', 'how-it-works', 'pricing', 'contact'];
      const scrollPosition = window.scrollY + 100;

      for (const section of sections) {
        const element = document.getElementById(section);
        if (element) {
          const offsetTop = element.offsetTop;
          const offsetBottom = offsetTop + element.offsetHeight;
          
          if (scrollPosition >= offsetTop && scrollPosition < offsetBottom) {
            setActiveSection(section);
            break;
          }
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="homepage">
      {/* Navigation */}
      <nav className={`navbar ${activeSection !== 'hero' ? 'scrolled' : ''}`}>
        <div className="nav-container">
          <div className="logo">TradeBuilder</div>
          <div className="nav-links">
            <button 
              type="button"
              onClick={() => scrollToSection('features')}
              className={activeSection === 'features' ? 'active' : ''}
            >
              Features
            </button>
            <button 
              type="button"
              onClick={() => scrollToSection('how-it-works')}
              className={activeSection === 'how-it-works' ? 'active' : ''}
            >
              How It Works
            </button>
            <button 
              type="button"
              onClick={() => scrollToSection('pricing')}
              className={activeSection === 'pricing' ? 'active' : ''}
            >
              Pricing
            </button>
            <button 
              type="button"
              onClick={() => scrollToSection('contact')}
              className={activeSection === 'contact' ? 'active' : ''}
            >
              Contact
            </button>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section id="hero" className="hero">
        <div className="hero-content">
          <h1>Build Trading Strategies Without Code</h1>
          <p>Describe your trading idea in plain English, and we'll build it for you. Connect directly to MT5 without writing a single line of MQL5.</p>
          <div className="hero-buttons">
            <button 
              type="button"
              onClick={() => scrollToSection('features')} 
              className="btn"
            >
              See How It Works
            </button>
            <button 
              type="button"
              onClick={() => scrollToSection('pricing')} 
              className="btn btn-secondary"
            >
              Start Free
            </button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="features">
        <div className="section-container">
          <h2>Everything You Need to Trade Better</h2>
          <div className="feature-grid">
            {[
              {
                icon: '🗣️',
                title: 'Describe Your Strategy',
                description: 'Just tell our chatbot what you want in plain English. No programming knowledge needed.'
              },
              {
                icon: '📊',
                title: 'Test With Real Data',
                description: 'Backtest your strategies using quality historical data. See how they would have performed.'
              },
              {
                icon: '�',
                title: 'Upload Your Own Data',
                description: 'Upload and validate your own historical trading data with our advanced data integrity pipeline.'
              },
              {
                icon: '�🚀',
                title: 'Trade on MT5',
                description: 'Connect your strategies directly to MetaTrader 5. No MQL5 code required.'
              },
              {
                icon: '🤖',
                title: 'AI Assistant',
                description: 'Get feedback on your trading and suggestions for improvement based on your history.'
              },
              {
                icon: '💰',
                title: 'Save Money',
                description: 'Stop paying for expensive EAs and indicators. Build exactly what you need for one low price.'
              },
              {
                icon: '📈',
                title: 'Track Performance',
                description: 'Monitor your results and get detailed analysis of what\'s working and what isn\'t.'
              }
            ].map((feature, index) => (
              <div key={index} className="feature">
                <div className="feature-icon" aria-hidden="true">{feature.icon}</div>
                <h3>{feature.title}</h3>
                <p>{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section id="how-it-works" className="how-it-works">
        <div className="section-container">
          <h2>How It Works</h2>
          <div className="steps">
            {[
              {
                step: '1',
                title: 'Describe Your Idea',
                description: 'Tell our chatbot what trading strategy you want in plain English.'
              },
              {
                step: '2',
                title: 'Upload Your Data',
                description: 'Use our advanced system to upload and validate your historical trading data.'
              },
              {
                step: '3',
                title: 'We Build It',
                description: 'Our AI converts your description into a working trading strategy.'
              },
              {
                step: '4',
                title: 'Test It',
                description: 'Backtest with your own data or our quality historical data to see how it performs.'
              },
              {
                step: '5',
                title: 'Start Trading',
                description: 'Connect to MT5 and let your strategy trade automatically.'
              }
            ].map((step, index) => (
              <div key={index} className="step">
                <div className="step-number" aria-label={`Step ${step.step}`}>{step.step}</div>
                <h3>{step.title}</h3>
                <p>{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="pricing">
        <div className="section-container">
          <h2>Simple Pricing</h2>
          <div className="pricing-cards">
            {[
              {
                name: 'Free',
                price: '$0',
                period: '/month',
                features: [
                  'Basic strategy building',
                  'Limited backtesting',
                  'Community support',
                  'Paper trading only'
                ],
                featured: false,
                buttonText: 'Get Started'
              },
              {
                name: 'Pro',
                price: '$49',
                period: '/month',
                features: [
                  'Unlimited strategies',
                  'Full backtesting',
                  'Live trading on MT5',
                  'AI optimization',
                  'Priority support'
                ],
                featured: true,
                buttonText: 'Start Free Trial'
              },
              {
                name: 'Team',
                price: '$99',
                period: '/month',
                features: [
                  'Everything in Pro',
                  'Team collaboration',
                  'Advanced analytics',
                  'Custom integrations',
                  'Dedicated support'
                ],
                featured: false,
                buttonText: 'Contact Sales'
              }
            ].map((plan, index) => (
              <div key={index} className={`pricing-card ${plan.featured ? 'featured' : ''}`}>
                <h3>{plan.name}</h3>
                <div className="price">{plan.price}<span>{plan.period}</span></div>
                <ul>
                  {plan.features.map((feature, idx) => (
                    <li key={idx}>{feature}</li>
                  ))}
                </ul>
                <button 
                  type="button"
                  className={`btn ${plan.featured ? '' : 'btn-secondary'}`}
                >
                  {plan.buttonText}
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer id="contact" className="footer">
        <div className="footer-content">
          <div className="footer-links">
            <a href="#" aria-label="Privacy Policy">Privacy</a>
            <a href="#" aria-label="Terms of Service">Terms</a>
            <a href="#" aria-label="Documentation">Documentation</a>
            <a href="#" aria-label="Support">Support</a>
          </div>
          <p>&copy; 2025 TradeBuilder. Built for traders, by traders.</p>
        </div>
      </footer>
    </div>
  );
};

export default Homepage;
