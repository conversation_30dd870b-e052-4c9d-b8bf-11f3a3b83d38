#!/usr/bin/env node

/**
 * Unified test runner for both Jest (TypeScript) and pytest (Python) tests
 * Ensures consistent testing across both systems
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

class TestRunner {
  constructor() {
    this.results = {
      jest: null,
      pytest: null,
    };
    this.startTime = Date.now();
  }

  log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
  }

  logSection(title) {
    this.log('\n' + '='.repeat(60), 'cyan');
    this.log(`  ${title}`, 'bright');
    this.log('='.repeat(60), 'cyan');
  }

  async runCommand(command, args, cwd = process.cwd()) {
    return new Promise((resolve, reject) => {
      this.log(`Running: ${command} ${args.join(' ')}`, 'blue');
      
      const process = spawn(command, args, {
        cwd,
        stdio: 'inherit',
        shell: true,
      });

      process.on('close', (code) => {
        if (code === 0) {
          resolve({ success: true, code });
        } else {
          resolve({ success: false, code });
        }
      });

      process.on('error', (error) => {
        reject(error);
      });
    });
  }

  async ensureDirectories() {
    const dirs = [
      'test-results',
      'test-results/jest',
      'test-results/pytest', 
      'test-results/integration',
      'test-results/coverage',
    ];

    for (const dir of dirs) {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    }
  }

  async runJestTests() {
    this.logSection('Running Jest Tests (TypeScript/Node.js)');
    
    try {
      // Run unit tests
      this.log('Running Jest unit tests...', 'yellow');
      const unitResult = await this.runCommand('npm', ['run', 'test:unit']);
      
      // Run integration tests
      this.log('Running Jest integration tests...', 'yellow');
      const integrationResult = await this.runCommand('npm', ['run', 'test:integration']);
      
      this.results.jest = {
        unit: unitResult,
        integration: integrationResult,
        overall: unitResult.success && integrationResult.success,
      };

      if (this.results.jest.overall) {
        this.log('✅ Jest tests passed!', 'green');
      } else {
        this.log('❌ Jest tests failed!', 'red');
      }

    } catch (error) {
      this.log(`❌ Jest tests error: ${error.message}`, 'red');
      this.results.jest = { overall: false, error };
    }
  }

  async runPytestTests() {
    this.logSection('Running pytest Tests (Python)');
    
    const pythonEnginePath = path.join(process.cwd(), 'python_engine');
    
    if (!fs.existsSync(pythonEnginePath)) {
      this.log('⚠️  Python engine directory not found, skipping pytest tests', 'yellow');
      this.results.pytest = { skipped: true, overall: true };
      return;
    }

    try {
      // Check if pytest is available
      const pytestCheck = await this.runCommand('python', ['-m', 'pytest', '--version'], pythonEnginePath);
      
      if (!pytestCheck.success) {
        this.log('⚠️  pytest not available, trying pip install...', 'yellow');
        await this.runCommand('pip', ['install', 'pytest', 'pytest-asyncio', 'pytest-cov', 'pytest-html', 'pytest-xdist'], pythonEnginePath);
      }

      // Run Python tests with pytest
      this.log('Running pytest tests...', 'yellow');
      const pytestResult = await this.runCommand('python', ['-m', 'pytest'], pythonEnginePath);
      
      this.results.pytest = {
        overall: pytestResult.success,
        code: pytestResult.code,
      };

      if (this.results.pytest.overall) {
        this.log('✅ pytest tests passed!', 'green');
      } else {
        this.log('❌ pytest tests failed!', 'red');
      }

    } catch (error) {
      this.log(`❌ pytest tests error: ${error.message}`, 'red');
      this.results.pytest = { overall: false, error };
    }
  }

  async generateUnifiedReport() {
    this.logSection('Generating Unified Test Report');
    
    const endTime = Date.now();
    const totalTime = (endTime - this.startTime) / 1000;
    
    const report = {
      timestamp: new Date().toISOString(),
      totalTime: `${totalTime.toFixed(2)}s`,
      results: this.results,
      summary: {
        jest: this.results.jest?.overall ?? false,
        pytest: this.results.pytest?.overall ?? false,
        overall: (this.results.jest?.overall ?? false) && (this.results.pytest?.overall ?? false),
      },
    };

    // Write JSON report
    const reportPath = path.join('test-results', 'unified-test-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    this.log(`📄 Unified report written to: ${reportPath}`, 'blue');

    // Generate HTML report
    const htmlReport = this.generateHTMLReport(report);
    const htmlPath = path.join('test-results', 'unified-test-report.html');
    fs.writeFileSync(htmlPath, htmlReport);
    this.log(`📄 HTML report written to: ${htmlPath}`, 'blue');

    return report;
  }

  generateHTMLReport(report) {
    return `
<!DOCTYPE html>
<html>
<head>
    <title>Unified Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .test-suite { border: 1px solid #ddd; padding: 15px; border-radius: 5px; flex: 1; }
        .success { border-left: 5px solid #28a745; }
        .failure { border-left: 5px solid #dc3545; }
        .skipped { border-left: 5px solid #ffc107; }
        .status { font-weight: bold; }
        .success .status { color: #28a745; }
        .failure .status { color: #dc3545; }
        .skipped .status { color: #ffc107; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 Unified Test Report</h1>
        <p><strong>Generated:</strong> ${report.timestamp}</p>
        <p><strong>Total Time:</strong> ${report.totalTime}</p>
        <p><strong>Overall Status:</strong> 
            <span class="status ${report.summary.overall ? 'success' : 'failure'}">
                ${report.summary.overall ? '✅ PASSED' : '❌ FAILED'}
            </span>
        </p>
    </div>

    <div class="summary">
        <div class="test-suite ${report.summary.jest ? 'success' : 'failure'}">
            <h3>Jest Tests (TypeScript/Node.js)</h3>
            <p class="status">${report.summary.jest ? '✅ PASSED' : '❌ FAILED'}</p>
            <p><strong>Unit Tests:</strong> ${report.results.jest?.unit?.success ? 'PASSED' : 'FAILED'}</p>
            <p><strong>Integration Tests:</strong> ${report.results.jest?.integration?.success ? 'PASSED' : 'FAILED'}</p>
        </div>

        <div class="test-suite ${report.results.pytest?.skipped ? 'skipped' : (report.summary.pytest ? 'success' : 'failure')}">
            <h3>pytest Tests (Python)</h3>
            <p class="status">
                ${report.results.pytest?.skipped ? '⚠️ SKIPPED' : 
                  (report.summary.pytest ? '✅ PASSED' : '❌ FAILED')}
            </p>
            ${report.results.pytest?.skipped ? 
              '<p>Python engine directory not found</p>' :
              `<p><strong>Exit Code:</strong> ${report.results.pytest?.code ?? 'N/A'}</p>`
            }
        </div>
    </div>

    <div class="details">
        <h3>Test Results Details</h3>
        <pre>${JSON.stringify(report.results, null, 2)}</pre>
    </div>
</body>
</html>`;
  }

  printSummary(report) {
    this.logSection('Test Summary');
    
    this.log(`📊 Total execution time: ${report.totalTime}`, 'blue');
    this.log('');
    
    // Jest results
    if (report.summary.jest) {
      this.log('✅ Jest Tests: PASSED', 'green');
    } else {
      this.log('❌ Jest Tests: FAILED', 'red');
    }
    
    // pytest results
    if (report.results.pytest?.skipped) {
      this.log('⚠️  pytest Tests: SKIPPED', 'yellow');
    } else if (report.summary.pytest) {
      this.log('✅ pytest Tests: PASSED', 'green');
    } else {
      this.log('❌ pytest Tests: FAILED', 'red');
    }
    
    this.log('');
    
    // Overall result
    if (report.summary.overall) {
      this.log('🎉 ALL TESTS PASSED!', 'green');
    } else {
      this.log('💥 SOME TESTS FAILED!', 'red');
    }
    
    this.log(`📄 Reports available in: test-results/`, 'blue');
  }

  async run() {
    try {
      this.logSection('AI Enhanced Trading Platform - Unified Test Runner');
      
      // Ensure test result directories exist
      await this.ensureDirectories();
      
      // Run Jest tests (TypeScript/Node.js)
      await this.runJestTests();
      
      // Run pytest tests (Python)
      await this.runPytestTests();
      
      // Generate unified report
      const report = await this.generateUnifiedReport();
      
      // Print summary
      this.printSummary(report);
      
      // Exit with appropriate code
      process.exit(report.summary.overall ? 0 : 1);
      
    } catch (error) {
      this.log(`💥 Test runner error: ${error.message}`, 'red');
      console.error(error);
      process.exit(1);
    }
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  jestOnly: args.includes('--jest-only'),
  pytestOnly: args.includes('--pytest-only'),
  verbose: args.includes('--verbose'),
  skipPython: args.includes('--skip-python'),
};

// Create and run test runner
const runner = new TestRunner();

if (options.jestOnly) {
  runner.runJestTests().then(() => process.exit(0));
} else if (options.pytestOnly) {
  runner.runPytestTests().then(() => process.exit(0));
} else {
  runner.run();
}