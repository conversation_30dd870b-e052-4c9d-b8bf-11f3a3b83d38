# src/ml/model_registry.py
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional
from .model_pipeline import ModelVersion, PerformanceMetrics

logger = logging.getLogger(__name__)

class ModelRegistry:
    """Registry for managing ML model versions"""
    
    def __init__(self, registry_path: str = "models/registry.json"):
        self.registry_path = Path(registry_path)
        self.models: Dict[str, ModelVersion] = {}
        self._load_registry()
    
    def _load_registry(self) -> None:
        """Load model registry from disk"""
        try:
            if self.registry_path.exists():
                with open(self.registry_path, 'r') as f:
                    data = json.load(f)
                    
                for model_data in data.get('models', []):
                    model = self._deserialize_model(model_data)
                    self.models[model.id] = model
                    
                logger.info(f"Loaded {len(self.models)} models from registry")
            else:
                logger.info("No existing registry found, starting fresh")
                
        except Exception as e:
            logger.error(f"Failed to load model registry: {str(e)}")
            self.models = {}
    
    def _save_registry(self) -> None:
        """Save model registry to disk"""
        try:
            # Ensure directory exists
            self.registry_path.parent.mkdir(parents=True, exist_ok=True)
            
            data = {
                'models': [self._serialize_model(model) for model in self.models.values()],
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.registry_path, 'w') as f:
                json.dump(data, f, indent=2, default=str)
                
            logger.info(f"Saved {len(self.models)} models to registry")
            
        except Exception as e:
            logger.error(f"Failed to save model registry: {str(e)}")
    
    def register_model(self, model: ModelVersion) -> None:
        """Register a new model version"""
        if model.id in self.models:
            logger.warning(f"Model {model.id} already exists, overwriting")
        
        self.models[model.id] = model
        self._save_registry()
        
        logger.info(f"Registered model {model.id} version {model.version}")
    
    def get_model(self, model_id: str) -> Optional[ModelVersion]:
        """Get a specific model by ID"""
        return self.models.get(model_id)
    
    def get_latest_model(self, algorithm: Optional[str] = None) -> Optional[ModelVersion]:
        """Get the latest model, optionally filtered by algorithm"""
        candidates = list(self.models.values())
        
        if algorithm:
            candidates = [m for m in candidates if m.algorithm == algorithm]
        
        if not candidates:
            return None
        
        # Sort by creation date and return the latest
        return max(candidates, key=lambda m: m.created_at)
    
    def get_best_model(self, metric: str = 'backtest_sharpe') -> Optional[ModelVersion]:
        """Get the best performing model by specified metric"""
        if not self.models:
            return None
        
        def get_metric_value(model: ModelVersion) -> float:
            metrics = model.performance_metrics
            metric_map = {
                'accuracy': metrics.accuracy,
                'precision': metrics.precision,
                'recall': metrics.recall,
                'f1_score': metrics.f1_score,
                'backtest_sharpe': metrics.backtest_sharpe
            }
            return metric_map.get(metric, 0.0)
        
        return max(self.models.values(), key=get_metric_value)
    
    def list_models(self, algorithm: Optional[str] = None) -> List[ModelVersion]:
        """List all models, optionally filtered by algorithm"""
        models = list(self.models.values())
        
        if algorithm:
            models = [m for m in models if m.algorithm == algorithm]
        
        # Sort by creation date (newest first)
        return sorted(models, key=lambda m: m.created_at, reverse=True)
    
    def remove_model(self, model_id: str) -> bool:
        """Remove a model from the registry"""
        if model_id in self.models:
            del self.models[model_id]
            self._save_registry()
            logger.info(f"Removed model {model_id}")
            return True
        
        logger.warning(f"Model {model_id} not found")
        return False
    
    def get_model_stats(self) -> Dict[str, any]:
        """Get registry statistics"""
        if not self.models:
            return {
                'total_models': 0,
                'algorithms': [],
                'latest_model': None,
                'best_model': None
            }
        
        algorithms = list(set(m.algorithm for m in self.models.values()))
        latest = self.get_latest_model()
        best = self.get_best_model()
        
        return {
            'total_models': len(self.models),
            'algorithms': algorithms,
            'latest_model': {
                'id': latest.id,
                'version': latest.version,
                'algorithm': latest.algorithm,
                'created_at': latest.created_at.isoformat()
            } if latest else None,
            'best_model': {
                'id': best.id,
                'version': best.version,
                'algorithm': best.algorithm,
                'sharpe_ratio': best.performance_metrics.backtest_sharpe
            } if best else None
        }
    
    def _serialize_model(self, model: ModelVersion) -> Dict:
        """Serialize model to dictionary"""
        return {
            'id': model.id,
            'version': model.version,
            'algorithm': model.algorithm,
            'training_data_hash': model.training_data_hash,
            'hyperparameters': model.hyperparameters,
            'performance_metrics': {
                'accuracy': model.performance_metrics.accuracy,
                'precision': model.performance_metrics.precision,
                'recall': model.performance_metrics.recall,
                'f1_score': model.performance_metrics.f1_score,
                'backtest_sharpe': model.performance_metrics.backtest_sharpe
            },
            'created_at': model.created_at.isoformat(),
            'model_hash': model.model_hash
        }
    
    def _deserialize_model(self, data: Dict) -> ModelVersion:
        """Deserialize model from dictionary"""
        return ModelVersion(
            id=data['id'],
            version=data['version'],
            algorithm=data['algorithm'],
            training_data_hash=data['training_data_hash'],
            hyperparameters=data['hyperparameters'],
            performance_metrics=PerformanceMetrics(
                accuracy=data['performance_metrics']['accuracy'],
                precision=data['performance_metrics']['precision'],
                recall=data['performance_metrics']['recall'],
                f1_score=data['performance_metrics']['f1_score'],
                backtest_sharpe=data['performance_metrics']['backtest_sharpe']
            ),
            created_at=datetime.fromisoformat(data['created_at']),
            model_hash=data['model_hash']
        )