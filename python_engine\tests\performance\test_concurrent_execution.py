"""
Concurrent Execution Performance Tests

Tests the system's ability to handle multiple concurrent strategy executions
with thread safety and performance under load.
"""

import pytest
import time
import threading
import statistics
import sys
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any
import queue
import random

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

from services.darwin_godel.strategy_verifier import DarwinGodelVerifier
from services.darwin_godel.secure_executor import SecureStrategyExecutor

class TestConcurrentExecution:
    """Performance tests for concurrent strategy execution"""
    
    def setup_method(self):
        """Setup before each test"""
        self.verifier = DarwinGodelVerifier()
        self.executor = SecureStrategyExecutor()
        
        # Different strategies for concurrent testing
        self.strategies = {
            'momentum': """
def trading_strategy(data, params):
    if len(data['close']) < 5:
        return {'signal': 'hold', 'confidence': 0.5}
    
    short_ma = sum(data['close'][-3:]) / 3
    long_ma = sum(data['close'][-5:]) / 5
    
    if short_ma > long_ma * 1.01:
        return {'signal': 'buy', 'confidence': 0.8}
    elif short_ma < long_ma * 0.99:
        return {'signal': 'sell', 'confidence': 0.8}
    else:
        return {'signal': 'hold', 'confidence': 0.4}
""",
            'mean_reversion': """
def trading_strategy(data, params):
    if len(data['close']) < 10:
        return {'signal': 'hold', 'confidence': 0.5}
    
    sma = sum(data['close'][-10:]) / 10
    current_price = data['close'][-1]
    
    if current_price < sma * 0.98:
        return {'signal': 'buy', 'confidence': 0.9}
    elif current_price > sma * 1.02:
        return {'signal': 'sell', 'confidence': 0.9}
    else:
        return {'signal': 'hold', 'confidence': 0.3}
""",
            'rsi_based': """
def trading_strategy(data, params):
    if len(data['close']) < 15:
        return {'signal': 'hold', 'confidence': 0.5}
    
    # Simple RSI calculation
    gains = []
    losses = []
    for i in range(1, min(15, len(data['close']))):
        change = data['close'][-i] - data['close'][-i-1]
        if change > 0:
            gains.append(change)
        else:
            losses.append(abs(change))
    
    avg_gain = sum(gains) / len(gains) if gains else 0
    avg_loss = sum(losses) / len(losses) if losses else 1
    
    rsi = 100 - (100 / (1 + avg_gain / avg_loss))
    
    if rsi < 30:
        return {'signal': 'buy', 'confidence': 0.85}
    elif rsi > 70:
        return {'signal': 'sell', 'confidence': 0.85}
    else:
        return {'signal': 'hold', 'confidence': 0.4}
"""
        }
        
        # Sample market data
        self.market_data = {
            'close': [100.0 + random.uniform(-1, 1) for _ in range(100)],
            'high': [101.0 + random.uniform(-1, 1) for _ in range(100)],
            'low': [99.0 + random.uniform(-1, 1) for _ in range(100)],
            'volume': [10000 + random.randint(-1000, 1000) for _ in range(100)]
        }
    
    def execute_strategy_worker(self, strategy_name: str, strategy_code: str, iterations: int) -> Dict[str, Any]:
        """Worker function for concurrent execution testing"""
        results = []
        execution_times = []
        errors = 0
        
        for _ in range(iterations):
            try:
                start_time = time.perf_counter()
                result = self.executor.execute_strategy(strategy_code, self.market_data, {})
                end_time = time.perf_counter()
                
                execution_time = (end_time - start_time) * 1000
                execution_times.append(execution_time)
                results.append(result)
                
                # Validate result
                assert 'signal' in result
                assert result['signal'] in ['buy', 'sell', 'hold']
                assert 0.0 <= result['confidence'] <= 1.0
                
            except Exception as e:
                errors += 1
        
        return {
            'strategy': strategy_name,
            'results': results,
            'execution_times': execution_times,
            'errors': errors,
            'avg_time': statistics.mean(execution_times) if execution_times else 0,
            'total_executions': len(results)
        }
    
    @pytest.mark.performance
    def test_concurrent_strategy_execution(self):
        """Test concurrent execution of multiple strategies"""
        num_threads = 4
        iterations_per_thread = 50
        
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            # Submit tasks for each strategy
            futures = []
            for i, (strategy_name, strategy_code) in enumerate(self.strategies.items()):
                future = executor.submit(
                    self.execute_strategy_worker,
                    strategy_name,
                    strategy_code,
                    iterations_per_thread
                )
                futures.append(future)
            
            # Collect results
            results = []
            for future in as_completed(futures):
                result = future.result()
                results.append(result)
        
        # Analyze results
        total_executions = sum(r['total_executions'] for r in results)
        total_errors = sum(r['errors'] for r in results)
        avg_execution_times = [r['avg_time'] for r in results if r['avg_time'] > 0]
        
        print(f"\n=== Concurrent Execution Test ===")
        print(f"Threads: {num_threads}")
        print(f"Total executions: {total_executions}")
        print(f"Total errors: {total_errors}")
        print(f"Overall avg time: {statistics.mean(avg_execution_times):.2f}ms")
        
        for result in results:
            print(f"Strategy {result['strategy']}: {result['total_executions']} executions, "
                  f"avg {result['avg_time']:.2f}ms, {result['errors']} errors")
        
        # Performance assertions
        assert total_errors == 0, f"Concurrent execution had {total_errors} errors"
        assert total_executions == num_threads * iterations_per_thread, "Missing executions"
        assert statistics.mean(avg_execution_times) < 50.0, "Concurrent execution too slow"
    
    @pytest.mark.performance
    def test_thread_safety(self):
        """Test thread safety with shared resources"""
        num_threads = 8
        iterations_per_thread = 25
        shared_results = queue.Queue()
        errors = queue.Queue()
        
        def worker(thread_id: int):
            """Worker function that uses shared executor"""
            for i in range(iterations_per_thread):
                try:
                    # Use different strategies randomly
                    strategy_name = random.choice(list(self.strategies.keys()))
                    strategy_code = self.strategies[strategy_name]
                    
                    result = self.executor.execute_strategy(strategy_code, self.market_data, {})
                    
                    # Validate result
                    assert 'signal' in result
                    assert result['signal'] in ['buy', 'sell', 'hold']
                    
                    shared_results.put({
                        'thread_id': thread_id,
                        'iteration': i,
                        'strategy': strategy_name,
                        'result': result
                    })
                    
                except Exception as e:
                    errors.put({
                        'thread_id': thread_id,
                        'iteration': i,
                        'error': str(e)
                    })
        
        # Start threads
        threads = []
        start_time = time.time()
        
        for thread_id in range(num_threads):
            thread = threading.Thread(target=worker, args=(thread_id,))
            threads.append(thread)
            thread.start()
        
        # Wait for completion
        for thread in threads:
            thread.join()
        
        end_time = time.time()
        
        # Collect results
        results_list = []
        while not shared_results.empty():
            results_list.append(shared_results.get())
        
        errors_list = []
        while not errors.empty():
            errors_list.append(errors.get())
        
        total_time = end_time - start_time
        expected_results = num_threads * iterations_per_thread
        
        print(f"\n=== Thread Safety Test ===")
        print(f"Threads: {num_threads}")
        print(f"Expected results: {expected_results}")
        print(f"Actual results: {len(results_list)}")
        print(f"Errors: {len(errors_list)}")
        print(f"Total time: {total_time:.2f}s")
        print(f"Throughput: {len(results_list) / total_time:.2f} exec/s")
        
        # Thread safety assertions
        assert len(errors_list) == 0, f"Thread safety test had {len(errors_list)} errors"
        assert len(results_list) == expected_results, f"Expected {expected_results}, got {len(results_list)}"
        
        # Verify all threads completed their work
        thread_counts = {}
        for result in results_list:
            thread_id = result['thread_id']
            thread_counts[thread_id] = thread_counts.get(thread_id, 0) + 1
        
        for thread_id in range(num_threads):
            assert thread_counts.get(thread_id, 0) == iterations_per_thread, \
                f"Thread {thread_id} completed {thread_counts.get(thread_id, 0)} instead of {iterations_per_thread}"
    
    @pytest.mark.performance
    def test_load_testing(self):
        """Test system under heavy concurrent load"""
        load_levels = [2, 4, 8, 16]  # Different numbers of concurrent threads
        results_by_load = {}
        
        for num_threads in load_levels:
            iterations_per_thread = 20
            
            with ThreadPoolExecutor(max_workers=num_threads) as executor:
                start_time = time.time()
                
                # Submit all tasks
                futures = []
                for thread_id in range(num_threads):
                    strategy_name = list(self.strategies.keys())[thread_id % len(self.strategies)]
                    strategy_code = self.strategies[strategy_name]
                    
                    future = executor.submit(
                        self.execute_strategy_worker,
                        strategy_name,
                        strategy_code,
                        iterations_per_thread
                    )
                    futures.append(future)
                
                # Wait for completion and collect results
                thread_results = []
                for future in as_completed(futures):
                    thread_results.append(future.result())
                
                end_time = time.time()
            
            # Analyze load test results
            total_time = end_time - start_time
            total_executions = sum(r['total_executions'] for r in thread_results)
            total_errors = sum(r['errors'] for r in thread_results)
            avg_times = [r['avg_time'] for r in thread_results if r['avg_time'] > 0]
            
            results_by_load[num_threads] = {
                'total_time': total_time,
                'total_executions': total_executions,
                'total_errors': total_errors,
                'throughput': total_executions / total_time,
                'avg_latency': statistics.mean(avg_times) if avg_times else 0,
                'p95_latency': statistics.quantiles(avg_times, n=20)[18] if len(avg_times) >= 20 else max(avg_times) if avg_times else 0
            }
        
        print(f"\n=== Load Testing Results ===")
        print(f"{'Threads':<8} {'Throughput':<12} {'Avg Latency':<12} {'P95 Latency':<12} {'Errors':<8}")
        print("-" * 60)
        
        for threads, metrics in results_by_load.items():
            print(f"{threads:<8} {metrics['throughput']:<12.2f} {metrics['avg_latency']:<12.2f} "
                  f"{metrics['p95_latency']:<12.2f} {metrics['total_errors']:<8}")
        
        # Performance assertions
        for threads, metrics in results_by_load.items():
            assert metrics['total_errors'] == 0, f"Load test with {threads} threads had errors"
            assert metrics['throughput'] > 10, f"Throughput too low with {threads} threads"
            assert metrics['avg_latency'] < 100, f"Latency too high with {threads} threads"
        
        # Throughput should scale reasonably with thread count (up to a point)
        throughput_2 = results_by_load[2]['throughput']
        throughput_4 = results_by_load[4]['throughput']
        
        scaling_factor = throughput_4 / throughput_2
        assert scaling_factor > 1.2, f"Poor scaling: {scaling_factor:.2f}x improvement with 2x threads"
    
    @pytest.mark.performance
    def test_concurrent_verification(self):
        """Test concurrent strategy verification performance"""
        num_threads = 6
        verifications_per_thread = 10
        
        def verification_worker(thread_id: int) -> Dict[str, Any]:
            """Worker for concurrent verification"""
            verification_times = []
            results = []
            errors = 0
            
            for i in range(verifications_per_thread):
                try:
                    # Use different strategies
                    strategy_name = list(self.strategies.keys())[i % len(self.strategies)]
                    strategy_code = self.strategies[strategy_name]
                    
                    start_time = time.perf_counter()
                    result = self.verifier.verify_strategy(strategy_code)
                    end_time = time.perf_counter()
                    
                    verification_time = (end_time - start_time) * 1000
                    verification_times.append(verification_time)
                    results.append(result)
                    
                    # Validate result
                    assert 'is_valid' in result
                    
                except Exception as e:
                    errors += 1
            
            return {
                'thread_id': thread_id,
                'verification_times': verification_times,
                'results': results,
                'errors': errors,
                'avg_time': statistics.mean(verification_times) if verification_times else 0
            }
        
        # Run concurrent verification
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            start_time = time.time()
            
            futures = [
                executor.submit(verification_worker, thread_id)
                for thread_id in range(num_threads)
            ]
            
            results = [future.result() for future in as_completed(futures)]
            
            end_time = time.time()
        
        # Analyze results
        total_time = end_time - start_time
        total_verifications = sum(len(r['results']) for r in results)
        total_errors = sum(r['errors'] for r in results)
        all_times = []
        for r in results:
            all_times.extend(r['verification_times'])
        
        print(f"\n=== Concurrent Verification Test ===")
        print(f"Threads: {num_threads}")
        print(f"Total verifications: {total_verifications}")
        print(f"Total time: {total_time:.2f}s")
        print(f"Throughput: {total_verifications / total_time:.2f} verifications/s")
        print(f"Avg verification time: {statistics.mean(all_times):.2f}ms")
        print(f"Errors: {total_errors}")
        
        # Assertions
        assert total_errors == 0, f"Concurrent verification had {total_errors} errors"
        assert total_verifications == num_threads * verifications_per_thread
        assert statistics.mean(all_times) < 200.0, "Concurrent verification too slow"
    
    @pytest.mark.performance
    def test_resource_contention(self):
        """Test performance under resource contention"""
        # Create resource contention by having many threads access shared resources
        num_threads = 12
        iterations_per_thread = 15
        contention_results = []
        
        def contention_worker(thread_id: int):
            """Worker that creates resource contention"""
            local_results = []
            
            for i in range(iterations_per_thread):
                # Simulate resource contention by using the same executor instance
                strategy_code = self.strategies['momentum']  # All threads use same strategy
                
                start_time = time.perf_counter()
                result = self.executor.execute_strategy(strategy_code, self.market_data, {})
                end_time = time.perf_counter()
                
                execution_time = (end_time - start_time) * 1000
                local_results.append({
                    'thread_id': thread_id,
                    'iteration': i,
                    'execution_time': execution_time,
                    'result': result
                })
                
                # Small delay to increase contention
                time.sleep(0.001)
            
            return local_results
        
        # Run with high contention
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            start_time = time.time()
            
            futures = [
                executor.submit(contention_worker, thread_id)
                for thread_id in range(num_threads)
            ]
            
            for future in as_completed(futures):
                contention_results.extend(future.result())
            
            end_time = time.time()
        
        # Analyze contention results
        total_time = end_time - start_time
        execution_times = [r['execution_time'] for r in contention_results]
        
        print(f"\n=== Resource Contention Test ===")
        print(f"Threads: {num_threads}")
        print(f"Total executions: {len(contention_results)}")
        print(f"Total time: {total_time:.2f}s")
        print(f"Avg execution time: {statistics.mean(execution_times):.2f}ms")
        print(f"Max execution time: {max(execution_times):.2f}ms")
        print(f"Std deviation: {statistics.stdev(execution_times):.2f}ms")
        
        # Contention should not cause excessive slowdown
        assert statistics.mean(execution_times) < 100.0, "Resource contention caused excessive slowdown"
        assert max(execution_times) < 500.0, "Some executions were extremely slow under contention"
        assert len(contention_results) == num_threads * iterations_per_thread, "Lost executions under contention"