"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PythonDarwinResponseSchema = exports.PythonDarwinRequestSchema = exports.DarwinResultsSchema = exports.DarwinJobStatusSchema = exports.ForexGenomeSchema = exports.DarwinEvolutionResponseSchema = exports.DarwinEvolutionRequestSchema = exports.EvolutionStateSchema = exports.EvolutionParametersSchema = exports.TradingStrategySchema = exports.RiskManagementSchema = exports.TradingConditionSchema = exports.FitnessObjectiveSchema = exports.EvolutionStatusSchema = void 0;
const zod_1 = require("zod");
// Evolution Status Enum
exports.EvolutionStatusSchema = zod_1.z.enum([
    'initializing',
    'running',
    'paused',
    'completed',
    'failed',
    'terminated'
]);
// Fitness Objective Enum
exports.FitnessObjectiveSchema = zod_1.z.enum([
    'sharpe_ratio',
    'profit_factor',
    'win_rate',
    'max_drawdown',
    'custom'
]);
// Trading Condition Schema
exports.TradingConditionSchema = zod_1.z.object({
    indicator: zod_1.z.string(),
    operator: zod_1.z.enum(['>', '<', '>=', '<=', '==', 'crossover', 'crossunder']),
    value: zod_1.z.union([zod_1.z.number(), zod_1.z.string()]),
    timeframe: zod_1.z.string().optional(),
});
// Risk Management Schema
exports.RiskManagementSchema = zod_1.z.object({
    stop_loss_pips: zod_1.z.number().optional(),
    take_profit_pips: zod_1.z.number().optional(),
    position_size_percent: zod_1.z.number().min(0).max(1),
    max_daily_loss_percent: zod_1.z.number().min(0).max(1),
    max_concurrent_trades: zod_1.z.number().int().positive(),
});
// Trading Strategy Schema
exports.TradingStrategySchema = zod_1.z.object({
    id: zod_1.z.string().uuid(),
    name: zod_1.z.string(),
    conditions: zod_1.z.array(exports.TradingConditionSchema),
    risk_management: exports.RiskManagementSchema,
    fitness_score: zod_1.z.number().optional(),
    is_verified: zod_1.z.boolean().default(false),
    verification_proof: zod_1.z.string().optional(),
    generation: zod_1.z.number().int().nonnegative().optional(),
    parent_ids: zod_1.z.array(zod_1.z.string().uuid()).optional(),
    created_at: zod_1.z.date().default(() => new Date()),
});
// Evolution Parameters Schema
exports.EvolutionParametersSchema = zod_1.z.object({
    population_size: zod_1.z.number().int().positive().default(50),
    max_generations: zod_1.z.number().int().positive().default(30),
    mutation_rate: zod_1.z.number().min(0).max(1).default(0.15),
    crossover_rate: zod_1.z.number().min(0).max(1).default(0.8),
    fitness_objective: exports.FitnessObjectiveSchema.default('sharpe_ratio'),
    elitism_rate: zod_1.z.number().min(0).max(1).default(0.1),
    tournament_size: zod_1.z.number().int().positive().default(3),
    max_strategy_complexity: zod_1.z.number().int().positive().default(10),
    verification_enabled: zod_1.z.boolean().default(true),
});
// Evolution State Schema
exports.EvolutionStateSchema = zod_1.z.object({
    job_id: zod_1.z.string().uuid(),
    status: exports.EvolutionStatusSchema,
    generation: zod_1.z.number().int().nonnegative(),
    population: zod_1.z.array(exports.TradingStrategySchema),
    best_fitness: zod_1.z.number(),
    average_fitness: zod_1.z.number(),
    verified_count: zod_1.z.number().int().nonnegative(),
    total_strategies_tested: zod_1.z.number().int().nonnegative(),
    start_time: zod_1.z.date(),
    last_update: zod_1.z.date(),
    estimated_completion: zod_1.z.date().optional(),
    error_message: zod_1.z.string().optional(),
});
// Darwin Evolution Request Schema
exports.DarwinEvolutionRequestSchema = zod_1.z.object({
    pair: zod_1.z.string(),
    timeframe: zod_1.z.string(),
    evolution_params: exports.EvolutionParametersSchema.optional(),
    data_start_date: zod_1.z.date().optional(),
    data_end_date: zod_1.z.date().optional(),
    callback_url: zod_1.z.string().url().optional(),
});
// Darwin Evolution Response Schema
exports.DarwinEvolutionResponseSchema = zod_1.z.object({
    job_id: zod_1.z.string().uuid(),
    status: zod_1.z.enum(['started', 'queued', 'failed']),
    message: zod_1.z.string(),
    estimated_duration_minutes: zod_1.z.number().optional(),
});
// Forex Genome Schema
exports.ForexGenomeSchema = zod_1.z.object({
    pair: zod_1.z.string(),
    timeframe: zod_1.z.string(),
    behavioral_patterns: zod_1.z.record(zod_1.z.any()),
    volatility_profile: zod_1.z.object({
        average_volatility: zod_1.z.number(),
        volatility_clusters: zod_1.z.array(zod_1.z.object({
            start_hour: zod_1.z.number().int().min(0).max(23),
            end_hour: zod_1.z.number().int().min(0).max(23),
            volatility_multiplier: zod_1.z.number(),
        })),
    }),
    trend_characteristics: zod_1.z.object({
        trend_persistence: zod_1.z.number().min(0).max(1),
        reversal_frequency: zod_1.z.number(),
        support_resistance_strength: zod_1.z.number().min(0).max(1),
    }),
    optimal_strategies: zod_1.z.array(exports.TradingStrategySchema),
    confidence_score: zod_1.z.number().min(0).max(1),
    generated_at: zod_1.z.date().default(() => new Date()),
});
// Darwin Job Status Schema
exports.DarwinJobStatusSchema = zod_1.z.object({
    job_id: zod_1.z.string().uuid(),
    status: exports.EvolutionStatusSchema,
    progress: zod_1.z.object({
        current_generation: zod_1.z.number().int().nonnegative(),
        total_generations: zod_1.z.number().int().positive(),
        completion_percentage: zod_1.z.number().min(0).max(100),
    }),
    metrics: zod_1.z.object({
        best_fitness: zod_1.z.number(),
        average_fitness: zod_1.z.number(),
        verified_strategies: zod_1.z.number().int().nonnegative(),
        total_strategies: zod_1.z.number().int().nonnegative(),
    }),
    runtime_info: zod_1.z.object({
        start_time: zod_1.z.date(),
        elapsed_seconds: zod_1.z.number().nonnegative(),
        estimated_remaining_seconds: zod_1.z.number().nonnegative().optional(),
    }),
    error: zod_1.z.string().optional(),
});
// Darwin Results Schema
exports.DarwinResultsSchema = zod_1.z.object({
    job_id: zod_1.z.string().uuid(),
    evolution_params: exports.EvolutionParametersSchema,
    final_state: exports.EvolutionStateSchema,
    best_strategies: zod_1.z.array(exports.TradingStrategySchema),
    forex_genome: exports.ForexGenomeSchema.optional(),
    performance_summary: zod_1.z.object({
        total_runtime_seconds: zod_1.z.number().nonnegative(),
        strategies_evolved: zod_1.z.number().int().nonnegative(),
        verification_success_rate: zod_1.z.number().min(0).max(1),
        fitness_improvement: zod_1.z.number(),
    }),
    completed_at: zod_1.z.date().default(() => new Date()),
});
// Python Darwin Engine Request Schema (for bridge communication)
exports.PythonDarwinRequestSchema = zod_1.z.object({
    action: zod_1.z.enum(['start_evolution', 'get_status', 'get_results', 'stop_evolution', 'get_genome']),
    job_id: zod_1.z.string().uuid().optional(),
    payload: zod_1.z.record(zod_1.z.any()).optional(),
    timestamp: zod_1.z.date().default(() => new Date()),
    request_id: zod_1.z.string().uuid(),
});
// Python Darwin Engine Response Schema
exports.PythonDarwinResponseSchema = zod_1.z.object({
    success: zod_1.z.boolean(),
    data: zod_1.z.any().optional(),
    error: zod_1.z.string().optional(),
    timestamp: zod_1.z.date().default(() => new Date()),
    request_id: zod_1.z.string().uuid(),
});
//# sourceMappingURL=darwin.schemas.js.map