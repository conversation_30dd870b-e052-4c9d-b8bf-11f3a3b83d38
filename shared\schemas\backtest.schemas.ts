import { z } from 'zod';
import { TradingSymbolSchema, OrderTypeSchema } from './trading.schemas';
import { IdSchema } from './common.schemas';

// Backtest Status
export const BacktestStatusSchema = z.enum(['pending', 'running', 'completed', 'error']);
export type BacktestStatus = z.infer<typeof BacktestStatusSchema>;

// Backtest Configuration
export const BacktestConfigSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  symbols: z.array(TradingSymbolSchema).min(1),
  start_date: z.date(),
  end_date: z.date(),
  initial_balance: z.number().positive().default(10000),
  strategy: z.object({
    name: z.string(),
    parameters: z.record(z.any()),
  }),
  risk_management: z.object({
    max_risk_per_trade: z.number().min(0).max(1).default(0.02), // 2%
    max_concurrent_trades: z.number().int().positive().default(5),
    stop_loss_pips: z.number().positive().optional(),
    take_profit_pips: z.number().positive().optional(),
  }),
}).refine(
  (data) => data.start_date < data.end_date,
  {
    message: "Start date must be before end date",
    path: ["end_date"],
  }
);
export type BacktestConfig = z.infer<typeof BacktestConfigSchema>;

// Backtest Request (from frontend to backend)
export const CreateBacktestRequestSchema = z.object({
  config: BacktestConfigSchema,
  data_source: z.enum(['historical', 'uploaded']),
  data_file_id: z.string().optional(), // If using uploaded data
});
export type CreateBacktestRequest = z.infer<typeof CreateBacktestRequestSchema>;

// Backtest Entity
export const BacktestSchema = z.object({
  id: IdSchema,
  user_id: IdSchema,
  config: BacktestConfigSchema,
  status: BacktestStatusSchema,
  progress: z.number().min(0).max(100).default(0),
  started_at: z.date().optional(),
  completed_at: z.date().optional(),
  error_message: z.string().optional(),
  created_at: z.date(),
  updated_at: z.date(),
});
export type Backtest = z.infer<typeof BacktestSchema>;

// Backtest Results (from Python engine)
export const BacktestTradeSchema = z.object({
  entry_time: z.date(),
  exit_time: z.date().optional(),
  symbol: TradingSymbolSchema,
  order_type: OrderTypeSchema,
  entry_price: z.number().positive(),
  exit_price: z.number().positive().optional(),
  volume: z.number().positive(),
  pnl: z.number(),
  pnl_pips: z.number(),
  duration_minutes: z.number().int().nonnegative().optional(),
  reason: z.enum(['stop_loss', 'take_profit', 'strategy_exit', 'timeout']).optional(),
});
export type BacktestTrade = z.infer<typeof BacktestTradeSchema>;

export const BacktestMetricsSchema = z.object({
  // Performance Metrics
  total_trades: z.number().int().nonnegative(),
  winning_trades: z.number().int().nonnegative(),
  losing_trades: z.number().int().nonnegative(),
  win_rate: z.number().min(0).max(1),
  
  // PnL Metrics
  total_pnl: z.number(),
  gross_profit: z.number().nonnegative(),
  gross_loss: z.number().nonpositive(),
  profit_factor: z.number().nonnegative(),
  
  // Risk Metrics
  max_drawdown: z.number().nonpositive(),
  max_drawdown_percent: z.number().min(-1).max(0),
  sharpe_ratio: z.number().optional(),
  sortino_ratio: z.number().optional(),
  
  // Trade Metrics
  average_win: z.number().nonnegative(),
  average_loss: z.number().nonpositive(),
  largest_win: z.number().nonnegative(),
  largest_loss: z.number().nonpositive(),
  
  // Time Metrics
  average_trade_duration_minutes: z.number().nonnegative().optional(),
  total_time_in_market_minutes: z.number().nonnegative().optional(),
  
  // Additional Metrics
  expectancy: z.number(),
  kelly_criterion: z.number().optional(),
  calmar_ratio: z.number().optional(),
});
export type BacktestMetrics = z.infer<typeof BacktestMetricsSchema>;

export const BacktestResultsSchema = z.object({
  backtest_id: IdSchema,
  config: BacktestConfigSchema,
  metrics: BacktestMetricsSchema,
  trades: z.array(BacktestTradeSchema),
  balance_curve: z.array(z.object({
    timestamp: z.date(),
    balance: z.number(),
    equity: z.number(),
    drawdown: z.number(),
  })),
  monthly_returns: z.array(z.object({
    year: z.number().int(),
    month: z.number().int().min(1).max(12),
    return_percent: z.number(),
  })),
  created_at: z.date(),
});
export type BacktestResults = z.infer<typeof BacktestResultsSchema>;

// Python Engine Integration Schemas
export const PythonBacktestRequestSchema = z.object({
  request_id: z.string().uuid(),
  config: BacktestConfigSchema,
  data: z.object({
    market_data: z.array(z.object({
      symbol: TradingSymbolSchema,
      timestamp: z.date(),
      open: z.number(),
      high: z.number(),
      low: z.number(),
      close: z.number(),
      volume: z.number().optional(),
    })),
  }),
});
export type PythonBacktestRequest = z.infer<typeof PythonBacktestRequestSchema>;

export const PythonBacktestResponseSchema = z.object({
  request_id: z.string().uuid(),
  success: z.boolean(),
  results: BacktestResultsSchema.optional(),
  error: z.string().optional(),
  execution_time_seconds: z.number().nonnegative(),
});
export type PythonBacktestResponse = z.infer<typeof PythonBacktestResponseSchema>;

// Progress Updates from Python Engine
export const BacktestProgressSchema = z.object({
  backtest_id: IdSchema,
  progress: z.number().min(0).max(100),
  current_date: z.date().optional(),
  trades_executed: z.number().int().nonnegative(),
  current_balance: z.number().optional(),
  status: BacktestStatusSchema,
  message: z.string().optional(),
});
export type BacktestProgress = z.infer<typeof BacktestProgressSchema>;