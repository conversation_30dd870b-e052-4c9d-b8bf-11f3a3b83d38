# tests/ml/test_signal_generator.py
import pytest
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from src.ml.signal_generator import SignalGenerator, SignalSchema
from src.ml.feature_engineering import FeatureEngineer
from src.ml.models import TradingModel

class TestSignalGenerator:
    """Test-first approach for ML signal generation"""
    
    @pytest.fixture
    def sample_market_data(self):
        """Generate sample market data for testing"""
        dates = pd.date_range(start='2024-01-01', periods=100, freq='1H')
        return pd.DataFrame({
            'timestamp': dates,
            'open': np.random.uniform(100, 110, 100),
            'high': np.random.uniform(110, 115, 100),
            'low': np.random.uniform(95, 100, 100),
            'close': np.random.uniform(100, 110, 100),
            'volume': np.random.uniform(1000000, 2000000, 100),
            'symbol': 'AAPL'
        })
    
    @pytest.fixture
    def signal_generator(self):
        """Create signal generator instance with mocked dependencies"""
        feature_engineer = Mock(spec=FeatureEngineer)
        model = Mock(spec=TradingModel)
        return SignalGenerator(feature_engineer, model)
    
    def test_signal_generation_with_valid_data(self, signal_generator, sample_market_data):
        """Test that valid market data produces trading signals"""
        # Arrange
        expected_features = np.random.rand(100, 20)
        expected_predictions = np.random.rand(100)
        
        signal_generator.feature_engineer.extract_features.return_value = expected_features
        signal_generator.model.predict.return_value = expected_predictions
        
        # Act
        signals = signal_generator.generate_signals(sample_market_data)
        
        # Assert
        assert len(signals) == len(sample_market_data)
        assert all(isinstance(signal, dict) for signal in signals)
        assert all('symbol' in signal for signal in signals)
        assert all('confidence' in signal for signal in signals)
        assert all('action' in signal for signal in signals)
        assert all(signal['action'] in ['buy', 'sell', 'hold'] for signal in signals)
    
    def test_signal_validation(self, signal_generator):
        """Test signal validation against schema"""
        # Arrange
        valid_signal = {
            'symbol': 'AAPL',
            'action': 'buy',
            'confidence': 0.85,
            'timestamp': datetime.now(),
            'price_target': 150.0,
            'stop_loss': 145.0,
            'features': {'rsi': 45, 'macd': 0.5}
        }
        
        invalid_signal = {
            'symbol': 'AAPL',
            'action': 'invalid_action',  # Invalid action
            'confidence': 1.5,  # Invalid confidence > 1
        }
        
        # Act & Assert
        assert SignalSchema.validate(valid_signal) is True
        
        with pytest.raises(ValidationError):
            SignalSchema.validate(invalid_signal)

# tests/ml/test_feature_engineering.py
class TestFeatureEngineering:
    """TDD for feature engineering pipeline"""
    
    def test_technical_indicators_calculation(self):
        """Test that technical indicators are correctly calculated"""
        # This test is written BEFORE implementing the feature
        # Arrange
        data = pd.DataFrame({
            'close': [100, 102, 101, 103, 105, 104, 106],
            'high': [101, 103, 102, 104, 106, 105, 107],
            'low': [99, 101, 100, 102, 104, 103, 105],
            'volume': [1000, 1100, 1050, 1200, 1150, 1100, 1250]
        })
        
        engineer = FeatureEngineer()
        
        # Act
        features = engineer.calculate_technical_indicators(data)
        
        # Assert
        assert 'rsi' in features.columns
        assert 'macd' in features.columns
        assert 'bb_upper' in features.columns
        assert 'bb_lower' in features.columns
        assert not features.isnull().any().any()
    
    def test_feature_scaling(self):
        """Test feature normalization"""
        # Arrange
        features = pd.DataFrame({
            'feature1': [1, 10, 100, 1000],
            'feature2': [0.1, 0.2, 0.3, 0.4]
        })
        
        engineer = FeatureEngineer()
        
        # Act
        scaled_features = engineer.scale_features(features)
        
        # Assert
        assert scaled_features.min().min() >= 0
        assert scaled_features.max().max() <= 1

# src/ml/signal_generator.py
from typing import List, Dict, Any
import pandas as pd
import numpy as np
from dataclasses import dataclass
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

@dataclass
class Signal:
    """Trading signal data class"""
    symbol: str
    action: str  # 'buy', 'sell', 'hold'
    confidence: float
    timestamp: datetime
    price_target: float
    stop_loss: float
    features: Dict[str, float]

class SignalSchema:
    """Schema validation for signals"""
    @staticmethod
    def validate(signal: Dict[str, Any]) -> bool:
        required_fields = ['symbol', 'action', 'confidence', 'timestamp']
        
        # Check required fields
        if not all(field in signal for field in required_fields):
            raise ValidationError("Missing required fields")
        
        # Validate action
        if signal['action'] not in ['buy', 'sell', 'hold']:
            raise ValidationError(f"Invalid action: {signal['action']}")
        
        # Validate confidence
        if not 0 <= signal['confidence'] <= 1:
            raise ValidationError(f"Invalid confidence: {signal['confidence']}")
        
        return True

class SignalGenerator:
    """ML-based signal generation with TDD approach"""
    
    def __init__(self, feature_engineer, model):
        self.feature_engineer = feature_engineer
        self.model = model
        self.signal_threshold = 0.7
    
    def generate_signals(self, market_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """Generate trading signals from market data"""
        try:
            # Extract features
            features = self.feature_engineer.extract_features(market_data)
            
            # Generate predictions
            predictions = self.model.predict(features)
            
            # Convert predictions to signals
            signals = []
            for i, (idx, row) in enumerate(market_data.iterrows()):
                signal = self._create_signal(row, predictions[i], features.iloc[i])
                if signal['confidence'] >= self.signal_threshold:
                    signals.append(signal)
            
            return signals
            
        except Exception as e:
            logger.error(f"Error generating signals: {e}")
            raise

    def _create_signal(self, market_row: pd.Series, prediction: float, 
                      features: pd.Series) -> Dict[str, Any]:
        """Create a signal from prediction"""
        # Determine action based on prediction
        if prediction > 0.7:
            action = 'buy'
        elif prediction < 0.3:
            action = 'sell'
        else:
            action = 'hold'
        
        # Calculate price targets
        current_price = market_row['close']
        price_target = current_price * (1 + 0.02) if action == 'buy' else current_price * (1 - 0.02)
        stop_loss = current_price * (1 - 0.01) if action == 'buy' else current_price * (1 + 0.01)
        
        return {
            'symbol': market_row['symbol'],
            'action': action,
            'confidence': abs(prediction - 0.5) * 2,  # Convert to confidence
            'timestamp': market_row['timestamp'],
            'price_target': price_target,
            'stop_loss': stop_loss,
            'features': features.to_dict()
        }

# src/ml/feature_engineering.py
import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler
import talib

class FeatureEngineer:
    """Feature engineering for trading signals"""
    
    def __init__(self):
        self.scaler = MinMaxScaler()
        self.feature_columns = []
    
    def extract_features(self, market_data: pd.DataFrame) -> pd.DataFrame:
        """Extract all features from market data"""
        features = pd.DataFrame(index=market_data.index)
        
        # Price-based features
        features['returns'] = market_data['close'].pct_change()
        features['log_returns'] = np.log(market_data['close'] / market_data['close'].shift(1))
        
        # Volume features
        features['volume_ratio'] = market_data['volume'] / market_data['volume'].rolling(20).mean()
        
        # Technical indicators
        technical_features = self.calculate_technical_indicators(market_data)
        features = pd.concat([features, technical_features], axis=1)
        
        # Clean up NaN values
        features = features.fillna(method='ffill').fillna(0)
        
        return features
    
    def calculate_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate technical indicators"""
        indicators = pd.DataFrame(index=data.index)
        
        # RSI
        indicators['rsi'] = talib.RSI(data['close'].values, timeperiod=14)
        
        # MACD
        macd, signal, hist = talib.MACD(data['close'].values)
        indicators['macd'] = macd
        indicators['macd_signal'] = signal
        indicators['macd_hist'] = hist
        
        # Bollinger Bands
        upper, middle, lower = talib.BBANDS(data['close'].values)
        indicators['bb_upper'] = upper
        indicators['bb_middle'] = middle
        indicators['bb_lower'] = lower
        indicators['bb_width'] = (upper - lower) / middle
        
        # Moving averages
        indicators['sma_20'] = talib.SMA(data['close'].values, timeperiod=20)
        indicators['ema_20'] = talib.EMA(data['close'].values, timeperiod=20)
        
        return indicators
    
    def scale_features(self, features: pd.DataFrame) -> pd.DataFrame:
        """Normalize features to [0, 1] range"""
        scaled_array = self.scaler.fit_transform(features)
        return pd.DataFrame(scaled_array, columns=features.columns, index=features.index)

# tests/ml/test_model_deployment.py
import pytest
from unittest.mock import Mock, patch
import joblib
import numpy as np

class TestModelDeployment:
    """TDD for model deployment and serving"""
    
    def test_model_versioning(self):
        """Test model versioning system"""
        # Arrange
        model_registry = ModelRegistry()
        model_v1 = Mock()
        model_v2 = Mock()
        
        # Act
        model_registry.register_model(model_v1, version="1.0.0", metrics={'accuracy': 0.85})
        model_registry.register_model(model_v2, version="2.0.0", metrics={'accuracy': 0.90})
        
        # Assert
        assert model_registry.get_latest_version() == "2.0.0"
        assert model_registry.get_model("1.0.0") == model_v1
        assert model_registry.get_model_metrics("2.0.0")['accuracy'] == 0.90
    
    def test_model_serving_api(self):
        """Test model serving endpoint"""
        # Arrange
        app = create_test_app()
        test_data = {
            'symbol': 'AAPL',
            'features': {
                'rsi': 45.5,
                'macd': 0.8,
                'volume_ratio': 1.2
            }
        }
        
        # Act
        response = app.test_client().post('/api/ml/predict', json=test_data)
        
        # Assert
        assert response.status_code == 200
        data = response.get_json()
        assert 'signal' in data
        assert 'confidence' in data
        assert data['signal'] in ['buy', 'sell', 'hold']
    
    def test_model_monitoring(self):
        """Test model performance monitoring"""
        # Arrange
        monitor = ModelMonitor()
        predictions = np.array([0.8, 0.3, 0.6, 0.9, 0.2])
        actuals = np.array([1, 0, 1, 1, 0])
        
        # Act
        monitor.log_predictions(predictions, actuals)
        metrics = monitor.calculate_metrics()
        
        # Assert
        assert 'accuracy' in metrics
        assert 'precision' in metrics
        assert 'recall' in metrics
        assert 'drift_score' in metrics
        assert metrics['accuracy'] > 0.5  # Ensure reasonable accuracy

class ValidationError(Exception):
    """Custom validation error"""
    pass