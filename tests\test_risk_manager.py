# tests/test_risk_manager.py
import pytest
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta
from python_engine.risk_manager import (
    RiskManager, RiskManagerException, RiskLevel, 
    Position, RiskMetrics, RiskLimits
)

class TestRiskManagerInitialization:
    """Test Risk Manager initialization and basic setup"""
    
    def test_risk_manager_initialization_default(self):
        """Test default initialization"""
        # Act
        risk_manager = RiskManager()
        
        # Assert
        assert risk_manager.account_balance == 10000.0
        assert risk_manager.offline_mode is True
        assert len(risk_manager.positions) == 0
        assert len(risk_manager.daily_pnl) == 0
        assert isinstance(risk_manager.risk_limits, RiskLimits)
    
    def test_risk_manager_initialization_custom(self):
        """Test custom initialization"""
        # Act
        risk_manager = RiskManager(account_balance=50000.0, offline_mode=False)
        
        # Assert
        assert risk_manager.account_balance == 50000.0
        assert risk_manager.offline_mode is False
        assert "EURUSD" in risk_manager.mock_prices
        assert risk_manager.mock_prices["EURUSD"] > 0

class TestPositionSizeValidation:
    """Test position size validation logic"""
    
    def test_validate_position_size_success(self):
        """Test successful position size validation"""
        # Arrange
        risk_manager = RiskManager()
        
        # Act
        is_valid, message = risk_manager.validate_position_size("EURUSD", 1.0)
        
        # Assert
        assert is_valid is True
        assert "validated" in message.lower()
    
    def test_validate_position_size_exceeds_maximum(self):
        """Test position size exceeding maximum limit"""
        # Arrange
        risk_manager = RiskManager()
        
        # Act
        is_valid, message = risk_manager.validate_position_size("EURUSD", 15.0)
        
        # Assert
        assert is_valid is False
        assert "exceeds maximum allowed" in message
    
    def test_validate_position_size_total_exposure_limit(self):
        """Test total exposure limit validation"""
        # Arrange
        risk_manager = RiskManager()
        # Add positions to approach limit
        risk_manager.add_position("EURUSD", 10.0, 1.0850, "BUY")
        risk_manager.add_position("GBPUSD", 10.0, 1.2650, "BUY")
        
        # Act
        is_valid, message = risk_manager.validate_position_size("USDJPY", 85.0)
        
        # Assert
        assert is_valid is False
        assert "Total exposure would exceed limit" in message
    
    def test_validate_position_size_correlation_limit(self):
        """Test correlation exposure limit"""
        # Arrange
        risk_manager = RiskManager()
        # Add highly correlated position
        risk_manager.add_position("EURUSD", 30.0, 1.0850, "BUY")
        
        # Act - GBPUSD is correlated with EURUSD
        is_valid, message = risk_manager.validate_position_size("GBPUSD", 25.0)
        
        # Assert
        assert is_valid is False
        assert "Correlation exposure" in message

class TestPositionManagement:
    """Test position addition and removal"""
    
    def test_add_position_success(self):
        """Test successful position addition"""
        # Arrange
        risk_manager = RiskManager()
        
        # Act
        result = risk_manager.add_position("EURUSD", 1.0, 1.0850, "BUY")
        
        # Assert
        assert result is True
        assert len(risk_manager.positions) == 1
        assert risk_manager.positions[0].symbol == "EURUSD"
        assert risk_manager.positions[0].volume == 1.0
        assert risk_manager.positions[0].order_type == "BUY"
    
    def test_add_position_buy_profit_calculation(self):
        """Test P&L calculation for BUY position"""
        # Arrange
        risk_manager = RiskManager()
        # Set current price higher than entry for profit
        risk_manager.mock_prices["EURUSD"] = 1.0900
        
        # Act
        risk_manager.add_position("EURUSD", 1.0, 1.0850, "BUY")
        
        # Assert
        position = risk_manager.positions[0]
        expected_pnl = (1.0900 - 1.0850) * 1.0 * 100000  # 500
        assert position.profit_loss == expected_pnl
        assert position.profit_loss > 0  # Should be profitable
    
    def test_add_position_sell_profit_calculation(self):
        """Test P&L calculation for SELL position"""
        # Arrange
        risk_manager = RiskManager()
        # Set current price lower than entry for profit
        risk_manager.mock_prices["EURUSD"] = 1.0800
        
        # Act
        risk_manager.add_position("EURUSD", 1.0, 1.0850, "SELL")
        
        # Assert
        position = risk_manager.positions[0]
        expected_pnl = (1.0850 - 1.0800) * 1.0 * 100000  # 500
        assert position.profit_loss == expected_pnl
        assert position.profit_loss > 0  # Should be profitable
    
    def test_add_position_validation_failure(self):
        """Test position addition with validation failure"""
        # Arrange
        risk_manager = RiskManager()
        
        # Act
        result = risk_manager.add_position("EURUSD", 15.0, 1.0850, "BUY")  # Exceeds limit
        
        # Assert
        assert result is False
        assert len(risk_manager.positions) == 0
    
    def test_remove_position_success(self):
        """Test successful position removal"""
        # Arrange
        risk_manager = RiskManager()
        risk_manager.add_position("EURUSD", 1.0, 1.0850, "BUY")
        
        # Act
        result = risk_manager.remove_position("EURUSD", 1.0)
        
        # Assert
        assert result is True
        assert len(risk_manager.positions) == 0
        assert len(risk_manager.daily_pnl) == 1  # P&L should be recorded
    
    def test_remove_position_not_found(self):
        """Test removing non-existent position"""
        # Arrange
        risk_manager = RiskManager()
        
        # Act
        result = risk_manager.remove_position("EURUSD", 1.0)
        
        # Assert
        assert result is False
        assert len(risk_manager.positions) == 0

class TestRiskCalculations:
    """Test risk calculation methods"""
    
    def test_calculate_total_exposure_empty_portfolio(self):
        """Test total exposure calculation with empty portfolio"""
        # Arrange
        risk_manager = RiskManager()
        
        # Act
        exposure = risk_manager.calculate_total_exposure()
        
        # Assert
        assert exposure == 0.0
    
    def test_calculate_total_exposure_with_positions(self):
        """Test total exposure calculation with positions"""
        # Arrange
        risk_manager = RiskManager()
        risk_manager.add_position("EURUSD", 1.0, 1.0850, "BUY")
        risk_manager.add_position("GBPUSD", 2.0, 1.2650, "SELL")
        
        # Act
        exposure = risk_manager.calculate_total_exposure()
        
        # Assert
        expected_exposure = (1.0 + 2.0) * 100000  # 300,000
        assert exposure == expected_exposure
    
    def test_calculate_correlation_exposure(self):
        """Test correlation exposure calculation"""
        # Arrange
        risk_manager = RiskManager()
        risk_manager.add_position("EURUSD", 2.0, 1.0850, "BUY")
        
        # Act - GBPUSD is correlated with EURUSD (0.75)
        correlation_exposure = risk_manager.calculate_correlation_exposure("GBPUSD", 1.0)
        
        # Assert
        expected_exposure = 1.0 + (2.0 * 0.75)  # 2.5
        assert correlation_exposure == expected_exposure
    
    def test_calculate_var_insufficient_data(self):
        """Test VaR calculation with insufficient data"""
        # Arrange
        risk_manager = RiskManager()
        risk_manager.daily_pnl = [100, -50, 200]  # Less than 30 days
        
        # Act
        var = risk_manager.calculate_var()
        
        # Assert
        assert var == 0.0
    
    def test_calculate_var_sufficient_data(self):
        """Test VaR calculation with sufficient data"""
        # Arrange
        risk_manager = RiskManager()
        # Generate 50 days of mock P&L data
        risk_manager.daily_pnl = [i * 10 - 250 for i in range(50)]  # Range from -250 to 240
        
        # Act
        var = risk_manager.calculate_var(confidence_level=0.95)
        
        # Assert
        assert var > 0
        assert isinstance(var, float)
    
    def test_calculate_max_drawdown_empty(self):
        """Test max drawdown with no data"""
        # Arrange
        risk_manager = RiskManager()
        
        # Act
        drawdown = risk_manager.calculate_max_drawdown()
        
        # Assert
        assert drawdown == 0.0
    
    def test_calculate_max_drawdown_with_data(self):
        """Test max drawdown calculation"""
        # Arrange
        risk_manager = RiskManager()
        risk_manager.daily_pnl = [100, -200, 150, -300, 250]  # Simulates drawdown
        
        # Act
        drawdown = risk_manager.calculate_max_drawdown()
        
        # Assert
        assert drawdown > 0
        assert isinstance(drawdown, float)
    
    def test_calculate_sharpe_ratio_insufficient_data(self):
        """Test Sharpe ratio with insufficient data"""
        # Arrange
        risk_manager = RiskManager()
        risk_manager.daily_pnl = [100]  # Only one data point
        
        # Act
        sharpe = risk_manager.calculate_sharpe_ratio()
        
        # Assert
        assert sharpe == 0.0
    
    def test_calculate_sharpe_ratio_with_data(self):
        """Test Sharpe ratio calculation"""
        # Arrange
        risk_manager = RiskManager()
        risk_manager.daily_pnl = [100, -50, 200, 75, -25, 150, 80]
        
        # Act
        sharpe = risk_manager.calculate_sharpe_ratio()
        
        # Assert
        assert isinstance(sharpe, float)

class TestRiskAssessment:
    """Test risk level assessment"""
    
    def test_assess_risk_level_low(self):
        """Test low risk level assessment"""
        # Arrange
        risk_manager = RiskManager()
        # Add small position
        risk_manager.add_position("EURUSD", 0.1, 1.0850, "BUY")
        
        # Act
        risk_level = risk_manager.assess_risk_level()
        
        # Assert
        assert risk_level == RiskLevel.LOW
    
    def test_assess_risk_level_high_exposure(self):
        """Test high risk due to exposure"""
        # Arrange
        risk_manager = RiskManager()
        # Add large positions to increase exposure
        risk_manager.add_position("EURUSD", 8.0, 1.0850, "BUY")
        risk_manager.add_position("GBPUSD", 8.0, 1.2650, "BUY")
        
        # Act
        risk_level = risk_manager.assess_risk_level()
        
        # Assert
        assert risk_level in [RiskLevel.MEDIUM, RiskLevel.HIGH, RiskLevel.CRITICAL]
    
    def test_assess_risk_level_high_drawdown(self):
        """Test high risk due to drawdown"""
        # Arrange
        risk_manager = RiskManager()
        # Simulate high drawdown
        risk_manager.daily_pnl = [100, -2000, -1500, -1000]  # High losses
        
        # Act
        risk_level = risk_manager.assess_risk_level()
        
        # Assert
        assert risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL]

class TestRiskMetrics:
    """Test comprehensive risk metrics"""
    
    def test_get_risk_metrics_empty_portfolio(self):
        """Test risk metrics with empty portfolio"""
        # Arrange
        risk_manager = RiskManager()
        
        # Act
        metrics = risk_manager.get_risk_metrics()
        
        # Assert
        assert isinstance(metrics, RiskMetrics)
        assert metrics.total_exposure == 0.0
        assert metrics.max_drawdown == 0.0
        assert metrics.var_95 == 0.0
        assert metrics.risk_level == RiskLevel.LOW
        assert metrics.margin_used == 0.0
        assert metrics.free_margin == 10000.0  # Full account balance
    
    def test_get_risk_metrics_with_positions(self):
        """Test risk metrics with active positions"""
        # Arrange
        risk_manager = RiskManager()
        risk_manager.add_position("EURUSD", 2.0, 1.0850, "BUY")
        risk_manager.daily_pnl = [100, -50, 200, 75]
        
        # Act
        metrics = risk_manager.get_risk_metrics()
        
        # Assert
        assert isinstance(metrics, RiskMetrics)
        assert metrics.total_exposure > 0
        assert metrics.margin_used > 0
        assert metrics.free_margin < 10000.0
        assert isinstance(metrics.risk_level, RiskLevel)

class TestPriceUpdates:
    """Test price update functionality"""
    
    def test_update_prices_single_symbol(self):
        """Test updating prices for single symbol"""
        # Arrange
        risk_manager = RiskManager()
        risk_manager.add_position("EURUSD", 1.0, 1.0850, "BUY")
        original_pnl = risk_manager.positions[0].profit_loss
        
        # Act
        risk_manager.update_prices({"EURUSD": 1.0950})
        
        # Assert
        updated_pnl = risk_manager.positions[0].profit_loss
        assert risk_manager.positions[0].current_price == 1.0950
        assert updated_pnl != original_pnl
        assert updated_pnl > original_pnl  # Should be more profitable
    
    def test_update_prices_multiple_symbols(self):
        """Test updating prices for multiple symbols"""
        # Arrange
        risk_manager = RiskManager()
        risk_manager.add_position("EURUSD", 1.0, 1.0850, "BUY")
        risk_manager.add_position("GBPUSD", 1.0, 1.2650, "SELL")
        
        # Act
        risk_manager.update_prices({
            "EURUSD": 1.0950,
            "GBPUSD": 1.2550
        })
        
        # Assert
        eur_position = next(p for p in risk_manager.positions if p.symbol == "EURUSD")
        gbp_position = next(p for p in risk_manager.positions if p.symbol == "GBPUSD")
        
        assert eur_position.current_price == 1.0950
        assert gbp_position.current_price == 1.2550
        assert eur_position.profit_loss > 0  # BUY position, price went up
        assert gbp_position.profit_loss > 0  # SELL position, price went down

class TestStopLossConditions:
    """Test stop loss condition checking"""
    
    def test_check_stop_loss_no_alerts(self):
        """Test stop loss check with no alerts"""
        # Arrange
        risk_manager = RiskManager()
        risk_manager.add_position("EURUSD", 1.0, 1.0850, "BUY")
        
        # Act
        alerts = risk_manager.check_stop_loss_conditions()
        
        # Assert
        assert isinstance(alerts, list)
        assert len(alerts) == 0
    
    def test_check_stop_loss_daily_limit_exceeded(self):
        """Test stop loss alert for daily limit"""
        # Arrange
        risk_manager = RiskManager()
        # Create losing positions that exceed daily limit
        risk_manager.add_position("EURUSD", 10.0, 1.0850, "BUY")
        risk_manager.update_prices({"EURUSD": 1.0750})  # Create loss
        
        # Act
        alerts = risk_manager.check_stop_loss_conditions()
        
        # Assert
        assert len(alerts) > 0
        daily_loss_alert = next((a for a in alerts if a["type"] == "daily_loss_limit"), None)
        assert daily_loss_alert is not None
        assert "Daily loss limit exceeded" in daily_loss_alert["message"]
    
    def test_check_stop_loss_position_drawdown(self):
        """Test stop loss alert for position drawdown"""
        # Arrange
        risk_manager = RiskManager()
        risk_manager.add_position("EURUSD", 1.0, 1.0850, "BUY")
        # Create significant drawdown (>5%)
        risk_manager.update_prices({"EURUSD": 1.0300})  # Large loss
        
        # Act
        alerts = risk_manager.check_stop_loss_conditions()
        
        # Assert
        assert len(alerts) > 0
        drawdown_alert = next((a for a in alerts if a["type"] == "position_drawdown"), None)
        assert drawdown_alert is not None
        assert "Position drawdown" in drawdown_alert["message"]

class TestPositionSummary:
    """Test position summary functionality"""
    
    def test_get_position_summary_empty(self):
        """Test position summary with empty portfolio"""
        # Arrange
        risk_manager = RiskManager()
        
        # Act
        summary = risk_manager.get_position_summary()
        
        # Assert
        assert summary["total_positions"] == 0
        assert summary["total_volume"] == 0.0
        assert summary["total_pnl"] == 0.0
        assert summary["win_rate"] == 0.0
    
    def test_get_position_summary_with_positions(self):
        """Test position summary with active positions"""
        # Arrange
        risk_manager = RiskManager()
        # Add winning position
        risk_manager.add_position("EURUSD", 1.0, 1.0850, "BUY")
        risk_manager.update_prices({"EURUSD": 1.0950})  # Make it profitable
        
        # Add losing position
        risk_manager.add_position("GBPUSD", 1.0, 1.2650, "BUY")
        risk_manager.update_prices({"GBPUSD": 1.2550})  # Make it losing
        
        # Act
        summary = risk_manager.get_position_summary()
        
        # Assert
        assert summary["total_positions"] == 2
        assert summary["total_volume"] == 2.0
        assert summary["winning_positions"] == 1
        assert summary["losing_positions"] == 1
        assert summary["win_rate"] == 0.5
        assert summary["average_win"] > 0
        assert summary["average_loss"] < 0

class TestRiskManagerEdgeCases:
    """Test edge cases and error handling"""
    
    def test_add_position_exception_handling(self):
        """Test exception handling in add_position"""
        # Arrange
        risk_manager = RiskManager()
        
        # Act & Assert
        with patch.object(risk_manager, 'validate_position_size', side_effect=Exception("Test error")):
            with pytest.raises(RiskManagerException):
                risk_manager.add_position("EURUSD", 1.0, 1.0850, "BUY")
    
    def test_remove_position_exception_handling(self):
        """Test exception handling in remove_position"""
        # Arrange
        risk_manager = RiskManager()
        risk_manager.add_position("EURUSD", 1.0, 1.0850, "BUY")
        
        # Act & Assert
        with patch.object(risk_manager.positions, 'pop', side_effect=Exception("Test error")):
            with pytest.raises(RiskManagerException):
                risk_manager.remove_position("EURUSD", 1.0)
    
    def test_update_prices_exception_handling(self):
        """Test exception handling in update_prices"""
        # Arrange
        risk_manager = RiskManager()
        
        # Act & Assert
        with patch.object(risk_manager.mock_prices, 'update', side_effect=Exception("Test error")):
            with pytest.raises(RiskManagerException):
                risk_manager.update_prices({"EURUSD": 1.0950})
    
    def test_reset_portfolio(self):
        """Test portfolio reset functionality"""
        # Arrange
        risk_manager = RiskManager()
        risk_manager.add_position("EURUSD", 1.0, 1.0850, "BUY")
        risk_manager.daily_pnl = [100, -50, 200]
        
        # Act
        risk_manager.reset_portfolio()
        
        # Assert
        assert len(risk_manager.positions) == 0
        assert len(risk_manager.daily_pnl) == 0

class TestRiskManagerIntegration:
    """Test integration scenarios"""
    
    def test_complete_risk_management_workflow(self):
        """Test complete risk management workflow"""
        # Arrange
        risk_manager = RiskManager(account_balance=50000.0)
        
        # Act - Complete workflow
        # 1. Add positions
        assert risk_manager.add_position("EURUSD", 2.0, 1.0850, "BUY") is True
        assert risk_manager.add_position("GBPUSD", 1.5, 1.2650, "SELL") is True
        
        # 2. Update prices
        risk_manager.update_prices({
            "EURUSD": 1.0950,  # Profitable for BUY
            "GBPUSD": 1.2550   # Profitable for SELL
        })
        
        # 3. Check risk metrics
        metrics = risk_manager.get_risk_metrics()
        assert metrics.total_exposure > 0
        assert metrics.risk_level in [RiskLevel.LOW, RiskLevel.MEDIUM]
        
        # 4. Check stop loss conditions
        alerts = risk_manager.check_stop_loss_conditions()
        assert isinstance(alerts, list)
        
        # 5. Get position summary
        summary = risk_manager.get_position_summary()
        assert summary["total_positions"] == 2
        assert summary["total_pnl"] > 0  # Both positions should be profitable
        
        # 6. Remove positions
        assert risk_manager.remove_position("EURUSD", 2.0) is True
        assert risk_manager.remove_position("GBPUSD", 1.5) is True
        
        # Assert final state
        assert len(risk_manager.positions) == 0
        assert len(risk_manager.daily_pnl) == 2  # P&L recorded for both positions
    
    def test_risk_escalation_scenario(self):
        """Test risk escalation from low to high"""
        # Arrange
        risk_manager = RiskManager(account_balance=10000.0)
        
        # Act - Gradually increase risk
        # Start with low risk
        risk_manager.add_position("EURUSD", 1.0, 1.0850, "BUY")
        initial_risk = risk_manager.assess_risk_level()
        
        # Increase exposure
        risk_manager.add_position("GBPUSD", 3.0, 1.2650, "BUY")
        risk_manager.add_position("USDJPY", 3.0, 149.50, "BUY")
        
        # Create losses
        risk_manager.update_prices({
            "EURUSD": 1.0750,  # Loss
            "GBPUSD": 1.2550,  # Loss
            "USDJPY": 148.50   # Loss
        })
        
        # Add historical losses
        risk_manager.daily_pnl = [-500, -800, -1200, -600, -900]
        
        final_risk = risk_manager.assess_risk_level()
        
        # Assert risk escalation
        assert initial_risk == RiskLevel.LOW
        assert final_risk in [RiskLevel.HIGH, RiskLevel.CRITICAL]
        
        # Check stop loss alerts
        alerts = risk_manager.check_stop_loss_conditions()
        assert len(alerts) > 0