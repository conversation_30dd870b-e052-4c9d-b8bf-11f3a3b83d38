"""
Memory Usage Performance Tests

Tests memory consumption, garbage collection, and memory leaks
during strategy execution and verification.
"""

import pytest
import time
import gc
import sys
import os
import tracemalloc
from typing import List, Dict, Any, Tuple

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

from services.darwin_godel.strategy_verifier import DarwinGodelVerifier
from services.darwin_godel.secure_executor import SecureStrategyExecutor

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("Warning: psutil not available, some memory tests will be skipped")

class TestMemoryUsage:
    """Memory usage and efficiency tests"""
    
    def setup_method(self):
        """Setup before each test"""
        self.verifier = DarwinGodelVerifier()
        self.executor = SecureStrategyExecutor()
        
        # Test strategies of varying complexity
        self.simple_strategy = """
def trading_strategy(data, params):
    if len(data['close']) < 2:
        return {'signal': 'hold', 'confidence': 0.5}
    
    if data['close'][-1] > data['close'][-2]:
        return {'signal': 'buy', 'confidence': 0.7}
    else:
        return {'signal': 'sell', 'confidence': 0.7}
"""
        
        self.complex_strategy = """
def trading_strategy(data, params):
    if len(data['close']) < 50:
        return {'signal': 'hold', 'confidence': 0.5}
    
    # Complex calculations that use more memory
    prices = data['close']
    
    # Multiple moving averages
    sma_5 = sum(prices[-5:]) / 5
    sma_10 = sum(prices[-10:]) / 10
    sma_20 = sum(prices[-20:]) / 20
    sma_50 = sum(prices[-50:]) / 50
    
    # Volatility calculation
    returns = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices))]
    mean_return = sum(returns) / len(returns)
    variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
    volatility = variance ** 0.5
    
    # RSI calculation
    gains = []
    losses = []
    for i in range(1, min(15, len(prices))):
        change = prices[-i] - prices[-i-1]
        if change > 0:
            gains.append(change)
        else:
            losses.append(abs(change))
    
    avg_gain = sum(gains) / len(gains) if gains else 0
    avg_loss = sum(losses) / len(losses) if losses else 1
    rsi = 100 - (100 / (1 + avg_gain / avg_loss))
    
    # Bollinger Bands
    bb_period = 20
    bb_std_dev = 2
    bb_sma = sum(prices[-bb_period:]) / bb_period
    bb_variance = sum((p - bb_sma) ** 2 for p in prices[-bb_period:]) / bb_period
    bb_std = bb_variance ** 0.5
    bb_upper = bb_sma + (bb_std * bb_std_dev)
    bb_lower = bb_sma - (bb_std * bb_std_dev)
    
    # Complex decision logic
    current_price = prices[-1]
    
    # Multiple conditions
    momentum_signal = 'buy' if sma_5 > sma_10 > sma_20 else 'sell'
    volatility_signal = 'hold' if volatility > 0.02 else 'trade'
    rsi_signal = 'buy' if rsi < 30 else 'sell' if rsi > 70 else 'hold'
    bb_signal = 'buy' if current_price < bb_lower else 'sell' if current_price > bb_upper else 'hold'
    
    # Combine signals
    signals = [momentum_signal, rsi_signal, bb_signal]
    if volatility_signal == 'hold':
        return {'signal': 'hold', 'confidence': 0.3}
    
    buy_votes = signals.count('buy')
    sell_votes = signals.count('sell')
    
    if buy_votes >= 2:
        return {'signal': 'buy', 'confidence': 0.8}
    elif sell_votes >= 2:
        return {'signal': 'sell', 'confidence': 0.8}
    else:
        return {'signal': 'hold', 'confidence': 0.4}
"""
        
        # Generate test data of different sizes
        self.small_data = self._generate_market_data(100)
        self.medium_data = self._generate_market_data(1000)
        self.large_data = self._generate_market_data(10000)
    
    def _generate_market_data(self, size: int) -> Dict[str, List[float]]:
        """Generate market data of specified size"""
        import random
        
        base_price = 100.0
        prices = [base_price]
        
        for i in range(1, size):
            change = random.uniform(-0.02, 0.02)  # ±2% change
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)
        
        return {
            'close': prices,
            'high': [p * 1.01 for p in prices],
            'low': [p * 0.99 for p in prices],
            'volume': [10000 + random.randint(-1000, 1000) for _ in range(size)]
        }
    
    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB"""
        if PSUTIL_AVAILABLE:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        else:
            # Fallback to basic memory tracking
            return 0.0
    
    @pytest.mark.performance
    @pytest.mark.skipif(not PSUTIL_AVAILABLE, reason="psutil not available")
    def test_memory_usage_simple_strategy(self):
        """Test memory usage with simple strategy"""
        gc.collect()
        baseline_memory = self._get_memory_usage()
        
        # Execute strategy multiple times
        memory_samples = [baseline_memory]
        
        for i in range(100):
            result = self.executor.execute_strategy(self.simple_strategy, self.small_data, {})
            assert 'signal' in result
            
            if i % 10 == 0:  # Sample memory every 10 executions
                memory_samples.append(self._get_memory_usage())
        
        gc.collect()
        final_memory = self._get_memory_usage()
        memory_growth = final_memory - baseline_memory
        max_memory = max(memory_samples)
        peak_growth = max_memory - baseline_memory
        
        print(f"\n=== Simple Strategy Memory Usage ===")
        print(f"Baseline: {baseline_memory:.2f}MB")
        print(f"Final: {final_memory:.2f}MB")
        print(f"Growth: {memory_growth:.2f}MB")
        print(f"Peak: {max_memory:.2f}MB")
        print(f"Peak growth: {peak_growth:.2f}MB")
        
        # Memory assertions
        assert memory_growth < 5.0, f"Memory grew by {memory_growth:.2f}MB, should be < 5MB"
        assert peak_growth < 10.0, f"Peak memory growth {peak_growth:.2f}MB, should be < 10MB"
    
    @pytest.mark.performance
    @pytest.mark.skipif(not PSUTIL_AVAILABLE, reason="psutil not available")
    def test_memory_usage_complex_strategy(self):
        """Test memory usage with complex strategy"""
        gc.collect()
        baseline_memory = self._get_memory_usage()
        
        # Execute complex strategy multiple times
        memory_samples = [baseline_memory]
        
        for i in range(50):  # Fewer iterations for complex strategy
            result = self.executor.execute_strategy(self.complex_strategy, self.medium_data, {})
            assert 'signal' in result
            
            if i % 5 == 0:  # Sample memory every 5 executions
                memory_samples.append(self._get_memory_usage())
        
        gc.collect()
        final_memory = self._get_memory_usage()
        memory_growth = final_memory - baseline_memory
        max_memory = max(memory_samples)
        peak_growth = max_memory - baseline_memory
        
        print(f"\n=== Complex Strategy Memory Usage ===")
        print(f"Baseline: {baseline_memory:.2f}MB")
        print(f"Final: {final_memory:.2f}MB")
        print(f"Growth: {memory_growth:.2f}MB")
        print(f"Peak: {max_memory:.2f}MB")
        print(f"Peak growth: {peak_growth:.2f}MB")
        
        # Complex strategy can use more memory but should still be reasonable
        assert memory_growth < 15.0, f"Memory grew by {memory_growth:.2f}MB, should be < 15MB"
        assert peak_growth < 25.0, f"Peak memory growth {peak_growth:.2f}MB, should be < 25MB"
    
    @pytest.mark.performance
    def test_memory_usage_with_tracemalloc(self):
        """Test memory usage using Python's tracemalloc"""
        tracemalloc.start()
        
        # Baseline snapshot
        baseline_snapshot = tracemalloc.take_snapshot()
        
        # Execute strategies
        for i in range(50):
            result = self.executor.execute_strategy(self.simple_strategy, self.small_data, {})
            assert 'signal' in result
        
        # Take snapshot after executions
        current_snapshot = tracemalloc.take_snapshot()
        
        # Compare snapshots
        top_stats = current_snapshot.compare_to(baseline_snapshot, 'lineno')
        
        total_memory_diff = sum(stat.size_diff for stat in top_stats)
        total_memory_diff_mb = total_memory_diff / 1024 / 1024
        
        print(f"\n=== Tracemalloc Memory Analysis ===")
        print(f"Total memory difference: {total_memory_diff_mb:.2f}MB")
        print(f"Top 5 memory differences:")
        
        for i, stat in enumerate(top_stats[:5]):
            print(f"  {i+1}. {stat.traceback.format()[-1].strip()}: {stat.size_diff / 1024:.2f}KB")
        
        tracemalloc.stop()
        
        # Memory difference should be minimal
        assert total_memory_diff_mb < 10.0, f"Memory difference {total_memory_diff_mb:.2f}MB too high"
    
    @pytest.mark.performance
    @pytest.mark.skipif(not PSUTIL_AVAILABLE, reason="psutil not available")
    def test_memory_scaling_with_data_size(self):
        """Test how memory usage scales with data size"""
        data_sizes = [
            ('small', self.small_data),
            ('medium', self.medium_data),
            ('large', self.large_data)
        ]
        
        memory_usage_by_size = {}
        
        for size_name, data in data_sizes:
            gc.collect()
            baseline_memory = self._get_memory_usage()
            
            # Execute strategy with this data size
            for _ in range(10):
                result = self.executor.execute_strategy(self.simple_strategy, data, {})
                assert 'signal' in result
            
            gc.collect()
            final_memory = self._get_memory_usage()
            memory_growth = final_memory - baseline_memory
            
            memory_usage_by_size[size_name] = {
                'data_size': len(data['close']),
                'memory_growth': memory_growth,
                'memory_per_datapoint': memory_growth / len(data['close']) * 1024  # KB per datapoint
            }
        
        print(f"\n=== Memory Scaling with Data Size ===")
        print(f"{'Size':<8} {'Data Points':<12} {'Memory Growth':<15} {'KB/Datapoint':<12}")
        print("-" * 55)
        
        for size_name, metrics in memory_usage_by_size.items():
            print(f"{size_name:<8} {metrics['data_size']:<12} {metrics['memory_growth']:<15.2f} "
                  f"{metrics['memory_per_datapoint']:<12.4f}")
        
        # Memory should scale reasonably with data size
        small_growth = memory_usage_by_size['small']['memory_growth']
        large_growth = memory_usage_by_size['large']['memory_growth']
        
        # Large dataset shouldn't use more than 10x memory of small dataset
        scaling_factor = large_growth / small_growth if small_growth > 0 else 1
        assert scaling_factor < 10.0, f"Memory scales poorly: {scaling_factor:.2f}x for 100x data"
    
    @pytest.mark.performance
    @pytest.mark.skipif(not PSUTIL_AVAILABLE, reason="psutil not available")
    def test_memory_leak_detection(self):
        """Test for memory leaks during repeated executions"""
        gc.collect()
        baseline_memory = self._get_memory_usage()
        
        memory_samples = []
        
        # Run many iterations to detect leaks
        for i in range(200):
            result = self.executor.execute_strategy(self.simple_strategy, self.small_data, {})
            assert 'signal' in result
            
            # Sample memory every 20 iterations
            if i % 20 == 0:
                gc.collect()  # Force garbage collection
                current_memory = self._get_memory_usage()
                memory_samples.append(current_memory)
        
        # Analyze memory trend
        memory_diffs = [memory_samples[i] - memory_samples[i-1] for i in range(1, len(memory_samples))]
        avg_memory_increase = sum(memory_diffs) / len(memory_diffs)
        total_memory_increase = memory_samples[-1] - memory_samples[0]
        
        print(f"\n=== Memory Leak Detection ===")
        print(f"Baseline: {baseline_memory:.2f}MB")
        print(f"Final: {memory_samples[-1]:.2f}MB")
        print(f"Total increase: {total_memory_increase:.2f}MB")
        print(f"Average increase per sample: {avg_memory_increase:.4f}MB")
        print(f"Memory samples: {[f'{m:.2f}' for m in memory_samples]}")
        
        # Memory leak detection
        assert total_memory_increase < 10.0, f"Potential memory leak: {total_memory_increase:.2f}MB increase"
        assert avg_memory_increase < 0.5, f"Consistent memory growth: {avg_memory_increase:.4f}MB per sample"
    
    @pytest.mark.performance
    def test_garbage_collection_efficiency(self):
        """Test garbage collection efficiency"""
        import gc
        
        # Disable automatic garbage collection
        gc.disable()
        
        try:
            # Create objects that should be garbage collected
            for i in range(100):
                result = self.executor.execute_strategy(self.complex_strategy, self.medium_data, {})
                assert 'signal' in result
                
                # Create some temporary objects
                temp_data = [x * 2 for x in range(1000)]
                temp_dict = {f'key_{j}': j for j in range(100)}
                del temp_data, temp_dict
            
            # Check garbage collection stats before manual collection
            gc_stats_before = gc.get_stats()
            objects_before = len(gc.get_objects())
            
            # Manual garbage collection
            collected = gc.collect()
            
            # Check stats after collection
            gc_stats_after = gc.get_stats()
            objects_after = len(gc.get_objects())
            
            print(f"\n=== Garbage Collection Efficiency ===")
            print(f"Objects before GC: {objects_before}")
            print(f"Objects after GC: {objects_after}")
            print(f"Objects collected: {collected}")
            print(f"Objects reduced: {objects_before - objects_after}")
            
            # GC should be effective
            assert collected > 0, "Garbage collection should collect some objects"
            assert objects_after < objects_before, "Object count should decrease after GC"
            
        finally:
            # Re-enable automatic garbage collection
            gc.enable()
    
    @pytest.mark.performance
    @pytest.mark.skipif(not PSUTIL_AVAILABLE, reason="psutil not available")
    def test_memory_usage_verification(self):
        """Test memory usage during strategy verification"""
        gc.collect()
        baseline_memory = self._get_memory_usage()
        
        memory_samples = []
        
        # Verify multiple strategies
        strategies = [self.simple_strategy, self.complex_strategy]
        
        for i in range(20):
            strategy = strategies[i % len(strategies)]
            result = self.verifier.verify_strategy(strategy)
            assert 'is_valid' in result
            
            if i % 5 == 0:
                memory_samples.append(self._get_memory_usage())
        
        gc.collect()
        final_memory = self._get_memory_usage()
        memory_growth = final_memory - baseline_memory
        max_memory = max(memory_samples) if memory_samples else final_memory
        peak_growth = max_memory - baseline_memory
        
        print(f"\n=== Verification Memory Usage ===")
        print(f"Baseline: {baseline_memory:.2f}MB")
        print(f"Final: {final_memory:.2f}MB")
        print(f"Growth: {memory_growth:.2f}MB")
        print(f"Peak: {max_memory:.2f}MB")
        print(f"Peak growth: {peak_growth:.2f}MB")
        
        # Verification should not use excessive memory
        assert memory_growth < 20.0, f"Verification memory growth {memory_growth:.2f}MB too high"
        assert peak_growth < 30.0, f"Peak verification memory {peak_growth:.2f}MB too high"
    
    @pytest.mark.performance
    def test_memory_efficiency_patterns(self):
        """Test memory efficiency of different strategy patterns"""
        patterns = {
            'simple_momentum': """
def trading_strategy(data, params):
    if len(data['close']) < 2:
        return {'signal': 'hold', 'confidence': 0.5}
    return {'signal': 'buy' if data['close'][-1] > data['close'][-2] else 'sell', 'confidence': 0.7}
""",
            'moving_average': """
def trading_strategy(data, params):
    if len(data['close']) < 10:
        return {'signal': 'hold', 'confidence': 0.5}
    sma = sum(data['close'][-10:]) / 10
    return {'signal': 'buy' if data['close'][-1] > sma else 'sell', 'confidence': 0.8}
""",
            'multiple_indicators': self.complex_strategy
        }
        
        memory_usage_by_pattern = {}
        
        for pattern_name, strategy_code in patterns.items():
            tracemalloc.start()
            
            # Execute strategy multiple times
            for _ in range(20):
                result = self.executor.execute_strategy(strategy_code, self.medium_data, {})
                assert 'signal' in result
            
            # Get memory statistics
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc.stop()
            
            memory_usage_by_pattern[pattern_name] = {
                'current_mb': current / 1024 / 1024,
                'peak_mb': peak / 1024 / 1024
            }
        
        print(f"\n=== Memory Efficiency by Pattern ===")
        print(f"{'Pattern':<20} {'Current (MB)':<15} {'Peak (MB)':<15}")
        print("-" * 50)
        
        for pattern, usage in memory_usage_by_pattern.items():
            print(f"{pattern:<20} {usage['current_mb']:<15.2f} {usage['peak_mb']:<15.2f}")
        
        # All patterns should use reasonable memory
        for pattern, usage in memory_usage_by_pattern.items():
            assert usage['peak_mb'] < 50.0, f"Pattern {pattern} uses too much memory: {usage['peak_mb']:.2f}MB"