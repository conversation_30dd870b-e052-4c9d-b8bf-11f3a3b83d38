"""
MT5 Account CRUD operations
"""

import uuid
import json
from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update, delete

from ..models import MT5Account

# For synchronous operations - used by the MT5 bridge
_mt5_accounts_cache = {}

async def get_mt5_account_by_id(db: AsyncSession, account_id: uuid.UUID) -> Optional[MT5Account]:
    """Get MT5 account by ID"""
    result = await db.execute(select(MT5Account).where(MT5Account.id == account_id))
    return result.scalars().first()

async def get_mt5_accounts_by_user(db: AsyncSession, user_id: uuid.UUID) -> List[MT5Account]:
    """Get all MT5 accounts for a user"""
    result = await db.execute(select(MT5Account).where(MT5Account.user_id == user_id))
    return result.scalars().all()

def encrypt_password(password: str) -> str:
    # Use a real encryption method in production!
    return "ENC:" + password[::-1]

async def create_mt5_account(
    db: AsyncSession, 
    user_id: uuid.UUID, 
    name: str, 
    server: str, 
    login: str, 
    password: str, 
    is_demo: bool = True
) -> MT5Account:
    """Create a new MT5 account"""
    encrypted_pw = encrypt_password(password)
    db_account = MT5Account(
        user_id=user_id,
        name=name,
        server=server,
        login=login,
        password=encrypted_pw,
        is_demo=is_demo,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    db.add(db_account)
    await db.commit()
    await db.refresh(db_account)
    return db_account

async def update_mt5_account(
    db: AsyncSession, 
    account_id: uuid.UUID, 
    account_data: Dict[str, Any]
) -> Optional[MT5Account]:
    """Update MT5 account"""
    # Add updated_at timestamp
    account_data["updated_at"] = datetime.utcnow()
    
    # Update the account
    await db.execute(
        update(MT5Account)
        .where(MT5Account.id == account_id)
        .values(**account_data)
    )
    await db.commit()
    
    # Return the updated account
    return await get_mt5_account_by_id(db, account_id)

async def delete_mt5_account(db: AsyncSession, account_id: uuid.UUID) -> bool:
    """Delete MT5 account"""
    result = await db.execute(delete(MT5Account).where(MT5Account.id == account_id))
    await db.commit()
    return result.rowcount > 0

async def update_last_connected(db: AsyncSession, account_id: uuid.UUID) -> Optional[MT5Account]:
    """Update last connected timestamp"""
    account = await get_mt5_account_by_id(db, account_id)
    if not account:
        return None
    
    account.last_connected_at = datetime.utcnow()
    await db.commit()
    await db.refresh(account)
    return account

# Synchronous functions for MT5 bridge

def create_or_update_mt5_account(login: str, server: str, account_info: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create or update an MT5 account in the cache (synchronous version)
    This is used by the MT5 bridge for quick access
    """
    key = f"{login}:{server}"
    
    # Store in cache with timestamp
    account_data = {
        "login": login,
        "server": server,
        "account_info": account_info,
        "last_updated": datetime.utcnow().isoformat()
    }
    
    _mt5_accounts_cache[key] = account_data
    
    # Save to file as backup
    try:
        import os
        cache_dir = os.path.join(os.path.dirname(__file__), "../../../storage/mt5_cache")
        os.makedirs(cache_dir, exist_ok=True)
        
        with open(os.path.join(cache_dir, f"{key}.json"), "w") as f:
            json.dump(account_data, f)
    except Exception as e:
        print(f"Failed to save MT5 account to file: {str(e)}")
    
    return account_data

def get_mt5_account(login: str, server: str) -> Optional[Dict[str, Any]]:
    """
    Get an MT5 account from the cache (synchronous version)
    """
    key = f"{login}:{server}"
    
    # Try cache first
    if key in _mt5_accounts_cache:
        return _mt5_accounts_cache[key]
    
    # Try file backup
    try:
        import os
        cache_file = os.path.join(
            os.path.dirname(__file__), 
            "../../../storage/mt5_cache", 
            f"{key}.json"
        )
        
        if os.path.exists(cache_file):
            with open(cache_file, "r") as f:
                account_data = json.load(f)
                # Refresh cache
                _mt5_accounts_cache[key] = account_data
                return account_data
    except Exception as e:
        print(f"Failed to load MT5 account from file: {str(e)}")
    
    return None