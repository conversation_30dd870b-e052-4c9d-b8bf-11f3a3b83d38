import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { TradingBridgeService, TradingBridgeServiceDependencies } from './trading-bridge.service';
import { PythonEngineService } from './python-engine.service';
import { OrderRequest, AccountInfo, OrderResult } from '../../../../shared/schemas';

// Mock logger implementation
const createMockLogger = () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
});

describe('TradingBridgeService', () => {
  let service: TradingBridgeService;
  let mockDependencies: TradingBridgeServiceDependencies;
  let mockPythonEngineService: jest.Mocked<PythonEngineService>;

  beforeEach(() => {
    // Mock PythonEngineService
    mockPythonEngineService = {
      sendTradingCommand: jest.fn(),
      checkHealth: jest.fn(),
      getHealthStatus: jest.fn(),
      on: jest.fn(),
      emit: jest.fn(),
      stop: jest.fn(),
    } as any;

    mockDependencies = {
      pythonEngineService: mockPythonEngineService,
      logger: createMockLogger(),
    };

    service = new TradingBridgeService(mockDependencies);
  });

  afterEach(async () => {
    await service.stop();
    jest.clearAllMocks();
  });

  describe('getAccountInfo', () => {
    it('should retrieve account info successfully', async () => {
      const mockAccountInfo: AccountInfo = {
        balance: 10000.50,
        equity: 10050.25,
        margin: 500.00,
        currency: 'USD',
      };

      mockPythonEngineService.sendTradingCommand.mockResolvedValue({
        success: true,
        data: {
          success: true,
          data: mockAccountInfo,
          timestamp: new Date(),
          request_id: 'test-request-id',
        },
      });

      const result = await service.getAccountInfo();

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockAccountInfo);
      expect(mockPythonEngineService.sendTradingCommand).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'get_account',
          timestamp: expect.any(Date),
          request_id: expect.any(String),
        })
      );
    });

    it('should handle account info retrieval errors', async () => {
      mockPythonEngineService.sendTradingCommand.mockResolvedValue({
        success: false,
        error: {
          code: 'TRADING_ENGINE_ERROR',
          message: 'Engine unavailable',
        },
      });

      const result = await service.getAccountInfo();

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('TRADING_ENGINE_ERROR');
      expect(result.error?.message).toBe('Engine unavailable');
    });

    it('should handle invalid account data response', async () => {
      mockPythonEngineService.sendTradingCommand.mockResolvedValue({
        success: true,
        data: {
          success: false,
          error: 'Invalid account configuration',
          timestamp: new Date(),
          request_id: 'test-request-id',
        },
      });

      const result = await service.getAccountInfo();

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('ACCOUNT_INFO_ERROR');
      expect(result.error?.message).toBe('Invalid account configuration');
    });
  });

  describe('submitOrder', () => {
    const validOrderRequest: OrderRequest = {
      symbol: 'EURUSD',
      volume: 0.01,
      order_type: 'buy',
      price: 1.1000,
      stop_loss: 1.0950,
      take_profit: 1.1050,
    };

    it('should submit order successfully', async () => {
      const mockOrderResult: OrderResult = {
        success: true,
        order_id: 12345,
      };

      mockPythonEngineService.sendTradingCommand.mockResolvedValue({
        success: true,
        data: {
          success: true,
          data: mockOrderResult,
          timestamp: new Date(),
          request_id: 'test-request-id',
        },
      });

      const result = await service.submitOrder(validOrderRequest);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockOrderResult);
      expect(mockPythonEngineService.sendTradingCommand).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'submit_order',
          payload: validOrderRequest,
          timestamp: expect.any(Date),
          request_id: expect.any(String),
        })
      );
    });

    it('should validate order before submission', async () => {
      const invalidOrder: OrderRequest = {
        symbol: 'EURUSD',
        volume: -0.01, // Invalid negative volume
        order_type: 'buy',
        price: 1.1000,
      };

      const result = await service.submitOrder(invalidOrder);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('INVALID_ORDER');
      expect(result.error?.details).toContain('Volume must be positive');
      expect(mockPythonEngineService.sendTradingCommand).not.toHaveBeenCalled();
    });

    it('should validate stop loss for buy orders', async () => {
      const invalidOrder: OrderRequest = {
        symbol: 'EURUSD',
        volume: 0.01,
        order_type: 'buy',
        price: 1.1000,
        stop_loss: 1.1050, // Stop loss above entry price for buy order
      };

      const result = await service.submitOrder(invalidOrder);

      expect(result.success).toBe(false);
      expect(result.error?.details).toContain('Stop loss must be below entry price for buy orders');
    });

    it('should validate take profit for sell orders', async () => {
      const invalidOrder: OrderRequest = {
        symbol: 'EURUSD',
        volume: 0.01,
        order_type: 'sell',
        price: 1.1000,
        take_profit: 1.1050, // Take profit above entry price for sell order
      };

      const result = await service.submitOrder(invalidOrder);

      expect(result.success).toBe(false);
      expect(result.error?.details).toContain('Take profit must be below entry price for sell orders');
    });

    it('should handle order rejection from trading engine', async () => {
      mockPythonEngineService.sendTradingCommand.mockResolvedValue({
        success: true,
        data: {
          success: false,
          error: 'Insufficient margin',
          timestamp: new Date(),
          request_id: 'test-request-id',
        },
      });

      const result = await service.submitOrder(validOrderRequest);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('ORDER_REJECTED');
      expect(result.error?.message).toBe('Insufficient margin');
    });

    it('should emit event on successful order submission', async () => {
      const mockOrderResult: OrderResult = {
        success: true,
        order_id: 12345,
      };

      mockPythonEngineService.sendTradingCommand.mockResolvedValue({
        success: true,
        data: {
          success: true,
          data: mockOrderResult,
          timestamp: new Date(),
          request_id: 'test-request-id',
        },
      });

      const eventListener = jest.fn();
      service.on('order_submitted', eventListener);

      await service.submitOrder(validOrderRequest);

      expect(eventListener).toHaveBeenCalledWith(
        expect.objectContaining({
          orderRequest: validOrderRequest,
          orderResult: mockOrderResult,
          requestId: expect.any(String),
        })
      );
    });
  });

  describe('closeOrder', () => {
    it('should close order successfully', async () => {
      const orderId = 12345;
      const mockOrderResult: OrderResult = {
        success: true,
        order_id: orderId,
      };

      mockPythonEngineService.sendTradingCommand.mockResolvedValue({
        success: true,
        data: {
          success: true,
          data: mockOrderResult,
          timestamp: new Date(),
          request_id: 'test-request-id',
        },
      });

      const result = await service.closeOrder(orderId);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockOrderResult);
      expect(mockPythonEngineService.sendTradingCommand).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'close_order',
          payload: { order_id: orderId },
          timestamp: expect.any(Date),
          request_id: expect.any(String),
        })
      );
    });

    it('should handle order close rejection', async () => {
      mockPythonEngineService.sendTradingCommand.mockResolvedValue({
        success: true,
        data: {
          success: false,
          error: 'Order not found',
          timestamp: new Date(),
          request_id: 'test-request-id',
        },
      });

      const result = await service.closeOrder(12345);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('ORDER_CLOSE_REJECTED');
      expect(result.error?.message).toBe('Order not found');
    });

    it('should emit event on successful order close', async () => {
      const orderId = 12345;
      const mockOrderResult: OrderResult = {
        success: true,
        order_id: orderId,
      };

      mockPythonEngineService.sendTradingCommand.mockResolvedValue({
        success: true,
        data: {
          success: true,
          data: mockOrderResult,
          timestamp: new Date(),
          request_id: 'test-request-id',
        },
      });

      const eventListener = jest.fn();
      service.on('order_closed', eventListener);

      await service.closeOrder(orderId);

      expect(eventListener).toHaveBeenCalledWith(
        expect.objectContaining({
          orderId,
          result: mockOrderResult,
          requestId: expect.any(String),
        })
      );
    });
  });

  describe('getPositions', () => {
    it('should retrieve positions successfully', async () => {
      const mockPositions = [
        {
          position_id: 1,
          symbol: 'EURUSD',
          volume: 0.01,
          open_price: 1.1000,
          current_price: 1.1050,
          pnl: 5.0,
          order_type: 'buy',
          open_time: new Date(),
        },
      ];

      mockPythonEngineService.sendTradingCommand.mockResolvedValue({
        success: true,
        data: {
          success: true,
          data: mockPositions,
          timestamp: new Date(),
          request_id: 'test-request-id',
        },
      });

      const result = await service.getPositions();

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockPositions);
      expect(mockPythonEngineService.sendTradingCommand).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'get_positions',
          timestamp: expect.any(Date),
          request_id: expect.any(String),
        })
      );
    });

    it('should handle empty positions response', async () => {
      mockPythonEngineService.sendTradingCommand.mockResolvedValue({
        success: true,
        data: {
          success: true,
          data: [],
          timestamp: new Date(),
          request_id: 'test-request-id',
        },
      });

      const result = await service.getPositions();

      expect(result.success).toBe(true);
      expect(result.data).toEqual([]);
    });
  });

  describe('isEngineHealthy', () => {
    it('should return health status', async () => {
      mockPythonEngineService.getHealthStatus.mockReturnValue({
        healthy: true,
        lastCheck: new Date(),
      });

      const isHealthy = await service.isEngineHealthy();

      expect(isHealthy).toBe(true);
      expect(mockPythonEngineService.getHealthStatus).toHaveBeenCalled();
    });
  });

  describe('getEngineHealth', () => {
    it('should return detailed health information', async () => {
      const mockHealthData = {
        status: 'healthy',
        version: '1.0.0',
        uptime: 3600,
      };

      const mockHealthStatus = {
        healthy: true,
        lastCheck: new Date(),
      };

      mockPythonEngineService.checkHealth.mockResolvedValue({
        success: true,
        data: mockHealthData,
      });

      mockPythonEngineService.getHealthStatus.mockReturnValue(mockHealthStatus);

      const result = await service.getEngineHealth();

      expect(result.success).toBe(true);
      expect(result.data?.healthy).toBe(true);
      expect(result.data?.lastCheck).toEqual(mockHealthStatus.lastCheck);
    });
  });

  describe('calculatePositionSize', () => {
    it('should calculate position size correctly', () => {
      const accountBalance = 10000;
      const riskPercent = 2; // 2%
      const entryPrice = 1.1000;
      const stopLoss = 1.0950;

      const positionSize = service.calculatePositionSize(
        accountBalance,
        riskPercent,
        entryPrice,
        stopLoss
      );

      expect(positionSize).toBeGreaterThan(0);
      expect(positionSize).toBeLessThanOrEqual(100); // Max position size
    });

    it('should return 0 for invalid risk parameters', () => {
      const positionSize = service.calculatePositionSize(10000, 2, 1.1000, 1.1000);
      expect(positionSize).toBe(0);
    });

    it('should enforce minimum position size', () => {
      const positionSize = service.calculatePositionSize(10, 1, 1.1000, 1.0999);
      expect(positionSize).toBeGreaterThanOrEqual(0.01);
    });
  });

  describe('Order Validation', () => {
    it('should validate maximum volume', async () => {
      const invalidOrder: OrderRequest = {
        symbol: 'EURUSD',
        volume: 150, // Exceeds maximum
        order_type: 'buy',
        price: 1.1000,
      };

      const result = await service.submitOrder(invalidOrder);

      expect(result.success).toBe(false);
      expect(result.error?.details).toContain('Volume exceeds maximum allowed');
    });

    it('should validate positive price', async () => {
      const invalidOrder: OrderRequest = {
        symbol: 'EURUSD',
        volume: 0.01,
        order_type: 'buy',
        price: 0, // Invalid zero price
      };

      const result = await service.submitOrder(invalidOrder);

      expect(result.success).toBe(false);
      expect(result.error?.details).toContain('Price must be positive');
    });
  });
});