import { useState } from 'react';
import { TrendingUp, TrendingDown, DollarSign, BarChart3, Calendar, Target } from 'lucide-react';
import { EquityCurve } from './EquityCurve';
import { TradesList } from './TradesList';

interface BacktestResults {
  id: string;
  name: string;
  symbol: string;
  startDate: string;
  endDate: string;
  initialBalance: number;
  finalBalance: number;
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  winRate: number;
  totalReturn: number;
  maxDrawdown: number;
  sharpeRatio: number;
  profitFactor: number;
  equityCurve: Array<{ date: string; value: number }>;
  trades: Array<{
    id: string;
    symbol: string;
    side: 'buy' | 'sell';
    entryPrice: number;
    exitPrice: number;
    quantity: number;
    entryTime: string;
    exitTime: string;
    pnl: number;
    pnlPercent: number;
  }>;
  status: 'running' | 'completed' | 'failed';
  createdAt: string;
}

interface BacktestResultsProps {
  results: BacktestResults;
  onRestart?: () => void;
}

export function BacktestResults({ results, onRestart }: BacktestResultsProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'trades' | 'chart'>('overview');

  const StatCard = ({ 
    title, 
    value, 
    change, 
    icon: Icon, 
    color = 'text-gray-600',
    changeColor
  }: any) => (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <Icon className={`w-8 h-8 ${color}`} />
        </div>
        <div className="ml-3 flex-1">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-semibold text-gray-900">{value}</p>
          {change !== undefined && (
            <div className="flex items-center mt-1">
              {change >= 0 ? (
                <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
              ) : (
                <TrendingDown className="w-4 h-4 text-red-500 mr-1" />
              )}
              <span className={`text-sm ${changeColor || (change >= 0 ? 'text-green-600' : 'text-red-600')}`}>
                {change >= 0 ? '+' : ''}{change.toFixed(2)}%
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  const tabs = [
    { id: 'overview', label: 'Overview', icon: BarChart3 },
    { id: 'chart', label: 'Equity Curve', icon: TrendingUp },
    { id: 'trades', label: 'Trades', icon: Target },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">{results.name}</h2>
            <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600">
              <span className="flex items-center">
                <Calendar className="w-4 h-4 mr-1" />
                {new Date(results.startDate).toLocaleDateString()} - {new Date(results.endDate).toLocaleDateString()}
              </span>
              <span>{results.symbol}</span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                results.status === 'completed' ? 'bg-green-100 text-green-800' :
                results.status === 'running' ? 'bg-blue-100 text-blue-800' :
                'bg-red-100 text-red-800'
              }`}>
                {results.status.charAt(0).toUpperCase() + results.status.slice(1)}
              </span>
            </div>
          </div>
          
          {onRestart && (
            <button
              onClick={onRestart}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Run Again
            </button>
          )}
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Return"
          value={`${results.totalReturn >= 0 ? '+' : ''}${results.totalReturn.toFixed(2)}%`}
          change={results.totalReturn}
          icon={DollarSign}
          color="text-green-600"
        />

        <StatCard
          title="Win Rate"
          value={`${results.winRate.toFixed(1)}%`}
          icon={Target}
          color="text-blue-600"
        />

        <StatCard
          title="Max Drawdown"
          value={`${results.maxDrawdown.toFixed(2)}%`}
          icon={TrendingDown}
          color="text-red-600"
        />

        <StatCard
          title="Sharpe Ratio"
          value={`${results.sharpeRatio.toFixed(2)}`}
          icon={BarChart3}
          color="text-purple-600"
        />
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`
                  flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm
                  ${activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                <tab.icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Summary Stats */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Summary</h3>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Initial Balance:</span>
                    <span className="font-medium">${results.initialBalance.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Final Balance:</span>
                    <span className="font-medium">${results.finalBalance.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Trades:</span>
                    <span className="font-medium">{results.totalTrades}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Winning Trades:</span>
                    <span className="font-medium text-green-600">{results.winningTrades}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Losing Trades:</span>
                    <span className="font-medium text-red-600">{results.losingTrades}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Profit Factor:</span>
                    <span className="font-medium">{results.profitFactor.toFixed(2)}</span>
                  </div>
                </div>
              </div>

              {/* Performance Chart Mini */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Performance</h3>
                <div className="h-48">
                  <EquityCurve 
                    data={results.equityCurve.map(point => ({
                      timestamp: point.date,
                      equity: point.value
                    }))} 
                    initialBalance={results.initialBalance}
                    compact={true}
                  />
                </div>
              </div>
            </div>
          )}

          {activeTab === 'chart' && (
            <div className="h-96">
              <EquityCurve 
                data={results.equityCurve.map(point => ({
                  timestamp: point.date,
                  equity: point.value
                }))} 
                initialBalance={results.initialBalance}
              />
            </div>
          )}

          {activeTab === 'trades' && (
            <TradesList trades={results.trades} />
          )}
        </div>
      </div>
    </div>
  );
}