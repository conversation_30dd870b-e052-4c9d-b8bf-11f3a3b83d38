import { Router, Request, Response } from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { DarwinBridgeService } from '../services/bridge/darwin-bridge.service';
import { PythonEngineService } from '../services/bridge/python-engine.service';
import { Logger } from '@/shared/types';
import {
  DarwinEvolutionRequestSchema,
  DarwinEvolutionRequest,
} from '@ai-trading/shared';

export function createDarwinRoutes(logger: Logger): Router {
  const router = Router();

  // Initialize services
  const pythonEngineService = new PythonEngineService({
    logger,
    config: {
      baseUrl: process.env.PYTHON_ENGINE_URL || 'http://localhost:8000',
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
    }
  });

  const darwinBridgeService = new DarwinBridgeService({
    pythonEngineService,
    logger,
  });

  // Set up event listeners for real-time updates
  darwinBridgeService.on('evolutionStarted', (data) => {
    logger.info('Darwin evolution started', data);
  });

  darwinBridgeService.on('evolutionCompleted', (data) => {
    logger.info('Darwin evolution completed', data);
  });

  darwinBridgeService.on('evolutionFailed', (data) => {
    logger.error('Darwin evolution failed', data);
  });

  /**
   * POST /api/darwin/evolve
   * Start a new Darwin evolution process
   */
  router.post('/evolve', [
    body('pair')
      .isString()
      .notEmpty()
      .withMessage('Trading pair is required'),
    body('timeframe')
      .isString()
      .notEmpty()
      .withMessage('Timeframe is required'),
    body('evolution_params.population_size')
      .optional()
      .isInt({ min: 10, max: 200 })
      .withMessage('Population size must be between 10 and 200'),
    body('evolution_params.max_generations')
      .optional()
      .isInt({ min: 5, max: 100 })
      .withMessage('Max generations must be between 5 and 100'),
    body('evolution_params.mutation_rate')
      .optional()
      .isFloat({ min: 0, max: 1 })
      .withMessage('Mutation rate must be between 0 and 1'),
  ], async (req: Request, res: Response) => {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
        });
      }

      // Validate request with Zod schema
      const zodValidation = DarwinEvolutionRequestSchema.safeParse(req.body);
      if (!zodValidation.success) {
        return res.status(400).json({
          success: false,
          error: 'Invalid request format',
          details: zodValidation.error.errors,
        });
      }

      const request: DarwinEvolutionRequest = zodValidation.data;

      // Start evolution
      const result = await darwinBridgeService.startEvolution(request);

      if (result.success) {
        return res.status(201).json(result);
      } else {
        return res.status(500).json(result);
      }

    } catch (error) {
      logger.error('Error starting Darwin evolution', { error: error instanceof Error ? error.message : String(error) });
      return res.status(500).json({
        success: false,
        error: 'Internal server error',
      });
    }
  });

  /**
   * GET /api/darwin/status/:jobId
   * Get the status of a Darwin evolution job
   */
  router.get('/status/:jobId', [
    param('jobId')
      .isUUID()
      .withMessage('Job ID must be a valid UUID'),
  ], async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
        });
      }

      const { jobId } = req.params;
      const result = await darwinBridgeService.getJobStatus(jobId);

      if (result.success) {
        return res.json(result);
      } else {
        return res.status(404).json(result);
      }

    } catch (error) {
      logger.error('Error getting Darwin job status', { error: error instanceof Error ? error.message : String(error) });
      return res.status(500).json({
        success: false,
        error: 'Internal server error',
      });
    }
  });

  /**
   * GET /api/darwin/results/:jobId
   * Get the results of a completed Darwin evolution job
   */
  router.get('/results/:jobId', [
    param('jobId')
      .isUUID()
      .withMessage('Job ID must be a valid UUID'),
  ], async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
        });
      }

      const { jobId } = req.params;
      const result = await darwinBridgeService.getJobResults(jobId);

      if (result.success) {
        return res.json(result);
      } else {
        return res.status(404).json(result);
      }

    } catch (error) {
      logger.error('Error getting Darwin job results', { error: error instanceof Error ? error.message : String(error) });
      return res.status(500).json({
        success: false,
        error: 'Internal server error',
      });
    }
  });

  /**
   * GET /api/darwin/strategies/:jobId
   * Get the best strategies from a completed job
   */
  router.get('/strategies/:jobId', [
    param('jobId')
      .isUUID()
      .withMessage('Job ID must be a valid UUID'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 50 })
      .withMessage('Limit must be between 1 and 50'),
    query('verified_only')
      .optional()
      .isBoolean()
      .withMessage('Verified only must be a boolean'),
  ], async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
        });
      }

      const { jobId } = req.params;
      const limit = parseInt(req.query.limit as string) || 10;
      const verifiedOnly = req.query.verified_only === 'true';

      const result = await darwinBridgeService.getBestStrategies(jobId, limit, verifiedOnly);

      if (result.success) {
        return res.json(result);
      } else {
        return res.status(404).json(result);
      }

    } catch (error) {
      logger.error('Error getting best strategies', { error: error instanceof Error ? error.message : String(error) });
      return res.status(500).json({
        success: false,
        error: 'Internal server error',
      });
    }
  });

  /**
   * GET /api/darwin/genome/:jobId
   * Get the forex genome for a specific job
   */
  router.get('/genome/:jobId', [
    param('jobId')
      .isUUID()
      .withMessage('Job ID must be a valid UUID'),
  ], async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
        });
      }

      const { jobId } = req.params;
      const result = await darwinBridgeService.getForexGenome(jobId);

      if (result.success) {
        return res.json(result);
      } else {
        return res.status(404).json(result);
      }

    } catch (error) {
      logger.error('Error getting forex genome', { error: error instanceof Error ? error.message : String(error) });
      return res.status(500).json({
        success: false,
        error: 'Internal server error',
      });
    }
  });

  /**
   * DELETE /api/darwin/jobs/:jobId
   * Stop a running Darwin evolution job
   */
  router.delete('/jobs/:jobId', [
    param('jobId')
      .isUUID()
      .withMessage('Job ID must be a valid UUID'),
  ], async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
        });
      }

      const { jobId } = req.params;
      const result = await darwinBridgeService.stopEvolution(jobId);

      if (result.success) {
        return res.json(result);
      } else {
        return res.status(404).json(result);
      }

    } catch (error) {
      logger.error('Error stopping Darwin evolution', { error: error instanceof Error ? error.message : String(error) });
      return res.status(500).json({
        success: false,
        error: 'Internal server error',
      });
    }
  });

  /**
   * GET /api/darwin/jobs
   * Get all active Darwin jobs
   */
  router.get('/jobs', async (_req: Request, res: Response) => {
    try {
      const activeJobs = darwinBridgeService.getActiveJobs();
      
      return res.json({
        success: true,
        data: activeJobs,
      });

    } catch (error) {
      logger.error('Error getting active Darwin jobs', { error: error instanceof Error ? error.message : String(error) });
      return res.status(500).json({
        success: false,
        error: 'Internal server error',
      });
    }
  });

  /**
   * POST /api/darwin/cleanup
   * Clean up old completed jobs
   */
  router.post('/cleanup', [
    body('max_age_hours')
      .optional()
      .isInt({ min: 1, max: 168 }) // 1 hour to 1 week
      .withMessage('Max age hours must be between 1 and 168'),
  ], async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
        });
      }

      const maxAgeHours = req.body.max_age_hours || 24;
      darwinBridgeService.cleanupOldJobs(maxAgeHours);

      return res.json({
        success: true,
        data: { message: 'Cleanup completed successfully' },
      });

    } catch (error) {
      logger.error('Error cleaning up Darwin jobs', { error: error instanceof Error ? error.message : String(error) });
      return res.status(500).json({
        success: false,
        error: 'Internal server error',
      });
    }
  });

  return router;
}