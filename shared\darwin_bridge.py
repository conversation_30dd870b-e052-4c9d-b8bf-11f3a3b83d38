#!/usr/bin/env python3
"""
Darwin Bridge - Interface between Node.js backend and Darwin Gödel Machine
Handles communication and job management for the Darwin evolution engine.
"""

import asyncio
import json
import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any, Optional
import uuid

# Add the shared directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from darwin_godel_orchestrator import DarwinGodelMachine
from enhanced_darwin_godel_core import (
    EvolutionParameters, EvolutionStatus, FitnessObjective
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DarwinBridge:
    """
    Bridge service for Darwin Gödel Machine integration
    """
    
    def __init__(self):
        self.active_jobs: Dict[str, DarwinGodelMachine] = {}
        self.job_results: Dict[str, Dict[str, Any]] = {}
        self.job_status: Dict[str, Dict[str, Any]] = {}
        
    async def handle_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle incoming requests from Node.js backend
        """
        try:
            action = request.get('action')
            job_id = request.get('job_id')
            payload = request.get('payload', {})
            request_id = request.get('request_id')
            
            logger.info(f"Handling request: {action} for job {job_id}")
            
            if action == 'start_evolution':
                result = await self.start_evolution(payload)
            elif action == 'get_status':
                result = await self.get_status(job_id)
            elif action == 'get_results':
                result = await self.get_results(job_id)
            elif action == 'stop_evolution':
                result = await self.stop_evolution(job_id)
            elif action == 'get_genome':
                result = await self.get_genome(job_id)
            else:
                result = {
                    'success': False,
                    'error': f'Unknown action: {action}'
                }
            
            # Add metadata to response
            result['timestamp'] = datetime.now().isoformat()
            result['request_id'] = request_id
            
            return result
            
        except Exception as e:
            logger.error(f"Error handling request: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'request_id': request.get('request_id')
            }
    
    async def start_evolution(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Start a new Darwin evolution process
        """
        try:
            job_id = payload['job_id']
            pair = payload['pair']
            timeframe = payload['timeframe']
            evolution_params_data = payload.get('evolution_params', {})
            
            # Create evolution parameters
            evolution_params = EvolutionParameters(
                population_size=evolution_params_data.get('population_size', 50),
                max_generations=evolution_params_data.get('max_generations', 30),
                mutation_rate=evolution_params_data.get('mutation_rate', 0.15),
                crossover_rate=evolution_params_data.get('crossover_rate', 0.8),
                fitness_objective=FitnessObjective(
                    evolution_params_data.get('fitness_objective', 'sharpe_ratio')
                ),
                elitism_rate=evolution_params_data.get('elitism_rate', 0.1),
                tournament_size=evolution_params_data.get('tournament_size', 3),
                max_strategy_complexity=evolution_params_data.get('max_strategy_complexity', 10),
                verification_enabled=evolution_params_data.get('verification_enabled', True)
            )
            
            # Initialize Darwin Gödel Machine
            dgm = DarwinGodelMachine(evolution_params=evolution_params)
            
            # Store the job
            self.active_jobs[job_id] = dgm
            
            # Initialize job status
            self.job_status[job_id] = {
                'job_id': job_id,
                'status': 'initializing',
                'progress': {
                    'current_generation': 0,
                    'total_generations': evolution_params.max_generations,
                    'completion_percentage': 0.0
                },
                'metrics': {
                    'best_fitness': 0.0,
                    'average_fitness': 0.0,
                    'verified_strategies': 0,
                    'total_strategies': 0
                },
                'runtime_info': {
                    'start_time': datetime.now().isoformat(),
                    'elapsed_seconds': 0,
                    'estimated_remaining_seconds': None
                }
            }
            
            # Start evolution in background
            asyncio.create_task(self._run_evolution(job_id, dgm, pair, timeframe))
            
            return {
                'success': True,
                'data': {
                    'job_id': job_id,
                    'status': 'started',
                    'message': 'Evolution process initiated'
                }
            }
            
        except Exception as e:
            logger.error(f"Error starting evolution: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _run_evolution(self, job_id: str, dgm: DarwinGodelMachine, pair: str, timeframe: str):
        """
        Run the evolution process in the background
        """
        try:
            logger.info(f"Starting evolution for job {job_id}: {pair} {timeframe}")
            
            # Update status to running
            self.job_status[job_id]['status'] = 'running'
            
            start_time = datetime.now()
            
            # Run evolution
            async for state in dgm.evolve_strategies(pair, timeframe):
                # Update job status
                elapsed_seconds = (datetime.now() - start_time).total_seconds()
                completion_percentage = (state.generation / dgm.evolution_params.max_generations) * 100
                
                self.job_status[job_id].update({
                    'status': state.status.value,
                    'progress': {
                        'current_generation': state.generation,
                        'total_generations': dgm.evolution_params.max_generations,
                        'completion_percentage': completion_percentage
                    },
                    'metrics': {
                        'best_fitness': state.best_fitness,
                        'average_fitness': state.average_fitness,
                        'verified_strategies': state.verified_count,
                        'total_strategies': state.total_strategies_tested
                    },
                    'runtime_info': {
                        'start_time': start_time.isoformat(),
                        'elapsed_seconds': elapsed_seconds,
                        'estimated_remaining_seconds': self._estimate_remaining_time(
                            elapsed_seconds, completion_percentage
                        )
                    }
                })
                
                logger.info(f"Job {job_id} - Generation {state.generation}: "
                           f"Best fitness: {state.best_fitness:.4f}")
                
                if state.status == EvolutionStatus.COMPLETED:
                    # Store final results
                    best_strategies = dgm.get_best_strategies(20, verified_only=False)
                    forex_genome = dgm.get_forex_genome(pair, timeframe)
                    
                    self.job_results[job_id] = {
                        'job_id': job_id,
                        'evolution_params': {
                            'population_size': dgm.evolution_params.population_size,
                            'max_generations': dgm.evolution_params.max_generations,
                            'mutation_rate': dgm.evolution_params.mutation_rate,
                            'fitness_objective': dgm.evolution_params.fitness_objective.value
                        },
                        'final_state': {
                            'job_id': job_id,
                            'status': state.status.value,
                            'generation': state.generation,
                            'best_fitness': state.best_fitness,
                            'average_fitness': state.average_fitness,
                            'verified_count': state.verified_count,
                            'total_strategies_tested': state.total_strategies_tested,
                            'start_time': start_time.isoformat(),
                            'last_update': datetime.now().isoformat()
                        },
                        'best_strategies': [self._strategy_to_dict(s) for s in best_strategies],
                        'forex_genome': self._genome_to_dict(forex_genome) if forex_genome else None,
                        'performance_summary': {
                            'total_runtime_seconds': elapsed_seconds,
                            'strategies_evolved': state.total_strategies_tested,
                            'verification_success_rate': state.verified_count / max(state.total_strategies_tested, 1),
                            'fitness_improvement': state.best_fitness
                        },
                        'completed_at': datetime.now().isoformat()
                    }
                    
                    logger.info(f"Evolution completed for job {job_id}")
                    break
                    
                elif state.status == EvolutionStatus.FAILED:
                    self.job_status[job_id]['error'] = 'Evolution failed'
                    logger.error(f"Evolution failed for job {job_id}")
                    break
                    
        except Exception as e:
            logger.error(f"Error during evolution for job {job_id}: {str(e)}")
            self.job_status[job_id]['status'] = 'failed'
            self.job_status[job_id]['error'] = str(e)
    
    async def get_status(self, job_id: str) -> Dict[str, Any]:
        """
        Get the status of a Darwin evolution job
        """
        if job_id not in self.job_status:
            return {
                'success': False,
                'error': 'Job not found'
            }
        
        return {
            'success': True,
            'data': self.job_status[job_id]
        }
    
    async def get_results(self, job_id: str) -> Dict[str, Any]:
        """
        Get the results of a completed Darwin evolution job
        """
        if job_id not in self.job_results:
            return {
                'success': False,
                'error': 'Results not found or job not completed'
            }
        
        return {
            'success': True,
            'data': self.job_results[job_id]
        }
    
    async def get_genome(self, job_id: str) -> Dict[str, Any]:
        """
        Get the forex genome for a specific job
        """
        if job_id not in self.job_results:
            return {
                'success': False,
                'error': 'Job results not found'
            }
        
        genome = self.job_results[job_id].get('forex_genome')
        if not genome:
            return {
                'success': False,
                'error': 'Forex genome not available'
            }
        
        return {
            'success': True,
            'data': genome
        }
    
    async def stop_evolution(self, job_id: str) -> Dict[str, Any]:
        """
        Stop a running Darwin evolution job
        """
        if job_id not in self.active_jobs:
            return {
                'success': False,
                'error': 'Job not found'
            }
        
        try:
            # Update status
            self.job_status[job_id]['status'] = 'terminated'
            
            # Remove from active jobs
            del self.active_jobs[job_id]
            
            return {
                'success': True,
                'data': {'message': 'Evolution stopped successfully'}
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _strategy_to_dict(self, strategy) -> Dict[str, Any]:
        """
        Convert a TradingStrategy object to dictionary
        """
        return {
            'id': str(uuid.uuid4()),
            'name': f"Strategy_{strategy.generation}_{strategy.fitness_score:.4f}",
            'conditions': [
                {
                    'indicator': cond.indicator,
                    'operator': cond.operator,
                    'value': cond.value,
                    'timeframe': cond.timeframe
                }
                for cond in strategy.conditions
            ],
            'risk_management': {
                'stop_loss_pips': strategy.risk_management.stop_loss_pips,
                'take_profit_pips': strategy.risk_management.take_profit_pips,
                'position_size_percent': strategy.risk_management.position_size_percent,
                'max_daily_loss_percent': strategy.risk_management.max_daily_loss_percent,
                'max_concurrent_trades': strategy.risk_management.max_concurrent_trades
            },
            'fitness_score': strategy.fitness_score,
            'is_verified': strategy.is_verified,
            'verification_proof': strategy.verification_proof,
            'generation': strategy.generation,
            'created_at': datetime.now().isoformat()
        }
    
    def _genome_to_dict(self, genome) -> Dict[str, Any]:
        """
        Convert a ForexGenome object to dictionary
        """
        return {
            'pair': genome.pair,
            'timeframe': genome.timeframe,
            'behavioral_patterns': genome.behavioral_patterns,
            'volatility_profile': {
                'average_volatility': genome.volatility_profile.average_volatility,
                'volatility_clusters': [
                    {
                        'start_hour': cluster.start_hour,
                        'end_hour': cluster.end_hour,
                        'volatility_multiplier': cluster.volatility_multiplier
                    }
                    for cluster in genome.volatility_profile.volatility_clusters
                ]
            },
            'trend_characteristics': {
                'trend_persistence': genome.trend_characteristics.trend_persistence,
                'reversal_frequency': genome.trend_characteristics.reversal_frequency,
                'support_resistance_strength': genome.trend_characteristics.support_resistance_strength
            },
            'optimal_strategies': [self._strategy_to_dict(s) for s in genome.optimal_strategies],
            'confidence_score': genome.confidence_score,
            'generated_at': datetime.now().isoformat()
        }
    
    def _estimate_remaining_time(self, elapsed_seconds: float, completion_percentage: float) -> Optional[float]:
        """
        Estimate remaining time based on current progress
        """
        if completion_percentage <= 0:
            return None
        
        total_estimated_seconds = elapsed_seconds / (completion_percentage / 100)
        remaining_seconds = total_estimated_seconds - elapsed_seconds
        
        return max(0, remaining_seconds)

async def main():
    """
    Main function to handle Darwin bridge communication
    """
    bridge = DarwinBridge()
    
    logger.info("Darwin Bridge started, waiting for requests...")
    
    # Read from stdin and process requests
    while True:
        try:
            line = await asyncio.get_event_loop().run_in_executor(None, sys.stdin.readline)
            if not line:
                break
                
            request = json.loads(line.strip())
            response = await bridge.handle_request(request)
            
            # Send response to stdout
            print(json.dumps(response), flush=True)
            
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON received: {e}")
            error_response = {
                'success': False,
                'error': 'Invalid JSON format',
                'timestamp': datetime.now().isoformat()
            }
            print(json.dumps(error_response), flush=True)
            
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            error_response = {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
            print(json.dumps(error_response), flush=True)

if __name__ == "__main__":
    asyncio.run(main())