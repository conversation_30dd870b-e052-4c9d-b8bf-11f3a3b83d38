﻿﻿﻿﻿﻿/**
 * DGM Experiments Component
 * Darwin GÃ¶del Machine experiment management and monitoring
 */

import { useState, useEffect } from 'react';
import { 
  Play, 
  Beaker, 
  TrendingUp, 
  AlertCircle, 
  CheckCircle, 
  Clock, 
  Settings, 
  Eye,
  Zap,
  Trophy,
  Bar<PERSON><PERSON>
} from 'lucide-react';

import { apiService } from '@/services/api';
import { webSocketService } from '@/services/websocket';
import { useAuth } from '@/hooks/useAuth';
import { useApi } from '@/hooks/useApi';
import { DGMExperimentForm } from './DGMExperimentForm';
import { DGMExperimentDetails } from './DGMExperimentDetails';

import type { DGMExperiment, DGMExperimentProgress } from '@shared/schemas';

export function DGMExperiments() {
  const { } = useAuth(); // user not currently used
  const [experiments, setExperiments] = useState<DGMExperiment[]>([]);
  const [selectedExperiment, setSelectedExperiment] = useState<DGMExperiment | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [experimentProgress, setExperimentProgress] = useState<Record<string, DGMExperimentProgress>>({});
  
  const { loading, execute } = useApi<DGMExperiment[]>();

  useEffect(() => {
    loadExperiments();
  }, []);

  // Subscribe to real-time progress updates
  useEffect(() => {
    const handleProgress = (progress: DGMExperimentProgress) => {
      setExperimentProgress(prev => ({
        ...prev,
        [progress.experiment_id]: progress
      }));
    };

    const handleCompleted = (data: { experimentId: string; success: boolean }) => {
      // Refresh experiments list
      loadExperiments();
      
      // Clear progress for completed experiment
      setExperimentProgress(prev => {
        const newProgress = { ...prev };
        delete newProgress[data.experimentId];
        return newProgress;
      });
    };

    webSocketService.on('job:dgm_experiment_progress', handleProgress);
    webSocketService.on('job:dgm_experiment_completed', handleCompleted);

    return () => {
      webSocketService.off('job:dgm_experiment_progress', handleProgress);
      webSocketService.off('job:dgm_experiment_completed', handleCompleted);
    };
  }, []);

  const loadExperiments = async () => {
    try {
      const data = await execute(() => apiService.getDGMExperiments());
      if (data) {
        setExperiments(data);
      }
    } catch (error) {
      console.error('Failed to load experiments:', error);
    }
  };

  const handleCreateExperiment = async (name: string, baseStrategy: Record<string, any>) => {
    try {
      const result = await apiService.createDGMExperiment(name, baseStrategy);
      if (result.experimentId) {
        // Subscribe to progress updates for this experiment
        webSocketService.subscribeToJob('dgm', result.experimentId);
        await loadExperiments();
        setShowCreateForm(false);
      }
    } catch (error) {
      console.error('Failed to create experiment:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-5 h-5 text-yellow-500" />;
      case 'running':
        return <Play className="w-5 h-5 text-blue-500 animate-pulse" />;
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'deployed':
        return <TrendingUp className="w-5 h-5 text-purple-500" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      'pending': 'bg-yellow-100 text-yellow-800 border-yellow-200',
      'running': 'bg-blue-100 text-blue-800 border-blue-200',
      'completed': 'bg-green-100 text-green-800 border-green-200',
      'deployed': 'bg-purple-100 text-purple-800 border-purple-200',
      'error': 'bg-red-100 text-red-800 border-red-200'
    };
    return colors[status] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const getProgressInfo = (experimentId: string) => {
    return experimentProgress[experimentId];
  };

  const runningExperiments = experiments.filter(e => e.status === 'running');
  const completedExperiments = experiments.filter(e => e.status === 'completed');
  const deployedExperiments = experiments.filter(e => e.status === 'deployed');
  // const errorExperiments = experiments.filter(e => e.status === 'error'); // Not currently used

  if (selectedExperiment) {
    return (
      <DGMExperimentDetails
        experiment={selectedExperiment}
        onBack={() => setSelectedExperiment(null)}
        progress={getProgressInfo(selectedExperiment.id)}
      />
    );
  }

  if (showCreateForm) {
    return (
      <DGMExperimentForm
        onSubmit={handleCreateExperiment}
        onCancel={() => setShowCreateForm(false)}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Darwin GÃ¶del Machine
          </h1>
          <p className="text-gray-600 max-w-2xl">
            Evolve and optimize your trading strategies automatically using advanced genetic algorithms 
            and machine learning. Let AI discover profitable patterns in your data.
          </p>
        </div>
        
        <button
          onClick={() => setShowCreateForm(true)}
          className="btn-primary flex items-center space-x-2"
          disabled={loading}
        >
          <Beaker className="w-4 h-4" />
          <span>New Experiment</span>
        </button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Experiments</p>
              <p className="text-3xl font-bold text-gray-900">{experiments.length}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Beaker className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>
        
        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Running</p>
              <p className="text-3xl font-bold text-blue-600">{runningExperiments.length}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Play className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>
        
        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-3xl font-bold text-green-600">{completedExperiments.length}</p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>
        
        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Deployed</p>
              <p className="text-3xl font-bold text-purple-600">{deployedExperiments.length}</p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Experiments List */}
      <div className="card">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">Experiments</h2>
            <div className="flex items-center space-x-2">
              <button className="text-sm text-gray-600 hover:text-gray-900">
                <Settings className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
        
        <div className="divide-y divide-gray-200">
          {loading ? (
            <div className="px-6 py-12 text-center">
              <div className="loading-spinner mx-auto mb-4"></div>
              <p className="text-gray-600">Loading experiments...</p>
            </div>
          ) : experiments.length === 0 ? (
            <div className="px-6 py-12 text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Beaker className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No experiments yet</h3>
              <p className="text-gray-600 mb-6 max-w-md mx-auto">
                Create your first DGM experiment to start evolving trading strategies. 
                The AI will automatically test thousands of variations to find the best performers.
              </p>
              <button
                onClick={() => setShowCreateForm(true)}
                className="btn-primary"
              >
                Create Your First Experiment
              </button>
            </div>
          ) : (
            experiments.map((experiment) => {
              const progress = getProgressInfo(experiment.id);
              
              return (
                <div key={experiment.id} className="px-6 py-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        {getStatusIcon(experiment.status)}
                        <h3 className="text-lg font-medium text-gray-900">
                          {experiment.experiment_name}
                        </h3>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(experiment.status)}`}>
                          {experiment.status}
                        </span>
                      </div>
                      
                      <div className="flex items-center space-x-6 text-sm text-gray-600">
                        <span>Created: {new Date(experiment.created_at).toLocaleDateString()}</span>
                        
                        {experiment.fitness_improvement !== undefined && (
                          <div className="flex items-center space-x-1">
                            <Trophy className="w-4 h-4 text-yellow-500" />
                            <span>Fitness: <span className="font-medium">{experiment.fitness_improvement.toFixed(3)}</span></span>
                          </div>
                        )}
                        
                        {experiment.fitness_improvement !== undefined && experiment.fitness_improvement > 0 && (
                          <div className="flex items-center space-x-1">
                            <BarChart className="w-4 h-4" />
                            <span className={experiment.fitness_improvement > 0 ? 'text-green-600' : 'text-red-600'}>
                              Improvement: {experiment.fitness_improvement > 0 ? '+' : ''}{(experiment.fitness_improvement * 100).toFixed(2)}%
                            </span>
                          </div>
                        )}
                      </div>
                      
                      {/* Progress Bar for Running Experiments */}
                      {progress && experiment.status === 'running' && (
                        <div className="mt-3">
                          <div className="flex items-center justify-between text-xs text-gray-600 mb-1">
                            <span>{progress.current_step}</span>
                            <span>{progress.progress_percent}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${progress.progress_percent}%` }}
                            />
                          </div>
                          <div className="flex items-center justify-between text-xs text-gray-500 mt-1">
                            <span>Generation {progress.current_generation || 0}</span>
                            <span>{progress.current_generation || 0} generations</span>
                          </div>
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => setSelectedExperiment(experiment)}
                        className="btn-secondary text-sm"
                      >
                        <Eye className="w-4 h-4 mr-1" />
                        View Details
                      </button>
                      
                      {experiment.status === 'completed' && experiment.fitness_improvement && experiment.fitness_improvement > 0.1 && (
                        <button className="btn-primary text-sm">
                          <Zap className="w-4 h-4 mr-1" />
                          Deploy Strategy
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              );
            })
          )}
        </div>
      </div>

      {/* Best Performing Strategies */}
      {completedExperiments.length > 0 && (
        <div className="card">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Top Performing Strategies</h2>
          </div>
          
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {completedExperiments
                .filter(e => e.fitness_improvement && e.fitness_improvement > 0)
                .sort((a, b) => (b.fitness_improvement || 0) - (a.fitness_improvement || 0))
                .slice(0, 6)
                .map((experiment, index) => (
                  <div key={experiment.id} className="bg-gradient-to-br from-green-50 to-blue-50 rounded-lg p-4 border border-green-200">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <div className="w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-xs font-bold">
                          {index + 1}
                        </div>
                        <span className="font-medium text-gray-900">{experiment.experiment_name}</span>
                      </div>
                      <Trophy className="w-5 h-5 text-yellow-500" />
                    </div>
                    
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Improvement:</span>
                        <span className="font-medium text-green-600">
                          +{((experiment.fitness_improvement || 0) * 100).toFixed(2)}%
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Fitness Score:</span>
                        <span className="font-medium">{(experiment.fitness_improvement || 0).toFixed(3)}</span>
                      </div>
                    </div>
                    
                    <div className="mt-3 flex space-x-2">
                      <button
                        onClick={() => setSelectedExperiment(experiment)}
                        className="flex-1 text-xs btn-secondary"
                      >
                        View
                      </button>
                      <button className="flex-1 text-xs btn-primary">
                        Deploy
                      </button>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
