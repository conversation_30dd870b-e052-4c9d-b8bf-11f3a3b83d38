#!/usr/bin/env python3
"""
Comprehensive TDD Tests for Darwin Gödel Machine Optimizer
Test-driven development implementation with cryptographic audit trail validation.
"""

import pytest
import numpy as np
import pandas as pd
import time
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from unittest.mock import Mock, patch, MagicMock
import threading
import sys
import os

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src', 'optimization'))

from dgm_optimizer import (
    DarwinGodelMachineOptimizer, OptimizationConfig, OptimizationResult, OptimizationStatus,
    AuditTrail, AuditEntry, Individual, GenerationResult, ParameterSpace,
    BaseStrategy, BacktestEngine, BacktestResult, MovingAverageCrossoverStrategy,
    SelectionMethod, MutationStrategy, create_dgm_optimizer
)


class TestParameterSpace:
    """Test parameter space definition and operations"""
    
    def test_parameter_space_creation_float(self):
        """Test creating float parameter space"""
        param = ParameterSpace("test_param", 0.0, 1.0, "float")
        
        assert param.name == "test_param"
        assert param.min_value == 0.0
        assert param.max_value == 1.0
        assert param.param_type == "float"
    
    def test_parameter_space_creation_int(self):
        """Test creating integer parameter space"""
        param = ParameterSpace("int_param", 1, 100, "int")
        
        assert param.name == "int_param"
        assert param.min_value == 1
        assert param.max_value == 100
        assert param.param_type == "int"
    
    def test_parameter_space_creation_categorical(self):
        """Test creating categorical parameter space"""
        categories = ["option1", "option2", "option3"]
        param = ParameterSpace("cat_param", 0, 0, "categorical", categories=categories)
        
        assert param.name == "cat_param"
        assert param.param_type == "categorical"
        assert param.categories == categories
    
    def test_parameter_space_categorical_without_categories_fails(self):
        """Test that categorical parameter without categories fails"""
        with pytest.raises(ValueError, match="Categories must be provided"):
            ParameterSpace("bad_param", 0, 0, "categorical")
    
    def test_generate_random_value_float(self):
        """Test generating random float values"""
        param = ParameterSpace("float_param", 0.0, 1.0, "float")
        
        for _ in range(10):
            value = param.generate_random_value()
            assert isinstance(value, float)
            assert 0.0 <= value <= 1.0
    
    def test_generate_random_value_int(self):
        """Test generating random integer values"""
        param = ParameterSpace("int_param", 1, 10, "int")
        
        for _ in range(10):
            value = param.generate_random_value()
            assert isinstance(value, (int, np.integer))
            assert 1 <= value <= 10
    
    def test_generate_random_value_categorical(self):
        """Test generating random categorical values"""
        categories = ["A", "B", "C"]
        param = ParameterSpace("cat_param", 0, 0, "categorical", categories=categories)
        
        for _ in range(10):
            value = param.generate_random_value()
            assert value in categories
    
    def test_mutate_value_float(self):
        """Test mutating float values"""
        param = ParameterSpace("float_param", 0.0, 1.0, "float")
        original_value = 0.5
        
        # Test with high mutation rate
        mutated = param.mutate_value(original_value, mutation_rate=1.0)
        assert isinstance(mutated, float)
        assert 0.0 <= mutated <= 1.0
        
        # Test with zero mutation rate
        no_mutation = param.mutate_value(original_value, mutation_rate=0.0)
        assert no_mutation == original_value
    
    def test_mutate_value_int(self):
        """Test mutating integer values"""
        param = ParameterSpace("int_param", 1, 100, "int")
        original_value = 50
        
        # Test with high mutation rate
        mutated = param.mutate_value(original_value, mutation_rate=1.0)
        assert isinstance(mutated, (int, np.integer))
        assert 1 <= mutated <= 100
    
    def test_mutate_value_categorical(self):
        """Test mutating categorical values"""
        categories = ["A", "B", "C"]
        param = ParameterSpace("cat_param", 0, 0, "categorical", categories=categories)
        original_value = "A"
        
        # Test with high mutation rate
        mutated = param.mutate_value(original_value, mutation_rate=1.0)
        assert mutated in categories


class TestIndividual:
    """Test Individual class and operations"""
    
    def test_individual_creation(self):
        """Test creating individual"""
        params = {"param1": 0.5, "param2": 10}
        individual = Individual(id="TEST_001", parameters=params)
        
        assert individual.id == "TEST_001"
        assert individual.parameters == params
        assert individual.fitness is None
        assert individual.generation == 0
        assert individual.parent_ids == []
    
    def test_individual_auto_id_generation(self):
        """Test automatic ID generation"""
        individual = Individual(id="", parameters={})
        
        assert individual.id.startswith("IND_")
        assert len(individual.id) == 12  # IND_ + 8 hex chars
    
    def test_individual_hash_generation(self):
        """Test individual hash generation"""
        params = {"param1": 0.5, "param2": 10}
        individual = Individual(id="TEST_001", parameters=params, fitness=0.8)
        
        hash1 = individual.get_hash()
        hash2 = individual.get_hash()
        
        # Same individual should produce same hash
        assert hash1 == hash2
        assert len(hash1) == 64  # SHA-256 hex length
        
        # Different individual should produce different hash
        individual2 = Individual(id="TEST_002", parameters=params, fitness=0.8)
        assert individual.get_hash() != individual2.get_hash()
    
    def test_individual_with_parents(self):
        """Test individual with parent information"""
        individual = Individual(
            id="CHILD_001",
            parameters={"param1": 0.5},
            parent_ids=["PARENT_001", "PARENT_002"],
            generation=1
        )
        
        assert individual.parent_ids == ["PARENT_001", "PARENT_002"]
        assert individual.generation == 1


class TestAuditEntry:
    """Test audit entry creation and verification"""
    
    def test_audit_entry_creation(self):
        """Test creating audit entry"""
        timestamp = datetime.now(timezone.utc)
        entry = AuditEntry(
            timestamp=timestamp,
            operation="test_operation",
            generation=1,
            individual_id="IND_001",
            parameters={"param1": 0.5},
            fitness=0.8,
            metadata={"test": "data"}
        )
        
        assert entry.timestamp == timestamp
        assert entry.operation == "test_operation"
        assert entry.generation == 1
        assert entry.individual_id == "IND_001"
        assert entry.parameters == {"param1": 0.5}
        assert entry.fitness == 0.8
        assert entry.metadata == {"test": "data"}
        assert entry.entry_hash is not None
        assert len(entry.entry_hash) == 64  # SHA-256 hex length
    
    def test_audit_entry_integrity_verification(self):
        """Test audit entry integrity verification"""
        entry = AuditEntry(
            timestamp=datetime.now(timezone.utc),
            operation="test_operation",
            generation=1,
            individual_id="IND_001",
            parameters={"param1": 0.5},
            fitness=0.8,
            metadata={}
        )
        
        # Should verify successfully
        assert entry.verify_integrity() is True
        
        # Tamper with entry
        entry.fitness = 0.9
        
        # Should fail verification after tampering
        assert entry.verify_integrity() is False


class TestAuditTrail:
    """Test audit trail functionality"""
    
    @pytest.fixture
    def audit_trail(self):
        """Create audit trail for testing"""
        return AuditTrail(secret_key="test_key_2024")
    
    def test_audit_trail_creation(self, audit_trail):
        """Test audit trail creation"""
        assert audit_trail.secret_key == "test_key_2024"
        assert audit_trail.session_id.startswith("DGM_")
        assert len(audit_trail.entries) == 1  # session_start entry
        assert audit_trail.entries[0].operation == "session_start"
    
    def test_log_operation(self, audit_trail):
        """Test logging operations"""
        audit_trail.log_operation(
            "test_operation",
            generation=1,
            individual_id="IND_001",
            parameters={"param1": 0.5},
            fitness=0.8,
            custom_data="test"
        )
        
        # Should have session_start + test_operation
        assert len(audit_trail.entries) == 2
        
        entry = audit_trail.entries[1]
        assert entry.operation == "test_operation"
        assert entry.generation == 1
        assert entry.individual_id == "IND_001"
        assert entry.parameters == {"param1": 0.5}
        assert entry.fitness == 0.8
        assert entry.metadata["custom_data"] == "test"
    
    def test_log_generation(self, audit_trail):
        """Test logging generation results"""
        individual1 = Individual(id="IND_001", parameters={"p1": 0.5}, fitness=0.8)
        individual2 = Individual(id="IND_002", parameters={"p1": 0.3}, fitness=0.6)
        population = [individual1, individual2]
        
        audit_trail.log_generation(1, population, individual1, 0.1)
        
        # Find generation_complete entry
        gen_entry = next(e for e in audit_trail.entries if e.operation == "generation_complete")
        
        assert gen_entry.generation == 1
        assert gen_entry.individual_id == "IND_001"
        assert gen_entry.fitness == 0.8
        assert gen_entry.metadata["population_size"] == 2
        assert gen_entry.metadata["convergence_metric"] == 0.1
    
    def test_log_individual_evaluation(self, audit_trail):
        """Test logging individual evaluation"""
        individual = Individual(
            id="IND_001",
            parameters={"param1": 0.5},
            fitness=0.8,
            generation=1,
            parent_ids=["PARENT_001"]
        )
        
        audit_trail.log_individual_evaluation(individual)
        
        eval_entry = next(e for e in audit_trail.entries if e.operation == "individual_evaluation")
        
        assert eval_entry.generation == 1
        assert eval_entry.individual_id == "IND_001"
        assert eval_entry.parameters == {"param1": 0.5}
        assert eval_entry.fitness == 0.8
        assert eval_entry.metadata["parent_ids"] == ["PARENT_001"]
    
    def test_get_trail(self, audit_trail):
        """Test getting complete audit trail"""
        audit_trail.log_operation("test_op1", generation=1)
        audit_trail.log_operation("test_op2", generation=2)
        
        trail = audit_trail.get_trail()
        
        assert len(trail) == 3  # session_start + 2 test operations
        assert all("timestamp" in entry for entry in trail)
        assert all("operation" in entry for entry in trail)
        assert all("entry_hash" in entry for entry in trail)
    
    def test_sha256_hash(self, audit_trail):
        """Test SHA-256 hash generation"""
        audit_trail.log_operation("test_operation")
        
        hash1 = audit_trail.sha256_hash()
        hash2 = audit_trail.sha256_hash()
        
        # Same trail should produce same hash
        assert hash1 == hash2
        assert len(hash1) == 64  # SHA-256 hex length
        
        # Adding entry should change hash
        audit_trail.log_operation("another_operation")
        hash3 = audit_trail.sha256_hash()
        assert hash1 != hash3
    
    def test_hmac_signature(self, audit_trail):
        """Test HMAC signature generation"""
        audit_trail.log_operation("test_operation")
        
        signature1 = audit_trail.hmac_signature()
        signature2 = audit_trail.hmac_signature()
        
        # Same trail should produce same signature
        assert signature1 == signature2
        assert len(signature1) == 64  # HMAC-SHA256 hex length
    
    def test_verify_integrity(self, audit_trail):
        """Test audit trail integrity verification"""
        audit_trail.log_operation("test_operation")
        
        # Should verify successfully
        assert audit_trail.verify_integrity() is True
        
        # Tamper with an entry
        audit_trail.entries[0].fitness = 999.0
        
        # Should fail verification after tampering
        assert audit_trail.verify_integrity() is False
    
    def test_get_statistics(self, audit_trail):
        """Test audit trail statistics"""
        audit_trail.log_operation("operation_a")
        audit_trail.log_operation("operation_b")
        audit_trail.log_operation("operation_a")  # Duplicate
        
        stats = audit_trail.get_statistics()
        
        assert stats["session_id"] == audit_trail.session_id
        assert stats["total_entries"] == 4  # session_start + 3 operations
        assert stats["operations"]["session_start"] == 1
        assert stats["operations"]["operation_a"] == 2
        assert stats["operations"]["operation_b"] == 1
        assert "start_time" in stats
        assert "duration_seconds" in stats
        assert "integrity_verified" in stats
    
    def test_context_manager(self):
        """Test audit trail as context manager"""
        with AuditTrail() as audit:
            audit.log_operation("test_operation")
            assert len(audit.entries) == 2  # session_start + test_operation
        
        # Should have session_end entry
        assert len(audit.entries) == 3
        assert audit.entries[-1].operation == "session_end"
    
    def test_thread_safety(self, audit_trail):
        """Test thread safety of audit trail"""
        def log_operations(thread_id):
            for i in range(10):
                audit_trail.log_operation(f"thread_{thread_id}_op_{i}")
        
        # Create multiple threads
        threads = []
        for i in range(3):
            thread = threading.Thread(target=log_operations, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Should have session_start + 30 operations
        assert len(audit_trail.entries) == 31
        
        # All entries should have valid hashes
        for entry in audit_trail.entries:
            assert entry.verify_integrity() is True


class TestBacktestEngine:
    """Test backtesting engine"""
    
    @pytest.fixture
    def sample_data(self):
        """Create sample market data"""
        np.random.seed(42)
        dates = pd.date_range('2020-01-01', '2020-12-31', freq='D')
        prices = 100 * np.exp(np.cumsum(np.random.normal(0.0005, 0.02, len(dates))))
        return pd.DataFrame({'close': prices}, index=dates)
    
    @pytest.fixture
    def backtest_engine(self):
        """Create backtest engine"""
        return BacktestEngine(initial_capital=100000.0, commission=0.001)
    
    def test_backtest_engine_creation(self, backtest_engine):
        """Test backtest engine creation"""
        assert backtest_engine.initial_capital == 100000.0
        assert backtest_engine.commission == 0.001
    
    def test_backtest_run_with_strategy(self, backtest_engine, sample_data):
        """Test running backtest with strategy"""
        strategy = MovingAverageCrossoverStrategy({
            'fast_period': 10,
            'slow_period': 30,
            'signal_threshold': 0.001
        })
        
        result = backtest_engine.run(sample_data, strategy)
        
        assert isinstance(result, BacktestResult)
        assert isinstance(result.total_return, float)
        assert isinstance(result.sharpe_ratio, float)
        assert isinstance(result.max_drawdown, float)
        assert isinstance(result.win_rate, float)
        assert isinstance(result.profit_factor, float)
        assert isinstance(result.trades_count, int)
        
        # Fitness should be non-negative
        fitness = result.get_fitness()
        assert fitness >= 0.0
    
    def test_backtest_handles_strategy_errors(self, backtest_engine, sample_data):
        """Test backtest handles strategy errors gracefully"""
        # Create strategy that will fail
        class FailingStrategy(BaseStrategy):
            @classmethod
            def get_params_space(cls):
                return []
            
            def generate_signals(self, data):
                raise ValueError("Strategy failed")
        
        strategy = FailingStrategy({})
        result = backtest_engine.run(sample_data, strategy)
        
        # Should return poor results for failed strategy
        assert result.total_return == -1.0
        assert result.sharpe_ratio == -1.0
        assert result.max_drawdown == -1.0
        assert result.win_rate == 0.0
        assert result.profit_factor == 0.0
        assert result.trades_count == 0


class TestMovingAverageCrossoverStrategy:
    """Test example strategy implementation"""
    
    def test_strategy_params_space(self):
        """Test strategy parameter space definition"""
        params_space = MovingAverageCrossoverStrategy.get_params_space()
        
        assert len(params_space) == 3
        
        # Check parameter names
        param_names = [p.name for p in params_space]
        assert "fast_period" in param_names
        assert "slow_period" in param_names
        assert "signal_threshold" in param_names
        
        # Check parameter types
        fast_param = next(p for p in params_space if p.name == "fast_period")
        assert fast_param.param_type == "int"
        assert fast_param.min_value == 5
        assert fast_param.max_value == 50
    
    def test_strategy_signal_generation(self):
        """Test strategy signal generation"""
        # Create sample data
        dates = pd.date_range('2020-01-01', '2020-12-31', freq='D')
        prices = pd.Series(range(100, 100 + len(dates)), index=dates)  # Trending up
        data = pd.DataFrame({'close': prices})
        
        strategy = MovingAverageCrossoverStrategy({
            'fast_period': 5,
            'slow_period': 20,
            'signal_threshold': 0.001
        })
        
        signals = strategy.generate_signals(data)
        
        assert isinstance(signals, pd.Series)
        assert len(signals) == len(data)
        assert signals.index.equals(data.index)
        
        # Signals should be -1, 0, or 1
        unique_signals = signals.unique()
        assert all(signal in [-1, 0, 1] for signal in unique_signals)


class TestOptimizationConfig:
    """Test optimization configuration"""
    
    def test_default_config(self):
        """Test default configuration values"""
        config = OptimizationConfig()
        
        assert config.population_size == 50
        assert config.generations == 100
        assert config.mutation_rate == 0.1
        assert config.crossover_rate == 0.8
        assert config.elitism_rate == 0.1
        assert config.selection_method == SelectionMethod.TOURNAMENT
        assert config.mutation_strategy == MutationStrategy.GAUSSIAN
        assert config.convergence_threshold == 1e-6
        assert config.max_stagnation_generations == 20
        assert config.parallel_evaluation is True
        assert config.max_workers == 4
        assert config.random_seed is None
    
    def test_custom_config(self):
        """Test custom configuration"""
        config = OptimizationConfig(
            population_size=20,
            generations=50,
            mutation_rate=0.2,
            selection_method=SelectionMethod.ROULETTE,
            random_seed=42
        )
        
        assert config.population_size == 20
        assert config.generations == 50
        assert config.mutation_rate == 0.2
        assert config.selection_method == SelectionMethod.ROULETTE
        assert config.random_seed == 42


class TestDarwinGodelMachineOptimizer:
    """Test main optimizer functionality"""
    
    @pytest.fixture
    def sample_data(self):
        """Create sample market data"""
        np.random.seed(42)
        dates = pd.date_range('2020-01-01', '2020-06-30', freq='D')
        prices = 100 * np.exp(np.cumsum(np.random.normal(0.0005, 0.02, len(dates))))
        return pd.DataFrame({'close': prices}, index=dates)
    
    @pytest.fixture
    def small_config(self):
        """Create small configuration for testing"""
        return OptimizationConfig(
            population_size=5,
            generations=3,
            mutation_rate=0.2,
            parallel_evaluation=False,  # Easier to test
            random_seed=42
        )
    
    @pytest.fixture
    def optimizer(self, small_config):
        """Create optimizer for testing"""
        return DarwinGodelMachineOptimizer(small_config)
    
    def test_optimizer_creation(self, optimizer, small_config):
        """Test optimizer creation"""
        assert optimizer.config == small_config
        assert isinstance(optimizer.backtest_engine, BacktestEngine)
    
    def test_create_initial_population(self, optimizer, sample_data):
        """Test initial population creation"""
        params_space = MovingAverageCrossoverStrategy.get_params_space()
        
        with AuditTrail() as audit:
            population = optimizer._create_initial_population(
                params_space, MovingAverageCrossoverStrategy, sample_data, audit
            )
        
        assert len(population) == optimizer.config.population_size
        
        for individual in population:
            assert isinstance(individual, Individual)
            assert individual.generation == 0
            assert individual.id.startswith("GEN0_IND_")
            assert len(individual.parameters) == len(params_space)
            
            # Check parameter values are within bounds
            for param in params_space:
                value = individual.parameters[param.name]
                if param.param_type == "int":
                    assert param.min_value <= value <= param.max_value
                elif param.param_type == "float":
                    assert param.min_value <= value <= param.max_value
    
    def test_evaluate_individual(self, optimizer, sample_data):
        """Test individual evaluation"""
        individual = Individual(
            id="TEST_001",
            parameters={
                'fast_period': 10,
                'slow_period': 30,
                'signal_threshold': 0.001
            }
        )
        
        fitness = optimizer._evaluate_individual(
            individual, MovingAverageCrossoverStrategy, sample_data
        )
        
        assert isinstance(fitness, float)
        assert fitness >= 0.0
    
    def test_evaluate_population_sequential(self, optimizer, sample_data):
        """Test sequential population evaluation"""
        params_space = MovingAverageCrossoverStrategy.get_params_space()
        
        with AuditTrail() as audit:
            population = optimizer._create_initial_population(
                params_space, MovingAverageCrossoverStrategy, sample_data, audit
            )
            
            evaluated_population = optimizer._evaluate_population_sequential(
                population, MovingAverageCrossoverStrategy, sample_data, audit
            )
        
        assert len(evaluated_population) == len(population)
        
        for individual in evaluated_population:
            assert individual.fitness is not None
            assert individual.fitness >= 0.0
            assert individual.evaluation_time is not None
    
    def test_tournament_selection(self, optimizer):
        """Test tournament selection"""
        # Create population with known fitness values
        population = [
            Individual(id=f"IND_{i}", parameters={}, fitness=float(i))
            for i in range(10)
        ]
        
        selected = optimizer._tournament_selection(population, tournament_size=3)
        
        assert len(selected) == len(population) // 2
        
        # Selected individuals should generally have higher fitness
        avg_selected_fitness = np.mean([ind.fitness for ind in selected])
        avg_population_fitness = np.mean([ind.fitness for ind in population])
        assert avg_selected_fitness >= avg_population_fitness
    
    def test_crossover(self, optimizer):
        """Test crossover operation"""
        params_space = MovingAverageCrossoverStrategy.get_params_space()
        
        parent1 = Individual(
            id="PARENT_1",
            parameters={'fast_period': 10, 'slow_period': 30, 'signal_threshold': 0.001},
            generation=0
        )
        parent2 = Individual(
            id="PARENT_2",
            parameters={'fast_period': 20, 'slow_period': 50, 'signal_threshold': 0.005},
            generation=0
        )
        
        with AuditTrail() as audit:
            offspring = optimizer._crossover(parent1, parent2, params_space, audit)
        
        assert offspring.generation == 1
        assert offspring.parent_ids == ["PARENT_1", "PARENT_2"]
        assert len(offspring.parameters) == len(parent1.parameters)
        
        # Offspring parameters should come from one of the parents
        for param_name, value in offspring.parameters.items():
            assert value in [parent1.parameters[param_name], parent2.parameters[param_name]]
    
    def test_mutation(self, optimizer):
        """Test mutation operation"""
        params_space = MovingAverageCrossoverStrategy.get_params_space()
        
        individual = Individual(
            id="IND_001",
            parameters={'fast_period': 10, 'slow_period': 30, 'signal_threshold': 0.001},
            generation=1
        )
        original_params = individual.parameters.copy()
        
        with AuditTrail() as audit:
            mutated = optimizer._mutate(individual, params_space, audit)
        
        assert mutated.id == individual.id
        assert len(mutated.mutation_history) == 1
        
        mutation_record = mutated.mutation_history[0]
        assert mutation_record['generation'] == 1
        assert mutation_record['original_params'] == original_params
        assert mutation_record['mutated_params'] == mutated.parameters
    
    def test_full_optimization(self, optimizer, sample_data):
        """Test complete optimization process"""
        result = optimizer.optimize(MovingAverageCrossoverStrategy, sample_data)
        
        assert isinstance(result, OptimizationResult)
        assert isinstance(result.best_individual, Individual)
        assert isinstance(result.best_parameters, dict)
        assert isinstance(result.best_fitness, float)
        assert result.best_fitness >= 0.0
        assert result.optimization_status in OptimizationStatus
        assert result.generations_completed <= optimizer.config.generations
        assert result.total_evaluations > 0
        assert len(result.convergence_history) == result.generations_completed
        assert len(result.generation_results) == result.generations_completed
        assert len(result.audit_trail) > 0
        assert result.verification_hash is not None
        assert result.hmac_signature is not None
        assert result.execution_time > 0
        assert result.config == optimizer.config
        
        # Verify result integrity
        assert result.verify_integrity() is True
    
    def test_optimization_with_convergence(self, sample_data):
        """Test optimization that converges early"""
        config = OptimizationConfig(
            population_size=5,
            generations=100,  # High number
            convergence_threshold=1.0,  # Easy to achieve
            random_seed=42
        )
        
        optimizer = DarwinGodelMachineOptimizer(config)
        result = optimizer.optimize(MovingAverageCrossoverStrategy, sample_data)
        
        # Should converge before max generations
        assert result.optimization_status == OptimizationStatus.CONVERGED
        assert result.generations_completed < config.generations
    
    def test_optimization_with_stagnation(self, sample_data):
        """Test optimization that terminates due to stagnation"""
        config = OptimizationConfig(
            population_size=5,
            generations=100,
            max_stagnation_generations=2,  # Low threshold
            convergence_threshold=1e-10,  # Hard to achieve
            random_seed=42
        )
        
        optimizer = DarwinGodelMachineOptimizer(config)
        result = optimizer.optimize(MovingAverageCrossoverStrategy, sample_data)
        
        # Should terminate due to stagnation
        assert result.optimization_status == OptimizationStatus.TERMINATED
    
    def test_parallel_evaluation(self, sample_data):
        """Test parallel population evaluation"""
        config = OptimizationConfig(
            population_size=6,
            generations=2,
            parallel_evaluation=True,
            max_workers=2,
            random_seed=42
        )
        
        optimizer = DarwinGodelMachineOptimizer(config)
        result = optimizer.optimize(MovingAverageCrossoverStrategy, sample_data)
        
        assert result.optimization_status in [OptimizationStatus.COMPLETED, OptimizationStatus.CONVERGED]
        assert result.total_evaluations > 0
    
    def test_different_selection_methods(self, sample_data):
        """Test different selection methods"""
        selection_methods = [
            SelectionMethod.TOURNAMENT,
            SelectionMethod.ROULETTE,
            SelectionMethod.RANK,
            SelectionMethod.ELITIST
        ]
        
        for method in selection_methods:
            config = OptimizationConfig(
                population_size=5,
                generations=2,
                selection_method=method,
                random_seed=42
            )
            
            optimizer = DarwinGodelMachineOptimizer(config)
            result = optimizer.optimize(MovingAverageCrossoverStrategy, sample_data)
            
            assert result.optimization_status in [
                OptimizationStatus.COMPLETED,
                OptimizationStatus.CONVERGED,
                OptimizationStatus.TERMINATED
            ]


class TestOptimizationResult:
    """Test optimization result functionality"""
    
    @pytest.fixture
    def sample_result(self):
        """Create sample optimization result"""
        individual = Individual(
            id="BEST_001",
            parameters={'fast_period': 15, 'slow_period': 35, 'signal_threshold': 0.002},
            fitness=0.85
        )
        
        config = OptimizationConfig(population_size=10, generations=5)
        
        return OptimizationResult(
            best_individual=individual,
            best_parameters=individual.parameters,
            best_fitness=individual.fitness,
            optimization_status=OptimizationStatus.COMPLETED,
            generations_completed=5,
            total_evaluations=50,
            convergence_history=[0.5, 0.3, 0.2, 0.15, 0.1],
            generation_results=[],
            audit_trail=[],
            verification_hash="test_hash",
            hmac_signature="test_signature",
            execution_time=10.5,
            config=config
        )
    
    def test_optimization_result_creation(self, sample_result):
        """Test optimization result creation"""
        assert sample_result.best_individual.id == "BEST_001"
        assert sample_result.best_fitness == 0.85
        assert sample_result.optimization_status == OptimizationStatus.COMPLETED
        assert sample_result.generations_completed == 5
        assert sample_result.total_evaluations == 50
        assert len(sample_result.convergence_history) == 5
        assert sample_result.execution_time == 10.5
    
    def test_result_integrity_verification_mock(self, sample_result):
        """Test result integrity verification with mock data"""
        # This test uses mock data, so integrity verification will fail
        # In real usage, the hash would be generated from actual audit trail
        assert sample_result.verify_integrity() is False


class TestFactoryFunction:
    """Test factory function"""
    
    def test_create_dgm_optimizer_default(self):
        """Test creating optimizer with default config"""
        optimizer = create_dgm_optimizer()
        
        assert isinstance(optimizer, DarwinGodelMachineOptimizer)
        assert isinstance(optimizer.config, OptimizationConfig)
        assert optimizer.config.population_size == 50  # Default value
    
    def test_create_dgm_optimizer_with_config(self):
        """Test creating optimizer with custom config"""
        config = OptimizationConfig(population_size=20, generations=30)
        optimizer = create_dgm_optimizer(config)
        
        assert isinstance(optimizer, DarwinGodelMachineOptimizer)
        assert optimizer.config == config
        assert optimizer.config.population_size == 20


class TestIntegrationScenarios:
    """Integration tests for complete workflows"""
    
    @pytest.fixture
    def integration_data(self):
        """Create larger dataset for integration testing"""
        np.random.seed(42)
        dates = pd.date_range('2020-01-01', '2022-12-31', freq='D')
        prices = 100 * np.exp(np.cumsum(np.random.normal(0.0005, 0.02, len(dates))))
        return pd.DataFrame({'close': prices}, index=dates)
    
    def test_complete_optimization_workflow(self, integration_data):
        """Test complete optimization workflow"""
        # Configure optimizer
        config = OptimizationConfig(
            population_size=10,
            generations=5,
            mutation_rate=0.15,
            crossover_rate=0.85,
            selection_method=SelectionMethod.TOURNAMENT,
            parallel_evaluation=True,
            max_workers=2,
            random_seed=42
        )
        
        # Create optimizer
        optimizer = create_dgm_optimizer(config)
        
        # Run optimization
        result = optimizer.optimize(MovingAverageCrossoverStrategy, integration_data)
        
        # Verify complete result
        assert result.optimization_status in [
            OptimizationStatus.COMPLETED,
            OptimizationStatus.CONVERGED,
            OptimizationStatus.TERMINATED
        ]
        
        # Verify best parameters are valid
        params_space = MovingAverageCrossoverStrategy.get_params_space()
        for param in params_space:
            value = result.best_parameters[param.name]
            if param.param_type == "int":
                assert param.min_value <= value <= param.max_value
            elif param.param_type == "float":
                assert param.min_value <= value <= param.max_value
        
        # Verify audit trail completeness
        assert len(result.audit_trail) > 0
        
        # Check for key operations in audit trail
        operations = [entry['operation'] for entry in result.audit_trail]
        assert 'session_start' in operations
        assert 'optimization_start' in operations
        assert 'parameter_space_defined' in operations
        assert 'initial_population_created' in operations
        assert 'generation_complete' in operations
        assert 'optimization_complete' in operations
        assert 'session_end' in operations
        
        # Verify generation results
        assert len(result.generation_results) == result.generations_completed
        for gen_result in result.generation_results:
            assert isinstance(gen_result, GenerationResult)
            assert gen_result.generation >= 0
            assert len(gen_result.population) == config.population_size
            assert gen_result.best_individual.fitness is not None
            assert gen_result.execution_time > 0
    
    def test_optimization_error_handling(self, integration_data):
        """Test optimization error handling"""
        # Create strategy that will fail during optimization
        class FailingStrategy(BaseStrategy):
            @classmethod
            def get_params_space(cls):
                return [ParameterSpace("param1", 0.0, 1.0, "float")]
            
            def generate_signals(self, data):
                # Fail randomly to test error handling
                if np.random.random() < 0.5:
                    raise ValueError("Random strategy failure")
                return pd.Series(0, index=data.index)
        
        config = OptimizationConfig(
            population_size=5,
            generations=2,
            random_seed=42
        )
        
        optimizer = create_dgm_optimizer(config)
        
        # Should handle errors gracefully
        result = optimizer.optimize(FailingStrategy, integration_data)
        
        # Should complete despite individual failures
        assert result.optimization_status in [
            OptimizationStatus.COMPLETED,
            OptimizationStatus.CONVERGED,
            OptimizationStatus.TERMINATED
        ]
        
        # Some individuals may have zero fitness due to failures
        assert result.best_fitness >= 0.0
    
    def test_reproducibility_with_seed(self, integration_data):
        """Test optimization reproducibility with random seed"""
        config = OptimizationConfig(
            population_size=5,
            generations=3,
            random_seed=12345
        )
        
        # Run optimization twice with same seed
        optimizer1 = create_dgm_optimizer(config)
        result1 = optimizer1.optimize(MovingAverageCrossoverStrategy, integration_data)
        
        optimizer2 = create_dgm_optimizer(config)
        result2 = optimizer2.optimize(MovingAverageCrossoverStrategy, integration_data)
        
        # Results should be identical
        assert result1.best_fitness == result2.best_fitness
        assert result1.best_parameters == result2.best_parameters
        assert result1.generations_completed == result2.generations_completed


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v", "--tb=short"])