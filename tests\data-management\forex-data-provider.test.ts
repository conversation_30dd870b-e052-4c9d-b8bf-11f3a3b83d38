// tests/data-management/forex-data-provider.test.ts
import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { ForexDataProvider } from '../../src/data-management/forex-data-provider';
import * as fs from 'fs/promises';

// STEP 1: Write tests FIRST (Red phase) - Following TDD principles
describe('ForexDataProvider - TDD Implementation', () => {
  let provider: ForexDataProvider;
  
  beforeEach(() => {
    provider = new ForexDataProvider('./test-data');
  });

  describe('initialization', () => {
    it('should create data directory if it does not exist', async () => {
      // Arrange
      const mkdirSpy = jest.spyOn(fs, 'mkdir').mockResolvedValue(undefined);
      
      // Act
      await provider.initialize();
      
      // Assert
      expect(mkdirSpy).toHaveBeenCalledWith('./test-data', { recursive: true });
    });

    it('should load pre-existing data files on initialization', async () => {
      // Arrange
      const mockFileData = {
        pair: 'EUR/USD',
        timeframe: 'H1',
        source: 'dukascopy',
        candles: [
          { timestamp: new Date('2024-01-01'), open: 1.1, high: 1.11, low: 1.09, close: 1.105, volume: 1000 }
        ],
        dataHash: 'test-hash',
        lastValidated: new Date()
      };
      
      jest.spyOn(fs, 'readdir').mockResolvedValue(['EUR_USD_H1.json'] as any);
      jest.spyOn(fs, 'readFile').mockResolvedValue(JSON.stringify(mockFileData));
      
      // Act
      await provider.initialize();
      const available = await provider.getAvailableData();
      
      // Assert
      expect(available.pairs).toHaveLength(1);
      expect(available.pairs[0].pair).toBe('EUR/USD');
    });
  });

  describe('data validation - Zero Hallucination Guarantee', () => {
    it('should reject data with invalid OHLC relationships', async () => {
      // This test ensures no invalid data enters the system
      const invalidCandle = {
        timestamp: new Date(),
        open: 1.1,
        high: 1.08,  // Invalid: high < open
        low: 1.09,
        close: 1.105,
        volume: 1000
      };
      
      const result = await provider.validateCandle(invalidCandle);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('High price must be >= open and close prices');
    });

    it('should reject data with non-sequential timestamps', async () => {
      const candles = [
        { timestamp: new Date('2024-01-02'), open: 1.1, high: 1.11, low: 1.09, close: 1.105, volume: 1000 },
        { timestamp: new Date('2024-01-01'), open: 1.1, high: 1.11, low: 1.09, close: 1.105, volume: 1000 }
      ];
      
      const result = await provider.validateSequence(candles);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Timestamps must be in sequential order');
    });

    it('should validate realistic price ranges for currency pairs', async () => {
      // JPY pairs
      const jpyCandle = { 
        pair: 'USD/JPY',
        timestamp: new Date(),
        open: 300,  // Unrealistic for JPY
        high: 305,
        low: 295,
        close: 302,
        volume: 1000
      };
      
      const jpyResult = await provider.validatePriceRange(jpyCandle);
      expect(jpyResult.isValid).toBe(false);
      expect(jpyResult.errors).toContain('Unrealistic price for JPY pair');
    });

    it('should accept valid OHLC data', async () => {
      const validCandle = {
        timestamp: new Date(),
        open: 1.1,
        high: 1.11,  // Valid: high >= open and close
        low: 1.09,   // Valid: low <= open and close
        close: 1.105,
        volume: 1000
      };
      
      const result = await provider.validateCandle(validCandle);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
  });

  describe('data retrieval with verification', () => {
    it('should return error when requested data does not exist', async () => {
      const result = await provider.getData('EUR/USD', 'H1');
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('No data available for EUR/USD H1');
      expect(result.availableTimeframes).toBeDefined();
    });

    it('should return validated data with verification hash', async () => {
      // First, load some data
      const testData = {
        pair: 'EUR/USD',
        timeframe: 'H1',
        candles: generateValidCandles(100),
        source: 'test',
        dataHash: 'hash-123',
        lastValidated: new Date()
      };
      
      await provider.loadDataset(testData);
      
      // Now retrieve it
      const result = await provider.getData('EUR/USD', 'H1');
      
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data!.verification).toBeDefined();
      expect(result.data!.verification.dataPoints).toBe(100);
      expect(result.data!.verification.originalHash).toBeDefined();
    });

    it('should filter data by date range when requested', async () => {
      // Arrange
      const candles = generateValidCandles(30); // 30 days of daily data
      await provider.loadDataset({
        pair: 'EUR/USD',
        timeframe: 'D1',
        candles,
        source: 'test'
      });
      
      const startDate = new Date(candles[10].timestamp);
      const endDate = new Date(candles[20].timestamp);
      
      // Act
      const result = await provider.getData('EUR/USD', 'D1', startDate, endDate);
      
      // Assert
      expect(result.success).toBe(true);
      expect(result.data!.candles).toHaveLength(11); // Inclusive range
    });
  });

  describe('data integrity - Hash Verification', () => {
    it('should calculate and verify SHA-256 hash for data', async () => {
      const candles = generateValidCandles(10);
      const hash1 = provider.calculateDataHash(candles);
      const hash2 = provider.calculateDataHash(candles);
      
      expect(hash1).toBe(hash2); // Same data = same hash
      expect(hash1).toMatch(/^[a-f0-9]{64}$/); // Valid SHA-256 format
      
      // Modify data
      candles[0].close = 1.2;
      const hash3 = provider.calculateDataHash(candles);
      
      expect(hash3).not.toBe(hash1); // Different data = different hash
    });

    it('should detect data corruption', async () => {
      const originalCandles = generateValidCandles(5);
      const originalHash = provider.calculateDataHash(originalCandles);
      
      // Simulate data corruption
      const corruptedCandles = [...originalCandles];
      corruptedCandles[2].high = 999; // Corrupt one value
      
      const corruptedHash = provider.calculateDataHash(corruptedCandles);
      
      expect(corruptedHash).not.toBe(originalHash);
    });
  });

  describe('audit trail', () => {
    it('should track all data operations with timestamps', async () => {
      const testData = {
        pair: 'GBP/USD',
        timeframe: 'H4',
        candles: generateValidCandles(50),
        source: 'test-source'
      };
      
      await provider.loadDataset(testData);
      
      const auditLog = await provider.getAuditTrail('GBP/USD', 'H4');
      
      expect(auditLog).toBeDefined();
      expect(auditLog.operations).toHaveLength(1);
      expect(auditLog.operations[0].operation).toBe('load_dataset');
      expect(auditLog.operations[0].timestamp).toBeDefined();
      expect(auditLog.operations[0].dataHash).toBeDefined();
    });
  });
});

// Helper function for tests
function generateValidCandles(count: number): any[] {
  const candles = [];
  const baseTime = new Date('2024-01-01').getTime();
  
  for (let i = 0; i < count; i++) {
    const open = 1.1 + Math.random() * 0.01;
    const close = open + (Math.random() - 0.5) * 0.005;
    const high = Math.max(open, close) + Math.random() * 0.002;
    const low = Math.min(open, close) - Math.random() * 0.002;
    
    candles.push({
      timestamp: new Date(baseTime + i * 86400000), // Daily candles
      open: parseFloat(open.toFixed(5)),
      high: parseFloat(high.toFixed(5)),
      low: parseFloat(low.toFixed(5)),
      close: parseFloat(close.toFixed(5)),
      volume: Math.floor(Math.random() * 1000000)
    });
  }
  
  return candles;
}