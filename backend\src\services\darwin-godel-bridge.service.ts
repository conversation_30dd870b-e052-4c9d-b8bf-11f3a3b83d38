import { spawn, ChildProcess } from 'child_process';
import path from 'path';

export interface VerificationResult {
  isValid: boolean;
  strategyType: string;
  riskScore: number;
  robustnessScore: number;
  warnings: string[] | null;
  metrics?: {
    totalReturn: number;
    sharpeRatio: number;
    maxDrawdown: number;
    winRate: number;
    numTrades: number;
  };
}

export interface MonteCarloResult {
  successRate: number;
  avgSharpe: number;
  avgReturn: number;
  consistencyScore: number;
  distribution: Array<{
    total_return: number;
    sharpe_ratio: number;
  }>;
}

export interface HistoricalData {
  close: number[];
  high: number[];
  low: number[];
  volume: number[];
}

export class DarwinGodelBridgeService {
  private pythonEnginePath: string;

  constructor(pythonEnginePath?: string) {
    // Default to the python_engine directory relative to backend
    this.pythonEnginePath = pythonEnginePath || path.resolve(__dirname, '../../../python_engine');
  }

  /**
   * Verify a trading strategy using the Darwin Godel model
   */
  async verifyStrategy(strategyCode: string): Promise<VerificationResult> {
    const pythonScript = `
import sys
import os
sys.path.append('${this.pythonEnginePath.replace(/\\/g, '/')}')

from services.darwin_godel.strategy_verifier import DarwinGodelVerifier
import json

try:
    verifier = DarwinGodelVerifier()
    strategy_code = '''${strategyCode.replace(/'/g, "\\'")}'''
    
    result = verifier.verify_strategy(strategy_code)
    print(json.dumps(result))
except Exception as e:
    import traceback
    print(f"Error: {str(e)}", file=sys.stderr)
    print(traceback.format_exc(), file=sys.stderr)
    sys.exit(1)
`;

    return this.executePythonScript(pythonScript, (data) => {
      return {
        isValid: data.is_valid,
        strategyType: data.strategy_type,
        riskScore: data.risk_score,
        robustnessScore: data.robustness_score,
        warnings: data.warnings
      };
    });
  }

  /**
   * Verify strategy with backtest using historical data
   */
  async verifyWithBacktest(
    strategyCode: string, 
    historicalData: HistoricalData, 
    initialCapital: number = 10000
  ): Promise<VerificationResult> {
    const pythonScript = `
import sys
import os
sys.path.append('${this.pythonEnginePath.replace(/\\/g, '/')}')

from services.darwin_godel.strategy_verifier import DarwinGodelVerifier
import json

try:
    verifier = DarwinGodelVerifier()
    strategy_code = '''${strategyCode.replace(/'/g, "\\'")}'''
    
    historical_data = ${JSON.stringify(historicalData)}
    initial_capital = ${initialCapital}
    
    result = verifier.verify_with_backtest(strategy_code, historical_data, initial_capital)
    print(json.dumps(result))
except Exception as e:
    import traceback
    print(f"Error: {str(e)}", file=sys.stderr)
    print(traceback.format_exc(), file=sys.stderr)
    sys.exit(1)
`;

    return this.executePythonScript(pythonScript, (data) => {
      const result: VerificationResult = {
        isValid: data.is_valid,
        strategyType: data.strategy_type,
        riskScore: data.risk_score,
        robustnessScore: data.robustness_score,
        warnings: data.warnings
      };

      if (data.metrics) {
        result.metrics = {
          totalReturn: data.metrics.total_return,
          sharpeRatio: data.metrics.sharpe_ratio,
          maxDrawdown: data.metrics.max_drawdown,
          winRate: data.metrics.win_rate,
          numTrades: data.metrics.num_trades
        };
      }

      return result;
    });
  }

  /**
   * Run Monte Carlo validation on a strategy
   */
  async runMonteCarloValidation(
    strategyCode: string,
    simulations: number = 100,
    dataVariations: number = 0.02
  ): Promise<MonteCarloResult> {
    const pythonScript = `
import sys
import os
sys.path.append('${this.pythonEnginePath.replace(/\\/g, '/')}')

from services.darwin_godel.strategy_verifier import DarwinGodelVerifier
import json

try:
    verifier = DarwinGodelVerifier()
    strategy_code = '''${strategyCode.replace(/'/g, "\\'")}'''
    
    result = verifier.run_monte_carlo_validation(
        strategy_code, 
        simulations=${simulations}, 
        data_variations=${dataVariations}
    )
    print(json.dumps(result))
except Exception as e:
    import traceback
    print(f"Error: {str(e)}", file=sys.stderr)
    print(traceback.format_exc(), file=sys.stderr)
    sys.exit(1)
`;

    return this.executePythonScript(pythonScript, (data) => {
      return {
        successRate: data.success_rate,
        avgSharpe: data.avg_sharpe,
        avgReturn: data.avg_return,
        consistencyScore: data.consistency_score,
        distribution: data.distribution
      };
    });
  }

  /**
   * Check if the Python engine is healthy and available
   */
  async checkHealth(): Promise<boolean> {
    const pythonScript = `
import sys
import os
sys.path.append('${this.pythonEnginePath.replace(/\\/g, '/')}')

try:
    from services.darwin_godel.strategy_verifier import DarwinGodelVerifier
    verifier = DarwinGodelVerifier()
    print("Python engine ready")
except Exception as e:
    print(f"Error: {str(e)}", file=sys.stderr)
    sys.exit(1)
`;

    try {
      await this.executePythonScript(pythonScript, () => true);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Execute a Python script and parse the response
   */
  private async executePythonScript<T>(
    script: string, 
    responseParser: (data: any) => T
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const pythonProcess: ChildProcess = spawn('python', ['-c', script], {
        cwd: this.pythonEnginePath,
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let stdout = '';
      let stderr = '';

      pythonProcess.stdout?.on('data', (data) => {
        stdout += data.toString();
      });

      pythonProcess.stderr?.on('data', (data) => {
        stderr += data.toString();
      });

      pythonProcess.on('error', (error) => {
        reject(error);
      });

      pythonProcess.on('close', (code) => {
        if (code !== 0) {
          reject(new Error(stderr.trim() || `Python process exited with code ${code}`));
          return;
        }

        try {
          // Try to parse JSON response
          const lines = stdout.trim().split('\n');
          const lastLine = lines[lines.length - 1];
          
          if (lastLine === 'Python engine ready') {
            resolve(responseParser(null));
            return;
          }

          const jsonResponse = JSON.parse(lastLine);
          const result = responseParser(jsonResponse);
          resolve(result);
        } catch (error) {
          reject(new Error(`Failed to parse verification response: ${error instanceof Error ? error.message : 'Unknown error'}`));
        }
      });
    });
  }
}