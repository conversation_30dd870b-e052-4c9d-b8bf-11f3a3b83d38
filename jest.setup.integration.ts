/**
 * Jest setup for integration tests
 * Configures testing environment for bridge service integration tests
 */

import { jest } from '@jest/globals';

// Extend Jest timeout for integration tests
jest.setTimeout(30000);

// Setup global test environment
beforeAll(async () => {
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.LOG_LEVEL = 'error'; // Reduce log noise during tests
  process.env.PYTHON_ENGINE_BASE_URL = 'http://localhost:8000';
  process.env.PYTHON_ENGINE_TIMEOUT = '10000';
  process.env.TEST_MODE = 'integration';
  
  // Initialize any global test resources
  console.log('🧪 Setting up integration test environment...');
});

afterAll(async () => {
  // Cleanup global test resources
  console.log('🧹 Cleaning up integration test environment...');
  
  // Give time for async cleanup
  await new Promise(resolve => setTimeout(resolve, 1000));
});

// Global error handling for unhandled promises
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  // Don't exit process in test environment, just log
});

// Global error handling for uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  // Don't exit process in test environment, just log
});

// Custom matchers for integration tests
expect.extend({
  toBeServiceResponse(received) {
    const hasSuccessProperty = typeof received === 'object' && 
                              received !== null && 
                              'success' in received;
    
    if (!hasSuccessProperty) {
      return {
        message: () => `Expected object to have 'success' property`,
        pass: false,
      };
    }
    
    const isValidSuccessResponse = received.success === true && 'data' in received;
    const isValidErrorResponse = received.success === false && 'error' in received;
    
    if (!isValidSuccessResponse && !isValidErrorResponse) {
      return {
        message: () => `Expected valid service response format`,
        pass: false,
      };
    }
    
    return {
      message: () => `Expected invalid service response format`,
      pass: true,
    };
  },
  
  toBeValidOrderRequest(received) {
    const requiredFields = ['symbol', 'volume', 'order_type', 'price'];
    const hasAllFields = requiredFields.every(field => field in received);
    
    if (!hasAllFields) {
      return {
        message: () => `Expected order request to have fields: ${requiredFields.join(', ')}`,
        pass: false,
      };
    }
    
    const isValid = received.volume > 0 && 
                   received.price > 0 && 
                   ['buy', 'sell'].includes(received.order_type);
    
    return {
      message: () => isValid ? 
        `Expected invalid order request` : 
        `Expected valid order request`,
      pass: isValid,
    };
  },
  
  toBeValidBacktestConfig(received) {
    const requiredFields = ['name', 'symbols', 'start_date', 'end_date', 'initial_balance', 'strategy'];
    const hasAllFields = requiredFields.every(field => field in received);
    
    if (!hasAllFields) {
      return {
        message: () => `Expected backtest config to have fields: ${requiredFields.join(', ')}`,
        pass: false,
      };
    }
    
    const isValid = Array.isArray(received.symbols) &&
                   received.symbols.length > 0 &&
                   received.start_date < received.end_date &&
                   received.initial_balance > 0;
    
    return {
      message: () => isValid ? 
        `Expected invalid backtest config` : 
        `Expected valid backtest config`,
      pass: isValid,
    };
  },
});

// Declare custom matcher types for TypeScript
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeServiceResponse(): R;
      toBeValidOrderRequest(): R;
      toBeValidBacktestConfig(): R;
    }
  }
}

// Console override for cleaner test output
const originalConsole = { ...console };

// Silence console during tests unless DEBUG is set
if (!process.env.DEBUG) {
  console.log = jest.fn();
  console.info = jest.fn();
  console.warn = jest.fn();
  // Keep console.error for debugging
}

// Restore console after tests if needed
afterAll(() => {
  if (!process.env.DEBUG) {
    Object.assign(console, originalConsole);
  }
});

// Mock global Date for consistent testing
const FIXED_DATE = new Date('2024-06-15T12:00:00Z');

beforeEach(() => {
  // Reset Date mock before each test
  jest.clearAllMocks();
  
  // Mock Date.now() for consistent timestamps
  jest.spyOn(global.Date, 'now').mockReturnValue(FIXED_DATE.getTime());
});

afterEach(() => {
  // Restore Date after each test
  jest.restoreAllMocks();
});