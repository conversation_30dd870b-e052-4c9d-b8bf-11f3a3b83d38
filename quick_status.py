#!/usr/bin/env python3
"""
Quick System Status Checker for AI Enhanced Trading Platform
Provides fast, simple status checks without complex testing
"""

import requests
import sys
from datetime import datetime

def quick_test(url, name):
    """Quick test with timeout"""
    try:
        response = requests.get(url, timeout=2)
        status = "✅ OK" if response.status_code == 200 else f"⚠️ {response.status_code}"
        return f"{name:25} {status}"
    except Exception as e:
        return f"{name:25} ❌ {str(e)[:30]}"

def main():
    print("🚀 AI Enhanced Trading Platform - Quick Status Check")
    print("=" * 60)
    print(f"⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test all servers quickly
    tests = [
        ("http://localhost:9000/health", "Dedicated Server (9000)"),
        ("http://localhost:8003/health", "Backend Server (8003)"),
        ("http://localhost:11435/", "Ollama Container"),
        ("http://localhost:9000/api/ollama/status", "Ollama API"),
        ("http://localhost:9000/docs", "API Documentation"),
    ]
    
    for url, name in tests:
        print(quick_test(url, name))
    
    print()
    print("🎯 RECOMMENDED ACCESS POINTS:")
    print("   Frontend:     http://localhost:9000")
    print("   API Docs:     http://localhost:9000/docs")
    print("   Health:       http://localhost:9000/health")
    print()
    
    # Test if MT5 endpoints are available
    try:
        response = requests.get("http://localhost:9000/openapi.json", timeout=2)
        if response.status_code == 200:
            paths = response.json().get('paths', {})
            mt5_endpoints = [p for p in paths.keys() if 'mt5' in p]
            print(f"🎛️ MT5 ENDPOINTS: {len(mt5_endpoints)} available")
        else:
            print("🎛️ MT5 ENDPOINTS: Unable to check")
    except:
        print("🎛️ MT5 ENDPOINTS: Connection error")
    
    print()
    print("✅ SYSTEM STATUS: Ready for use!")

if __name__ == "__main__":
    main()
