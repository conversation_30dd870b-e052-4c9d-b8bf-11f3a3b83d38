import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON>, 
  BarChart3, 
  MessageCircle, 
  <PERSON><PERSON>, 
  Lightbulb,
  Send,
  User,
  Bo<PERSON>,
  Save,
  TestTube
} from 'lucide-react';

interface AIPrompt {
  id: string;
  title: string;
  description: string;
  category: string;
  prompt_template: string;
  variables: string[];
  example_usage: string;
  detailed_info: string;
}

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  code?: string;
  timestamp: Date;
}

interface SimpleStrategyHelperProps {
  chatMessages: Message[];
  chatInput: string;
  setChatInput: (value: string) => void;
  isLoading: boolean;
  handleChatSubmit: (e: React.FormEvent) => void;
}

const SimpleStrategyHelper: React.FC<SimpleStrategyHelperProps> = ({
  chatMessages,
  chatInput,
  setChatInput,
  isLoading,
  handleChatSubmit
}) => {
  const [activeTab, setActiveTab] = useState('ai-prompts');
  const [selectedPrompt, setSelectedPrompt] = useState<AIPrompt | null>(null);
  const [promptVariables, setPromptVariables] = useState<Record<string, string>>({});
  const [generatedPrompt, setGeneratedPrompt] = useState<string>('');

  // AI Prompts from promptadvance.club
  const aiPrompts: AIPrompt[] = [
    {
      id: "market_scanner",
      title: "Market Analysis & Asset Scanner",
      description: "Identify trading assets that meet specific criteria",
      category: "Market Analysis",
      prompt_template: "Act as a day trading assistant. Your task is to identify trading assets that meet the specified {criteria}. Utilize your expertise and available market analysis tools to scan, filter, and evaluate potential assets for trading opportunities.\n\nPlease provide:\n1. A list of 5-10 assets that match the criteria\n2. Brief analysis of why each asset meets the requirements\n3. Current market conditions affecting these assets\n4. Risk factors to consider\n5. Recommended timeframes for analysis\n\nCriteria: {criteria}",
      variables: ["criteria"],
      example_usage: "criteria: 'tech stocks with strong momentum and AI exposure'",
      detailed_info: "This prompt helps you systematically scan markets for opportunities. It provides structured analysis including risk assessment and timing recommendations."
    },
    {
      id: "technical_analyzer",
      title: "Comprehensive Technical Analysis",
      description: "Detailed technical analysis with entry/exit points",
      category: "Technical Analysis",
      prompt_template: "Act as an experienced day trader. Your objective is to analyze the price and volume patterns of {trading_asset} to identify potential buying or selling opportunities.\n\nProvide a comprehensive analysis including:\n1. Current trend analysis (short, medium, long-term)\n2. Key support and resistance levels\n3. Technical indicators analysis (RSI, MACD, Moving Averages)\n4. Volume analysis and patterns\n5. Entry points with specific price levels\n6. Stop-loss recommendations\n7. Take-profit targets\n8. Risk-reward ratio assessment\n9. Market sentiment indicators\n10. Timeframe recommendations\n\nAsset: {trading_asset}",
      variables: ["trading_asset"],
      example_usage: "trading_asset: 'NVDA (NVIDIA Corporation)'",
      detailed_info: "Provides comprehensive technical analysis with specific entry/exit points, risk management, and multi-timeframe perspective."
    },
    {
      id: "trade_execution",
      title: "Optimal Trade Execution Strategy",
      description: "Determine optimal entry, stop-loss, and target points",
      category: "Trade Execution",
      prompt_template: "Act as an experienced day trader. Based on your comprehensive analysis of current market conditions for {asset}, provide an optimal trade execution strategy.\n\nInclude:\n1. Precise entry price and entry conditions\n2. Stop-loss placement with reasoning\n3. Multiple take-profit targets\n4. Position sizing recommendations\n5. Risk management rules\n6. Market timing considerations\n7. Alternative scenarios (if trade goes against you)\n8. Exit strategy for different market conditions\n\nAsset: {asset}\nAccount Size: {account_size}\nRisk Tolerance: {risk_tolerance}%",
      variables: ["asset", "account_size", "risk_tolerance"],
      example_usage: "asset: 'TSLA', account_size: '$50000', risk_tolerance: '2'",
      detailed_info: "Creates detailed execution plans with precise entry/exit criteria, position sizing, and contingency planning."
    }
  ];

  const generateAIPrompt = () => {
    if (!selectedPrompt) return;

    let prompt = selectedPrompt.prompt_template;
    
    // Replace variables in the prompt
    selectedPrompt.variables.forEach((variable: string) => {
      const value = promptVariables[variable] || `[${variable}]`;
      prompt = prompt.replace(new RegExp(`{${variable}}`, 'g'), value);
    });

    setGeneratedPrompt(prompt);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      'Market Analysis': 'bg-blue-100 text-blue-800',
      'Technical Analysis': 'bg-green-100 text-green-800',
      'Trade Execution': 'bg-purple-100 text-purple-800',
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200">
      <div className="p-6">
        {/* Tab Navigation */}
        <div className="flex items-center space-x-1 mb-6 bg-gray-100 p-1 rounded-lg">
          <button
            onClick={() => setActiveTab('ai-prompts')}
            className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
              activeTab === 'ai-prompts'
                ? 'bg-white text-blue-700 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Brain className="w-4 h-4 inline mr-2" />
            AI Prompts
          </button>
          <button
            onClick={() => setActiveTab('chat')}
            className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
              activeTab === 'chat'
                ? 'bg-white text-blue-700 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <MessageCircle className="w-4 h-4 inline mr-2" />
            AI Chat
          </button>
        </div>

        {/* AI Prompts Tab */}
        {activeTab === 'ai-prompts' && (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Professional Trading Prompts</h3>
              <p className="text-gray-600">9 proven ChatGPT prompts from trading experts - click any prompt to customize and copy</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {aiPrompts.map((prompt) => (
                <motion.div
                  key={prompt.id}
                  whileHover={{ scale: 1.02 }}
                  className="bg-gray-50 rounded-lg p-4 cursor-pointer hover:bg-gray-100 transition-colors border border-gray-200"
                  onClick={() => setSelectedPrompt(prompt)}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 text-sm mb-1">{prompt.title}</h4>
                      <span className={`inline-block px-2 py-1 text-xs rounded-full ${getCategoryColor(prompt.category)}`}>
                        {prompt.category}
                      </span>
                    </div>
                    <Brain className="w-5 h-5 text-blue-600 flex-shrink-0 ml-2" />
                  </div>
                  <p className="text-gray-600 text-sm mb-3">{prompt.description}</p>
                  <div className="text-xs text-gray-500 mb-2">
                    <strong>What it provides:</strong> {prompt.detailed_info}
                  </div>
                  <div className="text-xs text-blue-600">
                    <strong>Example:</strong> {prompt.example_usage}
                  </div>
                  <div className="mt-3 flex items-center justify-between">
                    <span className="text-xs text-gray-500">
                      {prompt.variables.length} variable{prompt.variables.length !== 1 ? 's' : ''}
                    </span>
                    <span className="text-xs text-blue-600 font-medium">Click to customize →</span>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Selected Prompt Details */}
            {selectedPrompt && (
              <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-lg font-semibold text-gray-900">{selectedPrompt.title}</h4>
                  <button
                    onClick={() => setSelectedPrompt(null)}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    ✕
                  </button>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <h5 className="font-medium text-gray-900 mb-2">Customize Variables:</h5>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {selectedPrompt.variables.map((variable: string) => (
                        <div key={variable}>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            {variable.replace('_', ' ').toUpperCase()}
                          </label>
                          <input
                            type="text"
                            value={promptVariables[variable] || ''}
                            onChange={(e) => setPromptVariables(prev => ({
                              ...prev,
                              [variable]: e.target.value
                            }))}
                            placeholder={`Enter ${variable.replace('_', ' ')}`}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex space-x-3">
                    <button
                      onClick={generateAIPrompt}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2"
                    >
                      <Lightbulb className="w-4 h-4" />
                      <span>Generate Prompt</span>
                    </button>
                    {generatedPrompt && (
                      <button
                        onClick={() => copyToClipboard(generatedPrompt)}
                        className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center space-x-2"
                      >
                        <Copy className="w-4 h-4" />
                        <span>Copy to Clipboard</span>
                      </button>
                    )}
                  </div>

                  {generatedPrompt && (
                    <div>
                      <h5 className="font-medium text-gray-900 mb-2">Generated Prompt:</h5>
                      <div className="bg-white p-4 rounded-md border border-gray-300 max-h-64 overflow-y-auto">
                        <pre className="whitespace-pre-wrap text-sm text-gray-800">{generatedPrompt}</pre>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Chat Tab */}
        {activeTab === 'chat' && (
          <div>
            <div className="text-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">AI Strategy Generator</h3>
              <p className="text-gray-600">Describe your strategy idea and get complete Python code</p>
            </div>
            
            {/* Chat Messages */}
            <div className="h-96 overflow-y-auto mb-4 p-4 bg-gray-50 rounded-lg">
              <div className="space-y-4">
                {chatMessages.map((message) => (
                  <div key={message.id} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-3xl ${message.role === 'user' ? 'bg-blue-600 text-white' : 'bg-white border border-gray-200'} rounded-lg p-4`}>
                      <div className="flex items-start space-x-3">
                        <div className={`p-1 rounded-full ${message.role === 'user' ? 'bg-blue-500' : 'bg-gray-100'}`}>
                          {message.role === 'user' ? 
                            <User className="w-4 h-4 text-white" /> : 
                            <Bot className="w-4 h-4 text-gray-600" />
                          }
                        </div>
                        <div className="flex-1">
                          <div className="whitespace-pre-wrap text-sm">{message.content}</div>
                          {message.code && (
                            <div className="mt-3">
                              <div className="bg-gray-900 text-green-400 p-4 rounded-lg text-sm font-mono overflow-x-auto">
                                <pre>{message.code}</pre>
                              </div>
                              <div className="flex space-x-2 mt-3">
                                <button className="flex items-center space-x-1 px-3 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700">
                                  <Save className="w-3 h-3" />
                                  <span>Save</span>
                                </button>
                                <button className="flex items-center space-x-1 px-3 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700">
                                  <TestTube className="w-3 h-3" />
                                  <span>Test</span>
                                </button>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
                {isLoading && (
                  <div className="flex justify-start">
                    <div className="bg-white border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center space-x-3">
                        <div className="p-1 rounded-full bg-gray-100">
                          <Bot className="w-4 h-4 text-gray-600" />
                        </div>
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Chat Input */}
            <form onSubmit={handleChatSubmit} className="flex space-x-3">
              <input
                type="text"
                value={chatInput}
                onChange={(e) => setChatInput(e.target.value)}
                placeholder="Describe your trading strategy idea..."
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isLoading}
              />
              <button
                type="submit"
                disabled={isLoading || !chatInput.trim()}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              >
                <Send className="w-4 h-4" />
                <span>Send</span>
              </button>
            </form>
          </div>
        )}
      </div>
    </div>
  );
};

export default SimpleStrategyHelper;