from typing import Dict

class PatternReport:
    """Generate user-friendly reports from pattern analysis"""
    
    @staticmethod
    def generate_report(analysis: Dict) -> str:
        """Create a formatted report from pattern analysis"""
        
        pattern_emoji = {
            'mean_reversion': '↕️',
            'momentum': '📈',
            'breakout': '🚀',
            'price_action': '📊',
            'mixed': '🔀',
            'custom': '🔧'
        }
        
        pattern_descriptions = {
            'mean_reversion': 'Your strategy buys low and sells high, expecting prices to return to average',
            'momentum': 'Your strategy follows trends, buying strength and selling weakness',
            'breakout': 'Your strategy trades on price breaking through key support/resistance levels',
            'price_action': 'Your strategy uses pure price movement without technical indicators',
            'mixed': 'Your strategy combines multiple approaches',
            'custom': 'Your strategy uses a unique approach'
        }
        
        # Build report
        report = f"""
╔══════════════════════════════════════════════════════╗
║          STRATEGY PATTERN ANALYSIS REPORT            ║
╚══════════════════════════════════════════════════════╝

Pattern Type: {pattern_emoji.get(analysis['pattern_type'], '❓')} {analysis['pattern_type'].upper().replace('_', ' ')}
Confidence: {'█' * int(analysis['confidence'] * 10)}{'░' * (10 - int(analysis['confidence'] * 10))} {analysis['confidence']:.0%}

📝 Description:
{pattern_descriptions.get(analysis['pattern_type'], 'Custom strategy pattern')}

📊 Technical Indicators Found: {len(analysis['indicators_found'])}
{' • ' + chr(10).join(analysis['indicators_found']) if analysis['indicators_found'] else ' • None (Price action only)'}

🎯 Key Characteristics:
{' • ' + chr(10).join(analysis['characteristics']) if analysis['characteristics'] else ' • Simple strategy'}

🔧 Complexity Score: {'█' * int(analysis.get('complexity_score', 0) * 10)}{'░' * (10 - int(analysis.get('complexity_score', 0) * 10))} {analysis.get('complexity_score', 0):.0%}

"""
        
        if analysis['warnings']:
            report += f"""⚠️  Warnings & Suggestions:
{' • ' + chr(10).join(analysis['warnings'])}

"""
        
        # Add pattern-specific tips
        tips = PatternReport._get_pattern_tips(analysis['pattern_type'])
        if tips:
            report += f"""💡 Tips for {analysis['pattern_type'].replace('_', ' ').title()} Strategies:
{tips}

"""
        
        # Add market condition recommendations
        market_conditions = PatternReport._get_market_conditions(analysis['pattern_type'])
        if market_conditions:
            report += f"""🌍 Optimal Market Conditions:
{market_conditions}

"""
        
        return report
    
    @staticmethod
    def _get_pattern_tips(pattern_type: str) -> str:
        """Get tips specific to each pattern type"""
        tips = {
            'mean_reversion': """• Works best in sideways/ranging markets
• Consider adding volatility filters (ATR)
• Use wider stops in trending markets
• Test with different reversion thresholds""",
            
            'momentum': """• Add trend strength filters (ADX)
• Consider multiple timeframe confirmation
• Use trailing stops to capture trends
• Avoid trading in choppy markets""",
            
            'breakout': """• Confirm breakouts with volume spikes
• Use tight stops below breakout levels
• Consider false breakout filters
• Test on different volatility regimes""",
            
            'price_action': """• Simple can be effective!
• Consider adding volume confirmation
• Test on different timeframes
• May benefit from market regime filters""",
            
            'mixed': """• Consider simplifying to one main approach
• Test each component separately
• Ensure signals don't conflict
• Document your logic clearly""",
            
            'custom': """• Keep testing and refining your approach
• Document your strategy logic clearly
• Consider adding risk management
• Backtest on different market conditions"""
        }
        
        return tips.get(pattern_type, "• Keep testing and refining your approach")
    
    @staticmethod
    def _get_market_conditions(pattern_type: str) -> str:
        """Get optimal market conditions for each pattern type"""
        conditions = {
            'mean_reversion': """• Sideways/ranging markets (low trend strength)
• High volatility environments
• Markets with clear support/resistance levels
• Avoid during strong trending periods""",
            
            'momentum': """• Strong trending markets (up or down)
• Markets with clear directional bias
• High volume confirmation periods
• Avoid during consolidation phases""",
            
            'breakout': """• Markets approaching key levels
• High volatility expansion periods
• News-driven or event-based moves
• Avoid during low volatility periods""",
            
            'price_action': """• Any market condition (versatile)
• Especially effective in liquid markets
• Works across different timeframes
• Requires good market timing skills""",
            
            'mixed': """• Depends on component strategies
• May work in various conditions
• Requires careful parameter tuning
• Test each component separately""",
            
            'custom': """• Depends on your specific approach
• Test across different market regimes
• Consider seasonal patterns
• Monitor performance regularly"""
        }
        
        return conditions.get(pattern_type, "• Test across different market conditions")
    
    @staticmethod
    def generate_simple_summary(analysis: Dict) -> str:
        """Generate a simple one-line summary"""
        pattern_type = analysis['pattern_type'].replace('_', ' ').title()
        confidence = analysis['confidence']
        indicators_count = len(analysis['indicators_found'])
        
        if indicators_count == 0:
            indicator_text = "using price action only"
        elif indicators_count == 1:
            indicator_text = f"using {analysis['indicators_found'][0].upper()}"
        else:
            indicator_text = f"using {indicators_count} indicators"
        
        return f"{pattern_type} strategy {indicator_text} (confidence: {confidence:.0%})"