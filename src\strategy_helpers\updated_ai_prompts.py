"""
Updated AI Trading Prompts Library
Based on the 9 proven prompts from promptadvance.club/blog/chat-gpt-prompts-for-trading
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import re

class PromptCategory(Enum):
    MARKET_ANALYSIS = "market_analysis"
    TECHNICAL_ANALYSIS = "technical_analysis"
    TRADE_EXECUTION = "trade_execution"
    TRADE_JOURNALING = "trade_journaling"
    PERFORMANCE_REVIEW = "performance_review"
    RESEARCH = "research"
    PSYCHOLOGY = "psychology"
    LEARNING = "learning"
    BACKTESTING = "backtesting"

@dataclass
class TradingPrompt:
    id: str
    category: PromptCategory
    title: str
    description: str
    prompt_template: str
    variables: List[str]
    example_usage: Optional[str] = None
    expected_output: Optional[str] = None

class UpdatedAITradingPromptsLibrary:
    """
    Updated AI Trading Prompts Library with the exact 9 prompts from promptadvance.club
    """
    
    def __init__(self):
        # Initialize with the exact 9 proven trading prompts from promptadvance.club
        self.prompts = {
            # 1. Market Analysis
            "market_analysis": TradingPrompt(
                id="market_analysis",
                category=PromptCategory.MARKET_ANALYSIS,
                title="Market Analysis & Asset Scanner",
                description="Identify trading assets that meet specific criteria",
                prompt_template="Act as a day trading assistant. Your task is to identify trading assets that meet the specified {criteria}. Utilize your expertise and available market analysis tools to scan, filter, and evaluate potential assets for trading. Once identified, create a comprehensive list with supporting data for each asset, indicating why it meets the criteria. Ensure that all information is up-to-date and relevant to the current market conditions.",
                variables=["criteria"],
                example_usage="criteria: 'tech stocks on the rise'",
                expected_output="Comprehensive list of assets with supporting data and rationale"
            ),
            
            # 2. Technical Analysis
            "technical_analysis": TradingPrompt(
                id="technical_analysis",
                category=PromptCategory.TECHNICAL_ANALYSIS,
                title="Comprehensive Technical Analysis",
                description="Analyze price and volume patterns to identify trading opportunities",
                prompt_template="Act as an experienced day trader. Your objective is to analyze the price and volume patterns of {trading_asset} to identify potential buying or selling opportunities. Utilize advanced charting tools and technical indicators to scrutinize both short-term and long-term patterns, taking into account historical data and recent market movements. Assess the correlation between price and volume to gauge the strength or weakness of a particular price trend. Provide a comprehensive analysis report that details potential breakout or breakdown points, support and resistance levels, and any anomalies or divergences noticed. Your analysis should be backed by logical reasoning and should include potential risk and reward scenarios. Always adhere to best practices in technical analysis and maintain the highest standards of accuracy and objectivity.",
                variables=["trading_asset"],
                example_usage="trading_asset: 'NVIDIA (NVDA)'",
                expected_output="Detailed technical analysis report with entry/exit points and risk scenarios"
            ),
            
            # 3. Trade Execution
            "trade_execution": TradingPrompt(
                id="trade_execution",
                category=PromptCategory.TRADE_EXECUTION,
                title="Optimal Trade Execution Strategy",
                description="Determine optimal entry, stop-loss, and target points for trades",
                prompt_template="Act as an experienced day trader. Based on your comprehensive analysis of current market conditions, historical data, and emerging trends, decide on optimal entry, stop-loss, and target points for a specified trading asset: {trading_asset}. Begin by thoroughly reviewing recent price action, key technical indicators, and relevant news that might influence the asset's direction. Consider market volatility, liquidity, and any upcoming events that could impact price movement. Provide specific price levels for entry, stop-loss, and profit targets, along with the reasoning behind each decision. Include risk-reward ratios and position sizing recommendations.",
                variables=["trading_asset"],
                example_usage="trading_asset: 'NVDA'",
                expected_output="Specific entry, stop-loss, and target levels with detailed reasoning"
            ),
            
            # 4. Trade Journaling
            "trade_journaling": TradingPrompt(
                id="trade_journaling",
                category=PromptCategory.TRADE_JOURNALING,
                title="Comprehensive Trade Journal System",
                description="Create and maintain a systematic trade journaling system",
                prompt_template="Act as an experienced day trader and take on the responsibility of documenting the details and outcomes of my trades in a meticulous and systematic manner. Your primary objective is to establish a structured format for the trade journal. This format should capture essential details such as the date and time of entry and exit, trade direction (be it long or short), the specific instrument or asset traded, entry and exit prices, the volume or size of the trade, initial risk and target levels, the strategic rationale behind the trade, the actual profit or loss outcome, and any pertinent notes or observations made during the trade. Furthermore, you will offer guidance on the nuances of effective trade journaling, emphasizing the significance of maintaining consistent documentation, the recommended frequency for journal updates, and strategies to prevent biases or post-trade rationalizations. It's also crucial that you suggest key metrics to monitor in order to assess trade performance, such as the win rate percentage, risk-reward ratio, comparisons of average profit and loss per trade, maximum drawdowns, and overall profitability. Lastly, share insights into how one can proactively review and glean lessons from past trades.",
                variables=[],
                example_usage="Create a comprehensive trade journal template",
                expected_output="Structured trade journal format with performance metrics and review guidelines"
            ),
            
            # 5. Performance Review
            "performance_review": TradingPrompt(
                id="performance_review",
                category=PromptCategory.PERFORMANCE_REVIEW,
                title="Trading Performance Analysis",
                description="Analyze and review trading performance for improvement",
                prompt_template="Act as a professional trading performance analyst. Review my trading performance data for the period {time_period} and provide a comprehensive analysis. Analyze key metrics including win rate, average profit/loss per trade, risk-reward ratios, maximum drawdown, and overall profitability. Identify patterns in winning and losing trades, assess the effectiveness of different strategies used, and highlight areas for improvement. Provide specific recommendations for optimizing performance, including adjustments to risk management, strategy selection, and execution timing. Include both quantitative analysis and qualitative insights based on market conditions during the review period.",
                variables=["time_period"],
                example_usage="time_period: 'last 3 months'",
                expected_output="Detailed performance analysis with specific improvement recommendations"
            ),
            
            # 6. Research Assistant
            "research_assistant": TradingPrompt(
                id="research_assistant",
                category=PromptCategory.RESEARCH,
                title="Market Research & Analysis",
                description="Conduct comprehensive market research and analysis",
                prompt_template="Act as a financial research analyst specializing in {market_sector}. Conduct comprehensive research and analysis on current market conditions, emerging trends, and potential trading opportunities. Analyze fundamental factors, technical indicators, market sentiment, and macroeconomic influences that could impact asset prices. Provide insights on sector rotation, institutional flows, and key events to watch. Include both short-term trading opportunities and longer-term investment themes. Present your findings in a structured report with actionable insights and risk considerations.",
                variables=["market_sector"],
                example_usage="market_sector: 'technology and AI stocks'",
                expected_output="Comprehensive market research report with trading opportunities and risk analysis"
            ),
            
            # 7. Trading Psychology
            "trading_psychology": TradingPrompt(
                id="trading_psychology",
                category=PromptCategory.PSYCHOLOGY,
                title="Trading Psychology Coach",
                description="Improve trading psychology and emotional discipline",
                prompt_template="Act as a trading psychology coach with expertise in behavioral finance. Help me address the psychological challenge: {psychological_issue}. Provide practical strategies for managing emotions, maintaining discipline, and avoiding common psychological pitfalls in trading. Include techniques for handling stress, fear, greed, and overconfidence. Offer specific exercises or routines that can be implemented before, during, and after trading sessions. Explain the psychological principles behind successful trading and how to develop a winning mindset. Provide actionable advice that can be immediately applied to improve trading performance through better emotional control.",
                variables=["psychological_issue"],
                example_usage="psychological_issue: 'fear of missing out (FOMO) leading to impulsive trades'",
                expected_output="Practical psychology strategies and exercises for better trading discipline"
            ),
            
            # 8. Learning & Education
            "learning_education": TradingPrompt(
                id="learning_education",
                category=PromptCategory.LEARNING,
                title="Trading Education & Skill Development",
                description="Provide structured learning and skill development guidance",
                prompt_template="Act as an expert trading educator. Create a comprehensive learning plan for {learning_topic} tailored to my current skill level: {skill_level}. Structure the learning path with clear milestones, practical exercises, and recommended resources. Include both theoretical concepts and hands-on practice opportunities. Explain complex topics in an accessible way with real-world examples and case studies. Provide assessment criteria to measure progress and suggest next steps for continued development. Include common mistakes to avoid and best practices to follow. Make the learning engaging and actionable with specific steps I can take immediately.",
                variables=["learning_topic", "skill_level"],
                example_usage="learning_topic: 'options trading strategies', skill_level: 'intermediate'",
                expected_output="Structured learning plan with milestones, exercises, and resources"
            ),
            
            # 9. Backtesting & Strategy Validation
            "backtesting_validation": TradingPrompt(
                id="backtesting_validation",
                category=PromptCategory.BACKTESTING,
                title="Strategy Backtesting & Validation",
                description="Backtest and validate trading strategies systematically",
                prompt_template="Act as a quantitative analyst specializing in strategy backtesting. Help me backtest and validate the trading strategy: {strategy_description}. Design a comprehensive backtesting framework that includes proper data selection, time periods, market conditions, and performance metrics. Explain how to avoid common backtesting pitfalls such as look-ahead bias, survivorship bias, and overfitting. Provide guidance on walk-forward analysis, out-of-sample testing, and stress testing under different market conditions. Include statistical significance testing and risk-adjusted performance measures. Suggest improvements to the strategy based on backtesting results and provide recommendations for live implementation.",
                variables=["strategy_description"],
                example_usage="strategy_description: 'RSI mean reversion strategy with Bollinger Bands confirmation'",
                expected_output="Comprehensive backtesting framework with validation methodology and results analysis"
            )
        }
    
    def get_prompt(self, prompt_id: str) -> Optional[TradingPrompt]:
        """Get a specific prompt by ID"""
        return self.prompts.get(prompt_id)
    
    def list_all_prompts(self) -> List[TradingPrompt]:
        """Get all available prompts"""
        return list(self.prompts.values())
    
    def get_prompts_by_category(self, category: PromptCategory) -> List[TradingPrompt]:
        """Get prompts by category"""
        return [prompt for prompt in self.prompts.values() if prompt.category == category]
    
    def format_prompt(self, prompt_id: str, **kwargs) -> Optional[str]:
        """Format a prompt with provided variables"""
        prompt = self.get_prompt(prompt_id)
        if not prompt:
            return None
        
        try:
            return prompt.prompt_template.format(**kwargs)
        except KeyError as e:
            print(f"Missing variable {e} for prompt {prompt_id}")
            return None
    
    def search_prompts(self, query: str) -> List[TradingPrompt]:
        """Search prompts by title, description, or content"""
        query_lower = query.lower()
        results = []
        
        for prompt in self.prompts.values():
            if (query_lower in prompt.title.lower() or 
                query_lower in prompt.description.lower() or
                query_lower in prompt.prompt_template.lower()):
                results.append(prompt)
        
        return results
    
    def get_prompt_categories(self) -> List[PromptCategory]:
        """Get all available categories"""
        return list(PromptCategory)
    
    def get_category_summary(self) -> Dict[str, int]:
        """Get summary of prompts per category"""
        summary = {}
        for category in PromptCategory:
            count = len(self.get_prompts_by_category(category))
            summary[category.value] = count
        return summary

# Example usage
if __name__ == "__main__":
    # Initialize the updated library
    prompts_lib = UpdatedAITradingPromptsLibrary()
    
    print("🧠 Updated AI Trading Prompts Library")
    print("=" * 50)
    print(f"Total prompts: {len(prompts_lib.list_all_prompts())}")
    
    # Show all categories
    print("\n📂 Categories:")
    for category in PromptCategory:
        count = len(prompts_lib.get_prompts_by_category(category))
        print(f"  {category.value.replace('_', ' ').title()}: {count} prompts")
    
    # Example: Format a prompt
    print("\n🎯 Example - Market Analysis Prompt:")
    formatted = prompts_lib.format_prompt(
        "market_analysis",
        criteria="AI and technology stocks with strong momentum"
    )
    if formatted:
        print(formatted[:200] + "...")
    
    # Show all prompt titles
    print("\n📋 All Available Prompts:")
    for i, prompt in enumerate(prompts_lib.list_all_prompts(), 1):
        print(f"  {i}. {prompt.title} ({prompt.category.value})")