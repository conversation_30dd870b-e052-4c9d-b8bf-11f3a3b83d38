# tests/test_mt5_bridge_integration.py
"""
Integration tests for MT5 Bridge with trading strategies
"""

import pytest
import logging
from unittest.mock import patch
from datetime import datetime, timedelta

# Import the MT5 bridge implementation
from src.trading.mt5_bridge_tdd import MT5Bridge


class SimpleStrategy:
    """
    Simple trading strategy for testing MT5 Bridge integration
    """
    
    def __init__(self, bridge):
        """
        Initialize strategy with MT5 Bridge
        
        Args:
            bridge: MT5 Bridge instance
        """
        self.bridge = bridge
        self.orders = []
        self.logger = logging.getLogger("SimpleStrategy")
    
    def execute(self, symbol, signal, lot_size=0.1):
        """
        Execute trading strategy based on signal
        
        Args:
            symbol: Trading symbol (e.g., "EURUSD")
            signal: Trading signal ("BUY", "SELL", "CLOSE")
            lot_size: Order size
            
        Returns:
            dict: Result of strategy execution
        """
        self.logger.info(f"Executing strategy: {signal} {lot_size} {symbol}")
        
        if signal == "CLOSE":
            return self._close_positions(symbol)
        
        # Place order based on signal
        try:
            order_id = self.bridge.place_order(
                symbol=symbol,
                order_type=signal,
                lot=lot_size
            )
            
            self.orders.append(order_id)
            
            return {
                "success": True,
                "order_id": order_id,
                "action": signal,
                "symbol": symbol,
                "lot": lot_size
            }
            
        except Exception as e:
            self.logger.error(f"Strategy execution error: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "action": signal,
                "symbol": symbol
            }
    
    def _close_positions(self, symbol=None):
        """
        Close positions for a symbol or all positions
        
        Args:
            symbol: Trading symbol or None for all positions
            
        Returns:
            dict: Result of closing positions
        """
        positions = self.bridge.get_positions()
        
        if symbol:
            positions = [p for p in positions if p["symbol"] == symbol]
        
        if not positions:
            return {
                "success": True,
                "message": "No positions to close",
                "closed_count": 0
            }
        
        closed_count = 0
        for position in positions:
            if self.bridge.close_order(position["id"]):
                closed_count += 1
        
        return {
            "success": True,
            "message": f"Closed {closed_count} positions",
            "closed_count": closed_count
        }


@pytest.mark.integration
class TestMT5BridgeIntegration:
    """
    Integration tests for MT5 Bridge with trading strategies
    """
    
    def setup_method(self):
        """Setup for each test method"""
        self.bridge = MT5Bridge(offline_mode=True)
        self.strategy = SimpleStrategy(self.bridge)
    
    def test_strategy_buy_signal(self):
        """Test strategy execution with BUY signal"""
        # Execute strategy with BUY signal
        result = self.strategy.execute("EURUSD", "BUY", 0.1)
        
        # Verify result
        assert result["success"] is True
        assert result["order_id"] > 0
        assert result["action"] == "BUY"
        assert result["symbol"] == "EURUSD"
        assert result["lot"] == 0.1
        
        # Verify position was created
        positions = self.bridge.get_positions()
        assert len(positions) == 1
        assert positions[0]["symbol"] == "EURUSD"
        assert positions[0]["type"] == "BUY"
        assert positions[0]["lot"] == 0.1
    
    def test_strategy_sell_signal(self):
        """Test strategy execution with SELL signal"""
        # Execute strategy with SELL signal
        result = self.strategy.execute("GBPUSD", "SELL", 0.2)
        
        # Verify result
        assert result["success"] is True
        assert result["order_id"] > 0
        assert result["action"] == "SELL"
        assert result["symbol"] == "GBPUSD"
        assert result["lot"] == 0.2
        
        # Verify position was created
        positions = self.bridge.get_positions()
        assert len(positions) == 1
        assert positions[0]["symbol"] == "GBPUSD"
        assert positions[0]["type"] == "SELL"
        assert positions[0]["lot"] == 0.2
    
    def test_strategy_close_positions(self):
        """Test strategy execution with CLOSE signal"""
        # Place some orders first
        self.strategy.execute("EURUSD", "BUY", 0.1)
        self.strategy.execute("GBPUSD", "SELL", 0.2)
        
        # Verify positions were created
        positions = self.bridge.get_positions()
        assert len(positions) == 2
        
        # Close positions for EURUSD
        result = self.strategy.execute("EURUSD", "CLOSE")
        
        # Verify result
        assert result["success"] is True
        assert result["closed_count"] == 1
        
        # Verify only EURUSD position was closed
        positions = self.bridge.get_positions()
        assert len(positions) == 1
        assert positions[0]["symbol"] == "GBPUSD"
        
        # Close all remaining positions
        result = self.strategy.execute(None, "CLOSE")
        
        # Verify result
        assert result["success"] is True
        assert result["closed_count"] == 1
        
        # Verify all positions were closed
        positions = self.bridge.get_positions()
        assert len(positions) == 0
    
    def test_strategy_with_multiple_orders(self):
        """Test strategy execution with multiple orders"""
        # Execute strategy multiple times
        symbols = ["EURUSD", "GBPUSD", "USDJPY"]
        signals = ["BUY", "SELL", "BUY"]
        lot_sizes = [0.1, 0.2, 0.3]
        
        for symbol, signal, lot in zip(symbols, signals, lot_sizes):
            result = self.strategy.execute(symbol, signal, lot)
            assert result["success"] is True
        
        # Verify positions were created
        positions = self.bridge.get_positions()
        assert len(positions) == 3
        
        # Verify each position
        for i, (symbol, signal, lot) in enumerate(zip(symbols, signals, lot_sizes)):
            position = next((p for p in positions if p["symbol"] == symbol), None)
            assert position is not None
            assert position["type"] == signal
            assert position["lot"] == lot
    
    @patch("src.trading.mt5_bridge_tdd.mt5")
    def test_strategy_with_api_error(self, mock_mt5):
        """Test strategy execution with API error"""
        # Configure mock to simulate error
        mock_mt5.order_send.return_value = None
        mock_mt5.last_error.return_value = (10013, "Invalid request")
        
        # Create bridge with mocked MT5
        bridge = MT5Bridge(offline_mode=False)
        strategy = SimpleStrategy(bridge)
        
        # Execute strategy
        result = strategy.execute("EURUSD", "BUY", 0.1)
        
        # Verify result
        assert result["success"] is False
        assert "error" in result
        assert "Invalid request" in result["error"]