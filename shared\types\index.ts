/**
 * Shared TypeScript type definitions
 */

// Re-export all schema types
export * from '../schemas';

// Common utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & globalThis.Required<Pick<T, K>>;
export type Nullable<T> = T | null;
export type Maybe<T> = T | undefined;

// Database types
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface SoftDeleteEntity extends BaseEntity {
  deletedAt?: Date;
}

// Service response types
export interface ServiceError {
  code: string;
  message: string;
  details?: string;
}

export interface ServiceResponse<T = void> {
  success: boolean;
  data?: T;
  error?: ServiceError;
}

// Repository types
export interface Repository<T extends BaseEntity> {
  findById(id: string): Promise<T | null>;
  findMany(filters?: Record<string, any>): Promise<T[]>;
  create(data: Omit<T, keyof BaseEntity>): Promise<T>;
  update(id: string, data: Partial<Omit<T, keyof BaseEntity>>): Promise<T | null>;
  delete(id: string): Promise<boolean>;
}

// HTTP types
export interface AuthenticatedRequest extends Express.Request {
  user?: import('../schemas').User;
}

// Logging types
export interface Logger {
  error(message: string, meta?: Record<string, any>): void;
  warn(message: string, meta?: Record<string, any>): void;
  info(message: string, meta?: Record<string, any>): void;
  debug(message: string, meta?: Record<string, any>): void;
  trace(message: string, meta?: Record<string, any>): void;
}

// Configuration types
export interface AppConfig {
  port: number;
  nodeEnv: 'development' | 'production' | 'test';
  corsOrigins: string[];
  jwt: {
    secret: string;
    accessTokenExpiry: string;
    refreshTokenExpiry: string;
  };
  database: {
    client: string;
    connection: {
      host: string;
      port: number;
      user: string;
      password: string;
      database: string;
    };
    pool: {
      min: number;
      max: number;
    };
  };
  bcrypt: {
    saltRounds: number;
  };
}

// Event types
export interface DomainEvent {
  id: string;
  type: string;
  payload: Record<string, any>;
  timestamp: Date;
  version: number;
}

// Cache types
export interface CacheOptions {
  ttl?: number; // Time to live in seconds
  prefix?: string;
}

export interface CacheService {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T, options?: CacheOptions): Promise<void>;
  delete(key: string): Promise<void>;
  clear(pattern?: string): Promise<void>;
}