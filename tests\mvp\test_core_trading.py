#!/usr/bin/env python3
"""
MVP Core Trading Tests - TDD Approach

This module contains focused tests for the core trading functionality needed for <PERSON>:
1. Basic order placement (buy/sell)
2. Portfolio balance calculation
3. Risk management basics
4. Order validation

Following TDD principles:
- Write tests first
- Keep tests simple and focused
- Test one thing at a time
- Use descriptive test names
"""

import pytest
import logging
from decimal import Decimal
from typing import Dict, List, Any

# Configure logging
logger = logging.getLogger(__name__)

# Import the core trading components
try:
    from src.trading.mt5_bridge_tdd import MT5Bridge
except ImportError:
    logger.warning("MT5Bridge not found, using mock implementation")
    # Mock implementation for testing
    class MT5Bridge:
        def __init__(self, offline_mode=True):
            self.offline_mode = offline_mode
            self.connected = True
            self.orders = []
            self.balance = 10000.0
            
        def connect(self):
            self.connected = True
            return True
            
        def is_connected(self):
            return self.connected
            
        def place_order(self, symbol, order_type, lot, price=None, stop_loss=None, take_profit=None):
            if symbol == "INVALID":
                raise ValueError("Invalid symbol")
            if lot <= 0:
                raise ValueError("Invalid lot size")
                
            order_id = len(self.orders) + 1
            self.orders.append({
                "id": order_id,
                "symbol": symbol,
                "type": order_type,
                "lot": lot,
                "price": price,
                "status": "filled"
            })
            return order_id
            
        def get_order_status(self, order_id):
            for order in self.orders:
                if order["id"] == order_id:
                    return order["status"]
            return "not_found"
            
        def get_positions(self):
            return [order for order in self.orders if order["status"] == "filled"]
            
        def close_order(self, order_id):
            for order in self.orders:
                if order["id"] == order_id:
                    order["status"] = "closed"
                    return True
            return False
            
        def disconnect(self):
            self.connected = False


class TestBasicOrderPlacement:
    """Test basic buy/sell order functionality"""
    
    @pytest.fixture
    def trading_bridge(self):
        """Create a trading bridge for testing"""
        bridge = MT5Bridge(offline_mode=True)
        bridge.connect()
        return bridge
    
    def test_place_buy_order(self, trading_bridge):
        """Test placing a basic buy order"""
        # Given: A connected trading bridge
        assert trading_bridge.is_connected()
        
        # When: Placing a buy order
        order_id = trading_bridge.place_order(
            symbol="EURUSD",
            order_type="BUY",
            lot=0.1
        )
        
        # Then: Order should be placed successfully
        assert order_id > 0
        assert trading_bridge.get_order_status(order_id) == "filled"
    
    def test_place_sell_order(self, trading_bridge):
        """Test placing a basic sell order"""
        # Given: A connected trading bridge
        assert trading_bridge.is_connected()
        
        # When: Placing a sell order
        order_id = trading_bridge.place_order(
            symbol="GBPUSD",
            order_type="SELL",
            lot=0.2
        )
        
        # Then: Order should be placed successfully
        assert order_id > 0
        assert trading_bridge.get_order_status(order_id) == "filled"
    
    def test_place_multiple_orders(self, trading_bridge):
        """Test placing multiple orders"""
        # Given: A connected trading bridge
        orders = []
        
        # When: Placing multiple orders
        for i, (symbol, order_type, lot) in enumerate([
            ("EURUSD", "BUY", 0.1),
            ("GBPUSD", "SELL", 0.2),
            ("USDJPY", "BUY", 0.3)
        ]):
            order_id = trading_bridge.place_order(
                symbol=symbol,
                order_type=order_type,
                lot=lot
            )
            orders.append(order_id)
        
        # Then: All orders should be placed successfully
        assert len(orders) == 3
        for order_id in orders:
            assert trading_bridge.get_order_status(order_id) == "filled"


class TestPortfolioBalance:
    """Test portfolio balance calculation"""
    
    @pytest.fixture
    def trading_bridge(self):
        """Create a trading bridge for testing"""
        bridge = MT5Bridge(offline_mode=True)
        bridge.connect()
        return bridge
    
    def test_empty_portfolio(self, trading_bridge):
        """Test portfolio with no positions"""
        # Given: A trading bridge with no positions
        positions = trading_bridge.get_positions()
        
        # Then: Portfolio should be empty
        assert len(positions) == 0
    
    def test_single_position_portfolio(self, trading_bridge):
        """Test portfolio with single position"""
        # Given: A trading bridge
        # When: Placing one order
        order_id = trading_bridge.place_order(
            symbol="EURUSD",
            order_type="BUY",
            lot=0.1
        )
        
        # Then: Portfolio should contain one position
        positions = trading_bridge.get_positions()
        assert len(positions) == 1
        
        position = positions[0]
        assert position["symbol"] == "EURUSD"
        assert position["type"] == "BUY"
        assert position["lot"] == 0.1
    
    def test_multiple_positions_portfolio(self, trading_bridge):
        """Test portfolio with multiple positions"""
        # Given: A trading bridge
        expected_positions = [
            ("EURUSD", "BUY", 0.1),
            ("GBPUSD", "SELL", 0.2),
            ("USDJPY", "BUY", 0.3)
        ]
        
        # When: Placing multiple orders
        order_ids = []
        for symbol, order_type, lot in expected_positions:
            order_id = trading_bridge.place_order(
                symbol=symbol,
                order_type=order_type,
                lot=lot
            )
            order_ids.append(order_id)
        
        # Then: Portfolio should contain all positions
        positions = trading_bridge.get_positions()
        assert len(positions) == 3
        
        # Verify each position
        for i, (expected_symbol, expected_type, expected_lot) in enumerate(expected_positions):
            position = next((p for p in positions if p["id"] == order_ids[i]), None)
            assert position is not None
            assert position["symbol"] == expected_symbol
            assert position["type"] == expected_type
            assert position["lot"] == expected_lot
    
    def test_portfolio_after_closing_positions(self, trading_bridge):
        """Test portfolio after closing some positions"""
        # Given: A trading bridge with multiple positions
        order_ids = []
        for symbol, order_type, lot in [("EURUSD", "BUY", 0.1), ("GBPUSD", "SELL", 0.2)]:
            order_id = trading_bridge.place_order(
                symbol=symbol,
                order_type=order_type,
                lot=lot
            )
            order_ids.append(order_id)
        
        # When: Closing one position
        trading_bridge.close_order(order_ids[0])
        
        # Then: Portfolio should contain only open positions
        positions = trading_bridge.get_positions()
        assert len(positions) == 1
        assert positions[0]["id"] == order_ids[1]


class TestRiskManagement:
    """Test basic risk limits"""
    
    @pytest.fixture
    def trading_bridge(self):
        """Create a trading bridge for testing"""
        bridge = MT5Bridge(offline_mode=True)
        bridge.connect()
        return bridge
    
    def test_valid_lot_size(self, trading_bridge):
        """Test placing order with valid lot size"""
        # Given: A trading bridge
        # When: Placing order with valid lot size
        order_id = trading_bridge.place_order(
            symbol="EURUSD",
            order_type="BUY",
            lot=0.1
        )
        
        # Then: Order should be placed successfully
        assert order_id > 0
        assert trading_bridge.get_order_status(order_id) == "filled"
    
    def test_maximum_lot_size(self, trading_bridge):
        """Test placing order with maximum allowed lot size"""
        # Given: A trading bridge
        max_lot_size = 1.0
        
        # When: Placing order with maximum lot size
        order_id = trading_bridge.place_order(
            symbol="EURUSD",
            order_type="BUY",
            lot=max_lot_size
        )
        
        # Then: Order should be placed successfully
        assert order_id > 0
        assert trading_bridge.get_order_status(order_id) == "filled"
    
    def test_position_count_limit(self, trading_bridge):
        """Test position count limits"""
        # Given: A trading bridge
        max_positions = 5
        
        # When: Placing orders up to the limit
        order_ids = []
        for i in range(max_positions):
            order_id = trading_bridge.place_order(
                symbol="EURUSD",
                order_type="BUY",
                lot=0.1
            )
            order_ids.append(order_id)
        
        # Then: All orders should be placed
        positions = trading_bridge.get_positions()
        assert len(positions) == max_positions
        
        # Clean up
        for order_id in order_ids:
            trading_bridge.close_order(order_id)


class TestOrderValidation:
    """Test order validation"""
    
    @pytest.fixture
    def trading_bridge(self):
        """Create a trading bridge for testing"""
        bridge = MT5Bridge(offline_mode=True)
        bridge.connect()
        return bridge
    
    def test_invalid_symbol_rejection(self, trading_bridge):
        """Test that invalid symbols are rejected"""
        # Given: A trading bridge
        # When: Placing order with invalid symbol
        # Then: Should raise ValueError
        with pytest.raises(ValueError, match="Invalid symbol"):
            trading_bridge.place_order(
                symbol="INVALID",
                order_type="BUY",
                lot=0.1
            )
    
    def test_zero_lot_size_rejection(self, trading_bridge):
        """Test that zero lot size is rejected"""
        # Given: A trading bridge
        # When: Placing order with zero lot size
        # Then: Should raise ValueError
        with pytest.raises(ValueError, match="Invalid lot size"):
            trading_bridge.place_order(
                symbol="EURUSD",
                order_type="BUY",
                lot=0
            )
    
    def test_negative_lot_size_rejection(self, trading_bridge):
        """Test that negative lot size is rejected"""
        # Given: A trading bridge
        # When: Placing order with negative lot size
        # Then: Should raise ValueError
        with pytest.raises(ValueError, match="Invalid lot size"):
            trading_bridge.place_order(
                symbol="EURUSD",
                order_type="BUY",
                lot=-0.1
            )


class TestOrderManagement:
    """Test order management functionality"""
    
    @pytest.fixture
    def trading_bridge(self):
        """Create a trading bridge for testing"""
        bridge = MT5Bridge(offline_mode=True)
        bridge.connect()
        return bridge
    
    def test_order_status_tracking(self, trading_bridge):
        """Test order status tracking"""
        # Given: A trading bridge
        # When: Placing an order
        order_id = trading_bridge.place_order(
            symbol="EURUSD",
            order_type="BUY",
            lot=0.1
        )
        
        # Then: Order status should be tracked correctly
        assert trading_bridge.get_order_status(order_id) == "filled"
    
    def test_order_closing(self, trading_bridge):
        """Test order closing functionality"""
        # Given: A trading bridge with an open order
        order_id = trading_bridge.place_order(
            symbol="EURUSD",
            order_type="BUY",
            lot=0.1
        )
        
        # When: Closing the order
        result = trading_bridge.close_order(order_id)
        
        # Then: Order should be closed successfully
        assert result is True
        assert trading_bridge.get_order_status(order_id) == "closed"
    
    def test_nonexistent_order_operations(self, trading_bridge):
        """Test operations on non-existent orders"""
        # Given: A trading bridge
        nonexistent_order_id = 9999
        
        # When/Then: Operations on non-existent orders should handle gracefully
        assert trading_bridge.get_order_status(nonexistent_order_id) == "not_found"
        assert trading_bridge.close_order(nonexistent_order_id) is False


if __name__ == "__main__":
    # Run tests if executed directly
    pytest.main([__file__, "-v"])