# AI Enhanced Trading Platform - API Reference

This document provides a comprehensive reference for the API endpoints available in the MVP version of the AI Enhanced Trading Platform.

## Base URL

All API endpoints are relative to the base URL:

```
http://localhost:8000
```

## Authentication

The API uses HTTP Basic Authentication. You need to include your username and password with each request.

**Default Credentials for MVP:**
- Username: `admin`
- Password: `trading123`

**Example with cURL:**

```bash
curl -X GET http://localhost:8000/api/strategies -u admin:trading123
```

**Example with JavaScript Fetch API:**

```javascript
fetch('http://localhost:8000/api/strategies', {
  headers: {
    'Authorization': 'Basic ' + btoa('admin:trading123')
  }
})
.then(response => response.json())
.then(data => console.log(data));
```

**Note:** In a production environment, you should always use HTTPS to encrypt credentials during transmission.

## Endpoints

### Health Check

#### `GET /health`

Check if the API server is running.

**Example Request:**

```bash
curl -X GET http://localhost:8000/health
```

Note: The health endpoint does not require authentication.

**Example Response:**

```json
{
  "status": "ok",
  "timestamp": "2025-08-03T14:30:00.123456"
}
```

### Historical Data Endpoints

#### `POST /api/historical-data/upload`

Upload historical price data in CSV format.

**Request Parameters:**

- `symbol` (query parameter): Trading symbol (e.g., 'EURUSD')
- `timeframe` (query parameter): Timeframe (e.g., 'H1', 'D1')
- `file` (form data): CSV file with historical data

The CSV file should have the following columns:
- `timestamp`: Date and time in ISO format or similar parsable format
- `open`: Opening price
- `high`: Highest price
- `low`: Lowest price
- `close`: Closing price
- `volume`: Volume

**Example Request:**

```bash
curl -X POST "http://localhost:8000/api/historical-data/upload?symbol=EURUSD&timeframe=H1" \
  -H "Content-Type: multipart/form-data" \
  -u admin:trading123 \
  -F "file=@/path/to/eurusd_h1_data.csv"
```

**Example Response:**

```json
{
  "symbol": "EURUSD",
  "timeframe": "H1",
  "start_date": "2023-01-01T00:00:00",
  "end_date": "2023-12-31T23:00:00",
  "rows": 8760,
  "columns": ["timestamp", "open", "high", "low", "close", "volume"]
}
```

#### `GET /api/historical-data`

Get a list of available historical data sets.

**Example Request:**

```bash
curl -X GET http://localhost:8000/api/historical-data -u admin:trading123
```

**Example Response:**

```json
[
  "EURUSD_H1",
  "GBPUSD_H4"
]
```

#### `GET /api/historical-data/{symbol}/{timeframe}`

Get historical data for a specific symbol and timeframe.

**Request Parameters:**

- `symbol` (path parameter): Trading symbol (e.g., 'EURUSD')
- `timeframe` (path parameter): Timeframe (e.g., 'H1', 'D1')
- `limit` (query parameter, optional): Maximum number of data points to return (default: 100)

**Example Request:**

```bash
curl -X GET "http://localhost:8000/api/historical-data/EURUSD/H1?limit=10" -u admin:trading123
```

**Example Response:**

```json
{
  "symbol": "EURUSD",
  "timeframe": "H1",
  "data": {
    "timestamp": [
      "2023-12-31T14:00:00",
      "2023-12-31T15:00:00",
      "2023-12-31T16:00:00",
      "2023-12-31T17:00:00",
      "2023-12-31T18:00:00",
      "2023-12-31T19:00:00",
      "2023-12-31T20:00:00",
      "2023-12-31T21:00:00",
      "2023-12-31T22:00:00",
      "2023-12-31T23:00:00"
    ],
    "open": [1.1091, 1.1092, 1.1093, 1.1094, 1.1095, 1.1096, 1.1097, 1.1098, 1.1099, 1.1100],
    "high": [1.1096, 1.1097, 1.1098, 1.1099, 1.1100, 1.1101, 1.1102, 1.1103, 1.1104, 1.1105],
    "low": [1.1086, 1.1087, 1.1088, 1.1089, 1.1090, 1.1091, 1.1092, 1.1093, 1.1094, 1.1095],
    "close": [1.1093, 1.1094, 1.1095, 1.1096, 1.1097, 1.1098, 1.1099, 1.1100, 1.1101, 1.1102],
    "volume": [1090, 1100, 1110, 1120, 1130, 1140, 1150, 1160, 1170, 1180]
  }
}
```

#### `DELETE /api/historical-data/{symbol}/{timeframe}`

Delete historical data for a specific symbol and timeframe.

**Request Parameters:**

- `symbol` (path parameter): Trading symbol (e.g., 'EURUSD')
- `timeframe` (path parameter): Timeframe (e.g., 'H1', 'D1')

**Example Request:**

```bash
curl -X DELETE "http://localhost:8000/api/historical-data/EURUSD/H1" -u admin:trading123
```

**Example Response:**

```json
{
  "message": "Historical data for EURUSD H1 deleted successfully"
}
```

### Trading Endpoints

#### `POST /api/mvp/trade`

Execute a trade (place an order or close an existing order).

**Request Body (Place Order):**

```json
{
  "symbol": "EURUSD",
  "order_type": "BUY",
  "lot": 0.1
}
```

**Request Body (Close Order):**

```json
{
  "action": "close",
  "ticket": 12345
}
```

**Example Request (Place Order):**

```bash
curl -X POST http://localhost:8000/api/mvp/trade \
  -H "Content-Type: application/json" \
  -u admin:trading123 \
  -d '{"symbol": "EURUSD", "order_type": "BUY", "lot": 0.1}'
```

**Example Response (Place Order):**

```json
{
  "status": "success",
  "ticket": 12345,
  "symbol": "EURUSD",
  "lot": 0.1,
  "order_type": "BUY",
  "open_price": 1.12345,
  "time": "2025-08-03T14:30:00.123456"
}
```

**Example Request (Close Order):**

```bash
curl -X POST http://localhost:8000/api/mvp/trade \
  -H "Content-Type: application/json" \
  -u admin:trading123 \
  -d '{"action": "close", "ticket": 12345}'
```

**Example Response (Close Order):**

```json
{
  "status": "success",
  "message": "Order #12345 closed successfully",
  "ticket": 12345,
  "close_time": "2025-08-03T14:35:00.123456"
}
```

#### `GET /api/mvp/trades`

Get a list of all trades.

**Example Request:**

```bash
curl -X GET http://localhost:8000/api/mvp/trades -u admin:trading123
```

**Example Response:**

```json
[
  {
    "status": "success",
    "ticket": 12345,
    "symbol": "EURUSD",
    "lot": 0.1,
    "order_type": "BUY",
    "open_price": 1.12345,
    "time": "2025-08-03T14:30:00.123456"
  },
  {
    "status": "success",
    "ticket": 12346,
    "symbol": "GBPUSD",
    "lot": 0.2,
    "order_type": "SELL",
    "open_price": 1.31452,
    "time": "2025-08-03T14:32:00.123456"
  }
]
```

### Strategy Endpoints

#### `GET /api/strategies`

Get a list of all strategies.

**Example Request:**

```bash
curl -X GET http://localhost:8000/api/strategies -u admin:trading123
```

**Example Response:**

```json
[
  {
    "id": 1,
    "name": "Moving Average Crossover",
    "description": "Simple strategy based on moving average crossover",
    "parameters": {
      "fast_period": 10,
      "slow_period": 20
    },
    "created_at": "2025-08-03T14:00:00.123456",
    "updated_at": "2025-08-03T14:00:00.123456"
  }
]
```

#### `POST /api/strategies`

Create a new strategy.

**Request Body:**

```json
{
  "name": "RSI Strategy",
  "description": "Strategy based on RSI indicator",
  "parameters": {
    "rsi_period": 14,
    "overbought": 70,
    "oversold": 30
  }
}
```

**Example Request:**

```bash
curl -X POST http://localhost:8000/api/strategies \
  -H "Content-Type: application/json" \
  -u admin:trading123 \
  -d '{"name": "RSI Strategy", "description": "Strategy based on RSI indicator", "parameters": {"rsi_period": 14, "overbought": 70, "oversold": 30}}'
```

**Example Response:**

```json
{
  "id": 2,
  "name": "RSI Strategy",
  "description": "Strategy based on RSI indicator",
  "parameters": {
    "rsi_period": 14,
    "overbought": 70,
    "oversold": 30
  },
  "created_at": "2025-08-03T14:40:00.123456",
  "updated_at": "2025-08-03T14:40:00.123456"
}
```

### Backtest Endpoints

#### `GET /api/backtests`

Get a list of all backtests.

**Example Request:**

```bash
curl -X GET http://localhost:8000/api/backtests -u admin:trading123
```

**Example Response:**

```json
[
  {
    "id": 1,
    "strategy_id": 1,
    "start_date": "2023-01-01T00:00:00",
    "end_date": "2023-12-31T23:59:59",
    "symbol": "EURUSD",
    "timeframe": "H1",
    "initial_capital": 10000,
    "results": {
      "final_capital": 12500,
      "profit_factor": 1.8,
      "sharpe_ratio": 1.2,
      "max_drawdown": 8.5,
      "win_rate": 65.2
    },
    "created_at": "2025-08-03T14:10:00.123456"
  }
]
```

#### `POST /api/backtests`

Create a new backtest.

**Request Body:**

```json
{
  "strategy_id": 2,
  "start_date": "2023-01-01T00:00:00",
  "end_date": "2023-12-31T23:59:59",
  "symbol": "GBPUSD",
  "timeframe": "H4",
  "initial_capital": 5000
}
```

**Example Request:**

```bash
curl -X POST http://localhost:8000/api/backtests \
  -H "Content-Type: application/json" \
  -u admin:trading123 \
  -d '{"strategy_id": 2, "start_date": "2023-01-01T00:00:00", "end_date": "2023-12-31T23:59:59", "symbol": "GBPUSD", "timeframe": "H4", "initial_capital": 5000}'
```

**Example Response:**

```json
{
  "id": 2,
  "strategy_id": 2,
  "start_date": "2023-01-01T00:00:00",
  "end_date": "2023-12-31T23:59:59",
  "symbol": "GBPUSD",
  "timeframe": "H4",
  "initial_capital": 5000,
  "results": {
    "final_capital": 6200,
    "profit_factor": 1.5,
    "sharpe_ratio": 1.1,
    "max_drawdown": 10.2,
    "win_rate": 58.7
  },
  "created_at": "2025-08-03T14:45:00.123456"
}
```

## Error Handling

The API returns standard HTTP status codes to indicate the success or failure of a request:

- `200 OK`: The request was successful
- `400 Bad Request`: The request was invalid
- `404 Not Found`: The requested resource was not found
- `422 Unprocessable Entity`: The request was well-formed but could not be processed
- `500 Internal Server Error`: An error occurred on the server

Error responses include a JSON object with details about the error:

```json
{
  "detail": "Error message describing what went wrong"
}
```

## Rate Limiting

The MVP version does not implement rate limiting. This will be added in future versions.

## Versioning

The API is currently in MVP stage and does not have explicit versioning. Future versions will include versioning in the URL path.