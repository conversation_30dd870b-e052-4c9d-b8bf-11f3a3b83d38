#!/bin/bash
# Ollama Trading Platform Instance Setup
# This script creates a dedicated Ollama instance for the AI Enhanced Trading Platform

echo "🚀 Setting up dedicated Ollama instance for AI Enhanced Trading Platform"
echo "========================================================================="

# Create dedicated directory for this project's Ollama instance
$OLLAMA_DIR = "c:\Users\<USER>\Projects\AI Enhanced Trading Platform-Sonnet-GPTmini\ollama_instance"
if (!(Test-Path $OLLAMA_DIR)) {
    New-Item -ItemType Directory -Path $OLLAMA_DIR -Force
    Write-Host "✅ Created Ollama instance directory: $OLLAMA_DIR"
}

# Set environment variables for dedicated instance
$env:OLLAMA_HOST = "127.0.0.1:11435"
$env:OLLAMA_MODELS = "$OLLAMA_DIR\models"
$env:OLLAMA_LOGS = "$OLLAMA_DIR\logs"

Write-Host "🔧 Configured Ollama for trading platform:"
Write-Host "   - Host: $env:OLLAMA_HOST"
Write-Host "   - Models: $env:OLLAMA_MODELS"
Write-Host "   - Logs: $env:OLLAMA_LOGS"

# Create directories
New-Item -ItemType Directory -Path $env:OLLAMA_MODELS -Force | Out-Null
New-Item -ItemType Directory -Path $env:OLLAMA_LOGS -Force | Out-Null

Write-Host "🎯 Starting dedicated Ollama server on port 11435..."
Write-Host "   This will run in the background and won't conflict with other Ollama instances"

# Start Ollama server with custom configuration
Start-Process -NoNewWindow -FilePath "ollama" -ArgumentList "serve" -RedirectStandardOutput "$env:OLLAMA_LOGS\server.log" -RedirectStandardError "$env:OLLAMA_LOGS\error.log"

Start-Sleep 3

# Test if server is running
try {
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:11435/" -TimeoutSec 5
    Write-Host "✅ Ollama trading platform instance is running on port 11435"
} catch {
    Write-Host "❌ Failed to start Ollama instance. Check logs in $env:OLLAMA_LOGS"
    exit 1
}

Write-Host ""
Write-Host "🎉 Setup complete! Your dedicated Ollama instance is ready."
Write-Host "📝 To use this instance:"
Write-Host "   - Server URL: http://127.0.0.1:11435"
Write-Host "   - Models directory: $env:OLLAMA_MODELS"
Write-Host "   - Logs directory: $env:OLLAMA_LOGS"
Write-Host ""
Write-Host "🔄 Next steps:"
Write-Host "   1. Pull required models for trading platform"
Write-Host "   2. Update trading platform configuration"
Write-Host "   3. Test the complete system"
