import { UserRepository } from './user.repository';
import { getMockUser } from '../../../test-factories/user.factory';
import { DatabaseConnection } from '../connection';
import { User, UserId } from '@/schemas';

// Mock database connection
jest.mock('../connection');

describe('UserRepository', () => {
  let userRepository: UserRepository;
  let mockDb: any;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockDb = {
      select: jest.fn().mockReturnThis(),
      from: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      first: jest.fn(),
      insert: jest.fn().mockReturnThis(),
      returning: jest.fn(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      offset: jest.fn().mockReturnThis(),
    };

    (DatabaseConnection.getInstance as jest.Mock).mockReturnValue({
      connection: mockDb,
    });

    userRepository = new UserRepository();
  });

  describe('findByEmail', () => {
    it('should find user by email', async () => {
      // Arrange
      const user = getMockUser();
      const dbUser = {
        id: user.id,
        email: user.email,
        full_name: user.fullName,
        password_hash: 'hashed_password',
        subscription_tier: user.subscriptionTier,
        api_quota_used: user.apiQuotaUsed,
        api_quota_limit: user.apiQuotaLimit,
        created_at: user.createdAt,
        updated_at: user.updatedAt,
      };
      mockDb.first.mockResolvedValue(dbUser);

      // Act
      const result = await userRepository.findByEmail(user.email);

      // Assert
      expect(result).toEqual({
        ...user,
        passwordHash: 'hashed_password',
      });
      expect(mockDb.select).toHaveBeenCalledWith('*');
      expect(mockDb.from).toHaveBeenCalledWith('users');
      expect(mockDb.where).toHaveBeenCalledWith('email', user.email);
      expect(mockDb.first).toHaveBeenCalled();
    });

    it('should return null when user not found', async () => {
      // Arrange
      mockDb.first.mockResolvedValue(undefined);

      // Act
      const result = await userRepository.findByEmail('<EMAIL>');

      // Assert
      expect(result).toBeNull();
    });

    it('should handle database errors', async () => {
      // Arrange
      mockDb.first.mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(
        userRepository.findByEmail('<EMAIL>')
      ).rejects.toThrow('Database error');
    });
  });

  describe('findById', () => {
    it('should find user by id', async () => {
      // Arrange
      const user = getMockUser();
      const dbUser = {
        id: user.id,
        email: user.email,
        full_name: user.fullName,
        password_hash: 'hashed_password',
        subscription_tier: user.subscriptionTier,
        api_quota_used: user.apiQuotaUsed,
        api_quota_limit: user.apiQuotaLimit,
        created_at: user.createdAt,
        updated_at: user.updatedAt,
      };
      mockDb.first.mockResolvedValue(dbUser);

      // Act
      const result = await userRepository.findById(user.id);

      // Assert
      expect(result).toEqual({
        ...user,
        passwordHash: 'hashed_password',
      });
      expect(mockDb.select).toHaveBeenCalledWith('*');
      expect(mockDb.from).toHaveBeenCalledWith('users');
      expect(mockDb.where).toHaveBeenCalledWith('id', user.id);
      expect(mockDb.first).toHaveBeenCalled();
    });

    it('should return null when user not found', async () => {
      // Arrange
      mockDb.first.mockResolvedValue(undefined);

      // Act
      const result = await userRepository.findById('user_non-existent-id' as UserId);

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('create', () => {
    it('should create new user', async () => {
      // Arrange
      const createData = {
        email: '<EMAIL>',
        fullName: 'Test User',
        passwordHash: 'hashed_password',
        subscriptionTier: 'free' as const,
        apiQuotaUsed: 0,
        apiQuotaLimit: 100,
      };
      const createdUser = getMockUser(createData);
      const dbUser = {
        id: createdUser.id,
        email: createdUser.email,
        full_name: createdUser.fullName,
        password_hash: createData.passwordHash,
        subscription_tier: createdUser.subscriptionTier,
        api_quota_used: createdUser.apiQuotaUsed,
        api_quota_limit: createdUser.apiQuotaLimit,
        created_at: createdUser.createdAt,
        updated_at: createdUser.updatedAt,
      };
      mockDb.returning.mockResolvedValue([dbUser]);

      // Act
      const result = await userRepository.create(createData);

      // Assert
      expect(result).toEqual({
        ...createdUser,
        passwordHash: createData.passwordHash,
      });
      expect(mockDb.insert).toHaveBeenCalledWith({
        id: expect.any(String),
        email: createData.email,
        full_name: createData.fullName,
        password_hash: createData.passwordHash,
        subscription_tier: createData.subscriptionTier,
        api_quota_used: createData.apiQuotaUsed,
        api_quota_limit: createData.apiQuotaLimit,
        created_at: expect.any(Date),
        updated_at: expect.any(Date),
      });
      expect(mockDb.returning).toHaveBeenCalledWith('*');
    });

    it('should handle creation errors', async () => {
      // Arrange
      const createData = {
        email: '<EMAIL>',
        fullName: 'Test User',
        passwordHash: 'hashed_password',
        subscriptionTier: 'free' as const,
        apiQuotaUsed: 0,
        apiQuotaLimit: 100,
      };
      mockDb.returning.mockRejectedValue(new Error('Creation failed'));

      // Act & Assert
      await expect(userRepository.create(createData)).rejects.toThrow('Creation failed');
    });
  });

  describe('update', () => {
    it('should update user', async () => {
      // Arrange
      const userId = 'user_123e4567-e89b-12d3-a456-426614174000' as UserId;
      const updateData = { fullName: 'Updated User' };
      const updatedUser = getMockUser({ id: userId, ...updateData });
      const dbUser = {
        id: updatedUser.id,
        email: updatedUser.email,
        full_name: updatedUser.fullName,
        password_hash: 'hashed_password',
        subscription_tier: updatedUser.subscriptionTier,
        api_quota_used: updatedUser.apiQuotaUsed,
        api_quota_limit: updatedUser.apiQuotaLimit,
        created_at: updatedUser.createdAt,
        updated_at: updatedUser.updatedAt,
      };
      mockDb.returning.mockResolvedValue([dbUser]);

      // Act
      const result = await userRepository.update(userId, updateData);

      // Assert
      expect(result).toEqual({
        ...updatedUser,
        passwordHash: 'hashed_password',
      });
      expect(mockDb.where).toHaveBeenCalledWith('id', userId);
      expect(mockDb.update).toHaveBeenCalledWith({
        full_name: updateData.fullName,
        updated_at: expect.any(Date),
      });
      expect(mockDb.returning).toHaveBeenCalledWith('*');
    });

    it('should return null when user not found for update', async () => {
      // Arrange
      mockDb.returning.mockResolvedValue([]);

      // Act
      const result = await userRepository.update(
        'user_non-existent-id' as UserId, 
        { fullName: 'Test' }
      );

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('delete', () => {
    it('should delete user', async () => {
      // Arrange
      const userId = 'user_123e4567-e89b-12d3-a456-426614174000' as UserId;
      mockDb.delete.mockResolvedValue(1);

      // Act
      const result = await userRepository.delete(userId);

      // Assert
      expect(result).toBe(true);
      expect(mockDb.from).toHaveBeenCalledWith('users');
      expect(mockDb.where).toHaveBeenCalledWith('id', userId);
      expect(mockDb.delete).toHaveBeenCalled();
    });

    it('should return false when user not found for deletion', async () => {
      // Arrange
      mockDb.delete.mockResolvedValue(0);

      // Act
      const result = await userRepository.delete('user_non-existent-id' as UserId);

      // Assert
      expect(result).toBe(false);
    });
  });
});