"""
Database configuration
"""

import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, declarative_base
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get database URL from environment variables
DATABASE_URL = os.getenv("DATABASE_URL")

# If DATABASE_URL is not set, use a default SQLite database for development
if not DATABASE_URL:
    DATABASE_URL = "sqlite+aiosqlite:///./app.db"
    print(f"Warning: DATABASE_URL not set, using {DATABASE_URL}")
else:
    # Convert the Neon PostgreSQL URL to use psycopg2 instead of asyncpg
    # psycopg2 is easier to install on Windows
    if DATABASE_URL.startswith("postgres://"):
        DATABASE_URL = DATABASE_URL.replace("postgres://", "postgresql://", 1)
    elif DATABASE_URL.startswith("postgresql://"):
        # Keep as is - we'll use the synchronous adapter
        pass

# Create engine
engine = create_engine(
    DATABASE_URL,
    echo=True,  # Set to False in production
    future=True,
)

# Create session factory
SessionLocal = sessionmaker(
    bind=engine,
    expire_on_commit=False,
)

# Create declarative base
Base = declarative_base()

# Dependency to get DB session
def get_db():
    """Get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()