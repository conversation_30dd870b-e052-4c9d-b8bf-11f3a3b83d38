#!/usr/bin/env python3
"""
Quick test of Darwin Engine Step 2 with synthetic data
"""

import sys
import os
sys.path.append('shared')

from darwin_engine_step2 import RealDataDarwinEngine, EvolutionConfig
import logging

logging.basicConfig(level=logging.INFO)

def test_step2_with_synthetic():
    """Test Step 2 engine with synthetic data to avoid network issues"""
    
    print("🧪 Testing Darwin Engine Step 2 with Synthetic Data")
    print("=" * 60)
    
    # Configure evolution for quick test
    config = EvolutionConfig(
        population_size=10,   # Small population for quick test
        max_generations=5,    # Few generations
        mutation_rate=0.2,
        elite_size=2
    )
    
    try:
        # Create engine - it will use synthetic data if real data fails
        engine = RealDataDarwinEngine(config, 'EURUSD')
        
        print("🚀 Starting evolution...")
        results = engine.run_evolution()
        
        # Display results
        print(f"\n📊 Evolution Results:")
        print(f"Best fitness: {results['best_strategy']['fitness_score']:.4f}")
        
        best = results['best_strategy']
        backtest = best['backtest_results']
        
        print(f"\n🏆 Best Strategy:")
        print(f"  Name: {best['name']}")
        print(f"  Action: {best['action']}")
        print(f"  Conditions: {len(best['conditions'])}")
        
        print(f"\n📈 Backtest Results:")
        print(f"  Total Return: {backtest.get('total_return_pct', 0):.2f}%")
        print(f"  Win Rate: {backtest.get('win_rate', 0):.2%}")
        print(f"  Total Trades: {backtest.get('total_trades', 0)}")
        print(f"  Sharpe Ratio: {backtest.get('sharpe_ratio', 0):.2f}")
        print(f"  Max Drawdown: {backtest.get('max_drawdown_pct', 0):.2f}%")
        print(f"  Profit Factor: {backtest.get('profit_factor', 0):.2f}")
        
        print(f"\n✅ Step 2 Test Complete!")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_step2_with_synthetic()