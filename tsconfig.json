{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "allowSyntheticDefaultImports": true, "moduleResolution": "node", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@shared/*": ["shared/src/*"]}, "types": ["node", "jest"]}, "include": ["src/**/*", "tests/**/*", "shared/src/**/*"], "exclude": ["node_modules", "dist", "coverage"]}