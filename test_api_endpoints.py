"""
Test API endpoints
This script tests the API endpoints using requests
"""

import requests
import json
import logging
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("api_endpoint_test")

# API base URL
BASE_URL = "http://localhost:8000/api/v1"

def test_endpoint(method, endpoint, data=None):
    """Test an API endpoint"""
    url = f"{BASE_URL}/{endpoint}"
    logger.info(f"Testing {method} {url}")
    
    try:
        if method.upper() == "GET":
            response = requests.get(url)
        elif method.upper() == "POST":
            response = requests.post(url, json=data)
        elif method.upper() == "PUT":
            response = requests.put(url, json=data)
        elif method.upper() == "DELETE":
            response = requests.delete(url)
        else:
            logger.error(f"Invalid method: {method}")
            return None
        
        response.raise_for_status()
        
        data = response.json()
        logger.info(f"Response: {json.dumps(data, indent=2)}")
        
        return data
    except requests.exceptions.RequestException as e:
        logger.error(f"Error testing endpoint: {str(e)}")
        return None

def run_tests():
    """Run all tests"""
    logger.info("Starting API endpoint tests...")
    
    # Test MT5 endpoints
    test_endpoint("GET", "mt5/status")
    test_endpoint("POST", "mt5/connect")
    
    # Place an order
    order_response = test_endpoint("POST", "mt5/order", {
        "symbol": "EURUSD",
        "orderType": "BUY",
        "volume": 0.1,
        "price": None,
        "stopLoss": None,
        "takeProfit": None
    })
    
    # Get positions
    positions = test_endpoint("GET", "mt5/positions")
    
    # Close the position
    if order_response and "orderId" in order_response:
        test_endpoint("POST", f"mt5/positions/{order_response['orderId']}/close")
    
    # Test disconnect
    test_endpoint("POST", "mt5/disconnect")
    
    logger.info("All tests completed")

if __name__ == "__main__":
    # Wait for the server to start
    logger.info("Waiting for server to start...")
    time.sleep(2)
    
    run_tests()