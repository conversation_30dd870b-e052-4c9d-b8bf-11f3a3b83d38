@tailwind base;
@tailwind components;
@tailwind utilities;

/* Reset and base styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f9fafb;
  line-height: 1.5;
}

/* Container constraints */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Layout utilities */
.max-w-7xl { max-width: 80rem !important; }
.max-w-6xl { max-width: 72rem !important; }
.max-w-4xl { max-width: 56rem !important; }
.max-w-2xl { max-width: 42rem !important; }
.max-w-xl { max-width: 36rem !important; }
.max-w-lg { max-width: 32rem !important; }
.max-w-md { max-width: 28rem !important; }
.max-w-sm { max-width: 24rem !important; }

/* Spacing utilities */
.space-x-1 > :not([hidden]) ~ :not([hidden]) { --tw-space-x-reverse: 0; margin-right: calc(0.25rem * var(--tw-space-x-reverse)); margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse))); }
.space-x-2 > :not([hidden]) ~ :not([hidden]) { --tw-space-x-reverse: 0; margin-right: calc(0.5rem * var(--tw-space-x-reverse)); margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse))); }
.space-x-3 > :not([hidden]) ~ :not([hidden]) { --tw-space-x-reverse: 0; margin-right: calc(0.75rem * var(--tw-space-x-reverse)); margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse))); }
.space-x-4 > :not([hidden]) ~ :not([hidden]) { --tw-space-x-reverse: 0; margin-right: calc(1rem * var(--tw-space-x-reverse)); margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse))); }
.space-x-6 > :not([hidden]) ~ :not([hidden]) { --tw-space-x-reverse: 0; margin-right: calc(1.5rem * var(--tw-space-x-reverse)); margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse))); }
.space-x-8 > :not([hidden]) ~ :not([hidden]) { --tw-space-x-reverse: 0; margin-right: calc(2rem * var(--tw-space-x-reverse)); margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse))); }

.space-y-1 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom: calc(0.25rem * var(--tw-space-y-reverse)); }
.space-y-2 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom: calc(0.5rem * var(--tw-space-y-reverse)); }
.space-y-3 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom: calc(0.75rem * var(--tw-space-y-reverse)); }
.space-y-4 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom: calc(1rem * var(--tw-space-y-reverse)); }
.space-y-6 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom: calc(1.5rem * var(--tw-space-y-reverse)); }
.space-y-8 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom: calc(2rem * var(--tw-space-y-reverse)); }

.p-1 { padding: 0.25rem !important; }
.p-2 { padding: 0.5rem !important; }
.p-3 { padding: 0.75rem !important; }
.p-4 { padding: 1rem !important; }
.p-6 { padding: 1.5rem !important; }
.p-8 { padding: 2rem !important; }

.px-1 { padding-left: 0.25rem !important; padding-right: 0.25rem !important; }
.px-2 { padding-left: 0.5rem !important; padding-right: 0.5rem !important; }
.px-3 { padding-left: 0.75rem !important; padding-right: 0.75rem !important; }
.px-4 { padding-left: 1rem !important; padding-right: 1rem !important; }
.px-6 { padding-left: 1.5rem !important; padding-right: 1.5rem !important; }
.px-8 { padding-left: 2rem !important; padding-right: 2rem !important; }

.py-1 { padding-top: 0.25rem !important; padding-bottom: 0.25rem !important; }
.py-2 { padding-top: 0.5rem !important; padding-bottom: 0.5rem !important; }
.py-3 { padding-top: 0.75rem !important; padding-bottom: 0.75rem !important; }
.py-4 { padding-top: 1rem !important; padding-bottom: 1rem !important; }
.py-6 { padding-top: 1.5rem !important; padding-bottom: 1.5rem !important; }
.py-8 { padding-top: 2rem !important; padding-bottom: 2rem !important; }
.py-12 { padding-top: 3rem !important; padding-bottom: 3rem !important; }

.m-1 { margin: 0.25rem !important; }
.m-2 { margin: 0.5rem !important; }
.m-3 { margin: 0.75rem !important; }
.m-4 { margin: 1rem !important; }
.m-6 { margin: 1.5rem !important; }
.m-8 { margin: 2rem !important; }

.mx-auto { margin-left: auto !important; margin-right: auto !important; }

.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 0.75rem !important; }
.mb-4 { margin-bottom: 1rem !important; }
.mb-6 { margin-bottom: 1.5rem !important; }
.mb-8 { margin-bottom: 2rem !important; }

.mt-1 { margin-top: 0.25rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 0.75rem !important; }
.mt-4 { margin-top: 1rem !important; }
.mt-6 { margin-top: 1.5rem !important; }
.mt-8 { margin-top: 2rem !important; }

/* Flexbox utilities */
.flex { display: flex !important; }
.flex-col { flex-direction: column !important; }
.flex-row { flex-direction: row !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-1 { flex: 1 1 0% !important; }
.items-center { align-items: center !important; }
.items-start { align-items: flex-start !important; }
.items-end { align-items: flex-end !important; }
.justify-center { justify-content: center !important; }
.justify-between { justify-content: space-between !important; }
.justify-start { justify-content: flex-start !important; }
.justify-end { justify-content: flex-end !important; }

/* Grid utilities */
.grid { display: grid !important; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }
.gap-1 { gap: 0.25rem !important; }
.gap-2 { gap: 0.5rem !important; }
.gap-3 { gap: 0.75rem !important; }
.gap-4 { gap: 1rem !important; }
.gap-6 { gap: 1.5rem !important; }
.gap-8 { gap: 2rem !important; }

/* Width and height utilities */
.w-3 { width: 0.75rem !important; }
.w-4 { width: 1rem !important; }
.w-5 { width: 1.25rem !important; }
.w-6 { width: 1.5rem !important; }
.w-8 { width: 2rem !important; }
.w-10 { width: 2.5rem !important; }
.w-12 { width: 3rem !important; }
.w-16 { width: 4rem !important; }
.w-full { width: 100% !important; }
.w-auto { width: auto !important; }
.w-fit { width: fit-content !important; }

.h-3 { height: 0.75rem !important; }
.h-4 { height: 1rem !important; }
.h-5 { height: 1.25rem !important; }
.h-6 { height: 1.5rem !important; }
.h-8 { height: 2rem !important; }
.h-10 { height: 2.5rem !important; }
.h-12 { height: 3rem !important; }
.h-16 { height: 4rem !important; }
.h-full { height: 100% !important; }
.h-auto { height: auto !important; }
.h-screen { height: 100vh !important; }
.min-h-screen { min-height: 100vh !important; }

/* Height specific utilities */
.h-\[800px\] { height: 800px !important; }
.h-\[400px\] { height: 400px !important; }
.h-\[300px\] { height: 300px !important; }

/* Text utilities */
.text-xs { font-size: 0.75rem !important; line-height: 1rem !important; }
.text-sm { font-size: 0.875rem !important; line-height: 1.25rem !important; }
.text-base { font-size: 1rem !important; line-height: 1.5rem !important; }
.text-lg { font-size: 1.125rem !important; line-height: 1.75rem !important; }
.text-xl { font-size: 1.25rem !important; line-height: 1.75rem !important; }
.text-2xl { font-size: 1.5rem !important; line-height: 2rem !important; }
.text-3xl { font-size: 1.875rem !important; line-height: 2.25rem !important; }

.font-normal { font-weight: 400 !important; }
.font-medium { font-weight: 500 !important; }
.font-semibold { font-weight: 600 !important; }
.font-bold { font-weight: 700 !important; }

.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }

/* Color utilities */
.text-white { color: #ffffff !important; }
.text-black { color: #000000 !important; }
.text-gray-50 { color: #f9fafb !important; }
.text-gray-100 { color: #f3f4f6 !important; }
.text-gray-200 { color: #e5e7eb !important; }
.text-gray-300 { color: #d1d5db !important; }
.text-gray-400 { color: #9ca3af !important; }
.text-gray-500 { color: #6b7280 !important; }
.text-gray-600 { color: #4b5563 !important; }
.text-gray-700 { color: #374151 !important; }
.text-gray-800 { color: #1f2937 !important; }
.text-gray-900 { color: #111827 !important; }

.text-blue-50 { color: #eff6ff !important; }
.text-blue-100 { color: #dbeafe !important; }
.text-blue-200 { color: #bfdbfe !important; }
.text-blue-300 { color: #93c5fd !important; }
.text-blue-400 { color: #60a5fa !important; }
.text-blue-500 { color: #3b82f6 !important; }
.text-blue-600 { color: #2563eb !important; }
.text-blue-700 { color: #1d4ed8 !important; }
.text-blue-800 { color: #1e40af !important; }
.text-blue-900 { color: #1e3a8a !important; }

.text-green-50 { color: #f0fdf4 !important; }
.text-green-100 { color: #dcfce7 !important; }
.text-green-500 { color: #22c55e !important; }
.text-green-600 { color: #16a34a !important; }
.text-green-700 { color: #15803d !important; }

.text-red-50 { color: #fef2f2 !important; }
.text-red-100 { color: #fee2e2 !important; }
.text-red-500 { color: #ef4444 !important; }
.text-red-600 { color: #dc2626 !important; }
.text-red-700 { color: #b91c1c !important; }

.text-yellow-600 { color: #d97706 !important; }
.text-purple-600 { color: #9333ea !important; }

/* Background colors */
.bg-white { background-color: #ffffff !important; }
.bg-black { background-color: #000000 !important; }
.bg-transparent { background-color: transparent !important; }

.bg-gray-50 { background-color: #f9fafb !important; }
.bg-gray-100 { background-color: #f3f4f6 !important; }
.bg-gray-200 { background-color: #e5e7eb !important; }
.bg-gray-300 { background-color: #d1d5db !important; }
.bg-gray-800 { background-color: #1f2937 !important; }
.bg-gray-900 { background-color: #111827 !important; }

.bg-blue-50 { background-color: #eff6ff !important; }
.bg-blue-100 { background-color: #dbeafe !important; }
.bg-blue-500 { background-color: #3b82f6 !important; }
.bg-blue-600 { background-color: #2563eb !important; }
.bg-blue-700 { background-color: #1d4ed8 !important; }

.bg-green-50 { background-color: #f0fdf4 !important; }
.bg-green-100 { background-color: #dcfce7 !important; }
.bg-green-500 { background-color: #22c55e !important; }
.bg-green-600 { background-color: #16a34a !important; }

.bg-red-50 { background-color: #fef2f2 !important; }
.bg-red-100 { background-color: #fee2e2 !important; }
.bg-red-500 { background-color: #ef4444 !important; }
.bg-red-600 { background-color: #dc2626 !important; }

.bg-yellow-100 { background-color: #fef3c7 !important; }
.bg-purple-500 { background-color: #a855f7 !important; }

/* Background gradients */
.bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)) !important; }
.from-blue-600 { --tw-gradient-from: #2563eb !important; --tw-gradient-to: rgb(37 99 235 / 0) !important; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important; }
.to-indigo-700 { --tw-gradient-to: #4338ca !important; }

/* Hover states */
.hover\:bg-blue-600:hover { background-color: #2563eb !important; }
.hover\:bg-blue-700:hover { background-color: #1d4ed8 !important; }
.hover\:bg-gray-100:hover { background-color: #f3f4f6 !important; }
.hover\:bg-gray-200:hover { background-color: #e5e7eb !important; }
.hover\:bg-gray-50:hover { background-color: #f9fafb !important; }

/* Border utilities */
.border { border-width: 1px !important; }
.border-0 { border-width: 0px !important; }
.border-2 { border-width: 2px !important; }
.border-b { border-bottom-width: 1px !important; }
.border-t { border-top-width: 1px !important; }
.border-gray-200 { border-color: #e5e7eb !important; }
.border-gray-300 { border-color: #d1d5db !important; }
.border-blue-500 { border-color: #3b82f6 !important; }

/* Border radius */
.rounded { border-radius: 0.25rem !important; }
.rounded-md { border-radius: 0.375rem !important; }
.rounded-lg { border-radius: 0.5rem !important; }
.rounded-xl { border-radius: 0.75rem !important; }
.rounded-full { border-radius: 9999px !important; }

/* Shadow utilities */
.shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important; }
.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important; }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important; }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important; }

/* Position utilities */
.relative { position: relative !important; }
.absolute { position: absolute !important; }
.fixed { position: fixed !important; }
.sticky { position: sticky !important; }

/* Display utilities */
.block { display: block !important; }
.inline-block { display: inline-block !important; }
.inline { display: inline !important; }
.hidden { display: none !important; }

/* Responsive utilities */
@media (min-width: 640px) {
  .sm\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
  .sm\:px-6 { padding-left: 1.5rem !important; padding-right: 1.5rem !important; }
  .sm\:block { display: block !important; }
  .sm\:hidden { display: none !important; }
}

@media (min-width: 768px) {
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
  .md\:px-8 { padding-left: 2rem !important; padding-right: 2rem !important; }
  .md\:flex { display: flex !important; }
  .md\:flex-row { flex-direction: row !important; }
  .md\:hidden { display: none !important; }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }
  .lg\:px-8 { padding-left: 2rem !important; padding-right: 2rem !important; }
  .lg\:px-12 { padding-left: 3rem !important; padding-right: 3rem !important; }
}

/* Custom component styles */
.gradient-bg {
  background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%) !important;
}

.card-shadow {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
  transition: box-shadow 0.2s ease-in-out !important;
}

.card-shadow:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

/* Loading animation */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite !important;
}

/* Transition utilities */
.transition { 
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter !important; 
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important; 
  transition-duration: 150ms !important; 
}

/* Focus utilities */
.focus\:outline-none:focus { outline: 2px solid transparent !important; outline-offset: 2px !important; }
.focus\:ring-2:focus { 
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important; 
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important; 
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important; 
}

/* Overflow utilities */
.overflow-hidden { overflow: hidden !important; }
.overflow-x-auto { overflow-x: auto !important; }
.overflow-y-auto { overflow-y: auto !important; }

/* Cursor utilities */
.cursor-pointer { cursor: pointer !important; }
.cursor-not-allowed { cursor: not-allowed !important; }

/* Opacity utilities */
.opacity-50 { opacity: 0.5 !important; }
.opacity-75 { opacity: 0.75 !important; }
.opacity-100 { opacity: 1 !important; }