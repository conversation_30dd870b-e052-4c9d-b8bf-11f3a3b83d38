import { describe, it, expect } from '@jest/globals';
import { 
  MarketDataSchema, 
  StrategyConfigSchema, 
  BacktestRequestSchema,
  BacktestConfigSchema,
  StrategyParametersSchema,
  validateBacktestRequest,
  ValidationError
} from '../trading.schema';

describe('Trading Schemas', () => {
  describe('MarketDataSchema', () => {
    it('should validate valid market data', () => {
      const validData = {
        symbol: 'AAPL',
        timeframe: '1h',
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        dataType: 'ohlcv'
      };

      const result = MarketDataSchema.safeParse(validData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.symbol).toBe('AAPL');
        expect(result.data.timeframe).toBe('1h');
      }
    });

    it('should reject invalid symbol format', () => {
      const invalidData = {
        symbol: 'invalid symbol!',
        timeframe: '1h',
        startDate: '2024-01-01',
        endDate: '2024-12-31'
      };

      const result = MarketDataSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toContain('symbol');
        expect(result.error.issues[0].message).toContain('Invalid symbol format');
      }
    });

    it('should reject invalid timeframe', () => {
      const invalidData = {
        symbol: 'AAPL',
        timeframe: '3h', // Invalid
        startDate: '2024-01-01',
        endDate: '2024-12-31'
      };

      const result = MarketDataSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toContain('timeframe');
      }
    });

    it('should validate date range', () => {
      const invalidDateRange = {
        symbol: 'AAPL',
        timeframe: '1h',
        startDate: '2024-12-31',
        endDate: '2024-01-01' // End before start
      };

      const result = MarketDataSchema.safeParse(invalidDateRange);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('Start date must be before end date');
      }
    });

    it('should accept valid crypto symbols', () => {
      const cryptoData = {
        symbol: 'BTCUSD',
        timeframe: '4h',
        startDate: '2024-01-01',
        endDate: '2024-12-31'
      };

      const result = MarketDataSchema.safeParse(cryptoData);
      expect(result.success).toBe(true);
    });

    it('should accept valid forex symbols', () => {
      const forexData = {
        symbol: 'EUR-USD',
        timeframe: '1d',
        startDate: '2024-01-01',
        endDate: '2024-12-31'
      };

      const result = MarketDataSchema.safeParse(forexData);
      expect(result.success).toBe(true);
    });
  });

  describe('StrategyParametersSchema', () => {
    it('should validate valid parameters', () => {
      const validParams = {
        shortPeriod: 20,
        longPeriod: 50,
        stopLoss: 0.02,
        takeProfit: 0.05,
        riskPerTrade: 0.01,
        enabled: true,
        description: 'SMA crossover strategy'
      };

      const result = StrategyParametersSchema.safeParse(validParams);
      expect(result.success).toBe(true);
    });

    it('should reject invalid stop loss values', () => {
      const invalidParams = {
        stopLoss: 1.5 // > 1 (150%)
      };

      const result = StrategyParametersSchema.safeParse(invalidParams);
      expect(result.success).toBe(false);
    });

    it('should reject invalid take profit values', () => {
      const invalidParams = {
        takeProfit: -0.1 // Negative
      };

      const result = StrategyParametersSchema.safeParse(invalidParams);
      expect(result.success).toBe(false);
    });

    it('should accept empty parameters', () => {
      const emptyParams = {};

      const result = StrategyParametersSchema.safeParse(emptyParams);
      expect(result.success).toBe(true);
    });
  });

  describe('StrategyConfigSchema', () => {
    it('should validate complete strategy configuration', () => {
      const validStrategy = {
        name: 'SMA Crossover',
        code: `
def trading_strategy(data, params):
    sma_short = calculate_sma(data['close'], params['shortPeriod'])
    sma_long = calculate_sma(data['close'], params['longPeriod'])
    
    if len(sma_short) == 0 or len(sma_long) == 0:
        return {'signal': 'hold'}
    
    if sma_short[-1] > sma_long[-1]:
        return {'signal': 'buy'}
    else:
        return {'signal': 'sell'}
`,
        parameters: {
          shortPeriod: 20,
          longPeriod: 50,
          stopLoss: 0.02
        }
      };

      const result = StrategyConfigSchema.safeParse(validStrategy);
      expect(result.success).toBe(true);
    });

    it('should reject strategy without trading_strategy function', () => {
      const invalidStrategy = {
        name: 'Invalid Strategy',
        code: 'def some_other_function(data, params): return {"signal": "hold"}',
        parameters: {}
      };

      const result = StrategyConfigSchema.safeParse(invalidStrategy);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues.some(issue => issue.message.includes('trading_strategy function'))).toBe(true);
      }
    });

    it('should reject empty strategy name', () => {
      const invalidStrategy = {
        name: '',
        code: 'def trading_strategy(data, params): return {"signal": "hold"}',
        parameters: {}
      };

      const result = StrategyConfigSchema.safeParse(invalidStrategy);
      expect(result.success).toBe(false);
    });

    it('should reject strategy name that is too long', () => {
      const invalidStrategy = {
        name: 'A'.repeat(101), // 101 characters
        code: 'def trading_strategy(data, params): return {"signal": "hold"}',
        parameters: {}
      };

      const result = StrategyConfigSchema.safeParse(invalidStrategy);
      expect(result.success).toBe(false);
    });

    it('should reject code that is too short', () => {
      const invalidStrategy = {
        name: 'Test Strategy',
        code: 'x=1', // Too short
        parameters: {}
      };

      const result = StrategyConfigSchema.safeParse(invalidStrategy);
      expect(result.success).toBe(false);
    });

    it('should reject code that is too long', () => {
      const longCode = 'def trading_strategy(data, params):\n' + '    # comment\n'.repeat(5000); // Actually too long
      const invalidStrategy = {
        name: 'Test Strategy',
        code: longCode,
        parameters: {}
      };

      const result = StrategyConfigSchema.safeParse(invalidStrategy);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues.some(issue => issue.message.includes('50,000 characters'))).toBe(true);
      }
    });
  });

  describe('BacktestConfigSchema', () => {
    it('should validate valid backtest configuration', () => {
      const validConfig = {
        initialCapital: 10000,
        positionSize: 0.1,
        commission: 0.001,
        slippage: 0.0005,
        marginRequirement: 0.1,
        maxPositions: 5
      };

      const result = BacktestConfigSchema.safeParse(validConfig);
      expect(result.success).toBe(true);
    });

    it('should reject invalid initial capital', () => {
      const invalidConfig = {
        initialCapital: 50, // Too low
        positionSize: 0.1,
        commission: 0.001,
        slippage: 0.0005
      };

      const result = BacktestConfigSchema.safeParse(invalidConfig);
      expect(result.success).toBe(false);
    });

    it('should reject invalid position size', () => {
      const invalidConfig = {
        initialCapital: 10000,
        positionSize: 1.5, // > 1 (150%)
        commission: 0.001,
        slippage: 0.0005
      };

      const result = BacktestConfigSchema.safeParse(invalidConfig);
      expect(result.success).toBe(false);
    });

    it('should reject invalid commission', () => {
      const invalidConfig = {
        initialCapital: 10000,
        positionSize: 0.1,
        commission: 0.02, // 2% commission is too high
        slippage: 0.0005
      };

      const result = BacktestConfigSchema.safeParse(invalidConfig);
      expect(result.success).toBe(false);
    });

    it('should accept minimal configuration', () => {
      const minimalConfig = {
        initialCapital: 1000,
        positionSize: 0.01,
        commission: 0,
        slippage: 0
      };

      const result = BacktestConfigSchema.safeParse(minimalConfig);
      expect(result.success).toBe(true);
    });
  });

  describe('BacktestRequestSchema', () => {
    it('should validate complete backtest request', () => {
      const validRequest = {
        strategy: {
          name: 'SMA Crossover',
          code: 'def trading_strategy(data, params): return {"signal": "buy"}',
          parameters: {
            shortPeriod: 20,
            longPeriod: 50,
            stopLoss: 0.02,
            takeProfit: 0.05
          }
        },
        marketData: {
          symbol: 'BTCUSD',
          timeframe: '4h',
          startDate: '2024-01-01',
          endDate: '2024-12-31'
        },
        config: {
          initialCapital: 10000,
          positionSize: 0.1,
          commission: 0.001,
          slippage: 0.0005
        }
      };

      const result = BacktestRequestSchema.safeParse(validRequest);
      expect(result.success).toBe(true);
    });

    it('should reject request with missing strategy', () => {
      const invalidRequest = {
        marketData: {
          symbol: 'BTCUSD',
          timeframe: '4h',
          startDate: '2024-01-01',
          endDate: '2024-12-31'
        },
        config: {
          initialCapital: 10000,
          positionSize: 0.1,
          commission: 0.001,
          slippage: 0.0005
        }
      };

      const result = BacktestRequestSchema.safeParse(invalidRequest);
      expect(result.success).toBe(false);
    });

    it('should reject request with missing market data', () => {
      const invalidRequest = {
        strategy: {
          name: 'Test Strategy',
          code: 'def trading_strategy(data, params): return {"signal": "buy"}',
          parameters: {}
        },
        config: {
          initialCapital: 10000,
          positionSize: 0.1,
          commission: 0.001,
          slippage: 0.0005
        }
      };

      const result = BacktestRequestSchema.safeParse(invalidRequest);
      expect(result.success).toBe(false);
    });

    it('should reject request with missing config', () => {
      const invalidRequest = {
        strategy: {
          name: 'Test Strategy',
          code: 'def trading_strategy(data, params): return {"signal": "buy"}',
          parameters: {}
        },
        marketData: {
          symbol: 'BTCUSD',
          timeframe: '4h',
          startDate: '2024-01-01',
          endDate: '2024-12-31'
        }
      };

      const result = BacktestRequestSchema.safeParse(invalidRequest);
      expect(result.success).toBe(false);
    });
  });

  describe('validateBacktestRequest', () => {
    it('should validate and return parsed data for valid request', () => {
      const validRequest = {
        strategy: {
          name: 'Test Strategy',
          code: 'def trading_strategy(data, params): return {"signal": "buy"}',
          parameters: { period: 20 }
        },
        marketData: {
          symbol: 'AAPL',
          timeframe: '1h',
          startDate: '2024-01-01',
          endDate: '2024-12-31'
        },
        config: {
          initialCapital: 10000,
          positionSize: 0.1,
          commission: 0.001,
          slippage: 0.0005
        }
      };

      const result = validateBacktestRequest(validRequest);
      expect(result).toBeDefined();
      expect(result.strategy.name).toBe('Test Strategy');
      expect(result.marketData.symbol).toBe('AAPL');
      expect(result.config.initialCapital).toBe(10000);
    });

    it('should throw ValidationError for invalid request', () => {
      const invalidRequest = {
        strategy: {
          name: '', // Invalid empty name
          code: 'def trading_strategy(data, params): return {"signal": "buy"}',
          parameters: {}
        },
        marketData: {
          symbol: 'AAPL',
          timeframe: '1h',
          startDate: '2024-01-01',
          endDate: '2024-12-31'
        },
        config: {
          initialCapital: 10000,
          positionSize: 0.1,
          commission: 0.001,
          slippage: 0.0005
        }
      };

      expect(() => validateBacktestRequest(invalidRequest)).toThrow(ValidationError);
    });

    it('should provide detailed error information', () => {
      const invalidRequest = {
        strategy: {
          name: '', // Invalid
          code: 'x=1', // Invalid
          parameters: {}
        },
        marketData: {
          symbol: 'invalid!', // Invalid
          timeframe: '3h', // Invalid
          startDate: '2024-01-01',
          endDate: '2024-12-31'
        },
        config: {
          initialCapital: 50, // Invalid
          positionSize: 0.1,
          commission: 0.001,
          slippage: 0.0005
        }
      };

      try {
        validateBacktestRequest(invalidRequest);
        fail('Should have thrown ValidationError');
      } catch (error) {
        expect(error).toBeInstanceOf(ValidationError);
        if (error instanceof ValidationError) {
          expect(error.errors.length).toBeGreaterThan(0);
          expect(error.errors.some(e => e.path.includes('strategy'))).toBe(true);
          expect(error.errors.some(e => e.path.includes('marketData'))).toBe(true);
          expect(error.errors.some(e => e.path.includes('config'))).toBe(true);
        }
      }
    });
  });

  describe('Edge cases and security', () => {
    it('should handle null and undefined values', () => {
      const nullRequest = null;
      const undefinedRequest = undefined;

      expect(() => validateBacktestRequest(nullRequest)).toThrow(ValidationError);
      expect(() => validateBacktestRequest(undefinedRequest)).toThrow(ValidationError);
    });

    it('should handle malformed objects', () => {
      const malformedRequest = {
        strategy: 'not an object',
        marketData: 123,
        config: []
      };

      expect(() => validateBacktestRequest(malformedRequest)).toThrow(ValidationError);
    });

    it('should prevent code injection in strategy name', () => {
      const injectionRequest = {
        strategy: {
          name: '<script>alert("xss")</script>',
          code: 'def trading_strategy(data, params): return {"signal": "buy"}',
          parameters: {}
        },
        marketData: {
          symbol: 'AAPL',
          timeframe: '1h',
          startDate: '2024-01-01',
          endDate: '2024-12-31'
        },
        config: {
          initialCapital: 10000,
          positionSize: 0.1,
          commission: 0.001,
          slippage: 0.0005
        }
      };

      // Should reject due to invalid characters in strategy name
      expect(() => validateBacktestRequest(injectionRequest)).toThrow(ValidationError);
    });

    it('should handle very large numbers', () => {
      const largeNumberRequest = {
        strategy: {
          name: 'Test',
          code: 'def trading_strategy(data, params): return {"signal": "buy"}',
          parameters: {}
        },
        marketData: {
          symbol: 'AAPL',
          timeframe: '1h',
          startDate: '2024-01-01',
          endDate: '2024-12-31'
        },
        config: {
          initialCapital: Number.MAX_SAFE_INTEGER,
          positionSize: 0.1,
          commission: 0.001,
          slippage: 0.0005
        }
      };

      expect(() => validateBacktestRequest(largeNumberRequest)).toThrow(ValidationError);
    });
  });
});