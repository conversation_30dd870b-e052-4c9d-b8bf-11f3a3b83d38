import { z } from 'zod';
import { ApiResponseSchema, PaginationRequestSchema, PaginationResponseSchema } from './common.schemas';
import { BacktestSchema, CreateBacktestRequestSchema, BacktestResultsSchema } from './backtest.schemas';
import { ChatSessionSchema, ChatMessageSchema, /* ChatRequestSchema, */ ChatResponseSchema } from './chat.schemas';
import { DataFileUploadSchema, CreateUploadRequestSchema, ColumnMappingRequestSchema } from './upload.schemas';
import { OrderRequestSchema, OrderResultSchema, AccountInfoSchema, MLPredictionSchema } from './trading.schemas';

// Auth API Schemas
export const LoginApiRequestSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
});

export const LoginApiResponseSchema = ApiResponseSchema(z.object({
  user: z.object({
    id: z.string(),
    email: z.string(),
    fullName: z.string().optional(),
    subscriptionTier: z.enum(['free', 'solo', 'pro', 'enterprise']),
  }),
  tokens: z.object({
    accessToken: z.string(),
    refreshToken: z.string(),
    expiresIn: z.number(),
  }),
}));

export const RegisterApiRequestSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
  fullName: z.string().optional(),
  acceptTerms: z.boolean().refine(val => val === true, {
    message: "Must accept terms and conditions"
  }),
});

export const RegisterApiResponseSchema = LoginApiResponseSchema;

// Trading API Schemas
export const SubmitOrderApiRequestSchema = OrderRequestSchema;
export const SubmitOrderApiResponseSchema = ApiResponseSchema(OrderResultSchema);

export const GetAccountInfoApiResponseSchema = ApiResponseSchema(AccountInfoSchema);

export const GetPositionsApiResponseSchema = ApiResponseSchema(z.array(z.object({
  position_id: z.number(),
  symbol: z.string(),
  volume: z.number(),
  open_price: z.number(),
  current_price: z.number(),
  pnl: z.number(),
  order_type: z.enum(['buy', 'sell']),
  open_time: z.date(),
})));

// Backtest API Schemas  
export const CreateBacktestApiRequestSchema = CreateBacktestRequestSchema;
export const CreateBacktestApiResponseSchema = ApiResponseSchema(BacktestSchema);

export const GetBacktestsApiRequestSchema = PaginationRequestSchema.extend({
  status: z.enum(['pending', 'running', 'completed', 'error']).optional(),
  symbol: z.string().optional(),
});

export const GetBacktestsApiResponseSchema = ApiResponseSchema(z.object({
  backtests: z.array(BacktestSchema),
  pagination: PaginationResponseSchema,
}));

export const GetBacktestApiResponseSchema = ApiResponseSchema(BacktestSchema);

export const GetBacktestResultsApiResponseSchema = ApiResponseSchema(BacktestResultsSchema);

export const DeleteBacktestApiResponseSchema = ApiResponseSchema(z.object({
  deleted: z.boolean(),
}));

// Chat API Schemas
export const CreateChatSessionApiRequestSchema = z.object({
  title: z.string().min(1).max(255).optional(),
  context: z.object({
    trading_symbols: z.array(z.string()).optional(),
    timeframe: z.string().optional(),
    strategy_focus: z.string().optional(),
    risk_tolerance: z.enum(['low', 'medium', 'high']).optional(),
  }).optional(),
});

export const CreateChatSessionApiResponseSchema = ApiResponseSchema(ChatSessionSchema);

export const GetChatSessionsApiRequestSchema = PaginationRequestSchema;

export const GetChatSessionsApiResponseSchema = ApiResponseSchema(z.object({
  sessions: z.array(ChatSessionSchema),
  pagination: PaginationResponseSchema,
}));

export const GetChatMessagesApiRequestSchema = PaginationRequestSchema;

export const GetChatMessagesApiResponseSchema = ApiResponseSchema(z.object({
  messages: z.array(ChatMessageSchema),
  pagination: PaginationResponseSchema,
}));

export const SendChatMessageApiRequestSchema = z.object({
  message: z.string().min(1),
  session_id: z.string().uuid(),
});

export const SendChatMessageApiResponseSchema = ApiResponseSchema(ChatResponseSchema);

// Upload API Schemas
export const CreateUploadApiRequestSchema = CreateUploadRequestSchema;
export const CreateUploadApiResponseSchema = ApiResponseSchema(DataFileUploadSchema);

export const GetUploadsApiRequestSchema = PaginationRequestSchema.extend({
  status: z.enum(['pending', 'uploading', 'mapping', 'parsing', 'validating', 'ready', 'error']).optional(),
  symbol: z.string().optional(),
});

export const GetUploadsApiResponseSchema = ApiResponseSchema(z.object({
  uploads: z.array(DataFileUploadSchema),
  pagination: PaginationResponseSchema,
}));

export const GetUploadApiResponseSchema = ApiResponseSchema(DataFileUploadSchema);

export const UpdateColumnMappingApiRequestSchema = ColumnMappingRequestSchema;
export const UpdateColumnMappingApiResponseSchema = ApiResponseSchema(DataFileUploadSchema);

export const GetFilePreviewApiResponseSchema = ApiResponseSchema(z.object({
  headers: z.array(z.string()),
  sample_rows: z.array(z.array(z.string())),
  detected_delimiter: z.string().optional(),
  detected_encoding: z.string().optional(),
  estimated_rows: z.number(),
}));

export const DeleteUploadApiResponseSchema = ApiResponseSchema(z.object({
  deleted: z.boolean(),
}));

// ML Prediction API Schemas
export const GetMLPredictionApiRequestSchema = z.object({
  symbol: z.string(),
  prediction_type: z.enum(['price', 'direction', 'volatility']),
  timeframe: z.string(),
});

export const GetMLPredictionApiResponseSchema = ApiResponseSchema(MLPredictionSchema);

export const GetMLPredictionsApiRequestSchema = PaginationRequestSchema.extend({
  symbol: z.string().optional(),
  prediction_type: z.enum(['price', 'direction', 'volatility']).optional(),
  min_confidence: z.number().min(0).max(1).optional(),
});

export const GetMLPredictionsApiResponseSchema = ApiResponseSchema(z.object({
  predictions: z.array(MLPredictionSchema),
  pagination: PaginationResponseSchema,
}));

// Dashboard API Schemas
export const GetDashboardDataApiResponseSchema = ApiResponseSchema(z.object({
  account_summary: z.object({
    balance: z.number(),
    equity: z.number(),
    margin_used: z.number(),
    free_margin: z.number(),
    profit_loss: z.number(),
  }),
  active_positions: z.array(z.object({
    symbol: z.string(),
    volume: z.number(),
    pnl: z.number(),
    open_price: z.number(),
    current_price: z.number(),
  })),
  recent_trades: z.array(z.object({
    symbol: z.string(),
    order_type: z.enum(['buy', 'sell']),
    volume: z.number(),
    pnl: z.number(),
    close_time: z.date(),
  })),
  performance_metrics: z.object({
    today_pnl: z.number(),
    week_pnl: z.number(),
    month_pnl: z.number(),
    win_rate: z.number(),
    total_trades: z.number(),
  }),
  market_overview: z.array(z.object({
    symbol: z.string(),
    current_price: z.number(),
    change: z.number(),
    change_percent: z.number(),
  })),
}));

// WebSocket API Schemas
export const WebSocketMessageSchema = z.object({
  type: z.enum([
    'price_update', 'position_update', 'order_update', 
    'backtest_progress', 'chat_response', 'error'
  ]),
  payload: z.any(),
  timestamp: z.date(),
  id: z.string().uuid().optional(),
});

export const WebSocketSubscriptionSchema = z.object({
  type: z.enum(['subscribe', 'unsubscribe']),
  channel: z.enum([
    'prices', 'positions', 'orders', 'backtest_progress', 'chat_updates'
  ]),
  symbol: z.string().optional(),
  session_id: z.string().uuid().optional(),
});

// Error Response Schemas
export const ValidationErrorResponseSchema = ApiResponseSchema(z.never()).extend({
  error: z.object({
    code: z.literal('VALIDATION_ERROR'),
    message: z.string(),
    details: z.array(z.object({
      field: z.string(),
      message: z.string(),
      value: z.any().optional(),
    })),
  }),
});

export const AuthErrorResponseSchema = ApiResponseSchema(z.never()).extend({
  error: z.object({
    code: z.enum(['UNAUTHORIZED', 'FORBIDDEN', 'TOKEN_EXPIRED']),
    message: z.string(),
    details: z.string().optional(),
  }),
});

export const RateLimitErrorResponseSchema = ApiResponseSchema(z.never()).extend({
  error: z.object({
    code: z.literal('RATE_LIMIT_EXCEEDED'),
    message: z.string(),
    retry_after: z.number().optional(),
  }),
});

// Health Check Schema
export const HealthCheckResponseSchema = z.object({
  status: z.enum(['healthy', 'degraded', 'unhealthy']),
  timestamp: z.date(),
  services: z.object({
    database: z.enum(['healthy', 'unhealthy']),
    python_engine: z.enum(['healthy', 'unhealthy']),
    redis: z.enum(['healthy', 'unhealthy']).optional(),
  }),
  version: z.string(),
  uptime: z.number(),
});

// Type exports for API responses
export type LoginApiRequest = z.infer<typeof LoginApiRequestSchema>;
export type LoginApiResponse = z.infer<typeof LoginApiResponseSchema>;
export type RegisterApiRequest = z.infer<typeof RegisterApiRequestSchema>;
export type RegisterApiResponse = z.infer<typeof RegisterApiResponseSchema>;

export type CreateBacktestApiRequest = z.infer<typeof CreateBacktestApiRequestSchema>;
export type CreateBacktestApiResponse = z.infer<typeof CreateBacktestApiResponseSchema>;
export type GetBacktestsApiRequest = z.infer<typeof GetBacktestsApiRequestSchema>;
export type GetBacktestsApiResponse = z.infer<typeof GetBacktestsApiResponseSchema>;

export type SendChatMessageApiRequest = z.infer<typeof SendChatMessageApiRequestSchema>;
export type SendChatMessageApiResponse = z.infer<typeof SendChatMessageApiResponseSchema>;

export type WebSocketMessage = z.infer<typeof WebSocketMessageSchema>;
export type WebSocketSubscription = z.infer<typeof WebSocketSubscriptionSchema>;

export type HealthCheckResponse = z.infer<typeof HealthCheckResponseSchema>;