#!/usr/bin/env python3
"""
Streamlined MVP Test Runner - TDD Approach

This script provides a focused test runner for the MVP version of the trading platform.
It consolidates all MVP tests into a single, easy-to-run command.

Key Features:
- Runs only MVP-critical tests
- Clear pass/fail reporting
- Minimal dependencies
- Fast execution
- TDD-focused output

Usage:
    python tests/run_mvp_tests.py [--verbose] [--offline] [--coverage]
"""

import sys
import os
import argparse
import logging
import time
from pathlib import Path
from typing import List, Dict, Any

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("mvp_test_runner")

# MVP Test Modules (in order of priority)
MVP_TEST_MODULES = [
    "tests.mvp.test_core_trading",
    "tests.mvp.test_mt5_integration", 
    "tests.mvp.test_api_endpoints",
    "tests.mvp.test_ui_integration"
]

# Test categories for selective running
TEST_CATEGORIES = {
    "core": ["tests.mvp.test_core_trading"],
    "mt5": ["tests.mvp.test_mt5_integration"],
    "api": ["tests.mvp.test_api_endpoints"],
    "ui": ["tests.mvp.test_ui_integration"],
    "backend": ["tests.mvp.test_core_trading", "tests.mvp.test_mt5_integration"],
    "frontend": ["tests.mvp.test_api_endpoints", "tests.mvp.test_ui_integration"],
    "all": MVP_TEST_MODULES
}


class MVPTestRunner:
    """Streamlined test runner for MVP tests"""
    
    def __init__(self, verbose=False, offline=True, coverage=False):
        self.verbose = verbose
        self.offline = offline
        self.coverage = coverage
        self.results = {
            "total": 0,
            "passed": 0,
            "failed": 0,
            "errors": 0,
            "skipped": 0,
            "modules": {}
        }
    
    def run_pytest_tests(self, modules: List[str]) -> bool:
        """Run tests using pytest"""
        try:
            import pytest
            
            # Build pytest arguments
            pytest_args = []
            
            # Add test modules
            for module in modules:
                module_path = module.replace(".", "/") + ".py"
                if Path(module_path).exists():
                    pytest_args.append(module_path)
                else:
                    logger.warning(f"Test module not found: {module_path}")
            
            # Add verbosity
            if self.verbose:
                pytest_args.extend(["-v", "-s"])
            else:
                pytest_args.append("-q")
            
            # Add offline mode marker
            if self.offline:
                pytest_args.extend(["-m", "not live"])
            
            # Add coverage
            if self.coverage:
                pytest_args.extend(["--cov=src", "--cov-report=term-missing"])
            
            # Add output formatting
            pytest_args.extend([
                "--tb=short",
                "--disable-warnings"
            ])
            
            logger.info(f"Running pytest with args: {pytest_args}")
            
            # Run tests
            exit_code = pytest.main(pytest_args)
            
            return exit_code == 0
            
        except ImportError:
            logger.error("pytest not available, falling back to unittest")
            return self.run_unittest_tests(modules)
    
    def run_unittest_tests(self, modules: List[str]) -> bool:
        """Run tests using unittest (fallback)"""
        import unittest
        import importlib
        
        # Create test suite
        test_suite = unittest.TestSuite()
        
        for module_name in modules:
            try:
                # Import the test module
                module = importlib.import_module(module_name)
                
                # Discover tests in the module
                loader = unittest.TestLoader()
                module_tests = loader.loadTestsFromModule(module)
                test_suite.addTest(module_tests)
                
                logger.info(f"Loaded tests from {module_name}")
                
            except ImportError as e:
                logger.error(f"Failed to import {module_name}: {e}")
                continue
            except Exception as e:
                logger.error(f"Error loading tests from {module_name}: {e}")
                continue
        
        # Run the tests
        runner = unittest.TextTestRunner(
            verbosity=2 if self.verbose else 1,
            stream=sys.stdout
        )
        
        result = runner.run(test_suite)
        
        # Update results
        self.results["total"] = result.testsRun
        self.results["failed"] = len(result.failures)
        self.results["errors"] = len(result.errors)
        self.results["skipped"] = len(result.skipped) if hasattr(result, 'skipped') else 0
        self.results["passed"] = (
            self.results["total"] - 
            self.results["failed"] - 
            self.results["errors"] - 
            self.results["skipped"]
        )
        
        return result.wasSuccessful()
    
    def run_simple_tests(self, modules: List[str]) -> bool:
        """Run tests using simple import and execution (minimal dependencies)"""
        import importlib
        
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        
        for module_name in modules:
            try:
                logger.info(f"Testing {module_name}...")
                
                # Import the module
                module = importlib.import_module(module_name)
                
                # Look for test functions
                test_functions = [
                    getattr(module, name) for name in dir(module)
                    if name.startswith('test_') and callable(getattr(module, name))
                ]
                
                # Look for test classes
                test_classes = [
                    getattr(module, name) for name in dir(module)
                    if name.startswith('Test') and isinstance(getattr(module, name), type)
                ]
                
                module_tests = 0
                module_passed = 0
                module_failed = 0
                
                # Run test functions
                for test_func in test_functions:
                    try:
                        test_func()
                        module_passed += 1
                        if self.verbose:
                            logger.info(f"  ✓ {test_func.__name__}")
                    except Exception as e:
                        module_failed += 1
                        logger.error(f"  ✗ {test_func.__name__}: {e}")
                    module_tests += 1
                
                # Run test classes
                for test_class in test_classes:
                    test_methods = [
                        name for name in dir(test_class)
                        if name.startswith('test_') and callable(getattr(test_class, name))
                    ]
                    
                    for method_name in test_methods:
                        try:
                            # Create instance and run test
                            instance = test_class()
                            method = getattr(instance, method_name)
                            method()
                            module_passed += 1
                            if self.verbose:
                                logger.info(f"  ✓ {test_class.__name__}.{method_name}")
                        except Exception as e:
                            module_failed += 1
                            logger.error(f"  ✗ {test_class.__name__}.{method_name}: {e}")
                        module_tests += 1
                
                total_tests += module_tests
                passed_tests += module_passed
                failed_tests += module_failed
                
                self.results["modules"][module_name] = {
                    "total": module_tests,
                    "passed": module_passed,
                    "failed": module_failed
                }
                
                logger.info(f"  {module_name}: {module_passed}/{module_tests} passed")
                
            except ImportError as e:
                logger.error(f"Failed to import {module_name}: {e}")
                continue
            except Exception as e:
                logger.error(f"Error testing {module_name}: {e}")
                continue
        
        # Update overall results
        self.results["total"] = total_tests
        self.results["passed"] = passed_tests
        self.results["failed"] = failed_tests
        
        return failed_tests == 0
    
    def print_summary(self):
        """Print test results summary"""
        print("\n" + "="*60)
        print("MVP TEST RESULTS SUMMARY")
        print("="*60)
        
        # Overall results
        total = self.results["total"]
        passed = self.results["passed"]
        failed = self.results["failed"]
        errors = self.results["errors"]
        skipped = self.results["skipped"]
        
        print(f"Total Tests: {total}")
        print(f"Passed: {passed} ✓")
        if failed > 0:
            print(f"Failed: {failed} ✗")
        if errors > 0:
            print(f"Errors: {errors} ⚠")
        if skipped > 0:
            print(f"Skipped: {skipped} ⊝")
        
        # Success rate
        if total > 0:
            success_rate = (passed / total) * 100
            print(f"Success Rate: {success_rate:.1f}%")
        
        # Module breakdown
        if self.results["modules"]:
            print("\nModule Breakdown:")
            for module, results in self.results["modules"].items():
                module_name = module.split(".")[-1]
                print(f"  {module_name}: {results['passed']}/{results['total']} passed")
        
        # Final status
        print("\n" + "-"*60)
        if failed == 0 and errors == 0:
            print("🎉 ALL MVP TESTS PASSED! Ready for deployment.")
        else:
            print("❌ Some tests failed. Please fix issues before deployment.")
        print("-"*60)


def main():
    """Main entry point for MVP test runner"""
    parser = argparse.ArgumentParser(
        description="Streamlined MVP Test Runner for AI Enhanced Trading Platform"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose output"
    )
    
    parser.add_argument(
        "--offline",
        action="store_true",
        default=True,
        help="Run tests in offline mode (default: True)"
    )
    
    parser.add_argument(
        "--coverage",
        action="store_true",
        help="Enable code coverage reporting"
    )
    
    parser.add_argument(
        "--category", "-c",
        choices=list(TEST_CATEGORIES.keys()),
        default="all",
        help="Run specific test category"
    )
    
    parser.add_argument(
        "--simple",
        action="store_true",
        help="Use simple test runner (minimal dependencies)"
    )
    
    parser.add_argument(
        "--fast",
        action="store_true",
        help="Run only core tests for quick feedback"
    )
    
    args = parser.parse_args()
    
    # Determine which tests to run
    if args.fast:
        test_modules = TEST_CATEGORIES["core"]
    else:
        test_modules = TEST_CATEGORIES[args.category]
    
    # Create test runner
    runner = MVPTestRunner(
        verbose=args.verbose,
        offline=args.offline,
        coverage=args.coverage
    )
    
    # Print startup message
    print("🚀 MVP Test Suite - TDD Approach")
    print(f"Running {len(test_modules)} test module(s)")
    print(f"Category: {args.category}")
    print(f"Offline mode: {args.offline}")
    print("-" * 60)
    
    # Record start time
    start_time = time.time()
    
    # Run tests
    try:
        if args.simple:
            success = runner.run_simple_tests(test_modules)
        else:
            success = runner.run_pytest_tests(test_modules)
    except KeyboardInterrupt:
        print("\n⚠ Tests interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Test runner error: {e}")
        return 1
    
    # Record end time
    end_time = time.time()
    duration = end_time - start_time
    
    # Print results
    runner.print_summary()
    print(f"\nExecution time: {duration:.2f} seconds")
    
    # Return appropriate exit code
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())