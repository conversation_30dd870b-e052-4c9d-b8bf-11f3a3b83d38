"""
Strategy Template Manager
Provides pre-built strategy templates that users can customize
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field

from .models import StrategyTemplate, StrategyType, GeneratedStrategy, TestCase


class StrategyTemplateManager:
    """Manages pre-built strategy templates"""
    
    def __init__(self):
        self.templates = self._initialize_templates()
    
    def _initialize_templates(self) -> Dict[str, StrategyTemplate]:
        """Initialize built-in strategy templates"""
        templates = {}
        
        # Mean Reversion RSI Template
        templates["mean_reversion_rsi"] = StrategyTemplate(
            name="Mean Reversion RSI",
            description="Classic mean reversion strategy using RSI indicator",
            strategy_type=StrategyType.MEAN_REVERSION,
            template_code=self._get_mean_reversion_rsi_template(),
            parameters={
                "symbols": ["EURUSD"],
                "rsi_period": 14,
                "oversold_level": 30,
                "overbought_level": 70,
                "risk_per_trade": 0.02,
                "max_positions": 3
            },
            required_indicators=["rsi"],
            difficulty_level="beginner",
            estimated_performance={
                "sharpe_ratio": 1.2,
                "max_drawdown": 0.15,
                "win_rate": 0.55
            },
            tags=["mean_reversion", "rsi", "beginner", "forex"]
        )
        
        # Momentum MACD Template
        templates["momentum_macd"] = StrategyTemplate(
            name="Momentum MACD",
            description="Trend-following strategy using MACD crossovers",
            strategy_type=StrategyType.MOMENTUM,
            template_code=self._get_momentum_macd_template(),
            parameters={
                "symbols": ["EURUSD"],
                "macd_fast": 12,
                "macd_slow": 26,
                "macd_signal": 9,
                "risk_per_trade": 0.02,
                "max_positions": 5
            },
            required_indicators=["macd"],
            difficulty_level="intermediate",
            estimated_performance={
                "sharpe_ratio": 1.0,
                "max_drawdown": 0.20,
                "win_rate": 0.48
            },
            tags=["momentum", "macd", "intermediate", "trend_following"]
        )
        
        # Bollinger Bands Breakout Template
        templates["bollinger_breakout"] = StrategyTemplate(
            name="Bollinger Bands Breakout",
            description="Volatility breakout strategy using Bollinger Bands",
            strategy_type=StrategyType.BREAKOUT,
            template_code=self._get_bollinger_breakout_template(),
            parameters={
                "symbols": ["EURUSD"],
                "bb_period": 20,
                "bb_std": 2.0,
                "risk_per_trade": 0.015,
                "max_positions": 3
            },
            required_indicators=["bollinger_bands"],
            difficulty_level="intermediate",
            estimated_performance={
                "sharpe_ratio": 0.9,
                "max_drawdown": 0.25,
                "win_rate": 0.42
            },
            tags=["breakout", "bollinger_bands", "volatility", "intermediate"]
        )
        
        # Machine Learning Template
        templates["ml_random_forest"] = StrategyTemplate(
            name="ML Random Forest",
            description="Machine learning strategy using Random Forest classifier",
            strategy_type=StrategyType.MACHINE_LEARNING,
            template_code=self._get_ml_random_forest_template(),
            parameters={
                "symbols": ["EURUSD"],
                "features": ["rsi", "macd", "price_momentum"],
                "training_bars": 1000,
                "retrain_frequency": 100,
                "risk_per_trade": 0.02,
                "max_positions": 3
            },
            required_indicators=["rsi", "macd"],
            difficulty_level="advanced",
            estimated_performance={
                "sharpe_ratio": 1.5,
                "max_drawdown": 0.18,
                "win_rate": 0.58
            },
            tags=["machine_learning", "random_forest", "advanced", "ai"]
        )
        
        # Multi-Timeframe Template
        templates["multi_timeframe"] = StrategyTemplate(
            name="Multi-Timeframe Strategy",
            description="Strategy that analyzes multiple timeframes for better signals",
            strategy_type=StrategyType.MOMENTUM,
            template_code=self._get_multi_timeframe_template(),
            parameters={
                "symbols": ["EURUSD"],
                "primary_timeframe": "1H",
                "secondary_timeframe": "4H",
                "risk_per_trade": 0.02,
                "max_positions": 2
            },
            required_indicators=["sma", "rsi"],
            difficulty_level="advanced",
            estimated_performance={
                "sharpe_ratio": 1.3,
                "max_drawdown": 0.16,
                "win_rate": 0.52
            },
            tags=["multi_timeframe", "advanced", "momentum", "complex"]
        )
        
        return templates
    
    def get_available_templates(self) -> List[StrategyTemplate]:
        """Get all available templates"""
        return list(self.templates.values())
    
    def get_template(self, template_name: str) -> Optional[StrategyTemplate]:
        """Get specific template by name"""
        return self.templates.get(template_name)
    
    def get_templates_by_difficulty(self, difficulty: str) -> List[StrategyTemplate]:
        """Get templates filtered by difficulty level"""
        return [t for t in self.templates.values() if t.difficulty_level == difficulty]
    
    def get_templates_by_strategy_type(self, strategy_type: StrategyType) -> List[StrategyTemplate]:
        """Get templates filtered by strategy type"""
        return [t for t in self.templates.values() if t.strategy_type == strategy_type]
    
    def search_templates(self, query: str) -> List[StrategyTemplate]:
        """Search templates by name, description, or tags"""
        query_lower = query.lower()
        results = []
        
        for template in self.templates.values():
            if (query_lower in template.name.lower() or 
                query_lower in template.description.lower() or
                any(query_lower in tag.lower() for tag in template.tags)):
                results.append(template)
        
        return results
    
    def customize_template(self, template: StrategyTemplate, custom_params: Dict[str, Any]) -> GeneratedStrategy:
        """Customize a template with user-specific parameters"""
        
        # Start with template code
        customized_code = template.template_code
        
        # Apply customizations
        for param_name, param_value in custom_params.items():
            customized_code = self._apply_parameter_customization(
                customized_code, param_name, param_value
            )
        
        # Generate strategy name
        strategy_name = f"{template.name} (Customized)"
        class_name = template.name.replace(" ", "") + "Strategy"
        
        # Create test cases
        test_cases = self._generate_template_test_cases(template, custom_params)
        
        # Generate documentation
        documentation = self._generate_template_documentation(template, custom_params)
        
        return GeneratedStrategy(
            code=customized_code,
            strategy_name=strategy_name,
            class_name=class_name,
            test_cases=test_cases,
            documentation=documentation,
            dependencies=["pandas", "numpy", "ta"] + (["scikit-learn"] if template.strategy_type == StrategyType.MACHINE_LEARNING else [])
        )
    
    def _apply_parameter_customization(self, code: str, param_name: str, param_value: Any) -> str:
        """Apply parameter customization to template code"""
        
        import re
        
        # Handle different parameter types
        if param_name == "symbols":
            if isinstance(param_value, list):
                symbols_str = str(param_value)
                code = re.sub(r'symbols=\[.*?\]', f'symbols={symbols_str}', code)
        
        elif param_name in ["rsi_period", "macd_fast", "macd_slow", "macd_signal", "bb_period"]:
            code = re.sub(f'{param_name}: int = \\d+', f'{param_name}: int = {param_value}', code)
            code = re.sub(f'self.{param_name} = \\d+', f'self.{param_name} = {param_value}', code)
        
        elif param_name in ["oversold_level", "overbought_level", "bb_std", "risk_per_trade"]:
            code = re.sub(f'{param_name}: float = [\\d.]+', f'{param_name}: float = {param_value}', code)
            code = re.sub(f'self.{param_name} = [\\d.]+', f'self.{param_name} = {param_value}', code)
        
        elif param_name == "max_positions":
            code = re.sub(f'max_positions: int = \\d+', f'max_positions: int = {param_value}', code)
        
        elif param_name == "features" and isinstance(param_value, list):
            # For ML strategies, update feature list
            features_str = str(param_value)
            code = re.sub(r'features=\[.*?\]', f'features={features_str}', code)
        
        return code
    
    def _generate_template_test_cases(self, template: StrategyTemplate, custom_params: Dict[str, Any]) -> List[TestCase]:
        """Generate test cases for customized template"""
        
        test_cases = []
        class_name = template.name.replace(" ", "") + "Strategy"
        
        # Basic functionality test
        test_cases.append(TestCase(
            name="test_template_basic_functionality",
            description=f"Test basic functionality of {template.name}",
            test_data={"template": template.name},
            expected_signal="hold",
            expected_confidence=0.5,
            test_code=f'''def test_template_basic_functionality():
    strategy = {class_name}(symbols={custom_params.get("symbols", ["EURUSD"])})
    
    # Mock market data
    import pandas as pd
    data = {{
        'close': pd.Series([1.1000, 1.1010, 1.1020, 1.1015, 1.1005] * 10),
        'high': pd.Series([1.1005, 1.1015, 1.1025, 1.1020, 1.1010] * 10),
        'low': pd.Series([1.0995, 1.1005, 1.1015, 1.1010, 1.1000] * 10),
        'volume': pd.Series([1000, 1100, 1200, 1150, 1050] * 10)
    }}
    
    signal = strategy.generate_signal("EURUSD", data)
    
    assert signal is not None
    assert 'signal' in signal
    assert 'confidence' in signal
    assert signal['signal'] in ['buy', 'sell', 'hold']'''
        ))
        
        # Strategy-specific tests
        if template.strategy_type == StrategyType.MEAN_REVERSION:
            test_cases.append(self._create_mean_reversion_test(class_name))
        elif template.strategy_type == StrategyType.MOMENTUM:
            test_cases.append(self._create_momentum_test(class_name))
        elif template.strategy_type == StrategyType.MACHINE_LEARNING:
            test_cases.append(self._create_ml_test(class_name))
        
        return test_cases
    
    def _create_mean_reversion_test(self, class_name: str) -> TestCase:
        """Create mean reversion specific test"""
        return TestCase(
            name="test_mean_reversion_signals",
            description="Test mean reversion signal generation",
            test_data={"strategy_type": "mean_reversion"},
            expected_signal="buy",
            expected_confidence=0.7,
            test_code=f'''def test_mean_reversion_signals():
    strategy = {class_name}(symbols=["EURUSD"])
    
    # Create oversold condition
    import pandas as pd
    declining_prices = pd.Series([1.1100, 1.1080, 1.1060, 1.1040, 1.1020, 1.1000] * 5)
    data = {{
        'close': declining_prices,
        'high': declining_prices * 1.001,
        'low': declining_prices * 0.999,
        'volume': pd.Series([1000] * len(declining_prices))
    }}
    
    signal = strategy.generate_signal("EURUSD", data)
    
    # Should generate buy signal in oversold conditions
    assert signal['signal'] in ['buy', 'hold']'''
        )
    
    def _create_momentum_test(self, class_name: str) -> TestCase:
        """Create momentum specific test"""
        return TestCase(
            name="test_momentum_signals",
            description="Test momentum signal generation",
            test_data={"strategy_type": "momentum"},
            expected_signal="buy",
            expected_confidence=0.7,
            test_code=f'''def test_momentum_signals():
    strategy = {class_name}(symbols=["EURUSD"])
    
    # Create uptrend condition
    import pandas as pd
    rising_prices = pd.Series([1.1000, 1.1010, 1.1020, 1.1030, 1.1040, 1.1050] * 5)
    data = {{
        'close': rising_prices,
        'high': rising_prices * 1.001,
        'low': rising_prices * 0.999,
        'volume': pd.Series([1000] * len(rising_prices))
    }}
    
    signal = strategy.generate_signal("EURUSD", data)
    
    # Should generate buy signal in uptrend
    assert signal['signal'] in ['buy', 'hold']'''
        )
    
    def _create_ml_test(self, class_name: str) -> TestCase:
        """Create ML specific test"""
        return TestCase(
            name="test_ml_feature_preparation",
            description="Test ML feature preparation",
            test_data={"strategy_type": "machine_learning"},
            expected_signal="hold",
            expected_confidence=0.0,
            test_code=f'''def test_ml_feature_preparation():
    strategy = {class_name}(symbols=["EURUSD"])
    
    # Create sufficient data for feature calculation
    import pandas as pd
    prices = pd.Series([1.1000 + i*0.0001 for i in range(100)])
    data = {{
        'close': prices,
        'high': prices * 1.001,
        'low': prices * 0.999,
        'volume': pd.Series([1000] * len(prices))
    }}
    
    features = strategy.prepare_features(data)
    
    assert isinstance(features, dict)
    assert len(features) > 0'''
        )
    
    def _generate_template_documentation(self, template: StrategyTemplate, custom_params: Dict[str, Any]) -> str:
        """Generate documentation for customized template"""
        
        doc_parts = [
            f"# {template.name} (Customized)",
            "",
            f"## Description",
            template.description,
            "",
            f"## Strategy Type",
            template.strategy_type.value.replace('_', ' ').title(),
            "",
            f"## Difficulty Level",
            template.difficulty_level.title(),
            "",
            f"## Configuration",
        ]
        
        # Add customized parameters
        for param_name, param_value in custom_params.items():
            doc_parts.append(f"- **{param_name.replace('_', ' ').title()}**: {param_value}")
        
        # Add required indicators
        if template.required_indicators:
            doc_parts.extend([
                "",
                "## Required Indicators",
                *[f"- {indicator.upper()}" for indicator in template.required_indicators]
            ])
        
        # Add estimated performance
        if template.estimated_performance:
            doc_parts.extend([
                "",
                "## Estimated Performance",
                f"- **Sharpe Ratio**: {template.estimated_performance.get('sharpe_ratio', 'N/A')}",
                f"- **Max Drawdown**: {template.estimated_performance.get('max_drawdown', 'N/A')}",
                f"- **Win Rate**: {template.estimated_performance.get('win_rate', 'N/A')}"
            ])
        
        doc_parts.extend([
            "",
            "## Usage",
            "```python",
            f"from src.strategies.{template.name.lower().replace(' ', '_')} import {template.name.replace(' ', '')}Strategy",
            f"strategy = {template.name.replace(' ', '')}Strategy(symbols={custom_params.get('symbols', ['EURUSD'])})",
            "signal = strategy.generate_signal('EURUSD', market_data)",
            "```",
            "",
            "## Tags",
            ", ".join(template.tags)
        ])
        
        return '\n'.join(doc_parts)
    
    # Template code definitions
    def _get_mean_reversion_rsi_template(self) -> str:
        """Get mean reversion RSI template code"""
        return '''"""
Mean Reversion RSI Strategy Template
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from src.strategies.strategy_base import StrategyBase
from ta.momentum import RSIIndicator


class MeanReversionRSIStrategy(StrategyBase):
    """
    Mean Reversion Strategy using RSI
    
    Generates buy signals when RSI is oversold and sell signals when overbought.
    """
    
    def __init__(self,
                 symbols: List[str],
                 timeframe: str = '1H',
                 rsi_period: int = 14,
                 oversold_level: float = 30,
                 overbought_level: float = 70,
                 risk_per_trade: float = 0.02,
                 max_positions: int = 3,
                 mt5_bridge: Optional[MT5Bridge] = None):
        
        super().__init__(
            name="Mean Reversion RSI",
            symbols=symbols,
            mt5_bridge=mt5_bridge,
            risk_per_trade=risk_per_trade,
            max_open_positions=max_positions
        )
        
        self.timeframe = timeframe
        self.rsi_period = rsi_period
        self.oversold_level = oversold_level
        self.overbought_level = overbought_level
    
    def calculate_rsi(self, prices: pd.Series, period: Optional[int] = None) -> pd.Series:
        """Calculate RSI indicator"""
        period = period or self.rsi_period
        rsi_indicator = RSIIndicator(close=prices, window=period)
        return rsi_indicator.rsi()
    
    def generate_signal(self, symbol: str, data: Dict[str, pd.Series]) -> Dict[str, Any]:
        """Generate trading signal based on RSI"""
        try:
            rsi = self.calculate_rsi(data['close'])
            current_rsi = rsi.iloc[-1]
            
            if current_rsi < self.oversold_level:
                return {
                    "signal": "buy",
                    "confidence": 0.8,
                    "reason": f"RSI oversold: {current_rsi:.2f}"
                }
            elif current_rsi > self.overbought_level:
                return {
                    "signal": "sell", 
                    "confidence": 0.8,
                    "reason": f"RSI overbought: {current_rsi:.2f}"
                }
            else:
                return {
                    "signal": "hold",
                    "confidence": 0.5,
                    "reason": f"RSI neutral: {current_rsi:.2f}"
                }
                
        except Exception as e:
            self.logger.error(f"Signal generation error for {symbol}: {e}")
            return {"signal": "hold", "confidence": 0.0, "reason": f"Error: {e}"}
    
    def calculate_position_size(self, symbol: str, signal: Dict[str, Any], account_balance: float) -> float:
        """Calculate position size based on risk management"""
        if signal['signal'] == 'hold':
            return 0.0
        
        risk_amount = account_balance * self.risk_per_trade
        confidence_multiplier = signal.get('confidence', 0.5)
        adjusted_risk = risk_amount * confidence_multiplier
        
        # Simplified position sizing
        position_size = adjusted_risk / 100
        return min(position_size, account_balance * 0.1)'''
    
    def _get_momentum_macd_template(self) -> str:
        """Get momentum MACD template code"""
        return '''"""
Momentum MACD Strategy Template
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from src.strategies.strategy_base import StrategyBase
from ta.trend import MACD


class MomentumMACDStrategy(StrategyBase):
    """
    Momentum Strategy using MACD
    
    Generates signals based on MACD line and signal line crossovers.
    """
    
    def __init__(self,
                 symbols: List[str],
                 timeframe: str = '1H',
                 macd_fast: int = 12,
                 macd_slow: int = 26,
                 macd_signal: int = 9,
                 risk_per_trade: float = 0.02,
                 max_positions: int = 5,
                 mt5_bridge: Optional[MT5Bridge] = None):
        
        super().__init__(
            name="Momentum MACD",
            symbols=symbols,
            mt5_bridge=mt5_bridge,
            risk_per_trade=risk_per_trade,
            max_open_positions=max_positions
        )
        
        self.timeframe = timeframe
        self.macd_fast = macd_fast
        self.macd_slow = macd_slow
        self.macd_signal = macd_signal
    
    def calculate_macd(self, prices: pd.Series) -> Dict[str, pd.Series]:
        """Calculate MACD indicator"""
        macd = MACD(close=prices,
                   window_fast=self.macd_fast,
                   window_slow=self.macd_slow,
                   window_sign=self.macd_signal)
        return {
            'macd': macd.macd(),
            'signal': macd.macd_signal(),
            'histogram': macd.macd_diff()
        }
    
    def generate_signal(self, symbol: str, data: Dict[str, pd.Series]) -> Dict[str, Any]:
        """Generate trading signal based on MACD"""
        try:
            macd_data = self.calculate_macd(data['close'])
            macd_line = macd_data['macd'].iloc[-1]
            signal_line = macd_data['signal'].iloc[-1]
            prev_macd = macd_data['macd'].iloc[-2]
            prev_signal = macd_data['signal'].iloc[-2]
            
            # Bullish crossover
            if macd_line > signal_line and prev_macd <= prev_signal:
                return {
                    "signal": "buy",
                    "confidence": 0.8,
                    "reason": "MACD bullish crossover"
                }
            # Bearish crossover
            elif macd_line < signal_line and prev_macd >= prev_signal:
                return {
                    "signal": "sell",
                    "confidence": 0.8,
                    "reason": "MACD bearish crossover"
                }
            else:
                return {
                    "signal": "hold",
                    "confidence": 0.5,
                    "reason": "No MACD crossover"
                }
                
        except Exception as e:
            self.logger.error(f"Signal generation error for {symbol}: {e}")
            return {"signal": "hold", "confidence": 0.0, "reason": f"Error: {e}"}
    
    def calculate_position_size(self, symbol: str, signal: Dict[str, Any], account_balance: float) -> float:
        """Calculate position size based on risk management"""
        if signal['signal'] == 'hold':
            return 0.0
        
        risk_amount = account_balance * self.risk_per_trade
        confidence_multiplier = signal.get('confidence', 0.5)
        adjusted_risk = risk_amount * confidence_multiplier
        
        position_size = adjusted_risk / 100
        return min(position_size, account_balance * 0.1)'''
    
    def _get_bollinger_breakout_template(self) -> str:
        """Get Bollinger Bands breakout template code"""
        return '''"""
Bollinger Bands Breakout Strategy Template
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from src.strategies.strategy_base import StrategyBase
from ta.volatility import BollingerBands


class BollingerBreakoutStrategy(StrategyBase):
    """
    Breakout Strategy using Bollinger Bands
    
    Generates signals when price breaks above or below Bollinger Bands.
    """
    
    def __init__(self,
                 symbols: List[str],
                 timeframe: str = '1H',
                 bb_period: int = 20,
                 bb_std: float = 2.0,
                 risk_per_trade: float = 0.015,
                 max_positions: int = 3,
                 mt5_bridge: Optional[MT5Bridge] = None):
        
        super().__init__(
            name="Bollinger Breakout",
            symbols=symbols,
            mt5_bridge=mt5_bridge,
            risk_per_trade=risk_per_trade,
            max_open_positions=max_positions
        )
        
        self.timeframe = timeframe
        self.bb_period = bb_period
        self.bb_std = bb_std
    
    def calculate_bollinger_bands(self, prices: pd.Series) -> Dict[str, pd.Series]:
        """Calculate Bollinger Bands"""
        bb = BollingerBands(close=prices, window=self.bb_period, window_dev=self.bb_std)
        return {
            'upper': bb.bollinger_hband(),
            'middle': bb.bollinger_mavg(),
            'lower': bb.bollinger_lband()
        }
    
    def generate_signal(self, symbol: str, data: Dict[str, pd.Series]) -> Dict[str, Any]:
        """Generate trading signal based on Bollinger Bands breakout"""
        try:
            bb = self.calculate_bollinger_bands(data['close'])
            current_price = data['close'].iloc[-1]
            upper_band = bb['upper'].iloc[-1]
            lower_band = bb['lower'].iloc[-1]
            
            # Breakout above upper band
            if current_price > upper_band:
                return {
                    "signal": "buy",
                    "confidence": 0.7,
                    "reason": f"Price breakout above upper BB: {current_price:.5f} > {upper_band:.5f}"
                }
            # Breakout below lower band
            elif current_price < lower_band:
                return {
                    "signal": "sell",
                    "confidence": 0.7,
                    "reason": f"Price breakout below lower BB: {current_price:.5f} < {lower_band:.5f}"
                }
            else:
                return {
                    "signal": "hold",
                    "confidence": 0.5,
                    "reason": "Price within Bollinger Bands"
                }
                
        except Exception as e:
            self.logger.error(f"Signal generation error for {symbol}: {e}")
            return {"signal": "hold", "confidence": 0.0, "reason": f"Error: {e}"}
    
    def calculate_position_size(self, symbol: str, signal: Dict[str, Any], account_balance: float) -> float:
        """Calculate position size based on risk management"""
        if signal['signal'] == 'hold':
            return 0.0
        
        risk_amount = account_balance * self.risk_per_trade
        confidence_multiplier = signal.get('confidence', 0.5)
        adjusted_risk = risk_amount * confidence_multiplier
        
        position_size = adjusted_risk / 100
        return min(position_size, account_balance * 0.1)'''
    
    def _get_ml_random_forest_template(self) -> str:
        """Get ML Random Forest template code"""
        return '''"""
Machine Learning Random Forest Strategy Template
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from src.strategies.strategy_base import StrategyBase
from ta.momentum import RSIIndicator
from ta.trend import MACD
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
import joblib


class MLRandomForestStrategy(StrategyBase):
    """
    Machine Learning Strategy using Random Forest
    
    Uses multiple technical indicators as features to predict price direction.
    """
    
    def __init__(self,
                 symbols: List[str],
                 timeframe: str = '1H',
                 features: List[str] = None,
                 training_bars: int = 1000,
                 retrain_frequency: int = 100,
                 risk_per_trade: float = 0.02,
                 max_positions: int = 3,
                 model_path: Optional[str] = None,
                 mt5_bridge: Optional[MT5Bridge] = None):
        
        super().__init__(
            name="ML Random Forest",
            symbols=symbols,
            mt5_bridge=mt5_bridge,
            risk_per_trade=risk_per_trade,
            max_open_positions=max_positions
        )
        
        self.timeframe = timeframe
        self.features = features or ["rsi", "macd", "price_momentum"]
        self.training_bars = training_bars
        self.retrain_frequency = retrain_frequency
        self.model_path = model_path
        
        self.model = None
        self.scaler = StandardScaler()
        self.bars_since_retrain = 0
    
    def prepare_features(self, data: Dict[str, pd.Series]) -> Dict[str, float]:
        """Prepare features for ML model"""
        features = {}
        
        try:
            if "rsi" in self.features:
                rsi_indicator = RSIIndicator(close=data['close'], window=14)
                features['rsi'] = rsi_indicator.rsi().iloc[-1]
            
            if "macd" in self.features:
                macd = MACD(close=data['close'], window_fast=12, window_slow=26, window_sign=9)
                features['macd'] = macd.macd().iloc[-1]
                features['macd_signal'] = macd.macd_signal().iloc[-1]
            
            if "price_momentum" in self.features:
                features['price_momentum'] = (data['close'].iloc[-1] / data['close'].iloc[-10] - 1) * 100
                
        except Exception as e:
            self.logger.warning(f"Error calculating features: {e}")
            return {}
        
        return features
    
    def train_model(self, historical_data: Dict[str, pd.DataFrame]) -> bool:
        """Train the ML model on historical data"""
        try:
            X, y = [], []
            
            for symbol, data in historical_data.items():
                for i in range(self.training_bars, len(data)):
                    features = self.prepare_features({
                        'close': data['close'].iloc[i-50:i],
                        'high': data['high'].iloc[i-50:i],
                        'low': data['low'].iloc[i-50:i],
                        'volume': data['volume'].iloc[i-50:i]
                    })
                    
                    if features:
                        X.append(list(features.values()))
                        future_return = (data['close'].iloc[i+1] / data['close'].iloc[i] - 1)
                        y.append(1 if future_return > 0.001 else 0)
            
            if len(X) < 100:
                self.logger.warning("Insufficient training data")
                return False
            
            X = np.array(X)
            y = np.array(y)
            
            X_scaled = self.scaler.fit_transform(X)
            
            self.model = RandomForestClassifier(n_estimators=100, random_state=42)
            self.model.fit(X_scaled, y)
            
            if self.model_path:
                joblib.dump({
                    'model': self.model,
                    'scaler': self.scaler
                }, self.model_path)
            
            self.bars_since_retrain = 0
            self.logger.info("Model training completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Model training failed: {e}")
            return False
    
    def predict_signal(self, data: Dict[str, pd.Series]) -> Dict[str, Any]:
        """Generate trading signal using ML model"""
        if self.model is None:
            return {"signal": "hold", "confidence": 0.0}
        
        try:
            features = self.prepare_features(data)
            if not features:
                return {"signal": "hold", "confidence": 0.0}
            
            X = np.array([list(features.values())])
            X_scaled = self.scaler.transform(X)
            
            prediction = self.model.predict(X_scaled)[0]
            probabilities = self.model.predict_proba(X_scaled)[0]
            
            confidence = max(probabilities)
            
            if prediction == 1 and confidence > 0.6:
                return {"signal": "buy", "confidence": confidence}
            elif prediction == 0 and confidence > 0.6:
                return {"signal": "sell", "confidence": confidence}
            else:
                return {"signal": "hold", "confidence": confidence}
                
        except Exception as e:
            self.logger.error(f"Prediction error: {e}")
            return {"signal": "hold", "confidence": 0.0}
    
    def generate_signal(self, symbol: str, data: Dict[str, pd.Series]) -> Dict[str, Any]:
        """Generate trading signal using ML model"""
        try:
            self.bars_since_retrain += 1
            if self.bars_since_retrain >= self.retrain_frequency:
                self.logger.info("Model retraining needed")
            
            return self.predict_signal(data)
            
        except Exception as e:
            self.logger.error(f"ML signal generation error for {symbol}: {e}")
            return {"signal": "hold", "confidence": 0.0, "reason": f"Error: {e}"}
    
    def calculate_position_size(self, symbol: str, signal: Dict[str, Any], account_balance: float) -> float:
        """Calculate position size based on risk management"""
        if signal['signal'] == 'hold':
            return 0.0
        
        risk_amount = account_balance * self.risk_per_trade
        confidence_multiplier = signal.get('confidence', 0.5)
        adjusted_risk = risk_amount * confidence_multiplier
        
        position_size = adjusted_risk / 100
        return min(position_size, account_balance * 0.1)'''
    
    def _get_multi_timeframe_template(self) -> str:
        """Get multi-timeframe template code"""
        return '''"""
Multi-Timeframe Strategy Template
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from src.strategies.strategy_base import StrategyBase
from ta.trend import SMAIndicator
from ta.momentum import RSIIndicator


class MultiTimeframeStrategy(StrategyBase):
    """
    Multi-Timeframe Strategy
    
    Analyzes multiple timeframes to generate higher quality signals.
    """
    
    def __init__(self,
                 symbols: List[str],
                 primary_timeframe: str = '1H',
                 secondary_timeframe: str = '4H',
                 risk_per_trade: float = 0.02,
                 max_positions: int = 2,
                 mt5_bridge: Optional[MT5Bridge] = None):
        
        super().__init__(
            name="Multi-Timeframe Strategy",
            symbols=symbols,
            mt5_bridge=mt5_bridge,
            risk_per_trade=risk_per_trade,
            max_open_positions=max_positions
        )
        
        self.primary_timeframe = primary_timeframe
        self.secondary_timeframe = secondary_timeframe
    
    def analyze_timeframe(self, data: Dict[str, pd.Series], timeframe: str) -> Dict[str, Any]:
        """Analyze single timeframe"""
        try:
            # Calculate indicators
            sma_20 = SMAIndicator(close=data['close'], window=20).sma_indicator()
            rsi = RSIIndicator(close=data['close'], window=14).rsi()
            
            current_price = data['close'].iloc[-1]
            current_sma = sma_20.iloc[-1]
            current_rsi = rsi.iloc[-1]
            
            # Determine trend
            trend = "bullish" if current_price > current_sma else "bearish"
            
            # Determine momentum
            momentum = "oversold" if current_rsi < 30 else "overbought" if current_rsi > 70 else "neutral"
            
            return {
                "trend": trend,
                "momentum": momentum,
                "price": current_price,
                "sma": current_sma,
                "rsi": current_rsi
            }
            
        except Exception as e:
            self.logger.error(f"Timeframe analysis error: {e}")
            return {"trend": "neutral", "momentum": "neutral"}
    
    def generate_signal(self, symbol: str, data: Dict[str, pd.Series]) -> Dict[str, Any]:
        """Generate signal based on multi-timeframe analysis"""
        try:
            # Analyze primary timeframe (for entry timing)
            primary_analysis = self.analyze_timeframe(data, self.primary_timeframe)
            
            # Analyze secondary timeframe (for trend direction)
            # Note: In practice, you'd fetch different timeframe data
            secondary_analysis = self.analyze_timeframe(data, self.secondary_timeframe)
            
            # Combine signals
            if (secondary_analysis["trend"] == "bullish" and 
                primary_analysis["momentum"] == "oversold"):
                return {
                    "signal": "buy",
                    "confidence": 0.8,
                    "reason": f"Higher TF bullish, lower TF oversold"
                }
            elif (secondary_analysis["trend"] == "bearish" and 
                  primary_analysis["momentum"] == "overbought"):
                return {
                    "signal": "sell",
                    "confidence": 0.8,
                    "reason": f"Higher TF bearish, lower TF overbought"
                }
            else:
                return {
                    "signal": "hold",
                    "confidence": 0.5,
                    "reason": "No multi-timeframe alignment"
                }
                
        except Exception as e:
            self.logger.error(f"Multi-timeframe signal error for {symbol}: {e}")
            return {"signal": "hold", "confidence": 0.0, "reason": f"Error: {e}"}
    
    def calculate_position_size(self, symbol: str, signal: Dict[str, Any], account_balance: float) -> float:
        """Calculate position size based on risk management"""
        if signal['signal'] == 'hold':
            return 0.0
        
        risk_amount = account_balance * self.risk_per_trade
        confidence_multiplier = signal.get('confidence', 0.5)
        adjusted_risk = risk_amount * confidence_multiplier
        
        position_size = adjusted_risk / 100
        return min(position_size, account_balance * 0.1)'''