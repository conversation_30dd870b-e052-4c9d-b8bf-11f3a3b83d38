README.md
pyproject.toml
python_engine/ai_enhanced_trading_platform.egg-info/PKG-INFO
python_engine/ai_enhanced_trading_platform.egg-info/SOURCES.txt
python_engine/ai_enhanced_trading_platform.egg-info/dependency_links.txt
python_engine/ai_enhanced_trading_platform.egg-info/top_level.txt
python_engine/api/worker_endpoints.py
python_engine/core/__init__.py
python_engine/core/dependency_injection.py
python_engine/core/interfaces.py
python_engine/core/service_configuration.py
python_engine/core/trading_engine.py
python_engine/examples/dependency_injection_demo.py
python_engine/scripts/test_pattern.py
python_engine/scripts/test_pattern_detection.py
python_engine/scripts/test_strategy.py
python_engine/services/__init__.py
python_engine/services/configuration_service.py
python_engine/services/logging_service.py
python_engine/services/market_data_service.py
python_engine/services/mock_services.py
python_engine/services/strategy_service.py
python_engine/services/darwin_godel/__init__.py
python_engine/services/darwin_godel/pattern_detector.py
python_engine/services/darwin_godel/pattern_report.py
python_engine/services/darwin_godel/secure_executor.py
python_engine/services/darwin_godel/strategy_verifier.py
python_engine/services/darwin_godel/test_interface.py
python_engine/services/darwin_godel/test_market_data_processor.py
python_engine/services/darwin_godel/test_portfolio_manager.py
python_engine/services/darwin_godel/test_risk_management.py
python_engine/services/darwin_godel/test_strategy_executor_comprehensive.py
python_engine/services/darwin_godel/__tests__/__init__.py
python_engine/services/darwin_godel/__tests__/test_integration_security_pattern.py
python_engine/services/darwin_godel/__tests__/test_pattern_detector.py
python_engine/services/darwin_godel/__tests__/test_secure_executor.py
python_engine/services/darwin_godel/__tests__/test_strategy_verifier.py
python_engine/services/strategy_executor/__init__.py
python_engine/services/strategy_executor/secure_executor.py
python_engine/services/strategy_executor/simple_secure_executor.py
python_engine/services/strategy_executor/__tests__/__init__.py
python_engine/services/strategy_executor/__tests__/test_secure_executor.py
python_engine/services/strategy_executor/__tests__/test_simple_secure_executor.py
python_engine/tests/test_architecture_demo.py
python_engine/tests/test_async_monitor.py
python_engine/tests/test_async_monitor_advanced.py
python_engine/tests/test_dependency_injection.py
python_engine/tests/test_integration_bridge.py
python_engine/tests/test_phase3_validation.py
python_engine/tests/test_portfolio_properties.py
python_engine/tests/test_simple_di.py
python_engine/tests/test_strategy_executor_comprehensive.py
python_engine/tests/test_trading_engine_integration.py
python_engine/tests/performance/__init__.py
python_engine/tests/performance/test_concurrent_execution.py
python_engine/tests/performance/test_high_frequency_trading.py
python_engine/tests/performance/test_latency_benchmarks.py
python_engine/tests/performance/test_memory_usage.py
python_engine/tests/property_based/__init__.py
python_engine/tests/property_based/test_portfolio_properties.py
python_engine/tests/property_based/test_strategy_properties.py
python_engine/tests/security/__init__.py
python_engine/tests/security/test_malicious_strategy_injection.py
python_engine/workers/__init__.py
python_engine/workers/backtest_runner.py
python_engine/workers/dgm_monitor.py
python_engine/workers/file_parser.py
python_engine/workers/worker_manager.py
tests/test_backtest.py
tests/test_backtest_engine.py
tests/test_backtest_tdd.py
tests/test_benchmarks.py
tests/test_chatbot_knowledge.py
tests/test_chatbot_regression.py
tests/test_critical_improvements_integration.py
tests/test_darwin_godel.py
tests/test_darwin_godel_machine.py
tests/test_data_loader.py
tests/test_data_validation.py
tests/test_dgm_optimizer.py
tests/test_doctest_strategies.py
tests/test_event_system.py
tests/test_historical_data.py
tests/test_history_tracker.py
tests/test_hypothesis_chatbot.py
tests/test_minimal_server.py
tests/test_ml_pipeline.py
tests/test_model_pipeline.py
tests/test_mt5_bridge.py
tests/test_mt5_bridge_advanced.py
tests/test_mt5_bridge_comprehensive.py
tests/test_mt5_bridge_integration.py
tests/test_mt5_bridge_property.py
tests/test_mt5_bridge_tdd.py
tests/test_mt5_integration.py
tests/test_mt5_mvp.py
tests/test_multi_source_feed.py
tests/test_mvp_core.py
tests/test_performance_monitor.py
tests/test_platform_integration.py
tests/test_portfolio_manager.py
tests/test_property_based.py
tests/test_risk_management.py
tests/test_risk_management_tdd.py
tests/test_risk_manager.py
tests/test_risk_manager2.py
tests/test_secure_mt5_bridge.py
tests/test_strategy_builder_tdd.py
tests/test_strategy_chatbot_integration.py
tests/test_strategy_execution_tdd.py
tests/test_strategy_ma_crossover.py
tests/test_strategy_templates_tdd.py