import re
from typing import Dict, List, Set, Tuple
from dataclasses import dataclass

@dataclass
class PatternAnalysis:
    pattern_type: str
    confidence: float
    indicators_found: List[str]
    characteristics: List[str]
    warnings: List[str] = None
    complexity_score: float = 0.0

class StrategyPatternDetector:
    """
    Detects common trading strategy patterns
    Helps classify and understand user strategies
    """
    
    def __init__(self):
        # Define indicator patterns to look for
        self.indicators = {
            'sma': r'calculate_sma|sma|simple.*moving.*average',
            'ema': r'calculate_ema|ema|exponential.*moving.*average',
            'rsi': r'calculate_rsi|rsi|relative.*strength',
            'macd': r'calculate_macd|macd',
            'bollinger': r'bollinger|calculate_bollinger',
            'stochastic': r'stochastic|stoch',
            'atr': r'atr|average.*true.*range',
            'volume': r'volume|vol',
        }
        
        # Define pattern characteristics
        self.mean_reversion_keywords = [
            'oversold', 'overbought', 'below.*average', 'above.*average',
            'reversion', 'mean', 'bounce', 'extreme'
        ]
        
        self.momentum_keywords = [
            'crossover', 'cross.*above', 'cross.*below', 'trend',
            'momentum', 'breakout', 'acceleration', 'strength'
        ]
        
        self.breakout_keywords = [
            'breakout', 'break.*above', 'break.*below', 'high', 'low',
            'resistance', 'support', 'range', 'channel'
        ]
    
    def analyze_strategy(self, strategy_code: str) -> Dict:
        """
        Analyze a strategy and detect its pattern
        
        Args:
            strategy_code: The Python strategy code
            
        Returns:
            Dictionary with pattern analysis results
        """
        # Extract features from code
        indicators = self._extract_indicators(strategy_code)
        characteristics = self._extract_characteristics(strategy_code)
        logic_patterns = self._analyze_logic_patterns(strategy_code)
        complexity_score = self._calculate_complexity(strategy_code)
        
        # Determine pattern type
        pattern_type, confidence = self._classify_pattern(
            indicators, characteristics, logic_patterns, strategy_code
        )
        
        # Generate warnings if needed
        warnings = self._generate_warnings(pattern_type, indicators, strategy_code, complexity_score)
        
        return {
            'pattern_type': pattern_type,
            'confidence': confidence,
            'indicators_found': indicators,
            'characteristics': characteristics,
            'warnings': warnings if warnings else [],
            'complexity_score': complexity_score
        }
    
    def _extract_indicators(self, code: str) -> List[str]:
        """Extract all technical indicators used in the strategy"""
        found_indicators = []
        code_lower = code.lower()
        
        for indicator, pattern in self.indicators.items():
            if re.search(pattern, code_lower):
                found_indicators.append(indicator)
        
        # Special case: detect multiple SMA/EMA instances
        sma_matches = len(re.findall(r'calculate_sma|sma', code_lower))
        ema_matches = len(re.findall(r'calculate_ema|ema', code_lower))
        
        # If we see multiple SMA calls, it likely means fast/slow MAs
        if sma_matches > 1 and 'sma' in found_indicators:
            # Add a second sma entry to indicate multiple SMAs
            found_indicators.append('sma')
        if ema_matches > 1 and 'ema' in found_indicators:
            found_indicators.append('ema')
        
        return found_indicators
    
    def _extract_characteristics(self, code: str) -> List[str]:
        """Extract strategy characteristics from the code"""
        characteristics = []
        code_lower = code.lower()
        
        # Check for mean reversion characteristics
        if re.search(r'<.*\*\s*0\.\d+', code):  # price < sma * 0.98
            characteristics.append('buying below average')
        if re.search(r'>.*\*\s*1\.\d+', code):  # price > sma * 1.02
            characteristics.append('selling above average')
            
        # Check for momentum characteristics
        if 'crossover' in code_lower or (
            re.search(r'>\s*.*and.*<=', code) or 
            re.search(r'<\s*.*and.*>=', code)
        ):
            characteristics.append('crossover')
            
        # Check for RSI levels
        if re.search(r'rsi.*<\s*3\d', code_lower):  # RSI < 30
            characteristics.append('oversold/overbought')
        if re.search(r'rsi.*>\s*7\d', code_lower):  # RSI > 70
            characteristics.append('oversold/overbought')
            
        # Check for breakout patterns
        if re.search(r'max\(.*high', code_lower) or re.search(r'min\(.*low', code_lower):
            characteristics.append('breakout levels')
        if 'recent_high' in code_lower or 'recent_low' in code_lower:
            characteristics.append('breakout levels')
            
        # Check for simple price action
        if re.search(r'close.*>.*close', code) and len(self._extract_indicators(code)) == 0:
            characteristics.append('simple')
            characteristics.append('price action only')
        
        return characteristics
    
    def _analyze_logic_patterns(self, code: str) -> Dict[str, bool]:
        """Analyze the trading logic patterns"""
        patterns = {
            'has_mean_reversion': False,
            'has_momentum': False,
            'has_breakout': False,
            'is_simple': False
        }
        
        code_lower = code.lower()
        
        # Mean reversion patterns
        if any(keyword in code_lower for keyword in self.mean_reversion_keywords):
            patterns['has_mean_reversion'] = True
        
        # Check for "buy low, sell high" pattern
        if re.search(r'if.*<.*:.*buy', code_lower) and re.search(r'if.*>.*:.*sell', code_lower):
            patterns['has_mean_reversion'] = True
            
        # Momentum patterns
        if any(keyword in code_lower for keyword in self.momentum_keywords):
            patterns['has_momentum'] = True
            
        # Breakout patterns
        if any(keyword in code_lower for keyword in self.breakout_keywords):
            patterns['has_breakout'] = True
            
        # Simple pattern (few conditions)
        if_count = code.count('if')
        if if_count <= 2:
            patterns['is_simple'] = True
            
        return patterns
    
    def _calculate_complexity(self, code: str) -> float:
        """Calculate complexity score (0-1, higher = more complex)"""
        complexity_factors = 0
        
        # Count conditions
        if_count = code.count('if')
        elif_count = code.count('elif')
        and_count = code.count(' and ')
        or_count = code.count(' or ')
        
        # Complexity scoring
        complexity_factors += min(if_count * 0.1, 0.3)  # Max 0.3 for conditions
        complexity_factors += min(elif_count * 0.05, 0.2)  # Max 0.2 for elif
        complexity_factors += min(and_count * 0.05, 0.2)  # Max 0.2 for and
        complexity_factors += min(or_count * 0.05, 0.2)  # Max 0.2 for or
        
        # Count indicators
        indicators_count = len(self._extract_indicators(code))
        complexity_factors += min(indicators_count * 0.05, 0.3)  # Max 0.3 for indicators
        
        # Count hard-coded numbers
        numbers = re.findall(r'\d+\.\d+|\d+', code)
        complexity_factors += min(len(numbers) * 0.01, 0.2)  # Max 0.2 for numbers
        
        return min(complexity_factors, 1.0)
    
    def _classify_pattern(self, indicators: List[str], 
                         characteristics: List[str],
                         logic_patterns: Dict[str, bool],
                         code: str) -> Tuple[str, float]:
        """
        Classify the strategy pattern based on extracted features
        
        Returns:
            (pattern_type, confidence)
        """
        confidence = 0.0
        pattern_scores = {
            'mean_reversion': 0,
            'momentum': 0,
            'breakout': 0,
            'price_action': 0,
            'mixed': 0
        }
        
        # Score based on indicators
        if 'rsi' in indicators and 'oversold/overbought' in characteristics:
            pattern_scores['mean_reversion'] += 3
        
        if len([ind for ind in indicators if ind in ['sma', 'ema']]) >= 2:
            if 'crossover' in characteristics:
                pattern_scores['momentum'] += 3
        
        # Score based on characteristics
        if 'buying below average' in characteristics:
            pattern_scores['mean_reversion'] += 2
        if 'selling above average' in characteristics:
            pattern_scores['mean_reversion'] += 2
            
        if 'crossover' in characteristics:
            pattern_scores['momentum'] += 2
            
        if 'breakout levels' in characteristics:
            pattern_scores['breakout'] += 3
            
        # Score based on logic patterns
        if logic_patterns['has_mean_reversion']:
            pattern_scores['mean_reversion'] += 2
        if logic_patterns['has_momentum']:
            pattern_scores['momentum'] += 2
        if logic_patterns['has_breakout']:
            pattern_scores['breakout'] += 2
            
        # Check for price action only
        if len(indicators) == 0 and logic_patterns['is_simple']:
            pattern_scores['price_action'] = 5
            
        # Determine winner
        max_score = max(pattern_scores.values())
        if max_score == 0:
            return 'custom', 0.5
            
        # Check for mixed patterns
        high_scores = [k for k, v in pattern_scores.items() if v >= max_score * 0.7]
        if len(high_scores) > 1 and 'price_action' not in high_scores:
            return 'mixed', 0.6
        
        # Special case: if we have both mean reversion and momentum indicators
        # but conflicting logic, it's likely mixed
        if (len(indicators) >= 2 and 
            'rsi' in indicators and 'sma' in indicators and
            'and' in code.lower()):  # Complex conditions suggest mixed logic
            return 'mixed', 0.6
            
        # Get pattern with highest score
        pattern_type = max(pattern_scores, key=pattern_scores.get)
        
        # Calculate confidence
        total_score = sum(pattern_scores.values())
        if total_score > 0:
            confidence = min(0.95, max_score / total_score + 0.3)
        else:
            confidence = 0.5
            
        return pattern_type, confidence
    
    def _generate_warnings(self, pattern_type: str, 
                          indicators: List[str], 
                          code: str,
                          complexity_score: float) -> List[str]:
        """Generate warnings based on pattern analysis"""
        warnings = []
        
        # Warn about mixed patterns
        if pattern_type == 'mixed':
            warnings.append("Strategy uses mixed patterns - consider focusing on one approach")
        
        # Warn about too many indicators
        if len(indicators) > 4:
            warnings.append(f"Using {len(indicators)} indicators - consider simplifying")
        
        # Warn about complexity
        if complexity_score > 0.7:
            warnings.append("High complexity detected - too many conditions may lead to overfitting")
        
        # Warn about no stop loss
        if 'stop' not in code.lower() and 'risk' not in code.lower():
            warnings.append("No stop loss or risk management detected")
            
        # Pattern-specific warnings
        if pattern_type == 'mean_reversion':
            warnings.append("Mean reversion works best in ranging markets")
        elif pattern_type == 'momentum':
            warnings.append("Momentum strategies need trend filters to avoid whipsaws")
        elif pattern_type == 'breakout':
            warnings.append("Breakout strategies benefit from volume confirmation")
            
        return warnings