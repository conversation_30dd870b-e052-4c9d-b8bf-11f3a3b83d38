# tests/test_multi_source_feed.py
import pytest
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Callable

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from data_feeds.multi_source_feed import (
    DataFeedAggregator, DataSource, PriceData, DataValidationResult,
    DataSourceType, DataQuality
)

class TestDataFeedAggregator:
    def setup_method(self):
        """Setup test environment"""
        self.aggregator = DataFeedAggregator()
        
        # Add test data sources
        self.test_sources = [
            DataSource(
                name="test_api_1",
                source_type=DataSourceType.REST_API,
                url="https://api.test1.com",
                api_key="test_key_1",
                priority=1,
                timeout_seconds=5,
                retry_attempts=3,
                quality_threshold=0.8
            ),
            DataSource(
                name="test_websocket_1",
                source_type=DataSourceType.WEBSOCKET,
                url="wss://ws.test1.com",
                api_key=None,
                priority=2,
                timeout_seconds=10,
                retry_attempts=2,
                quality_threshold=0.7
            ),
            DataSource(
                name="test_file_1",
                source_type=DataSourceType.FILE,
                url="/path/to/data.csv",
                api_key=None,
                priority=3,
                timeout_seconds=2,
                retry_attempts=1,
                quality_threshold=0.6
            )
        ]
        
        for source in self.test_sources:
            self.aggregator.add_data_source(source)
    
    def test_aggregator_initialization(self):
        """Test aggregator initialization"""
        assert len(self.aggregator.data_sources) == 3
        assert len(self.aggregator.validation_rules) > 0
        assert len(self.aggregator.quality_metrics) == 3
        assert len(self.aggregator.request_stats) == 3
    
    def test_add_remove_data_source(self):
        """Test adding and removing data sources"""
        initial_count = len(self.aggregator.data_sources)
        
        # Add new source
        new_source = DataSource(
            name="test_new",
            source_type=DataSourceType.REST_API,
            url="https://api.new.com",
            api_key="new_key",
            priority=4,
            timeout_seconds=5,
            retry_attempts=3,
            quality_threshold=0.8
        )
        
        self.aggregator.add_data_source(new_source)
        assert len(self.aggregator.data_sources) == initial_count + 1
        assert "test_new" in self.aggregator.data_sources
        assert "test_new" in self.aggregator.quality_metrics
        assert "test_new" in self.aggregator.request_stats
        
        # Remove source
        self.aggregator.remove_data_source("test_new")
        assert len(self.aggregator.data_sources) == initial_count
        assert "test_new" not in self.aggregator.data_sources
        assert "test_new" not in self.aggregator.quality_metrics
        assert "test_new" not in self.aggregator.request_stats
    
    def test_data_validation_rules(self):
        """Test data validation rules"""
        # Valid data
        valid_data = PriceData(
            symbol="EURUSD",
            timestamp=datetime.now(),
            bid=1.1000,
            ask=1.1002,
            last=1.1001,
            volume=1000.0,
            source="test_source",
            quality_score=0.9,
            latency_ms=50.0
        )
        
        result = self.aggregator.validate_data(valid_data)
        assert result.is_valid == True
        assert len(result.validation_errors) == 0
        assert result.quality_score > 0.5
        
        # Invalid data - negative prices
        invalid_data = PriceData(
            symbol="EURUSD",
            timestamp=datetime.now(),
            bid=-1.0,  # Invalid
            ask=1.1002,
            last=1.1001,
            volume=1000.0,
            source="test_source",
            quality_score=0.9,
            latency_ms=50.0
        )
        
        result = self.aggregator.validate_data(invalid_data)
        assert result.is_valid == False
        assert len(result.validation_errors) > 0
        assert "positive" in result.validation_errors[0].lower()
    
    def test_price_range_validation(self):
        """Test price range validation"""
        # Invalid data - ask < bid
        invalid_data = PriceData(
            symbol="EURUSD",
            timestamp=datetime.now(),
            bid=1.1002,
            ask=1.1000,  # Ask < Bid (invalid)
            last=1.1001,
            volume=1000.0,
            source="test_source",
            quality_score=0.9,
            latency_ms=50.0
        )
        
        result = self.aggregator.validate_data(invalid_data)
        assert result.is_valid == False
        assert any("ask price cannot be less than bid" in error.lower() for error in result.validation_errors)
    
    def test_timestamp_validation(self):
        """Test timestamp validation"""
        # Future timestamp (invalid)
        future_data = PriceData(
            symbol="EURUSD",
            timestamp=datetime.now() + timedelta(hours=1),  # Future
            bid=1.1000,
            ask=1.1002,
            last=1.1001,
            volume=1000.0,
            source="test_source",
            quality_score=0.9,
            latency_ms=50.0
        )
        
        result = self.aggregator.validate_data(future_data)
        assert result.is_valid == False
        assert any("future" in error.lower() for error in result.validation_errors)
        
        # Very old timestamp (invalid)
        old_data = PriceData(
            symbol="EURUSD",
            timestamp=datetime.now() - timedelta(hours=1),  # Too old
            bid=1.1000,
            ask=1.1002,
            last=1.1001,
            volume=1000.0,
            source="test_source",
            quality_score=0.9,
            latency_ms=50.0
        )
        
        result = self.aggregator.validate_data(old_data)
        assert result.is_valid == False
        assert any("too old" in error.lower() for error in result.validation_errors)
    
    def test_volume_validation(self):
        """Test volume validation"""
        # Negative volume (invalid)
        invalid_data = PriceData(
            symbol="EURUSD",
            timestamp=datetime.now(),
            bid=1.1000,
            ask=1.1002,
            last=1.1001,
            volume=-100.0,  # Invalid
            source="test_source",
            quality_score=0.9,
            latency_ms=50.0
        )
        
        result = self.aggregator.validate_data(invalid_data)
        assert result.is_valid == False
        assert any("negative" in error.lower() for error in result.validation_errors)
    
    @pytest.mark.asyncio
    async def test_fetch_price_data(self):
        """Test fetching price data from multiple sources"""
        # This will use simulated data sources
        data_list = await self.aggregator.fetch_price_data("EURUSD")
        
        # Should get data from all enabled sources
        assert len(data_list) > 0
        
        # Check data structure
        for data in data_list:
            assert isinstance(data, PriceData)
            assert data.symbol == "EURUSD"
            assert data.bid > 0
            assert data.ask > 0
            assert data.last > 0
            assert data.volume >= 0
            assert data.source in [s.name for s in self.test_sources]
    
    def test_cross_validation(self):
        """Test cross-validation between sources"""
        # Add some cached data
        base_price = 1.1000
        
        # Add data from different sources with similar prices
        for i, source in enumerate(self.test_sources):
            data = PriceData(
                symbol="EURUSD",
                timestamp=datetime.now(),
                bid=base_price - 0.0001,
                ask=base_price + 0.0001,
                last=base_price + (i * 0.0001),  # Slightly different prices
                volume=1000.0,
                source=source.name,
                quality_score=0.9,
                latency_ms=50.0
            )
            
            if "EURUSD" not in self.aggregator.price_cache:
                self.aggregator.price_cache["EURUSD"] = []
            self.aggregator.price_cache["EURUSD"].append(data)
        
        # Test new data that should pass cross-validation
        new_data = PriceData(
            symbol="EURUSD",
            timestamp=datetime.now(),
            bid=base_price - 0.0001,
            ask=base_price + 0.0001,
            last=base_price + 0.0002,  # Close to existing data
            volume=1000.0,
            source="new_source",
            quality_score=0.9,
            latency_ms=50.0
        )
        
        result = self.aggregator.validate_data(new_data)
        assert result.cross_validation_passed == True
        
        # Test data that should fail cross-validation
        outlier_data = PriceData(
            symbol="EURUSD",
            timestamp=datetime.now(),
            bid=base_price - 0.0001,
            ask=base_price + 0.0001,
            last=base_price + 0.1000,  # Way off from existing data
            volume=1000.0,
            source="outlier_source",
            quality_score=0.9,
            latency_ms=50.0
        )
        
        result = self.aggregator.validate_data(outlier_data)
        assert result.cross_validation_passed == False
    
    def test_quality_score_calculation(self):
        """Test quality score calculation"""
        # High quality data
        high_quality_data = PriceData(
            symbol="EURUSD",
            timestamp=datetime.now(),
            bid=1.1000,
            ask=1.1002,
            last=1.1001,
            volume=1000.0,
            source="test_source",
            quality_score=0.9,
            latency_ms=50.0  # Low latency
        )
        
        result = self.aggregator.validate_data(high_quality_data)
        assert result.quality_score > 0.8
        
        # Low quality data (high latency)
        low_quality_data = PriceData(
            symbol="EURUSD",
            timestamp=datetime.now(),
            bid=1.1000,
            ask=1.1002,
            last=1.1001,
            volume=1000.0,
            source="test_source",
            quality_score=0.9,
            latency_ms=1500.0  # High latency
        )
        
        result = self.aggregator.validate_data(low_quality_data)
        assert result.quality_score <= 0.8
    
    def test_best_price_selection(self):
        """Test best price selection"""
        # Add data with different quality scores
        data_list = [
            PriceData(
                symbol="EURUSD",
                timestamp=datetime.now(),
                bid=1.1000,
                ask=1.1002,
                last=1.1001,
                volume=1000.0,
                source="source_1",
                quality_score=0.7,
                latency_ms=100.0
            ),
            PriceData(
                symbol="EURUSD",
                timestamp=datetime.now(),
                bid=1.1000,
                ask=1.1002,
                last=1.1001,
                volume=1000.0,
                source="source_2",
                quality_score=0.9,  # Highest quality
                latency_ms=50.0
            ),
            PriceData(
                symbol="EURUSD",
                timestamp=datetime.now(),
                bid=1.1000,
                ask=1.1002,
                last=1.1001,
                volume=1000.0,
                source="source_3",
                quality_score=0.8,
                latency_ms=75.0
            )
        ]
        
        # Add to cache
        self.aggregator.price_cache["EURUSD"] = data_list
        
        # Get best price
        best_price = self.aggregator.get_best_price("EURUSD")
        
        assert best_price is not None
        assert best_price.source == "source_2"  # Highest quality score
        assert best_price.quality_score == 0.9
    
    def test_source_quality_metrics(self):
        """Test source quality metrics calculation"""
        # Simulate some requests
        source_name = "test_api_1"
        
        # Simulate successful requests
        for _ in range(5):
            self.aggregator._update_success_stats(source_name, 100.0)
        
        # Simulate failed requests
        for _ in range(2):
            self.aggregator._update_error_stats(source_name, "Test error")
        
        # Get quality metrics
        metrics = self.aggregator.get_source_quality_metrics()
        
        assert source_name in metrics
        source_metrics = metrics[source_name]
        
        assert "uptime_percent" in source_metrics
        assert "error_rate_percent" in source_metrics
        assert "avg_latency_ms" in source_metrics
        
        # Check calculated values
        expected_uptime = (5 / 7) * 100  # 5 successful out of 7 total
        expected_error_rate = (2 / 7) * 100  # 2 failed out of 7 total
        
        assert abs(source_metrics["uptime_percent"] - expected_uptime) < 0.1
        assert abs(source_metrics["error_rate_percent"] - expected_error_rate) < 0.1
        assert source_metrics["avg_latency_ms"] == 100.0
    
    def test_custom_validation_rule(self):
        """Test adding custom validation rules"""
        def custom_rule(data: PriceData) -> Tuple[bool, str]:
            """Custom rule: symbol must be EURUSD"""
            if data.symbol != "EURUSD":
                return False, "Only EURUSD is allowed"
            return True, "Custom validation passed"
        
        # Add custom rule
        self.aggregator.add_validation_rule(custom_rule)
        
        # Test with EURUSD (should pass)
        valid_data = PriceData(
            symbol="EURUSD",
            timestamp=datetime.now(),
            bid=1.1000,
            ask=1.1002,
            last=1.1001,
            volume=1000.0,
            source="test_source",
            quality_score=0.9,
            latency_ms=50.0
        )
        
        result = self.aggregator.validate_data(valid_data)
        assert result.is_valid == True
        
        # Test with GBPUSD (should fail)
        invalid_data = PriceData(
            symbol="GBPUSD",
            timestamp=datetime.now(),
            bid=1.2500,
            ask=1.2502,
            last=1.2501,
            volume=1000.0,
            source="test_source",
            quality_score=0.9,
            latency_ms=50.0
        )
        
        result = self.aggregator.validate_data(invalid_data)
        assert result.is_valid == False
        assert any("only eurusd is allowed" in error.lower() for error in result.validation_errors)
    
    def test_data_callbacks(self):
        """Test data callbacks"""
        callback_data = []
        
        def test_callback(data: PriceData):
            callback_data.append(data)
        
        # Add callback
        self.aggregator.add_data_callback(test_callback)
        
        # Add some data to trigger callback
        test_data = PriceData(
            symbol="EURUSD",
            timestamp=datetime.now(),
            bid=1.1000,
            ask=1.1002,
            last=1.1001,
            volume=1000.0,
            source="test_source",
            quality_score=0.9,
            latency_ms=50.0
        )
        
        # Manually add to cache and trigger callback
        if "EURUSD" not in self.aggregator.price_cache:
            self.aggregator.price_cache["EURUSD"] = []
        self.aggregator.price_cache["EURUSD"].append(test_data)
        
        # Trigger callback manually
        for callback in self.aggregator.data_callbacks:
            callback(test_data)
        
        assert len(callback_data) == 1
        assert callback_data[0] == test_data
    
    def test_validation_callbacks(self):
        """Test validation callbacks"""
        callback_results = []
        
        def test_validation_callback(result: DataValidationResult):
            callback_results.append(result)
        
        # Add callback
        self.aggregator.add_validation_callback(test_validation_callback)
        
        # Validate some data to trigger callback
        test_data = PriceData(
            symbol="EURUSD",
            timestamp=datetime.now(),
            bid=1.1000,
            ask=1.1002,
            last=1.1001,
            volume=1000.0,
            source="test_source",
            quality_score=0.9,
            latency_ms=50.0
        )
        
        result = self.aggregator.validate_data(test_data)
        
        assert len(callback_results) == 1
        assert callback_results[0] == result
    
    def test_aggregated_data(self):
        """Test aggregated data calculation"""
        # Add test data
        base_price = 1.1000
        test_data = []
        
        for i in range(5):
            data = PriceData(
                symbol="EURUSD",
                timestamp=datetime.now() - timedelta(seconds=i*10),
                bid=base_price - 0.0001,
                ask=base_price + 0.0001,
                last=base_price + (i * 0.0001),
                volume=1000.0 + (i * 100),
                source=f"source_{i}",
                quality_score=0.9 - (i * 0.05),
                latency_ms=50.0 + (i * 10)
            )
            test_data.append(data)
        
        # Add to cache
        self.aggregator.price_cache["EURUSD"] = test_data
        
        # Get aggregated data
        aggregated = self.aggregator.get_aggregated_data("EURUSD", time_window_seconds=120)
        
        assert aggregated is not None
        assert aggregated["symbol"] == "EURUSD"
        assert aggregated["data_points"] == 5
        assert "price_stats" in aggregated
        assert "volume_stats" in aggregated
        assert "quality_stats" in aggregated
        assert "sources" in aggregated
        
        # Check price statistics
        price_stats = aggregated["price_stats"]
        assert "min" in price_stats
        assert "max" in price_stats
        assert "avg" in price_stats
        assert "median" in price_stats
        
        # Check that min/max are reasonable
        assert price_stats["min"] >= base_price
        assert price_stats["max"] >= price_stats["min"]
    
    def test_no_data_scenarios(self):
        """Test scenarios with no data"""
        # Test best price with no data
        best_price = self.aggregator.get_best_price("NONEXISTENT")
        assert best_price is None
        
        # Test aggregated data with no data
        aggregated = self.aggregator.get_aggregated_data("NONEXISTENT")
        assert aggregated is None
        
        # Test aggregated data with old data
        old_data = PriceData(
            symbol="EURUSD",
            timestamp=datetime.now() - timedelta(hours=1),  # Too old
            bid=1.1000,
            ask=1.1002,
            last=1.1001,
            volume=1000.0,
            source="test_source",
            quality_score=0.9,
            latency_ms=50.0
        )
        
        self.aggregator.price_cache["EURUSD"] = [old_data]
        
        # Should return None for recent data
        best_price = self.aggregator.get_best_price("EURUSD")
        assert best_price is None