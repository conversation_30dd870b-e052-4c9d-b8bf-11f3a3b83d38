"""
Simplified FastAPI server that's more stable
"""

import sys
import os
import logging
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, List, Any, Optional
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("simple_server")

# Create FastAPI app
app = FastAPI(title="AI Trading Platform", version="1.0.0")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Simple models
class ChatRequest(BaseModel):
    message: str
    model: str = "llama3.2:1b"
    conversation_id: str = "default"

class ChatResponse(BaseModel):
    response: str
    success: bool = True
    template_data: Optional[Dict] = None

class StatusResponse(BaseModel):
    available: bool
    models: List[str] = []
    message: str

# Simple chatbot mock
class SimpleChatbot:
    def __init__(self):
        self.available = True
        self.models = ["llama3.2:1b"]
    
    async def chat(self, message: str) -> str:
        """Simple strategy generator"""
        if "rsi" in message.lower():
            return """
# RSI Mean Reversion Strategy

```python
import pandas as pd
import numpy as np

class RSIMeanReversionStrategy:
    def __init__(self, rsi_period=14, oversold=30, overbought=70):
        self.rsi_period = rsi_period
        self.oversold = oversold
        self.overbought = overbought
    
    def calculate_rsi(self, prices):
        delta = prices.diff()
        gain = delta.where(delta > 0, 0).rolling(window=self.rsi_period).mean()
        loss = -delta.where(delta < 0, 0).rolling(window=self.rsi_period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def generate_signals(self, data):
        rsi = self.calculate_rsi(data['close'])
        signals = pd.Series(0, index=data.index)
        signals[rsi < self.oversold] = 1  # Buy signal
        signals[rsi > self.overbought] = -1  # Sell signal
        return signals
```

This strategy uses RSI (Relative Strength Index) to identify mean reversion opportunities.
When RSI is below 30 (oversold), it generates buy signals.
When RSI is above 70 (overbought), it generates sell signals.
"""
        elif "macd" in message.lower():
            return """
# MACD Momentum Strategy

```python
import pandas as pd
import numpy as np

class MACDMomentumStrategy:
    def __init__(self, fast_period=12, slow_period=26, signal_period=9):
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.signal_period = signal_period
    
    def calculate_macd(self, prices):
        ema_fast = prices.ewm(span=self.fast_period).mean()
        ema_slow = prices.ewm(span=self.slow_period).mean()
        macd_line = ema_fast - ema_slow
        signal_line = macd_line.ewm(span=self.signal_period).mean()
        histogram = macd_line - signal_line
        return macd_line, signal_line, histogram
    
    def generate_signals(self, data):
        macd, signal, histogram = self.calculate_macd(data['close'])
        signals = pd.Series(0, index=data.index)
        signals[(macd > signal) & (macd.shift(1) <= signal.shift(1))] = 1  # Buy
        signals[(macd < signal) & (macd.shift(1) >= signal.shift(1))] = -1  # Sell
        return signals
```

This strategy uses MACD (Moving Average Convergence Divergence) crossovers for momentum trading.
Buy when MACD line crosses above the signal line.
Sell when MACD line crosses below the signal line.
"""
        else:
            return f"""
# Custom Trading Strategy

I understand you want to create a trading strategy with: {message}

```python
import pandas as pd
import numpy as np

class CustomTradingStrategy:
    def __init__(self, symbol='EURUSD', risk_per_trade=0.02):
        self.symbol = symbol
        self.risk_per_trade = risk_per_trade
    
    def analyze_market(self, data):
        # Add your custom analysis logic here
        # This is a template based on your request
        return data
    
    def generate_signals(self, data):
        signals = pd.Series(0, index=data.index)
        # Add your signal generation logic
        return signals
    
    def execute_trade(self, signal, current_price):
        if signal == 1:
            return {"action": "buy", "price": current_price}
        elif signal == -1:
            return {"action": "sell", "price": current_price}
        return {"action": "hold"}
```

This is a template strategy. Please provide more specific requirements for detailed implementation.
"""

# Global chatbot instance
chatbot = SimpleChatbot()

@app.get("/")
async def root():
    return {"message": "AI Enhanced Trading Platform", "status": "running"}

@app.get("/health")
async def health_check():
    return {"status": "ok", "ollama_available": True}

@app.get("/api/ollama/status")
async def get_ollama_status():
    return StatusResponse(
        available=True,
        models=["llama3.2:1b"],
        message="Ollama server running with 1 models available"
    )

@app.post("/api/ollama/chat")
async def chat_with_ollama(request: ChatRequest):
    try:
        response = await chatbot.chat(request.message)
        return ChatResponse(
            response=response,
            success=True,
            template_data={"code": response}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Simple MT5 endpoints
@app.post("/api/mt5/strategies/deploy")
async def deploy_strategy(strategy_data: dict):
    """Mock MT5 deployment"""
    strategy_name = strategy_data.get("strategy_name", "Unknown")
    strategy_code = strategy_data.get("strategy_code", "")
    
    if not strategy_code or len(strategy_code) < 50:
        raise HTTPException(status_code=400, detail="Strategy code is too short or invalid")
    
    return {
        "success": True,
        "strategy_id": f"strategy_{strategy_name}",
        "status": "deployed",
        "message": f"Strategy {strategy_name} successfully validated and ready for deployment"
    }

@app.get("/api/mt5/strategies")
async def list_strategies():
    """List deployed strategies"""
    return {
        "strategies": [
            {"id": "rsi_strategy", "name": "RSI Mean Reversion", "status": "active"},
            {"id": "macd_strategy", "name": "MACD Momentum", "status": "active"}
        ]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="localhost", port=8005)
