#!/usr/bin/env python3
"""
Moving Averages Strategy Implementation

This module implements various moving average calculations and crossover signals
with comprehensive doctest examples and robust error handling.
"""

import math
from typing import List, Union, Optional


def sma(prices: List[float], period: int) -> float:
    """
    Calculate Simple Moving Average (SMA).
    
    SMA is the arithmetic mean of prices over a specified number of periods.
    
    Args:
        prices: List of price values (floats)
        period: Number of periods for average calculation
    
    Returns:
        float: Simple moving average value
    
    Raises:
        ValueError: If not enough data or invalid parameters
        TypeError: If prices contain non-numeric values
    
    Examples:
        Basic SMA calculation:
        >>> sma([1, 2, 3, 4, 5], period=3)
        4.0
        
        SMA with decimal values:
        >>> sma([1.5, 2.5, 3.5, 4.5, 5.5], period=3)
        4.5
        
        SMA with single period:
        >>> sma([10], period=1)
        10.0
        
        SMA with exact period length:
        >>> sma([2, 4, 6, 8, 10], period=5)
        6.0
        
        SMA with larger dataset:
        >>> prices = [10, 12, 14, 16, 18, 20, 22, 24, 26, 28]
        >>> sma(prices, period=5)
        24.0
        
        Error cases - insufficient data:
        >>> sma([1, 2], period=5)
        Traceback (most recent call last):
            ...
        ValueError: Not enough data: need at least 5 prices for period 5, got 2
        
        Error cases - empty list:
        >>> sma([], period=3)
        Traceback (most recent call last):
            ...
        ValueError: Not enough data: need at least 3 prices for period 3, got 0
        
        Error cases - invalid period:
        >>> sma([1, 2, 3, 4, 5], period=0)
        Traceback (most recent call last):
            ...
        ValueError: Period must be positive, got 0
        
        Error cases - negative period:
        >>> sma([1, 2, 3, 4, 5], period=-3)
        Traceback (most recent call last):
            ...
        ValueError: Period must be positive, got -3
        
        Error cases - non-numeric prices:
        >>> sma([1, 2, "invalid", 4, 5], period=3)
        Traceback (most recent call last):
            ...
        TypeError: All prices must be numeric, found <class 'str'> at index 2
        
        Error cases - None in prices:
        >>> sma([1, 2, None, 4, 5], period=3)
        Traceback (most recent call last):
            ...
        TypeError: All prices must be numeric, found <class 'NoneType'> at index 2
        
        Error cases - infinite values:
        >>> sma([1, 2, float('inf'), 4, 5], period=3)
        Traceback (most recent call last):
            ...
        ValueError: Price at index 2 is not a valid number: inf
        
        Edge case - all same values:
        >>> sma([5, 5, 5, 5, 5], period=3)
        5.0
    """
    # Input validation
    if not isinstance(period, int) or period <= 0:
        raise ValueError(f"Period must be positive, got {period}")
    
    if not isinstance(prices, (list, tuple)):
        raise TypeError(f"Prices must be a list or tuple, got {type(prices)}")
    
    if len(prices) < period:
        raise ValueError(f"Not enough data: need at least {period} prices for period {period}, got {len(prices)}")
    
    # Validate all prices are numeric
    for i, price in enumerate(prices):
        if not isinstance(price, (int, float)) or price is None:
            raise TypeError(f"All prices must be numeric, found {type(price)} at index {i}")
        if math.isnan(price) or math.isinf(price):
            raise ValueError(f"Price at index {i} is not a valid number: {price}")
    
    # Calculate SMA using the last 'period' prices
    recent_prices = prices[-period:]
    return sum(recent_prices) / period


def ema(prices: List[float], period: int, smoothing: float = 2.0) -> float:
    """
    Calculate Exponential Moving Average (EMA).
    
    EMA gives more weight to recent prices and responds more quickly to price changes.
    
    Args:
        prices: List of price values (floats)
        period: Number of periods for EMA calculation
        smoothing: Smoothing factor (default: 2.0)
    
    Returns:
        float: Exponential moving average value
    
    Raises:
        ValueError: If not enough data or invalid parameters
        TypeError: If prices contain non-numeric values
    
    Examples:
        Basic EMA calculation:
        >>> round(ema([1, 2, 3, 4, 5], period=3), 2)
        4.0
        
        EMA with custom smoothing:
        >>> round(ema([1, 2, 3, 4, 5], period=3, smoothing=3.0), 2)
        4.62
        
        EMA with single period:
        >>> ema([10], period=1)
        10.0
        
        EMA with larger dataset:
        >>> prices = [10, 12, 14, 16, 18, 20, 22, 24, 26, 28]
        >>> round(ema(prices, period=5), 2)
        24.0
        
        Error cases - insufficient data:
        >>> ema([1, 2], period=5)
        Traceback (most recent call last):
            ...
        ValueError: Not enough data: need at least 5 prices for period 5, got 2
        
        Error cases - invalid smoothing:
        >>> ema([1, 2, 3, 4, 5], period=3, smoothing=0)
        Traceback (most recent call last):
            ...
        ValueError: Smoothing factor must be positive, got 0
        
        Error cases - negative smoothing:
        >>> ema([1, 2, 3, 4, 5], period=3, smoothing=-1)
        Traceback (most recent call last):
            ...
        ValueError: Smoothing factor must be positive, got -1
        
        Edge case - all same values:
        >>> ema([5, 5, 5, 5, 5], period=3)
        5.0
    """
    # Input validation
    if not isinstance(period, int) or period <= 0:
        raise ValueError(f"Period must be positive, got {period}")
    
    if not isinstance(smoothing, (int, float)) or smoothing <= 0:
        raise ValueError(f"Smoothing factor must be positive, got {smoothing}")
    
    if not isinstance(prices, (list, tuple)):
        raise TypeError(f"Prices must be a list or tuple, got {type(prices)}")
    
    if len(prices) < period:
        raise ValueError(f"Not enough data: need at least {period} prices for period {period}, got {len(prices)}")
    
    # Validate all prices are numeric
    for i, price in enumerate(prices):
        if not isinstance(price, (int, float)) or price is None:
            raise TypeError(f"All prices must be numeric, found {type(price)} at index {i}")
        if math.isnan(price) or math.isinf(price):
            raise ValueError(f"Price at index {i} is not a valid number: {price}")
    
    # Calculate EMA
    multiplier = smoothing / (period + 1)
    
    # Start with SMA for the first EMA value
    ema_value = sum(prices[:period]) / period
    
    # Apply EMA formula for remaining prices
    for price in prices[period:]:
        ema_value = (price * multiplier) + (ema_value * (1 - multiplier))
    
    return round(ema_value, 6)


def ma_crossover(short_ma: List[float], long_ma: List[float], 
                 lookback: int = 1) -> Optional[str]:
    """
    Detect moving average crossover signals.
    
    Args:
        short_ma: List of short-period moving average values
        long_ma: List of long-period moving average values
        lookback: Number of periods to look back for crossover detection
    
    Returns:
        Optional[str]: Crossover signal ('GOLDEN_CROSS', 'DEATH_CROSS', None)
    
    Examples:
        Golden cross (short MA crosses above long MA):
        >>> short_ma = [10, 11, 12, 11, 13]
        >>> long_ma = [12, 12, 12, 12, 12]
        >>> ma_crossover(short_ma, long_ma, lookback=1)
        'GOLDEN_CROSS'
        
        Death cross (short MA crosses below long MA):
        >>> short_ma = [14, 13, 12, 13, 11]
        >>> long_ma = [12, 12, 12, 12, 12]
        >>> ma_crossover(short_ma, long_ma, lookback=1)
        'DEATH_CROSS'
        
        No crossover:
        >>> short_ma = [10, 11, 12, 13, 14]
        >>> long_ma = [8, 9, 10, 11, 12]
        >>> ma_crossover(short_ma, long_ma, lookback=1) is None
        True
        
        Error cases - mismatched lengths:
        >>> ma_crossover([1, 2, 3], [4, 5], lookback=1)
        Traceback (most recent call last):
            ...
        ValueError: Short MA and Long MA must have the same length: got 3 and 2
        
        Error cases - insufficient data:
        >>> ma_crossover([1], [2], lookback=2)
        Traceback (most recent call last):
            ...
        ValueError: Not enough data: need at least 3 values for lookback 2, got 1
        
        Error cases - invalid lookback:
        >>> ma_crossover([1, 2], [3, 4], lookback=0)
        Traceback (most recent call last):
            ...
        ValueError: Lookback must be positive, got 0
    """
    # Input validation
    if len(short_ma) != len(long_ma):
        raise ValueError(f"Short MA and Long MA must have the same length: got {len(short_ma)} and {len(long_ma)}")
    
    if lookback <= 0:
        raise ValueError(f"Lookback must be positive, got {lookback}")
    
    if len(short_ma) < lookback + 1:
        raise ValueError(f"Not enough data: need at least {lookback + 1} values for lookback {lookback}, got {len(short_ma)}")
    
    # Check for crossover
    current_short = short_ma[-1]
    current_long = long_ma[-1]
    previous_short = short_ma[-(lookback + 1)]
    previous_long = long_ma[-(lookback + 1)]
    
    # Golden cross: short MA crosses above long MA
    if previous_short <= previous_long and current_short > current_long:
        return 'GOLDEN_CROSS'
    
    # Death cross: short MA crosses below long MA
    elif previous_short >= previous_long and current_short < current_long:
        return 'DEATH_CROSS'
    
    return None


def ma_signal(prices: List[float], short_period: int = 10, long_period: int = 20,
              ma_type: str = 'sma') -> str:
    """
    Generate moving average trading signal based on crossover.
    
    Args:
        prices: List of price values
        short_period: Short moving average period (default: 10)
        long_period: Long moving average period (default: 20)
        ma_type: Type of moving average ('sma' or 'ema', default: 'sma')
    
    Returns:
        str: Trading signal ('BUY', 'SELL', 'HOLD')
    
    Examples:
        SMA bullish signal:
        >>> prices = list(range(1, 31))  # Upward trend
        >>> ma_signal(prices, short_period=5, long_period=10, ma_type='sma')
        'BUY'
        
        SMA bearish signal:
        >>> prices = list(range(30, 0, -1))  # Downward trend
        >>> ma_signal(prices, short_period=5, long_period=10, ma_type='sma')
        'SELL'
        
        EMA signal:
        >>> prices = list(range(1, 31))
        >>> ma_signal(prices, short_period=5, long_period=10, ma_type='ema')
        'BUY'
        
        Neutral signal:
        >>> prices = [20] * 30  # Flat prices
        >>> ma_signal(prices, short_period=5, long_period=10, ma_type='sma')
        'HOLD'
        
        Error cases - insufficient data:
        >>> ma_signal([1, 2, 3], short_period=5, long_period=10)
        Traceback (most recent call last):
            ...
        ValueError: Not enough data: need at least 10 prices for long period 10, got 3
        
        Error cases - invalid MA type:
        >>> ma_signal([1]*30, short_period=5, long_period=10, ma_type='invalid')
        Traceback (most recent call last):
            ...
        ValueError: MA type must be 'sma' or 'ema', got 'invalid'
        
        Error cases - short >= long period:
        >>> ma_signal([1]*30, short_period=20, long_period=10)
        Traceback (most recent call last):
            ...
        ValueError: Short period (20) must be less than long period (10)
    """
    # Input validation
    if short_period >= long_period:
        raise ValueError(f"Short period ({short_period}) must be less than long period ({long_period})")
    
    if len(prices) < long_period:
        raise ValueError(f"Not enough data: need at least {long_period} prices for long period {long_period}, got {len(prices)}")
    
    if ma_type not in ['sma', 'ema']:
        raise ValueError(f"MA type must be 'sma' or 'ema', got '{ma_type}'")
    
    # Calculate moving averages
    if ma_type == 'sma':
        short_ma_value = sma(prices, short_period)
        long_ma_value = sma(prices, long_period)
    else:  # ema
        short_ma_value = ema(prices, short_period)
        long_ma_value = ema(prices, long_period)
    
    # Generate signal
    if short_ma_value > long_ma_value * 1.001:  # Small threshold to avoid noise
        return 'BUY'
    elif short_ma_value < long_ma_value * 0.999:  # Small threshold to avoid noise
        return 'SELL'
    else:
        return 'HOLD'


def ma_trend_strength(prices: List[float], period: int = 20, 
                     ma_type: str = 'sma') -> float:
    """
    Calculate trend strength based on price position relative to moving average.
    
    Args:
        prices: List of price values
        period: Moving average period
        ma_type: Type of moving average ('sma' or 'ema')
    
    Returns:
        float: Trend strength (-1.0 to 1.0, negative=bearish, positive=bullish)
    
    Examples:
        Strong bullish trend:
        >>> prices = list(range(1, 31))
        >>> strength = ma_trend_strength(prices, period=10)
        >>> strength > 0.05
        True
        
        Strong bearish trend:
        >>> prices = list(range(30, 0, -1))
        >>> strength = ma_trend_strength(prices, period=10)
        >>> strength < -0.5
        True
        
        Neutral trend:
        >>> prices = [20] * 30
        >>> abs(ma_trend_strength(prices, period=10)) < 0.1
        True
        
        Error cases - insufficient data:
        >>> ma_trend_strength([1, 2, 3], period=10)
        Traceback (most recent call last):
            ...
        ValueError: Not enough data: need at least 10 prices for period 10, got 3
    """
    if len(prices) < period:
        raise ValueError(f"Not enough data: need at least {period} prices for period {period}, got {len(prices)}")
    
    if ma_type not in ['sma', 'ema']:
        raise ValueError(f"MA type must be 'sma' or 'ema', got '{ma_type}'")
    
    # Calculate moving average
    if ma_type == 'sma':
        ma_value = sma(prices, period)
    else:
        ma_value = ema(prices, period)
    
    current_price = prices[-1]
    
    # Calculate trend strength as percentage difference
    if ma_value == 0:
        return 0.0
    
    strength = (current_price - ma_value) / ma_value
    
    # Clamp to [-1, 1] range
    return max(-1.0, min(1.0, strength))


if __name__ == "__main__":
    import doctest
    
    print("Running Moving Averages strategy doctests...")
    
    # Run doctests with verbose output
    result = doctest.testmod(verbose=True)
    
    if result.failed == 0:
        print(f"\n✅ All {result.attempted} doctests passed!")
    else:
        print(f"\n❌ {result.failed} out of {result.attempted} doctests failed!")
    
    # Additional manual tests
    print("\n🧪 Running additional manual tests...")
    
    # Test with sample data
    sample_prices = [
        20.0, 20.5, 21.0, 20.8, 21.2, 21.5, 21.8, 22.0, 22.2, 22.5,
        22.8, 23.0, 23.2, 23.5, 23.8, 24.0, 24.2, 24.5, 24.8, 25.0,
        25.2, 25.5, 25.8, 26.0, 26.2, 26.5, 26.8, 27.0, 27.2, 27.5
    ]
    
    try:
        sma_10 = sma(sample_prices, period=10)
        sma_20 = sma(sample_prices, period=20)
        ema_10 = ema(sample_prices, period=10)
        ema_20 = ema(sample_prices, period=20)
        
        signal_sma = ma_signal(sample_prices, short_period=10, long_period=20, ma_type='sma')
        signal_ema = ma_signal(sample_prices, short_period=10, long_period=20, ma_type='ema')
        
        trend_strength = ma_trend_strength(sample_prices, period=20)
        
        print(f"✅ SMA(10): {sma_10:.2f}, SMA(20): {sma_20:.2f}")
        print(f"✅ EMA(10): {ema_10:.2f}, EMA(20): {ema_20:.2f}")
        print(f"✅ SMA Signal: {signal_sma}")
        print(f"✅ EMA Signal: {signal_ema}")
        print(f"✅ Trend Strength: {trend_strength:.3f}")
        
        # Test crossover detection
        short_ma_values = [sma(sample_prices[:i+10], period=10) for i in range(len(sample_prices)-9)]
        long_ma_values = [sma(sample_prices[:i+20], period=20) for i in range(len(sample_prices)-19)]
        
        if len(short_ma_values) >= 2 and len(long_ma_values) >= 2:
            crossover = ma_crossover(short_ma_values[-2:], long_ma_values[-2:])
            print(f"✅ Crossover Signal: {crossover}")
        
    except Exception as e:
        print(f"❌ Manual test failed: {e}")
    
    print("\n🎯 Moving Averages strategy testing complete!")