import { describe, it, expect, beforeEach, jest, afterEach } from '@jest/globals';
import request from 'supertest';
import express from 'express';
import { darwinGodelRoutes } from '../darwin-godel.routes';
import { DarwinGodelBridgeService } from '../../services/darwin-godel-bridge.service';

// Mock the bridge service
jest.mock('../../services/darwin-godel-bridge.service');

describe('Darwin Godel Routes', () => {
  let app: express.Application;
  let mockBridgeService: jest.Mocked<DarwinGodelBridgeService>;

  beforeEach(() => {
    app = express();
    app.use(express.json());
    
    // Create mock service
    mockBridgeService = {
      verifyStrategy: jest.fn(),
      verifyWithBacktest: jest.fn(),
      runMonteCarloValidation: jest.fn(),
      checkHealth: jest.fn()
    } as any;

    // Mock the constructor
    (DarwinGodelBridgeService as jest.MockedClass<typeof DarwinGodelBridgeService>).mockImplementation(() => mockBridgeService);

    app.use('/api/darwin-godel', darwinGodelRoutes);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /verify', () => {
    it('should verify a valid strategy', async () => {
      // Arrange
      const strategyCode = `
def trading_strategy(data, params):
    sma = calculate_sma(data['close'], params['period'])
    current_price = data['close'][-1]
    
    if current_price < sma[-1] * 0.98:
        return {'signal': 'buy', 'confidence': 0.8}
    else:
        return {'signal': 'hold', 'confidence': 0.5}
`;

      const mockResult = {
        isValid: true,
        strategyType: 'mean_reversion',
        riskScore: 0.3,
        robustnessScore: 0.8,
        warnings: null
      };

      mockBridgeService.verifyStrategy.mockResolvedValue(mockResult);

      // Act
      const response = await request(app)
        .post('/api/darwin-godel/verify')
        .send({ strategyCode })
        .expect(200);

      // Assert
      expect(response.body).toEqual({
        success: true,
        data: mockResult
      });
      expect(mockBridgeService.verifyStrategy).toHaveBeenCalledWith(strategyCode);
    });

    it('should return 400 for missing strategy code', async () => {
      // Act
      const response = await request(app)
        .post('/api/darwin-godel/verify')
        .send({})
        .expect(400);

      // Assert
      expect(response.body).toEqual({
        success: false,
        error: 'Strategy code is required'
      });
    });

    it('should return 400 for empty strategy code', async () => {
      // Act
      const response = await request(app)
        .post('/api/darwin-godel/verify')
        .send({ strategyCode: '' })
        .expect(400);

      // Assert
      expect(response.body).toEqual({
        success: false,
        error: 'Strategy code cannot be empty'
      });
    });

    it('should handle verification errors', async () => {
      // Arrange
      const strategyCode = 'invalid code';
      mockBridgeService.verifyStrategy.mockRejectedValue(new Error('SecurityError: Malicious code detected'));

      // Act
      const response = await request(app)
        .post('/api/darwin-godel/verify')
        .send({ strategyCode })
        .expect(500);

      // Assert
      expect(response.body).toEqual({
        success: false,
        error: 'SecurityError: Malicious code detected'
      });
    });
  });

  describe('POST /verify-with-backtest', () => {
    it('should verify strategy with backtest', async () => {
      // Arrange
      const requestBody = {
        strategyCode: `
def trading_strategy(data, params):
    returns = calculate_returns(data['close'])
    if returns[-1] > 0:
        return {'signal': 'buy', 'confidence': 0.7}
    else:
        return {'signal': 'sell', 'confidence': 0.7}
`,
        historicalData: {
          close: [100, 101, 102, 101, 103],
          high: [101, 102, 103, 102, 104],
          low: [99, 100, 101, 100, 102],
          volume: [1000, 1100, 1200, 1050, 1300]
        },
        initialCapital: 10000
      };

      const mockResult = {
        isValid: true,
        strategyType: 'momentum',
        riskScore: 0.4,
        robustnessScore: 0.7,
        warnings: null,
        metrics: {
          totalReturn: 0.15,
          sharpeRatio: 1.2,
          maxDrawdown: 0.05,
          winRate: 0.65,
          numTrades: 8
        }
      };

      mockBridgeService.verifyWithBacktest.mockResolvedValue(mockResult);

      // Act
      const response = await request(app)
        .post('/api/darwin-godel/verify-with-backtest')
        .send(requestBody)
        .expect(200);

      // Assert
      expect(response.body).toEqual({
        success: true,
        data: mockResult
      });
      expect(mockBridgeService.verifyWithBacktest).toHaveBeenCalledWith(
        requestBody.strategyCode,
        requestBody.historicalData,
        requestBody.initialCapital
      );
    });

    it('should use default initial capital when not provided', async () => {
      // Arrange
      const requestBody = {
        strategyCode: 'def trading_strategy(data, params): return {"signal": "hold"}',
        historicalData: {
          close: [100, 101, 102],
          high: [101, 102, 103],
          low: [99, 100, 101],
          volume: [1000, 1100, 1200]
        }
      };

      const mockResult = {
        isValid: true,
        strategyType: 'custom',
        riskScore: 0.2,
        robustnessScore: 0.9,
        warnings: null
      };

      mockBridgeService.verifyWithBacktest.mockResolvedValue(mockResult);

      // Act
      await request(app)
        .post('/api/darwin-godel/verify-with-backtest')
        .send(requestBody)
        .expect(200);

      // Assert
      expect(mockBridgeService.verifyWithBacktest).toHaveBeenCalledWith(
        requestBody.strategyCode,
        requestBody.historicalData,
        10000 // Default initial capital
      );
    });

    it('should return 400 for missing historical data', async () => {
      // Act
      const response = await request(app)
        .post('/api/darwin-godel/verify-with-backtest')
        .send({
          strategyCode: 'def trading_strategy(data, params): return {"signal": "hold"}'
        })
        .expect(400);

      // Assert
      expect(response.body).toEqual({
        success: false,
        error: 'Historical data is required'
      });
    });
  });

  describe('POST /monte-carlo', () => {
    it('should run Monte Carlo validation', async () => {
      // Arrange
      const requestBody = {
        strategyCode: `
def trading_strategy(data, params):
    sma_short = calculate_sma(data['close'], params['short_period'])
    sma_long = calculate_sma(data['close'], params['long_period'])
    
    if len(sma_short) == 0 or len(sma_long) == 0:
        return {'signal': 'hold', 'confidence': 0.1}
    
    if sma_short[-1] > sma_long[-1]:
        return {'signal': 'buy', 'confidence': 0.75}
    else:
        return {'signal': 'sell', 'confidence': 0.75}
`,
        simulations: 100,
        dataVariations: 0.02
      };

      const mockResult = {
        successRate: 0.75,
        avgSharpe: 0.8,
        avgReturn: 0.12,
        consistencyScore: 0.85,
        distribution: [
          { total_return: 0.10, sharpe_ratio: 0.7 },
          { total_return: 0.15, sharpe_ratio: 0.9 }
        ]
      };

      mockBridgeService.runMonteCarloValidation.mockResolvedValue(mockResult);

      // Act
      const response = await request(app)
        .post('/api/darwin-godel/monte-carlo')
        .send(requestBody)
        .expect(200);

      // Assert
      expect(response.body).toEqual({
        success: true,
        data: mockResult
      });
      expect(mockBridgeService.runMonteCarloValidation).toHaveBeenCalledWith(
        requestBody.strategyCode,
        requestBody.simulations,
        requestBody.dataVariations
      );
    });

    it('should use default parameters when not provided', async () => {
      // Arrange
      const requestBody = {
        strategyCode: 'def trading_strategy(data, params): return {"signal": "hold"}'
      };

      const mockResult = {
        successRate: 0.6,
        avgSharpe: 0.5,
        avgReturn: 0.08,
        consistencyScore: 0.7,
        distribution: []
      };

      mockBridgeService.runMonteCarloValidation.mockResolvedValue(mockResult);

      // Act
      await request(app)
        .post('/api/darwin-godel/monte-carlo')
        .send(requestBody)
        .expect(200);

      // Assert
      expect(mockBridgeService.runMonteCarloValidation).toHaveBeenCalledWith(
        requestBody.strategyCode,
        100, // Default simulations
        0.02 // Default data variations
      );
    });
  });

  describe('GET /health', () => {
    it('should return healthy status when Python engine is available', async () => {
      // Arrange
      mockBridgeService.checkHealth.mockResolvedValue(true);

      // Act
      const response = await request(app)
        .get('/api/darwin-godel/health')
        .expect(200);

      // Assert
      expect(response.body).toEqual({
        success: true,
        data: {
          status: 'healthy',
          pythonEngine: 'available'
        }
      });
    });

    it('should return unhealthy status when Python engine is not available', async () => {
      // Arrange
      mockBridgeService.checkHealth.mockResolvedValue(false);

      // Act
      const response = await request(app)
        .get('/api/darwin-godel/health')
        .expect(503);

      // Assert
      expect(response.body).toEqual({
        success: false,
        data: {
          status: 'unhealthy',
          pythonEngine: 'unavailable'
        }
      });
    });

    it('should handle health check errors', async () => {
      // Arrange
      mockBridgeService.checkHealth.mockRejectedValue(new Error('Health check failed'));

      // Act
      const response = await request(app)
        .get('/api/darwin-godel/health')
        .expect(503);

      // Assert
      expect(response.body).toEqual({
        success: false,
        data: {
          status: 'unhealthy',
          pythonEngine: 'error'
        }
      });
    });
  });
});