#!/usr/bin/env python
"""
Performance Test Runner

Runs comprehensive performance tests and generates detailed reports.
"""

import subprocess
import sys
import time
import os

def run_performance_tests():
    """Run all performance tests with detailed reporting"""
    print("🚀 Starting Performance Test Suite")
    print("=" * 60)
    
    # Performance test categories
    test_categories = [
        {
            'name': 'High-Frequency Trading',
            'path': 'tests/performance/test_high_frequency_trading.py',
            'description': 'Tests latency and throughput for HFT scenarios'
        },
        {
            'name': 'Concurrent Execution',
            'path': 'tests/performance/test_concurrent_execution.py',
            'description': 'Tests thread safety and concurrent performance'
        },
        {
            'name': 'Memory Usage',
            'path': 'tests/performance/test_memory_usage.py',
            'description': 'Tests memory efficiency and leak detection'
        },
        {
            'name': 'Latency Benchmarks',
            'path': 'tests/performance/test_latency_benchmarks.py',
            'description': 'Comprehensive latency profiling and benchmarks'
        }
    ]
    
    results = {}
    
    for category in test_categories:
        print(f"\n📊 Running {category['name']}")
        print(f"   {category['description']}")
        print("-" * 40)
        
        start_time = time.time()
        
        try:
            # Run tests with verbose output
            result = subprocess.run([
                sys.executable, '-m', 'pytest',
                category['path'],
                '-v',
                '--tb=short',
                '-x',  # Stop on first failure
                '--disable-warnings'
            ], capture_output=True, text=True, cwd=os.getcwd())
            
            end_time = time.time()
            duration = end_time - start_time
            
            results[category['name']] = {
                'success': result.returncode == 0,
                'duration': duration,
                'output': result.stdout,
                'errors': result.stderr
            }
            
            if result.returncode == 0:
                print(f"✅ {category['name']} - PASSED ({duration:.2f}s)")
            else:
                print(f"❌ {category['name']} - FAILED ({duration:.2f}s)")
                print(f"Error: {result.stderr}")
                
        except Exception as e:
            print(f"❌ {category['name']} - ERROR: {e}")
            results[category['name']] = {
                'success': False,
                'duration': 0,
                'output': '',
                'errors': str(e)
            }
    
    # Summary report
    print("\n" + "=" * 60)
    print("📈 PERFORMANCE TEST SUMMARY")
    print("=" * 60)
    
    total_tests = len(test_categories)
    passed_tests = sum(1 for r in results.values() if r['success'])
    total_time = sum(r['duration'] for r in results.values())
    
    print(f"Total Categories: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Total Time: {total_time:.2f}s")
    print(f"Success Rate: {passed_tests/total_tests*100:.1f}%")
    
    # Detailed results
    print(f"\n📋 DETAILED RESULTS:")
    for name, result in results.items():
        status = "✅ PASS" if result['success'] else "❌ FAIL"
        print(f"  {name:<25} {status:<8} ({result['duration']:.2f}s)")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = run_performance_tests()
    sys.exit(0 if success else 1)