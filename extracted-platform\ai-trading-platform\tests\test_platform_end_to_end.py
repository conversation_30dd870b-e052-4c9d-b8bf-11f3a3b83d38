
def test_end_to_end_trade_flow():
    from bridge.mt5_client import MT5<PERSON><PERSON>
    from analysis.metrics import calculate_win_rate
    from simulation.broker import FakeBroker

    mt5 = MT5Client(dummy_mode=True)
    broker = FakeBroker()

    # Place an order via the bridge, fill it via the broker
    order = mt5.submit_order(mt5.get_account_info())
    result = broker.execute_order(order)
    assert result.success

    # Calculate win rate on mock trades
    trades = [{"pnl": 10}, {"pnl": -5}, {"pnl": 5}]
    win_rate = calculate_win_rate(trades)
    assert 0 <= win_rate <= 1
