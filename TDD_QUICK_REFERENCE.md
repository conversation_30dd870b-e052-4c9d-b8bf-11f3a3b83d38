# TDD Quick Reference Guide

This quick reference guide provides essential TDD patterns and practices for the AI Enhanced Trading Platform.

## TDD Cycle

```
┌─────────────┐
│             │
│  1. R<PERSON>     │
│  Write a    │
│  failing    │
│  test       │
│             │
└──────┬──────┘
       │
       ▼
┌─────────────┐
│             │
│  2. <PERSON><PERSON><PERSON>   │
│  Make the   │
│  test pass  │
│             │
└──────┬──────┘
       │
       ▼
┌─────────────┐
│             │
│  3. <PERSON><PERSON><PERSON><PERSON><PERSON>│
│  Improve    │
│  the code   │
│             │
└──────┬──────┘
       │
       ▼
    Repeat
```

## Test Structure

Use the AAA pattern (Arrange-Act-Assert):

```python
def test_when_condition_then_expected_result():
    # Arrange: Set up the test conditions
    feature = FeatureClass(dependencies)
    
    # Act: Call the method being tested
    result = feature.method(test_input)
    
    # Assert: Verify the expected outcome
    assert result == expected_output
```

## Test Naming Patterns

Use descriptive names that explain the behavior:

```python
# Good:
test_when_invalid_symbol_then_raises_value_error()
test_when_connection_lost_then_reconnects_automatically()
test_calculate_profit_returns_correct_value_for_buy_orders()

# Avoid:
test_place_order()  # Too vague
test_error_case()   # Not descriptive enough
```

## Common Test Patterns

### 1. Testing Return Values

```python
def test_when_valid_input_then_returns_expected_output():
    # Arrange
    calculator = Calculator()
    
    # Act
    result = calculator.add(2, 3)
    
    # Assert
    assert result == 5
```

### 2. Testing Exceptions

```python
def test_when_invalid_input_then_raises_value_error():
    # Arrange
    calculator = Calculator()
    
    # Act & Assert
    with pytest.raises(ValueError, match="Division by zero"):
        calculator.divide(10, 0)
```

### 3. Testing with Mocks

```python
@patch('module.Dependency')
def test_when_dependency_returns_value_then_uses_it_correctly(mock_dependency):
    # Arrange
    mock_dependency.return_value.get_data.return_value = [1, 2, 3]
    feature = Feature(dependency=mock_dependency.return_value)
    
    # Act
    result = feature.process_data()
    
    # Assert
    assert result == 6  # sum of [1, 2, 3]
    mock_dependency.return_value.get_data.assert_called_once()
```

### 4. Parameterized Tests

```python
@pytest.mark.parametrize("input_value,expected_output", [
    (0, 1),      # Edge case: factorial of 0
    (1, 1),      # Edge case: factorial of 1
    (5, 120),    # Normal case
    (10, 3628800)  # Larger number
])
def test_factorial_returns_correct_result(input_value, expected_output):
    calculator = Calculator()
    result = calculator.factorial(input_value)
    assert result == expected_output
```

### 5. Testing State Changes

```python
def test_when_order_placed_then_order_count_increases():
    # Arrange
    order_manager = OrderManager()
    initial_count = len(order_manager.orders)
    
    # Act
    order_manager.place_order("EURUSD", "BUY", 0.1)
    
    # Assert
    assert len(order_manager.orders) == initial_count + 1
```

## Common Testing Tools

### Fixtures

```python
@pytest.fixture
def sample_data():
    """Fixture providing sample data for tests"""
    return {
        "prices": [100, 101, 102, 101, 100],
        "volumes": [1000, 1200, 1500, 1300, 1100]
    }

def test_calculate_vwap(sample_data):
    calculator = PriceCalculator()
    vwap = calculator.calculate_vwap(
        prices=sample_data["prices"],
        volumes=sample_data["volumes"]
    )
    assert vwap == 101.0
```

### Markers

```python
@pytest.mark.slow
def test_performance_intensive_operation():
    # Test code...

@pytest.mark.integration
def test_integrates_with_external_system():
    # Test code...
```

### Test Doubles

```python
# Mock
from unittest.mock import MagicMock
data_provider = MagicMock()
data_provider.get_data.return_value = [1, 2, 3]

# Stub
class StubDataProvider:
    def get_data(self):
        return [1, 2, 3]

# Spy
class SpyDataProvider:
    def __init__(self):
        self.get_data_called = 0
    
    def get_data(self):
        self.get_data_called += 1
        return [1, 2, 3]
```

## Running Tests

### Basic Test Run

```bash
# Run a specific test file
python -m pytest tests/test_feature.py

# Run tests with a specific marker
python -m pytest -m "unit"

# Run tests with verbose output
python -m pytest -v
```

### Enhanced Test Run

```bash
# Run tests with coverage
python run_tests.py coverage

# Run quick tests
python run_tests.py quick

# Run pilot tests with enhanced reporting
python scripts/run_pilot_tests.py tests/test_feature.py
```

## TDD Resources

- **Templates**: `tests/templates/tdd_template.py`
- **Workflow Guide**: `TDD_WORKFLOW_GUIDE.md`
- **Implementation Roadmap**: `TDD_IMPLEMENTATION_ROADMAP.md`
- **Test-First Development Guide**: `TEST_FIRST_DEVELOPMENT.md`

## Common TDD Pitfalls to Avoid

1. **Writing tests after code**: Defeats the purpose of TDD
2. **Testing implementation details**: Focus on behavior, not implementation
3. **Brittle tests**: Tests that break when implementation changes
4. **Incomplete test coverage**: Missing edge cases or error conditions
5. **Slow tests**: Tests that take too long to run
6. **Test duplication**: Redundant tests that test the same behavior
7. **Overly complex tests**: Tests that are hard to understand or maintain