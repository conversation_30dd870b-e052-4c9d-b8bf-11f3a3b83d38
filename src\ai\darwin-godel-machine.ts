// src/ai/darwin-godel-machine.ts
// Reproducible Evolution with Zero Hallucination
import { ForexDataProvider } from '../data-management/forex-data-provider';

export interface EvolutionOptions {
  seed?: string;
  generations?: number;
  populationSize?: number;
  mutationRate?: number;
}

export interface BacktestResults {
  sharpeRatio: number;
  profitFactor: number;
  winRate: number;
  maxDrawdown: number;
  totalTrades?: number;
  totalReturn?: number;
}

export interface FitnessBreakdown {
  sharpeComponent: number;
  profitComponent: number;
  winRateComponent: number;
  drawdownComponent: number;
  totalFitness: number;
}

export interface ParameterMutation {
  parameter: string;
  oldValue: number;
  newValue: number;
  generation: number;
  fitness: number;
}

export interface EvolutionAuditTrail {
  evolutionId: string;
  seed: string;
  generations: number;
  startTime: Date;
  endTime: Date;
  dataHash: string;
  dataPoints: number;
  backtestsPerformed: number;
  mutations: ParameterMutation[];
}

export interface EvolutionResult {
  optimizedParameters: Record<string, number>;
  initialFitness: number;
  finalFitness: number;
  improvementPercentage: number;
  auditTrail: EvolutionAuditTrail;
}

export class DarwinGodelMachine {
  private dataProvider: ForexDataProvider;
  private seedValue: number = 0;

  constructor(dataProvider: ForexDataProvider) {
    this.dataProvider = dataProvider;
  }

  async evolve(
    strategy: any, 
    data: any, 
    options: EvolutionOptions = {}
  ): Promise<EvolutionResult> {
    const startTime = new Date();
    
    // Validate inputs
    this.validateStrategy(strategy);
    await this.validateData(data);
    
    // Set up evolution parameters
    const seed = options.seed || `${Date.now()}-${Math.random()}`;
    const generations = options.generations || 50;
    const populationSize = options.populationSize || 20;
    
    // Initialize seeded random number generator
    this.initializeSeed(seed);
    
    // Get verified data
    const verifiedData = await this.getVerifiedData(data);
    
    // Create audit trail
    const evolutionId = `evo_${Date.now()}_${this.generateId()}`;
    const auditTrail: EvolutionAuditTrail = {
      evolutionId,
      seed,
      generations,
      startTime,
      endTime: new Date(), // Will be updated
      dataHash: verifiedData.verification?.originalHash || 'unknown',
      dataPoints: verifiedData.candles.length,
      backtestsPerformed: 0,
      mutations: []
    };

    // Calculate initial fitness
    const initialBacktest = await this.runBacktest(strategy.parameters, verifiedData);
    const initialFitness = this.calculateFitness(initialBacktest);
    
    // Evolution loop
    let bestParameters = { ...strategy.parameters };
    let bestFitness = initialFitness;
    
    for (let generation = 0; generation < generations; generation++) {
      const population = this.generatePopulation(bestParameters, populationSize, strategy.parameterRanges);
      
      for (const individual of population) {
        const backtestResults = await this.runBacktest(individual, verifiedData);
        const fitness = this.calculateFitness(backtestResults);
        auditTrail.backtestsPerformed++;
        
        if (fitness > bestFitness) {
          // Record mutation
          const mutations = this.findParameterChanges(bestParameters, individual, generation, fitness);
          auditTrail.mutations.push(...mutations);
          
          bestParameters = { ...individual };
          bestFitness = fitness;
        }
      }
    }
    
    auditTrail.endTime = new Date();
    
    const improvementPercentage = ((bestFitness - initialFitness) / initialFitness) * 100;
    
    return {
      optimizedParameters: bestParameters,
      initialFitness,
      finalFitness: bestFitness,
      improvementPercentage,
      auditTrail
    };
  }

  calculateFitness(backtestResults: BacktestResults): number {
    // Validate backtest results
    this.validateBacktestResults(backtestResults);
    
    // Transparent fitness formula
    const sharpeWeight = 0.3;
    const profitWeight = 0.2;
    const winRateWeight = 0.2;
    const drawdownWeight = 0.3;
    
    const fitness = 
      (sharpeWeight * backtestResults.sharpeRatio) +
      (profitWeight * backtestResults.profitFactor) +
      (winRateWeight * backtestResults.winRate) +
      (drawdownWeight * (100 - backtestResults.maxDrawdown));
    
    return fitness;
  }

  getFitnessBreakdown(backtestResults: BacktestResults): FitnessBreakdown {
    this.validateBacktestResults(backtestResults);
    
    const sharpeComponent = 0.3 * backtestResults.sharpeRatio;
    const profitComponent = 0.2 * backtestResults.profitFactor;
    const winRateComponent = 0.2 * backtestResults.winRate;
    const drawdownComponent = 0.3 * (100 - backtestResults.maxDrawdown);
    
    return {
      sharpeComponent,
      profitComponent,
      winRateComponent,
      drawdownComponent,
      totalFitness: sharpeComponent + profitComponent + winRateComponent + drawdownComponent
    };
  }

  seededRandom(seed: string): number {
    // Simple seeded random number generator for reproducibility
    let hash = 0;
    for (let i = 0; i < seed.length; i++) {
      const char = seed.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    // Use the hash as seed for a simple LCG
    const a = 1664525;
    const c = 1013904223;
    const m = Math.pow(2, 32);
    
    this.seedValue = (a * Math.abs(hash) + c) % m;
    return this.seedValue / m;
  }

  private initializeSeed(seed: string): void {
    this.seededRandom(seed);
  }

  private nextSeededRandom(): number {
    const a = 1664525;
    const c = 1013904223;
    const m = Math.pow(2, 32);
    
    this.seedValue = (a * this.seedValue + c) % m;
    return this.seedValue / m;
  }

  private validateStrategy(strategy: any): void {
    if (!strategy.id || strategy.id.trim() === '') {
      throw new Error('Invalid strategy configuration: Missing strategy ID');
    }
    
    if (!strategy.parameters || Object.keys(strategy.parameters).length === 0) {
      throw new Error('Invalid strategy configuration: No parameters defined');
    }
  }

  private async validateData(data: any): Promise<void> {
    if (!data.candles || data.candles.length < 100) {
      throw new Error('Insufficient data for reliable evolution: Minimum 100 candles required');
    }
    
    // Validate OHLC relationships
    for (const candle of data.candles.slice(0, 10)) { // Check first 10 candles
      if (candle.high < candle.low || 
          candle.high < candle.open || 
          candle.high < candle.close ||
          candle.low > candle.open || 
          candle.low > candle.close) {
        throw new Error('Data validation failed: Invalid OHLC relationships detected');
      }
    }
  }

  private async getVerifiedData(data: any): Promise<any> {
    try {
      const result = await this.dataProvider.getData('EUR/USD', 'H1'); // Simplified for demo
      if (result.success) {
        return result.data;
      }
    } catch (error) {
      // Fall back to provided data if data provider fails
    }
    
    return data;
  }

  private validateBacktestResults(results: BacktestResults): void {
    if (results.sharpeRatio < -10 || results.sharpeRatio > 10) {
      throw new Error('Invalid backtest results: Sharpe ratio out of reasonable range');
    }
    
    if (results.profitFactor < 0 || results.profitFactor > 20) {
      throw new Error('Invalid backtest results: Profit factor out of reasonable range');
    }
    
    if (results.winRate < 0 || results.winRate > 100) {
      throw new Error('Invalid backtest results: Win rate must be between 0-100%');
    }
    
    if (results.maxDrawdown < 0 || results.maxDrawdown > 100) {
      throw new Error('Invalid backtest results: Max drawdown must be between 0-100%');
    }
  }

  private generatePopulation(
    baseParameters: Record<string, number>, 
    size: number, 
    ranges?: Record<string, { min: number; max: number }>
  ): Record<string, number>[] {
    const population: Record<string, number>[] = [];
    
    for (let i = 0; i < size; i++) {
      const individual: Record<string, number> = {};
      
      for (const [param, value] of Object.entries(baseParameters)) {
        const range = ranges?.[param];
        let newValue: number;
        
        if (range) {
          // Mutate within specified range
          const mutation = (this.nextSeededRandom() - 0.5) * 0.2; // ±10% mutation
          newValue = value + (value * mutation);
          newValue = Math.max(range.min, Math.min(range.max, newValue));
        } else {
          // Default mutation for parameters without ranges
          const mutation = (this.nextSeededRandom() - 0.5) * 0.1; // ±5% mutation
          newValue = value + (value * mutation);
        }
        
        individual[param] = Math.round(newValue * 100) / 100; // Round to 2 decimal places
      }
      
      population.push(individual);
    }
    
    return population;
  }

  private async runBacktest(parameters: Record<string, number>, data: any): Promise<BacktestResults> {
    // Simplified backtest simulation for demo
    // In real implementation, this would run the actual strategy
    
    const basePerformance = {
      sharpeRatio: 1.0 + this.nextSeededRandom() * 2,
      profitFactor: 1.2 + this.nextSeededRandom() * 1.5,
      winRate: 45 + this.nextSeededRandom() * 30,
      maxDrawdown: 5 + this.nextSeededRandom() * 20
    };
    
    // Simulate parameter impact on performance
    const rsiPeriod = parameters.rsi_period || 14;
    const rsiOptimal = 21; // Simulated optimal value
    const rsiDistance = Math.abs(rsiPeriod - rsiOptimal) / rsiOptimal;
    const rsiPenalty = rsiDistance * 0.3;
    
    return {
      sharpeRatio: Math.max(0.1, basePerformance.sharpeRatio - rsiPenalty),
      profitFactor: Math.max(0.8, basePerformance.profitFactor - rsiPenalty),
      winRate: Math.max(20, basePerformance.winRate - (rsiPenalty * 50)),
      maxDrawdown: Math.min(50, basePerformance.maxDrawdown + (rsiPenalty * 20))
    };
  }

  private findParameterChanges(
    oldParams: Record<string, number>,
    newParams: Record<string, number>,
    generation: number,
    fitness: number
  ): ParameterMutation[] {
    const mutations: ParameterMutation[] = [];
    
    for (const [param, newValue] of Object.entries(newParams)) {
      const oldValue = oldParams[param];
      if (oldValue !== newValue) {
        mutations.push({
          parameter: param,
          oldValue,
          newValue,
          generation,
          fitness
        });
      }
    }
    
    return mutations;
  }

  private generateId(): string {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(this.nextSeededRandom() * chars.length));
    }
    return result;
  }
}