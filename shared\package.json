{"name": "@ai-trading/shared", "version": "1.0.0", "description": "Shared schemas and types for AI Trading Platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "type-check": "tsc --noEmit"}, "dependencies": {"zod": "^3.22.4"}, "devDependencies": {"@types/jest": "^29.5.8", "@types/node": "^20.9.0", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "eslint": "^8.53.0", "jest": "^29.7.0", "rimraf": "^5.0.5", "ts-jest": "^29.1.1", "typescript": "^5.2.2"}}