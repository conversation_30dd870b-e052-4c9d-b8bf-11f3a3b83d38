﻿/**
 * Upload Page
 * Main page for file uploading and data management
 */

// import React from 'react'; // Not needed with new JSX transform
import { FileUpload } from '@/components/upload/FileUpload';
// import { useAuth } from '@/hooks/useAuth'; // Not currently used

export function UploadPage() {
  // const { user } = useAuth(); // Not currently used

  const handleUploadComplete = (session: any) => {
    // Redirect to backtesting or show success message
    console.log('Upload completed:', session);
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Upload Trading Data</h1>
        <p className="text-gray-600">
          Upload your trading data files to start backtesting and analysis. 
          We support CSV, Excel, and JSON formats with intelligent column detection.
        </p>
      </div>

      <FileUpload 
        onUploadComplete={handleUploadComplete}
        className="mb-8"
      />
    </div>
  );
}