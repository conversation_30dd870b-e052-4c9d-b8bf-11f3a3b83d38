import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Eye } from 'lucide-react';

interface ThemeDemoProps {
  currentTheme: string;
}

const ThemeDemo: React.FC<ThemeDemoProps> = ({ currentTheme }) => {
  const themeInfo = {
    enhanced: {
      name: 'Enhanced Dashboard',
      description: 'Multi-section layout with AI Strategy Helper, professional prompts, and comprehensive trading tools',
      features: ['Side-by-side AI prompts & chat', 'Professional trading prompts', 'Strategy management', 'Real-time metrics'],
      color: 'blue'
    },
    professional: {
      name: 'Professional Single-Page',
      description: 'Enterprise-grade single-page design with smooth scrolling, comprehensive analytics, and SOC 2 compliance',
      features: ['Single-page scrolling design', 'Enterprise security badges', 'Professional metrics', 'Comprehensive analytics'],
      color: 'indigo'
    }
  };

  const info = themeInfo[currentTheme as keyof typeof themeInfo] || themeInfo.enhanced;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={`bg-gradient-to-r from-${info.color}-50 to-${info.color}-100 border border-${info.color}-200 rounded-lg p-4 mb-4`}
    >
      <div className="flex items-start space-x-3">
        <div className={`p-2 bg-${info.color}-500 rounded-lg`}>
          <Eye className="w-5 h-5 text-white" />
        </div>
        <div className="flex-1">
          <h3 className={`text-lg font-semibold text-${info.color}-900 mb-1`}>
            Current Theme: {info.name}
          </h3>
          <p className={`text-${info.color}-700 text-sm mb-3`}>
            {info.description}
          </p>
          <div className="flex flex-wrap gap-2">
            {info.features.map((feature, index) => (
              <span
                key={index}
                className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-${info.color}-200 text-${info.color}-800`}
              >
                <Sparkles className="w-3 h-3 mr-1" />
                {feature}
              </span>
            ))}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default ThemeDemo;