// darwin-godel-api.ts
// Main Darwin Gödel API that integrates all components
// Provides the complete API interface described in the integration guide

import express, { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import WebSocket from 'ws';
import { z } from 'zod';
import { spawn } from 'child_process';
import path from 'path';

import S3CoreEngine from './s3-core-nlp-engine';
import StrategyVerificationEngine from './strategy-verification-engine';

// Schema definitions
const ChatRequestSchema = z.object({
  message: z.string().min(1).max(1000),
  context: z.object({
    pair: z.string().optional(),
    timeframe: z.string().optional(),
    sessionId: z.string().optional()
  }).optional()
});

const EvolutionRequestSchema = z.object({
  pair: z.string().regex(/^[A-Z]{6}$/), // e.g., EURUSD
  timeframe: z.enum(['1M', '5M', '15M', '1H', '4H', '1D', '1W']),
  generations: z.number().min(1).max(100).default(20),
  populationSize: z.number().min(10).max(200).default(50),
  fitnessGoal: z.enum(['sharpe', 'profit_factor', 'win_rate', 'max_drawdown']).default('sharpe')
});

const StrategyVerificationRequestSchema = z.object({
  strategy: z.object({
    id: z.string(),
    name: z.string(),
    description: z.string(),
    conditions: z.array(z.object({
      indicator: z.string(),
      operator: z.enum(['>', '<', '>=', '<=', '==', 'crossover', 'crossunder']),
      value: z.union([z.number(), z.string()]),
      timeframe: z.string().optional(),
      period: z.number().optional()
    })),
    action: z.enum(['buy', 'sell', 'close']),
    riskManagement: z.object({
      stopLossPct: z.number(),
      takeProfitPct: z.number(),
      positionSizePct: z.number(),
      maxPositions: z.number().optional(),
      riskPerTrade: z.number().optional()
    })
  }),
  pair: z.string().regex(/^[A-Z]{6}$/)
});

export class DarwinGodelAPI {
  private app: express.Application;
  private server: any;
  private wss: WebSocket.Server | null = null;
  private s3Core: S3CoreEngine;
  private verificationEngine: StrategyVerificationEngine;
  private pythonDarwinEngine: any = null;
  private activeEvolutionJobs: Map<string, any> = new Map();
  private websocketClients: Set<WebSocket> = new Set();

  constructor(
    private port: number = 3001,
    private _openaiApiKey?: string,
    private _coqPath: string = 'coqc'
  ) {
    this.app = express();
    this.s3Core = new S3CoreEngine(this._openaiApiKey || process.env.OPENAI_API_KEY || '');
    this.verificationEngine = new StrategyVerificationEngine(this._coqPath);
    
    this.setupMiddleware();
    this.setupRoutes();
  }

  private setupMiddleware(): void {
    // Security middleware
    this.app.use(helmet());
    this.app.use(cors({
      origin: process.env.FRONTEND_URL || 'http://localhost:3000',
      credentials: true
    }));

    // Rate limiting
    const limiter = rateLimit({
      windowMs: parseInt(process.env.RATE_LIMIT_WINDOW || '900000'), // 15 minutes
      max: parseInt(process.env.RATE_LIMIT_MAX || '100'), // limit each IP to 100 requests per windowMs
      message: 'Too many requests from this IP, please try again later.'
    });
    this.app.use('/api/', limiter as any);

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));

    // Request logging
    this.app.use((req: Request, _res: Response, next: NextFunction) => {
      console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
      next();
    });
  }

  private setupRoutes(): void {
    // Health check
    this.app.get('/health', (_req: Request, res: Response) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        services: {
          s3Core: 'active',
          verificationEngine: 'active',
          darwinEngine: this.pythonDarwinEngine ? 'active' : 'inactive'
        }
      });
    });

    // Chat endpoint - Main S3 Core interface
    this.app.post('/api/chat', async (req: Request, res: Response) => {
      try {
        const { message } = ChatRequestSchema.parse(req.body);
        
        // Translate natural language query
        const query = await this.s3Core.translateQuery(message);
        
        let response: any = {
          query,
          timestamp: new Date().toISOString()
        };

        // Handle different query types
        switch (query.type) {
          case 'analysis':
            if (query.pair && query.timeframe) {
              const analysis = await this.s3Core.generateAnalysis(query);
              response.analysis = analysis;
            } else {
              response.message = "Please specify a currency pair and timeframe for analysis.";
            }
            break;

          case 'scan':
            const scanResults = await this.s3Core.scanMarkets(message);
            response.scanResults = scanResults;
            break;

          case 'indicator':
            const explanation = await this.s3Core.explainIndicator(
              query.indicators?.[0] || 'RSI',
              query.pair,
              query.timeframe
            );
            response.explanation = explanation;
            break;

          case 'strategy':
            const validation = await this.s3Core.validateStrategy(message);
            response.validation = validation;
            break;

          default:
            response.message = "I understand your query. How can I help you with trading analysis?";
        }

        res.json(response);

      } catch (error) {
        console.error('Chat endpoint error:', error);
        res.status(500).json({
          error: 'Failed to process chat message',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    });

    // Strategy verification endpoint
    this.app.post('/api/verify-strategy', async (req: Request, res: Response) => {
      try {
        const { strategy, pair } = StrategyVerificationRequestSchema.parse(req.body);
        
        const verificationResult = await this.verificationEngine.verifyStrategy(strategy, pair);
        
        res.json({
          success: true,
          verification: verificationResult,
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        console.error('Strategy verification error:', error);
        res.status(500).json({
          error: 'Failed to verify strategy',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    });

    // Evolution endpoint - Start Darwin engine evolution
    this.app.post('/api/evolve-strategies', async (req: Request, res: Response) => {
      try {
        const evolutionParams = EvolutionRequestSchema.parse(req.body);
        
        // Start Python Darwin engine
        const jobResult = await this.startEvolutionJob(evolutionParams);
        
        res.json({
          success: true,
          job: jobResult,
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        console.error('Evolution endpoint error:', error);
        res.status(500).json({
          error: 'Failed to start evolution job',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    });

    // Get evolution job status
    this.app.get('/api/evolution-job/:jobId', (req: Request, res: Response) => {
      const { jobId } = req.params;
      const job = this.activeEvolutionJobs.get(jobId);
      
      if (!job) {
        return res.status(404).json({
          error: 'Evolution job not found'
        });
      }
      
      res.json({
        success: true,
        job,
        timestamp: new Date().toISOString()
      });
    });

    // Get proven strategies
    this.app.get('/api/proven-strategies', async (req: Request, res: Response) => {
      try {
        const { pair, limit = '10' } = req.query;
        
        // In a real implementation, this would query the database
        // For now, return mock data
        const strategies = await this.getProvenStrategies(
          pair as string,
          parseInt(limit as string)
        );
        
        res.json({
          success: true,
          strategies,
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        console.error('Get proven strategies error:', error);
        res.status(500).json({
          error: 'Failed to get proven strategies',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    });

    // Get forex genome
    this.app.get('/api/forex-genome/:pair/:timeframe', async (req: Request, res: Response) => {
      try {
        const { pair, timeframe } = req.params;
        
        // In a real implementation, this would query the database
        const genome = await this.getForexGenome(pair, timeframe);
        
        res.json({
          success: true,
          genome,
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        console.error('Get forex genome error:', error);
        res.status(500).json({
          error: 'Failed to get forex genome',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    });

    // Batch strategy verification
    this.app.post('/api/batch-verify', async (req: Request, res: Response) => {
      try {
        const { strategies, pair } = req.body;
        
        if (!Array.isArray(strategies) || !pair) {
          return res.status(400).json({
            error: 'Invalid request: strategies array and pair required'
          });
        }
        
        const results = await this.verificationEngine.batchVerifyStrategies(strategies, pair);
        
        res.json({
          success: true,
          results,
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        console.error('Batch verification error:', error);
        res.status(500).json({
          error: 'Failed to batch verify strategies',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    });

    // Error handling middleware
    this.app.use((error: Error, _req: Request, res: Response, _next: NextFunction) => {
      console.error('Unhandled error:', error);
      res.status(500).json({
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
      });
    });
  }

  private async startEvolutionJob(params: any): Promise<any> {
    return new Promise((resolve, reject) => {
      try {
        // Generate job ID
        const jobId = `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        // Start Python Darwin engine process
        const pythonScript = path.join(__dirname, '../../../shared/darwin_engine_integration.py');
        const pythonProcess = spawn('python', [
          '-c',
          `
import sys
sys.path.append('${path.dirname(pythonScript)}')
from darwin_engine_integration import DarwinGodelMachine
import asyncio
import json

async def main():
    dgm = DarwinGodelMachine(population_size=${params.populationSize})
    result = await dgm.evolve_strategies(
        pair="${params.pair}",
        timeframe="${params.timeframe}",
        generations=${params.generations},
        fitness_objective="${params.fitnessGoal}"
    )
    print(json.dumps(result))

asyncio.run(main())
          `
        ]);

        let output = '';
        let error = '';

        pythonProcess.stdout.on('data', (data) => {
          output += data.toString();
        });

        pythonProcess.stderr.on('data', (data) => {
          error += data.toString();
        });

        pythonProcess.on('close', (code) => {
          if (code === 0) {
            try {
              const result = JSON.parse(output.trim());
              result.jobId = jobId;
              
              // Store job info
              this.activeEvolutionJobs.set(jobId, {
                ...result,
                status: 'running',
                startTime: new Date().toISOString(),
                params
              });
              
              // Broadcast to WebSocket clients
              this.broadcastToClients({
                type: 'evolution_started',
                jobId,
                data: result
              });
              
              resolve(result);
            } catch (parseError) {
              reject(new Error(`Failed to parse Python output: ${parseError}`));
            }
          } else {
            reject(new Error(`Python process failed: ${error}`));
          }
        });

        pythonProcess.on('error', (err) => {
          reject(new Error(`Failed to start Python process: ${err.message}`));
        });

      } catch (error) {
        reject(error);
      }
    });
  }

  private async getProvenStrategies(_pair?: string, limit: number = 10): Promise<any[]> {
    // Mock implementation - in production, this would query your database
    return [
      {
        id: 'strategy_001',
        name: 'RSI Mean Reversion',
        description: 'Buy when RSI < 30, sell when RSI > 70',
        fitnessScore: 0.85,
        verified: true,
        pair: 'EURUSD',
        performance: {
          sharpeRatio: 1.8,
          winRate: 0.65,
          maxDrawdown: 0.12
        }
      },
      {
        id: 'strategy_002',
        name: 'MACD Crossover',
        description: 'Buy on MACD bullish crossover',
        fitnessScore: 0.78,
        verified: true,
        pair: 'GBPUSD',
        performance: {
          sharpeRatio: 1.5,
          winRate: 0.58,
          maxDrawdown: 0.15
        }
      }
    ].slice(0, limit);
  }

  private async getForexGenome(pair: string, timeframe: string): Promise<any> {
    // Mock implementation - in production, this would query your database
    return {
      pair,
      timeframe,
      sessionPatterns: {
        asianSession: { performance: 0.7, preferredIndicators: ['RSI', 'MACD'] },
        londonSession: { performance: 0.8, preferredIndicators: ['EMA', 'SMA'] },
        newYorkSession: { performance: 0.9, preferredIndicators: ['STOCH', 'RSI'] }
      },
      volatilityProfile: {
        lowVolatility: { preferredRisk: 1.5, optimalTimeframes: ['4H', '1D'] },
        mediumVolatility: { preferredRisk: 2.0, optimalTimeframes: ['1H', '4H'] },
        highVolatility: { preferredRisk: 3.0, optimalTimeframes: ['15M', '1H'] }
      },
      optimalStrategies: await this.getProvenStrategies(pair, 5),
      confidenceScore: 0.82,
      lastUpdated: new Date().toISOString()
    };
  }

  private setupWebSocket(): void {
    if (!this.server) return;

    this.wss = new WebSocket.Server({ server: this.server });

    this.wss.on('connection', (ws: WebSocket) => {
      console.log('New WebSocket connection established');
      this.websocketClients.add(ws);

      ws.on('message', (message: string) => {
        try {
          const data = JSON.parse(message);
          this.handleWebSocketMessage(ws, data);
        } catch (error) {
          console.error('Invalid WebSocket message:', error);
        }
      });

      ws.on('close', () => {
        console.log('WebSocket connection closed');
        this.websocketClients.delete(ws);
      });

      ws.on('error', (error) => {
        console.error('WebSocket error:', error);
        this.websocketClients.delete(ws);
      });

      // Send welcome message
      ws.send(JSON.stringify({
        type: 'welcome',
        message: 'Connected to Darwin Gödel Machine',
        timestamp: new Date().toISOString()
      }));
    });
  }

  private handleWebSocketMessage(ws: WebSocket, data: any): void {
    switch (data.type) {
      case 'subscribe_evolution':
        // Subscribe to evolution updates for specific job
        ws.send(JSON.stringify({
          type: 'subscription_confirmed',
          jobId: data.jobId,
          timestamp: new Date().toISOString()
        }));
        break;

      case 'ping':
        ws.send(JSON.stringify({
          type: 'pong',
          timestamp: new Date().toISOString()
        }));
        break;

      default:
        ws.send(JSON.stringify({
          type: 'error',
          message: 'Unknown message type',
          timestamp: new Date().toISOString()
        }));
    }
  }

  private broadcastToClients(message: any): void {
    const messageStr = JSON.stringify({
      ...message,
      timestamp: new Date().toISOString()
    });

    this.websocketClients.forEach((client) => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(messageStr);
      }
    });
  }

  async start(): Promise<void> {
    try {
      // Initialize verification engine
      await this.verificationEngine.initialize();
      console.log('Strategy Verification Engine initialized');

      // Start HTTP server
      this.server = this.app.listen(this.port, () => {
        console.log(`Darwin Gödel API server running on port ${this.port}`);
      });

      // Setup WebSocket server
      this.setupWebSocket();
      console.log('WebSocket server initialized');

      console.log('🚀 Darwin Gödel Machine API is ready!');
      console.log(`📊 S3 Core NLP Engine: Active`);
      console.log(`🔬 Strategy Verification Engine: Active`);
      console.log(`🧬 Evolution Engine: Ready`);
      console.log(`🌐 WebSocket Server: Active`);

    } catch (error) {
      console.error('Failed to start Darwin Gödel API:', error);
      throw error;
    }
  }

  async stop(): Promise<void> {
    console.log('Shutting down Darwin Gödel API...');

    // Close WebSocket server
    if (this.wss) {
      this.wss.close();
    }

    // Close HTTP server
    if (this.server) {
      this.server.close();
    }

    // Cleanup verification engine
    await this.verificationEngine.cleanup();

    console.log('Darwin Gödel API shutdown complete');
  }
}

// Export for use in other modules
export default DarwinGodelAPI;

// If running directly, start the server
if (require.main === module) {
  const api = new DarwinGodelAPI();
  
  api.start().catch((error) => {
    console.error('Failed to start server:', error);
    process.exit(1);
  });

  // Graceful shutdown
  process.on('SIGINT', async () => {
    console.log('Received SIGINT, shutting down gracefully...');
    await api.stop();
    process.exit(0);
  });

  process.on('SIGTERM', async () => {
    console.log('Received SIGTERM, shutting down gracefully...');
    await api.stop();
    process.exit(0);
  });
}