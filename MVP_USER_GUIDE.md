# AI Enhanced Trading Platform - MVP User Guide

This guide provides instructions for setting up and using the Minimum Viable Product (MVP) version of the AI Enhanced Trading Platform.

## Quick Start

### Prerequisites

- Python 3.13.2 or later
- Node.js and npm (for the frontend)
- MetaTrader 5 (optional, the MVP can run in offline mode)

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/ai-enhanced-trading-platform.git
   cd ai-enhanced-trading-platform
   ```

2. Run the MVP setup script:
   ```bash
   python mvp_setup_simple.py
   ```

3. Start the backend server:
   ```bash
   python backend/minimal_server.py
   ```

4. In a new terminal, start the frontend:
   ```bash
   cd frontend
   npm install
   npm run dev:mvp
   ```

5. Open your browser and navigate to:
   ```
   http://localhost:5173
   ```

## Using the Platform

### User Interface Overview

The MVP provides a simple but powerful user interface for managing your trading activities:

![Dashboard](docs/screenshots/dashboard.png)
*Dashboard showing portfolio overview and recent trades*

### Historical Data Management

The platform allows you to upload and manage historical price data for backtesting:

1. **Upload Historical Data**:
   - Prepare a CSV file with columns: timestamp, open, high, low, close, volume
   - Navigate to the "Data" section
   - Select the symbol and timeframe
   - Upload your CSV file

2. **View Available Data**:
   - Navigate to the "Data" section
   - View the list of available data sets
   - Click on a data set to view details

3. **Using Historical Data in Backtests**:
   - When creating a backtest, select from available data sets
   - The platform will automatically use the uploaded data for the selected symbol and timeframe

### Creating a Trading Strategy

1. Navigate to the "Strategies" section
2. Click "Create New Strategy"
3. Enter a name and description
4. Configure the strategy parameters (e.g., fast period, slow period)
5. Click "Create Strategy"

![Strategy Creation](docs/screenshots/strategy_creation.png)
*Strategy creation interface*

### Running a Backtest

1. Navigate to the "Backtests" section
2. Select a strategy from the dropdown
3. Configure the backtest parameters:
   - Symbol (e.g., "EURUSD")
   - Timeframe (e.g., "H1")
   - Date range
   - Initial capital
4. Click "Run Backtest"
5. View the results in the backtest report

![Backtesting Interface](docs/screenshots/backtesting.png)
*Backtesting interface with performance metrics*

### Executing Trades (Offline Mode)

1. Navigate to the "Trading" section
2. Select a symbol (e.g., "EURUSD")
3. Enter the lot size
4. Choose the order type (BUY or SELL)
5. Click "Execute Trade"
6. View the trade in the "Positions" section

![Trading Interface](docs/screenshots/trading.png)
*Trading interface for executing orders*

![Portfolio View](docs/screenshots/portfolio.png)
*Portfolio view showing open positions*

## Troubleshooting

### Common Issues

1. **Backend server not starting**
   - Check if port 8000 is already in use
   - Ensure all dependencies are installed: `pip install -r requirements-mvp.txt`

2. **Frontend not connecting to backend**
   - Verify the backend is running on port 8000
   - Check the VITE_API_URL environment variable in the frontend

3. **Tests failing**
   - Run `python tests/run_mvp_tests.py --verbose` for detailed error messages
   - Ensure you have the correct Python version (3.13.2+)

### Getting Help

If you encounter issues not covered in this guide:

1. Check the logs in the `logs` directory
2. Run the diagnostic tool: `python mvp_setup_simple.py --test-only`
3. Review the test files in `tests/mvp/` for examples of correct usage

## Next Steps

After getting familiar with the MVP:

1. Connect to a live MT5 account by setting `offline_mode=False` in the MT5Bridge
2. Implement your own trading strategies
3. Explore the advanced features in the full platform

## API Reference

For a complete API reference with examples, see the [API Reference Documentation](docs/API_REFERENCE.md).

### Core Endpoints

- `GET /health` - Check server status
- `GET /api/strategies` - List all strategies
- `POST /api/strategies` - Create a new strategy
- `GET /api/backtests` - List all backtests
- `POST /api/backtests` - Create a new backtest
- `POST /api/mvp/trade` - Execute a trade
- `GET /api/mvp/trades` - Get trade history
- `POST /api/historical-data/upload` - Upload historical data
- `GET /api/historical-data` - List available historical data
- `GET /api/historical-data/{symbol}/{timeframe}` - Get historical data for a symbol
- `DELETE /api/historical-data/{symbol}/{timeframe}` - Delete historical data

### API Authentication

The API uses HTTP Basic Authentication. You need to include your username and password with each request.

**Default Credentials for MVP:**
- Username: `admin`
- Password: `trading123`

### API Examples

#### Place a Buy Order

```bash
curl -X POST http://localhost:8000/api/mvp/trade \
  -H "Content-Type: application/json" \
  -u admin:trading123 \
  -d '{"symbol": "EURUSD", "order_type": "BUY", "lot": 0.1}'
```

#### Get All Trades

```bash
curl -X GET http://localhost:8000/api/mvp/trades -u admin:trading123
```

#### Upload Historical Data

```bash
curl -X POST "http://localhost:8000/api/historical-data/upload?symbol=EURUSD&timeframe=H1" \
  -H "Content-Type: multipart/form-data" \
  -u admin:trading123 \
  -F "file=@/path/to/eurusd_h1_data.csv"
```

#### Get Available Historical Data

```bash
curl -X GET http://localhost:8000/api/historical-data -u admin:trading123
```

#### Get Historical Data for a Symbol

```bash
curl -X GET "http://localhost:8000/api/historical-data/EURUSD/H1?limit=100" -u admin:trading123
```

#### Delete Historical Data

```bash
curl -X DELETE "http://localhost:8000/api/historical-data/EURUSD/H1" -u admin:trading123
```

## Feedback and Support

We welcome your feedback on the MVP. Please report any issues or suggestions through the GitHub repository's issue tracker.