#!/usr/bin/env python3
"""
Comprehensive TDD Tests for Secure MT5 Bridge
Test-driven development implementation with security focus and zero-hallucination validation.
"""

import pytest
import hashlib
import hmac
import json
import time
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from unittest.mock import Mock, patch, MagicMock
import threading
import sys
import os

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src', 'trading'))

from secure_mt5_bridge import (
    SecureMT5Bridge, TradeOrder, TradeReceipt, MarketData,
    OrderType, OrderStatus, SecurityLevel,
    InvalidOrderError, MarketConditionError, DataIntegrityError, CryptographicError,
    DataIntegrityValidator, SecureMarketDataProvider, ZeroHallucinationValidator,
    create_secure_bridge
)


class TestTradeOrder:
    """Test TradeOrder data class and validation"""
    
    def test_trade_order_creation(self):
        """Test basic trade order creation"""
        order = TradeOrder(
            symbol="EURUSD",
            order_type=OrderType.BUY,
            volume=Decimal('0.1')
        )
        
        assert order.symbol == "EURUSD"
        assert order.order_type == OrderType.BUY
        assert order.volume == Decimal('0.1')
        assert order.order_id.startswith('ORD_')
        assert isinstance(order.timestamp, datetime)
    
    def test_trade_order_decimal_conversion(self):
        """Test automatic conversion to Decimal types"""
        order = TradeOrder(
            symbol="GBPUSD",
            order_type=OrderType.SELL,
            volume=0.5,  # float input
            price=1.2650,  # float input
            stop_loss=1.2600,  # float input
            take_profit=1.2700  # float input
        )
        
        assert isinstance(order.volume, Decimal)
        assert isinstance(order.price, Decimal)
        assert isinstance(order.stop_loss, Decimal)
        assert isinstance(order.take_profit, Decimal)
        assert order.volume == Decimal('0.5')
    
    def test_trade_order_hash_generation(self):
        """Test order hash generation for integrity"""
        order = TradeOrder(
            symbol="USDJPY",
            order_type=OrderType.BUY,
            volume=Decimal('1.0')
        )
        
        hash1 = order.get_hash()
        hash2 = order.get_hash()
        
        # Same order should produce same hash
        assert hash1 == hash2
        assert len(hash1) == 64  # SHA-256 hex length
        
        # Different order should produce different hash
        order2 = TradeOrder(
            symbol="USDJPY",
            order_type=OrderType.SELL,  # Different type
            volume=Decimal('1.0')
        )
        
        assert order.get_hash() != order2.get_hash()
    
    def test_trade_order_to_dict(self):
        """Test order serialization to dictionary"""
        timestamp = datetime.now(timezone.utc)
        order = TradeOrder(
            symbol="AUDUSD",
            order_type=OrderType.BUY_LIMIT,
            volume=Decimal('0.2'),
            price=Decimal('0.6750'),
            timestamp=timestamp
        )
        
        order_dict = order.to_dict()
        
        assert order_dict['symbol'] == "AUDUSD"
        assert order_dict['order_type'] == "buy_limit"
        assert order_dict['volume'] == "0.2"
        assert order_dict['price'] == "0.6750"
        assert order_dict['timestamp'] == timestamp.isoformat()


class TestTradeReceipt:
    """Test TradeReceipt security and integrity"""
    
    def test_trade_receipt_creation(self):
        """Test trade receipt creation with security hash"""
        receipt = TradeReceipt(
            order_id="ORD_123456",
            status=OrderStatus.EXECUTED,
            execution_price=Decimal('1.0850')
        )
        
        assert receipt.order_id == "ORD_123456"
        assert receipt.status == OrderStatus.EXECUTED
        assert receipt.execution_price == Decimal('1.0850')
        assert receipt.security_hash is not None
        assert len(receipt.security_hash) == 64  # SHA-256 hex length
        assert isinstance(receipt.execution_time, datetime)
    
    def test_trade_receipt_integrity_verification(self):
        """Test receipt integrity verification"""
        receipt = TradeReceipt(
            order_id="ORD_789012",
            status=OrderStatus.EXECUTED,
            execution_price=Decimal('1.2650')
        )
        
        # Should verify successfully
        assert receipt.verify_integrity() is True
        
        # Tamper with receipt
        receipt.execution_price = Decimal('1.3000')
        
        # Should fail verification after tampering
        assert receipt.verify_integrity() is False
    
    def test_trade_receipt_audit_trail(self):
        """Test audit trail functionality"""
        audit_trail = [
            {'action': 'order_received', 'timestamp': datetime.now(timezone.utc).isoformat()},
            {'action': 'validation_passed', 'timestamp': datetime.now(timezone.utc).isoformat()}
        ]
        
        receipt = TradeReceipt(
            order_id="ORD_345678",
            status=OrderStatus.EXECUTED,
            audit_trail=audit_trail
        )
        
        assert len(receipt.audit_trail) == 2
        assert receipt.audit_trail[0]['action'] == 'order_received'
        assert receipt.audit_trail[1]['action'] == 'validation_passed'


class TestMarketData:
    """Test MarketData integrity and verification"""
    
    def test_market_data_creation(self):
        """Test market data creation with spread calculation"""
        timestamp = datetime.now(timezone.utc)
        market_data = MarketData(
            symbol="EURUSD",
            bid=Decimal('1.0845'),
            ask=Decimal('1.0847'),
            timestamp=timestamp
        )
        
        assert market_data.symbol == "EURUSD"
        assert market_data.bid == Decimal('1.0845')
        assert market_data.ask == Decimal('1.0847')
        assert market_data.spread == Decimal('0.0002')
        assert market_data.timestamp == timestamp
        assert market_data.data_hash is not None
    
    def test_market_data_integrity_verification(self):
        """Test market data integrity verification"""
        market_data = MarketData(
            symbol="GBPUSD",
            bid=Decimal('1.2648'),
            ask=Decimal('1.2652'),
            timestamp=datetime.now(timezone.utc)
        )
        
        # Should verify successfully
        assert market_data.verify_integrity() is True
        
        # Tamper with data
        market_data.bid = Decimal('1.2600')
        
        # Should fail verification after tampering
        assert market_data.verify_integrity() is False


class TestDataIntegrityValidator:
    """Test cryptographic data integrity validation"""
    
    @pytest.fixture
    def validator(self):
        """Create data integrity validator for testing"""
        return DataIntegrityValidator(secret_key="test_key_2024")
    
    def test_store_and_verify_price_data(self, validator):
        """Test storing and verifying price data"""
        symbol = "EURUSD"
        price_data = {
            'bid': '1.0845',
            'ask': '1.0847',
            'spread': '0.0002'
        }
        
        # Store price data
        data_hash = validator.store_price_data(symbol, price_data)
        
        assert data_hash is not None
        assert len(data_hash) == 64  # HMAC-SHA256 hex length
        assert symbol in validator.price_history
        assert len(validator.price_history[symbol]) == 1
    
    def test_verify_price_data_within_tolerance(self, validator):
        """Test price data verification within time tolerance"""
        symbol = "GBPUSD"
        price_data = {'bid': '1.2648', 'ask': '1.2652'}
        
        # Store price data
        validator.store_price_data(symbol, price_data)
        
        # Verify within tolerance
        timestamp = datetime.now(timezone.utc)
        assert validator.verify_price_data(symbol, timestamp, tolerance_seconds=60) is True
        
        # Verify outside tolerance
        old_timestamp = timestamp - timedelta(minutes=5)
        assert validator.verify_price_data(symbol, old_timestamp, tolerance_seconds=60) is False
    
    def test_check_hash_recent_data(self, validator):
        """Test hash checking for recent data"""
        # Recent timestamp should pass
        recent_timestamp = datetime.now(timezone.utc)
        assert validator.check_hash(recent_timestamp) is True
        
        # Old timestamp should fail
        old_timestamp = datetime.now(timezone.utc) - timedelta(minutes=10)
        assert validator.check_hash(old_timestamp) is False
    
    def test_integrity_report_generation(self, validator):
        """Test integrity report generation"""
        # Store some test data
        validator.store_price_data("EURUSD", {'bid': '1.0845', 'ask': '1.0847'})
        validator.store_price_data("GBPUSD", {'bid': '1.2648', 'ask': '1.2652'})
        
        report = validator.get_integrity_report()
        
        assert report['total_price_entries'] == 2
        assert report['symbols_tracked'] == 2
        assert 'report_timestamp' in report
        assert 'integrity_checks_performed' in report
    
    def test_thread_safety(self, validator):
        """Test thread safety of data integrity validator"""
        def store_data(symbol, index):
            price_data = {'bid': f'1.{index:04d}', 'ask': f'1.{index+1:04d}'}
            validator.store_price_data(f"{symbol}_{index}", price_data)
        
        # Create multiple threads
        threads = []
        for i in range(10):
            thread = threading.Thread(target=store_data, args=("TEST", i))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Verify all data was stored
        assert len(validator.price_history) == 10


class TestSecureMarketDataProvider:
    """Test secure market data provider"""
    
    @pytest.fixture
    def provider(self):
        """Create market data provider for testing"""
        return SecureMarketDataProvider(max_spread_pct=0.1)
    
    def test_get_market_data(self, provider):
        """Test getting market data for symbol"""
        market_data = provider.get_market_data("EURUSD")
        
        assert market_data is not None
        assert market_data.symbol == "EURUSD"
        assert market_data.bid > 0
        assert market_data.ask > market_data.bid
        assert market_data.spread > 0
        assert market_data.verify_integrity() is True
    
    def test_verify_spread_within_limits(self, provider):
        """Test spread verification within limits"""
        # Normal spread should pass
        assert provider.verify_spread("EURUSD") is True
        
        # Test with provider that has very tight spread limits
        tight_provider = SecureMarketDataProvider(max_spread_pct=0.001)  # 0.001%
        
        # Should fail with tight limits
        assert tight_provider.verify_spread("EURUSD") is False
        assert len(tight_provider.get_spread_violations()) > 0
    
    def test_spread_violation_logging(self, provider):
        """Test spread violation logging"""
        # Create provider with very tight limits to force violations
        tight_provider = SecureMarketDataProvider(max_spread_pct=0.001)
        
        # This should create a violation
        tight_provider.verify_spread("EURUSD")
        
        violations = tight_provider.get_spread_violations()
        assert len(violations) > 0
        
        violation = violations[0]
        assert violation['symbol'] == "EURUSD"
        assert 'spread_pct' in violation
        assert 'timestamp' in violation
    
    def test_market_data_caching(self, provider):
        """Test market data caching"""
        # Get market data twice
        data1 = provider.get_market_data("EURUSD")
        data2 = provider.get_market_data("EURUSD")
        
        # Should be cached (same timestamp within reasonable time)
        assert "EURUSD" in provider.market_data_cache
        
        # Data should be consistent
        assert data1.symbol == data2.symbol


class TestZeroHallucinationValidator:
    """Test zero-hallucination order validation"""
    
    @pytest.fixture
    def validator(self):
        """Create zero-hallucination validator for testing"""
        return ZeroHallucinationValidator()
    
    def test_validate_valid_order(self, validator):
        """Test validation of valid order"""
        order = TradeOrder(
            symbol="EURUSD",
            order_type=OrderType.BUY,
            volume=Decimal('0.1'),
            price=Decimal('1.0850')
        )
        
        is_valid, errors = validator.validate_order(order)
        
        assert is_valid is True
        assert len(errors) == 0
    
    @pytest.mark.parametrize("volume,expected_valid", [
        (Decimal('0.1'), True),
        (Decimal('1.0'), True),
        (Decimal('0'), False),
        (Decimal('-0.1'), False),
    ])
    def test_validate_volume_positive(self, validator, volume, expected_valid):
        """Test volume validation"""
        order = TradeOrder(
            symbol="EURUSD",
            order_type=OrderType.BUY,
            volume=volume
        )
        
        is_valid, errors = validator.validate_order(order)
        
        assert is_valid == expected_valid
        if not expected_valid:
            assert any("Volume must be positive" in error for error in errors)
    
    @pytest.mark.parametrize("symbol,expected_valid", [
        ("EURUSD", True),
        ("GBPUSD", True),
        ("USDJPY", True),
        ("INVALID", False),
        ("", False),
        ("EUR", False),
    ])
    def test_validate_symbol_format(self, validator, symbol, expected_valid):
        """Test symbol format validation"""
        order = TradeOrder(
            symbol=symbol,
            order_type=OrderType.BUY,
            volume=Decimal('0.1')
        )
        
        is_valid, errors = validator.validate_order(order)
        
        assert is_valid == expected_valid
        if not expected_valid:
            assert any("symbol" in error.lower() for error in errors)
    
    def test_validate_price_logic_for_limit_orders(self, validator):
        """Test price logic validation for limit orders"""
        # Limit order without price should fail
        order = TradeOrder(
            symbol="EURUSD",
            order_type=OrderType.BUY_LIMIT,
            volume=Decimal('0.1')
            # No price specified
        )
        
        is_valid, errors = validator.validate_order(order)
        
        assert is_valid is False
        assert any("Price required" in error for error in errors)
    
    def test_validate_stop_loss_logic(self, validator):
        """Test stop loss validation logic"""
        # BUY order with stop loss above entry price should fail
        order = TradeOrder(
            symbol="EURUSD",
            order_type=OrderType.BUY,
            volume=Decimal('0.1'),
            price=Decimal('1.0850'),
            stop_loss=Decimal('1.0900')  # Above entry price
        )
        
        is_valid, errors = validator.validate_order(order)
        
        assert is_valid is False
        assert any("Stop loss must be below entry price" in error for error in errors)
    
    def test_validate_take_profit_logic(self, validator):
        """Test take profit validation logic"""
        # SELL order with take profit above entry price should fail
        order = TradeOrder(
            symbol="EURUSD",
            order_type=OrderType.SELL,
            volume=Decimal('0.1'),
            price=Decimal('1.0850'),
            take_profit=Decimal('1.0900')  # Above entry price for SELL
        )
        
        is_valid, errors = validator.validate_order(order)
        
        assert is_valid is False
        assert any("Take profit must be below entry price" in error for error in errors)
    
    def test_validate_timestamp_recent(self, validator):
        """Test timestamp validation"""
        # Old timestamp should fail
        old_timestamp = datetime.now(timezone.utc) - timedelta(minutes=5)
        order = TradeOrder(
            symbol="EURUSD",
            order_type=OrderType.BUY,
            volume=Decimal('0.1'),
            timestamp=old_timestamp
        )
        
        is_valid, errors = validator.validate_order(order)
        
        assert is_valid is False
        assert any("timestamp too old" in error.lower() for error in errors)
    
    def test_validate_order_id_format(self, validator):
        """Test order ID format validation"""
        order = TradeOrder(
            symbol="EURUSD",
            order_type=OrderType.BUY,
            volume=Decimal('0.1')
        )
        
        # Tamper with order ID
        order.order_id = "INVALID_ID"
        
        is_valid, errors = validator.validate_order(order)
        
        assert is_valid is False
        assert any("Invalid order ID format" in error for error in errors)
    
    def test_validation_statistics(self, validator):
        """Test validation statistics tracking"""
        # Validate some orders
        valid_order = TradeOrder(
            symbol="EURUSD",
            order_type=OrderType.BUY,
            volume=Decimal('0.1')
        )
        
        invalid_order = TradeOrder(
            symbol="INVALID",
            order_type=OrderType.BUY,
            volume=Decimal('0.1')
        )
        
        validator.validate_order(valid_order)
        validator.validate_order(invalid_order)
        
        stats = validator.get_validation_stats()
        
        assert stats['total_validations'] == 2
        assert stats['successful_validations'] == 1
        assert stats['failure_rate'] == 0.5
        assert 'last_validation' in stats


class TestSecureMT5Bridge:
    """Test secure MT5 bridge main functionality"""
    
    @pytest.fixture
    def bridge(self):
        """Create secure bridge for testing"""
        return SecureMT5Bridge(security_level=SecurityLevel.TESTING)
    
    @pytest.fixture
    def valid_order(self):
        """Create valid test order"""
        return TradeOrder(
            symbol="EURUSD",
            order_type=OrderType.BUY,
            volume=Decimal('0.1'),
            price=Decimal('1.0850')
        )
    
    def test_bridge_initialization(self, bridge):
        """Test bridge initialization with security level"""
        assert bridge.security_level == SecurityLevel.TESTING
        assert bridge.data_integrity is not None
        assert bridge.market_data is not None
        assert bridge.order_validator is not None
        assert len(bridge.executed_orders) == 0
        assert len(bridge.security_violations) == 0
    
    def test_security_config_by_level(self):
        """Test security configuration based on security level"""
        dev_bridge = SecureMT5Bridge(SecurityLevel.DEVELOPMENT)
        prod_bridge = SecureMT5Bridge(SecurityLevel.PRODUCTION)
        
        # Development should be less strict
        assert dev_bridge.security_config['require_data_integrity'] is False
        assert dev_bridge.security_config['max_order_age_seconds'] == 300
        
        # Production should be more strict
        assert prod_bridge.security_config['require_data_integrity'] is True
        assert prod_bridge.security_config['max_order_age_seconds'] == 30
    
    def test_execute_valid_order(self, bridge, valid_order):
        """Test executing valid order"""
        receipt = bridge.execute_order(valid_order)
        
        assert receipt.order_id == valid_order.order_id
        assert receipt.status == OrderStatus.EXECUTED
        assert receipt.execution_price is not None
        assert receipt.security_hash is not None
        assert receipt.verify_integrity() is True
        
        # Check order was stored
        assert len(bridge.executed_orders) == 1
        assert bridge.executed_orders[0].order_id == valid_order.order_id
    
    def test_execute_invalid_order_volume(self, bridge):
        """Test executing order with invalid volume"""
        invalid_order = TradeOrder(
            symbol="EURUSD",
            order_type=OrderType.BUY,
            volume=Decimal('0')  # Invalid volume
        )
        
        with pytest.raises(InvalidOrderError) as exc_info:
            bridge.execute_order(invalid_order)
        
        assert "Volume must be positive" in str(exc_info.value)
        assert len(bridge.executed_orders) == 0
    
    def test_execute_invalid_order_symbol(self, bridge):
        """Test executing order with invalid symbol"""
        invalid_order = TradeOrder(
            symbol="INVALID",  # Invalid symbol
            order_type=OrderType.BUY,
            volume=Decimal('0.1')
        )
        
        with pytest.raises(InvalidOrderError) as exc_info:
            bridge.execute_order(invalid_order)
        
        assert "not in approved list" in str(exc_info.value)
    
    def test_execute_order_with_spread_violation(self, bridge):
        """Test executing order when spread exceeds limits"""
        # Mock market data provider to return excessive spread
        bridge.market_data.max_spread_pct = 0.001  # Very tight limit
        
        order = TradeOrder(
            symbol="EURUSD",
            order_type=OrderType.BUY,
            volume=Decimal('0.1')
        )
        
        with pytest.raises(MarketConditionError) as exc_info:
            bridge.execute_order(order)
        
        assert "Spread exceeds safety limits" in str(exc_info.value)
    
    def test_execute_order_with_old_timestamp(self, bridge):
        """Test executing order with old timestamp"""
        old_order = TradeOrder(
            symbol="EURUSD",
            order_type=OrderType.BUY,
            volume=Decimal('0.1'),
            timestamp=datetime.now(timezone.utc) - timedelta(minutes=5)
        )
        
        with pytest.raises(InvalidOrderError) as exc_info:
            bridge.execute_order(old_order)
        
        assert "Order too old" in str(exc_info.value)
    
    def test_data_integrity_verification(self, bridge, valid_order):
        """Test data integrity verification during execution"""
        # Mock data integrity check to fail
        bridge.data_integrity.check_hash = Mock(return_value=False)
        
        with pytest.raises(DataIntegrityError) as exc_info:
            bridge.execute_order(valid_order)
        
        assert "Price data verification failed" in str(exc_info.value)
    
    def test_security_context_manager(self, bridge):
        """Test security context manager"""
        operation_name = "test_operation"
        
        # Should complete without error
        with bridge.security_context(operation_name):
            pass  # Normal operation
        
        # Should handle security errors
        with pytest.raises(InvalidOrderError):
            with bridge.security_context(operation_name):
                raise InvalidOrderError("Test security error")
        
        # Check security violation was logged
        violations = bridge.get_security_violations()
        assert len(violations) > 0
        assert violations[0]['operation'] == operation_name
    
    def test_security_report_generation(self, bridge, valid_order):
        """Test security report generation"""
        # Execute an order to populate data
        bridge.execute_order(valid_order)
        
        report = bridge.get_security_report()
        
        assert report['security_level'] == SecurityLevel.TESTING.value
        assert 'security_config' in report
        assert report['executed_orders_count'] == 1
        assert report['security_violations_count'] == 0
        assert 'data_integrity_report' in report
        assert 'validation_stats' in report
        assert 'report_timestamp' in report
    
    def test_thread_safety(self, bridge):
        """Test thread safety of bridge operations"""
        def execute_order(index):
            order = TradeOrder(
                symbol="EURUSD",
                order_type=OrderType.BUY,
                volume=Decimal('0.1'),
                order_id=f"ORD_THREAD_{index}"
            )
            try:
                bridge.execute_order(order)
            except Exception:
                pass  # Some may fail due to timing
        
        # Create multiple threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=execute_order, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Should have executed some orders without crashes
        assert len(bridge.executed_orders) >= 0  # Some may have failed validation


class TestSecurityLevels:
    """Test different security levels"""
    
    @pytest.mark.parametrize("security_level", [
        SecurityLevel.DEVELOPMENT,
        SecurityLevel.TESTING,
        SecurityLevel.STAGING,
        SecurityLevel.PRODUCTION
    ])
    def test_bridge_creation_with_different_levels(self, security_level):
        """Test bridge creation with different security levels"""
        bridge = SecureMT5Bridge(security_level)
        
        assert bridge.security_level == security_level
        assert bridge.security_config is not None
        assert 'require_data_integrity' in bridge.security_config
        assert 'max_order_age_seconds' in bridge.security_config
    
    def test_development_vs_production_security(self):
        """Test security differences between development and production"""
        dev_bridge = SecureMT5Bridge(SecurityLevel.DEVELOPMENT)
        prod_bridge = SecureMT5Bridge(SecurityLevel.PRODUCTION)
        
        # Production should be more restrictive
        assert (prod_bridge.security_config['max_order_age_seconds'] < 
                dev_bridge.security_config['max_order_age_seconds'])
        
        # Production should require more security checks
        assert prod_bridge.security_config['require_data_integrity'] is True
        assert prod_bridge.security_config['require_spread_verification'] is True


class TestFactoryFunction:
    """Test factory function for bridge creation"""
    
    def test_create_secure_bridge_default(self):
        """Test creating bridge with default security level"""
        bridge = create_secure_bridge()
        
        assert isinstance(bridge, SecureMT5Bridge)
        assert bridge.security_level == SecurityLevel.DEVELOPMENT
    
    def test_create_secure_bridge_with_level(self):
        """Test creating bridge with specific security level"""
        bridge = create_secure_bridge(SecurityLevel.PRODUCTION)
        
        assert isinstance(bridge, SecureMT5Bridge)
        assert bridge.security_level == SecurityLevel.PRODUCTION


class TestIntegrationScenarios:
    """Integration tests for complete workflows"""
    
    @pytest.fixture
    def production_bridge(self):
        """Create production-level bridge for integration testing"""
        return SecureMT5Bridge(SecurityLevel.PRODUCTION)
    
    def test_complete_order_execution_workflow(self, production_bridge):
        """Test complete order execution workflow"""
        # Create order
        order = TradeOrder(
            symbol="EURUSD",
            order_type=OrderType.BUY,
            volume=Decimal('0.1'),
            price=Decimal('1.0850'),
            stop_loss=Decimal('1.0800'),
            take_profit=Decimal('1.0900')
        )
        
        # Execute order
        receipt = production_bridge.execute_order(order)
        
        # Verify execution
        assert receipt.status == OrderStatus.EXECUTED
        assert receipt.verify_integrity() is True
        assert len(receipt.audit_trail) > 0
        
        # Verify bridge state
        executed_orders = production_bridge.get_executed_orders()
        assert len(executed_orders) == 1
        assert executed_orders[0].order_id == order.order_id
        
        # Generate security report
        report = production_bridge.get_security_report()
        assert report['executed_orders_count'] == 1
        assert report['security_violations_count'] == 0
    
    def test_multiple_orders_execution(self, production_bridge):
        """Test executing multiple orders"""
        orders = [
            TradeOrder(symbol="EURUSD", order_type=OrderType.BUY, volume=Decimal('0.1')),
            TradeOrder(symbol="GBPUSD", order_type=OrderType.SELL, volume=Decimal('0.2')),
            TradeOrder(symbol="USDJPY", order_type=OrderType.BUY, volume=Decimal('0.15'))
        ]
        
        receipts = []
        for order in orders:
            receipt = production_bridge.execute_order(order)
            receipts.append(receipt)
        
        # Verify all orders executed
        assert len(receipts) == 3
        assert all(receipt.status == OrderStatus.EXECUTED for receipt in receipts)
        assert all(receipt.verify_integrity() for receipt in receipts)
        
        # Verify bridge state
        executed_orders = production_bridge.get_executed_orders()
        assert len(executed_orders) == 3
    
    def test_error_handling_and_recovery(self, production_bridge):
        """Test error handling and recovery"""
        # Execute valid order first
        valid_order = TradeOrder(
            symbol="EURUSD",
            order_type=OrderType.BUY,
            volume=Decimal('0.1')
        )
        
        receipt1 = production_bridge.execute_order(valid_order)
        assert receipt1.status == OrderStatus.EXECUTED
        
        # Try invalid order
        invalid_order = TradeOrder(
            symbol="INVALID",
            order_type=OrderType.BUY,
            volume=Decimal('0.1')
        )
        
        with pytest.raises(InvalidOrderError):
            production_bridge.execute_order(invalid_order)
        
        # Execute another valid order to verify recovery
        valid_order2 = TradeOrder(
            symbol="GBPUSD",
            order_type=OrderType.SELL,
            volume=Decimal('0.2')
        )
        
        receipt2 = production_bridge.execute_order(valid_order2)
        assert receipt2.status == OrderStatus.EXECUTED
        
        # Verify final state
        executed_orders = production_bridge.get_executed_orders()
        assert len(executed_orders) == 2  # Only valid orders executed


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v", "--tb=short"])