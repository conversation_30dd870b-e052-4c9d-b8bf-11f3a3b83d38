// tests/infrastructure/event-bus.test.ts
import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { EventBus, Event, EventHandler } from '../../src/infrastructure/event-bus';
import { z } from 'zod';

describe('EventBus', () => {
  let eventBus: EventBus;

  beforeEach(() => {
    eventBus = new EventBus();
  });

  describe('publish/subscribe pattern', () => {
    it('should deliver events to subscribed handlers', async () => {
      // Arrange
      const handler = jest.fn();
      const event: Event = {
        type: 'trade.executed',
        payload: { tradeId: '123', amount: 1000 },
        timestamp: new Date(),
        correlationId: 'corr-123',
      };

      eventBus.subscribe('trade.executed', handler);

      // Act
      await eventBus.publish(event);

      // Assert
      expect(handler).toHaveBeenCalledWith(event);
      expect(handler).toHaveBeenCalledTimes(1);
    });

    it('should support multiple handlers for same event', async () => {
      // Arrange
      const handler1 = jest.fn();
      const handler2 = jest.fn();
      const event: Event = {
        type: 'market.update',
        payload: { symbol: 'AAPL', price: 150 },
        timestamp: new Date(),
        correlationId: 'corr-456',
      };

      eventBus.subscribe('market.update', handler1);
      eventBus.subscribe('market.update', handler2);

      // Act
      await eventBus.publish(event);

      // Assert
      expect(handler1).toHaveBeenCalledWith(event);
      expect(handler2).toHaveBeenCalledWith(event);
    });

    it('should handle handler errors gracefully', async () => {
      // Arrange
      const errorHandler = jest.fn().mockRejectedValue(new Error('Handler error'));
      const successHandler = jest.fn();
      const event: Event = {
        type: 'trade.failed',
        payload: { reason: 'Insufficient funds' },
        timestamp: new Date(),
        correlationId: 'corr-789',
      };

      eventBus.subscribe('trade.failed', errorHandler);
      eventBus.subscribe('trade.failed', successHandler);

      // Act & Assert - should not throw
      await expect(eventBus.publish(event)).resolves.not.toThrow();
      expect(successHandler).toHaveBeenCalled();
    });
  });

  describe('event replay', () => {
    it('should replay events from event store', async () => {
      // Arrange
      const handler = jest.fn();
      const events = [
        { type: 'trade.executed', payload: { id: 1 }, timestamp: new Date() },
        { type: 'trade.executed', payload: { id: 2 }, timestamp: new Date() },
        { type: 'trade.executed', payload: { id: 3 }, timestamp: new Date() },
      ];

      eventBus.subscribe('trade.executed', handler);

      // Act
      await eventBus.replay('trade.executed', events);

      // Assert
      expect(handler).toHaveBeenCalledTimes(3);
    });
  });
});

// src/infrastructure/event-bus.ts
import { EventEmitter } from 'events';
import { z } from 'zod';
import { Logger } from '../utils/logger';

// Event schemas for type safety
export const EventSchema = z.object({
  type: z.string(),
  payload: z.any(),
  timestamp: z.date(),
  correlationId: z.string(),
  causationId: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

export type Event = z.infer<typeof EventSchema>;
export type EventHandler = (event: Event) => Promise<void> | void;

export interface IEventStore {
  save(event: Event): Promise<void>;
  getEvents(type: string, from: Date, to: Date): Promise<Event[]>;
}

export class EventBus {
  private emitter: EventEmitter;
  private eventStore?: IEventStore;
  private logger: Logger;
  private handlers: Map<string, Set<EventHandler>>;

  constructor(eventStore?: IEventStore) {
    this.emitter = new EventEmitter();
    this.eventStore = eventStore;
    this.logger = new Logger('EventBus');
    this.handlers = new Map();
  }

  async publish(event: Event): Promise<void> {
    // Validate event schema
    const validatedEvent = EventSchema.parse(event);

    // Persist event if store is configured
    if (this.eventStore) {
      await this.eventStore.save(validatedEvent);
    }

    // Emit event to all handlers
    const handlers = this.handlers.get(event.type) || new Set();
    
    await Promise.allSettled(
      Array.from(handlers).map(handler => 
        this.executeHandler(handler, validatedEvent)
      )
    );
  }

  subscribe(eventType: string, handler: EventHandler): void {
    if (!this.handlers.has(eventType)) {
      this.handlers.set(eventType, new Set());
    }
    this.handlers.get(eventType)!.add(handler);
  }

  unsubscribe(eventType: string, handler: EventHandler): void {
    this.handlers.get(eventType)?.delete(handler);
  }

  async replay(eventType: string, events: Event[]): Promise<void> {
    const handlers = this.handlers.get(eventType) || new Set();
    
    for (const event of events) {
      await Promise.allSettled(
        Array.from(handlers).map(handler => 
          this.executeHandler(handler, event)
        )
      );
    }
  }

  private async executeHandler(handler: EventHandler, event: Event): Promise<void> {
    try {
      await handler(event);
    } catch (error) {
      this.logger.error(`Handler error for event ${event.type}:`, error);
      // Could publish error event here for monitoring
    }
  }
}

// tests/infrastructure/websocket-manager.test.ts
import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { WebSocketManager } from '../../src/infrastructure/websocket-manager';
import { EventBus } from '../../src/infrastructure/event-bus';
import WS from 'jest-websocket-mock';

describe('WebSocketManager', () => {
  let wsManager: WebSocketManager;
  let eventBus: jest.Mocked<EventBus>;
  let mockServer: WS;

  beforeEach(async () => {
    mockServer = new WS('ws://localhost:8080');
    eventBus = {
      publish: jest.fn(),
      subscribe: jest.fn(),
    } as any;
    
    wsManager = new WebSocketManager(eventBus);
  });

  afterEach(() => {
    WS.clean();
  });

  describe('real-time market data streaming', () => {
    it('should connect and subscribe to market data', async () => {
      // Arrange
      const symbols = ['AAPL', 'GOOGL'];

      // Act
      await wsManager.connect('ws://localhost:8080');
      await wsManager.subscribeToMarketData(symbols);

      // Assert
      await mockServer.connected;
      await expect(mockServer).toReceiveMessage(
        JSON.stringify({
          action: 'subscribe',
          symbols: symbols,
          type: 'market_data',
        })
      );
    });

    it('should publish market updates to event bus', async () => {
      // Arrange
      await wsManager.connect('ws://localhost:8080');
      const marketUpdate = {
        type: 'market_update',
        symbol: 'AAPL',
        price: 150.25,
        volume: 1000000,
        timestamp: new Date().toISOString(),
      };

      // Act
      mockServer.send(JSON.stringify(marketUpdate));

      // Assert
      await new Promise(resolve => setTimeout(resolve, 100)); // Wait for async processing
      expect(eventBus.publish).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'market.update',
          payload: expect.objectContaining({
            symbol: 'AAPL',
            price: 150.25,
          }),
        })
      );
    });

    it('should handle reconnection on disconnect', async () => {
      // Arrange
      await wsManager.connect('ws://localhost:8080');
      const reconnectSpy = jest.spyOn(wsManager, 'reconnect');

      // Act
      mockServer.close();

      // Assert
      await new Promise(resolve => setTimeout(resolve, 1100)); // Wait for reconnect delay
      expect(reconnectSpy).toHaveBeenCalled();
    });
  });
});

// src/infrastructure/websocket-manager.ts
import WebSocket from 'ws';
import { EventBus } from './event-bus';
import { Logger } from '../utils/logger';

export class WebSocketManager {
  private ws?: WebSocket;
  private eventBus: EventBus;
  private logger: Logger;
  private url?: string;
  private reconnectInterval: number = 1000;
  private maxReconnectAttempts: number = 5;
  private reconnectAttempts: number = 0;
  private subscribedSymbols: Set<string> = new Set();

  constructor(eventBus: EventBus) {
    this.eventBus = eventBus;
    this.logger = new Logger('WebSocketManager');
  }

  async connect(url: string): Promise<void> {
    this.url = url;
    
    return new Promise((resolve, reject) => {
      this.ws = new WebSocket(url);

      this.ws.on('open', () => {
        this.logger.info('WebSocket connected');
        this.reconnectAttempts = 0;
        resolve();
      });

      this.ws.on('message', (data: string) => {
        this.handleMessage(data);
      });

      this.ws.on('close', () => {
        this.logger.warn('WebSocket disconnected');
        this.reconnect();
      });

      this.ws.on('error', (error) => {
        this.logger.error('WebSocket error:', error);
        reject(error);
      });
    });
  }

  async subscribeToMarketData(symbols: string[]): Promise<void> {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      throw new Error('WebSocket not connected');
    }

    symbols.forEach(symbol => this.subscribedSymbols.add(symbol));

    const message = {
      action: 'subscribe',
      symbols: symbols,
      type: 'market_data',
    };

    this.ws.send(JSON.stringify(message));
  }

  private handleMessage(data: string): void {
    try {
      const message = JSON.parse(data);

      switch (message.type) {
        case 'market_update':
          this.eventBus.publish({
            type: 'market.update',
            payload: {
              symbol: message.symbol,
              price: message.price,
              volume: message.volume,
              bid: message.bid,
              ask: message.ask,
            },
            timestamp: new Date(message.timestamp),
            correlationId: `market-${Date.now()}`,
          });
          break;

        case 'trade_execution':
          this.eventBus.publish({
            type: 'trade.real_time_update',
            payload: message,
            timestamp: new Date(),
            correlationId: message.tradeId,
          });
          break;
      }
    } catch (error) {
      this.logger.error('Error handling message:', error);
    }
  }

  async reconnect(): Promise<void> {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.logger.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    this.logger.info(`Reconnecting... Attempt ${this.reconnectAttempts}`);

    setTimeout(async () => {
      try {
        await this.connect(this.url!);
        
        // Resubscribe to symbols
        if (this.subscribedSymbols.size > 0) {
          await this.subscribeToMarketData(Array.from(this.subscribedSymbols));
        }
      } catch (error) {
        this.logger.error('Reconnection failed:', error);
      }
    }, this.reconnectInterval * this.reconnectAttempts);
  }

  disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = undefined;
    }
  }
}

// tests/infrastructure/saga-orchestrator.test.ts
import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { SagaOrchestrator, Saga, SagaStep } from '../../src/infrastructure/saga-orchestrator';

describe('SagaOrchestrator', () => {
  let orchestrator: SagaOrchestrator;

  beforeEach(() => {
    orchestrator = new SagaOrchestrator();
  });

  describe('saga execution', () => {
    it('should execute saga steps in order', async () => {
      // Arrange
      const executionOrder: string[] = [];
      
      const saga: Saga = {
        name: 'order-placement',
        steps: [
          {
            name: 'validate-order',
            execute: jest.fn(async () => {
              executionOrder.push('validate');
              return { valid: true };
            }),
            compensate: jest.fn(),
          },
          {
            name: 'reserve-funds',
            execute: jest.fn(async () => {
              executionOrder.push('reserve');
              return { reserved: true };
            }),
            compensate: jest.fn(),
          },
          {
            name: 'place-order',
            execute: jest.fn(async () => {
              executionOrder.push('place');
              return { orderId: '123' };
            }),
            compensate: jest.fn(),
          },
        ],
      };

      // Act
      const result = await orchestrator.execute(saga, { amount: 1000 });

      // Assert
      expect(executionOrder).toEqual(['validate', 'reserve', 'place']);
      expect(result).toEqual({ orderId: '123' });
    });

    it('should compensate on failure', async () => {
      // Arrange
      const compensationOrder: string[] = [];
      
      const saga: Saga = {
        name: 'failing-saga',
        steps: [
          {
            name: 'step1',
            execute: jest.fn(async () => ({ success: true })),
            compensate: jest.fn(async () => {
              compensationOrder.push('compensate1');
            }),
          },
          {
            name: 'step2',
            execute: jest.fn(async () => ({ success: true })),
            compensate: jest.fn(async () => {
              compensationOrder.push('compensate2');
            }),
          },
          {
            name: 'step3',
            execute: jest.fn(async () => {
              throw new Error('Step 3 failed');
            }),
            compensate: jest.fn(),
          },
        ],
      };

      // Act & Assert
      await expect(orchestrator.execute(saga, {})).rejects.toThrow('Step 3 failed');
      expect(compensationOrder).toEqual(['compensate2', 'compensate1']);
    });
  });
});

// src/infrastructure/saga-orchestrator.ts
export interface SagaStep {
  name: string;
  execute: (context: any) => Promise<any>;
  compensate: (context: any) => Promise<void>;
}

export interface Saga {
  name: string;
  steps: SagaStep[];
}

export class SagaOrchestrator {
  private logger: Logger;

  constructor() {
    this.logger = new Logger('SagaOrchestrator');
  }

  async execute(saga: Saga, initialContext: any): Promise<any> {
    const executedSteps: SagaStep[] = [];
    let context = { ...initialContext };

    try {
      for (const step of saga.steps) {
        this.logger.info(`Executing step: ${step.name}`);
        
        const result = await step.execute(context);
        context = { ...context, ...result };
        executedSteps.push(step);
      }

      return context;
    } catch (error) {
      this.logger.error(`Saga ${saga.name} failed at step ${executedSteps.length + 1}:`, error);
      
      // Compensate in reverse order
      for (const step of executedSteps.reverse()) {
        try {
          this.logger.info(`Compensating step: ${step.name}`);
          await step.compensate(context);
        } catch (compensateError) {
          this.logger.error(`Compensation failed for step ${step.name}:`, compensateError);
        }
      }

      throw error;
    }
  }
}