import React from 'react';
import { Palette, Sparkles } from 'lucide-react';

interface ThemeSelectorProps {
  currentTheme: string;
  onThemeChange: (theme: string) => void;
}

const ThemeSelector: React.FC<ThemeSelectorProps> = ({ currentTheme, onThemeChange }) => {
  const themes = [
    { id: 'enhanced', name: 'Enhanced Dashboard', description: 'Multi-section layout with AI helpers' },
    { id: 'professional', name: 'Professional Single-Page', description: 'Enterprise single-page design' },
    // Future themes can be added here
    // { id: 'cyberpunk', name: 'Cyberpunk', description: 'Futuristic neon design' },
    // { id: 'dystopian', name: 'Dystopian', description: 'Terminal-style interface' },
    // { id: 'steampunk', name: 'Steampunk', description: 'Victorian mechanical design' },
  ];

  return (
    <div className="flex items-center space-x-3">
      <div className="flex items-center space-x-2">
        <Palette className="w-5 h-5 text-white" />
        <span className="text-sm font-medium text-white">Theme:</span>
      </div>
      <select
        value={currentTheme}
        onChange={(e) => onThemeChange(e.target.value)}
        className="bg-white border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[200px]"
      >
        {themes.map((theme) => (
          <option key={theme.id} value={theme.id}>
            {theme.name}
          </option>
        ))}
      </select>
      {currentTheme === 'professional' && (
        <div className="flex items-center space-x-1 bg-yellow-400 text-yellow-900 px-2 py-1 rounded-full text-xs font-medium">
          <Sparkles className="w-3 h-3" />
          <span>NEW</span>
        </div>
      )}
    </div>
  );
};

export default ThemeSelector;