// test/helpers/test-factory.ts
import { z } from 'zod';
import { faker } from '@faker-js/faker';

/**
 * Test Factory Pattern for consistent test data generation
 * Following TDD principles: Write tests first, then implementation
 */

// Define schemas first (Schema-First Development)
export const TradeSchema = z.object({
  id: z.string().uuid(),
  symbol: z.string().min(1).max(10),
  quantity: z.number().positive(),
  price: z.number().positive(),
  side: z.enum(['buy', 'sell']),
  status: z.enum(['pending', 'executed', 'cancelled', 'failed']),
  timestamp: z.date(),
  userId: z.string().uuid(),
});

export const MarketDataSchema = z.object({
  symbol: z.string(),
  price: z.number().positive(),
  volume: z.number().nonnegative(),
  bid: z.number().positive(),
  ask: z.number().positive(),
  timestamp: z.date(),
});

// Test data factories
export class TestFactory {
  static createTrade(overrides?: Partial<z.infer<typeof TradeSchema>>) {
    const trade = {
      id: faker.string.uuid(),
      symbol: faker.helpers.arrayElement(['AAPL', 'GOOGL', 'MSFT', 'AMZN']),
      quantity: faker.number.int({ min: 1, max: 1000 }),
      price: faker.number.float({ min: 10, max: 500, precision: 0.01 }),
      side: faker.helpers.arrayElement(['buy', 'sell'] as const),
      status: 'pending' as const,
      timestamp: faker.date.recent(),
      userId: faker.string.uuid(),
      ...overrides,
    };
    
    return TradeSchema.parse(trade);
  }

  static createMarketData(overrides?: Partial<z.infer<typeof MarketDataSchema>>) {
    const price = faker.number.float({ min: 10, max: 500, precision: 0.01 });
    const spread = faker.number.float({ min: 0.01, max: 0.5, precision: 0.01 });
    
    const marketData = {
      symbol: faker.helpers.arrayElement(['AAPL', 'GOOGL', 'MSFT', 'AMZN']),
      price,
      volume: faker.number.int({ min: 100000, max: 10000000 }),
      bid: price - spread,
      ask: price + spread,
      timestamp: faker.date.recent(),
      ...overrides,
    };
    
    return MarketDataSchema.parse(marketData);
  }
}

// test/helpers/test-database.ts
import { Pool } from 'pg';
import { migrate } from '../migrations';

/**
 * Test database management for integration tests
 */
export class TestDatabase {
  private pool: Pool;
  private dbName: string;

  constructor() {
    this.dbName = `test_${process.env.JEST_WORKER_ID || '1'}`;
    this.pool = new Pool({
      database: this.dbName,
      host: process.env.TEST_DB_HOST || 'localhost',
      port: parseInt(process.env.TEST_DB_PORT || '5432'),
      user: process.env.TEST_DB_USER || 'test',
      password: process.env.TEST_DB_PASSWORD || 'test',
    });
  }

  async setup() {
    // Create test database
    const adminPool = new Pool({
      database: 'postgres',
      host: process.env.TEST_DB_HOST || 'localhost',
      port: parseInt(process.env.TEST_DB_PORT || '5432'),
      user: process.env.TEST_DB_USER || 'test',
      password: process.env.TEST_DB_PASSWORD || 'test',
    });

    await adminPool.query(`DROP DATABASE IF EXISTS ${this.dbName}`);
    await adminPool.query(`CREATE DATABASE ${this.dbName}`);
    await adminPool.end();

    // Run migrations
    await migrate(this.pool);
  }

  async teardown() {
    await this.pool.end();
  }

  async clean() {
    // Clean all tables for test isolation
    const tables = ['trades', 'users', 'market_data', 'signals'];
    for (const table of tables) {
      await this.pool.query(`TRUNCATE TABLE ${table} CASCADE`);
    }
  }

  getPool() {
    return this.pool;
  }
}

// test/helpers/test-server.ts
import { FastifyInstance } from 'fastify';
import { build } from '../../src/app';

/**
 * Test server helper for API testing
 */
export class TestServer {
  private app: FastifyInstance;

  async start() {
    this.app = await build({
      logger: false, // Disable logging in tests
    });
    
    await this.app.ready();
    return this.app;
  }

  async stop() {
    await this.app.close();
  }

  getApp() {
    return this.app;
  }
}

// test/unit/services/trade-service.test.ts
import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { TradeService } from '../../../src/services/trade-service';
import { TestFactory } from '../../helpers/test-factory';
import { TradeRepository } from '../../../src/repositories/trade-repository';
import { EventBus } from '../../../src/infrastructure/event-bus';
import { MarketDataService } from '../../../src/services/market-data-service';

describe('TradeService', () => {
  let tradeService: TradeService;
  let tradeRepository: jest.Mocked<TradeRepository>;
  let eventBus: jest.Mocked<EventBus>;
  let marketDataService: jest.Mocked<MarketDataService>;

  beforeEach(() => {
    // Create mocks
    tradeRepository = {
      create: jest.fn(),
      findById: jest.fn(),
      update: jest.fn(),
      findByUserId: jest.fn(),
    } as any;

    eventBus = {
      publish: jest.fn(),
      subscribe: jest.fn(),
    } as any;

    marketDataService = {
      getCurrentPrice: jest.fn(),
      validateTradeability: jest.fn(),
    } as any;

    tradeService = new TradeService(tradeRepository, eventBus, marketDataService);
  });

  describe('executeTrade', () => {
    it('should execute a valid trade successfully', async () => {
      // Arrange
      const trade = TestFactory.createTrade({ status: 'pending' });
      const currentPrice = 150.50;
      
      marketDataService.validateTradeability.mockResolvedValue(true);
      marketDataService.getCurrentPrice.mockResolvedValue(currentPrice);
      tradeRepository.create.mockResolvedValue(trade);
      tradeRepository.update.mockResolvedValue({ ...trade, status: 'executed' });

      // Act
      const result = await tradeService.executeTrade(trade);

      // Assert
      expect(result.status).toBe('executed');
      expect(marketDataService.validateTradeability).toHaveBeenCalledWith(trade.symbol);
      expect(eventBus.publish).toHaveBeenCalledWith('trade.executed', {
        tradeId: trade.id,
        userId: trade.userId,
        symbol: trade.symbol,
        executedPrice: currentPrice,
      });
    });

    it('should fail trade when market is closed', async () => {
      // Arrange
      const trade = TestFactory.createTrade({ status: 'pending' });
      
      marketDataService.validateTradeability.mockResolvedValue(false);
      tradeRepository.create.mockResolvedValue(trade);
      tradeRepository.update.mockResolvedValue({ ...trade, status: 'failed' });

      // Act
      const result = await tradeService.executeTrade(trade);

      // Assert
      expect(result.status).toBe('failed');
      expect(marketDataService.getCurrentPrice).not.toHaveBeenCalled();
      expect(eventBus.publish).toHaveBeenCalledWith('trade.failed', {
        tradeId: trade.id,
        reason: 'Market closed',
      });
    });
  });
});

// test/integration/api/trades.test.ts
import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { TestServer } from '../../helpers/test-server';
import { TestDatabase } from '../../helpers/test-database';
import { TestFactory } from '../../helpers/test-factory';

describe('Trades API Integration Tests', () => {
  let server: TestServer;
  let db: TestDatabase;
  let app: any;

  beforeAll(async () => {
    db = new TestDatabase();
    await db.setup();
    
    server = new TestServer();
    app = await server.start();
  });

  afterAll(async () => {
    await server.stop();
    await db.teardown();
  });

  beforeEach(async () => {
    await db.clean();
  });

  describe('POST /api/trades', () => {
    it('should create a new trade', async () => {
      // Arrange
      const tradeData = TestFactory.createTrade();
      delete (tradeData as any).id; // Remove ID for creation

      // Act
      const response = await app.inject({
        method: 'POST',
        url: '/api/trades',
        headers: {
          'content-type': 'application/json',
          'authorization': 'Bearer test-token',
        },
        body: tradeData,
      });

      // Assert
      expect(response.statusCode).toBe(201);
      const body = JSON.parse(response.body);
      expect(body).toMatchObject({
        symbol: tradeData.symbol,
        quantity: tradeData.quantity,
        side: tradeData.side,
        status: 'pending',
      });
      expect(body.id).toBeDefined();
    });

    it('should validate trade data', async () => {
      // Arrange
      const invalidTrade = {
        symbol: '', // Invalid: empty string
        quantity: -10, // Invalid: negative
        side: 'invalid', // Invalid: not buy/sell
      };

      // Act
      const response = await app.inject({
        method: 'POST',
        url: '/api/trades',
        headers: {
          'content-type': 'application/json',
          'authorization': 'Bearer test-token',
        },
        body: invalidTrade,
      });

      // Assert
      expect(response.statusCode).toBe(400);
      const body = JSON.parse(response.body);
      expect(body.errors).toBeDefined();
      expect(body.errors).toHaveLength(3);
    });
  });
});

// test/e2e/trading-workflow.test.ts
import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import { chromium, Browser, Page } from 'playwright';
import { TestDatabase } from '../helpers/test-database';
import { TestFactory } from '../helpers/test-factory';

describe('E2E: Trading Workflow', () => {
  let browser: Browser;
  let page: Page;
  let db: TestDatabase;

  beforeAll(async () => {
    db = new TestDatabase();
    await db.setup();
    
    browser = await chromium.launch();
    page = await browser.newPage();
  });

  afterAll(async () => {
    await browser.close();
    await db.teardown();
  });

  it('should complete a full trading workflow', async () => {
    // Navigate to trading platform
    await page.goto('http://localhost:3000');
    
    // Login
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'testpassword');
    await page.click('[data-testid="login-button"]');
    
    // Wait for dashboard
    await page.waitForSelector('[data-testid="dashboard"]');
    
    // Search for stock
    await page.fill('[data-testid="stock-search"]', 'AAPL');
    await page.click('[data-testid="search-button"]');
    
    // Place order
    await page.waitForSelector('[data-testid="stock-details"]');
    await page.fill('[data-testid="quantity-input"]', '10');
    await page.click('[data-testid="buy-button"]');
    
    // Confirm order
    await page.waitForSelector('[data-testid="order-confirmation"]');
    await page.click('[data-testid="confirm-order"]');
    
    // Verify order in portfolio
    await page.waitForSelector('[data-testid="order-success"]');
    await page.click('[data-testid="view-portfolio"]');
    
    const portfolioText = await page.textContent('[data-testid="portfolio-table"]');
    expect(portfolioText).toContain('AAPL');
    expect(portfolioText).toContain('10');
  });
});