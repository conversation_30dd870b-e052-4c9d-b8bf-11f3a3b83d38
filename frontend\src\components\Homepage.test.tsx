/**
 * MainHomepage Component Tests
 * Updated to test the current production homepage component
 */

import { render, screen, fireEvent } from '@testing-library/react';
import { describe, test, expect, beforeEach, vi } from 'vitest';
import MainHomepage from './MainHomepage';

// Mock the aiPrompts service
vi.mock('../services/aiPrompts', () => ({
  aiPromptsService: {
    getPrompts: vi.fn().mockResolvedValue([
      {
        id: '1',
        title: 'Trend Following Strategy',
        description: 'Follow market trends with moving averages',
        category: 'Trend',
        prompt: 'Create a trend following strategy using moving averages',
      },
      {
        id: '2',
        title: 'Mean Reversion Strategy',
        description: 'Trade when prices deviate from average',
        category: 'Mean Reversion',
        prompt: 'Create a mean reversion strategy using RSI',
      },
    ]),
  },
}));

// Mock StrategyChatbot component
vi.mock('./StrategyChatbot', () => ({
  default: ({ initialPrompt }: { initialPrompt: string }) => (
    <div data-testid="strategy-chatbot">
      <div><PERSON>ck <PERSON></div>
      {initialPrompt && <div data-testid="initial-prompt">{initialPrompt}</div>}
    </div>
  ),
}));

// Mock scrollIntoView since it's not implemented in jsdom
Object.defineProperty(Element.prototype, 'scrollIntoView', {
  value: vi.fn(),
  writable: true,
});

describe('MainHomepage Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Hero Section', () => {
    test('renders hero section with correct heading', () => {
      render(<MainHomepage />);

      const heroHeading = screen.getByRole('heading', { level: 1 });
      expect(heroHeading).toHaveTextContent('Build Trading Strategies Without Code');
    });

    test('renders hero description with key value proposition', () => {
      render(<MainHomepage />);

      const description = screen.getByText(/Describe your trading idea in plain English/i);
      expect(description).toBeInTheDocument();
    });

    test('renders primary and secondary CTA buttons', () => {
      render(<MainHomepage />);

      const primaryCTA = screen.getByRole('button', { name: /see how it works/i });
      const secondaryCTA = screen.getByRole('button', { name: /start free/i });

      expect(primaryCTA).toBeInTheDocument();
      expect(secondaryCTA).toBeInTheDocument();
    });
  });

  describe('Navigation', () => {
    test('renders navigation with brand name', () => {
      render(<MainHomepage />);

      const logo = screen.getByText('TradeBuilder');
      expect(logo).toBeInTheDocument();
    });

    test('renders all navigation links', () => {
      render(<MainHomepage />);

      const expectedLinks = ['Features', 'How It Works', 'Pricing', 'Contact'];

      expectedLinks.forEach((linkText) => {
        const link = screen.getByRole('button', { name: linkText });
        expect(link).toBeInTheDocument();
      });
    });

    test('navigation buttons trigger smooth scroll', () => {
      render(<MainHomepage />);

      const featuresButton = screen.getByRole('button', { name: /features/i });
      fireEvent.click(featuresButton);

      expect(Element.prototype.scrollIntoView).toHaveBeenCalledWith({
        behavior: 'smooth',
      });
    });
  });

  describe('Chat Demo Section', () => {
    test('renders chat demo section with heading', () => {
      render(<MainHomepage />);

      const chatHeading = screen.getByRole('heading', {
        name: /try our ai assistant right now/i,
      });
      expect(chatHeading).toBeInTheDocument();
    });

    test('renders strategy chatbot component', () => {
      render(<MainHomepage />);

      const chatbot = screen.getByTestId('strategy-chatbot');
      expect(chatbot).toBeInTheDocument();
    });

    test('renders expand/minimize button', () => {
      render(<MainHomepage />);

      const expandButton = screen.getByTitle(/expand chat/i);
      expect(expandButton).toBeInTheDocument();
    });

    test('expand button toggles chat layout', () => {
      render(<MainHomepage />);

      const expandButton = screen.getByTitle(/expand chat/i);
      fireEvent.click(expandButton);

      const minimizeButton = screen.getByTitle(/minimize chat/i);
      expect(minimizeButton).toBeInTheDocument();
    });
  });

  describe('AI Prompts Integration', () => {
    test('renders prompts sidebar with heading', async () => {
      render(<MainHomepage />);

      const promptsHeading = await screen.findByRole('heading', {
        name: /trading strategy prompts/i,
      });
      expect(promptsHeading).toBeInTheDocument();
    });

    test('renders prompt cards when loaded', async () => {
      render(<MainHomepage />);

      const trendCard = await screen.findByText('Trend Following Strategy');
      const meanReversionCard = await screen.findByText('Mean Reversion Strategy');

      expect(trendCard).toBeInTheDocument();
      expect(meanReversionCard).toBeInTheDocument();
    });

    test('clicking prompt card activates it and fills chatbot', async () => {
      render(<MainHomepage />);

      const trendCard = await screen.findByText('Trend Following Strategy');
      fireEvent.click(trendCard);

      const initialPrompt = await screen.findByTestId('initial-prompt');
      expect(initialPrompt).toHaveTextContent('Create a trend following strategy using moving averages');
    });

    test('active prompt shows in chatbot header', async () => {
      render(<MainHomepage />);

      const trendCard = await screen.findByText('Trend Following Strategy');
      fireEvent.click(trendCard);

      const activeIndicator = await screen.findByText('Trend Following Strategy');
      expect(activeIndicator).toBeInTheDocument();
    });
  });

  describe('Features Section', () => {
    test('renders features section heading', () => {
      render(<MainHomepage />);

      const featuresHeading = screen.getByRole('heading', {
        name: /everything you need to trade better/i,
      });
      expect(featuresHeading).toBeInTheDocument();
    });

    test('renders all six feature cards', () => {
      render(<MainHomepage />);

      const expectedFeatures = [
        'Describe Your Strategy',
        'Test With Real Data',
        'Trade on MT5',
        'AI Assistant',
        'Save Money',
        'Track Performance',
      ];

      expectedFeatures.forEach((featureTitle) => {
        const feature = screen.getByRole('heading', { name: featureTitle });
        expect(feature).toBeInTheDocument();
      });
    });
  });

  describe('How It Works Section', () => {
    test('renders how it works section', () => {
      render(<MainHomepage />);

      const howItWorksHeading = screen.getByRole('heading', { name: /how it works/i });
      expect(howItWorksHeading).toBeInTheDocument();
    });

    test('renders all four steps', () => {
      render(<MainHomepage />);

      const expectedSteps = ['Describe Your Idea', 'We Build It', 'Test It', 'Start Trading'];

      expectedSteps.forEach((stepTitle) => {
        const step = screen.getByRole('heading', { name: stepTitle });
        expect(step).toBeInTheDocument();
      });
    });
  });

  describe('Pricing Section', () => {
    test('renders pricing section heading', () => {
      render(<MainHomepage />);

      const pricingHeading = screen.getByRole('heading', { name: /simple pricing/i });
      expect(pricingHeading).toBeInTheDocument();
    });

    test('renders all three pricing plans', () => {
      render(<MainHomepage />);

      const expectedPlans = ['Free', 'Pro', 'Team'];

      expectedPlans.forEach((planName) => {
        const plan = screen.getByRole('heading', { name: planName });
        expect(plan).toBeInTheDocument();
      });
    });

    test('pro plan is marked as featured', () => {
      render(<MainHomepage />);

      const proPrice = screen.getByText('$49');
      expect(proPrice).toBeInTheDocument();

      const proButton = screen.getByRole('button', { name: /start free trial/i });
      expect(proButton).toBeInTheDocument();
    });
  });

  describe('Footer', () => {
    test('renders footer with copyright', () => {
      render(<MainHomepage />);

      const copyright = screen.getByText(/© 2025 TradeBuilder/i);
      expect(copyright).toBeInTheDocument();
    });

    test('renders footer links', () => {
      render(<MainHomepage />);

      const expectedLinks = ['Privacy Policy', 'Terms of Service', 'Documentation', 'Support'];

      expectedLinks.forEach((linkText) => {
        const link = screen.getByLabelText(linkText);
        expect(link).toBeInTheDocument();
      });
    });
  });
});
